{
  "description": "Schema for validating the settings on a tenant",
  "$schema": "<%= TenantSchema::ExtendedSchema::SCHEMA_URL %>",
  "type": "object",
  "required": ["core"],
  "additionalProperties": false,
  "properties":
    {
      "core": {
        "type": "object",
        "title": "Core system",
        "description": "The container for general settings. The core system should always be enabled.",
        "required-settings": [
          "organization_type",
          "timezone",
          "currency",
          "locales",
          "color_main",
          "color_secondary",
          "color_text",
          "lifecycle_stage",
          "country_code",
          "authentication_token_lifetime_in_days"
        ],
        "additionalProperties": false,
        "required": ["allowed", "enabled", "lifecycle_stage", "country_code"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true},
          "organization_name": {
            "title": "Organisation Name",
            "description": "How the city or organisation is referred to throughout the platform.",
            "$ref": "#/definitions/multiloc_string"
          },
          "organization_site": {
            "title": "Organisation Website",
            "description": "The city or organisation's official website.",
            "type": "string",
            "pattern": "^$|^((http:\/\/.+)|(https:\/\/.+))"
          },
          "organization_type": {
            "title": "Organisation Type",
            "description": "Based on city's population: <40k small, 40-120k medium, >120k large. Otherwise, select 'generic'.",
            "type": "string",
            "enum": ["small_city", "medium_city", "large_city", "generic"],
            "default": "medium_city"
          },
          "lifecycle_stage": {
            "title": "Lifecycle Stage",
            "description": "The lifecycle stage of the customer.",
            "type": "string",
            "enum": ["trial", "expired_trial", "demo", "active", "churned", "not_applicable"]
          },
          <% country_codes_service = CountryCodesService.new %>
          "country_code": {
            "title": "Country",
            "description": "The country where the platform's citizens are located. Type first letters of the country name, in English, to search.",
            "type": ["string", "null"],
            "enum": <%= country_codes_service.codes_in_name_order.to_json %>,
            "enumNames": <%= country_codes_service.names_in_name_order.to_json %>
          },
          "timezone": {
            "title": "Time Zone",
            "type": "string",
            "default": "Europe/Brussels",
            "enum": [<%= TimezoneService::SUPPORTED_TIMEZONES.map{|l| "\"#{l}\""}.join(",") %>],
            "enumNames": [<%= TimezoneService::SUPPORTED_TIMEZONES.map{|tz| TimezoneService.new.display_timezone(tz)}.map{|l| "\"#{l}\""}.join(",") %>]
          },
          "currency": {
            "title": "Currency",
            "description": "Currency used by the organisation, for projects like participatory budgeting that have budget fields.",
            "type": "string",
            "default": "EUR",
            "enum": [<%= CL2_SUPPORTED_CURRENCIES.map{|l| "\"#{l}\""}.join(",") %>]
          },
          "locales": {
            "title": "Platform Languages",
            "description": "Select all of the languages that will be in use on the platform.",
            "type": "array",
            "items": {
              "type": "string",
              "enum": [<%= CL2_SUPPORTED_LOCALES.map { |locale| "\"#{locale}\"" }.join(",") %>],
              "enumNames": [<%= CL2_SUPPORTED_LOCALES.map { |locale| locale.to_s == 'en' ? '"en-US"' : "\"#{locale}\"" }.join(",") %>]
            },
            "uniqueItems": true,
            "minItems": 1,
            "default": ["en"]
          },
          "population": {
            "title": "Population",
            "description": "Number of inhabitants in the territory. Leave empty if not applicable.",
            "type": ["integer", "null"],
            "minimum": 0
          },
          "weglot_api_key": {
            "title": "Weglot API key",
            "description": "Set the API key of Weglot to enable automatic page translations.",
            "type": "string"
          },
          "color_main": {
            "title": "Main Colour",
            "description": "Main colour of platform. Input as a 6-digit HEX color, including the # sign (e.g. #073F80).",
            "type": "string",
            "pattern": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$",
            "default": "#163A7D"
          },
          "color_secondary": {
            "title": "Secondary Colour",
            "description": "Secondary colour of platform. Input as a 6-digit HEX color, including the # sign (e.g. #ff672f).",
            "type": "string",
            "pattern": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$",
            "default": "#CF4040"
          },
          "color_text": {
            "title": "Text Colour",
            "description": "Colour of platform text. Input as a 6-digit HEX color, including the # sign (e.g. #7fbbca).",
            "type": "string",
            "pattern": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$",
            "default": "#163A7D"
          },
          "meta_title": {
            "title": "Page Title",
            "description": "The title of the homepage displayed on the browser tab in search engine results and when shared on social media.",
            "$ref": "#/definitions/multiloc_string"
          },
          "meta_description": {
            "title": "Social Media Description",
            "description": "The description of the platform shown in search engine results and when shared on some social media platforms.",
            "$ref": "#/definitions/multiloc_string"
          },
          "google_search_console_meta_attribute": {
            "title": "Google Search Console Meta Attribute",
            "description": "Set this token to verify the ownership of the platform in Google Search Console. The value you should set is the content of the \"content\"= HTML tag attribute in meta tag Google provides.",
            "type": ["string", "null"]
          },
          "custom_onboarding_message": {
            "title": "Header Banner Call-to-Action Text",
            "description": "Optional Call-to-Action text for signed in users, shown on a banner on the top of the homepage. Accompanied by a Call-to-Action (CTA) button. If this field is left blank, 'Header banner non-call to action' text is shown in the banner.",
            "$ref": "#/definitions/multiloc_string",
            "private": true
          },
          "custom_onboarding_button": {
            "title": "Header Banner CTA Button",
            "description": "Text for the CTA button in the header banner for signed-in users. Accompanies the 'Header banner call to action text' field.",
            "$ref": "#/definitions/multiloc_string",
            "private": true
          },
          "custom_onboarding_link": {
            "title": "Header Banner CTA Link",
            "description": "Web location that the CTA button links to. This should be a relative link on the platform starting with (e.g., platform.govocal.com/initiatives should be inputted here as /initiatives).",
            "type": "string",
            "pattern": "^$|^/.*$",
            "private": true
          },
          "signup_helper_text": {
            "title": "Sign-up Helper Text (Step 1)",
            "description": "Optional short text, shown at the top of Step 1 of the sign up form (i.e., email and password). Supports HTML.",
            "$ref": "#/definitions/multiloc_string"
          },
          "custom_fields_signup_helper_text":  {
            "title": "Sign-up Helper Text (Step 2)",
            "description": "Optional short text, shown at the top of Step 2 of the sign up form (i.e., additional registration fields). Supports HTML.",
            "$ref": "#/definitions/multiloc_string"
          },
          "onboarding":  {
            "title": "Onboarding (Step 3)",
            "description": "Onboarding step after login or signup, asking for topics you are interested in.",
            "type": "boolean",
            "default": false
          },
          "allow_sharing":  {
            "title": "Sharing content on socials",
            "description": "Allow users to share content via social media or email.",
            "type": "boolean",
            "default": true
          },
          "areas_term": {
            "title": "Geographic Units",
            "description": "What a geographic unit should be called on this platform (e.g., neighbourhoods, districts, etc.). Input the plural form here with all lowercase letters. If left blank, this field defaults to 'areas'.",
            "$ref": "#/definitions/multiloc_string"
          },
          "area_term": {
            "title": "Geographic Unit",
            "description": "What a geographic unit should be called on this platform (e.g., neighbourhood, district, etc.)? Input the singular form here with all lowercase letters. If left blank, this field defaults to 'area'.",
            "$ref": "#/definitions/multiloc_string"
          },
          "topics_term": {
            "title": "Tag Units",
            "description": "What a tag unit should be called on this platform (e.g., departments, themes, etc.). Input the plural form here with all lowercase letters. If left blank, this field defaults to 'tags'.",
            "$ref": "#/definitions/multiloc_string"
          },
          "topic_term": {
            "title": "Tag Unit",
            "description": "What a tag unit should be called on this platform (e.g., department, theme, etc.). Input the singular form here with all lowercase letters. If left blank, this field defaults to 'tag'.",
            "$ref": "#/definitions/multiloc_string"
          },
          "from_email": {
            "title": "From email",
            "description": "The email used in the from field when users receive emails. This should only be configured when the corresponding Second Line Support work has already happened and the customer has made the necessary DNS changes, or emails will not function at all.",
            "type": "string",
            "format": "email"
          },
          "reply_to_email": {
            "title": "Reply-to email",
            "description": "The email used in the reply-to field when users receive emails from automated campaigns.",
            "type": "string",
            "format": "email",
            "default": "<EMAIL>"
          },
          "authentication_token_lifetime_in_days": {
            "title": "Authentication token lifetime in days",
            "description": "How many days before an authentication token (a user session) is expired.",
            "type": "integer",
            "minimum": 1,
            "maximum": 30,
            "default": 30
          },
          "maximum_admins_number": {
            "title": "Maximum number of admin seats in the contract",
            "description": "Maximum allowed number of admin seats specified in the contract (leave blank for unlimited).",
            "type": ["integer", "null"]
          },
          "maximum_moderators_number": {
            "title": "Maximum number of managers in the contract",
            "description": "Maximum allowed number of project and folder managers specified in the contract (leave blank for unlimited).",
            "type": ["integer", "null"]
          },
          "additional_admins_number": {
            "title": "Additional number of admin seats",
            "description": "Number of admin seats bought additionally over the contract limit.",
            "type": ["integer", "null"]
          },
          "additional_moderators_number": {
            "title": "Additional number of manager seats",
            "description": "Number of project and folder manager seats bought additionally over the contract limit.",
            "type": ["integer", "null"]
          },
          "customer_portal_url": {
            "title": "Customer portal URL",
            "description": "Link to the customer portal in Planhat.",
            "type": "string",
            "format": "uri"
          },
          "anonymous_name_scheme":  {
            "title": "Scheme to use when displaying anonymous names",
            "description": "Default is User 123456.",
            "type": "string",
            "enum": ["user", "animal"],
            "default": "user"
          },
          "private_attributes_in_export":  {
            "title": "Allow private attributes (user name & email) in native survey exports & analysis?",
            "description": "When disabled exports and analyses on native survey phases will not return any private attributes.",
            "type": "boolean",
            "default": true
          }
        }
      },

      "user_blocking": {
        "type": "object",
        "title": "User blocking",
        "description": "Admin can block or unblock users.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "required-settings": ["duration"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false },
          "duration": {
            "title": "Duration of block (in days)",
            "description": "How many days a user will be blocked for. The user will remain blocked until this period expires, or an admin manually removes the block on the user. Changing this value changes the duration of subsequent blocks only.",
            "type": "integer",
            "minimum": 1,
            "default": 90
          }
        }
      },

      "user_avatars": {
        "type": "object",
        "title": "User avatars",
        "description": "Allow users to upload a profile picture.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "gravatar_avatars": {
        "type": "object",
        "title": "Automatically try to download an avatar from an external service called Gravatar when a user registers without an avatar",
        "description": "Allow users to upload a profile picture.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "internal_commenting": {
        "type": "object",
        "title": "Internal commenting",
        "description": "Admins and managers can comment internally on ideas and proposals.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "follow": {
        "type": "object",
        "title": "Follow",
        "description": "Allow users to passively follow and get notifications about some parts of the application",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "jsonforms_custom_fields": {
        "type": "object",
        "title": "Rework of user custom fields (experimental)",
        "description":  "Replaces react-json-forms with jsonforms for user custom fields form. Don't turn this on unless you know what you're doing.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "advanced_custom_pages": {
        "type": "object",
        "title": "Advanced Custom Pages",
        "description":	"Advanced Custom Pages features.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "project_folders": {
        "type": "object",
        "title": "Project Folders",
        "description": "Allow project folders.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "project_preview_link" : {
        "type": "object",
        "title": "Project Preview Link",
        "description": "Allows sharing a preview of a draft project via a link, which can be found in the project's settings.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "password_login": {
        "type": "object",
        "title": "Password Login",
        "description": "Allow users to register with an email and password.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "required-settings": ["minimum_length", "enable_signup"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true},
          "enable_signup": {
            "type": "boolean",
            "title": "Enable sign-up",
            "description": "If unchecked, only login via password is allowed for existing users; sign-up via password is disabled for new users.",
            "default": true
          },
          "minimum_length": {
            "type": "number",
            "title": "Minimum password length",
            "description": "The minimum number of characters required for a user password. The default value is 8 (min. 5, max. 72). Longer passwords are generally more secure. Changes to the min. password length will only apply to users that create a new account or update their existing password (existing accounts remain the same).",
            "minimum": 5,
            "maximum": 72,
            "default": 8
          }
        }
      },

      "facebook_login": {
        "type": "object",
        "title": "Facebook Login",
        "description":	"Allow users to register and sign in through their Facebook account.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false},
          "app_id": {
            "title": "App ID",
            "type": "string"
          },
          "app_secret": {
            "title": "App Secret",
            "type": "string",
            "private": true
          }
        }
      },

      "google_login": {
        "type": "object",
        "title": "Google Login",
        "description":  "Allow users to register and sign in through their Google account.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false},
          "client_id": {
            "title": "Client ID",
            "type": "string"
          },
          "client_secret": {
            "title": "Client Secret",
            "type": "string",
            "private": true
          }
        }
      },

      "azure_ad_login": {
        "type": "object",
        "title": "Azure AD Login",
        "description":  "Allow users to register and sign in with Entra ID (fka Azure Active Directory).",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false},
          "tenant": {
            "title": "Directory (tenant) ID",
            "type": "string",
            "private": true
          },
          "client_id": {
            "title": "Application (client) ID",
            "description": "Sometimes also called 'application_id'",
            "type": "string",
            "private": true
          },
          "logo_url": {
            "title": "Logo",
            "type": "string",
            "pattern": "^https:\/\/.+",
            "description": "The full URL to the logo image that is shown on the authentication button. Logo should be approx. 25px in height."
          },
          "login_mechanism_name": {
            "title": "Login Mechanism Name",
            "type": "string",
            "description": "The Login Mechanism Name is used for user-facing copy. For instance, \"Sign up with {login_mechanism_name}.\"."
          },
          "visibility": {
            "title": "Visibility",
            "type": "string",
            "enum": ["show", "link", "hide"],
            "default": "show",
            "description": "Should this login mechanism be shown with other options to everyone, be hidden but available at /sign-in/admin or linked from the login modal via an 'admin options' link?"
          }
        }
      },

      "azure_ad_b2c_login": {
        "type": "object",
        "title": "Azure AD B2C Login",
        "description":  "Allow users to register and sign in with Azure AD B2C.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false},
          "tenant_name": {
            "title": "Directory (tenant) Name",
            "description": "The name of the Azure AD B2C tenant. The first part of the domain name. E.g. in citizenlabdevdemo.onmicrosoft.com, it's citizenlabdevdemo",
            "type": "string",
            "private": true
          },
          "tenant_id": {
            "title": "Directory (tenant) ID",
            "type": "string",
            "private": true
          },
          "policy_name": {
            "title": "Policy (User Flow, User Journey) Name",
            "description": "The name of the policy (user flow, user journey) in the Azure AD B2C tenant. This is the policy that is used for sign-in and sign-up. tenant_name, tenant_id, and policy_name are used together to form such configuration URL https://{tenant_name}.b2clogin.com/tfp/{tenant_id}/{policy_name}/v2.0/.well-known/openid-configuration that returns JSON configuration.",
            "type": "string",
            "private": true
          },
          "client_id": {
            "title": "Application (client) ID",
            "description": "Sometimes also called 'application_id'",
            "type": "string",
            "private": true
          },
          "logo_url": {
            "title": "Logo",
            "type": "string",
            "pattern": "^https:\/\/.+",
            "description": "The full URL to the logo image that is shown on the authentication button. Logo should be approx. 25px in height."
          },
          "login_mechanism_name": {
            "title": "Login Mechanism Name",
            "type": "string",
            "description": "The Login Mechanism Name is used for user-facing copy. For instance, \"Sign up with {login_mechanism_name}.\"."
          }
        }
      },

      "integration_onze_stad_app": {
        "type": "object",
        "description":  "Allow posting ideas to Onze StadApp as news.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "required-settings": ["app_id", "api_key"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false},
          "app_id": {
            "title": "App ID",
            "type": "string",
            "private": true
          },
          "api_key": {
            "title": "API Key",
            "type": "string",
            "private": true
          }
        }
      },

      "maps": {
        "type": "object",
        "title": "Maps Default Settings",
        "description":  "Default settings for maps. Note that 'allowed' and 'enabled' settings are redundant and toggling them will not have any effect.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true},
          "tile_provider": {
            "title": "Tile provider",
            "description": "The OSM or basemap.at compatible tile provider URL pattern (https is required).",
            "type": "string",
            "pattern": "^https:\/\/.+",
            "default": "<%= ENV.fetch('DEFAULT_MAPS_TILE_PROVIDER', 'https://api.maptiler.com/maps/basic/{z}/{x}/{y}.png?key=R0U21P01bsRLx7I7ZRqp')%>"
          },
          "map_center": {
            "title": "Map Center",
            "description": "The default center point of the platform maps (latitude/longitude). Use a dot as a decimal separator. South and West values should be negative.",
            "type": "object",
            "additionalProperties": false,
            "properties": {
              "lat": {
                "title": "Latitude",
                "type": "string",
                "default": "50.8503"
              },
              "long": {
                "title": "Longitude",
                "type": "string",
                "default": "4.3517"
              }
            }
          },
          "zoom_level": {
            "type": "number",
            "title": "Map Zoom Level",
            "description": "The zoom level of the platform maps. The Zoom Level should be a number between 0 - 19. The default zoom level is 12 (town). See https://wiki.openstreetmap.org/wiki/Zoom_levels for more information about zoom levels.",
            "default": 12
          },
          "osm_relation_id": {
            "type": "integer",
            "title": "OSM Relation ID",
            "description": "The boundary of the municipality/organisation is specified by its Relation ID in OpenStreetMap (OSM). You can find the Relation ID at https://www.openstreetmap.org. Search for the place, click on the correct result and you'll find the Relation ID if the result is a relation (e.g. \"Relation: Knokke-Heist (4569)\"). If there is no relation defined for the place (e.g. Scheveningen), search for the next largest administrative territory (e.g. The Hague)."
          }
        }
      },

      "esri_integration": {
        "type": "object",
        "title": "Esri Integration",
        "description":  "Esri integration for maps. Enables the use of Esri layers and Esri web maps with our maps. An add-on that can be purchased, and is not restricted to specific pricing plan(s).",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false},
          "api_key": {
            "title": "API Key",
            "description": "Add customer’s Esri API key to allow importing map layers from their ArcGIS Online in the map tabs in projects. This setting is also available to admins in the Tools: Esri Maps item.",
            "type": "string"
          }
        }
      },

      "intercom": {
        "type": "object",
        "title": "Intercom Integration",
        "description":  "Integrates Intercom messenger and data collection",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true}
        }
      },

      "segment": {
        "type": "object",
        "title": "Segment Integration",
        "description":  "Integrates segment data collection",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false},
          "destinations": {
            "type": "string",
            "description": "As more tools can be activated through Segment, here you can specify them using comma separated text, shown in the cookie consent"
          }
        }
      },

      "planhat": {
        "type": "object",
        "title": "Planhat Integration",
        "description":  "Planhat integration for data collection (via Segment).",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "satismeter": {
        "type": "object",
        "title": "Satismeter Integration",
        "description":  "Integrates Satismeter polls and data collection to the front-end",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "required-settings": ["write_key"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true},
          "write_key": {
            "type": "string",
            "description": "The write key the front-end send its data to.",
            "default": "<%= ENV.fetch('DEFAULT_SATISMETER_WRITE_KEY', '')%>"
          }
        }
      },

      "google_analytics": {
        "type": "object",
        "title": "Google Analytics Integration",
        "description":  "Integrates Google Analytics data collection to the front-end",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "required-settings": ["tracking_id"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true},
          "tracking_id": {
            "type": "string",
            "description": "The tracking ID the front-end send it's data to. Format UA-XXXXXXXXX-XX",
            "pattern": "^(UA|YT|MO)\-[0-9]+\-[0-9]+$",
            "default": "<%= ENV.fetch('DEFAULT_GA_TRACKING_ID', '')%>"
          }
        }
      },

      "polls": {
        "type": "object",
        "title": "Polls",
        "description": "Allow polls.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true}
        }
      },

      "prescreening": {
        "type": "object",
        "title": "Screening (proposals)",
        "description": "When active, screening can be activated for proposals phases, which requires admins/moderators to explicitly approve each input before it becomes visible to the public.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "prescreening_ideation": {
        "type": "object",
        "title": "Screening (ideation)",
        "description": "When active, screening can be activated for ideation phases, which requires admins/moderators to explicitly approve each input before it becomes visible to the public.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "surveys": {
        "type": "object",
        "title": "Surveys",
        "description":  "Allow external surveys to be embedded.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true}
        }
      },

      "typeform_surveys": {
        "type": "object",
        "title": "Typeform Surveys",
        "description":  "Allow Typeform surveys to be embedded.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true},
          "user_token": {
            "title": "User Token",
            "description": "The user token is generated from the tenant's typeform profile. Survey results are only downloadable for this token.",
            "type": "string",
            "private": true,
            "default": "<%= ENV.fetch('DEFAULT_TYPEFORM_USER_TOKEN', '') %>"
          }
        }
      },

      "google_forms_surveys": {
        "type": "object",
        "title": "Google Forms Surveys",
        "description":  "Allow Google Form surveys to be embedded.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true}
        }
      },

      "enalyzer_surveys": {
        "type": "object",
        "title": "Enalyzer Forms Surveys",
        "description":  "Allow Enalyzer surveys to be embedded.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "survey_xact_surveys": {
        "type": "object",
        "title": "Survey Xact Forms Surveys",
        "description":  "Allow Survey Xact surveys to be embedded.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "surveymonkey_surveys": {
        "type": "object",
        "title": "Surveymonkey Surveys",
        "description":  "Allow the embedding of Surveymonkey surveys. Deprecated, since not supported on mobile.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "snap_survey_surveys": {
        "type": "object",
        "title": "Snap Survey Surveys",
        "description":  "Allow the embedding of Snap Survey surveys.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "qualtrics_surveys": {
        "type": "object",
        "title": "Qualtrics Surveys",
        "description":  "Allow Qualtrics surveys to be embedded.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "microsoft_forms_surveys": {
        "type": "object",
        "title": "Microsoft Forms Surveys",
        "description":  "Allow Microsoft Forms surveys to be embedded.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "smart_survey_surveys": {
        "type": "object",
        "title": "SmartSurvey Surveys",
        "description":  "Allow SmartSurvey surveys to be embedded.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "konveio_document_annotation": {
        "type": "object",
        "title": "Konveio Document Annotation",
        "description":  "Allow Konveio document annotation to be embedded.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "workshops": {
        "title": "Workshops",
        "type": "object",
        "description":	"Allow workshops.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true}
        }
      },

      "blocking_profanity": {
        "type": "object",
        "title": "Block Profanity",
        "description": "Prevent input with profanity in it from being posted (can be turned on/off from the platform)",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": false},
          "extended_blocking": { "type": "boolean", "default": false}
        }
      },

      "custom_accessibility_statement_link": {
        "type": "object",
        "title": "Custom A11y Statement Link",
        "description":  "Allows the Front-End to direct the user to a different accessibility statement link than our own.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "required-settings": ["url"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false},
          "url": {
            "title": "URL",
            "description": "The link to the page to which users should be redirected when clicking on the accessibility statement link within the footer.",
            "type": "string",
            "format": "uri"
          }
        }
      },

      "disable_disliking": {
        "type": "object",
        "title": "Disliking Toggle",
        "description": "Display toggle in Project settings or Project’s timeline phase settings, that permits the disabling and re-enabling of dislike reactions. By default, disliking is enabled. (previously called disable_downvoting",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true}
        }
      },

      "granular_permissions": {
        "type": "object",
        "title": "Granular Permissions",
        "description": "Admin can specify permissions for specific projects and project phases (e.g., who can post, react, etc.).",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true}
        }
      },

      "widgets": {
        "type": "object",
        "title": "Widgets",
        "description":  "Admins can generate platform widgets to embed on external websites.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true}
        }
      },

      "pages": {
        "type": "object",
        "title": "Custom Pages",
        "description":	"Allow custom pages to be made and added to the platform's navigation.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": true}
        }
      },

      "events_widget": {
        "type": "object",
        "title": "Events Landing Page Widget",
        "description":	"Display a widget of the next upcoming events in your landing page.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": true},
          "widget_title": {"$ref": "#/definitions/multiloc_string"}
        }
      },

      "redirects": {
        "type": "object",
        "title": "URL redirection rules",
        "description":  "Define redirection rules on certain targets",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false },
          "rules": {
            "type": "array",
            "default": [],
            "items": {
              "type": "object",
              "required": ["path", "target"],
              "additionalProperties": false,
              "properties": {
                "path": {"type": "string", "description": "The URL path that should trigger the redirect. This is the part after the locale. For e.g. https://my-platform.com/en-GB/projects/my-project, the path would be projects/my-project."},
                "target": {"type": "string", "description": "The URL where the path should redirected to, e.g. https://google.com"}
              }
            }
          }
        }
      },

      "abbreviated_user_names": {
        "type": "object",
        "title": "Abbreviated User Name",
        "description": "NOTE: Once you have selected this option, you cannot change it back. User names are shown on the platform as first name + first initial (Jane D. instead of Jane Doe).",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "idea_author_change": {
        "type": "object",
        "title": "Idea Author Change",
        "description": "Allows city admins to create ideas and assign users as their authors.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "disable_user_bios": {
        "type": "object",
        "title": "Disable User Biographies",
        "description":  "When this is enabled, the user biographies get disabled on the platform.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "form_mapping": {
        "type": "object",
        "title": "Mapping features used in in-platform surveys",
        "description":  "Mapping questions (pin, route & area), Esri Shapefile upload, and map page.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "input_form_custom_fields": {
        "type": "object",
        "title": "Input form builder custom fields",
        "description": "Enables the ability to create custom fields within the ideation form builder.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "user_confirmation": {
        "type": "object",
        "title": "User email confirmation",
        "description": "Enables the ability for emails to be confirmed after registration.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "permissions_custom_fields": {
        "type": "object",
        "title": "Permission custom fields on project actions",
        "description": "Enables the option for admins to add custom questions to project action permissions.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "anonymous_participation": {
        "type": "object",
        "title": "Anonymous participation",
        "description": "Turning on this feature is recommended if boosting engagement is important and/or the client is running a complex project where they anticipate some residents being afraid of speaking up. Users may still choose to participate with their real name, but if enabled they will have the option to submit inputs, comments or proposals anonymously if they choose to do so. All users will still need to comply with the requirements for participation set by admins or project moderators for that project.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "custom_idea_statuses": {
        "type": "object",
        "title": "Custom Input Statuses",
        "description": "Allows admin to define custom input statuses.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "public_api_tokens": {
        "type": "object",
        "title": "Public API tokens",
        "description": "Enables the ability to create API tokens for the public API.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "power_bi": {
        "type": "object",
        "title": "Power BI",
        "description": "Allows downloading of pre-configured templates for PowerBI.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "import_printed_forms": {
        "type": "object",
        "title": "Form Sync (fka import printed forms)",
        "description": "Enables the importing of PDF files from handwritten ideas and surveys on printed forms.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "html_pdfs": {
        "type": "object",
        "title": "Use HTML PDF generator",
        "description": "Enables the new more advanced PDF generation for export and import (DO NOT USE UNTIL FURTHER NOTICE).",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "input_importer": {
        "type": "object",
        "title": "Input importer",
        "description": "Enables the importing of Excel (XLSX) files with ideas and surveys.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "posthog_user_tracking": {
        "type": "object",
        "title": "Posthog user tracking",
        "description": "This integration enables sending usage events to PostHog, a product analytics tools. It applies to all users. It requires active consent from the user.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "user_session_recording": {
        "type": "object",
        "title": "Posthog user session recording",
        "description": "Enables the recording of a small fraction of user sessions for analysis and product research purposes. Requires active consent from the user.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "analysis": {
        "type": "object",
        "title": "AI-powered analysis for surveys and ideation",
        "description": "Enables the use of AI-powered analysis for surveys and ideation.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "large_summaries": {
        "type": "object",
        "title": "AI-powered analysis for surveys and ideation (large summaries)",
        "description": "Enables the use of AI-powered analysis for surveys and ideation for a large number of inputs.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "ask_a_question": {
        "type": "object",
        "title": "AI-powered questions and answers feature for surveys and ideation",
        "description": "Enables the use of AI-powered questions and answers feature for surveys and ideation.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "advanced_autotagging": {
        "type": "object",
        "title": "AI-powered advanced autotagging for surveys and ideation",
        "description": "Enables the use of AI-powered advanced autotagging feature for surveys and ideation.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "auto_insights": {
        "type": "object",
        "title": "Statistical insights for surveys and ideation",
        "description": "Enables the use of statistical insights in an analysis for surveys and ideation",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "multi_language_platform": {
        "type": "object",
        "title": "Multi-language platform",
        "description": "Enables the ability to add multiple languages on the platform.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "customisable_homepage_banner": {
        "type": "object",
        "title": "Customisable homepage banner",
        "description": "Enables customization of various settings for the homepage banner, including layout options, overlay colors, and more, beyond just the banner text.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "proposals_participation_method": {
        "type": "object",
        "title": "Proposals participation method",
        "description": "Enables the ability to choose proposals as a participation method for a project.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "input_cosponsorship": {
        "type": "object",
        "title": "Input co-sponsorship",
        "description": "Enables the ability to invite co-sponsors to your inputs.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "management_feed": {
        "type": "object",
        "title": "Management Feed",
        "description": "Experimental feature: A minimal list of selected actions performed by admins or managers in the last 30 days. Not all actions are included.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "project_review": {
        "type": "object",
        "title": "Project review",
        "description": "Enables the ability to review projects before they are published.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "authoring_assistance_prototype": {
        "type": "object",
        "title": "Experimental feature: Authoring assistance prototype",
        "description":	"Show an informative box to admins on the idea pages with guidance on how the written text could be improved (duplicate detection, toxicity, free custom prompt...). Inputs posted while the feature was disabled will not be included as duplicates.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "input_iq": {
        "type": "object",
        "title": "InputIQ",
        "description":	"Help the user with writing better ideas and proposals through AI assistance while typing in the input form. Inputs posted while the feature was disabled will not be listed as similar inputs.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false},
          "enabled": { "type": "boolean", "default": false}
        }
      },

      "platform_templates": {
        "type": "object",
        "title": "Platform templates",
        "description": "Allow non-superadmins to create reports using platform templates.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "project_library": {
        "type": "object",
        "title": "Inspiration hub",
        "description": "Temporary feature flag we are using to be able to hide our work in progress from the live platforms. Do not enable this feature!",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "user_fields_in_surveys": {
        "type": "object",
        "title": "User fields in surveys",
        "description": "Allow surveys to embed user fields directly in the survey form, instead of asking through registration.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": true },
          "enabled": { "type": "boolean", "default": true }
        }
      },

      "verification": {
        "type": "object",
        "title": "Verification",
        "description": "Use a pre-defined list to verify user identities during registration.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false },
          "verification_methods": {
            "title": "Verification Methods",
            "private": true,
            "type": "array",
            "default": [],
            "items": {
              "anyOf": <%= ::Verification::VerificationService.new.all_methods_json_schema %>
            }
          }
        }
      },

      "community_monitor": {
        "type": "object",
        "title": "Community Monitor",
        "description": "Long running public survey.",
        "additionalProperties": false,
        "required": [
          "allowed",
          "enabled"
        ],
        "properties": {
          "allowed": {
            "type": "boolean",
            "default": false
          },
          "enabled": {
            "type": "boolean",
            "default": false
          },
          "project_id": {
            "title": "The ID of the required hidden project",
            "description": "Do not edit unless you know what you're doing. This is usually set by the system",
            "type": "string"
          }
        }
      },

      "common_ground": {
        "type": "object",
        "title": "Common ground",
        "description": "Common Ground is a participation method that lets participants post and react to concise statements using a swipe style: agree, disagree, or unsure. Statements are then ranked by consensus and controversy.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "data_repository": {
        "type": "object",
        "title": "Data repository",
        "description": "Data Repository feature (360 Input). Enables a files tab in the project back office, where admins & PMs can upload files and documents related to the project.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "data_repository_ai_analysis": {
        "type": "object",
        "title": "Data repository: AI analysis",
        "description": "Enables the AI Analysis features for the Data Repository (360 Input).",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "project_planning": {
        "type": "object",
        "title": "Project planning",
        "description": "New back office projects overview [DO NOT ENABLE]",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "project_planning_calendar": {
        "type": "object",
        "title": "Project planning: calendar view",
        "description": "Feature flag for calendar view in new projects page",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "customised_automated_emails": {
        "type": "object",
        "title": "Customised automated emails",
        "description": "Override text of the default emails and preview [DO NOT ENABLE]",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      },

      "project_importer": {
        "type": "object",
        "title": "Project importer [Alpha]",
        "description": "Super admin only feature to import project, phases and content from XLSX.",
        "additionalProperties": false,
        "required": ["allowed", "enabled"],
        "properties": {
          "allowed": { "type": "boolean", "default": false },
          "enabled": { "type": "boolean", "default": false }
        }
      }
    },
  "dependencies": {
    "planhat": ["segment"],

    "typeform_surveys": ["surveys"],
    "google_forms_surveys": ["surveys"],
    "enalyzer_surveys": ["surveys"],
    "survey_xact_surveys": ["surveys"],
    "surveymonkey_surveys": ["surveys"],
    "qualtrics_surveys": ["surveys"],
    "snap_survey_surveys": ["surveys"],
    "microsoft_forms_surveys": ["surveys"],
    "smart_survey_surveys": ["surveys"],

    "id_auth0": ["verification"],
    "id_bogus": ["verification"],
    "id_bosa_fas": ["verification"],
    "id_clave_unica": ["verification"],
    "id_cow": ["verification"],
    "id_franceconnect": ["verification"],
    "id_gent_rrn": ["verification"],
    "id_oostende_rrn": ["verification"],
    "id_id_card_lookup": ["verification"],
    "id_keycloak": ["verification"],

    "power_bi": ["public_api_tokens"],
    "large_summaries": ["analysis"],
    "ask_a_question": ["analysis"],
    "advanced_autotagging": ["analysis"],
    "auto_insights": ["analysis"],
    "import_printed_forms": ["input_importer"]
  },
  "definitions": {
    "multiloc_string": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        <%= CL2_SUPPORTED_LOCALES.map{|l| "\"#{l.to_s}\": {\"type\": \"string\"}"}.join(",") %>
      }
    }
  }
}

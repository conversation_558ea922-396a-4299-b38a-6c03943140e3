# frozen_string_literal: true

module Files
  # Service to migrate legacy file records to the new Files::File system
  #
  # This service handles the migration of legacy file models (IdeaFile, ProjectFile,
  # EventFile, PhaseFile, StaticPageFile, ProjectFolders::File) to the new unified
  # Files::File and Files::FileAttachment system.
  #
  # Usage:
  #   # Migrate all legacy files
  #   Files::LegacyFileMigrationService.new.migrate_all
  #
  #   # Migrate files for a specific container
  #   Files::LegacyFileMigrationService.new.migrate_container(project)
  #
  class LegacyFileMigrationService
    LEGACY_FILE_ASSOCIATIONS = {
      Idea => :idea_files,
      Project => :project_files,
      Event => :event_files,
      Phase => :phase_files,
      ProjectFolders::Folder => :project_folders_files,
      StaticPage => :static_page_files
    }

    def migrate_all
      LEGACY_FILE_ASSOCIATIONS.each do |container_class, legacy_file_association|
        migrate_container_class(container_class, legacy_file_association)
      end
    end

    def migrate_container_class(container_class, legacy_file_association)
      container_class.where.associated(legacy_file_association).find_each do |container|
        migrate_container(container, legacy_file_association)
      end
    end

    def migrate_container(container, legacy_file_association)
      Files::File.transaction do
        container.send(legacy_file_association).find_each do |legacy_file|
          file = Files::File.new(
            name: legacy_file.name,
            content: legacy_file.file,
            uploader: (container.author if container.is_a?(Idea)),
            created_at: legacy_file.created_at
          )

          if file.content.blank?
            Rails.logger.error 'Skipping file with missing content', legacy_file: legacy_file.id, container: container.id, container_class: container.class
            next
          end

          attachment = file.attachments.build(
            position: legacy_file.ordering,
            attachable: container
          )

          if (project = container.try(:project))
            file.files_projects.build(project: project)
          end

          file.save!

          update_idea_cf_values!(container, legacy_file.id, attachment.id) if container.is_a?(Idea)
          legacy_file.update!(migrated_file: file)
          Rails.logger.info 'Migrated legacy file', legacy_file: legacy_file.id, file: file.id, container: container.id, container_class: container.class
        end

        Rails.logger.info 'Migrated all legacy files for container', container: container.id, container_class: container.class
      end
    end

    def update_idea_cf_values!(idea, legacy_file_id, attachment_id)
      idea.custom_field_values.transform_values! do |value|
        value.merge('id' => attachment_id) if value['id'] == legacy_file_id
      end

      idea.save!
    end
  end
end

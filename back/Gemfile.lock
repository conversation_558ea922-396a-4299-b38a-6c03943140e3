PATH
  remote: engines/commercial/admin_api
  specs:
    admin_api (0.1.0)
      active_model_serializers (~> 0.10.7)
      graphql (~> 2.3)
      kaminari (~> 1.2)
      multi_tenancy
      rails (~> 7.0)
      ros-apartment (>= 2.9.0)

PATH
  remote: engines/commercial/aggressive_caching
  specs:
    aggressive_caching (0.1.0)
      actionpack-action_caching (~> 1.2)
      rails (~> 7.0)

PATH
  remote: engines/commercial/analysis
  specs:
    analysis (0.1.0)
      concurrent-ruby (~> 1.2.3)
      distribution (~> 0.8.0)
      matrix (~> 0.4.2)
      prime (~> 0.1.3)
      rails (~> 7.0)
      ruby-openai (>= 6.3, < 8.0)
      tiktoken_ruby (~> 0.0.7)

PATH
  remote: engines/commercial/analytics
  specs:
    analytics (0.1.0)
      rails (~> 7.0)
      scenic

PATH
  remote: engines/commercial/bulk_import_ideas
  specs:
    bulk_import_ideas (0.1.0)
      active_model_serializers (~> 0.10.7)
      pundit (~> 2.0)
      rails (~> 7.0)
      ros-apartment (>= 2.9.0)

PATH
  remote: engines/commercial/content_builder
  specs:
    content_builder (0.1.0)
      active_model_serializers (~> 0.10.7)
      nanoid (~> 2.0)
      pundit (~> 2.0)
      rails (~> 7.0)
      ros-apartment (>= 2.9.0)

PATH
  remote: engines/commercial/custom_maps
  specs:
    custom_maps (0.1.0)
      pundit (~> 2.0)
      rails (~> 7.0)
      ros-apartment (>= 2.9.0)

PATH
  remote: engines/commercial/flag_inappropriate_content
  specs:
    flag_inappropriate_content (0.1.0)
      active_model_serializers (~> 0.10.7)
      email_campaigns
      moderation
      pundit (~> 2.0)
      rails (~> 7.0)
      ros-apartment (>= 2.9.0)

PATH
  remote: engines/commercial/google_tag_manager
  specs:
    google_tag_manager (0.1.0)
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_auth0
  specs:
    id_auth0 (0.1.0)
      omniauth-auth0 (~> 2.0)
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_bogus
  specs:
    id_bogus (0.1.0)
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_bosa_fas
  specs:
    id_bosa_fas (0.1.0)
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_clave_unica
  specs:
    id_clave_unica (0.1.0)
      id_id_card_lookup
      omniauth_openid_connect
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_cow
  specs:
    id_cow (0.1.0)
      rails (~> 7.0)
      savon (>= 2.12, < 2.15)

PATH
  remote: engines/commercial/id_criipto
  specs:
    id_criipto (0.1.0)
      omniauth_openid_connect (~> 0.7.1)
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_fake_sso
  specs:
    id_fake_sso (0.1.0)
      omniauth_openid_connect
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_franceconnect
  specs:
    id_franceconnect (0.1.0)
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_gent_rrn
  specs:
    id_gent_rrn (0.1.0)
      httparty
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_hoplr
  specs:
    id_hoplr (0.1.0)
      omniauth_openid_connect
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_id_austria
  specs:
    id_id_austria (1.0.0)
      omniauth_openid_connect (~> 0.7.1)
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_id_card_lookup
  specs:
    id_id_card_lookup (0.1.0)
      admin_api
      rails (~> 7.0)
      savon (>= 2.12, < 2.15)

PATH
  remote: engines/commercial/id_keycloak
  specs:
    id_keycloak (0.1.0)
      omniauth_openid_connect (~> 0.7.1)
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_nemlog_in
  specs:
    id_nemlog_in (1.0.0)
      omniauth-saml (~> 2.2.0)
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_oostende_rrn
  specs:
    id_oostende_rrn (0.1.0)
      httparty
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_twoday
  specs:
    id_twoday (1.0.0)
      omniauth_openid_connect (~> 0.7.1)
      rails (~> 7.0)

PATH
  remote: engines/commercial/id_vienna_saml
  specs:
    id_vienna_saml (1.0.0)
      omniauth-saml (~> 2.2.0)
      rails (~> 7.0)

PATH
  remote: engines/commercial/idea_assignment
  specs:
    idea_assignment (0.1.0)
      email_campaigns
      rails (~> 7.0)

PATH
  remote: engines/commercial/idea_custom_fields
  specs:
    idea_custom_fields (0.1.0)
      rails (~> 7.0)

PATH
  remote: engines/commercial/impact_tracking
  specs:
    impact_tracking (0.1.0)
      crawler_detect (~> 1.2.1)
      rails (~> 7.0)

PATH
  remote: engines/commercial/machine_translations
  specs:
    machine_translations (0.1.0)
      active_model_serializers (~> 0.10.7)
      easy_translate
      pundit (~> 2.0)
      rails (~> 7.0)
      ros-apartment (>= 2.9.0)

PATH
  remote: engines/commercial/matomo
  specs:
    matomo (0.1.0)
      rails (~> 7.0)

PATH
  remote: engines/commercial/moderation
  specs:
    moderation (0.1.0)
      active_model_serializers (~> 0.10.7)
      pundit (~> 2.0)
      rails (~> 7.0)

PATH
  remote: engines/commercial/multi_tenancy
  specs:
    multi_tenancy (0.1.0)
      rails (~> 7.0)
      ros-apartment (>= 2.9.0)

PATH
  remote: engines/commercial/posthog_integration
  specs:
    posthog_integration (0.1.0)
      http (~> 5.1)
      posthog-ruby (~> 2.1)
      rails (~> 7.0)

PATH
  remote: engines/commercial/public_api
  specs:
    public_api (0.1.0)
      active_model_serializers (~> 0.10.7)
      kaminari (~> 1.2)
      pundit (~> 2.0)
      rails (~> 7.0)
      ros-apartment (>= 2.9.0)

PATH
  remote: engines/commercial/remove_vendor_branding
  specs:
    remove_vendor_branding (0.1.0)

PATH
  remote: engines/commercial/report_builder
  specs:
    report_builder (0.1.0)
      analytics
      content_builder
      rails (~> 7.0)
      user_custom_fields

PATH
  remote: engines/commercial/smart_groups
  specs:
    smart_groups (0.1.0)
      rails (~> 7.0)

PATH
  remote: engines/commercial/user_custom_fields
  specs:
    user_custom_fields (0.1.0)
      active_model_serializers (~> 0.10.7)
      pundit (~> 2.0)
      rails (~> 7.0)
      ros-apartment (>= 2.9.0)

PATH
  remote: engines/free/document_annotation
  specs:
    document_annotation (0.1.0)
      active_model_serializers (~> 0.10.7)
      httparty
      kaminari (~> 1.2)
      pundit (~> 2.0)
      rails (~> 7.0)

PATH
  remote: engines/free/email_campaigns
  specs:
    email_campaigns (0.1.0)
      active_model_serializers (~> 0.10.7)
      ice_cube
      kaminari (~> 1.2)
      liquid (>= 4, < 6)
      mailgun-ruby (~> 1.2.0)
      pundit (~> 2.0)
      rails (~> 7.0)

PATH
  remote: engines/free/frontend
  specs:
    frontend (0.1.0)
      rails (~> 7.0)

PATH
  remote: engines/free/onboarding
  specs:
    onboarding (0.1.0)
      active_model_serializers (~> 0.10.7)
      kaminari (~> 1.2)
      pundit (~> 2.0)
      rails (~> 7.0)

PATH
  remote: engines/free/polls
  specs:
    polls (0.1.0)
      acts_as_list (~> 1.0)
      kaminari (~> 1.2)
      pundit (~> 2.0)
      rails (~> 7.0)

PATH
  remote: engines/free/seo
  specs:
    seo (0.1.0)
      aws-sdk-route53
      google-api-client
      httparty
      koala
      rails (~> 7.0)

PATH
  remote: engines/free/surveys
  specs:
    surveys (0.1.0)
      active_model_serializers (~> 0.10.7)
      httparty
      kaminari (~> 1.2)
      pundit (~> 2.0)
      rails (~> 7.0)

PATH
  remote: engines/free/volunteering
  specs:
    volunteering (0.1.0)
      acts_as_list (~> 1.0)
      kaminari (~> 1.2)
      pundit (~> 2.0)
      rails (~> 7.0)

GEM
  remote: https://rubygems.org/
  specs:
    Ascii85 (2.0.1)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actionpack-action_caching (1.2.2)
      actionpack (>= 4.0.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_model_serializers (0.10.15)
      actionpack (>= 4.1)
      activemodel (>= 4.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-import (1.7.0)
      activerecord (>= 4.2)
    activerecord-postgis-adapter (9.0.2)
      activerecord (~> 7.1.0)
      rgeo-activerecord (~> 7.0.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    acts_as_list (1.2.4)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    aes_key_wrap (1.1.0)
    afm (0.2.2)
    akami (1.3.1)
      gyoku (>= 0.4.0)
      nokogiri
    amq-protocol (2.3.4)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    api-pagination (6.0.0)
    ast (2.4.2)
    attr_required (1.0.1)
    awesome_nested_set (3.6.0)
      activerecord (>= 4.0.0, < 7.2)
    aws-eventstream (1.3.2)
    aws-partitions (1.1094.0)
    aws-sdk-bedrockruntime (1.37.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.222.3)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.99.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-route53 (1.76.0)
      aws-sdk-core (~> 3, >= 3.177.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.184.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    axlsx (3.0.0.pre)
      htmlentities (~> 4.3, >= 4.3.4)
      mimemagic (~> 0.3)
      nokogiri (~> 1.8, >= 1.8.2)
      rubyzip (~> 1.2, >= 1.2.1)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindata (2.5.0)
    binding_of_caller (1.0.0)
      debug_inspector (>= 0.0.1)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    browser (6.1.0)
    builder (3.3.0)
    bullet (7.1.6)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    bundler-audit (0.9.2)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    bunny (2.24.0)
      amq-protocol (~> 2.3)
      sorted_set (~> 1, >= 1.0.2)
    byebug (11.1.3)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    carrierwave (3.0.7)
      activemodel (>= 6.0.0)
      activesupport (>= 6.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      ssrf_filter (~> 1.0)
    carrierwave-base64 (2.11.0)
      carrierwave (>= 2.2.1)
      marcel (~> 1.0.0)
      mime-types (~> 3.0)
    case_transform (0.2)
      activesupport
    cloudfront-rails (0.4.0)
      railties (> 4.0)
    coderay (1.1.3)
    combine_pdf (1.0.26)
      matrix
      ruby-rc4 (>= 0.1.5)
    concurrent-ruby (1.2.3)
    connection_pool (2.5.3)
    counter_culture (3.5.3)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    crawler_detect (1.2.6)
      qonfig (>= 0.24)
    css_parser (1.14.0)
      addressable
    csv (3.3.0)
    dalli (3.2.8)
    database_cleaner (2.0.2)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.1.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    debug_inspector (1.1.0)
    declarative (0.0.20)
    diff-lcs (1.5.1)
    distribution (0.8.0)
    docile (1.4.0)
    domain_name (0.6.20240107)
    drb (2.2.0)
      ruby2_keywords
    easy_translate (0.5.1)
      thread
      thread_safe
    erubi (1.13.1)
    event_stream_parser (1.0.0)
    excon (0.111.0)
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.12.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-jwt (0.1.0)
      faraday (~> 2.0)
      json-jwt (~> 1.16)
    faraday-multipart (1.1.0)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    faraday-retry (2.2.1)
      faraday (~> 2.0)
    ffi (1.17.2-aarch64-linux-gnu)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    ffi-compiler (1.3.2)
      ffi (>= 1.15.5)
      rake
    fiber-storage (1.0.0)
    fog-aws (3.24.0)
      fog-core (~> 2.1)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
    fog-core (2.4.0)
      builder
      excon (~> 0.71)
      formatador (>= 0.2, < 2.0)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.4)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (1.1.0)
    forwardable (1.3.3)
    gapic-common (0.21.1)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-protobuf (~> 3.18)
      googleapis-common-protos (>= 1.4.0, < 2.a)
      googleapis-common-protos-types (>= 1.11.0, < 2.a)
      googleauth (~> 1.9)
      grpc (~> 1.59)
    gems (1.2.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-api-client (0.53.0)
      google-apis-core (~> 0.1)
      google-apis-generator (~> 0.1)
    google-apis-core (0.15.1)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-apis-discovery_v1 (0.8.0)
      google-apis-core (>= 0.4, < 2.a)
    google-apis-generator (0.4.1)
      activesupport (>= 5.0)
      gems (~> 1.2)
      google-apis-core (>= 0.4, < 2.a)
      google-apis-discovery_v1 (~> 0.5)
      thor (>= 0.20, < 2.a)
    google-cloud-core (1.7.0)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-document_ai (1.4.0)
      google-cloud-core (~> 1.6)
      google-cloud-document_ai-v1 (>= 0.17, < 2.a)
      google-cloud-document_ai-v1beta3 (>= 0.30, < 2.a)
    google-cloud-document_ai-v1 (0.21.0)
      gapic-common (>= 0.21.1, < 2.a)
      google-cloud-errors (~> 1.0)
      google-cloud-location (>= 0.7, < 2.a)
    google-cloud-document_ai-v1beta3 (0.35.0)
      gapic-common (>= 0.21.1, < 2.a)
      google-cloud-errors (~> 1.0)
      google-cloud-location (>= 0.7, < 2.a)
    google-cloud-env (2.1.1)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.4.0)
    google-cloud-location (0.8.0)
      gapic-common (>= 0.21.1, < 2.a)
      google-cloud-errors (~> 1.0)
    google-protobuf (3.25.5-aarch64-linux)
    google-protobuf (3.25.5-arm64-darwin)
    google-protobuf (3.25.5-x86_64-darwin)
    google-protobuf (3.25.5-x86_64-linux)
    googleapis-common-protos (1.5.0)
      google-protobuf (~> 3.18)
      googleapis-common-protos-types (~> 1.7)
      grpc (~> 1.41)
    googleapis-common-protos-types (1.14.0)
      google-protobuf (~> 3.18)
    googleauth (1.11.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    graphql (2.4.13)
      base64
      fiber-storage
      logger
    groupdate (6.5.1)
      activesupport (>= 7)
    grpc (1.63.0-aarch64-linux)
      google-protobuf (~> 3.25)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.63.0-arm64-darwin)
      google-protobuf (~> 3.25)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.63.0-x86_64-darwin)
      google-protobuf (~> 3.25)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.63.0-x86_64-linux)
      google-protobuf (~> 3.25)
      googleapis-common-protos-types (~> 1.0)
    gyoku (1.4.0)
      builder (>= 2.1.2)
      rexml (~> 3.0)
    hashdiff (1.1.0)
    hashery (2.1.2)
    hashie (5.0.0)
    htmlentities (4.3.4)
    http (5.2.0)
      addressable (~> 2.8)
      base64 (~> 0.1)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.5.0)
    http-accept (1.7.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    httpi (3.0.1)
      rack
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    icalendar (2.10.1)
      ice_cube (~> 0.16)
    ice_cube (0.17.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    interactor (3.1.2)
    interactor-rails (2.3.0)
      interactor (~> 3.0)
      railties (>= 7.0)
    interception (0.5)
    intercom (4.2.1)
    io-console (0.8.0)
    irb (1.15.1)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.2)
    json (2.9.1)
    json-jwt (1.16.6)
      activesupport (>= 4.2)
      aes_key_wrap
      base64
      bindata
      faraday (~> 2.0)
      faraday-follow_redirects
    json-schema (4.3.0)
      addressable (>= 2.8)
    jsonapi-renderer (0.2.2)
    jsonapi-serializer (2.2.0)
      activesupport (>= 4.2)
    jwt (2.8.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    koala (3.6.0)
      addressable
      base64
      faraday
      faraday-multipart
      json (>= 1.8)
      rexml
    language_server-protocol (********)
    license_finder (7.2.1)
      bundler
      csv (~> 3.2)
      rubyzip (>= 1, < 3)
      thor (~> 1.2)
      tomlrb (>= 1.3, < 2.1)
      with_env (= 1.1.0)
      xml-simple (~> 1.1.9)
    liquid (5.5.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    llhttp-ffi (0.5.0)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    logger (1.7.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    mailgun-ruby (1.2.16)
      rest-client (>= 2.0.2)
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.1.0)
    mime-types (3.6.2)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2025.0429)
    mimemagic (0.4.3)
      nokogiri (~> 1)
      rake
    mini_magick (4.13.2)
    mini_mime (1.1.5)
    minitest (5.25.5)
    mjml-rails (4.11.0)
    mrml (1.7.0-aarch64-linux)
    mrml (1.7.0-arm64-darwin)
    mrml (1.7.0-x86_64-darwin)
    mrml (1.7.0-x86_64-linux)
    msgpack (1.7.2)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mustache (1.1.1)
    mutex_m (0.2.0)
    nanoid (2.0.0)
    neighbor (0.5.0)
      activerecord (>= 7)
    net-http (0.6.0)
      uri
    net-imap (0.5.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    netrc (0.11.0)
    nio4r (2.7.4)
    nkf (0.1.3)
    nokogiri (1.18.9-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    nori (2.6.0)
    oauth2 (1.4.11)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_json (~> 1.3)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
    okcomputer (1.18.5)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-auth0 (2.4.1)
      omniauth-oauth2 (~> 1.5)
    omniauth-facebook (10.0.0)
      bigdecimal
      omniauth-oauth2 (>= 1.2, < 3)
    omniauth-google-oauth2 (1.0.1)
      jwt (>= 2.0)
      oauth2 (~> 1.1)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.7.1)
    omniauth-oauth2 (1.7.3)
      oauth2 (>= 1.4, < 3)
      omniauth (>= 1.9, < 3)
    omniauth-rails_csrf_protection (0.1.2)
      actionpack (>= 4.2)
      omniauth (>= 1.3.1)
    omniauth-saml (2.2.3)
      omniauth (~> 2.1)
      ruby-saml (~> 1.18)
    omniauth_openid_connect (0.7.1)
      omniauth (>= 1.9, < 3)
      openid_connect (~> 2.2)
    openid_connect (2.2.0)
      activemodel
      attr_required (>= 1.0.0)
      faraday (~> 2.0)
      faraday-follow_redirects
      json-jwt (>= 1.16)
      net-smtp
      rack-oauth2 (~> 2.2)
      swd (~> 2.0)
      tzinfo
      validate_email
      validate_url
      webfinger (~> 2.0)
    order_as_specified (1.7)
      activerecord (>= 5.0.0)
    os (1.1.4)
    parallel (1.26.3)
    parser (3.3.6.0)
      ast (~> 2.4.1)
      racc
    pdf-core (0.10.0)
    pdf-reader (2.14.1)
      Ascii85 (>= 1.0, < 3.0, != 2.0.0)
      afm (~> 0.2.1)
      hashery (~> 2.0)
      ruby-rc4
      ttfunk
    pg (1.5.6)
    pg_search (2.3.6)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    positioning (0.4.6)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    posthog-ruby (2.5.0)
      concurrent-ruby (~> 1)
    pp (0.6.2)
      prettyprint
    prawn (2.5.0)
      matrix (~> 0.4)
      pdf-core (~> 0.10.0)
      ttfunk (~> 1.8)
    prawn-grouping (0.2.0)
      prawn (>= 2.0.0)
    premailer (1.19.0)
      addressable
      css_parser (>= 1.12.0)
      htmlentities (>= 4.0.0)
    premailer-rails (1.12.0)
      actionmailer (>= 3)
      net-smtp
      premailer (~> 1.7, >= 1.7.9)
    prettyprint (0.2.0)
    prime (0.1.3)
      forwardable
      singleton
    proc_to_ast (0.1.0)
      coderay
      parser
      unparser
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    pry-rails (0.3.11)
      pry (>= 0.13.0)
    pry-rescue (1.6.0)
      interception (>= 0.5)
      pry (>= 0.12.0)
    psych (5.2.3)
      date
      stringio
    public_suffix (5.1.1)
    puma (6.4.3)
      nio4r (~> 2.0)
    pundit (2.5.0)
      activesupport (>= 3.0.0)
    qonfig (0.30.0)
      base64 (>= 0.2)
    que (2.3.0)
    racc (1.8.1)
    rack (2.2.14)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (2.0.0)
      rack (>= 2.0.0)
    rack-mini-profiler (3.3.1)
      rack (>= 1.2.0)
    rack-oauth2 (2.2.0)
      activesupport
      attr_required
      faraday (~> 2.0)
      faraday-follow_redirects
      json-jwt (>= 1.11.0)
      rack (>= 2.1.0)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-i18n (7.0.9)
      i18n (>= 0.7, < 2)
      railties (>= 6.0.0, < 8)
    rails_semantic_logger (4.14.0)
      rack
      railties (>= 5.1)
      semantic_logger (~> 4.13)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rbtree (0.4.6)
    rdoc (6.11.0)
      psych (>= 4.0.0)
    redcarpet (3.6.1)
    regexp_parser (2.10.0)
    reline (0.6.0)
      io-console (~> 0.5)
    representable (3.1.1)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    rexml (3.4.1)
    rgeo (3.0.1)
    rgeo-activerecord (7.0.1)
      activerecord (>= 5.0)
      rgeo (>= 1.0.0)
    rgeo-geojson (2.2.0)
      multi_json (~> 1.15)
      rgeo (>= 1.0.0)
    ricecream (0.2.1)
    rinku (2.0.6)
    ros-apartment (3.1.0)
      activerecord (>= 6.1.0, < 7.2)
      parallel (< 2.0)
      public_suffix (>= 2.0.5, < 6.0)
      rack (>= 1.3.6, < 4.0)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.2)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-html-matchers (0.10.0)
      nokogiri (~> 1)
      rspec (>= 3.0.0.a)
    rspec-its (2.0.0)
      rspec-core (>= 3.13.0)
      rspec-expectations (>= 3.13.0)
    rspec-mocks (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-parameterized (1.0.0)
      rspec-parameterized-core (< 2)
      rspec-parameterized-table_syntax (< 2)
    rspec-parameterized-core (1.0.0)
      parser
      proc_to_ast
      rspec (>= 2.13, < 4)
      unparser
    rspec-parameterized-table_syntax (1.0.0)
      binding_of_caller
      rspec-parameterized-core (< 2)
    rspec-rails (6.1.5)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-sqlimit (0.0.6)
      activerecord (>= 4.2.0, < 8)
      rspec (~> 3.0)
    rspec-support (3.13.2)
    rspec_api_documentation (6.1.0)
      activesupport (>= 3.0.0)
      mustache (~> 1.0, >= 0.99.4)
      rspec (~> 3.0)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rubocop (1.69.2)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.36.2, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.37.0)
      parser (>= 3.3.1.0)
    rubocop-capybara (2.20.0)
      rubocop (~> 1.41)
    rubocop-factory_bot (2.25.1)
      rubocop (~> 1.41)
    rubocop-performance (1.23.1)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails (2.28.0)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.52.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rspec (2.29.2)
      rubocop (~> 1.40)
      rubocop-capybara (~> 2.17)
      rubocop-factory_bot (~> 2.22)
      rubocop-rspec_rails (~> 2.28)
    rubocop-rspec_rails (2.28.3)
      rubocop (~> 1.40)
    ruby-openai (7.1.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-progressbar (1.13.0)
    ruby-rc4 (0.1.5)
    ruby-saml (1.18.1)
      nokogiri (>= 1.13.10)
      rexml
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyXL (3.4.27)
      nokogiri (>= 1.10.8)
      rubyzip (>= 1.3.0)
    rubyzip (1.3.0)
    saharspec (0.0.10)
      ruby2_keywords
    savon (2.14.0)
      akami (~> 1.2)
      builder (>= 2.1.2)
      gyoku (~> 1.2)
      httpi (>= 2.4.5)
      mail (~> 2.5)
      nokogiri (>= 1.8.1)
      nori (~> 2.4)
      wasabi (~> 3.4)
    scenic (1.8.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    scientist (1.6.4)
    securerandom (0.4.1)
    semantic_logger (4.15.0)
      concurrent-ruby (~> 1.0)
    sentry-rails (5.21.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.21.0)
    sentry-ruby (5.21.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    set (1.1.2)
    shoulda-matchers (6.2.0)
      activesupport (>= 5.2.0)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simple_segment (1.5.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov-rcov (0.3.7)
      simplecov (>= 0.4.1)
    simplecov_json_formatter (0.1.4)
    singleton (0.3.0)
    sorted_set (1.0.3)
      rbtree
      set (~> 1.0)
    spring (4.2.1)
    spring-commands-rspec (1.0.4)
      spring (>= 0.9.1)
    spring-watcher-listen (2.1.0)
      listen (>= 2.7, < 4.0)
      spring (>= 4)
    ssrf_filter (1.2.0)
    stackprof (0.2.26)
    stringio (3.1.2)
    swd (2.0.2)
      activesupport (>= 3)
      attr_required (>= 0.0.5)
      faraday (~> 2.0)
      faraday-follow_redirects
    test-prof (1.3.3)
    thor (1.4.0)
    thread (0.2.2)
    thread_safe (0.3.6)
    tiktoken_ruby (0.0.9-aarch64-linux)
    tiktoken_ruby (0.0.9-arm64-darwin)
    tiktoken_ruby (0.0.9-x86_64-darwin)
    tiktoken_ruby (0.0.9-x86_64-linux)
    timeout (0.4.3)
    tomlrb (2.0.3)
    trailblazer-option (0.1.2)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uniform_notifier (1.16.0)
    unparser (0.6.7)
      diff-lcs (~> 1.3)
      parser (>= 3.2.0)
    uri (1.0.3)
    validate_email (0.1.6)
      activemodel (>= 3.0)
      mail (>= 2.2.5)
    validate_url (1.0.15)
      activemodel (>= 3.0.0)
      public_suffix
    vcr (6.2.0)
    wasabi (3.8.0)
      addressable
      httpi (~> 3.0)
      nokogiri (>= 1.4.2)
    webfinger (2.1.2)
      activesupport
      faraday (~> 2.0)
      faraday-follow_redirects
    webmock (3.23.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    with_env (1.1.0)
    xml-simple (1.1.9)
      rexml
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.1)

PLATFORMS
  aarch64-linux
  arm64-darwin-21
  arm64-darwin-24
  x86_64-darwin-21
  x86_64-darwin-23
  x86_64-linux

DEPENDENCIES
  active_model_serializers (~> 0.10.15)
  activerecord-import (~> 1.7)
  activerecord-postgis-adapter (~> 9.0.2)
  acts_as_list (~> 1.2)
  admin_api!
  aggressive_caching!
  analysis!
  analytics!
  annotate
  api-pagination (~> 6.0.0)
  awesome_nested_set (~> 3.6.0)
  aws-sdk-bedrockruntime (~> 1)
  aws-sdk-s3 (~> 1)
  axlsx (= 3.0.0.pre)
  bcrypt (~> 3.1.20)
  bootsnap (~> 1)
  browser (~> 6.1)
  bulk_import_ideas!
  bullet
  bundler-audit
  bunny (>= 2.7.2)
  byebug
  capybara (~> 3.40)
  carrierwave (~> 3.0.7)
  carrierwave-base64 (~> 2.11)
  cloudfront-rails (~> 0.4)
  combine_pdf (~> 1.0.26)
  content_builder!
  counter_culture (~> 3.5)
  csv (~> 3.3.0)
  custom_maps!
  dalli (~> 3.2.8)
  database_cleaner (~> 2.0.2)
  document_annotation!
  drb
  email_campaigns!
  factory_bot_rails
  faker
  faraday
  faraday-jwt (~> 0.1.0)
  faraday-multipart
  flag_inappropriate_content!
  fog-aws (~> 3.24)
  frontend!
  google-cloud-document_ai (~> 1.4)
  google_tag_manager!
  groupdate (~> 6.5)
  icalendar (~> 2.10)
  ice_cube (~> 0.17)
  id_auth0!
  id_bogus!
  id_bosa_fas!
  id_clave_unica!
  id_cow!
  id_criipto!
  id_fake_sso!
  id_franceconnect!
  id_gent_rrn!
  id_hoplr!
  id_id_austria!
  id_id_card_lookup!
  id_keycloak!
  id_nemlog_in!
  id_oostende_rrn!
  id_twoday!
  id_vienna_saml!
  idea_assignment!
  idea_custom_fields!
  impact_tracking!
  interactor
  interactor-rails
  intercom (~> 4.2)
  json-schema (~> 4.3)
  jsonapi-serializer
  jwt (~> 2.8.1)
  kaminari (~> 1.2)
  license_finder
  liquid (~> 5.5)
  listen (>= 3.0.5, < 4.0)
  machine_translations!
  mailgun-ruby (~> 1.2.16)
  matomo!
  mini_magick (~> 4.13)
  mjml-rails (~> 4.11)
  moderation!
  mrml
  multi_tenancy!
  mutex_m
  neighbor (~> 0.5.0)
  nkf
  nokogiri (~> 1.18.9)
  okcomputer
  omniauth (~> 2.1)
  omniauth-facebook
  omniauth-google-oauth2
  omniauth-rails_csrf_protection
  omniauth-saml (~> 2.2.3)
  omniauth_openid_connect (~> 0.7.1)
  onboarding!
  order_as_specified
  pdf-reader
  pg (~> 1.5.6)
  pg_search (~> 2.3.5)
  polls!
  positioning (~> 0.4.6)
  posthog_integration!
  prawn (~> 2.5)
  prawn-grouping (~> 0.2.0)
  premailer-rails (~> 1.12.0)
  pry
  pry-byebug
  pry-rails
  pry-rescue
  public_api!
  puma (~> 6.4.3)
  pundit (~> 2.5.0)
  que (~> 2.3.0)
  rack-attack (~> 6)
  rack-cors (= 2.0.0)
  rack-mini-profiler
  rails (~> *******)
  rails-i18n (~> 7.0.9)
  rails_semantic_logger
  redcarpet
  remove_vendor_branding!
  report_builder!
  rest-client
  rgeo-geojson
  ricecream (~> 0.2.1)
  rinku (~> 2)
  ros-apartment (~> 3.1.0)
  rspec-html-matchers (~> 0.10)
  rspec-its
  rspec-parameterized
  rspec-rails (~> 6.1.2)
  rspec-sqlimit
  rspec_api_documentation
  rspec_junit_formatter
  rubocop
  rubocop-ast
  rubocop-performance
  rubocop-rails
  rubocop-rspec
  rubyXL (~> 3.4.27)
  rubyzip (~> 1.3.0)
  saharspec
  scenic
  scientist (~> 1.6.4)
  sentry-rails
  sentry-ruby
  seo!
  shoulda-matchers (~> 6.2.0)
  simple_segment (~> 1.5)
  simplecov
  simplecov-rcov
  smart_groups!
  spring
  spring-commands-rspec
  spring-watcher-listen (~> 2.1)
  stackprof (~> 0.2.26)
  surveys!
  test-prof (~> 1.3)
  user_custom_fields!
  vcr (~> 6.2)
  volunteering!
  webmock (~> 3.23)

RUBY VERSION
   ruby 3.3.4p94

BUNDLED WITH
   2.5.11

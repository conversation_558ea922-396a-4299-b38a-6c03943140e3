# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Files::LegacyFileMigrationJob do
  describe '#perform' do
    context 'when migrating a specific container' do
      let(:project) { create(:project) }
      let!(:project_file) { create(:project_file, project: project, name: 'test.pdf') }

      it 'migrates files for the specified container' do
        expect {
          described_class.perform_now(container_type: 'Project', container_id: project.id)
        }.to change(Files::File, :count).by(1)
          .and change(Files::FileAttachment, :count).by(1)
          .and change(Files::FilesProject, :count).by(1)
      end

      it 'logs success message' do
        allow(Rails.logger).to receive(:info)
        
        described_class.perform_now(container_type: 'Project', container_id: project.id)
        
        expect(Rails.logger).to have_received(:info).with(match(/Starting migration for Project##{project.id}/))
        expect(Rails.logger).to have_received(:info).with(match(/Successfully migrated 1 files for Project##{project.id}/))
      end

      context 'when container does not exist' do
        it 'logs error and does not raise exception' do
          allow(Rails.logger).to receive(:error)
          
          expect {
            described_class.perform_now(container_type: 'Project', container_id: 'nonexistent')
          }.not_to raise_error
          
          expect(Rails.logger).to have_received(:error).with(match(/Container not found: Project#nonexistent/))
        end
      end

      context 'when container type is invalid' do
        it 'logs error and does not raise exception' do
          allow(Rails.logger).to receive(:error)
          
          expect {
            described_class.perform_now(container_type: 'InvalidType', container_id: project.id)
          }.not_to raise_error
          
          expect(Rails.logger).to have_received(:error).with(match(/Invalid container type: InvalidType/))
        end
      end

      context 'when migration fails' do
        before do
          allow_any_instance_of(Files::LegacyFileMigrationService).to receive(:migrate_container)
            .and_return({ success: false, error: 'Migration failed' })
        end

        it 'raises an error to trigger job retry' do
          expect {
            described_class.perform_now(container_type: 'Project', container_id: project.id)
          }.to raise_error(StandardError, /Migration failed for Project##{project.id}: Migration failed/)
        end
      end
    end

    context 'when migrating all containers' do
      let!(:project) { create(:project) }
      let!(:idea) { create(:idea, project: project) }
      let!(:project_file) { create(:project_file, project: project, name: 'project.pdf') }
      let!(:idea_file) { create(:idea_file, idea: idea, name: 'idea.pdf') }

      it 'migrates all legacy files' do
        expect {
          described_class.perform_now
        }.to change(Files::File, :count).by(2)
          .and change(Files::FileAttachment, :count).by(2)
          .and change(Files::FilesProject, :count).by(2)
      end

      it 'logs migration progress' do
        allow(Rails.logger).to receive(:info)
        
        described_class.perform_now
        
        expect(Rails.logger).to have_received(:info).with('[LegacyFileMigrationJob] Starting migration of all legacy files')
        expect(Rails.logger).to have_received(:info).with('[LegacyFileMigrationJob] Migration completed')
      end

      context 'when migration has errors' do
        before do
          allow_any_instance_of(Files::LegacyFileMigrationService).to receive(:migrate_all)
            .and_return({
                          containers_processed: 1,
                          files_migrated: 1,
                          files_skipped: 0,
                          containers_failed: 1,
                          containers_with_errors: [{ container_type: 'Project', container_id: project.id, error: 'Test error' }]
            })
        end

        it 'raises an error to indicate migration issues' do
          expect {
            described_class.perform_now
          }.to raise_error(StandardError, /Migration completed with 1 errors/)
        end
      end
    end
  end

  describe 'job configuration' do
    it 'is configured with correct queue' do
      expect(described_class.queue_name).to eq 'default'
    end

    it 'has retry configuration' do
      # The job should retry on StandardError with exponential backoff
      expect(described_class.retry_on_exceptions).to include(StandardError)
    end
  end

  describe 'private methods' do
    let(:job) { described_class.new }

    describe '#find_container' do
      let(:project) { create(:project) }

      it 'finds existing container' do
        container = job.send(:find_container, 'Project', project.id)
        expect(container).to eq project
      end

      it 'returns nil for non-existent container' do
        container = job.send(:find_container, 'Project', 'nonexistent')
        expect(container).to be_nil
      end

      it 'returns nil for invalid container type' do
        container = job.send(:find_container, 'InvalidType', project.id)
        expect(container).to be_nil
      end
    end
  end

  describe 'integration with service' do
    let(:project) { create(:project) }
    let!(:project_file) { create(:project_file, project: project, name: 'integration.pdf', ordering: 5) }

    it 'preserves all file attributes through the job' do
      described_class.perform_now(container_type: 'Project', container_id: project.id)

      files_file = Files::File.find_by(name: 'integration.pdf')
      expect(files_file).to be_present
      expect(files_file.category).to eq 'other'

      attachment = Files::FileAttachment.find_by(attachable: project)
      expect(attachment.file).to eq files_file
      expect(attachment.position).to eq 5

      files_project = Files::FilesProject.find_by(file: files_file, project: project)
      expect(files_project).to be_present
    end

    it 'is idempotent when run multiple times' do
      # Run the job twice
      described_class.perform_now(container_type: 'Project', container_id: project.id)
      described_class.perform_now(container_type: 'Project', container_id: project.id)

      # Should only have one set of migrated files
      expect(Files::File.count).to eq 1
      expect(Files::FileAttachment.count).to eq 1
      expect(Files::FilesProject.count).to eq 1
    end
  end
end

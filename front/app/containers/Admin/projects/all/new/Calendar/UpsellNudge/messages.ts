import { defineMessages } from 'react-intl';

export default defineMessages({
  upsellTitle: {
    id: 'app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle',
    defaultMessage: 'Understand what is happening and when',
  },
  upsellDescription: {
    id: 'app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2',
    defaultMessage:
      'Get a visual overview of your project timelines in our calendar view. Quickly identify which projects and phases are starting or ending soon and require action.',
  },
  featureNotIncluded: {
    id: 'app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded',
    defaultMessage:
      'This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.',
  },
  enableCalendarView: {
    id: 'app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView',
    defaultMessage: 'Enable calendar view',
  },
  learnMore: {
    id: 'app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore',
    defaultMessage: 'Learn more',
  },
  timelineSupportArticle: {
    id: 'app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle',
    defaultMessage:
      'https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9',
  },
});

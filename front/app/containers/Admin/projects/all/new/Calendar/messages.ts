import { defineMessages } from 'react-intl';

export default defineMessages({
  project: {
    id: 'app.containers.Admin.projects.all.new.timeline.project',
    defaultMessage: 'Project',
  },
  noEndDay: {
    id: 'app.containers.Admin.projects.all.new.timeline.noEndDay',
    defaultMessage: 'Project has no end date',
  },
  failedToLoadTimelineError: {
    id: 'app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError',
    defaultMessage: 'Failed to load timeline.',
  },
});

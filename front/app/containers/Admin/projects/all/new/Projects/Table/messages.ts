import { defineMessages } from 'react-intl';

export default defineMessages({
  project: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.project',
    defaultMessage: 'Project',
  },
  phase: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.phase',
    defaultMessage: 'Phase',
  },
  manager: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.manager',
    defaultMessage: 'Manager',
  },
  start: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.start',
    defaultMessage: 'Start',
  },
  end: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.end',
    defaultMessage: 'End',
  },
  status: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.status',
    defaultMessage: 'Status',
  },
  visibility: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.visibility',
    defaultMessage: 'Visibility',
  },
  preLaunch: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.preLaunch',
    defaultMessage: 'Pre-launch',
  },
  ended: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.ended',
    defaultMessage: 'Ended',
  },
  endsToday: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.endsToday',
    defaultMessage: 'Ends today',
  },
  daysLeft: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.daysLeft',
    defaultMessage: '{days}d left',
  },
  monthsLeft: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.monthsLeft',
    defaultMessage: '{months}mo left',
  },
  yearsLeft: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.yearsLeft',
    defaultMessage: '{years}y left',
  },
  daysToStart: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.daysToStart',
    defaultMessage: '{days}d to start',
  },
  monthsToStart: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.monthsToStart',
    defaultMessage: '{months}mo to start',
  },
  yearsToStart: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.yearsToStart',
    defaultMessage: '{years}y to start',
  },
  currentPhase: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.currentPhase',
    defaultMessage: 'Current phase:',
  },
  nextPhase: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.nextPhase',
    defaultMessage: 'Next phase:',
  },
  statusColon: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.statusColon',
    defaultMessage: 'Status: ',
  },
  visibilityColon: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.visibilityColon',
    defaultMessage: 'Visibility: ',
  },
  xGroups: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.xGroups',
    defaultMessage: '{numberOfGroups} groups',
  },
  hidden: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.hidden',
    defaultMessage: 'Hidden',
  },
  public: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.public',
    defaultMessage: 'Public',
  },
  discoverability: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.discoverability',
    defaultMessage: 'Discoverability:',
  },
  thisColumnUsesCache: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache',
    defaultMessage:
      'This column uses cached participant data. To see the latest numbers, check the "Participants" tab of the project.',
  },
});

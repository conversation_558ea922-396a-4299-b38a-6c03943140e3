import { defineMessages } from 'react-intl';

export default defineMessages({
  modalTitle: {
    id: 'app.sessionRecording.modalTitle',
    defaultMessage: 'Help us improve this website',
  },
  modalDescription1: {
    id: 'app.sessionRecording.modalDescription1',
    defaultMessage:
      'In order to better understand our users, we randomly ask a small percentage of visitors to track their browsing session in detail.',
  },
  modalDescription2: {
    id: 'app.sessionRecording.modalDescription2',
    defaultMessage:
      'The sole purpose of the recorded data is to improve the website. None of your data will be shared with a 3rd party. Any sensitive information you enter will be filtered.',
  },
  modalDescriptionFaq: {
    id: 'app.sessionRecording.modalDescriptionFaq',
    defaultMessage: 'FAQ here.',
  },
  modalDescription3: {
    id: 'app.sessionRecording.modalDescription3',
    defaultMessage: 'Do you accept?',
  },
  accept: {
    id: 'app.sessionRecording.accept',
    defaultMessage: 'Yes, I accept',
  },
  reject: {
    id: 'app.sessionRecording.reject',
    defaultMessage: 'No, I reject',
  },
});

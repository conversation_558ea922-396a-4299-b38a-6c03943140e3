{"EmailSettingsPage.emailSettings": "Email settings", "EmailSettingsPage.initialUnsubscribeError": "Дошло је до проблема при опозиву пријаве за ову кампању, покушајте поново.", "EmailSettingsPage.initialUnsubscribeLoading": "Ваш захтев се обрађује, сачекајте...", "EmailSettingsPage.initialUnsubscribeSuccess": "Успешно сте отказали претплату на {campaignTitle}.", "UI.FormComponents.optional": "опционо", "app.closeIconButton.a11y_buttonActionMessage": "Близу", "app.components.Areas.areaUpdateError": "Дошло је до грешке при чувању ваше области. Молим вас, покушајте поново.", "app.components.Areas.followedArea": "Followed area: {areaTitle}", "app.components.Areas.followedTopic": "Followed topic: {topicTitle}", "app.components.Areas.topicUpdateError": "Дошло је до грешке при чувању ваше теме. Молим вас, покушајте поново.", "app.components.Areas.unfollowedArea": "Unfollowed area: {areaTitle}", "app.components.Areas.unfollowedTopic": "Unfollowed topic: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Цена:", "app.components.AssignBudgetControl.add": "Додати", "app.components.AssignBudgetControl.added": "Додато", "app.components.AssignMultipleVotesControl.addVote": "Додајте глас", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "You have distributed all of your credits.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "You have distributed the maximum number of credits for this option.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "You have distributed all of your points.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "You have distributed the maximum number of points for this option.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "You have distributed all of your tokens.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "You have distributed the maximum number of tokens for this option.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "You have distributed all of your votes.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "You have distributed the maximum number of votes for this option.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Voting is not available, since this phase is not active.", "app.components.AssignMultipleVotesControl.removeVote": "Уклони гласање", "app.components.AssignMultipleVotesControl.select": "Изаберите", "app.components.AssignMultipleVotesControl.votesSubmitted1": "You have already submitted your vote. To modify it, click \"Modify your submission\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "You have already submitted your vote. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {credit} other {credits}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {point} other {points}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {vote} other {votes}}", "app.components.AssignVoteControl.maxVotesReached1": "You have distributed all of your votes.", "app.components.AssignVoteControl.phaseNotActive": "Voting is not available, since this phase is not active.", "app.components.AssignVoteControl.select": "Изаберите", "app.components.AssignVoteControl.selected2": "Одабрано", "app.components.AssignVoteControl.voteForAtLeastOne": "Гласајте за најмање 1 опцију", "app.components.AssignVoteControl.votesSubmitted1": "You have already submitted your vote. To modify it, click \"Modify your submission\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "You have already submitted your vote. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.AuthProviders.continue": "Настави", "app.components.AuthProviders.continueWithAzure": "Наставите са {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Наставите са Фејсбуком", "app.components.AuthProviders.continueWithFakeSSO": "Continue with Fake SSO", "app.components.AuthProviders.continueWithGoogle": "Наставите са Гоогле-ом", "app.components.AuthProviders.continueWithHoplr": "Наставите са Хоплр", "app.components.AuthProviders.continueWithIdAustria": "Continue with ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Наставите са {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Continue with MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Већ постоји налог са овом адресом е-поште.{br}{br}Не можете приступити платформи користећи ФранцеЦоннецт јер се лични подаци не поклапају. Да бисте се пријавили помоћу ФранцеЦоннецт-а, прво ћете морати да промените своје име или презиме на овој платформи како би одговарали вашим званичним подацима.{br}{br}Можете се пријавити као што то обично чините испод.", "app.components.AuthProviders.goToLogIn": "Већ имате налог? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Немате налог? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Пријавите се", "app.components.AuthProviders.logInWithEmail": "Пријавите се путем е-поште", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Морате имати наведену минималну старост или више да бисте били верификовани.", "app.components.AuthProviders.signUp2": "Региструјте се", "app.components.AuthProviders.signUpButtonAltText": "Пријавите се са {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Пријавите се путем е-поште", "app.components.AuthProviders.verificationRequired": "Verification required", "app.components.Author.a11yPostedBy": "Posted by", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 учесник} other {{numberOfParticipants} учесника}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} users", "app.components.AvatarBubbles.participant": "participant", "app.components.AvatarBubbles.participants1": "учесника", "app.components.Comments.cancel": "Поништити, отказати", "app.components.Comments.commentingDisabledInCurrentPhase": "У тренутној фази коментарисање није могуће.", "app.components.Comments.commentingDisabledInactiveProject": "Коментарисање није могуће јер овај пројекат тренутно није активан.", "app.components.Comments.commentingDisabledProject": "Ова врста пројекта не предвиђа коментарисање", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} за коментар.", "app.components.Comments.commentingMaybeNotPermitted": "Молимо {signInLink} да видите које радње се могу предузети.", "app.components.Comments.inputsAssociatedWithProfile": "Подразумевано, ваши поднесци ће бити повезани са вашим профилом, осим ако не изаберете ову опцију.", "app.components.Comments.invisibleTitleComments": "Коментари", "app.components.Comments.leastRecent": "Најмање недавно", "app.components.Comments.likeComment": "Лајкујте овај коментар", "app.components.Comments.mostLiked": "Највише реакција", "app.components.Comments.mostRecent": "Најновијe", "app.components.Comments.official": "званичник", "app.components.Comments.postAnonymously": "Објавите анонимно", "app.components.Comments.replyToComment": "Одговорите на коментар", "app.components.Comments.reportAsSpam": "Пријави као нежељену пошту", "app.components.Comments.seeOriginal": "Види оригинал", "app.components.Comments.seeTranslation": "Види превод", "app.components.Comments.yourComment": "<PERSON><PERSON><PERSON> коментар", "app.components.CommonGroundResults.divisiveDescription": "Statements where people agree and disagree equally:", "app.components.CommonGroundResults.divisiveTitle": "Divisive", "app.components.CommonGroundResults.majorityDescription": "More than 60% voted one way or the other on the following:", "app.components.CommonGroundResults.majorityTitle": "Majority", "app.components.CommonGroundResults.participantLabel": "participant", "app.components.CommonGroundResults.participantsLabel1": "participants", "app.components.CommonGroundResults.statementLabel": "statement", "app.components.CommonGroundResults.statementsLabel1": "statements", "app.components.CommonGroundResults.votesLabe": "vote", "app.components.CommonGroundResults.votesLabel1": "votes", "app.components.CommonGroundStatements.agreeLabel": "Agree", "app.components.CommonGroundStatements.disagreeLabel": "Disagree", "app.components.CommonGroundStatements.noMoreStatements": "There are no statements to respond to right now", "app.components.CommonGroundStatements.noResults": "There are no results to show yet. Please make sure you have participated in the Common Ground phase and check back here after.", "app.components.CommonGroundStatements.unsureLabel": "Unsure", "app.components.CommonGroundTabs.resultsTabLabel": "Results", "app.components.CommonGroundTabs.statementsTabLabel": "Statements", "app.components.CommunityMonitorModal.formError": "Encountered an error.", "app.components.CommunityMonitorModal.surveyDescription2": "This ongoing survey tracks how you feel about governance and public services.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Takes <1 minute} one {Takes 1 minute} other {Takes # minutes}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Е-маил са кодом за потврду је послат на {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Промените своју е-пошту.", "app.components.ConfirmationModal.codeInput": "<PERSON>од", "app.components.ConfirmationModal.confirmationCodeSent": "Нови код је послан", "app.components.ConfirmationModal.didntGetAnEmail": "Нисте примили имејл?", "app.components.ConfirmationModal.foundYourCode": "Пронашли сте код?", "app.components.ConfirmationModal.goBack": "Повратак назад.", "app.components.ConfirmationModal.sendEmailWithCode": "Пошаљите е-пошту са кодом", "app.components.ConfirmationModal.sendNewCode": "Пошаљи нови код.", "app.components.ConfirmationModal.verifyAndContinue": "Потврдите и наставите", "app.components.ConfirmationModal.wrongEmail": "Погрешан емаил?", "app.components.ConsentManager.Banner.accept": "Прихвати", "app.components.ConsentManager.Banner.ariaButtonClose2": "Одбијте политику и затворите банер", "app.components.ConsentManager.Banner.close": "Близу", "app.components.ConsentManager.Banner.mainText": "Ова платформа користи колачиће у складу са нашoм {policyLink}.", "app.components.ConsentManager.Banner.manage": "Управљајте", "app.components.ConsentManager.Banner.policyLink": "Декларација о колачићима", "app.components.ConsentManager.Banner.reject": "Одбиј", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Оглашавање", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Користимо ово да персонализујемо и меримо ефикасност рекламних кампања наше веб странице. Нећемо приказивати никакво оглашавање на овој платформи, али следеће услуге вам могу понудити персонализовани оглас на основу страница које посећујете на нашем сајту.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Дозволи", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Аналитика", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Користимо ово праћење да бисмо боље разумели како користите платформу да бисте научили и побољшали своју навигацију. Ове информације се користе само у масовној аналитици, а ни на који начин за праћење појединачних људи.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Повратак назад", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Прекид", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Забрани", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Функционални", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Ово је потребно да би се омогућиле и надгледале основне функционалности веб локације. Неки алати наведени овде се можда не односе на вас. Молимо прочитајте нашу политику колачића за више информација.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Гоогле менаџер ознака ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Потребан", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Да бисмо имали функционалну платформу, чувамо колачић за аутентификацију ако се региструјете и језик на којем користите ову платформу.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "сачувати", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Ваше поставке колачића", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "<PERSON>ла<PERSON><PERSON>", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Одрицање одговорности за отпремање садржаја", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Отпремањем садржаја изјављујете да овај садржај не крши никакве прописе или права трећих лица, као што су права интелектуалне својине, права на приватност, права на пословне тајне и тако даље. Сходно томе, отпремањем овог садржаја преузимате пуну и искључиву одговорност за сву директну и индиректну штету која проистиче из отпремљеног садржаја. Штавише, обавезујете се да обештетите власника платформе и Go Vocal од било каквих потраживања или обавеза трећих страна према трећим лицима, као и свих повезаних трошкова, који би настали или би били резултат садржаја који сте отпремили.", "app.components.ContentUploadDisclaimer.onAccept": "разумем", "app.components.ContentUploadDisclaimer.onCancel": "Поништити, отказати", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Tell us why", "app.components.CustomFieldsForm.addressInputAriaLabel": "Address input", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Enter an address...", "app.components.CustomFieldsForm.adminFieldTooltip": "Field only visible to admins", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "All responses to this survey are anonymized.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "At least three points are required for a polygon.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "At least two points are required for a line.", "app.components.CustomFieldsForm.attachmentRequired": "At least one attachment is required", "app.components.CustomFieldsForm.authorFieldLabel": "Author", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Start typing to search by user email or name...", "app.components.CustomFieldsForm.back": "Back", "app.components.CustomFieldsForm.budgetFieldLabel": "Budget", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Click on the map to draw. Then, drag on points to move them.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Click on the map or type an address below to add your answer.", "app.components.CustomFieldsForm.confirm": "Confirm", "app.components.CustomFieldsForm.descriptionMinLength": "The description must be at least {min} characters long", "app.components.CustomFieldsForm.descriptionRequired": "The description is required", "app.components.CustomFieldsForm.fieldMaximumItems": "At most {maxSelections, plural, one {# option} other {# options}} can be selected for the field \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "At least {minSelections, plural, one {# option} other {# options}} can be selected for the field \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "The field \"{fieldName}\" is required", "app.components.CustomFieldsForm.fileSizeLimit": "The file size limit is {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "The image is required", "app.components.CustomFieldsForm.minimumCoordinates2": "A minimum of {numPoints} map points is required.", "app.components.CustomFieldsForm.notPublic1": "*This answer will only be shared with project managers, and not to the public.", "app.components.CustomFieldsForm.otherArea": "Somewhere else", "app.components.CustomFieldsForm.progressBarLabel": "Progress", "app.components.CustomFieldsForm.removeAnswer": "Remove answer", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Select as many as you like", "app.components.CustomFieldsForm.selectBetween": "*Select between {minItems} and {maxItems} options", "app.components.CustomFieldsForm.selectExactly2": "*Select exactly {selectExactly, plural, one {# option} other {# options}}", "app.components.CustomFieldsForm.selectMany": "*Choose as many as you like", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Tap on the map to draw. Then, drag on points to move them.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Tap on the map to draw.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Tap on the map to add your answer.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Tap on the map or type an address below to add your answer.", "app.components.CustomFieldsForm.tapToAddALine": "Tap to add a line", "app.components.CustomFieldsForm.tapToAddAPoint": "Tap to add a point", "app.components.CustomFieldsForm.tapToAddAnArea": "Tap to add an area", "app.components.CustomFieldsForm.titleMaxLength": "The title must be at most {max} characters long", "app.components.CustomFieldsForm.titleMinLength": "The title must be at least {min} characters long", "app.components.CustomFieldsForm.titleRequired": "The title is required", "app.components.CustomFieldsForm.topicRequired": "At least one tag is required", "app.components.CustomFieldsForm.typeYourAnswer": "Type your answer", "app.components.CustomFieldsForm.typeYourAnswerRequired": "It is required to type your answer", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Upload a zip file containing one or more shapefiles.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "If the location is not displayed among the options as you type, you can add valid coordinates in the format 'latitude, longitude' to specify a precise location (eg: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Нека поља су била неважећа. Исправите грешке и покушајте поново.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Дошло је до непознате грешке приликом слања извештаја. Молим вас, покушајте поново.", "app.components.ErrorBoundary.errorFormLabelClose": "Близу", "app.components.ErrorBoundary.errorFormLabelComments": "Шта се десило?", "app.components.ErrorBoundary.errorFormLabelEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelName": "Име", "app.components.ErrorBoundary.errorFormLabelSubmit": "прихвати", "app.components.ErrorBoundary.errorFormSubtitle": "Наш тим је обавештен.", "app.components.ErrorBoundary.errorFormSubtitle2": "Ако желите да вам помогнемо, реците нам шта се догодило у наставку.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Ваше повратне информације су послате. Хвала вам!", "app.components.ErrorBoundary.errorFormTitle": "Изгледа да постоји проблем.", "app.components.ErrorBoundary.genericErrorWithForm": "Дошло је до грешке и не можемо да прикажемо овај садржај. Покушајте поново или {openForm}", "app.components.ErrorBoundary.openFormText": "помози нам да то схватимо", "app.components.ErrorToast.budgetExceededError": "Немате довољно буџета", "app.components.ErrorToast.votesExceededError": "Немате довољно гласова", "app.components.EventAttendanceButton.forwardToFriend": "Проследи пријатељу", "app.components.EventAttendanceButton.maxRegistrationsReached": "The maximum number of event registrations has been reached. There are no spots left.", "app.components.EventAttendanceButton.register": "Register", "app.components.EventAttendanceButton.registered": "Registered", "app.components.EventAttendanceButton.seeYouThere": "Видимо се тамо!", "app.components.EventAttendanceButton.seeYouThereName": "Видимо се тамо, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Мање информација о догађајима постало је видљиво.", "app.components.EventCard.a11y_moreContentVisible": "Више информација о догађају постало је видљиво.", "app.components.EventCard.a11y_readMore": "Read more about the \"{eventTitle}\" event.", "app.components.EventCard.endsAt": "Завршава се у", "app.components.EventCard.readMore": "Опширније", "app.components.EventCard.showLess": "Прикажи мање", "app.components.EventCard.showMore": "Прикажи више", "app.components.EventCard.startsAt": "Почиње у", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Предстојећи и текући догађаји у овом пројекту", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Предстојећи и текући догађаји у овој фази", "app.components.FileUploader.a11y_file": "Фајл:", "app.components.FileUploader.a11y_filesToBeUploaded": "Фајлови за отпремање: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Нема додатих датотека.", "app.components.FileUploader.a11y_removeFile": "Уклоните ову датотеку", "app.components.FileUploader.fileInputDescription": "Кликните да бисте изабрали датотеку", "app.components.FileUploader.fileUploadLabel": "Прилози (макс. 50МБ)", "app.components.FileUploader.file_too_large2": "Files larger than {maxSizeMb}MB are not permitted.", "app.components.FileUploader.incorrect_extension": "{fileName} није подржан од стране нашег система, неће бити постављен.", "app.components.FilterBoxes.a11y_allFilterSelected": "Изабрани статусни филтер: све", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# подношење} other {# поднесака}}", "app.components.FilterBoxes.a11y_removeFilter": "Уклоните филтер", "app.components.FilterBoxes.a11y_selectedFilter": "Изабрани статусни филтер: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Изабрано {numberOfSelectedTopics, plural, =0 {филтери нула ознака} one {филтер једне ознаке} other {# филтера ознака}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Све", "app.components.FilterBoxes.areas": "Филтрирајте по области", "app.components.FilterBoxes.inputs": "inputs", "app.components.FilterBoxes.noValuesFound": "Тренутно нема филтрирања по темама.", "app.components.FilterBoxes.showLess": "Show less", "app.components.FilterBoxes.showTagsWithNumber": "Прикажи све ({numberTags})", "app.components.FilterBoxes.statusTitle": "Статус", "app.components.FilterBoxes.topicsTitle": "Teme", "app.components.FiltersModal.filters": "Филтери", "app.components.FolderFolderCard.a11y_folderDescription": "Опис фасцикле:", "app.components.FolderFolderCard.a11y_folderTitle": "Наслов фолдера:", "app.components.FolderFolderCard.archived": "Ар<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# пројекта} one {# пројекат} other {# пројекта}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "The field type cannot be changed once there are submissions.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Type", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Autosave", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Auto-saving is enabled by default when you open the form editor. Any time you close the field settings panel using the \"X\" button, it will automatically trigger a save.", "app.components.GanttChart.timeRange.month": "Month", "app.components.GanttChart.timeRange.quarter": "Quarter", "app.components.GanttChart.timeRange.timeRangeMultiyear": "Multi-year", "app.components.GanttChart.timeRange.year": "Year", "app.components.GanttChart.today": "Today", "app.components.GoBackButton.group.edit.goBack": "Повратак назад", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Вратите се на претходну страницу", "app.components.HookForm.Feedback.errorTitle": "Постоји проблем", "app.components.HookForm.Feedback.submissionError": "Покушајте поново. Ако се проблем настави, контактирајте нас", "app.components.HookForm.Feedback.submissionErrorTitle": "Дошло је до проблема на нашој страни, извините", "app.components.HookForm.Feedback.successMessage": "Образац је успешно послат", "app.components.HookForm.PasswordInput.passwordLabel": "Лозинка", "app.components.HorizontalScroll.scrollLeftLabel": "<PERSON><PERSON> left.", "app.components.HorizontalScroll.scrollRightLabel": "<PERSON>roll right.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} ideas have loaded.", "app.components.IdeaCards.filters": "Филтери", "app.components.IdeaCards.filters.mostDiscussed": "Највише коментара", "app.components.IdeaCards.filters.newest": "Најновије", "app.components.IdeaCards.filters.oldest": "Најстарије", "app.components.IdeaCards.filters.popular": "Највише гласова", "app.components.IdeaCards.filters.random": "Насумично", "app.components.IdeaCards.filters.sortBy": "Прикажи", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Тренутни приказ: {currentSortType}'", "app.components.IdeaCards.filters.trending": "Најпопуларније", "app.components.IdeaCards.showMore": "Прикажи више", "app.components.IdeasMap.a11y_hideIdeaCard": "Сак<PERSON>иј картицу идеја.", "app.components.IdeasMap.a11y_mapTitle": "Преглед мапе", "app.components.IdeasMap.clickOnMapToAdd": "Кликните на мапу да додате свој унос", "app.components.IdeasMap.clickOnMapToAddAdmin2": "As an admin, you can click on the map to add your input, even if this phase is not active.", "app.components.IdeasMap.filters": "Филтери", "app.components.IdeasMap.multipleInputsAtLocation": "Multiple inputs at this location", "app.components.IdeasMap.noFilteredResults": "Филтери које сте изабрали нису дали резултате", "app.components.IdeasMap.noResults": "Нема резултата", "app.components.IdeasMap.or": "или", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, no dislikes.} one {1 dislike.} other {, # dislikes.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, no likes.} one {, 1 like.} other {, # likes.}}", "app.components.IdeasMap.signInLinkText": "Пријавите се", "app.components.IdeasMap.signUpLinkText": "Региструјте се", "app.components.IdeasMap.submitIdea2": "Submit input", "app.components.IdeasMap.tapOnMapToAdd": "Додирните мапу да додате свој унос", "app.components.IdeasMap.tapOnMapToAddAdmin2": "As an admin, you can tap on the map to add your input, even if this phase is not active.", "app.components.IdeasMap.userInputs2": "Inputs from participants", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, no comments} one {, 1 comment} other {, # comments}}", "app.components.IdeasShow.bodyTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.deletePost": "Избриши", "app.components.IdeasShow.editPost": "Уредити", "app.components.IdeasShow.goBack": "Повратак назад", "app.components.IdeasShow.moreOptions": "Више опција", "app.components.IdeasShow.or": "или", "app.components.IdeasShow.proposedBudgetTitle": "Предложени буџет", "app.components.IdeasShow.reportAsSpam": "Пријави као нежељену пошту", "app.components.IdeasShow.send": "Пошаљи", "app.components.IdeasShow.skipSharing": "Прескочи, урадићу то касније", "app.components.IdeasShowPage.signIn2": "Пријавите се", "app.components.IdeasShowPage.sorryNoAccess": "Жао нам је, не можете да приступите овој страници. Можда ћете морати да се пријавите или региструјете да бисте му приступили.", "app.components.LocationInput.noOptions": "Нема опција", "app.components.Modal.closeWindow": "Close window", "app.components.MultiSelect.clearButtonAction": "Clear selection", "app.components.MultiSelect.clearSearchButtonAction": "Clear search", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Унесите нову адресу е-поште", "app.components.PageNotFound.goBackToHomePage": "Назад на почетну страницу", "app.components.PageNotFound.notFoundTitle": "Страница није пронађена", "app.components.PageNotFound.pageNotFoundDescription": "Тражена страница није пронађена.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Обезбедите садржај за најмање један језик", "app.components.PagesForm.editContent": "Садржај", "app.components.PagesForm.fileUploadLabel": "Прилози (макс. 50МБ)", "app.components.PagesForm.fileUploadLabelTooltip": "Датотеке не би требало да буду веће од 50Мб. Додате датотеке ће бити приказане на дну ове странице.", "app.components.PagesForm.navbarItemTitle": "Име у навигационој траци", "app.components.PagesForm.pageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PagesForm.savePage": "Сачувај страницу", "app.components.PagesForm.saveSuccess": "Страница је успешно сачувана.", "app.components.PagesForm.titleMissingOneLanguageError": "Наведите наслов за најмање један језик", "app.components.Pagination.back": "Previous page", "app.components.Pagination.next": "Next page", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Потроши<PERSON>и сте {votesCast}, што премашује границу од {votesLimit}. Уклоните неке ставке из корпе и покушајте поново.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} left", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "You need to spend a minimum of {votesMinimum} before you can submit your basket.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "You need to select at least one option before you can submit.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Морате нешто да додате у корпу пре него што то можете да пошаљете.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {No credits left} other {# out of {totalNumberOfVotes, plural, one {1 credit} other {# credits}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {No points left} other {# out of {totalNumberOfVotes, plural, one {1 point} other {# points}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {No tokens left} other {# out of {totalNumberOfVotes, plural, one {1 token} other {# tokens}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {No votes left} other {# out of {totalNumberOfVotes, plural, one {1 vote} other {# votes}} left}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# гласова} one {#глас} other {# гласова}} цаст", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Дали сте {votesCast} гласова, што премашује ограничење од {votesLimit}. Уклоните неке гласове и покушајте поново.", "app.components.ParticipationCTABars.addInput": "Add input", "app.components.ParticipationCTABars.allocateBudget": "Одредите свој буџет", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Your budget has been submitted successfully.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "У току", "app.components.ParticipationCTABars.poll": "Учествујте у анкети", "app.components.ParticipationCTABars.reviewDocument": "Прегледајте документ", "app.components.ParticipationCTABars.seeContributions": "See contributions", "app.components.ParticipationCTABars.seeEvents3": "Погледајте догађаје", "app.components.ParticipationCTABars.seeIdeas": "Погледајте идеје", "app.components.ParticipationCTABars.seeInitiatives": "See initiatives", "app.components.ParticipationCTABars.seeIssues": "See comments", "app.components.ParticipationCTABars.seeOptions": "See options", "app.components.ParticipationCTABars.seePetitions": "See petitions", "app.components.ParticipationCTABars.seeProjects": "Поглeдајтe пројeктe", "app.components.ParticipationCTABars.seeProposals": "See proposals", "app.components.ParticipationCTABars.seeQuestions": "See questions", "app.components.ParticipationCTABars.submit": "прихвати", "app.components.ParticipationCTABars.takeTheSurvey": "Попуните анкету", "app.components.ParticipationCTABars.userHasParticipated": "Учествовали сте у овом пројекту.", "app.components.ParticipationCTABars.viewInputs": "View inputs", "app.components.ParticipationCTABars.volunteer": "Волонтеер", "app.components.ParticipationCTABars.votesCounter.vote": "воте", "app.components.ParticipationCTABars.votesCounter.votes": "гласови", "app.components.PasswordInput.a11y_passwordHidden": "Лозинка је скривена", "app.components.PasswordInput.a11y_passwordVisible": "Лозинка је видљива", "app.components.PasswordInput.a11y_strength1Password": "Слаба јачина лозинке", "app.components.PasswordInput.a11y_strength2Password": "Слаба јачина лозинке", "app.components.PasswordInput.a11y_strength3Password": "Средња јачина лозинке", "app.components.PasswordInput.a11y_strength4Password": "Јака снага лозинке", "app.components.PasswordInput.a11y_strength5Password": "Веома јака снага лозинке", "app.components.PasswordInput.hidePassword": "Сакриј лозинку", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "Прекратко (мин. {minimumPasswordLength} знакова)", "app.components.PasswordInput.minimumPasswordLengthError": "Наведите лозинку која има најмање {minimumPasswordLength} знакова", "app.components.PasswordInput.passwordEmptyError": "Унесите лозинку", "app.components.PasswordInput.passwordStrengthTooltip1": "Да бисте своју лозинку учинили јачом:", "app.components.PasswordInput.passwordStrengthTooltip2": "Користите комбинацију неузастопних малих слова, великих слова, цифара, специјалних знакова и интерпункције", "app.components.PasswordInput.passwordStrengthTooltip3": "Избегавајте уобичајене речи или речи које се лако погађају", "app.components.PasswordInput.passwordStrengthTooltip4": "Повећајте дужину", "app.components.PasswordInput.showPassword": "Покажи лозинку", "app.components.PasswordInput.strength1Password": "Јадно", "app.components.PasswordInput.strength2Password": "Слабо", "app.components.PasswordInput.strength3Password": "Средње", "app.components.PasswordInput.strength4Password": "Јака", "app.components.PasswordInput.strength5Password": "Веома јако", "app.components.PostCardsComponents.list": "Листа", "app.components.PostCardsComponents.map": "Мапа", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Додајте званично ажурирање", "app.components.PostComponents.OfficialFeedback.cancel": "Поништити, отказати", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Избриши", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Да ли сте сигурни да желите да избришете ово званично ажурирање?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Уредити", "app.components.PostComponents.OfficialFeedback.lastEdition": "Последњи пут уређено {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Последње ажурирање: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "званичник", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Одаберите како људи виде ваше име", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Званично име аутора ажурирања", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Званични основни текст ажурирања", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Зва<PERSON><PERSON><PERSON>на ажурирања", "app.components.PostComponents.OfficialFeedback.postedOn": "Објављено {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Објавите", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Прикажи претходна ажурирања", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Дајте ажурирање...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "Жао нам је, дошло је до проблема", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Ажурирајте поруку", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Ваше ажурирање је успешно објављено!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Подржите мој допринос '{postTitle}' у {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Подржите мој допринос: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Подржите мој допринос: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Подржите моју идеју '{postTitle}' у {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Подржите моју идеју: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Подржите моју идеју: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Шта мислите о овом предлогу? Гласајте за то и поделите дискусију на {postUrl} да би се ваш глас чуо!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Подржите мој предлог: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Support my initiative: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Поставио сам коментар '{postTitle}' у {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Управо сам поставио коментар: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Управо сам поставио коментар: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Подржите моју предложену опцију '{postTitle}' на {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Подржите моју предложену опцију: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Подржите моју опцију: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Support my petition '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Support my petition: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Support my petition: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Подржите мој пројекат '{postTitle}' у {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Подржите мој пројекат: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Подржите мој пројекат: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Support my proposal '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Support my proposal: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "I just posted a proposal for {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Придружите се дискусији о овом питању '{postTitle}' у {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Придружите се дискусији: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Придружите се дискусији: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Гла<PERSON><PERSON><PERSON>те за {postTitle} на", "app.components.PostComponents.linkToHomePage": "Линк до почетне странице", "app.components.PostComponents.readMore": "Опширније...", "app.components.PostComponents.topics": "Теме", "app.components.ProjectArchivedIndicator.archivedProject": "Нажалост, не можете више да учествујете у овом пројекту јер је архивиран", "app.components.ProjectArchivedIndicator.previewProject": "Draft project:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Visible only to moderators and those with a preview link.", "app.components.ProjectCard.a11y_projectDescription": "Опис пројекта:", "app.components.ProjectCard.a11y_projectTitle": "Назив пројекта:", "app.components.ProjectCard.addYourOption": "Додајте своју опцију", "app.components.ProjectCard.allocateYourBudget": "Одредите свој буџет", "app.components.ProjectCard.archived": "Ар<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.comment": "Коментар", "app.components.ProjectCard.contributeYourInput": "Дајте свој допринос", "app.components.ProjectCard.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.joinDiscussion": "Придружите се дискусији", "app.components.ProjectCard.learnMore": "Сазнајте више", "app.components.ProjectCard.reaction": "Гл<PERSON><PERSON><PERSON><PERSON><PERSON>е", "app.components.ProjectCard.readTheReport": "Read the report", "app.components.ProjectCard.reviewDocument": "Прегледајте документ", "app.components.ProjectCard.submitAnIssue": "Пошаљите коментар", "app.components.ProjectCard.submitYourIdea": "Пошаљите своју идеју", "app.components.ProjectCard.submitYourInitiative": "Submit your initiative", "app.components.ProjectCard.submitYourPetition": "Submit your petition", "app.components.ProjectCard.submitYourProject": "Пошаљите свој пројекат", "app.components.ProjectCard.submitYourProposal": "Submit your proposal", "app.components.ProjectCard.takeThePoll": "Учествујте у анкети", "app.components.ProjectCard.takeTheSurvey": "Попуните анкету", "app.components.ProjectCard.viewTheContributions": "Погледајте доприносе", "app.components.ProjectCard.viewTheIdeas": "Погледајте идеје", "app.components.ProjectCard.viewTheInitiatives": "View the initiatives", "app.components.ProjectCard.viewTheIssues": "Погледајте коментаре", "app.components.ProjectCard.viewTheOptions": "Погледајте опције", "app.components.ProjectCard.viewThePetitions": "View the petitions", "app.components.ProjectCard.viewTheProjects": "Погледајте пројекте", "app.components.ProjectCard.viewTheProposals": "View the proposals", "app.components.ProjectCard.viewTheQuestions": "Погледајте питања", "app.components.ProjectCard.vote": "Vote", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# цомментс} other {# цомментс}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# допринос} other {# доприноса}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {још нема идеја} one {#идеја} other {#идеје}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# initiatives} one {# initiative} other {# initiatives}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# цоммент} other {# цомментс}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# опција} other {# Опције}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petitions} one {# petition} other {# petitions}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# пројекат} other {# пројекта}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# proposals} one {# proposal} other {# proposals}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {#питање} other {#питања}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# цомментс} one {# цомментс} other {# цомментс}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# улаза} one {# улазни} other {# улаза}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# пројекта} one {# пројекат} other {# пројекта}}", "app.components.ProjectFolderCards.components.Topbar.all": "Све", "app.components.ProjectFolderCards.components.Topbar.archived": "Ар<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.draft": "Нацрт", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Филтер од", "app.components.ProjectFolderCards.components.Topbar.published2": "Објављено", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Таг", "app.components.ProjectFolderCards.noProjectYet": "Тренутно нема отворених пројеката", "app.components.ProjectFolderCards.noProjectsAvailable": "Нема доступних пројеката", "app.components.ProjectFolderCards.showMore": "Прикажи више", "app.components.ProjectFolderCards.stayTuned": "Проверите поново за нове прилике за ангажовање", "app.components.ProjectFolderCards.tryChangingFilters": "Покушајте да промените изабране филтере.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Такође се користи у овим градовима:", "app.components.ProjectTemplatePreview.copied": "Копирано", "app.components.ProjectTemplatePreview.copyLink": "Копи<PERSON><PERSON><PERSON> линк", "app.components.QuillEditor.alignCenter": "Централни текст", "app.components.QuillEditor.alignLeft": "Поравнати лево", "app.components.QuillEditor.alignRight": "Поравнајте десно", "app.components.QuillEditor.bold": "Одважан", "app.components.QuillEditor.clean": "Уклоните форматирање", "app.components.QuillEditor.customLink": "дугме Додај", "app.components.QuillEditor.customLinkPrompt": "Унесите линк:", "app.components.QuillEditor.edit": "Уредити", "app.components.QuillEditor.image": "Учитај слику", "app.components.QuillEditor.imageAltPlaceholder": "Кратак опис слике", "app.components.QuillEditor.italic": "Ку<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.link": "Додај везу", "app.components.QuillEditor.linkPrompt": "Унесите линк:", "app.components.QuillEditor.normalText": "Нормално", "app.components.QuillEditor.orderedList": "Наручена листа", "app.components.QuillEditor.remove": "Уклони", "app.components.QuillEditor.save": "сачувати", "app.components.QuillEditor.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.unorderedList": "Неуређена листа", "app.components.QuillEditor.video": "Додај видео", "app.components.QuillEditor.videoPrompt": "Унесите видео:", "app.components.QuillEditor.visitPrompt": "Посетите везу:", "app.components.ReactionControl.completeProfileToReact": "Попуните свој профил да бисте реаговали", "app.components.ReactionControl.dislike": "Не свиђа ми се", "app.components.ReactionControl.dislikingDisabledMaxReached": "Достигли сте максималан број несвиђања за {projectName}", "app.components.ReactionControl.like": "Као", "app.components.ReactionControl.likingDisabledMaxReached": "Достигли сте максималан број свиђања за {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Реаговање ће бити омогућено када ова фаза почне", "app.components.ReactionControl.reactingDisabledPhaseOver": "У овој фази више није могуће реаговати", "app.components.ReactionControl.reactingDisabledProjectInactive": "Више не можете реаговати на идеје у {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Реаговање тренутно није омогућено за овај пројекат", "app.components.ReactionControl.reactingNotPermitted": "Гла<PERSON><PERSON>ње у овом пројекту је завршено", "app.components.ReactionControl.reactingNotSignedIn": "Пријавите се да бисте реаговали.", "app.components.ReactionControl.reactingPossibleLater": "Реаговање ће почети {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Потврдите свој идентитет да бисте реаговали.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Event date: {startDate} at {startTime} to {endDate} at {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Event date: {eventDate} from {startTime} to {endTime}.", "app.components.Sharing.linkCopied": "Линк је копиран", "app.components.Sharing.or": "или", "app.components.Sharing.share": "Подели", "app.components.Sharing.shareByEmail": "Поделите путем е-поште", "app.components.Sharing.shareByLink": "Копи<PERSON><PERSON><PERSON> линк", "app.components.Sharing.shareOnFacebook": "Поделите на Фејсбуку", "app.components.Sharing.shareOnTwitter": "Делите на Твитеру", "app.components.Sharing.shareThisEvent": "Поделите овај догађај", "app.components.Sharing.shareThisFolder": "Подели", "app.components.Sharing.shareThisProject": "Поделите овај пројекат", "app.components.Sharing.shareViaMessenger": "Делите преко Месинџера", "app.components.Sharing.shareViaWhatsApp": "Поделите преко ВхатсАпп-а", "app.components.SideModal.closeButtonAria": "Близу", "app.components.StatusModule.futurePhase": "Гледате фазу која још није почела. Моћи ћете да учествујете када фаза почне.", "app.components.StatusModule.modifyYourSubmission1": "Modify your submission", "app.components.StatusModule.submittedUntil3": "Your vote may be submitted until", "app.components.TopicsPicker.numberOfSelectedTopics": "Изабрано {numberOfSelectedTopics, plural, =0 {зеро тагс} one {једна ознака} other {# ознаке}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Expand image", "app.components.UI.MoreActionsMenu.moreOptions": "More options", "app.components.UI.MoreActionsMenu.showMoreActions": "Прика<PERSON><PERSON> још радњи", "app.components.UI.NewLabel.new": "NEW", "app.components.UI.PhaseFilter.noAppropriatePhases": "No appropriate phases found for this project", "app.components.UI.RemoveImageButton.a11y_removeImage": "Уклони", "app.components.UI.TranslateButton.original": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.UI.TranslateButton.translate": "превести", "app.components.Unauthorized.additionalInformationRequired": "Додатне информације су потребне да бисте учествовали.", "app.components.Unauthorized.completeProfile": "Комплетан профил", "app.components.Unauthorized.completeProfileTitle": "Попуните свој профил да бисте учествовали", "app.components.Unauthorized.noPermission": "Немате дозволу да видите ову страницу", "app.components.Unauthorized.notAuthorized": "Жа<PERSON> нам је, нисте овлашћени да приступите овој страници.", "app.components.Upload.errorImageMaxSizeExceeded": "Слика коју сте изабрали је већа од {maxFileSize}МБ", "app.components.Upload.errorImagesMaxSizeExceeded": "Једна или више слика које сте изабрали су веће од {maxFileSize}МБ", "app.components.Upload.onlyOneImage": "Можете да отпремите само 1 слику", "app.components.Upload.onlyXImages": "Можете да отпремите само {maxItemsCount} слика", "app.components.Upload.remaining": "преосталих", "app.components.Upload.uploadImageLabel": "Изаберите слику (макс. {maxImageSizeInMb}МБ)", "app.components.Upload.uploadMultipleImagesLabel": "Изаберите једну или више слика", "app.components.UpsellTooltip.tooltipContent": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.components.UserName.anonymous": "Анонимоус", "app.components.UserName.anonymousTooltip2": "Овај корисник је одлучио да анонимизира свој допринос", "app.components.UserName.authorWithNoNameTooltip": "Your name has been autogenerated because you have not entered your name. Please update your profile if you would like to change it.", "app.components.UserName.deletedUser": "непознатог аутора", "app.components.UserName.verified": "Проверено", "app.components.VerificationModal.verifyAuth0": "Verify with NemID", "app.components.VerificationModal.verifyBOSA": "Потврдите са итсме или еИД-ом", "app.components.VerificationModal.verifyBosaFas": "Verify with itsme or eID", "app.components.VerificationModal.verifyClaveUnica": "Проверите са Цлаве Уница", "app.components.VerificationModal.verifyFakeSSO": "Verify with Fake SSO", "app.components.VerificationModal.verifyIdAustria": "Verify with ID Austria", "app.components.VerificationModal.verifyKeycloak": "Verify with ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Проверите помоћу МитИД-а", "app.components.VerificationModal.verifyTwoday2": "Verify with BankID or Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Потврдите свој идентитет", "app.components.VoteControl.budgetingFutureEnabled": "Можете да доделите свој буџет почевши од {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Партиципативно буџетирање тренутно није омогућено.", "app.components.VoteControl.budgetingNotPossible": "Уношење промена у буџет тренутно није могуће.", "app.components.VoteControl.budgetingNotVerified": "Молимо {verifyAccountLink} за наставак.", "app.components.VoteInputs._shared.currencyLeft1": "You have {budgetLeft} / {totalBudget} left", "app.components.VoteInputs._shared.numberOfCreditsLeft": "You have {votesLeft, plural, =0 {no credits left} other {# out of {totalNumberOfVotes, plural, one {1 credit} other {# credits}} left}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "You have {votesLeft, plural, =0 {no points left} other {# out of {totalNumberOfVotes, plural, one {1 point} other {# points}} left}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "You have {votesLeft, plural, =0 {no tokens left} other {# out of {totalNumberOfVotes, plural, one {1 token} other {# tokens}} left}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "You have {votesLeft, plural, =0 {no votes left} other {# out of {totalNumberOfVotes, plural, one {1 vote} other {# votes}} left}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "You have already submitted your budget. To modify it, click \"Modify your submission\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "You have already submitted your budget. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budgeting is not available, since this phase is not active.", "app.components.VoteInputs.single.youHaveVotedForX2": "Гла<PERSON>а<PERSON>и сте за {votes, plural, =0 {# Опције} one {# опција} other {# Опције}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "То значи да ћете изгубити све податке повезане са овим уносом, попут коментара, реакција и гласова. Ова радња се не може опозвати.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Да ли сте сигурни да желите да избришете овај унос?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "Поништити, отказати", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Потврди", "app.components.admin.SlugInput.resultingURL": "Резултат УРЛ", "app.components.admin.SlugInput.slugTooltip": "Пуж је јединствени скуп речи на крају веб адресе или УРЛ адресе.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "Ако промените УРЛ, везе до странице које користе стари УРЛ више неће радити.", "app.components.admin.SlugInput.urlSlugLabel": "Слуг", "app.components.admin.UserFilterConditions.addCondition": "Дода<PERSON><PERSON>е услов", "app.components.admin.UserFilterConditions.field_email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_event_attendance": "Пријаве догађаја", "app.components.admin.UserFilterConditions.field_follow": "Пратите", "app.components.admin.UserFilterConditions.field_lives_in": "<PERSON>и<PERSON><PERSON> <PERSON>", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Community monitor survey", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Интеракција са уносом са статусом", "app.components.admin.UserFilterConditions.field_participated_in_project": "Допринео пројекту", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Објавио нешто са ознаком", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Датум регистрације", "app.components.admin.UserFilterConditions.field_role": "Улога", "app.components.admin.UserFilterConditions.field_verified": "Верификација", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideation", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Proposals", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "није регистрован ни за један од ових догађаја", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "није регистрован ни за један догађај", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "је регистрован за један од ових догађаја", "app.components.admin.UserFilterConditions.predicate_attends_something": "је регистрован за најмање један догађај", "app.components.admin.UserFilterConditions.predicate_begins_with": "почиње са", "app.components.admin.UserFilterConditions.predicate_commented_in": "коментарисао", "app.components.admin.UserFilterConditions.predicate_contains": "садр<PERSON>и", "app.components.admin.UserFilterConditions.predicate_ends_on": "завршава на", "app.components.admin.UserFilterConditions.predicate_has_value": "има вредност", "app.components.admin.UserFilterConditions.predicate_in": "извршио било коју радњу", "app.components.admin.UserFilterConditions.predicate_is": "је", "app.components.admin.UserFilterConditions.predicate_is_admin": "је админ", "app.components.admin.UserFilterConditions.predicate_is_after": "је после", "app.components.admin.UserFilterConditions.predicate_is_before": "је пре", "app.components.admin.UserFilterConditions.predicate_is_checked": "је проверено", "app.components.admin.UserFilterConditions.predicate_is_empty": "Празно", "app.components.admin.UserFilterConditions.predicate_is_equal": "је", "app.components.admin.UserFilterConditions.predicate_is_exactly": "је тачно", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "је већи од", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "је већи или једнак", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "је нормалан корисник", "app.components.admin.UserFilterConditions.predicate_is_not_area": "искључује област", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "искључује фасциклу", "app.components.admin.UserFilterConditions.predicate_is_not_input": "excludes input", "app.components.admin.UserFilterConditions.predicate_is_not_project": "искључује пројекат", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "искључује тему", "app.components.admin.UserFilterConditions.predicate_is_one_of": "је један од", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "једна од области", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "један од фолдера", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "one of the inputs", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "један од пројеката", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "једна од тема", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "је менаџер пројекта", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "је мањи од", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "је мањи или једнак", "app.components.admin.UserFilterConditions.predicate_is_verified": "је верификован", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "не почиње са", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "није коментарисао", "app.components.admin.UserFilterConditions.predicate_not_contains": "не садржи", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "не завршава на", "app.components.admin.UserFilterConditions.predicate_not_has_value": "нема вредност", "app.components.admin.UserFilterConditions.predicate_not_in": "није допринео", "app.components.admin.UserFilterConditions.predicate_not_is": "није", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "није админ", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "није проверено", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "није празан", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "није", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "није нормалан корисник", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "није један од", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "није менаџер пројекта", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "није верификовано", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "није објавио унос", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "није реаговао на коментар", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "није реаговао на унос", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "didn't register to an event", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "has not taken survey", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "није добровољно пријавио", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "није учествовао у гласању", "app.components.admin.UserFilterConditions.predicate_nothing": "ништа", "app.components.admin.UserFilterConditions.predicate_posted_input": "објавио унос", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "реаговао на коментар", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "реаговао на унос", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "registered to an event", "app.components.admin.UserFilterConditions.predicate_something": "нешто", "app.components.admin.UserFilterConditions.predicate_taken_survey": "has taken survey", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "волонтирао", "app.components.admin.UserFilterConditions.predicate_voted_in3": "учествовао у гласању", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Атрибут", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Стање", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "Валуе", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Нећете добијати обавештења о свом доприносу", "app.components.anonymousParticipationModal.cancel": "Поништити, отказати", "app.components.anonymousParticipationModal.continue": "Настави", "app.components.anonymousParticipationModal.participateAnonymously": "Учествујте анонимно", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "This will safely <b>hide your profile</b> from admins, project managers and other residents for this specific contribution so that nobody is able to link this contribution to you. Anonymous contributions cannot be edited, and are considered final.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Учинити нашу платформу безбедном за сваког корисника је за нас главни приоритет. Речи су важне, зато будите љубазни једни према другима.", "app.components.avatar.titleForAccessibility": "Profile of {fullName}", "app.components.customFields.mapInput.removeAnswer": "Remove answer", "app.components.customFields.mapInput.undo": "Undo", "app.components.customFields.mapInput.undoLastPoint": "Undo last point", "app.components.followUnfollow.follow": "Пратите", "app.components.followUnfollow.followADiscussion": "Пратите дискусију", "app.components.followUnfollow.followTooltipInputPage2": "Праћење активира имејл обавештења о пројекту. {unsubscribeLink} у било ком тренутку.", "app.components.followUnfollow.followTooltipProjects2": "Праћење активира имејл обавештења о пројекту. {unsubscribeLink} у било ком тренутку.", "app.components.followUnfollow.unFollow": "Престани пратити", "app.components.followUnfollow.unsubscribe": "Oдјавите се", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "наше смернице", "app.components.form.ErrorDisplay.next": "Следећи", "app.components.form.ErrorDisplay.previous": "Претходна", "app.components.form.ErrorDisplay.save": "сачувати", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Почните да куцате да бисте претраживали по корисничком имејлу или имену...", "app.components.form.anonymousSurveyMessage2": "All responses to this survey are anonymized.", "app.components.form.backToInputManager": "Back to input manager", "app.components.form.backToProject": "Back to project", "app.components.form.components.controls.mapInput.removeAnswer": "Remove answer", "app.components.form.components.controls.mapInput.undo": "Undo", "app.components.form.components.controls.mapInput.undoLastPoint": "Undo last point", "app.components.form.controls.addressInputAriaLabel": "Address input", "app.components.form.controls.addressInputPlaceholder6": "Enter an address...", "app.components.form.controls.adminFieldTooltip": "Поље видљиво само администраторима", "app.components.form.controls.allStatementsError": "An answer must be selected for all statements.", "app.components.form.controls.back": "Back", "app.components.form.controls.clearAll": "Clear all", "app.components.form.controls.clearAllScreenreader": "Clear all answers from above matrix question", "app.components.form.controls.clickOnMapMultipleToAdd3": "Click on the map to draw. Then, drag on points to move them.", "app.components.form.controls.clickOnMapToAddOrType": "Click on the map or type an address below to add your answer.", "app.components.form.controls.confirm": "Confirm", "app.components.form.controls.cosponsorsPlaceholder": "Start typing a name to search", "app.components.form.controls.currentRank": "Current rank:", "app.components.form.controls.minimumCoordinates2": "A minimum of {numPoints} map points is required.", "app.components.form.controls.noRankSelected": "No rank selected", "app.components.form.controls.notPublic1": "*This answer will only be shared with project managers, and not to the public.", "app.components.form.controls.optionalParentheses": "(optional)", "app.components.form.controls.rankingInstructions": "Drag and drop to rank options.", "app.components.form.controls.selectAsManyAsYouLike": "* Изаберите онолико колико желите", "app.components.form.controls.selectBetween": "*Изаберите између {minItems} и {maxItems} опција", "app.components.form.controls.selectExactly2": "* Изаберите тачно {selectExactly, plural, one {# опција} other {# Опције}}", "app.components.form.controls.selectMany": "* Изаберите колико желите", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Tap on the map to draw. Then, drag on points to move them.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Tap on the map to draw.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Tap on the map to add your answer.", "app.components.form.controls.tapOnMapToAddOrType": "Tap on the map or type an address below to add your answer.", "app.components.form.controls.tapToAddALine": "Tap to add a line", "app.components.form.controls.tapToAddAPoint": "Tap to add a point", "app.components.form.controls.tapToAddAnArea": "Tap to add an area", "app.components.form.controls.uploadShapefileInstructions": "* Upload a zip file containing one or more shapefiles.", "app.components.form.controls.validCordinatesTooltip2": "If the location is not displayed among the options as you type, you can add valid coordinates in the format 'latitude, longitude' to specify a precise location (eg: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} out of {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} out of {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} out of {total}, where {maxValue} is {maxLabel}", "app.components.form.error": "Грешка", "app.components.form.locationGoogleUnavailable": "Није могуће учитати поље локације које пружају Гоогле мапе.", "app.components.form.progressBarLabel": "Survey progress", "app.components.form.submit": "прихвати", "app.components.form.submitApiError": "Дошло је до проблема при слању обрасца. Проверите да ли постоје грешке и покушајте поново.", "app.components.form.verifiedBlocked": "You can't edit this field because it contains verified information", "app.components.formBuilder.Page": "Страна", "app.components.formBuilder.accessibilityStatement": "accessibility statement", "app.components.formBuilder.addAnswer": "Додајте одговор", "app.components.formBuilder.addStatement": "Add statement", "app.components.formBuilder.agree": "Agree", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "If you have access to our AI package, you will be able to summarise and categorise text responses with AI", "app.components.formBuilder.askFollowUpToggleLabel": "Ask follow up", "app.components.formBuilder.bad": "Bad", "app.components.formBuilder.buttonLabel": "Button label", "app.components.formBuilder.buttonLink": "Button link", "app.components.formBuilder.cancelLeaveBuilderButtonText": "Cancel", "app.components.formBuilder.category": "Category", "app.components.formBuilder.chooseMany": "Изаберите много", "app.components.formBuilder.chooseOne": "Изабери једно", "app.components.formBuilder.close": "Близу", "app.components.formBuilder.closed": "Затворено", "app.components.formBuilder.configureMap": "Configure map", "app.components.formBuilder.confirmLeaveBuilderButtonText": "Yes, I want to leave", "app.components.formBuilder.content": "Садржај", "app.components.formBuilder.continuePageLabel": "Continues to", "app.components.formBuilder.cosponsors": "Co-sponsors", "app.components.formBuilder.default": "Уобичајено", "app.components.formBuilder.defaultContent": "Подразумевани садржај", "app.components.formBuilder.delete": "Избриши", "app.components.formBuilder.deleteButtonLabel": "Избриши", "app.components.formBuilder.description": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Ово је већ додато у образац. Подразумевани садржај се може користити само једном.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Adding custom content is not part of your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.components.formBuilder.disagree": "Disagree", "app.components.formBuilder.displayAsDropdown": "Display as dropdown", "app.components.formBuilder.displayAsDropdownTooltip": "Display the options in a dropdown. If you have many options, this is recommended.", "app.components.formBuilder.done": "Готово", "app.components.formBuilder.drawArea": "Draw area", "app.components.formBuilder.drawRoute": "Draw route", "app.components.formBuilder.dropPin": "Drop pin", "app.components.formBuilder.editButtonLabel": "Уредити", "app.components.formBuilder.emptyImageOptionError": "Provide at least 1 answer. Please note that each answer has to have a title.", "app.components.formBuilder.emptyOptionError": "Наведите најмање 1 одговор", "app.components.formBuilder.emptyStatementError": "Provide at least 1 statement", "app.components.formBuilder.emptyTitleError": "Наведите наслов питања", "app.components.formBuilder.emptyTitleMessage": "Provide a title for all the answers", "app.components.formBuilder.emptyTitleStatementMessage": "Provide a title for all the statements", "app.components.formBuilder.enable": "О<PERSON><PERSON>гу<PERSON>и", "app.components.formBuilder.errorMessage": "Постоји проблем, решите проблем да бисте могли да сачувате промене", "app.components.formBuilder.fieldGroup.description": "Опис (опционо)", "app.components.formBuilder.fieldGroup.title": "На<PERSON>лов (опционо)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Тренутно су одговори на ова питања доступни само у извезеној екцел датотеци на Инпут Манагер-у и нису видљиви корисницима.", "app.components.formBuilder.fieldLabel": "Избори одговора", "app.components.formBuilder.fieldLabelStatement": "Statements", "app.components.formBuilder.fileUpload": "Отпремање датотеке", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Map-based page", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Embed map as context or ask location based questions to participants.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "For optimal user experience, we do not recommend adding point, route, or area questions to map-based pages.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normal page", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Survey mapping features are not included in your current license. Reach out to your GovSuccess Manager to learn more.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Page type", "app.components.formBuilder.formEnd": "Крај обрасца", "app.components.formBuilder.formField.cancelDeleteButtonText": "Cancel", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "Yes, delete page", "app.components.formBuilder.formField.copyNoun": "Copy", "app.components.formBuilder.formField.copyVerb": "Copy", "app.components.formBuilder.formField.delete": "Delete", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Deleting this page will also delete the logic associated with it. Are you sure you want to delete it?", "app.components.formBuilder.formField.deleteResultsInfo": "This cannot be undone", "app.components.formBuilder.goToPageInputLabel": "Затим следећа страница је:", "app.components.formBuilder.good": "Good", "app.components.formBuilder.helmetTitle": "Форм буилдер", "app.components.formBuilder.imageFileUpload": "Отпремање слике", "app.components.formBuilder.invalidLogicBadgeMessage": "Неважећа логика", "app.components.formBuilder.labels2": "Labels (optional)", "app.components.formBuilder.labelsTooltipContent2": "Choose optional labels for any of the linear scale values.", "app.components.formBuilder.lastPage": "Ending", "app.components.formBuilder.layout": "Лаи<PERSON><PERSON>т", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Are you sure you want to leave?", "app.components.formBuilder.leaveBuilderText": "You have unsaved changes. Please save before leaving. If you leave, you'll lose your changes.", "app.components.formBuilder.limitAnswersTooltip": "Када је укључено, испитаници треба да изаберу одређени број одговора да би наставили.", "app.components.formBuilder.limitNumberAnswers": "Ограничите број одговора", "app.components.formBuilder.linePolygonMapWarning2": "Line and polygon drawing may not meet accessibility standards. More information can be found in the {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Лин<PERSON><PERSON><PERSON>на скала", "app.components.formBuilder.locationDescription": "Локација", "app.components.formBuilder.logic": "Логика", "app.components.formBuilder.logicAnyOtherAnswer": "Any other answer", "app.components.formBuilder.logicConflicts.conflictingLogic": "Conflicting logic", "app.components.formBuilder.logicConflicts.interQuestionConflict": "This page contains questions that lead to different pages. If participants answer multiple questions, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "This page has multiple logic rules applied: multi-select question logic, page-level logic, and inter-question logic. When these conditions overlap, question logic will take precedence over page logic, and the furthest page will be shown. Review the logic to ensure it aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "This page contains a multi-select question where options lead to different pages. If participants select multiple options, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "This page contains a multi-select question where options lead to different pages and has questions that lead to other pages. The furthest page will be shown if these conditions overlap. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "This page contains a multi-select question where options lead to different pages and has logic set at both the page and question level. Question logic will take precedence, and the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "This page has logic set at both the page level and question level. Question logic will take precedence over page-level logic. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "This page has logic set at both the page and question levels, and multiple questions direct to different pages. Question logic will take precedence, and the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicNoAnswer2": "Not answered", "app.components.formBuilder.logicPanelAnyOtherAnswer": "If any other answer", "app.components.formBuilder.logicPanelNoAnswer": "If not answered", "app.components.formBuilder.logicValidationError": "Логика можда неће имати везе са претходним страницама", "app.components.formBuilder.longAnswer": "Дуг одговор", "app.components.formBuilder.mapConfiguration": "Map configuration", "app.components.formBuilder.mapping": "Mapping", "app.components.formBuilder.mappingNotInCurrentLicense": "Survey mapping features are not included in your current license. Reach out to your GovSuccess Manager to learn more.", "app.components.formBuilder.matrix": "Matrix", "app.components.formBuilder.matrixSettings.columns": "Columns", "app.components.formBuilder.matrixSettings.rows": "Rows", "app.components.formBuilder.multipleChoice": "Вишеструки избор", "app.components.formBuilder.multipleChoiceHelperText": "If multiple options lead to different pages and participants select more than one, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.multipleChoiceImage": "Image choice", "app.components.formBuilder.multiselect.maximum": "Максимум", "app.components.formBuilder.multiselect.minimum": "Мини<PERSON>ум", "app.components.formBuilder.neutral": "Neutral", "app.components.formBuilder.newField": "Ново поље", "app.components.formBuilder.number": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Отвори", "app.components.formBuilder.optional": "Опционо", "app.components.formBuilder.other": "Other", "app.components.formBuilder.otherOption": "\"Other\" option", "app.components.formBuilder.otherOptionTooltip": "Allow participants to enter a custom response if the provided answers do not match their preference", "app.components.formBuilder.page": "Страна", "app.components.formBuilder.pageCannotBeDeleted": "This page can't be deleted.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "This page cannot be deleted and does not allow any additional fields to be added.", "app.components.formBuilder.pageRuleLabel": "Следећа страница је:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "If no logic is added, the form will follow its normal flow. If both the page and its questions have logic, the question logic will take precedence. Ensure this aligns with your intended flow For more information, visit {supportPageLink}", "app.components.formBuilder.preview": "Preview:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Co-sponsors are not shown on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "File upload questions are shown as unsupported on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Mapping questions are shown on the downloaded PDF, but layers will not be visible. Mapping questions are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Matrix questions are shown on the downloaded PDF but are not currently supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Page titles and descriptions are shown as a section header in the downloaded PDF.", "app.components.formBuilder.printSupportTooltip.ranking": "Ranking questions are shown on the downloaded PDF but are not currently supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Tags are shown as unsupported on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.proposedBudget": "Предложени буџет", "app.components.formBuilder.question": "Пита<PERSON>е", "app.components.formBuilder.questionCannotBeDeleted": "Ово питање се не може избрисати.", "app.components.formBuilder.questionDescriptionOptional": "Опис питања (опционо)", "app.components.formBuilder.questionTitle": "Наслов питања", "app.components.formBuilder.randomize": "Randomize", "app.components.formBuilder.randomizeToolTip": "The order of the answers will be randomized per user", "app.components.formBuilder.range": "Домет", "app.components.formBuilder.ranking": "Ranking", "app.components.formBuilder.rating": "Rating", "app.components.formBuilder.removeAnswer": "Уклони одговор", "app.components.formBuilder.required": "Потребан", "app.components.formBuilder.requiredToggleLabel": "Учините одговор на ово питање обавезним", "app.components.formBuilder.ruleForAnswerLabel": "Ако је одговор:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "If answers include:", "app.components.formBuilder.save": "сачувати", "app.components.formBuilder.selectRangeTooltip": "Изаберите максималну вредност за своју скалу.", "app.components.formBuilder.sentiment": "Sentiment scale", "app.components.formBuilder.shapefileUpload": "Esri shapefile upload", "app.components.formBuilder.shortAnswer": "Кратак одговор", "app.components.formBuilder.showResponseToUsersToggleLabel": "Покажите одговор корисницима", "app.components.formBuilder.singleChoice": "Један избор", "app.components.formBuilder.staleDataErrorMessage2": "There has been a problem. This input form has been saved more recently somewhere else. This may be because you or another user has it open for editing in another browser window. Please refresh the page to get the latest form and then make your changes again.", "app.components.formBuilder.stronglyAgree": "Strongly agree", "app.components.formBuilder.stronglyDisagree": "Strongly disagree", "app.components.formBuilder.supportArticleLinkText": "Ова страница", "app.components.formBuilder.tags": "Тема", "app.components.formBuilder.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.toLabel": "до", "app.components.formBuilder.unsavedChanges": "You have unsaved changes", "app.components.formBuilder.useCustomButton2": "Use custom page button", "app.components.formBuilder.veryBad": "Very bad", "app.components.formBuilder.veryGood": "Very good", "app.components.ideas.similarIdeas.engageHere": "Engage here", "app.components.ideas.similarIdeas.noSimilarSubmissions": "No similar submissions found.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "We found similar submisisons - engaging with them can help make them stronger!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Similar submissions already posted:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Looking for similar submissions ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {Less than a day} one {# day} other {# days}} left", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  weeks left", "app.components.screenReaderCurrency.AED": "United Arab Emirates Dirham", "app.components.screenReaderCurrency.AFN": "Afghan Afghani", "app.components.screenReaderCurrency.ALL": "Albanian Lek", "app.components.screenReaderCurrency.AMD": "Armenian Dram", "app.components.screenReaderCurrency.ANG": "Netherlands Antillean Guilder", "app.components.screenReaderCurrency.AOA": "Angolan <PERSON>", "app.components.screenReaderCurrency.ARS": "Argentine Peso", "app.components.screenReaderCurrency.AUD": "Australian Dollar", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "Azerbaijani Manat", "app.components.screenReaderCurrency.BAM": "Bosnia-Herzegovina Convertible Mark", "app.components.screenReaderCurrency.BBD": "Barbadian Dollar", "app.components.screenReaderCurrency.BDT": "Bangladeshi Taka", "app.components.screenReaderCurrency.BGN": "Bulgarian Lev", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "Burundian Franc", "app.components.screenReaderCurrency.BMD": "Bermudian Dollar", "app.components.screenReaderCurrency.BND": "Brunei Dollar", "app.components.screenReaderCurrency.BOB": "Bolivian Boliviano", "app.components.screenReaderCurrency.BOV": "Bolivian M<PERSON>", "app.components.screenReaderCurrency.BRL": "Brazilian Real", "app.components.screenReaderCurrency.BSD": "Bahamian Dollar", "app.components.screenReaderCurrency.BTN": "Bhutanese Ngultrum", "app.components.screenReaderCurrency.BWP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "Belarusian Ruble", "app.components.screenReaderCurrency.BZD": "Belize Dollar", "app.components.screenReaderCurrency.CAD": "Canadian Dollar", "app.components.screenReaderCurrency.CDF": "Congolese Franc", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Swiss Franc", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Chilean Unit of Account (UF)", "app.components.screenReaderCurrency.CLP": "Chilean Peso", "app.components.screenReaderCurrency.CNY": "Chinese Yuan", "app.components.screenReaderCurrency.COP": "Colombian Peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Rican Colón", "app.components.screenReaderCurrency.CRE": "Credit", "app.components.screenReaderCurrency.CUC": "Cuban Convertible Peso", "app.components.screenReaderCurrency.CUP": "Cuban Peso", "app.components.screenReaderCurrency.CVE": "Cape Verdean Escudo", "app.components.screenReaderCurrency.CZK": "Czech Koruna", "app.components.screenReaderCurrency.DJF": "Djiboutian Franc", "app.components.screenReaderCurrency.DKK": "Danish Krone", "app.components.screenReaderCurrency.DOP": "Dominican Peso", "app.components.screenReaderCurrency.DZD": "Algerian Dinar", "app.components.screenReaderCurrency.EGP": "Egyptian Pound", "app.components.screenReaderCurrency.ERN": "Eritrean Nakfa", "app.components.screenReaderCurrency.ETB": "Ethiopian Birr", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Fijian Dollar", "app.components.screenReaderCurrency.FKP": "Falkland Islands Pound", "app.components.screenReaderCurrency.GBP": "British Pound", "app.components.screenReaderCurrency.GEL": "Georgian Lari", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Gibraltar Pound", "app.components.screenReaderCurrency.GMD": "Gambian Dalasi", "app.components.screenReaderCurrency.GNF": "Guinean Franc", "app.components.screenReaderCurrency.GTQ": "Guatemalan <PERSON>", "app.components.screenReaderCurrency.GYD": "Guyanese Dollar", "app.components.screenReaderCurrency.HKD": "Hong Kong Dollar", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "Croatian Kuna", "app.components.screenReaderCurrency.HTG": "Haitian Gourde", "app.components.screenReaderCurrency.HUF": "Hungarian Forint", "app.components.screenReaderCurrency.IDR": "Indonesian Rupiah", "app.components.screenReaderCurrency.ILS": "Israeli New <PERSON>", "app.components.screenReaderCurrency.INR": "Indian Rupee", "app.components.screenReaderCurrency.IQD": "Iraqi <PERSON>", "app.components.screenReaderCurrency.IRR": "Iranian Rial", "app.components.screenReaderCurrency.ISK": "Icelandic Króna", "app.components.screenReaderCurrency.JMD": "Jamaican Dollar", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "Japanese Yen", "app.components.screenReaderCurrency.KES": "Kenyan Shilling", "app.components.screenReaderCurrency.KGS": "Kyrgyzstani Som", "app.components.screenReaderCurrency.KHR": "Cambodian Riel", "app.components.screenReaderCurrency.KMF": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KPW": "North Korean Won", "app.components.screenReaderCurrency.KRW": "South Korean Won", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "Cayman Islands Dollar", "app.components.screenReaderCurrency.KZT": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Lebanese Pound", "app.components.screenReaderCurrency.LKR": "Sri Lankan Rupee", "app.components.screenReaderCurrency.LRD": "Liberian Dollar", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Lithuanian Litas", "app.components.screenReaderCurrency.LVL": "Latvian Lats", "app.components.screenReaderCurrency.LYD": "Libyan Dinar", "app.components.screenReaderCurrency.MAD": "Moroccan <PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MDL": "Moldovan Leu", "app.components.screenReaderCurrency.MGA": "Malagasy Ariary", "app.components.screenReaderCurrency.MKD": "Macedonian Denar", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "Mongolian Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "Mauritanian Ouguiya", "app.components.screenReaderCurrency.MUR": "Mauritian Rupee", "app.components.screenReaderCurrency.MVR": "Maldivian Rufiyaa", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MXN": "Mexican Peso", "app.components.screenReaderCurrency.MXV": "Mexican Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malaysian Ringgit", "app.components.screenReaderCurrency.MZN": "Mozambican Metical", "app.components.screenReaderCurrency.NAD": "Namibian Dollar", "app.components.screenReaderCurrency.NGN": "Nigerian Naira", "app.components.screenReaderCurrency.NIO": "Nicaraguan Córdoba", "app.components.screenReaderCurrency.NOK": "Norwegian Krone", "app.components.screenReaderCurrency.NPR": "Nepalese Rupee", "app.components.screenReaderCurrency.NZD": "New Zealand Dollar", "app.components.screenReaderCurrency.OMR": "Omani R<PERSON>", "app.components.screenReaderCurrency.PAB": "Panamanian Balboa", "app.components.screenReaderCurrency.PEN": "Peruvian Sol", "app.components.screenReaderCurrency.PGK": "Papua New Guinean Kina", "app.components.screenReaderCurrency.PHP": "Philippine Peso", "app.components.screenReaderCurrency.PKR": "Pakistani Rupee", "app.components.screenReaderCurrency.PLN": "Polish Złoty", "app.components.screenReaderCurrency.PYG": "Paraguayan Guaraní", "app.components.screenReaderCurrency.QAR": "Qatari Riyal", "app.components.screenReaderCurrency.RON": "Romanian Leu", "app.components.screenReaderCurrency.RSD": "Serbian Dinar", "app.components.screenReaderCurrency.RUB": "Russian Ruble", "app.components.screenReaderCurrency.RWF": "Rwandan <PERSON>", "app.components.screenReaderCurrency.SAR": "Saudi Riyal", "app.components.screenReaderCurrency.SBD": "Solomon Islands Dollar", "app.components.screenReaderCurrency.SCR": "Seychellois Rupee", "app.components.screenReaderCurrency.SDG": "Sudanese Pound", "app.components.screenReaderCurrency.SEK": "Swedish Krona", "app.components.screenReaderCurrency.SGD": "Singapore Dollar", "app.components.screenReaderCurrency.SHP": "<PERSON>", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "Somali Shilling", "app.components.screenReaderCurrency.SRD": "Surinamese Dollar", "app.components.screenReaderCurrency.SSP": "South Sudanese Pound", "app.components.screenReaderCurrency.STD": "São Tomé and Pr<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SYP": "Syrian Pound", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "Thai Baht", "app.components.screenReaderCurrency.TJS": "<PERSON>i Somoni", "app.components.screenReaderCurrency.TMT": "Turkmenistani Manat", "app.components.screenReaderCurrency.TND": "Tunisian Dinar", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Tongan Paʻanga", "app.components.screenReaderCurrency.TRY": "Turkish Lira", "app.components.screenReaderCurrency.TTD": "Trinidad and Tobago Dollar", "app.components.screenReaderCurrency.TWD": "New Taiwan Dollar", "app.components.screenReaderCurrency.TZS": "Tanzanian <PERSON>", "app.components.screenReaderCurrency.UAH": "Ukrainian Hryvnia", "app.components.screenReaderCurrency.UGX": "Ugandan <PERSON>", "app.components.screenReaderCurrency.USD": "United States Dollar", "app.components.screenReaderCurrency.USN": "United States Dollar (Next day)", "app.components.screenReaderCurrency.USS": "United States Dollar (Same day)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguayan Peso", "app.components.screenReaderCurrency.UZS": "Uzbekistani Som", "app.components.screenReaderCurrency.VEF": "Venezuelan Bolívar", "app.components.screenReaderCurrency.VND": "Vietnamese Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoan <PERSON>", "app.components.screenReaderCurrency.XAF": "Central African CFA Franc", "app.components.screenReaderCurrency.XAG": "Silver (one troy ounce)", "app.components.screenReaderCurrency.XAU": "Gold (one troy ounce)", "app.components.screenReaderCurrency.XBA": "European Composite Unit (EURCO)", "app.components.screenReaderCurrency.XBB": "European Monetary Unit (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "European Unit of Account 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "European Unit of Account 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "East Caribbean Dollar", "app.components.screenReaderCurrency.XDR": "Special Drawing Rights", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "West African CFA Franc", "app.components.screenReaderCurrency.XPD": "Palladium (one troy ounce)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "Platinum (one troy ounce)", "app.components.screenReaderCurrency.XTS": "Codes specifically reserved for testing purposes", "app.components.screenReaderCurrency.XXX": "No currency", "app.components.screenReaderCurrency.YER": "Yemeni R<PERSON>", "app.components.screenReaderCurrency.ZAR": "South African Rand", "app.components.screenReaderCurrency.ZMW": "Zambian <PERSON>", "app.components.screenReaderCurrency.amount": "Amount", "app.components.screenReaderCurrency.currency": "<PERSON><PERSON><PERSON><PERSON>", "app.components.trendIndicator.lastQuarter2": "last quarter", "app.containers.AccessibilityStatement.applicability": "Ова изјава о приступачности се односи на {demoPlatformLink} који је репрезентативан за ову веб страницу; користи исти изворни код и има исту функционалност.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Метода процене", "app.containers.AccessibilityStatement.assesmentText2022": "Приступачност овог сајта је проценио спољни ентитет који није укључен у процес дизајна и развоја. Усклађеност горе поменутог {demoPlatformLink} може се идентификовати на овом {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "можете променити своја подешавања", "app.containers.AccessibilityStatement.changePreferencesText": "У било ком тренутку, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Изузеци усклађености", "app.containers.AccessibilityStatement.conformanceStatus": "Статус усаглашености", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Трудимо се да наш садржај учинимо инклузивним за све. Међутим, у неким случајевима може бити недоступан садржај на платформи као што је наведено у наставку:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "демо вебсите", "app.containers.AccessibilityStatement.email": "Емаил:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Embedded survey tools", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "The embedded survey tools that are available for use on this platform are third-party software and may not be accessible.", "app.containers.AccessibilityStatement.exception_1": "Наше платформе за дигитално ангажовање олакшавају садржај који генеришу корисници који постављају појединци и организације. Могуће је да се ПДФ-ови, слике или други типови датотека, укључујући мултимедију, отпремају на платформу као прилози или додају у текстуална поља од стране корисника платформе. Ови документи можда неће бити у потпуности доступни.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Поздрављамо ваше повратне информације о приступачности ове странице. Контактирајте нас на један од следећих начина:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Процес повратних информација", "app.containers.AccessibilityStatement.govocalAddress2022": "Боулевард Пацхецо 34, 1000 Брисел, Белгија", "app.containers.AccessibilityStatement.headTitle": "Accessibility Statement | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} је посвећен пружању платформе која је доступна свим корисницима, без обзира на технологију или способност. Тренутни релевантни стандарди приступачности се придржавају у нашим сталним напорима да максимално повећамо доступност и употребљивост наших платформи за све кориснике.", "app.containers.AccessibilityStatement.mapping": "Mapping", "app.containers.AccessibilityStatement.mapping_1": "Мапе на платформи делимично испуњавају стандарде приступачности. Обим мапе, зумирање и УИ виџети се могу контролисати помоћу тастатуре приликом прегледа мапа. Администратори такође могу да конфигуришу стил слојева мапе у командном панелу или користећи Есри интеграцију, да креирају приступачније палете боја и симбологију. Коришћење различитих стилова линија или полигона (нпр. испрекиданих линија) ће такође помоћи да се разликују слојеви мапе где год је то могуће, и иако такав стил тренутно не може да се конфигурише у оквиру наше платформе, може се конфигурисати ако користите мапе са Есри интеграцијом.", "app.containers.AccessibilityStatement.mapping_2": "Мапе на платформи нису у потпуности доступне јер не представљају адекватно основне мапе, слојеве мапе или трендове у подацима корисницима који користе читаче екрана. Потпуно доступне мапе би морале да звучно представе слојеве карте и опишу све релевантне трендове у подацима. Штавише, цртање мапа линија и полигона у анкетама није доступно јер се облици не могу цртати помоћу тастатуре. Алтернативне методе уноса тренутно нису доступне због техничке сложености.", "app.containers.AccessibilityStatement.mapping_3": "To make line and polygon map drawing more accessible, we recommend including an introduction or explanation in the survey question or page description of what the map is showing and any relevant trends. Furthermore, a short or long answer text question could be provided so respondents can describe their answer in plain terms if needed (rather than clicking on the map). We also recommend including contact information for the project manager so respondents who cannot fill in a map question can request an alternative method to answer the question (E.g. Video meeting).", "app.containers.AccessibilityStatement.mapping_4": "За пројекте и предлоге постоји опција за приказ уноса у приказу мапе. За ове методе постоји и алтернативни приказ листе.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Наше онлајн радионице имају компоненту за видео стриминг уживо, која тренутно не подржава титлове.", "app.containers.AccessibilityStatement.pageDescription": "Изјава о приступачности ове веб странице", "app.containers.AccessibilityStatement.postalAddress": "Поштанска адреса:", "app.containers.AccessibilityStatement.publicationDate": "Датум објављивања", "app.containers.AccessibilityStatement.publicationDate2024": "This accessibility statement was published on August 21, 2024.", "app.containers.AccessibilityStatement.responsiveness": "Циљ нам је да одговоримо на повратне информације у року од 1-2 радна дана.", "app.containers.AccessibilityStatement.statusPageText": "стату<PERSON>на страница", "app.containers.AccessibilityStatement.technologiesIntro": "Приступачност овог сајта зависи од следећих технологија за рад:", "app.containers.AccessibilityStatement.technologiesTitle": "Тецхнологиес", "app.containers.AccessibilityStatement.title": "Изјава о приступачности", "app.containers.AccessibilityStatement.userGeneratedContent": "Корисник генерише садржај", "app.containers.AccessibilityStatement.workshops": "Радионице", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Select project", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Using the Content Builder will let you use more advanced layout options. For languages where no content is available in the content builder, the regular project description content will be displayed instead.", "app.containers.AdminPage.ProjectDescription.linkText": "Edit description in Content Builder", "app.containers.AdminPage.ProjectDescription.saveError": "Something went wrong while saving the project description.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Use Content Builder for description", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Using the Content Builder will let you use more advanced layout options.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "View project", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Крај анкете", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Направите паметну групу", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Корисници који испуњавају све следеће услове биће аутоматски додати у групу:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Наведите бар једно правило", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Неки услови су непотпуни", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Сачувај групу", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Configuring smart groups is not part of your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Наведите име групе", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Верификација је онемогућена за вашу платформу, уклоните правило верификације или контактирајте подршку.", "app.containers.App.appMetaDescription": "Добродошли на онлајн платформу за учешће {orgName}. \nИстражите локалне пројекте и укључите се у дискусију!", "app.containers.App.loading": "Учитавање...", "app.containers.App.metaTitle1": "Citizen engagement platform | {orgName}", "app.containers.App.skipLinkText": "Skip to main content", "app.containers.AreaTerms.areaTerm": "area", "app.containers.AreaTerms.areasTerm": "areas", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Налог са овом е-поштом већ постоји. Можете се одјавити, пријавити са овом адресом е-поште и верификовати свој налог на страници са подешавањима.", "app.containers.Authentication.steps.AccessDenied.close": "Close", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "You do not meet the requirements to participate in this process.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Go back to single sign-on verification", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Унесите токен", "app.containers.Authentication.steps.Invitation.token": "Токен", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Already have an account? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Log in", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Е-поруке у овој категорији", "app.containers.CampaignsConsentForm.messageError": "Дошло је до грешке при чувању ваших подешавања е-поште.", "app.containers.CampaignsConsentForm.messageSuccess": "Ваша подешавања е-поште су сачувана.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Које врсте обавештења путем е-поште желите да примате? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Обавештења", "app.containers.CampaignsConsentForm.submit": "Сачувајте", "app.containers.ChangeEmail.backToProfile": "Назад на подешавања профила", "app.containers.ChangeEmail.confirmationModalTitle": "Потврди свој емаил", "app.containers.ChangeEmail.emailEmptyError": "Наведите адресу е-поште", "app.containers.ChangeEmail.emailInvalidError": "Наведите адресу е-поште у исправном формату, на пример име@провидер.цом", "app.containers.ChangeEmail.emailRequired": "Молимо Вас да унесете емаил адресу.", "app.containers.ChangeEmail.emailTaken": "Овај емаил је већ у употреби.", "app.containers.ChangeEmail.emailUpdateCancelled": "Ажурирање имејлом је отказано.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Да бисте ажурирали своју е-пошту, поново покрените процес.", "app.containers.ChangeEmail.helmetDescription": "Промените своју страницу е-поште", "app.containers.ChangeEmail.helmetTitle": "Промените своју е-пошту", "app.containers.ChangeEmail.newEmailLabel": "Нова е-маил", "app.containers.ChangeEmail.submitButton": "прихвати", "app.containers.ChangeEmail.titleAddEmail": "Додајте своју е-пошту", "app.containers.ChangeEmail.titleChangeEmail": "Промените своју е-пошту", "app.containers.ChangeEmail.updateSuccessful": "Ваша е-пошта је успешно ажурирана.", "app.containers.ChangePassword.currentPasswordLabel": "Тренутна лозинка", "app.containers.ChangePassword.currentPasswordRequired": "Унесите своју тренутну лозинку", "app.containers.ChangePassword.goHome": "Иди кући", "app.containers.ChangePassword.helmetDescription": "Промените страницу са лозинком", "app.containers.ChangePassword.helmetTitle": "Промените своју лозинку", "app.containers.ChangePassword.newPasswordLabel": "Нова лозинка", "app.containers.ChangePassword.newPasswordRequired": "Унесите своју нову лозинку", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Наведите лозинку која има најмање {minimumPasswordLength} знакова", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Ваша лозинка је успешно ажурирана", "app.containers.ChangePassword.passwordEmptyError": "Унесите лозинку", "app.containers.ChangePassword.passwordsDontMatch": "Потврдите нову лозинку", "app.containers.ChangePassword.titleAddPassword": "Додајте лозинку", "app.containers.ChangePassword.titleChangePassword": "Промените своју лозинку", "app.containers.Comments.a11y_commentDeleted": "Коментар је обрисан", "app.containers.Comments.a11y_commentPosted": "Коментар је објављен", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {нема лајкова} one {1 лике} other {# свиђа}}", "app.containers.Comments.a11y_undoLike": "Поништи свиђање", "app.containers.Comments.addCommentError": "Нешто није у реду. Покушајте поново касније.", "app.containers.Comments.adminCommentDeletionCancelButton": "Поништити, отказати", "app.containers.Comments.adminCommentDeletionConfirmButton": "Избришите овај коментар", "app.containers.Comments.cancelCommentEdit": "Поништити, отказати", "app.containers.Comments.childCommentBodyPlaceholder": "Напиши одговор...", "app.containers.Comments.commentCancelUpvote": "Поништи", "app.containers.Comments.commentDeletedPlaceholder": "О<PERSON><PERSON><PERSON> коментар је обрисан.", "app.containers.Comments.commentDeletionCancelButton": "Задржи мој коментар", "app.containers.Comments.commentDeletionConfirmButton": "Обриши мој коментар", "app.containers.Comments.commentLike": "Као", "app.containers.Comments.commentReplyButton": "Одговорити", "app.containers.Comments.commentsSortTitle": "Сортирај коментаре по", "app.containers.Comments.completeProfileLinkText": "попуните свој профил", "app.containers.Comments.completeProfileToComment": "Молимо вас {completeRegistrationLink} да бисте коментарисали.", "app.containers.Comments.confirmCommentDeletion": "Да ли сте сигурни да желите да избришете овај коментар? Нема повратка!", "app.containers.Comments.deleteComment": "Избриши", "app.containers.Comments.deleteReasonDescriptionError": "Наведите више информација о свом разлогу", "app.containers.Comments.deleteReasonError": "Наведите разлог", "app.containers.Comments.deleteReason_inappropriate": "То је неприкладно или увредљиво", "app.containers.Comments.deleteReason_irrelevant": "Ово није релевантно", "app.containers.Comments.deleteReason_other": "Други разлог", "app.containers.Comments.editComment": "Уредити", "app.containers.Comments.guidelinesLinkText": "наше смернице заједнице", "app.containers.Comments.ideaCommentBodyPlaceholder": "Напишите свој коментар овде", "app.containers.Comments.internalCommentingNudgeMessage": "Making internal comments is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Comments.internalConversation": "Интерни разговор", "app.containers.Comments.loadMoreComments": "Учитајте још коментара", "app.containers.Comments.loadingComments": "Учитавање коментара...", "app.containers.Comments.loadingMoreComments": "Учитавање више коментара...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Овај коментар није видљив редовним корисницима", "app.containers.Comments.postInternalComment": "Објавите интерни коментар", "app.containers.Comments.postPublicComment": "Објавите јавни коментар", "app.containers.Comments.profanityError": "Упс! Изгледа да ваш пост садржи неки језик који не одговара {guidelinesLink}. Трудимо се да ово буде безбедан простор за све. Измените свој унос и покушајте поново.", "app.containers.Comments.publicDiscussion": "Јавна расправа", "app.containers.Comments.publishComment": "Објавите свој коментар", "app.containers.Comments.reportAsSpamModalTitle": "Зашто желите да ово пријавите као нежељену пошту?", "app.containers.Comments.saveComment": "сачувати", "app.containers.Comments.signInLinkText": "Пријавите се", "app.containers.Comments.signInToComment": "Молимо {signInLink} за коментар.", "app.containers.Comments.signUpLinkText": "пријави се", "app.containers.Comments.verifyIdentityLinkText": "Потврдите свој идентитет", "app.containers.Comments.visibleToUsersPlaceholder": "Овај коментар је видљив редовним корисницима", "app.containers.Comments.visibleToUsersWarning": "Коментари објављени овде биће видљиви редовним корисницима.", "app.containers.ContentBuilder.PageTitle": "Project description", "app.containers.CookiePolicy.advertisingContent": "Колачићи за оглашавање могу да се користе за персонализацију и мерење ефикасности спољних маркетиншких кампања на интеракцију са овом платформом. Нећемо приказивати никакво оглашавање на овој платформи, али ћете можда примати персонализоване огласе на основу страница које посећујете.", "app.containers.CookiePolicy.advertisingTitle": "Оглашавање", "app.containers.CookiePolicy.analyticsContents": "Колачићи аналитике прате понашање посетилаца, на пример које странице су посећене и колико дуго. Они такође могу прикупљати неке техничке податке укључујући информације о прегледачу, приближну локацију и ИП адресе. Ове податке користимо само интерно да бисмо наставили да побољшавамо свеукупно корисничко искуство и функционисање платформе. Такви подаци се такође могу делити између Go Vocal-а и {orgName} ради процене и побољшања ангажовања на пројектима на платформи. Имајте на уму да су подаци анонимни и да се користе на збирном нивоу – не идентификују вас лично. Међутим, могуће је да ако се ови подаци комбинују са другим изворима података, може доћи до такве идентификације.", "app.containers.CookiePolicy.analyticsTitle": "Колачићи аналитике", "app.containers.CookiePolicy.cookiePolicyDescription": "Детаљно објашњење како користимо колачиће на овој платформи", "app.containers.CookiePolicy.cookiePolicyTitle": "Политиком колачића", "app.containers.CookiePolicy.essentialContent": "Неки колачићи су неопходни да би се обезбедило правилно функционисање ове платформе. Ови основни колачићи се првенствено користе за аутентификацију вашег налога када посетите платформу и за чување жељеног језика.", "app.containers.CookiePolicy.essentialTitle": "Есенцијални колачићи", "app.containers.CookiePolicy.externalContent": "Неке од наших страница могу да приказују садржај спољних добављача, нпр. ИоуТубе или Типеформ. Ми немамо контролу над овим колачићима трећих страна и преглед садржаја ових спољних провајдера такође може довести до инсталирања колачића на ваш уређај.", "app.containers.CookiePolicy.externalTitle": "Екстерни колачићи", "app.containers.CookiePolicy.functionalContents": "Функционални колачићи могу бити омогућени посетиоцима да примају обавештења о ажурирањима и да приступе каналима подршке директно са платформе.", "app.containers.CookiePolicy.functionalTitle": "Функционални колачићи", "app.containers.CookiePolicy.headCookiePolicyTitle": "Cookie Policy | {orgName}", "app.containers.CookiePolicy.intro": "Колачићи су текстуалне датотеке које се чувају у претраживачу или на чврстом диску вашег рачунара или мобилног уређаја када посетите веб локацију и које веб локација може да референцира током наредних посета. Користимо колачиће да бисмо разумели како посетиоци користе ову платформу да побољшају њен дизајн и искуство, да запамтимо ваше преференције (као што је ваш жељени језик) и да подржимо кључне функције за регистроване кориснике и администраторе платформе.", "app.containers.CookiePolicy.manageCookiesDescription": "Можете да омогућите или онемогућите аналитичке, маркетиншке и функционалне колачиће у било ком тренутку у подешавањима колачића. Такође можете ручно или аутоматски избрисати све постојеће колачиће преко свог интернет претраживача. Међутим, колачићи се могу поново поставити након вашег пристанка након сваке наредне посете овој платформи. Ако не избришете колачиће, ваше поставке колачића се чувају 60 дана, након чега ће од вас поново бити затражено да дате сагласност.", "app.containers.CookiePolicy.manageCookiesPreferences": "Идите на своју {manageCookiesPreferencesButtonText} да бисте видели комплетну листу интеграција трећих страна које се користе на овој платформи и да бисте управљали својим преференцама.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "подешавања колачића", "app.containers.CookiePolicy.manageCookiesTitle": "Управљање колачићима", "app.containers.CookiePolicy.viewPreferencesButtonText": "Подешавања колачића", "app.containers.CookiePolicy.viewPreferencesText": "Доле наведене категорије колачића се можда не односе на све посетиоце или платформе; погледајте свој {viewPreferencesButton} за комплетну листу интеграција треће стране које се примењују на вас.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "За шта користимо колачиће?", "app.containers.CustomPageShow.editPage": "Уреди страницу", "app.containers.CustomPageShow.goBack": "Повратак назад", "app.containers.CustomPageShow.notFound": "Страница није пронађена", "app.containers.DisabledAccount.bottomText": "Можете се поново пријавити од {date}.", "app.containers.DisabledAccount.termsAndConditions": "Услови коришћења", "app.containers.DisabledAccount.text2": "Ваш налог на платформи за учешће {orgName} је привремено онемогућен због кршења смерница заједнице. За више информација о овоме, можете консултовати {TermsAndConditions}.", "app.containers.DisabledAccount.title": "Ваш налог је привремено онемогућен", "app.containers.EventsShow.addToCalendar": "Дода<PERSON> у календар", "app.containers.EventsShow.editEvent": "Уредите догађај", "app.containers.EventsShow.emailSharingBody2": "Attend this event: {eventTitle}. Read more at {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Event date and time", "app.containers.EventsShow.eventFrom2": "Објављено у \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Повратак назад", "app.containers.EventsShow.goToProject": "Погледајте све", "app.containers.EventsShow.haveRegistered": "have registered", "app.containers.EventsShow.icsError": "Грешка при преузимању ИЦС датотеке", "app.containers.EventsShow.linkToOnlineEvent": "Линк до онлајн догађаја", "app.containers.EventsShow.locationIconAltText": "Location", "app.containers.EventsShow.metaTitle": "Event: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Онлине састанак", "app.containers.EventsShow.onlineLinkIconAltText": "Online meeting link", "app.containers.EventsShow.registered": "registered", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 registrants} one {1 registrant} other {# registrants}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} registrants", "app.containers.EventsShow.registrantsIconAltText": "Registrants", "app.containers.EventsShow.socialMediaSharingMessage": "Присуствујте овом догађају: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# учесник} other {# учесника}}", "app.containers.EventsViewer.allTime": "Све време", "app.containers.EventsViewer.date": "Датум", "app.containers.EventsViewer.thisMonth2": "Предстојећи месец", "app.containers.EventsViewer.thisWeek2": "Предстојећа недеља", "app.containers.EventsViewer.today": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAContribution": "Додајте допринос", "app.containers.IdeaButton.addAPetition": "Add a petition", "app.containers.IdeaButton.addAProject": "Додајте пројекат", "app.containers.IdeaButton.addAProposal": "Add a proposal", "app.containers.IdeaButton.addAQuestion": "Додајте питање", "app.containers.IdeaButton.addAnInitiative": "Add an initiative", "app.containers.IdeaButton.addAnOption": "Додајте опцију", "app.containers.IdeaButton.postingDisabled": "Нове пријаве се тренутно не прихватају", "app.containers.IdeaButton.postingInNonActivePhases": "Нове пријаве се могу додати само у активним фазама.", "app.containers.IdeaButton.postingInactive": "Нове пријаве се тренутно не прихватају.", "app.containers.IdeaButton.postingLimitedMaxReached": "Већ сте попунили ову анкету. Хвала на одговору!", "app.containers.IdeaButton.postingNoPermission": "Нове пријаве се тренутно не прихватају", "app.containers.IdeaButton.postingNotYetPossible": "Нове пријаве се још не прихватају.", "app.containers.IdeaButton.signInLinkText": "Пријавите се", "app.containers.IdeaButton.signUpLinkText": "пријави се", "app.containers.IdeaButton.submitAnIssue": "Пошаљите коментар", "app.containers.IdeaButton.submitYourIdea": "Пошаљите своју идеју", "app.containers.IdeaButton.takeTheSurvey": "Попуните анкету", "app.containers.IdeaButton.verificationLinkText": "Потврдите свој идентитет сада.", "app.containers.IdeaCard.readMore": "Опширније", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {нема коментара} one {1 коментар} other {# цомментс}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {no votes} one {1 vote} other {# votes}} out of {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Close filters panel", "app.containers.IdeaCards.a11y_totalItems": "Укупно постова: {ideasCount}", "app.containers.IdeaCards.all": "Све", "app.containers.IdeaCards.allStatuses": "Сви статуси", "app.containers.IdeaCards.contributions": "Прилози", "app.containers.IdeaCards.ideaTerm": "Идеје", "app.containers.IdeaCards.initiatives": "Иницијативе", "app.containers.IdeaCards.issueTerm": "Issues", "app.containers.IdeaCards.list": "Листа", "app.containers.IdeaCards.map": "Мапа", "app.containers.IdeaCards.mostDiscussed": "Most discussed", "app.containers.IdeaCards.newest": "Најновијe", "app.containers.IdeaCards.noFilteredResults": "Нема резултата. Покушајте са другим филтером или термином за претрагу.", "app.containers.IdeaCards.numberResults": "Results ({postCount})", "app.containers.IdeaCards.oldest": "Најстарији", "app.containers.IdeaCards.optionTerm": "Опције", "app.containers.IdeaCards.petitions": "Petitions", "app.containers.IdeaCards.popular": "Највише гласано", "app.containers.IdeaCards.projectFilterTitle": "Пројекти", "app.containers.IdeaCards.projectTerm": "Пројекти", "app.containers.IdeaCards.proposals": "Proposals", "app.containers.IdeaCards.questionTerm": "Питања", "app.containers.IdeaCards.random": "Насумично", "app.containers.IdeaCards.resetFilters": "Ресетујте филтере", "app.containers.IdeaCards.showXResults": "Прикажи {ideasCount, plural, one {# резултат} other {# резултата}}", "app.containers.IdeaCards.sortTitle": "Сортирање", "app.containers.IdeaCards.statusTitle": "Статус", "app.containers.IdeaCards.statusesTitle": "Статус", "app.containers.IdeaCards.topics": "Тема", "app.containers.IdeaCards.topicsTitle": "Тема", "app.containers.IdeaCards.trending": "У тренду", "app.containers.IdeaCards.tryDifferentFilters": "Нема резултата. Покушајте са другим филтером или термином за претрагу.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} comments} one {{ideasCount} comment} other {{ideasCount} comments}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} contributions} one {{ideasCount} contribution} other {{ideasCount} contributions}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} идеја} one {{ideasCount} идеја} other {{ideasCount} идеја}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} initiatives} one {{ideasCount} initiative} other {{ideasCount} initiatives}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} options} one {{ideasCount} option} other {{ideasCount} options}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} petitions} one {{ideasCount} petition} other {{ideasCount} petitions}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} пројеката} one {{ideasCount} пројекат} other {{ideasCount} пројеката}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} proposals} one {{ideasCount} proposal} other {{ideasCount} proposals}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} questions} one {{ideasCount} question} other {{ideasCount} questions}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# резултат} other {# резултата}}", "app.containers.IdeasEditPage.contributionFormTitle": "Уреди допринос", "app.containers.IdeasEditPage.editedPostSave": "сачувати", "app.containers.IdeasEditPage.fileUploadError": "Отпремање једне или више датотека није успело. Проверите величину и формат датотеке и покушајте поново.", "app.containers.IdeasEditPage.formTitle": "Уредите идеју", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Уредите свој пост. Додајте нове и промените старе информације.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Уреди {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Edit initiative", "app.containers.IdeasEditPage.issueFormTitle": "Уреди коментар", "app.containers.IdeasEditPage.optionFormTitle": "Измени опцију", "app.containers.IdeasEditPage.petitionFormTitle": "Edit petition", "app.containers.IdeasEditPage.projectFormTitle": "Уреди пројекат", "app.containers.IdeasEditPage.proposalFormTitle": "Edit proposal", "app.containers.IdeasEditPage.questionFormTitle": "Уреди питање", "app.containers.IdeasEditPage.save": "сачувати", "app.containers.IdeasEditPage.submitApiError": "Дошло је до проблема при слању обрасца. Проверите да ли постоје грешке и покушајте поново.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "All inputs posted", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Истражите све уносе објављене на платформи за учешће {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Posts | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Постови", "app.containers.IdeasIndexPage.loadMore": "Учитај више...", "app.containers.IdeasIndexPage.loading": "Учитавање...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Подразумевано, ваши поднесци ће бити повезани са вашим профилом, осим ако не изаберете ову опцију.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Објавите анонимно", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Видљивост профила", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "This survey is not currently open for responses. Please return to the project for more information.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "This survey is not currently active.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Вратите се на пројекат", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Већ сте попунили ову анкету.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Анкета је послата", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Хвала на одговору!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Опис прилога мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Тело идеје мора да има више од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Наслов прилога мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "Наслов прилога мора да има више од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Please select at least one cosponsor", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Опис идеје мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Опис идеје мора да има више од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Наведите опис", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Наслов идеје мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Наслов идеје мора да има више од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "The initiative description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "The initiative description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "The initiative title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "The initiative title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Опис проблема мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Опис проблема мора да има више од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Наслов издања мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "The issue title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_number_required": "This field is required, please enter a valid number", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Опис опције мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "The option description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Наслов опције мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Наслов опције мора бити дужи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Изаберите најмање једну ознаку", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "The petition description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "The petition description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "The petition title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "The petition title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Опис пројекта мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Опис пројекта мора бити дужи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Наслов пројекта мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Наслов пројекта мора бити дужи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "The proposal description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "The proposal description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "The proposal title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "The proposal title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Унесите број", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Унесите број", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "Опис питања мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "Опис питања мора да има више од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "Наслов питања мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "Наслов питања мора да има више од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Наведите наслов", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Опис прилога мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Опис прилога мора имати најмање 30 знакова", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Наслов прилога мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Наслов прилога мора имати најмање 10 знакова", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Опис идеје мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Опис идеје мора да има најмање 30 знакова", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Наведите наслов", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Наслов идеје мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Наслов идеје мора да има најмање 10 знакова", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Можда сте користили једну или више речи које се {guidelinesLink}сматрају вулгарним. Измените свој текст да бисте уклонили све вулгарности које би могле бити присутне.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "The initiative description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "The initiative description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "The initiative title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "The initiative title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Опис проблема мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Опис проблема мора да има најмање 30 знакова", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Наслов издања мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Наслов издања мора да има најмање 10 знакова", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Опис опције мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Опис опције мора да има најмање 30 знакова", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Наслов опције мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Наслов опције мора да има најмање 10 знакова", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "The petition description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "The petition description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "The petition title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "The petition title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Опис пројекта мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Опис пројекта мора имати најмање 30 знакова", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Наслов пројекта мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Наслов пројекта мора имати најмање 10 знакова", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "The proposal description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "The proposal description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "The proposal title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "The proposal title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Наведите опис", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Опис питања мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Опис питања мора да има најмање 30 знакова", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Наслов питања мора бити краћи од 80 знакова", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Наслов питања мора да има најмање 10 знакова", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "Поништити, отказати", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "Yes, I want to leave", "app.containers.IdeasNewPage.contributionMetaTitle1": "Add new contribution to project | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Уреди анкету", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Поставите пријаву и придружите се разговору на платформи за учешће {orgName}.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Add new idea to project | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Add new initiative to project | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Add new issue to project | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Are you sure you want to leave?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Your draft answers have been saved privately and you can return to complete this later.", "app.containers.IdeasNewPage.leaveSurvey": "Leave survey", "app.containers.IdeasNewPage.leaveSurveyText": "Ваши одговори неће бити сачувани.", "app.containers.IdeasNewPage.optionMetaTitle1": "Add new option to project | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Add new petition to project | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Add new project to project | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Add new proposal to project | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Add new question to project | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Accept invitation", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Co-sponsorship invitation", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Co-sponsors", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "You have been invited to become a co-sponsor.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Invitation accepted", "app.containers.IdeasShow.Cosponsorship.pending": "pending", "app.containers.IdeasShow.MetaInformation.attachments": "Прилози", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} на дан {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Тренутни статус", "app.containers.IdeasShow.MetaInformation.location": "Локација", "app.containers.IdeasShow.MetaInformation.postedBy": "Поставио", "app.containers.IdeasShow.MetaInformation.similar": "Similar inputs", "app.containers.IdeasShow.MetaInformation.topics": "Тема", "app.containers.IdeasShow.commentCTA": "Додајте коментар", "app.containers.IdeasShow.contributionEmailSharingBody": "Подржите овај допринос '{postTitle}' на {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Подржите овај допринос: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Хвала што сте послали свој допринос!", "app.containers.IdeasShow.contributionTwitterMessage": "Подржите овај допринос: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Подржите овај допринос: {postTitle}", "app.containers.IdeasShow.currentStatus": "Тренутни статус", "app.containers.IdeasShow.deletedUser": "непознатог аутора", "app.containers.IdeasShow.ideaEmailSharingBody": "Подржите моју идеју '{ideaTitle}' у {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Подржите моју идеју: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Подржите ову идеју: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Подржите ову идеју: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Подржите овај коментар: {postTitle}", "app.containers.IdeasShow.imported": "Увезено", "app.containers.IdeasShow.importedTooltip": "Ова<PERSON> {inputTerm} је сакупљен ван мреже и аутоматски постављен на платформу.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Support this initiative '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Support this initiative: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Thanks for submitting your initiative!", "app.containers.IdeasShow.initiativeTwitterMessage": "Support this initiative: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Support this initiative: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Подржите овај коментар '{postTitle}' у {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Подржите овај коментар: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Хвала што сте послали коментар!", "app.containers.IdeasShow.issueTwitterMessage": "Подржите овај коментар: {postTitle}", "app.containers.IdeasShow.metaTitle": "Input: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Подржите ову опцију '{postTitle}' на {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Подржите ову опцију: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Ваша опција је успешно објављена!", "app.containers.IdeasShow.optionTwitterMessage": "Подржите ову опцију: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Подржите ову опцију: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Support this petition '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Support this petition: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Thanks for submitting your petition!", "app.containers.IdeasShow.petitionTwitterMessage": "Support this petition: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Support this petition: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Подржите овај пројекат '{postTitle}' у {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Подржите овај пројекат: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Хвала што сте послали пројекат!", "app.containers.IdeasShow.projectTwitterMessage": "Подржите овај пројекат: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Подржите овај пројекат: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Support this proposal '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Support this proposal: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Thanks for submitting your proposal!", "app.containers.IdeasShow.proposalTwitterMessage": "Support this proposal: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Support this proposal: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Time left to vote:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} out of {votingThreshold} required votes", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Cancel vote", "app.containers.IdeasShow.proposals.VoteControl.days": "days", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "our guidelines", "app.containers.IdeasShow.proposals.VoteControl.hours": "hours", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status and votes", "app.containers.IdeasShow.proposals.VoteControl.minutes": "mins", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "More info", "app.containers.IdeasShow.proposals.VoteControl.vote": "Vote", "app.containers.IdeasShow.proposals.VoteControl.voted": "Voted", "app.containers.IdeasShow.proposals.VoteControl.votedText": "You'll get notified when this initiative goes to the next step. {x, plural, =0 {There's {xDays} left.} one {There's {xDays} left.} other {There are {xDays} left.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Your vote has been submitted!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Unfortunately, you cannot vote on this proposal. Read why in {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {less than a day} one {one day} other {# days}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {no votes} one {1 vote} other {# votes}}", "app.containers.IdeasShow.questionEmailSharingBody": "Придружите се дискусији о овом питању '{postTitle}' у {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Придружите се дискусији: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Ваше питање је успешно постављено!", "app.containers.IdeasShow.questionTwitterMessage": "Придружите се дискусији: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Придружите се дискусији: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Зашто желите да ово пријавите као нежељену пошту?", "app.containers.IdeasShow.share": "Подели", "app.containers.IdeasShow.sharingModalSubtitle": "Досегните више људи и нека се ваш глас чује.", "app.containers.IdeasShow.sharingModalTitle": "Хвала што сте послали идеју!", "app.containers.Navbar.completeOnboarding": "Попуните свој профил", "app.containers.Navbar.completeProfile": "Комплетан профил", "app.containers.Navbar.confirmEmail2": "Confirm email", "app.containers.Navbar.unverified": "Неверификовано", "app.containers.Navbar.verified": "Проверено", "app.containers.NewAuthModal.beforeYouFollow": "Пре него што следите", "app.containers.NewAuthModal.beforeYouParticipate": "Пре него што учествујете", "app.containers.NewAuthModal.completeYourProfile": "Попуните свој профил", "app.containers.NewAuthModal.confirmYourEmail": "Потврди свој емаил", "app.containers.NewAuthModal.logIn": "Пријавите се", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Прегледајте услове у наставку да бисте наставили.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Попуните свој профил.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Вратите се на опције за пријаву", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Немате налог? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Региструјте се", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Код мора имати 4 цифре.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Наставите са ФранцеЦоннецт-ом", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "На овој платформи нису омогућене методе аутентификације.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "Ако наставите, прихватате да примате е-поруке са ове платформе. Можете да изаберете које имејлове желите да примате на страници „Моја подешавања“.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Наведите адресу е-поште у исправном формату, на пример име@провидер.цом", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Наведите адресу е-поште", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Унесите своју адресу е-поште да бисте наставили.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Заборавили сте лозинку?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Пријавите се на свој налог: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Молим вас унесите вашу шифру", "app.containers.NewAuthModal.steps.Password.password": "Лозинка", "app.containers.NewAuthModal.steps.Password.rememberMe": "Сети ме се", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Немојте бирати ако користите јавни рачунар", "app.containers.NewAuthModal.steps.Success.allDone": "Завршено", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Сада наставите са учешћем.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Ваш идентитет је верификован. Сада сте пуноправни члан заједнице на овој платформи.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Сада сте верификовани!", "app.containers.NewAuthModal.steps.close": "Близу", "app.containers.NewAuthModal.steps.continue": "Настави", "app.containers.NewAuthModal.whatAreYouInterestedIn": "За шта си заинтересован?", "app.containers.NewAuthModal.youCantParticipate": "You can't participate", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {нема непрегледаних обавештења} one {1 непрегледано обавештење} other {# непрегледано обавештење}}", "app.containers.NotificationMenu.adminRightsReceived": "Сада сте администратор платформе", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Ва<PERSON> коментар на \"{postTitle}\" је обрисан од стране администратора јер\n      {reasonCode, select, irrelevant {то је небитно} inappropriate {његов садржај је неприкладан} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} accepted your co-sponsorship invitation", "app.containers.NotificationMenu.deletedUser": "Непознати аутор", "app.containers.NotificationMenu.error": "Учитавање обавештења није успело", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} је интерно коментариса<PERSON> унос који вам је додељен", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} је интерно коментарисало унос који сте ви интерно коментарисали", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} је интерно коментари<PERSON><PERSON><PERSON> унос у пројекат којим управљате", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} је интерно коментарисао недодељени унос у пројекту којим се не управља", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} је коментарисало ваш интерни коментар", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} invited you to co-sponsor a contribution", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} invited you to co-sponsor an idea", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} вас је позвао да будете коспонзор предлога", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} invited you to co-sponsor an issue", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} invited you to co-sponsor an option", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} invited you to co-sponsor a petition", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} invited you to co-sponsor a project", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} invited you to co-sponsor a proposal", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} invited you to co-sponsor a question", "app.containers.NotificationMenu.loadMore": "Учитај више...", "app.containers.NotificationMenu.loading": "Учитавање обавештења...", "app.containers.NotificationMenu.mentionInComment": "{name} вас је поменуо у коментару", "app.containers.NotificationMenu.mentionInInternalComment": "{name} вас је поменуо у интерном коментару", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} вас је поменуо у званичном ажурирању", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "You didn't submit your survey", "app.containers.NotificationMenu.noNotifications": "Још увек немате никаква обавештења", "app.containers.NotificationMenu.notificationsLabel": "Обавештења", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} је дао званично ажурирање о доприносу који пратите", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} је дао званично ажурирање идеје коју пратите", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} је дао званично ажурирање о иницијативи коју пратите", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} је дао званично ажурирање о проблему који пратите", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} је дао званично ажурирање опције коју пратите", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} gave an official update on a petition you follow", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} је дао званично ажурирање о пројекту који пратите", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} gave an official update on a proposal you follow", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} је дао званично ажурирање на питање које пратите", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} вам је додељено", "app.containers.NotificationMenu.projectModerationRightsReceived": "Сада сте менаџер пројекта {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} је ушло у нову фазу", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} ће ући у нову фазу {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "Објављен је нови пројекат", "app.containers.NotificationMenu.projectReviewRequest": "{name} requested approval to publish the project \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} approved \"{projectTitle}\" for publication", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} статус је промењен у {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} је достигло цензус за гласање", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} је прихватио ваш позив", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} је коментарисало допринос који пратите", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} је коментарисао идеју коју пратите", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} је коментарисао иницијативу коју пратите", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} је коментарисао проблем који пратите", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} је коментарисало опцију коју пратите", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} commented on a petition that you follow", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} је коментарисало пројекат који пратите", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} commented on a proposal that you follow", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} је коментарисао питање које пратите", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} је пријавило „{postTitle}“ као нежељену пошту", "app.containers.NotificationMenu.userReactedToYourComment": "{name} је реаговао на ваш коментар", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} је пријавио коментар на \"{postTitle}\" као непожељан", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Нисте предали своје гласове", "app.containers.NotificationMenu.votingBasketSubmitted": "Успешно сте гласали", "app.containers.NotificationMenu.votingLastChance": "Последња прилика да гласате за {phaseTitle}", "app.containers.NotificationMenu.votingResults": "Откривено {phaseTitle} резултата гласања", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} вам је доделио {postTitle}", "app.containers.PasswordRecovery.emailError": "Ово не изгледа као важећа е-пошта", "app.containers.PasswordRecovery.emailLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.PasswordRecovery.emailPlaceholder": "Моја адреса е-поште", "app.containers.PasswordRecovery.helmetDescription": "Ресетујте страницу са лозинком", "app.containers.PasswordRecovery.helmetTitle": "Ресетујте вашу лозинку", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Ако је ова адреса е-поште регистрована на платформи, послата је веза за ресетовање лозинке.", "app.containers.PasswordRecovery.resetPassword": "Пошаљите везу за ресетовање лозинке", "app.containers.PasswordRecovery.submitError": "Нисмо могли да пронађемо налог повезан са овом е-поруком. Уместо тога, можете покушати да се региструјете.", "app.containers.PasswordRecovery.subtitle": "Где можемо да пошаљемо линк за одабир нове лозинке?", "app.containers.PasswordRecovery.title": "Ресетовање лозинке", "app.containers.PasswordReset.helmetDescription": "Ресетујте страницу са лозинком", "app.containers.PasswordReset.helmetTitle": "Ресетујте вашу лозинку", "app.containers.PasswordReset.login": "Пријавите се", "app.containers.PasswordReset.passwordError": "Лозинка мора имати најмање 8 знакова", "app.containers.PasswordReset.passwordLabel": "Лозинка", "app.containers.PasswordReset.passwordPlaceholder": "Нова лозинка", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Ваша лозинка је успешно ажурирана.", "app.containers.PasswordReset.pleaseLogInMessage": "Молимо пријавите се са својом новом лозинком.", "app.containers.PasswordReset.requestNewPasswordReset": "Затражите ново ресетовање лозинке", "app.containers.PasswordReset.submitError": "Нешто није у реду. Покушајте поново касније.", "app.containers.PasswordReset.title": "Ресетујте вашу лозинку", "app.containers.PasswordReset.updatePassword": "Потврдите нову лозинку", "app.containers.ProjectFolderCards.allProjects": "Сви пројекти", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} тренутно ради на", "app.containers.ProjectFolderShowPage.editFolder": "Уреди фолдер", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Информације о овом пројекту", "app.containers.ProjectFolderShowPage.metaTitle1": "Folder: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Опширније", "app.containers.ProjectFolderShowPage.seeLess": "Видите мање", "app.containers.ProjectFolderShowPage.share": "Подели", "app.containers.Projects.PollForm.document": "Документ", "app.containers.Projects.PollForm.formCompleted": "Хвала вам! Ваш одговор је примљен.", "app.containers.Projects.PollForm.maxOptions": "мак. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Већ сте узели ову анкету.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Ова анкета се може узети само када је ова фаза активна.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Ова анкета тренутно није омогућена", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Тренутно је немогуће попунити ову анкету.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Анкета више није доступна јер овај пројекат више није активан.", "app.containers.Projects.PollForm.sendAnswer": "Пошаљи", "app.containers.Projects.a11y_phase": "Фаза {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Преглед фаза", "app.containers.Projects.a11y_titleInputs": "Сви доприноси достављени овом пројекту", "app.containers.Projects.a11y_titleInputsPhase": "Сви доприноси достављени у овој фази", "app.containers.Projects.accessRights": "Права приступа", "app.containers.Projects.addedToBasket": "Додато у вашу корпу", "app.containers.Projects.allocateBudget": "Одредите свој буџет", "app.containers.Projects.archived": "Ар<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.basketSubmitted": "Ваша корпа је послата!", "app.containers.Projects.contributions": "Прилози", "app.containers.Projects.createANewPhase": "Направите нову фазу", "app.containers.Projects.currentPhase": "Тренутна фаза", "app.containers.Projects.document": "Документ", "app.containers.Projects.editProject": "Уреди пројекат", "app.containers.Projects.emailSharingBody": "Шта мислите о овој иницијативи? Гласајте за то и поделите дискусију на {initiativeUrl} да би се ваш глас чуо!", "app.containers.Projects.emailSharingSubject": "Подржите моју иницијативу: {initiativeTitle}.", "app.containers.Projects.endedOn": "За<PERSON><PERSON><PERSON><PERSON>но {date}", "app.containers.Projects.events": "Догађаји", "app.containers.Projects.header": "Пројекти", "app.containers.Projects.ideas": "Идеје", "app.containers.Projects.information": "Информације", "app.containers.Projects.initiatives": "Initiatives", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Прегледајте документ", "app.containers.Projects.invisibleTitlePhaseAbout": "О овој фази", "app.containers.Projects.invisibleTitlePoll": "Учествујте у анкети", "app.containers.Projects.invisibleTitleSurvey": "Попуните анкету", "app.containers.Projects.issues": "Коментари", "app.containers.Projects.liveDataMessage": "You're viewing real-time data. Participant counts are continuously updated for administrators. Please note that regular users see cached data, which may result in slight differences in the numbers.", "app.containers.Projects.location": "Локација:", "app.containers.Projects.manageBasket": "Управљајте корпом", "app.containers.Projects.meetMinBudgetRequirement": "Испуните минимални буџет да бисте послали своју корпу.", "app.containers.Projects.meetMinSelectionRequirement": "Испуните тражени избор да бисте послали своју корпу.", "app.containers.Projects.metaTitle1": "Пројекат: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Потребан минимални буџет", "app.containers.Projects.myBasket": "Корпа", "app.containers.Projects.navPoll": "Анкета", "app.containers.Projects.navSurvey": "Анкета", "app.containers.Projects.newPhase": "Нова фаза", "app.containers.Projects.nextPhase": "Следећа фаза", "app.containers.Projects.noEndDate": "Кон<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>о", "app.containers.Projects.noItems": "Још увек нисте изабрали ниједну ставку", "app.containers.Projects.noPastEvents": "Нема прошлих догађаја за приказ", "app.containers.Projects.noPhaseSelected": "Ниједна фаза није изабрана", "app.containers.Projects.noUpcomingOrOngoingEvents": "Тренутно нису заказани предстојећи или текући догађаји.", "app.containers.Projects.offlineVotersTooltip": "This number does not reflect any offline voter counts.", "app.containers.Projects.options": "Опције", "app.containers.Projects.participants": "Учесници", "app.containers.Projects.participantsTooltip4": "Овај број такође одражава анонимно подношење анкете. Анонимно подношење анкета је могуће ако су анкете отворене за све (погледајте картицу {accessRightsLink} за овај пројекат).", "app.containers.Projects.pastEvents": "Прошли догађаји", "app.containers.Projects.petitions": "Petitions", "app.containers.Projects.phases": "Фазе", "app.containers.Projects.previousPhase": "Претходна фаза", "app.containers.Projects.project": "Пројекат", "app.containers.Projects.projectTwitterMessage": "Нека се ваш глас чује! Учествујте у {projectName} | {orgName}", "app.containers.Projects.projects": "Пројекти", "app.containers.Projects.proposals": "Proposals", "app.containers.Projects.questions": "Питања", "app.containers.Projects.readLess": "Читај мање", "app.containers.Projects.readMore": "Опширније", "app.containers.Projects.removeItem": "Обриши предмет", "app.containers.Projects.requiredSelection": "Неопходан избор", "app.containers.Projects.reviewDocument": "Прегледајте документ", "app.containers.Projects.seeTheContributions": "Погледајте доприносе", "app.containers.Projects.seeTheIdeas": "Погледајте идеје", "app.containers.Projects.seeTheInitiatives": "See the initiatives", "app.containers.Projects.seeTheIssues": "Погледајте коментаре", "app.containers.Projects.seeTheOptions": "Погледајте опције", "app.containers.Projects.seeThePetitions": "See the petitions", "app.containers.Projects.seeTheProjects": "Погледајте пројекте", "app.containers.Projects.seeTheProposals": "See the proposals", "app.containers.Projects.seeTheQuestions": "Видите питања", "app.containers.Projects.seeUpcomingEvents": "Погледајте предстојеће догађаје", "app.containers.Projects.share": "Подели", "app.containers.Projects.shareThisProject": "Поделите овај пројекат", "app.containers.Projects.submitMyBasket": "Пошаљите корпу", "app.containers.Projects.survey": "Анкета", "app.containers.Projects.takeThePoll": "Учествујте у анкети", "app.containers.Projects.takeTheSurvey": "Попуните анкету", "app.containers.Projects.timeline": "Временска линија", "app.containers.Projects.upcomingAndOngoingEvents": "Предстојећи и текући догађаји", "app.containers.Projects.upcomingEvents": "Предстојећи догађаји", "app.containers.Projects.whatsAppMessage": "{projectName} | са платформе за учешће од {orgName}", "app.containers.Projects.yourBudget": "Укупан буџет", "app.containers.ProjectsIndexPage.metaDescription": "Истражите све текуће пројекте {orgName} да бисте разумели како можете да учествујете.\n Дођите да разговарате о локалним пројектима који су вам најважнији.", "app.containers.ProjectsIndexPage.metaTitle1": "Пројекти • {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Пројекти", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Желим да волонтирам", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Молимо прво {signInLink} или {signUpLink} да бисте волонтирали за ову активност", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Учешће тренутно није отворено за ову активност.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "Пријавите се", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "пријави се", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Повлачим своју понуду за волонтирање", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {нема волонтера} one {#добровољац} other {# волонтера}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Warning: The embedded survey may have accessibility issues for screenreader users. If you experience any challenges, please reach out to the platform admin to receive a link to the survey from the original platform. Alternatively, you can request other ways to fill out the survey.", "app.containers.ProjectsShowPage.process.survey.survey": "Анкета", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Да бисте сазнали да ли можете да учествујете у овој анкети, прво {logInLink} на платформи.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Ова анкета се може узети само када је ова фаза на временској линији активна.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Молимо {completeRegistrationLink} да попуните анкету.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Ова анкета тренутно није омогућена", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "За попуњавање ове анкете потребна је верификација вашег идентитета. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "The survey is no longer available, since this project is no longer active.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "комплетне регистрације", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "Пријавите се", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "пријави се", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Верификујте свој налог сада.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Само одређени корисници могу прегледати овај документ. Молимо прво {signUpLink} или {logInLink}.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Овај документ се може прегледати само када је ова фаза активна.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Молимо {completeRegistrationLink} да прегледате документ.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Нажалост, немате права да прегледате овај документ.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Преглед овог документа захтева верификацију вашег налога. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Документ више није доступан, пошто овај пројекат више није активан.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 pick} other {# picks}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "The percentage of participants who picked this option.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "The percentage of total votes this option received.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Цена:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Show more", "app.containers.ReactionControl.a11y_likesDislikes": "Укупно свиђања: {likesCount}, укупно несвиђања: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "You cancelled your dislike for this input successfully.", "app.containers.ReactionControl.cancelLikeSuccess": "You cancelled your like for this input successfully.", "app.containers.ReactionControl.dislikeSuccess": "You disliked this input successfully.", "app.containers.ReactionControl.likeSuccess": "You liked this input successfully.", "app.containers.ReactionControl.reactionErrorSubTitle": "Због грешке није било могуће регистровати вашу реакцију. Молим покушајте поново за неколико минута.", "app.containers.ReactionControl.reactionSuccessTitle": "Ваша реакција је успешно регистрована!", "app.containers.ReactionControl.vote": "Vote", "app.containers.ReactionControl.voted": "Voted", "app.containers.SearchInput.a11y_cancelledPostingComment": "Cancelled posting comment.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} comments have loaded.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# events have loaded} one {# event has loaded} other {# events have loaded}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# results have loaded} one {# result has loaded} other {# results have loaded}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# search results have loaded} one {# search result has loaded} other {# search results have loaded}}.", "app.containers.SearchInput.removeSearchTerm": "Уклони термин за претрагу", "app.containers.SearchInput.searchAriaLabel": "Претрага", "app.containers.SearchInput.searchLabel": "Претрага", "app.containers.SearchInput.searchPlaceholder": "Претрага", "app.containers.SearchInput.searchTerm": "Појам за претрагу: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "ФранцеЦоннецт је решење које је предложила француска држава да обезбеди и поједностави пријаву на више од 700 онлајн услуга.", "app.containers.SignIn.or": "<PERSON><PERSON>", "app.containers.SignIn.signInError": "Наведене информације нису тачне. Кликните на „Заборавили сте лозинку?“ да ресетујете лозинку.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Користите ФранцеЦоннецт да бисте се пријавили, регистровали или верификовали свој налог.", "app.containers.SignIn.whatIsFranceConnect": "Шта је Франце Цоннецт?", "app.containers.SignUp.adminOptions2": "For admins and project managers", "app.containers.SignUp.backToSignUpOptions": "Вратите се на опције регистрације", "app.containers.SignUp.continue": "Настави", "app.containers.SignUp.emailConsent": "Пријављивањем прихватате да примате е-поруке са ове платформе. Можете да изаберете које имејлове желите да примате на страници „Моја подешавања“.", "app.containers.SignUp.emptyFirstNameError": "Унесите своје име", "app.containers.SignUp.emptyLastNameError": "Унесите своје презиме", "app.containers.SignUp.firstNamesLabel": "Име", "app.containers.SignUp.goToLogIn": "Већ имате налог? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "Прочитао/ла сам и слажем се са {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "Прочитао/ла сам и слажем се са {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "Прихватам да ће се подаци користити на митгесталтен.виен.гв.ат. Додатне информације можете пронаћи {link}.", "app.containers.SignUp.invitationErrorText": "Ваша позивница је истекла или је већ искоришћена. Ако сте већ користили везу са позивницом за креирање налога, покушајте да се пријавите. У супротном, пријавите се да бисте креирали нови налог.", "app.containers.SignUp.lastNameLabel": "Презиме", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Пратите своје области фокуса да бисте били обавештени о њима:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Пратите своје омиљене теме да бисте били обавештени о њима:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Сачувај подешавања", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Прескочи ово за сада", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Прихватите нашу политику приватности да бисте наставили", "app.containers.SignUp.signUp2": "Региструјте се", "app.containers.SignUp.skip": "Прескочите овај корак", "app.containers.SignUp.tacError": "Прихватите наше услове и одредбе да бисте наставили", "app.containers.SignUp.thePrivacyPolicy": "политика приватности", "app.containers.SignUp.theTermsAndConditions": "услови", "app.containers.SignUp.unknownError": "{tenantName, select, LiberalDemocrats {Чини се да сте раније покушали да се региструјете, а да нисте довршили процес. Уместо тога, кликните на Лог Ин користећи акредитиве изабране током претходног покушаја.} other {Нешто није у реду. Покушајте поново касније.}}", "app.containers.SignUp.viennaConsentEmail": "Адреса Е-поште", "app.containers.SignUp.viennaConsentFirstName": "Име", "app.containers.SignUp.viennaConsentFooter": "Можете да промените информације о свом профилу након што се пријавите. Ако већ имате налог са истом адресом е-поште на митгесталтен.виен.гв.ат, он ће бити повезан са вашим тренутним налогом.", "app.containers.SignUp.viennaConsentHeader": "Биће пренети следећи подаци:", "app.containers.SignUp.viennaConsentLastName": "Презиме", "app.containers.SignUp.viennaConsentUserName": "Корисничко име", "app.containers.SignUp.viennaDataProtection": "бечку политику приватности", "app.containers.SiteMap.contributions": "Прилози", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "Коментари", "app.containers.SiteMap.options": "Опције", "app.containers.SiteMap.projects": "Пројекти", "app.containers.SiteMap.questions": "Питања", "app.containers.SpamReport.buttonSave": "Извештај", "app.containers.SpamReport.buttonSuccess": "Успех", "app.containers.SpamReport.inappropriate": "То је неприкладно или увредљиво", "app.containers.SpamReport.messageError": "Дошло је до грешке при слању обрасца, покушајте поново.", "app.containers.SpamReport.messageSuccess": "Ваш извештај је послат", "app.containers.SpamReport.other": "Други разлог", "app.containers.SpamReport.otherReasonPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.wrong_content": "Ово није релевантно", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Уклони слику профила", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Ваши гласови о предлозима који су још отворени за гласање биће обрисани. Гласови о предлозима за које је рок за гласање истекао неће се брисати.", "app.containers.UsersEditPage.addPassword": "Додајте лозинку", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Да учествује у пројектима који захтевају верификацију.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Потврдите свој идентитет", "app.containers.UsersEditPage.bio": "О теби", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Не можете да измените ово поље јер садржи верификоване информације.", "app.containers.UsersEditPage.buttonSuccessLabel": "Успех", "app.containers.UsersEditPage.cancel": "Поништити, отказати", "app.containers.UsersEditPage.changeEmail": "Промена Е-маил", "app.containers.UsersEditPage.changePassword2": "Промени лозинку", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Кликните овде да бисте ажурирали своју верификацију.", "app.containers.UsersEditPage.conditionsLinkText": "нашим условима", "app.containers.UsersEditPage.contactUs": "Још један разлог за одлазак? {feedbackLink} и можда можемо помоћи.", "app.containers.UsersEditPage.deleteAccountSubtext": "Жао нам је да одете.", "app.containers.UsersEditPage.deleteMyAccount": "Обриши мој профил", "app.containers.UsersEditPage.deleteYourAccount": "Избришите свој налог", "app.containers.UsersEditPage.deletionSection": "Избришите свој налог", "app.containers.UsersEditPage.deletionSubtitle": "Ова радња се не може опозвати. Садржај који сте објавили на платформи биће анонимизован. Ако желите да избришете сав свој садржај, можете нас контактирати на <EMAIL>.", "app.containers.UsersEditPage.email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.emailEmptyError": "Наведите адресу е-поште", "app.containers.UsersEditPage.emailInvalidError": "Наведите адресу е-поште у исправном формату, на пример име@провидер.цом", "app.containers.UsersEditPage.feedbackLinkText": "Обавестите нас", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Име", "app.containers.UsersEditPage.firstNamesEmptyError": "Наведите име", "app.containers.UsersEditPage.h1": "Информације о вашем налогу", "app.containers.UsersEditPage.h1sub": "Измените информације о налогу", "app.containers.UsersEditPage.image": "Ава<PERSON><PERSON><PERSON> слика", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Кликните да бисте изабрали слику профила (макс. 5МБ)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Сва подешавања за ваш профил", "app.containers.UsersEditPage.language": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.lastName": "Презиме", "app.containers.UsersEditPage.lastNameEmptyError": "Наведите презиме", "app.containers.UsersEditPage.loading": "Учитавање...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Овде можете променити своју адресу е-поште или лозинку.", "app.containers.UsersEditPage.loginCredentialsTitle": "Акредитиве за пријављивање", "app.containers.UsersEditPage.messageError": "Нисмо успели да сачувамо ваш профил. Покушајте поново касније или контактирајте <EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Ваш профил је сачуван.", "app.containers.UsersEditPage.metaDescription": "Ово је страница са подешавањима профила {firstName} {lastName} на платформи за онлајн учешће {tenantName}. Овде можете да верификујете свој идентитет, измените информације о налогу, избришете налог и измените подешавања е-поште.", "app.containers.UsersEditPage.metaTitle1": "Profile settings page of {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Када кликнете на ово дугме, нећемо имати начина да вратимо ваш налог.", "app.containers.UsersEditPage.noNameWarning2": "Your name is currently displayed on the platform as: \"{displayName}\" because you have not entered your name. This is an autogenerated name. If you would like to change it, please enter your name below.", "app.containers.UsersEditPage.notificationsSubTitle": "Које врсте обавештења путем е-поште желите да примате? ", "app.containers.UsersEditPage.notificationsTitle": "Е<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ења", "app.containers.UsersEditPage.password": "Изаберите нову лозинку", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Наведите лозинку која има најмање {minimumPasswordLength} знакова", "app.containers.UsersEditPage.passwordAddSection": "Додајте лозинку", "app.containers.UsersEditPage.passwordAddSubtitle2": "Поставите лозинку и лако се пријавите на платформу, без потребе да сваки пут потврђујете своју е-пошту.", "app.containers.UsersEditPage.passwordChangeSection": "Промените своју лозинку", "app.containers.UsersEditPage.passwordChangeSubtitle": "Потврдите своју тренутну лозинку и промените је на нову.", "app.containers.UsersEditPage.privacyReasons": "Ако сте забринути за своју приватност, можете прочитати {conditionsLink}.", "app.containers.UsersEditPage.processing": "Слање...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Име је обавезно када наведете презиме", "app.containers.UsersEditPage.reasonsToStayListTitle": "Пре него што одеш...", "app.containers.UsersEditPage.submit": "Сачувај измене", "app.containers.UsersEditPage.tooManyEmails": "Примате превише е-порука? Можете да управљате подешавањима е-поште у подешавањима профила.", "app.containers.UsersEditPage.updateverification": "Да ли су се ваше званичне информације промениле? {reverifyButton}", "app.containers.UsersEditPage.user": "Када желите да вам пошаљемо е-пошту да вас обавестимо?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Можете учествовати у пројектима који захтевају верификацију.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Верификовани сте", "app.containers.UsersEditPage.verifyNow": "Потврдите сада", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Download your responses (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {нема лајкова} one {1 лике} other {# свиђа}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Унесите да је овај коментар објављен као одговор на:", "app.containers.UsersShowPage.areas": "Подручја", "app.containers.UsersShowPage.commentsWithCount": "Коментари ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Уреди мој профил", "app.containers.UsersShowPage.emptyInfoText": "Не пратите ниједну ставку горе наведеног филтера.", "app.containers.UsersShowPage.eventsWithCount": "Догађаји ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Прати ({followingCount})", "app.containers.UsersShowPage.inputs": "Инпутс", "app.containers.UsersShowPage.invisibleTitlePostsList": "Сви доприноси које је доставио овај учесник", "app.containers.UsersShowPage.invisibleTitleUserComments": "Сви коментари које је објавио овај учесник", "app.containers.UsersShowPage.loadMore": "Учитај више", "app.containers.UsersShowPage.loadMoreComments": "Учитајте још коментара", "app.containers.UsersShowPage.loadingComments": "Учитавање коментара...", "app.containers.UsersShowPage.loadingEvents": "Учитавање догађаја...", "app.containers.UsersShowPage.memberSince": "<PERSON><PERSON>ан од {date}", "app.containers.UsersShowPage.metaTitle1": "Profile page of {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Ова особа још увек није поставила ниједан коментар.", "app.containers.UsersShowPage.noCommentsForYou": "Овде још нема коментара.", "app.containers.UsersShowPage.noEventsForUser": "Још нисте присуствовали ниједном догађају.", "app.containers.UsersShowPage.postsWithCount": "Пријаве ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Фасцикле пројекта", "app.containers.UsersShowPage.projects": "Пројекти", "app.containers.UsersShowPage.proposals": "Предлози", "app.containers.UsersShowPage.seePost": "Погледајте подношење", "app.containers.UsersShowPage.surveyResponses": "Responses ({responses})", "app.containers.UsersShowPage.topics": "Теме", "app.containers.UsersShowPage.tryAgain": "Дошло је до грешке. Молимо Вас покушајте поново касније.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Ово је профилна страница {firstName} {lastName} на платформи за онлајн учешће {orgName}. Ево прегледа свих њихових доприноса.", "app.containers.VoteControl.close": "Близу", "app.containers.VoteControl.voteErrorTitle": "Нешто није у реду", "app.containers.admin.ContentBuilder.default": "default", "app.containers.admin.ContentBuilder.imageTextCards": "Image & text cards", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info & accordions", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 column", "app.containers.admin.ContentBuilder.projectDescription": "Project description", "app.containers.app.navbar.admin": "Управљајте платформом", "app.containers.app.navbar.allProjects": "Сви пројекти", "app.containers.app.navbar.ariaLabel": "Примарна", "app.containers.app.navbar.closeMobileNavMenu": "Затворите мобилни навигациони мени", "app.containers.app.navbar.editProfile": "Моја подешавања", "app.containers.app.navbar.fullMobileNavigation": "Фулл мобиле", "app.containers.app.navbar.logIn": "Пријавите се", "app.containers.app.navbar.logoImgAltText": "{orgName} Почетна", "app.containers.app.navbar.myProfile": "Моја активност", "app.containers.app.navbar.search": "Претрага", "app.containers.app.navbar.showFullMenu": "Прикажи цео мени", "app.containers.app.navbar.signOut": "Одјава", "app.containers.eventspage.errorWhenFetchingEvents": "Дошло је до грешке при учитавању догађаја. Покушајте поново да учитате страницу.", "app.containers.eventspage.events": "Догађаји", "app.containers.eventspage.eventsPageDescription": "Прикажи све догађаје објављене на платформи {orgName}.", "app.containers.eventspage.eventsPageTitle1": "Events | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Пројекти", "app.containers.eventspage.noPastEvents": "Нема прошлих догађаја за приказ", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Тренутно нису заказани предстојећи или текући догађаји.", "app.containers.eventspage.pastEvents": "Прошли догађаји", "app.containers.eventspage.upcomingAndOngoingEvents": "Предстојећи и текући догађаји", "app.containers.footer.accessibility-statement": "Изјава о приступачности", "app.containers.footer.ariaLabel": "Секундарни", "app.containers.footer.cookie-policy": "Декларација о колачићима", "app.containers.footer.cookieSettings": "Подешавања колачића", "app.containers.footer.feedbackEmptyError": "Поље за повратне информације не може бити празно.", "app.containers.footer.poweredBy": "Покреће га", "app.containers.footer.privacy-policy": "Правила приватности", "app.containers.footer.siteMap": "Мапа сајта", "app.containers.footer.terms-and-conditions": "Услови", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "Cancel", "app.containers.ideaHeading.confirmLeaveFormButtonText": "Yes, I want to leave", "app.containers.ideaHeading.editForm": "Edit form", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Are you sure you want to leave?", "app.containers.ideaHeading.leaveIdeaForm": "Leave idea form", "app.containers.ideaHeading.leaveIdeaText": "Your responses won't be saved.", "app.containers.landing.cityProjects": "Пројекти", "app.containers.landing.completeProfile": "Попуните свој профил", "app.containers.landing.completeYourProfile": "Доброд<PERSON><PERSON><PERSON><PERSON>, {firstName}. Време је да попуните свој профил.", "app.containers.landing.createAccount": "Региструјте се", "app.containers.landing.defaultSignedInMessage": "{orgName} те слуша. Ваш је ред да се чује ваш глас!", "app.containers.landing.doItLater": "Урадићу то касније", "app.containers.landing.new": "Нова", "app.containers.landing.subtitleCity": "Добродошли на платформу за учешће {orgName}", "app.containers.landing.titleCity": "Хајде да заједно обликујемо будућност {orgName}", "app.containers.landing.twitterMessage": "Гла<PERSON><PERSON><PERSON>те за {ideaTitle} на", "app.containers.landing.upcomingEventsWidgetTitle": "Предстојећи и текући догађаји", "app.containers.landing.userDeletedSubtitle": "Можете да креирате нови налог у било ком тренутку или {contactLink} да бисте нас обавестили шта можемо да побољшамо.", "app.containers.landing.userDeletedSubtitleLinkText": "Пишите нам", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "Ваш налог је избрисан.", "app.containers.landing.userDeletionFailed": "Дошло је до грешке при брисању вашег налога, обавештени смо о проблему и даћемо све од себе да га решимо. Покушајте поново касније.", "app.containers.landing.verifyNow": "Потврдите сада", "app.containers.landing.verifyYourIdentity": "Потврдите свој идентитет", "app.containers.landing.viewAllEventsText": "Погледајте све догађаје", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Повратак", "app.errors.after_end_at": "Датум почетка наступа после датума завршетка", "app.errors.avatar_carrierwave_download_error": "Није могуће преузети датотеку аватара.", "app.errors.avatar_carrierwave_integrity_error": "Аватар фајл није дозвољеног типа.", "app.errors.avatar_carrierwave_processing_error": "Није могуће обрадити аватар.", "app.errors.avatar_extension_blacklist_error": "Екстензија датотеке слике аватара није дозвољена. Дозвољене екстензије су: јпг, јпег, гиф и пнг.", "app.errors.avatar_extension_whitelist_error": "Екстензија датотеке слике аватара није дозвољена. Дозвољене екстензије су: јпг, јпег, гиф и пнг.", "app.errors.banner_cta_button_multiloc_blank": "Унесите текст дугмета.", "app.errors.banner_cta_button_url_blank": "Унесите везу.", "app.errors.banner_cta_button_url_url": "Unesite važeću vezu. Uverite se da veza počinje sa 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Унесите текст дугмета.", "app.errors.banner_cta_signed_in_url_blank": "Унесите везу.", "app.errors.banner_cta_signed_in_url_url": "Unesite važeću vezu. Uverite se da veza počinje sa 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Унесите текст дугмета.", "app.errors.banner_cta_signed_out_url_blank": "Унесите везу.", "app.errors.banner_cta_signed_out_url_url": "Unesite važeću vezu. Uverite se da veza počinje sa 'https://'.", "app.errors.base_includes_banned_words": "You may have used one or more words that are considered profanity. Please alter your text to remove any profanities that might be present.", "app.errors.body_multiloc_includes_banned_words": "Опис садржи речи које се сматрају неприкладним.", "app.errors.bulk_import_idea_not_valid": "The resulting idea is not valid: {value}.", "app.errors.bulk_import_image_url_not_valid": "No image could be downloaded from {value}. Make sure the URL is valid and ends with a file extension such as .png or .jpg. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Idea location with a missing coordinate in {value}. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Idea location with a non-numeric coordinate in {value}. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_malformed_pdf": "The uploaded PDF file appears to be malformed. Try exporting the PDF again from your source and then upload again.", "app.errors.bulk_import_maximum_ideas_exceeded": "The maximum of {value} ideas has been exceeded.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "The maximum of {value} pages in a PDF has been exceeded.", "app.errors.bulk_import_not_enough_pdf_pages": "The uploaded PDF does not have enough pages - it should have at least the same number of pages as the downloaded template.", "app.errors.bulk_import_publication_date_invalid_format": "Idea with invalid publication date format \"{value}\". Please use the format \"DD-MM-YYYY\".", "app.errors.cannot_contain_ideas": "Метод учешћа који сте изабрали не подржава ову врсту објаве. Измените свој избор и покушајте поново.", "app.errors.cant_change_after_first_response": "Ово више не можете да промените, пошто су неки корисници већ одговорили", "app.errors.category_name_taken": "Категорија са овим именом већ постоји", "app.errors.confirmation_code_expired": "Код је истекао. Затражите нови код.", "app.errors.confirmation_code_invalid": "Неважећи код за потврду. Проверите своју е-пошту за тачан код или покушајте са „Пошаљи нови код“", "app.errors.confirmation_code_too_many_resets": "Превише пута сте послали код за потврду. Контактирајте нас да бисте добили позивни код.", "app.errors.confirmation_code_too_many_retries": "Покушали сте превише пута. Затражите нови код или покушајте да промените своју адресу е-поште.", "app.errors.email_already_active": "Адреса е-поште {value} која се налази у реду {row} већ припада регистрованом учеснику", "app.errors.email_already_invited": "Адреса е-поште {value} која се налази у реду {row} је већ позвана", "app.errors.email_blank": "Ово не може бити празно", "app.errors.email_domain_blacklisted": "За регистрацију користите други домен е-поште.", "app.errors.email_invalid": "Молимо користите важећу адресу е-поште.", "app.errors.email_taken": "Налог са овом е-поштом већ постоји. Уместо тога, можете се пријавити.", "app.errors.email_taken_by_invite": "{value} је већ заузето позивом на чекању. Проверите фасциклу за нежељену пошту или контактирајте {supportEmail} ако не можете да је пронађете.", "app.errors.emails_duplicate": "Једна или више дупликата вредности за адресу е-поште {value} пронађени су у следећим редовима: {rows}", "app.errors.extension_whitelist_error": "The format of the file you tried to upload is not supported.", "app.errors.file_extension_whitelist_error": "Формат датотеке коју сте покушали да отпремите није подржан.", "app.errors.first_name_blank": "Ово не може бити празно", "app.errors.generics.blank": "Ово не може бити празно.", "app.errors.generics.invalid": "Ово не изгледа као ваљана вредност", "app.errors.generics.taken": "Ова е-маил већ постоји. Други налог је повезан са њим.", "app.errors.generics.unsupported_locales": "Ово поље не подржава тренутни локалитет.", "app.errors.group_ids_unauthorized_choice_moderator": "Као менаџер пројекта, можете да пошаљете е-пошту само људима који могу да приступе вашим пројектима", "app.errors.has_other_overlapping_phases": "Пројекти не могу имати фазе које се преклапају.", "app.errors.invalid_email": "Адреса е-поште {value} која се налази у реду {row} није важећа адреса е-поште", "app.errors.invalid_row": "Дошло је до непознате грешке при покушају обраде реда {row}", "app.errors.is_not_timeline_project": "Тренутни пројекат не подржава фазе.", "app.errors.key_invalid": "Кључ може да садржи само слова, бројеве и доње црте (_)", "app.errors.last_name_blank": "Ово не може бити празно", "app.errors.locale_blank": "Молимо изаберите језик", "app.errors.locale_inclusion": "Изаберите подржани језик", "app.errors.malformed_admin_value": "Администраторска вредност {value} која се налази у реду {row} није важећа", "app.errors.malformed_groups_value": "Група {value} која се налази у реду {row} није важећа група", "app.errors.max_invites_limit_exceeded1": "Број позивница прелази границу од 1000.", "app.errors.maximum_attendees_greater_than1": "The maximum number of registrants must be greater than 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "The maximum number of registrants must be greater than or equal to the current number of registrants.", "app.errors.no_invites_specified": "Није могуће пронаћи ниједну адресу е-поште.", "app.errors.no_recipients": "Кампања се не може послати јер нема прималаца. Група којој шаљете је празна или нико није пристао да прима е-поруке.", "app.errors.number_invalid": "Please enter a valid number.", "app.errors.password_blank": "Ово не може бити празно", "app.errors.password_invalid": "Поново проверите своју тренутну лозинку.", "app.errors.password_too_short": "Лозинка мора имати најмање 8 знакова", "app.errors.resending_code_failed": "Нешто је пошло наопако при слању кода за потврду.", "app.errors.slug_taken": "УРЛ овог пројекта већ постоји. Молимо вас да промените ознаку пројекта у нешто друго.", "app.errors.tag_name_taken": "Ознака са овим именом већ постоји", "app.errors.title_multiloc_blank": "Наслов не може бити празан.", "app.errors.title_multiloc_includes_banned_words": "Наслов садржи речи које се сматрају неприкладним.", "app.errors.token_invalid": "Везе за ресетовање лозинке могу се користити само једном и важе један сат након слања. {passwordResetLink}.", "app.errors.too_common": "Ова лозинка се може лако погодити. Изаберите јачу лозинку.", "app.errors.too_long": "Изаберите краћу лозинку (максимално 72 знака)", "app.errors.too_short": "Изаберите лозинку са најмање 8 знакова", "app.errors.uncaught_error": "An unknown error occurred.", "app.errors.unknown_group": "Група {value} која се налази у реду {row} није позната група", "app.errors.unknown_locale": "Језик {value} који се налази у реду {row} није конфигурисани језик", "app.errors.unparseable_excel": "Није могуће обрадити изабрану Екцел датотеку.", "app.errors.url": "Unesite važeću vezu. Uverite se da veza počinje sa 'https://", "app.errors.verification_taken": "Verification cannot be completed as another account has been verified using the same details.", "app.errors.view_name_taken": "Поглед са овим именом већ постоји", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Неприкладан садржај је аутоматски откривен у објави или коментару", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Пријавите се са СтандардПортал", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Пријавите се на СтандардПортал", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Креирајте сада Стадт Виен налог и користите једну пријаву за многе дигиталне услуге Беча.", "app.modules.id_cow.cancel": "Поништити, отказати", "app.modules.id_cow.emptyFieldError": "Ово поље не може бити празно.", "app.modules.id_cow.helpAltText": "Показује где се може пронаћи серијски број личне карте", "app.modules.id_cow.invalidIdSerialError": "Неважећи ИД серијски", "app.modules.id_cow.invalidRunError": "Неважећи РУН", "app.modules.id_cow.noMatchFormError": "Подударање није пронађено.", "app.modules.id_cow.notEntitledFormError": "Нема право.", "app.modules.id_cow.showCOWHelp": "Где могу да пронађем свој ИД серијски број?", "app.modules.id_cow.somethingWentWrongError": "Не можемо да вас верификујемо јер је нешто пошло наопако", "app.modules.id_cow.submit": "прихвати", "app.modules.id_cow.takenFormError": "Већ узето.", "app.modules.id_cow.verifyCow": "Проверите помоћу ЦОВ", "app.modules.id_franceconnect.verificationButtonAltText": "Потврдите са ФранцеЦоннецт-ом", "app.modules.id_gent_rrn.cancel": "Поништити, отказати", "app.modules.id_gent_rrn.emptyFieldError": "Ово поље не може бити празно.", "app.modules.id_gent_rrn.gentRrnHelp": "Ваш број социјалног осигурања је приказан на полеђини ваше дигиталне личне карте", "app.modules.id_gent_rrn.invalidRrnError": "Неважећи број социјалног осигурања", "app.modules.id_gent_rrn.noMatchFormError": "Нисмо могли да пронађемо повратне информације о вашем броју социјалног осигурања", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Не можемо да вас верификујемо јер живите ван Гента", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Не можемо да вас верификујемо јер сте млађи од 14 година", "app.modules.id_gent_rrn.rrnLabel": "Број социјалног осигурања", "app.modules.id_gent_rrn.rrnTooltip": "Тражимо ваш број социјалног осигурања да потврдите да ли сте држављанин Гента, старији од 14 година.", "app.modules.id_gent_rrn.showGentRrnHelp": "Где могу да пронађем свој ИД серијски број?", "app.modules.id_gent_rrn.somethingWentWrongError": "Не можемо да вас верификујемо јер је нешто пошло наопако", "app.modules.id_gent_rrn.submit": "прихвати", "app.modules.id_gent_rrn.takenFormError": "Ваш број социјалног осигурања је већ коришћен за верификацију другог налога", "app.modules.id_gent_rrn.verifyGentRrn": "Проверите помоћу ГентРрн", "app.modules.id_id_card_lookup.cancel": "Поништити, отказати", "app.modules.id_id_card_lookup.emptyFieldError": "Ово поље не може бити празно.", "app.modules.id_id_card_lookup.helpAltText": "Образложење личне карте", "app.modules.id_id_card_lookup.invalidCardIdError": "Овај ИД није важећи.", "app.modules.id_id_card_lookup.noMatchFormError": "Подударање није пронађено.", "app.modules.id_id_card_lookup.showHelp": "Где могу да пронађем свој ИД серијски број?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Не можемо да вас верификујемо јер је нешто пошло наопако", "app.modules.id_id_card_lookup.submit": "прихвати", "app.modules.id_id_card_lookup.takenFormError": "Већ узето.", "app.modules.id_oostende_rrn.cancel": "Поништити, отказати", "app.modules.id_oostende_rrn.emptyFieldError": "Ово поље не може бити празно.", "app.modules.id_oostende_rrn.invalidRrnError": "Неважећи број социјалног осигурања", "app.modules.id_oostende_rrn.noMatchFormError": "Нисмо могли да пронађемо повратне информације о вашем броју социјалног осигурања", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Не можемо да вас верификујемо јер живите ван Остендеа", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Не можемо да вас верификујемо јер сте млађи од 14 година", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Ваш број социјалног осигурања је приказан на полеђини ваше дигиталне личне карте", "app.modules.id_oostende_rrn.rrnLabel": "Број социјалног осигурања", "app.modules.id_oostende_rrn.rrnTooltip": "Тражимо ваш број социјалног осигурања да потврдите да ли сте држављанин Остендеа, старији од 14 година.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Где могу да пронађем свој број социјалног осигурања?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Не можемо да вас верификујемо јер је нешто пошло наопако", "app.modules.id_oostende_rrn.submit": "прихвати", "app.modules.id_oostende_rrn.takenFormError": "Ваш број социјалног осигурања је већ коришћен за верификацију другог налога", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Потврдите користећи број социјалног осигурања", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Добили сте администраторска права над директоријумом \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Подели", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Погледајте пројекте на {folderUrl} да бисте чули свој глас!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | са платформе за учешће од {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | са платформе за учешће од {orgName}", "app.sessionRecording.accept": "Yes, I accept", "app.sessionRecording.modalDescription1": "In order to better understand our users, we randomly ask a small percentage of visitors to track their browsing session in detail.", "app.sessionRecording.modalDescription2": "The sole purpose of the recorded data is to improve the website. None of your data will be shared with a 3rd party. Any sensitive information you enter will be filtered.", "app.sessionRecording.modalDescription3": "Do you accept?", "app.sessionRecording.modalDescriptionFaq": "FAQ here.", "app.sessionRecording.modalTitle": "Help us improve this website", "app.sessionRecording.reject": "No, I reject", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Спроведите вежбу расподеле буџета", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Прикупите повратне информације о документу", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Направите анкету на платформи", "app.utils.AdminPage.ProjectEdit.createPoll": "Направите анкету", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Уградите екстерну анкету", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Пронађите добровољце", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Прикупите унос и повратне информације", "app.utils.AdminPage.ProjectEdit.shareInformation": "Делите информације", "app.utils.FormattedCurrency.credits": "кредити", "app.utils.FormattedCurrency.tokens": "токенс", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# credits} one {# credit} other {# credits}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokens} one {# token} other {# tokens}}", "app.utils.IdeaCards.mostDiscussed": "Most discussed", "app.utils.IdeaCards.mostReacted": "Највише реакција", "app.utils.IdeaCards.newest": "Најновије", "app.utils.IdeaCards.oldest": "Најстарији", "app.utils.IdeaCards.random": "Насумично", "app.utils.IdeaCards.trending": "У тренду", "app.utils.IdeasNewPage.contributionFormTitle": "Додајте нови допринос", "app.utils.IdeasNewPage.ideaFormTitle": "Додајте нову идеју", "app.utils.IdeasNewPage.initiativeFormTitle": "Add new initiative", "app.utils.IdeasNewPage.issueFormTitle1": "Add new comment", "app.utils.IdeasNewPage.optionFormTitle": "Додајте нову опцију", "app.utils.IdeasNewPage.petitionFormTitle": "Add new petition", "app.utils.IdeasNewPage.projectFormTitle": "Додајте нови пројекат", "app.utils.IdeasNewPage.proposalFormTitle": "Add new proposal", "app.utils.IdeasNewPage.questionFormTitle": "Додајте ново питање", "app.utils.IdeasNewPage.surveyTitle": "Анкета", "app.utils.IdeasNewPage.viewYourComment": "View your comment", "app.utils.IdeasNewPage.viewYourContribution": "View your contribution", "app.utils.IdeasNewPage.viewYourIdea": "View your idea", "app.utils.IdeasNewPage.viewYourInitiative": "View your initiative", "app.utils.IdeasNewPage.viewYourInput": "View your input", "app.utils.IdeasNewPage.viewYourIssue": "View your issue", "app.utils.IdeasNewPage.viewYourOption": "View your option", "app.utils.IdeasNewPage.viewYourPetition": "View your petition", "app.utils.IdeasNewPage.viewYourProject": "View your project", "app.utils.IdeasNewPage.viewYourProposal": "View your proposal", "app.utils.IdeasNewPage.viewYourQuestion": "View your question", "app.utils.Projects.sendSubmission": "Send submission identifier to my email", "app.utils.Projects.sendSurveySubmission": "Send survey submission identifier to my email", "app.utils.Projects.surveySubmission": "Survey submission", "app.utils.Projects.yourResponseHasTheFollowingId": "Your response has the following identifier: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "If you later decide that you want your response to be removed, please contact us with the following unique identifier:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "You must complete your profile to attend this event.", "app.utils.actionDescriptors.attendingEventNotInGroup": "You do not meet the requirements to attend this event.", "app.utils.actionDescriptors.attendingEventNotPermitted": "You are not permitted to attend this event.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "You must log in or register to attend this event.", "app.utils.actionDescriptors.attendingEventNotVerified": "You must verify your account before you can attend this event.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "You must complete your profile to volunteer.", "app.utils.actionDescriptors.volunteeringNotInGroup": "You do not meet the requirements to volunteer.", "app.utils.actionDescriptors.volunteeringNotPermitted": "You are not permitted to volunteer.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "You must log in or register to volunteer.", "app.utils.actionDescriptors.volunteeringNotVerified": "You must verify your account before you can volunteer.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Please {completeRegistrationLink} to volunteer.", "app.utils.errors.api_error_default.in": "Није у реду", "app.utils.errors.default.ajv_error_birthyear_required": "Молимо вас да унесете своју годину рођења", "app.utils.errors.default.ajv_error_date_any": "Молимо унесите важећи датум", "app.utils.errors.default.ajv_error_domicile_required": "Молимо вас да унесете своје место становања", "app.utils.errors.default.ajv_error_gender_required": "Унесите свој пол", "app.utils.errors.default.ajv_error_invalid": "Је неважећи", "app.utils.errors.default.ajv_error_maxItems": "Не може укључити више од {limit, plural, one {# ставка} other {# предмета}}", "app.utils.errors.default.ajv_error_minItems": "Мора укључити најмање {limit, plural, one {# ставка} other {# предмета}}", "app.utils.errors.default.ajv_error_number_any": "Унесите исправан број", "app.utils.errors.default.ajv_error_politician_required": "Унесите да ли сте политичар", "app.utils.errors.default.ajv_error_required3": "Field is required: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Не може бити празно", "app.utils.errors.default.api_error_accepted": "Мора се прихватити", "app.utils.errors.default.api_error_blank": "Не може бити празно", "app.utils.errors.default.api_error_confirmation": "Не поклапа се", "app.utils.errors.default.api_error_empty": "Не може бити празно", "app.utils.errors.default.api_error_equal_to": "Није у реду", "app.utils.errors.default.api_error_even": "Мора бити равномерно", "app.utils.errors.default.api_error_exclusion": "Је резервисано", "app.utils.errors.default.api_error_greater_than": "Премален је", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Премален је", "app.utils.errors.default.api_error_inclusion": "Није укључен у листу", "app.utils.errors.default.api_error_invalid": "Је неважећи", "app.utils.errors.default.api_error_less_than": "Превелика је", "app.utils.errors.default.api_error_less_than_or_equal_to": "Превелика је", "app.utils.errors.default.api_error_not_a_number": "Није број", "app.utils.errors.default.api_error_not_an_integer": "Мора бити цео број", "app.utils.errors.default.api_error_other_than": "Није у реду", "app.utils.errors.default.api_error_present": "Мора бити празно", "app.utils.errors.default.api_error_too_long": "Је предугачак", "app.utils.errors.default.api_error_too_short": "Прекратко је", "app.utils.errors.default.api_error_wrong_length": "Да ли је погрешна дужина", "app.utils.errors.defaultapi_error_.odd": "Мора да је чудно", "app.utils.notInGroup": "Не испуњавате услове за учешће.", "app.utils.participationMethod.onSurveySubmission": "Хвала вам. Ваш одговор је примљен.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Voting is no longer available, since this phase is no longer active.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "You do not meet the requirements to vote.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "You are not permitted to vote.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "You must log in or register to vote.", "app.utils.participationMethodConfig.voting.votingNotVerified": "You must verify your account before you can vote.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Submitting budgets closed on {endDate}.</b> Participants had a total of <b>{maxBudget} each to distribute between {optionCount} options.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Буџет је поднет", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Буџет је поднет 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "You do not meet the requirements to assign budgets.", "app.utils.votingMethodUtils.budgetingNotPermitted": "You are not permitted to assign budgets.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "You must log in or register to assign budgets.", "app.utils.votingMethodUtils.budgetingNotVerified": "You must verify your account before you can assign budgets.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Ваш буџет се неће рачунати</b> док не кликнете на „Пошаљи“", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "The minimum required budget is {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "Када завршите, кликните на „Пошаљи“ да бисте послали свој буџет.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Изаберите жељене опције додиром на „Додај“.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "You have a total of <b>{maxBudget} to distribute between {optionCount} options</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b>Честитамо, ваш буџет је достављен!</b> Можете да проверите своје опције испод у било ком тренутку или да их измените пре <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b>Честитамо, ваш буџет је достављен!</b> У било ком тренутку можете да проверите своје опције испод.", "app.utils.votingMethodUtils.castYourVote": "Дајте свој глас", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "You can add a maximum of {maxVotes, plural, one {# credit} other {# credits}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "You can add a maximum of {maxVotes, plural, one {# point} other {# points}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "You can add a maximum of {maxVotes, plural, one {# token} other {# tokens}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "You can add a maximum of {maxVotes, plural, one {# vote} other {# votes}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "Када завршите, кликните на „Пошаљи“ да бисте дали свој глас.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Select your preferred options by tapping on \"Select\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "You have a total of <b>{totalVotes, plural, one {# credit} other {# credits}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "You have a total of <b>{totalVotes, plural, one {# token} other {# tokens}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "You have a total of <b>{totalVotes, plural, one {# vote} other {# votes}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "Коначни резултати", "app.utils.votingMethodUtils.finalTally": "Ко<PERSON><PERSON><PERSON>н број", "app.utils.votingMethodUtils.howToParticipate": "Како учествовати", "app.utils.votingMethodUtils.howToVote": "Како гласати", "app.utils.votingMethodUtils.multipleVotingEnded1": "Voting closed on <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 credits} one {1 credit} other {# credits}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 points} one {1 point} other {# points}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokens} one {1 token} other {# tokens}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 votes} one {1 vote} other {# votes}}", "app.utils.votingMethodUtils.results": "Резултати", "app.utils.votingMethodUtils.singleVotingEnded": "Гласање је завршено <b>{endDate}.</b> Учесника су могла <b>гласати за {maxVotes} опције.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Изаберите жељене опције додиром на „Гласајте“", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Имате <b>{totalVotes} гласова</b> које можете доделити опцијама.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "Када завршите, кликните на „Пошаљи“ да бисте дали свој глас.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Гласање је завршено <b>{endDate}.</b> учесника су могла <b>гласати за 1 опцију.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Изаберите жељену опцију додиром на „Гласај“.", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Имате <b>1 глас</b> који можете доделити једној од опција.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Гласање је завршено <b>{endDate}.</b> Учесници су могли да гласају за <b>опција колико желе.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Можете гласати за онолико опција колико желите.", "app.utils.votingMethodUtils.submitYourBudget": "Пошаљите свој буџет", "app.utils.votingMethodUtils.submittedBudgetCountText2": "person submitted their budget online", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "people submitted their budgets online", "app.utils.votingMethodUtils.submittedVoteCountText2": "person submitted their vote online", "app.utils.votingMethodUtils.submittedVotesCountText2": "people submitted their votes online", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Гласање послато 🎉", "app.utils.votingMethodUtils.votesCast": "Дати гласови", "app.utils.votingMethodUtils.votingClosed": "Гла<PERSON><PERSON>ње је затворено", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Your vote will not be counted</b> until you click \"Submit\"", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b>Congratulations, your vote has been submitted!</b> You can check or modify your submission before <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b>Congratulations, your vote has been submitted!</b> You can check or modify your submission below at any point.", "components.UI.IdeaSelect.noIdeaAvailable": "There are no ideas available.", "components.UI.IdeaSelect.selectIdea": "Select idea", "containers.SiteMap.allProjects": "Сви пројекти", "containers.SiteMap.customPageSection": "Прилагођене странице", "containers.SiteMap.folderInfo": "Више информација", "containers.SiteMap.headSiteMapTitle": "Site map | {orgName}", "containers.SiteMap.homeSection": "General<PERSON>", "containers.SiteMap.pageContents": "Сад<PERSON><PERSON><PERSON><PERSON> странице", "containers.SiteMap.profilePage": "Ваша профилна страница", "containers.SiteMap.profileSettings": "Ваша подешавања", "containers.SiteMap.projectEvents": "Догађаји", "containers.SiteMap.projectIdeas": "Идеје", "containers.SiteMap.projectInfo": "Информације", "containers.SiteMap.projectPoll": "Анкета", "containers.SiteMap.projectSurvey": "Анкета", "containers.SiteMap.projectsArchived": "Архивирани пројекти", "containers.SiteMap.projectsCurrent": "Актуелни пројекти", "containers.SiteMap.projectsDraft": "Нацрти пројеката", "containers.SiteMap.projectsSection": "Пројекти од {orgName}", "containers.SiteMap.signInPage": "Пријавите се", "containers.SiteMap.signUpPage": "Региструјте се", "containers.SiteMap.siteMapDescription": "Са ове странице можете се кретати до било ког садржаја на платформи.", "containers.SiteMap.siteMapTitle": "Мапа сајта платформе за учешће од {orgName}", "containers.SiteMap.successStories": "Успешна прича", "containers.SiteMap.timeline": "Фазе пројекта", "containers.SiteMap.userSpaceSection": "Ваш рачун", "containers.SubscriptionEndedPage.accessDenied": "Више немате приступ", "containers.SubscriptionEndedPage.subscriptionEnded": "Ова страница је доступна само за платформе са активном претплатом."}
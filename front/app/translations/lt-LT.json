{"EmailSettingsPage.emailSettings": "El. p<PERSON><PERSON> nust<PERSON>", "EmailSettingsPage.initialUnsubscribeError": "Nepavyko atsisakyti šios kampanijos prenumeratos, pabandykite dar kartą.", "EmailSettingsPage.initialUnsubscribeLoading": "Jūsų užklausa a<PERSON>dor<PERSON>ma, palaukite...", "EmailSettingsPage.initialUnsubscribeSuccess": "Sėkmingai atsisakėte prenumeratos {campaignTitle}.", "UI.FormComponents.optional": "pasirinktinai", "app.closeIconButton.a11y_buttonActionMessage": "Uždaryti", "app.components.Areas.areaUpdateError": "<PERSON><PERSON><PERSON><PERSON><PERSON> sritį įvyko klaida. Bandykite dar kartą.", "app.components.Areas.followedArea": "<PERSON><PERSON><PERSON> sritis: {areaTitle}", "app.components.Areas.followedTopic": "<PERSON><PERSON><PERSON> tema: {topicTitle}", "app.components.Areas.topicUpdateError": "<PERSON><PERSON><PERSON><PERSON><PERSON> temą įvyko klaida. Bandykite dar kartą.", "app.components.Areas.unfollowedArea": "Nelydima sritis: {areaTitle}", "app.components.Areas.unfollowedTopic": "Nenufotografuota tema: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Kaina:", "app.components.AssignBudgetControl.add": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignBudgetControl.added": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.addVote": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Išdalijote visus kreditus.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Išdalijote didžiausią galimą kreditų skaičių šiai galimybei.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Išdalijote visus savo punktus.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "<PERSON><PERSON> šią parinktį išdalijote maksimalų taškų skaičių.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Išdalijote visus žetonus.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Išdalijote didžiausią šios parinkties žetonų skaičių.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Paskirstėte visus savo balsus.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "<PERSON><PERSON>ią parinktį išdalijote maksimalų balsų skaičių.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(įskaitant 1 neprisijungus)} other {(įskaitant # neprisijungus)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>, nes <PERSON>is etapas nėra aktyvus.", "app.components.AssignMultipleVotesControl.removeVote": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "app.components.AssignMultipleVotesControl.select": "Pasirinkite", "app.components.AssignMultipleVotesControl.votesSubmitted1": "<PERSON><PERSON><PERSON> jau pat<PERSON> savo balsą. Norėdami jį pakeisti, spustelėki<PERSON> \"Modify your submission\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "<PERSON><PERSON><PERSON> jau pat<PERSON> savo balsą. Norėdami jį pakeisti, grįžkite į projekto puslapį ir spustelėkite \"Modify your submission\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {kreditas} other {kreditai}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {taš<PERSON>} other {taškai}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {ž<PERSON><PERSON>} other {žetonai}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {balsavimas} other {balsai}}", "app.components.AssignVoteControl.maxVotesReached1": "Paskirstėte visus savo balsus.", "app.components.AssignVoteControl.phaseNotActive": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>, nes <PERSON>is etapas nėra aktyvus.", "app.components.AssignVoteControl.select": "Pasirinkite", "app.components.AssignVoteControl.selected2": "Pasirinkta", "app.components.AssignVoteControl.voteForAtLeastOne": "Balsuokite už bent 1 variantą", "app.components.AssignVoteControl.votesSubmitted1": "<PERSON><PERSON><PERSON> jau pat<PERSON> savo balsą. Norėdami jį pakeisti, spustelėki<PERSON> \"Modify your submission\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "<PERSON><PERSON><PERSON> jau pat<PERSON> savo balsą. Norėdami jį pakeisti, grįžkite į projekto puslapį ir spustelėkite \"Modify your submission\".", "app.components.AuthProviders.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.continueWithAzure": "Tęsti su {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Tęsti su \"Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Tęskite darbą su suklastotu SSO", "app.components.AuthProviders.continueWithGoogle": "Tęsti su \"Google", "app.components.AuthProviders.continueWithHoplr": "Tęsti su \"Hoplr", "app.components.AuthProviders.continueWithIdAustria": "Tęsti su ID Austrija", "app.components.AuthProviders.continueWithLoginMechanism": "Tęsti su {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Tęsti su \"MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Šiuo el. pašto adresu jau yra sukurta paskyra.{br}{br}Negalite prisijungti prie platformos naudodamiesi FranceConnect, nes asmens duomenys nesutampa. Norėdami prisijungti naudodamiesi \"FranceConnect\", pirmiausia turėsite šioje platformoje pakeisti savo vardą arba pavardę, kad jie atitiktų jūsų oficialius duomenis.{br}{br}Toliau galite prisijungti kaip įprastai.", "app.components.AuthProviders.goToLogIn": "Jau turite paskyrą? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Neturite paskyros? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.logInWithEmail": "Prisijunkite naudodami el. paštą", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "<PERSON><PERSON><PERSON> būti <PERSON>, turite būti nurodyto ar v<PERSON><PERSON>.", "app.components.AuthProviders.signUp2": "Užsiregistruokite", "app.components.AuthProviders.signUpButtonAltText": "Užsiregistruokite {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Užsiregistruokite naudodami el. paštą", "app.components.AuthProviders.verificationRequired": "<PERSON><PERSON><PERSON><PERSON> pat<PERSON>", "app.components.Author.a11yPostedBy": "Paskelbė", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 dalyvis} other {{numberOfParticipants} dalyviai}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} vartotojai", "app.components.AvatarBubbles.participant": "<PERSON><PERSON><PERSON>", "app.components.AvatarBubbles.participants1": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "Dabartiniame etape komentarai negalimi.", "app.components.Comments.commentingDisabledInactiveProject": "<PERSON><PERSON><PERSON><PERSON>, nes šis projektas šiuo metu nėra aktyvu<PERSON>.", "app.components.Comments.commentingDisabledProject": "Komentarai šiame projekte šiuo metu yra išjungti.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} koment<PERSON><PERSON>.", "app.components.Comments.commentingMaybeNotPermitted": "Prašome {signInLink} <PERSON><PERSON><PERSON><PERSON>, kokių veiksmų galima im<PERSON>.", "app.components.Comments.inputsAssociatedWithProfile": "<PERSON><PERSON> numatytu<PERSON><PERSON> nustatymus jūsų pateikimai bus susieti su jūsų profiliu, nebent pasirinksite šią parinktį.", "app.components.Comments.invisibleTitleComments": "Komentarai", "app.components.Comments.leastRecent": "Mažiausiai <PERSON>i", "app.components.Comments.likeComment": "<PERSON><PERSON><PERSON> š<PERSON> komenta<PERSON>", "app.components.Comments.mostLiked": "Dauguma reakcijų", "app.components.Comments.mostRecent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.official": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.postAnonymously": "Rašyti anonimiškai", "app.components.Comments.replyToComment": "Atsakyti į komentarą", "app.components.Comments.reportAsSpam": "Praneš<PERSON> ka<PERSON>", "app.components.Comments.seeOriginal": "<PERSON><PERSON>. original<PERSON>", "app.components.Comments.seeTranslation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "app.components.Comments.yourComment": "Jūsų komentaras", "app.components.CommonGroundResults.divisiveDescription": "<PERSON><PERSON><PERSON><PERSON>, su kuriais ž<PERSON> vienodai sutinka ir nesutinka:", "app.components.CommonGroundResults.divisiveTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.majorityDescription": "Daugiau nei 60 % balsavo vienaip ar kitaip dėl š<PERSON>ų klausimų:", "app.components.CommonGroundResults.majorityTitle": "Dauguma", "app.components.CommonGroundResults.participantLabel": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.participantsLabel1": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.statementLabel": "par<PERSON>š<PERSON><PERSON>", "app.components.CommonGroundResults.statementsLabel1": "pareiškimai", "app.components.CommonGroundResults.votesLabe": "balsavimas", "app.components.CommonGroundResults.votesLabel1": "balsai", "app.components.CommonGroundStatements.agreeLabel": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundStatements.disagreeLabel": "Nesutinku", "app.components.CommonGroundStatements.noMoreStatements": "<PERSON><PERSON>o metu nėra par<PERSON>, į kuriuos būtų galima reaguoti", "app.components.CommonGroundStatements.noResults": "Kol kas nėra jokių rezultatų. Įsitikinkite, kad daly<PERSON>te \"Common Ground\" etape, ir po jo vėl apsilankykite čia.", "app.components.CommonGroundStatements.unsureLabel": "Nežinoma", "app.components.CommonGroundTabs.resultsTabLabel": "Rezultatai", "app.components.CommonGroundTabs.statementsTabLabel": "Pareiškimai", "app.components.CommunityMonitorModal.formError": "Įvyko klaida.", "app.components.CommunityMonitorModal.surveyDescription2": "Šia nuolatine apklausa siekiama <PERSON>, ką manote apie valdymą ir vieš<PERSON> pas<PERSON>.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Užtrunka <1 minutę} one {Užtrunka 1 minutę} other {Užtrunka # minučių}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "El. laiš<PERSON> su patvirtinimo kodu buvo išsiųstas adresu {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Pakeiskite el. pašto adresą.", "app.components.ConfirmationModal.codeInput": "<PERSON><PERSON>", "app.components.ConfirmationModal.confirmationCodeSent": "Išsiųstas naujas kodas", "app.components.ConfirmationModal.didntGetAnEmail": "Negavote el. laiško?", "app.components.ConfirmationModal.foundYourCode": "Radote savo kodą?", "app.components.ConfirmationModal.goBack": "Grįžti atgal.", "app.components.ConfirmationModal.sendEmailWithCode": "Siųsti el. laišką su kodu", "app.components.ConfirmationModal.sendNewCode": "Siųsti naują kodą.", "app.components.ConfirmationModal.verifyAndContinue": "Patikrinkite ir tęskite", "app.components.ConfirmationModal.wrongEmail": "Klaidingas el. paš<PERSON>?", "app.components.ConsentManager.Banner.accept": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.ariaButtonClose2": "Atmesti politiką ir uždaryti reklaminį skydelį", "app.components.ConsentManager.Banner.close": "Uždaryti", "app.components.ConsentManager.Banner.mainText": "Šioje platformoje slapukai naudojami pagal mū<PERSON>ų {policyLink}.", "app.components.ConsentManager.Banner.manage": "Tvarkykite", "app.components.ConsentManager.Banner.policyLink": "Slapukų politika", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>, kad gal<PERSON>tume suasmeninti ir įvertinti mūsų svetainės reklamos kampanijų veiksmingumą. Šioje platformoje nerodysime jokios reklamos, tačiau toliau nurodytos paslaugos gali pasiūlyti jums asmeninę reklamą pagal jūsų aplankytus mūsų svetainės puslapius.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analiz<PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Šį stebėjimą naudojame nor<PERSON>dami geriau suprasti, kaip naudo<PERSON>, kad gal<PERSON>tume mokytis ir tobulinti navigaciją. Ši informacija naudojama tik masinei analizei ir jokiu būdu nenaudojama atskiriems žmonėms sekti.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Grįžti atgal", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Tai reikalinga norint įjungti ir steb<PERSON>ti pagrindines svet<PERSON><PERSON>. Kai kurie čia išvardyti įrankiai jums gali būti netaikomi. Daugiau informacijos rasite mūsų slapukų politikoje.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "\"Google Tag Manager\" ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Kad platforma veiktų, iš<PERSON>ug<PERSON> autentifikavimo <PERSON>, j<PERSON>, ir ka<PERSON>, k<PERSON> naudo<PERSON> platforma.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Jūsų <PERSON>uk<PERSON> parinktys", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Įrankiai", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Turinio įkėlimo atsakomybės apribojimas", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Įkeldami turinį patvir<PERSON>te, kad šis turinys nepažeidžia jokių taisyklių ar trečiųjų šalių teisių, pavyzdžiui, intelektinės nuosavybės teisių, privatumo teisių, teisių į komercines paslaptis ir pan. Todėl įkeldami šį turinį įsipareigojate prisiimti visą ir išimtinę atsakomybę už visą tiesioginę ir netiesioginę žalą, atsiradusią dėl įkelto turinio. Be to, įsipareigojate atlyginti platformos savininkui ir \"Go Vocal\" bet kokius trečiųjų šalių ieškinius ar atsakomybę prieš trečiąsias šalis ir bet kokias susijusias išlaidas, kurios atsirastų ar atsirastų dėl jūsų įkelto turinio.", "app.components.ContentUploadDisclaimer.onAccept": "<PERSON><PERSON> supra<PERSON>", "app.components.ContentUploadDisclaimer.onCancel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Papasako<PERSON><PERSON> mums, k<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.addressInputAriaLabel": "Adreso įvedimas", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Įveskite adresą...", "app.components.CustomFieldsForm.adminFieldTooltip": "<PERSON><PERSON> mat<PERSON> t<PERSON>", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Visi šios apklausos atsakymai yra anoniminiai.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Daugiakampiui reikia bent trijų taškų.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "<PERSON><PERSON><PERSON> re<PERSON>a bent dviejų taškų.", "app.components.CustomFieldsForm.attachmentRequired": "<PERSON><PERSON><PERSON> p<PERSON> bent vien<PERSON> priedą", "app.components.CustomFieldsForm.authorFieldLabel": "Autorius", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Pradėkite r<PERSON>š<PERSON>i, jei norite ieškoti pagal naudotojo el. pašto ad<PERSON> arba vardą...", "app.components.CustomFieldsForm.back": "Atgal", "app.components.CustomFieldsForm.budgetFieldLabel": "Biudžetas", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Spustelėkite žemėlapį ir pieškite. <PERSON><PERSON> vil<PERSON> taš<PERSON>, kad juo<PERSON>.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Spustelėkite žemėlapį arba įveskite adresą ir pridėkite atsakymą.", "app.components.CustomFieldsForm.confirm": "Patvirtinkite", "app.components.CustomFieldsForm.descriptionMinLength": "<PERSON><PERSON><PERSON><PERSON> turi būti ne trumpesnis kaip {min} simbolių.", "app.components.CustomFieldsForm.descriptionRequired": "<PERSON><PERSON><PERSON> pat<PERSON> aprašym<PERSON>", "app.components.CustomFieldsForm.fieldMaximumItems": "<PERSON><PERSON><PERSON><PERSON> \"{fieldName}\" galima pasirinkti ne daugiau kaip {maxSelections, plural, one {# parinktį} other {# parinktis}} .", "app.components.CustomFieldsForm.fieldMinimumItems": "<PERSON><PERSON><PERSON><PERSON> \"{fieldN<PERSON>}\" galima pasirinkti bent {minSelections, plural, one {# parinktį} other {# parinktis}} .", "app.components.CustomFieldsForm.fieldRequired": "<PERSON><PERSON> \"{fieldName}\" yra privalomas", "app.components.CustomFieldsForm.fileSizeLimit": "Failo dyd<PERSON>io riba yra {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "Vaizdas yra b<PERSON>", "app.components.CustomFieldsForm.minimumCoordinates2": "Reikia surinkti ne ma<PERSON>iau kaip {numPoints} žemėlapio taškų.", "app.components.CustomFieldsForm.notPublic1": "*Šiuo atsakymu bus dalijamasi tik su projektų vadovais, o ne viešai.", "app.components.CustomFieldsForm.otherArea": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.progressBarLabel": "Pažanga", "app.components.CustomFieldsForm.removeAnswer": "Pašalinti atsakymą", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Pasirinkite tiek, kiek norite", "app.components.CustomFieldsForm.selectBetween": "*Pasirinkite iš {minItems} ir {maxItems} parinkčių", "app.components.CustomFieldsForm.selectExactly2": "*Pasirinkite tiksliai {selectExactly, plural, one {# parinktis} other {# parinktys}}", "app.components.CustomFieldsForm.selectMany": "*Pasirinkite tiek, kiek norite", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Bakstelėkite žemėlapį ir bra<PERSON>ž<PERSON>. <PERSON><PERSON> vil<PERSON> ta<PERSON>, kad juo<PERSON>.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Bakstelėkite žemėlapį ir braižykite.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Bakstelėkite žemėlapį ir pridėkite atsakymą.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Bakstelėkite žemėlapį arba toliau įveskite adresą ir pridėkite atsakymą.", "app.components.CustomFieldsForm.tapToAddALine": "Bakstelėkite , kad pridėtumėte eilutę", "app.components.CustomFieldsForm.tapToAddAPoint": "Bakstelėkite , kad pridėtumėte tašką", "app.components.CustomFieldsForm.tapToAddAnArea": "Bakstelėkite , kad p<PERSON>tumėte sritį", "app.components.CustomFieldsForm.titleMaxLength": "Pavadinimas turi būti ne ilgesnis kaip {max} simbolių.", "app.components.CustomFieldsForm.titleMinLength": "Pavadinimas turi būti ne trumpesnis nei {min} simbolių.", "app.components.CustomFieldsForm.titleRequired": "Reikia nurodyti p<PERSON>", "app.components.CustomFieldsForm.topicRequired": "<PERSON><PERSON><PERSON> bent viena <PERSON>a", "app.components.CustomFieldsForm.typeYourAnswer": "Įveskite atsakymą", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Būtina įvesti atsakymą", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Įkelkite zip failą, kuriame yra vienas ar daugiau shape failų.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "Jei vieta nerodoma tarp įvestų par<PERSON>, galite prid<PERSON>ti galiojanč<PERSON> koordinates formatu \"platuma, ilguma\" ir nurodyti tikslią vietą (pvz., -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Kai kurie laukai buvo neteisingi. Ištaisykite klaidas ir bandykite dar kartą.", "app.components.ErrorBoundary.errorFormErrorGeneric": "<PERSON><PERSON><PERSON>ant ataskaitą įvyko než<PERSON>ma klaida. Bandykite dar kartą.", "app.components.ErrorBoundary.errorFormLabelClose": "Uždaryti", "app.components.ErrorBoundary.errorFormLabelComments": "<PERSON><PERSON> nut<PERSON>?", "app.components.ErrorBoundary.errorFormLabelEmail": "El. <PERSON>", "app.components.ErrorBoundary.errorFormLabelName": "Pavadinimas", "app.components.ErrorBoundary.errorFormLabelSubmit": "Pat<PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormSubtitle": "Mūsų komandai buvo pranešta.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON><PERSON> nor<PERSON>, kad <PERSON>, to<PERSON><PERSON>, kas nut<PERSON>.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Jūsų atsiliepimas buvo išsiųstas. Dėkojame!", "app.components.ErrorBoundary.errorFormTitle": "<PERSON><PERSON><PERSON>, kad yra problema.", "app.components.ErrorBoundary.genericErrorWithForm": "Įvyko klaida, tod<PERSON><PERSON> negalime rodyti šio turinio. Pabandykite dar kartą arba {openForm}", "app.components.ErrorBoundary.openFormText": "padėkite mums tai i<PERSON><PERSON>", "app.components.ErrorToast.budgetExceededError": "<PERSON>urite pakankamai bi<PERSON>", "app.components.ErrorToast.votesExceededError": "Jums neliko pakankamai balsų", "app.components.EventAttendanceButton.forwardToFriend": "Persiųsti draugui", "app.components.EventAttendanceButton.maxRegistrationsReached": "Pasiektas maksimalus renginio registracijų skaičius. Vietų nebėra.", "app.components.EventAttendanceButton.register": "<PERSON><PERSON><PERSON><PERSON>", "app.components.EventAttendanceButton.registered": "<PERSON><PERSON><PERSON><PERSON>", "app.components.EventAttendanceButton.seeYouThere": "Iki pasimatymo!", "app.components.EventAttendanceButton.seeYouThereName": "<PERSON><PERSON>, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "<PERSON><PERSON> mažiau informacijos apie įvykius.", "app.components.EventCard.a11y_moreContentVisible": "<PERSON><PERSON> matoma daugiau informacijos apie įvykius.", "app.components.EventCard.a11y_readMore": "Daugiau informacijos apie renginį \"{eventTitle}\".", "app.components.EventCard.endsAt": "Baigiasi ties", "app.components.EventCard.readMore": "<PERSON><PERSON><PERSON><PERSON>", "app.components.EventCard.showLess": "<PERSON><PERSON><PERSON>", "app.components.EventCard.showMore": "<PERSON><PERSON><PERSON>", "app.components.EventCard.startsAt": "<PERSON><PERSON><PERSON><PERSON> nuo", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Artėjantys ir v<PERSON>stantys šio projekto renginiai", "app.components.EventPreviews.eventPreviewTimelineTitle3": "<PERSON><PERSON><PERSON><PERSON> ir v<PERSON><PERSON> š<PERSON> etapo reng<PERSON>i", "app.components.FileUploader.a11y_file": "Failas:", "app.components.FileUploader.a11y_filesToBeUploaded": "Įkeliami failai: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Failų nepridėta.", "app.components.FileUploader.a11y_removeFile": "<PERSON><PERSON>lint<PERSON> šį failą", "app.components.FileUploader.fileInputDescription": "Spustelėkite , kad pasirinktumėte failą", "app.components.FileUploader.fileUploadLabel": "<PERSON><PERSON><PERSON> (ne daugiau kaip 50 MB)", "app.components.FileUploader.file_too_large2": "Didesni nei {maxSizeMb}MB failai neleidžiami.", "app.components.FileUploader.incorrect_extension": "{fileName} mū<PERSON><PERSON> siste<PERSON>, jis nebus įkeltas.", "app.components.FilterBoxes.a11y_allFilterSelected": "Pasirinktas būsenos filtras: visi", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# submission} other {# submissions}}", "app.components.FilterBoxes.a11y_removeFilter": "<PERSON><PERSON><PERSON><PERSON> filtrą", "app.components.FilterBoxes.a11y_selectedFilter": "Pasirinktas būsenos filtras: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Pasirinkta {numberOfSelectedTopics, plural, =0 {nulin<PERSON><PERSON> žym<PERSON> filtrai} one {vienos žym<PERSON> filtras} other {# žymės filtrai}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Visi", "app.components.FilterBoxes.areas": "Filtruoti pagal sritį", "app.components.FilterBoxes.inputs": "įėjimai", "app.components.FilterBoxes.noValuesFound": "Verčių nėra.", "app.components.FilterBoxes.showLess": "<PERSON><PERSON><PERSON>", "app.components.FilterBoxes.showTagsWithNumber": "Rod<PERSON>i visus ({numberTags})", "app.components.FilterBoxes.statusTitle": "Statusas", "app.components.FilterBoxes.topicsTitle": "<PERSON><PERSON><PERSON>", "app.components.FiltersModal.filters": "Filtrai", "app.components.FolderFolderCard.a11y_folderDescription": "Aplankų aprašymas:", "app.components.FolderFolderCard.a11y_folderTitle": "Aplankų pavadinimas:", "app.components.FolderFolderCard.archived": "Archyvuota", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projektai} one {# projektas} other {# projektai}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "<PERSON><PERSON> tipo negalima keisti, kai jau yra pateiktų duomenų.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Tipas", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Automat<PERSON><PERSON>", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Automatinis i<PERSON>ugojimas įjungiamas pagal numatytu<PERSON>ius nustatymus, kai atidarote formos redaktorių. Bet kada, kai uždarysite lauko nustatymų skydelį naudodami mygtuką \"X\", jis bus automatiškai išsaugotas.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.timeRangeMultiyear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.year": "Metai", "app.components.GanttChart.today": "Šiandien", "app.components.GoBackButton.group.edit.goBack": "Grįžti atgal", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Grįžti į ankstesnį puslapį", "app.components.HookForm.Feedback.errorTitle": "<PERSON><PERSON> problema", "app.components.HookForm.Feedback.submissionError": "Pabandykite dar kartą. Jei problema <PERSON>, susisiekite su mumis", "app.components.HookForm.Feedback.submissionErrorTitle": "Mūsų pusėje kilo problema, atsiprašome", "app.components.HookForm.Feedback.successMessage": "Forma s<PERSON>kmingai pateikta", "app.components.HookForm.PasswordInput.passwordLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.HorizontalScroll.scrollLeftLabel": "Slinkite į kairę.", "app.components.HorizontalScroll.scrollRightLabel": "Slinkite į dešinę.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} id<PERSON><PERSON> buvo įkeltos.", "app.components.IdeaCards.filters": "Filtrai", "app.components.IdeaCards.filters.mostDiscussed": "Daugiausia diskusijų", "app.components.IdeaCards.filters.newest": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.oldest": "Senasis", "app.components.IdeaCards.filters.popular": "Labiausiai patiko", "app.components.IdeaCards.filters.random": "Atsitiktinis", "app.components.IdeaCards.filters.sortBy": "Rūšiuoti pagal", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Rūšiavimas pakeistas į: {currentSortType}", "app.components.IdeaCards.filters.trending": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.showMore": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.a11y_hideIdeaCard": "Paslėpti idėjos kort<PERSON>ę.", "app.components.IdeasMap.a11y_mapTitle": "Žemėlapio apžvalga", "app.components.IdeasMap.clickOnMapToAdd": "Spustelėkite žemėlapį ir įneškite savo indėlį", "app.components.IdeasMap.clickOnMapToAddAdmin2": "<PERSON><PERSON> <PERSON>ius gal<PERSON> s<PERSON> žemėlapį ir pridėti savo indėlį, net jei šis etapas nėra aktyvus.", "app.components.IdeasMap.filters": "Filtrai", "app.components.IdeasMap.multipleInputsAtLocation": "Ke<PERSON> įėjimai šioje vietoje", "app.components.IdeasMap.noFilteredResults": "Pasirinkti filtrai nedavė jokių rezultatų", "app.components.IdeasMap.noResults": "Rezultatų nerasta", "app.components.IdeasMap.or": "arba", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, nepatinka.} one {1 nepatinka.} other {, # nepatinka.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, nepatinka.} one {, 1 like.} other {, # patinka.}}", "app.components.IdeasMap.signInLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasMap.signUpLinkText": "užsiregistruoti", "app.components.IdeasMap.submitIdea2": "Pateikti įvestį", "app.components.IdeasMap.tapOnMapToAdd": "Bakstelėkite žemėlapį ir pridėkite savo įvestį", "app.components.IdeasMap.tapOnMapToAddAdmin2": "<PERSON><PERSON> <PERSON>ius gal<PERSON> b<PERSON> žemėlapį ir pridėti savo indėlį, net jei šis etapas nėra aktyvus.", "app.components.IdeasMap.userInputs2": "Dalyvių indėlis", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, komentarų nėra} one {, 1 komentaras} other {, # komentarų}}", "app.components.IdeasShow.bodyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.deletePost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.editPost": "Red<PERSON><PERSON><PERSON>", "app.components.IdeasShow.goBack": "Grįžti atgal", "app.components.IdeasShow.moreOptions": "Daugiau galimybių", "app.components.IdeasShow.or": "arba", "app.components.IdeasShow.proposedBudgetTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.reportAsSpam": "Praneš<PERSON> ka<PERSON>", "app.components.IdeasShow.send": "Si<PERSON>sti", "app.components.IdeasShow.skipSharing": "Praleiskite, tai padarysiu vėliau", "app.components.IdeasShowPage.signIn2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>, negalite pasiekti š<PERSON> pusla<PERSON>. Norint jį pasiekti, gali tekti prisijungti arba užsiregistruoti.", "app.components.LocationInput.noOptions": "Nėra galimybių", "app.components.Modal.closeWindow": "Uždaryti langą", "app.components.MultiSelect.clearButtonAction": "Išvalyti pasirinkimą", "app.components.MultiSelect.clearSearchButtonAction": "Aiški p<PERSON>š<PERSON>", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Įveskite naują el. pa<PERSON><PERSON> ad<PERSON>", "app.components.PageNotFound.goBackToHomePage": "Grįžti į pagrindinį puslapį", "app.components.PageNotFound.notFoundTitle": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>", "app.components.PageNotFound.pageNotFoundDescription": "Prašomo puslapio nepavyko rasti.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Pateikite turinį bent viena kalba.", "app.components.PagesForm.editContent": "Turinys", "app.components.PagesForm.fileUploadLabel": "<PERSON><PERSON><PERSON> (ne daugiau kaip 50 MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Failai neturėtų būti didesni nei 50 MB. Pridėti failai bus rodomi šio puslapio apačioje.", "app.components.PagesForm.navbarItemTitle": "Pavadinimas navbar'e", "app.components.PagesForm.pageTitle": "Pavadinimas", "app.components.PagesForm.savePage": "<PERSON>šsaugoti puslapį", "app.components.PagesForm.saveSuccess": "<PERSON><PERSON><PERSON><PERSON>.", "app.components.PagesForm.titleMissingOneLanguageError": "Pateikite bent vienos kalbos pavadinim<PERSON>", "app.components.Pagination.back": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Pagination.next": "<PERSON><PERSON>", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Išleidote {votesCast}, o tai viršija {votesLimit}limitą. Išimkite keletą prekių iš savo krepšelio ir pabandykite dar kartą.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} left", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "<PERSON><PERSON><PERSON> pat<PERSON>i savo krepšelį turite išleisti mažiausiai {votesMinimum} .", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "<PERSON><PERSON><PERSON> pateikdami paraišką turite pasirinkti bent vieną parinktį.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "<PERSON><PERSON><PERSON>, turite ką nors įtraukti į krepšelį.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {Neliko kredit<PERSON>} other {Liko # iš {totalNumberOfVotes, plural, one {1 kreditas} other {# kreditų}}}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {<PERSON><PERSON><PERSON>} other {Liko # iš {totalNumberOfVotes, plural, one {1 taškas} other {# taškų}}}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Žetonų neliko} other {Liko # iš {totalNumberOfVotes, plural, one {1 žetonas} other {# žetonų}}}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Neliko balsų} other {Liko # iš {totalNumberOfVotes, plural, one {1 balsas} other {# balsų}}}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# balsų} one {# balsų} other {# balsų}} mesti", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "<PERSON><PERSON><PERSON> {votesCast} balsų, t. y. v<PERSON> {votesLimit}limitą. Pašalinkite keletą balsų ir bandykite dar kartą.", "app.components.ParticipationCTABars.addInput": "<PERSON><PERSON><PERSON><PERSON> įvestį", "app.components.ParticipationCTABars.allocateBudget": "Skirkite biudžetą", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Jūsų biudžetas sėkmingai pateiktas.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.poll": "Atlikite apklausą", "app.components.ParticipationCTABars.reviewDocument": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeContributions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> įnašus", "app.components.ParticipationCTABars.seeEvents3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeIdeas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeInitiatives": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in<PERSON>", "app.components.ParticipationCTABars.seeIssues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.ParticipationCTABars.seeOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seePetitions": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeProjects": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> projektus", "app.components.ParticipationCTABars.seeProposals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeQuestions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.ParticipationCTABars.submit": "Pat<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.takeTheSurvey": "Atlikite apklausą", "app.components.ParticipationCTABars.userHasParticipated": "Dalyvavote šiame projekte.", "app.components.ParticipationCTABars.viewInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> įvestis", "app.components.ParticipationCTABars.volunteer": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.votesCounter.vote": "balsavimas", "app.components.ParticipationCTABars.votesCounter.votes": "balsai", "app.components.PasswordInput.a11y_passwordHidden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_passwordVisible": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength1Password": "Prastas slaptažodžio stiprumas", "app.components.PasswordInput.a11y_strength2Password": "<PERSON>l<PERSON>nas slaptažodžio stiprumas", "app.components.PasswordInput.a11y_strength3Password": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "app.components.PasswordInput.a11y_strength4Password": "St<PERSON><PERSON>ž<PERSON> stiprumas", "app.components.PasswordInput.a11y_strength5Password": "Labai stiprus slap<PERSON>žodžio stiprumas", "app.components.PasswordInput.hidePassword": "Paslėpti slaptažodį", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "Per trumpas (min. {minimumPasswordLength} ženklų)", "app.components.PasswordInput.minimumPasswordLengthError": "pateikti bent {minimumPasswordLength} simbolių ilgio slaptažodį.", "app.components.PasswordInput.passwordEmptyError": "Įveskite slaptažodį", "app.components.PasswordInput.passwordStrengthTooltip1": "<PERSON><PERSON> nor<PERSON>, kad <PERSON> bū<PERSON> stipresnis:", "app.components.PasswordInput.passwordStrengthTooltip2": "Naudokite ne iš eilės einančių mažųjų raidžių, didž<PERSON><PERSON><PERSON><PERSON> raidžių, skaitmenų, specialiųjų ženklų ir skyrybos ženklų derinį.", "app.components.PasswordInput.passwordStrengthTooltip3": "Venkite įprastų ar lengvai atspėjamų žodžių.", "app.components.PasswordInput.passwordStrengthTooltip4": "Padidinkite ilgį", "app.components.PasswordInput.showPassword": "Rod<PERSON><PERSON> slaptažodį", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength3Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength4Password": "St<PERSON><PERSON>", "app.components.PasswordInput.strength5Password": "Labai stiprus", "app.components.PostCardsComponents.list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostCardsComponents.map": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "P<PERSON>ėti oficialų atnaujinimą", "app.components.PostComponents.OfficialFeedback.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Ar tikrai norite ištrinti šį oficialų atnaujinimą?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Red<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "Paskutinį kartą redaguota {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Paskuti<PERSON> atnaujinimas: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON>, ka<PERSON> mat<PERSON> j<PERSON> vardą", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "<PERSON><PERSON><PERSON>us atna<PERSON><PERSON>imo autoriaus vardas ir pavard<PERSON>", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "<PERSON><PERSON><PERSON><PERSON> at<PERSON><PERSON><PERSON> te<PERSON>", "app.components.PostComponents.OfficialFeedback.officialUpdates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.postedOn": "Paskelbta {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Paskelbti", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "<PERSON><PERSON><PERSON> an<PERSON><PERSON> at<PERSON>", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Pateikite atnaujintą informaciją...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON> problema", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Atnaujin<PERSON>", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "<PERSON><PERSON><PERSON><PERSON> atnaujinimas buvo sėkmingai paskelbtas!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Paremkite mano įnašą '{postTitle}' adresu {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Palaikykite mano indėlį: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Palaikykite mano indėlį: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Palaikykite mano id<PERSON> '{postTitle}' adresu {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Palaikykite mano id<PERSON>: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Palaikykite mano id<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Ką manote apie šį pasiūlymą? Balsuokite dėl jo ir dalinkit<PERSON> diskusija svetainėje {postUrl} , kad jūsų balsas būtų išgirstas!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Pritarkite mano p<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Paremkite mano iniciatyvą: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Paskelbiau komentarą '{postTitle}' adresu {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "<PERSON>ą tik paskelbiau komentarą: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "<PERSON>ą tik paskelbiau komentarą: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Palaikykite mano si<PERSON> variantą \"{postTitle}\" adresu {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Pritarkite mano p<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Palaikykite mano parinktį: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "<PERSON>lai<PERSON><PERSON><PERSON> mano peticiją \"{postTitle}\" adresu {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Palaikyki<PERSON> mano peticiją: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Palaikyki<PERSON> mano peticiją: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Paremkite mano projektą '{postTitle}' adresu {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Paremkite mano projektą: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Paremkite mano projektą: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Palaikykite mano p<PERSON> '{postTitle}' adresu {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Pritarkite mano p<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "<PERSON>ą tik paskelbiau pasiūly<PERSON>ą {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Prisijunkite prie diskusijos apie šį klausimą '{postTitle}' adresu {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Prisijunkite prie diskusijos: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Prisijunkite prie diskusijos: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "<PERSON><PERSON><PERSON><PERSON> u<PERSON> {postTitle}", "app.components.PostComponents.linkToHomePage": "Nuoroda į pagrindinį puslapį", "app.components.PostComponents.readMore": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>...", "app.components.PostComponents.topics": "Temos", "app.components.ProjectArchivedIndicator.archivedProject": "<PERSON><PERSON>, <PERSON>iame projekte nebe<PERSON> da<PERSON>, nes jis buvo archy<PERSON>.", "app.components.ProjectArchivedIndicator.previewProject": "Projekto projektas:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "<PERSON><PERSON><PERSON> tik moderatoriams ir tie<PERSON>, kurie turi <PERSON> nuo<PERSON>.", "app.components.ProjectCard.a11y_projectDescription": "<PERSON>je<PERSON><PERSON>:", "app.components.ProjectCard.a11y_projectTitle": "Projekto pavadinimas:", "app.components.ProjectCard.addYourOption": "Pridėkite savo parinktį", "app.components.ProjectCard.allocateYourBudget": "Skirkite biudžetą", "app.components.ProjectCard.archived": "Archyvuota", "app.components.ProjectCard.comment": "Komentaras", "app.components.ProjectCard.contributeYourInput": "Prisidėkite savo indėliu", "app.components.ProjectCard.finished": "Baigtas", "app.components.ProjectCard.joinDiscussion": "Prisijunkite prie diskusijos", "app.components.ProjectCard.learnMore": "Sužinokite daugiau", "app.components.ProjectCard.reaction": "Reakcija", "app.components.ProjectCard.readTheReport": "Skaitykite ataskaitą", "app.components.ProjectCard.reviewDocument": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.submitAnIssue": "Pateikti komentarą", "app.components.ProjectCard.submitYourIdea": "Pateikite savo idėją", "app.components.ProjectCard.submitYourInitiative": "Pateikite savo iniciatyvą", "app.components.ProjectCard.submitYourPetition": "Pateikti peticiją", "app.components.ProjectCard.submitYourProject": "Pateikite savo projektą", "app.components.ProjectCard.submitYourProposal": "Pateikite savo pasiūlymą", "app.components.ProjectCard.takeThePoll": "Atlikite apklausą", "app.components.ProjectCard.takeTheSurvey": "Atlikite apklausą", "app.components.ProjectCard.viewTheContributions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> įnašus", "app.components.ProjectCard.viewTheIdeas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheInitiatives": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in<PERSON>", "app.components.ProjectCard.viewTheIssues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.ProjectCard.viewTheOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewThePetitions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheProjects": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> projektus", "app.components.ProjectCard.viewTheProposals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheQuestions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.ProjectCard.vote": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# komentarai} other {# komentarai}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# įnašas} other {# įnašai}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {dar n<PERSON>ra id<PERSON>} one {# idėja} other {# idėjos}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# iniciatyvos} one {# iniciatyvos} other {# iniciatyvos}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# komentaras} other {# komentarai}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# parinktis} other {# parinktys}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# peticijos} one {# peticija} other {# peticijos}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# projektas} other {# projektai}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# pasiūlymai} one {# pasiūlymas} other {# pasiūlymai}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# klausimas} other {# klausimai}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# komentarai} one {# komentarai} other {# komentarai}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# įvestys} one {# įvestis} other {# įvestys}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projektai} one {# projektas} other {# projektai}}", "app.components.ProjectFolderCards.components.Topbar.all": "Visi", "app.components.ProjectFolderCards.components.Topbar.archived": "Archyvuota", "app.components.ProjectFolderCards.components.Topbar.draft": "Projektas", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filt<PERSON><PERSON><PERSON> pagal", "app.components.ProjectFolderCards.components.Topbar.published2": "Paskelbta", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "<PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.noProjectYet": "Šiuo metu nėra atvirų projektų", "app.components.ProjectFolderCards.noProjectsAvailable": "Projektų nėra", "app.components.ProjectFolderCards.showMore": "<PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.stayTuned": "Dar kartą apsilankykite ir sužinokite apie naujas įsitraukimo galimybes", "app.components.ProjectFolderCards.tryChangingFilters": "Pabandykite pakeisti pasirinktus filtrus.", "app.components.ProjectTemplatePreview.alsoUsedIn": "<PERSON>p pat naudo<PERSON> miestuose:", "app.components.ProjectTemplatePreview.copied": "Nukopijuota", "app.components.ProjectTemplatePreview.copyLink": "Kopijuoti nuorodą", "app.components.QuillEditor.alignCenter": "Tekstas centre", "app.components.QuillEditor.alignLeft": "Lygiuoti į kairę", "app.components.QuillEditor.alignRight": "Sulygiuoti į dešinę", "app.components.QuillEditor.bold": "Drąsiai", "app.components.QuillEditor.clean": "<PERSON><PERSON>lint<PERSON>avi<PERSON>", "app.components.QuillEditor.customLink": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.customLinkPrompt": "Įveskite nuorodą:", "app.components.QuillEditor.edit": "Red<PERSON><PERSON><PERSON>", "app.components.QuillEditor.image": "Įkelti paveikslėlį", "app.components.QuillEditor.imageAltPlaceholder": "Trumpas v<PERSON>", "app.components.QuillEditor.italic": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.link": "Pridėti nuorodą", "app.components.QuillEditor.linkPrompt": "Įveskite nuorodą:", "app.components.QuillEditor.normalText": "Normalus", "app.components.QuillEditor.orderedList": "Užsakytas sąrašas", "app.components.QuillEditor.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.components.QuillEditor.subtitle": "Paantraštė", "app.components.QuillEditor.title": "Pavadinimas", "app.components.QuillEditor.unorderedList": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.video": "<PERSON><PERSON><PERSON><PERSON> vaizdo įrašą", "app.components.QuillEditor.videoPrompt": "Įveskite vaizdo įrašą:", "app.components.QuillEditor.visitPrompt": "Apsilankykite nuorodoje:", "app.components.ReactionControl.completeProfileToReact": "Užpildykite savo profilį ir reaguokite", "app.components.ReactionControl.dislike": "Nepatinka", "app.components.ReactionControl.dislikingDisabledMaxReached": "Pasiekėte maksimalų nepatiko s<PERSON>čių {projectName}", "app.components.ReactionControl.like": "<PERSON><PERSON>", "app.components.ReactionControl.likingDisabledMaxReached": "Pasiekėte ma<PERSON> \"patinka\" skai<PERSON> {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Reagavimas bus įjungtas prasidėjus šiam etapui", "app.components.ReactionControl.reactingDisabledPhaseOver": "Šioje fazėje nebegalima reaguoti", "app.components.ReactionControl.reactingDisabledProjectInactive": "Daugiau nebegalite reaguoti į idėjas {projectName}.", "app.components.ReactionControl.reactingNotEnabled": "<PERSON><PERSON><PERSON> metu šiame projekte \"Reacting\" nėra įjungta", "app.components.ReactionControl.reactingNotPermitted": "Reagavimas įjungtas tik tam tikroms grupėms", "app.components.ReactionControl.reactingNotSignedIn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad <PERSON>.", "app.components.ReactionControl.reactingPossibleLater": "Reaga<PERSON><PERSON> p<PERSON> {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Patikrinkite savo tapatybę, kad <PERSON> reaguoti.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Renginio data: nuo {startDate} adresu {startTime} iki {endDate} adresu {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Renginio data: {eventDate} nuo {startTime} iki {endTime}.", "app.components.Sharing.linkCopied": "Nukopijuota nuoroda", "app.components.Sharing.or": "arba", "app.components.Sharing.share": "<PERSON><PERSON><PERSON>", "app.components.Sharing.shareByEmail": "<PERSON><PERSON><PERSON>", "app.components.Sharing.shareByLink": "Kopijuoti nuorodą", "app.components.Sharing.shareOnFacebook": "Dalytis \"Facebook", "app.components.Sharing.shareOnTwitter": "Dalytis \"Twitter", "app.components.Sharing.shareThisEvent": "<PERSON><PERSON> re<PERSON>", "app.components.Sharing.shareThisFolder": "<PERSON><PERSON><PERSON>", "app.components.Sharing.shareThisProject": "Dalytis š<PERSON>o projektu", "app.components.Sharing.shareViaMessenger": "<PERSON><PERSON> per \"Messenger", "app.components.Sharing.shareViaWhatsApp": "<PERSON><PERSON> per \"WhatsApp", "app.components.SideModal.closeButtonAria": "Uždaryti", "app.components.StatusModule.futurePhase": "Peržiūrite dar neprasidėjusį etapą. Galėsite dalyvauti, kai etapas prasid<PERSON>.", "app.components.StatusModule.modifyYourSubmission1": "Pakeiskite pateiktą informaciją", "app.components.StatusModule.submittedUntil3": "Jūsų balsas gali būti pateiktas iki", "app.components.TopicsPicker.numberOfSelectedTopics": "Pasirinkta {numberOfSelectedTopics, plural, =0 {nulis <PERSON>} one {viena žyma} other {# žymų}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Išplėsti vaizdą", "app.components.UI.MoreActionsMenu.moreOptions": "Daugiau galimybių", "app.components.UI.MoreActionsMenu.showMoreActions": "Rodyti daugiau veiksmų", "app.components.UI.NewLabel.new": "NAUJIENA", "app.components.UI.PhaseFilter.noAppropriatePhases": "Šiam projektui tinkamų etapų nerasta", "app.components.UI.RemoveImageButton.a11y_removeImage": "<PERSON><PERSON><PERSON><PERSON>", "app.components.UI.TranslateButton.original": "Originalus", "app.components.UI.TranslateButton.translate": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Unauthorized.additionalInformationRequired": "<PERSON><PERSON>, reikia pateikti papildomos informacijos.", "app.components.Unauthorized.completeProfile": "<PERSON><PERSON><PERSON> profilis", "app.components.Unauthorized.completeProfileTitle": "Užpildykite savo profilį, kad gal<PERSON>ėte dalyvauti", "app.components.Unauthorized.noPermission": "<PERSON><PERSON><PERSON> neturite leidimo <PERSON> šį puslapį", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON><PERSON><PERSON>, nesate įgaliotas pasiekti šį puslapį.", "app.components.Upload.errorImageMaxSizeExceeded": "Pasirinktas vaizdas yra didesnis nei {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Vienas ar keli pasirinkti vaizdai yra didesni nei {maxFileSize}MB.", "app.components.Upload.onlyOneImage": "Galite įkelti tik 1 paveikslėlį", "app.components.Upload.onlyXImages": "Galite įkelti tik {maxItemsCount} vaiz<PERSON>.", "app.components.Upload.remaining": "lik<PERSON>s", "app.components.Upload.uploadImageLabel": "Pasirinkite vaizdą (ne daugiau kaip {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Pasirinkite vieną ar daugiau vaizdų", "app.components.UpsellTooltip.tooltipContent": "Ši funkcija neįtraukta į dabartinį planą. Norėdami j<PERSON> atrak<PERSON>, pasitarkite su savo Vyriausybės sėkmės vadybininku arba administratoriumi.", "app.components.UserName.anonymous": "<PERSON><PERSON><PERSON><PERSON>", "app.components.UserName.anonymousTooltip2": "Šis naudotojas nusprendė anonimizuoti savo indėlį", "app.components.UserName.authorWithNoNameTooltip": "Jūsų vardas buvo sugeneruotas automatiškai, nes neįvedėte savo vardo. Jei norite jį pakeisti, atnaujinkite savo profilį.", "app.components.UserName.deletedUser": "nežinomas autorius", "app.components.UserName.verified": "<PERSON><PERSON><PERSON><PERSON>", "app.components.VerificationModal.verifyAuth0": "Patikrinkite su NemID", "app.components.VerificationModal.verifyBOSA": "Patikrinkite pagal itsme arba eID", "app.components.VerificationModal.verifyBosaFas": "Patikrinkite pagal itsme arba eID", "app.components.VerificationModal.verifyClaveUnica": "Patikrinti su \"Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "Patikrinimas naudojant suklastotą SSO", "app.components.VerificationModal.verifyIdAustria": "Patikrinkite su ID Austria", "app.components.VerificationModal.verifyKeycloak": "Patikrinkite su \"ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Patikrinkite su \"MitID", "app.components.VerificationModal.verifyTwoday2": "Patikrinkite naudodami \"BankID\" arba \"Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Patikrinkite savo tapatybę", "app.components.VoteControl.budgetingFutureEnabled": "Biudžetą galite skirti nuo {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Šiuo metu nėra galimybės sudaryti dalyvaujamąjį biudžetą.", "app.components.VoteControl.budgetingNotPossible": "Šiuo metu biudžeto keisti negalima.", "app.components.VoteControl.budgetingNotVerified": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>, apsilankykite {verifyAccountLink} .", "app.components.VoteInputs._shared.currencyLeft1": "Ju<PERSON> liko {budgetLeft} / {totalBudget}", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Jums liko {votesLeft, plural, =0 {j<PERSON><PERSON> kreditų} other {# iš {totalNumberOfVotes, plural, one {1 kreditas} other {# kreditų}}}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Jums liko {votesLeft, plural, =0 {j<PERSON><PERSON> ta<PERSON>} other {# iš {totalNumberOfVotes, plural, one {1 taškas} other {# taškų}}}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Jums liko {votesLeft, plural, =0 {j<PERSON><PERSON> žetonų} other {# iš {totalNumberOfVotes, plural, one {1 žetonas} other {# žetonų}}}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Ju<PERSON> liko {votesLeft, plural, =0 {balsų} other {# iš {totalNumberOfVotes, plural, one {1 balsas} other {# balsų}}}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "<PERSON>au pateikėte savo biudžetą. Norėdami jį pakeisti, spustelėkite \"Modify your submission\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "<PERSON>au pateikėte savo biudžetą. Norėdami jį pakeisti, grįžkite į projekto puslapį ir spustelėkite \"Modify your submission\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Biudžeto su<PERSON> negal<PERSON>, nes šis etapas nėra aktyvu<PERSON>.", "app.components.VoteInputs.single.youHaveVotedForX2": "<PERSON><PERSON><PERSON> balsavote už {votes, plural, =0 {# parinktys} one {# parinktis} other {# parinktys}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON>, kad prarasite visus su <PERSON>ia įvestimi susi<PERSON><PERSON> duomenis, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, re<PERSON><PERSON><PERSON> ir balsus. <PERSON><PERSON> ve<PERSON> at<PERSON> neįmanoma.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Ar tikrai norite ištrinti šią įvestį?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Patvirtinkite", "app.components.admin.SlugInput.resultingURL": "Gautas URL adresas", "app.components.admin.SlugInput.slugTooltip": "Slugas - tai unikalus <PERSON>, esantis puslapio <PERSON> (URL) pabaigoje.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "<PERSON><PERSON> p<PERSON>te URL, n<PERSON><PERSON><PERSON> į puslapį, k<PERSON><PERSON> naudo<PERSON><PERSON> senasis <PERSON>, nebeveiks.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_email": "El. <PERSON>", "app.components.admin.UserFilterConditions.field_event_attendance": "Renginių registracija", "app.components.admin.UserFilterConditions.field_follow": "Sekite", "app.components.admin.UserFilterConditions.field_lives_in": "Gyvena", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Bend<PERSON>jos s<PERSON>jų apklausa", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Sąveikavo su įvestimi, k<PERSON><PERSON> b<PERSON>", "app.components.admin.UserFilterConditions.field_participated_in_project": "Prisidėjo prie projekto", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Paskelbta kažkas su žyma", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Registracijos data", "app.components.admin.UserFilterConditions.field_role": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_verified": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "nėra užsiregistravęs nė viename iš š<PERSON> re<PERSON>ini<PERSON>.", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "nėra užsiregistravęs joki<PERSON>.", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "yra užsiregistravęs viename iš š<PERSON> rengini<PERSON>.", "app.components.admin.UserFilterConditions.predicate_attends_something": "yra u<PERSON>egist<PERSON><PERSON><PERSON><PERSON> bent viename reng<PERSON>y<PERSON>.", "app.components.admin.UserFilterConditions.predicate_begins_with": "prasideda nuo", "app.components.admin.UserFilterConditions.predicate_commented_in": "komentavo", "app.components.admin.UserFilterConditions.predicate_contains": "yra", "app.components.admin.UserFilterConditions.predicate_ends_on": "b<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_has_value": "turi vert<PERSON>", "app.components.admin.UserFilterConditions.predicate_in": "atliko bet kokį veiksmą.", "app.components.admin.UserFilterConditions.predicate_is": "yra .", "app.components.admin.UserFilterConditions.predicate_is_admin": "yra <PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_after": "yra po", "app.components.admin.UserFilterConditions.predicate_is_before": "yra p<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_checked": "tikrinama", "app.components.admin.UserFilterConditions.predicate_is_empty": "yra t<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_equal": "yra .", "app.components.admin.UserFilterConditions.predicate_is_exactly": "yra lygiai", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "yra <PERSON>", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "yra didesnis arba lygus", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "yra paprastas naudotojas", "app.components.admin.UserFilterConditions.predicate_is_not_area": "<PERSON><PERSON><PERSON> terito<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "neįtraukia aplanko", "app.components.admin.UserFilterConditions.predicate_is_not_input": "neįtraukia įvesties", "app.components.admin.UserFilterConditions.predicate_is_not_project": "neapima projekto", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "neapima temos", "app.components.admin.UserFilterConditions.predicate_is_one_of": "yra vienas iš", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "viena iš sričių", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "vienas iš aplankų", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "vienas iš įėjimų", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "vienas iš projektų", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "viena iš temų", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "yra projektų vadovas.", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "yra ma<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "yra ma<PERSON> arba lygus", "app.components.admin.UserFilterConditions.predicate_is_verified": "yra pat<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "neprasideda nuo", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "nekomentavo", "app.components.admin.UserFilterConditions.predicate_not_contains": "nėra", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "<PERSON>sibaigi<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_has_value": "neturi vert<PERSON>s", "app.components.admin.UserFilterConditions.predicate_not_in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is": "nėra", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "nėra administratorius", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "n<PERSON><PERSON> t<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "nėra", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "nėra įprastas naudotojas", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "nėra vienas iš", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "nėra projektų vadovas.", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "<PERSON><PERSON><PERSON> pat<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "nepaskelbė įvesties", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "nereagavo į komentarą", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "nereagavo į įvestį", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "neužsiregistravo į renginį", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "neatlik<PERSON> a<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "savanoriškai nedalyvavo", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "ne<PERSON><PERSON><PERSON><PERSON> balsavime", "app.components.admin.UserFilterConditions.predicate_nothing": "nieko", "app.components.admin.UserFilterConditions.predicate_posted_input": "paskelbė įvestį", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reagavo į komentarą", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reagavo į įvestį", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "užsiregistravę į renginį", "app.components.admin.UserFilterConditions.predicate_something": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_taken_survey": "atliko apklausą", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "savanoriškai", "app.components.admin.UserFilterConditions.predicate_voted_in3": "da<PERSON><PERSON><PERSON> balsavime", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Atributas", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Būklė", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "Vertė", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Negausite pranešimų apie savo indėlį", "app.components.anonymousParticipationModal.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.participateAnonymously": "Dalyvaukite anonimiškai", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "Taip <b>j<PERSON><PERSON><PERSON> profilis</b> bus saugiai <b>p<PERSON><PERSON><PERSON><PERSON><PERSON></b> nuo administratorių, projektų vadovų ir kitų šio konkretaus įnašo gyventojų, kad niekas negalėtų susieti šio įnašo su jumis. Anoniminiai įnašai negali būti redaguojami ir laikomi galutiniais.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Svarbiausias mūsų prioritetas - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad mūsų platforma būtų saugi kiekvienam naudotojui. Žodžiai yra <PERSON>, to<PERSON><PERSON><PERSON> būkite malonūs vieni kitiems.", "app.components.avatar.titleForAccessibility": "<PERSON><PERSON> p<PERSON> {fullName}", "app.components.customFields.mapInput.removeAnswer": "Pašalinti atsakymą", "app.components.customFields.mapInput.undo": "<PERSON><PERSON><PERSON><PERSON>", "app.components.customFields.mapInput.undoLastPoint": "Panaikinti paskutinį punktą", "app.components.followUnfollow.follow": "Sekite", "app.components.followUnfollow.followADiscussion": "<PERSON><PERSON><PERSON>", "app.components.followUnfollow.followTooltipInputPage2": "Toliau siunčiami el. pa<PERSON>to p<PERSON>i apie bū<PERSON>, oficialius atnaujinimus ir komentarus. Bet kuriuo metu galite {unsubscribeLink} .", "app.components.followUnfollow.followTooltipProjects2": "Toliau siunčiami el. pašto pranešimai apie projekto pakeitimus. Bet kuriuo metu galite {unsubscribeLink} .", "app.components.followUnfollow.unFollow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.followUnfollow.unsubscribe": "atsisakyti prenumeratos", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "mūsų gairės", "app.components.form.ErrorDisplay.next": "Kitas", "app.components.form.ErrorDisplay.previous": "<PERSON><PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Pradėkite r<PERSON>š<PERSON>i, jei norite ieškoti pagal naudotojo el. pašto ad<PERSON> arba vardą...", "app.components.form.anonymousSurveyMessage2": "Visi šios apklausos atsakymai yra anoniminiai.", "app.components.form.backToInputManager": "Grįžti į įvesties tvarkyklę", "app.components.form.backToProject": "Atgal į projektą", "app.components.form.components.controls.mapInput.removeAnswer": "Pašalinti atsakymą", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON><PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "Panaikinti paskutinį punktą", "app.components.form.controls.addressInputAriaLabel": "Adreso įvedimas", "app.components.form.controls.addressInputPlaceholder6": "Įveskite adresą...", "app.components.form.controls.adminFieldTooltip": "<PERSON><PERSON> mat<PERSON> t<PERSON>", "app.components.form.controls.allStatementsError": "<PERSON><PERSON> b<PERSON><PERSON> p<PERSON>rin<PERSON>as atsaky<PERSON> į visus teiginius.", "app.components.form.controls.back": "Atgal", "app.components.form.controls.clearAll": "Išvalyti viską", "app.components.form.controls.clearAllScreenreader": "Išvalykite visus atsakymus iš pirmiau pateikto matricos klausimo", "app.components.form.controls.clickOnMapMultipleToAdd3": "Spustelėkite žemėlapį ir pieškite. <PERSON><PERSON> vil<PERSON> taš<PERSON>, kad juo<PERSON>.", "app.components.form.controls.clickOnMapToAddOrType": "Spustelėkite žemėlapį arba įveskite adresą ir pridėkite atsakymą.", "app.components.form.controls.confirm": "Patvirtinkite", "app.components.form.controls.cosponsorsPlaceholder": "Pradėkite rinkti vardą, kurio norite i<PERSON>škoti", "app.components.form.controls.currentRank": "<PERSON><PERSON><PERSON><PERSON> rang<PERSON>:", "app.components.form.controls.minimumCoordinates2": "Reikia surinkti ne ma<PERSON>iau kaip {numPoints} žemėlapio taškų.", "app.components.form.controls.noRankSelected": "Nepasirinktas rangas", "app.components.form.controls.notPublic1": "*Šiuo atsakymu bus dalijamasi tik su projektų vadovais, o ne viešai.", "app.components.form.controls.optionalParentheses": "(neprivaloma)", "app.components.form.controls.rankingInstructions": "Vilkite ir nuleiskite rang<PERSON> par<PERSON>.", "app.components.form.controls.selectAsManyAsYouLike": "*Pasirinkite tiek, kiek norite", "app.components.form.controls.selectBetween": "*Pasirinkite iš {minItems} ir {maxItems} parinkčių", "app.components.form.controls.selectExactly2": "*Pasirinkite tiksliai {selectExactly, plural, one {# parinktis} other {# parinktys}}", "app.components.form.controls.selectMany": "*Pasirinkite tiek, kiek norite", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Bakstelėkite žemėlapį ir bra<PERSON>ž<PERSON>. <PERSON><PERSON> vil<PERSON> ta<PERSON>, kad juo<PERSON>.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Bakstelėkite žemėlapį ir braižykite.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Bakstelėkite žemėlapį ir pridėkite atsakymą.", "app.components.form.controls.tapOnMapToAddOrType": "Bakstelėkite žemėlapį arba toliau įveskite adresą ir pridėkite atsakymą.", "app.components.form.controls.tapToAddALine": "Bakstelėkite , kad pridėtumėte eilutę", "app.components.form.controls.tapToAddAPoint": "Bakstelėkite , kad pridėtumėte tašką", "app.components.form.controls.tapToAddAnArea": "Bakstelėkite , kad p<PERSON>tumėte sritį", "app.components.form.controls.uploadShapefileInstructions": "* Įkelkite zip failą, kuriame yra vienas ar daugiau shape failų.", "app.components.form.controls.validCordinatesTooltip2": "Jei vieta nerodoma tarp įvestų par<PERSON>, galite prid<PERSON>ti galiojanč<PERSON> koordinates formatu \"platuma, ilguma\" ir nurodyti tikslią vietą (pvz., -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} iš {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} iš {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} iš {total}, kur {maxValue} yra {maxLabel}.", "app.components.form.error": "<PERSON><PERSON><PERSON>", "app.components.form.locationGoogleUnavailable": "Nepavyko įkelti \"Google Maps\" pateikto vietos lauko.", "app.components.form.progressBarLabel": "Apklausos pažanga", "app.components.form.submit": "Pat<PERSON><PERSON><PERSON>", "app.components.form.submitApiError": "<PERSON><PERSON> problema pateiki<PERSON> formą. <PERSON><PERSON><PERSON><PERSON><PERSON>, ar <PERSON><PERSON><PERSON>, ir bandykite dar kartą.", "app.components.form.verifiedBlocked": "<PERSON><PERSON> la<PERSON> ne<PERSON>, nes jame yra patikrinta informacija.", "app.components.formBuilder.Page": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.accessibilityStatement": "prieina<PERSON><PERSON>", "app.components.formBuilder.addAnswer": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.addStatement": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.agree": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "Jei turite prieigą prie mūsų dirbtinio intelekto paketo, galėsite apibendrinti ir kategorizuoti teksto atsakymus naudodami dirbtinį intelektą.", "app.components.formBuilder.askFollowUpToggleLabel": "Paklauskite tolesnių veiksmų", "app.components.formBuilder.bad": "Blogas", "app.components.formBuilder.buttonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.buttonLink": "<PERSON><PERSON>uk<PERSON> nuoroda", "app.components.formBuilder.cancelLeaveBuilderButtonText": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.category": "Kategorija", "app.components.formBuilder.chooseMany": "Pasirinkite daug", "app.components.formBuilder.chooseOne": "Pasirinkite vieną", "app.components.formBuilder.close": "Uždaryti", "app.components.formBuilder.closed": "Uždaras", "app.components.formBuilder.configureMap": "Konfigūruoti žemėlapį", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON><PERSON>, nor<PERSON>i", "app.components.formBuilder.content": "Turinys", "app.components.formBuilder.continuePageLabel": "Tęsia", "app.components.formBuilder.cosponsors": "Bend<PERSON>aut<PERSON><PERSON>", "app.components.formBuilder.default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.defaultContent": "<PERSON>uma<PERSON><PERSON><PERSON> turinys", "app.components.formBuilder.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Tai jau įtraukta į formą. Numatytąjį turinį galima naudoti tik vieną kartą.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Pasirinktinio turinio p<PERSON> nėra dabartinės licencijos dalis. Susisiekite su savo \"GovSuccess\" vadybininku ir sužinokite apie tai daugiau.", "app.components.formBuilder.disagree": "Nesutinku", "app.components.formBuilder.displayAsDropdown": "<PERSON><PERSON><PERSON> kaip išskleidžiamąjį langą", "app.components.formBuilder.displayAsDropdownTooltip": "Rodyti parinktis išskleidžiamajame lange. Jei turite daug parinkčių, rekomenduojama rinktis šį variantą.", "app.components.formBuilder.done": "Atlikta", "app.components.formBuilder.drawArea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.drawRoute": "Nubrė<PERSON><PERSON> ma<PERSON>", "app.components.formBuilder.dropPin": "Įspaudžiamas kai<PERSON>tis", "app.components.formBuilder.editButtonLabel": "Red<PERSON><PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "Pateikite bent 1 atsakymą. Atkreipkite dėmesį, kad kiekvienas atsakymas turi turėti pavadinim<PERSON>.", "app.components.formBuilder.emptyOptionError": "Pateikite bent 1 atsakymą", "app.components.formBuilder.emptyStatementError": "Pateikite bent 1 teiginį", "app.components.formBuilder.emptyTitleError": "Pateikite klaus<PERSON> p<PERSON>", "app.components.formBuilder.emptyTitleMessage": "Pateikite visų atsakymų pavadinimus", "app.components.formBuilder.emptyTitleStatementMessage": "Pateikite visų teiginių pavadinimus", "app.components.formBuilder.enable": "Įjungti", "app.components.formBuilder.errorMessage": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>, kad <PERSON> išsaugoti p<PERSON>.", "app.components.formBuilder.fieldGroup.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> (neprivaloma)", "app.components.formBuilder.fieldGroup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (neprivaloma)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "<PERSON><PERSON><PERSON> metu atsakymus į šiuos klausimus galima rasti tik \"Input Manager\" eksportuotame \"Excel\" faile, tač<PERSON><PERSON> jie nėra matomi naudo<PERSON>.", "app.components.formBuilder.fieldLabel": "Atsakymų variantai", "app.components.formBuilder.fieldLabelStatement": "Pareiškimai", "app.components.formBuilder.fileUpload": "Failų įkėlimas", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Žemėlapiu pagrįstas puslapis", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Įterpkite žemėlapį kaip konte<PERSON> arba užduokite dalyviams su vieta susijusių klausimų.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Kad naudo<PERSON>jo patirtis būtų optimali, nerekomenduojame į žemėlapiais pagrįstus puslapius įtraukti taškų, mar<PERSON><PERSON><PERSON><PERSON> ar vietovių klausimų.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Įprastas puslapis", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Apklausų kartografavimo funkcijos nėra įtrauktos į dabartinę licenciją. Susisiekite su savo \"GovSuccess\" vadybininku ir sužinokite daugiau.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Puslapio tipas", "app.components.formBuilder.formEnd": "Formos pabaiga", "app.components.formBuilder.formField.cancelDeleteButtonText": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "Taip, ištrinkite puslapį", "app.components.formBuilder.formField.copyNoun": "Ko<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.copyVerb": "Ko<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "<PERSON><PERSON><PERSON>nus šį puslapį, taip pat bus ištrinta su juo susijusi logika. Ar tikrai norite jį ištrinti?", "app.components.formBuilder.formField.deleteResultsInfo": "To negalima at<PERSON><PERSON><PERSON>", "app.components.formBuilder.goToPageInputLabel": "Kitas puslapis yra:", "app.components.formBuilder.good": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.helmetTitle": "<PERSON><PERSON>", "app.components.formBuilder.imageFileUpload": "Vaizdų įkėlimas", "app.components.formBuilder.invalidLogicBadgeMessage": "Netinkama logika", "app.components.formBuilder.labels2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (neprivaloma)", "app.components.formBuilder.labelsTooltipContent2": "Pasirinkite ne<PERSON><PERSON><PERSON><PERSON><PERSON> et<PERSON> bet kuriai linijinės s<PERSON> verte<PERSON>.", "app.components.formBuilder.lastPage": "Pabaiga", "app.components.formBuilder.layout": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Ar tikrai norite išvykti?", "app.components.formBuilder.leaveBuilderText": "Neišsaugoti pakeitimai. <PERSON><PERSON><PERSON> i<PERSON>dam<PERSON> išsaugokite. Jei <PERSON>, prarasite pakeitimus.", "app.components.formBuilder.limitAnswersTooltip": "Įjungus <PERSON><PERSON><PERSON>, respondentai turi pasirinkti nurodytą atsakymų skaičių, kad gal<PERSON>tų tęsti tyrimą.", "app.components.formBuilder.limitNumberAnswers": "Apriboti atsakymų skaičių", "app.components.formBuilder.linePolygonMapWarning2": "Linijų ir daugiakampių brėžiniai gali neatitikti prieinamumo standartų. Daugiau informacijos rasite {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Linijinė skalė", "app.components.formBuilder.locationDescription": "Vieta", "app.components.formBuilder.logic": "Logika", "app.components.formBuilder.logicAnyOtherAnswer": "Bet koks kitas atsakymas", "app.components.formBuilder.logicConflicts.conflictingLogic": "Prieštaringa logika", "app.components.formBuilder.logicConflicts.interQuestionConflict": "<PERSON>iame puslapyje patei<PERSON> klaus<PERSON>, kurie veda į skirtingus puslapius. <PERSON><PERSON> dalyviai atsakys į kelis klausimus, bus rodomas to<PERSON>i esantis puslapis. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad toks elgesys atitiktų jūsų numatytą srautą.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Šiame puslapyje taikomos kelios loginės taisyklės: keli<PERSON> klausimų pasirinkimo logika, puslapio lygmens logika ir klausimų tarpusavio logika. Kai šios s<PERSON>lygos sutampa, klausimo logika bus viršesnė už puslapio logiką ir bus rodomas toliausiai esantis puslapis. Peržiūrėkite logiką, kad įsitikintumėte, jog ji atitinka jūsų numatytą srautą.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Šiame puslapyje patei<PERSON> daug<PERSON><PERSON><PERSON> pasi<PERSON> k<PERSON>, k<PERSON><PERSON> parink<PERSON> veda į skirtingus puslapius. Jei dalyviai pasirinks kelias parink<PERSON>, bus rod<PERSON><PERSON> to<PERSON> puslapis. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad toks elgesys atitiktų jūsų numatytą srautą.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Šiame puslapyje patei<PERSON> daug<PERSON><PERSON><PERSON> p<PERSON> klaus<PERSON>, kuri<PERSON> par<PERSON> veda į skirtingus puslapius, ir k<PERSON><PERSON>, kurie veda į kitus puslapius. <PERSON><PERSON> s<PERSON> sutam<PERSON>, bus rod<PERSON><PERSON> to<PERSON> puslapis. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad toks elgesys atitiktų jūsų numatytą srautą.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Šiame puslapyje pateikiamas daug<PERSON><PERSON><PERSON> p<PERSON> klaus<PERSON>, k<PERSON><PERSON> par<PERSON> veda į skirtingus puslapius, o logika nustatyta ir puslapio, ir klausimo lygmeniu. Pirmenybė teikiama klausimo logikai, tod<PERSON>l bus rodomas toliausiai esantis puslapis. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad toks elgesys atitiktų jūsų numatytą srautą.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Šiame puslapyje logika nustatyta ir puslapio, ir k<PERSON><PERSON>. Klausimo logika bus viršesnė už puslapio lygio logiką. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad toks elgesys atitiktų jūsų numatytą srautą.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Šiame puslapyje logika nustatyta ir pu<PERSON><PERSON>, ir klaus<PERSON>, o keli klausimai nukreipiami į skirtingus puslapius. Pirmenybė teikiama klausimų logikai, todėl bus rodomas toliausiai esantis puslapis. Įsitikinkite, kad toks elgesys atitinka jūsų numatytą srautą.", "app.components.formBuilder.logicNoAnswer2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Jei bet koks kitas atsakymas", "app.components.formBuilder.logicPanelNoAnswer": "<PERSON><PERSON>", "app.components.formBuilder.logicValidationError": "Logika negali pateikti nuorodų į ankstesnius puslapius", "app.components.formBuilder.longAnswer": "Ilgas atsakymas", "app.components.formBuilder.mapConfiguration": "Žemėlapio konfigūracija", "app.components.formBuilder.mapping": "Žemėlapių sudarymas", "app.components.formBuilder.mappingNotInCurrentLicense": "Apklausų kartografavimo funkcijos nėra įtrauktos į dabartinę licenciją. Susisiekite su savo \"GovSuccess\" vadybininku ir sužinokite daugiau.", "app.components.formBuilder.matrix": "Matrica", "app.components.formBuilder.matrixSettings.columns": "Stulpeliai", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "<PERSON><PERSON>", "app.components.formBuilder.multipleChoiceHelperText": "<PERSON>i kelios parinktys veda į skirtingus puslapius ir dalyviai pasirenka daugiau nei vieną, bus rodomas toliausiai esantis puslapis. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad toks elgesys atitiktų jūsų numatytą srautą.", "app.components.formBuilder.multipleChoiceImage": "Vaizdų pasirinkimas", "app.components.formBuilder.multiselect.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.minimum": "Minimal<PERSON>", "app.components.formBuilder.neutral": "Neutral<PERSON>", "app.components.formBuilder.newField": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.number": "Numeris", "app.components.formBuilder.ok": "G<PERSON><PERSON>", "app.components.formBuilder.open": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.optional": "Pasirinktinai", "app.components.formBuilder.other": "<PERSON><PERSON>", "app.components.formBuilder.otherOption": "\"Kita\" parinktis", "app.components.formBuilder.otherOptionTooltip": "<PERSON><PERSON><PERSON> įvesti pasirinktinį atsakymą, jei pateikti atsakymai neatitinka jų pageidavimų.", "app.components.formBuilder.page": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.pageCannotBeDeleted": "Šio puslapio negalima ištrinti.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Šio puslapio negalima ištrinti ir jame negalima pridėti jokių papildomų laukų.", "app.components.formBuilder.pageRuleLabel": "Kitas puslapis yra:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "<PERSON>i neprid<PERSON>ta jokios logikos, forma bus tvarkoma įprastai. Jei ir pusla<PERSON>, ir jo klausimuose yra logikos, pirmenybė bus teikiama klausimų logikai. Įsitikinkite, kad tai atitinka jūsų numatytą srautą Daugiau informacijos rasite svetainėje {supportPageLink}.", "app.components.formBuilder.preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Atsisiųstame PDF faile bendrapartneriai nerodomi ir nėra palaikomi importuojant per FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "Atsisiųstame PDF faile failų įkėlimo klausimai rodomi kaip nepalaikomi ir nepalaikomi importuojant per \"FormSync\".", "app.components.formBuilder.printSupportTooltip.mapping": "Žemėlapio klausimai rodomi atsisiųstame PDF dokumente, tačiau sluoksniai nebus matomi. Atvaizdavimo klausimai nepalaikomi importuojant per FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Matricos klausimai rodomi atsisiųstame PDF dokumente, tačiau šiuo metu jie nepalaikomi importuojant per \"FormSync\".", "app.components.formBuilder.printSupportTooltip.page": "Puslapių pavadinimai ir aprašymai atsisiunčiamame PDF dokumente rodomi kaip <PERSON>ų antraštės.", "app.components.formBuilder.printSupportTooltip.ranking": "Reitingo klausimai rodomi atsisiųstame PDF dokumente, tačiau šiuo metu jų negalima importuoti per \"FormSync\".", "app.components.formBuilder.printSupportTooltip.topics2": "Atsisiųstame PDF dokumente žymos rodomos kaip nepalaikomos ir nepalaikomos importuojant per FormSync.", "app.components.formBuilder.proposedBudget": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "Šio klausimo negalima ištrinti.", "app.components.formBuilder.questionDescriptionOptional": "<PERSON><PERSON> (neprivaloma)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON>", "app.components.formBuilder.randomize": "<PERSON>si<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "app.components.formBuilder.randomizeToolTip": "Kiekvieno naudotojo atsakymų eiliškumas bus nustatomas atsitiktine tvarka.", "app.components.formBuilder.range": "Diapazonas", "app.components.formBuilder.ranking": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.rating": "Įvertinimas", "app.components.formBuilder.removeAnswer": "Pašalinti atsakymą", "app.components.formBuilder.required": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.requiredToggleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad į šį klausimą reikia atsakyti", "app.components.formBuilder.ruleForAnswerLabel": "<PERSON>i atsakymas yra:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "<PERSON><PERSON> at<PERSON>i a<PERSON>:", "app.components.formBuilder.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.components.formBuilder.selectRangeTooltip": "Pasirinkite didžiausią skalės re<PERSON>.", "app.components.formBuilder.sentiment": "Nuotaikų skalė", "app.components.formBuilder.shapefileUpload": "Esri shapefile įkėlimas", "app.components.formBuilder.shortAnswer": "<PERSON><PERSON>", "app.components.formBuilder.showResponseToUsersToggleLabel": "Rodyti atsakymą naudotojams", "app.components.formBuilder.singleChoice": "Vienas pasirinkim<PERSON>", "app.components.formBuilder.staleDataErrorMessage2": "<PERSON><PERSON><PERSON><PERSON> problema. Ši įvesties forma neseniai buvo išsaugota kitoje vietoje. <PERSON><PERSON> gali b<PERSON><PERSON>, kad jūs arba kitas naudotojas ją atidarėte redaguoti kitame naršyklės lange. Atnaujinkite puslapį, kad gautumėte naujausią formą, ir vėl atlikite pakeitimus.", "app.components.formBuilder.stronglyAgree": "Visiškai sutinku", "app.components.formBuilder.stronglyDisagree": "Visiškai nesutinku", "app.components.formBuilder.supportArticleLinkText": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.tags": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.title": "Pavadinimas", "app.components.formBuilder.toLabel": "į", "app.components.formBuilder.unsavedChanges": "Turite neišsaugotų pakeitimų", "app.components.formBuilder.useCustomButton2": "<PERSON><PERSON><PERSON> pasirinktinį pusla<PERSON> my<PERSON>", "app.components.formBuilder.veryBad": "Labai blogai", "app.components.formBuilder.veryGood": "Labai gerai", "app.components.ideas.similarIdeas.engageHere": "Įsitraukite čia", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Panašių paraiškų nerasta.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Radome panašių submisijų - bendravimas su jomis gali padėti jas sustiprinti!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> jau paskel<PERSON>i:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Ieškote panašių paraiškų...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {Mažiau nei dien<PERSON>} one {# diena} other {# dienų}} palikta", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  lik<PERSON><PERSON>", "app.components.screenReaderCurrency.AED": "Jungtinių Arabų Emyratų dirhamas", "app.components.screenReaderCurrency.AFN": "Afganų afganų", "app.components.screenReaderCurrency.ALL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.AMD": "Armėnų dramos", "app.components.screenReaderCurrency.ANG": "Nyderlandų Antilų guldenas", "app.components.screenReaderCurrency.AOA": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ARS": "Argentinos pesas", "app.components.screenReaderCurrency.AUD": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>", "app.components.screenReaderCurrency.AWG": "Arubo florinas", "app.components.screenReaderCurrency.AZN": "Azerbaidžano manatas", "app.components.screenReaderCurrency.BAM": "Bosnija ir Hercegovina Konvertuojamasis ženklas", "app.components.screenReaderCurrency.BBD": "<PERSON><PERSON> doleris", "app.components.screenReaderCurrency.BDT": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BGN": "<PERSON><PERSON><PERSON><PERSON><PERSON> levas", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "<PERSON><PERSON><PERSON><PERSON><PERSON> frank<PERSON>", "app.components.screenReaderCurrency.BMD": "Bermudų doleris", "app.components.screenReaderCurrency.BND": "<PERSON><PERSON><PERSON><PERSON><PERSON> doleris", "app.components.screenReaderCurrency.BOB": "<PERSON>li<PERSON><PERSON>", "app.components.screenReaderCurrency.BOV": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BRL": "Brazilijos realas", "app.components.screenReaderCurrency.BSD": "<PERSON>ham<PERSON> doleris", "app.components.screenReaderCurrency.BTN": "<PERSON>ano <PERSON>ultrum", "app.components.screenReaderCurrency.BWP": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "<PERSON>lta<PERSON><PERSON><PERSON> rublis", "app.components.screenReaderCurrency.BZD": "<PERSON><PERSON><PERSON> do<PERSON>", "app.components.screenReaderCurrency.CAD": "<PERSON><PERSON><PERSON> doleris", "app.components.screenReaderCurrency.CDF": "<PERSON><PERSON> frank<PERSON>", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Šveicarijos frankas", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Čilės <PERSON> vien<PERSON> (UF)", "app.components.screenReaderCurrency.CLP": "<PERSON><PERSON><PERSON><PERSON> pesas", "app.components.screenReaderCurrency.CNY": "<PERSON><PERSON><PERSON> juan<PERSON>", "app.components.screenReaderCurrency.COP": "Kolumbijos pesas", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Kosta Rikos Colón", "app.components.screenReaderCurrency.CRE": "K<PERSON><PERSON>", "app.components.screenReaderCurrency.CUC": "<PERSON><PERSON> k<PERSON> pesas", "app.components.screenReaderCurrency.CUP": "<PERSON><PERSON> pesas", "app.components.screenReaderCurrency.CVE": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CZK": "Čekijos kronos", "app.components.screenReaderCurrency.DJF": "Džib<PERSON><PERSON><PERSON> frank<PERSON>", "app.components.screenReaderCurrency.DKK": "<PERSON><PERSON> k<PERSON>", "app.components.screenReaderCurrency.DOP": "Dominikos pesas", "app.components.screenReaderCurrency.DZD": "Alžyro <PERSON>", "app.components.screenReaderCurrency.EGP": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "app.components.screenReaderCurrency.ERN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ETB": "<PERSON>ti<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.FKP": "Folklando salų svaras", "app.components.screenReaderCurrency.GBP": "Didžiosios Britanijos svaras sterlingų", "app.components.screenReaderCurrency.GEL": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GMD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GNF": "<PERSON><PERSON><PERSON><PERSON> frank<PERSON>", "app.components.screenReaderCurrency.GTQ": "Gvatemalos ketzalas", "app.components.screenReaderCurrency.GYD": "<PERSON><PERSON><PERSON> doleris", "app.components.screenReaderCurrency.HKD": "Honkongo doleris", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "Kroatijos kuna", "app.components.screenReaderCurrency.HTG": "<PERSON><PERSON><PERSON> gur<PERSON>", "app.components.screenReaderCurrency.HUF": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.IDR": "Indonezijos rupija", "app.components.screenReaderCurrency.ILS": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.INR": "Indijos rup<PERSON>", "app.components.screenReaderCurrency.IQD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.IRR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.ISK": "Islandų kalba Króna", "app.components.screenReaderCurrency.JMD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "J<PERSON><PERSON>jos jena", "app.components.screenReaderCurrency.KES": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KGS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KHR": "Kambodžos rieliai", "app.components.screenReaderCurrency.KMF": "Komorų frankas", "app.components.screenReaderCurrency.KPW": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KRW": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "Kaimanų salų doleris", "app.components.screenReaderCurrency.KZT": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.LAK": "Lao Kipas", "app.components.screenReaderCurrency.LBP": "Libano svaras", "app.components.screenReaderCurrency.LKR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LRD": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>", "app.components.screenReaderCurrency.LSL": "Lesotas <PERSON>", "app.components.screenReaderCurrency.LTL": "Lietuvos litai", "app.components.screenReaderCurrency.LVL": "Latvijos latas", "app.components.screenReaderCurrency.LYD": "Libijos din<PERSON>s", "app.components.screenReaderCurrency.MAD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MDL": "Moldovos lėja", "app.components.screenReaderCurrency.MGA": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MKD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MMK": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.screenReaderCurrency.MNT": "Mongolų kalba Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MUR": "<PERSON><PERSON><PERSON><PERSON><PERSON> rup<PERSON>", "app.components.screenReaderCurrency.MVR": "Maldyvų Rufiyaa", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MXN": "Meksikos pesas", "app.components.screenReaderCurrency.MXV": "Meksikos Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malaizijos ringitas", "app.components.screenReaderCurrency.MZN": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NAD": "<PERSON><PERSON><PERSON> do<PERSON>", "app.components.screenReaderCurrency.NGN": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NIO": "Nikaragvos Kordoba", "app.components.screenReaderCurrency.NOK": "Norvegijos krona", "app.components.screenReaderCurrency.NPR": "Nepalo rupija", "app.components.screenReaderCurrency.NZD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.OMR": "<PERSON><PERSON> rialas", "app.components.screenReaderCurrency.PAB": "Panamos Balboa", "app.components.screenReaderCurrency.PEN": "Peru Sol", "app.components.screenReaderCurrency.PGK": "Papua Naujosios Gvin<PERSON>jos kina", "app.components.screenReaderCurrency.PHP": "Filipinų pesas", "app.components.screenReaderCurrency.PKR": "Pakistano rupija", "app.components.screenReaderCurrency.PLN": "Lenkų kalba Złoty", "app.components.screenReaderCurrency.PYG": "Paragvajaus gvaranių kalba", "app.components.screenReaderCurrency.QAR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.RON": "R<PERSON>unijos l<PERSON>", "app.components.screenReaderCurrency.RSD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.RUB": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.RWF": "<PERSON><PERSON><PERSON> frankas", "app.components.screenReaderCurrency.SAR": "<PERSON><PERSON> <PERSON>", "app.components.screenReaderCurrency.SBD": "<PERSON><PERSON><PERSON> do<PERSON>", "app.components.screenReaderCurrency.SCR": "Seišelių rupija", "app.components.screenReaderCurrency.SDG": "<PERSON><PERSON>", "app.components.screenReaderCurrency.SEK": "Š<PERSON><PERSON>jos kron<PERSON>", "app.components.screenReaderCurrency.SGD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SHP": "Šventos<PERSON> s<PERSON> s<PERSON>", "app.components.screenReaderCurrency.SLL": "Siera Leonė Leone", "app.components.screenReaderCurrency.SOS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.SRD": "Surina<PERSON> doleris", "app.components.screenReaderCurrency.SSP": "Pietų Sudano svaras", "app.components.screenReaderCurrency.STD": "San Tomė ir Prinsipė", "app.components.screenReaderCurrency.SYP": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SZL": "Svazijų kalba Lilangeni", "app.components.screenReaderCurrency.THB": "Tai<PERSON>o bahtai", "app.components.screenReaderCurrency.TJS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TMT": "Turkmėnistano manatas", "app.components.screenReaderCurrency.TND": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TOK": "Žetonas", "app.components.screenReaderCurrency.TOP": "Tongų kalba Paʻanga", "app.components.screenReaderCurrency.TRY": "<PERSON><PERSON><PERSON><PERSON> liros", "app.components.screenReaderCurrency.TTD": "Trinidado ir Tobago doleris", "app.components.screenReaderCurrency.TWD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TZS": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.UAH": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "app.components.screenReaderCurrency.UGX": "Ugandos šilingai", "app.components.screenReaderCurrency.USD": "JAV doleris", "app.components.screenReaderCurrency.USN": "JAV doleris (kitą dieną)", "app.components.screenReaderCurrency.USS": "JAV doleris (tą pačią dieną)", "app.components.screenReaderCurrency.UYI": "Urugvajaus peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Urugva<PERSON><PERSON> pesas", "app.components.screenReaderCurrency.UZS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.VEF": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.VND": "Vietnamiečių kalba Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoa Tala", "app.components.screenReaderCurrency.XAF": "Centrinės Afrikos CFA frankas", "app.components.screenReaderCurrency.XAG": "Sidabras (viena Trojos uncija)", "app.components.screenReaderCurrency.XAU": "Auksas (viena Trojos uncija)", "app.components.screenReaderCurrency.XBA": "Europos sudėtinis <PERSON> (EURCO)", "app.components.screenReaderCurrency.XBB": "Europos piniginis vien<PERSON> (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "9 Europos apskaitos vienetas (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "17 Europos apskaitos vienetas (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "Rytų Karibų jūros regiono doleris", "app.components.screenReaderCurrency.XDR": "Specialiosios s<PERSON>linimosi <PERSON>", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "Vakarų Afrikos CFA frankas", "app.components.screenReaderCurrency.XPD": "<PERSON><PERSON><PERSON> (viena Trojos uncija)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "Platina (viena Trojos uncija)", "app.components.screenReaderCurrency.XTS": "Kodai, specialiai rezervuoti testavimo tikslams", "app.components.screenReaderCurrency.XXX": "<PERSON><PERSON><PERSON> vali<PERSON>", "app.components.screenReaderCurrency.YER": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ZAR": "Piet<PERSON> A<PERSON>", "app.components.screenReaderCurrency.ZMW": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.amount": "<PERSON><PERSON>", "app.components.screenReaderCurrency.currency": "Valiuta", "app.components.trendIndicator.lastQuarter2": "<PERSON><PERSON><PERSON><PERSON> ket<PERSON>", "app.containers.AccessibilityStatement.applicability": "<PERSON>is prieinamumo pareiškimas taikomas svetainei {demoPlatformLink} , kuri yra <PERSON> svetain<PERSON> pav<PERSON>; joje naudo<PERSON> tas pats pirminis kodas ir funkcijos.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Vertinimo metodas", "app.containers.AccessibilityStatement.assesmentText2022": "Š<PERSON> svetainės prieinamumą vertino išorinis subjektas, nedaly<PERSON><PERSON>ęs projektavimo ir kūrimo procese. Atitiktį pirmiau minėtiems reikalavimams {demoPlatformLink} galima nustatyti šioje svetainėje {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "galite keisti savo nuostatas", "app.containers.AccessibilityStatement.changePreferencesText": "Bet kuriuo metu {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Atitikties išimtys", "app.containers.AccessibilityStatement.conformanceStatus": "Atitikties statusas", "app.containers.AccessibilityStatement.contentConformanceExceptions": "<PERSON><PERSON><PERSON><PERSON>, kad mūsų turinys būtų pritaikytas visiems. Tačiau kai kuriais atvejais platformoje gali būti nep<PERSON> turi<PERSON>, kaip nurodyta to<PERSON>:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demonstracinė svet<PERSON>", "app.containers.AccessibilityStatement.email": "El. p<PERSON>:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Įterptieji apklausos įrankiai", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Įterptosios a<PERSON><PERSON><PERSON><PERSON>, kuria<PERSON> gal<PERSON> naudoti <PERSON>, yra trečiosios šalies programinė įranga ir gali būti nep<PERSON>.", "app.containers.AccessibilityStatement.exception_1": "<PERSON><PERSON><PERSON><PERSON> skaitmeninės dalyvavimo platformos palengvina asmenų ir organizacijų skelbiamą naudotojų sukurtą turinį. <PERSON><PERSON> b<PERSON><PERSON>, kad platformos naudotojai į platformą įkelia PDF, paveikslėlius ar kitų tipų failus, įskaitant daug<PERSON><PERSON><PERSON><PERSON><PERSON> terpė<PERSON> failus, kaip priedus arba įtraukia juos į teksto laukus. Šie dokumentai gali būti nevisiškai prieinami.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Laukiame jūsų atsiliepimų apie šios svetainės p<PERSON>inamumą. Susisiekite su mumis vienu iš toliau nurodytų būdų:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Grįžtamojo ryšio procesas", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Briuselis, Belgija", "app.containers.AccessibilityStatement.headTitle": "Prieina<PERSON><PERSON> | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} yra įsipareigojusi sukurti platformą, kuri būtų prieinama visiems naudotojams, nepriklausomai nuo jų technologijų ar gebėjimų. Nuolat stengdamiesi užtikrinti kuo didesnį mūsų platformų prieinamumą ir patogumą visiems naudotojams, laikomės galiojančių atitinkamų prieinamumo standartų.", "app.containers.AccessibilityStatement.mapping": "Žemėlapių sudarymas", "app.containers.AccessibilityStatement.mapping_1": "Platformoje esantys žemėlapiai iš dalies atitinka prieinamumo standartus. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ž<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> apimtį, pria<PERSON><PERSON><PERSON><PERSON> ir vartotojo sąsa<PERSON> valdiklius galima valdyti klaviatūra. Administratoriai taip pat gali konfigūruoti žemėlapių sluoksnių stilių pagalbiniame biure arba naudodami \"Esri\" integraciją, kad sukurtų prieinamesnes spalvų paletes ir simboliką. Skirtingų linijų ar poligonų stilių (pvz., brūkšninių linijų) naudojimas taip pat padės atskirti žemėlapio sluoksnius, kai tik įmanoma, ir nors šiuo metu tokio stiliaus negalima konfigūruoti mūsų platformoje, jį galima konfigūruoti naudojant žemėlapius su \"Esri\" integracija.", "app.containers.AccessibilityStatement.mapping_2": "Platformoje esantys žemėlapiai nėra visiškai prieinami, nes juose naudotoja<PERSON>, naudojantiems ekrano s<PERSON>tytuvu<PERSON>, nėra girdimai pateikiami baziniai žemėlapiai, žemėlapių sluoksniai ar duomenų tendencijos. Visiškai prieinamuose žemėlapiuose žemėlapių sluoksniai turėtų būti pateikti garsiai ir aprašytos visos svarbios duomenų tendencijos. Be to, linijų ir poligonų žemėlapių braižymas tyrimuose nėra prieinamas, nes figūrų negalima braižyti klaviatūra. Alternatyvių įvesties metodų šiuo metu nėra dėl techninio sudėtingumo.", "app.containers.AccessibilityStatement.mapping_3": "Kad linijinių ir poligoninių žemėlapių braižymas būtų prieinamesnis, rekomenduojame į apklausos klausimą ar puslapio aprašym<PERSON> įtraukti įžangą ar paaiškinimą, ką žem<PERSON>lap<PERSON> rodo, ir visas susijusias tendencijas. Be to, būtų galima pateikti trumpo ar ilgo atsakymo tekstinį klausimą, kad prireikus respondentai galėtų paprastai apibūdinti savo atsakymą (užuot spragtelėję žemėlapį). Taip pat rekomenduojame įtraukti projekto vadovo kontaktinę informaciją, kad respondentai, negalintys užpildyti žemėlapio klausimo, galėtų paprašyti alternatyvaus atsakymo į klausimą būdo (pvz., vaizdo susitikimo).", "app.containers.AccessibilityStatement.mapping_4": "\"Ideation\" projektams ir pasiūlymams yra galimybė įvestis rodyti žem<PERSON><PERSON> rod<PERSON>, kuris nėra prieina<PERSON>. Tačiau šiems metodams yra alternatyvus įvesties duomenų s<PERSON> rod<PERSON>, kuris yra prieinamas.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Mūsų internetiniuose seminaruose yra ties<PERSON>ginės vaizdo transliacijos komponentas, kuris šiuo metu nepalaiko subtitrų.", "app.containers.AccessibilityStatement.pageDescription": "Pareiškimas d<PERSON> š<PERSON> svetain<PERSON>s p<PERSON>inamumo", "app.containers.AccessibilityStatement.postalAddress": "<PERSON><PERSON><PERSON>:", "app.containers.AccessibilityStatement.publicationDate": "Paskelbimo data", "app.containers.AccessibilityStatement.publicationDate2024": "Šis prieinamumo pareiš<PERSON> paskelbtas 2024 m. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 21 d.", "app.containers.AccessibilityStatement.responsiveness": "Į atsiliepimus siekiame atsa<PERSON>ti per 1-2 darbo dienas.", "app.containers.AccessibilityStatement.statusPageText": "b<PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.technologiesIntro": "<PERSON><PERSON> svetainės prieinamumas priklauso nuo šių technologijų:", "app.containers.AccessibilityStatement.technologiesTitle": "Technologijos", "app.containers.AccessibilityStatement.title": "Prieina<PERSON><PERSON>", "app.containers.AccessibilityStatement.userGeneratedContent": "Vartotojo sukurtas turinys", "app.containers.AccessibilityStatement.workshops": "Seminarai", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Pasirinkite projektą", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Naudodami turinio konstruktorių galėsite naudoti pažangesnes išdėstymo parinktis. Kalbų, kurių turinio kūrimo įrankyje nėra turinio, atveju vietoj jo bus rodomas įprastas projekto aprašymo turinys.", "app.containers.AdminPage.ProjectDescription.linkText": "Redaguoti aprašymą Turinio kūrimo įrankyje", "app.containers.AdminPage.ProjectDescription.saveError": "<PERSON><PERSON><PERSON><PERSON>nt projekto aprašymą kažkas nutiko ne taip.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Aprašymui naudokite \"Content Builder", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Naudodami turinio konstruktorių galėsite naudoti pažangesnes išdėstymo parinktis.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> projekt<PERSON>", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Apklausos pabaiga", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Sukurti išmaniąją grupę", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, atitinkantys visas toliau nurodytas sąlygas, bus automatiškai įtraukti į grupę:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Pateikite bent vieną ta<PERSON>ę", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Kai kurios s<PERSON>lygos yra ne<PERSON>", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Išsaugoti grupę", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Išmaniųjų grupių konfigūravimas nėra dabartinės licenci<PERSON> da<PERSON>. Norėdami sužinoti daugiau apie tai, kre<PERSON><PERSON><PERSON><PERSON><PERSON> į \"GovSuccess\" vadybininką.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Pateikite grupės p<PERSON>", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Patikrinimas jūsų platformoje yra <PERSON>, pašalinkite tikrinimo taisyklę arba kreipkitės į palaikymo komandą.", "app.containers.App.appMetaDescription": "Sveiki atvykę į internetinę dalyvavimo platformą {orgName}. \nSusipažinkite su vietos projektais ir dalyvaukite diskusijose!", "app.containers.App.loading": "<PERSON><PERSON><PERSON><PERSON>...", "app.containers.App.metaTitle1": "Piliečių dalyvavimo platforma | {orgName}", "app.containers.App.skipLinkText": "Pereiti prie pagrindinio turinio", "app.containers.AreaTerms.areaTerm": "sritis", "app.containers.AreaTerms.areasTerm": "sritys", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Paskyra su šiuo el. pašto adresu jau egzistuoja. <PERSON><PERSON><PERSON>, prisijungti šiuo el. pašto adresu ir patvirtinti paskyrą nustatymų puslapyje.", "app.containers.Authentication.steps.AccessDenied.close": "Uždaryti", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Neatitinkate reikalavimų, kad gal<PERSON>tumėte dalyvauti šiame procese.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Grįžkite prie vieno prisijungimo tikrinimo", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Įveskite simbolį", "app.containers.Authentication.steps.Invitation.token": "Žetonas", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Jau turite paskyrą? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Šios kategorijos el. laiškai", "app.containers.CampaignsConsentForm.messageError": "Įrašant jūsų el. pašto nuostatas įvyko klaida.", "app.containers.CampaignsConsentForm.messageSuccess": "Jūsų el. pašto nuosta<PERSON> i<PERSON>.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "<PERSON><PERSON><PERSON> el. pa<PERSON>to p<PERSON> norite gauti? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Pranešimai", "app.containers.CampaignsConsentForm.submit": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.ChangeEmail.backToProfile": "Atgal į profilio nustatymus", "app.containers.ChangeEmail.confirmationModalTitle": "Patvirtinkite savo el. pašto ad<PERSON>", "app.containers.ChangeEmail.emailEmptyError": "Nurodykite el. pašto ad<PERSON>ą", "app.containers.ChangeEmail.emailInvalidError": "Nurodykite el. pašto adresą tinkamu formatu, pavyzdžiui, <EMAIL>.", "app.containers.ChangeEmail.emailRequired": "Įveskite el. pa<PERSON><PERSON> ad<PERSON>.", "app.containers.ChangeEmail.emailTaken": "Šis el. pa<PERSON>to adresas jau naudoja<PERSON>.", "app.containers.ChangeEmail.emailUpdateCancelled": "Atnaujinimas el. paštu atšauktas.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Jei norite atnaujinti el. p<PERSON><PERSON><PERSON>, iš naujo paleiskite procesą.", "app.containers.ChangeEmail.helmetDescription": "Pakeiskite el. pašto puslapį", "app.containers.ChangeEmail.helmetTitle": "Pakeiskite savo el. pašto ad<PERSON>", "app.containers.ChangeEmail.newEmailLabel": "Naujas el. paštas", "app.containers.ChangeEmail.submitButton": "Pat<PERSON><PERSON><PERSON>", "app.containers.ChangeEmail.titleAddEmail": "Pridėkite savo el. pašto ad<PERSON>ą", "app.containers.ChangeEmail.titleChangeEmail": "Pakeiskite savo el. pašto ad<PERSON>", "app.containers.ChangeEmail.updateSuccessful": "Jūsų el. paštas sėkmingai atnaujintas.", "app.containers.ChangePassword.currentPasswordLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.currentPasswordRequired": "Įveskite dabartinį slaptažodį", "app.containers.ChangePassword.goHome": "Eiti į pradžią", "app.containers.ChangePassword.helmetDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> keit<PERSON>", "app.containers.ChangePassword.helmetTitle": "Pakeiskite slaptažodį", "app.containers.ChangePassword.newPasswordLabel": "<PERSON><PERSON><PERSON>", "app.containers.ChangePassword.newPasswordRequired": "Įveskite naują slaptažodį", "app.containers.ChangePassword.password.minimumPasswordLengthError": "pateikti bent {minimumPasswordLength} simbolių ilgio slaptažodį.", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Jū<PERSON>ų <PERSON>žodis sėkmingai atnaujintas", "app.containers.ChangePassword.passwordEmptyError": "Įveskite slaptažodį", "app.containers.ChangePassword.passwordsDontMatch": "<PERSON><PERSON><PERSON><PERSON> nauj<PERSON> slaptažodį", "app.containers.ChangePassword.titleAddPassword": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "app.containers.ChangePassword.titleChangePassword": "Pakeiskite slaptažodį", "app.containers.Comments.a11y_commentDeleted": "Komentaras <PERSON>", "app.containers.Comments.a11y_commentPosted": "Paskelbtas komentaras", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {nepa<PERSON>ka} one {1 patinka} other {# patinka}}", "app.containers.Comments.a11y_undoLike": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.Comments.addCommentError": "<PERSON><PERSON><PERSON> nutiko ne taip. Prašome pabandyti vėliau.", "app.containers.Comments.adminCommentDeletionCancelButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.adminCommentDeletionConfirmButton": "Ištrinti šį komentarą", "app.containers.Comments.cancelCommentEdit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.childCommentBodyPlaceholder": "Parašykite atsakymą...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "Šis komentaras buvo ištrintas.", "app.containers.Comments.commentDeletionCancelButton": "Laikykitės mano komentaro", "app.containers.Comments.commentDeletionConfirmButton": "<PERSON><PERSON><PERSON><PERSON> mano komentar<PERSON>", "app.containers.Comments.commentLike": "<PERSON><PERSON>", "app.containers.Comments.commentReplyButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.commentsSortTitle": "Rūšiuoti komentarus pagal", "app.containers.Comments.completeProfileLinkText": "užpildykite savo profilį", "app.containers.Comments.completeProfileToComment": "Prašome {completeRegistrationLink} komentuoti.", "app.containers.Comments.confirmCommentDeletion": "Ar tikrai norite ištrinti šį komentarą? Nėra kelio atgal!", "app.containers.Comments.deleteComment": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.deleteReasonDescriptionError": "Pateikite daugiau informacijos apie savo priežastį", "app.containers.Comments.deleteReasonError": "Nurodykite priežastį", "app.containers.Comments.deleteReason_inappropriate": "<PERSON>s yra netinkamas arba įžeidžiantis.", "app.containers.Comments.deleteReason_irrelevant": "<PERSON> n<PERSON>ra svarbu", "app.containers.Comments.deleteReason_other": "Kita priežastis", "app.containers.Comments.editComment": "Red<PERSON><PERSON><PERSON>", "app.containers.Comments.guidelinesLinkText": "m<PERSON>sų bendru<PERSON>", "app.containers.Comments.ideaCommentBodyPlaceholder": "Parašykite savo komentarą čia", "app.containers.Comments.internalCommentingNudgeMessage": "Į dabartinę licenciją neįtrauktas vidinių komentarų rašymas. Susisiekite su savo \"GovSuccess\" vadybininku ir sužinokite apie tai daugiau.", "app.containers.Comments.internalConversation": "<PERSON><PERSON><PERSON><PERSON> poka<PERSON>", "app.containers.Comments.loadMoreComments": "Įkelti daugiau komentarų", "app.containers.Comments.loadingComments": "Įkeliami komentarai...", "app.containers.Comments.loadingMoreComments": "Įkeliama daugiau komentarų...", "app.containers.Comments.notVisibleToUsersPlaceholder": "<PERSON>is komenta<PERSON> nėra matomas įprastiems naudotojams", "app.containers.Comments.postInternalComment": "Paskelbti vidaus komentarą", "app.containers.Comments.postPublicComment": "Paskelbti viešą komentarą", "app.containers.Comments.profanityError": "Ups! <PERSON><PERSON><PERSON>, kad jū<PERSON><PERSON> p<PERSON> yra <PERSON>, kuri neat<PERSON> {guidelinesLink}. <PERSON><PERSON><PERSON><PERSON><PERSON>, kad ši erdvė būtų saugi visiems. Prašome redaguoti savo įrašą ir bandyti dar kartą.", "app.containers.Comments.publicDiscussion": "<PERSON><PERSON><PERSON>", "app.containers.Comments.publishComment": "Paskelbkite savo komentarą", "app.containers.Comments.reportAsSpamModalTitle": "Kodėl norite pranešti apie tai kaip apie šlam<PERSON>tą?", "app.containers.Comments.saveComment": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.Comments.signInLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.signInToComment": "Prašome {signInLink} komentuoti.", "app.containers.Comments.signUpLinkText": "užsiregistruoti", "app.containers.Comments.verifyIdentityLinkText": "Patikrinkite savo tapatybę", "app.containers.Comments.visibleToUsersPlaceholder": "Šis komentaras matomas nuolatiniams naudotojams", "app.containers.Comments.visibleToUsersWarning": "Čia paskelbti komentarai bus matomi nuolatiniams naudotojams.", "app.containers.ContentBuilder.PageTitle": "<PERSON>je<PERSON><PERSON>", "app.containers.CookiePolicy.advertisingContent": "Reklaminiai slapukai gali būti naudojami siekiant suasmeninti ir įvertinti išorinių rinkodaros kampanijų veiksmingumą įsitraukiant į šią platformą. Šioje platformoje nerodysime jokios reklamos, tačiau galite gauti personalizuotų reklamų, atsižvelgiant į jūsų lankomus puslapius.", "app.containers.CookiePolicy.advertisingTitle": "<PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.analyticsContents": "\"Analytics\" slapuka<PERSON> stebima lankytoj<PERSON> el<PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuriuose puslapiuose lankomasi ir kiek laiko. Jie taip pat gali rinkti tam tikrus techninius duomenis, įskaitant nar<PERSON><PERSON><PERSON><PERSON>s informaciją, apyti<PERSON><PERSON><PERSON> buvimo vietą ir IP adresus. Šiuos duomenis naudojame tik savo viduje, kad galėtume toliau tobulinti bendrą naudotojų patirtį ir platformos veikimą. Tokiais duomenimis taip pat gali dalytis \"Go Vocal\" ir {orgName} , kad įvertintų ir pagerintų dalyvavimą platformos projektuose. Atkreipkite dėmesį, kad šie duomenys yra anoniminiai ir naudojami apibendrintu lygmeniu - jie neidentifikuoja jūsų asmeniškai. Tačiau gali būti, kad jei šie duomenys būtų sujungti su kitais duomenų šaltiniais, toks identifikavimas galėtų įvykti.", "app.containers.CookiePolicy.analyticsTitle": "\"Analytics\" slap<PERSON>i", "app.containers.CookiePolicy.cookiePolicyDescription": "<PERSON><PERSON><PERSON><PERSON>, ka<PERSON> <PERSON><PERSON><PERSON> naudo<PERSON>s", "app.containers.CookiePolicy.cookiePolicyTitle": "Slapukų politika", "app.containers.CookiePolicy.essentialContent": "Kai kurie slapukai yra būtini tinkamam šios platformos veikimui užtikrinti. Šie esminiai slapukai pirmiausia naudojami jūsų paskyrai autentifikuoti, kai la<PERSON> platformoje, ir jū<PERSON><PERSON> pageidaujamai kalbai išsaugoti.", "app.containers.CookiePolicy.essentialTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.externalContent": "Kai kuriuose mūsų puslapiuose gali būti rodomas išorinių paslaugų teikėjų, pvz., \"YouTube\" arba \"Typeform\", turinys. Mes nekontroliuojame šių trečiųjų šalių slapukų, to<PERSON><PERSON><PERSON>, perž<PERSON><PERSON>rint šių išorės paslaugų teikėjų turinį, jūsų įrenginyje taip pat gali būti įdiegti slapukai.", "app.containers.CookiePolicy.externalTitle": "Išoriniai slap<PERSON>i", "app.containers.CookiePolicy.functionalContents": "Funkciniai slapukai gali būti įjungti, kad lankytojai galėtų gauti pranešimus apie atnaujinimus ir tiesiogiai iš platformos pasiekti pagalbos kanalus.", "app.containers.CookiePolicy.functionalTitle": "Funkciniai slapukai", "app.containers.CookiePolicy.headCookiePolicyTitle": "Slapukų politika | {orgName}", "app.containers.CookiePolicy.intro": "Slapukai - tai te<PERSON><PERSON> fail<PERSON>, saugomi jūsų kompiuterio ar mobiliojo įrenginio naršyklėje arba stand<PERSON> diske, kai lanko<PERSON><PERSON>, ir į kuriuos svetainė gali pateikti nuorodą vėlesnių apsilankymų metu. Slapukus naudojame norėdami suprasti, kaip lankytojai naudojasi šia platforma, kad pagerintume jos dizainą ir patirtį, įsimintume jūs<PERSON> nuostatas (pvz., pageidaujamą kalbą) ir palaikytume pagrindines registruotų naudotojų ir platformos administratorių funkcijas.", "app.containers.CookiePolicy.manageCookiesDescription": "Savo slapukų nustatymuose galite bet kada įjungti arba išjungti analizės, rink<PERSON><PERSON> ir funkcinius slapukus. Taip pat galite rankiniu būdu arba automatiškai ištrinti visus esamus slapukus naudodamiesi savo interneto naršykle. <PERSON><PERSON><PERSON><PERSON>, gavus jū<PERSON>ų sutikimą, slapukai gali būti vėl įrašomi bet kurio vėlesnio apsilankymo šioje platformoje metu. Jei slapukų neištrinsite, jūs<PERSON> slapukų nuostatos bus saugomos 60 dienų, po to jūsų vėl bus paprašyta duoti sutikimą.", "app.containers.CookiePolicy.manageCookiesPreferences": "Eikite į savo {manageCookiesPreferencesButtonText} , kad pamatytumėte visą šioje platformoje naudojamų trečiųjų šalių integracijų sąrašą ir galėtumėte tvarkyti savo nuostatas.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "slapukų nustatymai", "app.containers.CookiePolicy.manageCookiesTitle": "Slapukų tvarkymas", "app.containers.CookiePolicy.viewPreferencesButtonText": "Slapukų nustatymai", "app.containers.CookiePolicy.viewPreferencesText": "Toliau pateiktos slapukų kategorijos gali būti taikomos ne visiems lankytojams ar platformoms; išsamų jums taikomų trečiųjų šalių integracijų sąrašą rasite {viewPreferencesButton} .", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Kam naudojame <PERSON>?", "app.containers.CustomPageShow.editPage": "Redaguoti puslapį", "app.containers.CustomPageShow.goBack": "Grįžti atgal", "app.containers.CustomPageShow.notFound": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>", "app.containers.DisabledAccount.bottomText": "Dar kartą prisijungti galite iš {date}.", "app.containers.DisabledAccount.termsAndConditions": "terminai ir <PERSON>", "app.containers.DisabledAccount.text2": "Jūsų paskyra {orgName} dalyvavimo platformoje buvo laikinai išjungta dėl bendruomenės gairių pažeidimo. Daugiau informacijos apie tai galite rasti {TermsAndConditions}.", "app.containers.DisabledAccount.title": "Jūsų paskyra laikinai išjungta", "app.containers.EventsShow.addToCalendar": "Įtraukti į kalendorių", "app.containers.EventsShow.editEvent": "Redaguoti įvykį", "app.containers.EventsShow.emailSharingBody2": "Dalyvaukite šiame renginyje: {eventTitle}. Daugiau informacijos {eventUrl}.", "app.containers.EventsShow.eventDateTimeIcon": "Įvykio data ir laikas", "app.containers.EventsShow.eventFrom2": "<PERSON><PERSON> \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Grįžti atgal", "app.containers.EventsShow.goToProject": "Eikite į projektą", "app.containers.EventsShow.haveRegistered": "užsiregistravo", "app.containers.EventsShow.icsError": "Klaida atsisiunčiant ICS failą", "app.containers.EventsShow.linkToOnlineEvent": "Nuoroda į internetinį renginį", "app.containers.EventsShow.locationIconAltText": "Vieta", "app.containers.EventsShow.metaTitle": "Renginys: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Susitikimas internetu", "app.containers.EventsShow.onlineLinkIconAltText": "Internetinio susitikimo nuoroda", "app.containers.EventsShow.registered": "registruota", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 registruotojų} one {1 registruotojas} other {# registruotojų}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} registruotojų", "app.containers.EventsShow.registrantsIconAltText": "Registruotojai", "app.containers.EventsShow.socialMediaSharingMessage": "Dalyvaukite šiame reng<PERSON>yje: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# dalyvis} other {# dalyviai}}", "app.containers.EventsViewer.allTime": "<PERSON><PERSON><PERSON>", "app.containers.EventsViewer.date": "Data", "app.containers.EventsViewer.thisMonth2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.EventsViewer.thisWeek2": "Ateinanti savaitė", "app.containers.EventsViewer.today": "Šiandien", "app.containers.IdeaButton.addAContribution": "<PERSON><PERSON><PERSON><PERSON> įnašą", "app.containers.IdeaButton.addAPetition": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAProject": "Pridėti projektą", "app.containers.IdeaButton.addAProposal": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAnInitiative": "Pridėti iniciatyvą", "app.containers.IdeaButton.addAnOption": "<PERSON><PERSON><PERSON><PERSON> parinktį", "app.containers.IdeaButton.postingDisabled": "Naujos paraiškos šiuo metu nepriimamos", "app.containers.IdeaButton.postingInNonActivePhases": "Naujas paraiškas galima pridėti tik aktyviais etapais.", "app.containers.IdeaButton.postingInactive": "Šiuo metu naujos paraiškos nepriimamos.", "app.containers.IdeaButton.postingLimitedMaxReached": "<PERSON><PERSON><PERSON> jau atlikote šią apklausą. Ačiū už atsakymą!", "app.containers.IdeaButton.postingNoPermission": "Naujos paraiškos šiuo metu nepriimamos", "app.containers.IdeaButton.postingNotYetPossible": "<PERSON><PERSON>jos <PERSON>š<PERSON> dar nepriimamos.", "app.containers.IdeaButton.signInLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.signUpLinkText": "užsiregistruoti", "app.containers.IdeaButton.submitAnIssue": "Pateikti komentarą", "app.containers.IdeaButton.submitYourIdea": "Pateikite savo idėją", "app.containers.IdeaButton.takeTheSurvey": "Atlikite apklausą", "app.containers.IdeaButton.verificationLinkText": "Dabar patikrinkite savo tapatybę.", "app.containers.IdeaCard.readMore": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {be komentarų} one {1 komentaras} other {# komentarai}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {nėra balsų} one {1 balsas} other {# balsų}} iš {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Uždaryti filtrų skydelį", "app.containers.IdeaCards.a11y_totalItems": "<PERSON><PERSON> viso p<PERSON>: {ideasCount}", "app.containers.IdeaCards.all": "Visi", "app.containers.IdeaCards.allStatuses": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.contributions": "Įnašai", "app.containers.IdeaCards.ideaTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.initiatives": "Iniciatyvos", "app.containers.IdeaCards.issueTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.map": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.mostDiscussed": "Daugiausia diskusijų", "app.containers.IdeaCards.newest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.noFilteredResults": "Rezultatų nerasta. Pabandykite naudoti kitą filtrą arba paieškos žodį.", "app.containers.IdeaCards.numberResults": "Rezultatai ({postCount})", "app.containers.IdeaCards.oldest": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.optionTerm": "Parinktys", "app.containers.IdeaCards.petitions": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.popular": "Daugiausia balsų", "app.containers.IdeaCards.projectFilterTitle": "Projektai", "app.containers.IdeaCards.projectTerm": "Projektai", "app.containers.IdeaCards.proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "Atsitiktinis", "app.containers.IdeaCards.resetFilters": "<PERSON><PERSON> na<PERSON>jo nustatyti filtrus", "app.containers.IdeaCards.showXResults": "Rod<PERSON>i {ideasCount, plural, one {# rezultatas} other {# rezultatai}}", "app.containers.IdeaCards.sortTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.statusTitle": "Statusas", "app.containers.IdeaCards.statusesTitle": "Statusas", "app.containers.IdeaCards.topics": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.topicsTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.trending": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.tryDifferentFilters": "Rezultatų nerasta. Pabandykite naudoti kitą filtrą arba paieškos žodį.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} komentarai} one {{ideasCount} komentaras} other {~{ideasCount} komentarai}}.", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} įnašai} one {{ideasCount} įnašas} other {~{ideasCount} įnašai}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} idėjos} one {{ideasCount} idėja} other {~{ideasCount} idėjos}}.", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} iniciatyvos} one {{ideasCount} iniciatyva} other {~{ideasCount} iniciatyvos}}.", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} parinktys} one {{ideasCount} parinktis} other {~{ideasCount} parinktys}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} peticijos} one {{ideasCount} peticija} other {~{ideasCount} peticijos}}.", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projektai} one {{ideasCount} projektas} other {~{ideasCount} projektai}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} pasiū<PERSON><PERSON>i} one {{ideasCount} pasiūly<PERSON>} other {~{ideasCount} pasiūlymai}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} klausimai} one {{ideasCount} klausimas} other {~{ideasCount} klausimai}}.", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# rezultatas} other {# rezultatai}}", "app.containers.IdeasEditPage.contributionFormTitle": "Redaguoti indėlį", "app.containers.IdeasEditPage.editedPostSave": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.IdeasEditPage.fileUploadError": "Nepavyko įkelti vieno ar daugiau failų. Patikrinkite failo dydį ir formatą ir bandykite dar kartą.", "app.containers.IdeasEditPage.formTitle": "Redaguoti idėją", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Redaguoti savo pranešimą. Pridėkite naują ir pakeiskite seną informaciją.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Redaguoti {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Redaguoti iniciatyvą", "app.containers.IdeasEditPage.issueFormTitle": "Redaguoti komentarą", "app.containers.IdeasEditPage.optionFormTitle": "Redaguoti parinktį", "app.containers.IdeasEditPage.petitionFormTitle": "Redaguoti peticiją", "app.containers.IdeasEditPage.projectFormTitle": "Redaguoti projektą", "app.containers.IdeasEditPage.proposalFormTitle": "Redaguoti p<PERSON>ū<PERSON>ą", "app.containers.IdeasEditPage.questionFormTitle": "Redaguot<PERSON> k<PERSON>", "app.containers.IdeasEditPage.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.IdeasEditPage.submitApiError": "<PERSON><PERSON> problema pateiki<PERSON> formą. <PERSON><PERSON><PERSON><PERSON><PERSON>, ar <PERSON><PERSON><PERSON>, ir bandykite dar kartą.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Visi įvesties duomenys p<PERSON>i", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Išnagrinėkite visą dalyvavimo platformoje {orgName}paskelbtą informaciją.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Pranešimai | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Pranešimai", "app.containers.IdeasIndexPage.loadMore": "Įkelti daugiau...", "app.containers.IdeasIndexPage.loading": "<PERSON><PERSON><PERSON><PERSON>...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "<PERSON><PERSON> numatytu<PERSON><PERSON> nustatymus jūsų pateikimai bus susieti su jūsų profiliu, nebent pasirinksite šią parinktį.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Rašyti anonimiškai", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Šiuo metu į šią apklausą atsakymų negaunama. Norėdami gauti daugiau informacijos, grįžkite į projektą.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Ši apklausa šiuo metu n<PERSON>ra aktyvi.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Grįžti į projektą", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "<PERSON><PERSON><PERSON> jau atlikote šią apkla<PERSON>.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Pateikta apklausa", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Ačiū už atsakymą!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Įnašo apraš<PERSON>as turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Idėjos tekstas turi būti ilges<PERSON> nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Įnašo pavadinimas turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "Įnašo pavadinimas turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Prašome pasirinkti bent vieną rėmėją", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Pateikite a<PERSON>šymą", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "<PERSON><PERSON><PERSON><PERSON> pavadinimas turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON> pavadin<PERSON>s turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "Iniciatyvos aprašymas turi būti trumpesnis nei {limit} ženklų.", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "Iniciatyvos aprašymas turi būti ilgesnis nei {limit} ženklų.", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "Iniciatyvos pavadinimas turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "Iniciatyvos pavadinimas turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Problemos aprašymas turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Problemos aprašymas turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Numerio pavadinimas turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "Numerio pavadinimas turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_number_required": "<PERSON><PERSON> la<PERSON> yra p<PERSON>, įveskite galiojantį numerį", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Pasirinkties aprašas turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "Pasirinkties aprašymas turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Pasirinkties pavadinimas turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Pasirinkties pavadinimas turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Pasirinkite bent vieną <PERSON>", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "Pet<PERSON>jos a<PERSON>š<PERSON> turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "Peticijos aprašymas turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "Peticijos pavadinimas turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "Peticijos pavadinimas turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Projekto aprašymas turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Projekto aprašymas turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Projekto pavadinimas turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Projekto pavadinimas turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> turi būti trumpesnis nei {limit} ženklų.", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> turi būti ilgesnis nei {limit} ženklų.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> pavadinimas turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "Pa<PERSON><PERSON><PERSON><PERSON> pavadin<PERSON>s turi būti ilgesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Įveskite skaičių", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Įveskite skaičių", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "<PERSON><PERSON> turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "<PERSON><PERSON> turi būti ilges<PERSON> nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "<PERSON><PERSON> p<PERSON> turi būti trumpesnis nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "<PERSON><PERSON> turi būti ilges<PERSON> nei {limit} simbolių.", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Nurodykite pavadinimą", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Įnašo aprašymas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Įnašo apra<PERSON>s turi būti ne trumpesnis kaip 30 simbolių.", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Įnašo pavadinimas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Įnašo pavadinimas turi būti ne trumpesnis kaip 10 simbolių.", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>š<PERSON> turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> turi būti ne trumpesnis kaip 30 simbolių.", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Nurodykite pavadinimą", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON> pavadinimas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "<PERSON><PERSON><PERSON><PERSON> pavadinimas turi būti ne trumpesnis kaip 10 simbolių.", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Galbūt pavartojote vieną ar daugiau žodži<PERSON>, kurie {guidelinesLink}laikomi keiksma<PERSON>ž<PERSON>. Pakeiskite savo tekstą, kad pa<PERSON>lintumėte visus galimus keiks<PERSON>.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "Iniciatyvos aprašymas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "Iniciatyvos aprašymas turi būti ne trumpesnis kaip 30 simbolių.", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Iniciatyvos pavadinimas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Iniciatyvos pavadinimas turi būti ne trumpesnis kaip 10 simbolių.", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Problemos aprašymas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Problemos aprašas turi būti ne trumpesnis kaip 30 simbolių.", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Numerio pavadinimas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Numerio pavadinimas turi būti ne trumpesnis kaip 10 simbolių.", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Pasirinkties aprašymas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Pasirinkties aprašas turi būti ne trumpesnis kaip 30 simbolių.", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Pasirinkties pavadinimas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Pasirinkties pavadinimas turi būti ne trumpesnis kaip 10 simbolių.", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "Peticijos aprašymas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "Peticijos aprašymas turi būti ne trumpesnis kaip 30 simbolių.", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Peticijos pavadinimas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Peticijos pavadinimas turi būti ne trumpesnis kaip 10 simbolių.", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Projekto aprašymas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Projekto aprašymas turi būti ne trumpesnis kaip 30 simbolių.", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Projekto pavadinimas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Projekto pavadinimas turi būti ne trumpesnis kaip 10 simbolių.", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON>š<PERSON> turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "<PERSON><PERSON><PERSON><PERSON><PERSON>š<PERSON> turi būti ne trumpesnis kaip 30 simbolių.", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Pasiū<PERSON><PERSON> pavadinimas turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Pa<PERSON><PERSON><PERSON><PERSON> pavadinimas turi būti ne trumpesnis kaip 10 simbolių.", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Pateikite a<PERSON>šymą", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "<PERSON><PERSON> turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "<PERSON><PERSON> turi būti ne trumpesnis kaip 30 simbolių.", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "<PERSON><PERSON> p<PERSON> turi būti trumpesnis nei 80 simbolių.", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "<PERSON><PERSON> turi būti ne trumpesnis kaip 10 simbolių.", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON>, nor<PERSON>i", "app.containers.IdeasNewPage.contributionMetaTitle1": "<PERSON><PERSON><PERSON><PERSON> naują indėlį į projektą | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Redaguoti a<PERSON>ą", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Skelbkite paraišką ir įsitraukite į pokalbį {orgName}dalyvavimo platformoje.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Įtraukti naują idėją į projektą | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Naujos iniciatyvos įtraukimas į projektą | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "<PERSON><PERSON><PERSON> klausimo įtraukimas į projektą | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Ar tikrai norite išvykti?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "<PERSON><PERSON><PERSON><PERSON> atsakymų juodraštis buvo išsaugotas privačiai ir galėsite grįžti jį užbaigti vėliau.", "app.containers.IdeasNewPage.leaveSurvey": "Apklausa dėl atostogų", "app.containers.IdeasNewPage.leaveSurveyText": "Jūsų atsakymai nebus išsaugoti.", "app.containers.IdeasNewPage.optionMetaTitle1": "Naujos parinkties įtraukimas į projektą | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Naujos peticijos įtraukimas į projektą | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Naujo projekto įtraukimas į projektą | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "<PERSON><PERSON><PERSON> įtraukimas į projektą | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "<PERSON><PERSON><PERSON> klausimo įtraukimas į projektą | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Kvietimas dalyvauti kartu su kitais rėmėjais", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Bend<PERSON>aut<PERSON><PERSON>", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Kviečiame tapti vienu iš rė<PERSON>ėjų.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Priimtas kvietimas", "app.containers.IdeasShow.Cosponsorship.pending": "lauki<PERSON>", "app.containers.IdeasShow.MetaInformation.attachments": "Pried<PERSON>", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} s<PERSON><PERSON><PERSON><PERSON> {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "<PERSON><PERSON><PERSON><PERSON> bū<PERSON>", "app.containers.IdeasShow.MetaInformation.location": "Vieta", "app.containers.IdeasShow.MetaInformation.postedBy": "Paskelbė", "app.containers.IdeasShow.MetaInformation.similar": "Panašūs įvesties duomenys", "app.containers.IdeasShow.MetaInformation.topics": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.commentCTA": "Pridėti komentarą", "app.containers.IdeasShow.contributionEmailSharingBody": "Palaikykite šį įnašą '{postTitle}' adresu {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Palaikykite šį įnašą: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON>, kad pat<PERSON>te savo indėlį!", "app.containers.IdeasShow.contributionTwitterMessage": "Palaikykite šį įnašą: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Palaikykite šį įnašą: {postTitle}", "app.containers.IdeasShow.currentStatus": "<PERSON><PERSON><PERSON><PERSON> bū<PERSON>", "app.containers.IdeasShow.deletedUser": "nežinomas autorius", "app.containers.IdeasShow.ideaEmailSharingBody": "Palaikykite mano id<PERSON> '{ideaTitle}' adresu {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Palaikykite mano id<PERSON>: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Palaikykite šią id<PERSON>ją: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Palaikykite šią id<PERSON>ją: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Pritarti šiam komentarui: {postTitle}", "app.containers.IdeasShow.imported": "I<PERSON>rt<PERSON><PERSON>", "app.containers.IdeasShow.importedTooltip": "Šis {inputTerm} buvo surinktas neprisijungus prie interneto ir automatiškai įkeltas į platformą.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Palaikykite šią iniciatyvą '{ideaTitle}' adresu {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Palaikykite šią iniciatyvą: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON>, kad pateik<PERSON>te iniciatyvą!", "app.containers.IdeasShow.initiativeTwitterMessage": "Palaikykite šią iniciatyvą: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Palaikykite šią iniciatyvą: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Palaikykite šį komentarą '{postTitle}' adresu {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Pritarti šiam komentarui: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON>, kad pateik<PERSON>te komentarą!", "app.containers.IdeasShow.issueTwitterMessage": "Pritarti šiam komentarui: {postTitle}", "app.containers.IdeasShow.metaTitle": "Įvestis: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Palaikykite šią parinktį '{postTitle}' adresu {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Palaikykite šią parinktį: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON> parink<PERSON> buvo sėkmingai paskelbta!", "app.containers.IdeasShow.optionTwitterMessage": "Palaikykite šią parinktį: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Palaikykite šią parinktį: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Palaikykite šią peticiją '{ideaTitle}' adresu {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Palaikykite šią peticiją: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON>, kad pateik<PERSON>te peticiją!", "app.containers.IdeasShow.petitionTwitterMessage": "Palaikykite šią peticiją: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Palaikykite šią peticiją: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Palaikykite šį projektą '{postTitle}' adresu {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Paremkite šį projektą: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON>, kad pateik<PERSON>te projektą!", "app.containers.IdeasShow.projectTwitterMessage": "Paremkite šį projektą: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Paremkite šį projektą: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Pritarkite šiam p<PERSON> '{ideaTitle}' adresu {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Pritarti šiam p<PERSON>: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad patei<PERSON> p<PERSON>ūly<PERSON>ą!", "app.containers.IdeasShow.proposalTwitterMessage": "Pritarti šiam p<PERSON>: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Pritarti šiam p<PERSON>: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "<PERSON><PERSON> laiko balsuoti:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} iš {votingThreshold} reikiamų balsų", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "app.containers.IdeasShow.proposals.VoteControl.days": "dienos", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "mūsų gairės", "app.containers.IdeasShow.proposals.VoteControl.hours": "valandos", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "<PERSON><PERSON> ir balsai", "app.containers.IdeasShow.proposals.VoteControl.minutes": "minučių", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Daugiau informacijos", "app.containers.IdeasShow.proposals.VoteControl.vote": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.voted": "Balsavo", "app.containers.IdeasShow.proposals.VoteControl.votedText": "Jums bus p<PERSON>, kai ši iniciatyva pereis į kitą etapą. {x, plural, =0 {Liko {xDays} .} one {Liko {xDays} .} other {Yra {xDays} palikta.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "<PERSON><PERSON>sų balsas jau atiduotas!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "<PERSON><PERSON>, d<PERSON><PERSON> p<PERSON> balsuoti negalite. Ko<PERSON><PERSON><PERSON> taip yra, skaitykite {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {mažiau nei dieną} one {vieną dieną} other {# dienų}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {nėra balsų} one {1 balsas} other {# balsų}}", "app.containers.IdeasShow.questionEmailSharingBody": "Prisijunkite prie diskusijos apie šį klausimą '{postTitle}' adresu {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Prisijunkite prie diskusijos: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "<PERSON><PERSON><PERSON>ų klausimas buvo sėkmingai paskelbtas!", "app.containers.IdeasShow.questionTwitterMessage": "Prisijunkite prie diskusijos: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Prisijunkite prie diskusijos: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Kodėl norite pranešti apie tai kaip apie šlam<PERSON>tą?", "app.containers.IdeasShow.share": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.sharingModalSubtitle": "Kreipkitės į daugiau žmonių ir išsakykite savo nuomonę.", "app.containers.IdeasShow.sharingModalTitle": "<PERSON><PERSON><PERSON><PERSON>, kad pateik<PERSON>te savo idėją!", "app.containers.Navbar.completeOnboarding": "Užbaigti įdarbinimą", "app.containers.Navbar.completeProfile": "<PERSON><PERSON><PERSON> profilis", "app.containers.Navbar.confirmEmail2": "Patvirtinkite el. paštą", "app.containers.Navbar.unverified": "Nepatikrintas", "app.containers.Navbar.verified": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.beforeYouFollow": "<PERSON><PERSON>š pradėdami sekti", "app.containers.NewAuthModal.beforeYouParticipate": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.completeYourProfile": "Užpildykite savo profilį", "app.containers.NewAuthModal.confirmYourEmail": "Patvirtinkite savo el. pašto ad<PERSON>", "app.containers.NewAuthModal.logIn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toliau nurodytas sąlygas.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Užpildykite savo profilį.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Grįžkite į prisijun<PERSON><PERSON> parinktis", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Neturite paskyros? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Užsiregistruokite", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Kodą turi sudaryti 4 skaitmenys.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Tęsti su FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Šioje platformoje neįjungti jokie autentifikavimo metodai.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "Tęsdami sutinkate gauti el. laiškus iš šios platformos. Puslapyje \"Mano nustatymai\" galite pasirinkti, kokius el. laiškus norite gauti.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "El. <PERSON>", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Nurodykite el. pašto adresą tinkamu formatu, pavyzdžiui, <EMAIL>.", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Nurodykite el. pašto ad<PERSON>ą", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "<PERSON><PERSON><PERSON><PERSON>, įveskite savo el. pašto ad<PERSON>.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Pamiršote slaptažodį?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Prisijunkite prie savo paskyros: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Įveskite savo slaptažodį", "app.containers.NewAuthModal.steps.Password.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.Password.rememberMe": "<PERSON><PERSON><PERSON><PERSON> mane", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jei na<PERSON> vieš<PERSON> kompi<PERSON>", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "<PERSON><PERSON> da<PERSON>.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Jūsų tapatybė buvo patikrinta. Dabar esate visateisis šios platformos bendruomenės nary<PERSON>.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "<PERSON>bar esate patvirtin<PERSON> !", "app.containers.NewAuthModal.steps.close": "Uždaryti", "app.containers.NewAuthModal.steps.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Kas jus domina?", "app.containers.NewAuthModal.youCantParticipate": "Negalite <PERSON>", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {nėra neperžiūrėtų pranešimų} one {1 neperžiūrėtas pranešimas} other {# neperžiūr<PERSON>ti pranešimai}}", "app.containers.NotificationMenu.adminRightsReceived": "Dabar esate platformos administratorius", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "<PERSON><PERSON><PERSON><PERSON> komentarą \"{postTitle}\" i<PERSON><PERSON><PERSON><PERSON>, nes\n      {reasonCode, select, irrelevant {jis yra nesvarbus} inappropriate {jo turinys yra netinkama<PERSON>} other {~{otherReason}~}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} p<PERSON>ė<PERSON>ė jūsų kvietimą tapti vienu iš rė<PERSON>", "app.containers.NotificationMenu.deletedUser": "Nežinomas autorius", "app.containers.NotificationMenu.error": "Nepavyko įkelti pranešimų", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} vidiniai komentarai apie jums priskirtą įvestį.", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} viduje komentavote įvestį, kurią komentavote viduje.", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} savo viduje komentavote įvesties duomenis jūsų valdomame projekte.", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} vidiniai komentarai apie nepaskirtą įvestį nevaldomame projekte.", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} pakomentavo jūsų vidinį komentarą", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} pakvietė jus prisidėti prie įnašo", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} pakvietė prisidėti prie idėjos rėmimo", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} pakvietė jus tapti iniciatyvos bendra rėmėjais", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} pakvietė jus tapti vienu iš klaus<PERSON>", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} pakvietė jus kartu su kitais rėmė<PERSON>is", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} pakvietė jus tapti peticijos bendraautoriais", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} pakvietė jus tapti vienu iš projekto rėmėjų.", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} pakvietė jus tapti vienu iš pasi<PERSON><PERSON> r<PERSON>.", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} pakvietė jus kartu su kitais autoriais pateikti klausimą", "app.containers.NotificationMenu.loadMore": "Įkelti daugiau...", "app.containers.NotificationMenu.loading": "Įkeliami pranešimai...", "app.containers.NotificationMenu.mentionInComment": "{name} pami<PERSON><PERSON><PERSON> jus komentare", "app.containers.NotificationMenu.mentionInInternalComment": "{name} pamin<PERSON><PERSON> jus vidiniame komentare", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} pamin<PERSON><PERSON> jus oficialiame atnaujinime", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Nepateikėte apklausos", "app.containers.NotificationMenu.noNotifications": "<PERSON><PERSON><PERSON> dar neturite joki<PERSON> p<PERSON>", "app.containers.NotificationMenu.notificationsLabel": "Pranešimai", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} oficialiai atnaujino informaciją apie įnašą, kurį sekate", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} pateikė oficialų atnaujintą informaciją apie idėją, kurią sekate.", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} pateikė oficialų atnaujintą informaciją apie iniciatyvą, kurią sekate.", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} pat<PERSON><PERSON><PERSON> oficialią atnaujintą informaciją apie klausim<PERSON>, kurį sekate.", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} pateikė oficialų atnaujinimą d<PERSON>, k<PERSON><PERSON> se<PERSON>e", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} pateikė oficialų atnaujintą informaciją apie peticiją, kurią sekate", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} pat<PERSON><PERSON><PERSON> oficialų projekto, kurį sekate, atna<PERSON>jinimą.", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} pat<PERSON><PERSON><PERSON> oficialų atnaujintą informaciją apie p<PERSON>ū<PERSON>, kurį sekate.", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} pat<PERSON><PERSON><PERSON> oficialų atnaujintą informaciją apie klausimą, kurį sekate", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} jums buvo priskirtas", "app.containers.NotificationMenu.projectModerationRightsReceived": "Dabar esate {projectLink}projekto vadovas.", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} prasidėjo naujas etapas.", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} prad<PERSON><PERSON> nauj<PERSON> etapą svetain<PERSON>je {phaseStartAt}.", "app.containers.NotificationMenu.projectPublished": "Paskelbtas naujas projektas", "app.containers.NotificationMenu.projectReviewRequest": "{name} p<PERSON><PERSON><PERSON><PERSON> leidimo skelbti projektą \"{projectTitle}\".", "app.containers.NotificationMenu.projectReviewStateChange": "{name} pat<PERSON><PERSON><PERSON> \"{projectTitle}\" skelbti", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} bū<PERSON>a pakeista į {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} p<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} p<PERSON>ė<PERSON>ė jū<PERSON>ų kvietimą", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} pakomentuoti jū<PERSON> seka<PERSON>ą įrašą", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} pakomenta<PERSON>ėją, k<PERSON><PERSON>s", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} pakomentavo iniciatyvą, kurią sekate.", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} pakoment<PERSON><PERSON>, kurį sekate.", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} pakomentavo parinktį, k<PERSON><PERSON>s", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} pakomentuo<PERSON> pet<PERSON>, k<PERSON><PERSON> sekate.", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} pakoment<PERSON><PERSON> proje<PERSON>, kurį sekate.", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} pakoment<PERSON><PERSON>, k<PERSON><PERSON>.", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} pakomentavo klausimą, kurį sekate", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} p<PERSON><PERSON><PERSON> \"{postTitle}\" ka<PERSON>", "app.containers.NotificationMenu.userReactedToYourComment": "{name} reagavo į jūsų komentarą", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} p<PERSON><PERSON><PERSON> apie komentarą \"{postTitle}\" kaip <PERSON>", "app.containers.NotificationMenu.votingBasketNotSubmitted": "<PERSON><PERSON><PERSON> savo balsų", "app.containers.NotificationMenu.votingBasketSubmitted": "Sėkmingai balsavote", "app.containers.NotificationMenu.votingLastChance": "Paskutinė gal<PERSON> balsuoti už {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} atskleisti balsavimo rezultatai", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} jums priskirta {postTitle}", "app.containers.PasswordRecovery.emailError": "Tai neatrodo kaip <PERSON> el. pa<PERSON>", "app.containers.PasswordRecovery.emailLabel": "El. <PERSON>", "app.containers.PasswordRecovery.emailPlaceholder": "Mano el. pa<PERSON><PERSON> ad<PERSON>as", "app.containers.PasswordRecovery.helmetDescription": "Slap<PERSON>žod<PERSON><PERSON> atstatymo pu<PERSON>", "app.containers.PasswordRecovery.helmetTitle": "<PERSON><PERSON> na<PERSON> nustat<PERSON>i slaptažodį", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Jei šis el. pašto adresas užregistruotas platformoje, buvo išsiųsta slaptažodžio atstatymo nuoroda.", "app.containers.PasswordRecovery.resetPassword": "Siųsti slaptažodžio atstatymo nuorodą", "app.containers.PasswordRecovery.submitError": "Nepavyko rasti su šiuo el. paštu susietos paskyros. Vietoj to galite pabandyti užsiregistruoti.", "app.containers.PasswordRecovery.subtitle": "Kur galima nusiųsti nuorodą, kad būtų galima pasirinkti naują slaptažodį?", "app.containers.PasswordRecovery.title": "Slap<PERSON>ž<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.helmetDescription": "Slap<PERSON>žod<PERSON><PERSON> atstatymo pu<PERSON>", "app.containers.PasswordReset.helmetTitle": "<PERSON><PERSON> na<PERSON> nustat<PERSON>i slaptažodį", "app.containers.PasswordReset.login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordError": "Slap<PERSON>žodis turi būti ne trumpesnis kaip 8 simbolių.", "app.containers.PasswordReset.passwordLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "<PERSON><PERSON><PERSON>ų slap<PERSON>žodis sėkmingai atnaujintas.", "app.containers.PasswordReset.pleaseLogInMessage": "Prisijunkite naudodami naują slaptažodį.", "app.containers.PasswordReset.requestNewPasswordReset": "<PERSON><PERSON><PERSON><PERSON><PERSON> iš naujo nustatyti slaptažodį", "app.containers.PasswordReset.submitError": "<PERSON><PERSON><PERSON> nutiko ne taip. Prašome pabandyti vėliau.", "app.containers.PasswordReset.title": "<PERSON><PERSON> na<PERSON> nustat<PERSON>i slaptažodį", "app.containers.PasswordReset.updatePassword": "<PERSON><PERSON><PERSON><PERSON> nauj<PERSON> slaptažodį", "app.containers.ProjectFolderCards.allProjects": "Visi projektai", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} š<PERSON>o metu dirba", "app.containers.ProjectFolderShowPage.editFolder": "Redaguoti aplanką", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informacija apie šį projektą", "app.containers.ProjectFolderShowPage.metaTitle1": "Aplankas: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectFolderShowPage.share": "<PERSON><PERSON><PERSON>", "app.containers.Projects.PollForm.document": "Dokumentas", "app.containers.Projects.PollForm.formCompleted": "Ačiū! Jūsų atsakymas gautas.", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "<PERSON><PERSON><PERSON> j<PERSON> da<PERSON>te šioje apklausoje.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "<PERSON><PERSON><PERSON> a<PERSON><PERSON> galima atlikti tik tada, kai šis etapas yra aktyvu<PERSON>.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Ši apklausa šiuo metu nėra įjungta", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Šiuo metu šios apklausos atlikti neįmanoma.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Ap<PERSON><PERSON> ne<PERSON>, nes šis projektas nebėra aktyvu<PERSON>.", "app.containers.Projects.PollForm.sendAnswer": "Si<PERSON>sti", "app.containers.Projects.a11y_phase": "Fazė {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Etapų apžvalga", "app.containers.Projects.a11y_titleInputs": "Visa šiam projektui pateikta informacija", "app.containers.Projects.a11y_titleInputsPhase": "Visa šiame etape pateikta informacija", "app.containers.Projects.accessRights": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.addedToBasket": "Pridėta į krepšelį", "app.containers.Projects.allocateBudget": "Skirkite biudžetą", "app.containers.Projects.archived": "Archyvuota", "app.containers.Projects.basketSubmitted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>!", "app.containers.Projects.contributions": "Įnašai", "app.containers.Projects.createANewPhase": "Sukurti naują etapą", "app.containers.Projects.currentPhase": "<PERSON><PERSON><PERSON><PERSON> etapas", "app.containers.Projects.document": "Dokumentas", "app.containers.Projects.editProject": "Redaguoti projektą", "app.containers.Projects.emailSharingBody": "Ką manote apie šią iniciatyvą? Balsuokite už ją ir dalinkitės diskusija svetainėje {initiativeUrl} , kad jūsų balsas būtų išgirstas!", "app.containers.Projects.emailSharingSubject": "Paremkite mano iniciatyvą: {initiativeTitle}.", "app.containers.Projects.endedOn": "Pasi<PERSON>ig<PERSON> {date}", "app.containers.Projects.events": "Rengin<PERSON><PERSON>", "app.containers.Projects.header": "Projektai", "app.containers.Projects.ideas": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.information": "Informacija", "app.containers.Projects.initiatives": "Iniciatyvos", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "<PERSON><PERSON><PERSON>", "app.containers.Projects.invisibleTitlePhaseAbout": "Apie šį etapą", "app.containers.Projects.invisibleTitlePoll": "Atlikite apklausą", "app.containers.Projects.invisibleTitleSurvey": "Atlikite apklausą", "app.containers.Projects.issues": "Komentarai", "app.containers.Projects.liveDataMessage": "Peržiūrite realaus laiko duomenis. Dalyvių skaičius administratoriams nuolat atnaujinamas. Atkreipkite dėmesį, kad įprasti naudotojai mato spartina<PERSON> duomenis, to<PERSON><PERSON><PERSON> skaičiai gali šiek tiek skirt<PERSON>.", "app.containers.Projects.location": "Vieta:", "app.containers.Projects.manageBasket": "<PERSON><PERSON><PERSON><PERSON> krepšelį", "app.containers.Projects.meetMinBudgetRequirement": "Įvykdykite minimalų biud<PERSON>, kad gal<PERSON>tumėte pateikti krepšelį.", "app.containers.Projects.meetMinSelectionRequirement": "Įvykdykite minimalų biud<PERSON>, kad gal<PERSON>tumėte pateikti krepšelį.", "app.containers.Projects.metaTitle1": "Projektas: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "<PERSON><PERSON><PERSON> re<PERSON>", "app.containers.Projects.myBasket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.navPoll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.navSurvey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.newPhase": "Naujas etapas", "app.containers.Projects.nextPhase": "Kitas etapas", "app.containers.Projects.noEndDate": "Galutinė data nenurodyta", "app.containers.Projects.noItems": "Dar nepasirinkote jokių elementų", "app.containers.Projects.noPastEvents": "Nėra rodomų praeities įvykių", "app.containers.Projects.noPhaseSelected": "<PERSON><PERSON><PERSON>", "app.containers.Projects.noUpcomingOrOngoingEvents": "Šiuo metu neplanuojama jokių būsimų ar vykstančių renginių.", "app.containers.Projects.offlineVotersTooltip": "Šiame skaičiuje neatsispindi jokie balsavusiųjų ne internetu skaičiavimai.", "app.containers.Projects.options": "Parinktys", "app.containers.Projects.participants": "Dalyviai", "app.containers.Projects.participantsTooltip4": "Šiame skaičiuje taip pat atsispindi anoniminės apklausos duomenys. Anoniminių apklausų pateikimas galima<PERSON>, jei apklausos yra atviros visiems (žr. <PERSON><PERSON> projekto skirtuką {accessRightsLink}).", "app.containers.Projects.pastEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.petitions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.phases": "Fazė<PERSON>", "app.containers.Projects.previousPhase": "Anks<PERSON><PERSON> etapa<PERSON>", "app.containers.Projects.project": "Projektas", "app.containers.Projects.projectTwitterMessage": "Išgirskite savo balsą! Dalyvaukite {projectName} | {orgName}", "app.containers.Projects.projects": "Projektai", "app.containers.Projects.proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.readMore": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.removeItem": "Pašalinti elementą", "app.containers.Projects.requiredSelection": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.reviewDocument": "<PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheContributions": "Ž<PERSON>. indėlį", "app.containers.Projects.seeTheIdeas": "Peržiūrėkite idė<PERSON>", "app.containers.Projects.seeTheInitiatives": "<PERSON><PERSON><PERSON> in<PERSON>", "app.containers.Projects.seeTheIssues": "<PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> par<PERSON>", "app.containers.Projects.seeThePetitions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheProjects": "<PERSON><PERSON><PERSON> proje<PERSON>", "app.containers.Projects.seeTheProposals": "<PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheQuestions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.seeUpcomingEvents": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> re<PERSON>us", "app.containers.Projects.share": "<PERSON><PERSON><PERSON>", "app.containers.Projects.shareThisProject": "Dalytis š<PERSON>o projektu", "app.containers.Projects.submitMyBasket": "Pateikti krepšelį", "app.containers.Projects.survey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.takeThePoll": "Atlikite apklausą", "app.containers.Projects.takeTheSurvey": "Atlikite apklausą", "app.containers.Projects.timeline": "<PERSON><PERSON> j<PERSON>", "app.containers.Projects.upcomingAndOngoingEvents": "<PERSON><PERSON><PERSON><PERSON> <PERSON>r v<PERSON><PERSON>", "app.containers.Projects.upcomingEvents": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.whatsAppMessage": "{projectName} | iš <PERSON> platformos {orgName}", "app.containers.Projects.yourBudget": "Bendras biudžetas", "app.containers.ProjectsIndexPage.metaDescription": "Susipažinkite su visais vykdomais {orgName} projektais ir sužino<PERSON>te, kaip galite juose da<PERSON>.\n Ateikite aptarti jums svarbiausių vietos projektų.", "app.containers.ProjectsIndexPage.metaTitle1": "Projektai | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projektai", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "<PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Norėdami sa<PERSON>oriauti š<PERSON> ve<PERSON>, pirmiausia apsilankykite adresu {signInLink} arba {signUpLink} .", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Šiuo metu šioje veikloje daly<PERSON>uti negalima.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "užsiregistruoti", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Atsiimu savo pasiūly<PERSON>ą sa<PERSON>oriauti", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {nėra <PERSON>} one {# dalyvis} other {# dalyviai}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Įspėjimas: Perspėjimas: Įterpta apklausa gali būti neprieinama ekrano skaitytuvų naudotojams. <PERSON><PERSON> kyla sunk<PERSON>, kreipkitės į platformos administratorių, kad gautumėte apklausos nuorodą iš originalios platformos. Arba galite paprašyti kitų būdų užpildyti apklausą.", "app.containers.ProjectsShowPage.process.survey.survey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "<PERSON><PERSON><PERSON><PERSON>, ar gal<PERSON> da<PERSON>, pirmiausia prisijunkite prie platformos {logInLink} .", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "<PERSON><PERSON><PERSON> a<PERSON><PERSON> galima atlikti tik tada, kai šis laiko juostos etapas yra aktyvus.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Prašome {completeRegistrationLink} atlikti apklausą.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Ši apklausa šiuo metu nėra įjungta", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Atliekant šią apklausą reikia patvirtinti savo tapatybę. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "Ap<PERSON><PERSON> ne<PERSON>, nes šis projektas nebėra aktyvu<PERSON>.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "užbaigti registraciją", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "užsiregistruoti", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Patikrinkite savo paskyrą dabar.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Šį dokumentą gali peržiūr<PERSON>ti tik tam tikri naudotojai. Pirmiausia apsilankykite adresu {signUpLink} arba {logInLink} .", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Šį dokumentą galima per<PERSON> tik tada, kai šis etapas yra aktyvus.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Prašome {completeRegistrationLink} peržiūrėti dokumentą.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "<PERSON><PERSON>, neturite te<PERSON><PERSON><PERSON> š<PERSON> dokumento.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "<PERSON><PERSON> šį dokumentą, reikia patvirtinti savo paskyrą. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "<PERSON><PERSON><PERSON>, nes šis projektas nebeaktyvu<PERSON>.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(įskaitant 1 neprisijungus)} other {(įskaitant # neprisijungus)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 pasirinkimas} other {# pasirinkimai}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "<PERSON><PERSON><PERSON>bę pasirinkusių dalyvių procentinė dalis.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "<PERSON><PERSON><PERSON><PERSON> visų balsų, gaut<PERSON> už šią parinktį, dalis.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "<PERSON><PERSON><PERSON><PERSON>:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "<PERSON><PERSON><PERSON>", "app.containers.ReactionControl.a11y_likesDislikes": "<PERSON><PERSON> viso pat<PERSON>: {likesCount}, iš viso nepa<PERSON>: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Sėkmingai atšaukėte šio įvesties įvesties nemėgstamumą.", "app.containers.ReactionControl.cancelLikeSuccess": "Sėkmingai atšaukėte šio įvesties įvestį.", "app.containers.ReactionControl.dislikeSuccess": "Jums nepatiko šis įvesties veiksmas.", "app.containers.ReactionControl.likeSuccess": "Jums patiko ši įvestis s<PERSON>ai.", "app.containers.ReactionControl.reactionErrorSubTitle": "Dėl klaidos jūsų reakcijos nepavyko užregistruoti. Bandykite dar kartą po kelių minučių.", "app.containers.ReactionControl.reactionSuccessTitle": "Jūsų reakcija sėkmingai užregistruota!", "app.containers.ReactionControl.vote": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ReactionControl.voted": "Balsavo", "app.containers.SearchInput.a11y_cancelledPostingComment": "Atšauktas komandiravimo komentaras.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} įkelti komentarai.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# įvykiai įkelti} one {# įvykis įkeltas} other {# įvykiai įkelti}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# rezultatai įkelti} one {# rezultatas įkeltas} other {# rezultatai įkelti}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# paieškos rezultatai įkelti} one {# paieškos rezultatai įkelti} other {# paieškos rezultatai įkelti}}.", "app.containers.SearchInput.removeSearchTerm": "Pašalinti paieš<PERSON> terminą", "app.containers.SearchInput.searchAriaLabel": "Pa<PERSON>š<PERSON>", "app.containers.SearchInput.searchLabel": "Pa<PERSON>š<PERSON>", "app.containers.SearchInput.searchPlaceholder": "Pa<PERSON>š<PERSON>", "app.containers.SearchInput.searchTerm": "Paieškos terminas: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "\"FranceConnect\" - tai P<PERSON> valstybės p<PERSON>ū<PERSON> sprendi<PERSON>, kuri<PERSON> siekiama užtikrinti ir supaprastinti prisijungimą prie daugiau kaip 700 internetinių paslaugų.", "app.containers.SignIn.or": "<PERSON><PERSON><PERSON>", "app.containers.SignIn.signInError": "Pateikta informacija nėra teisinga. Spustelėkite \"Pamiršote slaptažodį?\", kad iš naujo nustatytumėte slaptažodį.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Prisijunkite, užsiregistruokite arba patikrinkite savo paskyrą naudodamiesi FranceConnect.", "app.containers.SignIn.whatIsFranceConnect": "<PERSON><PERSON> <PERSON>ra \"France Connect\"?", "app.containers.SignUp.adminOptions2": "<PERSON><PERSON><PERSON> ir projekt<PERSON> vadova<PERSON>", "app.containers.SignUp.backToSignUpOptions": "Grįžti į registracijos parinktis", "app.containers.SignUp.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.emailConsent": "Užsiregistruodami sutinkate gauti el. laiškus iš šios platformos. Puslapyje \"Mano nustatymai\" galite pasirinkti, kokius el. laiškus norite gauti.", "app.containers.SignUp.emptyFirstNameError": "Įveskite savo vardą", "app.containers.SignUp.emptyLastNameError": "Įveskite savo pavardę", "app.containers.SignUp.firstNamesLabel": "Vardas", "app.containers.SignUp.goToLogIn": "Jau turite paskyrą? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "Perskaičiau ir sutinku su {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "Perskaičiau ir sutinku su {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "<PERSON><PERSON><PERSON>, kad duomenys būtų naudojami mitgestalten.wien.gv.at. Daugiau informacijos galima rasti {link}.", "app.containers.SignUp.invitationErrorText": "Jūsų kvietimas baigė galioti arba jau buvo panaudotas. Jei jau pasinaudojote kvietimo nuoroda paskyrai sukurti, pabandykite prisijungti. Priešingu atveju prisijunkite ir susikurkite naują paskyrą.", "app.containers.SignUp.lastNameLabel": "Pa<PERSON><PERSON>", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "<PERSON><PERSON><PERSON> savo sritis, į <PERSON><PERSON><PERSON>, kad gautum<PERSON><PERSON> p<PERSON> apie jas:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "<PERSON><PERSON><PERSON> m<PERSON> te<PERSON>, kad gautum<PERSON>te p<PERSON>šimų apie jas:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Išsaugoti nuostatas", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "<PERSON><PERSON> p<PERSON>i", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Norėdami tę<PERSON>i, sutikite su mūsų privatumo politika", "app.containers.SignUp.signUp2": "Užsiregistruokite", "app.containers.SignUp.skip": "Praleiskite šį veiksmą", "app.containers.SignUp.tacError": "Sutikite su mūsų taisyklėmis ir sąlygomis, kad gal<PERSON> tęsti", "app.containers.SignUp.thePrivacyPolicy": "privatumo politika", "app.containers.SignUp.theTermsAndConditions": "terminai ir <PERSON>", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {<PERSON><PERSON><PERSON>, kad band<PERSON>te užsiregistruoti an<PERSON>, bet nebaigėte proceso. Vieto<PERSON> to spustel<PERSON><PERSON><PERSON>, naudodami anks<PERSON> bandymo metu pasirinktus prisijungimo duomenis.} other {Kažkas nepavyko. Prašome pabandyti dar kartą vėliau.}}", "app.containers.SignUp.viennaConsentEmail": "<PERSON>. p<PERSON><PERSON><PERSON> ad<PERSON>", "app.containers.SignUp.viennaConsentFirstName": "Vardas", "app.containers.SignUp.viennaConsentFooter": "Prisijungę galite keisti savo profilio informaciją. Jei jau turite paskyrą tuo pačiu el. pašto adresu mitgestalten.wien.gv.at, ji bus susieta su jūsų dabartine paskyra.", "app.containers.SignUp.viennaConsentHeader": "Bus perduodami šie duomenys:", "app.containers.SignUp.viennaConsentLastName": "Pa<PERSON><PERSON>", "app.containers.SignUp.viennaConsentUserName": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>", "app.containers.SignUp.viennaDataProtection": "Vienos privatumo politika", "app.containers.SiteMap.contributions": "Įnašai", "app.containers.SiteMap.cookiePolicyLinkTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.issues": "Komentarai", "app.containers.SiteMap.options": "Parinktys", "app.containers.SiteMap.projects": "Projektai", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "Ataskaita", "app.containers.SpamReport.buttonSuccess": "Sėkmė", "app.containers.SpamReport.inappropriate": "<PERSON>s yra netinkamas arba įžeidžiantis.", "app.containers.SpamReport.messageError": "Įvyko klaida patei<PERSON>ant formą, pabandykite dar kartą.", "app.containers.SpamReport.messageSuccess": "Jūsų ataskaita išsiųsta", "app.containers.SpamReport.other": "Kita priežastis", "app.containers.SpamReport.otherReasonPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.wrong_content": "<PERSON> n<PERSON>ra svarbu", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "<PERSON><PERSON><PERSON><PERSON> profilio <PERSON>", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Jūsų balsai u<PERSON> pasiū<PERSON>, u<PERSON> kuriuos vis dar galima balsuoti, bus ištrinti. Balsai už pasiū<PERSON><PERSON>, kuri<PERSON> balsavimo laiko<PERSON> j<PERSON> b<PERSON>, nebus iš<PERSON>.", "app.containers.UsersEditPage.addPassword": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "<PERSON><PERSON><PERSON> projektuose, kuri<PERSON> reikia patikrinimo.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Patikrinkite savo tapatybę", "app.containers.UsersEditPage.bio": "<PERSON><PERSON> jus", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "<PERSON><PERSON> la<PERSON> ne<PERSON>, nes jame patei<PERSON>ama patikrinta informacija.", "app.containers.UsersEditPage.buttonSuccessLabel": "Sėkmė", "app.containers.UsersEditPage.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.changeEmail": "Keisti el. paš<PERSON>ą", "app.containers.UsersEditPage.changePassword2": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Spustelėkite čia, kad atna<PERSON>jin<PERSON>te savo patvir<PERSON>.", "app.containers.UsersEditPage.conditionsLinkText": "mūsų sąlygos", "app.containers.UsersEditPage.contactUs": "Dar viena priežastis išvykti? {feedbackLink} ir gal<PERSON><PERSON><PERSON> gal<PERSON>.", "app.containers.UsersEditPage.deleteAccountSubtext": "<PERSON><PERSON> g<PERSON>, kad <PERSON>.", "app.containers.UsersEditPage.deleteMyAccount": "<PERSON><PERSON><PERSON><PERSON> mano p<PERSON>", "app.containers.UsersEditPage.deleteYourAccount": "Ištrinkite savo paskyrą", "app.containers.UsersEditPage.deletionSection": "Ištrinkite savo paskyrą", "app.containers.UsersEditPage.deletionSubtitle": "Šio veiksmo negalima atšaukti. Platformoje paskelbtas turinys bus nuasmenintas. Jei norite ištrinti visą savo turinį, galite susisiekti su <NAME_EMAIL>.", "app.containers.UsersEditPage.email": "El. <PERSON>", "app.containers.UsersEditPage.emailEmptyError": "Nurodykite el. pašto ad<PERSON>ą", "app.containers.UsersEditPage.emailInvalidError": "Nurodykite el. pašto adresą tinkamu formatu, pavyzdžiui, <EMAIL>.", "app.containers.UsersEditPage.feedbackLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON> mums", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Vardas", "app.containers.UsersEditPage.firstNamesEmptyError": "Nurodykite vardą", "app.containers.UsersEditPage.h1": "Jūsų paskyros informacija", "app.containers.UsersEditPage.h1sub": "Redaguokite savo paskyros informaciją", "app.containers.UsersEditPage.image": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Spustelėkite norėdami pasirinkti profilio n<PERSON>uką (ne daugiau kaip 5 MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Visi jūsų profilio nustatymai", "app.containers.UsersEditPage.language": "Kalba", "app.containers.UsersEditPage.lastName": "Pa<PERSON><PERSON>", "app.containers.UsersEditPage.lastNameEmptyError": "Nurodykite pavardę", "app.containers.UsersEditPage.loading": "<PERSON><PERSON><PERSON><PERSON>...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Čia galite pakeisti el. pašto adresą arba slaptažodį.", "app.containers.UsersEditPage.loginCredentialsTitle": "Prisijungimo duomenys", "app.containers.UsersEditPage.messageError": "Nepavyko išsaugoti jūsų profilio. Pabandykite vėliau arba <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Jūsų profilis <PERSON>.", "app.containers.UsersEditPage.metaDescription": "Tai {firstName} {lastName} profilio nustatymų puslapis internetinėje dalyvavimo platformoje {tenantName}. Čia galite patvirtinti savo tapatybę, redaguoti savo paskyros informaciją, iš<PERSON>nti paskyrą ir redaguoti el. pašto nuostatas.", "app.containers.UsersEditPage.metaTitle1": "Profilio nustatymų puslapis {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Pa<PERSON>aud<PERSON> šį mygtuką, negalėsime atkurti jūsų paskyros.", "app.containers.UsersEditPage.noNameWarning2": "Šiuo metu jūsų vardas platformoje rodomas kaip: \"{displayName}\", nes neįvedėte savo vardo. Tai automatiškai sugeneruotas vardas. Jei norite jį pakeisti, toliau įveskite savo vardą.", "app.containers.UsersEditPage.notificationsSubTitle": "<PERSON><PERSON><PERSON> el. pa<PERSON>to p<PERSON> norite gauti? ", "app.containers.UsersEditPage.notificationsTitle": "Pranešimai el. paštu", "app.containers.UsersEditPage.password": "Pasirinkite naują slaptažodį", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "pateikti bent {minimumPasswordLength} simbolių ilgio slaptažodį.", "app.containers.UsersEditPage.passwordAddSection": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "app.containers.UsersEditPage.passwordAddSubtitle2": "Nustatykite slaptažodį ir lengvai prisijunkite prie platformos, kad nereik<PERSON>t<PERSON> kaskart patvirtinti savo el. paš<PERSON>.", "app.containers.UsersEditPage.passwordChangeSection": "Pakeiskite slaptažodį", "app.containers.UsersEditPage.passwordChangeSubtitle": "Patvirtinkite dabartinį slaptažodį ir pakeiskite naują slaptažodį.", "app.containers.UsersEditPage.privacyReasons": "<PERSON><PERSON> ne<PERSON>te d<PERSON>l savo privatumo, galite skai<PERSON> {conditionsLink}.", "app.containers.UsersEditPage.processing": "Siųsti...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> var<PERSON>", "app.containers.UsersEditPage.reasonsToStayListTitle": "<PERSON><PERSON><PERSON>...", "app.containers.UsersEditPage.submit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "app.containers.UsersEditPage.tooManyEmails": "Gaunate per daug el. laišk<PERSON>? Savo el. pašto nuostatas galite tvarkyti profilio nustatymuose.", "app.containers.UsersEditPage.updateverification": "<PERSON>r <PERSON><PERSON><PERSON><PERSON><PERSON> jū<PERSON>ų oficiali informacija? {reverifyButton}", "app.containers.UsersEditPage.user": "<PERSON><PERSON> nor<PERSON>, kad i<PERSON>siųstume jums p<PERSON>šimą el. paš<PERSON>?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "<PERSON><PERSON><PERSON> da<PERSON> projektuose, kuri<PERSON><PERSON> reikia pat<PERSON>.", "app.containers.UsersEditPage.verifiedIdentityTitle": "<PERSON><PERSON><PERSON> es<PERSON> pat<PERSON>", "app.containers.UsersEditPage.verifyNow": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Atsisiųskite savo atsakymus (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {nepa<PERSON>ka} one {1 patinka} other {# patinka}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Įvestis, į kurią šis komentaras buvo paskelbtas kaip atsakas į:", "app.containers.UsersShowPage.areas": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "Komentarai ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Red<PERSON><PERSON><PERSON> mano profilį", "app.containers.UsersShowPage.emptyInfoText": "Nesilaikote nė vieno pirmiau nurodyto filtro elemento.", "app.containers.UsersShowPage.eventsWithCount": "Renginiai ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Sekimas ({followingCount})", "app.containers.UsersShowPage.inputs": "Įėjimai", "app.containers.UsersShowPage.invisibleTitlePostsList": "Visi šio dalyvio pateikti duomenys", "app.containers.UsersShowPage.invisibleTitleUserComments": "Visi šio dalyvio pask<PERSON>bti komentarai", "app.containers.UsersShowPage.loadMore": "Įkelti daugiau", "app.containers.UsersShowPage.loadMoreComments": "Įkelti daugiau komentarų", "app.containers.UsersShowPage.loadingComments": "Įkeliami komentarai...", "app.containers.UsersShowPage.loadingEvents": "Įkeliami įvykiai...", "app.containers.UsersShowPage.memberSince": "<PERSON><PERSON><PERSON> nuo {date}", "app.containers.UsersShowPage.metaTitle1": "<PERSON><PERSON><PERSON> {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "<PERSON><PERSON> asmuo dar neparašė jokių komentarų.", "app.containers.UsersShowPage.noCommentsForYou": "Komentarų čia dar nėra.", "app.containers.UsersShowPage.noEventsForUser": "Dar <PERSON>te nė viename reng<PERSON>je.", "app.containers.UsersShowPage.postsWithCount": "<PERSON><PERSON><PERSON><PERSON>š<PERSON> ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Projekto aplankai", "app.containers.UsersShowPage.projects": "Projektai", "app.containers.UsersShowPage.proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.seePost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.surveyResponses": "Atsakymai ({responses})", "app.containers.UsersShowPage.topics": "Temos", "app.containers.UsersShowPage.tryAgain": "Įvyko klaida, bandykite vėliau.", "app.containers.UsersShowPage.userShowPageMetaDescription": "<PERSON> yra {firstName} {lastName} profilio puslapis internetinėje dalyvavimo platformoje {orgName}. Čia apžvelgiamas visas jų indėlis.", "app.containers.VoteControl.close": "Uždaryti", "app.containers.VoteControl.voteErrorTitle": "<PERSON><PERSON><PERSON> nutiko ne taip", "app.containers.admin.ContentBuilder.default": "numatytasis", "app.containers.admin.ContentBuilder.imageTextCards": "Paveikslėlių ir teksto k<PERSON>s", "app.containers.admin.ContentBuilder.infoWithAccordions": "Informacija ir akordeonai", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 stulpelis", "app.containers.admin.ContentBuilder.projectDescription": "<PERSON>je<PERSON><PERSON>", "app.containers.app.navbar.admin": "Valdyti platformą", "app.containers.app.navbar.allProjects": "Visi projektai", "app.containers.app.navbar.ariaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.closeMobileNavMenu": "Uždaryti mobiliosios navigacijos meniu", "app.containers.app.navbar.editProfile": "<PERSON><PERSON>", "app.containers.app.navbar.fullMobileNavigation": "Visas mobilusis telefonas", "app.containers.app.navbar.logIn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.logoImgAltText": "{orgName} <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.myProfile": "<PERSON><PERSON>", "app.containers.app.navbar.search": "Pa<PERSON>š<PERSON>", "app.containers.app.navbar.showFullMenu": "Rodyt<PERSON> visą meniu", "app.containers.app.navbar.signOut": "<PERSON>si<PERSON><PERSON><PERSON>", "app.containers.eventspage.errorWhenFetchingEvents": "Įkeliant įvykius įvyko klaida. Pabandykite iš naujo įkelti puslapį.", "app.containers.eventspage.events": "Rengin<PERSON><PERSON>", "app.containers.eventspage.eventsPageDescription": "Rod<PERSON>i visus {orgName}platformoje paskelbtus įvykius.", "app.containers.eventspage.eventsPageTitle1": "Renginiai | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projektai", "app.containers.eventspage.noPastEvents": "Nėra rodomų praeities įvykių", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Šiuo metu neplanuojama jokių būsimų ar vykstančių renginių.", "app.containers.eventspage.pastEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.upcomingAndOngoingEvents": "<PERSON><PERSON><PERSON><PERSON> <PERSON>r v<PERSON><PERSON>", "app.containers.footer.accessibility-statement": "Prieina<PERSON><PERSON>", "app.containers.footer.ariaLabel": "<PERSON><PERSON><PERSON>", "app.containers.footer.cookie-policy": "Slapukų politika", "app.containers.footer.cookieSettings": "Slapukų nustatymai", "app.containers.footer.feedbackEmptyError": "Grįžtamojo ry<PERSON><PERSON> la<PERSON> negali būti t<PERSON>.", "app.containers.footer.poweredBy": "Powered by", "app.containers.footer.privacy-policy": "Privatumo politika", "app.containers.footer.siteMap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.terms-and-conditions": "Terminai ir <PERSON>", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON>, nor<PERSON>i", "app.containers.ideaHeading.editForm": "Redaguoti formą", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Ar tikrai norite išvykti?", "app.containers.ideaHeading.leaveIdeaForm": "Palikite idėjos formą", "app.containers.ideaHeading.leaveIdeaText": "Jūsų atsakymai nebus išsaugoti.", "app.containers.landing.cityProjects": "Projektai", "app.containers.landing.completeProfile": "Užpildykite savo profilį", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON>, {firstName}. Atėjo laikas užpildyti savo profilį.", "app.containers.landing.createAccount": "Užsiregistruokite", "app.containers.landing.defaultSignedInMessage": "{orgName} k<PERSON><PERSON><PERSON> jū<PERSON>. Atėjo jūsų e<PERSON><PERSON>, kad jūsų balsas būtų išgi<PERSON>as!", "app.containers.landing.doItLater": "Tai padarysiu vėliau", "app.containers.landing.new": "naujas", "app.containers.landing.subtitleCity": "Sveiki atvykę į {orgName}dalyvavimo platformą", "app.containers.landing.titleCity": "Kurkime {orgName} ateitį kartu", "app.containers.landing.twitterMessage": "<PERSON><PERSON><PERSON><PERSON> u<PERSON> {ideaTitle}", "app.containers.landing.upcomingEventsWidgetTitle": "<PERSON><PERSON><PERSON><PERSON> <PERSON>r v<PERSON><PERSON>", "app.containers.landing.userDeletedSubtitle": "Bet kada galite sukurti naują paskyrą arba {contactLink} ir p<PERSON><PERSON> mums, ką gal<PERSON> patob<PERSON>.", "app.containers.landing.userDeletedSubtitleLinkText": "Parašykite mums la<PERSON>", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "Jūsų paskyra ištrinta.", "app.containers.landing.userDeletionFailed": "Įvyko jūs<PERSON> paskyros <PERSON><PERSON>, mums buvo pranešta apie problemą ir mes stengsim<PERSON>s ją išspręsti. Prašome pabandyti dar kartą vėliau.", "app.containers.landing.verifyNow": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.landing.verifyYourIdentity": "Patikrinkite savo tapatybę", "app.containers.landing.viewAllEventsText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visus renginius", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Grįžti į aplanką", "app.errors.after_end_at": "Pradžios data yra vėlesnė už pabaigos datą", "app.errors.avatar_carrierwave_download_error": "Nepavyko atsisiųsti avataro failo.", "app.errors.avatar_carrierwave_integrity_error": "Avataro failas yra neleistino tipo.", "app.errors.avatar_carrierwave_processing_error": "Nepavyko apdoroti avataro.", "app.errors.avatar_extension_blacklist_error": "Avataro atvaizdo failo plėtinys neleidžiamas. Leidžiami šie plėtiniai: jpg, jpeg, gif ir png.", "app.errors.avatar_extension_whitelist_error": "Avataro atvaizdo failo plėtinys neleidžiamas. Leidžiami šie plėtiniai: jpg, jpeg, gif ir png.", "app.errors.banner_cta_button_multiloc_blank": "Įveskite mygtuko tekstą.", "app.errors.banner_cta_button_url_blank": "Įveskite nuorodą.", "app.errors.banner_cta_button_url_url": "Įveskite galiojančią nuorodą. Įsitikinkite, kad nuoroda prasideda \"https://\".", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Įveskite mygtuko tekstą.", "app.errors.banner_cta_signed_in_url_blank": "Įveskite nuorodą.", "app.errors.banner_cta_signed_in_url_url": "Įveskite galiojančią nuorodą. Įsitikinkite, kad nuoroda prasideda \"https://\".", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Įveskite mygtuko tekstą.", "app.errors.banner_cta_signed_out_url_blank": "Įveskite nuorodą.", "app.errors.banner_cta_signed_out_url_url": "Įveskite galiojančią nuorodą. Įsitikinkite, kad nuoroda prasideda \"https://\".", "app.errors.base_includes_banned_words": "Galbūt pavartojote vieną ar daugiau žodžių, kurie laikomi keiksmažodžiais. Pakeiskite tekstą ir pašalinkite visus galimus keiksmaž<PERSON>žius.", "app.errors.body_multiloc_includes_banned_words": "<PERSON>a<PERSON><PERSON> y<PERSON>, kurie la<PERSON><PERSON>.", "app.errors.bulk_import_idea_not_valid": "<PERSON><PERSON><PERSON> id<PERSON> negalioja: {value}.", "app.errors.bulk_import_image_url_not_valid": "I<PERSON> {value}ne<PERSON><PERSON><PERSON> atsisiųsti jokio vaizdo. Įsitikinkite, kad URL yra galiojantis ir baigiasi failo p<PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, .png arba .jpg. Ši problema kyla e<PERSON>, kurios ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>, kuri<PERSON><PERSON> trū<PERSON><PERSON> k<PERSON> {value}. <PERSON>i problema kyla e<PERSON>, kurios <PERSON> {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Idėjos vieta su neskaitmenine koordinate {value}. Ši problema kyla e<PERSON>, kurios ID {row}.", "app.errors.bulk_import_malformed_pdf": "<PERSON><PERSON><PERSON>, kad įkeltas PDF failas yra netinkamos formos. Pabandykite dar kartą eksportuoti PDF failą iš savo šaltinio ir tada įkelkite jį iš naujo.", "app.errors.bulk_import_maximum_ideas_exceeded": "Ma<PERSON><PERSON><PERSON> {value} id<PERSON><PERSON><PERSON> s<PERSON> virš<PERSON>.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> did<PERSON> le<PERSON> {value} puslapių skaičius PDF formate.", "app.errors.bulk_import_not_enough_pdf_pages": "Įkeltame PDF dokumente yra nepakankamai puslapių - jis turi tur<PERSON>ti bent tiek pat puslapi<PERSON>, kiek ir atsisiųstas šab<PERSON>as.", "app.errors.bulk_import_publication_date_invalid_format": "Idėja su neteisingu paskelbimo datos formatu \"{value}\". Prašome naudoti formatą \"DD-MM-GYYYY\".", "app.errors.cannot_contain_ideas": "Pasirinktas dalyvavimo būdas nepalaiko šio tipo pranešimų. Redaguokite savo pasirinkimą ir bandykite dar kartą.", "app.errors.cant_change_after_first_response": "To nebegalite p<PERSON><PERSON>i, nes kai kurie naudotojai jau atsak<PERSON>", "app.errors.category_name_taken": "Kategorija su šiuo pavadinimu jau egzistuoja", "app.errors.confirmation_code_expired": "<PERSON><PERSON> b<PERSON>. Prašykite naujo kodo.", "app.errors.confirmation_code_invalid": "Netinkamas pat<PERSON><PERSON><PERSON> koda<PERSON>. Patikrinkite savo el. pa<PERSON><PERSON>, ar joje yra te<PERSON> koda<PERSON>, arba pabandykite \"Siųsti naują kodą\".", "app.errors.confirmation_code_too_many_resets": "Patvirtinimo kodą siuntėte per daug kartų. Susisiekite su mumis, kad vietoj jo gautumėte kvietimo kodą.", "app.errors.confirmation_code_too_many_retries": "Bandėte per daug kartų. Prašykite naujo kodo arba pabandykite pakeisti el. pašto adresą.", "app.errors.email_already_active": "Eilutėje {row} rastas el. pašto adresas {value} jau priklauso registruotam dalyviui.", "app.errors.email_already_invited": "Eilutėje {row} rastas el. pašto adresas {value} jau buvo pakviestas", "app.errors.email_blank": "Ši reikšmė negali būti tuš<PERSON>", "app.errors.email_domain_blacklisted": "Registruodamiesi naudokite kitą el. pašto domeną.", "app.errors.email_invalid": "Naudokite galiojantį el. pašto ad<PERSON>.", "app.errors.email_taken": "Paskyra su šiuo el. pašto adresu jau egzistuoja. Galite prisijungti vietoj jos.", "app.errors.email_taken_by_invite": "{value} jau užimtas laukiančiu kvietimu. Patikrinkite nepageidaujamų laiškų aplanką arba susisiekite su {supportEmail} , jei jo nerandate.", "app.errors.emails_duplicate": "Viena ar daugiau pasikar<PERSON>čių el. pašto adreso {value} reikšmių rasta šioje (-se) eilutėje (-ėse): {rows}", "app.errors.extension_whitelist_error": "<PERSON><PERSON><PERSON>, kurį bandėte įkelti, formatas nepalai<PERSON>.", "app.errors.file_extension_whitelist_error": "<PERSON><PERSON><PERSON>, kurį bandėte įkelti, formatas nepalai<PERSON>.", "app.errors.first_name_blank": "Ši reikšmė negali būti tuš<PERSON>", "app.errors.generics.blank": "<PERSON><PERSON> negali būti <PERSON>.", "app.errors.generics.invalid": "<PERSON>o ka<PERSON>", "app.errors.generics.taken": "Šis el. paštas jau egzistuoja. Su juo susieta kita paskyra.", "app.errors.generics.unsupported_locales": "<PERSON><PERSON> la<PERSON> v<PERSON>.", "app.errors.group_ids_unauthorized_choice_moderator": "Kaip projekto vadovas galite siųsti el. laiškus tik tiems žmonėms, kurie turi prieigą prie jūsų projekto (-ų).", "app.errors.has_other_overlapping_phases": "Projektų etapai negali sutapti.", "app.errors.invalid_email": "Eilutėje {row} rastas el. pašto adresas {value} nėra galiojantis el. pašto adresas", "app.errors.invalid_row": "Bandant apdoroti eilutę {row}įvyko ne<PERSON><PERSON><PERSON> k<PERSON>a.", "app.errors.is_not_timeline_project": "Dabartinis projektas nepalaiko etapų.", "app.errors.key_invalid": "Raktą gali sudaryti tik raidė<PERSON>, skaičiai ir pabraukimai(_)", "app.errors.last_name_blank": "Ši reikšmė negali būti tuš<PERSON>", "app.errors.locale_blank": "Pasirinkite kalbą", "app.errors.locale_inclusion": "Pasirinkite palaikomą kalbą", "app.errors.malformed_admin_value": "Eilutėje {row} rasta administratoriaus re<PERSON> {value} negalioja", "app.errors.malformed_groups_value": "{value} eilutėje {row} esanti grupė nėra galiojanti grupė.", "app.errors.max_invites_limit_exceeded1": "Kvietimų skaičius viršija 1000.", "app.errors.maximum_attendees_greater_than1": "Didžiausias registruotoj<PERSON> skaičius turi būti did<PERSON>nis nei 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Didžiausias registruotoj<PERSON> skaičius turi būti didesnis arba lygus dabartiniam registruotoj<PERSON> skaičiui.", "app.errors.no_invites_specified": "Nepavyko rasti jokio el. pašto adreso.", "app.errors.no_recipients": "Kampanijos negalima išsiųsti, nes nėra gavėjų. Grupė, k<PERSON><PERSON>, yra arba t<PERSON>, arba niekas nesutiko gauti el. laiškų.", "app.errors.number_invalid": "Įveskite galiojantį numerį.", "app.errors.password_blank": "Ši reikšmė negali būti tuš<PERSON>", "app.errors.password_invalid": "Dar kartą patikrinkite dabartinį slaptažodį.", "app.errors.password_too_short": "Slap<PERSON>žodis turi būti ne trumpesnis kaip 8 simbolių.", "app.errors.resending_code_failed": "Siunčiant patvir<PERSON><PERSON> kod<PERSON> kaž<PERSON> nepavy<PERSON>.", "app.errors.slug_taken": "Šis projekto URL jau egzistuoja. Pakeiskite projekto slugą į kitą.", "app.errors.tag_name_taken": "Žyma su šiuo pavadinimu jau egzistuoja", "app.errors.title_multiloc_blank": "Pavadinimas negali bū<PERSON>.", "app.errors.title_multiloc_includes_banned_words": "Pavadinime yra <PERSON>, kurie la<PERSON>.", "app.errors.token_invalid": "Slap<PERSON>ž<PERSON><PERSON>io atstatymo nuorodas galima naudoti tik vieną kartą ir jos galioja vieną valandą nuo išsiuntimo. {passwordResetLink}.", "app.errors.too_common": "Šį slaptažodį galima lengvai atspėti. Pasirinkite stipresnį slaptažodį.", "app.errors.too_long": "Pasirinkite trumpesnį slaptažodį (ne daugiau kaip 72 simboliai)", "app.errors.too_short": "Pasirinkite bent 8 simbolių slaptažodį", "app.errors.uncaught_error": "Įvyko ne<PERSON><PERSON><PERSON> k<PERSON>.", "app.errors.unknown_group": "{value} eilutėje {row} esanti grupė nėra žinoma grupė.", "app.errors.unknown_locale": "{value} eilutėje {row} rasta kalba nėra sukonfigūruota kalba", "app.errors.unparseable_excel": "<PERSON><PERSON><PERSON><PERSON> \"Excel\" failo nepavy<PERSON> a<PERSON>i.", "app.errors.url": "Įveskite galiojančią nuorodą. Įsitikinkite, kad nuoroda prasideda https://.", "app.errors.verification_taken": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>, nes naudo<PERSON>t tuos pa<PERSON>ius duomenis buvo patikrinta kita paskyra.", "app.errors.view_name_taken": "Rodinys su šiuo pavadinimu jau egzistuoja", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Įraše ar komentare automatiškai aptiktas netinkamas turinys", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Prisijunkite su \"StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Užsiregistruokite \"StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Sukurkite \"Stadt Wien\" paskyrą ir naudokite vieną prisijungimo vardą daugeliui Vienos skaitmeninių paslaugų.", "app.modules.id_cow.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_cow.emptyFieldError": "<PERSON><PERSON> lauka<PERSON> negali bū<PERSON>.", "app.modules.id_cow.helpAltText": "<PERSON><PERSON><PERSON>, kur rasti asmens tapat<PERSON>s kortel<PERSON>s serijos numerį.", "app.modules.id_cow.invalidIdSerialError": "Neteisingas serijinis ID", "app.modules.id_cow.invalidRunError": "Neteisingas RUN", "app.modules.id_cow.noMatchFormError": "Nebuvo rasta atitikmenų.", "app.modules.id_cow.notEntitledFormError": "<PERSON><PERSON>.", "app.modules.id_cow.showCOWHelp": "Kur galiu rasti savo ID serijos numerį?", "app.modules.id_cow.somethingWentWrongError": "Negalime j<PERSON>, nes ka<PERSON><PERSON>", "app.modules.id_cow.submit": "Pat<PERSON><PERSON><PERSON>", "app.modules.id_cow.takenFormError": "Jau užimta.", "app.modules.id_cow.verifyCow": "Patikrinkite naudodami COW", "app.modules.id_franceconnect.verificationButtonAltText": "Patikrinkite su \"FranceConnect", "app.modules.id_gent_rrn.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "<PERSON><PERSON> lauka<PERSON> negali bū<PERSON>.", "app.modules.id_gent_rrn.gentRrnHelp": "Jūsų socialinio draudimo numeris nurodytas skaitmeninės asmens tapatyb<PERSON> kort<PERSON> kitoje pu<PERSON>.", "app.modules.id_gent_rrn.invalidRrnError": "Neteisingas socialinio draudimo numeris", "app.modules.id_gent_rrn.noMatchFormError": "Nepavyko rasti atgalinės informacijos apie jūsų socialinio draudimo numerį", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Negalime j<PERSON>, nes gyvenate ne Gente", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "<PERSON>eg<PERSON><PERSON> j<PERSON>, nes esate jaunesnis nei 14 metų.", "app.modules.id_gent_rrn.rrnLabel": "Socialinio draudimo numeris", "app.modules.id_gent_rrn.rrnTooltip": "<PERSON><PERSON>, ar esate vyres<PERSON> nei 14 metų Gento pilietis, prašome nurodyti savo socialinio draudimo numerį.", "app.modules.id_gent_rrn.showGentRrnHelp": "Kur galiu rasti savo ID serijos numerį?", "app.modules.id_gent_rrn.somethingWentWrongError": "Negalime j<PERSON>, nes ka<PERSON><PERSON>", "app.modules.id_gent_rrn.submit": "Pat<PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.takenFormError": "Jūsų socialinio draudimo numeris jau buvo panaudotas kitai paskyrai pat<PERSON>ti.", "app.modules.id_gent_rrn.verifyGentRrn": "Patikrinkite naudodami GentRrn", "app.modules.id_id_card_lookup.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.emptyFieldError": "<PERSON><PERSON> lauka<PERSON> negali bū<PERSON>.", "app.modules.id_id_card_lookup.helpAltText": "<PERSON><PERSON><PERSON> k<PERSON>", "app.modules.id_id_card_lookup.invalidCardIdError": "Šis ID negalioja.", "app.modules.id_id_card_lookup.noMatchFormError": "Nebuvo rasta atitikmenų.", "app.modules.id_id_card_lookup.showHelp": "Kur galiu rasti savo ID serijos numerį?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Negalime j<PERSON>, nes ka<PERSON><PERSON>", "app.modules.id_id_card_lookup.submit": "Pat<PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.takenFormError": "Jau užimta.", "app.modules.id_oostende_rrn.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.emptyFieldError": "<PERSON><PERSON> lauka<PERSON> negali bū<PERSON>.", "app.modules.id_oostende_rrn.invalidRrnError": "Neteisingas socialinio draudimo numeris", "app.modules.id_oostende_rrn.noMatchFormError": "Nepavyko rasti atgalinės informacijos apie jūsų socialinio draudimo numerį", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Negalime j<PERSON>, nes gyvenate ne Oostendėje", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "<PERSON>eg<PERSON><PERSON> j<PERSON>, nes esate jaunesnis nei 14 metų.", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Jūsų socialinio draudimo numeris nurodytas skaitmeninės asmens tapatyb<PERSON> kort<PERSON> kitoje pu<PERSON>.", "app.modules.id_oostende_rrn.rrnLabel": "Socialinio draudimo numeris", "app.modules.id_oostende_rrn.rrnTooltip": "<PERSON><PERSON>, ar <PERSON><PERSON>, v<PERSON><PERSON> nei 14 metų, prašome jūsų socialinio draudimo numerio.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Kur galiu rasti savo socialinio draudimo numerį?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Negalime j<PERSON>, nes ka<PERSON><PERSON>", "app.modules.id_oostende_rrn.submit": "Pat<PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.takenFormError": "Jūsų socialinio draudimo numeris jau buvo panaudotas kitai paskyrai pat<PERSON>ti.", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Patikrinkite naudodami socialinio draudimo numerį", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Gavote administratoriaus teises į aplanką \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "<PERSON><PERSON><PERSON>", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Peržiūrėkite projektus adresu {folderUrl} ir pareikškite savo nuomonę!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | iš {orgName}dalyvavimo platformos", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | iš {orgName}dalyvavimo platformos", "app.sessionRecording.accept": "Taip, sutinku", "app.sessionRecording.modalDescription1": "Siekdami geriau suprasti savo naudotojus, atsitiktine tvarka prašome nedidelės lankytojų dalies išsamiai sekti jų naršymo sesiją.", "app.sessionRecording.modalDescription2": "Vienintelis įrašytų duomenų tikslas - tobulinti svetainę. Jokie jūsų duomenys nebus perduoti trečiajai šaliai. Bet kokia jūsų įvesta neskelbtina informacija bus filtruojama.", "app.sessionRecording.modalDescription3": "Ar sutinkate?", "app.sessionRecording.modalDescriptionFaq": "DUK čia.", "app.sessionRecording.modalTitle": "Padėkite mums to<PERSON><PERSON><PERSON> svet<PERSON>", "app.sessionRecording.reject": "Ne, atmetu", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Atlikti biudžeto paskirstymą", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "<PERSON><PERSON><PERSON> atsiliepimus apie dokument<PERSON>", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Sukurkite platformoje atliekamą apklausą", "app.utils.AdminPage.ProjectEdit.createPoll": "Su<PERSON><PERSON><PERSON>", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Įdiegti išorinę apkla<PERSON>ą", "app.utils.AdminPage.ProjectEdit.findVolunteers": "<PERSON><PERSON><PERSON>", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "<PERSON>ink<PERSON> informaciją ir atsiliepimus", "app.utils.AdminPage.ProjectEdit.shareInformation": "Dalytis informacija", "app.utils.FormattedCurrency.credits": "kreditai", "app.utils.FormattedCurrency.tokens": "žetonai", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# kreditai} one {# kreditai} other {# kreditai}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# žetonai} one {# žetonas} other {# žetonai}}", "app.utils.IdeaCards.mostDiscussed": "Daugiausia diskusijų", "app.utils.IdeaCards.mostReacted": "Dauguma reakcijų", "app.utils.IdeaCards.newest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.oldest": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.random": "Atsitiktinis", "app.utils.IdeaCards.trending": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.contributionFormTitle": "<PERSON><PERSON><PERSON><PERSON> nauj<PERSON> indėlį", "app.utils.IdeasNewPage.ideaFormTitle": "Pridėti naują <PERSON>", "app.utils.IdeasNewPage.initiativeFormTitle": "Pridėti naują iniciatyvą", "app.utils.IdeasNewPage.issueFormTitle1": "Pridėti naują komentarą", "app.utils.IdeasNewPage.optionFormTitle": "<PERSON><PERSON><PERSON><PERSON> nauj<PERSON> parinktį", "app.utils.IdeasNewPage.petitionFormTitle": "Pridė<PERSON> nauj<PERSON> peticij<PERSON>", "app.utils.IdeasNewPage.projectFormTitle": "Pridėti naują projektą", "app.utils.IdeasNewPage.proposalFormTitle": "Pridėti naują p<PERSON>", "app.utils.IdeasNewPage.questionFormTitle": "Pridėti nauj<PERSON> k<PERSON>", "app.utils.IdeasNewPage.surveyTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourComment": "Peržiūrėti savo komentarą", "app.utils.IdeasNewPage.viewYourContribution": "Peržiūrėkite savo indėlį", "app.utils.IdeasNewPage.viewYourIdea": "Peržiūrėkite savo idėją", "app.utils.IdeasNewPage.viewYourInitiative": "Peržiūrėkite savo iniciatyvą", "app.utils.IdeasNewPage.viewYourInput": "Peržiūrėkite savo indėlį", "app.utils.IdeasNewPage.viewYourIssue": "Peržiūrėkite savo problemą", "app.utils.IdeasNewPage.viewYourOption": "Peržiūrėkite savo parinktį", "app.utils.IdeasNewPage.viewYourPetition": "Peržiūrėkite savo peticiją", "app.utils.IdeasNewPage.viewYourProject": "Peržiūrėkite savo projektą", "app.utils.IdeasNewPage.viewYourProposal": "Peržiūrėkite savo pasiūlymą", "app.utils.IdeasNewPage.viewYourQuestion": "<PERSON><PERSON><PERSON><PERSON><PERSON>ėti savo klausimą", "app.utils.Projects.sendSubmission": "Siųsti pateikimo identifikatorių į mano el. paštą", "app.utils.Projects.sendSurveySubmission": "Siųsti apklausos pateikimo identifikatorių į mano el. paštą", "app.utils.Projects.surveySubmission": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.Projects.yourResponseHasTheFollowingId": "<PERSON><PERSON><PERSON><PERSON> atsakymas turi tokį identifikatorių: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "Jei vėliau nuspr<PERSON>, kad norite, jog jūs<PERSON> atsakymas būt<PERSON> p<PERSON>, susisiekite su mumis nurodydami šį unikalų identifikatorių:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Norėdami <PERSON> š<PERSON>, turite užpildyti savo profilį.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Neatitinkate reikalavimų, kad gal<PERSON>te dalyvauti šiame renginyje.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Jums <PERSON>idž<PERSON>a daly<PERSON>uti šiame renginyje.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "<PERSON><PERSON><PERSON><PERSON>, turite prisijungti arba užsiregistruoti.", "app.utils.actionDescriptors.attendingEventNotVerified": "<PERSON><PERSON><PERSON> šiame renginyje turite patvirtinti savo paskyrą.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "<PERSON><PERSON><PERSON><PERSON>, turite užpildyti savo profilį.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Neatitinkate savanorystei keliamų reikalavimų.", "app.utils.actionDescriptors.volunteeringNotPermitted": "<PERSON><PERSON>.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "<PERSON><PERSON><PERSON><PERSON>, turite prisijungti arba užsiregistru<PERSON>i.", "app.utils.actionDescriptors.volunteeringNotVerified": "<PERSON><PERSON><PERSON><PERSON>, turite patvir<PERSON>ti savo p<PERSON>.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Prašome {completeRegistrationLink} savanoriauti.", "app.utils.errors.api_error_default.in": "<PERSON><PERSON><PERSON>", "app.utils.errors.default.ajv_error_birthyear_required": "Įrašykite savo gimimo metus", "app.utils.errors.default.ajv_error_date_any": "Prašome įrašyti galiojančią datą", "app.utils.errors.default.ajv_error_domicile_required": "Įrašykite savo gyvenamąją vietą", "app.utils.errors.default.ajv_error_gender_required": "Įrašykite savo lytį", "app.utils.errors.default.ajv_error_invalid": "Negalioja", "app.utils.errors.default.ajv_error_maxItems": "Negalima įtraukti daugiau nei {limit, plural, one {# elementas} other {# elementai}}", "app.utils.errors.default.ajv_error_minItems": "<PERSON><PERSON> b<PERSON><PERSON> bent {limit, plural, one {# elementas} other {# elementai}}", "app.utils.errors.default.ajv_error_number_any": "Įveskite galiojantį numerį", "app.utils.errors.default.ajv_error_politician_required": "Įrašykite, ar esate politikas", "app.utils.errors.default.ajv_error_required3": "Laukas yra privalomas: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> t<PERSON>", "app.utils.errors.default.api_error_accepted": "<PERSON><PERSON> b<PERSON><PERSON>", "app.utils.errors.default.api_error_blank": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> t<PERSON>", "app.utils.errors.default.api_error_confirmation": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_empty": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> t<PERSON>", "app.utils.errors.default.api_error_equal_to": "<PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_even": "<PERSON><PERSON> b<PERSON> net", "app.utils.errors.default.api_error_exclusion": "Rezervuota", "app.utils.errors.default.api_error_greater_than": "<PERSON>ra per ma<PERSON>", "app.utils.errors.default.api_error_greater_than_or_equal_to": "<PERSON>ra per ma<PERSON>", "app.utils.errors.default.api_error_inclusion": "Neįtrauktas į sąrašą", "app.utils.errors.default.api_error_invalid": "Negalioja", "app.utils.errors.default.api_error_less_than": "<PERSON><PERSON> per <PERSON>", "app.utils.errors.default.api_error_less_than_or_equal_to": "<PERSON><PERSON> per <PERSON>", "app.utils.errors.default.api_error_not_a_number": "<PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_not_an_integer": "<PERSON><PERSON> <PERSON><PERSON><PERSON> s<PERSON>", "app.utils.errors.default.api_error_other_than": "<PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_present": "<PERSON><PERSON> b<PERSON><PERSON>", "app.utils.errors.default.api_error_too_long": "<PERSON>ra per ilgas", "app.utils.errors.default.api_error_too_short": "Yra per trumpas", "app.utils.errors.default.api_error_wrong_length": "Yra netinkamo ilgio", "app.utils.errors.defaultapi_error_.odd": "<PERSON><PERSON> b<PERSON><PERSON> k<PERSON>", "app.utils.notInGroup": "Neatitinkate reikalavimų, kad gal<PERSON>tumėte dalyvauti.", "app.utils.participationMethod.onSurveySubmission": "Ačiū. Jūsų atsakymas gautas.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "<PERSON><PERSON><PERSON><PERSON>, nes <PERSON>is etapas nebėra aktyvu<PERSON>.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "<PERSON><PERSON><PERSON> balsavimo <PERSON>.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "<PERSON><PERSON> b<PERSON>.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "<PERSON><PERSON><PERSON><PERSON>, turite prisijungti arba užsiregist<PERSON>i.", "app.utils.participationMethodConfig.voting.votingNotVerified": "<PERSON><PERSON><PERSON> balsuodami turite patvirtinti savo paskyrą.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Biudžetų teikimas baigtas {endDate}.</b> Dalyviai turėjo iš viso <b> po{maxBudget} , k<PERSON><PERSON><PERSON> gal<PERSON> pask<PERSON> {optionCount} variantams.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Pateiktas biudžetas", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Pateiktas biudžetas 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Neatitinkate reikalavimų, kad galėtumėte priskirti biudžetus.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Jums neleidžiama priskirti biudžetų.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Norėdami pris<PERSON>, turite prisijungti arba užsiregistruoti.", "app.utils.votingMethodUtils.budgetingNotVerified": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> bi<PERSON>žetus turite patvirtinti savo paskyrą.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b><PERSON><PERSON><PERSON><PERSON> nebus įskaitytas,</b> kol nepaspausite \"Pateikti\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Mažiausias reikalaujamas bi<PERSON>žetas yra {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "Baigę spauskite \"Pateikti\", kad pateiktumėte savo biudžetą.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Pasirinkite norimas parinktis spustelėdami „Pridėti“.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "<PERSON><PERSON> viso <b>{maxBudget} galite pask<PERSON><PERSON>ti {optionCount} parinktims</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON>, jūsų biudžetas pateiktas!</b> Toliau esančias parinktis galite patikrinti bet kuriuo metu arba pakeisti prieš tai. <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON>, jūsų biudžetas pateiktas!</b> Toliau bet kuriuo metu galite patikrinti savo galimybes.", "app.utils.votingMethodUtils.castYourVote": "Atiduokite savo balsą", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "<PERSON><PERSON><PERSON> p<PERSON> ne daugiau kaip {maxVotes, plural, one {# kreditą} other {# kreditus}} už kiekvieną pasirinkimą.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "<PERSON><PERSON><PERSON> p<PERSON> ne daugiau kaip {maxVotes, plural, one {# tašką} other {# taškus}} už kiekvieną variantą.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "<PERSON><PERSON><PERSON> p<PERSON> ne daugiau kaip {maxVotes, plural, one {# žetoną} other {# žetonus}} vienam p<PERSON>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "<PERSON><PERSON><PERSON> p<PERSON> ne daugiau kaip {maxVotes, plural, one {# balsą} other {# balsus}} už kiekvieną variantą.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "Baigę spauskite \"Pateikti\", kad atiduotum<PERSON>te savo balsą.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Pasirinkite norimas parinktis s<PERSON>telėdami „Pasirinkite“.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "<PERSON><PERSON><PERSON> turite iš viso <b>{totalVotes, plural, one {# credit} other {# credits}} balsų, kuriuos galite paskirs<PERSON>ti tarp {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "<PERSON><PERSON><PERSON> turite iš viso <b>{totalVotes, plural, one {# tašką} other {# taškus}} kurį galite paskirstyti {optionCount, plural, one {# pasirinkimui} other {# pasirinkimų}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "<PERSON><PERSON><PERSON> turite iš viso <b>{totalVotes, plural, one {# žetoną} other {# žetonus}} kurį galite paskirstyti {optionCount, plural, one {# pasirinkimui} other {# pasirinkimų}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "<PERSON><PERSON><PERSON> turite iš viso <b>{totalVotes, plural, one {# balsą} other {# balsus}} kurį galite paskirstyti {optionCount, plural, one {# pasirinkimui} other {# pasirinkimų}}</b>.", "app.utils.votingMethodUtils.finalResults": "Galutiniai rezultatai", "app.utils.votingMethodUtils.finalTally": "Galutinis rezulta<PERSON>", "app.utils.votingMethodUtils.howToParticipate": "<PERSON><PERSON>", "app.utils.votingMethodUtils.howToVote": "<PERSON><PERSON>", "app.utils.votingMethodUtils.multipleVotingEnded1": "Bals<PERSON><PERSON> baigtas <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 kreditų} one {1 kreditas} other {# kreditai}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 taškų} one {1 taškas} other {# taškai}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 žetonų} one {1 žetonas} other {# žetonai}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 balsų} one {1 balsas} other {# balsų}}", "app.utils.votingMethodUtils.results": "Rezultatai", "app.utils.votingMethodUtils.singleVotingEnded": "Bals<PERSON><PERSON> baigtas <b>{endDate}.</b> <PERSON><PERSON><PERSON> <b>balsuoti už {maxVotes} variantus</b>.", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Pasirinkite page<PERSON><PERSON><PERSON><PERSON> par<PERSON> b<PERSON> \"Balsuoti\".", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Turite <b>{totalVotes} balsų, kuriuos</b> galite priskirti parinktims.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "Baigę spauskite \"Pateikti\", kad atiduotum<PERSON>te savo balsą.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Ba<PERSON><PERSON><PERSON> baigtas <b>{endDate}.</b> Dalyviai <PERSON> <b>balsuoti už 1 variantą</b>.", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Pasirinkite pageidaujamą variant<PERSON> b<PERSON> \"Balsuoti\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Turite <b>1 balsą</b>, kurį galite skirti vienai iš galim<PERSON>.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Bals<PERSON><PERSON> baigtas <b>{endDate}.</b> Dalyviai gal<PERSON>jo <b>balsuoti už tiek variantų, kiek nor<PERSON>.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Galite balsuoti už tiek variantų, kiek norite.", "app.utils.votingMethodUtils.submitYourBudget": "Pateikite savo biudžetą", "app.utils.votingMethodUtils.submittedBudgetCountText2": "<PERSON><PERSON><PERSON> pat<PERSON> savo biudžetą internetu", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "žmonės pateikė savo biudžetus internetu.", "app.utils.votingMethodUtils.submittedVoteCountText2": "<PERSON><PERSON><PERSON> savo balsą internetu", "app.utils.votingMethodUtils.submittedVotesCountText2": "ž<PERSON><PERSON>s balsavo internetu.", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "<PERSON><PERSON><PERSON><PERSON> balsavi<PERSON> 🎉", "app.utils.votingMethodUtils.votesCast": "<PERSON><PERSON><PERSON><PERSON> balsai", "app.utils.votingMethodUtils.votingClosed": "<PERSON><PERSON><PERSON><PERSON> baigtas", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b><PERSON><PERSON><PERSON><PERSON> balsas bus įskaitytas tik</b> tada, kai paspausite \"Pateikti\"", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON>, jū<PERSON>ų balsas jau atiduotas!</b> Galite patikrinti arba pakeisti savo pateiktą paraišką prieš <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON>, jū<PERSON><PERSON> balsas jau atiduotas!</b> To<PERSON>u bet kuriuo metu galite patikrinti arba pakeisti savo pateiktą balsą.", "components.UI.IdeaSelect.noIdeaAvailable": "Nėra jokių idėjų.", "components.UI.IdeaSelect.selectIdea": "Pasirinkite idėją", "containers.SiteMap.allProjects": "Visi projektai", "containers.SiteMap.customPageSection": "Pasirinktiniai pu<PERSON>i", "containers.SiteMap.folderInfo": "Daugiau informacijos", "containers.SiteMap.headSiteMapTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> | {orgName}", "containers.SiteMap.homeSection": "Bendra", "containers.SiteMap.pageContents": "Pus<PERSON>io turinys", "containers.SiteMap.profilePage": "Jūs<PERSON> profilio <PERSON>", "containers.SiteMap.profileSettings": "Jūsų nustatymai", "containers.SiteMap.projectEvents": "Rengin<PERSON><PERSON>", "containers.SiteMap.projectIdeas": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectInfo": "Informacija", "containers.SiteMap.projectPoll": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectSurvey": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectsArchived": "Archyvuoti projektai", "containers.SiteMap.projectsCurrent": "Dabartiniai projektai", "containers.SiteMap.projectsDraft": "Projektų projektai", "containers.SiteMap.projectsSection": "{orgName}projektai", "containers.SiteMap.signInPage": "Prisijunkite", "containers.SiteMap.signUpPage": "Užsiregistruokite", "containers.SiteMap.siteMapDescription": "Šiame puslapyje galite pereiti prie bet kokio platformoje esančio turinio.", "containers.SiteMap.siteMapTitle": "Dalyvavimo platformos {orgName}svet<PERSON><PERSON><PERSON>", "containers.SiteMap.successStories": "<PERSON>ėk<PERSON><PERSON><PERSON>", "containers.SiteMap.timeline": "Projekto etapai", "containers.SiteMap.userSpaceSection": "Jūsų paskyra", "containers.SubscriptionEndedPage.accessDenied": "<PERSON><PERSON><PERSON> ne<PERSON> prieigos", "containers.SubscriptionEndedPage.subscriptionEnded": "Šis puslapis pasiekiamas tik aktyvią prenumeratą turinčioms platformoms."}
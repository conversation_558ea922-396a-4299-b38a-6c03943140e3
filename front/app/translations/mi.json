{"EmailSettingsPage.initialUnsubscribeError": "There was an issue unsubscribing from this campaign, please try again.", "EmailSettingsPage.initialUnsubscribeLoading": "Your request is being processed, please wait...", "EmailSettingsPage.initialUnsubscribeSuccess": "You successfully unsubscribed from {campaignTitle}.", "UI.FormComponents.optional": "optional", "app.components.AssignBudgetControl.a11y_price": "Price:", "app.components.AssignBudgetControl.add": "Add", "app.components.Comments.cancel": "Cancel", "app.components.Comments.commentingDisabledInCurrentPhase": "Commenting is not possible in the current phase.", "app.components.Comments.commentingDisabledInactiveProject": "Commenting is not possible because this project is currently not active.", "app.components.Comments.commentingDisabledProject": "Commenting in this project is currently disabled.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} to comment.", "app.components.Comments.commentingMaybeNotPermitted": "Please {signInLink} to see what actions can be taken.", "app.components.Comments.invisibleTitleComments": "Comments", "app.components.Comments.official": "Official", "app.components.Comments.replyToComment": "Reply to comment", "app.components.Comments.reportAsSpam": "Report as spam", "app.components.Comments.seeOriginal": "See original", "app.components.Comments.seeTranslation": "See translation", "app.components.Comments.yourComment": "Your comment", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "An email with a confirmation code has been sent to {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Change your email.", "app.components.ConfirmationModal.codeInput": "Code", "app.components.ConfirmationModal.confirmationCodeSent": "New code sent", "app.components.ConfirmationModal.didntGetAnEmail": "Didn't receive an email?", "app.components.ConfirmationModal.foundYourCode": "Found your code?", "app.components.ConfirmationModal.goBack": "Go back.", "app.components.ConfirmationModal.sendEmailWithCode": "Send Email with Code", "app.components.ConfirmationModal.sendNewCode": "Send New Code.", "app.components.ConfirmationModal.verifyAndContinue": "Verify and Continue", "app.components.ConfirmationModal.wrongEmail": "Wrong email?", "app.components.ConsentManager.Banner.accept": "Accept", "app.components.ConsentManager.Banner.close": "<PERSON><PERSON>", "app.components.ConsentManager.Banner.mainText": "This platform uses cookies in accordance with our {policyLink}.", "app.components.ConsentManager.Banner.manage": "Manage", "app.components.ConsentManager.Banner.policyLink": "Kaupapa<PERSON> pihikete", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Advertising", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "We use this to personalise and measure the effectiveness of advertising campaigns of our website. We will not show any advertising on this platform, but the following services might offer you a personalised ad based on the pages you visit on our site.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Allow", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analytics", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "We use this tracking to understand better how you use the platform in order to learn and improve your navigation. This information is only used in mass analytics, and in no way to track individual people.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Go back", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Cancel", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Disallow", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Functional", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "This is required to enable and monitor basic functionalities of the website. Some tools listed here might not apply to you. Please read our cookie policy for more information.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Required", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "To have a functional platform, we save an authenticating cookie if you sign up, and the language in which you use this platform.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Save", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Your cookie preferences", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Tools", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Some fields were invalid. Please correct the errors and try again.", "app.components.ErrorBoundary.errorFormErrorGeneric": "An unknown error occurred while submitting your report. Please try again.", "app.components.ErrorBoundary.errorFormLabelClose": "<PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelComments": "What happened?", "app.components.ErrorBoundary.errorFormLabelEmail": "Email", "app.components.ErrorBoundary.errorFormLabelName": "Name", "app.components.ErrorBoundary.errorFormLabelSubmit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormSubtitle": "Our team has been notified.", "app.components.ErrorBoundary.errorFormSubtitle2": "If you’d like us to help, tell us what happened below.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Your feedback has been sent. Thank you!", "app.components.ErrorBoundary.errorFormTitle": "It looks like there is an issue.", "app.components.ErrorBoundary.genericErrorWithForm": "An error occurred and we cannot display this content. Please try again, or {openForm}", "app.components.ErrorBoundary.openFormText": "help us figure it out", "app.components.EventCard.a11y_lessContentVisible": "Less event information became visible.", "app.components.EventCard.a11y_moreContentVisible": "More event information became visible.", "app.components.EventCard.endsAt": "Ends at", "app.components.EventCard.showLess": "Show less", "app.components.EventCard.showMore": "Show more", "app.components.EventCard.startsAt": "Starts at", "app.components.FileUploader.a11y_file": "File:", "app.components.FileUploader.a11y_filesToBeUploaded": "Files to be uploaded: {fileNames}", "app.components.FileUploader.a11y_noFiles": "No files added.", "app.components.FileUploader.a11y_removeFile": "Remove this file", "app.components.FileUploader.fileInputDescription": "Click to select a file", "app.components.FileUploader.fileUploadLabel": "Attachments (max. 50MB)", "app.components.FileUploader.incorrect_extension": "{fileName} is not supported by our system, it will not be uploaded.", "app.components.FilterBoxes.a11y_allFilterSelected": "Selected status filter: all", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# submission} other {# submissions}}", "app.components.FilterBoxes.a11y_removeFilter": "Remove filter", "app.components.FilterBoxes.a11y_selectedFilter": "Selected status filter: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Selected {numberOfSelectedTopics, plural, =0 {zero tag filters} one {one tag filter} other {# tag filters}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "All", "app.components.FilterBoxes.areas": "Filter by area", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "Tags", "app.components.FiltersModal.filters": "Filters", "app.components.FolderFolderCard.a11y_folderDescription": "Folder description:", "app.components.FolderFolderCard.a11y_folderTitle": "Folder title:", "app.components.FolderFolderCard.archived": "Archived", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projects} one {# project} other {# projects}}", "app.components.GoBackButton.group.edit.goBack": "Go back", "app.components.IdeaCards.showMore": "Show more", "app.components.IdeasMap.a11y_hideIdeaCard": "Hide idea card.", "app.components.IdeasMap.a11y_mapTitle": "Map overview", "app.components.IdeasMap.clickOnMapToAdd": "Click on the map to add your input", "app.components.IdeasMap.noFilteredResults": "The filters you selected did not return any results", "app.components.IdeasMap.noResults": "No results found", "app.components.IdeasMap.or": "or", "app.components.IdeasMap.signInLinkText": "takiuru", "app.components.IdeasMap.signUpLinkText": "<PERSON><PERSON>u", "app.components.IdeasMap.tapOnMapToAdd": "Tap on the map to add your input", "app.components.IdeasShow.bodyTitle": "Description", "app.components.IdeasShow.deletePost": "Delete", "app.components.IdeasShow.editPost": "Edit", "app.components.IdeasShow.goBack": "Go back", "app.components.IdeasShow.moreOptions": "More options", "app.components.IdeasShow.or": "or", "app.components.IdeasShow.proposedBudgetTitle": "Proposed budget", "app.components.IdeasShow.reportAsSpam": "Report as spam", "app.components.IdeasShow.send": "Send", "app.components.IdeasShow.skipSharing": "Skip it, I'll do it later", "app.components.IdeasShowPage.sorryNoAccess": "Sorry, you can't access this page. You may need to log in or sign up to access it.", "app.components.PagesForm.editContent": "Content", "app.components.PagesForm.fileUploadLabel": "Attachments (max. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the bottom of this page.", "app.components.PagesForm.pageTitle": "Title", "app.components.PasswordInput.a11y_passwordHidden": "Password hidden", "app.components.PasswordInput.a11y_passwordVisible": "Password visible", "app.components.PasswordInput.a11y_strength1Password": "Poor password strength", "app.components.PasswordInput.a11y_strength2Password": "Weak password strength", "app.components.PasswordInput.a11y_strength3Password": "Medium password strength", "app.components.PasswordInput.a11y_strength4Password": "Strong password strength", "app.components.PasswordInput.a11y_strength5Password": "Very strong password strength", "app.components.PasswordInput.hidePassword": "Hide password", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "Too short (min. {minimumPasswordLength} characters)", "app.components.PasswordInput.minimumPasswordLengthError": "Provide a password that is at least {minimumPasswordLength} characters long", "app.components.PasswordInput.passwordEmptyError": "Enter your password", "app.components.PasswordInput.passwordStrengthTooltip1": "To make your password stronger:", "app.components.PasswordInput.passwordStrengthTooltip2": "Use a combination of non-consecutive lowercase letters, uppercase letters, digits, special characters and punctuation", "app.components.PasswordInput.passwordStrengthTooltip3": "Avoid common or easily guessed words", "app.components.PasswordInput.passwordStrengthTooltip4": "Increase the length", "app.components.PasswordInput.showPassword": "Show password", "app.components.PasswordInput.strength1Password": "Poor", "app.components.PasswordInput.strength2Password": "Weak", "app.components.PasswordInput.strength3Password": "Medium", "app.components.PasswordInput.strength4Password": "Strong", "app.components.PasswordInput.strength5Password": "Very strong", "app.components.PostCardsComponents.list": "List", "app.components.PostCardsComponents.map": "Map", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Add an official update", "app.components.PostComponents.OfficialFeedback.cancel": "Cancel", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Delete", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Are you sure you want to delete this official update?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Edit", "app.components.PostComponents.OfficialFeedback.lastEdition": "Last edited on {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Last update: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Official", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Choose how people see your name", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Official update author name", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Official update body text", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Official updates", "app.components.PostComponents.OfficialFeedback.postedOn": "Posted on {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Publish", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Show previous updates", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Give an update...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "Sorry, there was a problem", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Update message", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Your update was published successfully!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Support my contribution '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Support my contribution: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Support my contribution: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Support my idea '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Support my idea: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Support my idea: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "I posted a comment '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "I just posted a comment: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "I just posted a comment: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Support my proposed option '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Support my proposed option: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Support my option: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Support my project '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Support my project: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Support my project: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Join the discussion about this question '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Join the discussion: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Join the discussion: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "<PERSON><PERSON><PERSON><PERSON> te {postTitle} mō", "app.components.PostComponents.linkToHomePage": "Link to home page", "app.components.PostComponents.readMore": "Read more...", "app.components.ProjectArchivedIndicator.archivedProject": "Unfortunately, you can't participate in this project anymore because it has been archived", "app.components.ProjectCard.a11y_projectDescription": "Project description:", "app.components.ProjectCard.a11y_projectTitle": "Project title:", "app.components.ProjectCard.addYourOption": "Add your option", "app.components.ProjectCard.allocateYourBudget": "Allocate your budget", "app.components.ProjectCard.archived": "Archived", "app.components.ProjectCard.comment": "Comment", "app.components.ProjectCard.contributeYourInput": "Contribute your input", "app.components.ProjectCard.finished": "Finished", "app.components.ProjectCard.joinDiscussion": "Join the discussion", "app.components.ProjectCard.learnMore": "Learn more", "app.components.ProjectCard.submitAnIssue": "Submit a comment", "app.components.ProjectCard.submitYourIdea": "Submit your idea", "app.components.ProjectCard.submitYourProject": "Submit your project", "app.components.ProjectCard.takeThePoll": "Take the poll", "app.components.ProjectCard.takeTheSurvey": "Take the survey", "app.components.ProjectCard.viewTheContributions": "View the contributions", "app.components.ProjectCard.viewTheIdeas": "View the ideas", "app.components.ProjectCard.viewTheIssues": "View the comments", "app.components.ProjectCard.viewTheOptions": "View the options", "app.components.ProjectCard.viewTheProjects": "View the projects", "app.components.ProjectCard.viewTheQuestions": "View the questions", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# comments} other {# comments}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# contribution} other {# contributions}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {no ideas yet} one {# idea} other {# ideas}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# comment} other {# comments}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# option} other {# options}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# project} other {# projects}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# question} other {# questions}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projects} one {# project} other {# projects}}", "app.components.ProjectFolderCards.components.Topbar.all": "All", "app.components.ProjectFolderCards.components.Topbar.archived": "Archived", "app.components.ProjectFolderCards.components.Topbar.draft": "Draft", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filter by", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tag", "app.components.ProjectFolderCards.noProjectYet": "There are currently no open projects", "app.components.ProjectFolderCards.noProjectsAvailable": "No projects available", "app.components.ProjectFolderCards.showMore": "Show more", "app.components.ProjectFolderCards.stayTuned": "Check back again for new engagement opportunities", "app.components.ProjectFolderCards.tryChangingFilters": "Try changing the selected filters.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Also used in these cities:", "app.components.ProjectTemplatePreview.copied": "<PERSON>pied", "app.components.ProjectTemplatePreview.copyLink": "Copy link", "app.components.QuillEditor.alignCenter": "Center text", "app.components.QuillEditor.alignLeft": "<PERSON><PERSON> left", "app.components.QuillEditor.alignRight": "Align right", "app.components.QuillEditor.bold": "Bold", "app.components.QuillEditor.clean": "Remove formatting", "app.components.QuillEditor.customLink": "Add button", "app.components.QuillEditor.customLinkPrompt": "Enter link:", "app.components.QuillEditor.edit": "Edit", "app.components.QuillEditor.image": "Upload image", "app.components.QuillEditor.imageAltPlaceholder": "Short description of the image", "app.components.QuillEditor.italic": "Italic", "app.components.QuillEditor.link": "Add link", "app.components.QuillEditor.linkPrompt": "Enter link:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Ordered list", "app.components.QuillEditor.remove": "Remove", "app.components.QuillEditor.save": "Save", "app.components.QuillEditor.subtitle": "Subtitle", "app.components.QuillEditor.title": "Title", "app.components.QuillEditor.unorderedList": "Unordered list", "app.components.QuillEditor.video": "Add video", "app.components.QuillEditor.videoPrompt": "Enter video:", "app.components.QuillEditor.visitPrompt": "Visit link:", "app.components.Sharing.share": "Share", "app.components.Sharing.shareByEmail": "Share by email", "app.components.Sharing.shareOnFacebook": "Share on Facebook", "app.components.Sharing.shareOnTwitter": "Share on Twitter", "app.components.Sharing.shareThisFolder": "Share", "app.components.Sharing.shareThisProject": "Share this project", "app.components.Sharing.shareViaMessenger": "Share via Messenger", "app.components.Sharing.shareViaWhatsApp": "Share via WhatsApp", "app.components.SideModal.closeButtonAria": "<PERSON><PERSON>", "app.components.TopicsPicker.numberOfSelectedTopics": "Selected {numberOfSelectedTopics, plural, =0 {zero tags} one {one tag} other {# tags}}. {selectedTopicNames}", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "Translate", "app.components.Upload.errorImageMaxSizeExceeded": "The image you selected is larger than {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "One or several images you selected are larger than {maxFileSize}MB", "app.components.Upload.onlyOneImage": "You can only upload 1 image", "app.components.Upload.onlyXImages": "You can only upload {maxItemsCount} images", "app.components.Upload.remaining": "remaining", "app.components.Upload.uploadImageLabel": "Select an image (max. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Select one or more images", "app.components.UserName.deletedUser": "unknown author", "app.components.UserName.verified": "Verified", "app.components.VerificationModal.verifyBOSA": "Verify with itsme or eID", "app.components.VerificationModal.verifyClaveUnica": "Verify with Clave Unica", "app.components.VerificationModal.verifyYourIdentity": "Verify your identity", "app.components.VoteControl.budgetingFutureEnabled": "You can allocate your budget starting from {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Participatory budgeting is not currently enabled.", "app.components.VoteControl.budgetingNotPossible": "Making changes to your budget is not possible at this time.", "app.components.VoteControl.budgetingNotVerified": "Please {verifyAccountLink} to continue.", "app.components.admin.UserFilterConditions.addCondition": "Add a condition", "app.components.admin.UserFilterConditions.field_email": "Email", "app.components.admin.UserFilterConditions.field_lives_in": "Lives in", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Interacted with an input with status", "app.components.admin.UserFilterConditions.field_participated_in_project": "Contributed to project", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Posted something with tag", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Registration date", "app.components.admin.UserFilterConditions.field_role": "Role", "app.components.admin.UserFilterConditions.field_verified": "Verification", "app.components.admin.UserFilterConditions.predicate_begins_with": "begins with", "app.components.admin.UserFilterConditions.predicate_commented_in": "commented", "app.components.admin.UserFilterConditions.predicate_contains": "contains", "app.components.admin.UserFilterConditions.predicate_ends_on": "ends on", "app.components.admin.UserFilterConditions.predicate_has_value": "has value", "app.components.admin.UserFilterConditions.predicate_in": "performed any action", "app.components.admin.UserFilterConditions.predicate_is": "is", "app.components.admin.UserFilterConditions.predicate_is_admin": "is an admin", "app.components.admin.UserFilterConditions.predicate_is_after": "is after", "app.components.admin.UserFilterConditions.predicate_is_before": "is before", "app.components.admin.UserFilterConditions.predicate_is_checked": "is checked", "app.components.admin.UserFilterConditions.predicate_is_empty": "is empty", "app.components.admin.UserFilterConditions.predicate_is_equal": "is", "app.components.admin.UserFilterConditions.predicate_is_exactly": "is exactly", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "is larger than", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "is larger than or equal to", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "is a normal user", "app.components.admin.UserFilterConditions.predicate_is_one_of": "is one of", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "is a project manager", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "is smaller than", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "is smaller than or equal to", "app.components.admin.UserFilterConditions.predicate_is_verified": "is verified", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "does not begin with", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "didn't comment", "app.components.admin.UserFilterConditions.predicate_not_contains": "does not contain", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "does not end on", "app.components.admin.UserFilterConditions.predicate_not_has_value": "does not have value", "app.components.admin.UserFilterConditions.predicate_not_in": "didn't contribute", "app.components.admin.UserFilterConditions.predicate_not_is": "is not", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "is not an admin", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "is not checked", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "is not empty", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "is not", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "is not a normal user", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "is not one of", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "is not a project manager", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "is not verified", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "didn't post an input", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "didn't volunteer", "app.components.admin.UserFilterConditions.predicate_posted_input": "posted an input", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "volunteered", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Attribute", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Condition", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "Value", "app.components.form.ErrorDisplay.guidelinesLinkText": "our guidelines", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Start typing to search by user email or name...", "app.components.form.error": "Error", "app.components.form.locationGoogleUnavailable": "Couldn't load location field provided by google maps.", "app.components.form.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.form.submitApiError": "There was an issue submitting the form. Please check for any errors and try again.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Assessment method", "app.containers.AccessibilityStatement.changePreferencesButtonText": "you can change your preferences", "app.containers.AccessibilityStatement.changePreferencesText": "At any time, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceStatus": "Conformance status", "app.containers.AccessibilityStatement.contentConformanceExceptions": "We strive to make our content inclusive for all. However, in some instances there may be inaccessible content on the platform as outlined below:", "app.containers.AccessibilityStatement.email": "Email:", "app.containers.AccessibilityStatement.exception_1": "Our digital engagement platforms facilitate user-generated content posted by individuals and organisations. It is possible that PDFs, images or other file types including multi-media are uploaded to the platform as attachments or added into text fields by platform users. These documents may not be fully accessible.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "We welcome your feedback on the accessibility of this site. Please contact us via one of the following methods:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Feedback process", "app.containers.AccessibilityStatement.pageDescription": "A statement on the accessibility of this website", "app.containers.AccessibilityStatement.postalAddress": "Postal address:", "app.containers.AccessibilityStatement.responsiveness": "We aim to respond to feedback within 1-2 business days.", "app.containers.AccessibilityStatement.statusPageText": "status page", "app.containers.AccessibilityStatement.technologiesIntro": "The accessibility of this site relies on the following technologies to work:", "app.containers.AccessibilityStatement.technologiesTitle": "Technologies", "app.containers.AccessibilityStatement.title": "Accessibility Statement", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Create a smart group", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Users matching all of the following conditions will be automatically added to the group:", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Some conditions are incomplete", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Verification is disabled for your platform, remove the verification rule or contact support.", "app.containers.App.appMetaDescription": "Welcome to the online participation platform of {orgName}. \nExplore local projects and engage in the discussion!", "app.containers.App.loading": "Loading...", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Emails in this category", "app.containers.CampaignsConsentForm.messageError": "There was an error saving your email preferences.", "app.containers.CampaignsConsentForm.messageSuccess": "Your email preferences have been saved.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "What kinds of email notifications do you want to receive? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Notifications", "app.containers.CampaignsConsentForm.submit": "Save", "app.containers.Comments.a11y_commentDeleted": "Comment deleted", "app.containers.Comments.a11y_commentPosted": "Comment posted", "app.containers.Comments.addCommentError": "Something went wrong. Please try again later.", "app.containers.Comments.adminCommentDeletionCancelButton": "Cancel", "app.containers.Comments.adminCommentDeletionConfirmButton": "Delete this comment", "app.containers.Comments.cancelCommentEdit": "Cancel", "app.containers.Comments.childCommentBodyPlaceholder": "Write a reply...", "app.containers.Comments.commentCancelUpvote": "Undo", "app.containers.Comments.commentDeletedPlaceholder": "This comment has been deleted.", "app.containers.Comments.commentDeletionCancelButton": "Keep my comment", "app.containers.Comments.commentDeletionConfirmButton": "Delete my comment", "app.containers.Comments.commentReplyButton": "Reply", "app.containers.Comments.commentsSortTitle": "Sort comments by", "app.containers.Comments.confirmCommentDeletion": "Are you sure you want to delete this comment? There's no turning back!", "app.containers.Comments.deleteComment": "Delete", "app.containers.Comments.deleteReason_inappropriate": "It is inappropriate or offensive", "app.containers.Comments.deleteReason_irrelevant": "This is not relevant", "app.containers.Comments.deleteReason_other": "Other reason", "app.containers.Comments.editComment": "Edit", "app.containers.Comments.guidelinesLinkText": "our community guidelines", "app.containers.Comments.ideaCommentBodyPlaceholder": "Write your comment here", "app.containers.Comments.loadMoreComments": "Load more comments", "app.containers.Comments.loadingComments": "Loading comments...", "app.containers.Comments.loadingMoreComments": "Loading more comments...", "app.containers.Comments.profanityError": "Oops! It looks like your post contains some language that doesn’t meet {guidelinesLink}. We try to keep this a safe space for everyone. Please edit your input and try again.", "app.containers.Comments.publishComment": "Post your comment", "app.containers.Comments.reportAsSpamModalTitle": "Why do you want to report this as spam?", "app.containers.Comments.saveComment": "Save", "app.containers.Comments.signInLinkText": "takiuru", "app.containers.Comments.signInToComment": "Please {signInLink} to comment.", "app.containers.Comments.signUpLinkText": "<PERSON><PERSON>u", "app.containers.Comments.verifyIdentityLinkText": "Verify your identity", "app.containers.CookiePolicy.advertisingContent": "As {orgName}, we don’t use advertising tools on the participation platforms.", "app.containers.CookiePolicy.advertisingTitle": "Advertising", "app.containers.CookiePolicy.analyticsContents": "Analytics cookies track visitor behaviour, such as which pages are visited and for how long. They may also collect some technical data including browser information, approximate location and IP addresses. We only use this data internally to continue to improve the overall user experience and functioning of the platform. Such data may also be shared between Go Vocal and {orgName} to assess and improve engagement with projects on the platform. Note that the data is anonymous and used at an aggregated level - it does not identify you personally. However, it is possible that if this data were to be combined with other data sources, such identification could occur.", "app.containers.CookiePolicy.analyticsTitle": "Analytics cookies", "app.containers.CookiePolicy.cookiePolicyDescription": "A detailed explanation of how we use cookies on this platform", "app.containers.CookiePolicy.cookiePolicyTitle": "Kaupapa<PERSON> pihikete", "app.containers.CookiePolicy.essentialContent": "Some cookies are essential to ensure the proper functioning of this platform. These essential cookies are primarily used to authenticate your account when you visit the platform and to save your preferred language.", "app.containers.CookiePolicy.essentialTitle": "Essential cookies", "app.containers.CookiePolicy.externalContent": "Some of our pages may display content from external providers, e.g., YouTube or Typeform. We do not have control over these third-party cookies and viewing the content from these external providers may also result in cookies being installed on your device.", "app.containers.CookiePolicy.externalTitle": "External cookies", "app.containers.CookiePolicy.functionalContents": "Functional cookies may be enabled for visitors to receive notifications about updates and to access support channels directly from the platform.", "app.containers.CookiePolicy.functionalTitle": "Functional cookies", "app.containers.CookiePolicy.intro": "As most websites, we use cookies to optimise the experience you and other visitors have on this platform. Because we want to be fully transparent on the why & how of this cookie use, you’ll find all details below, in as clearly written wording as possible. Cookies used on our platform are never used to identify and track specific users, they don’t \"know who you are\". Important to stress though, is that they do track technical data, such as a browser info, approximate location and an IP address. Although they are not used in such a way, in combination with other data sources, this could lead to an identification.", "app.containers.CookiePolicy.manageCookiesDescription": "You can enable or disable analytics, marketing and functional cookies at any time in your cookie preferences. You can also manually or automatically delete any existing cookies via your internet browser. However, the cookies may be placed again after your consent upon any subsequent visits to this platform. If you do not delete the cookies, your cookie preferences are stored for 60 days, after which you will be asked again for your consent.", "app.containers.CookiePolicy.manageCookiesPreferences": "Go to your {manageCookiesPreferencesButtonText} to see a full list of 3rd party integrations used on this platform and to manage your preferences.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "cookie settings", "app.containers.CookiePolicy.manageCookiesTitle": "Managing your cookies", "app.containers.CookiePolicy.viewPreferencesButtonText": "<PERSON><PERSON>", "app.containers.CookiePolicy.viewPreferencesText": "The below cookie categories may not apply to all visitors or platforms; view your {viewPreferencesButton} for a full list of 3rd party integrations applicable to you.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "What do we use cookies for?", "app.containers.IdeaButton.addAContribution": "Add a contribution", "app.containers.IdeaButton.addAProject": "Add a project", "app.containers.IdeaButton.addAQuestion": "Add a question", "app.containers.IdeaButton.addAnOption": "Add an option", "app.containers.IdeaButton.postingDisabled": "New submissions are not currently being accepted", "app.containers.IdeaButton.postingInNonActivePhases": "New submissions can only be added in active phases.", "app.containers.IdeaButton.postingInactive": "New submissions are not currently being accepted.", "app.containers.IdeaButton.postingNoPermission": "New submissions are not currently being accepted", "app.containers.IdeaButton.postingNotYetPossible": "New submissions are not yet being accepted.", "app.containers.IdeaButton.signInLinkText": "takiuru", "app.containers.IdeaButton.signUpLinkText": "<PERSON><PERSON>u", "app.containers.IdeaButton.submitAnIssue": "Submit a comment", "app.containers.IdeaButton.submitYourIdea": "Submit your idea", "app.containers.IdeaButton.verificationLinkText": "Verify your identity now.", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {no comments} one {1 comment} other {# comments}}", "app.containers.IdeaCards.a11y_totalItems": "Total posts: {ideasCount}", "app.containers.IdeaCards.all": "All", "app.containers.IdeaCards.list": "List", "app.containers.IdeaCards.map": "Map", "app.containers.IdeaCards.newest": "Most recent", "app.containers.IdeaCards.noFilteredResults": "No results found. Please try a different filter or search term.", "app.containers.IdeaCards.oldest": "Oldest", "app.containers.IdeaCards.popular": "Most voted", "app.containers.IdeaCards.projectFilterTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "Random", "app.containers.IdeaCards.resetFilters": "Reset filters", "app.containers.IdeaCards.showXResults": "Show {ideasCount, plural, one {# result} other {# results}}", "app.containers.IdeaCards.sortTitle": "Sorting", "app.containers.IdeaCards.statusTitle": "Status", "app.containers.IdeaCards.topics": "Tags", "app.containers.IdeaCards.topicsTitle": "Tags", "app.containers.IdeaCards.trending": "Trending", "app.containers.IdeaCards.tryDifferentFilters": "No results found. Please try a different filter or search term.", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# result} other {# results}}", "app.containers.IdeasEditPage.contributionFormTitle": "Edit contribution", "app.containers.IdeasEditPage.editedPostSave": "Save", "app.containers.IdeasEditPage.fileUploadError": "One or more files failed to upload. Please check the file size and format and try again.", "app.containers.IdeasEditPage.formTitle": "Edit idea", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Edit your post. Add new and change old information.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Edit {postTitle} | {projectName}", "app.containers.IdeasEditPage.issueFormTitle": "Edit comment", "app.containers.IdeasEditPage.optionFormTitle": "Edit option", "app.containers.IdeasEditPage.projectFormTitle": "Edit project", "app.containers.IdeasEditPage.questionFormTitle": "Edit question", "app.containers.IdeasEditPage.save": "Save", "app.containers.IdeasEditPage.submitApiError": "There was an issue submitting the form. Please check for any errors and try again.", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Explore all input posted on the participation platform of {orgName}.", "app.containers.IdeasIndexPage.inputsPageTitle": "Posts", "app.containers.IdeasIndexPage.loadMore": "Load more...", "app.containers.IdeasIndexPage.loading": "Loading...", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "The contribution description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "The idea body must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "The contribution title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "The idea description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "The idea description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Please provide a description", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "The idea title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "The issue description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "The issue description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "The issue title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "The option description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "The option title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Please select at least one tag", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "The project description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "The project description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "The project title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Please enter a number", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Please enter a number", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "The question description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "The question description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "The question title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Please provide a title", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "The contribution description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "The contribution description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "The contribution title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "The contribution title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "The idea description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "The idea description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Please provide a title", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "The idea title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "The idea title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_includes_banned_words": "You may have used one or more words that are considered profanity by {guidelinesLink}. Please alter your text to remove any profanities that might be present.", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "The issue description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "The issue description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "The issue title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "The issue title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "The option description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "The option description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "The option title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "The option title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "The project description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "The project description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "The project title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "The project title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Please provide a description", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "The question description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "The question description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "The question title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "The question title must be at least 10 characters long", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Post a submission and join the conversation at {orgName}'s participation platform.", "app.containers.IdeasShow.MetaInformation.attachments": "Attachments", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} on {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Current status", "app.containers.IdeasShow.MetaInformation.location": "Location", "app.containers.IdeasShow.MetaInformation.postedBy": "Posted by", "app.containers.IdeasShow.MetaInformation.topics": "Tags", "app.containers.IdeasShow.commentCTA": "Add a comment", "app.containers.IdeasShow.contributionEmailSharingBody": "Support this contribution '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Support this contribution: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Thanks for submitting your contribution!", "app.containers.IdeasShow.contributionTwitterMessage": "Support this contribution: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Support this contribution: {postTitle}", "app.containers.IdeasShow.currentStatus": "Current status", "app.containers.IdeasShow.deletedUser": "unknown author", "app.containers.IdeasShow.ideaEmailSharingBody": "Support my idea '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Support my idea: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Support this idea: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Support this idea: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Support this comment: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Support this comment '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Support this comment: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Thanks for submitting your comment!", "app.containers.IdeasShow.issueTwitterMessage": "Support this comment: {postTitle}", "app.containers.IdeasShow.optionEmailSharingBody": "Support this option '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Support this option: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Your option has been successfully posted!", "app.containers.IdeasShow.optionTwitterMessage": "Support this option: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Support this option: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Support this project '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Support this project: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Thanks for submitting your project!", "app.containers.IdeasShow.projectTwitterMessage": "Support this project: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Support this project: {postTitle}", "app.containers.IdeasShow.questionEmailSharingBody": "Join the discussion about this question '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Join the discussion: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Your question has been successfully posted!", "app.containers.IdeasShow.questionTwitterMessage": "Join the discussion: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Join the discussion: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Why do you want to report this as spam?", "app.containers.IdeasShow.share": "Share", "app.containers.IdeasShow.sharingModalSubtitle": "Reach more people and make your voice heard.", "app.containers.IdeasShow.sharingModalTitle": "Thanks for submitting your idea!", "app.containers.Navbar.unverified": "Unverified", "app.containers.Navbar.verified": "Verified", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {no unviewed notifications} one {1 unviewed notification} other {# unviewed notifications}}", "app.containers.NotificationMenu.adminRightsReceived": "You're now an administrator of the platform", "app.containers.NotificationMenu.deletedUser": "Unknown author", "app.containers.NotificationMenu.error": "Couldn't load notifications", "app.containers.NotificationMenu.loadMore": "Load more...", "app.containers.NotificationMenu.loading": "Loading notifications...", "app.containers.NotificationMenu.mentionInComment": "{name} mentioned you in a comment", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} mentioned you in an official update", "app.containers.NotificationMenu.noNotifications": "You don't have any notifications yet", "app.containers.NotificationMenu.notificationsLabel": "Notifications", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} was assigned to you", "app.containers.NotificationMenu.projectModerationRightsReceived": "You're now a project manager of {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} entered a new phase", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} will enter a new phase on {phaseStartAt}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} reached the voting threshold", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} accepted your invitation", "app.containers.NotificationMenu.userReactedToYourComment": "{name} reacted to your comment", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} assigned {postTitle} to you", "app.containers.PasswordRecovery.emailError": "This doesn't look like a valid email", "app.containers.PasswordRecovery.emailLabel": "Email", "app.containers.PasswordRecovery.emailPlaceholder": "My email address", "app.containers.PasswordRecovery.helmetDescription": "Reset your password page", "app.containers.PasswordRecovery.helmetTitle": "Reset your password", "app.containers.PasswordRecovery.resetPassword": "Send a password reset link", "app.containers.PasswordRecovery.submitError": "We couldn't find an account linked to this email. You can try to sign up instead.", "app.containers.PasswordRecovery.subtitle": "Where can we send a link to choose a new password?", "app.containers.PasswordRecovery.title": "Password reset", "app.containers.PasswordReset.helmetDescription": "Reset your password page", "app.containers.PasswordReset.helmetTitle": "Reset your password", "app.containers.PasswordReset.passwordError": "The password must be at least 8 characters long", "app.containers.PasswordReset.passwordLabel": "Password", "app.containers.PasswordReset.passwordPlaceholder": "New password", "app.containers.PasswordReset.requestNewPasswordReset": "Request a new password reset", "app.containers.PasswordReset.submitError": "Something went wrong. Please try again later.", "app.containers.PasswordReset.title": "Reset your password", "app.containers.PasswordReset.updatePassword": "Confirm new password", "app.containers.ProjectFolderCards.allProjects": "<PERSON><PERSON> kaupapa katoa", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} is currently working on", "app.containers.ProjectFolderShowPage.editFolder": "Edit folder", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Information about this project", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Read more", "app.containers.ProjectFolderShowPage.seeLess": "See less", "app.containers.ProjectFolderShowPage.share": "Share", "app.containers.Projects.PollForm.formCompleted": "Thank you! Your response has been received.", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "This poll is not currently enabled", "app.containers.Projects.PollForm.pollDisabledNotPossible": "It is currently impossible to take this poll.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "The poll is no longer available since this project is no longer active.", "app.containers.Projects.PollForm.sendAnswer": "Send", "app.containers.Projects.a11y_phase": "Phase {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Phases overview", "app.containers.Projects.a11y_titleInputs": "All input submitted to this project", "app.containers.Projects.a11y_titleInputsPhase": "All input submitted in this phase", "app.containers.Projects.addedToBasket": "Added to your basket", "app.containers.Projects.allocateBudget": "Allocate your budget", "app.containers.Projects.archived": "Archived", "app.containers.Projects.basketSubmitted": "Your basket has been submitted!", "app.containers.Projects.contributions": "Contributions", "app.containers.Projects.currentPhase": "Current phase", "app.containers.Projects.editProject": "Edit project", "app.containers.Projects.endedOn": "Ended on {date}", "app.containers.Projects.events": "Events", "app.containers.Projects.header": "<PERSON><PERSON><PERSON>", "app.containers.Projects.ideas": "Ideas", "app.containers.Projects.information": "Information", "app.containers.Projects.invisibleTitlePhaseAbout": "About this phase", "app.containers.Projects.invisibleTitlePoll": "Take the poll", "app.containers.Projects.invisibleTitleSurvey": "Take the survey", "app.containers.Projects.issues": "Comments", "app.containers.Projects.location": "Location:", "app.containers.Projects.manageBasket": "Manage basket", "app.containers.Projects.meetMinBudgetRequirement": "Meet the minimum budget to submit your basket.", "app.containers.Projects.meetMinSelectionRequirement": "Meet the required selection to submit your basket.", "app.containers.Projects.minBudgetRequired": "Minimum budget required", "app.containers.Projects.myBasket": "My basket", "app.containers.Projects.navPoll": "Poll", "app.containers.Projects.navSurvey": "Survey", "app.containers.Projects.nextPhase": "Next phase", "app.containers.Projects.noItems": "You haven't selected any items yet", "app.containers.Projects.noPhaseSelected": "No phase selected", "app.containers.Projects.options": "Options", "app.containers.Projects.phases": "Phases", "app.containers.Projects.previousPhase": "Previous phase", "app.containers.Projects.project": "Project", "app.containers.Projects.projectTwitterMessage": "Make your voice heard! Participate in {projectName} | {orgName}", "app.containers.Projects.projects": "<PERSON><PERSON><PERSON>", "app.containers.Projects.questions": "Questions", "app.containers.Projects.readMore": "Read more", "app.containers.Projects.removeItem": "Remove item", "app.containers.Projects.requiredSelection": "Required selection", "app.containers.Projects.seeTheContributions": "See the contributions", "app.containers.Projects.seeTheIdeas": "See the ideas", "app.containers.Projects.seeTheIssues": "See the comments", "app.containers.Projects.seeTheOptions": "See the options", "app.containers.Projects.seeTheProjects": "See the projects", "app.containers.Projects.seeTheQuestions": "See the questions", "app.containers.Projects.share": "Share", "app.containers.Projects.shareThisProject": "Share this project", "app.containers.Projects.submitMyBasket": "Submit my basket", "app.containers.Projects.survey": "Survey", "app.containers.Projects.takeThePoll": "Take the poll", "app.containers.Projects.takeTheSurvey": "Take the survey", "app.containers.Projects.timeline": "Timeline", "app.containers.Projects.upcomingEvents": "Upcoming events", "app.containers.Projects.whatsAppMessage": "{projectName} | from the participation platform of {orgName}", "app.containers.Projects.yourBudget": "Your budget", "app.containers.ProjectsIndexPage.metaDescription": "Explore all ongoing projects of {orgName} to understand how you can participate.\n Come discuss local projects that matter most to you.", "app.containers.ProjectsIndexPage.pageTitle": "<PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "I want to volunteer", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Please {signInLink} or {signUpLink} first in order to volunteer for this activity", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "takiuru", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "<PERSON><PERSON>u", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "I withdraw my offer to volunteer", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {no volunteers} one {# volunteer} other {# volunteers}}", "app.containers.ProjectsShowPage.process.survey.survey": "Survey", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "To know if you can take part in this survey, please {logInLink} to the platform first.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "This survey can only be taken when this phase in the timeline is active.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "This survey is not currently enabled", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Taking this survey requires verification of your identity. {verificationLink}", "app.containers.SearchInput.removeSearchTerm": "Remove search term", "app.containers.SearchInput.searchAriaLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchPlaceholder": "<PERSON><PERSON>", "app.containers.SearchInput.searchTerm": "Search term: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect is the solution proposed by the French state to secure and simplify the sign up to more than 700 online services.", "app.containers.SignIn.or": "Or", "app.containers.SignIn.signInError": "The provided information is not correct. Click 'Forgot Password?' to reset your password.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Use FranceConnect to log in, sign up or verify your account.", "app.containers.SignIn.whatIsFranceConnect": "What is France Connect?", "app.containers.SignUp.backToSignUpOptions": "Go back to sign up options", "app.containers.SignUp.emailConsent": "By signing up, you agree to receive emails from this platform. You can select which emails you wish to receive in the 'My Settings' page.", "app.containers.SignUp.emptyFirstNameError": "Enter your first name", "app.containers.SignUp.emptyLastNameError": "Enter your last name", "app.containers.SignUp.firstNamesLabel": "First name", "app.containers.SignUp.goToLogIn": "Already have an account? {goToOtherFlowLink}", "app.containers.SignUp.lastNameLabel": "Last name", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Accept our privacy policy to proceed", "app.containers.SignUp.signUp2": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.skip": "Skip this step", "app.containers.SignUp.tacError": "Accept our terms and conditions to proceed", "app.containers.SignUp.thePrivacyPolicy": "the privacy policy", "app.containers.SignUp.theTermsAndConditions": "the terms and conditions", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {It appears you tried to sign up before without completing the process. Click Log In instead, using the credentials chosen during the previous attempt.} other {Something went wrong. Please try again later.}}", "app.containers.SiteMap.contributions": "Contributions", "app.containers.SiteMap.issues": "Comments", "app.containers.SiteMap.options": "Options", "app.containers.SiteMap.projects": "<PERSON><PERSON><PERSON>", "app.containers.SiteMap.questions": "Questions", "app.containers.SpamReport.buttonSave": "Report", "app.containers.SpamReport.buttonSuccess": "Success", "app.containers.SpamReport.inappropriate": "It is inappropriate or offensive", "app.containers.SpamReport.messageError": "There was an error submitting the form, please try again.", "app.containers.SpamReport.messageSuccess": "Your report has been sent", "app.containers.SpamReport.other": "Other reason", "app.containers.SpamReport.otherReasonPlaceholder": "Description", "app.containers.SpamReport.wrong_content": "This is not relevant", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Remove profile picture", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "To participate in projects for verified citizens.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Verify your identity", "app.containers.UsersEditPage.bio": "About you", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "You can't edit this field because it contains verified information.", "app.containers.UsersEditPage.buttonSuccessLabel": "Success", "app.containers.UsersEditPage.cancel": "Cancel", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Please click here to update your verification.", "app.containers.UsersEditPage.conditionsLinkText": "our conditions", "app.containers.UsersEditPage.contactUs": "Another reason to leave? {feedbackLink} and maybe we can help.", "app.containers.UsersEditPage.deleteAccountSubtext": "We are sorry to see you go.", "app.containers.UsersEditPage.deleteMyAccount": "Delete my account", "app.containers.UsersEditPage.deleteYourAccount": "Delete your account", "app.containers.UsersEditPage.deletionSection": "Delete your account", "app.containers.UsersEditPage.deletionSubtitle": "This action can not be undone. The content you published on the platform will be anonymised. If you wish to delete all your content, you can contact <NAME_EMAIL>.", "app.containers.UsersEditPage.email": "Email", "app.containers.UsersEditPage.feedbackLinkText": "Let us know", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "First name", "app.containers.UsersEditPage.h1": "Your account information", "app.containers.UsersEditPage.h1sub": "Edit your account information", "app.containers.UsersEditPage.image": "Avatar image", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Click to select a profile picture (max. 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "All settings for your profile", "app.containers.UsersEditPage.language": "Language", "app.containers.UsersEditPage.lastName": "Last name", "app.containers.UsersEditPage.loading": "Loading...", "app.containers.UsersEditPage.messageError": "We weren't able to save your profile. Try again later <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Your profile has been saved.", "app.containers.UsersEditPage.metaDescription": "This is the profile settings page of {firstName} {lastName} on the online participation platform of {tenantName}. Here you can verify your identity, edit your account information, delete your account and edit your email preferences.", "app.containers.UsersEditPage.noGoingBack": "Once you click this button, we will have no way to restore your account.", "app.containers.UsersEditPage.notificationsSubTitle": "What kinds of email notifications do you want to receive? ", "app.containers.UsersEditPage.notificationsTitle": "Email notifications", "app.containers.UsersEditPage.password": "Choose a new password", "app.containers.UsersEditPage.privacyReasons": "If you are worried about your privacy, you can read {conditionsLink}.", "app.containers.UsersEditPage.processing": "Sending...", "app.containers.UsersEditPage.reasonsToStayListTitle": "Before you go...", "app.containers.UsersEditPage.submit": "Save changes", "app.containers.UsersEditPage.tooManyEmails": "Receiving too many emails? You can manage your email preferences in your profile settings.", "app.containers.UsersEditPage.updateverification": "Did your official information change? {reverify<PERSON><PERSON>on}", "app.containers.UsersEditPage.user": "When do you want us to send you an email to notify you?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "You can participate in projects that are only accessible to verified citizens.", "app.containers.UsersEditPage.verifiedIdentityTitle": "You are a verified citizen", "app.containers.UsersEditPage.verifyNow": "Manatoko ināianei", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Input that this comment was posted in response to:", "app.containers.UsersShowPage.commentsWithCount": "Comments ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Edit my profile", "app.containers.UsersShowPage.invisibleTitlePostsList": "All input submitted by this participant", "app.containers.UsersShowPage.invisibleTitleUserComments": "All comments posted by this participant", "app.containers.UsersShowPage.loadMoreComments": "Load more comments", "app.containers.UsersShowPage.loadingComments": "Loading comments...", "app.containers.UsersShowPage.memberSince": "Member since {date}", "app.containers.UsersShowPage.noCommentsForUser": "This person hasn't posted any comments yet.", "app.containers.UsersShowPage.noCommentsForYou": "There are no comments here yet.", "app.containers.UsersShowPage.postsWithCount": "Submissions ({ideasCount})", "app.containers.UsersShowPage.seePost": "See submission", "app.containers.UsersShowPage.tryAgain": "An error has occurred, please try again later.", "app.containers.UsersShowPage.userShowPageMetaDescription": "This is the profile page of {firstName} {lastName} on the online participation platform of {orgName}. Here is an overview of all of their input.", "app.containers.VoteControl.close": "<PERSON><PERSON>", "app.containers.VoteControl.voteErrorTitle": "Something went wrong", "app.containers.app.navbar.admin": "Manage platform", "app.containers.app.navbar.allProjects": "<PERSON><PERSON> kaupapa katoa", "app.containers.app.navbar.ariaLabel": "Primary", "app.containers.app.navbar.closeMobileNavMenu": "Close mobile navigation menu", "app.containers.app.navbar.editProfile": "<PERSON><PERSON>", "app.containers.app.navbar.fullMobileNavigation": "Full mobile", "app.containers.app.navbar.logIn": "Takiuru", "app.containers.app.navbar.logoImgAltText": "{orgName} Home", "app.containers.app.navbar.myProfile": "Aku mahi", "app.containers.app.navbar.search": "<PERSON><PERSON>", "app.containers.app.navbar.signOut": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.errorWhenFetchingEvents": "An error occurred while loading events. Please try reloading the page.", "app.containers.eventspage.eventsPageDescription": "Show all events posted on {orgName}'s platform.", "app.containers.eventspage.filterDropdownTitle": "<PERSON><PERSON><PERSON>", "app.containers.eventspage.noPastEvents": "No past events to display", "app.containers.eventspage.noUpcomingOrOngoingEvents": "No upcoming or ongoing events are currently scheduled.", "app.containers.eventspage.pastEvents": "Past events", "app.containers.eventspage.upcomingAndOngoingEvents": "Upcoming and ongoing events", "app.containers.footer.accessibility-statement": "<PERSON><PERSON><PERSON><PERSON> whai wāhi", "app.containers.footer.ariaLabel": "Secondary", "app.containers.footer.cookie-policy": "Kaupapa<PERSON> pihikete", "app.containers.footer.cookieSettings": "<PERSON><PERSON><PERSON><PERSON> pihikete", "app.containers.footer.feedbackEmptyError": "<PERSON><PERSON> e waiho te āpure whakahoki kōrero kia puare ana.", "app.containers.footer.poweredBy": "He mea whakahiko e", "app.containers.footer.privacy-policy": "Kaupapa<PERSON> tūmata<PERSON>ga", "app.containers.footer.siteMap": "<PERSON><PERSON>", "app.containers.footer.terms-and-conditions": "<PERSON><PERSON> tikanga", "app.containers.landing.cityProjects": "<PERSON><PERSON><PERSON>", "app.containers.landing.completeProfile": "Whak<PERSON><PERSON> tō kōtaha ", "app.containers.landing.completeYourProfile": "<PERSON>u mai, {firstName}. Ko tēnei te wā hei whakaoti i tō kōtaha.", "app.containers.landing.createAccount": "<PERSON><PERSON><PERSON>", "app.containers.landing.defaultSignedInMessage": "{orgName} e whakarongo ana ki a koe. <PERSON><PERSON>u te wā, kōrero kia rongohia tō reo.", "app.containers.landing.doItLater": "Tai<PERSON><PERSON> e mahi ai", "app.containers.landing.new": "hou", "app.containers.landing.subtitleCity": "Nau mai ki te pūhara whai wāhitanga o {orgName}", "app.containers.landing.titleCity": "Me ahuahu tahi tātou i te ao mō āpōpō mō {orgName}", "app.containers.landing.twitterMessage": "<PERSON><PERSON><PERSON><PERSON> te {ideaTitle} mō", "app.containers.landing.upcomingEventsWidgetTitle": "Upcoming and ongoing events", "app.containers.landing.userDeletedSubtitle": "Ka taea e koe te hanga pūkete hou ahakoa te wā me toro atu rānei ki {contactLink} ki te whakamōhio i a mātou me pēhea e pai ake ai. ", "app.containers.landing.userDeletedSubtitleLinkText": "whakapā mai", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "<PERSON>a mukua tō pūkete", "app.containers.landing.userDeletionFailed": "Nā tētahi hapa i mukua ai tō pūkete. Kua whakamōhiotia mātou ki tēnei raru, ā, ka ngana mātou ki te whakatikatika. Me tarai anō koe ā muri nei.", "app.containers.landing.verifyNow": "Manatoko ināianei", "app.containers.landing.verifyYourIdentity": "Kia noho koe hei tangat whaikoha manatoko", "app.containers.landing.viewAllEventsText": "View all events", "app.errors.after_end_at": "The start date occurs after the end date", "app.errors.avatar_carrierwave_download_error": "Could not download avatar file.", "app.errors.avatar_carrierwave_integrity_error": "Avatar file is not of an allowed type.", "app.errors.avatar_carrierwave_processing_error": "Could not process avatar.", "app.errors.avatar_extension_blacklist_error": "The file extension of the avatar image is not allowed. Allowed extensions are: jpg, jpeg, gif and png.", "app.errors.avatar_extension_whitelist_error": "The file extension of the avatar image is not allowed. Allowed extensions are: jpg, jpeg, gif and png.", "app.errors.cannot_contain_ideas": "The participation method you selected does not support this type of post. Please edit your selection and try again.", "app.errors.cant_change_after_first_response": "You can no longer change this, since some users already responded", "app.errors.category_name_taken": "A category with this name already exists", "app.errors.confirmation_code_expired": "Code expired. Please request a new code.", "app.errors.confirmation_code_invalid": "Invalid confirmation code. Please check your email for the correct code or try 'Send New Code'", "app.errors.confirmation_code_too_many_resets": "You've resent the confirmation code too many times. Please contact us to receive an invitation code instead.", "app.errors.confirmation_code_too_many_retries": "You've tried too many times. Please request a new code or try changing your email.", "app.errors.email_already_active": "The email address {value} found in row {row} already belongs to a registered participant", "app.errors.email_already_invited": "The email address {value} found in row {row} was already invited", "app.errors.email_blank": "This cannot be empty", "app.errors.email_domain_blacklisted": "Please use a different email domain to register.", "app.errors.email_invalid": "Please use a valid email address.", "app.errors.email_taken": "An account with this email already exists. You can log in instead.", "app.errors.email_taken_by_invite": "{value} is already taken by a pending invite. Check your spam folder or contact {supportEmail} if you can't find it.", "app.errors.emails_duplicate": "One or more duplicate values for the email address {value} were found in the following row(s): {rows}", "app.errors.file_extension_whitelist_error": "The format of the file you tried to upload is not supported.", "app.errors.first_name_blank": "This cannot be empty", "app.errors.generics.blank": "This cannot be empty.", "app.errors.generics.invalid": "This doesn't look like a valid value", "app.errors.generics.taken": "This email already exists. Another account is linked to it.", "app.errors.generics.unsupported_locales": "This field does not support the current locale.", "app.errors.group_ids_unauthorized_choice_moderator": "As a project manager, you can only email to people that can access your project(s)", "app.errors.has_other_overlapping_phases": "Projects cannot have overlapping phases.", "app.errors.invalid_email": "The email {value} found in row {row} is not a valid email address", "app.errors.invalid_row": "An unknown error occurred while trying to process row {row}", "app.errors.is_not_timeline_project": "The current project does not support phases.", "app.errors.key_invalid": "The key can only contain letters, numbers and underscores(_)", "app.errors.last_name_blank": "This cannot be empty", "app.errors.locale_blank": "Please choose a language", "app.errors.locale_inclusion": "Please choose a supported language", "app.errors.malformed_admin_value": "The admin value {value} found in row {row} is not valid", "app.errors.malformed_groups_value": "The group {value} found in row {row} is not a valid group", "app.errors.no_invites_specified": "Could not find any email address.", "app.errors.password_blank": "This cannot be empty", "app.errors.password_too_short": "The password must be at least 8 characters long", "app.errors.slug_taken": "This project URL already exists. Please change the project slug to something else.", "app.errors.title_multiloc_blank": "The title cannot be empty.", "app.errors.token_invalid": "Password reset links can only be used once and are valid for one hour after being sent. {passwordResetLink}.", "app.errors.too_common": "This password can be easily guessed. Please choose a stronger password.", "app.errors.too_long": "Please choose a shorter password (max 72 characters)", "app.errors.too_short": "Please choose a password with at least 8 characters", "app.errors.unknown_group": "The group {value} found in row {row} is not a known group", "app.errors.unknown_locale": "The language {value} found in row {row} is not a configured language", "app.errors.unparseable_excel": "The selected Excel file could not be processed.", "app.errors.view_name_taken": "A view with this name already exists", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Inappropriate content was auto-detected in a post or comment", "app.modules.id_cow.cancel": "Cancel", "app.modules.id_cow.emptyFieldError": "This field cannot be empty.", "app.modules.id_cow.helpAltText": "Shows where to find the ID serial number on an identity card", "app.modules.id_cow.invalidIdSerialError": "Invalid ID serial", "app.modules.id_cow.invalidRunError": "Invalid RUN", "app.modules.id_cow.noMatchFormError": "No match was found.", "app.modules.id_cow.notEntitledFormError": "Not entitled.", "app.modules.id_cow.showCOWHelp": "Where can I find my ID Serial Number ?", "app.modules.id_cow.somethingWentWrongError": "We can't verify you because something went wrong", "app.modules.id_cow.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_cow.takenFormError": "Already taken.", "app.modules.id_cow.verifyCow": "Verify using COW", "app.modules.id_franceconnect.verificationButtonAltText": "Verify with FranceConnect", "app.modules.id_gent_rrn.cancel": "Cancel", "app.modules.id_gent_rrn.emptyFieldError": "This field cannot be empty.", "app.modules.id_gent_rrn.gentRrnHelp": "Your social security number is shown on the back of your digital identity card", "app.modules.id_gent_rrn.invalidRrnError": "Invalid social security number", "app.modules.id_gent_rrn.noMatchFormError": "We couldn't find back information on your social security number", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "We can't verify you because you live outside of Ghent", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "We can't verify you because you are younger than 14 years", "app.modules.id_gent_rrn.rrnLabel": "Social security number", "app.modules.id_gent_rrn.rrnTooltip": "We ask your social security number to verify whether you are a citizen of Ghent, older than 14 year old.", "app.modules.id_gent_rrn.showGentRrnHelp": "Where can I find my ID Serial Number ?", "app.modules.id_gent_rrn.somethingWentWrongError": "We can't verify you because something went wrong", "app.modules.id_gent_rrn.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.takenFormError": "Your social security number has already been used to verify another account", "app.modules.id_gent_rrn.verifyGentRrn": "Verify using GentRrn", "app.modules.id_id_card_lookup.cancel": "Cancel", "app.modules.id_id_card_lookup.emptyFieldError": "This field cannot be empty.", "app.modules.id_id_card_lookup.helpAltText": "ID card explanation", "app.modules.id_id_card_lookup.invalidCardIdError": "This id is not valid.", "app.modules.id_id_card_lookup.noMatchFormError": "No match was found.", "app.modules.id_id_card_lookup.showHelp": "Where can I find my ID Serial Number?", "app.modules.id_id_card_lookup.somethingWentWrongError": "We can't verify you because something went wrong", "app.modules.id_id_card_lookup.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.takenFormError": "Already taken.", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "You received admin rights over the \"{folderName}\" folder.", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Share", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | from the participation platform of {orgName}", "app.utils.FormattedCurrency.credits": "credits", "app.utils.FormattedCurrency.tokens": "tokens", "app.utils.errors.api_error_default.in": "Is not right", "app.utils.errors.default.ajv_error_birthyear_required": "Please fill in your year of birth", "app.utils.errors.default.ajv_error_date_any": "Please fill in a valid date", "app.utils.errors.default.ajv_error_domicile_required": "Please fill in your place of residence", "app.utils.errors.default.ajv_error_gender_required": "Please fill in your gender", "app.utils.errors.default.ajv_error_invalid": "Is invalid", "app.utils.errors.default.ajv_error_maxItems": "Can't include more than {limit, plural, one {# item} other {# items}}", "app.utils.errors.default.ajv_error_minItems": "Must include at least {limit, plural, one {# item} other {# items}}", "app.utils.errors.default.ajv_error_number_any": "Please fill in a valid number", "app.utils.errors.default.ajv_error_politician_required": "Please fill in whether you are a politician", "app.utils.errors.default.ajv_error_type": "Can't be blank", "app.utils.errors.default.api_error_accepted": "Must be accepted", "app.utils.errors.default.api_error_blank": "Can't be blank", "app.utils.errors.default.api_error_confirmation": "Doesn't match", "app.utils.errors.default.api_error_empty": "Can't be empty", "app.utils.errors.default.api_error_equal_to": "Is not right", "app.utils.errors.default.api_error_even": "Must be even", "app.utils.errors.default.api_error_exclusion": "Is reserved", "app.utils.errors.default.api_error_greater_than": "Is too small", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Is too small", "app.utils.errors.default.api_error_inclusion": "Is not included in the list", "app.utils.errors.default.api_error_invalid": "Is invalid", "app.utils.errors.default.api_error_less_than": "Is too big", "app.utils.errors.default.api_error_less_than_or_equal_to": "Is too big", "app.utils.errors.default.api_error_not_a_number": "Is not a number", "app.utils.errors.default.api_error_not_an_integer": "Must be an integer", "app.utils.errors.default.api_error_other_than": "Is not right", "app.utils.errors.default.api_error_present": "Must be blank", "app.utils.errors.default.api_error_too_long": "Is too long", "app.utils.errors.default.api_error_too_short": "Is too short", "app.utils.errors.default.api_error_wrong_length": "Is the wrong length", "app.utils.errors.defaultapi_error_.odd": "Must be odd", "containers.SiteMap.allProjects": "<PERSON><PERSON> kaupapa katoa", "containers.SiteMap.customPageSection": "Custom pages", "containers.SiteMap.folderInfo": "More info", "containers.SiteMap.homeSection": "General", "containers.SiteMap.pageContents": "Page content", "containers.SiteMap.profilePage": "Your profile page", "containers.SiteMap.profileSettings": "Your settings", "containers.SiteMap.projectEvents": "Events", "containers.SiteMap.projectIdeas": "Ideas", "containers.SiteMap.projectInfo": "Information", "containers.SiteMap.projectPoll": "Poll", "containers.SiteMap.projectSurvey": "Survey", "containers.SiteMap.projectsArchived": "Archived projects", "containers.SiteMap.projectsCurrent": "Current projects", "containers.SiteMap.projectsDraft": "Draft projects", "containers.SiteMap.projectsSection": "Projects of {orgName}", "containers.SiteMap.signInPage": "Sign in", "containers.SiteMap.signUpPage": "<PERSON><PERSON><PERSON>", "containers.SiteMap.siteMapDescription": "From this page, you can navigate to any content on the platform.", "containers.SiteMap.siteMapTitle": "Site map of the participation platform of {orgName}", "containers.SiteMap.successStories": "Success stories", "containers.SiteMap.timeline": "Project phases", "containers.SiteMap.userSpaceSection": "Your account", "containers.SubscriptionEndedPage.accessDenied": "You no longer have access", "containers.SubscriptionEndedPage.subscriptionEnded": "This page is only accessible for platforms with an active subscription."}
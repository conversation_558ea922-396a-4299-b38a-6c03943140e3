{"EmailSettingsPage.emailSettings": "Email settings", "EmailSettingsPage.initialUnsubscribeError": "Uuminnga paasititsiniaanermit taamaatitsiniarnerit ajornartorsiute<PERSON>q, misileqqilaaruk.", "EmailSettingsPage.initialUnsubscribeLoading": "Qinnuiginninnerit su<PERSON>oq, utaqqilaarit...", "EmailSettingsPage.initialUnsubscribeSuccess": "{campaignTitle}-mit taamaatits<PERSON>.", "UI.FormComponents.optional": "nammineq to<PERSON>", "app.closeIconButton.a11y_buttonActionMessage": "Close", "app.components.Areas.areaUpdateError": "An error occurred while saving your area. Please try again.", "app.components.Areas.followedArea": "Followed area: {areaTitle}", "app.components.Areas.followedTopic": "Followed topic: {topicTitle}", "app.components.Areas.topicUpdateError": "An error occurred while saving your topic. Please try again.", "app.components.Areas.unfollowedArea": "Unfollowed area: {areaTitle}", "app.components.Areas.unfollowedTopic": "Unfollowed topic: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "<PERSON><PERSON>", "app.components.AssignBudgetControl.add": "Ilannguguk", "app.components.AssignBudgetControl.added": "Added", "app.components.AssignMultipleVotesControl.addVote": "Add vote", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "You have distributed all of your credits.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "You have distributed the maximum number of credits for this option.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "You have distributed all of your points.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "You have distributed the maximum number of points for this option.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "You have distributed all of your tokens.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "You have distributed the maximum number of tokens for this option.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "You have distributed all of your votes.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "You have distributed the maximum number of votes for this option.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Voting is not available, since this phase is not active.", "app.components.AssignMultipleVotesControl.removeVote": "Remove vote", "app.components.AssignMultipleVotesControl.select": "Select", "app.components.AssignMultipleVotesControl.votesSubmitted1": "You have already submitted your vote. To modify it, click \"Modify your submission\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "You have already submitted your vote. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {credit} other {credits}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {point} other {points}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {vote} other {votes}}", "app.components.AssignVoteControl.maxVotesReached1": "You have distributed all of your votes.", "app.components.AssignVoteControl.phaseNotActive": "Voting is not available, since this phase is not active.", "app.components.AssignVoteControl.select": "Select", "app.components.AssignVoteControl.selected2": "Selected", "app.components.AssignVoteControl.voteForAtLeastOne": "Vote for at least 1 option", "app.components.AssignVoteControl.votesSubmitted1": "You have already submitted your vote. To modify it, click \"Modify your submission\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "You have already submitted your vote. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.AuthProviders.continue": "Continue", "app.components.AuthProviders.continueWithAzure": "Continue with {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Continue with Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Continue with Fake SSO", "app.components.AuthProviders.continueWithGoogle": "Continue with Google", "app.components.AuthProviders.continueWithHoplr": "Continue with Hoplr", "app.components.AuthProviders.continueWithIdAustria": "Continue with ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Continue with {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Continue with MitID", "app.components.AuthProviders.franceConnectMergingFailed": "An account already exists with this email address.{br}{br}You cannot access the platform using FranceConnect as the personal details do not match. To log in using FranceConnect, you will have to first change your first name or last name on this platform to match your official details.{br}{br}You can log in as you normally do below.", "app.components.AuthProviders.goToLogIn": "Already have an account? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Don't have an account? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Log in", "app.components.AuthProviders.logInWithEmail": "Log in with <PERSON><PERSON>", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "You must be the specified minimum age or above to be verified.", "app.components.AuthProviders.signUp2": "Sign up", "app.components.AuthProviders.signUpButtonAltText": "{loginMechanismName} atorlugu nalunaarit", "app.components.AuthProviders.signUpWithEmail": "Sign up with <PERSON><PERSON>", "app.components.AuthProviders.verificationRequired": "Verification required", "app.components.Author.a11yPostedBy": "Posted by", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 participant} other {{numberOfParticipants} participants}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} users", "app.components.AvatarBubbles.participant": "participant", "app.components.AvatarBubbles.participants1": "participants", "app.components.Comments.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "Immikkoortumi massakkut atuuttumi o<PERSON>ineq ajornarallarpoq.", "app.components.Comments.commentingDisabledInactiveProject": "<PERSON><PERSON><PERSON> ammannginnera <PERSON> o<PERSON>oqarsinn<PERSON>gi<PERSON>.", "app.components.Comments.commentingDisabledProject": "<PERSON><PERSON>ut uunga o<PERSON>ineq ajornarallarpoq.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} oqaasertaliipput.", "app.components.Comments.commentingMaybeNotPermitted": "Inuit tamarmik oqaaseqaateqarnissaminnut akuersissuteqanngillat. {signInLink} atulaaruk takuniarlugu akuersissutaateqarnersutit.", "app.components.Comments.inputsAssociatedWithProfile": "By default your submissions will be associated with your profile, unless you select this option.", "app.components.Comments.invisibleTitleComments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.leastRecent": "Least recent", "app.components.Comments.likeComment": "Like this comment", "app.components.Comments.mostLiked": "Most reactions", "app.components.Comments.mostRecent": "Most recent", "app.components.Comments.official": "Pisortatigoortoq", "app.components.Comments.postAnonymously": "Post anonymously", "app.components.Comments.replyToComment": "Oqaaseqaat akiuk", "app.components.Comments.reportAsSpam": "Spam-im<PERSON>", "app.components.Comments.seeOriginal": "Allaqqa<PERSON><PERSON>a ta<PERSON>", "app.components.Comments.seeTranslation": "Nutsernera ta<PERSON>ju<PERSON>", "app.components.Comments.yourComment": "<PERSON><PERSON>", "app.components.CommonGroundResults.divisiveDescription": "Statements where people agree and disagree equally:", "app.components.CommonGroundResults.divisiveTitle": "Divisive", "app.components.CommonGroundResults.majorityDescription": "More than 60% voted one way or the other on the following:", "app.components.CommonGroundResults.majorityTitle": "Majority", "app.components.CommonGroundResults.participantLabel": "participant", "app.components.CommonGroundResults.participantsLabel1": "participants", "app.components.CommonGroundResults.statementLabel": "statement", "app.components.CommonGroundResults.statementsLabel1": "statements", "app.components.CommonGroundResults.votesLabe": "vote", "app.components.CommonGroundResults.votesLabel1": "votes", "app.components.CommonGroundStatements.agreeLabel": "Agree", "app.components.CommonGroundStatements.disagreeLabel": "Disagree", "app.components.CommonGroundStatements.noMoreStatements": "There are no statements to respond to right now", "app.components.CommonGroundStatements.noResults": "There are no results to show yet. Please make sure you have participated in the Common Ground phase and check back here after.", "app.components.CommonGroundStatements.unsureLabel": "Unsure", "app.components.CommonGroundTabs.resultsTabLabel": "Results", "app.components.CommonGroundTabs.statementsTabLabel": "Statements", "app.components.CommunityMonitorModal.formError": "Encountered an error.", "app.components.CommunityMonitorModal.surveyDescription2": "This ongoing survey tracks how you feel about governance and public services.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Takes <1 minute} one {Takes 1 minute} other {Takes # minutes}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Emaileq uppernarsa<PERSON>uni isissutissaq ilinnut nassiunneqarpoq {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Email <PERSON>tiguk.", "app.components.ConfirmationModal.codeInput": "<PERSON><PERSON>", "app.components.ConfirmationModal.confirmationCodeSent": "Isissutissaq nutaa nassiuneqarp<PERSON>q", "app.components.ConfirmationModal.didntGetAnEmail": "<PERSON>aili suli tigun<PERSON>?", "app.components.ConfirmationModal.foundYourCode": "Isissutissaq nassaaraajuk?", "app.components.ConfirmationModal.goBack": "Uterit.", "app.components.ConfirmationModal.sendEmailWithCode": "<PERSON><PERSON><PERSON> isissutissarta<PERSON> na<PERSON>", "app.components.ConfirmationModal.sendNewCode": "Isissutissaq nutaaq nassiuguk.", "app.components.ConfirmationModal.verifyAndContinue": "<PERSON><PERSON><PERSON><PERSON> ing<PERSON>", "app.components.ConfirmationModal.wrongEmail": "<PERSON><PERSON>i kuk<PERSON>q?", "app.components.ConsentManager.Banner.accept": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.ariaButtonClose2": "Reject policy and close banner", "app.components.ConsentManager.Banner.close": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.mainText": "Iliuuseqarnikkut {policyLink}-erput akuerivat.", "app.components.ConsentManager.Banner.manage": "Aqutsinerit", "app.components.ConsentManager.Banner.policyLink": "Cookiet pillugit politik", "app.components.ConsentManager.Banner.reject": "Reject", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i inunnut tulluussaaniarluta ussassaarinermillu pisariillisaaneq nalilerniarlugu Uani platformimi ussassaarutinik takutitsissanngilagut, kisianni kiffartuussinerit ilai nittartakkatsinni iserfigisatit malillugit inummut tulluussakkamik ussassaarsinnaavaatit.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "A<PERSON><PERSON>ineq", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Suliap qanoq ittorpiaaneranik paasiniaanermi sakkussat", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Ujartuineq una platformimik atuinerit ilinniarniarlugu sumiiffiillu pitsanngorsarniarlugu atorparput.  Paasissutissaq una misissuinerinnarnut atorpoq inunnillu ataasiakkaanik ujartuisinnaanermut atorsinnaanngilaq.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Uterit", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Inerteqqutigiuk", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Atorsinnaassusaa", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Pisariaqarpoq nittartakkami tunngaviusumik atortut atuutilersinniarlugit nakkutiginiarlugillu. Uani sakkussat atorneqartut ilai immaqa ilinnuunngillat. Paasissutissanik amerlanerusunik piniaruit cookiet pillugit politik takujuk.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Piumasaqaataavoq", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>, at<PERSON><PERSON><PERSON><PERSON> oqaatsillu nittartakkami atorusutatit nalu<PERSON>, cookienik akuerisanik toqqorsisarpugut.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Toqqoruk", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Cookiet salliutitatit", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Sakkussat", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Content upload disclaimer", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "By uploading content, you declare that this content does not violate any regulations or rights of third parties, such as intellectual property rights, privacy rights, rights to trade secrets, and so on. Consequently, by uploading this content, you undertake to bear full and exclusive liability for all direct and indirect damages resulting from the uploaded content. Furthermore, you undertake to indemnify the platform owner and Go Vocal against any third party claims or liabilities against third parties, and any associated costs, that would arise or result from the content you uploaded.", "app.components.ContentUploadDisclaimer.onAccept": "I understand", "app.components.ContentUploadDisclaimer.onCancel": "Cancel", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Tell us why", "app.components.CustomFieldsForm.addressInputAriaLabel": "Address input", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Enter an address...", "app.components.CustomFieldsForm.adminFieldTooltip": "Field only visible to admins", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "All responses to this survey are anonymized.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "At least three points are required for a polygon.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "At least two points are required for a line.", "app.components.CustomFieldsForm.attachmentRequired": "At least one attachment is required", "app.components.CustomFieldsForm.authorFieldLabel": "Author", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Start typing to search by user email or name...", "app.components.CustomFieldsForm.back": "Back", "app.components.CustomFieldsForm.budgetFieldLabel": "Budget", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Click on the map to draw. Then, drag on points to move them.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Click on the map or type an address below to add your answer.", "app.components.CustomFieldsForm.confirm": "Confirm", "app.components.CustomFieldsForm.descriptionMinLength": "The description must be at least {min} characters long", "app.components.CustomFieldsForm.descriptionRequired": "The description is required", "app.components.CustomFieldsForm.fieldMaximumItems": "At most {maxSelections, plural, one {# option} other {# options}} can be selected for the field \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "At least {minSelections, plural, one {# option} other {# options}} can be selected for the field \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "The field \"{fieldName}\" is required", "app.components.CustomFieldsForm.fileSizeLimit": "The file size limit is {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "The image is required", "app.components.CustomFieldsForm.minimumCoordinates2": "A minimum of {numPoints} map points is required.", "app.components.CustomFieldsForm.notPublic1": "*This answer will only be shared with project managers, and not to the public.", "app.components.CustomFieldsForm.otherArea": "Somewhere else", "app.components.CustomFieldsForm.progressBarLabel": "Progress", "app.components.CustomFieldsForm.removeAnswer": "Remove answer", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Select as many as you like", "app.components.CustomFieldsForm.selectBetween": "*Select between {minItems} and {maxItems} options", "app.components.CustomFieldsForm.selectExactly2": "*Select exactly {selectExactly, plural, one {# option} other {# options}}", "app.components.CustomFieldsForm.selectMany": "*Choose as many as you like", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Tap on the map to draw. Then, drag on points to move them.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Tap on the map to draw.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Tap on the map to add your answer.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Tap on the map or type an address below to add your answer.", "app.components.CustomFieldsForm.tapToAddALine": "Tap to add a line", "app.components.CustomFieldsForm.tapToAddAPoint": "Tap to add a point", "app.components.CustomFieldsForm.tapToAddAnArea": "Tap to add an area", "app.components.CustomFieldsForm.titleMaxLength": "The title must be at most {max} characters long", "app.components.CustomFieldsForm.titleMinLength": "The title must be at least {min} characters long", "app.components.CustomFieldsForm.titleRequired": "The title is required", "app.components.CustomFieldsForm.topicRequired": "At least one tag is required", "app.components.CustomFieldsForm.typeYourAnswer": "Type your answer", "app.components.CustomFieldsForm.typeYourAnswerRequired": "It is required to type your answer", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Upload a zip file containing one or more shapefiles.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "If the location is not displayed among the options as you type, you can add valid coordinates in the format 'latitude, longitude' to specify a precise location (eg: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Allaffissat ilai atortussaanngillat. Kukkunerit aaqqilaarigit misileqqillugulu.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Nalunaaruternik nassiussiniarnermi ilisimaneqanngitsumik akornuteqalerpoq. Misileeqqigit.", "app.components.ErrorBoundary.errorFormLabelClose": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelComments": "<PERSON><PERSON><PERSON><PERSON><PERSON>?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-mail", "app.components.ErrorBoundary.errorFormLabelName": "Ateq", "app.components.ErrorBoundary.errorFormLabelSubmit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormSubtitle": "Suleqa<PERSON>gut p<PERSON>tinneqarput.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>.", "app.components.ErrorBoundary.errorFormSuccessMessage": "<PERSON><PERSON>p sun<PERSON>anik malugisaqarnerit nassiunneqarpoq Qujana<PERSON>!", "app.components.ErrorBoundary.errorFormTitle": "Ajornartorsiuteqarpasippugut.", "app.components.ErrorBoundary.genericErrorWithForm": "Akornuteqalerpoq uumallu imarisaa nuisissinnaanngilarput. Misileqqilaaruk imaluunniit {openForm}!", "app.components.ErrorBoundary.openFormText": "ajornartorsiut aaqqinniarlugu i<PERSON>ortigut", "app.components.ErrorToast.budgetExceededError": "You don't have enough budget", "app.components.ErrorToast.votesExceededError": "You don't have enough votes left", "app.components.EventAttendanceButton.forwardToFriend": "Forward to a friend", "app.components.EventAttendanceButton.maxRegistrationsReached": "The maximum number of event registrations has been reached. There are no spots left.", "app.components.EventAttendanceButton.register": "Register", "app.components.EventAttendanceButton.registered": "Registered", "app.components.EventAttendanceButton.seeYouThere": "See you there!", "app.components.EventAttendanceButton.seeYouThereName": "See you there, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Paasissutissat annikinnerusut saqqummerput.", "app.components.EventCard.a11y_moreContentVisible": "Paasissutissat annertunerusut saqqummerput.", "app.components.EventCard.a11y_readMore": "Read more about the \"{eventTitle}\" event.", "app.components.EventCard.endsAt": "Ends at", "app.components.EventCard.readMore": "Read more", "app.components.EventCard.showLess": "Annikinnerusoq takutiguk", "app.components.EventCard.showMore": "<PERSON><PERSON>", "app.components.EventCard.startsAt": "Starts at", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Upcoming and ongoing events in this project", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Upcoming and ongoing events in this phase", "app.components.FileUploader.a11y_file": "Fiili:", "app.components.FileUploader.a11y_filesToBeUploaded": "Fiilit ikkunneqartussat: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Fiilinik ilanngussisoqanngilaq.", "app.components.FileUploader.a11y_removeFile": "<PERSON>", "app.components.FileUploader.fileInputDescription": "<PERSON>ili<PERSON>k <PERSON>", "app.components.FileUploader.fileUploadLabel": "Kakkiussinerit (annerpaamik 50MB)", "app.components.FileUploader.file_too_large2": "Files larger than {maxSizeMb}MB are not permitted.", "app.components.FileUploader.incorrect_extension": "{fileName} systemitsinnit taperserneqanngilaq, ikkunneqassanngilaq.", "app.components.FilterBoxes.a11y_allFilterSelected": "Statusfilterit toqqakkat: tamarmik", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, 0 {# ilannguteqanngilaq} one {# ilanngussat} other {# ilanngussa}}", "app.components.FilterBoxes.a11y_removeFilter": "Immikkoortitaq peeruk", "app.components.FilterBoxes.a11y_selectedFilter": "Killiffinnik immikkoortiterinernik toqqaaneq: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Selected {numberOfSelectedTopics, plural, =0 {zero topic filters} one {one topic filter} other {# topic filters}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Tamakkerlutik", "app.components.FilterBoxes.areas": "Sumiiffikkaarlugit immikkoortiterineq", "app.components.FilterBoxes.inputs": "inputs", "app.components.FilterBoxes.noValuesFound": "No values available.", "app.components.FilterBoxes.showLess": "Show less", "app.components.FilterBoxes.showTagsWithNumber": "Show all ({numberTags})", "app.components.FilterBoxes.statusTitle": "<PERSON><PERSON><PERSON>", "app.components.FilterBoxes.topicsTitle": "Sammisassat imaat:", "app.components.FiltersModal.filters": "Immikkoortitikkat", "app.components.FolderFolderCard.a11y_folderDescription": "Mappip allaaserinera:", "app.components.FolderFolderCard.a11y_folderTitle": "Mappip taaguutaa:", "app.components.FolderFolderCard.archived": "Allagaasivimmut inissinneqarpoq", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {Suliassaqanngilaq} one {Suliassaq ataasiuvoq} other {Suliassat #}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "The field type cannot be changed once there are submissions.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Type", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Autosave", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Auto-saving is enabled by default when you open the form editor. Any time you close the field settings panel using the \"X\" button, it will automatically trigger a save.", "app.components.GanttChart.timeRange.month": "Month", "app.components.GanttChart.timeRange.quarter": "Quarter", "app.components.GanttChart.timeRange.timeRangeMultiyear": "Multi-year", "app.components.GanttChart.timeRange.year": "Year", "app.components.GanttChart.today": "Today", "app.components.GoBackButton.group.edit.goBack": "Uterit", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Go back to previous page", "app.components.HookForm.Feedback.errorTitle": "Ajornartorsiuteqarpoq", "app.components.HookForm.Feedback.submissionError": "<PERSON><PERSON><PERSON><PERSON>qigit, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>, attavigitigut", "app.components.HookForm.Feedback.submissionErrorTitle": "Uatsinni ajorna<PERSON>, utoqqatserpugut", "app.components.HookForm.Feedback.successMessage": "Formip ikku<PERSON> il<PERSON>", "app.components.HookForm.PasswordInput.passwordLabel": "Password", "app.components.HorizontalScroll.scrollLeftLabel": "<PERSON><PERSON> left.", "app.components.HorizontalScroll.scrollRightLabel": "<PERSON>roll right.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} ideas have loaded.", "app.components.IdeaCards.filters": "Filters", "app.components.IdeaCards.filters.mostDiscussed": "Most discussed", "app.components.IdeaCards.filters.newest": "New", "app.components.IdeaCards.filters.oldest": "Old", "app.components.IdeaCards.filters.popular": "Most liked", "app.components.IdeaCards.filters.random": "Random", "app.components.IdeaCards.filters.sortBy": "Sort by", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Sorting changed to: {currentSortType}", "app.components.IdeaCards.filters.trending": "Trending", "app.components.IdeaCards.showMore": "<PERSON><PERSON>", "app.components.IdeasMap.a11y_hideIdeaCard": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.components.IdeasMap.a11y_mapTitle": "Najoqquta<PERSON><PERSON>t ta<PERSON>", "app.components.IdeasMap.clickOnMapToAdd": "Aningaasaliiniaruit a<PERSON>utissaq tooruk ", "app.components.IdeasMap.clickOnMapToAddAdmin2": "As an admin, you can click on the map to add your input, even if this phase is not active.", "app.components.IdeasMap.filters": "Filters", "app.components.IdeasMap.multipleInputsAtLocation": "Multiple inputs at this location", "app.components.IdeasMap.noFilteredResults": "Aalajangersimasunik u<PERSON>lernermi na<PERSON>artoqanngilaq.", "app.components.IdeasMap.noResults": "Inerneranik ersittoqanngilaq", "app.components.IdeasMap.or": "im<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, no dislikes.} one {1 dislike.} other {, # dislikes.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, no likes.} one {, 1 like.} other {, # likes.}}", "app.components.IdeasMap.signInLinkText": "Iserneq", "app.components.IdeasMap.signUpLinkText": "nalunaars<PERSON>t", "app.components.IdeasMap.submitIdea2": "Submit input", "app.components.IdeasMap.tapOnMapToAdd": "Aningaasaliiniaruit a<PERSON>utissaq tooruk ", "app.components.IdeasMap.tapOnMapToAddAdmin2": "As an admin, you can tap on the map to add your input, even if this phase is not active.", "app.components.IdeasMap.userInputs2": "Inputs from participants", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, no comments} one {, 1 comment} other {, # comments}}", "app.components.IdeasShow.bodyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.deletePost": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.editPost": "<PERSON><PERSON>qi<PERSON><PERSON><PERSON><PERSON><PERSON>uk", "app.components.IdeasShow.goBack": "Uterit", "app.components.IdeasShow.moreOptions": "Qinigassat arlallit", "app.components.IdeasShow.or": "im<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.proposedBudgetTitle": "Missingersuusiaq", "app.components.IdeasShow.reportAsSpam": "Spam-im<PERSON>", "app.components.IdeasShow.send": "Nassiutiguk", "app.components.IdeasShow.skipSharing": "Kingusinn<PERSON>uk<PERSON><PERSON> si<PERSON>", "app.components.IdeasShowPage.signIn2": "Log in", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, qupper<PERSON><PERSON>t uunga isersinnaanngilatit. Iserniaruit iseriarlutit misilissinnaavat imaluunniit atuisumik pilersitsillutit.", "app.components.LocationInput.noOptions": "No options", "app.components.Modal.closeWindow": "Close window", "app.components.MultiSelect.clearButtonAction": "Clear selection", "app.components.MultiSelect.clearSearchButtonAction": "Clear search", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Enter a new email address", "app.components.PageNotFound.goBackToHomePage": "Back to the homepage", "app.components.PageNotFound.notFoundTitle": "Page not found", "app.components.PageNotFound.pageNotFoundDescription": "The requested page could not be found.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Provide content for at least one language", "app.components.PagesForm.editContent": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PagesForm.fileUploadLabel": "Kakkiussinerit (annerpaamik 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Fil-it 50 Mb-imit annertunerussanngillat. Fil-it ilanngunneqartut quppernermi matumani allermi nuisinneqassapput.", "app.components.PagesForm.navbarItemTitle": "Name in navbar", "app.components.PagesForm.pageTitle": "<PERSON><PERSON><PERSON>", "app.components.PagesForm.savePage": "Qupperneq toq<PERSON>uk", "app.components.PagesForm.saveSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> il<PERSON>.", "app.components.PagesForm.titleMissingOneLanguageError": "Provide title for at least one language", "app.components.Pagination.back": "Previous page", "app.components.Pagination.next": "Next page", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "You spent {votesCast}, which exceeds the limit of {votesLimit}. Please remove some items from your basket and try again.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} left", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "You need to spend a minimum of {votesMinimum} before you can submit your basket.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "You need to select at least one option before you can submit.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "You need to add something to your basket before you can submit it.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {No credits left} other {# out of {totalNumberOfVotes, plural, one {1 credit} other {# credits}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {No points left} other {# out of {totalNumberOfVotes, plural, one {1 point} other {# points}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {No tokens left} other {# out of {totalNumberOfVotes, plural, one {1 token} other {# tokens}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {No votes left} other {# out of {totalNumberOfVotes, plural, one {1 vote} other {# votes}} left}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# votes} one {# vote} other {# votes}} cast", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "You cast {votesCast} votes, which exceeds the limit of {votesLimit}. Please remove some votes and try again.", "app.components.ParticipationCTABars.addInput": "Add input", "app.components.ParticipationCTABars.allocateBudget": "Allocate your budget", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Your budget has been submitted successfully.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Akissuteqartoqarsinnaavoq", "app.components.ParticipationCTABars.poll": "Take the poll", "app.components.ParticipationCTABars.reviewDocument": "Review the document", "app.components.ParticipationCTABars.seeContributions": "See contributions", "app.components.ParticipationCTABars.seeEvents3": "See events", "app.components.ParticipationCTABars.seeIdeas": "See ideas", "app.components.ParticipationCTABars.seeInitiatives": "See initiatives", "app.components.ParticipationCTABars.seeIssues": "See issues", "app.components.ParticipationCTABars.seeOptions": "See options", "app.components.ParticipationCTABars.seePetitions": "See petitions", "app.components.ParticipationCTABars.seeProjects": "See projects", "app.components.ParticipationCTABars.seeProposals": "See proposals", "app.components.ParticipationCTABars.seeQuestions": "See questions", "app.components.ParticipationCTABars.submit": "Submit", "app.components.ParticipationCTABars.takeTheSurvey": "Take the survey", "app.components.ParticipationCTABars.userHasParticipated": "You have participated in this project.", "app.components.ParticipationCTABars.viewInputs": "View inputs", "app.components.ParticipationCTABars.volunteer": "Volunteer", "app.components.ParticipationCTABars.votesCounter.vote": "vote", "app.components.ParticipationCTABars.votesCounter.votes": "votes", "app.components.PasswordInput.a11y_passwordHidden": "Isert<PERSON>t n<PERSON>", "app.components.PasswordInput.a11y_passwordVisible": "Isertaat nuisavoq", "app.components.PasswordInput.a11y_strength1Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength2Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength3Password": "Isertaatip nalu<PERSON> na<PERSON>annarp<PERSON>q", "app.components.PasswordInput.a11y_strength4Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength5Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.hidePassword": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON><PERSON><PERSON>ar<PERSON><PERSON>  (min. {minimumPasswordLength} karakterer)", "app.components.PasswordInput.minimumPasswordLengthError": "Minnerpaamik {minimumPasswordLength} takissusilik", "app.components.PasswordInput.passwordEmptyError": "Isissutissat allaguk", "app.components.PasswordInput.passwordStrengthTooltip1": "Eqqoriaruminaannerusumik isertaateqarusukkuit:", "app.components.PasswordInput.passwordStrengthTooltip2": "Naqinnerit mikisut, an<PERSON><PERSON><PERSON>, kisi<PERSON><PERSON><PERSON>, nalu<PERSON><PERSON><PERSON>utit immikkuullarissut killiffilersuutillu imminnut atassuteqanngitsut akuleriisillugit atukkit", "app.components.PasswordInput.passwordStrengthTooltip3": "Oqaatsit naling<PERSON>aasut eqqoriaruminartulluunniit atornaveersaakkit", "app.components.PasswordInput.passwordStrengthTooltip4": "<PERSON><PERSON>nngor<PERSON>", "app.components.PasswordInput.showPassword": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength1Password": "Naammaginanngilaq", "app.components.PasswordInput.strength2Password": "Sakkukippoq", "app.components.PasswordInput.strength3Password": "Na<PERSON><PERSON>anna<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength4Password": "Pitsaavoq", "app.components.PasswordInput.strength5Password": "Pitsaalluinnarpoq", "app.components.PostCardsComponents.list": "Alluttuiffik", "app.components.PostCardsComponents.map": "<PERSON><PERSON>p assinga", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Tamanut paasissutissat kingulliit ilanngullugit takussutissiamik ilanngussigit", "app.components.PostComponents.OfficialFeedback.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Ilumoorpit tamanut paasissutissat kingulliit ilanngullugit takussutissiaq nunguterniarpiuk?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Aaqqissuuguk", "app.components.PostComponents.OfficialFeedback.lastEdition": "Kingullermik nutarterneqarpoq ulloq {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Paasissutissat kingulliit ilanngullugit takussutissiaq kingulleq: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Pisortatigoortoq", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Inuit atit qanoq takus<PERSON>eraat toqqaruk", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Pisortatigoortumik paasissutissat kingulliit ilanngullugit takussutissiami allagaqartap atia", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Pisortatigoortumik paasissutissat kingulliit ilanngullugit takussutissiami allakkap allagartaa", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Pisortatigoortumik paasissutissat kingulliit ilanngullugit takussutissiat", "app.components.PostComponents.OfficialFeedback.postedOn": "<PERSON><PERSON>a <PERSON>imavoq {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Siusinnerusukkut paasissutissat kingulliit ilanngullugit takussutissiat nuisikkit", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Paasissutissat kingulliit ilanngullugit takussutissiamik saqqummiussigit...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON> a<PERSON>", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Allakkat nutarsaruk", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Paasissutissat kingulliit ilanngullugit takussutissiaq saqqummersinneqarpoq!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "<PERSON><PERSON><PERSON><PERSON><PERSON> '{postTitle}' {postUrl}-imi taperse<PERSON>", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> taperseruk: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> taperseruk: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Isumassarsiaq una qanoq igaajuk? <PERSON>ajuk oqa<PERSON> uani {postUrl} tusaaneqarumallutit siammaruk!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Isumassarsiara taperseruk: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "{tenantName, select, DeloitteDK {Isumarnik ikkussigit: {postTitle}.} other {Isumassarsiara taperseruk: {postTitle}.}}", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Innuttaasunit siunnersuut una qanoq igaajuk? <PERSON><PERSON><PERSON> oqa<PERSON> uani {postUrl} tusaaneqarumallutit siammaruk!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Innuttaasutut siunnersuutiga taperseruk: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Support my initiative: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "{postTitle} -imi a<PERSON><PERSON>t {postUrl} ikkuppara!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Ajornartorsiummik ikkusseqqammerpunga: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Ajornartorsiummik ikkusseqqammerpunga: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Qinigassiissutigisara '{postTitle}' {postUrl}-imi taperseruk!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Qinigassiissutigisara taperseruk: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Qinigassara taperseruk: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Support my petition '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Support my petition: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Support my petition: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Sul<PERSON> '{postTitle} {postUrl}-imi taperseruk!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Suliaq una taperseruk: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Suliaq una taperseruk: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Support my proposal '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Support my proposal: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "I just posted a proposal for {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "{postUrl}-imi apeqqut question '{postTitle}' -ip oqa<PERSON>t peqata<PERSON>t", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Oqallinnermut peqataagit: {postTitle}", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Oqallinnermut peqataagit: {postTitle}", "app.components.PostComponents.SharingModalContent.twitterMessage": "{tenantName, select, DeloitteDK {Isumarnik ikkussigit {postTitle}} other {Uani {postTitle} toqqaagit}}", "app.components.PostComponents.linkToHomePage": "Nittartakkamut iserneq <PERSON>", "app.components.PostComponents.readMore": "Atuarnerugit...", "app.components.PostComponents.topics": "Topics", "app.components.ProjectArchivedIndicator.archivedProject": "{tenantName, select, DeloitteDK {Ajoraluartumik uunga nunarsuaq tamakkerlugu anguniakkanut peqataasinnaajunnaarputit, toqqorneqarmat} other {Ajoraluartumik uunga suliamut peqataasinnaajunnaarputit, toqqorneqarmat}}", "app.components.ProjectArchivedIndicator.previewProject": "Draft project:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Visible only to moderators and those with a preview link.", "app.components.ProjectCard.a11y_projectDescription": "<PERSON><PERSON><PERSON><PERSON>:", "app.components.ProjectCard.a11y_projectTitle": "Suliniutip taaguutaa", "app.components.ProjectCard.addYourOption": "Qinigassiamik suliannik ilanngussigit", "app.components.ProjectCard.allocateYourBudget": "Missingersuutit iluanaarutigiuk ", "app.components.ProjectCard.archived": "Allagaasivimmut inissinneqarpoq", "app.components.ProjectCard.comment": "Oqaaseqaat", "app.components.ProjectCard.contributeYourInput": "Ilanngussat <PERSON>annguguk", "app.components.ProjectCard.finished": "Naammassereerpoq", "app.components.ProjectCard.joinDiscussion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> peqa<PERSON>t", "app.components.ProjectCard.learnMore": "Paasisaqarnerugit ", "app.components.ProjectCard.reaction": "Reaction", "app.components.ProjectCard.readTheReport": "Read the report", "app.components.ProjectCard.reviewDocument": "Review the document", "app.components.ProjectCard.submitAnIssue": "Ajornartorsiummik ilanngussigit", "app.components.ProjectCard.submitYourIdea": "Isumassarsiannik ilanngussigit", "app.components.ProjectCard.submitYourInitiative": "Submit your initiative", "app.components.ProjectCard.submitYourPetition": "Submit your petition", "app.components.ProjectCard.submitYourProject": "Suliannik ilanngussigit", "app.components.ProjectCard.submitYourProposal": "Submit your proposal", "app.components.ProjectCard.takeThePoll": "Taasigit", "app.components.ProjectCard.takeTheSurvey": "Apeqqutit akikkit ", "app.components.ProjectCard.viewTheContributions": "Ilanngussat takukkit", "app.components.ProjectCard.viewTheIdeas": "{tenantName, select, DeloitteDK {Ilanngussaq nuisiguk} other {Isumassarsiat takutikkit}}", "app.components.ProjectCard.viewTheInitiatives": "View the initiatives", "app.components.ProjectCard.viewTheIssues": "Ajornartorsiutit takukkit", "app.components.ProjectCard.viewTheOptions": "Qinigassat takukkit ", "app.components.ProjectCard.viewThePetitions": "View the petitions", "app.components.ProjectCard.viewTheProjects": "Suliat takukkit", "app.components.ProjectCard.viewTheProposals": "View the proposals", "app.components.ProjectCard.viewTheQuestions": "Apeqqutit takukkit", "app.components.ProjectCard.vote": "Vote", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# oqaaseqaatit} other {# oqaaseqaatit}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, 0 {# ilanngussaqanngilaq} one {# ilanngussaq} other {# ilanngussat}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {no isumassarsiat} one {# isumassarsiat} other {# isumassarsiat}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# initiatives} one {# initiative} other {# initiatives}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, 0 {# ajornartorsiuteqanngilaq} one {# ajornartorsiut} other {# ajornartorsiutit}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, 0 {# qinigassaqanngilaq} one {# qinigassaq} other {# qinigassat}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petitions} one {# petition} other {# petitions}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, 0 {# suliassaqanngilaq} one {# suliaq} other {# suliat}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# proposals} one {# proposal} other {# proposals}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, 0 {# apeqquteqanngilaq} one {# apeqqut} other {# apeqqutit}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# comments} one {# comments} other {# comments}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# inputs} one {# input} other {# inputs}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projects} one {# project} other {# projects}}", "app.components.ProjectFolderCards.components.Topbar.all": "All", "app.components.ProjectFolderCards.components.Topbar.archived": "Archived", "app.components.ProjectFolderCards.components.Topbar.draft": "Draft", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Una malillugu filtereruk", "app.components.ProjectFolderCards.components.Topbar.published2": "Published", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tag", "app.components.ProjectFolderCards.noProjectYet": "Suli su<PERSON>gi<PERSON>", "app.components.ProjectFolderCards.noProjectsAvailable": "Suliniutinik ersittoqanngilaq", "app.components.ProjectFolderCards.showMore": "<PERSON><PERSON>", "app.components.ProjectFolderCards.stayTuned": "Suliassamik ta<PERSON>vo<PERSON>, malinnaagit.", "app.components.ProjectFolderCards.tryChangingFilters": "Filterit toqqakkat allanngortillugit misiliguk.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Illoqarfinni ukunani a<PERSON>ma at<PERSON>qarpoq:", "app.components.ProjectTemplatePreview.copied": "Assilivoq", "app.components.ProjectTemplatePreview.copyLink": "<PERSON><PERSON> assiliuk", "app.components.QuillEditor.alignCenter": "Allagaq qitiutiguk", "app.components.QuillEditor.alignLeft": "Saamimmut <PERSON>", "app.components.QuillEditor.alignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.bold": "Silittoq", "app.components.QuillEditor.clean": "Formattering peeruk", "app.components.QuillEditor.customLink": "Toortaammik ilanngussigit", "app.components.QuillEditor.customLinkPrompt": "<PERSON>:", "app.components.QuillEditor.edit": "Aaqqissuuguk", "app.components.QuillEditor.image": "Assimik n<PERSON>uss<PERSON>", "app.components.QuillEditor.imageAltPlaceholder": "<PERSON><PERSON><PERSON> na<PERSON><PERSON> nassuiarnera", "app.components.QuillEditor.italic": "Uingasoq", "app.components.QuillEditor.link": "Innersuussissummik ilanngussigit", "app.components.QuillEditor.linkPrompt": "<PERSON>:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Allattorsimaffik aalajangeruk", "app.components.QuillEditor.remove": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.save": "Toqqoruk", "app.components.QuillEditor.subtitle": "Qulequtaaraq", "app.components.QuillEditor.title": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.unorderedList": "Allattorsimaffik aalajangersimanngitsoq", "app.components.QuillEditor.video": "Videomik ilanngussigit", "app.components.QuillEditor.videoPrompt": "Video tooruk", "app.components.QuillEditor.visitPrompt": "Innersuuss<PERSON> al<PERSON>:", "app.components.ReactionControl.completeProfileToReact": "Complete your profile to react", "app.components.ReactionControl.dislike": "Dislike", "app.components.ReactionControl.dislikingDisabledMaxReached": "You've reached your maximum number of dislikes in {projectName}", "app.components.ReactionControl.like": "Like", "app.components.ReactionControl.likingDisabledMaxReached": "You've reached your maximum number of likes in {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Reacting will be enabled once this phase starts", "app.components.ReactionControl.reactingDisabledPhaseOver": "It's no longer possible to react in this phase", "app.components.ReactionControl.reactingDisabledProjectInactive": "You can no longer react to ideas in {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Reacting is currently not enabled for this project", "app.components.ReactionControl.reactingNotPermitted": "Reacting is only enabled for certain groups", "app.components.ReactionControl.reactingNotSignedIn": "Sign in to react.", "app.components.ReactionControl.reactingPossibleLater": "Reacting will start on {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Verify your identity in order to react.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Event date: {startDate} at {startTime} to {endDate} at {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Event date: {eventDate} from {startTime} to {endTime}.", "app.components.Sharing.linkCopied": "<PERSON> k<PERSON>", "app.components.Sharing.or": "or", "app.components.Sharing.share": "Siammarteruk", "app.components.Sharing.shareByEmail": "E-mailikkut <PERSON>", "app.components.Sharing.shareByLink": "Copy link", "app.components.Sharing.shareOnFacebook": "<PERSON><PERSON><PERSON>", "app.components.Sharing.shareOnTwitter": "<PERSON><PERSON><PERSON>", "app.components.Sharing.shareThisEvent": "Share this event", "app.components.Sharing.shareThisFolder": "Siammarteruk", "app.components.Sharing.shareThisProject": "{tenantName, select, DeloitteDK {Una siammarteruk} other {Suliaq una siammarteruk}}", "app.components.Sharing.shareViaMessenger": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Sharing.shareViaWhatsApp": "Share via WhatsApp", "app.components.SideModal.closeButtonAria": "<PERSON><PERSON><PERSON><PERSON>", "app.components.StatusModule.futurePhase": "You are viewing a phase that has not started yet. You will be able to participate when the phase starts.", "app.components.StatusModule.modifyYourSubmission1": "Modify your submission", "app.components.StatusModule.submittedUntil3": "Your vote may be submitted until", "app.components.TopicsPicker.numberOfSelectedTopics": "Selected {numberOfSelectedTopics, plural, =0 {zero topics} one {one topic} other {# topics}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Expand image", "app.components.UI.MoreActionsMenu.moreOptions": "More options", "app.components.UI.MoreActionsMenu.showMoreActions": "Show more actions", "app.components.UI.NewLabel.new": "NEW", "app.components.UI.PhaseFilter.noAppropriatePhases": "No appropriate phases found for this project", "app.components.UI.RemoveImageButton.a11y_removeImage": "Remove", "app.components.UI.TranslateButton.original": "Allagaq peqqaartoq", "app.components.UI.TranslateButton.translate": "Nutseruk", "app.components.Unauthorized.additionalInformationRequired": "Additional information is required for you to participate.", "app.components.Unauthorized.completeProfile": "Complete profile", "app.components.Unauthorized.completeProfileTitle": "Complete your profile to participate", "app.components.Unauthorized.noPermission": "You don't have permission to view this page", "app.components.Unauthorized.notAuthorized": "Sorry, you're not authorized to access this page.", "app.components.Upload.errorImageMaxSizeExceeded": "Asseq <PERSON> {maxFileSize}MB -mit angineruvoq", "app.components.Upload.errorImagesMaxSizeExceeded": "Asseq ataaseq arlallilluunniit angissutsimut akuerisaasumut {maxFileSize} Mb asseq ataatsimut, anner<PERSON>allaarput", "app.components.Upload.onlyOneImage": "<PERSON><PERSON><PERSON> ataaseq nu<PERSON>", "app.components.Upload.onlyXImages": "Assit {maxItemsCount} nuussinnaavatit", "app.components.Upload.remaining": "sinner<PERSON><PERSON>", "app.components.Upload.uploadImageLabel": "<PERSON><PERSON><PERSON><PERSON> (ann. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Assimik ataatsimik amerlanernilluunniit to<PERSON>t", "app.components.UpsellTooltip.tooltipContent": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.components.UserName.anonymous": "Anonymous", "app.components.UserName.anonymousTooltip2": "This user has decided to anonymize their contribution", "app.components.UserName.authorWithNoNameTooltip": "Your name has been autogenerated because you have not entered your name. Please update your profile if you would like to change it.", "app.components.UserName.deletedUser": "allagaqartoq ilisi<PERSON>q", "app.components.UserName.verified": "Uppernarseriigaq", "app.components.VerificationModal.verifyAuth0": "Verify with NemID", "app.components.VerificationModal.verifyBOSA": "Itsme imaluunniit eID atorlugu uppernarsaruk", "app.components.VerificationModal.verifyBosaFas": "Itsme imaluunniit eID atorlugu uppernarsaruk", "app.components.VerificationModal.verifyClaveUnica": "Clave Unica atorlugu uppernarsaruk", "app.components.VerificationModal.verifyFakeSSO": "SSO ilumuunngitsoq atuutile<PERSON>iguk", "app.components.VerificationModal.verifyIdAustria": "Verify with ID Austria", "app.components.VerificationModal.verifyKeycloak": "ID-<PERSON>en atorlugu atuutilersiguk", "app.components.VerificationModal.verifyNemLogIn": "Verify with MitID", "app.components.VerificationModal.verifyTwoday2": "Verify with BankID or Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "{tenantName, select, DeloitteDK {Atuisuunerit uppernarsaruk} other {Kinaassutsit uppernarsaruk}}", "app.components.VoteControl.budgetingFutureEnabled": "Missingersuutitit {enabledFromDate}-imiit agguataarsinnaavatit.", "app.components.VoteControl.budgetingNotPermitted": "Missingersuusiornerit peqataasunut eqimattanut a<PERSON>naq atuuppoq.", "app.components.VoteControl.budgetingNotPossible": "Missingersuusiannut allannguisinnaanngikkallarputit.", "app.components.VoteControl.budgetingNotVerified": "Ingerlaqqinniaruit {verifyAccountLink}-erit.", "app.components.VoteInputs._shared.currencyLeft1": "You have {budgetLeft} / {totalBudget} left", "app.components.VoteInputs._shared.numberOfCreditsLeft": "You have {votesLeft, plural, =0 {no credits left} other {# out of {totalNumberOfVotes, plural, one {1 credit} other {# credits}} left}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "You have {votesLeft, plural, =0 {no points left} other {# out of {totalNumberOfVotes, plural, one {1 point} other {# points}} left}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "You have {votesLeft, plural, =0 {no tokens left} other {# out of {totalNumberOfVotes, plural, one {1 token} other {# tokens}} left}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "You have {votesLeft, plural, =0 {no votes left} other {# out of {totalNumberOfVotes, plural, one {1 vote} other {# votes}} left}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "You have already submitted your budget. To modify it, click \"Modify your submission\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "You have already submitted your budget. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budgeting is not available, since this phase is not active.", "app.components.VoteInputs.single.youHaveVotedForX2": "You have voted for {votes, plural, =0 {# options} one {# option} other {# options}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "This means you will lose all data associated with this input, like comments, reactions and votes. This action cannot be undone.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Are you sure you want to delete this input?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "Cancel", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Confirm", "app.components.admin.SlugInput.resultingURL": "Resulting URL", "app.components.admin.SlugInput.slugTooltip": "The slug is the unique set of words at the end of page's web address, or URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "URL allanngortikkukku, URL pisoqaq atorlugu quppernermut linkit atorsinnaajunnaassapput.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "Piumasaqaammik ilanngussigit", "app.components.admin.UserFilterConditions.field_email": "E-mail", "app.components.admin.UserFilterConditions.field_event_attendance": "Event registrations", "app.components.admin.UserFilterConditions.field_follow": "Follow", "app.components.admin.UserFilterConditions.field_lives_in": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Community monitor survey", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Inputi statusiliorfigaa", "app.components.admin.UserFilterConditions.field_participated_in_project": "Suliniummut <PERSON>anngus<PERSON>rp<PERSON>", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Tag-eraluni allagarsiisimavoq", "app.components.admin.UserFilterConditions.field_registration_completed_at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ul<PERSON>a", "app.components.admin.UserFilterConditions.field_role": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_verified": "Uppernarsaaneq", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideation", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Proposals", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "is not registered for any of these events", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "is not registered for any event", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "is registered for one of these events", "app.components.admin.UserFilterConditions.predicate_attends_something": "is registered for at least one event", "app.components.admin.UserFilterConditions.predicate_begins_with": "ima a<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_commented_in": "oqaaseqaateqarpoq", "app.components.admin.UserFilterConditions.predicate_contains": "<PERSON><PERSON>i", "app.components.admin.UserFilterConditions.predicate_ends_on": "naammassivoq", "app.components.admin.UserFilterConditions.predicate_has_value": "naleqarpoq", "app.components.admin.UserFilterConditions.predicate_in": "iliuuseqarpoq", "app.components.admin.UserFilterConditions.predicate_is": "-voq", "app.components.admin.UserFilterConditions.predicate_is_admin": "aqutsisuuvoq", "app.components.admin.UserFilterConditions.predicate_is_after": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_before": "siulianiuvoq", "app.components.admin.UserFilterConditions.predicate_is_checked": "takuneqareerpoq", "app.components.admin.UserFilterConditions.predicate_is_empty": "imaqanngilaq", "app.components.admin.UserFilterConditions.predicate_is_equal": "is", "app.components.admin.UserFilterConditions.predicate_is_exactly": "eqqorpoq", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "u<PERSON><PERSON> an<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "uannga annertuneruvoq assigaluguluunniit", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "nalinginnaasumik at<PERSON>uvoq", "app.components.admin.UserFilterConditions.predicate_is_not_area": "excludes area", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "excludes folder", "app.components.admin.UserFilterConditions.predicate_is_not_input": "excludes input", "app.components.admin.UserFilterConditions.predicate_is_not_project": "excludes project", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "excludes topic", "app.components.admin.UserFilterConditions.predicate_is_one_of": "ukununnga ilaavoq", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "one of the areas", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "one of the folders", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "one of the inputs", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "one of the projects", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "one of the topics", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "sulini<PERSON><PERSON> aq<PERSON>voq", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "uannga minneruvoq", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "uannga minneruvoq assigaluguluunniit", "app.components.admin.UserFilterConditions.predicate_is_verified": "uppernarsarneqareerpoq", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "ima a<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "oqaaseqaateqanngilaq", "app.components.admin.UserFilterConditions.predicate_not_contains": "una imarinngilaa", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "uani na<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_has_value": "naleqanngilaq", "app.components.admin.UserFilterConditions.predicate_not_in": "ilanngussaqanngilaq", "app.components.admin.UserFilterConditions.predicate_not_is": "-nng<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "aq<PERSON><PERSON><PERSON><PERSON>ngilaq", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "takuneqanngilaq", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "is not", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "nalinginnaasumik at<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "ukun<PERSON><PERSON> il<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "sulini<PERSON><PERSON> aq<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "uppernarsarneqanngilaq", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "<PERSON><PERSON><PERSON> tun<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "didn't react to comment", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "didn't react to input", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "didn't register to an event", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "has not taken survey", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "kajumissutsimik sulisussatut nalunaanngilaq", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "didn't participate in voting", "app.components.admin.UserFilterConditions.predicate_nothing": "nothing", "app.components.admin.UserFilterConditions.predicate_posted_input": "<PERSON><PERSON><PERSON> tunnius<PERSON>", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reacted to comment", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reacted to input", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "registered to an event", "app.components.admin.UserFilterConditions.predicate_something": "something", "app.components.admin.UserFilterConditions.predicate_taken_survey": "has taken survey", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "kajumissutsimik sulisussatut nalunaarpoq", "app.components.admin.UserFilterConditions.predicate_voted_in3": "participated in voting", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Piginnaasaq", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Piumasaqaat", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "<PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "You won't get notifications on your contribution", "app.components.anonymousParticipationModal.cancel": "Cancel", "app.components.anonymousParticipationModal.continue": "Continue", "app.components.anonymousParticipationModal.participateAnonymously": "Participate anonymously", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "This will safely <b>hide your profile</b> from admins, project managers and other residents for this specific contribution so that nobody is able to link this contribution to you. Anonymous contributions cannot be edited, and are considered final.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Making our platform safe for every user is a top priority for us. Words matter, so please be kind to each other.", "app.components.avatar.titleForAccessibility": "Profile of {fullName}", "app.components.customFields.mapInput.removeAnswer": "Remove answer", "app.components.customFields.mapInput.undo": "Undo", "app.components.customFields.mapInput.undoLastPoint": "Undo last point", "app.components.followUnfollow.follow": "Follow", "app.components.followUnfollow.followADiscussion": "Follow the discussion", "app.components.followUnfollow.followTooltipInputPage2": "Following triggers email updates about status changes, official updates, and comments. You can {unsubscribeLink} at any time.", "app.components.followUnfollow.followTooltipProjects2": "Following triggers email updates about project changes. You can {unsubscribeLink} at any time.", "app.components.followUnfollow.unFollow": "Unfollow", "app.components.followUnfollow.unsubscribe": "unsubscribe", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "malittarisassavut", "app.components.form.ErrorDisplay.next": "Next", "app.components.form.ErrorDisplay.previous": "Previous", "app.components.form.ErrorDisplay.save": "Save", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Atuisup aqqanik mailimilluunniit ujaasiniaruit allallutit aallartiguk...", "app.components.form.anonymousSurveyMessage2": "All responses to this survey are anonymized.", "app.components.form.backToInputManager": "Back to input manager", "app.components.form.backToProject": "Back to project", "app.components.form.components.controls.mapInput.removeAnswer": "Remove answer", "app.components.form.components.controls.mapInput.undo": "Undo", "app.components.form.components.controls.mapInput.undoLastPoint": "Undo last point", "app.components.form.controls.addressInputAriaLabel": "Address input", "app.components.form.controls.addressInputPlaceholder6": "Enter an address...", "app.components.form.controls.adminFieldTooltip": "Field only visible to admins", "app.components.form.controls.allStatementsError": "An answer must be selected for all statements.", "app.components.form.controls.back": "Back", "app.components.form.controls.clearAll": "Clear all", "app.components.form.controls.clearAllScreenreader": "Clear all answers from above matrix question", "app.components.form.controls.clickOnMapMultipleToAdd3": "Click on the map to draw. Then, drag on points to move them.", "app.components.form.controls.clickOnMapToAddOrType": "Click on the map or type an address below to add your answer.", "app.components.form.controls.confirm": "Confirm", "app.components.form.controls.cosponsorsPlaceholder": "Start typing a name to search", "app.components.form.controls.currentRank": "Current rank:", "app.components.form.controls.minimumCoordinates2": "A minimum of {numPoints} map points is required.", "app.components.form.controls.noRankSelected": "No rank selected", "app.components.form.controls.notPublic1": "*This answer will only be shared with project managers, and not to the public.", "app.components.form.controls.optionalParentheses": "(optional)", "app.components.form.controls.rankingInstructions": "Drag and drop to rank options.", "app.components.form.controls.selectAsManyAsYouLike": "*Qanorlunniit amerlatigisunik <PERSON>t", "app.components.form.controls.selectBetween": "*Select between {minItems} and {maxItems} options", "app.components.form.controls.selectExactly2": "*Select exactly {selectExactly, plural, one {# option} other {# options}}", "app.components.form.controls.selectMany": "*Qanorlunniit amerlatigisunik <PERSON>t", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Tap on the map to draw. Then, drag on points to move them.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Tap on the map to draw.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Tap on the map to add your answer.", "app.components.form.controls.tapOnMapToAddOrType": "Tap on the map or type an address below to add your answer.", "app.components.form.controls.tapToAddALine": "Tap to add a line", "app.components.form.controls.tapToAddAPoint": "Tap to add a point", "app.components.form.controls.tapToAddAnArea": "Tap to add an area", "app.components.form.controls.uploadShapefileInstructions": "* Upload a zip file containing one or more shapefiles.", "app.components.form.controls.validCordinatesTooltip2": "If the location is not displayed among the options as you type, you can add valid coordinates in the format 'latitude, longitude' to specify a precise location (eg: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} out of {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} out of {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} out of {total}, where {maxValue} is {maxLabel}", "app.components.form.error": "Error", "app.components.form.locationGoogleUnavailable": "Sumiiffik google mapsikkut nanineqanngilaq.", "app.components.form.progressBarLabel": "Survey progress", "app.components.form.submit": "Submit", "app.components.form.submitApiError": "There was an issue submitting the form. Please check for any errors and try again.", "app.components.form.verifiedBlocked": "You can't edit this field because it contains verified information", "app.components.formBuilder.Page": "Page", "app.components.formBuilder.accessibilityStatement": "accessibility statement", "app.components.formBuilder.addAnswer": "Add answer", "app.components.formBuilder.addStatement": "Add statement", "app.components.formBuilder.agree": "Agree", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "If you have access to our AI package, you will be able to summarise and categorise text responses with AI", "app.components.formBuilder.askFollowUpToggleLabel": "Ask follow up", "app.components.formBuilder.bad": "Bad", "app.components.formBuilder.buttonLabel": "Button label", "app.components.formBuilder.buttonLink": "Button link", "app.components.formBuilder.cancelLeaveBuilderButtonText": "Cancel", "app.components.formBuilder.category": "Category", "app.components.formBuilder.chooseMany": "Choose many", "app.components.formBuilder.chooseOne": "Choose one", "app.components.formBuilder.close": "Close", "app.components.formBuilder.closed": "Closed", "app.components.formBuilder.configureMap": "Configure map", "app.components.formBuilder.confirmLeaveBuilderButtonText": "Yes, I want to leave", "app.components.formBuilder.content": "Content", "app.components.formBuilder.continuePageLabel": "Continues to", "app.components.formBuilder.cosponsors": "Co-sponsors", "app.components.formBuilder.default": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.defaultContent": "Default content", "app.components.formBuilder.delete": "Delete", "app.components.formBuilder.deleteButtonLabel": "Delete", "app.components.formBuilder.description": "Description", "app.components.formBuilder.disabledBuiltInFieldTooltip": "This has already been added in the form. Default content may only be used once.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Adding custom content is not part of your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.components.formBuilder.disagree": "Disagree", "app.components.formBuilder.displayAsDropdown": "Display as dropdown", "app.components.formBuilder.displayAsDropdownTooltip": "Display the options in a dropdown. If you have many options, this is recommended.", "app.components.formBuilder.done": "Done", "app.components.formBuilder.drawArea": "Draw area", "app.components.formBuilder.drawRoute": "Draw route", "app.components.formBuilder.dropPin": "Drop pin", "app.components.formBuilder.editButtonLabel": "Edit", "app.components.formBuilder.emptyImageOptionError": "Provide at least 1 answer. Please note that each answer has to have a title.", "app.components.formBuilder.emptyOptionError": "Provide at least 1 answer", "app.components.formBuilder.emptyStatementError": "Provide at least 1 statement", "app.components.formBuilder.emptyTitleError": "Provide a question title", "app.components.formBuilder.emptyTitleMessage": "Provide a title for all the answers", "app.components.formBuilder.emptyTitleStatementMessage": "Provide a title for all the statements", "app.components.formBuilder.enable": "Enable", "app.components.formBuilder.errorMessage": "There is a problem, please fix the issue to be able to save your changes", "app.components.formBuilder.fieldGroup.description": "Description (optional)", "app.components.formBuilder.fieldGroup.title": "Title (optional)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Currently, answers to these questions are only available in the exported excel file on Input Manager, and not visible to the users.", "app.components.formBuilder.fieldLabel": "Answer choices", "app.components.formBuilder.fieldLabelStatement": "Statements", "app.components.formBuilder.fileUpload": "File upload", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Map-based page", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Embed map as context or ask location based questions to participants.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "For optimal user experience, we do not recommend adding point, route, or area questions to map-based pages.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normal page", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Survey mapping features are not included in your current license. Reach out to your GovSuccess Manager to learn more.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Page type", "app.components.formBuilder.formEnd": "Form end", "app.components.formBuilder.formField.cancelDeleteButtonText": "Cancel", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "Yes, delete page", "app.components.formBuilder.formField.copyNoun": "Copy", "app.components.formBuilder.formField.copyVerb": "Copy", "app.components.formBuilder.formField.delete": "Delete", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Deleting this page will also delete the logic associated with it. Are you sure you want to delete it?", "app.components.formBuilder.formField.deleteResultsInfo": "This cannot be undone", "app.components.formBuilder.goToPageInputLabel": "Then next page is:", "app.components.formBuilder.good": "Good", "app.components.formBuilder.helmetTitle": "Form builder", "app.components.formBuilder.imageFileUpload": "Image upload", "app.components.formBuilder.invalidLogicBadgeMessage": "Invalid logic", "app.components.formBuilder.labels2": "Labels (optional)", "app.components.formBuilder.labelsTooltipContent2": "Choose optional labels for any of the linear scale values.", "app.components.formBuilder.lastPage": "Ending", "app.components.formBuilder.layout": "Layout", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Are you sure you want to leave?", "app.components.formBuilder.leaveBuilderText": "You have unsaved changes. Please save before leaving. If you leave, you'll lose your changes.", "app.components.formBuilder.limitAnswersTooltip": "When turned on, respondents need to select the specified number of answers to proceed.", "app.components.formBuilder.limitNumberAnswers": "Limit number of answers", "app.components.formBuilder.linePolygonMapWarning2": "Line and polygon drawing may not meet accessibility standards. More information can be found in the {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Linear scale", "app.components.formBuilder.locationDescription": "Location", "app.components.formBuilder.logic": "Logic", "app.components.formBuilder.logicAnyOtherAnswer": "Any other answer", "app.components.formBuilder.logicConflicts.conflictingLogic": "Conflicting logic", "app.components.formBuilder.logicConflicts.interQuestionConflict": "This page contains questions that lead to different pages. If participants answer multiple questions, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "This page has multiple logic rules applied: multi-select question logic, page-level logic, and inter-question logic. When these conditions overlap, question logic will take precedence over page logic, and the furthest page will be shown. Review the logic to ensure it aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "This page contains a multi-select question where options lead to different pages. If participants select multiple options, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "This page contains a multi-select question where options lead to different pages and has questions that lead to other pages. The furthest page will be shown if these conditions overlap. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "This page contains a multi-select question where options lead to different pages and has logic set at both the page and question level. Question logic will take precedence, and the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "This page has logic set at both the page level and question level. Question logic will take precedence over page-level logic. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "This page has logic set at both the page and question levels, and multiple questions direct to different pages. Question logic will take precedence, and the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicNoAnswer2": "Not answered", "app.components.formBuilder.logicPanelAnyOtherAnswer": "If any other answer", "app.components.formBuilder.logicPanelNoAnswer": "If not answered", "app.components.formBuilder.logicValidationError": "Logic may not link to prior pages", "app.components.formBuilder.longAnswer": "Long answer", "app.components.formBuilder.mapConfiguration": "Map configuration", "app.components.formBuilder.mapping": "Mapping", "app.components.formBuilder.mappingNotInCurrentLicense": "Survey mapping features are not included in your current license. Reach out to your GovSuccess Manager to learn more.", "app.components.formBuilder.matrix": "Matrix", "app.components.formBuilder.matrixSettings.columns": "Columns", "app.components.formBuilder.matrixSettings.rows": "Rows", "app.components.formBuilder.multipleChoice": "Multiple choice", "app.components.formBuilder.multipleChoiceHelperText": "If multiple options lead to different pages and participants select more than one, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.multipleChoiceImage": "Image choice", "app.components.formBuilder.multiselect.maximum": "Amerlanerpaaffik", "app.components.formBuilder.multiselect.minimum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.neutral": "Neutral", "app.components.formBuilder.newField": "New field", "app.components.formBuilder.number": "Number", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Open", "app.components.formBuilder.optional": "Optional", "app.components.formBuilder.other": "Other", "app.components.formBuilder.otherOption": "\"Other\" option", "app.components.formBuilder.otherOptionTooltip": "Allow participants to enter a custom response if the provided answers do not match their preference", "app.components.formBuilder.page": "Page", "app.components.formBuilder.pageCannotBeDeleted": "This page can't be deleted.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "This page cannot be deleted and does not allow any additional fields to be added.", "app.components.formBuilder.pageRuleLabel": "Next page is:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "If no logic is added, the form will follow its normal flow. If both the page and its questions have logic, the question logic will take precedence. Ensure this aligns with your intended flow For more information, visit {supportPageLink}", "app.components.formBuilder.preview": "Preview:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Co-sponsors are not shown on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "File upload questions are shown as unsupported on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Mapping questions are shown on the downloaded PDF, but layers will not be visible. Mapping questions are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Matrix questions are shown on the downloaded PDF but are not currently supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Page titles and descriptions are shown as a section header in the downloaded PDF.", "app.components.formBuilder.printSupportTooltip.ranking": "Ranking questions are shown on the downloaded PDF but are not currently supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Tags are shown as unsupported on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.proposedBudget": "Proposed budget", "app.components.formBuilder.question": "Question", "app.components.formBuilder.questionCannotBeDeleted": "This question can't be deleted.", "app.components.formBuilder.questionDescriptionOptional": "Question description (optional)", "app.components.formBuilder.questionTitle": "Question title", "app.components.formBuilder.randomize": "Randomize", "app.components.formBuilder.randomizeToolTip": "The order of the answers will be randomized per user", "app.components.formBuilder.range": "Range", "app.components.formBuilder.ranking": "Ranking", "app.components.formBuilder.rating": "Rating", "app.components.formBuilder.removeAnswer": "Remove answer", "app.components.formBuilder.required": "Required", "app.components.formBuilder.requiredToggleLabel": "Make answering this question required", "app.components.formBuilder.ruleForAnswerLabel": "If answer is:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "If answers include:", "app.components.formBuilder.save": "Save", "app.components.formBuilder.selectRangeTooltip": "Choose the maximum value for your scale.", "app.components.formBuilder.sentiment": "Sentiment scale", "app.components.formBuilder.shapefileUpload": "Esri shapefile upload", "app.components.formBuilder.shortAnswer": "Short answer", "app.components.formBuilder.showResponseToUsersToggleLabel": "Show response to users", "app.components.formBuilder.singleChoice": "Single choice", "app.components.formBuilder.staleDataErrorMessage2": "There has been a problem. This input form has been saved more recently somewhere else. This may be because you or another user has it open for editing in another browser window. Please refresh the page to get the latest form and then make your changes again.", "app.components.formBuilder.stronglyAgree": "Strongly agree", "app.components.formBuilder.stronglyDisagree": "Strongly disagree", "app.components.formBuilder.supportArticleLinkText": "this page", "app.components.formBuilder.tags": "Tags", "app.components.formBuilder.title": "Title", "app.components.formBuilder.toLabel": "to", "app.components.formBuilder.unsavedChanges": "You have unsaved changes", "app.components.formBuilder.useCustomButton2": "Use custom page button", "app.components.formBuilder.veryBad": "Very bad", "app.components.formBuilder.veryGood": "Very good", "app.components.ideas.similarIdeas.engageHere": "Engage here", "app.components.ideas.similarIdeas.noSimilarSubmissions": "No similar submissions found.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "We found similar submisisons - engaging with them can help make them stronger!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Similar submissions already posted:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Looking for similar submissions ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {Less than a day} one {# day} other {# days}} left", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  weeks left", "app.components.screenReaderCurrency.AED": "United Arab Emirates Dirham", "app.components.screenReaderCurrency.AFN": "Afghan Afghani", "app.components.screenReaderCurrency.ALL": "Albanian Lek", "app.components.screenReaderCurrency.AMD": "Armenian Dram", "app.components.screenReaderCurrency.ANG": "Netherlands Antillean Guilder", "app.components.screenReaderCurrency.AOA": "Angolan <PERSON>", "app.components.screenReaderCurrency.ARS": "Argentine Peso", "app.components.screenReaderCurrency.AUD": "Australian Dollar", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "Azerbaijani Manat", "app.components.screenReaderCurrency.BAM": "Bosnia-Herzegovina Convertible Mark", "app.components.screenReaderCurrency.BBD": "Barbadian Dollar", "app.components.screenReaderCurrency.BDT": "Bangladeshi Taka", "app.components.screenReaderCurrency.BGN": "Bulgarian Lev", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "Burundian Franc", "app.components.screenReaderCurrency.BMD": "Bermudian Dollar", "app.components.screenReaderCurrency.BND": "Brunei Dollar", "app.components.screenReaderCurrency.BOB": "Bolivian Boliviano", "app.components.screenReaderCurrency.BOV": "Bolivian M<PERSON>", "app.components.screenReaderCurrency.BRL": "Brazilian Real", "app.components.screenReaderCurrency.BSD": "Bahamian Dollar", "app.components.screenReaderCurrency.BTN": "Bhutanese Ngultrum", "app.components.screenReaderCurrency.BWP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "Belarusian Ruble", "app.components.screenReaderCurrency.BZD": "Belize Dollar", "app.components.screenReaderCurrency.CAD": "Canadian Dollar", "app.components.screenReaderCurrency.CDF": "Congolese Franc", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Swiss Franc", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Chilean Unit of Account (UF)", "app.components.screenReaderCurrency.CLP": "Chilean Peso", "app.components.screenReaderCurrency.CNY": "Chinese Yuan", "app.components.screenReaderCurrency.COP": "Colombian Peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Rican Colón", "app.components.screenReaderCurrency.CRE": "Credit", "app.components.screenReaderCurrency.CUC": "Cuban Convertible Peso", "app.components.screenReaderCurrency.CUP": "Cuban Peso", "app.components.screenReaderCurrency.CVE": "Cape Verdean Escudo", "app.components.screenReaderCurrency.CZK": "Czech Koruna", "app.components.screenReaderCurrency.DJF": "Djiboutian Franc", "app.components.screenReaderCurrency.DKK": "Danish Krone", "app.components.screenReaderCurrency.DOP": "Dominican Peso", "app.components.screenReaderCurrency.DZD": "Algerian Dinar", "app.components.screenReaderCurrency.EGP": "Egyptian Pound", "app.components.screenReaderCurrency.ERN": "Eritrean Nakfa", "app.components.screenReaderCurrency.ETB": "Ethiopian Birr", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Fijian Dollar", "app.components.screenReaderCurrency.FKP": "Falkland Islands Pound", "app.components.screenReaderCurrency.GBP": "British Pound", "app.components.screenReaderCurrency.GEL": "Georgian Lari", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Gibraltar Pound", "app.components.screenReaderCurrency.GMD": "Gambian Dalasi", "app.components.screenReaderCurrency.GNF": "Guinean Franc", "app.components.screenReaderCurrency.GTQ": "Guatemalan <PERSON>", "app.components.screenReaderCurrency.GYD": "Guyanese Dollar", "app.components.screenReaderCurrency.HKD": "Hong Kong Dollar", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "Croatian Kuna", "app.components.screenReaderCurrency.HTG": "Haitian Gourde", "app.components.screenReaderCurrency.HUF": "Hungarian Forint", "app.components.screenReaderCurrency.IDR": "Indonesian Rupiah", "app.components.screenReaderCurrency.ILS": "Israeli New <PERSON>", "app.components.screenReaderCurrency.INR": "Indian Rupee", "app.components.screenReaderCurrency.IQD": "Iraqi <PERSON>", "app.components.screenReaderCurrency.IRR": "Iranian Rial", "app.components.screenReaderCurrency.ISK": "Icelandic Króna", "app.components.screenReaderCurrency.JMD": "Jamaican Dollar", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "Japanese Yen", "app.components.screenReaderCurrency.KES": "Kenyan Shilling", "app.components.screenReaderCurrency.KGS": "Kyrgyzstani Som", "app.components.screenReaderCurrency.KHR": "Cambodian Riel", "app.components.screenReaderCurrency.KMF": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KPW": "North Korean Won", "app.components.screenReaderCurrency.KRW": "South Korean Won", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "Cayman Islands Dollar", "app.components.screenReaderCurrency.KZT": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Lebanese Pound", "app.components.screenReaderCurrency.LKR": "Sri Lankan Rupee", "app.components.screenReaderCurrency.LRD": "Liberian Dollar", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Lithuanian Litas", "app.components.screenReaderCurrency.LVL": "Latvian Lats", "app.components.screenReaderCurrency.LYD": "Libyan Dinar", "app.components.screenReaderCurrency.MAD": "Moroccan <PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MDL": "Moldovan Leu", "app.components.screenReaderCurrency.MGA": "Malagasy Ariary", "app.components.screenReaderCurrency.MKD": "Macedonian Denar", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "Mongolian Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "Mauritanian Ouguiya", "app.components.screenReaderCurrency.MUR": "Mauritian Rupee", "app.components.screenReaderCurrency.MVR": "Maldivian Rufiyaa", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MXN": "Mexican Peso", "app.components.screenReaderCurrency.MXV": "Mexican Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malaysian Ringgit", "app.components.screenReaderCurrency.MZN": "Mozambican Metical", "app.components.screenReaderCurrency.NAD": "Namibian Dollar", "app.components.screenReaderCurrency.NGN": "Nigerian Naira", "app.components.screenReaderCurrency.NIO": "Nicaraguan Córdoba", "app.components.screenReaderCurrency.NOK": "Norwegian Krone", "app.components.screenReaderCurrency.NPR": "Nepalese Rupee", "app.components.screenReaderCurrency.NZD": "New Zealand Dollar", "app.components.screenReaderCurrency.OMR": "Omani R<PERSON>", "app.components.screenReaderCurrency.PAB": "Panamanian Balboa", "app.components.screenReaderCurrency.PEN": "Peruvian Sol", "app.components.screenReaderCurrency.PGK": "Papua New Guinean Kina", "app.components.screenReaderCurrency.PHP": "Philippine Peso", "app.components.screenReaderCurrency.PKR": "Pakistani Rupee", "app.components.screenReaderCurrency.PLN": "Polish Złoty", "app.components.screenReaderCurrency.PYG": "Paraguayan Guaraní", "app.components.screenReaderCurrency.QAR": "Qatari Riyal", "app.components.screenReaderCurrency.RON": "Romanian Leu", "app.components.screenReaderCurrency.RSD": "Serbian Dinar", "app.components.screenReaderCurrency.RUB": "Russian Ruble", "app.components.screenReaderCurrency.RWF": "Rwandan <PERSON>", "app.components.screenReaderCurrency.SAR": "Saudi Riyal", "app.components.screenReaderCurrency.SBD": "Solomon Islands Dollar", "app.components.screenReaderCurrency.SCR": "Seychellois Rupee", "app.components.screenReaderCurrency.SDG": "Sudanese Pound", "app.components.screenReaderCurrency.SEK": "Swedish Krona", "app.components.screenReaderCurrency.SGD": "Singapore Dollar", "app.components.screenReaderCurrency.SHP": "<PERSON>", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "Somali Shilling", "app.components.screenReaderCurrency.SRD": "Surinamese Dollar", "app.components.screenReaderCurrency.SSP": "South Sudanese Pound", "app.components.screenReaderCurrency.STD": "São Tomé and Pr<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SYP": "Syrian Pound", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "Thai Baht", "app.components.screenReaderCurrency.TJS": "<PERSON>i Somoni", "app.components.screenReaderCurrency.TMT": "Turkmenistani Manat", "app.components.screenReaderCurrency.TND": "Tunisian Dinar", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Tongan Paʻanga", "app.components.screenReaderCurrency.TRY": "Turkish Lira", "app.components.screenReaderCurrency.TTD": "Trinidad and Tobago Dollar", "app.components.screenReaderCurrency.TWD": "New Taiwan Dollar", "app.components.screenReaderCurrency.TZS": "Tanzanian <PERSON>", "app.components.screenReaderCurrency.UAH": "Ukrainian Hryvnia", "app.components.screenReaderCurrency.UGX": "Ugandan <PERSON>", "app.components.screenReaderCurrency.USD": "United States Dollar", "app.components.screenReaderCurrency.USN": "United States Dollar (Next day)", "app.components.screenReaderCurrency.USS": "United States Dollar (Same day)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguayan Peso", "app.components.screenReaderCurrency.UZS": "Uzbekistani Som", "app.components.screenReaderCurrency.VEF": "Venezuelan Bolívar", "app.components.screenReaderCurrency.VND": "Vietnamese Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoan <PERSON>", "app.components.screenReaderCurrency.XAF": "Central African CFA Franc", "app.components.screenReaderCurrency.XAG": "Silver (one troy ounce)", "app.components.screenReaderCurrency.XAU": "Gold (one troy ounce)", "app.components.screenReaderCurrency.XBA": "European Composite Unit (EURCO)", "app.components.screenReaderCurrency.XBB": "European Monetary Unit (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "European Unit of Account 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "European Unit of Account 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "East Caribbean Dollar", "app.components.screenReaderCurrency.XDR": "Special Drawing Rights", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "West African CFA Franc", "app.components.screenReaderCurrency.XPD": "Palladium (one troy ounce)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "Platinum (one troy ounce)", "app.components.screenReaderCurrency.XTS": "Codes specifically reserved for testing purposes", "app.components.screenReaderCurrency.XXX": "No currency", "app.components.screenReaderCurrency.YER": "Yemeni R<PERSON>", "app.components.screenReaderCurrency.ZAR": "South African Rand", "app.components.screenReaderCurrency.ZMW": "Zambian <PERSON>", "app.components.screenReaderCurrency.amount": "Amount", "app.components.screenReaderCurrency.currency": "<PERSON><PERSON><PERSON><PERSON>", "app.components.trendIndicator.lastQuarter2": "last quarter", "app.containers.AccessibilityStatement.applicability": "Uunga iseqqusaanermut uppernarsaat atuuttoq {demoPlatformLink}, nittartakkamut uunga takutitsisoq; atuisoq kildekodep assinga kiisalu atorneqarsinnaanerata assinga.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Assessment method", "app.containers.AccessibilityStatement.assesmentText2022": "Webbimut tassunga isersinnaaneq avataanit nali<PERSON>q, isik<PERSON><PERSON>t ineriartorneranullu peqataanngitsunit. Qulaaniittut eqquutsinnissaannut {demoPlatformLink} uani suussusersineqarsinnaapput {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "nuannarinerusatit allanngortissinnaavatit", "app.containers.AccessibilityStatement.changePreferencesText": "sukkulluunniit {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Il<PERSON><PERSON>neqanngit<PERSON><PERSON>", "app.containers.AccessibilityStatement.conformanceStatus": "Conformance status", "app.containers.AccessibilityStatement.contentConformanceExceptions": "We strive to make our content inclusive for all. However, in some instances there may be inaccessible content on the platform as outlined below:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demo website", "app.containers.AccessibilityStatement.email": "E-mail:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Embedded survey tools", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "The embedded survey tools that are available for use on this platform are third-party software and may not be accessible.", "app.containers.AccessibilityStatement.exception_1": "Digitalikkut ataatsimoortitsilluni nittartagaativut inunnit suliffeqarfinnillu ikkunneqartartunit aqunneqartumik ingerlanneqarput. \n\nNittartakkamik atuisut PDF-inik, assinik allanilluunniit filinik multimedianillu nittartakkamut kakkiussisinnaapput allaffissamulluunniit ikkussillutik. \n\nTakussutissiat taakku tamakkiisumik takuneqarsinnaanerat killeqarsinnaavoq.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "We welcome your feedback on the accessibility of this site. Please contact us via one of the following methods:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Feedback process", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Brussels, Belgium", "app.containers.AccessibilityStatement.headTitle": "Accessibility Statement | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{citizenLabLink} platforminut pilersuisinnaapput atuisunut tamanut taakkuninnga atuisinnaasunut teknologi piginnaasalluunniit apeqqutaatinnagit. Platforminut tamanut atuisartutsinnut isersinnaanerat atuisinnaanerallu periaatsit akuerisaasut naapertorlugit malinneqarnissaat anguniartuaannarparput.", "app.containers.AccessibilityStatement.mapping": "Mapping", "app.containers.AccessibilityStatement.mapping_1": "Maps on the platform partially meet accessibility standards. Map extent, zoom, and UI widgets can be controlled using a keyboard when viewing maps. Admins can also configure the style of map layers in the back office, or using the Esri integration, to create more accessible colour palettes and symbology. Using different line or polygon styles (e.g. dashed lines) will also help differentiate map layers wherever possible, and although such styling cannot be configured within our platform at this time, it can be configured if using maps with the Esri integration.", "app.containers.AccessibilityStatement.mapping_2": "Maps in the platform are not fully accessible as they do not audibly present basemaps, map layers, or trends in the data to users utilizing screen readers. Fully accessible maps would need to audibly present the map layers and describe any relevant trends in the data. Furthermore, line and polygon map drawing in surveys is not accessible as shapes cannot be drawn using a keyboard. Alternative input methods are not available at this time due to technical complexity.", "app.containers.AccessibilityStatement.mapping_3": "To make line and polygon map drawing more accessible, we recommend including an introduction or explanation in the survey question or page description of what the map is showing and any relevant trends. Furthermore, a short or long answer text question could be provided so respondents can describe their answer in plain terms if needed (rather than clicking on the map). We also recommend including contact information for the project manager so respondents who cannot fill in a map question can request an alternative method to answer the question (E.g. Video meeting).", "app.containers.AccessibilityStatement.mapping_4": "For Ideation projects and proposals, there is an option to display inputs in a map view, which is not accessible. However, for these methods there is an alternative list view of inputs available, which is accessible.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Internetti aqqutigalugu workshopi ingerlaartoq videomik tigooraasinnaavoq taannali piffissami matumani allagartalersuisinnaanngilaq.", "app.containers.AccessibilityStatement.pageDescription": "A statement on the accessibility of this website", "app.containers.AccessibilityStatement.postalAddress": "Postal address:", "app.containers.AccessibilityStatement.publicationDate": "<PERSON><PERSON><PERSON> saqq<PERSON>", "app.containers.AccessibilityStatement.publicationDate2024": "This accessibility statement was published on August 21, 2024.", "app.containers.AccessibilityStatement.responsiveness": "We aim to respond to feedback within 1-2 business days.", "app.containers.AccessibilityStatement.statusPageText": "status page", "app.containers.AccessibilityStatement.technologiesIntro": "The accessibility of this site relies on the following technologies to work:", "app.containers.AccessibilityStatement.technologiesTitle": "Technologies", "app.containers.AccessibilityStatement.title": "Accessibility Statement", "app.containers.AccessibilityStatement.userGeneratedContent": "<PERSON><PERSON><PERSON><PERSON> imai", "app.containers.AccessibilityStatement.workshops": "Workshops", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Select project", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Using the Content Builder will let you use more advanced layout options. For languages where no content is available in the content builder, the regular project description content will be displayed instead.", "app.containers.AdminPage.ProjectDescription.linkText": "Edit description in Content Builder", "app.containers.AdminPage.ProjectDescription.saveError": "Something went wrong while saving the project description.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Use Content Builder for description", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Using the Content Builder will let you use more advanced layout options.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "View project", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Survey end", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Smart gruppimik pilersitsigit", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Atuisut piumasaqaatinik eqquutsitsisut gruppimut automatiskimik ilanngunneqassapput:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Minnerpaamik ataatsimik malittarisassaliorit", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Piumasaqaatit ilai na<PERSON>at", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Save group", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Configuring smart groups is not part of your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Provide a group name", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Verification is disabled for your platform, remove the verification rule or contact support.", "app.containers.App.appMetaDescription": "{orgName} -ip nittartakkakkut innuttaasunut peqataatitsivianut tikilluarit\nNajukkami suliniutit misissuataakkit oqallinnermullu peqataallutit!", "app.containers.App.loading": "<PERSON><PERSON><PERSON><PERSON> …", "app.containers.App.metaTitle1": "Citizen engagement platform | {orgName}", "app.containers.App.skipLinkText": "Skip to main content", "app.containers.AreaTerms.areaTerm": "area", "app.containers.AreaTerms.areasTerm": "areas", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "An account with this email already exists. You can sign out, log in with this email address and verify your account on the settings page.", "app.containers.Authentication.steps.AccessDenied.close": "Close", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "You do not meet the requirements to participate in this process.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Go back to single sign-on verification", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Please enter a token", "app.containers.Authentication.steps.Invitation.token": "Token", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Already have an account? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Log in", "app.containers.CampaignsConsentForm.ally_categoryLabel": "<PERSON>ussusermut tassunga e-mailit", "app.containers.CampaignsConsentForm.messageError": "Allatavit to<PERSON> k<PERSON>q", "app.containers.CampaignsConsentForm.messageSuccess": "E-mailit nassiussifigisartakka<PERSON>t to<PERSON>", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Nalunaarutit qanoq ittut e-mailikkut pisarusuppigit? ", "app.containers.CampaignsConsentForm.notificationsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CampaignsConsentForm.submit": "Toqqoruk", "app.containers.ChangeEmail.backToProfile": "Back to profile settings", "app.containers.ChangeEmail.confirmationModalTitle": "Confirm your email", "app.containers.ChangeEmail.emailEmptyError": "E-mail addressi allaguk", "app.containers.ChangeEmail.emailInvalidError": "Provide an email address in the correct format, <NAME_EMAIL>", "app.containers.ChangeEmail.emailRequired": "Please enter an email address.", "app.containers.ChangeEmail.emailTaken": "This email is already in use.", "app.containers.ChangeEmail.emailUpdateCancelled": "Email update has been cancelled.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "To update your email, please restart the process.", "app.containers.ChangeEmail.helmetDescription": "Change your email page", "app.containers.ChangeEmail.helmetTitle": "<PERSON><PERSON>", "app.containers.ChangeEmail.newEmailLabel": "New email", "app.containers.ChangeEmail.submitButton": "Submit", "app.containers.ChangeEmail.titleAddEmail": "Add your email", "app.containers.ChangeEmail.titleChangeEmail": "<PERSON><PERSON>", "app.containers.ChangeEmail.updateSuccessful": "Your email has been successfully updated.", "app.containers.ChangePassword.currentPasswordLabel": "Current password", "app.containers.ChangePassword.currentPasswordRequired": "Enter your current password", "app.containers.ChangePassword.goHome": "Go to home", "app.containers.ChangePassword.helmetDescription": "Change your password page", "app.containers.ChangePassword.helmetTitle": "Change your password", "app.containers.ChangePassword.newPasswordLabel": "New password", "app.containers.ChangePassword.newPasswordRequired": "Enter your new password", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Provide a password that is at least {minimumPasswordLength} characters long", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Your password has been successfully updated", "app.containers.ChangePassword.passwordEmptyError": "Enter your password", "app.containers.ChangePassword.passwordsDontMatch": "Confirm new password", "app.containers.ChangePassword.titleAddPassword": "Add a password", "app.containers.ChangePassword.titleChangePassword": "Change your password", "app.containers.Comments.a11y_commentDeleted": "Oqaaseqaat peerneqarpoq", "app.containers.Comments.a11y_commentPosted": "Oqaaseqaat allanneqarpoq", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {no likes} one {1 like} other {# likes}}", "app.containers.Comments.a11y_undoLike": "Undo like", "app.containers.Comments.addCommentError": "<PERSON><PERSON><PERSON><PERSON> il<PERSON>git<PERSON>orpoq. Ungasinngitsumi misilee<PERSON>a", "app.containers.Comments.adminCommentDeletionCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.adminCommentDeletionConfirmButton": "<PERSON><PERSON>ase<PERSON>atit una peeruk", "app.containers.Comments.cancelCommentEdit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.childCommentBodyPlaceholder": "Akissummik allagit …", "app.containers.Comments.commentCancelUpvote": "Peqqissimigit", "app.containers.Comments.commentDeletedPlaceholder": "Oqaaseqaat p<PERSON>neqarsimavoq", "app.containers.Comments.commentDeletionCancelButton": "Oqaaseqaatiga pigii<PERSON>a", "app.containers.Comments.commentDeletionConfirmButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.commentLike": "Like", "app.containers.Comments.commentReplyButton": "Akigit", "app.containers.Comments.commentsSortTitle": "Uku malillugit oqaaseqaatit immikkoortitikkkt", "app.containers.Comments.completeProfileLinkText": "profilit <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.completeProfileToComment": "Please {completeRegistrationLink} to comment.", "app.containers.Comments.confirmCommentDeletion": "Ilumut oqa<PERSON>t peer<PERSON>? Peerniarukku allaq<PERSON>anngilat!", "app.containers.Comments.deleteComment": "<PERSON><PERSON><PERSON>", "app.containers.Comments.deleteReasonDescriptionError": "Tunngaviit pillugu annerusumik paasissutissiigit", "app.containers.Comments.deleteReasonError": "Tunngavimmik allagit", "app.containers.Comments.deleteReason_inappropriate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> naleqqun<PERSON>it", "app.containers.Comments.deleteReason_irrelevant": "<PERSON> ma<PERSON>", "app.containers.Comments.deleteReason_other": "<PERSON><PERSON><PERSON>", "app.containers.Comments.editComment": "Aaqqissuuguk", "app.containers.Comments.guidelinesLinkText": "Malittarisassavut", "app.containers.Comments.ideaCommentBodyPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uani allaguk", "app.containers.Comments.internalCommentingNudgeMessage": "Making internal comments is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Comments.internalConversation": "Internal conversation", "app.containers.Comments.loadMoreComments": "Isumassarsiat amerlanerit a<PERSON>t", "app.containers.Comments.loadingComments": "Oqaaseqaatit a<PERSON>...", "app.containers.Comments.loadingMoreComments": "Oqaaseqaatit arlallit a<PERSON>qarput", "app.containers.Comments.notVisibleToUsersPlaceholder": "This comment is not visible to regular users", "app.containers.Comments.postInternalComment": "Post internal comment", "app.containers.Comments.postPublicComment": "Post public comment", "app.containers.Comments.profanityError": "Oqaasipiluusinnaasunik ataasiakkaanik arlalinnilluunniit allassimarpasipputit {guidelinesLink}. Allatat oqaasipiluiarsinnaavat allanngortinneqarsinnaanera toorlugu.", "app.containers.Comments.publicDiscussion": "Public discussion", "app.containers.Comments.publishComment": "Oqaaseqaatit nassiutiguk", "app.containers.Comments.reportAsSpamModalTitle": "Sooq una spamisut nalu<PERSON>", "app.containers.Comments.saveComment": "Toqqoruk", "app.containers.Comments.signInLinkText": "Iserneq", "app.containers.Comments.signInToComment": "{AtsiornermutLink} oqaaseqaateqarniarluni.", "app.containers.Comments.signUpLinkText": "nalunaars<PERSON>t", "app.containers.Comments.verifyIdentityLinkText": "Kinaassutsit uppernarsaruk", "app.containers.Comments.visibleToUsersPlaceholder": "This comment is visible to regular users", "app.containers.Comments.visibleToUsersWarning": "Comments posted here will be visible to regular users.", "app.containers.ContentBuilder.PageTitle": "Project description", "app.containers.CookiePolicy.advertisingContent": "00", "app.containers.CookiePolicy.advertisingTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.analyticsContents": "Cookies nittartakkami pulaartut iliuusaat malinnaavigisarpai, soorlu quppernerit sorliit qanorlu sivisutigisumik pulaarneraat. Aamma paasissutissat teknikkimut tunnasut katerserneqarsinnaapput, tassani browserimi paasissutissat, suminngaanneerneq aammalu IP-adresset pineqarput. Paasissutissat taakku taamaallat suliffeqarfiup iluani atorneqartarput, paasissutissallu taakku atuinermi ajornanngillisaanermut aammalu nittartakkap atortuinut pitsanngorsaanissamut atortarpavut. Paasissutissat CitizenLabip aammalu {orgName}-ip akornanni avitseqatigiissutigineqaratarsinnaapput, suliniutinut soqutiginninneq nalilersorniarlugu pitsanngorsarniarlugulu. Malugiuk, paasissutissat kinaassutsimik isertuisuupput - taamaasilluni kikkunneernersut paasineqarsinnaanatik. Taamaattorli paasissutissat paasissutissanik allanik ataqatigiissarneqarneratigut kinaassusermik paasinnittoqaratartoqarsinnaavoq.", "app.containers.CookiePolicy.analyticsTitle": "Suliap qanoq ittorpiaaneranik paasiniaanermi sakkussat", "app.containers.CookiePolicy.cookiePolicyDescription": "Isaaf<PERSON>mmi uani cookiesit qanoq atortarnerigut sukumiisumik nassuiaaneq", "app.containers.CookiePolicy.cookiePolicyTitle": "Cookiet pillugit politik", "app.containers.CookiePolicy.essentialContent": "<PERSON>ie-t ilaat quppernerup atuuffiinut pingaaruteqarluin<PERSON>put. Cookie-t quppernerup atornerani illit atuisutut akuerinissaanut atorneqartarput, a<PERSON>ma oqaatsit atorumasavit toqqorn<PERSON>anut atorneqartarput.", "app.containers.CookiePolicy.essentialTitle": "<PERSON><PERSON><PERSON><PERSON> pinga<PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.externalContent": "Quppernerit atukkatta ilaani internettimut atassusiisartut allat, assersuutigalugu YouTube imaluunniit Typerform takussaaratarsinnaapput. Cookie-t allameersut aqussinnaanngilagut, aamma internettimut atassusiisartuniit allaniit imarisanik takuguit qarasaasiami atukkanni cookie-t ikkunneqaratarsinnaapput.", "app.containers.CookiePolicy.externalTitle": "<PERSON><PERSON><PERSON><PERSON> allameers<PERSON>", "app.containers.CookiePolicy.functionalContents": "Atuuffinnut cookies-it atu<PERSON>rsinn<PERSON>qa<PERSON>inn<PERSON><PERSON>ut, isersimasut taamaalillutik nutarterinerit pillugit nalunaarutisisarniassammata kiisalu quppernermit toqqaannartumik supportimut attaveqarnissamut periarfissaqarniassammata.", "app.containers.CookiePolicy.functionalTitle": "Atorsinnaassusaa", "app.containers.CookiePolicy.headCookiePolicyTitle": "Cookie Policy | {orgName}", "app.containers.CookiePolicy.intro": "Nittartakkat amerlanerusut assigalugit cookiesit illit allallu isaaffimmik uuminnga  pitsaasumik misigisaqarnissarsi pitsanngorsarniarlugu cookiesit atortarpagut. <PERSON><PERSON> aamma qanoq cookiesinik atuinerput innersoq pillugu ammasumik paasissutis<PERSON>rusuk<PERSON>ta, ataani immikkuualuttut erseqqissumik allassimasut nassaarisinnaavatit. Cookiesit isaaffitsinni atorneqartut, atuisut aalajangersimasut kinaassusaannik paasiniaanermut ujaasinermullu atorneqanngisaannar<PERSON>, ima oqartoqarsinnaavoq, \"kinaasutit\" nalugaat. Taamaattorli pingaaruteqarpoq erseqqissassallugu, teknikkikkut paasissutissat ujartaraat, ass. browserit pillugit paasissutissat, sumiinnerit aamma IP-adressit. Taama atorneqarsinnanngikkaluartut, allanut paasissutissanut ataatsimoortikkaanni kinaassutsimik nassaarsinnaapput.", "app.containers.CookiePolicy.manageCookiesDescription": "Misis<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, niuer<PERSON><PERSON>t atuuffinnullu cookie-t cookie<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qaqugukkulluunniit atortussanngortillugillu atorunnaarsittarsinnaavatit. Internetbrowseritut atukkat aqqutigalugu cookie-t pioreersut toortaanikkut imaluunniit nammineq isumaminik peertussanngorlugit piiarsinnaavatit. Qupperneq ammaqqikkukku cookie-t akuerigukkit ikkuteqqinneqaratarsinnaapput. Cookie-t piianngikkukkit cookiepræference ulluni 60-ini toqqo<PERSON>pp<PERSON>, tamatuma kingorna akuerserusunnersutit aperineqaqqissaatit.", "app.containers.CookiePolicy.manageCookiesPreferences": "<PERSON><PERSON><PERSON>nermi matumani allat ikkussaasa takussutissartaat tamakkiisoq takuniarlugu præferencillu naleqqussarniarlugit {manageCookiesPreferencesButtonText}-mukarit.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "cookie-t iluarsaatikkit", "app.containers.CookiePolicy.manageCookiesTitle": "Cook<PERSON>-t na<PERSON>qqussak<PERSON>t", "app.containers.CookiePolicy.viewPreferencesButtonText": "Cookie-t iluarsaatikkit", "app.containers.CookiePolicy.viewPreferencesText": "Cookie-t assigiinngitsut ataani takusinnaasatit quppernernut alakkarterisunut tamanut qupernernulluunniit tamanut atuutinngitsooratarsinnaapput, allaniit ikkussat ilinnut atuutsinneqartut takussutissartaat tamaat takuniarlugu {viewPreferencesButton}-mukarit.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Cookie-t sumut atortarpavut?", "app.containers.CustomPageShow.editPage": "Edit page", "app.containers.CustomPageShow.goBack": "Go back", "app.containers.CustomPageShow.notFound": "Qupperneq nassaarineqann<PERSON>laq", "app.containers.DisabledAccount.bottomText": "You can sign in again from {date}.", "app.containers.DisabledAccount.termsAndConditions": "terms & conditions", "app.containers.DisabledAccount.text2": "Your account on the participation platform of {orgName} has been temporarily disabled for a violation of community guidelines. For more information on this, you can consult the {TermsAndConditions}.", "app.containers.DisabledAccount.title": "Your account has been temporarily disabled", "app.containers.EventsShow.addToCalendar": "Add to calendar", "app.containers.EventsShow.editEvent": "Edit event", "app.containers.EventsShow.emailSharingBody2": "Attend this event: {eventTitle}. Read more at {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Event date and time", "app.containers.EventsShow.eventFrom2": "From \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Go back", "app.containers.EventsShow.goToProject": "Go to the project", "app.containers.EventsShow.haveRegistered": "have registered", "app.containers.EventsShow.icsError": "Error downloading the ICS file", "app.containers.EventsShow.linkToOnlineEvent": "Link to online event", "app.containers.EventsShow.locationIconAltText": "Location", "app.containers.EventsShow.metaTitle": "Event: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Online meeting", "app.containers.EventsShow.onlineLinkIconAltText": "Online meeting link", "app.containers.EventsShow.registered": "registered", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 registrants} one {1 registrant} other {# registrants}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} registrants", "app.containers.EventsShow.registrantsIconAltText": "Registrants", "app.containers.EventsShow.socialMediaSharingMessage": "Attend this event: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# participant} other {# participants}}", "app.containers.EventsViewer.allTime": "All time", "app.containers.EventsViewer.date": "Date", "app.containers.EventsViewer.thisMonth2": "Upcoming month", "app.containers.EventsViewer.thisWeek2": "Upcoming week", "app.containers.EventsViewer.today": "Today", "app.containers.IdeaButton.addAContribution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAPetition": "Add a petition", "app.containers.IdeaButton.addAProject": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAProposal": "Siunnersuummik ilanngussigit", "app.containers.IdeaButton.addAQuestion": "Apeqqummik nutaamik ilanngussigit", "app.containers.IdeaButton.addAnInitiative": "Add an initiative", "app.containers.IdeaButton.addAnOption": "Qinigassamik nutaamik ilanngussigit", "app.containers.IdeaButton.postingDisabled": "Ilanngussanik <PERSON>aanik tigusisoqarsinnaanngikkallarpoq", "app.containers.IdeaButton.postingInNonActivePhases": "Ilanngussat nutaat immikkoortunut ammasunut taamaallaat ilanngunneqarsinn<PERSON>pp<PERSON>.", "app.containers.IdeaButton.postingInactive": "Ilanngussanik <PERSON>aanik tigusisoqarsinnaanngikkallarpoq", "app.containers.IdeaButton.postingLimitedMaxReached": "You have already completed this survey. Thanks for your response!", "app.containers.IdeaButton.postingNoPermission": "Ilanngussanik <PERSON>aanik tigusisoqarsinnaanngikkallarpoq", "app.containers.IdeaButton.postingNotYetPossible": "Ilanngussanik <PERSON>aanik tigusisoqarsinnaanngikkallarpoq", "app.containers.IdeaButton.signInLinkText": "Iserneq", "app.containers.IdeaButton.signUpLinkText": "nalunaars<PERSON>t", "app.containers.IdeaButton.submitAnIssue": "Ajornartorsiummik ilanngussigit", "app.containers.IdeaButton.submitYourIdea": "Isumassarsiannik ilanngussigit", "app.containers.IdeaButton.takeTheSurvey": "Take the survey", "app.containers.IdeaButton.verificationLinkText": "Kontut uppernarsaruk.", "app.containers.IdeaCard.readMore": "Read more", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {no oqaaseqaatit} one {1 oqaaseqaatit} other {# oqaaseqaatit}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {no votes} one {1 vote} other {# votes}} out of {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Close filters panel", "app.containers.IdeaCards.a11y_totalItems": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> katil<PERSON>it: {ideasCount}", "app.containers.IdeaCards.all": "Tamakkerlutik", "app.containers.IdeaCards.allStatuses": "All statuses", "app.containers.IdeaCards.contributions": "Contributions", "app.containers.IdeaCards.ideaTerm": "Ideas", "app.containers.IdeaCards.initiatives": "Initiatives", "app.containers.IdeaCards.issueTerm": "Issues", "app.containers.IdeaCards.list": "Alluttuiffik", "app.containers.IdeaCards.map": "<PERSON><PERSON>p assinga", "app.containers.IdeaCards.mostDiscussed": "Most discussed", "app.containers.IdeaCards.newest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.noFilteredResults": "Inerneranik ersittoqanngilaq. Allaffissaq taaguulluunniit alla atoruk.", "app.containers.IdeaCards.numberResults": "Results ({postCount})", "app.containers.IdeaCards.oldest": "Qanganisaanerit", "app.containers.IdeaCards.optionTerm": "Options", "app.containers.IdeaCards.petitions": "Petitions", "app.containers.IdeaCards.popular": "Amerlanernik taaneqartut", "app.containers.IdeaCards.projectFilterTitle": "Suliniutit ", "app.containers.IdeaCards.projectTerm": "Projects", "app.containers.IdeaCards.proposals": "Proposals", "app.containers.IdeaCards.questionTerm": "Questions", "app.containers.IdeaCards.random": "Taamungaannaq", "app.containers.IdeaCards.resetFilters": "Immikkoortitikkat aallaqqaataanit aallartittussangorlugu aaqqikkit", "app.containers.IdeaCards.showXResults": "Takutiguk {ideasCount, plural, 0 {# inerneqanngilaq} one {# inerniliussaq} other {# inernerit}}", "app.containers.IdeaCards.sortTitle": "Immikkoortiterineq", "app.containers.IdeaCards.statusTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "Sammisassat imaat:", "app.containers.IdeaCards.topicsTitle": "Sammisassat imaat:", "app.containers.IdeaCards.trending": "Nuannarineqarnerit", "app.containers.IdeaCards.tryDifferentFilters": "Inerneranik ersittoqanngilaq. Allaffissaq taaguulluunniit alla atoruk.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} comments} one {{ideasCount} comment} other {{ideasCount} comments}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} contributions} one {{ideasCount} contribution} other {{ideasCount} contributions}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ideas} one {{ideasCount} idea} other {{ideasCount} ideas}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} initiatives} one {{ideasCount} initiative} other {{ideasCount} initiatives}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} options} one {{ideasCount} option} other {{ideasCount} options}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} petitions} one {{ideasCount} petition} other {{ideasCount} petitions}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projects} one {{ideasCount} project} other {{ideasCount} projects}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} proposals} one {{ideasCount} proposal} other {{ideasCount} proposals}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} questions} one {{ideasCount} question} other {{ideasCount} questions}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, 0 {# inerneqanngilaq} one {# inerniliussaq} other {# inernerit}}", "app.containers.IdeasEditPage.contributionFormTitle": "Ilanngussaq <PERSON>", "app.containers.IdeasEditPage.editedPostSave": "Toqqoruk", "app.containers.IdeasEditPage.fileUploadError": "Fileq ataaseq arlall<PERSON>unniit ikkutinngitsoorput. <PERSON><PERSON> il<PERSON>u misissoriarlugit misileeqqigit.", "app.containers.IdeasEditPage.formTitle": "{tenantName, select, DeloitteDK {Ilanngussaq aaqqissoruk} other {Isumassarsiaq aaqqissoruk}}", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Ikkussat aaqqiissuteqarfigiuk. Nutaanik ilanngussigit paasissutissallu nutaanngitsut allanngortillugit.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Aaqqiissutit {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Edit initiative", "app.containers.IdeasEditPage.issueFormTitle": "Ajornart<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.optionFormTitle": "Qinigassaq a<PERSON>uk", "app.containers.IdeasEditPage.petitionFormTitle": "Edit petition", "app.containers.IdeasEditPage.projectFormTitle": "Suliannik aaqqiissuteqarfigiuk", "app.containers.IdeasEditPage.proposalFormTitle": "Edit proposal", "app.containers.IdeasEditPage.questionFormTitle": "Apeqqut a<PERSON>qarfigiuk", "app.containers.IdeasEditPage.save": "Toqqoruk", "app.containers.IdeasEditPage.submitApiError": "Immersukkavit nassiunniarnissaanut a<PERSON>lusoortoqarpoq. Kukkuneqarnersoq misissoriarlugu misileqqiguk.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "All inputs posted", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Ilanngussat {orgName} -imit peqataatitsinermut nittartakkamut iliorarsimasut misissuivigikkit", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Posts | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Ilanngussat", "app.containers.IdeasIndexPage.loadMore": "Arlallit immiutikkit…", "app.containers.IdeasIndexPage.loading": "<PERSON><PERSON><PERSON><PERSON> …", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "By default your submissions will be associated with your profile, unless you select this option.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Post anonymously", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Profile visibility", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "This survey is not currently open for responses. Please return to the project for more information.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "This survey is not currently active.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Return to project", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "You have already completed this survey.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Survey submitted", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Thanks for your response!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Ilann<PERSON><PERSON><PERSON><PERSON> nassuiaat {limit}-nit ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Isumassarsiap nassuiarnera uannga ta<PERSON>aq {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Il<PERSON><PERSON><PERSON><PERSON> quleq<PERSON> {limit}-nit ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "The contribution title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Please select at least one cosponsor", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Isumassarsiap nassui<PERSON> {limit}-nit ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Isumassarsiap nass<PERSON> {limit}-nit amerlanernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Please provide a description", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "The idea title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "The idea title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "The initiative description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "The initiative description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "The initiative title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "The initiative title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Ajornartorsiummut nassuiaat {limit}-nit ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Ajornartorsiummut nassuiaat {limit}-nit amerlanernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Ajorna<PERSON><PERSON><PERSON><PERSON> qulequtaa {limit}-nit ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "The issue title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_number_required": "This field is required, please enter a valid number", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nassuiaat {limit}-nit ikinnernik naqinnertaqa<PERSON>aq", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "The option description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "To<PERSON><PERSON><PERSON><PERSON><PERSON> qulequtaa {limit}-nit ikinnernik naqinnertaqa<PERSON>aq", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "The option title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Inussiarnersumik minnerpaamik tag ataaseq toqqaruk", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "The petition description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "The petition description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "The petition title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "The petition title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Suliniummut nassuiaat {limit}-nit ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Suliniummut nassuiaat {limit}-nit amerlanernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Suliniutip qulequtaa {limit}-nit ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "The project title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "The proposal description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "The proposal description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "The proposal title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "The proposal title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Inussiarnersumik kisitsimmik allataqarit", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Please enter a number", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "Apeqqum<PERSON>t nassuiaat {limit}-nit ikinnernik naqinnertaqa<PERSON>aq", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "Apeqqum<PERSON>t nassuiaat {limit}-nit amerlanernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "Apeqqutip qulequtaa {limit}-nit ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "The question title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Please provide a title", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Ilanngussamut nassuiaat 80-init ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Ilanngussamut nassuiaat minnerpaamik 30-nik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Ilanngussap qule<PERSON> 80-init ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Ilanngussap qulequtaa minnerpaamik 10-nik naqinnerta<PERSON>aq", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Isumassarsiap nassuiaataa 80-init ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "The idea description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Please provide a title", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Isumassarsiap qulequtaa 80-init ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "The idea title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_includes_banned_words": "{guidelinesLink} naapertorlugu oqaasipiluusinnaasunik ataasiakkaanik arlalinnilluunniit allassimarpasipputit. Inussiarnersumik oqaasipiluit tamaasa allatannit piikkit.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "The initiative description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "The initiative description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "The initiative title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "The initiative title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Ajornartorsiummut nassuiaat 80-init ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Ajornartorsiummut nassuiaat minnerpaamik 30-nik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Ajornartors<PERSON><PERSON> quleq<PERSON>a 80-init ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Ajornartorsiu<PERSON> qulequtaa minnerpaamik 10-nik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Toqqagassamut nassuiaat 80-init ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Toqqagassa<PERSON>t nassuiaat minnerpaamik 30-nik naqinnertaqa<PERSON>aq", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Toqqagassap qulequtaa minnerpaamik 80-inik naqinnertaqa<PERSON>aq", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Inassuteqaatip qulequtaa minnerpaamik qulinik naqinneqassaaq", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "The petition description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "The petition description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "The petition title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "The petition title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Sulinniummut nassuiaat 80-init ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Suliniummut nassuiaat minnerpaamik 30-nik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Suliniutip qulequtaa 80-init ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Suliniutip qulequtaa minnerpaamik 10-nik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "The proposal description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "The proposal description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "The proposal title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "The proposal title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Please provide a description", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Apeqqummut nassuiaat 80-init ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Apeqqum<PERSON>t nassuiaat minnerpaamik 30-nik naqinnerta<PERSON>aq", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Apeqqutip qulequtaa 80-init ikinnernik naqinnertaqassaaq", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Apeqqutip qulequtaa minnerpaamik 10-nik naqinnerta<PERSON>aq", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "Cancel", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "Yes, I want to leave", "app.containers.IdeasNewPage.contributionMetaTitle1": "Add new contribution to project | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Edit survey", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Ikkussat saqqumiuguk, {orgName}-illu peqataatitsinermut nittartagaani oqallinnermut peqataallutit.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Add new idea to project | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Add new initiative to project | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Add new issue to project | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Are you sure you want to leave?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Your draft answers have been saved privately and you can return to complete this later.", "app.containers.IdeasNewPage.leaveSurvey": "Leave survey", "app.containers.IdeasNewPage.leaveSurveyText": "Your answers won't be saved.", "app.containers.IdeasNewPage.optionMetaTitle1": "Add new option to project | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Add new petition to project | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Add new project to project | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Add new proposal to project | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Add new question to project | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Accept invitation", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Co-sponsorship invitation", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Co-sponsors", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "You have been invited to become a co-sponsor.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Invitation accepted", "app.containers.IdeasShow.Cosponsorship.pending": "pending", "app.containers.IdeasShow.MetaInformation.attachments": "<PERSON>lit <PERSON>ann<PERSON>", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} qanga {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON> killiffik", "app.containers.IdeasShow.MetaInformation.location": "<PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.postedBy": "Nassiunneqarpoq", "app.containers.IdeasShow.MetaInformation.similar": "Similar inputs", "app.containers.IdeasShow.MetaInformation.topics": "Sammisassat imaat", "app.containers.IdeasShow.commentCTA": "Oqaaseqaatit nassiutiguk", "app.containers.IdeasShow.contributionEmailSharingBody": "<PERSON><PERSON><PERSON><PERSON><PERSON> una '{postTitle}' {postUrl}-imi taperseruk!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Ilanngussaq una taperseruk: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Ilanngussat ilanngukkakku qujanaq!", "app.containers.IdeasShow.contributionTwitterMessage": "Ilanngussaq una taperseruk: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Ilanngussaq una taperseruk: {postTitle}", "app.containers.IdeasShow.currentStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON> killiffik", "app.containers.IdeasShow.deletedUser": "allagaqartoq ilisi<PERSON>q", "app.containers.IdeasShow.ideaEmailSharingBody": "Isumassars<PERSON> '{ideaTitle}' {ideaUrl}-imi taperseruk!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Isumassars<PERSON> taperseruk: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Isumassarsiaq una taperseruk: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Isumassarsiaq una taperseruk: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Ajornartorsiut una taperseruk: {postTitle}", "app.containers.IdeasShow.imported": "Imported", "app.containers.IdeasShow.importedTooltip": "This {inputTerm} was collected offline and automatically uploaded to the platform.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Support this initiative '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Support this initiative: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Thanks for submitting your initiative!", "app.containers.IdeasShow.initiativeTwitterMessage": "Support this initiative: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Support this initiative: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Ajorna<PERSON><PERSON>iut una '{postTitle}' {postUrl}-imi taperseruk!", "app.containers.IdeasShow.issueEmailSharingSubject": "Ajornartorsiut una taperseruk: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Ajornartorsiutigisat ilanngukkakku qujanaq!", "app.containers.IdeasShow.issueTwitterMessage": "Ajornartorsiut una taperseruk: {postTitle}", "app.containers.IdeasShow.metaTitle": "Input: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Qinigassaq una '{postTitle}' {postUrl}-imi taperseruk!", "app.containers.IdeasShow.optionEmailSharingSubject": "Qinigassaq una taperseruk: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Qinigassavit saqqumiunnera iluatsilluarpoq!", "app.containers.IdeasShow.optionTwitterMessage": "Qinigassaq una taperseruk: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Qinigassaq una taperseruk: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Support this petition '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Support this petition: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Thanks for submitting your petition!", "app.containers.IdeasShow.petitionTwitterMessage": "Support this petition: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Support this petition: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Suliaq una '{postTitle}' {postUrl}-imi taperseruk!", "app.containers.IdeasShow.projectEmailSharingSubject": "Suliaq una taperseruk: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "<PERSON><PERSON>t ilanngukkakku qujanaq!", "app.containers.IdeasShow.projectTwitterMessage": "Suliaq una taperseruk: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Suliaq una taperseruk: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Support this proposal '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Support this proposal: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Thanks for submitting your proposal!", "app.containers.IdeasShow.proposalTwitterMessage": "Support this proposal: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Support this proposal: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Time left to vote:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} out of {votingThreshold} required votes", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Cancel vote", "app.containers.IdeasShow.proposals.VoteControl.days": "days", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "our guidelines", "app.containers.IdeasShow.proposals.VoteControl.hours": "hours", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status and votes", "app.containers.IdeasShow.proposals.VoteControl.minutes": "mins", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "More info", "app.containers.IdeasShow.proposals.VoteControl.vote": "Vote", "app.containers.IdeasShow.proposals.VoteControl.voted": "Voted", "app.containers.IdeasShow.proposals.VoteControl.votedText": "You'll get notified when this initiative goes to the next step. {x, plural, =0 {There's {xDays} left.} one {There's {xDays} left.} other {There are {xDays} left.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Your vote has been submitted!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Unfortunately, you cannot vote on this proposal. Read why in {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {less than a day} one {one day} other {# days}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {no votes} one {1 vote} other {# votes}}", "app.containers.IdeasShow.questionEmailSharingBody": "{postUrl}-imi apeqqut question '{postTitle}' -ip oqa<PERSON>t peqata<PERSON>t", "app.containers.IdeasShow.questionEmailSharingSubject": "Oqallinnermut peqataagit: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Apeqqutigis<PERSON>t saqqummiunnera iluatsilluarpoq!", "app.containers.IdeasShow.questionTwitterMessage": "Oqallinnermut peqataagit: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Oqallinnermut peqataagit: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Sooq una spamisut nalu<PERSON>", "app.containers.IdeasShow.share": "Siammarteruk", "app.containers.IdeasShow.sharingModalSubtitle": "Amerlanerit anngukkit tusa<PERSON>llu", "app.containers.IdeasShow.sharingModalTitle": "Isumassarsiat ilanngukkakku qujanaq!", "app.containers.Navbar.completeOnboarding": "Complete onboarding", "app.containers.Navbar.completeProfile": "Prof<PERSON><PERSON>", "app.containers.Navbar.confirmEmail2": "Confirm email", "app.containers.Navbar.unverified": "Uppernarsineqanngilaq", "app.containers.Navbar.verified": "Uppernarseriigaq", "app.containers.NewAuthModal.beforeYouFollow": "Before you follow", "app.containers.NewAuthModal.beforeYouParticipate": "Before you participate", "app.containers.NewAuthModal.completeYourProfile": "Prof<PERSON><PERSON>", "app.containers.NewAuthModal.confirmYourEmail": "Confirm your email", "app.containers.NewAuthModal.logIn": "Iserit", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Review the terms below to continue.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Please complete your profile.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Go back to login options", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Don't have an account? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Code must have 4 digits.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Continue with FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "No authentication methods are enabled on this platform.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "By continuing, you agree to receive emails from this platform. You can select which emails you wish to receive in the 'My Settings' page.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "E-mail", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Provide an email address in the correct format, <NAME_EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "E-mail addressi allaguk", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Enter your email address to continue.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Kode isissutigisartakkat allanngortiguk?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Log in to your account: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Please enter your password", "app.containers.NewAuthModal.steps.Password.password": "Kode <PERSON>", "app.containers.NewAuthModal.steps.Password.rememberMe": "Remember me", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Do not select if using a public computer", "app.containers.NewAuthModal.steps.Success.allDone": "All done", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Now continue your participation.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Kinaassutsit uppernarsarneqarpoq. Platformimi uani ataats<PERSON>orn<PERSON>mut il<PERSON>sortanngorputit.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "You're now verified !", "app.containers.NewAuthModal.steps.close": "Close", "app.containers.NewAuthModal.steps.continue": "Continue", "app.containers.NewAuthModal.whatAreYouInterestedIn": "What are you interested in?", "app.containers.NewAuthModal.youCantParticipate": "You can't participate", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {no unviewed notifications} one{1 unviewed notification} other {# unviewed notifications}}", "app.containers.NotificationMenu.adminRightsReceived": "<PERSON><PERSON> illit isaa<PERSON><PERSON><PERSON>t a<PERSON>t", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Your comment on \"{postTitle}\" has been deleted by an admin because\n      {reasonCode, select, irrelevant {it is irrelevant} inappropriate {its content is inappropriate} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} accepted your co-sponsorship invitation", "app.containers.NotificationMenu.deletedUser": "Allattoq ilisi<PERSON>", "app.containers.NotificationMenu.error": "Na<PERSON>naaru<PERSON>t atua<PERSON>ng<PERSON>", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} commented internally on an input assigned to you", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} commented internally on an input that you commented on internally", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} commented internally on an input in a project you manage", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} commented internally on an unassigned input in an unmanaged project", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} commented on your internal comment", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} invited you to co-sponsor a contribution", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} invited you to co-sponsor an idea", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} invited you to co-sponsor an initiative", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} invited you to co-sponsor an issue", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} invited you to co-sponsor an option", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} invited you to co-sponsor a petition", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} invited you to co-sponsor a project", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} invited you to co-sponsor a proposal", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} invited you to co-sponsor a question", "app.containers.NotificationMenu.loadMore": "Arlallit immiutikkit…", "app.containers.NotificationMenu.loading": "Na<PERSON><PERSON><PERSON><PERSON>t amerlanerit atuakkit …", "app.containers.NotificationMenu.mentionInComment": "{name} o<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.mentionInInternalComment": "{name} mentioned you in an internal comment", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{officialName} pisortatigut nutarterinermi taasimavaatit", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "You didn't submit your survey", "app.containers.NotificationMenu.noNotifications": "Suli nalu<PERSON>aru<PERSON>manngilatit", "app.containers.NotificationMenu.notificationsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} gave an official update on a contribution you follow", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} gave an official update on an idea you follow", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} gave an official update on an initiative you follow", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} gave an official update on an issue you follow", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} gave an official update on an option you follow", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} gave an official update on a petition you follow", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} gave an official update on a project you follow", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} gave an official update on a proposal you follow", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} gave an official update on a question you follow", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} il<PERSON><PERSON> tun<PERSON>q", "app.containers.NotificationMenu.projectModerationRightsReceived": "<PERSON><PERSON> illit aqutsisunngorputit uunga {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} nikeriarfimmut nutaamut ingerlaqqippoq", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} nikeriarfik nutaaq a<PERSON>artissa<PERSON>a uani {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "A new project was published", "app.containers.NotificationMenu.projectReviewRequest": "{name} requested approval to publish the project \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} approved \"{projectTitle}\" for publication", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} status has changed to {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} taasisinnaasut amerlassuserisinaasaat anguneqarsimavoq", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} qaaqq<PERSON><PERSON>it a<PERSON>a", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} commented on a contribution that you follow", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} commented on an idea that you follow", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} commented on an initiative that you follow", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} commented on a issue that you follow", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} commented on an option that you follow", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} commented on a petition that you follow", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} commented on a project that you follow", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} commented on a proposal that you follow", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} commented on a question that you follow", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} reported \"{postTitle}\" as spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} oqaaseqa<PERSON>t qisuariarfigisimavaa", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} reported a comment on \"{postTitle}\" as spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "You didn't submit your votes", "app.containers.NotificationMenu.votingBasketSubmitted": "You voted successfully", "app.containers.NotificationMenu.votingLastChance": "Last chance to vote for {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} vote results revealed", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} il<PERSON><PERSON> tun<PERSON>neqa<PERSON>imasoq {postTitle} ", "app.containers.PasswordRecovery.emailError": "E-mail atuuppasinngilaq.", "app.containers.PasswordRecovery.emailLabel": "E-mail", "app.containers.PasswordRecovery.emailPlaceholder": "E-mailadressera", "app.containers.PasswordRecovery.helmetDescription": "Kodep isissutissap allanngortin<PERSON>sa<PERSON> qupperneq", "app.containers.PasswordRecovery.helmetTitle": "Kode isissutigisartakkat allanngortiguk", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "If this email address is registered on the platform, a password reset link has been sent.", "app.containers.PasswordRecovery.resetPassword": "Kodep isissutigisartakkap linkia nassiuguk.", "app.containers.PasswordRecovery.submitError": "E-mailimut uunga konto atasoq nassarinngilarput. Taarsiullugu allatsillutit misilissinnaavat.", "app.containers.PasswordRecovery.subtitle": "Nutaamik <PERSON>arnissamut linki sumut nassiutissavarput?", "app.containers.PasswordRecovery.title": "Kodemik isissutissamik allannguineq", "app.containers.PasswordReset.helmetDescription": "Kodep isissutissap allanngortin<PERSON>sa<PERSON> qupperneq", "app.containers.PasswordReset.helmetTitle": "Kode isissutit allanngortiguk", "app.containers.PasswordReset.login": "Log in", "app.containers.PasswordReset.passwordError": "Kode isissutigisartakkat minnerpaamik 8-nik naqinneqassaaq", "app.containers.PasswordReset.passwordLabel": "Kode <PERSON>", "app.containers.PasswordReset.passwordPlaceholder": "Kode isissuti<PERSON>aaq", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Your password has been successfully updated.", "app.containers.PasswordReset.pleaseLogInMessage": "Please log in with your new password.", "app.containers.PasswordReset.requestNewPasswordReset": "Nutaamik isertaatiliornissaq qinnutigiuk", "app.containers.PasswordReset.submitError": "<PERSON><PERSON><PERSON><PERSON> il<PERSON>git<PERSON>orpoq. Ungasinngitsumi misilee<PERSON>a", "app.containers.PasswordReset.title": "Kode isissutigisartakkat allanngortiguk", "app.containers.PasswordReset.updatePassword": "Kode isissutigisartakkat allaqqiguk", "app.containers.ProjectFolderCards.allProjects": "Suliniutit tamarmik", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} maanna una suliarivaa", "app.containers.ProjectFolderShowPage.editFolder": "Ilioraavik a<PERSON>qi<PERSON>uguk", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Suliniut una pillugu paasissutissat", "app.containers.ProjectFolderShowPage.metaTitle1": "Folder: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Atuarnerugit", "app.containers.ProjectFolderShowPage.seeLess": "Se mindre", "app.containers.ProjectFolderShowPage.share": "Siammarteruk", "app.containers.Projects.PollForm.document": "Document", "app.containers.Projects.PollForm.formCompleted": "Taasineq una akigakku qujanaq!", "app.containers.Projects.PollForm.maxOptions": "annerpaamik {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "You've already taken this poll.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "This poll can only be taken when this phase is active.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Ajoraluartumik taasisinnaatitaanngilatit.", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Maannak<PERSON>t ta<PERSON>.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Suliniut unissimammat taasisoqarsinnaajunnaarpoq.", "app.containers.Projects.PollForm.sendAnswer": "Nassiutiguk", "app.containers.Projects.a11y_phase": "Nikeriarfik {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Nikeriarfinnut ta<PERSON>", "app.containers.Projects.a11y_titleInputs": "Ilanngussat tamarmik suliamut uunga ilanngunneqarput", "app.containers.Projects.a11y_titleInputsPhase": "Ilanngussat tamarmik immikkoortumut uunga ilanngunneqarput", "app.containers.Projects.accessRights": "Access rights", "app.containers.Projects.addedToBasket": "Koorinniittunut <PERSON>annguguk", "app.containers.Projects.allocateBudget": "Fordel dit budget", "app.containers.Projects.archived": "Allagaasivimmut inissinneqarpoq", "app.containers.Projects.basketSubmitted": "Koorinniittut nassiunneqarp<PERSON>!", "app.containers.Projects.contributions": "Ilanngussat", "app.containers.Projects.createANewPhase": "Create a new phase", "app.containers.Projects.currentPhase": "Igangværende fase", "app.containers.Projects.document": "Document", "app.containers.Projects.editProject": "Rediger dette projekt", "app.containers.Projects.emailSharingBody": "Sul<PERSON>ut una qanoq igaajuk? Taajuk oqalo<PERSON>inn<PERSON>lu uani {initiativeUrl} tusaaneqarumallutit siammaruk!", "app.containers.Projects.emailSharingSubject": "Suliniutiga tapersersoruk: {initiativeTitle}.", "app.containers.Projects.endedOn": "Ulloq {date} taamaatippoq", "app.containers.Projects.events": "Pisussat", "app.containers.Projects.header": "Suliniutit ", "app.containers.Projects.ideas": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.information": "Paasissutissat", "app.containers.Projects.initiatives": "Initiatives", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Review the document", "app.containers.Projects.invisibleTitlePhaseAbout": "Nikeriarfik una pillugu", "app.containers.Projects.invisibleTitlePoll": "Taasigit", "app.containers.Projects.invisibleTitleSurvey": "Apeqqutit akikkit ", "app.containers.Projects.issues": "Ajornartorsiu<PERSON><PERSON>", "app.containers.Projects.liveDataMessage": "You're viewing real-time data. Participant counts are continuously updated for administrators. Please note that regular users see cached data, which may result in slight differences in the numbers.", "app.containers.Projects.location": "<PERSON><PERSON><PERSON><PERSON>:", "app.containers.Projects.manageBasket": "Koorinniittut ingerlateqqikkit", "app.containers.Projects.meetMinBudgetRequirement": "<PERSON><PERSON>jumallug<PERSON> missingersuutitut minnerpaaffioqquneqartoq eqqortissimassavat.", "app.containers.Projects.meetMinSelectionRequirement": "<PERSON><PERSON> na<PERSON><PERSON> toqqaq<PERSON>ut toqqa<PERSON>it.", "app.containers.Projects.metaTitle1": "Project: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Missingersuutinut minnerpaaffigeqquneqartoq", "app.containers.Projects.myBasket": "Basket", "app.containers.Projects.navPoll": "Taasineq", "app.containers.Projects.navSurvey": "Immersuilluni apersuineq", "app.containers.Projects.newPhase": "New phase", "app.containers.Projects.nextPhase": "<PERSON><PERSON>ste fase", "app.containers.Projects.noEndDate": "No end date", "app.containers.Projects.noItems": "Pisiassanik suli to<PERSON>manngilatit", "app.containers.Projects.noPastEvents": "No past events to display", "app.containers.Projects.noPhaseSelected": "Nikeriarfik toqqarne<PERSON>iman<PERSON>", "app.containers.Projects.noUpcomingOrOngoingEvents": "No upcoming or ongoing events are currently scheduled.", "app.containers.Projects.offlineVotersTooltip": "This number does not reflect any offline voter counts.", "app.containers.Projects.options": "<PERSON>iar<PERSON><PERSON><PERSON>", "app.containers.Projects.participants": "Participants", "app.containers.Projects.participantsTooltip4": "This number also reflects anonymous survey submissions. Anonymous survey submissions are possible if surveys are open to everyone (see the {accessRightsLink} tab for this project).", "app.containers.Projects.pastEvents": "Past events", "app.containers.Projects.petitions": "Petitions", "app.containers.Projects.phases": "<PERSON><PERSON><PERSON>", "app.containers.Projects.previousPhase": "Foregående fase", "app.containers.Projects.project": "Suliniut", "app.containers.Projects.projectTwitterMessage": "Isummatit tusartikkit! {projectName}-imut peqataagit | {orgName}", "app.containers.Projects.projects": "Suliniutit", "app.containers.Projects.proposals": "Proposals", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> takuuk", "app.containers.Projects.readLess": "Atuagassaq annikillisi<PERSON>k", "app.containers.Projects.readMore": "Atuarnerugit", "app.containers.Projects.removeItem": "Toqqagak peeruk", "app.containers.Projects.requiredSelection": "Toqqaqquneqartoq", "app.containers.Projects.reviewDocument": "Review the document", "app.containers.Projects.seeTheContributions": "Ajornartorsiutit takukkit", "app.containers.Projects.seeTheIdeas": "Se ideerne", "app.containers.Projects.seeTheInitiatives": "See the initiatives", "app.containers.Projects.seeTheIssues": "Suliat takukkit", "app.containers.Projects.seeTheOptions": "Qinigassat takukkit", "app.containers.Projects.seeThePetitions": "See the petitions", "app.containers.Projects.seeTheProjects": "Suliat takukkit", "app.containers.Projects.seeTheProposals": "See the proposals", "app.containers.Projects.seeTheQuestions": "Apeqqutit takukkit", "app.containers.Projects.seeUpcomingEvents": "See upcoming events", "app.containers.Projects.share": "Del", "app.containers.Projects.shareThisProject": "Del dette projekt", "app.containers.Projects.submitMyBasket": "Submit basket", "app.containers.Projects.survey": "Immersuilluni apersuineq", "app.containers.Projects.takeThePoll": "Tag afstemningen", "app.containers.Projects.takeTheSurvey": "<PERSON><PERSON><PERSON> spørgeskema<PERSON>", "app.containers.Projects.timeline": "Tidslinje", "app.containers.Projects.upcomingAndOngoingEvents": "Upcoming and ongoing events", "app.containers.Projects.upcomingEvents": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "app.containers.Projects.whatsAppMessage": "{projectName} | from the participation platform of {orgName}", "app.containers.Projects.yourBudget": "Total budget", "app.containers.ProjectsIndexPage.metaDescription": "Uani {orgName} suliniutit ingerlasut tamaasa paasiniarlugu sumi peqataasinnaanerlutit misissukkit. Sumiiffimmi suliniutit ilinnut pingaarutillit oqallisiginiarlugit aggerit. ", "app.containers.ProjectsIndexPage.metaTitle1": "Projects | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Suliniutit ", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Nammineq kaju<PERSON>sutsinnik sulerusuppunga", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Uunga sammisamut nammineq kajumissutsinnik suleqataarusukkuit inussiarnersumik iserit{signInLink} imaluunniit nalunaarit {signUpLink} ", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Participation is not currently open for this activity.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "Iserneq", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "nalunaars<PERSON>t", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Nammeq ka<PERSON>tsinn<PERSON> suleqa<PERSON>arusun<PERSON>a at<PERSON>inn<PERSON>a", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {no frivillige} one {# frivillig} other {# frivillige}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Warning: The embedded survey may have accessibility issues for screenreader users. If you experience any challenges, please reach out to the platform admin to receive a link to the survey from the original platform. Alternatively, you can request other ways to fill out the survey.", "app.containers.ProjectsShowPage.process.survey.survey": "Immersuilluni apersuineq", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Misissuinermut uunga peqataasinnaanerlutit paasiniarukku isaaffimmut uunga iseqqaartariaqarputit {logInLink}.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Misissuineq una aatsaat ingerlanneqarsinnaavoq nikeriarfik piffissamut nalunaarsuiffik aatsaat ikissimappat", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Please {completeRegistrationLink} to take the survey.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Ajoraluartumik uuminnga misissuisinnaatitaanngilatit.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Uuminnga misissuiniaruit kontut uppernarsaqqaartariaqarpat.\n{verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "The survey is no longer available, since this project is no longer active.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "complete registration", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "log in", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "sign up", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Verify your account now.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Only certain users can review this document. Please {signUpLink} or {logInLink} first.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "This document can only be reviewed when this phase is active.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Please {completeRegistrationLink} to review the document.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Unfortunately, you don't have the rights to review this document.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Reviewing this document requires verification of your account. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "The document is no longer available, since this project is no longer active.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 pick} other {# picks}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "The percentage of participants who picked this option.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "The percentage of total votes this option received.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Cost:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Show more", "app.containers.ReactionControl.a11y_likesDislikes": "Total likes: {likesCount}, total dislikes: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "You cancelled your dislike for this input successfully.", "app.containers.ReactionControl.cancelLikeSuccess": "You cancelled your like for this input successfully.", "app.containers.ReactionControl.dislikeSuccess": "You disliked this input successfully.", "app.containers.ReactionControl.likeSuccess": "You liked this input successfully.", "app.containers.ReactionControl.reactionErrorSubTitle": "Due to an error your reaction could not being registered. Please try again in a few minutes.", "app.containers.ReactionControl.reactionSuccessTitle": "Your reaction was successfully registered!", "app.containers.ReactionControl.vote": "Vote", "app.containers.ReactionControl.voted": "Voted", "app.containers.SearchInput.a11y_cancelledPostingComment": "Cancelled posting comment.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} comments have loaded.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# events have loaded} one {# event has loaded} other {# events have loaded}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# results have loaded} one {# result has loaded} other {# results have loaded}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# search results have loaded} one {# search result has loaded} other {# search results have loaded}}.", "app.containers.SearchInput.removeSearchTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>uk", "app.containers.SearchInput.searchAriaLabel": "Ujaruk", "app.containers.SearchInput.searchLabel": "Search", "app.containers.SearchInput.searchPlaceholder": "Ujaruk", "app.containers.SearchInput.searchTerm": "Misissuinermut taaguut: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "Franskit naalagaaffiata internettikkut kiffartuussissutinut 700-nit amerlanerusunut ilannguttarneq qulakkeerniarlugu ajornaallisarniarlugulu FranceConnect aaqqiissutissatut siunnersuutigaa.", "app.containers.SignIn.or": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SignIn.signInError": "Paasissutissat allanneqartut eqqortuunngillat. <PERSON><PERSON><PERSON> \"password\" pu<PERSON><PERSON>a tooruk nutaamik taarserniarukku.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Iserniaruit, atuisumik pilersitsiniaruit imaluunniit atuisuuffiit uppernarsarniarukku FranceConnect atoruk.", "app.containers.SignIn.whatIsFranceConnect": "France Connect sunaana?", "app.containers.SignUp.adminOptions2": "For admins and project managers", "app.containers.SignUp.backToSignUpOptions": "Go back to sign up options", "app.containers.SignUp.continue": "Continue", "app.containers.SignUp.emailConsent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nittartakkamiit uannga e-mailinik nassitsittarnissat akuerissavat. Atuisutut nalunaarsuinikkut e-mailit sorliit tigusassallugit kissaatiginerlugit toqqaasinnaavutit.", "app.containers.SignUp.emptyFirstNameError": "Atit allaguk", "app.containers.SignUp.emptyLastNameError": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.firstNamesLabel": "Ateq", "app.containers.SignUp.goToLogIn": "Already have an account? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "I have read and agree to {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "I have read and agree to {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "I accept that the data will be used on mitgestalten.wien.gv.at. Further information can befound {link}.", "app.containers.SignUp.invitationErrorText": "Your invitation has expired or has already been used. If you have already used the invitation link to create an account, try signing in. Otherwise, sign up to create a new account.", "app.containers.SignUp.lastNameLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Follow your areas of focus to be notified about them:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Follow your favorite topics to be notified about them:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Save preferences", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Skip for now", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Ingerlaqqinnissannut inuttut inuuneq pillugu politikkerput akueriuk", "app.containers.SignUp.signUp2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.skip": "Alloriarneq una qarsuguk", "app.containers.SignUp.tacError": "Ingerlaqqinniaraanni atugassarititatta piumasattalu akuerinissaat pisariaqarpoq", "app.containers.SignUp.thePrivacyPolicy": "inuttut inuuneq pillugu politikki", "app.containers.SignUp.theTermsAndConditions": "pium<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.unknownError": "<PERSON><PERSON><PERSON><PERSON> il<PERSON>git<PERSON>orpoq. Ungasinngitsumi misilee<PERSON>a", "app.containers.SignUp.viennaConsentEmail": "Mail<PERSON><PERSON><PERSON>", "app.containers.SignUp.viennaConsentFirstName": "First name", "app.containers.SignUp.viennaConsentFooter": "You can change your profile information after sign-in. If you already have an account with the same email address on mitgestalten.wien.gv.at, it will be linked with your current account.", "app.containers.SignUp.viennaConsentHeader": "The following data will be transmitted:", "app.containers.SignUp.viennaConsentLastName": "Last name", "app.containers.SignUp.viennaConsentUserName": "User name", "app.containers.SignUp.viennaDataProtection": "the vienna privacy policy", "app.containers.SiteMap.contributions": "Ilanngussat", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "Ajornartorsiu<PERSON><PERSON>", "app.containers.SiteMap.options": "<PERSON>iar<PERSON><PERSON><PERSON>", "app.containers.SiteMap.projects": "Suliniutit", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> takuuk", "app.containers.SpamReport.buttonSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSuccess": "iluatsilluarpoq", "app.containers.SpamReport.inappropriate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> naleqqun<PERSON>it", "app.containers.SpamReport.messageError": "Immersukkap nassi<PERSON> k<PERSON>, mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t.", "app.containers.SpamReport.messageSuccess": "Na<PERSON><PERSON>aru<PERSON>t nassiunneqarp<PERSON>.", "app.containers.SpamReport.other": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.otherReasonPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.wrong_content": "<PERSON> ma<PERSON>", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Profilinnut assi peeruk", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Your votes on proposals that are still open for voting will be deleted. Votes on proposals where the voting period has closed will not be deleted.", "app.containers.UsersEditPage.addPassword": "Add password", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Innuttaasunit suliniutinut akuerisaasimasunut peqataaniaraanni", "app.containers.UsersEditPage.becomeVerifiedTitle": "{tenantName, select, DeloitteDK {Atuisuunerit uppernarsaruk} other {Kinaassutsit uppernarsaruk}}", "app.containers.UsersEditPage.bio": "<PERSON><PERSON>", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Una allaffissaq aaqqissuussinnaanngilat akuerisanik paasissutissartaqarmat", "app.containers.UsersEditPage.buttonSuccessLabel": "iluatsilluarpoq", "app.containers.UsersEditPage.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.changeEmail": "Change email", "app.containers.UsersEditPage.changePassword2": "Change password", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Akuersinerit nutarterniarukku inussiarnersumik una tooruk.", "app.containers.UsersEditPage.conditionsLinkText": "<PERSON><PERSON><PERSON> p<PERSON>t", "app.containers.UsersEditPage.contactUs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nut peqqut? <PERSON> {feedbackLink}, ta<PERSON> immaqa i<PERSON>orsinnaavatsi<PERSON>.", "app.containers.UsersEditPage.deleteAccountSubtext": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qima<PERSON>atsigut", "app.containers.UsersEditPage.deleteMyAccount": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.deleteYourAccount": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.deletionSection": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.deletionSubtitle": "Iliuuseq una aaqqeqqinneqarsinnaanngilaq. Isaaffimmut imarisat saqqummersitatit kinaassutsit pillugu ilisarnarunnaarsinneqassapput. Imarisat tamaasa peerusukkukkit <NAME_EMAIL>.", "app.containers.UsersEditPage.email": "E-mail", "app.containers.UsersEditPage.emailEmptyError": "E-mail addressi allaguk", "app.containers.UsersEditPage.emailInvalidError": "Provide an email address in the correct format, <NAME_EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "<PERSON>", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Ateq", "app.containers.UsersEditPage.firstNamesEmptyError": "<PERSON><PERSON><PERSON><PERSON> siulleq allaguk", "app.containers.UsersEditPage.h1": "Kontuit p<PERSON>tissartai", "app.containers.UsersEditPage.h1sub": "Kontuit paasissutissartai aaqqissuutikkit", "app.containers.UsersEditPage.image": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.imageDropzonePlaceholder": "profilinnut asseq toqqarniarlugu tooruk (annerpaamik 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Kontunnut inissiissutit tamarmik", "app.containers.UsersEditPage.language": "Oqaatsit ", "app.containers.UsersEditPage.lastName": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.lastNameEmptyError": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.loading": "<PERSON><PERSON><PERSON><PERSON> …", "app.containers.UsersEditPage.loginCredentialsSubtitle": "You can change your email or password here.", "app.containers.UsersEditPage.loginCredentialsTitle": "Login credentials", "app.containers.UsersEditPage.messageError": "Profilit toqqorsinnaanngilarput. Kingusinnerulaartukkut misileqqigit imaluunniit <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.metaDescription": "Una qupperneq tassaavoq uuma {firstName} {lastName} peqataasut isaaffianni profilianut inissiissutinik uunga allattuiffik {tenantName}. Uani kinaassutsit uppernarsarsinnaavat, kontunnut paasissutissat aaqqissinnaavatit, kontut peersinnaavat aamma e-mailinik nassiusarninnut/pisarninnut paasissutissat aaqqissinnaavatit. ", "app.containers.UsersEditPage.metaTitle1": "Profile settings page of {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Una tuugassaq toorukku taava kontut pilerse<PERSON>naanngilarput.", "app.containers.UsersEditPage.noNameWarning2": "Your name is currently displayed on the platform as: \"{displayName}\" because you have not entered your name. This is an autogenerated name. If you would like to change it, please enter your name below.", "app.containers.UsersEditPage.notificationsSubTitle": "Nalunaarutit qanoq ittut e-mailikkut pisarusuppigit? ", "app.containers.UsersEditPage.notificationsTitle": "E-mail notifikationer", "app.containers.UsersEditPage.password": "Kode isissutissat nutaaq allaguk", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Provide a password that is at least {minimumPasswordLength} characters long", "app.containers.UsersEditPage.passwordAddSection": "Add a password", "app.containers.UsersEditPage.passwordAddSubtitle2": "Set a password and easily login to the platform, without having to confirm your email every time.", "app.containers.UsersEditPage.passwordChangeSection": "Change your password", "app.containers.UsersEditPage.passwordChangeSubtitle": "Confirm your current password and change to new password.", "app.containers.UsersEditPage.privacyReasons": "Nammineq inuuneqarnerit ernummatigigukku uani atuarsinnaavutit {conditionsLink}.", "app.containers.UsersEditPage.processing": "Nassiunneqarpoq ..", "app.containers.UsersEditPage.provideFirstNameIfLastName": "First name is required when providing last name", "app.containers.UsersEditPage.reasonsToStayListTitle": "Aninn<PERSON><PERSON><PERSON> ..", "app.containers.UsersEditPage.submit": "Allannguutit toq<PERSON>t", "app.containers.UsersEditPage.tooManyEmails": "Amerlavallaanik e-mailisisarpit? E-mailisiffigisartakkatit profilinnut inissiissutini aaqqissuussinnaavatit.", "app.containers.UsersEditPage.updateverification": "Pisortatigut paasissutissaatitit allanngortikkakkit? {reverifyButton}", "app.containers.UsersEditPage.user": "Nalunaarfiginiarlutit e-mailernissarput qanoq ilinerani nassiutsikkusuppiuk?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Suliniutit innuttaasunuinnaq peqataaffigineqarsinnaasut peqataaffigisinnaavatit.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Innuttaasutut peqataasutut nalu<PERSON>orn<PERSON>qa<PERSON>utit", "app.containers.UsersEditPage.verifyNow": "<PERSON><PERSON>", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Download your responses (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {no likes} one {1 like} other {# likes}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uuma sumi saqqummiun<PERSON><PERSON><PERSON><PERSON><PERSON> pillugu saqqummiussigit:", "app.containers.UsersShowPage.areas": "Areas", "app.containers.UsersShowPage.commentsWithCount": "Oqaaseqaatit ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Profilera a<PERSON>", "app.containers.UsersShowPage.emptyInfoText": "You are not following any items of the specified filter above.", "app.containers.UsersShowPage.eventsWithCount": "Events ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Following ({followingCount})", "app.containers.UsersShowPage.inputs": "Inputs", "app.containers.UsersShowPage.invisibleTitlePostsList": "Ikkussat tamarmik uuminnga peqataasumit ilanngunneqartut", "app.containers.UsersShowPage.invisibleTitleUserComments": "Atuisumit uuminnga isumasssarsiat siunnersuutigineqarsimasut tamarmik", "app.containers.UsersShowPage.loadMore": "<PERSON><PERSON>", "app.containers.UsersShowPage.loadMoreComments": "Isumassarsiat amerlanerit a<PERSON>t", "app.containers.UsersShowPage.loadingComments": "Oqaaseqaatit a<PERSON>...", "app.containers.UsersShowPage.loadingEvents": "Loading events...", "app.containers.UsersShowPage.memberSince": "Ilaasortanut qupperneq {date}", "app.containers.UsersShowPage.metaTitle1": "Profile page of {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Inuk una suli oqa<PERSON>.", "app.containers.UsersShowPage.noCommentsForYou": "<PERSON><PERSON> suli o<PERSON>.", "app.containers.UsersShowPage.noEventsForUser": "You have not attended any events yet.", "app.containers.UsersShowPage.postsWithCount": "Sa<PERSON><PERSON><PERSON><PERSON>ussat ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Project folders", "app.containers.UsersShowPage.projects": "Projects", "app.containers.UsersShowPage.proposals": "Proposals", "app.containers.UsersShowPage.seePost": "Ilanngussat takukkit", "app.containers.UsersShowPage.surveyResponses": "Responses ({responses})", "app.containers.UsersShowPage.topics": "Topics", "app.containers.UsersShowPage.tryAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, king<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mi<PERSON>k<PERSON>.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Una {firstName} {lastName} -ip peqata<PERSON>ts<PERSON><PERSON>t nittartagaani {orgName} -ip ilisarisitsissutigaa. Uani saqqummiuneqartut takuneqarsinaapput.", "app.containers.VoteControl.close": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.VoteControl.voteErrorTitle": "Arlaanik ajortoqarpoq.", "app.containers.admin.ContentBuilder.default": "default", "app.containers.admin.ContentBuilder.imageTextCards": "Image & text cards", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info & accordions", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 column", "app.containers.admin.ContentBuilder.projectDescription": "Project description", "app.containers.app.navbar.admin": "Aqutsisoq", "app.containers.app.navbar.allProjects": "Suliniutit tamarmik", "app.containers.app.navbar.ariaLabel": "Pingaarneq", "app.containers.app.navbar.closeMobileNavMenu": "Close mobile navigation menu", "app.containers.app.navbar.editProfile": "Inissiissutit", "app.containers.app.navbar.fullMobileNavigation": "Full mobile", "app.containers.app.navbar.logIn": "Iserit", "app.containers.app.navbar.logoImgAltText": "{orgName} Sa<PERSON><PERSON><PERSON>t", "app.containers.app.navbar.myProfile": "Profilera", "app.containers.app.navbar.search": "Ujaruk", "app.containers.app.navbar.showFullMenu": "Show full menu", "app.containers.app.navbar.signOut": "<PERSON><PERSON><PERSON>", "app.containers.eventspage.errorWhenFetchingEvents": "Pisussat saqqummersinniarneranni kukkuneqarpoq. Qupperneq immiuteqqiuk.", "app.containers.eventspage.events": "Events", "app.containers.eventspage.eventsPageDescription": "Pisussat attaveqatigiittarfimmi {orgName} nassiunneqarsimasut tamaasa saqqummersikkit.", "app.containers.eventspage.eventsPageTitle1": "Events | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Suliniutit ", "app.containers.eventspage.noPastEvents": "Pereersimasunik saqqum<PERSON>saqanngilaq", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Massakkut piffissami aggersumi imaluunniit ingerlasumik pisussaqanngilaq.", "app.containers.eventspage.pastEvents": "Siusinnerusukkut pisimasut", "app.containers.eventspage.upcomingAndOngoingEvents": "Piffissami aggersumi pisussat imaluunniit massakkut ingerlasut", "app.containers.footer.accessibility-statement": "Accessibility statement", "app.containers.footer.ariaLabel": "Pingaarnerup tullia", "app.containers.footer.cookie-policy": "Cookiet pillugit politik", "app.containers.footer.cookieSettings": "Cookiet pillugit inissiissutit", "app.containers.footer.feedbackEmptyError": "Una allassimasoqassaaq", "app.containers.footer.poweredBy": "Uuminnga ingerlan<PERSON>rp<PERSON>q", "app.containers.footer.privacy-policy": "Nammineq inuunermut politikki", "app.containers.footer.siteMap": "Sitemap", "app.containers.footer.terms-and-conditions": "Atugassarititaasut", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "Cancel", "app.containers.ideaHeading.confirmLeaveFormButtonText": "Yes, I want to leave", "app.containers.ideaHeading.editForm": "Edit form", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Are you sure you want to leave?", "app.containers.ideaHeading.leaveIdeaForm": "Leave idea form", "app.containers.ideaHeading.leaveIdeaText": "Your responses won't be saved.", "app.containers.landing.cityProjects": "Suliniutit ", "app.containers.landing.completeProfile": "Prof<PERSON><PERSON>", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {firstName}. Profili<PERSON>t <PERSON> piff<PERSON>gorpo<PERSON>. ", "app.containers.landing.createAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.landing.defaultSignedInMessage": "{orgName}-p tusa<PERSON>ar<PERSON>atit. <PERSON><PERSON> tusa<PERSON>rnissannut piffissanngorpoq!", "app.containers.landing.doItLater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.landing.new": "<PERSON><PERSON><PERSON>", "app.containers.landing.subtitleCity": "Isumassarsiat a<PERSON>tseqatigiuk!", "app.containers.landing.titleCity": "Suleqataagit {orgName}", "app.containers.landing.twitterMessage": "{tenantName, select, DeloitteDK {Isumarnik ikkussigit {ideaTitle}} other {Uani {postTitle} toqqaagit}}", "app.containers.landing.upcomingEventsWidgetTitle": "Upcoming and ongoing events", "app.containers.landing.userDeletedSubtitle": "Qaqugukkulluunniit nutaamik kontoliorsinnaavutit {contactLink} sun<PERSON>u <PERSON>ut oqa<PERSON>ull<PERSON>. ", "app.containers.landing.userDeletedSubtitleLinkText": "allaffigitigut", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "<PERSON><PERSON><PERSON>", "app.containers.landing.userDeletionFailed": "Kontuit peer<PERSON><PERSON>a a<PERSON>, ajorna<PERSON><PERSON><PERSON><PERSON> sunaan<PERSON>q paasitinneqarsimavugut aamma sapinngisarput tamaat aaqqiiv<PERSON>ssavarput. Kinguninngua misileeqqikkina.", "app.containers.landing.verifyNow": "<PERSON><PERSON>", "app.containers.landing.verifyYourIdentity": "Innuttaasutut uppernarsar<PERSON>allugu ilaa<PERSON>ngo<PERSON>", "app.containers.landing.viewAllEventsText": "<PERSON><PERSON>t tamaasa saqqummersikkit", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Back to folder", "app.errors.after_end_at": "<PERSON><PERSON><PERSON><PERSON><PERSON> ullup na<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.errors.avatar_carrierwave_download_error": "<PERSON>ili<PERSON>t timitaliaq a<PERSON>.", "app.errors.avatar_carrierwave_integrity_error": "Fiilimut timitaliaq fiilit suussusaannut akuerisatut ilaanng<PERSON>q.", "app.errors.avatar_carrierwave_processing_error": "Timitaliaq suliarineqarsinnaanngilaq.", "app.errors.avatar_extension_blacklist_error": "<PERSON>ili<PERSON>t timitaliap assinga fiilit suussusaannut akuerisanut ilaan<PERSON>q. Fiilit atorneqarsinnaasut suussusaar: jpg, jpeg, gif a<PERSON> png.", "app.errors.avatar_extension_whitelist_error": "<PERSON>ili<PERSON>t timitaliap assinga fiilit suussusaannut akuerisanut ilaan<PERSON>q. Fiilit atorneqarsinnaasut suussusaar: jpg, jpeg, gif a<PERSON> png.", "app.errors.banner_cta_button_multiloc_blank": "Allatamik toorneqarsinnaasumik ilanngussigit.", "app.errors.banner_cta_button_url_blank": "<PERSON>.", "app.errors.banner_cta_button_url_url": "Enter a valid link. Make sure the link starts with 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Enter a button text.", "app.errors.banner_cta_signed_in_url_blank": "Enter a link.", "app.errors.banner_cta_signed_in_url_url": "Enter a valid link. Make sure the link starts with 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Enter a button text.", "app.errors.banner_cta_signed_out_url_blank": "Enter a link.", "app.errors.banner_cta_signed_out_url_url": "Enter a valid link. Make sure the link starts with 'https://'.", "app.errors.base_includes_banned_words": "You may have used one or more words that are considered profanity. Please alter your text to remove any profanities that might be present.", "app.errors.body_multiloc_includes_banned_words": "The description contains words that are considered inappropriate.", "app.errors.bulk_import_idea_not_valid": "The resulting idea is not valid: {value}.", "app.errors.bulk_import_image_url_not_valid": "No image could be downloaded from {value}. Make sure the URL is valid and ends with a file extension such as .png or .jpg. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Idea location with a missing coordinate in {value}. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Idea location with a non-numeric coordinate in {value}. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_malformed_pdf": "The uploaded PDF file appears to be malformed. Try exporting the PDF again from your source and then upload again.", "app.errors.bulk_import_maximum_ideas_exceeded": "The maximum of {value} ideas has been exceeded.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "The maximum of {value} pages in a PDF has been exceeded.", "app.errors.bulk_import_not_enough_pdf_pages": "The uploaded PDF does not have enough pages - it should have at least the same number of pages as the downloaded template.", "app.errors.bulk_import_publication_date_invalid_format": "Idea with invalid publication date format \"{value}\". Please use the format \"DD-MM-YYYY\".", "app.errors.cannot_contain_ideas": "<PERSON><PERSON><PERSON><PERSON><PERSON> uuma imarai {ideasCount, plural, one {one idea}other {{ideasCount} ideas}} a<PERSON>ma peqa<PERSON><PERSON>t periutsip, allanngortinniakkavit isumassarsiat ikorfartunngilaat. Peeruk {ideasCount, plural, one {the idea}other {the ideas}} nikeriarfimmit, misilee<PERSON><PERSON><PERSON>u.", "app.errors.cant_change_after_first_response": "Una allanngorteqqi<PERSON>, atuisut allat qisuariareersimammata", "app.errors.category_name_taken": "Kategorimik taamatut atsikkamik peqareerpoq", "app.errors.confirmation_code_expired": "Isissutissaq qaangiuppoq. Isissutissamik allamik nassiusseqqigit.", "app.errors.confirmation_code_invalid": "Uppernarsaanermi isissutissaq atorsinnaanngilaq. Isissutissaq eqqortoq atorniarlugu emaili misissoruk imaluunniit ‘Isissutissaq Nutaaq’ misiliguk", "app.errors.confirmation_code_too_many_resets": "Uppernarsaanermi isissutissaq arlaleriarlugu nassiussimavat. Qaaqquneqarnikkut isissutissamik piniarnikkut attavigisigut.", "app.errors.confirmation_code_too_many_retries": "Misiliivallaarsimavutit. Isissutissamik allamik qinugit, imaluunniit emailit allanngortillugu misiliguk.", "app.errors.email_already_active": "E-mailadresse {value} titarnerni quleri<PERSON> nassa<PERSON> {row} atuisup allap pigereerpaa", "app.errors.email_already_invited": "E-mailadresse {value} titarnerni qule<PERSON> nassa<PERSON> {row} qaaqquneqareersimavoq", "app.errors.email_blank": "Una allassimasoqassaaq", "app.errors.email_domain_blacklisted": "E-mailip <PERSON>t atuuffia alla atorlugu isertaatiliorit.", "app.errors.email_invalid": "E-mail atorsinnaasoq allalaaruk", "app.errors.email_taken": "Konto taama e-maililik <PERSON>. Isiinnarsinnaavutit.", "app.errors.email_taken_by_invite": "{value} allamit <PERSON>tinneqarnissaminik utaqqisumit atorneqareersimavoq. Spam-indbakkit takujuk, imaluunniit nassaarisinnaanngikkukku <NAME_EMAIL>.", "app.errors.emails_duplicate": "E-mailadressimut uunga {value} assingusut duplikatværdit ataaseq arlallilluunniit titarnerni quleriiaani ukunani nassaarineqarput(r): {rows}", "app.errors.extension_whitelist_error": "The format of the file you tried to upload is not supported.", "app.errors.file_extension_whitelist_error": "<PERSON><PERSON><PERSON><PERSON> formatia atorn<PERSON>qarsinnaanngilaq.", "app.errors.first_name_blank": "Una allassimasoqassaaq", "app.errors.generics.blank": "Una allassimasoqassaaq", "app.errors.generics.invalid": "Taanna atorsinn<PERSON> is<PERSON>oq", "app.errors.generics.taken": "E-maili taanna <PERSON>. Kontomut allamut attave<PERSON>.", "app.errors.generics.unsupported_locales": "Immersuiffissap taassuma maanna sumiiffik  ikortartunngilaa. ", "app.errors.group_ids_unauthorized_choice_moderator": "Suliniummi aqutsisutut ta<PERSON>aat inunnut suliniutinnut isersinnaasunut mailinik nassiussisinnaavutit", "app.errors.has_other_overlapping_phases": "Suliniutit qalleraattunik nikeriarfeqarsinnaanngillat. ", "app.errors.invalid_email": "E-maili {value} titarnerni qule<PERSON> nassaaq {row} atorneqarsinnaanngilaq", "app.errors.invalid_row": "Titarnerit quleriiaat  {row}suliareriaraluarlugit  kukkuneq pinngorpoq. ", "app.errors.is_not_timeline_project": "<PERSON><PERSON> su<PERSON> i<PERSON>.", "app.errors.key_invalid": "Matuersaat ta<PERSON>allaat naqinnernik, kisitsisinik aamma underscore(_) allassimassaaq", "app.errors.last_name_blank": "Una allassimasoqassaaq", "app.errors.locale_blank": "Oqaatsit to<PERSON>t", "app.errors.locale_inclusion": "Oqaatsit nittartakkami atorsinnaasut to<PERSON>t", "app.errors.malformed_admin_value": "Aq<PERSON><PERSON><PERSON> nalinga {value} tull<PERSON>iaani {row}-imi na<PERSON>q atorsinn<PERSON>ilaq", "app.errors.malformed_groups_value": "Ataatsimoortut {value} titarnerni quleriiaani nassaat {row} atuuttuunngillat", "app.errors.max_invites_limit_exceeded1": "The number of invitations exceeds the limit of 1000.", "app.errors.maximum_attendees_greater_than1": "The maximum number of registrants must be greater than 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "The maximum number of registrants must be greater than or equal to the current number of registrants.", "app.errors.no_invites_specified": "E-mailad<PERSON>e <PERSON>.", "app.errors.no_recipients": "The campaign can't be sent out because there are no recipients. The group you're sending to is either empty, or nobody has consented to receiving emails.", "app.errors.number_invalid": "Please enter a valid number.", "app.errors.password_blank": "Una allassimasoqassaaq", "app.errors.password_invalid": "Please check your current password again.", "app.errors.password_too_short": "Kode isissutissaq minnerpaamik 8-nik naqinneqassaaq.", "app.errors.resending_code_failed": "Something went wrong while sending out the confirmation code.", "app.errors.slug_taken": "URL taanna atorne<PERSON>reerpoq. Inussiarnersumik suliniutip slugia allanngortiguk.", "app.errors.tag_name_taken": "A tag with this name already exists", "app.errors.title_multiloc_blank": "Atorfiup ta<PERSON>", "app.errors.title_multiloc_includes_banned_words": "The title contains words that are considered inappropriate.", "app.errors.token_invalid": "Nutaamik isertaatiliornissamut iserfissaq ataasiaannaq atorneqa<PERSON>innaavoq nassiutereerneratalu kingorna akunneq ataaseq atuussinn<PERSON>luni. {passwordResetLink}.", "app.errors.too_common": "This password can be easily guessed. Please choose a stronger password.", "app.errors.too_long": "Please choose a shorter password (max 72 characters)", "app.errors.too_short": "Kode isissutissaq minnerpaamik 8-nik naqinneqassaaq.", "app.errors.uncaught_error": "An unknown error occurred.", "app.errors.unknown_group": "Ataatsimoortut {value} titarnerni quleriiaani nassaarineqartut {row} ilisimaneqanngillat", "app.errors.unknown_locale": "Oqaatsit {value} titarnerni quleri<PERSON>ani nassaarineqartut {row} atortussanngortinneqarsimanngillat", "app.errors.unparseable_excel": "Excel-fil toqqagaq suliarineqarsinnaanngilaq", "app.errors.url": "Enter a valid link. Make sure the link starts with https://", "app.errors.verification_taken": "Verification cannot be completed as another account has been verified using the same details.", "app.errors.view_name_taken": "Ateq taanna atorn<PERSON>poq", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Il<PERSON><PERSON><PERSON><PERSON>luunniit naleqqutinngitsutut nalilerneqartoq siumorne<PERSON>rpoq", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Sign in with StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Sign up with StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Create a Stadt Wien Account now and use one login for many digital services of Vienna.", "app.modules.id_cow.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_cow.emptyFieldError": "I<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_cow.helpAltText": "Kinaassutsimut uppernarsaammi ID-serienummer sumi nanineqarsinnaanersoq takutittarpaa", "app.modules.id_cow.invalidIdSerialError": "ID-serie atuutinngilaq", "app.modules.id_cow.invalidRunError": "RUN atuutinngilaq", "app.modules.id_cow.noMatchFormError": "Tulluartumik na<PERSON>art<PERSON>qanngilaq.", "app.modules.id_cow.notEntitledFormError": "Piginnaassuseqartinneqanngilaq.", "app.modules.id_cow.showCOWHelp": "ID-Serienummer sumi pissarsiarisinnaavara ?", "app.modules.id_cow.somethingWentWrongError": "Ajortoqarmat uppernarsarsinnaanngilatsigit", "app.modules.id_cow.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_cow.takenFormError": "Tiguneqareerpoq.", "app.modules.id_cow.verifyCow": "COW  atorlugu uppernarsaaneq", "app.modules.id_franceconnect.verificationButtonAltText": "FranceConnect atorlugu uppernarsaagit", "app.modules.id_gent_rrn.cancel": "Cancel", "app.modules.id_gent_rrn.emptyFieldError": "This field cannot be empty.", "app.modules.id_gent_rrn.gentRrnHelp": "Inuttut normut kinaassutsimut uppernarsaativit digitaliusup tunuani allaqqavoq", "app.modules.id_gent_rrn.invalidRrnError": "<PERSON><PERSON><PERSON> normua atorsinn<PERSON>", "app.modules.id_gent_rrn.noMatchFormError": "<PERSON><PERSON>ut normut pillugu paasissutissanik nassaanngilagut", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Ghent avataani najugaqaravit eqqortuunerarsinnaanngilatsigit", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "14-it inorlugit ukioqaravit uppernarsarsinnaanngilatsigit", "app.modules.id_gent_rrn.rrnLabel": "<PERSON><PERSON><PERSON>ua", "app.modules.id_gent_rrn.rrnTooltip": "<PERSON>u<PERSON>ua <PERSON>, ta<PERSON><PERSON>lluta uppernarsarsinnaaniassagatsigut Ghentimi najugaqartuusutit 14-inillu amerlanerusunik ukioqartutit.", "app.modules.id_gent_rrn.showGentRrnHelp": "Where can I find my ID Serial Number ?", "app.modules.id_gent_rrn.somethingWentWrongError": "We can't verify you because something went wrong", "app.modules.id_gent_rrn.submit": "Submit", "app.modules.id_gent_rrn.takenFormError": "<PERSON><PERSON>ut normut atuisuuffik alla uppernarsarniarlugu atorn<PERSON>qareersimavoq", "app.modules.id_gent_rrn.verifyGentRrn": "GentRm-imik <PERSON>", "app.modules.id_id_card_lookup.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.emptyFieldError": "I<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.helpAltText": "ID kortimik nassuiaat", "app.modules.id_id_card_lookup.invalidCardIdError": "ID una atuutinngilaq.", "app.modules.id_id_card_lookup.noMatchFormError": "Tulluartumik na<PERSON>art<PERSON>qanngilaq.", "app.modules.id_id_card_lookup.showHelp": "ID-Serienummer sumi pissarsiarisinnaavara?", "app.modules.id_id_card_lookup.somethingWentWrongError": "We can't verify you because something went wrong", "app.modules.id_id_card_lookup.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.takenFormError": "Tiguneqareerpoq.", "app.modules.id_oostende_rrn.cancel": "Cancel", "app.modules.id_oostende_rrn.emptyFieldError": "This field cannot be empty.", "app.modules.id_oostende_rrn.invalidRrnError": "Invalid social security number", "app.modules.id_oostende_rrn.noMatchFormError": "We couldn't find back information on your social security number", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "We can't verify you because you live outside of Oostende", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "We can't verify you because you are younger than 14 years", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Your social security number is shown on the back of your digital identity card", "app.modules.id_oostende_rrn.rrnLabel": "Social security number", "app.modules.id_oostende_rrn.rrnTooltip": "We ask your social security number to verify whether you are a citizen of Oostende, older than 14 year old.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Where can I find my social security number?", "app.modules.id_oostende_rrn.somethingWentWrongError": "We can't verify you because something went wrong", "app.modules.id_oostende_rrn.submit": "Submit", "app.modules.id_oostende_rrn.takenFormError": "Your social security number has already been used to verify another account", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Verify using social security number", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Mappe-mut \"{folderName}\"-mut aqutsisinnaasutut pisinnaatitaaffilerneqarnermik tigusaqarnikuuvutit.", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Siammarteruk", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Suliniutit takukkit {folderUrl} taasinerit takutikkumallugu!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | from the participation platform of {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | from the participation platform of {orgName}", "app.sessionRecording.accept": "Yes, I accept", "app.sessionRecording.modalDescription1": "In order to better understand our users, we randomly ask a small percentage of visitors to track their browsing session in detail.", "app.sessionRecording.modalDescription2": "The sole purpose of the recorded data is to improve the website. None of your data will be shared with a 3rd party. Any sensitive information you enter will be filtered.", "app.sessionRecording.modalDescription3": "Do you accept?", "app.sessionRecording.modalDescriptionFaq": "FAQ here.", "app.sessionRecording.modalTitle": "Help us improve this website", "app.sessionRecording.reject": "No, I reject", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Conduct a budget allocation exercise", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Collect feedback on a document", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Create an in-platform survey", "app.utils.AdminPage.ProjectEdit.createPoll": "Create a poll", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Embed an external survey", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Find volunteers", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Collect input and feedback", "app.utils.AdminPage.ProjectEdit.shareInformation": "Share information", "app.utils.FormattedCurrency.credits": "credits", "app.utils.FormattedCurrency.tokens": "tokens", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# credits} one {# credit} other {# credits}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokens} one {# token} other {# tokens}}", "app.utils.IdeaCards.mostDiscussed": "Most discussed", "app.utils.IdeaCards.mostReacted": "Most reactions", "app.utils.IdeaCards.newest": "Newest", "app.utils.IdeaCards.oldest": "Oldest", "app.utils.IdeaCards.random": "Random", "app.utils.IdeaCards.trending": "Trending", "app.utils.IdeasNewPage.contributionFormTitle": "Add new contribution", "app.utils.IdeasNewPage.ideaFormTitle": "Add new idea", "app.utils.IdeasNewPage.initiativeFormTitle": "Add new initiative", "app.utils.IdeasNewPage.issueFormTitle1": "Add new comment", "app.utils.IdeasNewPage.optionFormTitle": "Add new option", "app.utils.IdeasNewPage.petitionFormTitle": "Add new petition", "app.utils.IdeasNewPage.projectFormTitle": "Add new project", "app.utils.IdeasNewPage.proposalFormTitle": "Add new proposal", "app.utils.IdeasNewPage.questionFormTitle": "Add new question", "app.utils.IdeasNewPage.surveyTitle": "Survey", "app.utils.IdeasNewPage.viewYourComment": "View your comment", "app.utils.IdeasNewPage.viewYourContribution": "View your contribution", "app.utils.IdeasNewPage.viewYourIdea": "View your idea", "app.utils.IdeasNewPage.viewYourInitiative": "View your initiative", "app.utils.IdeasNewPage.viewYourInput": "View your input", "app.utils.IdeasNewPage.viewYourIssue": "View your issue", "app.utils.IdeasNewPage.viewYourOption": "View your option", "app.utils.IdeasNewPage.viewYourPetition": "View your petition", "app.utils.IdeasNewPage.viewYourProject": "View your project", "app.utils.IdeasNewPage.viewYourProposal": "View your proposal", "app.utils.IdeasNewPage.viewYourQuestion": "View your question", "app.utils.Projects.sendSubmission": "Send submission identifier to my email", "app.utils.Projects.sendSurveySubmission": "Send survey submission identifier to my email", "app.utils.Projects.surveySubmission": "Survey submission", "app.utils.Projects.yourResponseHasTheFollowingId": "Your response has the following identifier: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "If you later decide that you want your response to be removed, please contact us with the following unique identifier:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "You must complete your profile to attend this event.", "app.utils.actionDescriptors.attendingEventNotInGroup": "You do not meet the requirements to attend this event.", "app.utils.actionDescriptors.attendingEventNotPermitted": "You are not permitted to attend this event.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "You must log in or register to attend this event.", "app.utils.actionDescriptors.attendingEventNotVerified": "You must verify your account before you can attend this event.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "You must complete your profile to volunteer.", "app.utils.actionDescriptors.volunteeringNotInGroup": "You do not meet the requirements to volunteer.", "app.utils.actionDescriptors.volunteeringNotPermitted": "You are not permitted to volunteer.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "You must log in or register to volunteer.", "app.utils.actionDescriptors.volunteeringNotVerified": "You must verify your account before you can volunteer.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Please {completeRegistrationLink} to volunteer.", "app.utils.errors.api_error_default.in": "Eqq<PERSON><PERSON><PERSON>ngilaq", "app.utils.errors.default.ajv_error_birthyear_required": "Inussiarnersumik ukioq inuuiit allaguk", "app.utils.errors.default.ajv_error_date_any": "Inussiarnersumik ullumik atorsinnaasumik allagit", "app.utils.errors.default.ajv_error_domicile_required": "Inussiarnersumik sumi najugaqarnerlutit allaguk", "app.utils.errors.default.ajv_error_gender_required": "Inussiarnersumik suiaassutsit allaguk", "app.utils.errors.default.ajv_error_invalid": "Is invalid", "app.utils.errors.default.ajv_error_maxItems": "Ukunannga annertunermik imaqars<PERSON>aanngilaq {limit, plural, one {# item} other {# items}}", "app.utils.errors.default.ajv_error_minItems": "<PERSON><PERSON><PERSON><PERSON><PERSON> ima imaqassaaq {limit, plural, one {# item} other {# items}}", "app.utils.errors.default.ajv_error_number_any": "Inussiarnersumik kisitsimmik atorsinnaasumik allagit", "app.utils.errors.default.ajv_error_politician_required": "Inussiarnersumik politikeriunerlutit allaguk", "app.utils.errors.default.ajv_error_required3": "Field is required: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Can't be blank", "app.utils.errors.default.api_error_accepted": "Akuerineqassaaq", "app.utils.errors.default.api_error_blank": "Immersorneqassaaq", "app.utils.errors.default.api_error_confirmation": "Asseqanngilaq", "app.utils.errors.default.api_error_empty": "Imaqartariaqarpoq", "app.utils.errors.default.api_error_equal_to": "Is not right", "app.utils.errors.default.api_error_even": "Akunnaatsuussaaq", "app.utils.errors.default.api_error_exclusion": "Inniminnerneqareerpoq", "app.utils.errors.default.api_error_greater_than": "Annikippallaarpoq", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Is too small", "app.utils.errors.default.api_error_inclusion": "Allattorsimaffimmi <PERSON>", "app.utils.errors.default.api_error_invalid": "Atorsinnaanngilaq", "app.utils.errors.default.api_error_less_than": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_less_than_or_equal_to": "Is too big", "app.utils.errors.default.api_error_not_a_number": "Kisitsisaanngilaq", "app.utils.errors.default.api_error_not_an_integer": "Kisitsit ilivitsuussaaq", "app.utils.errors.default.api_error_other_than": "Is not right", "app.utils.errors.default.api_error_present": "Immersorneqassanngilaq", "app.utils.errors.default.api_error_too_long": "Takivallaarpoq", "app.utils.errors.default.api_error_too_short": "Naappallaarpoq", "app.utils.errors.default.api_error_wrong_length": "Kukkusumik takissuseqarpoq", "app.utils.errors.defaultapi_error_.odd": "A<PERSON><PERSON><PERSON>uussaaq", "app.utils.notInGroup": "You do not meet the requirements to participate.", "app.utils.participationMethod.onSurveySubmission": "Thank you. Your response has been received.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Voting is no longer available, since this phase is no longer active.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "You do not meet the requirements to vote.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "You are not permitted to vote.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "You must log in or register to vote.", "app.utils.participationMethodConfig.voting.votingNotVerified": "You must verify your account before you can vote.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Submitting budgets closed on {endDate}.</b> Participants had a total of <b>{maxBudget} each to distribute between {optionCount} options.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Budget submitted", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Budget submitted 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "You do not meet the requirements to assign budgets.", "app.utils.votingMethodUtils.budgetingNotPermitted": "You are not permitted to assign budgets.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "You must log in or register to assign budgets.", "app.utils.votingMethodUtils.budgetingNotVerified": "You must verify your account before you can assign budgets.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Your budget will not be counted</b> until you click \"Submit\"", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "The minimum required budget is {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "Once you are done, click \"Submit\" to submit your budget.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Select your preferred options by tapping on \"Add\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "You have a total of <b>{maxBudget} to distribute between {optionCount} options</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b>Congratulations, your budget has been submitted!</b> You can check your options below at any point or modify them before <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b>Congratulations, your budget has been submitted!</b> You can check your options below at any point.", "app.utils.votingMethodUtils.castYourVote": "Cast your vote", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "You can add a maximum of {maxVotes, plural, one {# credit} other {# credits}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "You can add a maximum of {maxVotes, plural, one {# point} other {# points}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "You can add a maximum of {maxVotes, plural, one {# token} other {# tokens}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "You can add a maximum of {maxVotes, plural, one {# vote} other {# votes}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "Once you are done, click “Submit” to cast your vote.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Select your preferred options by tapping on \"Select\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "You have a total of <b>{totalVotes, plural, one {# credit} other {# credits}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "You have a total of <b>{totalVotes, plural, one {# token} other {# tokens}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "You have a total of <b>{totalVotes, plural, one {# vote} other {# votes}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "Final results", "app.utils.votingMethodUtils.finalTally": "Final tally", "app.utils.votingMethodUtils.howToParticipate": "How to participate", "app.utils.votingMethodUtils.howToVote": "How to vote", "app.utils.votingMethodUtils.multipleVotingEnded1": "Voting closed on <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 credits} one {1 credit} other {# credits}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 points} one {1 point} other {# points}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokens} one {1 token} other {# tokens}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 votes} one {1 vote} other {# votes}}", "app.utils.votingMethodUtils.results": "Results", "app.utils.votingMethodUtils.singleVotingEnded": "Voting closed on <b>{endDate}.</b> Participants could <b>vote for {maxVotes} options.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Select your preferred options by tapping on “Vote”", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "You have <b>{totalVotes} votes</b> that you can assign to the options.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "Once you are done, click “Submit” to cast your vote.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Voting closed on <b>{endDate}.</b> Participants could <b>vote for 1 option.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Select your preferred option by tapping on “Vote”.", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "You have <b>1 vote</b> that you can assign to one of the options.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Voting closed on <b>{endDate}.</b> Participants could <b>vote for as many options as they wished.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "You can vote for as many options as you would like.", "app.utils.votingMethodUtils.submitYourBudget": "Submit your budget", "app.utils.votingMethodUtils.submittedBudgetCountText2": "person submitted their budget online", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "people submitted their budgets online", "app.utils.votingMethodUtils.submittedVoteCountText2": "person submitted their vote online", "app.utils.votingMethodUtils.submittedVotesCountText2": "people submitted their votes online", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Vote submitted 🎉", "app.utils.votingMethodUtils.votesCast": "Votes cast", "app.utils.votingMethodUtils.votingClosed": "Voting closed", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Your vote will not be counted</b> until you click \"Submit\"", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b>Congratulations, your vote has been submitted!</b> You can check or modify your submission before <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b>Congratulations, your vote has been submitted!</b> You can check or modify your submission below at any point.", "components.UI.IdeaSelect.noIdeaAvailable": "There are no ideas available.", "components.UI.IdeaSelect.selectIdea": "Select idea", "containers.SiteMap.allProjects": "Suliniutit tamarmik", "containers.SiteMap.customPageSection": "Quppernerit naleqqussakkit", "containers.SiteMap.folderInfo": "Paasissutissat allat", "containers.SiteMap.headSiteMapTitle": "Site map | {orgName}", "containers.SiteMap.homeSection": "<PERSON><PERSON>inn<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.pageContents": "<PERSON><PERSON><PERSON><PERSON><PERSON> imarisat", "containers.SiteMap.profilePage": "<PERSON><PERSON> qupper<PERSON>t", "containers.SiteMap.profileSettings": "<PERSON>lit pillutit aaqqiinerit", "containers.SiteMap.projectEvents": "Pisimasut", "containers.SiteMap.projectIdeas": "Isumassarsiat", "containers.SiteMap.projectInfo": "Paasissutissat", "containers.SiteMap.projectPoll": "Taasineq", "containers.SiteMap.projectSurvey": "Immersuilluni apersuineq", "containers.SiteMap.projectsArchived": "Suliniutit to<PERSON>orn<PERSON>qa<PERSON>t", "containers.SiteMap.projectsCurrent": "<PERSON><PERSON><PERSON><PERSON> maanna inger<PERSON>ut", "containers.SiteMap.projectsDraft": "Suliniutit missingersukkat", "containers.SiteMap.projectsSection": "Uannga suliniutit {orgName}", "containers.SiteMap.signInPage": "Iserit", "containers.SiteMap.signUpPage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.siteMapDescription": "Quppernermit uannga quppernermit imaasut tamaasa qupperarsinnaavatit.", "containers.SiteMap.siteMapTitle": "Peqataasut isaaffianut sitemap {orgName}", "containers.SiteMap.successStories": "Oqaluttuat iluatsilluarsimasut", "containers.SiteMap.timeline": "Suliniutini nikeriarfiit", "containers.SiteMap.userSpaceSection": "<PERSON><PERSON> kontut", "containers.SubscriptionEndedPage.accessDenied": "Isersinnaajunnaarputit", "containers.SubscriptionEndedPage.subscriptionEnded": "Qupperneq una tamaallaat quppernernit atuuttunik atuisuuffeqartunit iserfigineqarsinnaavoq."}
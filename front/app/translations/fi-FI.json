{"EmailSettingsPage.emailSettings": "Sähköpostiasetukset", "EmailSettingsPage.initialUnsubscribeError": "<PERSON><PERSON><PERSON><PERSON><PERSON> kampanjan tilauksen peruuttamisessa ilmeni ongelma. Yritä uudelleen.", "EmailSettingsPage.initialUnsubscribeLoading": "Pyyntöäsi k<PERSON>ll<PERSON>, odota...", "EmailSettingsPage.initialUnsubscribeSuccess": "<PERSON><PERSON> on<PERSON>esti peruuttanut tilauksen {campaignTitle}.", "UI.FormComponents.optional": "<PERSON><PERSON><PERSON><PERSON>", "app.closeIconButton.a11y_buttonActionMessage": "kiinni", "app.components.Areas.areaUpdateError": "<PERSON><PERSON><PERSON><PERSON> tallennettaessa tapahtui virhe. Yrit<PERSON> uudelleen.", "app.components.Areas.followedArea": "Seurattu alue: {areaTitle}", "app.components.Areas.followedTopic": "<PERSON><PERSON><PERSON><PERSON> aihe: {topicTitle}", "app.components.Areas.topicUpdateError": "<PERSON><PERSON><PERSON> tallennetta<PERSON>a tapahtui virhe. <PERSON><PERSON><PERSON> uude<PERSON>.", "app.components.Areas.unfollowedArea": "Ei-seurattu alue: {areaTitle}", "app.components.Areas.unfollowedTopic": "<PERSON><PERSON><PERSON><PERSON> aihe: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "<PERSON>nta:", "app.components.AssignBudgetControl.add": "Valitse", "app.components.AssignBudgetControl.added": "Valitt<PERSON>", "app.components.AssignMultipleVotesControl.addVote": "Lisää ääni", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "<PERSON><PERSON> j<PERSON> ka<PERSON>ki k<PERSON>.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "<PERSON>t jaka<PERSON> tämän vaiht<PERSON>hdon enimmäismäärän krediittej<PERSON>.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "<PERSON><PERSON> j<PERSON> kaikki pist<PERSON>i.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "<PERSON><PERSON> jaka<PERSON> tämän vaihtoehdon enimmäispistemäärän.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "<PERSON><PERSON> j<PERSON> kaikki <PERSON>.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "<PERSON>t jaka<PERSON> tämän vaihtoehdon enimmäismäärän token<PERSON>.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "<PERSON><PERSON> j<PERSON> ka<PERSON>.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "<PERSON>t jaka<PERSON> tälle vaihtoehdolle enimmäismäärän ääniä.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(sis. 1 offline-tilassa)} other {(sis. # offline-tilassa)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Äänestys ei ole ma<PERSON>, koska tämä vaihe ei ole aktiivinen.", "app.components.AssignMultipleVotesControl.removeVote": "Poista ääni", "app.components.AssignMultipleVotesControl.select": "Valitse", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Olet jo lähettänyt äänesi. Voit muokata sitä napsauttamalla \"Muokkaa lähetystäsi\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Olet jo lähettänyt äänesi. Voit muokata sitä palaamalla projektisivulle ja napsauttamalla \"Muokkaa lähetystäsi\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {opintopisteet} other {opintopisteet}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {piste} other {pistettä}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokenit}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {ää<PERSON><PERSON><PERSON>} other {äänt<PERSON>}}", "app.components.AssignVoteControl.maxVotesReached1": "<PERSON><PERSON> j<PERSON> ka<PERSON>.", "app.components.AssignVoteControl.phaseNotActive": "Äänestys ei ole ma<PERSON>, koska tämä vaihe ei ole aktiivinen.", "app.components.AssignVoteControl.select": "Valitse", "app.components.AssignVoteControl.selected2": "Valitt<PERSON>", "app.components.AssignVoteControl.voteForAtLeastOne": "Äänestä vähintään yhtä vaihtoehtoa", "app.components.AssignVoteControl.votesSubmitted1": "Olet jo lähettänyt äänesi. Voit muokata sitä napsauttamalla \"Muokkaa lähetystäsi\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Olet jo lähettänyt äänesi. Voit muokata sitä palaamalla projektisivulle ja napsauttamalla \"Muokkaa lähetystäsi\".", "app.components.AuthProviders.continue": "Jatka", "app.components.AuthProviders.continueWithAzure": "Jatka {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Jatka Facebookissa", "app.components.AuthProviders.continueWithFakeSSO": "Jatka Fake SSO:lla", "app.components.AuthProviders.continueWithGoogle": "<PERSON><PERSON><PERSON>", "app.components.AuthProviders.continueWithHoplr": "Jatka Hoplr:lla", "app.components.AuthProviders.continueWithIdAustria": "Jatka ID Austrialla", "app.components.AuthProviders.continueWithLoginMechanism": "Jatka {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Jatka MitID:llä", "app.components.AuthProviders.franceConnectMergingFailed": "<PERSON><PERSON><PERSON>ä sähköpostiosoitteella on jo tili.{br}{br}Et voi käyttää alustaa FranceConnectin avulla, koska henkilökohtaiset tiedot eivät täsmää. Jo<PERSON> haluat kirjautua sisään FranceConnectilla, sinun on ensin vaihdettava etu- tai sukunimesi tällä alustalla vastaamaan virallisia tietojasi.{br}{br}Voit kirjautua sisään tavalliseen tapaan alla.", "app.components.AuthProviders.goToLogIn": "Onko sinulla jo tili? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "<PERSON><PERSON><PERSON> sinulla ole tiliä? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.logInWithEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>n sähköpostilla", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "<PERSON>un on oltava määritettyä vähimmäisikää tai sitä vanhempi voidaksesi vahvistaa.", "app.components.AuthProviders.signUp2": "Rekisteröidy", "app.components.AuthProviders.signUpButtonAltText": "Rekisteröidy numerolla {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Rekisteröidy sähköpostilla", "app.components.AuthProviders.verificationRequired": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "app.components.Author.a11yPostedBy": "lähettänyt", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 osallistuja} other {{numberOfParticipants} osallistujaa}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} käyttäjää", "app.components.AvatarBubbles.participant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AvatarBubbles.participants1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.cancel": "Peruuta", "app.components.Comments.commentingDisabledInCurrentPhase": "Kommentointi ei ole ma<PERSON>llis<PERSON> nykyisessä vaiheessa.", "app.components.Comments.commentingDisabledInactiveProject": "Kommentointi ei ole ma<PERSON>, koska tämä projekti ei ole tällä hetkellä aktiivinen.", "app.components.Comments.commentingDisabledProject": "Kommentointi tässä projektissa on tällä hetkellä poissa käytöstä.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} kommentoida.", "app.components.Comments.commentingMaybeNotPermitted": "Ole hyvä ja {signInLink} nähdäksesi mitä toimia voidaan tehdä.", "app.components.Comments.inputsAssociatedWithProfile": "Oletuksena lähetyksesi liitetään profi<PERSON>, el<PERSON> valitse tätä vaihtoehtoa.", "app.components.Comments.invisibleTitleComments": "Ko<PERSON>ntit", "app.components.Comments.leastRecent": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.likeComment": "Tykkää tästä kommentista", "app.components.Comments.mostLiked": "<PERSON><PERSON><PERSON> o<PERSON> re<PERSON>", "app.components.Comments.mostRecent": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.official": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.postAnonymously": "Postaa nimettömänä", "app.components.Comments.replyToComment": "Vastaa kommenttiin", "app.components.Comments.reportAsSpam": "<PERSON><PERSON><PERSON>", "app.components.Comments.seeOriginal": "<PERSON><PERSON>", "app.components.Comments.seeTranslation": "<PERSON><PERSON>", "app.components.Comments.yourComment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.divisiveDescription": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON> ihm<PERSON>t ovat yhtä mieltä ja eri mieltä:", "app.components.CommonGroundResults.divisiveTitle": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.majorityDescription": "Yli 60 % ä<PERSON><PERSON><PERSON> su<PERSON>an tai toiseen seuraavista asioista:", "app.components.CommonGroundResults.majorityTitle": "Enemmistö", "app.components.CommonGroundResults.participantLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.participantsLabel1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.statementLabel": "lausunto", "app.components.CommonGroundResults.statementsLabel1": "la<PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.votesLabe": "äänestää", "app.components.CommonGroundResults.votesLabel1": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundStatements.agreeLabel": "Samaa mieltä", "app.components.CommonGroundStatements.disagreeLabel": "<PERSON><PERSON>", "app.components.CommonGroundStatements.noMoreStatements": "Ei ole vastattavia lausuntoja juuri nyt", "app.components.CommonGroundStatements.noResults": "Ei vielä näytettäviä tuloksia. Varmista, että olet osallistunut Common Ground -vaiheeseen ja tarkista tilanne my<PERSON> uudelleen.", "app.components.CommonGroundStatements.unsureLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundTabs.resultsTabLabel": "Tulokset", "app.components.CommonGroundTabs.statementsTabLabel": "<PERSON><PERSON><PERSON>", "app.components.CommunityMonitorModal.formError": "Tapaht<PERSON> virhe.", "app.components.CommunityMonitorModal.surveyDescription2": "Tämä jatkuva kysely <PERSON>, mitä mieltä olet hallinn<PERSON>a ja julkis<PERSON> palveluista.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Kestä<PERSON> <1 minuutin} one {Kestää 1 minuutin} other {Kestää # minuuttia}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Vahvistuskoodin sisältävä sähköposti on lähetetty osoitteeseen {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Vaihda sähköpostiosoitteesi.", "app.components.ConfirmationModal.codeInput": "<PERSON><PERSON><PERSON>", "app.components.ConfirmationModal.confirmationCodeSent": "<PERSON><PERSON><PERSON> koodi l<PERSON>", "app.components.ConfirmationModal.didntGetAnEmail": "Etkö saanut sähköpostia?", "app.components.ConfirmationModal.foundYourCode": "Löysitkö koodisi?", "app.components.ConfirmationModal.goBack": "<PERSON><PERSON>.", "app.components.ConfirmationModal.sendEmailWithCode": "Lähetä sähköposti koodilla", "app.components.ConfirmationModal.sendNewCode": "Lähetä uusi koodi.", "app.components.ConfirmationModal.verifyAndContinue": "Vahvista ja jatka", "app.components.ConfirmationModal.wrongEmail": "vä<PERSON><PERSON>ä säh<PERSON>öposti?", "app.components.ConsentManager.Banner.accept": "Hyväksy", "app.components.ConsentManager.Banner.ariaButtonClose2": "Hylkää käytäntö ja sulje banneri", "app.components.ConsentManager.Banner.close": "kiinni", "app.components.ConsentManager.Banner.mainText": "Tämä alusta käyttää evästeitä {policyLink} mukaisesti.", "app.components.ConsentManager.Banner.manage": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.policyLink": "Evästekäytäntö", "app.components.ConsentManager.Banner.reject": "Hylkää", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Mainonta", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Käytämme tätä mukauttaaksemme ja mitataksemme verkkosivustomme mainoskampanjoiden tehokkuutta. Emme näytä mainoksia tällä alustalla, mutta seuraavat palvelut voivat tarjota sinulle räätälöidyn mainoksen sivustollamme vierailemiesi sivujen perusteella.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Sallia", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analytics", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Käytämme tätä seurantaa ymmärtääksemme paremmin, kuinka käytät alustaa oppiaksemme ja parantaaksemme navigointiasi. Näitä tietoja käytetään vain massaanalytiikassa, eikä millään tavalla yksittäisten ihmisten seurantaan.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Peruuta", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Estä", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Toimiva", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Tämä on tarpeen verkkosivuston perustoimintojen mahdollistamiseksi ja valvomiseksi. Jotkut tässä luetellut työkalut eivät välttämättä koske sinua. Lue evästekäytäntömme saadaksesi lisätietoja.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Toimivan alustan sa<PERSON>ksi tallen<PERSON>, jos <PERSON>, sek<PERSON> kielen, jolla käytät tätä alustaa.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Ev<PERSON><PERSON>asetu<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Työkalut", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>pa<PERSON>lauseke", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Lataamalla sisältöä vakuutat, että tämä sisältö ei riko kolmansien osapuolten säädöksiä tai oikeuksia, kuten immateriaalioikeuksia, yksityisyysoikeuksia, oikeuksia liikesalaisuuksiin ja niin edelleen. <PERSON>äin ollen lataamalla tämän sisällön sitoudut kantamaan täyden ja yksinomaisen vastuun kaikista välittömistä ja välillisistä vahingoista, jotka johtuvat ladatusta sisällöstä. Lisäksi sitoudut korvaamaan alustan omistajan ja Go Vocalin kaikista kolmansien osapuolien vaatimuksista tai vastuista kolmansia osapuolia kohtaan ja kaikista niihin liittyvistä kuluista, jotka syntyisivät lataamastasi sisällöstä.", "app.components.ContentUploadDisclaimer.onAccept": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ContentUploadDisclaimer.onCancel": "Peruuta", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "<PERSON><PERSON> meille miksi", "app.components.CustomFieldsForm.addressInputAriaLabel": "Osoitteen s<PERSON>öttö", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Anna osoite...", "app.components.CustomFieldsForm.adminFieldTooltip": "Kenttä nä<PERSON>y vain yll<PERSON>pitäjille", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "<PERSON><PERSON><PERSON> tämän kyselyn vastaukset anonymisoidaan.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Monikulmioon tarvitaan vähintään kolme pistettä.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "<PERSON><PERSON><PERSON> varten tarvitaan vähintään kaksi pistettä.", "app.components.CustomFieldsForm.attachmentRequired": "Vähintään yksi liite vaaditaan", "app.components.CustomFieldsForm.authorFieldLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Aloita kirjo<PERSON>aminen hakeaksesi käyttäjän sähköpostiosoitteella tai nimellä...", "app.components.CustomFieldsForm.back": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.budgetFieldLabel": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Piirr<PERSON> karttaa napsauttamalla sitä. Vedä sitten pisteitä siirtääksesi niitä.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Lisää vastau<PERSON> napsauttamalla karttaa tai kirjoittamalla osoite alla.", "app.components.CustomFieldsForm.confirm": "Vahvistaa", "app.components.CustomFieldsForm.descriptionMinLength": "<PERSON><PERSON><PERSON><PERSON> on oltava vähintään {min} merkkiä pitkä", "app.components.CustomFieldsForm.descriptionRequired": "<PERSON><PERSON><PERSON> on pakollinen", "app.components.CustomFieldsForm.fieldMaximumItems": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{fieldN<PERSON>}\" voidaan valita enintään {maxSelections, plural, one {# vaihtoehto} other {# vaihtoehtoa}}", "app.components.CustomFieldsForm.fieldMinimumItems": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{fieldName}\" voidaan valita vähintään {minSelections, plural, one {# vaihtoehto} other {# vaihtoehtoa}}", "app.components.CustomFieldsForm.fieldRequired": "<PERSON><PERSON><PERSON> \"{fieldName}\" on pak<PERSON><PERSON>", "app.components.CustomFieldsForm.fileSizeLimit": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>us on {maxFileSize} Mt.", "app.components.CustomFieldsForm.imageRequired": "<PERSON><PERSON> on pak<PERSON>inen", "app.components.CustomFieldsForm.minimumCoordinates2": "Vähintään {numPoints} karttapistettä vaaditaan.", "app.components.CustomFieldsForm.notPublic1": "*Tämä vastaus jaetaan vain projektipäälliköiden kanssa, eikä sitä jaeta julkisesti.", "app.components.CustomFieldsForm.otherArea": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.progressBarLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.removeAnswer": "Poista vastaus", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Valitse niin monta kuin haluat", "app.components.CustomFieldsForm.selectBetween": "*Valitse vaiht<PERSON>doista {minItems} ja {maxItems}", "app.components.CustomFieldsForm.selectExactly2": "*<PERSON><PERSON>e täsmälleen {selectExactly, plural, one {# vaihtoehto} other {# vaihtoehtoa}}", "app.components.CustomFieldsForm.selectMany": "*Valitse niin monta kuin haluat", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Napauta karttaa piirtääksesi. Vedä sitten pisteitä siirtääksesi niitä.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Napauta karttaa piirtääksesi.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Lisää vastauksesi napauttamalla karttaa.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Lisää vastauksesi napauttamalla karttaa tai kirjoittamalla osoite alla.", "app.components.CustomFieldsForm.tapToAddALine": "Napauta lisätäksesi rivin", "app.components.CustomFieldsForm.tapToAddAPoint": "Napauta lisätäks<PERSON> pisteen", "app.components.CustomFieldsForm.tapToAddAnArea": "Napauta lisätäks<PERSON> al<PERSON>en", "app.components.CustomFieldsForm.titleMaxLength": "Otsikon on oltava enintään {max} merk<PERSON>ä pitkä", "app.components.CustomFieldsForm.titleMinLength": "Otsikon on oltava vähintään {min} merkkiä pitkä", "app.components.CustomFieldsForm.titleRequired": "<PERSON><PERSON><PERSON><PERSON> on pakollinen", "app.components.CustomFieldsForm.topicRequired": "Vähintään yksi tunniste vaaditaan", "app.components.CustomFieldsForm.typeYourAnswer": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Vastauksen kirjo<PERSON>n on pakollista", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Lähetä zip-tiedosto, joka sisältää yhden tai useamman shapefile-tiedoston.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "<PERSON>s sijaint<PERSON> ei näytetä vaihtoehtojen jou<PERSON> kir<PERSON>, voit lisätä kelvolliset koordinaatit muodossa 'leveysaste, pituusaste' määrittääks<PERSON> tarkan sijainnin (esim.: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Jotkut kentät olivat virheellisiä. <PERSON><PERSON><PERSON><PERSON> virheet ja yritä uudelleen.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Raporttia lähetettäessä tapahtui tuntematon virhe. Yritä uude<PERSON>en.", "app.components.ErrorBoundary.errorFormLabelClose": "kiinni", "app.components.ErrorBoundary.errorFormLabelComments": "<PERSON><PERSON><PERSON>?", "app.components.ErrorBoundary.errorFormLabelEmail": "Sähköposti", "app.components.ErrorBoundary.errorFormLabelName": "<PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelSubmit": "Lähetä", "app.components.ErrorBoundary.errorFormSubtitle": "Tiimillemme on ilmoitettu.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON><PERSON> ha<PERSON> me<PERSON>, k<PERSON><PERSON> me<PERSON>, mitä tap<PERSON>i alla.", "app.components.ErrorBoundary.errorFormSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> on lähetetty. Kiitos!", "app.components.ErrorBoundary.errorFormTitle": "Näyttää siltä, että tässä on ongelma.", "app.components.ErrorBoundary.genericErrorWithForm": "Ta<PERSON><PERSON><PERSON> virhe, emmekä voi näyttää tätä sisältöä. Yritä uudelleen tai {openForm}", "app.components.ErrorBoundary.openFormText": "auta meitä selvittämään se", "app.components.ErrorToast.budgetExceededError": "<PERSON><PERSON><PERSON> ei ole tarp<PERSON> budjettia", "app.components.ErrorToast.votesExceededError": "<PERSON>ulla ei ole tarpeeksi ääniä jäljellä", "app.components.EventAttendanceButton.forwardToFriend": "Lähetä ystävälle", "app.components.EventAttendanceButton.maxRegistrationsReached": "Tapahtuman ilmoittautumisten enimmäismäärä on saavutettu. Paikkoja ei ole enää jäljellä.", "app.components.EventAttendanceButton.register": "Rekisteröidy", "app.components.EventAttendanceButton.registered": "Rekisteröity", "app.components.EventAttendanceButton.seeYouThere": "Nähdään siellä!", "app.components.EventAttendanceButton.seeYouThereName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Tapahtumatietoa näkyi vähemmän.", "app.components.EventCard.a11y_moreContentVisible": "Lisää tietoa tapahtumasta tuli näkyviin.", "app.components.EventCard.a11y_readMore": "<PERSON><PERSON> lisä<PERSON> \"{eventTitle}\"-tap<PERSON>tumasta.", "app.components.EventCard.endsAt": "Päättyy klo", "app.components.EventCard.readMore": "<PERSON><PERSON> l<PERSON>", "app.components.EventCard.showLess": "Näytä vähemmän", "app.components.EventCard.showMore": "Näytä lisää", "app.components.EventCard.startsAt": "Alkaa", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Tämän projektin tulevat ja meneillään olevat tapahtumat", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Tulevat ja meneillään olevat tapahtumat tässä vaiheessa", "app.components.FileUploader.a11y_file": "Tiedosto:", "app.components.FileUploader.a11y_filesToBeUploaded": "Ladattavat tiedostot: {fileNames}", "app.components.FileUploader.a11y_noFiles": "<PERSON><PERSON> l<PERSON><PERSON><PERSON>.", "app.components.FileUploader.a11y_removeFile": "Poista tämä tiedosto", "app.components.FileUploader.fileInputDescription": "<PERSON><PERSON><PERSON><PERSON>", "app.components.FileUploader.fileUploadLabel": "Liitteet (enintään 50 Mt)", "app.components.FileUploader.file_too_large2": "Yli {maxSizeMb}Mt:n kokoisia tiedostoja ei sallita.", "app.components.FileUploader.incorrect_extension": "Järjestelmämme ei tue {fileName} , sitä ei ladata.", "app.components.FilterBoxes.a11y_allFilterSelected": "Valittu tilasuodatin: kaikki", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# lähetys} other {# palautetta}}", "app.components.FilterBoxes.a11y_removeFilter": "Poista suoda<PERSON>", "app.components.FilterBoxes.a11y_selectedFilter": "Valittu tilasuodatin: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Valitut {numberOfSelectedTopics, plural, =0 {nolla tunnistesuodatinta} one {yhden tagin suodatin} other {# tunnistesuodatinta}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "<PERSON><PERSON><PERSON>", "app.components.FilterBoxes.areas": "<PERSON><PERSON><PERSON> al<PERSON>en mukaan", "app.components.FilterBoxes.inputs": "tulot", "app.components.FilterBoxes.noValuesFound": "Arvoja ei ole saatavilla.", "app.components.FilterBoxes.showLess": "Näytä vähemmän", "app.components.FilterBoxes.showTagsWithNumber": "<PERSON><PERSON><PERSON><PERSON> kaikki ({numberTags})", "app.components.FilterBoxes.statusTitle": "Tila", "app.components.FilterBoxes.topicsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.FiltersModal.filters": "<PERSON><PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.a11y_folderDescription": "Kansion kuvaus:", "app.components.FolderFolderCard.a11y_folderTitle": "Kansion otsikko:", "app.components.FolderFolderCard.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projektia} one {# projekti} other {# projektia}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "<PERSON><PERSON><PERSON> tyyppiä ei voi muuttaa, kun lähetyksiä on tullut.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Tyyppi", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Automaattinen tallennus", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Automaattinen tallennus on oletusarvoisesti käytössä, kun avaat lomakeeditorin. Aina kun suljet kenttäasetuspaneelin \"X\"-<PERSON><PERSON><PERSON><PERSON>, se käynnistää automaattisesti tallennuksen.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "Neljännes", "app.components.GanttChart.timeRange.timeRangeMultiyear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON><PERSON>", "app.components.GanttChart.today": "Tänää<PERSON>", "app.components.GoBackButton.group.edit.goBack": "<PERSON><PERSON>", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "<PERSON><PERSON><PERSON> ed<PERSON> sivulle", "app.components.HookForm.Feedback.errorTitle": "<PERSON> on<PERSON>ma", "app.components.HookForm.Feedback.submissionError": "<PERSON><PERSON><PERSON> uude<PERSON>en. <PERSON><PERSON>, ota meihin yht<PERSON>", "app.components.HookForm.Feedback.submissionErrorTitle": "<PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON>", "app.components.HookForm.Feedback.successMessage": "Lomake lähe<PERSON>tty onnistuneesti", "app.components.HookForm.PasswordInput.passwordLabel": "<PERSON><PERSON><PERSON>", "app.components.HorizontalScroll.scrollLeftLabel": "Vieritä vasemmalle.", "app.components.HorizontalScroll.scrollRightLabel": "Vieritä o<PERSON>alle.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} ideoita on ladattu.", "app.components.IdeaCards.filters": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.mostDiscussed": "Eniten keskusteltu", "app.components.IdeaCards.filters.newest": "<PERSON>us<PERSON>", "app.components.IdeaCards.filters.oldest": "<PERSON><PERSON>", "app.components.IdeaCards.filters.popular": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.random": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.sortBy": "Lajitteluperuste", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Lajittelu muuttui: {currentSortType}", "app.components.IdeaCards.filters.trending": "Trendaavat", "app.components.IdeaCards.showMore": "Näytä lisää", "app.components.IdeasMap.a11y_hideIdeaCard": "<PERSON><PERSON><PERSON>.", "app.components.IdeasMap.a11y_mapTitle": "Kartan yleiskatsaus", "app.components.IdeasMap.clickOnMapToAdd": "Lisää s<PERSON>öttötietosi napsauttamalla karttaa", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Järjestelmänvalvojana voit lisätä syötteesi napsauttamalla karttaa, vaikka tämä vaihe ei olisi aktiivinen.", "app.components.IdeasMap.filters": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasMap.multipleInputsAtLocation": "<PERSON>ita tuloja tässä paikassa", "app.components.IdeasMap.noFilteredResults": "Valitsemasi suodattimet eivät palauttaneet tuloksia", "app.components.IdeasMap.noResults": "<PERSON><PERSON> tuloksia", "app.components.IdeasMap.or": "tai", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, ei inhoa.} one {1 ei tykkää.} other {, # ei tykkää.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, ei tykkäyksiä.} one {, 1 tykkäys.} other {, # tykkää.}}", "app.components.IdeasMap.signInLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasMap.signUpLinkText": "Rekisteröidy", "app.components.IdeasMap.submitIdea2": "Lähetä s<PERSON>öte", "app.components.IdeasMap.tapOnMapToAdd": "Napauta karttaa lisätäksesi s<PERSON>öttötietosi", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Järjestelmänvalvojana voit napauttaa karttaa lisätäksesi s<PERSON>ö<PERSON>, vaikka tämä vaihe ei olisi aktiivinen.", "app.components.IdeasMap.userInputs2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, ei kommentteja} one {, 1 kommentti} other {, # kommenttia}}", "app.components.IdeasShow.bodyTitle": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.deletePost": "Poistaa", "app.components.IdeasShow.editPost": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.goBack": "<PERSON><PERSON>", "app.components.IdeasShow.moreOptions": "Lisää vaihtoehtoja", "app.components.IdeasShow.or": "tai", "app.components.IdeasShow.proposedBudgetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> bud<PERSON>ti", "app.components.IdeasShow.reportAsSpam": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.send": "Lähettää", "app.components.IdeasShow.skipSharing": "<PERSON><PERSON> se, teen sen my<PERSON><PERSON><PERSON>", "app.components.IdeasShowPage.signIn2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShowPage.sorryNoAccess": "Valitettavasti et pääse tälle sivulle. Saatat joutua kirjautumaan sisään tai rekisteröitymään päästäks<PERSON> siihen.", "app.components.LocationInput.noOptions": "<PERSON><PERSON> v<PERSON>", "app.components.Modal.closeWindow": "Sulje i<PERSON>kuna", "app.components.MultiSelect.clearButtonAction": "Tyhjennä valinta", "app.components.MultiSelect.clearSearchButtonAction": "Tyhjenn<PERSON> haku", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>i sähköpostiosoite", "app.components.PageNotFound.goBackToHomePage": "<PERSON><PERSON><PERSON> et<PERSON><PERSON>e", "app.components.PageNotFound.notFoundTitle": "Sivua ei l<PERSON>y", "app.components.PageNotFound.pageNotFoundDescription": "Pyydettyä sivua ei l<PERSON>.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Tarjoa sisältöä vähintään yhdelle kielelle", "app.components.PagesForm.editContent": "Sisältö", "app.components.PagesForm.fileUploadLabel": "Liitteet (enintään 50 Mt)", "app.components.PagesForm.fileUploadLabelTooltip": "Tiedostot eivät saa olla suurempia kuin 50 Mt. Lisätyt tiedostot näkyvät tämän sivun alao<PERSON>.", "app.components.PagesForm.navbarItemTitle": "<PERSON><PERSON>", "app.components.PagesForm.pageTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PagesForm.savePage": "<PERSON><PERSON><PERSON> sivu", "app.components.PagesForm.saveSuccess": "<PERSON>vu tallennettu onnistuneesti.", "app.components.PagesForm.titleMissingOneLanguageError": "<PERSON> v<PERSON>ään y<PERSON>delle kielelle", "app.components.Pagination.back": "<PERSON><PERSON><PERSON> sivu", "app.components.Pagination.next": "<PERSON><PERSON><PERSON> sivu", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Käytit {votesCast}, mik<PERSON> ylit<PERSON> raja<PERSON> {votesLimit}. Poista joitakin tuotteita ostoskoristasi ja yritä uudelleen.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "jeljellä {budgetLeft} / {totalBudget}", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Sinun on käytettävä vähintään {votesMinimum} ennen kuin voit lähettää ostoskorin.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Sinun on valittava vähintään yksi vaihtoehto ennen kuin voit lähettää.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Sinun on lisättävä jotain ostoskoriin ennen kuin voit lähettää sen.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {<PERSON>i j<PERSON>l<PERSON> olevia krediittejä} other {# / {totalNumberOfVotes, plural, one {1 krediitti} other {# krediittejä}} jäljell<PERSON>}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {Ei pisteitä jäljellä} other {# / {totalNumberOfVotes, plural, one {1 piste} other {# pisteet}} jäl<PERSON>ll<PERSON>}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Ei tokeneita jäljellä} other {# / {totalNumberOfVotes, plural, one {1 token} other {# tokeneita}} jä<PERSON><PERSON><PERSON><PERSON>}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Ei ääniä jäljellä} other {# / {totalNumberOfVotes, plural, one {1 ääni} other {# ääniä}} jäljell<PERSON>}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# ääntä} one {# äänestä} other {# ääntä}} an<PERSON>u", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "<PERSON><PERSON> {votesCast} <PERSON><PERSON><PERSON><PERSON>, mik<PERSON> ylit<PERSON> {votesLimit}rajan. Poista joitakin ääniä ja yritä uudelleen.", "app.components.ParticipationCTABars.addInput": "Lisää syöte", "app.components.ParticipationCTABars.allocateBudget": "<PERSON><PERSON><PERSON> bud<PERSON>", "app.components.ParticipationCTABars.budgetSubmitSuccess": "<PERSON><PERSON><PERSON><PERSON> on lähetetty onnistuneesti.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "app.components.ParticipationCTABars.poll": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.reviewDocument": "Tarkista asiakirja", "app.components.ParticipationCTABars.seeContributions": "<PERSON><PERSON>", "app.components.ParticipationCTABars.seeEvents3": "<PERSON><PERSON>", "app.components.ParticipationCTABars.seeIdeas": "<PERSON><PERSON> ideoita", "app.components.ParticipationCTABars.seeInitiatives": "<PERSON><PERSON>", "app.components.ParticipationCTABars.seeIssues": "Kat<PERSON> kommentit", "app.components.ParticipationCTABars.seeOptions": "<PERSON><PERSON>", "app.components.ParticipationCTABars.seePetitions": "<PERSON><PERSON>", "app.components.ParticipationCTABars.seeProjects": "Katso projektit", "app.components.ParticipationCTABars.seeProposals": "<PERSON><PERSON>", "app.components.ParticipationCTABars.seeQuestions": "<PERSON><PERSON>", "app.components.ParticipationCTABars.submit": "Lähetä", "app.components.ParticipationCTABars.takeTheSurvey": "Vastaa k<PERSON>elyyn", "app.components.ParticipationCTABars.userHasParticipated": "<PERSON><PERSON> o<PERSON>ut tähän projektiin.", "app.components.ParticipationCTABars.viewInputs": "Näytä syötteet", "app.components.ParticipationCTABars.volunteer": "liity postituslistalle", "app.components.ParticipationCTABars.votesCounter.vote": "äänestys", "app.components.ParticipationCTABars.votesCounter.votes": "ääniä", "app.components.PasswordInput.a11y_passwordHidden": "<PERSON><PERSON><PERSON> p<PERSON>", "app.components.PasswordInput.a11y_passwordVisible": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength1Password": "<PERSON><PERSON> sa<PERSON>n vah<PERSON>us", "app.components.PasswordInput.a11y_strength2Password": "<PERSON><PERSON><PERSON> v<PERSON>", "app.components.PasswordInput.a11y_strength3Password": "Keskitasoinen sa<PERSON>n v<PERSON>", "app.components.PasswordInput.a11y_strength4Password": "<PERSON><PERSON><PERSON> salasanan vahvuus", "app.components.PasswordInput.a11y_strength5Password": "<PERSON><PERSON><PERSON><PERSON><PERSON> vahva salasanan vahvuus", "app.components.PasswordInput.hidePassword": "<PERSON><PERSON><PERSON> sa<PERSON>", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON> (vähintään {minimumPasswordLength} merkkiä)", "app.components.PasswordInput.minimumPasswordLengthError": "<PERSON>, joka on vähintään {minimumPasswordLength} merkkiä pitkä", "app.components.PasswordInput.passwordEmptyError": "Syötä sa<PERSON>", "app.components.PasswordInput.passwordStrengthTooltip1": "<PERSON>ah<PERSON>a sa<PERSON> se<PERSON>i:", "app.components.PasswordInput.passwordStrengthTooltip2": "Käytä ei-peräkkäisten pienten kirjainten, isojen kirjainten, nume<PERSON><PERSON>, erikoismerkkien ja välimerkkien yhdistelmää", "app.components.PasswordInput.passwordStrengthTooltip3": "Vältä yleisiä tai helposti arvattavia sanoja", "app.components.PasswordInput.passwordStrengthTooltip4": "<PERSON><PERSON><PERSON><PERSON> pituutta", "app.components.PasswordInput.showPassword": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength3Password": "Keski<PERSON>koinen", "app.components.PasswordInput.strength4Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>va", "app.components.PostCardsComponents.list": "Lista", "app.components.PostCardsComponents.map": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Lisää kommentti", "app.components.PostComponents.OfficialFeedback.cancel": "Peruuta", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Poistaa", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän virallisen päivityksen?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "<PERSON><PERSON><PERSON><PERSON><PERSON> muo<PERSON> {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Viimeisin päivitys: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "<PERSON><PERSON><PERSON>, miten ihmiset näkevät nimesi", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Virallinen pä<PERSON>s kirjoittajan nimi", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Virallinen päivitysteksti", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Viralliset päivitykset", "app.components.PostComponents.OfficialFeedback.postedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Julkai<PERSON>", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Näytä aiemmat päivitykset", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "<PERSON><PERSON><PERSON><PERSON> kom<PERSON>tis<PERSON> tähän...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON><PERSON><PERSON><PERSON> on<PERSON>", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Päivitä viesti", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Päivityksesi julkaistiin onnistuneesti!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "<PERSON>e pan<PERSON> '{postTitle}' osoitteessa {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "<PERSON><PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "<PERSON><PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "<PERSON><PERSON> <PERSON>{postTitle}osoitteessa {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "<PERSON><PERSON><PERSON>: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "<PERSON><PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Mitä mieltä olet tästä ehdotuksesta? Äänestä sitä ja jaa keskustelu osoitteessa {postUrl} saadaksesi äänesi kuuluviin!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "<PERSON><PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "<PERSON><PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "<PERSON><PERSON><PERSON><PERSON> kommentin '{postTitle}' osoitteessa {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON> juuri kommentin: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON> juuri kommentin: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "<PERSON><PERSON> vaiht<PERSON>ht<PERSON> '{postTitle}' osoitteessa {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "<PERSON><PERSON> vai<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "<PERSON><PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "<PERSON><PERSON>{postTitle}osoitte<PERSON>a {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "<PERSON><PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "<PERSON><PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "<PERSON><PERSON> proje<PERSON>{postTitle}osoitte<PERSON>a {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "<PERSON><PERSON> proje<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "<PERSON><PERSON> proje<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "<PERSON><PERSON> '{postTitle}' {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "<PERSON><PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON> ju<PERSON> kohteelle {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Liity keskusteluun tästä kysymyksestä '{postTitle}' osoitteessa {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Li<PERSON> keskusteluun: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Li<PERSON> keskusteluun: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Äänestä {postTitle} päällä", "app.components.PostComponents.linkToHomePage": "<PERSON><PERSON> k<PERSON>", "app.components.PostComponents.readMore": "<PERSON>e lisä<PERSON>...", "app.components.PostComponents.topics": "Aiheet", "app.components.ProjectArchivedIndicator.archivedProject": "Valitettavasti et voi enää osallistua tähän projektiin, koska se on arkistoitu", "app.components.ProjectArchivedIndicator.previewProject": "Projektiluonnos:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Näkyy vain valvojille ja ni<PERSON>, j<PERSON><PERSON> on esikatselulink<PERSON>.", "app.components.ProjectCard.a11y_projectDescription": "Hank<PERSON>en kuvaus:", "app.components.ProjectCard.a11y_projectTitle": "<PERSON><PERSON><PERSON><PERSON> nimi:", "app.components.ProjectCard.addYourOption": "Lisää vai<PERSON>i", "app.components.ProjectCard.allocateYourBudget": "<PERSON><PERSON><PERSON> bud<PERSON>", "app.components.ProjectCard.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.comment": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.contributeYourInput": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.finished": "Val<PERSON>", "app.components.ProjectCard.joinDiscussion": "<PERSON><PERSON> keskusteluun", "app.components.ProjectCard.learnMore": "<PERSON><PERSON> l<PERSON>", "app.components.ProjectCard.reaction": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.readTheReport": "<PERSON><PERSON>", "app.components.ProjectCard.reviewDocument": "Tarkista asiakirja", "app.components.ProjectCard.submitAnIssue": "Lähetä kommentti", "app.components.ProjectCard.submitYourIdea": "Lähetä ideasi", "app.components.ProjectCard.submitYourInitiative": "Lähetä aloitteesi", "app.components.ProjectCard.submitYourPetition": "Lähetä hakemuksesi", "app.components.ProjectCard.submitYourProject": "Lähetä projektisi", "app.components.ProjectCard.submitYourProposal": "Lähetä ehdotuksesi", "app.components.ProjectCard.takeThePoll": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.takeTheSurvey": "Vastaa k<PERSON>elyyn", "app.components.ProjectCard.viewTheContributions": "<PERSON><PERSON>", "app.components.ProjectCard.viewTheIdeas": "<PERSON><PERSON>", "app.components.ProjectCard.viewTheInitiatives": "<PERSON><PERSON>", "app.components.ProjectCard.viewTheIssues": "Kat<PERSON> kommentit", "app.components.ProjectCard.viewTheOptions": "<PERSON><PERSON>", "app.components.ProjectCard.viewThePetitions": "<PERSON><PERSON>", "app.components.ProjectCard.viewTheProjects": "Katso projektit", "app.components.ProjectCard.viewTheProposals": "<PERSON><PERSON>", "app.components.ProjectCard.viewTheQuestions": "<PERSON><PERSON>", "app.components.ProjectCard.vote": "Äänestys", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# kommenttia} other {# kommenttia}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# osallistuminen} other {# osallistuminen}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {ei vielä ideoita} one {# idea} other {# ideaa}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# aloitteita} one {# aloite} other {# aloitteita}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# kommentti} other {# kommenttia}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# vaihtoehto} other {# vaihtoehtoa}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# vetoomusta} one {# vetoomus} other {# vetoomusta}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# projekti} other {# projektia}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# ehdotusta} one {# ehdotus} other {# ehdotusta}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# kysymys} other {# kysymystä}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# kommenttia} one {# kommenttia} other {# kommenttia}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# tuloa} one {# tulo} other {# tuloa}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projektia} one {# projekti} other {# projektia}}", "app.components.ProjectFolderCards.components.Topbar.all": "<PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.draft": "Luonnos", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Suodatusperust<PERSON>", "app.components.ProjectFolderCards.components.Topbar.published2": "Julkaistu", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tag", "app.components.ProjectFolderCards.noProjectYet": "Tällä hetkellä ei ole avoimia projekteja", "app.components.ProjectFolderCards.noProjectsAvailable": "Projekteja ei ole saatavilla", "app.components.ProjectFolderCards.showMore": "Näytä lisää", "app.components.ProjectFolderCards.stayTuned": "Tarkista uudelleen uusien sitoutumismahdollisuuksien varalta", "app.components.ProjectFolderCards.tryChangingFilters": "Yrit<PERSON> vaihtaa valitut suodattimet.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Käytetään my<PERSON> näissä kaupungeissa:", "app.components.ProjectTemplatePreview.copied": "Ko<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.copyLink": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.alignCenter": "Keskiteksti", "app.components.QuillEditor.alignLeft": "<PERSON><PERSON><PERSON> vasemmalle", "app.components.QuillEditor.alignRight": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.bold": "Lihavoitu", "app.components.QuillEditor.clean": "<PERSON><PERSON> muotoilu", "app.components.QuillEditor.customLink": "Lisää-painike", "app.components.QuillEditor.customLinkPrompt": "<PERSON>:", "app.components.QuillEditor.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.image": "<PERSON><PERSON><PERSON> kuva", "app.components.QuillEditor.imageAltPlaceholder": "Lyhyt kuvaus kuvasta", "app.components.QuillEditor.italic": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.link": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.linkPrompt": "<PERSON>:", "app.components.QuillEditor.normalText": "<PERSON><PERSON>", "app.components.QuillEditor.orderedList": "Tilattu lista", "app.components.QuillEditor.remove": "Poista", "app.components.QuillEditor.save": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.subtitle": "Tekstitys", "app.components.QuillEditor.title": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.unorderedList": "Järjestämätön lista", "app.components.QuillEditor.video": "Lisää video", "app.components.QuillEditor.videoPrompt": "Syötä video:", "app.components.QuillEditor.visitPrompt": "<PERSON><PERSON><PERSON>:", "app.components.ReactionControl.completeProfileToReact": "Täytä profiilisi reagoidaksesi", "app.components.ReactionControl.dislike": "<PERSON>i pidä", "app.components.ReactionControl.dislikingDisabledMaxReached": "<PERSON><PERSON> sa<PERSON>t enimmäismäärän ei-tykkäyksiä {projectName}", "app.components.ReactionControl.like": "<PERSON><PERSON>", "app.components.ReactionControl.likingDisabledMaxReached": "<PERSON><PERSON> sa<PERSON>t enimmäismäärän tykkäyksiä {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "<PERSON><PERSON><PERSON>inen otetaan k<PERSON>öö<PERSON>, kun tämä vaihe alkaa", "app.components.ReactionControl.reactingDisabledPhaseOver": "T<PERSON>ss<PERSON> vaiheessa ei ole enää mahdollista reagoida", "app.components.ReactionControl.reactingDisabledProjectInactive": "Et voi enää reagoida ideoihin alueella {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Reagoiminen ei ole tällä hetkellä käytössä tässä projektissa", "app.components.ReactionControl.reactingNotPermitted": "<PERSON><PERSON><PERSON><PERSON> on sallittu vain tietyille ryhmille", "app.components.ReactionControl.reactingNotSignedIn": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON><PERSON><PERSON><PERSON>.", "app.components.ReactionControl.reactingPossibleLater": "<PERSON><PERSON><PERSON>inen alkaa {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Vahvista henkilö<PERSON>, jotta voit reagoida.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Tapahtuman päivämäärä: {startDate} kello {startTime} - {endDate} kello {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Tapahtuman päivämäärä: {eventDate} {startTime} - {endTime}.", "app.components.Sharing.linkCopied": "<PERSON><PERSON>", "app.components.Sharing.or": "tai", "app.components.Sharing.share": "Jaa", "app.components.Sharing.shareByEmail": "Jaa sähköpostilla", "app.components.Sharing.shareByLink": "<PERSON><PERSON><PERSON>", "app.components.Sharing.shareOnFacebook": "Jaa Facebookissa", "app.components.Sharing.shareOnTwitter": "Jaa Twitterissä", "app.components.Sharing.shareThisEvent": "Jaa tämä tapahtuma", "app.components.Sharing.shareThisFolder": "Jaa", "app.components.Sharing.shareThisProject": "Jaa tämä projekti", "app.components.Sharing.shareViaMessenger": "<PERSON><PERSON> <PERSON><PERSON> kautta", "app.components.Sharing.shareViaWhatsApp": "<PERSON>aa <PERSON><PERSON><PERSON><PERSON><PERSON> kautta", "app.components.SideModal.closeButtonAria": "kiinni", "app.components.StatusModule.futurePhase": "<PERSON><PERSON><PERSON> v<PERSON>, joka ei ole vielä alkanut. <PERSON><PERSON>, kun vaihe alkaa.", "app.components.StatusModule.modifyYourSubmission1": "Muokkaa lähetystäsi", "app.components.StatusModule.submittedUntil3": "<PERSON> viimeistään", "app.components.TopicsPicker.numberOfSelectedTopics": "Valitut {numberOfSelectedTopics, plural, =0 {nolla tagia} one {yksi tunniste} other {# tagia}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>va", "app.components.UI.MoreActionsMenu.moreOptions": "Lisää vaihtoehtoja", "app.components.UI.MoreActionsMenu.showMoreActions": "Näytä lisää toimintoja", "app.components.UI.NewLabel.new": "UUSI", "app.components.UI.PhaseFilter.noAppropriatePhases": "<PERSON><PERSON><PERSON> hank<PERSON>elle ei löytynyt sopivia vaiheita", "app.components.UI.RemoveImageButton.a11y_removeImage": "Poista", "app.components.UI.TranslateButton.original": "Alkuperäinen", "app.components.UI.TranslateButton.translate": "Kääntää", "app.components.Unauthorized.additionalInformationRequired": "Osallistuminen edellyttää lisätietoja.", "app.components.Unauthorized.completeProfile": "Täydellinen profiili", "app.components.Unauthorized.completeProfileTitle": "Täytä profiilisi osallistuaksesi", "app.components.Unauthorized.noPermission": "<PERSON>ulla ei ole lupaa tarkastella tätä sivua", "app.components.Unauthorized.notAuthorized": "Valitettavasti sinulla ei ole oikeutta käyttää tätä sivua.", "app.components.Upload.errorImageMaxSizeExceeded": "<PERSON><PERSON><PERSON><PERSON> kuva on suurempi kuin {maxFileSize}Mt", "app.components.Upload.errorImagesMaxSizeExceeded": "Yksi tai use<PERSON>i valitsemasi kuva on suurempi kuin {maxFileSize}Mt", "app.components.Upload.onlyOneImage": "Voit ladata vain yhden kuvan", "app.components.Upload.onlyXImages": "Voit ladata vain {maxItemsCount} kuvia", "app.components.Upload.remaining": "j<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "app.components.Upload.uploadImageLabel": "<PERSON><PERSON><PERSON> kuva (enintään {maxImageSizeInMb}Mt)", "app.components.Upload.uploadMultipleImagesLabel": "Valitse yksi tai useampi kuva", "app.components.UpsellTooltip.tooltipContent": "Tämä ominaisuus ei sisälly nykyiseen suunnitelmaasi. Keskustele hallituksen menestyspäällikön tai järjestelmänvalvojan kanssa avataksesi sen.", "app.components.UserName.anonymous": "Nimetön", "app.components.UserName.anonymousTooltip2": "Tämä käyttäjä on päättänyt muuttaa panoksensa nimettömäksi", "app.components.UserName.authorWithNoNameTooltip": "<PERSON><PERSON><PERSON> on luotu automaattises<PERSON>, koska et ole antanut ni<PERSON>. Päivitä profiilisi, jos haluat muuttaa sitä.", "app.components.UserName.deletedUser": "t<PERSON><PERSON><PERSON> k<PERSON>", "app.components.UserName.verified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.VerificationModal.verifyAuth0": "Vahvista NemID:llä", "app.components.VerificationModal.verifyBOSA": "Vahvista itsme- tai eID-tunnuksella", "app.components.VerificationModal.verifyBosaFas": "Vahvista itsme- tai eID-tunnuksella", "app.components.VerificationModal.verifyClaveUnica": "Tarkista Clave Unicalla", "app.components.VerificationModal.verifyFakeSSO": "Vahvista fake SSO:lla", "app.components.VerificationModal.verifyIdAustria": "Vahvista ID Itävallalla", "app.components.VerificationModal.verifyKeycloak": "Vahvista ID-Portenilla", "app.components.VerificationModal.verifyNemLogIn": "Vahvista MitID:llä", "app.components.VerificationModal.verifyTwoday2": "Vahvista BankID:llä tai Freja eID+:lla", "app.components.VerificationModal.verifyYourIdentity": "Vahvista henkilöllisyytesi", "app.components.VoteControl.budgetingFutureEnabled": "Voit jakaa budjettis<PERSON> al<PERSON> {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Osallist<PERSON>va budjetointi ei ole tällä hetkellä käytössä.", "app.components.VoteControl.budgetingNotPossible": "Budjettimuutosten tekeminen ei ole tällä hetkellä mah<PERSON>.", "app.components.VoteControl.budgetingNotVerified": "<PERSON> hyvä ja {verifyAccountLink} j<PERSON><PERSON><PERSON><PERSON>.", "app.components.VoteInputs._shared.currencyLeft1": "<PERSON><PERSON><PERSON> on {budgetLeft} / {totalBudget} j<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.VoteInputs._shared.numberOfCreditsLeft": "<PERSON><PERSON>a on {votesLeft, plural, =0 {ei yhtään krediittiä jäljellä} other {# / {totalNumberOfVotes, plural, one {1 krediitti} other {# krediittiä}} jäljell<PERSON>}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "<PERSON><PERSON><PERSON> on {votesLeft, plural, =0 {ei pisteitä jäljellä} other {# / {totalNumberOfVotes, plural, one {1 piste} other {# pistettä}} jälje<PERSON><PERSON>}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "<PERSON><PERSON>a on {votesLeft, plural, =0 {ei yhtään tokenia jäljellä} other {# / {totalNumberOfVotes, plural, one {1 token} other {# tokenia}} jäljell<PERSON>}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "<PERSON><PERSON><PERSON> on {votesLeft, plural, =0 {ei ääniä jäljellä} other {# / {totalNumberOfVotes, plural, one {1 ääni} other {# ääntä}} jäljell<PERSON>}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Olet jo lähettänyt budjettisi. Voit muokata sitä napsauttamalla \"Muokkaa lähetystäsi\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Olet jo lähettänyt budjettisi. Voit muokata sitä palaamalla projektisivulle ja napsauttamalla \"Muokkaa lähetystäsi\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budjetointi ei ole käytettävissä, koska tämä vaihe ei ole aktiivinen.", "app.components.VoteInputs.single.youHaveVotedForX2": "<PERSON><PERSON> {votes, plural, =0 {# vaihtoehtoa} one {# vaihtoehtoa} other {# vaihtoehtoa}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON>ä<PERSON>ä ta<PERSON>, että menetät kaikki tähän s<PERSON>tteeseen liittyvät tiedot, kuten kommentit, reaktiot ja äänet. Tätä toimintoa ei voi kumota.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän s<PERSON>tteen?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "Peruuta", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Vahvistaa", "app.components.admin.SlugInput.resultingURL": "Tuloksena oleva URL-osoite", "app.components.admin.SlugInput.slugTooltip": "Slug on ainutlaatuinen sanajoukko sivun verkko-osoitteen tai URL-osoitteen lopussa.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "<PERSON><PERSON> mu<PERSON>t URL-osoitetta, vanhaa URL-osoitetta käyttävän sivun linkit eivät enää toimi.", "app.components.admin.SlugInput.urlSlugLabel": "Etana", "app.components.admin.UserFilterConditions.addCondition": "Lis<PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_email": "Sähköposti", "app.components.admin.UserFilterConditions.field_event_attendance": "Ilmoittautumiset <PERSON>n", "app.components.admin.UserFilterConditions.field_follow": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_lives_in": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tilan s<PERSON>en kanssa", "app.components.admin.UserFilterConditions.field_participated_in_project": "Osallistunut projektiin", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Jul<PERSON><PERSON> jotain tagilla", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Rekisteröinti päivämäärä", "app.components.admin.UserFilterConditions.field_role": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_verified": "Todentaminen", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "ehdotuksia", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "ei ole rekisteröitynyt mihinkään näistä tapahtumista", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "ei ole il<PERSON>ittaut<PERSON>ut mi<PERSON>ään tap<PERSON>", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "on rekisteröity johonkin näistä tapahtumista", "app.components.admin.UserFilterConditions.predicate_attends_something": "on rekisteröitynyt vähintään yhteen tapahtumaan", "app.components.admin.UserFilterConditions.predicate_begins_with": "alkaa j<PERSON>n", "app.components.admin.UserFilterConditions.predicate_commented_in": "kommentoi", "app.components.admin.UserFilterConditions.predicate_contains": "sisältää", "app.components.admin.UserFilterConditions.predicate_ends_on": "pä<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_has_value": "on arvoa", "app.components.admin.UserFilterConditions.predicate_in": "tehnyt mit<PERSON><PERSON>n toim<PERSON>oa", "app.components.admin.UserFilterConditions.predicate_is": "On", "app.components.admin.UserFilterConditions.predicate_is_admin": "on järjestelmänvalvoja", "app.components.admin.UserFilterConditions.predicate_is_after": "on perässä", "app.components.admin.UserFilterConditions.predicate_is_before": "on ennen", "app.components.admin.UserFilterConditions.predicate_is_checked": "on tarkistettu", "app.components.admin.UserFilterConditions.predicate_is_empty": "on tyhjä", "app.components.admin.UserFilterConditions.predicate_is_equal": "On", "app.components.admin.UserFilterConditions.predicate_is_exactly": "on täsmälleen", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "on suurempi kuin", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "on suurempi tai yhtä suuri kuin", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "on tavallinen käyttäjä", "app.components.admin.UserFilterConditions.predicate_is_not_area": "ei sis<PERSON>ll<PERSON> al<PERSON>tta", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "ei sisällä kansiota", "app.components.admin.UserFilterConditions.predicate_is_not_input": "ei sisällä s<PERSON>ötettä", "app.components.admin.UserFilterConditions.predicate_is_not_project": "ei sisällä projektia", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "sulkee pois aiheen", "app.components.admin.UserFilterConditions.predicate_is_one_of": "on yksi", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "yksi al<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "yksi kansio<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "yksi tulo<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "yksi proje<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "yksi a<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "on projektipäällikkö", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "on pienempi kuin", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "on pienempi tai yhtä suuri kuin", "app.components.admin.UserFilterConditions.predicate_is_verified": "on vahvistettu", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "ei ala", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "ei kommentoinut", "app.components.admin.UserFilterConditions.predicate_not_contains": "ei sis<PERSON>llä", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "ei pä<PERSON>y", "app.components.admin.UserFilterConditions.predicate_not_has_value": "ei ole arvoa", "app.components.admin.UserFilterConditions.predicate_not_in": "ei osallist<PERSON>ut", "app.components.admin.UserFilterConditions.predicate_not_is": "ei ole", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "ei ole järjestelmänvalvoja", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "ei ole tark<PERSON>ettu", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "ei ole tyhjä", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "ei ole", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "ei ole tavallinen k<PERSON>täj<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "ei ole yksi ni<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "ei ole projektipäällikkö", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "ei ole vah<PERSON>ettu", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "ei lähettänyt syöttöä", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "ei reagoinut kommenttiin", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "ei reagoinut syötteeseen", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "ei il<PERSON><PERSON><PERSON><PERSON><PERSON> tap<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "ei ole o<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "ei tehnyt vapaaehtoistyötä", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "ei osallistunut äänestykseen", "app.components.admin.UserFilterConditions.predicate_nothing": "ei mit<PERSON>än", "app.components.admin.UserFilterConditions.predicate_posted_input": "l<PERSON><PERSON>tti <PERSON>", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reagoinut kommenttiin", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reagoinut syötteeseen", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "rekisteröitynyt tap<PERSON>tumaan", "app.components.admin.UserFilterConditions.predicate_something": "jotain", "app.components.admin.UserFilterConditions.predicate_taken_survey": "on tehnyt kyselyn", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_voted_in3": "osallistunut äänestykseen", "app.components.admin.UserFilterConditions.rulesFormLabelField": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "Arvo", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Et saa ilmoituksia lahjoituksestasi", "app.components.anonymousParticipationModal.cancel": "Peruuta", "app.components.anonymousParticipationModal.continue": "Jatka", "app.components.anonymousParticipationModal.participateAnonymously": "<PERSON><PERSON><PERSON><PERSON> anonyym<PERSON>i", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "Tämä <b>pii<PERSON><PERSON><PERSON> profiilisi</b> turvallisesti ylläpitäjiltä, projektipäälliköiltä ja muilta asukkailta tämän tietyn osallistumisen osalta, jotta kukaan ei voi yhdistää tätä osallistumista sinuun. Anonyymejä osallistumisia ei voi muokata, ja ne katsotaan lopullisiksi.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Alustamme tekeminen turvalliseksi jokaiselle k<PERSON>äj<PERSON> on meille ensisijaisen tärkeää. Sanoilla on väliä, joten olkaa ystävällisiä toisillenne.", "app.components.avatar.titleForAccessibility": "K<PERSON><PERSON>täjän {fullName}profiili", "app.components.customFields.mapInput.removeAnswer": "Poista vastaus", "app.components.customFields.mapInput.undo": "<PERSON><PERSON><PERSON>", "app.components.customFields.mapInput.undoLastPoint": "Kumoa viimeinen piste", "app.components.followUnfollow.follow": "<PERSON><PERSON><PERSON>", "app.components.followUnfollow.followADiscussion": "<PERSON><PERSON><PERSON> k<PERSON>", "app.components.followUnfollow.followTooltipInputPage2": "Seuraaminen käynnistää sähköpostipäivitykset tilamuutoksista, virallisista päivityksistä ja kommenteista. Voit {unsubscribeLink} mill<PERSON>n ta<PERSON>.", "app.components.followUnfollow.followTooltipProjects2": "Seuraaminen käynnistää sähköpostipäivitykset projektin muutoksista. Voit {unsubscribeLink} milloin ta<PERSON>.", "app.components.followUnfollow.unFollow": "<PERSON><PERSON><PERSON>", "app.components.followUnfollow.unsubscribe": "<PERSON><PERSON><PERSON> til<PERSON>", "app.components.followUnfollow.unsubscribeUrl": "/profiili/muokkaa", "app.components.form.ErrorDisplay.guidelinesLinkText": "ohjeistamme", "app.components.form.ErrorDisplay.next": "<PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.previous": "<PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.save": "<PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Aloita kirjoittaminen etsiäksesi käyttäjän sähköpostiosoitteen tai nimen perusteella...", "app.components.form.anonymousSurveyMessage2": "<PERSON><PERSON><PERSON> tämän kyselyn vastaukset ovat anonyymejä.", "app.components.form.backToInputManager": "<PERSON><PERSON><PERSON>", "app.components.form.backToProject": "<PERSON><PERSON><PERSON> projektiin", "app.components.form.components.controls.mapInput.removeAnswer": "Poista vastaus", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "Kumoa viimeinen kohta", "app.components.form.controls.addressInputAriaLabel": "Osoitteen s<PERSON>öttö", "app.components.form.controls.addressInputPlaceholder6": "Anna osoite...", "app.components.form.controls.adminFieldTooltip": "Kenttä näkyy vain järjestelmänvalvojille", "app.components.form.controls.allStatementsError": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> on valittava vastaus.", "app.components.form.controls.back": "<PERSON><PERSON><PERSON>", "app.components.form.controls.clearAll": "Tyh<PERSON><PERSON><PERSON> kaikki", "app.components.form.controls.clearAllScreenreader": "Poista kaikki vastaukset yllä olevasta matriisikysymyksestä", "app.components.form.controls.clickOnMapMultipleToAdd3": "Napsauta karttaa piirtääksesi. Siirrä sitten pisteitä vetämällä niitä.", "app.components.form.controls.clickOnMapToAddOrType": "<PERSON><PERSON><PERSON>a karttaa tai kirjoita osoite alle lisätäks<PERSON> vast<PERSON>.", "app.components.form.controls.confirm": "Vahvistaa", "app.components.form.controls.cosponsorsPlaceholder": "<PERSON><PERSON>a <PERSON>ttävän nimen kir<PERSON>n", "app.components.form.controls.currentRank": "Nykyinen sijoitus:", "app.components.form.controls.minimumCoordinates2": "Vähintään {numPoints} karttapisteitä vaaditaan.", "app.components.form.controls.noRankSelected": "Ar<PERSON>a ei ole valittu", "app.components.form.controls.notPublic1": "*Tämä vastaus jaetaan vain projektipäälliköille, ei y<PERSON>.", "app.components.form.controls.optionalParentheses": "(<PERSON><PERSON><PERSON><PERSON>)", "app.components.form.controls.rankingInstructions": "<PERSON><PERSON><PERSON> vaiht<PERSON>htoja vetämällä ja pudotta<PERSON>.", "app.components.form.controls.selectAsManyAsYouLike": "*Valitse yksi tai useampi alla olevista vaihtoehdoista", "app.components.form.controls.selectBetween": "*Valitse {minItems} - {maxItems} vaiht<PERSON>htoa", "app.components.form.controls.selectExactly2": "*<PERSON><PERSON>e täsmälleen {selectExactly, plural, one {# vaihtoehto} other {# vaihtoehtoa}}", "app.components.form.controls.selectMany": "*Valitse yksi tai useampi alla olevista vaihtoehdoista", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Piirrä napauttamalla karttaa. Siirrä sitten pisteitä vetämällä niitä.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "<PERSON><PERSON><PERSON><PERSON> napauttamalla karttaa.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Napauta karttaa lisätäksesi vastau<PERSON>.", "app.components.form.controls.tapOnMapToAddOrType": "Napauta karttaa tai kirjoita osoite alle lisätäksesi vast<PERSON>.", "app.components.form.controls.tapToAddALine": "Napauta lisätäksesi rivin", "app.components.form.controls.tapToAddAPoint": "Lisää piste napauttamalla", "app.components.form.controls.tapToAddAnArea": "Napauta lisätäks<PERSON> al<PERSON>en", "app.components.form.controls.uploadShapefileInstructions": "* La<PERSON>a zip-tiedosto, joka si<PERSON>lt<PERSON> yhden tai useamman shape-tiedoston.", "app.components.form.controls.validCordinatesTooltip2": "<PERSON><PERSON> sijainti ei näy vaihtoehtojen jou<PERSON> kir<PERSON>, voit määrittää tarkan sijainnin lisäämällä kelvollisia koordinaatteja muodossa \"leveysaste, pituusaste\" (esim. -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} / {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} / {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} / {total}, jossa {maxValue} on {maxLabel}", "app.components.form.error": "<PERSON><PERSON><PERSON>", "app.components.form.locationGoogleUnavailable": "Google Mapsin toimittamaa sijaintikenttää ei voitu ladata.", "app.components.form.progressBarLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.form.submit": "Lähetä", "app.components.form.submitApiError": "Lomakkeen lähettämisessä oli on<PERSON>. Tarkista mahdolliset virheet ja yritä uudelleen.", "app.components.form.verifiedBlocked": "Et voi muokata tätä kenttää, koska se sisältää vahvistettuja tietoja", "app.components.formBuilder.Page": "<PERSON><PERSON>", "app.components.formBuilder.accessibilityStatement": "saav<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>o", "app.components.formBuilder.addAnswer": "Lisää <PERSON>", "app.components.formBuilder.addStatement": "Lisää lausunto", "app.components.formBuilder.agree": "samaa mi<PERSON>", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "<PERSON><PERSON> on pääsy tekoälypakettimme, voit tehdä yhteenvedon ja luokitella tekstivastaukset tekoälyllä", "app.components.formBuilder.askFollowUpToggleLabel": "Pyydä se<PERSON>", "app.components.formBuilder.bad": "<PERSON><PERSON>", "app.components.formBuilder.buttonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.buttonLink": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.cancelLeaveBuilderButtonText": "Peruuta", "app.components.formBuilder.category": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.chooseMany": "Valitse monta", "app.components.formBuilder.chooseOne": "Valitse yksi", "app.components.formBuilder.close": "kiinni", "app.components.formBuilder.closed": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.configureMap": "Määritä kartta", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON><PERSON><PERSON><PERSON>, haluan poistua", "app.components.formBuilder.content": "Sisältö", "app.components.formBuilder.continuePageLabel": "Jat<PERSON><PERSON>", "app.components.formBuilder.cosponsors": "Yhteissponsorit", "app.components.formBuilder.default": "<PERSON><PERSON>", "app.components.formBuilder.defaultContent": "Oletussisältö", "app.components.formBuilder.delete": "Poistaa", "app.components.formBuilder.deleteButtonLabel": "Poistaa", "app.components.formBuilder.description": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.disabledBuiltInFieldTooltip": "<PERSON><PERSON><PERSON>ä on jo lis<PERSON><PERSON> lo<PERSON>. Oletussisältöä saa käyttää vain kerran.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Mukautetun sisällön lisääminen ei ole osa nykyistä lisenssiäsi. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja siitä.", "app.components.formBuilder.disagree": "eri mi<PERSON>", "app.components.formBuilder.displayAsDropdown": "Näytä pudotusvalikkona", "app.components.formBuilder.displayAsDropdownTooltip": "Näytä vaihtoehdot avattavassa valikossa. <PERSON><PERSON> on monia vaiht<PERSON>htoja, täm<PERSON> on suositeltavaa.", "app.components.formBuilder.done": "Tehty", "app.components.formBuilder.drawArea": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.drawRoute": "<PERSON><PERSON><PERSON><PERSON> reitti", "app.components.formBuilder.dropPin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "Anna vähintään 1 vastaus. <PERSON><PERSON><PERSON>, ett<PERSON> j<PERSON><PERSON><PERSON> vast<PERSON> on oltava otsikko.", "app.components.formBuilder.emptyOptionError": "Anna vähintään 1 vastaus", "app.components.formBuilder.emptyStatementError": "Anna vähintään 1 lausunto", "app.components.formBuilder.emptyTitleError": "<PERSON>", "app.components.formBuilder.emptyTitleMessage": "<PERSON> ka<PERSON>", "app.components.formBuilder.emptyTitleStatementMessage": "<PERSON> ka<PERSON> v<PERSON><PERSON><PERSON> ots<PERSON>", "app.components.formBuilder.enable": "ota <PERSON>", "app.components.formBuilder.errorMessage": "On ongelma. <PERSON><PERSON><PERSON><PERSON>, jotta voit tallentaa muutokset", "app.components.formBuilder.fieldGroup.description": "kuvaus (vapaaehtoinen)", "app.components.formBuilder.fieldGroup.title": "<PERSON><PERSON><PERSON><PERSON> (valinnainen)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Tällä hetkellä vastaukset näihin kysymyksiin ovat saatavilla vain viedyssä Excel-tiedostossa Input Managerissa, eivätkä ne näy käyttäjille.", "app.components.formBuilder.fieldLabel": "Vastausvaihtoehdot", "app.components.formBuilder.fieldLabelStatement": "la<PERSON><PERSON><PERSON>", "app.components.formBuilder.fileUpload": "Tiedoston lataus", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sivu", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Upota kartta kontekstiksi tai kysy osallistujille sijaintiin perustuvia kysymyksiä.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ökokemuksen takaamiseksi emme suosittele piste-, reitti- tai aluekysymysten lisäämistä karttapohjaisille sivuille.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "<PERSON><PERSON> sivu", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Kyselykartoitusominaisuudet eivät sisälly nykyiseen lisenssiisi. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "<PERSON><PERSON>n t<PERSON>i", "app.components.formBuilder.formEnd": "Lomakkeen loppu", "app.components.formBuilder.formField.cancelDeleteButtonText": "Peruuta", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "<PERSON><PERSON><PERSON><PERSON>, poista sivu", "app.components.formBuilder.formField.copyNoun": "<PERSON><PERSON>", "app.components.formBuilder.formField.copyVerb": "<PERSON><PERSON>", "app.components.formBuilder.formField.delete": "Poistaa", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Tä<PERSON>än sivun poistaminen poistaa myös siihen liittyvän logiikan. <PERSON><PERSON><PERSON><PERSON> varmasti poistaa sen?", "app.components.formBuilder.formField.deleteResultsInfo": "Tätä ei voi peruuttaa", "app.components.formBuilder.goToPageInputLabel": "<PERSON><PERSON><PERSON> sivu on sitten:", "app.components.formBuilder.good": "Hyvä", "app.components.formBuilder.helmetTitle": "Lomakke<PERSON> muokkaus", "app.components.formBuilder.imageFileUpload": "<PERSON><PERSON>", "app.components.formBuilder.invalidLogicBadgeMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> logiikka", "app.components.formBuilder.labels2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (v<PERSON><PERSON><PERSON>)", "app.components.formBuilder.labelsTooltipContent2": "Valitse valinnaiset tarrat mille tahansa lineaarisen asteikon arvolle.", "app.components.formBuilder.lastPage": "Lo<PERSON><PERSON>u", "app.components.formBuilder.layout": "Layout", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "<PERSON><PERSON><PERSON>, ett<PERSON> haluat poistua?", "app.components.formBuilder.leaveBuilderText": "<PERSON><PERSON><PERSON> on tallentamattomia muutoksia. Tallenna ennen poistumista. <PERSON><PERSON>, menetät muutokset.", "app.components.formBuilder.limitAnswersTooltip": "<PERSON>n se on käytössä, vast<PERSON><PERSON><PERSON> on valittava määritetty määrä vastauksia jatkaakseen.", "app.components.formBuilder.limitNumberAnswers": "<PERSON><PERSON><PERSON> vast<PERSON> m<PERSON>är<PERSON>", "app.components.formBuilder.linePolygonMapWarning2": "Viiva- ja monikulmiopiirrokset eivät välttämättä täytä esteettömyysstandardeja. Lisätietoja löytyy {accessibilityStatement}.", "app.components.formBuilder.linearScale": "<PERSON><PERSON><PERSON> asteikko", "app.components.formBuilder.locationDescription": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.logic": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.logicAnyOtherAnswer": "<PERSON><PERSON><PERSON> ta<PERSON> muu vastaus", "app.components.formBuilder.logicConflicts.conflictingLogic": "Ristiriitaista logiikkaa", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Tämä sivu sisältää kysymyksiä, jotka johtavat eri sivu<PERSON>. <PERSON><PERSON>t vastaavat use<PERSON> kys<PERSON>, ka<PERSON><PERSON><PERSON> oleva sivu näytetään. Varmista, että tämä toiminta on linjassa aiotun virtauksen kanssa.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Tällä sivulla on käytössä useita logiikkasääntöjä: usean valinnan kysymyslogiikka, sivutason logiikka ja kysymysten välinen logiikka. Kun nämä ehdot menevät päällekkäin, kysymyslogiikka menee sivulogiikkaan nähden etusijalle ja kauimpana oleva sivu näytetään. Tarkista logiikka varmistaaksesi, että se on linjassa aiotun kulkusi kanssa.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Täll<PERSON> sivulla on monivalintak<PERSON>ymys, jossa vaiht<PERSON><PERSON><PERSON>t johtavat eri sivuille. <PERSON><PERSON> valits<PERSON>t useita vaiht<PERSON>, pisin sivu näytetään. Varmista, että tämä toiminta on linjassa aiotun virtauksen kanssa.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Tällä sivulla on mon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jossa vaiht<PERSON><PERSON><PERSON><PERSON> johtavat eri sivuille ja kysymyksiä, jotka johtavat muille sivuille. Kauimpana oleva sivu näytetään, jos nämä ehdot menevät päällekkäin. Varmista, että tämä toiminta on linjassa aiotun virtauksen kanssa.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Tällä sivulla on monivalintakysymys, jossa vaiht<PERSON><PERSON><PERSON><PERSON> johtavat eri sivuille ja logiikka on asetettu sekä sivu- että kysymystasolla. Kysymyslogiikka on etus<PERSON>lla, ja kauimpana oleva sivu näytetään. Varmista, että tämä toiminta on linjassa aiotun virtauksen kanssa.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Täll<PERSON> sivulla on logiikka asetettuna sekä sivu- että kysymystasolla. Kysymyslogiikka on etusijalla sivutason logiikkaan nähden. Varmista, että tämä toiminta on linjassa aiotun virtauksen kanssa.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Tällä sivulla on logiikka asetettuna sekä sivu- että kysymys<PERSON>e, ja useita kysymyksiä suoraan eri sivuille. Kysymyslogiikka on etusijalla, ja kauimpana oleva sivu näytetään. Varmista, että tämä toiminta on linjassa aiotun virtauksen kanssa.", "app.components.formBuilder.logicNoAnswer2": "<PERSON><PERSON> vastattu", "app.components.formBuilder.logicPanelAnyOtherAnswer": "<PERSON><PERSON> joku muu vastaus", "app.components.formBuilder.logicPanelNoAnswer": "<PERSON><PERSON> ei vastata", "app.components.formBuilder.logicValidationError": "Logiikka ei välttämättä linkitä aikaisemmille sivuille", "app.components.formBuilder.longAnswer": "Pitkä <PERSON>", "app.components.formBuilder.mapConfiguration": "Kartan määritys", "app.components.formBuilder.mapping": "Kartat", "app.components.formBuilder.mappingNotInCurrentLicense": "Kyselykartoitusominaisuudet eivät sisälly nykyiseen lisenssiisi. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja.", "app.components.formBuilder.matrix": "Matrix", "app.components.formBuilder.matrixSettings.columns": "Sarakkeet", "app.components.formBuilder.matrixSettings.rows": "Rivit", "app.components.formBuilder.multipleChoice": "Monivalinta", "app.components.formBuilder.multipleChoiceHelperText": "<PERSON><PERSON> useat vaihtoehdot johtavat eri sivuille ja osallistujat valitsevat use<PERSON>man kuin yhden, ka<PERSON>mpana oleva sivu näytetään. Varmista, että tämä toiminta on linjassa aiotun virtauksen kanssa.", "app.components.formBuilder.multipleChoiceImage": "<PERSON><PERSON> valinta", "app.components.formBuilder.multiselect.maximum": "Enimmäismäärä", "app.components.formBuilder.multiselect.minimum": "<PERSON><PERSON>", "app.components.formBuilder.neutral": "Neutraali", "app.components.formBuilder.newField": "<PERSON><PERSON><PERSON> kent<PERSON>", "app.components.formBuilder.number": "Määrä", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "<PERSON><PERSON>", "app.components.formBuilder.optional": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.other": "<PERSON><PERSON>", "app.components.formBuilder.otherOption": "\"Muu\" vaiht<PERSON>hto", "app.components.formBuilder.otherOptionTooltip": "<PERSON><PERSON><PERSON> kirjoittaa mukautettu vastaus, jos annetut vastaukset eivät vastaa heidän mieltymyksiään", "app.components.formBuilder.page": "<PERSON><PERSON>", "app.components.formBuilder.pageCannotBeDeleted": "Tätä sivua ei voi poistaa.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Tätä sivua ei voi poistaa, eikä siihen voi lisätä kenttiä.", "app.components.formBuilder.pageRuleLabel": "<PERSON><PERSON><PERSON> sivu on:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Jos logiikkaa ei lisätä, lomake noudattaa normaalia kulkuaan. Jos sekä sivulla että sen kysymyksissä on logiikkaa, kysymyslogiikka on etusijalla. Varmista, että tämä vastaa aiottua kulkua. Lisätietoja on osoitteessa {supportPageLink}", "app.components.formBuilder.preview": "Esikatselu:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Yhteissponsoreita ei näytetä ladatussa PDF-tiedostossa, eikä niitä tueta tuoda FormSyncin kautta.", "app.components.formBuilder.printSupportTooltip.fileupload": "Tiedoston latauskysymykset näkyvät ladatussa PDF-tiedostossa ei-tuettuina, eik<PERSON> niitä tueta tuoda FormSyncin kautta.", "app.components.formBuilder.printSupportTooltip.mapping": "Kartoituskysymykset näkyvät ladatussa PDF-tiedostossa, mutta tasot eivät ole näkyvissä. Kartoituskysymysten tuontia FormSyncin kautta ei tueta.", "app.components.formBuilder.printSupportTooltip.matrix": "Matriisikysymykset näkyvät ladatussa PDF-tiedostossa, mutta niitä ei tällä hetkellä tueta tuoda FormSyncin kautta.", "app.components.formBuilder.printSupportTooltip.page": "Sivujen otsikot ja kuvaukset näkyvät ladatun PDF-tiedoston osioiden ylätunnisteina.", "app.components.formBuilder.printSupportTooltip.ranking": "Sijoituskysymykset näkyvät ladatussa PDF-tiedostossa, mutta niitä ei tällä hetkellä tueta tuoda FormSyncin kautta.", "app.components.formBuilder.printSupportTooltip.topics2": "Tagit näkyvät ladatussa PDF-tiedostossa ei-tuettuina, eikä niitä tueta tuoda FormSyncin kautta.", "app.components.formBuilder.proposedBudget": "<PERSON><PERSON><PERSON><PERSON><PERSON> bud<PERSON>ti", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "Tätä kysymystä ei voi poistaa.", "app.components.formBuilder.questionDescriptionOptional": "<PERSON><PERSON><PERSON><PERSON><PERSON> (valinnainen)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>ik<PERSON>", "app.components.formBuilder.randomize": "Satunnai<PERSON>", "app.components.formBuilder.randomizeToolTip": "Vastausten järjestys satunnaistetaan käyttäjää kohti", "app.components.formBuilder.range": "<PERSON><PERSON>", "app.components.formBuilder.ranking": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.rating": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.removeAnswer": "Poista vastaus", "app.components.formBuilder.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.requiredToggleLabel": "<PERSON><PERSON> tähän kysymykseen vastaaminen pakolliseksi", "app.components.formBuilder.ruleForAnswerLabel": "<PERSON><PERSON> on:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "<PERSON><PERSON> vast<PERSON> sisältävät:", "app.components.formBuilder.save": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.selectRangeTooltip": "Valitse mittakaavallesi maksimiarvo.", "app.components.formBuilder.sentiment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.shapefileUpload": "Esri-muo<PERSON><PERSON><PERSON><PERSON> lataus", "app.components.formBuilder.shortAnswer": "<PERSON>y<PERSON>t <PERSON>", "app.components.formBuilder.showResponseToUsersToggleLabel": "Näytä vastaus käyttäjille", "app.components.formBuilder.singleChoice": "<PERSON><PERSON><PERSON><PERSON><PERSON> valinta", "app.components.formBuilder.staleDataErrorMessage2": "On ollut ongelma. Tämä syöttölomake on tallennettu äskettäin jonnekin muualle. Tämä voi johtua siitä, että sinä tai joku toinen käyttäjä on avannut sen muokkausta varten toisessa selainikkunassa. Päivitä sivu saadaksesi uusimman lomakkeen ja tee sitten muutokset uudelleen.", "app.components.formBuilder.stronglyAgree": "<PERSON><PERSON><PERSON><PERSON> samaa mi<PERSON>", "app.components.formBuilder.stronglyDisagree": "jyr<PERSON><PERSON><PERSON>i eri mieltä", "app.components.formBuilder.supportArticleLinkText": "tämä sivu", "app.components.formBuilder.tags": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.title": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.toLabel": "to", "app.components.formBuilder.unsavedChanges": "<PERSON><PERSON><PERSON> on tallentamattomia muutoksia", "app.components.formBuilder.useCustomButton2": "Käytä mukautettua sivupainiketta", "app.components.formBuilder.veryBad": "<PERSON><PERSON><PERSON><PERSON><PERSON> huono", "app.components.formBuilder.veryGood": "<PERSON><PERSON><PERSON><PERSON><PERSON> hyvä", "app.components.ideas.similarIdeas.engageHere": "Osallistu tä<PERSON>ä", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Vastaavia lähetyksiä ei löytynyt.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON> niiden kanssa tekeminen voi auttaa vahvistamaan niitä!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Samanlaisia lähetyksiä jo lä<PERSON>tty:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Etsitään vastaavia esityksiä...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {Alle päivä} one {# päivä} other {# päivää}} j<PERSON><PERSON><PERSON><PERSON>ä", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  viikkoa j<PERSON>ljellä", "app.components.screenReaderCurrency.AED": "Yhdistyneiden arabiemiirikuntien dirham", "app.components.screenReaderCurrency.AFN": "a<PERSON><PERSON><PERSON> af<PERSON>", "app.components.screenReaderCurrency.ALL": "albanialainen Lek", "app.components.screenReaderCurrency.AMD": "Armenian Dram", "app.components.screenReaderCurrency.ANG": "Alankomaiden Antillien guldeni", "app.components.screenReaderCurrency.AOA": "Angolan <PERSON>", "app.components.screenReaderCurrency.ARS": "Argentiinan peso", "app.components.screenReaderCurrency.AUD": "Australian dollari", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "Azerbaidžanin Manat", "app.components.screenReaderCurrency.BAM": "Bosnia-Hertsegovinan avoautomerkki", "app.components.screenReaderCurrency.BBD": "Barbadin dollari", "app.components.screenReaderCurrency.BDT": "Bangladeshi Taka", "app.components.screenReaderCurrency.BGN": "Bulgarian Lev", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "Burundin frangi", "app.components.screenReaderCurrency.BMD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BND": "Brunein dollari", "app.components.screenReaderCurrency.BOB": "Bolivian Boliviano", "app.components.screenReaderCurrency.BOV": "Bolivian M<PERSON>", "app.components.screenReaderCurrency.BRL": "Brasilian real", "app.components.screenReaderCurrency.BSD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BTN": "Bhutanilainen Ngultrum", "app.components.screenReaderCurrency.BWP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "Valko-Venäjän rupla", "app.components.screenReaderCurrency.BZD": "Belizen dollari", "app.components.screenReaderCurrency.CAD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CDF": "<PERSON><PERSON> frangi", "app.components.screenReaderCurrency.CHE": "WIR euroa", "app.components.screenReaderCurrency.CHF": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Chilen <PERSON> (UF)", "app.components.screenReaderCurrency.CLP": "Chilen peso", "app.components.screenReaderCurrency.CNY": "Kiinan yuania", "app.components.screenReaderCurrency.COP": "Kolumbian peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Rican Colón", "app.components.screenReaderCurrency.CRE": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CUC": "<PERSON><PERSON><PERSON> v<PERSON> peso", "app.components.screenReaderCurrency.CUP": "Kuuban peso", "app.components.screenReaderCurrency.CVE": "Kap Verden escudo", "app.components.screenReaderCurrency.CZK": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.screenReaderCurrency.DJF": "Djiboutin frangi", "app.components.screenReaderCurrency.DKK": "Tanskan kruunu", "app.components.screenReaderCurrency.DOP": "Dominikaaninen peso", "app.components.screenReaderCurrency.DZD": "Algerian dinaari", "app.components.screenReaderCurrency.EGP": "<PERSON><PERSON> punta", "app.components.screenReaderCurrency.ERN": "Eritrealainen Nakfa", "app.components.screenReaderCurrency.ETB": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.EUR": "euroa", "app.components.screenReaderCurrency.FJD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.FKP": "Falklandinsaarten punta", "app.components.screenReaderCurrency.GBP": "Britannian punta", "app.components.screenReaderCurrency.GEL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "<PERSON><PERSON> punta", "app.components.screenReaderCurrency.GMD": "Gambian Dalasi", "app.components.screenReaderCurrency.GNF": "Guinean frangi", "app.components.screenReaderCurrency.GTQ": "Guatemalan <PERSON>", "app.components.screenReaderCurrency.GYD": "Guyanan dollari", "app.components.screenReaderCurrency.HKD": "Hongkongin dollari", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "Kroatian kuna", "app.components.screenReaderCurrency.HTG": "Haitin gourde", "app.components.screenReaderCurrency.HUF": "<PERSON><PERSON><PERSON> for<PERSON>", "app.components.screenReaderCurrency.IDR": "Indonesian rupia", "app.components.screenReaderCurrency.ILS": "<PERSON><PERSON> u<PERSON>i se<PERSON>i", "app.components.screenReaderCurrency.INR": "Intian rupia", "app.components.screenReaderCurrency.IQD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.IRR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.ISK": "<PERSON><PERSON><PERSON> k<PERSON>", "app.components.screenReaderCurrency.JMD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KES": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KGS": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KHR": "Kambodžalainen Riel", "app.components.screenReaderCurrency.KMF": "<PERSON><PERSON><PERSON> frangi", "app.components.screenReaderCurrency.KPW": "<PERSON><PERSON><PERSON><PERSON>-Korean won", "app.components.screenReaderCurrency.KRW": "Etelä-Korean wonia", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "Caymansaarten dollari", "app.components.screenReaderCurrency.KZT": "Kazakstan<PERSON>", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Libanonin punta", "app.components.screenReaderCurrency.LKR": "Sri Lankan rupia", "app.components.screenReaderCurrency.LRD": "Liberian dollari", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Liettuan litiä", "app.components.screenReaderCurrency.LVL": "Latvian latit", "app.components.screenReaderCurrency.LYD": "Libyan dinaari", "app.components.screenReaderCurrency.MAD": "<PERSON><PERSON><PERSON> dirham", "app.components.screenReaderCurrency.MDL": "Moldovan leu", "app.components.screenReaderCurrency.MGA": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MKD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "Mongolian Tögrög", "app.components.screenReaderCurrency.MOP": "Macaon pataca", "app.components.screenReaderCurrency.MRO": "Mauritanian Ouguiya", "app.components.screenReaderCurrency.MUR": "<PERSON><PERSON><PERSON><PERSON><PERSON> rupia", "app.components.screenReaderCurrency.MVR": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MXN": "Meksikon peso", "app.components.screenReaderCurrency.MXV": "Meksikon Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malesian ringgit", "app.components.screenReaderCurrency.MZN": "Mosambikin Metical", "app.components.screenReaderCurrency.NAD": "Namibian dollari", "app.components.screenReaderCurrency.NGN": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NIO": "Nicaraguan Córdoba", "app.components.screenReaderCurrency.NOK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.NPR": "Nepalin rupia", "app.components.screenReaderCurrency.NZD": "Uuden-<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.OMR": "Omani R<PERSON>", "app.components.screenReaderCurrency.PAB": "Panaman Balboa", "app.components.screenReaderCurrency.PEN": "Perun Sol", "app.components.screenReaderCurrency.PGK": "Papua-Uusi-Guinean Kiina", "app.components.screenReaderCurrency.PHP": "<PERSON><PERSON><PERSON><PERSON><PERSON> peso", "app.components.screenReaderCurrency.PKR": "Pakistanin rupia", "app.components.screenReaderCurrency.PLN": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.PYG": "Paraguayn Gua<PERSON>í", "app.components.screenReaderCurrency.QAR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.RON": "Romanian Leu", "app.components.screenReaderCurrency.RSD": "Serbian dinaari", "app.components.screenReaderCurrency.RUB": "Venäjän rupla", "app.components.screenReaderCurrency.RWF": "<PERSON><PERSON><PERSON> frangi", "app.components.screenReaderCurrency.SAR": "Saudi Rial", "app.components.screenReaderCurrency.SBD": "Salomonsaarten dollari", "app.components.screenReaderCurrency.SCR": "<PERSON><PERSON><PERSON><PERSON> rupia", "app.components.screenReaderCurrency.SDG": "<PERSON><PERSON> punta", "app.components.screenReaderCurrency.SEK": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SGD": "Singaporen dollari", "app.components.screenReaderCurrency.SHP": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.SRD": "<PERSON>ina<PERSON> dollari", "app.components.screenReaderCurrency.SSP": "Etelä-<PERSON><PERSON> punta", "app.components.screenReaderCurrency.STD": "São Tomé ja Principe <PERSON>", "app.components.screenReaderCurrency.SYP": "Syyrian punta", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "<PERSON><PERSON><PERSON> bah<PERSON>", "app.components.screenReaderCurrency.TJS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TMT": "Turkmenistani Manat", "app.components.screenReaderCurrency.TND": "Tunisian dinaari", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Tongan Paʻanga", "app.components.screenReaderCurrency.TRY": "<PERSON>rkin liira", "app.components.screenReaderCurrency.TTD": "Trinidad ja Tobagon dollari", "app.components.screenReaderCurrency.TWD": "<PERSON>usi <PERSON>i", "app.components.screenReaderCurrency.TZS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.UAH": "Ukrainan hryvnia", "app.components.screenReaderCurrency.UGX": "Uganda<PERSON>", "app.components.screenReaderCurrency.USD": "Yhdysvaltain dollari", "app.components.screenReaderCurrency.USN": "<PERSON>h<PERSON>s<PERSON><PERSON> (seuraavana päivänä)", "app.components.screenReaderCurrency.USS": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dollar<PERSON> (sama päivä)", "app.components.screenReaderCurrency.UYI": "Uruguayn peso ja Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguayn peso", "app.components.screenReaderCurrency.UZS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.VEF": "Venezuelan bolívar", "app.components.screenReaderCurrency.VND": "<PERSON><PERSON>", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.XAF": "Keski-Afrikan CFA-frangi", "app.components.screenReaderCurrency.XAG": "<PERSON><PERSON> (yksi troy <PERSON>)", "app.components.screenReaderCurrency.XAU": "<PERSON>lta (yksi troy <PERSON>)", "app.components.screenReaderCurrency.XBA": "European Composite Unit (EURCO)", "app.components.screenReaderCurrency.XBB": "Euroopan rahay<PERSON> (EMU-6)", "app.components.screenReaderCurrency.XBC": "Euroopan laskentayksikkö 9 (EUA-9)", "app.components.screenReaderCurrency.XBD": "Euroopan laskentayksikkö 17 (EUA-17)", "app.components.screenReaderCurrency.XCD": "Itä-Karibian dollari", "app.components.screenReaderCurrency.XDR": "<PERSON><PERSON><PERSON><PERSON> nosto-o<PERSON>t", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "Länsi-Afrikan CFA-frangi", "app.components.screenReaderCurrency.XPD": "Palladium (yksi troy <PERSON>ssi)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON><PERSON> (yksi troy <PERSON>)", "app.components.screenReaderCurrency.XTS": "<PERSON><PERSON><PERSON><PERSON> testaustarkoit<PERSON><PERSON><PERSON> varatut koodit", "app.components.screenReaderCurrency.XXX": "<PERSON>i valu<PERSON>aa", "app.components.screenReaderCurrency.YER": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ZAR": "Etelä-Afrikan rand", "app.components.screenReaderCurrency.ZMW": "Sambian kwacha", "app.components.screenReaderCurrency.amount": "Määrä", "app.components.screenReaderCurrency.currency": "<PERSON><PERSON><PERSON><PERSON>", "app.components.trendIndicator.lastQuarter2": "viimeisellä neljänneksellä", "app.containers.AccessibilityStatement.applicability": "Tämä saavutettavuus<PERSON>unto kos<PERSON>e, {demoPlatformLink}, joka edustaa tätä verkkosivustoa. Sivu käyttää samaa lähdekoodia ja sillä on samat to<PERSON>.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Arviointi", "app.containers.AccessibilityStatement.assesmentText2022": "<PERSON><PERSON><PERSON><PERSON><PERSON> sivuston saavutettavuuden arvioi ulko<PERSON>en taho, joka ei ollut mukana suunnittelu- ja kehitysprosessissa. Edellä mainitun {demoPlatformLink} vaatimustenmukaisuus voidaan tunnistaa tästä {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "voit muuttaa <PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.changePreferencesText": "<PERSON><PERSON><PERSON> {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Poikkeukset vaatimustenmukaisuudesta", "app.containers.AccessibilityStatement.conformanceStatus": "<PERSON>gipal<PERSON><PERSON> saavutettavuuden tila", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Palvelu täyttää saavutettavuusvaatimukset osittain:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demosivust<PERSON>", "app.containers.AccessibilityStatement.email": "Sähköposti:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Sulautetut kyselytyökalut", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Sulautetut k<PERSON>elytyökalut, jotka ovat käytettävissä tällä alust<PERSON>, ovat kolmannen osapuolen oh<PERSON>lmistoja, eivätkä ne välttämättä ole käytettävissä.", "app.containers.AccessibilityStatement.exception_1": "On ma<PERSON><PERSON><PERSON><PERSON>, että alustan käyttäjät lataavat alustalle PDF-tiedostoja, kuvia tai muita tiedostotyyppejä, liitteinä tai lisäävät ne tekstikenttiin. Nämä asiakirjat eivät välttämättä ole täysin saavutettavia.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "<PERSON><PERSON><PERSON> al<PERSON> p<PERSON><PERSON> ottaa mielell<PERSON>än vastaan palautetta tämän sivuston saavutettavuudesta. Ota meihin heihin yhteyttä jollakin seuraavista tavoista:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "<PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Bryssel, Belgia", "app.containers.AccessibilityStatement.headTitle": "Helppokäyttöisyyslausunto | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} on sitoutunut ta<PERSON><PERSON><PERSON><PERSON> alustan, joka on kaikkien käyttäjien käytettävissä tekniikasta tai kyvystä riippumatta. Noudatamme nykyisiä asiaankuuluvia saavutettavuusstandardeja noudatamme jatkuvasti pyrkiessämme maksimoimaan alustamme saavutettavuuden ja käytettävyyden kaikille käyttäjille.", "app.containers.AccessibilityStatement.mapping": "Kartat", "app.containers.AccessibilityStatement.mapping_1": "Alustalla olevat kartat täyttävät saavutettavuusvaatimukset osittain. Kartan laajuutta, zoomausta ja käyttöliittymäwidgettejä voidaan ohjata näppäimistöllä karttoja katseltaessa. Järjestelmänvalvojat voivat myös määrittää karttatasojen tyylin taustatoiminnoissa tai käyttämällä Esri-integraatiota luodakseen helpommin käytettävissä olevia väripaletteja ja symboliikkaa. Erilaisten viiva- tai monikulmiotyylien (esim. katkoviivojen) käyttö auttaa myös erottamaan karttatasot aina kun mahdollista, ja vaikka tällaista tyyliä ei voida määrittää alustassamme tällä hetkellä, se voidaan määrittää, jos käytetään karttoja Esri-integraation kanssa.", "app.containers.AccessibilityStatement.mapping_2": "Alustan kartat eivät ole täysin saavutettavia, koska ne eivät puhu ääneen pohjakarttoja karttatasoja tai datan trendejä näytönlukuohjelmia käyttäville käyttäjille. Täysin saavutettavissa kartoissa on esitettävä kuuluvasti karttatasot ja kuvailtava datan merkitykselliset trendit. Lisäksi viiva- ja polygonikartan piirtäminen ei ole käytettävissä, koska muotoja ei voi piirtää näppäimistöllä. Vaihtoehtoisia syöttötapoja ei ole tällä hetkellä saatavilla teknisen monimutkaisuuden vuoksi.", "app.containers.AccessibilityStatement.mapping_3": "Jotta viiva- ja monikulmiokartan piirtäminen olisi <PERSON>, suosittelemme sisällyttämään kyselykysymykseen tai sivun kuvaukseen johdannon tai selityksen siitä, mitä kartta näyttää ja mahdollisista asiaan liittyvistä trendeistä. Lisäksi voidaan antaa lyhyt tai pitkä vastaustekstikysymys, jotta vastaajat voivat tarvittaessa kuvailla vastaustaan selkeästi kartan klikkaamisen sijaan. Suosittelemme lisäämään myös projektipäällikön yhteystiedot, jotta vastaajat, jotka eivät osaa täyttää karttakysymystä, voivat pyytää vaihtoehtoista tapaa vastata kysymykseen (esim. videokokous).", "app.containers.AccessibilityStatement.mapping_4": "Ideointiprojekteissa ja -ehdotuksissa on mahdollisuus näyttää syötteet karttanäkymässä, joka ei ole käytettävissä. Näille menetelmille on kuitenkin käytettävissä vaihtoehtoinen luettelonäkymä syötteistä, joka on käytettävissä.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Verk<PERSON>yö<PERSON><PERSON><PERSON>mme on live-videon suoratoistokomponentti, joka ei tällä hetkellä tue tekstitystä.", "app.containers.AccessibilityStatement.pageDescription": "<PERSON><PERSON><PERSON> tämän sivuston sa<PERSON>", "app.containers.AccessibilityStatement.postalAddress": "Postiosoite:", "app.containers.AccessibilityStatement.publicationDate": "Julkaisupäivämäärä", "app.containers.AccessibilityStatement.publicationDate2024": "Tämä sa<PERSON>ttavuusseloste on julkaistu 21.8.2024.", "app.containers.AccessibilityStatement.responsiveness": "Pyrimme vastaamaan palautteeseen 1-2 arkipäivän kuluessa.", "app.containers.AccessibilityStatement.statusPageText": "tilasivu", "app.containers.AccessibilityStatement.technologiesIntro": "<PERSON><PERSON><PERSON><PERSON><PERSON> sivuston saavutettavuus perustuu seuraaviin tekni<PERSON>in:", "app.containers.AccessibilityStatement.technologiesTitle": "Teknologiat", "app.containers.AccessibilityStatement.title": "Saavutettavuusseloste", "app.containers.AccessibilityStatement.userGeneratedContent": "Käyttäjien luoma si<PERSON>ältö", "app.containers.AccessibilityStatement.workshops": "Työpajat", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Valitse projekti", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Content Builderin avulla voit käyttää kehittyneempiä asetteluvaihtoehtoja. <PERSON><PERSON><PERSON> kieliss<PERSON>, joissa sisältöä ei ole saatavilla sisällö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tavallinen projektin kuvaussisältö näytetään sen sijaan.", "app.containers.AdminPage.ProjectDescription.linkText": "Muokkaa kuvausta Content Builderissa", "app.containers.AdminPage.ProjectDescription.saveError": "Jo<PERSON> meni pieleen projektin kuvausta tallenne<PERSON>a.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Käytä sisällön rakennustyökalua kuvaukseen", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Content Builderin avulla voit käyttää kehittyneempiä asetteluvaihtoehtoja.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "Näytä projekti", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "<PERSON><PERSON>", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jotka täyttävät kaikki se<PERSON>t ehdo<PERSON>, lisätään automaattisesti ryhmään:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "<PERSON> v<PERSON>ään yksi s<PERSON>", "app.containers.AdminPage.Users.UsersGroup.rulesError": "<PERSON><PERSON><PERSON><PERSON> ehdot ovat puutt<PERSON>", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Älykkäiden ryhmien mä<PERSON>täminen ei ole osa nykyistä lisenssiäsi. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja siitä.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "<PERSON>", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "<PERSON><PERSON><PERSON><PERSON> on poistettu käyt<PERSON>stä alust<PERSON>, poista vahvistussääntö tai ota yhteyttä tukeen.", "app.containers.App.appMetaDescription": "Tervetuloa {orgName}online-osallistumisalustaan. \nTutustu paikallisiin hankkeisiin ja osallistu keskusteluun!", "app.containers.App.loading": "Ladataan...", "app.containers.App.metaTitle1": "Osallistu ja vaikuta | {orgName}", "app.containers.App.skipLinkText": "<PERSON><PERSON><PERSON>öö<PERSON>", "app.containers.AreaTerms.areaTerm": "al<PERSON><PERSON>", "app.containers.AreaTerms.areasTerm": "al<PERSON><PERSON>", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Tällä sähköpostiosoitteella on jo tili. Voit kirjautua ul<PERSON>, kirjautua sisään tällä sähköpostiosoitteella ja vahvistaa tilisi asetussivulla.", "app.containers.Authentication.steps.AccessDenied.close": "Lähellä", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Et täytä tähän prosessiin o<PERSON>sen edellytyksiä.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "<PERSON> tun<PERSON>", "app.containers.Authentication.steps.Invitation.token": "Token", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "<PERSON><PERSON> sinulla jo tili? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Sähköpostit tässä kategoriassa", "app.containers.CampaignsConsentForm.messageError": "Sähköpostiasetusten tallentamisessa tapahtui virhe.", "app.containers.CampaignsConsentForm.messageSuccess": "Sähköpostiasetuksesi on tallennettu.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Millaisia sähköposti-ilmoituksia haluat saada? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Ilmoitukset", "app.containers.CampaignsConsentForm.submit": "<PERSON><PERSON><PERSON>", "app.containers.ChangeEmail.backToProfile": "<PERSON><PERSON><PERSON> pro<PERSON><PERSON>", "app.containers.ChangeEmail.confirmationModalTitle": "vahvista sähköpostiosoitteesi", "app.containers.ChangeEmail.emailEmptyError": "<PERSON>", "app.containers.ChangeEmail.emailInvalidError": "<PERSON>öpostiosoite oikeassa muodossa, esimerkiksi <EMAIL>", "app.containers.ChangeEmail.emailRequired": "Ole hyvä ja lisää sähköpostiosoitteesi.", "app.containers.ChangeEmail.emailTaken": "Tämä sähköpostiosoite on jo käytössä.", "app.containers.ChangeEmail.emailUpdateCancelled": "Sähköpostipäivitys on peruttu.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Päivitä sähköpostisi käynnistämällä prosessi uudelleen.", "app.containers.ChangeEmail.helmetDescription": "Vaihda sähköpostisivusi", "app.containers.ChangeEmail.helmetTitle": "Vaihda sähköpostiosoitteesi", "app.containers.ChangeEmail.newEmailLabel": "Uusi sähköposti", "app.containers.ChangeEmail.submitButton": "Lähetä", "app.containers.ChangeEmail.titleAddEmail": "Lisää sähköpostiosoitteesi", "app.containers.ChangeEmail.titleChangeEmail": "Vaihda sähköpostiosoitteesi", "app.containers.ChangeEmail.updateSuccessful": "Sähköpostisi päivitys onnistui.", "app.containers.ChangePassword.currentPasswordLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.currentPasswordRequired": "Syötä tämänhetkinen salasanasi", "app.containers.ChangePassword.goHome": "<PERSON>e kotiin", "app.containers.ChangePassword.helmetDescription": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.helmetTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.newPasswordLabel": "<PERSON><PERSON><PERSON>", "app.containers.ChangePassword.newPasswordRequired": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.password.minimumPasswordLengthError": "<PERSON>, joka on vähintään {minimumPasswordLength} merkkiä pitkä", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Salasanasi on päivitetty onnistuneesti", "app.containers.ChangePassword.passwordEmptyError": "Syötä sa<PERSON>", "app.containers.ChangePassword.passwordsDontMatch": "Vahvista uusi salasana", "app.containers.ChangePassword.titleAddPassword": "Lis<PERSON><PERSON> sa<PERSON>", "app.containers.ChangePassword.titleChangePassword": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.a11y_commentDeleted": "<PERSON><PERSON><PERSON><PERSON> pois<PERSON>u", "app.containers.Comments.a11y_commentPosted": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {ei tykkää} one {1 tykkäys} other {# tykkää}}", "app.containers.Comments.a11y_undoLike": "<PERSON><PERSON><PERSON>", "app.containers.Comments.addCommentError": "<PERSON><PERSON> meni pieleen. Yritä uudelleen myö<PERSON>.", "app.containers.Comments.adminCommentDeletionCancelButton": "Peruuta", "app.containers.Comments.adminCommentDeletionConfirmButton": "Poista tämä kommentti", "app.containers.Comments.cancelCommentEdit": "Peruuta", "app.containers.Comments.childCommentBodyPlaceholder": "<PERSON><PERSON><PERSON><PERSON>...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "<PERSON><PERSON><PERSON><PERSON> kommentti on poistettu.", "app.containers.Comments.commentDeletionCancelButton": "<PERSON><PERSON><PERSON> k<PERSON>", "app.containers.Comments.commentDeletionConfirmButton": "Poista kom<PERSON>tini", "app.containers.Comments.commentLike": "<PERSON><PERSON>", "app.containers.Comments.commentReplyButton": "Vastaa", "app.containers.Comments.commentsSortTitle": "Lajittele kommentit", "app.containers.Comments.completeProfileLinkText": "viimeistele profiilisi", "app.containers.Comments.completeProfileToComment": "Kommentoi {completeRegistrationLink} .", "app.containers.Comments.confirmCommentDeletion": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän kommentin? Ei ole paluuta!", "app.containers.Comments.deleteComment": "Poistaa", "app.containers.Comments.deleteReasonDescriptionError": "<PERSON> l<PERSON> s<PERSON>ä<PERSON>", "app.containers.Comments.deleteReasonError": "<PERSON> s<PERSON>y", "app.containers.Comments.deleteReason_inappropriate": "Se on sopimatonta tai loukkaavaa", "app.containers.Comments.deleteReason_irrelevant": "Tämä ei o<PERSON>tia", "app.containers.Comments.deleteReason_other": "<PERSON><PERSON> s<PERSON>y", "app.containers.Comments.editComment": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.guidelinesLinkText": "yhteisön sääntömme", "app.containers.Comments.ideaCommentBodyPlaceholder": "<PERSON><PERSON><PERSON><PERSON> kom<PERSON> t<PERSON>n", "app.containers.Comments.internalCommentingNudgeMessage": "Sisäisten kommenttien tekeminen ei sisälly nykyiseen lisenssiisi. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja siitä.", "app.containers.Comments.internalConversation": "Sisäinen keskustelu", "app.containers.Comments.loadMoreComments": "Lataa lisää kommentteja", "app.containers.Comments.loadingComments": "Ladataan kommentteja...", "app.containers.Comments.loadingMoreComments": "Ladataan lisää kommentteja...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Tämä kommentti ei näy taval<PERSON>ille käyttäjille", "app.containers.Comments.postInternalComment": "Lähetä sisäinen kommentti", "app.containers.Comments.postPublicComment": "Lähetä julkinen kommentti", "app.containers.Comments.profanityError": "Oho! Näyttää siltä, että viestissäsi on kieltä, joka ei täytä {guidelinesLink}. Pyrimme pitämään tämän turvallisena paikan kaikille. Muokkaa syötettäsi ja yritä uudelleen.", "app.containers.Comments.publicDiscussion": "<PERSON><PERSON>en keskustelu", "app.containers.Comments.publishComment": "Lähetä kommenttisi", "app.containers.Comments.reportAsSpamModalTitle": "<PERSON><PERSON>i haluat ilmoittaa tämän roskapostiksi?", "app.containers.Comments.saveComment": "<PERSON><PERSON><PERSON>", "app.containers.Comments.signInLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.signInToComment": "Kommentoi {signInLink} .", "app.containers.Comments.signUpLinkText": "Rekisteröidy", "app.containers.Comments.verifyIdentityLinkText": "Vahvista henkilöllisyytesi", "app.containers.Comments.visibleToUsersPlaceholder": "Tämä kommentti näkyy tavallisille käyttäjille", "app.containers.Comments.visibleToUsersWarning": "Täällä lähetetyt kommentit näkyvät tavallisille käyttäjille.", "app.containers.ContentBuilder.PageTitle": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.CookiePolicy.advertisingContent": "Mainosevästeitä voidaan käyttää personoimaan ja mittaamaan ulkopuolisten markkinointikampanjoiden tehokkuutta tämän alustan kanssa. Emme näytä mainoksia tällä alustalla, mutta saatat saada räätälöityjä mainoksia vierailemiesi sivujen perusteella.", "app.containers.CookiePolicy.advertisingTitle": "Mainonta", "app.containers.CookiePolicy.analyticsContents": "Analytics-evä<PERSON><PERSON> seuraavat vierailijoiden käyttäytymistä, kuten millä sivuilla on vierailtu ja kuinka kauan. He voivat myös kerätä joitain teknisiä tietoja, kuten selaintietoja, likimääräistä sijaintia ja IP-osoitteita. Käytämme näitä tietoja vain sisäisesti parantaaksemme yleistä käyttökokemusta ja alustan toimintaa. Tällaisia tietoja voidaan myös jakaa Go Vocalin ja {orgName} välillä arvioidakseen ja parantaakseen sitoutumista alustalla oleviin projekteihin. <PERSON><PERSON><PERSON>, että tiedot ovat anonyymejä ja niitä käytetään kootusti – ne eivät tunnista sinua henkilökohtaisesti. On kuitenkin mahdollista, että jos nämä tiedot yhdistettäisiin muihin tietol<PERSON>htei<PERSON>in, tällainen tunnistaminen voisi tapahtua.", "app.containers.CookiePolicy.analyticsTitle": "Analytics-evästeet", "app.containers.CookiePolicy.cookiePolicyDescription": "Yksityiskohtainen selvitys siitä, kuinka käytämme evästeitä tällä alustalla", "app.containers.CookiePolicy.cookiePolicyTitle": "Evästekäytäntö", "app.containers.CookiePolicy.essentialContent": "Jotkut evästeet ovat välttämättömiä tämän alustan asianmukaisen toiminnan varmistamiseksi. Näitä välttämättömiä evästeitä käytetään ensisijaisesti tilisi todentamiseen, kun vierailet alustalla, ja valitsemasi kielen tallentamiseen.", "app.containers.CookiePolicy.essentialTitle": "Välttämättömät evästeet", "app.containers.CookiePolicy.externalContent": "Joillakin sivuillamme voi olla ulkopuolisten palveluntarjoajien, kuten YouTuben tai Typeformin, sis<PERSON>ltöä. Emme voi hallita näitä kolmannen osapuolen evästeitä, ja näiden ulkoisten palveluntarjoajien sisällön katseleminen saattaa myös johtaa evästeiden asentamiseen laitteellesi.", "app.containers.CookiePolicy.externalTitle": "Ulkoiset evästeet", "app.containers.CookiePolicy.functionalContents": "Toiminnalliset evästeet voidaan ottaa k<PERSON>öön, jotta vierailijat saavat ilmoituksia päivityksistä ja pääsevät tukikanaviin suoraan alustalta.", "app.containers.CookiePolicy.functionalTitle": "Toiminnalliset evästeet", "app.containers.CookiePolicy.headCookiePolicyTitle": "Evästekäytäntö | {orgName}", "app.containers.CookiePolicy.intro": "Evästeet ovat tekstitiedostoja, jotka tallennetaan selaimeesi tai tietokoneesi tai mobiililaitteesi kiintolevylle vieraillessasi verkkosivustolla ja joihin verkkosivusto voi viitata myöhempien vierailujen aikana. Käytämme evästeitä ymmärtääksemme, kuinka vierailijat käyttävät tätä alustaa parantaaksemme sen suunnittelua ja kokemusta, muistaaks<PERSON><PERSON> valinta<PERSON> (kuten haluamasi kielen) ja tukeaksemme rekisteröityjen käyttäjien ja alustan ylläpitäjien tärkeimpiä toimintoja.", "app.containers.CookiePolicy.manageCookiesDescription": "Voit ottaa käyttöön tai poistaa käytöstä analytiikka-, mark<PERSON>ointi- ja toiminnall<PERSON>t evästeet milloin tahansa evästeasetuksissasi. Voit myös manuaalisesti tai automaattisesti poistaa olemassa olevat evästeet Internet-selaimesi kautta. Evästeet voidaan kuitenkin sijoittaa uudelleen suostumuksesi jälkeen, kun vierailet tällä alustalla. Jo<PERSON> et poista evästeitä, evästeasetuksesi säilyvät 60 päivää, jonka jälkeen sinulta kysytään uudelleen suostumuksesi.", "app.containers.CookiePolicy.manageCookiesPreferences": "<PERSON><PERSON><PERSON> {manageCookiesPreferencesButtonText} nähdäksesi täydellisen luettelon tällä alustalla käytetyistä kolmannen osapuolen integroinneista ja hallitaksesi asetuksiasi.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "evästeasetukset", "app.containers.CookiePolicy.manageCookiesTitle": "Evästeiden hallinta", "app.containers.CookiePolicy.viewPreferencesButtonText": "Evästeasetukset", "app.containers.CookiePolicy.viewPreferencesText": "Alla olevat evästeluokat eivät välttämättä koske kaikkia vierailijoita tai alustoja; katso {viewPreferencesButton} saada<PERSON><PERSON> tä<PERSON><PERSON>sen luettelon sinua koskevista kolmannen osapuolen integraatioista.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "<PERSON><PERSON> käytämme evästeitä?", "app.containers.CustomPageShow.editPage": "<PERSON><PERSON><PERSON><PERSON> sivua", "app.containers.CustomPageShow.goBack": "<PERSON><PERSON>", "app.containers.CustomPageShow.notFound": "Sivua ei l<PERSON>y", "app.containers.DisabledAccount.bottomText": "<PERSON>oit kirjautua uudelleen sisään osoitteesta {date}.", "app.containers.DisabledAccount.termsAndConditions": "käyttöehdot", "app.containers.DisabledAccount.text2": "Tilisi osallistumisalustalla {orgName} on poistettu tilapäisesti käytöstä yhteisön sääntöjen rikkomisen vuoksi. Lisätietoja tästä saat osoitteesta {TermsAndConditions}.", "app.containers.DisabledAccount.title": "<PERSON><PERSON><PERSON> on väliaikaisesti poistettu käytöstä", "app.containers.EventsShow.addToCalendar": "Lisää kalenteriin", "app.containers.EventsShow.editEvent": "<PERSON><PERSON><PERSON><PERSON> tap<PERSON>", "app.containers.EventsShow.emailSharingBody2": "O<PERSON><PERSON><PERSON> tähän tap<PERSON>: {eventTitle}. Lue lisää osoitteesta {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Tapahtuman päivämäärä ja aika", "app.containers.EventsShow.eventFrom2": "Lähettäjä \"{projectTitle}\"", "app.containers.EventsShow.goBack": "<PERSON><PERSON>", "app.containers.EventsShow.goToProject": "<PERSON><PERSON><PERSON><PERSON><PERSON>e", "app.containers.EventsShow.haveRegistered": "ovat <PERSON><PERSON><PERSON>", "app.containers.EventsShow.icsError": "Virhe ICS-tiedoston lataamisessa", "app.containers.EventsShow.linkToOnlineEvent": "<PERSON><PERSON>", "app.containers.EventsShow.locationIconAltText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.EventsShow.metaTitle": "Tapahtuma: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Online-kokous", "app.containers.EventsShow.onlineLinkIconAltText": "Linkki online-kokoukseen", "app.containers.EventsShow.registered": "rekisteröity", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 rekisteröitynyttä} one {1 rekisteröitynyt} other {# rekisteröitynyttä}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} rekisteröitynyttä", "app.containers.EventsShow.registrantsIconAltText": "Rekisteröityjä", "app.containers.EventsShow.socialMediaSharingMessage": "<PERSON><PERSON><PERSON><PERSON> tähän tap<PERSON>: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# osallistuja} other {# osallistuja}}", "app.containers.EventsViewer.allTime": "<PERSON><PERSON> a<PERSON>", "app.containers.EventsViewer.date": "Päivämäärä", "app.containers.EventsViewer.thisMonth2": "<PERSON><PERSON><PERSON>", "app.containers.EventsViewer.thisWeek2": "Tuleva viikko", "app.containers.EventsViewer.today": "Tänää<PERSON>", "app.containers.IdeaButton.addAContribution": "Lisää panos", "app.containers.IdeaButton.addAPetition": "<PERSON><PERSON><PERSON><PERSON>us", "app.containers.IdeaButton.addAProject": "Lisää projekti", "app.containers.IdeaButton.addAProposal": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAQuestion": "Lisää kysymys", "app.containers.IdeaButton.addAnInitiative": "Lisää aloite", "app.containers.IdeaButton.addAnOption": "Lisää vaiht<PERSON>hto", "app.containers.IdeaButton.postingDisabled": "Uusia ehdotuksia ei tällä hetkellä oteta vastaan", "app.containers.IdeaButton.postingInNonActivePhases": "Uusia lähetyksiä voidaan lisätä vain aktiivisissa vaiheissa.", "app.containers.IdeaButton.postingInactive": "Uusia ehdotuksia ei tällä hetkellä oteta vastaan.", "app.containers.IdeaButton.postingLimitedMaxReached": "<PERSON>t jo suorittanut tämän kys<PERSON>. Ki<PERSON>s vastauksestasi!", "app.containers.IdeaButton.postingNoPermission": "Uusia ehdotuksia ei tällä hetkellä oteta vastaan", "app.containers.IdeaButton.postingNotYetPossible": "Uusia esityksiä ei vielä oteta vastaan.", "app.containers.IdeaButton.signInLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.signUpLinkText": "Rekisteröidy", "app.containers.IdeaButton.submitAnIssue": "Lähetä kommentti", "app.containers.IdeaButton.submitYourIdea": "Lähetä ideasi", "app.containers.IdeaButton.takeTheSurvey": "Vastaa k<PERSON>elyyn", "app.containers.IdeaButton.verificationLinkText": "Vahvista henkilöllisyytesi nyt.", "app.containers.IdeaCard.readMore": "<PERSON><PERSON> l<PERSON>", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {ei kommentteja} one {1 kommentti} other {# kommenttia}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {ei ääni<PERSON>} one {1 ääni} other {# ääntä}} / {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "<PERSON><PERSON>", "app.containers.IdeaCards.a11y_totalItems": "Viestejä yhteensä: {ideasCount}", "app.containers.IdeaCards.all": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.allStatuses": "Kaikki tilat", "app.containers.IdeaCards.contributions": "Avustukset", "app.containers.IdeaCards.ideaTerm": "Ideoita", "app.containers.IdeaCards.initiatives": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.issueTerm": "Ko<PERSON>ntit", "app.containers.IdeaCards.list": "Lista", "app.containers.IdeaCards.map": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.mostDiscussed": "Eniten keskusteltu", "app.containers.IdeaCards.newest": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.noFilteredResults": "<PERSON>i tuloksia. Kokeile toista suodatinta tai hakutermiä.", "app.containers.IdeaCards.numberResults": "Tulokset ({postCount})", "app.containers.IdeaCards.oldest": "<PERSON><PERSON>", "app.containers.IdeaCards.optionTerm": "Vaihtoehdot", "app.containers.IdeaCards.petitions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.popular": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.projectFilterTitle": "Projektit", "app.containers.IdeaCards.projectTerm": "Projektit", "app.containers.IdeaCards.proposals": "ehdotuksia", "app.containers.IdeaCards.questionTerm": "Kysymyksiä", "app.containers.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.resetFilters": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.showXResults": "Näytä {ideasCount, plural, one {# tulos} other {# tulosta}}", "app.containers.IdeaCards.sortTitle": "Lajittelu", "app.containers.IdeaCards.statusTitle": "Tila", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.topicsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.trending": "Trendaavat", "app.containers.IdeaCards.tryDifferentFilters": "<PERSON>i tuloksia. Kokeile toista suodatinta tai hakutermiä.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} kommentit} one {{ideasCount} kommentti} other {{ideasCount} kommenttia}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} ideoita} one {{ideasCount} idea} other {{ideasCount} ideaa}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ideoita} one {{ideasCount} idea} other {{ideasCount} ideoita}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} aloitteita} one {{ideasCount} aloite} other {{ideasCount} aloitetta}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} valinnat} one {{ideasCount} vaihtoehto} other {{ideasCount} valinnat}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} vetoomuksia} one {{ideasCount} vetoomus} other {{ideasCount} vetoomuksia}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projektit} one {{ideasCount} projekti} other {{ideasCount} projektit}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} ehdotuks<PERSON>} one {{ideasCount} ehdo<PERSON>} other {{ideasCount} ehdotu<PERSON><PERSON>}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} kys<PERSON><PERSON><PERSON><PERSON>} one {{ideasCount} kysymys} other {{ideasCount} kysymystä}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# tulos} other {# tulosta}}", "app.containers.IdeasEditPage.contributionFormTitle": "Muokkaa panosta", "app.containers.IdeasEditPage.editedPostSave": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.fileUploadError": "<PERSON><PERSON>den tai useamman tiedoston lataaminen epäonnistui. Tarkista tiedoston koko ja muoto ja yritä uudelleen.", "app.containers.IdeasEditPage.formTitle": "<PERSON><PERSON><PERSON><PERSON> ideaa", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Muokkaa viestiäsi. Lisää uusia ja muuta vanhoja tietoja.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Muokkaa {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>", "app.containers.IdeasEditPage.issueFormTitle": "Muokkaa kommenttia", "app.containers.IdeasEditPage.optionFormTitle": "Muokkaa v<PERSON>", "app.containers.IdeasEditPage.petitionFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.projectFormTitle": "Muokkaa projektia", "app.containers.IdeasEditPage.proposalFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.questionFormTitle": "Muokkaa kysymystä", "app.containers.IdeasEditPage.save": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.submitApiError": "Lomakkeen lähettämisessä oli on<PERSON>. Tarkista mahdolliset virheet ja yritä uudelleen.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Kaikki s<PERSON>ötteet lähetetty", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "<PERSON><PERSON><PERSON> ka<PERSON> {orgName}osallistumisalustalle julkaistuihin tuloksiin.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Viestit | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Viestit", "app.containers.IdeasIndexPage.loadMore": "Lataa lisää...", "app.containers.IdeasIndexPage.loading": "Ladataan...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Oletuksena lähetyksesi liitetään profi<PERSON>, el<PERSON> valitse tätä vaihtoehtoa.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Postaa nimettömänä", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Profiilin näkyvyys", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Tämä kysely ei ole tällä hetkellä avoin vastausta varten. Palaa projektiin saadaksesi lisätietoja.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Tämä kysely ei ole tällä hetkellä aktiivinen.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "<PERSON><PERSON><PERSON> projektiin", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "<PERSON>t jo suorittanut tämän k<PERSON>.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Kiitos vastauksestasi!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "<PERSON><PERSON><PERSON> k<PERSON> tulee olla alle {limit} merk<PERSON><PERSON> pitkä", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Idean rungon on oltava yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "<PERSON><PERSON><PERSON> on oltava alle {limit} merk<PERSON><PERSON> pitkä", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "<PERSON><PERSON><PERSON> on oltava yli {limit} merk<PERSON><PERSON> pitkä", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Valitse vähintään yksi sponsori", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Idean kuvauksen tulee olla alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Idean kuvauksen tulee olla yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "<PERSON>", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Idean otsikon on oltava alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Idean otsikon on oltava yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "Aloitteen kuvauksen tulee olla alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "Aloitteen kuva<PERSON>sen tulee olla yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "Aloitteen otsikon on oltava alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "Aloitteen otsikon on oltava yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Ongelman kuva<PERSON>sen tulee olla alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON> on oltava yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "<PERSON><PERSON><PERSON> otsikon on oltava alle {limit} merk<PERSON><PERSON> pitkä", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "Ongelman otsikon on oltava yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_number_required": "<PERSON>ämä kenttä on pakollinen. Anna kelvollinen numero", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Vaiht<PERSON>htokuvauksen tulee olla alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "<PERSON><PERSON><PERSON><PERSON> ku<PERSON><PERSON>sen on oltava yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "<PERSON><PERSON>uksen otsikon on oltava alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "<PERSON><PERSON><PERSON>sen otsikon on oltava yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Valitse vähintään yksi tunniste", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "<PERSON><PERSON>om<PERSON><PERSON> kuvauksen tulee olla alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuvauksen tulee olla yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "Vetoomuksen otsikon on oltava alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "<PERSON>etoomuksen otsikon on oltava yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "<PERSON>je<PERSON><PERSON> kuva<PERSON>sen tulee olla alle {limit} merk<PERSON>ä pitkä", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "<PERSON>je<PERSON><PERSON> ku<PERSON> tulee olla yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Projektin otsikon on oltava alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Projektin otsikon on oltava yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kuva<PERSON>sen tulee olla alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kuva<PERSON>sen tulee olla yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> otsikon on oltava alle {limit} merk<PERSON>ä pitkä", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> otsikon on oltava yli {limit} merk<PERSON>ä pitkä", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Anna numero", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Anna numero", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuva<PERSON>sen tulee olla alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuva<PERSON>sen tulee olla yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> otsikon on oltava alle {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "<PERSON><PERSON><PERSON><PERSON><PERSON> otsikon on oltava yli {limit} merkkiä pitkä", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "<PERSON>", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "<PERSON><PERSON><PERSON> k<PERSON> tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "<PERSON><PERSON><PERSON> k<PERSON> tulee olla vähintään 30 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "<PERSON><PERSON><PERSON> on oltava alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "<PERSON>kstin otsikon on oltava vähintään 10 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Idean kuvauksen tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Idean kuvauksen tulee olla vähintään 30 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "<PERSON>", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Idean otsikon tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Idean otsikon on oltava vähintään 10 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_includes_banned_words": "<PERSON><PERSON> sa<PERSON>anut käyttää yhtä tai useampaa sanaa, joita {guidelinesLink}pitää kirosana. Muuta tekstiäsi poistaaksesi mahdolliset kirosanat.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "Aloitteen kuvauksen tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "Aloitteen kuvauksen tulee olla vähintään 30 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Aloitteen otsikon on oltava alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Aloitteen otsikon on oltava vähintään 10 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Ongelman kuvauksen tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Ongelman kuvauksen on oltava vähintään 30 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Ongelman otsikon on oltava alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Ongelman otsikon on oltava vähintään 10 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Vaihtoehtojen kuvauksen tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Vaihtoehtojen kuvauksen tulee olla vähintään 30 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Vaihtoehdon otsikon tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Vaihtoehdon otsikon on oltava vähintään 10 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "Vetoomuksen kuvauksen tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "Vetoomuksen kuvauksen tulee olla vähintään 30 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Vetoomuksen otsikon tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Vetoomuksen otsikon tulee olla vähintään 10 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Projektin kuvauksen tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Projektin kuvauksen tulee olla vähintään 30 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Projektin otsikon on oltava alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Projektin otsikon on oltava vähintään 10 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "Ehdotuksen kuvauksen tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "Ehdotuksen kuvauksen tulee olla vähintään 30 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Ehdotuksen otsikon on oltava alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Ehdotuksen otsikon on oltava vähintään 10 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "<PERSON>", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Kysymyksen kuvauksen tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Kysymyksen kuvauksen tulee olla vähintään 30 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Kysymyksen otsikon tulee olla alle 80 merkkiä pitkä", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Kysymyksen otsikon on oltava vähintään 10 merkkiä pitkä", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "Peruuta", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON><PERSON><PERSON>, haluan poistua", "app.containers.IdeasNewPage.contributionMetaTitle1": "Lisää uusi panos hankkeeseen | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Muok<PERSON>a kyselyä", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Lähetä palaute ja liity keskusteluun {orgName}:n osallistumisalustalla.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Lisää uusi idea projektiin | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Lisää uusi aloite projektiin | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Lisää uusi numero projektiin | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "<PERSON><PERSON><PERSON>, ett<PERSON> haluat poistua?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "V<PERSON><PERSON><PERSON><PERSON><PERSON> on tallenne<PERSON><PERSON> y<PERSON><PERSON>, ja voit palata täydentämään tätä my<PERSON>.", "app.containers.IdeasNewPage.leaveSurvey": "<PERSON><PERSON><PERSON> k<PERSON>", "app.containers.IdeasNewPage.leaveSurveyText": "Vastauksiasi ei tallenneta.", "app.containers.IdeasNewPage.optionMetaTitle1": "Lisää uusi vaihtoehto projektiin | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Lisää uusi vetoomus projektiin | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Lisää uusi projekti projektiin | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Lisää uusi ehdotus hank<PERSON> | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Lisää uusi kysymys projektiin | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Hyväksy kutsu", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Yhteissponsorointikutsu", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Yhteissponsorit", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Sinut on kutsuttu tukisponsoriksi.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Kutsu hyväksytty", "app.containers.IdeasShow.Cosponsorship.pending": "vireillä", "app.containers.IdeasShow.MetaInformation.attachments": "Li<PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} osoitteessa {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Nyk<PERSON>nen tila", "app.containers.IdeasShow.MetaInformation.location": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.postedBy": "lähettänyt", "app.containers.IdeasShow.MetaInformation.similar": "<PERSON><PERSON><PERSON><PERSON> tulot", "app.containers.IdeasShow.MetaInformation.topics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.commentCTA": "Lisää kommentti", "app.containers.IdeasShow.contributionEmailSharingBody": "Tue tätä panosta '{postTitle}' osoitteessa {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "<PERSON><PERSON> tätä panosta: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Kiitos panoksesi lähettämisestä!", "app.containers.IdeasShow.contributionTwitterMessage": "<PERSON><PERSON> tätä panosta: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "<PERSON><PERSON> tätä panosta: {postTitle}", "app.containers.IdeasShow.currentStatus": "Nyk<PERSON>nen tila", "app.containers.IdeasShow.deletedUser": "t<PERSON><PERSON><PERSON> k<PERSON>", "app.containers.IdeasShow.ideaEmailSharingBody": "<PERSON><PERSON> ideaani{ideaTitle}osoitteessa {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "<PERSON><PERSON> ideaani: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "<PERSON><PERSON> tät<PERSON> ideaa: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "<PERSON><PERSON> tät<PERSON> ideaa: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "<PERSON><PERSON> tätä kommenttia: {postTitle}", "app.containers.IdeasShow.imported": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.importedTooltip": "Tämä {inputTerm} kerät<PERSON>in offline-tilassa ja ladattiin automaattisesti alustalle.", "app.containers.IdeasShow.initiativeEmailSharingBody": "<PERSON><PERSON> tät<PERSON> aloit<PERSON>{ideaTitle}osoitteessa {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "<PERSON><PERSON> t<PERSON> aloitetta: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Kiitos aloitteesi lähettämisestä!", "app.containers.IdeasShow.initiativeTwitterMessage": "<PERSON><PERSON> t<PERSON> aloitetta: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "<PERSON><PERSON> t<PERSON> aloitetta: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Tue tätä kommenttia '{postTitle}' osoitteessa {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "<PERSON><PERSON> tätä kommenttia: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Kiitos kommentistasi!", "app.containers.IdeasShow.issueTwitterMessage": "<PERSON><PERSON> tätä kommenttia: {postTitle}", "app.containers.IdeasShow.metaTitle": "Syöte: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Tue tätä vaihtoehtoa '{postTitle}' osoitteessa {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "<PERSON><PERSON> tät<PERSON> vaihtoehtoa: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on lähetetty onnistuneesti!", "app.containers.IdeasShow.optionTwitterMessage": "<PERSON><PERSON> tät<PERSON> vaihtoehtoa: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "<PERSON><PERSON> tät<PERSON> vaihtoehtoa: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Tue tätä vetoomusta '{ideaTitle}' osoitteessa {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "<PERSON><PERSON> t<PERSON> vetoomusta: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "<PERSON><PERSON><PERSON>, että lähetit vetoomuksen!", "app.containers.IdeasShow.petitionTwitterMessage": "<PERSON><PERSON> t<PERSON>omusta: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "<PERSON><PERSON> t<PERSON>omusta: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Tue tätä projektia '{postTitle}' osoitteessa {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "<PERSON>e tätä projektia: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Ki<PERSON>s, että lähetit projektisi!", "app.containers.IdeasShow.projectTwitterMessage": "<PERSON>e tätä projektia: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "<PERSON>e tätä projektia: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Tue tätä ehdotusta '{ideaTitle}' {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "<PERSON><PERSON> tätä ehdotusta: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "<PERSON><PERSON>s eh<PERSON>esta<PERSON>!", "app.containers.IdeasShow.proposalTwitterMessage": "<PERSON><PERSON> tätä ehdotusta: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "<PERSON><PERSON> tätä ehdotusta: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Äänestysaikaa jäljellä:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} {votingThreshold} vaa<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Peruuta <PERSON>", "app.containers.IdeasShow.proposals.VoteControl.days": "päivää", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "ohjeistamme", "app.containers.IdeasShow.proposals.VoteControl.hours": "tuntia", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status ja äänet", "app.containers.IdeasShow.proposals.VoteControl.minutes": "min", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Lisätietoja", "app.containers.IdeasShow.proposals.VoteControl.vote": "Äänestys", "app.containers.IdeasShow.proposals.VoteControl.voted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.votedText": "<PERSON><PERSON>, kun tämä aloite siirtyy se<PERSON>an vai<PERSON>seen. {x, plural, =0 {{xDays} jäljellä.} one {{xDays} jäljellä.} other {{xDays} jäljellä.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Äänesi on lähetetty!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Valitettavasti et voi äänestää tästä ehdotuksesta. Lue miksi kohdasta {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {alle päivä} one {yksi päivä} other {# päivää}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {ei ääni<PERSON>} one {1 ääni} other {# ääntä}}", "app.containers.IdeasShow.questionEmailSharingBody": "Liity keskusteluun tästä kysymyksestä '{postTitle}' osoitteessa {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Li<PERSON> keskusteluun: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Kysymyksesi on lähetetty onnistuneesti!", "app.containers.IdeasShow.questionTwitterMessage": "Li<PERSON> keskusteluun: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Li<PERSON> keskusteluun: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "<PERSON><PERSON>i haluat ilmoittaa tämän roskapostiksi?", "app.containers.IdeasShow.share": "Jaa", "app.containers.IdeasShow.sharingModalSubtitle": "Tavoita enemmän ihmisiä ja anna <PERSON> kuulu<PERSON>.", "app.containers.IdeasShow.sharingModalTitle": "<PERSON><PERSON>s, että lähetit ideasi!", "app.containers.Navbar.completeOnboarding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Navbar.completeProfile": "Täydellinen profiili", "app.containers.Navbar.confirmEmail2": "Vahvista sähköposti", "app.containers.Navbar.unverified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Navbar.verified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.beforeYouFollow": "<PERSON>nen kuin se<PERSON>at", "app.containers.NewAuthModal.beforeYouParticipate": "<PERSON>nen kuin o<PERSON>ut", "app.containers.NewAuthModal.completeYourProfile": "Täytä profiilisi", "app.containers.NewAuthModal.confirmYourEmail": "vahvista sähköpostiosoitteesi", "app.containers.NewAuthModal.logIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Lue alla olevat ehdot jat<PERSON>aksesi.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Täytä profiilisi.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "<PERSON><PERSON><PERSON> sinulla ole tiliä? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Rekisteröidy", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Koodissa on oltava 4 numeroa.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Jatka FranceConnectilla", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Tällä alustalla ei ole käytössä todennusmenetelmiä.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "Jatkamalla hyväksyt sähköpostien vastaanottamisen tältä alustalta. V<PERSON> valita, mitkä sähköpostit haluat vastaanottaa \"Omat asetuk<PERSON>\" -sivulla.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "Sähköposti", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "<PERSON>öpostiosoite oikeassa muodossa, esimerkiksi <EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "<PERSON>", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "<PERSON>öpostiosoitteesi jatkaaksesi.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "<PERSON><PERSON><PERSON><PERSON>?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON><PERSON> til<PERSON>: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "<PERSON>", "app.containers.NewAuthModal.steps.Password.password": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.Password.rememberMe": "Muista minut", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "<PERSON><PERSON><PERSON>, jos käyt<PERSON> julkista tietokonetta", "app.containers.NewAuthModal.steps.Success.allDone": "Valmista", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Jatka nyt osallistumistasi.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Henkilöllisyytesi on vahvistettu. Olet nyt tämän alustan yhteisön täysjäsen.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "<PERSON>t nyt vahvistettu!", "app.containers.NewAuthModal.steps.close": "kiinni", "app.containers.NewAuthModal.steps.continue": "Jatka", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Mistä olet kiinnostunut?", "app.containers.NewAuthModal.youCantParticipate": "Et voi osallistua", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {ei katsomattomia ilmoituksia} one {1 katsomaton ilmoitus} other {# katsomaton ilmoitus}}", "app.containers.NotificationMenu.adminRightsReceived": "Olet nyt alustan j<PERSON>rjestelmänvalvoja", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Järjestelmänvalvoja on poistanut kommenttisi aiheesta{postTitle}, koska\n      {reasonCode, select, irrelevant {sillä ei ole merkitystä} inappropriate {sen sis<PERSON><PERSON><PERSON> on sopimatonta} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} hyväksyi yhteissponsorointikutsusi", "app.containers.NotificationMenu.deletedUser": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.NotificationMenu.error": "Ilmoitusten la<PERSON> epä<PERSON>", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} kommentoi sisäisesti sinulle määritettyä syötettä", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} kommentoi sisäisesti syötettä, jota kommentoit sisäisesti", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} kommentoi sisäisesti syötteitä hallinnoimassasi projektissa", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} kommentoi sisäisesti määrittämätöntä syötettä hallitsemattomassa projektissa", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} kommentoi sisäistä kommenttiasi", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} k<PERSON>ui sinut tukemaan lahjo<PERSON>a", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} k<PERSON>ui sinut <PERSON> ideaa", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} k<PERSON>ui sinut tukemaan eh<PERSON>", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} k<PERSON>ui sinut <PERSON> on<PERSON>", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} k<PERSON>ui sinut <PERSON> v<PERSON>", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} k<PERSON>ui sinut tukemaan vetoomusta", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} kutsui sinut osallistumaan projektiin", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} k<PERSON>ui sinut <PERSON>", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} k<PERSON>ui sinut <PERSON> kysym<PERSON>tä", "app.containers.NotificationMenu.loadMore": "Lataa lisää...", "app.containers.NotificationMenu.loading": "Ladataan ilmoituk<PERSON>...", "app.containers.NotificationMenu.mentionInComment": "{name} main<PERSON>i sinut kommentissa", "app.containers.NotificationMenu.mentionInInternalComment": "{name} mainitsi sinut sisäisessä kommentissa", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} mainitsi sinut virallisessa p<PERSON>ivityksessä", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Et lähettänyt kyselyäsi", "app.containers.NotificationMenu.noNotifications": "Sinulla ei ole vielä ilmoituksia", "app.containers.NotificationMenu.notificationsLabel": "Ilmoitukset", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} antoi <PERSON><PERSON>en p<PERSON>ksen seuraamastasi tekstistä", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} antoi <PERSON><PERSON>en p<PERSON>ivityksen ideastasi, jota seuraat", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} antoi <PERSON><PERSON>en p<PERSON>ks<PERSON> aloitteesta", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} antoi <PERSON><PERSON>en p<PERSON>ks<PERSON> ongelmasta", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} antoi <PERSON>lisen päivityksen valitsemastasi vaiht<PERSON>dosta", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} antoi <PERSON><PERSON>en p<PERSON>ks<PERSON>", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} antoi <PERSON>lisen päivityksen seuraamastasi projektista", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} antoi <PERSON><PERSON>en p<PERSON>ksen se<PERSON>ama<PERSON> ehdotuksesta", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} antoi <PERSON><PERSON>en p<PERSON>ksen seuraamastasi kysymyksestä", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sinulle", "app.containers.NotificationMenu.projectModerationRightsReceived": "Olet nyt {projectLink}:n projektipäällikkö", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} si<PERSON><PERSON> uuteen vai<PERSON>seen", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} siirtyy uuteen vaiheeseen {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "Uusi projekti julkaistiin", "app.containers.NotificationMenu.projectReviewRequest": "{name} pyysi lupaa julkaista projektin \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} hy<PERSON><PERSON><PERSON><PERSON> \"{projectTitle}\" julkaistavaksi", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} tila on muuttunut arvoon {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} sa<PERSON><PERSON><PERSON>skynnyksen", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} hyväksyi kutsusi", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} kommentoi artikkelia, jota seuraat", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} kommentoi ideaa, jota seuraat", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} kom<PERSON>i al<PERSON>, jota seuraat", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} kom<PERSON>i asiaa, jota seuraat", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} kom<PERSON>i v<PERSON>, jota seuraat", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} kom<PERSON>i <PERSON>omusta, jota seuraat", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} kommentoi projektia, jota seuraat", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} kom<PERSON>i <PERSON>, jota seuraat", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} kom<PERSON>i kysymystä, jota seuraat", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} il<PERSON><PERSON><PERSON> \"{postTitle}\" roskapost<PERSON><PERSON>", "app.containers.NotificationMenu.userReactedToYourComment": "{name} re<PERSON><PERSON> k<PERSON>", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} il<PERSON><PERSON><PERSON> kommentin \"{postTitle}\" roskapostik<PERSON>", "app.containers.NotificationMenu.votingBasketNotSubmitted": "<PERSON>t antanut ä<PERSON>", "app.containers.NotificationMenu.votingBasketSubmitted": "Äänestit onnistuneesti", "app.containers.NotificationMenu.votingLastChance": "Viimeinen mahdollisuus äänestää {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} äänestystulokset paljastettiin", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} m<PERSON><PERSON><PERSON><PERSON> sinulle {postTitle}", "app.containers.PasswordRecovery.emailError": "Tämä ei näytä kelvolliselta sähköpostilta", "app.containers.PasswordRecovery.emailLabel": "Sähköposti", "app.containers.PasswordRecovery.emailPlaceholder": "<PERSON><PERSON>post<PERSON>teen<PERSON>", "app.containers.PasswordRecovery.helmetDescription": "<PERSON><PERSON><PERSON>", "app.containers.PasswordRecovery.helmetTitle": "<PERSON><PERSON><PERSON>", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "<PERSON><PERSON> tä<PERSON> sähköpostiosoite on rekisteröity alustalle, salasanan palautuslink<PERSON> on lähetetty.", "app.containers.PasswordRecovery.resetPassword": "Lähetä salasanan palaut<PERSON>link<PERSON>", "app.containers.PasswordRecovery.submitError": "<PERSON><PERSON> l<PERSON>t tähän sähköpostiin yhdistettyä tiliä. Voit yrittää rekisteröityä sen sijaan.", "app.containers.PasswordRecovery.subtitle": "<PERSON><PERSON> voimme lähettää linkin uuden salasanan valintaa varten?", "app.containers.PasswordRecovery.title": "<PERSON><PERSON><PERSON> nollaus", "app.containers.PasswordReset.helmetDescription": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.helmetTitle": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.login": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordError": "Salasanan tulee olla vähintään 8 merkkiä pitkä", "app.containers.PasswordReset.passwordLabel": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Salasanasi on päivitetty onnistuneesti.", "app.containers.PasswordReset.pleaseLogInMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON> u<PERSON>.", "app.containers.PasswordReset.requestNewPasswordReset": "Pyydä uusi salasana", "app.containers.PasswordReset.submitError": "<PERSON><PERSON> meni pieleen. Yritä uudelleen myö<PERSON>.", "app.containers.PasswordReset.title": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.updatePassword": "Vahvista uusi salasana", "app.containers.ProjectFolderCards.allProjects": "Kaikki projektit", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} työske<PERSON><PERSON> parhai<PERSON>an", "app.containers.ProjectFolderShowPage.editFolder": "Muok<PERSON><PERSON> kansiota", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Tietoa tästä projektista", "app.containers.ProjectFolderShowPage.metaTitle1": "Kansio: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON> l<PERSON>", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON><PERSON>ä<PERSON>", "app.containers.ProjectFolderShowPage.share": "Jaa", "app.containers.Projects.PollForm.document": "Asiakirja", "app.containers.Projects.PollForm.formCompleted": "Kiitos! Vastauksesi on vastaanotettu.", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "<PERSON><PERSON> jo o<PERSON>ut tähän k<PERSON>.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "<PERSON>ä<PERSON><PERSON> kysely voidaan suorittaa vain, kun tämä vaihe on aktiivinen.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Tämä kysely ei ole tällä hetkellä käytössä", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Tällä hetkellä k<PERSON><PERSON><PERSON> on mahdotonta.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Kysely ei ole enää saatavilla, koska tämä projekti ei ole enää aktiivinen.", "app.containers.Projects.PollForm.sendAnswer": "Lähettää", "app.containers.Projects.a11y_phase": "Vaihe {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Vaiheiden yleiskatsaus", "app.containers.Projects.a11y_titleInputs": "Kaik<PERSON> tähän projektiin lähetetty panos", "app.containers.Projects.a11y_titleInputsPhase": "<PERSON><PERSON><PERSON> on lähetetty tässä vaiheessa", "app.containers.Projects.accessRights": "Käyttöoikeudet", "app.containers.Projects.addedToBasket": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.allocateBudget": "<PERSON><PERSON><PERSON> bud<PERSON>", "app.containers.Projects.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.basketSubmitted": "<PERSON><PERSON> on lähete<PERSON>!", "app.containers.Projects.contributions": "Avustukset", "app.containers.Projects.createANewPhase": "<PERSON><PERSON> uusi vaihe", "app.containers.Projects.currentPhase": "Nykyinen vaihe", "app.containers.Projects.document": "Asiakirja", "app.containers.Projects.editProject": "Muokkaa projektia", "app.containers.Projects.emailSharingBody": "Mitä mieltä olet tästä aloitteesta? Äänestä sitä ja jaa keskustelu osoitteessa {initiativeUrl} saadaksesi äänesi kuuluviin!", "app.containers.Projects.emailSharingSubject": "<PERSON><PERSON>: {initiativeTitle}.", "app.containers.Projects.endedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "app.containers.Projects.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.header": "Projektit", "app.containers.Projects.ideas": "Ideat", "app.containers.Projects.information": "<PERSON><PERSON><PERSON>", "app.containers.Projects.initiatives": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Tarkista asiakirja", "app.containers.Projects.invisibleTitlePhaseAbout": "<PERSON>äst<PERSON> vai<PERSON>sta", "app.containers.Projects.invisibleTitlePoll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.invisibleTitleSurvey": "Vastaa k<PERSON>elyyn", "app.containers.Projects.issues": "Ko<PERSON>ntit", "app.containers.Projects.liveDataMessage": "Katselet reaaliaikaista dataa. Osallistujamäärää päivitetään jatkuvasti ylläpitäjille. Huomaa, että tavalliset käyttäjät näkevät välimuistissa olevia tietoja, mikä voi aiheuttaa pieniä eroja luvuissa.", "app.containers.Projects.location": "Sijainti:", "app.containers.Projects.manageBasket": "Muokkaa koria", "app.containers.Projects.meetMinBudgetRequirement": "Täytä vähimmäisbudjetti korin lähettämiseksi.", "app.containers.Projects.meetMinSelectionRequirement": "Täytä vähimmäisbudjetti korin lähettämiseksi.", "app.containers.Projects.metaTitle1": "Projekti: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Vaadittu vähimmäisbudjetti", "app.containers.Projects.myBasket": "<PERSON><PERSON>", "app.containers.Projects.navPoll": "Poll", "app.containers.Projects.navSurvey": "<PERSON><PERSON><PERSON>", "app.containers.Projects.newPhase": "<PERSON><PERSON><PERSON> vaihe", "app.containers.Projects.nextPhase": "<PERSON><PERSON><PERSON> vaihe", "app.containers.Projects.noEndDate": "<PERSON><PERSON> lopet<PERSON>päivä<PERSON>", "app.containers.Projects.noItems": "Et ole vielä valinnut yhtään tuotetta", "app.containers.Projects.noPastEvents": "Ei näytettäviä menneitä tapahtumia", "app.containers.Projects.noPhaseSelected": "Vai<PERSON><PERSON> ei ole valittu", "app.containers.Projects.noUpcomingOrOngoingEvents": "Mitään tulevia tai meneillään olevia tapahtumia ei ole tällä hetkellä suunniteltu.", "app.containers.Projects.offlineVotersTooltip": "Tämä luku ei heijasta offline-äänestysten määrää.", "app.containers.Projects.options": "Vaihtoehdot", "app.containers.Projects.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.participantsTooltip4": "Tämä luku heijastaa myös anonyymejä kyselyjä. Anonyymit kyselyn lähettäminen on ma<PERSON><PERSON><PERSON><PERSON>, jos kyselyt ovat avoimia kaikille (katso tämän projektin välilehti {accessRightsLink} ).", "app.containers.Projects.pastEvents": "<PERSON><PERSON><PERSON> tap<PERSON>", "app.containers.Projects.petitions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.phases": "<PERSON><PERSON><PERSON><PERSON> vai<PERSON>t", "app.containers.Projects.previousPhase": "<PERSON><PERSON><PERSON> v<PERSON>", "app.containers.Projects.project": "Projekti", "app.containers.Projects.projectTwitterMessage": "Saa äänesi kuuluviin! Osallistu {projectName} | {orgName}", "app.containers.Projects.projects": "Projektit", "app.containers.Projects.proposals": "ehdotuksia", "app.containers.Projects.questions": "Kysymyksiä", "app.containers.Projects.readLess": "<PERSON><PERSON> v<PERSON>hem<PERSON>än", "app.containers.Projects.readMore": "<PERSON><PERSON> l<PERSON>", "app.containers.Projects.removeItem": "Poista esine", "app.containers.Projects.requiredSelection": "<PERSON><PERSON><PERSON> valinta", "app.containers.Projects.reviewDocument": "Tarkista asiakirja", "app.containers.Projects.seeTheContributions": "<PERSON><PERSON>", "app.containers.Projects.seeTheIdeas": "<PERSON><PERSON>", "app.containers.Projects.seeTheInitiatives": "<PERSON><PERSON>", "app.containers.Projects.seeTheIssues": "Kat<PERSON> kommentit", "app.containers.Projects.seeTheOptions": "<PERSON><PERSON>", "app.containers.Projects.seeThePetitions": "<PERSON><PERSON>", "app.containers.Projects.seeTheProjects": "Katso projektit", "app.containers.Projects.seeTheProposals": "<PERSON><PERSON>", "app.containers.Projects.seeTheQuestions": "<PERSON><PERSON>", "app.containers.Projects.seeUpcomingEvents": "Katso tulevat tapahtumat", "app.containers.Projects.share": "Jaa", "app.containers.Projects.shareThisProject": "Jaa tämä projekti", "app.containers.Projects.submitMyBasket": "Lähetä kori", "app.containers.Projects.survey": "<PERSON><PERSON><PERSON>", "app.containers.Projects.takeThePoll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.takeTheSurvey": "Vastaa k<PERSON>elyyn", "app.containers.Projects.timeline": "<PERSON><PERSON><PERSON>", "app.containers.Projects.upcomingAndOngoingEvents": "Tulevat tapahtumat", "app.containers.Projects.upcomingEvents": "Tulevat tapahtumat", "app.containers.Projects.whatsAppMessage": "{projectName} | {orgName}osallistumisalustalta", "app.containers.Projects.yourBudget": "Kokonais<PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsIndexPage.metaDescription": "<PERSON><PERSON><PERSON> ka<PERSON> {orgName} käynnissä oleviin projekteihin ymmärtä<PERSON><PERSON><PERSON>, kuinka voit osallistua.\n Tule keskustelemaan sinulle tärkeimmistä paikallisista projekteista.", "app.containers.ProjectsIndexPage.metaTitle1": "Projektit | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projektit", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "<PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "<PERSON> hyvä {signInLink} tai {signUpLink} ensin, jotta voit osallistua vapaaehtoiseksi tähän aktiviteettiin", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Osallistuminen tähän aktiviteettiin ei ole tällä hetkellä avointa.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "rekisteröidy", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Peruutan tar<PERSON><PERSON><PERSON> v<PERSON>i", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {ei osallistujia} one {# osallistuja} other {# osallistujaa}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Varoitus: Upotetussa kyselyssä voi olla esteettömyysongelmia näytönlukulaitteiden käyttäjille. <PERSON><PERSON> koh<PERSON> ha<PERSON>eit<PERSON>, ota yhteyttä alustan järjestelmänvalvojaan saadaksesi linkin kyselyyn alkuperäiseltä alustalta. Vaihtoehtoisesti voit pyytää muita tapoja täyttää kysely.", "app.containers.ProjectsShowPage.process.survey.survey": "<PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "<PERSON><PERSON>, v<PERSON><PERSON> o<PERSON> tä<PERSON>, si<PERSON><PERSON> ensin alustalle {logInLink} .", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "<PERSON>ä<PERSON><PERSON> kysely voidaan suorittaa vain, kun tämä aikajanan vaihe on aktiivinen.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Ole hyvä ja {completeRegistrationLink} vastaa kyselyyn.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Tämä kysely ei ole tällä hetkellä käytössä", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "<PERSON><PERSON><PERSON><PERSON>n edellyttää henkilöllisyytesi vahvistamista. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "Kysely ei ole enää saatavilla, koska tämä projekti ei ole enää aktiivinen.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "täydellinen rekisteröinti", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "rekisteröidy", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Vahvista tilisi nyt.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Vain tietyt käyttäjät voivat tarkastella tätä asiakirjaa. Ole hyvä ja {signUpLink} tai {logInLink} ensin.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "<PERSON><PERSON>m<PERSON> asia<PERSON><PERSON><PERSON> voidaan tarkastella vain, kun tämä vaihe on aktiivinen.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Tarkista asiakirja {completeRegistrationLink} .", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Valitettavasti sinulla ei ole oikeutta tarkistaa tätä asiakirjaa.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "<PERSON><PERSON><PERSON>ä<PERSON> asia<PERSON>jan tark<PERSON>minen edellyttää tilisi vahvistamista. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Asiakirja ei ole enää saatavilla, koska tämä projekti ei ole enää aktiivinen.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(sis. 1 offline-tilassa)} other {(sis. # offline-tilassa)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 poiminta} other {# poiminta}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "<PERSON><PERSON><PERSON><PERSON><PERSON> vaiht<PERSON>don valinneiden osallistujien prosentti<PERSON>u<PERSON>.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> vaiht<PERSON>don saamien äänien prosenttiosuus.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Kustannus:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Näytä lisää", "app.containers.ReactionControl.a11y_likesDislikes": "Tykkäyksiä yhteensä: {likesCount}, ei-tykkäyksiä yhteensä: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Peruutit ei-tykkäyksesi tälle s<PERSON>ötteelle.", "app.containers.ReactionControl.cancelLikeSuccess": "Peruutit tykkäyksen tälle s<PERSON>ötteelle.", "app.containers.ReactionControl.dislikeSuccess": "Et pitänyt tästä s<PERSON>ötteestä onnistuneesti.", "app.containers.ReactionControl.likeSuccess": "Tykkäsit tästä syötteestä onnistuneesti.", "app.containers.ReactionControl.reactionErrorSubTitle": "Reaktiota ei voitu rekisteröidä virheen vuoksi. Yritä uudelleen muutaman minuutin kuluttua.", "app.containers.ReactionControl.reactionSuccessTitle": "Reaktiosi rekisteröitiin onnistuneesti!", "app.containers.ReactionControl.vote": "Äänestys", "app.containers.ReactionControl.voted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SearchInput.a11y_cancelledPostingComment": "<PERSON><PERSON><PERSON>.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} kommentit on ladattu.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# tapahtumaa on ladattu} one {# tapahtuma on ladattu} other {# tapahtumaa on ladattu}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# tulokset on ladattu} one {# tulos on ladattu} other {# tulokset on ladattu}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# hakutulos on ladattu} one {# hakutulos on ladattu} other {# hakutulos on ladattu}}.", "app.containers.SearchInput.removeSearchTerm": "<PERSON><PERSON> haku<PERSON>", "app.containers.SearchInput.searchAriaLabel": "Hae", "app.containers.SearchInput.searchLabel": "Hae", "app.containers.SearchInput.searchPlaceholder": "Hae", "app.containers.SearchInput.searchTerm": "<PERSON><PERSON><PERSON><PERSON>: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect on Ranskan valtion ehdottama ratkaisu yli 700 verkkopalveluun liittymisen turvaamiseksi ja yksinkertaistamiseksi.", "app.containers.SignIn.or": "Tai", "app.containers.SignIn.signInError": "<PERSON><PERSON><PERSON> tiedot eivät pidä paikkaansa. <PERSON><PERSON><PERSON>a <PERSON>ohdit<PERSON> salas<PERSON>? nollataksesi salas<PERSON>.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Käytä FranceConnectia kirjautuaksesi si<PERSON>n, rekisteröityäksesi tai vahvistaaksesi tilisi.", "app.containers.SignIn.whatIsFranceConnect": "<PERSON><PERSON><PERSON> on France Connect?", "app.containers.SignUp.adminOptions2": "Järjestelmänvalvojille ja projektipäälliköille", "app.containers.SignUp.backToSignUpOptions": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.continue": "Jatka", "app.containers.SignUp.emailConsent": "Rekisteröitymällä hyväksyt sähköpostien vastaanottamisen tältä alustalta. V<PERSON> valita, mitkä sähköpostit haluat vastaanottaa \"Omat asetukset\" -sivulla.", "app.containers.SignUp.emptyFirstNameError": "<PERSON>", "app.containers.SignUp.emptyLastNameError": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.firstNamesLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.goToLogIn": "Onko sinulla jo tili? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "<PERSON>n lukenut ja hyv<PERSON><PERSON><PERSON> {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "<PERSON>n lukenut ja hyv<PERSON><PERSON><PERSON> {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, että tietoja käytetään osoitteessa mitgestalten.wien.gv.at. Lisätietoja löytyy {link}.", "app.containers.SignUp.invitationErrorText": "<PERSON><PERSON><PERSON> on van<PERSON><PERSON>ut tai sitä on jo <PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON> o<PERSON> jo käyttänyt kutsulinkkiä tilin luomiseen, yrit<PERSON> kirjautua sis<PERSON>n. <PERSON><PERSON>a tapauksessa rekisteröidy luodaksesi uusi tili.", "app.containers.SignUp.lastNameLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "<PERSON><PERSON><PERSON><PERSON>i saada<PERSON> il<PERSON> niist<PERSON>:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "<PERSON><PERSON><PERSON> su<PERSON>heitas<PERSON> saadaks<PERSON> il<PERSON> niist<PERSON>:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "<PERSON><PERSON> nyt", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Hyväksy tietosuojakäytäntömme jatkaaksesi", "app.containers.SignUp.signUp2": "Rekisteröidy", "app.containers.SignUp.skip": "<PERSON><PERSON> täm<PERSON> vaihe", "app.containers.SignUp.tacError": "Hyväksy k<PERSON>öehtomme j<PERSON>esi", "app.containers.SignUp.thePrivacyPolicy": "tietosuojakäytäntö", "app.containers.SignUp.theTermsAndConditions": "ehdot", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {<PERSON><PERSON><PERSON><PERSON><PERSON> siltä, että yritit rekisteröityä aiemmin suorittamatta prosessia loppuun. Na<PERSON><PERSON>a sen sijaan Kirjaudu sisään käyttämällä edellisen yrityksen aikana valittuja tunnistetietoja.} other {Jotain meni pieleen. Yritä uudelleen myöhemmin.}}", "app.containers.SignUp.viennaConsentEmail": "Sähköpostiosoite", "app.containers.SignUp.viennaConsentFirstName": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.viennaConsentFooter": "Voit muuttaa profiilitietojasi kirjautumi<PERSON> jälk<PERSON>. <PERSON><PERSON> on jo tili samalla sähköpostiosoitteella osoitteessa mitgestalten.wien.gv.at, se linkitetään nykyiseen tiliisi.", "app.containers.SignUp.viennaConsentHeader": "<PERSON><PERSON><PERSON><PERSON> tiedot lähetetään:", "app.containers.SignUp.viennaConsentLastName": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.viennaConsentUserName": "Käyttäjänimi", "app.containers.SignUp.viennaDataProtection": "<PERSON><PERSON>ytäntö", "app.containers.SiteMap.contributions": "Avustukset", "app.containers.SiteMap.cookiePolicyLinkTitle": "Evästeet", "app.containers.SiteMap.issues": "Ko<PERSON>ntit", "app.containers.SiteMap.options": "Vaihtoehdot", "app.containers.SiteMap.projects": "Projektit", "app.containers.SiteMap.questions": "Kysymyksiä", "app.containers.SpamReport.buttonSave": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSuccess": "<PERSON><PERSON><PERSON> tall<PERSON>", "app.containers.SpamReport.inappropriate": "Se on sopimatonta tai loukkaavaa", "app.containers.SpamReport.messageError": "Lomakkeen lähettämisessä tapahtui virhe. Yritä uudelleen.", "app.containers.SpamReport.messageSuccess": "<PERSON><PERSON><PERSON><PERSON> on lähetetty", "app.containers.SpamReport.other": "<PERSON><PERSON> s<PERSON>y", "app.containers.SpamReport.otherReasonPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.wrong_content": "Tämä ei o<PERSON>tia", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Poista profiilikuva", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Äänestyksenne ehdotuksista, jotka ovat vielä äänestettävänä, poiste<PERSON>an. Äänestystä ehdotuksista, joissa <PERSON>ty<PERSON> on päättynyt, ei poisteta.", "app.containers.UsersEditPage.addPassword": "Lis<PERSON><PERSON> sa<PERSON>", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Osallistua varmennusta vaativiin proje<PERSON>.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Vahvista henkilöllisyytesi", "app.containers.UsersEditPage.bio": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Et voi muokata tätä kenttää, koska se sisältää vahvistettuja tietoja.", "app.containers.UsersEditPage.buttonSuccessLabel": "<PERSON><PERSON><PERSON> tall<PERSON>", "app.containers.UsersEditPage.cancel": "Peruuta", "app.containers.UsersEditPage.changeEmail": "Vaihda sähköposti", "app.containers.UsersEditPage.changePassword2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Päivitä vahvistuksesi napsauttamalla tätä.", "app.containers.UsersEditPage.conditionsLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.contactUs": "Toinen syy lähteä? {feedbackLink} ja ehkä voimme auttaa.", "app.containers.UsersEditPage.deleteAccountSubtext": "<PERSON><PERSON> p<PERSON>, että poistut.", "app.containers.UsersEditPage.deleteMyAccount": "Poista minun tilini", "app.containers.UsersEditPage.deleteYourAccount": "Poista tilisi", "app.containers.UsersEditPage.deletionSection": "Poista tilisi", "app.containers.UsersEditPage.deletionSubtitle": "Tätä toimintoa ei voi kumota. Alustalla julkaisemasi sisältö anonymisoidaan. <PERSON><PERSON> haluat poistaa kaiken si<PERSON><PERSON><PERSON><PERSON>, voit ottaa meihin yhteyttä osoitteeseen <EMAIL>.", "app.containers.UsersEditPage.email": "Sähköposti", "app.containers.UsersEditPage.emailEmptyError": "<PERSON>", "app.containers.UsersEditPage.emailInvalidError": "<PERSON>öpostiosoite oikeassa muodossa, esimerkiksi <EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "<PERSON><PERSON> me<PERSON>", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.firstNamesEmptyError": "<PERSON>", "app.containers.UsersEditPage.h1": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.h1sub": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.image": "Avatar-kuva", "app.containers.UsersEditPage.imageDropzonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> pro<PERSON> (max. 5 Mt)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "<PERSON><PERSON><PERSON> pro<PERSON>", "app.containers.UsersEditPage.language": "<PERSON><PERSON>", "app.containers.UsersEditPage.lastName": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.lastNameEmptyError": "<PERSON>", "app.containers.UsersEditPage.loading": "Ladataan...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Voit vaihtaa sähköpostiosoitteesi tai salasanasi täällä.", "app.containers.UsersEditPage.loginCredentialsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.messageError": "Em<PERSON> voineet tallentaa profiiliasi. Yritä myöhemmin uudelleen tai ota yhteyttä <EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "<PERSON><PERSON><PERSON><PERSON> on tallennettu.", "app.containers.UsersEditPage.metaDescription": "<PERSON><PERSON><PERSON>ä on profi<PERSON>n asetussivu {firstName} {lastName} online-osallistumisalustalla {tenantName}. Täällä voit vahvistaa henkilöllisyytesi, muokata tilitietojasi, poistaa tilisi ja muokata sähköpostiasetuksiasi.", "app.containers.UsersEditPage.metaTitle1": "<PERSON><PERSON><PERSON> asetussivu {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "<PERSON>n napsautat tätä painiketta, emme voi enää palauttaa tiliäsi.", "app.containers.UsersEditPage.noNameWarning2": "<PERSON><PERSON>i näkyy tällä hetkellä alustalla muodossa: \"{displayName}\", koska et ole kirjoittanut nimeäsi. Tämä on automaattisesti luotu nimi. <PERSON><PERSON> haluat muuttaa sitä, kirjoita nimesi alle.", "app.containers.UsersEditPage.notificationsSubTitle": "Millaisia sähköposti-ilmoituksia haluat saada? ", "app.containers.UsersEditPage.notificationsTitle": "Sähköposti-ilmoitukset", "app.containers.UsersEditPage.password": "Valitse uusi salasana", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "<PERSON>, joka on vähintään {minimumPasswordLength} merkkiä pitkä", "app.containers.UsersEditPage.passwordAddSection": "Lis<PERSON><PERSON> sa<PERSON>", "app.containers.UsersEditPage.passwordAddSubtitle2": "Aseta salasana ja kirjaudu helposti al<PERSON>, että sinun tarvitsee vahvistaa sähköpostiosoitettasi joka kerta.", "app.containers.UsersEditPage.passwordChangeSection": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.passwordChangeSubtitle": "Vahvista nykyinen salasanasi ja vaihda uuteen salasana<PERSON>.", "app.containers.UsersEditPage.privacyReasons": "<PERSON><PERSON> <PERSON><PERSON>, voit lukea {conditionsLink}.", "app.containers.UsersEditPage.processing": "Lähetetään...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Etunimi vaaditaan sukunimeä annettaessa", "app.containers.UsersEditPage.reasonsToStayListTitle": "Ennen kuin menet...", "app.containers.UsersEditPage.submit": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.tooManyEmails": "Saatko liian monta sähköpostia? Voit hallita sähköpostiasetuksiasi profiiliasetuksissa.", "app.containers.UsersEditPage.updateverification": "<PERSON>utt<PERSON>vat<PERSON> viralliset tietosi? {reverifyButton}", "app.containers.UsersEditPage.user": "<PERSON>oin haluat meidän lähettävän sinulle ilmoituksen sähköpostitse?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Voit osallistua todentamista vaativiin proje<PERSON>.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Sinut on vahvistettu", "app.containers.UsersEditPage.verifyNow": "Vahvista nyt", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "<PERSON><PERSON><PERSON> (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {ei tykkää} one {1 tykkäys} other {# tykkää}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "<PERSON><PERSON><PERSON><PERSON>, että tämä kommentti lähetettiin vast<PERSON>:", "app.containers.UsersShowPage.areas": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "Kommentit ({commentsCount})", "app.containers.UsersShowPage.editProfile": "<PERSON><PERSON><PERSON><PERSON> pro<PERSON>", "app.containers.UsersShowPage.emptyInfoText": "Et seuraa mitään yllä määritetyn suodattimen kohteita.", "app.containers.UsersShowPage.eventsWithCount": "Tapahtumat ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "<PERSON><PERSON><PERSON><PERSON> ({followingCount})", "app.containers.UsersShowPage.inputs": "Toiminnat", "app.containers.UsersShowPage.invisibleTitlePostsList": "<PERSON><PERSON><PERSON> tämän o<PERSON>listujan lähettämät tiedot", "app.containers.UsersShowPage.invisibleTitleUserComments": "Kaikki tämän osallistujan lähettämät kommentit", "app.containers.UsersShowPage.loadMore": "Lataa lisää", "app.containers.UsersShowPage.loadMoreComments": "Lataa lisää kommentteja", "app.containers.UsersShowPage.loadingComments": "Ladataan kommentteja...", "app.containers.UsersShowPage.loadingEvents": "Ladataan tapah<PERSON>...", "app.containers.UsersShowPage.memberSince": "<PERSON><PERSON><PERSON> vuodes<PERSON> {date}", "app.containers.UsersShowPage.metaTitle1": "Profiilisivu {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Tämä henkilö ei ole vielä lähettänyt yhtään kommenttia.", "app.containers.UsersShowPage.noCommentsForYou": "Täällä ei ole vielä kommentteja.", "app.containers.UsersShowPage.noEventsForUser": "Et ole vielä osallistunut tap<PERSON>iin.", "app.containers.UsersShowPage.postsWithCount": "Lähetykset ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Projektikansiot", "app.containers.UsersShowPage.projects": "Projektit", "app.containers.UsersShowPage.proposals": "ehdotuksia", "app.containers.UsersShowPage.seePost": "<PERSON><PERSON>", "app.containers.UsersShowPage.surveyResponses": "Vastaukset ({responses})", "app.containers.UsersShowPage.topics": "Aiheet", "app.containers.UsersShowPage.tryAgain": "<PERSON><PERSON><PERSON><PERSON> virhe, y<PERSON><PERSON>.", "app.containers.UsersShowPage.userShowPageMetaDescription": "<PERSON><PERSON><PERSON><PERSON> on {firstName} {lastName} profiilisivu verkkoosallistumisalustalla {orgName}. <PERSON><PERSON><PERSON><PERSON> on yleiskatsaus heidän pan<PERSON>.", "app.containers.VoteControl.close": "kiinni", "app.containers.VoteControl.voteErrorTitle": "<PERSON><PERSON> meni pieleen", "app.containers.admin.ContentBuilder.default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageTextCards": "<PERSON><PERSON>- ja te<PERSON><PERSON>", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info & harmonikka", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 sarake", "app.containers.admin.ContentBuilder.projectDescription": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.app.navbar.admin": "<PERSON><PERSON><PERSON> al<PERSON>aa", "app.containers.app.navbar.allProjects": "Kaikki projektit", "app.containers.app.navbar.ariaLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.closeMobileNavMenu": "<PERSON>je mob<PERSON>gointivalik<PERSON>", "app.containers.app.navbar.editProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.fullMobileNavigation": "<PERSON><PERSON><PERSON><PERSON> mobiili", "app.containers.app.navbar.logIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.logoImgAltText": "{orgName} <PERSON><PERSON>", "app.containers.app.navbar.myProfile": "<PERSON><PERSON>", "app.containers.app.navbar.search": "Hae", "app.containers.app.navbar.showFullMenu": "Näytä koko valikko", "app.containers.app.navbar.signOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.errorWhenFetchingEvents": "Tapahtumia ladattaessa tapahtui virhe. Yritä ladata sivu uudelleen.", "app.containers.eventspage.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.eventsPageDescription": "Näytä kaikki tap<PERSON>, jotka on julkaistu käyttäjän {orgName}alustalla.", "app.containers.eventspage.eventsPageTitle1": "Tapahtumat | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projektit", "app.containers.eventspage.noPastEvents": "Ei näytettäviä menneitä tapahtumia", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Mitään tulevia tai meneillään olevia tapahtumia ei ole tällä hetkellä suunniteltu.", "app.containers.eventspage.pastEvents": "<PERSON><PERSON><PERSON> tap<PERSON>", "app.containers.eventspage.upcomingAndOngoingEvents": "Tulevat tapahtumat", "app.containers.footer.accessibility-statement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.ariaLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.cookie-policy": "Evästekäytäntö", "app.containers.footer.cookieSettings": "Evästeasetukset", "app.containers.footer.feedbackEmptyError": "Palautekenttä ei voi olla tyhjä.", "app.containers.footer.poweredBy": "Yhteistyössä", "app.containers.footer.privacy-policy": "Tietosuojakäytäntö", "app.containers.footer.siteMap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.terms-and-conditions": "Käyttöehdot", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "<PERSON>utt<PERSON>", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON><PERSON><PERSON>, haluan poistua", "app.containers.ideaHeading.editForm": "Muokkaa lo<PERSON>ta", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "<PERSON><PERSON><PERSON>, ett<PERSON> haluat poistua?", "app.containers.ideaHeading.leaveIdeaForm": "Jätä <PERSON>", "app.containers.ideaHeading.leaveIdeaText": "Vastauksiasi ei tallenneta.", "app.containers.landing.cityProjects": "Projektit", "app.containers.landing.completeProfile": "Täytä profiilisi", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {firstName}. On aika täydentää profiilisi.", "app.containers.landing.createAccount": "Rekisteröidy", "app.containers.landing.defaultSignedInMessage": "{orgName} kuuntelee sinua. On sinun vuorosi saada äänesi kuuluviin!", "app.containers.landing.doItLater": "Teen sen my<PERSON><PERSON><PERSON>", "app.containers.landing.new": "<PERSON>us<PERSON>", "app.containers.landing.subtitleCity": "Tervetuloa {orgName}osallistumisalustaan", "app.containers.landing.titleCity": "Muodostetaan {orgName} :n tulevaisuus yhdessä", "app.containers.landing.twitterMessage": "Äänestä {ideaTitle} päällä", "app.containers.landing.upcomingEventsWidgetTitle": "Tulevat tapahtumat", "app.containers.landing.userDeletedSubtitle": "Voit luoda uuden tilin milloin tahansa tai {contactLink} kertoa meille, mitä voimme parantaa.", "app.containers.landing.userDeletedSubtitleLinkText": "laita meille viesti", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "<PERSON><PERSON><PERSON> on poistettu.", "app.containers.landing.userDeletionFailed": "Tiliäsi poistamisessa tapahtui virhe. Olemme sa<PERSON>et il<PERSON> ongelmasta ja teemme parhaamme korjataksemme sen. <PERSON><PERSON><PERSON> uudelleen my<PERSON>.", "app.containers.landing.verifyNow": "Vahvista nyt", "app.containers.landing.verifyYourIdentity": "Vahvista henkilöllisyytesi", "app.containers.landing.viewAllEventsText": "<PERSON><PERSON> ka<PERSON>ki tap<PERSON>", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "<PERSON><PERSON><PERSON> kansioon", "app.errors.after_end_at": "Aloituspäivä on päättymispäivän jälkeen", "app.errors.avatar_carrierwave_download_error": "Avatar-<PERSON><PERSON><PERSON> ei voitu ladata.", "app.errors.avatar_carrierwave_integrity_error": "Avatar-<PERSON><PERSON>o ei ole sallittua tyyppiä.", "app.errors.avatar_carrierwave_processing_error": "Avataria ei voitu k<PERSON>.", "app.errors.avatar_extension_blacklist_error": "Avatar-k<PERSON><PERSON>äät<PERSON> ei ole sallittu. Sallitut la<PERSON>ennukset ovat: jpg, jpeg, gif ja png.", "app.errors.avatar_extension_whitelist_error": "Avatar-k<PERSON><PERSON>äät<PERSON> ei ole sallittu. Sallitut la<PERSON>ennukset ovat: jpg, jpeg, gif ja png.", "app.errors.banner_cta_button_multiloc_blank": "<PERSON><PERSON><PERSON><PERSON>.", "app.errors.banner_cta_button_url_blank": "<PERSON><PERSON><PERSON><PERSON>.", "app.errors.banner_cta_button_url_url": "<PERSON> k<PERSON>vo<PERSON>en linkki. Varmista, että linkin alussa on \"https://\".", "app.errors.banner_cta_signed_in_text_multiloc_blank": "<PERSON><PERSON><PERSON><PERSON>.", "app.errors.banner_cta_signed_in_url_blank": "<PERSON><PERSON><PERSON><PERSON>.", "app.errors.banner_cta_signed_in_url_url": "<PERSON> k<PERSON>vo<PERSON>en linkki. Varmista, että linkin alussa on \"https://\".", "app.errors.banner_cta_signed_out_text_multiloc_blank": "<PERSON><PERSON><PERSON><PERSON>.", "app.errors.banner_cta_signed_out_url_blank": "<PERSON><PERSON><PERSON><PERSON>.", "app.errors.banner_cta_signed_out_url_url": "<PERSON> k<PERSON>vo<PERSON>en linkki. Varmista, että linkin alussa on \"https://\".", "app.errors.base_includes_banned_words": "<PERSON><PERSON> sa<PERSON>t käyttää yhtä tai useampaa sanaa, jota pidetään kirosanoina. Muokkaa tekstiäsi poistaaksesi mahdo<PERSON>et kirosanat.", "app.errors.body_multiloc_includes_banned_words": "<PERSON><PERSON>us sisältää sanoja, joita p<PERSON><PERSON><PERSON>n sopi<PERSON>.", "app.errors.bulk_import_idea_not_valid": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> idea ei kelpaa: {value}.", "app.errors.bulk_import_image_url_not_valid": "<PERSON>vaa ei voitu ladata osoitteesta {value}. Varmista, että URL-osoite on kelvollinen ja että sen lopussa on tiedostopääte, kuten .png tai .jpg. Tämä ongelma ilmenee rivillä, jonka tunnus on {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "<PERSON><PERSON><PERSON> si<PERSON><PERSON>, josta puuttuu koor<PERSON>atti kohdassa {value}. Tämä ongelma ilmenee rivillä, jonka tunnus on {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Idean sijainti ei-numeerisella koordinaatilla {value}. Tämä ongelma ilmenee rivillä, jonka tunnus on {row}.", "app.errors.bulk_import_malformed_pdf": "Ladattu PDF-tiedosto näyttää olevan virheellinen. Yritä viedä PDF uudelleen lähteestäsi ja ladata sitten uudelleen.", "app.errors.bulk_import_maximum_ideas_exceeded": "Ideoiden enimmäismäärä {value} on ylitetty.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "PDF-tiedoston enimmäismäärä {value} sivua on ylitetty.", "app.errors.bulk_import_not_enough_pdf_pages": "Ladatussa PDF-tiedostossa ei ole tarpeeksi sivuja - siinä tulee olla vähintään yhtä monta sivua kuin ladatussa mallissa.", "app.errors.bulk_import_publication_date_invalid_format": "<PERSON><PERSON>, jonka jul<PERSON>supäivämäär<PERSON> on virheellinen \"{value}\". Käytä muotoa \"PP-KK-VVVV\".", "app.errors.cannot_contain_ideas": "Valitsemasi osallistumistapa ei tue tämän tyyppistä postausta. Muokkaa valintaasi ja yritä uudelleen.", "app.errors.cant_change_after_first_response": "Et voi enää muuttaa tätä, koska jotkut käyttäjät ovat jo vastanneet", "app.errors.category_name_taken": "Tä<PERSON>ä<PERSON><PERSON><PERSON> luokka on jo olemassa", "app.errors.confirmation_code_expired": "<PERSON><PERSON><PERSON>. Pyydä uusi koodi.", "app.errors.confirmation_code_invalid": "Virheellinen vahvistuskoodi. Tarkista sähköpostistasi oikea koodi tai kokeile \"Lähetä uusi koodi\"", "app.errors.confirmation_code_too_many_resets": "Olet lähettänyt vahvistuskoodin uudelleen liian monta kertaa. Ota meihin yhteyttä saadaksesi kutsukoodin.", "app.errors.confirmation_code_too_many_retries": "<PERSON><PERSON> yrittä<PERSON>t liian monta kertaa. Pyydä uusi koodi tai yritä vaihtaa sähköpostiosoitteesi.", "app.errors.email_already_active": "Riviltä {row} löytynyt sähköpostiosoite {value} kuuluu jo rekisteröityneelle osallistujalle", "app.errors.email_already_invited": "Sähköpostiosoite {value} löytyi riviltä {row} on jo kutsuttu", "app.errors.email_blank": "Tä<PERSON>ä ei voi olla tyhjä", "app.errors.email_domain_blacklisted": "Käytä rekisteröitymiseen toista sähköpostiosoitetta.", "app.errors.email_invalid": "Käytä kelvollista sähköpostiosoitetta.", "app.errors.email_taken": "Tällä sähköpostiosoitteella on jo tili. Voit kirjautua sisään sen sijaan.", "app.errors.email_taken_by_invite": "{value} on jo varattu odotta<PERSON> kutsuun. Tarkista roskapostikansiosi tai ota yhteyttä {supportEmail} , jos et löydä sitä.", "app.errors.emails_duplicate": "Yksi tai useampi päällekkäinen arvo sähköpostiosoitteelle {value} löytyi seuraavilta riveiltä: {rows}", "app.errors.extension_whitelist_error": "Ladattavan tiedoston muotoa ei tueta.", "app.errors.file_extension_whitelist_error": "Ladattavan tiedoston muotoa ei tueta.", "app.errors.first_name_blank": "Tä<PERSON>ä ei voi olla tyhjä", "app.errors.generics.blank": "Tämä ei voi olla tyhjä.", "app.errors.generics.invalid": "Tämä ei näytä kelvolliselta arvolta", "app.errors.generics.taken": "Tämä sähköpostiosoite on jo olemassa. <PERSON><PERSON> til<PERSON> on y<PERSON>ste<PERSON> si<PERSON>en.", "app.errors.generics.unsupported_locales": "Tä<PERSON>ä kenttä ei tue nykyistä aluetta.", "app.errors.group_ids_unauthorized_choice_moderator": "Projektipäällikkönä voit lähettää sähköpostia vain henkilöille, jotka voivat käyttää projektejasi", "app.errors.has_other_overlapping_phases": "Projekteissa ei voi olla päällekkäisiä vaiheita.", "app.errors.invalid_email": "Riviltä {row} löytynyt sähköposti {value} ei ole kelvollinen sähköpostiosoite", "app.errors.invalid_row": "<PERSON><PERSON><PERSON><PERSON> tunt<PERSON><PERSON> virhe, kun yrite<PERSON>in käsitellä riviä {row}", "app.errors.is_not_timeline_project": "Nyk<PERSON>nen hanke ei tue vaiheita.", "app.errors.key_invalid": "Avain voi sisältää vain kirja<PERSON>, numeroita ja alaviivoja (_)", "app.errors.last_name_blank": "Tä<PERSON>ä ei voi olla tyhjä", "app.errors.locale_blank": "Valitse kieli", "app.errors.locale_inclusion": "Valitse tuettu kieli", "app.errors.malformed_admin_value": "Riviltä {row} löytynyt järjestelmänvalvojan arvo {value} ei kelpaa", "app.errors.malformed_groups_value": "Riviltä {row} l<PERSON><PERSON><PERSON>yt ryhmä {value} ei ole kelvollinen ryhmä", "app.errors.max_invites_limit_exceeded1": "Kutsujen määrä ylittää 1000:n rajan.", "app.errors.maximum_attendees_greater_than1": "Rekisteröityneiden enimmäismäärän on oltava suurempi kuin 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Rekisteröityneiden enimmäismäärän on oltava suurempi tai yhtä suuri kuin nykyinen rekisteröityneiden määrä.", "app.errors.no_invites_specified": "Sähköpostiosoitetta ei löytynyt.", "app.errors.no_recipients": "Kampanjaa ei voi lähettää, koska vastaan<PERSON>ajia ei ole<PERSON> <PERSON><PERSON><PERSON><PERSON>, johon l<PERSON>, on joko tyhjä tai kukaan ei ole suostunut vastaanottamaan sähköposteja.", "app.errors.number_invalid": "<PERSON> k<PERSON>en numero.", "app.errors.password_blank": "Tä<PERSON>ä ei voi olla tyhjä", "app.errors.password_invalid": "Tarkista nykyinen sa<PERSON>anasi uudelleen.", "app.errors.password_too_short": "Salasanan tulee olla vähintään 8 merkkiä pitkä", "app.errors.resending_code_failed": "Jotain meni pieleen l<PERSON>ttäessä v<PERSON>.", "app.errors.slug_taken": "Tämä projektin URL-osoite on jo olemassa. Vaihda projektin slug johonkin muuhun.", "app.errors.tag_name_taken": "<PERSON><PERSON><PERSON><PERSON><PERSON> niminen tunniste on jo o<PERSON><PERSON>a", "app.errors.title_multiloc_blank": "<PERSON>tsik<PERSON> ei voi olla tyhj<PERSON>.", "app.errors.title_multiloc_includes_banned_words": "<PERSON><PERSON><PERSON><PERSON> sanoja, joita p<PERSON><PERSON><PERSON><PERSON> so<PERSON>.", "app.errors.token_invalid": "Salasanan palautuslinkkejä voi käyttää vain kerran ja ne ovat voimassa tunnin ajan lähettä<PERSON>n jälkeen. {passwordResetLink}.", "app.errors.too_common": "Tämä salasana on helppo arvata. Valitse vahvempi salasana.", "app.errors.too_long": "Valitse lyhyempi <PERSON> (enintään 72 merkkiä)", "app.errors.too_short": "Val<PERSON><PERSON>, jossa on vähintään 8 merkkiä", "app.errors.uncaught_error": "Ta<PERSON><PERSON><PERSON> tuntematon virhe.", "app.errors.unknown_group": "Riviltä {row} l<PERSON><PERSON><PERSON>yt ryhmä {value} ei ole tunnettu ryhmä", "app.errors.unknown_locale": "Riviltä {row} löytyvä kieli {value} ei ole mää<PERSON>tty kieli", "app.errors.unparseable_excel": "Valittua Excel-tiedostoa ei voitu kä<PERSON>llä.", "app.errors.url": "<PERSON> k<PERSON>vo<PERSON>en linkki. Varmista, että linkki alkaa https://", "app.errors.verification_taken": "Vahvistusta ei voi suorittaa loppuun, koska toinen tili on vahvistettu samoilla tied<PERSON>la.", "app.errors.view_name_taken": "<PERSON><PERSON><PERSON>ä<PERSON> niminen näky<PERSON>ä on jo o<PERSON><PERSON>a", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Sopimaton sisältö tunnistettiin automaattisesti viestissä tai kommentissa", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>n StandardPortalilla", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Rekisteröidy StandardPortalilla", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Luo Stadt Wien -tili nyt ja käytä yhtä kirjautumista useisiin Wienin digitaalisiin palveluihin.", "app.modules.id_cow.cancel": "Peruuta", "app.modules.id_cow.emptyFieldError": "Tä<PERSON>ä kenttä ei voi olla tyhjä.", "app.modules.id_cow.helpAltText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> henkilökortin sarjanumero löytyy", "app.modules.id_cow.invalidIdSerialError": "Virheellinen ID-sarja", "app.modules.id_cow.invalidRunError": "Virheellinen RUN", "app.modules.id_cow.noMatchFormError": "Vastaavaa ei l<PERSON>.", "app.modules.id_cow.notEntitledFormError": "<PERSON>i o<PERSON>u.", "app.modules.id_cow.showCOWHelp": "Mistä löydän ID-sarjanumeroni?", "app.modules.id_cow.somethingWentWrongError": "<PERSON><PERSON> voi vahvistaa sinua, koska jokin meni pieleen", "app.modules.id_cow.submit": "Lähetä", "app.modules.id_cow.takenFormError": "Varattu.", "app.modules.id_cow.verifyCow": "Vahvista COW:lla", "app.modules.id_franceconnect.verificationButtonAltText": "Vahvista FranceConnectilla", "app.modules.id_gent_rrn.cancel": "Peruuta", "app.modules.id_gent_rrn.emptyFieldError": "Tä<PERSON>ä kenttä ei voi olla tyhjä.", "app.modules.id_gent_rrn.gentRrnHelp": "Sosiaaliturvatunnuksesi näkyy digitaalisen henkilökorttisi takana", "app.modules.id_gent_rrn.invalidRrnError": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>tur<PERSON>nus", "app.modules.id_gent_rrn.noMatchFormError": "<PERSON><PERSON>t tietoja sosiaaliturvatunnuksestasi", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Emme voi vahvistaa sinua, koska asut <PERSON><PERSON>", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "<PERSON><PERSON> voi vahvistaa sinua, koska olet alle 14-vuo<PERSON><PERSON>", "app.modules.id_gent_rrn.rrnLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.rrnTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> sosiaaliturvatun<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, yli 14-vuo<PERSON><PERSON>.", "app.modules.id_gent_rrn.showGentRrnHelp": "Mistä löydän ID-sarjanumeroni?", "app.modules.id_gent_rrn.somethingWentWrongError": "<PERSON><PERSON> voi vahvistaa sinua, koska jokin meni pieleen", "app.modules.id_gent_rrn.submit": "Lähetä", "app.modules.id_gent_rrn.takenFormError": "Sosia<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on jo k<PERSON><PERSON><PERSON> toisen tilin vahvistamiseen", "app.modules.id_gent_rrn.verifyGentRrn": "Vahvista käyttämällä GentRrn", "app.modules.id_id_card_lookup.cancel": "Peruuta", "app.modules.id_id_card_lookup.emptyFieldError": "Tä<PERSON>ä kenttä ei voi olla tyhjä.", "app.modules.id_id_card_lookup.helpAltText": "Henkil<PERSON><PERSON><PERSON> se<PERSON>s", "app.modules.id_id_card_lookup.invalidCardIdError": "<PERSON>ä<PERSON>ä tunnus ei kelpaa.", "app.modules.id_id_card_lookup.noMatchFormError": "Vastaavaa ei l<PERSON>.", "app.modules.id_id_card_lookup.showHelp": "Mistä löydän ID-sarjanumeroni?", "app.modules.id_id_card_lookup.somethingWentWrongError": "<PERSON><PERSON> voi vahvistaa sinua, koska jokin meni pieleen", "app.modules.id_id_card_lookup.submit": "Lähetä", "app.modules.id_id_card_lookup.takenFormError": "Varattu.", "app.modules.id_oostende_rrn.cancel": "Peruuta", "app.modules.id_oostende_rrn.emptyFieldError": "Tä<PERSON>ä kenttä ei voi olla tyhjä.", "app.modules.id_oostende_rrn.invalidRrnError": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>tur<PERSON>nus", "app.modules.id_oostende_rrn.noMatchFormError": "<PERSON><PERSON>t tietoja sosiaaliturvatunnuksestasi", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Emme voi vahvistaa sinua, koska asut Oostenden ulkopuolella", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "<PERSON><PERSON> voi vahvistaa sinua, koska olet alle 14-vuo<PERSON><PERSON>", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Sosiaaliturvatunnuksesi näkyy digitaalisen henkilökorttisi takana", "app.modules.id_oostende_rrn.rrnLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.rrnTooltip": "Pyyd<PERSON>mme henkilötunnus<PERSON>i <PERSON>, <PERSON><PERSON><PERSON> ka<PERSON>, yli 14-vuotia<PERSON>.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Mist<PERSON> l<PERSON>än sosiaaliturvatunnukseni?", "app.modules.id_oostende_rrn.somethingWentWrongError": "<PERSON><PERSON> voi vahvistaa sinua, koska jokin meni pieleen", "app.modules.id_oostende_rrn.submit": "Lähetä", "app.modules.id_oostende_rrn.takenFormError": "Sosia<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on jo k<PERSON><PERSON><PERSON> toisen tilin vahvistamiseen", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Vahvista käyttämällä sosiaaliturvatunnusta", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "<PERSON>t j<PERSON>r<PERSON>stelmänvalvojan oikeudet kansioon \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Jaa", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Katso projektit osoitteessa {folderUrl} saadaksesi äänes<PERSON> kuuluviin!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | {orgName}osallistumisalustalta", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | {orgName}osallistumisalustalta", "app.sessionRecording.accept": "Kyllä, hyväksyn", "app.sessionRecording.modalDescription1": "Ymmärtääksemme käyttäjiämme paremmin, pyydämme satunnaisesti pientä osaa kävijöistä seuraamaan selausistuntoaan yksityiskohtaisesti.", "app.sessionRecording.modalDescription2": "Tallennettujen tietojen ainoa tarkoitus on parantaa verkkosivustoa. Mitään tietojasi ei jaeta kolmannen osapuolen kanssa. <PERSON><PERSON><PERSON> ant<PERSON><PERSON> a<PERSON>t tiedot suodatetaan.", "app.sessionRecording.modalDescription3": "Hyväksytkö?", "app.sessionRecording.modalDescriptionFaq": "FAQ täällä.", "app.sessionRecording.modalTitle": "Auta meitä parantamaan tätä verkkosivustoa", "app.sessionRecording.reject": "<PERSON><PERSON>, hylkään", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Kerää palautetta asiakirjasta", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "<PERSON><PERSON> al<PERSON><PERSON> si<PERSON> kysely", "app.utils.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON> k<PERSON>", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Upota ulkoinen kysely", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Etsi vapaaehtoisia", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Ke<PERSON><PERSON><PERSON> palautetta ja palautetta", "app.utils.AdminPage.ProjectEdit.shareInformation": "<PERSON><PERSON><PERSON> tie<PERSON>", "app.utils.FormattedCurrency.credits": "krediittejä", "app.utils.FormattedCurrency.tokens": "r<PERSON><PERSON><PERSON><PERSON>", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# krediittiä} one {# luotto} other {# krediittiä}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# rahakkeita} one {# token} other {# rahakkeita}}", "app.utils.IdeaCards.mostDiscussed": "Eniten keskusteltu", "app.utils.IdeaCards.mostReacted": "<PERSON><PERSON><PERSON> o<PERSON> re<PERSON>", "app.utils.IdeaCards.newest": "Uusin", "app.utils.IdeaCards.oldest": "<PERSON><PERSON>", "app.utils.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.trending": "Trendaavat", "app.utils.IdeasNewPage.contributionFormTitle": "Lisää uusi panos", "app.utils.IdeasNewPage.ideaFormTitle": "Lisää uusi idea", "app.utils.IdeasNewPage.initiativeFormTitle": "Lisää uusi aloite", "app.utils.IdeasNewPage.issueFormTitle1": "Lisää uusi kommentti", "app.utils.IdeasNewPage.optionFormTitle": "Lisää uusi vaiht<PERSON>hto", "app.utils.IdeasNewPage.petitionFormTitle": "Lisää uusi vetoomus", "app.utils.IdeasNewPage.projectFormTitle": "Lisää uusi projekti", "app.utils.IdeasNewPage.proposalFormTitle": "Lisää uusi ehdotus", "app.utils.IdeasNewPage.questionFormTitle": "Lisää uusi kysymys", "app.utils.IdeasNewPage.surveyTitle": "<PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourComment": "<PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourContribution": "<PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourIdea": "<PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourInitiative": "<PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourInput": "Näytä s<PERSON>ötteesi", "app.utils.IdeasNewPage.viewYourIssue": "Tarkastele ongelmaasi", "app.utils.IdeasNewPage.viewYourOption": "<PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourPetition": "<PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourProject": "Katso projekt<PERSON>", "app.utils.IdeasNewPage.viewYourProposal": "<PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourQuestion": "<PERSON><PERSON>", "app.utils.Projects.sendSubmission": "Lähetä lähetyksen tunniste sähköpostiini", "app.utils.Projects.sendSurveySubmission": "Lähetä kyselyn lähetyksen tunniste sähköpostiini", "app.utils.Projects.surveySubmission": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.Projects.yourResponseHasTheFollowingId": "Vastauksesi sisältää seuraavan tunnisteen: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "<PERSON><PERSON> pä<PERSON>, että haluat poistaa vast<PERSON>, ota meihin yhteyttä seuraavalla yksilöllisellä tunnisteella:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Sinun on täytettävä profiilisi osallistuaksesi tähän tapah<PERSON>.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Et täytä vaatimuksia osallistuaksesi tähän tap<PERSON>.", "app.utils.actionDescriptors.attendingEventNotPermitted": "<PERSON>ulla ei ole lupaa osallistua tähän tap<PERSON>.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "<PERSON>un tulee kirjautua sisään tai rekisteröityä osallistuaksesi tähän tapahtumaan.", "app.utils.actionDescriptors.attendingEventNotVerified": "<PERSON><PERSON> on vahvistettava tilisi ennen kuin voit osallistua tähän tapah<PERSON>.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "<PERSON>un on täytettävä profiilisi voidaksesi tehdä vapaaehtoistyötä.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Et täytä vapaaehtoistyön vaatimuksia.", "app.utils.actionDescriptors.volunteeringNotPermitted": "<PERSON><PERSON><PERSON> ei ole lupaa toimia v<PERSON>.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "<PERSON>un tulee kirjautua sisään tai rekisteröityä vapaaehtoiseksi.", "app.utils.actionDescriptors.volunteeringNotVerified": "Sinun on vahvistettava tilisi ennen kuin voit ryhtyä vapaaehtoiseksi.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "<PERSON> hyvä ja {completeRegistrationLink} tee vapaaehtoistyötä.", "app.utils.errors.api_error_default.in": "ei ole o<PERSON>in", "app.utils.errors.default.ajv_error_birthyear_required": "Täytä syntymävuotesi", "app.utils.errors.default.ajv_error_date_any": "Täytä kelvollinen päivämäärä", "app.utils.errors.default.ajv_error_domicile_required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.ajv_error_gender_required": "Täytä sukupuolesi", "app.utils.errors.default.ajv_error_invalid": "<PERSON><PERSON> kel<PERSON>a", "app.utils.errors.default.ajv_error_maxItems": "Enintään {limit, plural, one {# kohdetta} other {# kohdetta}}", "app.utils.errors.default.ajv_error_minItems": "Sisä<PERSON><PERSON><PERSON> vähintään {limit, plural, one {# kohde} other {# kohdetta}}", "app.utils.errors.default.ajv_error_number_any": "Täytä kelvollinen numero", "app.utils.errors.default.ajv_error_politician_required": "<PERSON><PERSON><PERSON><PERSON>, oletko poliitikko", "app.utils.errors.default.ajv_error_required3": "<PERSON><PERSON><PERSON> on pakollinen: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Ei voi olla tyhjä", "app.utils.errors.default.api_error_accepted": "On hyväksyttävä", "app.utils.errors.default.api_error_blank": "Ei voi olla tyhjä", "app.utils.errors.default.api_error_confirmation": "<PERSON>i vastaa", "app.utils.errors.default.api_error_empty": "Ei voi olla tyhjä", "app.utils.errors.default.api_error_equal_to": "ei ole o<PERSON>in", "app.utils.errors.default.api_error_even": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "app.utils.errors.default.api_error_exclusion": "On varattu", "app.utils.errors.default.api_error_greater_than": "On liian pieni", "app.utils.errors.default.api_error_greater_than_or_equal_to": "On liian pieni", "app.utils.errors.default.api_error_inclusion": "<PERSON><PERSON> sis<PERSON><PERSON> lue<PERSON>on", "app.utils.errors.default.api_error_invalid": "<PERSON><PERSON> kel<PERSON>a", "app.utils.errors.default.api_error_less_than": "On liian iso", "app.utils.errors.default.api_error_less_than_or_equal_to": "On liian iso", "app.utils.errors.default.api_error_not_a_number": "Ei ole numero", "app.utils.errors.default.api_error_not_an_integer": "Täytyy olla k<PERSON>u", "app.utils.errors.default.api_error_other_than": "ei ole o<PERSON>in", "app.utils.errors.default.api_error_present": "<PERSON><PERSON><PERSON><PERSON><PERSON> olla tyhjä", "app.utils.errors.default.api_error_too_long": "On liian pitkä", "app.utils.errors.default.api_error_too_short": "On liian lyhyt", "app.utils.errors.default.api_error_wrong_length": "<PERSON><PERSON> v<PERSON>ä<PERSON> pituus", "app.utils.errors.defaultapi_error_.odd": "<PERSON><PERSON><PERSON><PERSON><PERSON> olla <PERSON>oa", "app.utils.notInGroup": "Et täytä osallistumisvaatimuksia.", "app.utils.participationMethod.onSurveySubmission": "Kiitos. Vastau<PERSON>esi on vastaanotettu.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Äänestys ei ole enää ma<PERSON>, koska tämä vaihe ei ole enää aktiivinen.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Et täytä äänestysvaatimuksia.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Et saa äänestää.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Sinun tulee kirjautua sisään tai rekisteröityä äänestääksesi.", "app.utils.participationMethodConfig.voting.votingNotVerified": "<PERSON>un on vahvistettava tilisi ennen kuin voit äänestää.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b><PERSON><PERSON><PERSON> {endDate}.</b> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oli <PERSON>ht<PERSON> <b>{maxBudget} jaettavaks<PERSON> {optionCount} vaihtoehdon välillä.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "<PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "<PERSON><PERSON><PERSON> 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Et täytä budjettien määrittämisvaatimuksia.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Et saa määrittää budjetteja.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "<PERSON>un täyt<PERSON>y kirjautua sisään tai rekisteröityä voidaksesi määrittää budjetteja.", "app.utils.votingMethodUtils.budgetingNotVerified": "<PERSON><PERSON> on v<PERSON><PERSON><PERSON><PERSON> til<PERSON>, ennen kuin voit määrittää budjetteja.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "Valintojasi ei lasketa äänestystulokseen ennen kuin olet lähettänyt äänesi Lähetä-painikkeella", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Vaa<PERSON><PERSON> vähimmäisbudjetti on {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "<PERSON><PERSON> o<PERSON> val<PERSON>, vahvista valintasi Lähetä-painik<PERSON>ella.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Valitse haluamasi vaihtoehdot Lisää-painikkeella.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "<PERSON><PERSON><PERSON> on tällä alueella käytettävänä yhteensä <b>{maxBudget} euroa, jonka voit jakaa {optionCount} eri v<PERSON> </b>välillä.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON>, bud<PERSON><PERSON><PERSON> on lähetetty!</b> Voit tarkistaa vaihtoehdot alta milloin tahansa tai muokata niitä ennen <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON>, bud<PERSON><PERSON><PERSON> on lähetetty!</b> <PERSON>oit tarkistaa vaihtoehdot alta milloin tahan<PERSON>.", "app.utils.votingMethodUtils.castYourVote": "<PERSON>", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Voit lisätä enintään {maxVotes, plural, one {# krediittiä} other {# krediittiä}} vaiht<PERSON>ht<PERSON> kohden.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Voit lisätä enintään {maxVotes, plural, one {# pistettä} other {# pistettä}} vaiht<PERSON>ht<PERSON> kohden.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Voit lisätä enintään {maxVotes, plural, one {# tokenia} other {# tokenia}} vaiht<PERSON>ht<PERSON> kohden.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Voit lisätä enintään {maxVotes, plural, one {# ääntä} other {# ääntä}} vaiht<PERSON>ht<PERSON> kohden.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON>n olet val<PERSON>, <PERSON><PERSON><PERSON> \"l<PERSON>hetä\" äänestä<PERSON><PERSON><PERSON>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Valitse halu<PERSON>si vaihtoehdot napauttamalla \"Valitse\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "<PERSON><PERSON>a on yhteensä <b>{totalVotes, plural, one {# krediittiä} other {# krediittiä}} jae<PERSON><PERSON> {optionCount, plural, one {# vaihtoehdon} other {# vaihtoehdon}}</b>välill<PERSON>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "<PERSON><PERSON>a on yhteensä <b>{totalVotes, plural, one {# pistettä} other {# pistettä}} jae<PERSON><PERSON> {optionCount, plural, one {# vaihtoehdon} other {# vaihtoehdon}}</b>välill<PERSON>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "<PERSON><PERSON>a on yhteensä <b>{totalVotes, plural, one {# tokenia} other {# tokenia}} jae<PERSON><PERSON> {optionCount, plural, one {# vaihtoehdon} other {# vaihtoehdon}}</b>väli<PERSON><PERSON>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "<PERSON><PERSON>a on yhteensä <b>{totalVotes, plural, one {# ääntä} other {# ääntä}} jaetta<PERSON> {optionCount, plural, one {# vaihtoehdon} other {# vaihtoehdon}}</b>välill<PERSON>.", "app.utils.votingMethodUtils.finalResults": "Lopputulokset", "app.utils.votingMethodUtils.finalTally": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.howToParticipate": "<PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.howToVote": "Kuinka äänestää", "app.utils.votingMethodUtils.multipleVotingEnded1": "Äänestys päättyi <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 opintopistettä} one {1 opintopiste} other {# opintopistettä}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 pistettä} one {1 piste} other {# pistettä}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokenia} one {1 token} other {# tokeneita}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 ääntä} one {1 ääni} other {# ääntä}}", "app.utils.votingMethodUtils.results": "Tulokset", "app.utils.votingMethodUtils.singleVotingEnded": "Äänestys päättyi <b>{endDate}.</b> Osallistujat voivat <b>äänestää {maxVotes} vaihtoehtoja.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "<PERSON><PERSON><PERSON> halu<PERSON> vaiht<PERSON>hto nap<PERSON> \"valitse\"", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "<PERSON><PERSON><PERSON> on <b>{totalVotes} äänt<PERSON></b> , jotka voit määrittää vaihtoehdoille.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON>n olet val<PERSON>, <PERSON><PERSON><PERSON> \"l<PERSON>hetä\" äänestä<PERSON><PERSON><PERSON>.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Äänestys päättyi <b>{endDate}.</b> Osallistujat voivat <b>äänestää 1 vaihtoehdon puolesta.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Valitse haluama<PERSON> vaihtoehto napauttamalla Äänestä.", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "<PERSON><PERSON><PERSON> on <b>1 <PERSON><PERSON><PERSON></b> , jonka voit määrittä<PERSON> johon<PERSON> vaiht<PERSON><PERSON><PERSON>.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Äänestys päättyi <b>{endDate}.</b> Osallistujat saivat <b>äänestää niin monta vaihtoehtoa kuin halusivat.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Voit äänestää niin monta vaihtoehtoa kuin haluat.", "app.utils.votingMethodUtils.submitYourBudget": "Lähetä valintasi äänestyksessä", "app.utils.votingMethodUtils.submittedBudgetCountText2": "henkilö lähetti budjettinsa verkossa", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "kaupunkilaista on jo äänestänyt verkossa", "app.utils.votingMethodUtils.submittedVoteCountText2": "henkilö antoi äänensä verkossa", "app.utils.votingMethodUtils.submittedVotesCountText2": "ihmiset antoivat äänensä verkossa", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Äänestys lähetetty 🎉", "app.utils.votingMethodUtils.votesCast": "<PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.votingClosed": "Äänestys päättynyt", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Ääntäsi ei lasketa</b> ennen kuin napsautat \"Lähetä\"", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> on lähetetty!</b> Voit tarkistaa tai muokata lähetyst<PERSON><PERSON> ennen <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> on lähetetty!</b> Voit tarkistaa tai muokata lähetystäsi alla milloin tahansa.", "components.UI.IdeaSelect.noIdeaAvailable": "Ideoita ei ole saatavilla.", "components.UI.IdeaSelect.selectIdea": "Valitse idea", "containers.SiteMap.allProjects": "Kaikki projektit", "containers.SiteMap.customPageSection": "Mukautetut sivut", "containers.SiteMap.folderInfo": "Lisätietoja", "containers.SiteMap.headSiteMapTitle": "Sivustokartta | {orgName}", "containers.SiteMap.homeSection": "<PERSON><PERSON><PERSON>", "containers.SiteMap.pageContents": "<PERSON><PERSON><PERSON> si<PERSON>", "containers.SiteMap.profilePage": "Profiilis<PERSON><PERSON><PERSON>", "containers.SiteMap.profileSettings": "<PERSON><PERSON>uk<PERSON><PERSON>", "containers.SiteMap.projectEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectIdeas": "Ideat", "containers.SiteMap.projectInfo": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectPoll": "Poll", "containers.SiteMap.projectSurvey": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectsArchived": "Arkistoidut projektit", "containers.SiteMap.projectsCurrent": "Nykyiset projektit", "containers.SiteMap.projectsDraft": "<PERSON><PERSON><PERSON> luonn<PERSON>", "containers.SiteMap.projectsSection": "Projektit {orgName}", "containers.SiteMap.signInPage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.signUpPage": "Rekisteröidy", "containers.SiteMap.siteMapDescription": "Tältä sivulta voit siirtyä mihin tahansa alustan sisältöön.", "containers.SiteMap.siteMapTitle": "Osallist<PERSON>salustan {orgName}sivustokartta", "containers.SiteMap.successStories": "Menestystarinat", "containers.SiteMap.timeline": "<PERSON><PERSON><PERSON><PERSON> vai<PERSON>t", "containers.SiteMap.userSpaceSection": "<PERSON><PERSON><PERSON>", "containers.SubscriptionEndedPage.accessDenied": "<PERSON>ulla ei ole enää k<PERSON>töoikeutta", "containers.SubscriptionEndedPage.subscriptionEnded": "Täm<PERSON> sivu on käytettävissä vain alustoille, joilla on aktiivinen tilaus."}
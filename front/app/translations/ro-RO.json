{"EmailSettingsPage.initialUnsubscribeError": "A apărut o problemă în dezabonarea de la această campanie. Încercați din nou.", "EmailSettingsPage.initialUnsubscribeLoading": "Solicitarea dvs. este în curs de procesare, vă rugăm să așteptați ...", "EmailSettingsPage.initialUnsubscribeSuccess": "Dezabonat cu succes de la {campaignTitle}.", "UI.FormComponents.optional": "opțional", "app.closeIconButton.a11y_buttonActionMessage": "<PERSON><PERSON><PERSON>", "app.components.AssignBudgetControl.a11y_price": "Preț:", "app.components.AssignBudgetControl.add": "Adaugă", "app.components.AuthProviders.continue": "Con<PERSON><PERSON><PERSON>", "app.components.AuthProviders.continueWithAzure": "Continuă cu {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Continuați cu Facebook", "app.components.AuthProviders.continueWithGoogle": "Continuați cu Google", "app.components.AuthProviders.franceConnectMergingFailed": "Există deja un cont cu această adresă de e-mail.{br}{br}Nu puteți accesa platforma folosind FranceConnect deoarece datele personale nu corespund. Pentru a vă conecta folosind FranceConnect, va trebui mai întâi să vă schimbați numele sau prenumele pe această platformă pentru a corespunde detaliilor oficiale.{br}{br}Vă puteți conecta așa cum o faceți în mod normal mai jos.", "app.components.AuthProviders.goToLogIn": "Ai deja un cont? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Nu ai un cont? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Autentificare", "app.components.AuthProviders.logInWithEmail": "Autentificați-vă cu adresa de e-mail", "app.components.AuthProviders.signUp2": "Înscrie-te", "app.components.AuthProviders.signUpButtonAltText": "Înregistrează-te cu {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Înscrie-te cu e-mailul", "app.components.Comments.cancel": "<PERSON><PERSON><PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "Nu este posibilă lăsarea comentariilor în această fază.", "app.components.Comments.commentingDisabledInactiveProject": "<PERSON>u pute<PERSON>i comenta, deoarece acest proiect nu este încă activ.", "app.components.Comments.commentingDisabledProject": "Comenta<PERSON><PERSON> sunt momentan dezactivate.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} pentru a comenta.", "app.components.Comments.commentingMaybeNotPermitted": "Este posibil sa nu aveți acces la secțiunea de comentarii. Vă rugăm să accesați {signInLink} pentru a vedea dacă respectați criteriile.", "app.components.Comments.invisibleTitleComments": "Comenta<PERSON><PERSON>", "app.components.Comments.official": "Oficial", "app.components.Comments.replyToComment": "Răspunde la comentariu", "app.components.Comments.reportAsSpam": "Raportează ca spam", "app.components.Comments.seeOriginal": "A se vedea versiunea originală", "app.components.Comments.seeTranslation": "<PERSON><PERSON><PERSON> traduce<PERSON>", "app.components.Comments.yourComment": "Comentariul tău", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Un e-mail cu un cod de confirmare a fost trimis la {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Schimbați-vă adresa de e-mail.", "app.components.ConfirmationModal.codeInput": "Cod", "app.components.ConfirmationModal.confirmationCodeSent": "Cod nou trimis", "app.components.ConfirmationModal.didntGetAnEmail": "Nu ați primit un e-mail?", "app.components.ConfirmationModal.foundYourCode": "<PERSON><PERSON>i găsit codul dvs.?", "app.components.ConfirmationModal.goBack": "Întoarceți-vă.", "app.components.ConfirmationModal.sendEmailWithCode": "Trimiteți un e-mail cu codul", "app.components.ConfirmationModal.sendNewCode": "Trimiteți un nou cod.", "app.components.ConfirmationModal.verifyAndContinue": "Verificați și continuați", "app.components.ConfirmationModal.wrongEmail": "E-mail greșit?", "app.components.ConsentManager.Banner.accept": "Acceptă", "app.components.ConsentManager.Banner.close": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.mainText": "<PERSON><PERSON> navi<PERSON>, sunteți de acord cu {policyLink}.", "app.components.ConsentManager.Banner.manage": "Administrează", "app.components.ConsentManager.Banner.policyLink": "Politica <PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Re<PERSON>lamă", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Folosim acest lucru pentru a personaliza și a măsura eficiența campaniilor publicitare de pe site-ului nostru. Nu vom afișa orice formă de publicitate pe această platformă, dar următoarele servicii ar putea oferi un anunț personalizat bazat pe paginile pe care le vizitați pe site-ul nostru.\n", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Permite", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analize statistice", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Folosim această urmărire pentru a înțelege mai bine modul de utilizare al platformei, cu scopul de a învăța cum să îmbunătățim navigarea. Aceste informații sunt utilizate numai în analiza de masă, și în nici un caz pentru a urmări anumite persoane.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Întoarce-te", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Nu permite", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funcțional", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Acest lucru este necesar pentru a permite monitorizarea funcționalităților de bază ale site-ului. Unele instrumente enumerate aici s-ar putea să nu se aplice. Vă rugăm să citiți politica noastră cookie pentru mai multe informații.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Manager de etichete Google ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Obligator<PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Pentru a avea o platformă funcțională, salvăm un cookie de autentificare dacă vă înscrieți și limba în care utilizați această platformă.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Preferințele dvs. pentru cookie-uri", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Unelte", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Unele câmpuri au fost invalide. Vă rugăm să corectați erorile și să încercați din nou.", "app.components.ErrorBoundary.errorFormErrorGeneric": "A apărut o eroare necunoscută în trimiterea raportului. Vă rugăm să încercați din nou.\n", "app.components.ErrorBoundary.errorFormLabelClose": "<PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelComments": "Ce s-a întâmplat?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-mail", "app.components.ErrorBoundary.errorFormLabelName": "Nume", "app.components.ErrorBoundary.errorFormLabelSubmit": "Trimite", "app.components.ErrorBoundary.errorFormSubtitle": "Echipa noastră a fost notificată.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON><PERSON><PERSON>, spune-ne ce s-a întâmplat mai jos.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Feedback-ul dvs. a fost trimis. Mulțumesc!", "app.components.ErrorBoundary.errorFormTitle": "Se pare că există o problemă.", "app.components.ErrorBoundary.genericErrorWithForm": "A apărut o eroare și nu putem afișa acest conținut. Vă rugăm să încercați din nou, sau {openForm}!", "app.components.ErrorBoundary.openFormText": "ajută-ne să o identificăm", "app.components.EventCard.a11y_lessContentVisible": "Mai puține informații despre evenimente au devenit vizibile.", "app.components.EventCard.a11y_moreContentVisible": "Mai multe informații despre eveniment au devenit vizibile.", "app.components.EventCard.endsAt": "Se termină la", "app.components.EventCard.showLess": "Arat<PERSON> mai puțin", "app.components.EventCard.showMore": "Afișați mai multe", "app.components.EventCard.startsAt": "<PERSON><PERSON><PERSON> de <PERSON>", "app.components.FileUploader.a11y_file": "Fişier:", "app.components.FileUploader.a11y_filesToBeUploaded": "Fișierele de încărcat: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Nu există fișiere adăugate.", "app.components.FileUploader.a11y_removeFile": "Eliminați acest fișier", "app.components.FileUploader.fileInputDescription": "<PERSON><PERSON><PERSON> click pentru a selecta un fișier", "app.components.FileUploader.fileUploadLabel": "Atașamente (max. 50MB)", "app.components.FileUploader.incorrect_extension": "{fileName} nu este acceptat de sistemul nostru, fișierul nu va fi încărcat.", "app.components.FilterBoxes.a11y_allFilterSelected": "Filtrul de selectare ales: toate", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# postare} other {# postări}}", "app.components.FilterBoxes.a11y_removeFilter": "Îndepărtează filtrul", "app.components.FilterBoxes.a11y_selectedFilter": "Filtrul de stare selectat: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Selectate {numberOfSelectedTopics, plural, =0 {zero filtre pe subiecte} one {un filtru pe subiecte} other {# filtre pe subiecte}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Toate", "app.components.FilterBoxes.areas": "Filtru după zonă", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "Subiecte", "app.components.FiltersModal.filters": "Filtre", "app.components.FolderFolderCard.a11y_folderDescription": "Descriere folder:", "app.components.FolderFolderCard.a11y_folderTitle": "Titlu folder:", "app.components.FolderFolderCard.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# proiecte} one {# proiect} other {# proiecte}}", "app.components.GoBackButton.group.edit.goBack": "Întoarce-te", "app.components.HookForm.Feedback.errorTitle": "Există o problemă.", "app.components.HookForm.Feedback.submissionError": "Încearcă din nou. Dacă problema persistă, contactați-ne", "app.components.HookForm.Feedback.submissionErrorTitle": "A fost o problemă la noi, ne pare rău.", "app.components.HookForm.Feedback.successMessage": "Formularul a fost trimis cu succes", "app.components.IdeaCards.showMore": "Afișați mai multe", "app.components.IdeasMap.a11y_hideIdeaCard": "Ascundeți cartea de idei.", "app.components.IdeasMap.a11y_mapTitle": "Prezentare generală a hărții", "app.components.IdeasMap.clickOnMapToAdd": "<PERSON><PERSON><PERSON> clic pe hartă pentru a adăuga contribuția dumneavoastră", "app.components.IdeasMap.noFilteredResults": "Filtrele pe care le-ați selectat nu au returnat niciun rezultat", "app.components.IdeasMap.noResults": "Nici un rezultat gasit", "app.components.IdeasMap.or": "sau", "app.components.IdeasMap.signInLinkText": "Autentificare", "app.components.IdeasMap.signUpLinkText": "Înscrie-te", "app.components.IdeasMap.tapOnMapToAdd": "Atingeți pe hartă pentru a adăuga contribuția dvs.", "app.components.IdeasShow.bodyTitle": "Des<PERSON><PERSON><PERSON>", "app.components.IdeasShow.deletePost": "Șterge", "app.components.IdeasShow.editPost": "Editează", "app.components.IdeasShow.goBack": "Întoarce-te", "app.components.IdeasShow.moreOptions": "Mai multe opțiuni", "app.components.IdeasShow.or": "sau", "app.components.IdeasShow.proposedBudgetTitle": "<PERSON><PERSON>t propus", "app.components.IdeasShow.reportAsSpam": "Raportează ca spam", "app.components.IdeasShow.send": "Trimite", "app.components.IdeasShow.skipSharing": "<PERSON><PERSON><PERSON> peste, o voi face mai tâ<PERSON>iu", "app.components.IdeasShowPage.sorryNoAccess": "Ne pare rău, nu puteți accesa această pagină. Este posibil să trebuiască să vă conectați sau să vă înregistrați pentru a o accesa.", "app.components.PageNotFound.goBackToHomePage": "Înapoi la pagina principală", "app.components.PageNotFound.notFoundTitle": "Pagina nu a fost găsită", "app.components.PageNotFound.pageNotFoundDescription": "Pagina solicitată nu a putut fi găsită.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Furnizați conținut pentru cel puțin o limbă", "app.components.PagesForm.editContent": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PagesForm.fileUploadLabel": "Documente atașate (max. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Fișierele adăuga<PERSON> vor fi afișate în partea de jos a acestei pagini.", "app.components.PagesForm.navbarItemTitle": "Nume în bara de navigare", "app.components.PagesForm.pageTitle": "Titlu", "app.components.PagesForm.savePage": "Salvați pagina", "app.components.PagesForm.saveSuccess": "Pagina a fost salvată cu succes.", "app.components.PagesForm.titleMissingOneLanguageError": "Furnizați titlul pentru cel puțin o limbă", "app.components.ParticipationCTABars.allocateBudget": "Alocați bugetul", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "<PERSON><PERSON>s pentru participare", "app.components.ParticipationCTABars.poll": "Participă la sondaj", "app.components.ParticipationCTABars.seeIdeas": "<PERSON><PERSON><PERSON> idei", "app.components.ParticipationCTABars.takeTheSurvey": "Completează chestionarul", "app.components.ParticipationCTABars.userHasParticipated": "Ați participat la acest proiect.", "app.components.ParticipationCTABars.volunteer": "Voluntar", "app.components.PasswordInput.a11y_passwordHidden": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_passwordVisible": "Parolă vizibilă", "app.components.PasswordInput.a11y_strength1Password": "<PERSON><PERSON><PERSON> foar<PERSON>", "app.components.PasswordInput.a11y_strength2Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength3Password": "<PERSON><PERSON><PERSON> medie", "app.components.PasswordInput.a11y_strength4Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength5Password": "<PERSON><PERSON><PERSON> foarte put<PERSON>", "app.components.PasswordInput.hidePassword": "Ascunde parola", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON> scurt<PERSON> (min. {minimumPasswordLength} caractere)", "app.components.PasswordInput.minimumPasswordLengthError": "Furnizați o parolă care să aibă cel puțin {minimumPasswordLength} caractere", "app.components.PasswordInput.passwordEmptyError": "Introduceți parola dvs.", "app.components.PasswordInput.passwordStrengthTooltip1": "Pentru a crea o parolă puternică:", "app.components.PasswordInput.passwordStrengthTooltip2": "Utilizați o combinație de litere non-consecutive minuscule, majuscule, cifre, caractere speciale și semne de punctuație", "app.components.PasswordInput.passwordStrengthTooltip3": "Evitați cuvintele obișnuite sau ușor de ghicit", "app.components.PasswordInput.passwordStrengthTooltip4": "<PERSON><PERSON><PERSON><PERSON>i lungimea parolei", "app.components.PasswordInput.showPassword": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "Slabă", "app.components.PasswordInput.strength3Password": "Me<PERSON>", "app.components.PasswordInput.strength4Password": "Puternică", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON><PERSON>", "app.components.PostCardsComponents.list": "Listă", "app.components.PostCardsComponents.map": "Hartă", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Adăugați o actualizare oficială", "app.components.PostComponents.OfficialFeedback.cancel": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Șterge", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Sunteți sigur că doriți să ștergeți această actualizare oficială?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Edita<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "Ultima modificare făcută la {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Ultima actualizare: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Oficial", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Alegeți modul în care oamenii văd numele dvs.", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Actualizare oficială a numelui autorului", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Actualizare oficială a textului", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Actualizări oficiale", "app.components.PostComponents.OfficialFeedback.postedOn": "Postat pe data de {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Publică", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Afișați actualizările anterioare", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Oferă o actualizare", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON>e cerem scuze, a ap<PERSON><PERSON>t o <PERSON>", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Actualizarea dvs. a fost publicată cu succes!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Sprijină-mi contribuția '{postTitle}' la {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Sprijină-mi contribuția: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Sprijină-mi contribuția: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Ce părere ai despre această idee? Votează-o și distribuie discuția la {postUrl} pentru a te face auzit!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Sp<PERSON>jină-mi ideea: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Sp<PERSON>jiniți ideea mea: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Am postat o problemă '{postTitle}' la {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Tocmai am postat o problemă: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Tocmai am postat o problemă: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Sprijină-mi opțiunea propusă '{postTitle}' la {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Sprijină-mi opțiunea propusă: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Sprijină-mi opțiunea: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Sprijină-mi proiectul '{postTitle}' la {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Sprijină-mi proiectul: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Sprijină-mi proiectul: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Alăturați-vă discuției cu privire la această întrebare '{postTitle}' la {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Alăturați-vă discuției: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Alăturați-vă discuției: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Votul activat pentru {postTitle}", "app.components.PostComponents.linkToHomePage": "<PERSON> către pagina de start", "app.components.PostComponents.readMore": "Citește mai mult...", "app.components.ProjectArchivedIndicator.archivedProject": "Din păcate nu mai poți participa la acest proiect deoarece a fost arhivat", "app.components.ProjectCard.a11y_projectDescription": "Descrierea proiectului:", "app.components.ProjectCard.a11y_projectTitle": "Titlul proiectului:", "app.components.ProjectCard.addYourOption": "Adăugați opțiunea", "app.components.ProjectCard.allocateYourBudget": "Alocați bugetul", "app.components.ProjectCard.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.comment": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.contributeYourInput": "Sprijiniți-vă contribuția", "app.components.ProjectCard.finished": "Terminat", "app.components.ProjectCard.joinDiscussion": "Alăturați-vă discuției", "app.components.ProjectCard.learnMore": "Aflați mai multe", "app.components.ProjectCard.submitAnIssue": "Trimite o problemă", "app.components.ProjectCard.submitYourIdea": "Trimite o idee", "app.components.ProjectCard.submitYourProject": "Trimite un proiect", "app.components.ProjectCard.takeThePoll": "Completează sondajul de tip poll", "app.components.ProjectCard.takeTheSurvey": "Completează chestionarul", "app.components.ProjectCard.viewTheContributions": "Vizualizați contribuțiile", "app.components.ProjectCard.viewTheIdeas": "<PERSON><PERSON><PERSON> ideile", "app.components.ProjectCard.viewTheIssues": "Vizualizați problemele", "app.components.ProjectCard.viewTheOptions": "Vizualizați opțiunile", "app.components.ProjectCard.viewTheProjects": "Vizualizați proiectele", "app.components.ProjectCard.viewTheQuestions": "Vizualizați întrebările", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# comentarii} other {# comentarii}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# contribuție} other {# contribuții}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {nicio idee înc<PERSON>} one {# idee} other {# idei}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# problemă} other {# probleme}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# opțiune} other {# opțiuni}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# proiect} other {# proiecte}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# întrebare} other {# între<PERSON><PERSON><PERSON>}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# proiecte} one {# proiect} other {# proiecte}}", "app.components.ProjectFolderCards.components.Topbar.all": "Toate", "app.components.ProjectFolderCards.components.Topbar.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.draft": "Proiect", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filtreaz<PERSON> după", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Etichetă", "app.components.ProjectFolderCards.noProjectYet": "Nu există nici un proiect la acest moment", "app.components.ProjectFolderCards.noProjectsAvailable": "Nu există proiecte disponibile", "app.components.ProjectFolderCards.showMore": "Afișați mai multe", "app.components.ProjectFolderCards.stayTuned": "Rămâneți aproape, un proiect va apărea în curând.", "app.components.ProjectFolderCards.tryChangingFilters": "Încercați să schimbați filtrele selectate.", "app.components.ProjectTemplatePreview.alsoUsedIn": "De asemene<PERSON>, utilizate în aceste orașe", "app.components.ProjectTemplatePreview.copied": "Copiată", "app.components.ProjectTemplatePreview.copyLink": "Copiază linkul", "app.components.QuillEditor.alignCenter": "Centrați textul", "app.components.QuillEditor.alignLeft": "Alineză la stânga", "app.components.QuillEditor.alignRight": "Alineză la dreapta", "app.components.QuillEditor.bold": "Îngroșat", "app.components.QuillEditor.clean": "Eliminați formatarea", "app.components.QuillEditor.customLink": "<PERSON><PERSON>", "app.components.QuillEditor.customLinkPrompt": "Introduceți link-ul:", "app.components.QuillEditor.edit": "Edita<PERSON><PERSON>", "app.components.QuillEditor.image": "Încar<PERSON><PERSON>a", "app.components.QuillEditor.imageAltPlaceholder": "Scurtă descriere a imaginii", "app.components.QuillEditor.italic": "Italic", "app.components.QuillEditor.link": "Adăugați link-ul", "app.components.QuillEditor.linkPrompt": "Introduceți link-ul:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Listă ordonată", "app.components.QuillEditor.remove": "Elimină", "app.components.QuillEditor.save": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.subtitle": "Subtitlu", "app.components.QuillEditor.title": "Titlu", "app.components.QuillEditor.unorderedList": "Listă neordonată", "app.components.QuillEditor.video": "Încarcă un videoclip", "app.components.QuillEditor.videoPrompt": "Adaugă videoclipul:", "app.components.QuillEditor.visitPrompt": "Vizitați linkul", "app.components.Sharing.linkCopied": "Link copiat", "app.components.Sharing.share": "Distribuie", "app.components.Sharing.shareByEmail": "Distribuie și prietenilor tăi prin e-mail", "app.components.Sharing.shareByLink": "Copiază linkul", "app.components.Sharing.shareOnFacebook": "Distribuiți pe Facebook", "app.components.Sharing.shareOnTwitter": "Distribuiți pe Twitter", "app.components.Sharing.shareThisFolder": "Distribuie", "app.components.Sharing.shareThisProject": "Distribuie acest proiect", "app.components.Sharing.shareViaMessenger": "Trimite prin Messenger", "app.components.Sharing.shareViaWhatsApp": "Distribuiți prin WhatsApp", "app.components.SideModal.closeButtonAria": "<PERSON><PERSON><PERSON>", "app.components.TopicsPicker.numberOfSelectedTopics": "Selectate {numberOfSelectedTopics, plural, =0 {zero topics} one {one topic} other {# topics}}. {selectedTopicNames}", "app.components.UI.MoreActionsMenu.showMoreActions": "Afișați mai multe acțiuni", "app.components.UI.RemoveImageButton.a11y_removeImage": "Elimină", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Unauthorized.noPermission": "Nu aveți permisiunea de a vizualiza această pagină", "app.components.Unauthorized.notAuthorized": "Ne pare rău, nu sunteți autorizat să accesați această pagină.", "app.components.Upload.errorImageMaxSizeExceeded": "Imaginea selectată este mai mare decât {maxFileSize} MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Una sau mai multe imagini selectate sunt mai mari decât {maxFileSize} MB", "app.components.Upload.onlyOneImage": "Puteți încărca o singură imagine", "app.components.Upload.onlyXImages": "Puteți încărca maxim {maxItemsCount} imagini", "app.components.Upload.remaining": "<PERSON><PERSON><PERSON>", "app.components.Upload.uploadImageLabel": "Selectați o imagine (max. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Selectați una sau mai multe imagini", "app.components.UserName.deletedUser": "autor necu<PERSON><PERSON>", "app.components.UserName.verified": "Verificat", "app.components.VerificationModal.verifyBOSA": "Verificați cu itsme sau eID", "app.components.VerificationModal.verifyClaveUnica": "Verificați cu Clave Unica", "app.components.VerificationModal.verifyYourIdentity": "Verificați-vă identitatea", "app.components.VoteControl.budgetingFutureEnabled": "Vă puteți aloca bugetul începând cu {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Bugetarea participativă nu este activată momentan.", "app.components.VoteControl.budgetingNotPossible": "Modificarea bugetului dvs. nu este posibilă în acest moment.", "app.components.VoteControl.budgetingNotVerified": "<PERSON><PERSON> rugăm să {verifyAccountLink} pentru a continua.", "app.components.admin.SlugInput.resultingURL": "URL-ul rezultat", "app.components.admin.SlugInput.slugTooltip": "Slug-ul este setul unic de cuvinte de la sfârșitul adresei web a paginii, sau URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "Dacă modificați URL-ul, legăturile către pagina care utilizează vechiul URL nu vor mai funcționa.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "Adăugați o condiție", "app.components.admin.UserFilterConditions.field_email": "E-mail", "app.components.admin.UserFilterConditions.field_lives_in": "Locuiește în", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "A interacționat cu o intrare cu statut", "app.components.admin.UserFilterConditions.field_participated_in_project": "A contribuit la proiect", "app.components.admin.UserFilterConditions.field_participated_in_topic": "A postat ceva cu tag-ul", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Data înregistrării", "app.components.admin.UserFilterConditions.field_role": "Rol", "app.components.admin.UserFilterConditions.field_verified": "Verificare", "app.components.admin.UserFilterConditions.predicate_begins_with": "începe cu", "app.components.admin.UserFilterConditions.predicate_commented_in": "a comentat", "app.components.admin.UserFilterConditions.predicate_contains": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_ends_on": "se termină pe", "app.components.admin.UserFilterConditions.predicate_has_value": "are valoare", "app.components.admin.UserFilterConditions.predicate_in": "a efectuat orice acțiune", "app.components.admin.UserFilterConditions.predicate_is": "este", "app.components.admin.UserFilterConditions.predicate_is_admin": "este un administrator", "app.components.admin.UserFilterConditions.predicate_is_after": "este dup<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_before": "este înainte de", "app.components.admin.UserFilterConditions.predicate_is_checked": "se verifică", "app.components.admin.UserFilterConditions.predicate_is_empty": "este gol", "app.components.admin.UserFilterConditions.predicate_is_equal": "este", "app.components.admin.UserFilterConditions.predicate_is_exactly": "este exact", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "este mai mare decât", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "este mai mare sau egală cu", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "este un utilizator normal", "app.components.admin.UserFilterConditions.predicate_is_one_of": "este unul dintre", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "este un manager de proiect", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "este mai mic decât", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "este mai mic sau egal cu", "app.components.admin.UserFilterConditions.predicate_is_verified": "este verificată", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "nu începe cu", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "nu a comentat", "app.components.admin.UserFilterConditions.predicate_not_contains": "nu conține", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "nu se termină la", "app.components.admin.UserFilterConditions.predicate_not_has_value": "nu are valoare", "app.components.admin.UserFilterConditions.predicate_not_in": "nu a contribuit", "app.components.admin.UserFilterConditions.predicate_not_is": "nu este", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "nu este administrator", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "nu este verificată", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "nu este gol", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "nu este", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "nu este un utilizator normal", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "nu este unul dintre", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "nu este un manager de proiect", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "nu este verificată", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "nu a postat o intrare", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "nu s-a oferit voluntar", "app.components.admin.UserFilterConditions.predicate_posted_input": "a postat o intrare", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "s-a of<PERSON>t voluntar", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Atributul", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Stare", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "Valoare", "app.components.form.ErrorDisplay.guidelinesLinkText": "orientările no<PERSON>re", "app.components.form.ErrorDisplay.next": "Următorul", "app.components.form.ErrorDisplay.previous": "Anterior", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Începeți să tastați pentru a căuta după adresa de e-mail sau numele utilizatorului...", "app.components.form.controls.adminFieldTooltip": "Câmp vizibil doar pentru administratori", "app.components.form.controls.selectMany": "*Alegeți câte doriți", "app.components.form.error": "Eroare", "app.components.form.locationGoogleUnavailable": "Nu s-a putut încărca câmpul de locație furnizat de Google Maps.", "app.components.form.submit": "Trimite", "app.components.form.submitApiError": "A existat o problemă la trimiterea formularului. Vă rugăm să verificați dacă există erori și să încercați din nou.", "app.components.formBuilder.Page": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.addAnswer": "Adăugați un răspuns", "app.components.formBuilder.chooseMany": "Alegeți mai multe", "app.components.formBuilder.chooseOne": "Alegeți unul", "app.components.formBuilder.close": "Înch<PERSON><PERSON><PERSON>", "app.components.formBuilder.closed": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.content": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.default": "Implicit", "app.components.formBuilder.defaultContent": "Conținutul implicit", "app.components.formBuilder.delete": "Ștergeți", "app.components.formBuilder.deleteButtonLabel": "Ștergeți", "app.components.formBuilder.description": "Des<PERSON><PERSON><PERSON>", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Acest lucru a fost deja adăugat în formular. Conținutul implicit poate fi utilizat o singură dată.", "app.components.formBuilder.done": "Realizat", "app.components.formBuilder.editButtonLabel": "Edita<PERSON><PERSON>", "app.components.formBuilder.emptyOptionError": "Furnizați cel puțin 1 răspuns", "app.components.formBuilder.emptyTitleError": "Furnizați un titlu de întrebare", "app.components.formBuilder.enable": "Activați", "app.components.formBuilder.errorMessage": "Există o problemă, vă rugăm să o remediați pentru a putea salva modificările.", "app.components.formBuilder.fieldGroup.description": "Descriere (opțional)", "app.components.formBuilder.fieldGroup.title": "Titlu (opțional)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "În prezent, răspunsurile la aceste întrebări sunt disponibile doar în fișierul Excel exportat în Input Manager și nu sunt vizibile pentru utilizatori.", "app.components.formBuilder.fieldLabel": "Opțiuni de răspuns", "app.components.formBuilder.fileUpload": "Înc<PERSON><PERSON><PERSON>", "app.components.formBuilder.formEnd": "Sfârșitul formularului", "app.components.formBuilder.goToPageInputLabel": "<PERSON><PERSON><PERSON>, următoarea pagină este:", "app.components.formBuilder.helmetTitle": "Constructor de formulare", "app.components.formBuilder.imageFileUpload": "<PERSON><PERSON><PERSON><PERSON><PERSON> imagine", "app.components.formBuilder.invalidLogicBadgeMessage": "Logică invalidă", "app.components.formBuilder.layout": "Layout", "app.components.formBuilder.linearScale": "Scară liniar<PERSON>", "app.components.formBuilder.locationDescription": "Locație", "app.components.formBuilder.logic": "Logică", "app.components.formBuilder.logicValidationError": "Logic nu poate avea legături către pagini anterioare", "app.components.formBuilder.longAnswer": "<PERSON><PERSON><PERSON><PERSON> lung", "app.components.formBuilder.multipleChoice": "Alegere multiplă", "app.components.formBuilder.newField": "Câmpul nou", "app.components.formBuilder.number": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.open": "Deschide<PERSON><PERSON>", "app.components.formBuilder.optional": "Opțional", "app.components.formBuilder.page": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.pageRuleLabel": "Pagina urmă<PERSON>are este:", "app.components.formBuilder.proposedBudget": "<PERSON><PERSON>t propus", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "Această întrebare nu poate fi ștearsă.", "app.components.formBuilder.questionDescriptionOptional": "Descrierea întrebării (opțional)", "app.components.formBuilder.questionTitle": "Titlul întrebării", "app.components.formBuilder.range": "Gama", "app.components.formBuilder.removeAnswer": "Eliminați răspunsul", "app.components.formBuilder.required": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.requiredToggleLabel": "Obligativitatea răspunsului la această întrebare", "app.components.formBuilder.ruleForAnswerLabel": "Dacă răspunsul este:", "app.components.formBuilder.save": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.selectRangeTooltip": "Alegeți valoarea maximă pentru scara dumneavoastră.", "app.components.formBuilder.shortAnswer": "Răspuns scurt", "app.components.formBuilder.showResponseToUsersToggleLabel": "Afișați răspunsul utilizatorilor", "app.components.formBuilder.singleChoice": "O singură alegere", "app.components.formBuilder.supportArticleLinkText": "această pagină", "app.components.formBuilder.tags": "Etichete", "app.components.formBuilder.title": "Titlu", "app.components.formBuilder.toLabel": "la", "app.containers.AccessibilityStatement.applicability": "Această declarație de accesibilitate se aplică unui {demoPlatformLink} care este reprezentativ pentru acest site web; acesta utilizează același cod sursă și are aceeași funcționalitate.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Metoda de evaluare", "app.containers.AccessibilityStatement.assesmentText2022": "Accesibilitatea acestui site a fost evaluată de o entitate externă neimplicată în procesul de proiectare și dezvoltare. Conformitatea {demoPlatformLink} menționată mai sus poate fi identificată pe această {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "vă puteți schimba preferințele", "app.containers.AccessibilityStatement.changePreferencesText": "În orice moment, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Excepții de conformitate", "app.containers.AccessibilityStatement.conformanceStatus": "Starea conformității", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Ne străduim să facem conținutul nostru inclusiv pentru toți. Cu toate acestea, în unele cazuri, poate exista conținut inaccesibil unor utilizatori ai platformei, după cum se arată mai jos:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "site demo", "app.containers.AccessibilityStatement.email": "Email:", "app.containers.AccessibilityStatement.exception_1": "Platformele noastre digitale de bugetare participativă facilitează conținutul postat de către persoane și organizații. Este posibil ca PDF-urile, imaginile sau alte tipuri de fișiere, inclusiv multimedia, să fie încărcate pe platformă ca atașamente sau adăugate în câmpurile de text de către utilizatorii platformei. Este posibil ca aceste documente să nu fie accesibile în totalitate.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Apreciem recenzia ta cu privire la accesibilitatea acestui site. Te rugăm să ne contactezi prin una dintre următoarele metode:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Procesul de feedback", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} se angajează să ofere o platformă care să fie accesibilă tuturor utilizatorilor, indiferent de tehnologie sau de capacitate. Standardele actuale de accesibilitate relevante sunt respectate în eforturile noastre continue de a maximiza accesibilitatea și utilizabilitatea platformelor noastre pentru toți utilizatorii.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Atelierele noastre online au o componentă de streaming video live, care nu suportă în prezent subtitrări.", "app.containers.AccessibilityStatement.pageDescription": "O declarație privind accesibilitatea acestui site web", "app.containers.AccessibilityStatement.postalAddress": "<PERSON><PERSON><PERSON>:", "app.containers.AccessibilityStatement.publicationDate": "Data publicării", "app.containers.AccessibilityStatement.responsiveness": "Ne propunem să răspundem la feedback în termen de 1-2 zile luc<PERSON>.", "app.containers.AccessibilityStatement.statusPageText": "pagina de status", "app.containers.AccessibilityStatement.technologiesIntro": "Accesibilitatea acestui site se bazează pe următoarele tehnologii pentru a funcționa:", "app.containers.AccessibilityStatement.technologiesTitle": "Tehnologii", "app.containers.AccessibilityStatement.title": "Declarație de accesibilitate", "app.containers.AccessibilityStatement.userGeneratedContent": "Conținut generat de utilizatori", "app.containers.AccessibilityStatement.workshops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Sfârșitul sondajului", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Creați un grup inteligent", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Utilizatorii care corespund tuturor condițiilor următoare vor fi adăugați automat la grup:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Furnizați cel puțin o regulă", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Unele condiții sunt incomplete", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Salvați grupul", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Furnizați un nume de grup", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Verificarea este dezactivată pentru platforma dvs., eliminați regula de verificare sau apelați serviciul de asistență tehnică.", "app.containers.App.appMetaDescription": "Bine ați venit pe platforma online de participare civică a {orgName}.\nExplorează proiectele locale și implică-te în discuție!", "app.containers.App.loading": "Se încarcă...", "app.containers.CampaignsConsentForm.ally_categoryLabel": "E-mailurile din această categorie", "app.containers.CampaignsConsentForm.messageError": "A apărut o eroare în salvarea preferințelor dvs. pentru e-mail.", "app.containers.CampaignsConsentForm.messageSuccess": "Preferințele dvs. de e-mail au fost salvate.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Ce fel de notificări prin e-mail doriți să primiți? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Notific<PERSON><PERSON>", "app.containers.CampaignsConsentForm.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.currentPasswordLabel": "<PERSON><PERSON><PERSON>", "app.containers.ChangePassword.currentPasswordRequired": "Introduceți parola cure<PERSON>ă", "app.containers.ChangePassword.goHome": "Mergeți la acasă", "app.containers.ChangePassword.helmetDescription": "Pagina de schimbare a parolei", "app.containers.ChangePassword.helmetTitle": "Schimbați-vă parola", "app.containers.ChangePassword.newPasswordLabel": "Parolă nou<PERSON>", "app.containers.ChangePassword.newPasswordRequired": "Introduceți noua dvs. parolă", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Furnizați o parolă care să aibă cel puțin {minimumPasswordLength} caractere.", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Parola dvs. a fost actualizată cu succes", "app.containers.ChangePassword.passwordEmptyError": "Introduceți parola dvs.", "app.containers.ChangePassword.passwordsDontMatch": "Confirmați noua parolă", "app.containers.Comments.a11y_commentDeleted": "Comentariul a fost șters", "app.containers.Comments.a11y_commentPosted": "Comentariu postat", "app.containers.Comments.addCommentError": "A apărut o eroare. Vă rugăm să încercați din nou mai târziu.", "app.containers.Comments.adminCommentDeletionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.Comments.adminCommentDeletionConfirmButton": "Șterge acest comentariu", "app.containers.Comments.cancelCommentEdit": "<PERSON><PERSON><PERSON>", "app.containers.Comments.childCommentBodyPlaceholder": "Scrie un răspuns...", "app.containers.Comments.commentCancelUpvote": "Anulează", "app.containers.Comments.commentDeletedPlaceholder": "Acest comentariu a fost șters.", "app.containers.Comments.commentDeletionCancelButton": "Păstrează comentariul meu", "app.containers.Comments.commentDeletionConfirmButton": "Șterge comentariul meu", "app.containers.Comments.commentReplyButton": "Ră<PERSON><PERSON>", "app.containers.Comments.commentsSortTitle": "Sortează comentariile după:", "app.containers.Comments.confirmCommentDeletion": "Sunteți sigur că doriți să ștergeți acest comentariu? Nu puteți anula ulterior acest pas!", "app.containers.Comments.deleteComment": "Șterge", "app.containers.Comments.deleteReasonDescriptionError": "Furnizați mai multe informații despre motivul dvs.", "app.containers.Comments.deleteReasonError": "Furnizați un motiv", "app.containers.Comments.deleteReason_inappropriate": "Este inadecvat sau jignitor", "app.containers.Comments.deleteReason_irrelevant": "Acest lucru nu este potrivit aici", "app.containers.Comments.deleteReason_other": "Alt motiv", "app.containers.Comments.editComment": "Edita<PERSON><PERSON>", "app.containers.Comments.guidelinesLinkText": "liniile directoare ale comunității noastre", "app.containers.Comments.ideaCommentBodyPlaceholder": "Scrie comentariul tău aici", "app.containers.Comments.loadMoreComments": "Încărcați mai multe comentarii", "app.containers.Comments.loadingComments": "Se încarcă comentarii...", "app.containers.Comments.loadingMoreComments": "Se încarcă mai multe comentarii ...", "app.containers.Comments.profanityError": "Oops! Se pare că mesajul dvs. conține un limbaj care nu respectă {guidelinesLink}. Încercăm să păstrăm acest spațiu sigur pentru toată lumea. Vă rugăm să vă editați contribuția și să încercați din nou.", "app.containers.Comments.publishComment": "Postează un comentariu", "app.containers.Comments.reportAsSpamModalTitle": "De ce intenționezi să raportezi acest lucru ca spam?", "app.containers.Comments.saveComment": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.signInLinkText": "Autentificare", "app.containers.Comments.signInToComment": "<PERSON>ă rugăm să {signInLink} pentru a comenta.", "app.containers.Comments.signUpLinkText": "Înscrie-te", "app.containers.Comments.verifyIdentityLinkText": "Verificați-vă identitatea", "app.containers.CookiePolicy.advertisingContent": "La {orgName}, nu folosim instrumente de publicitate pe platformele de bugetare participativă.", "app.containers.CookiePolicy.advertisingTitle": "Re<PERSON>lamă", "app.containers.CookiePolicy.analyticsContents": "Cookie-urile de analiză urmăresc comportamentul vizitatorilor, cum ar fi paginile vizitate și durata vizitelor. De asemenea, acestea pot colecta unele date tehnice, inclusiv informații despre browser, locația aproximativă și adresele IP. Folosim aceste date doar la nivel intern pentru a continua să îmbunătățim experiența generală a utilizatorilor și funcționarea platformei. Astfel de date pot fi, de asemenea, partajate între Go Vocal și {orgName} pentru a evalua și îmbunătăți implicarea în proiectele de pe platformă. Rețineți că aceste date sunt anonime și sunt utilizate la nivel agregat - nu vă identifică personal. Cu toate acestea, este posibil ca, în cazul în care aceste date ar fi combinate cu alte surse de date, să apară o astfel de identificare.", "app.containers.CookiePolicy.analyticsTitle": "Analize statistice", "app.containers.CookiePolicy.cookiePolicyDescription": "O explicație detaliată a modului în care folosim cookie-uri pe această platformă", "app.containers.CookiePolicy.cookiePolicyTitle": "Politica <PERSON>", "app.containers.CookiePolicy.essentialContent": "Unele cookie-uri sunt esențiale pentru a asigura funcționarea corectă a acestei platforme. Aceste cookie-uri esențiale sunt utilizate în principal pentru a vă autentifica contul atunci când vizitați platforma și pentru a vă salva limba preferată.", "app.containers.CookiePolicy.essentialTitle": "<PERSON>ie-<PERSON><PERSON>", "app.containers.CookiePolicy.externalContent": "Unele dintre paginile noastre pot afișa conținut de la furnizori externi, de exemplu, YouTube sau Typeform. Nu avem control asupra acestor module cookie ale terților, iar vizualizarea conținutului de la acești furnizori externi poate duce, de asemenea, la instalarea de module cookie pe dispozitivul dumneavoastră.", "app.containers.CookiePolicy.externalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> externe", "app.containers.CookiePolicy.functionalContents": "Cookie-urile funcționale pot fi activate pentru ca vizitatorii să primească notificări despre actualizări și să acceseze canalele de asistență direct de pe platformă.", "app.containers.CookiePolicy.functionalTitle": "Funcțional", "app.containers.CookiePolicy.intro": "Ca majoritatea site-urilor web, folosim cookieuri pentru a optimiza experiența dvs. și a altor vizitatori de pe această platformă. Pentru că dorim să fim pe deplin transparenți cu privire la motivul și modul de utilizare a acestui cookie, veț<PERSON> găsi toate detaliile mai jos, în formulare scrise cât se poate de clar. Cookie-urile utilizate pe platforma noastră nu sunt niciodată folosite pentru a identifica și urmări utilizatorii, ele nu „știu cine ești“. Când ești autentificat, le folosim pentru a fi în măsură să îți trimitem notificări în aplicație sau prin e-mail cu privire la anumite evenimente relevante pentru tine. Aceste setări de notificare pot fi modificate în orice moment de pe pagina de profil.", "app.containers.CookiePolicy.manageCookiesDescription": "Puteți activa sau dezactiva cookie-urile analitice, de marketing și funcționale în orice moment în preferințele dumneavoastră privind cookie-urile. De asemenea, puteți șterge manual sau automat orice cookie-uri existente prin intermediul browserului dvs. de internet. Cu toate acestea, modulele cookie pot fi plasate din nou după consimțământul dvs. la orice vizite ulterioare pe această platformă. Dacă nu ștergeți modulele cookie, preferințele dvs. privind modulele cookie sunt stocate timp de 60 de zile, după care vi se va cere din nou consimțământul dvs.", "app.containers.CookiePolicy.manageCookiesPreferences": "Accesați {manageCookiesPreferencesButtonText} pentru a vedea o listă completă a integrărilor de la terțe părți utilizate pe această platformă și pentru a vă gestiona preferințele.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "<PERSON><PERSON><PERSON> cookie", "app.containers.CookiePolicy.manageCookiesTitle": "Gestionarea cookie-urilor dvs.", "app.containers.CookiePolicy.viewPreferencesButtonText": "<PERSON><PERSON><PERSON> cookie", "app.containers.CookiePolicy.viewPreferencesText": "Este posibil ca categoriile de cookie-uri de mai jos să nu se aplice tuturor vizitatorilor sau platformelor; consultați {viewPreferencesButton} pentru o listă completă a integrărilor de la terțe părți care vi se aplică.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Pentru ce folosim cookie-urile?", "app.containers.CustomPageShow.editPage": "Editați pagina", "app.containers.CustomPageShow.goBack": "Întoarce-te", "app.containers.CustomPageShow.notFound": "Pagina nu a fost găsită", "app.containers.IdeaButton.addAContribution": "Adăugați o contribuție", "app.containers.IdeaButton.addAProject": "Adăugați un proiect", "app.containers.IdeaButton.addAQuestion": "<PERSON>ug<PERSON>", "app.containers.IdeaButton.addAnOption": "Adăugați o opțiune", "app.containers.IdeaButton.postingDisabled": "Înscrierile noi nu sunt acceptate momentan", "app.containers.IdeaButton.postingInNonActivePhases": "Înscrierile noi pot fi adăugate numai când fazele sunt active.", "app.containers.IdeaButton.postingInactive": "Înscrierile noi nu sunt acceptate momentan.", "app.containers.IdeaButton.postingLimitedMaxReached": "Ați completat deja acest sondaj. Vă mulțumim pentru răspuns!", "app.containers.IdeaButton.postingNoPermission": "Înscrierile noi nu sunt acceptate momentan", "app.containers.IdeaButton.postingNotYetPossible": "Înscrierile noi nu sunt acceptate momentan.", "app.containers.IdeaButton.signInLinkText": "Autentifică-te", "app.containers.IdeaButton.signUpLinkText": "Înscrie-te", "app.containers.IdeaButton.submitAnIssue": "Trimite o problemă", "app.containers.IdeaButton.submitYourIdea": "Trimite-ți ideea", "app.containers.IdeaButton.takeTheSurvey": "Completează chestionarul", "app.containers.IdeaButton.verificationLinkText": "Confirmați-vă identitatea acum.", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {no comments} one {1 comment} other {# comments}}", "app.containers.IdeaCards.a11y_totalItems": "Total postări: {ideasCount}", "app.containers.IdeaCards.all": "Toate", "app.containers.IdeaCards.list": "Listă", "app.containers.IdeaCards.map": "Hartă", "app.containers.IdeaCards.newest": "<PERSON><PERSON> mai recente", "app.containers.IdeaCards.noFilteredResults": "Nici un rezultat gasit. Încercați un alt filtru sau termen de căutare.", "app.containers.IdeaCards.oldest": "<PERSON><PERSON> mai vechi", "app.containers.IdeaCards.popular": "<PERSON>le mai votate", "app.containers.IdeaCards.projectFilterTitle": "Proiecte", "app.containers.IdeaCards.random": "Aleat<PERSON><PERSON>", "app.containers.IdeaCards.resetFilters": "Resetați filtrele", "app.containers.IdeaCards.showXResults": "Arată {ideasCount, plural, one {# rezultat} other {# rezultate}}", "app.containers.IdeaCards.sortTitle": "Ordonare", "app.containers.IdeaCards.statusTitle": "Stare", "app.containers.IdeaCards.topics": "Subiecte", "app.containers.IdeaCards.topicsTitle": "Subiecte", "app.containers.IdeaCards.trending": "În tendințe", "app.containers.IdeaCards.tryDifferentFilters": "Nici un rezultat gasit. Încercați un alt filtru sau termen de căutare.", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# rezultat} other {# rezultate}}", "app.containers.IdeasEditPage.contributionFormTitle": "Editați contribuția", "app.containers.IdeasEditPage.editedPostSave": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.fileUploadError": "Unul sau mai multe fișiere nu au putut fi încărcate. Vă rugăm să verificați dimensiunea și formatul fișierului și încercați din nou.", "app.containers.IdeasEditPage.formTitle": "Editează ideea", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Editați-vă postarea. Adăugați informații noi și modificați vechile informații.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Editați {postTitle} | {projectName}", "app.containers.IdeasEditPage.issueFormTitle": "Editați problema", "app.containers.IdeasEditPage.optionFormTitle": "Editați opțiunea", "app.containers.IdeasEditPage.projectFormTitle": "Editați proiectul", "app.containers.IdeasEditPage.questionFormTitle": "Editați întrebarea", "app.containers.IdeasEditPage.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.submitApiError": "A existat o problemă la trimiterea formularului. Vă rugăm să verificați dacă există erori și să încercați din nou.", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Explorează toate postările de pe platformă de consultare civică a {orgName}.", "app.containers.IdeasIndexPage.inputsPageTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeasIndexPage.loadMore": "Încarcă mai mult...", "app.containers.IdeasIndexPage.loading": "Se încarcă...", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Descrierea contribuției trebuie să aibă o lungime mai mică de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Corpul ideii trebuie să aibă o lungime mai mare de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Titlul contribuției trebuie să aibă o lungime mai mică de {limit} caractere.", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Descrierea ideii trebuie să aibă mai puțin de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Descrierea ideii trebuie să aibă mai mult de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Vă rugăm să furnizați o descriere", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Titlul ideii trebuie să aibă mai mult de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Descrierea problemei trebuie să aibă mai puțin de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Descrierea problemei trebuie să aibă mai mult de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Titlul problemei trebuie să aibă mai puțin de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Descrierea opțiunii trebuie să aibă mai puțin de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Titlul opțiunii trebuie să aibă mai puțin de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Vă rugăm să selectați cel puțin o etichetă", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Descrierea proiectului trebuie să aibă mai puțin de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Descrierea proiectului trebuie să aibă mai mult de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Titlul proiectului trebuie să aibă mai puțin de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "<PERSON><PERSON> rugăm s<PERSON> introduceți un număr", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "<PERSON><PERSON> rugăm s<PERSON> introduceți un număr", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "Descrierea întrebării trebuie să aibă mai puțin de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "Descrierea întrebării trebuie să aibă mai mult de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "Titlul întrebării trebuie să aibă mai puțin de {limit} caractere", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "<PERSON>ă rugăm să furnizați un titlu", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Descrierea contribuției trebuie să aibă o lungime mai mică de 80 de caractere.", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Descrierea contribuției trebuie să aibă o lungime de cel puțin 30 de caractere.", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Titlul contribuției trebuie să aibă mai puțin de 80 de caractere", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Titlul contribuției trebuie să aibă cel puțin 10 caractere.", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Descrierea ideii trebuie să aibă mai puțin de 80 de caractere", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Descrierea ideii trebuie să fie de cel puțin 30 de caractere", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "<PERSON>ă rugăm să furnizați un titlu", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Titlul ideii trebuie să aibă mai puțin de 80 de caractere", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Titlul ideii trebuie să fie de cel puțin 10 caractere", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Este posibil să fi folosit unul sau mai multe cuvinte care sunt considerate blasfemii de către {guidelinesLink}. Vă rugăm să vă modificați textul pentru a elimina orice blasfemie care ar putea fi prezentă.", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Descrierea problemei trebuie să aibă mai puțin de 80 de caractere", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Descrierea problemei trebuie să aibă mai mult de 30 de caractere", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Titlul problemei trebuie să aibă mai puțin de 80 de caractere", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Titlul problemei trebuie să aibă cel puțin 10 caractere", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Descrierea opțiunii trebuie să aibă mai puțin de 80 de caractere", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Descrierea opțiunii trebuie să aibă mai mult de 30 de caractere", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Titlul opțiunii trebuie să aibă mai puțin de 80 de caractere", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Titlul opțiunii trebuie să aibă cel puțin 10 caractere", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Descrierea proiectului trebuie să aibă mai puțin de 80 de caractere", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Descrierea proiectului trebuie să aibă cel puțin 30 de caractere", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Titlul proiectului trebuie să aibă mai puțin de 80 de caractere", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Titlul proiectului trebuie să aibă cel puțin 10 caractere", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Vă rugăm să furnizați o descriere", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Descrierea întrebării trebuie să aibă mai puțin de 80 de caractere", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Descrierea întrebării trebuie să aibă cel puțin 30 de caractere", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Titlul întrebării trebuie să aibă mai puțin de 80 de caractere", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Titlul întrebării trebuie să aibă cel puțin 10 caractere", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.editSurvey": "Edita<PERSON><PERSON>", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Postați o acțiune și alăturați-vă conversației pe platforma de consultare civică a {orgName}.", "app.containers.IdeasNewPage.leaveSurveyText": "Răspunsurile dvs. nu vor fi salvate.", "app.containers.IdeasShow.MetaInformation.attachments": "Atașamente", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} la {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "<PERSON><PERSON> curent", "app.containers.IdeasShow.MetaInformation.location": "Locație", "app.containers.IdeasShow.MetaInformation.postedBy": "Postat de", "app.containers.IdeasShow.MetaInformation.topics": "Subiecte", "app.containers.IdeasShow.commentCTA": "Adaugă un comentariu", "app.containers.IdeasShow.contributionEmailSharingBody": "Susține această contribuție '{postTitle}' la {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Susține această contribuție: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Vă mulțumim pentru contribuția dvs!", "app.containers.IdeasShow.contributionTwitterMessage": "Susține această contribuție: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Susține această contribuție: {postTitle}", "app.containers.IdeasShow.currentStatus": "Status-ul curent", "app.containers.IdeasShow.deletedUser": "autor necu<PERSON><PERSON>", "app.containers.IdeasShow.ideaEmailSharingBody": "Susține-mi ideea '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Susține-mi ideea: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Susține această idee: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Susține ideea aceasta: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Susține această problemă: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Susține această problemă: '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Susține această problemă: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Vă mulțumim pentru problema trimisă!", "app.containers.IdeasShow.issueTwitterMessage": "Susține această problemă: {postTitle}", "app.containers.IdeasShow.optionEmailSharingBody": "Susține această opțiune '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Susține această opțiune: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Opțiunea dvs. a fost postată cu succes!", "app.containers.IdeasShow.optionTwitterMessage": "Susține această opțiune: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Susține această opțiune: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "<PERSON><PERSON><PERSON>ine acest proiect '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Susține acest proiect: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Vă mulțumim pentru proiectul dumneavoastră!", "app.containers.IdeasShow.projectTwitterMessage": "Susține acest proiect: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Susține acest proiect: {postTitle}", "app.containers.IdeasShow.questionEmailSharingBody": "Alăturați-vă discuției despre această întrebare '{postTitle}' la {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Alăturați-vă discuției: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Întrebarea dvs. a fost postată cu succes!", "app.containers.IdeasShow.questionTwitterMessage": "Alăturați-vă discuției: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Alăturați-vă discuției: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "De ce dorești să raportezi acest lucru ca spam?", "app.containers.IdeasShow.share": "Distribuie", "app.containers.IdeasShow.sharingModalSubtitle": "Adună cât mai multă susținere și fă-ți vocea auzită.", "app.containers.IdeasShow.sharingModalTitle": "Vă mulțumim pentru ideea dumneavoastră!", "app.containers.Navbar.unverified": "Neverificat", "app.containers.Navbar.verified": "Verificat", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {nicio notificare nevizualizată} one {1 notificare nevizualizată} other {# notificări nevizualizate}}", "app.containers.NotificationMenu.adminRightsReceived": "Ați devenit administrator pe <PERSON>a", "app.containers.NotificationMenu.deletedUser": "Autor <PERSON>", "app.containers.NotificationMenu.error": "Notificările nu s-au putut încărca", "app.containers.NotificationMenu.loadMore": "Încarcă mai mult...", "app.containers.NotificationMenu.loading": "Se încarcă notificările...", "app.containers.NotificationMenu.mentionInComment": "{name} v-a menționat într-un comentariu", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} v-a menționat într-o actualizare oficială", "app.containers.NotificationMenu.noNotifications": "Nu aveți încă nicio notificare", "app.containers.NotificationMenu.notificationsLabel": "Notific<PERSON><PERSON>", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} ți-a fost atribuită", "app.containers.NotificationMenu.projectModerationRightsReceived": "Acum sun<PERSON>ți manager de proiect al {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} a intrat într-o nouă etapă", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} va intra într-o nouă etapă pe {phaseStartAt}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} a atins pragul de voturi necesar", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} ți-a acceptat invitația", "app.containers.NotificationMenu.userReactedToYourComment": "{name} a reacționat la comentariul tău", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} atribuit {postTitle} ție", "app.containers.PasswordRecovery.emailError": "Aceasta nu pare să fie o adresă de e-mail validă", "app.containers.PasswordRecovery.emailLabel": "E-mail", "app.containers.PasswordRecovery.emailPlaceholder": "Adresa mea de email", "app.containers.PasswordRecovery.helmetDescription": "Resetează pagina de parolă", "app.containers.PasswordRecovery.helmetTitle": "Resetează parola", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "În cazul în care această adresă de e-mail este înregistrată pe platformă, a fost trimis un link de resetare a parolei.", "app.containers.PasswordRecovery.resetPassword": "Trimite un link de resetare a parolei", "app.containers.PasswordRecovery.submitError": "Nu am putut găsi un cont legat de acestă adresă de e-mail. În schimb vă puteți înscrie.", "app.containers.PasswordRecovery.subtitle": "Unde putem trimite un link pentru a alege o nouă parolă?", "app.containers.PasswordRecovery.title": "Reset<PERSON>", "app.containers.PasswordReset.helmetDescription": "Resetează pagina aferentă parolei", "app.containers.PasswordReset.helmetTitle": "Resetează parola", "app.containers.PasswordReset.login": "Autentificare", "app.containers.PasswordReset.passwordError": "Parola dvs. trebuie să conțină minim 8 caractere", "app.containers.PasswordReset.passwordLabel": "Pa<PERSON><PERSON>", "app.containers.PasswordReset.passwordPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Parola dvs. a fost actualizată cu succes.", "app.containers.PasswordReset.pleaseLogInMessage": "Vă rugăm să vă conectați cu noua parolă.", "app.containers.PasswordReset.requestNewPasswordReset": "Solicitați o nouă resetare a parolei", "app.containers.PasswordReset.submitError": "A apărut o eroare. Vă rugăm să încercați din nou mai târziu.", "app.containers.PasswordReset.title": "Resetează parola", "app.containers.PasswordReset.updatePassword": "Confirmă noua parolă", "app.containers.ProjectFolderCards.allProjects": "Toate proiectele", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} lucrează în prezent la:", "app.containers.ProjectFolderShowPage.editFolder": "Editați folderul", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informații despre acest proiect", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Citește mai mult", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON><PERSON><PERSON><PERSON> mai pu<PERSON>in", "app.containers.ProjectFolderShowPage.share": "Distribuie", "app.containers.Projects.PollForm.formCompleted": "Vă mulțumim că ați răspuns la acest sondaj de tip poll!", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "<PERSON>, nu aveți permisiunea de a participa la acest sondaj.", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Pentru moment nu este posibil să completați acest poll.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Sondajul de tip poll nu mai este disponibil, deoarece acest proiect nu mai este activ.", "app.containers.Projects.PollForm.sendAnswer": "Trimite", "app.containers.Projects.a11y_phase": "Faza {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Rezumatul etapelor", "app.containers.Projects.a11y_titleInputs": "Toate post<PERSON><PERSON>e trimise la acest proiect", "app.containers.Projects.a11y_titleInputsPhase": "Toate post<PERSON>rile trimise în această fază", "app.containers.Projects.addedToBasket": "Adăugat în coșul dvs", "app.containers.Projects.allocateBudget": "Alocați bugetul", "app.containers.Projects.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.basketSubmitted": "Coșul dumneavoastră a fost trimis!", "app.containers.Projects.contributions": "Contribuții", "app.containers.Projects.currentPhase": "<PERSON><PERSON><PERSON>", "app.containers.Projects.editProject": "Editați acest proiect", "app.containers.Projects.emailSharingBody": "Ce părere aveți despre această inițiativă? Votați-o și împărtășiți discuția la {initiativeUrl} pentru a vă face vocea auzită!", "app.containers.Projects.emailSharingSubject": "Susțineți inițiativa mea: {initiativeTitle}.", "app.containers.Projects.endedOn": "Încheiat la {data}", "app.containers.Projects.events": "Evenimente", "app.containers.Projects.header": "Proiecte", "app.containers.Projects.ideas": "<PERSON><PERSON><PERSON>", "app.containers.Projects.information": "Informații", "app.containers.Projects.invisibleTitlePhaseAbout": "Despre această etapă", "app.containers.Projects.invisibleTitlePoll": "Completează sondajul de tip poll", "app.containers.Projects.invisibleTitleSurvey": "Completează sondajul de opinie", "app.containers.Projects.issues": "Probleme", "app.containers.Projects.location": "Locație:", "app.containers.Projects.manageBasket": "Gestionați coșul", "app.containers.Projects.meetMinBudgetRequirement": "Îndepliniți bugetul minim pentru a vă depune coșul.", "app.containers.Projects.meetMinSelectionRequirement": "Faceți selecția necesară pentru a vă trimite coșul.", "app.containers.Projects.minBudgetRequired": "Bugetul minim necesar", "app.containers.Projects.myBasket": "Coșul meu", "app.containers.Projects.navPoll": "<PERSON><PERSON><PERSON>", "app.containers.Projects.navSurvey": "Chestionar", "app.containers.Projects.nextPhase": "<PERSON>aza u<PERSON>", "app.containers.Projects.noItems": "Nu ați selectat încă niciun element", "app.containers.Projects.noPastEvents": "Nu există evenimente anterioare de afișat", "app.containers.Projects.noPhaseSelected": "Nicio etapă selectată", "app.containers.Projects.noUpcomingOrOngoingEvents": "În prezent nu sunt programate evenimente viitoare sau în curs de desfășurare.", "app.containers.Projects.options": "Opțiuni", "app.containers.Projects.pastEvents": "Evenimente anterioare", "app.containers.Projects.phases": "Fazele", "app.containers.Projects.previousPhase": "Faza anterioară", "app.containers.Projects.project": "Proiect", "app.containers.Projects.projectTwitterMessage": "Fă-ți auzit vocea! Participa la {projectName} | {orgName}", "app.containers.Projects.projects": "Proiecte", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "Citește mai pu<PERSON>in", "app.containers.Projects.readMore": "Citește mai mult", "app.containers.Projects.removeItem": "Eliminați elementul", "app.containers.Projects.requiredSelection": "Selecție necesară", "app.containers.Projects.seeTheContributions": "Vezi contribuțiile", "app.containers.Projects.seeTheIdeas": "<PERSON><PERSON><PERSON> ideile", "app.containers.Projects.seeTheIssues": "<PERSON>ezi problemele", "app.containers.Projects.seeTheOptions": "Vezi opțiunile", "app.containers.Projects.seeTheProjects": "Vezi proiectele", "app.containers.Projects.seeTheQuestions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.share": "Distribuie", "app.containers.Projects.shareThisProject": "Distribuie acest proiect", "app.containers.Projects.submitMyBasket": "Trimite coșul meu", "app.containers.Projects.survey": "Chestionar", "app.containers.Projects.takeThePoll": "Completează sondajul de tip poll", "app.containers.Projects.takeTheSurvey": "Completează chestionarul", "app.containers.Projects.timeline": "Cronologie", "app.containers.Projects.upcomingAndOngoingEvents": "Evenimente viitoare și în curs de desfășurare", "app.containers.Projects.upcomingEvents": "Evenimente viitoare", "app.containers.Projects.whatsAppMessage": "{projectName} de pe platforma de participare a {orgName}", "app.containers.Projects.yourBudget": "Bugetul dumneavoastră", "app.containers.ProjectsIndexPage.metaDescription": "Explorează toate proiectele în curs de desfășurare a {orgName} pentru a înțelege modul în care puteți să participați.\n Vino și discută proiectele locale care contează cel mai mult pentru tine.", "app.containers.ProjectsIndexPage.pageTitle": "Proiecte", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Vreau să fiu voluntar", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Vă rugăm să {signInLink} sau {signUpLink} mai î<PERSON>, pentru a vă putea oferi voluntar la această activitate", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "autentifică-te", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "Înscrie-te", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "<PERSON><PERSON> retrag oferta de a fi voluntar", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {0 voluntari} one {# voluntar} other {# voluntari}}", "app.containers.ProjectsShowPage.process.survey.survey": "<PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Pentru a ști dacă puteți lua parte la acest chestionar, vă rugăm ca mai întâi să vă logați {logInLink} pe platformă.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Se poate răspunde la acest chestionar doar atunci când această etapă din cronologie este activă.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "<PERSON>, nu aveți permisiunea de a participa la acest chestionar.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Pentru a putea completa acest chestionar trebuie să vă verificăm identitatea. {verificationLink}", "app.containers.SearchInput.removeSearchTerm": "Eliminați termenul de căutare", "app.containers.SearchInput.searchAriaLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchTerm": "<PERSON><PERSON><PERSON>: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect este soluția propusă de statul francez pentru a securiza și simplifica înscrierea la peste 700 de servicii online.", "app.containers.SignIn.or": "Sau", "app.containers.SignIn.signInError": "Informațiile furnizate nu sunt corecte. Faceți clic pe „Parolă uitată“ pentru a vă reseta parola.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Folosiți FranceConnect pentru a vă conecta, a vă înscrie sau a vă verifica contul.", "app.containers.SignIn.whatIsFranceConnect": "Ce este France Connect?", "app.containers.SignUp.backToSignUpOptions": "Reveniți la opțiunile de conectare", "app.containers.SignUp.emailConsent": "Prin înregistrare, sunteți de acord să primiți e-mailuri de la această platformă. Puteți selecta ce e-mailuri doriți să primiți din setările dvs. de utilizator.", "app.containers.SignUp.emptyFirstNameError": "Introduceți prenumele dvs.", "app.containers.SignUp.emptyLastNameError": "Introduceți numele dvs. de familie", "app.containers.SignUp.firstNamesLabel": "Prenume", "app.containers.SignUp.goToLogIn": "Ai deja un cont? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "Accept ca datele să fie utilizate pe mitgestalten.wien.gv.at. Informații suplimentare pot fi găsite {link}.", "app.containers.SignUp.lastNameLabel": "Numele de familie", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Acceptați politica noastră de confidențialitate pentru a continua", "app.containers.SignUp.signUp2": "Înscrie-te", "app.containers.SignUp.skip": "Sari peste acest pas", "app.containers.SignUp.tacError": "Acceptarea termenilor și condițiilor noastre este necesară pentru a putea merge mai departe", "app.containers.SignUp.thePrivacyPolicy": "politica de confidențialitate", "app.containers.SignUp.theTermsAndConditions": "termenii și condițiile", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {Se pare că ați încercat să vă înscrieți înainte de a finaliza procesul. În schimb, dați click pe Logare/Înscriere, folosind datele de acreditare alese în timpul încercării anterioare.} other {Ceva nu a mers corect. Vă rugăm să încercați din nou mai târziu.}}", "app.containers.SignUp.viennaConsentEmail": "Adresa de e-mail", "app.containers.SignUp.viennaConsentFirstName": "Prenume", "app.containers.SignUp.viennaConsentFooter": "Vă puteți modifica informațiile de profil după ce vă conectați. Dacă aveți deja un cont cu aceeași adresă de e-mail pe mitgestalten.wien.gv.at, aceasta va fi legată de contul dvs. actual.", "app.containers.SignUp.viennaConsentHeader": "Vor fi transmise următoarele date:", "app.containers.SignUp.viennaConsentLastName": "Numele de familie", "app.containers.SignUp.viennaConsentUserName": "Numele de utilizator", "app.containers.SignUp.viennaDataProtection": "politica de confidențialitate a vienelor", "app.containers.SiteMap.contributions": "Contribuții", "app.containers.SiteMap.issues": "Probleme", "app.containers.SiteMap.options": "Opțiuni", "app.containers.SiteMap.projects": "Proiecte", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "<PERSON><PERSON>", "app.containers.SpamReport.buttonSuccess": "Succes", "app.containers.SpamReport.inappropriate": "Este inadecvat sau jignitor", "app.containers.SpamReport.messageError": "A apărut o eroare la trimiterea formularului, vă rugăm să încercați din nou.", "app.containers.SpamReport.messageSuccess": "Raportul tău a fost trimis", "app.containers.SpamReport.other": "Alt motiv", "app.containers.SpamReport.otherReasonPlaceholder": "Des<PERSON><PERSON><PERSON>", "app.containers.SpamReport.wrong_content": "Acest lucru nu este potrivit aici", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Eliminați fotografia de profil", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Pentru a participa la proiecte pentru cetățeni verificați.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Verificați-vă identitatea", "app.containers.UsersEditPage.bio": "Despre tine", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Nu puteți edita acest câmp deoarece conține informații verificate.", "app.containers.UsersEditPage.buttonSuccessLabel": "Succes", "app.containers.UsersEditPage.cancel": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Vă rugăm să faceți click aici pentru a vă actualiza verificarea.", "app.containers.UsersEditPage.conditionsLinkText": "condiț<PERSON>le noastre", "app.containers.UsersEditPage.contactUs": "Aveți un alt motiv pentru a ne părăsi? {feedbackLink} și poate vă putem ajuta.", "app.containers.UsersEditPage.deleteAccountSubtext": "Ne pare rău sa te vedem că pleci.", "app.containers.UsersEditPage.deleteMyAccount": "<PERSON><PERSON>ge contul meu", "app.containers.UsersEditPage.deleteYourAccount": "<PERSON><PERSON><PERSON> contul", "app.containers.UsersEditPage.deletionSection": "<PERSON><PERSON><PERSON> contul", "app.containers.UsersEditPage.deletionSubtitle": "Această acțiune nu poate fi anulată. Conținutul publicat pe platformă va fi anonim. Dacă doriți să ștergeți tot conținutul, puteți să ne contactați la adresa de e-mail: <EMAIL>.", "app.containers.UsersEditPage.email": "E-mail", "app.containers.UsersEditPage.emailEmptyError": "Furnizați o adresă de e-mail", "app.containers.UsersEditPage.emailInvalidError": "Furnizați o adresă de e-mail în formatul corect, <NAME_EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Anunță-ne", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Prenume", "app.containers.UsersEditPage.firstNamesEmptyError": "Furnizați un prenume", "app.containers.UsersEditPage.h1": "Informațiile contului tău", "app.containers.UsersEditPage.h1sub": "Editați informațiile contului", "app.containers.UsersEditPage.image": "Imagine avatar", "app.containers.UsersEditPage.imageDropzonePlaceholder": "<PERSON><PERSON><PERSON> click pentru a selecta o fotografie de profil (max. 5 MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "<PERSON>ate set<PERSON>rile profilului tău", "app.containers.UsersEditPage.language": "Limbă", "app.containers.UsersEditPage.lastName": "Numele de familie", "app.containers.UsersEditPage.lastNameEmptyError": "Furnizați un nume de familie", "app.containers.UsersEditPage.loading": "Se încarcă...", "app.containers.UsersEditPage.messageError": "Nu am putut salva profilul tău. Încercați din nou mai târziu sau trimiteți un e-mail la adresa: <EMAIL> de contact.", "app.containers.UsersEditPage.messageSuccess": "Profilul tău a fost salvat.", "app.containers.UsersEditPage.metaDescription": "Aceasta este pagina de setări a profilului lui {firstName} {lastName} de pe platforma de participare online a {tenantName}. Aici vă puteți confirma identitatea, modifica informațiile contului, șterge contul sau modifica preferințele de e-mail.", "app.containers.UsersEditPage.noGoingBack": "Odată ce apăsați acest buton nu veți mai avea posibilitatea de a vă mai recupera contul.", "app.containers.UsersEditPage.notificationsSubTitle": "Ce fel de notificări prin e-mail doriți să primiți? ", "app.containers.UsersEditPage.notificationsTitle": "Notificări prin e-mail", "app.containers.UsersEditPage.password": "Alegeți o parolă nouă", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Furnizați o parolă care să aibă cel puțin {minimumPasswordLength} caractere", "app.containers.UsersEditPage.passwordChangeSection": "Schimbați-vă parola", "app.containers.UsersEditPage.passwordChangeSubtitle": "Confirmați parola actuală și schimbați-o cu o nouă parolă.", "app.containers.UsersEditPage.privacyReasons": "Dacă sunteți îngrijorat cu privire la confidențialitatea datelor dumneavoastră, puteți citi {conditionsLink}.", "app.containers.UsersEditPage.processing": "Se trimite...", "app.containers.UsersEditPage.reasonsToStayListTitle": "Înainte sa pleci...", "app.containers.UsersEditPage.submit": "Salvează modificările", "app.containers.UsersEditPage.tooManyEmails": "Primiți prea multe e-mailuri? Puteți gestiona preferințele de notificări prin e-mail din setările profilului.", "app.containers.UsersEditPage.updateverification": "S-au schimbat informațiile dumneavoastră oficiale? {reverifyButton}", "app.containers.UsersEditPage.user": "<PERSON><PERSON>d do<PERSON>ți să vă trimitem un e-mail pentru a vă notifica?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Puteți participa la proiectele care sunt accesibile numai cetățenilor verificați.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Sunteți un cetățean verificat al platformei", "app.containers.UsersEditPage.verifyNow": "Verifică acum", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Postare la care a fost postat acest comentariu:", "app.containers.UsersShowPage.commentsWithCount": "Comentarii ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Editează profilul", "app.containers.UsersShowPage.invisibleTitlePostsList": "Toate <PERSON><PERSON><PERSON><PERSON> trimise de acest participant", "app.containers.UsersShowPage.invisibleTitleUserComments": "Toate comenta<PERSON>ile postate de către acest utilizator", "app.containers.UsersShowPage.loadMoreComments": "Încărcați mai multe comentarii", "app.containers.UsersShowPage.loadingComments": "Se încarcă comentarii...", "app.containers.UsersShowPage.memberSince": "<PERSON><PERSON><PERSON> din {date}", "app.containers.UsersShowPage.noCommentsForUser": "Această persoană nu a postat niciun comentariu momentan.", "app.containers.UsersShowPage.noCommentsForYou": "Nu există niciun comentariu momentan.", "app.containers.UsersShowPage.postsWithCount": "<PERSON><PERSON><PERSON> ({ideasCount})", "app.containers.UsersShowPage.seePost": "<PERSON><PERSON><PERSON>a", "app.containers.UsersShowPage.tryAgain": "A apărut o eroare. Vă rugăm să încercați din nou mai târziu.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Aceasta este pagina de profil a {firstName} {lastName} de pe platforma online de participare civică a {orgName}. Iată o prezentare generală a tuturor postărilor lor.", "app.containers.VoteControl.close": "<PERSON><PERSON><PERSON>", "app.containers.VoteControl.voteErrorTitle": "A apărut o eroare", "app.containers.app.navbar.admin": "Administrează platforma", "app.containers.app.navbar.allProjects": "Toate proiectele", "app.containers.app.navbar.ariaLabel": "Primare", "app.containers.app.navbar.closeMobileNavMenu": "Închideți meniul de navigare mobilă", "app.containers.app.navbar.editProfile": "<PERSON><PERSON><PERSON><PERSON> mele", "app.containers.app.navbar.fullMobileNavigation": "<PERSON><PERSON> complet", "app.containers.app.navbar.logIn": "Autentificare", "app.containers.app.navbar.logoImgAltText": "{orgName} Acasă", "app.containers.app.navbar.myProfile": "Profilul meu", "app.containers.app.navbar.search": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.signOut": "Deconectează-te", "app.containers.eventspage.errorWhenFetchingEvents": "S-a produs o eroare în timpul încărcării evenimentelor. Vă rugăm să încercați să reîncărcați pagina.", "app.containers.eventspage.eventsPageDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toate evenimentele postate pe platforma {orgName}.", "app.containers.eventspage.filterDropdownTitle": "Proiecte", "app.containers.eventspage.noPastEvents": "Nu există evenimente anterioare de afișat", "app.containers.eventspage.noUpcomingOrOngoingEvents": "În prezent nu sunt programate evenimente viitoare sau în curs de desfășurare.", "app.containers.eventspage.pastEvents": "Evenimente anterioare", "app.containers.eventspage.upcomingAndOngoingEvents": "Evenimente viitoare și în curs de desfășurare", "app.containers.footer.accessibility-statement": "Declarație de accesibilitate", "app.containers.footer.ariaLabel": "Secundar", "app.containers.footer.cookie-policy": "Politica <PERSON>", "app.containers.footer.cookieSettings": "<PERSON><PERSON><PERSON>", "app.containers.footer.feedbackEmptyError": "Câmpul de feedback nu poate să rămână gol.", "app.containers.footer.poweredBy": "<PERSON><PERSON> spri<PERSON>", "app.containers.footer.privacy-policy": "Politica de confidențialitate", "app.containers.footer.siteMap": "<PERSON><PERSON> pro<PERSON><PERSON>", "app.containers.footer.terms-and-conditions": "Termeni și condiții", "app.containers.landing.cityProjects": "Proiecte", "app.containers.landing.completeProfile": "Completează-ți profilul", "app.containers.landing.completeYourProfile": "Bine ai venit, {firstName}. Este timpul să îți finalizezi profilul.", "app.containers.landing.createAccount": "Înscrie-te", "app.containers.landing.defaultSignedInMessage": "{orgName} te ascultă. E rândul tău să îți faci vocea auzită!", "app.containers.landing.doItLater": "<PERSON><PERSON> face asta mai t<PERSON><PERSON><PERSON>", "app.containers.landing.new": "nou", "app.containers.landing.subtitleCity": "Bine aţi venit pe platforma de bugetare participativă a {orgName}", "app.containers.landing.titleCity": "Haideți să modelăm viitorul Primăriei {orgName} împreună", "app.containers.landing.twitterMessage": "Votează pentru {ideaTitle} pe", "app.containers.landing.upcomingEventsWidgetTitle": "Evenimente viitoare și în curs de desfășurare", "app.containers.landing.userDeletedSubtitle": "Puteți crea un nou cont în orice moment sau {contactLink} pentru a ne informa cu privire la ceea ce am putea îmbunătăți.", "app.containers.landing.userDeletedSubtitleLinkText": "scrieți-ne", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "Contul tău a fost șters.", "app.containers.landing.userDeletionFailed": "A apărut o eroare la ștergerea contului tău, am fost anunțați despre problemă și facem tot posibilul pentru a remedia problema. Vă rugăm să încercați din nou mai târziu.", "app.containers.landing.verifyNow": "Verifică acum", "app.containers.landing.verifyYourIdentity": "Devin-o un cetățean verificat", "app.containers.landing.viewAllEventsText": "<PERSON>ez<PERSON> toate even<PERSON><PERSON>", "app.errors.after_end_at": "Data de începere apare după data de încheiere", "app.errors.avatar_carrierwave_download_error": "Nu s-a putut descărca imaginea de avatar.", "app.errors.avatar_carrierwave_integrity_error": "Imaginea de avatar nu respectă formatul permis.", "app.errors.avatar_carrierwave_processing_error": "Imaginea de avatar nu a putut fi procesată.", "app.errors.avatar_extension_blacklist_error": "Extensia de fișier a imaginii avatarului nu este permisă. Extensiile permise sunt: jpg, jpeg, gif și png.", "app.errors.avatar_extension_whitelist_error": "Extensia de fișier a imaginii avatarului nu este permisă. Extensiile permise sunt: jpg, jpeg, gif și png.", "app.errors.banner_cta_button_multiloc_blank": "Introduceți textul unui buton.", "app.errors.banner_cta_button_url_blank": "Introduceți un link.", "app.errors.banner_cta_button_url_url": "Introduceți un link valid. Asigurați-vă că linkul începe cu 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Introduceți textul unui buton.", "app.errors.banner_cta_signed_in_url_blank": "Introduceți un link.", "app.errors.banner_cta_signed_in_url_url": "Introduceți un link valid. Asigurați-vă că linkul începe cu 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Introduceți textul unui buton.", "app.errors.banner_cta_signed_out_url_blank": "Introduceți un link.", "app.errors.banner_cta_signed_out_url_url": "Introduceți un link valid. Asigurați-vă că linkul începe cu 'https://'.", "app.errors.cannot_contain_ideas": "Această etapă conține {ideasCount, plural, one {o idee} other {{ideasCount} idei}} și metoda de participare pe care încercați să o schimbați nu acceptă idei. Vă rugăm să eliminați {ideasCount, plural, one {ideea} other {ideile}} din etapă și să încercați din nou.", "app.errors.cant_change_after_first_response": "Nu mai puteți modifica acest lucru, deoarece unii utilizatori au răspuns deja", "app.errors.category_name_taken": "O categorie cu acest nume există deja", "app.errors.confirmation_code_expired": "Codul a expirat. Vă rugăm să solicitați un nou cod.", "app.errors.confirmation_code_invalid": "Cod de confirmare invalid. Verificați e-mailul pentru codul corect sau încercați „Trimite cod nou”", "app.errors.confirmation_code_too_many_resets": "Ați retrimis codul de confirmare de prea multe ori. V<PERSON> rugăm să ne contactați pentru a primi un cod de invitație.", "app.errors.confirmation_code_too_many_retries": "Ați încercat de prea multe ori. Vă rugăm să solicitați un nou cod sau încercați să vă schimbați adresa de e-mail.", "app.errors.email_already_active": "Adresa de e-mail {value} găsită în rândul {row} aparține deja unui utilizator înregistrat", "app.errors.email_already_invited": "Adresa de e-mail {value} găsită în rândul {row} i-a fost trimisă deja o invitație", "app.errors.email_blank": "Acest câmp nu poate fi gol", "app.errors.email_domain_blacklisted": "Vă rugăm să utilizați un alt domeniu de e-mail pentru a vă înregistra.", "app.errors.email_invalid": "Vă rugăm să utilizați o adresă de e-mail validă.", "app.errors.email_taken": "Există deja un cont cu această adresă de e-mail. În consecință vă puteți conecta.", "app.errors.email_taken_by_invite": "{value} este deja luată de către o invitație aflată în așteptare. Verificați folderul spam iar dacă nu îl găsiți contactați {supportEmail}.", "app.errors.emails_duplicate": "Una sau mai multe valori pentru adresa de e-mail {value} au fost găsite în următoarele rând(uri): {rows}", "app.errors.file_extension_whitelist_error": "Formatul fișierului pe care ați încercat să îl încărcați nu este acceptat.", "app.errors.first_name_blank": "Acest câmp nu poate fi gol", "app.errors.generics.blank": "Acest câmp nu poate fi gol.", "app.errors.generics.invalid": "Aceasta nu este o valoare validă", "app.errors.generics.taken": "Acest e-mail există deja. Un alt cont este legat de ea.", "app.errors.generics.unsupported_locales": "Acest câmp nu acceptă setările locale curente.", "app.errors.group_ids_unauthorized_choice_moderator": "În calitate de manager de proiect, puteți trimite e-mailuri doar persoanelor care au acces la proiectul (proiectele) dvs.", "app.errors.has_other_overlapping_phases": "Proiectele nu pot avea faze suprapuse.", "app.errors.invalid_email": "E-mailul {value} găsit în rândul {row} nu este o adresă de e-mail validă", "app.errors.invalid_row": "A apărut o eroare necunoscută în timpul procesării câmpului {row}", "app.errors.is_not_timeline_project": "Proiectul actual nu oferă etape.", "app.errors.key_invalid": "Parola poate conține doar litere, cifre și bară jos (_)", "app.errors.last_name_blank": "Acest câmp nu poate fi gol", "app.errors.locale_blank": "Vă rugăm să selectați o limbă", "app.errors.locale_inclusion": "Vă rugăm să selectați o limbă acceptată", "app.errors.malformed_admin_value": "Valoarea de administrator {value} găsită în rândul {row} nu este validă", "app.errors.malformed_groups_value": "Grupul {value} gă<PERSON>t în rândul {row} nu este un grup valid", "app.errors.no_invites_specified": "Nu s-a găsit nicio adresă de e-mail.", "app.errors.password_blank": "Acest câmp nu poate fi gol", "app.errors.password_invalid": "Vă rugăm să verificați din nou parola actuală.", "app.errors.password_too_short": "Parola trebuie să fie de cel puțin 8 caractere", "app.errors.slug_taken": "Această adresă URL a proiectului există deja. Vă rugăm să schimbați slug-ul proiectului.", "app.errors.title_multiloc_blank": "Titlul nu poate fi gol.", "app.errors.token_invalid": "Link-urile de resetare a parolei pot fi utilizate o singură dată și sunt valabile timp de o oră după ce au fost trimise. {passwordResetLink}.", "app.errors.too_common": "Este prea ușor de ghicit", "app.errors.too_long": "Parola trebuie să aibă cel mult 72 de caractere", "app.errors.too_short": "Trebuie să aibă cel mult 8 caractere", "app.errors.unknown_group": "Grupul {value} gă<PERSON>t în rândul {row} nu este un grup cunoscut", "app.errors.unknown_locale": "Limbajul {value} g<PERSON><PERSON>t în rândul {row} nu este un limbaj configurat", "app.errors.unparseable_excel": "Fișierul Excel selectat nu a putut fi procesat.", "app.errors.view_name_taken": "O vizualizare cu acest nume există deja", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Conținutul nepotrivit a fost detectat automat într-o postare sau comentariu", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Conectați-vă cu StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Înregistrați-vă cu StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Creați acum un cont Stadt Wien și folosiți o singură autentificare pentru multe servicii digitale din Viena.", "app.modules.id_cow.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_cow.emptyFieldError": "Acest câmp nu poate fi gol.", "app.modules.id_cow.helpAltText": "Arată unde se găsește numărul de serie al cărții de identitate pe o carte de identitate", "app.modules.id_cow.invalidIdSerialError": "Seria ID-ului nu este valabilă", "app.modules.id_cow.invalidRunError": "RUN nevalid", "app.modules.id_cow.noMatchFormError": "Nu a fost găsită o potrivire", "app.modules.id_cow.notEntitledFormError": "Nu are dreptul.", "app.modules.id_cow.showCOWHelp": "Unde pot găsi seria actului meu de identitate ?", "app.modules.id_cow.somethingWentWrongError": "Nu putem verifica identitatea ta din cauza unei erori", "app.modules.id_cow.submit": "Trimite", "app.modules.id_cow.takenFormError": "<PERSON><PERSON> luat.", "app.modules.id_cow.verifyCow": "Confirmați folosind COW", "app.modules.id_franceconnect.verificationButtonAltText": "Verificați cu FranceConnect", "app.modules.id_gent_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "Acest câmp nu poate fi gol.", "app.modules.id_gent_rrn.gentRrnHelp": "Numărul de asigurare socială este indicat pe spatele cărții de identitate digitale.", "app.modules.id_gent_rrn.invalidRrnError": "<PERSON><PERSON><PERSON><PERSON> asigurare socială <PERSON>", "app.modules.id_gent_rrn.noMatchFormError": "Nu am putut găsi informații despre numărul de asigurare socială.", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Nu vă putem verifica pentru că locuiți în afara orașului Ghent.", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Nu vă putem verifica pentru că aveți mai puțin de 14 ani.", "app.modules.id_gent_rrn.rrnLabel": "Numărul de securitate socială", "app.modules.id_gent_rrn.rrnTooltip": "Vă solicităm numărul de asigurare socială pentru a verifica dacă sunteți cetățean al orașului Ghent, cu vârsta mai mare de 14 ani.", "app.modules.id_gent_rrn.showGentRrnHelp": "Unde pot găsi seria actului meu de identitate ?", "app.modules.id_gent_rrn.somethingWentWrongError": "Nu putem verifica identitatea ta din cauza unei erori", "app.modules.id_gent_rrn.submit": "Trimite", "app.modules.id_gent_rrn.takenFormError": "Numărul dvs. de securitate socială a fost deja folosit pentru a verifica un alt cont.", "app.modules.id_gent_rrn.verifyGentRrn": "Verificați folosind GentRrn", "app.modules.id_id_card_lookup.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.emptyFieldError": "Acest câmp nu poate fi gol.", "app.modules.id_id_card_lookup.helpAltText": "Explicația cărții de identitate", "app.modules.id_id_card_lookup.invalidCardIdError": "Acest ID nu este valid", "app.modules.id_id_card_lookup.noMatchFormError": "Nu a fost găsită o potrivire", "app.modules.id_id_card_lookup.showHelp": "Unde pot găsi seria actului meu de identitate?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Nu putem verifica identitatea ta din cauza unei erori", "app.modules.id_id_card_lookup.submit": "Trimite", "app.modules.id_id_card_lookup.takenFormError": "<PERSON><PERSON> luat.", "app.modules.id_oostende_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.emptyFieldError": "Acest câmp nu poate fi gol.", "app.modules.id_oostende_rrn.invalidRrnError": "<PERSON><PERSON><PERSON><PERSON> asigurare socială <PERSON>", "app.modules.id_oostende_rrn.noMatchFormError": "Nu am putut găsi informații despre numărul de asigurare socială.", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Nu vă putem verifica pentru că locuiți în afara Oostende", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Nu vă putem verifica pentru că aveți mai puțin de 14 ani.", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Numărul de asigurare socială este indicat pe spatele cărții de identitate digitale.", "app.modules.id_oostende_rrn.rrnLabel": "Numărul de securitate socială", "app.modules.id_oostende_rrn.rrnTooltip": "Vă solicităm numărul de asigurare socială pentru a verifica dacă sunteți cetățean al orașului Oostende și dacă aveți peste 14 ani.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Unde pot găsi numărul meu de asigurare socială?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Nu putem verifica identitatea ta din cauza unei erori", "app.modules.id_oostende_rrn.submit": "Trimite", "app.modules.id_oostende_rrn.takenFormError": "Numărul dvs. de securitate socială a fost deja folosit pentru a verifica un alt cont.", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Verificați folosind numărul de asigurare socială", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Ați primit drepturi de administrator pentru dosarul \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Distribuie", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Consultați proiectele la {folderUrl} pentru a vă face vocea auzită!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} |de pe platforma de participare a {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} |de pe platforma de participare a {orgName}", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Efectuarea unui exercițiu de alocare bugetară", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Creați un sondaj în platformă", "app.utils.AdminPage.ProjectEdit.createPoll": "Creați un sondaj", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Integrați un sondaj extern", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Găsiți voluntari", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Colectarea de informații și feedback", "app.utils.AdminPage.ProjectEdit.shareInformation": "Informații de partajare", "app.utils.FormattedCurrency.credits": "credite", "app.utils.FormattedCurrency.tokens": "<PERSON><PERSON>", "app.utils.IdeaCards.newest": "Cel mai nou", "app.utils.IdeaCards.oldest": "Cel mai vechi", "app.utils.IdeaCards.random": "Aleat<PERSON><PERSON>", "app.utils.IdeaCards.trending": "Trending", "app.utils.IdeasNewPage.contributionFormTitle": "Adaugă o contribuție nouă", "app.utils.IdeasNewPage.ideaFormTitle": "Adaugă o idee nouă", "app.utils.IdeasNewPage.optionFormTitle": "Adaugă o opțiune nouă", "app.utils.IdeasNewPage.projectFormTitle": "Adaugă un proiect nou", "app.utils.IdeasNewPage.questionFormTitle": "Adaugă o nouă întrebare", "app.utils.IdeasNewPage.surveyTitle": "Chestionar", "app.utils.errors.api_error_default.in": "Nu este corect", "app.utils.errors.default.ajv_error_birthyear_required": "Vă rugăm să introduceți anul nașterii dvs.", "app.utils.errors.default.ajv_error_date_any": "Vă rugăm să introduceți o dată validă", "app.utils.errors.default.ajv_error_domicile_required": "Vă rugăm s<PERSON> introduceți locul de reședință", "app.utils.errors.default.ajv_error_gender_required": "<PERSON>ă rugăm să introduceți genul dvs.", "app.utils.errors.default.ajv_error_invalid": "Nu este valabil", "app.utils.errors.default.ajv_error_maxItems": "Nu poate include mai mult de {limit, plural, one {# element} other {# elemente}}", "app.utils.errors.default.ajv_error_minItems": "Trebuie să includă cel puțin {limit, plural, one {# element} other {# elemente}}", "app.utils.errors.default.ajv_error_number_any": "<PERSON><PERSON> rugăm să introduceți un număr valid", "app.utils.errors.default.ajv_error_politician_required": "<PERSON><PERSON>m s<PERSON>i da<PERSON>ț<PERSON> politician", "app.utils.errors.default.ajv_error_type": "Nu poate fi gol", "app.utils.errors.default.api_error_accepted": "Trebuie să fie acceptată", "app.utils.errors.default.api_error_blank": "Nu poate fi gol", "app.utils.errors.default.api_error_confirmation": "Nu se potrivește", "app.utils.errors.default.api_error_empty": "Nu poate fi gol", "app.utils.errors.default.api_error_equal_to": "Nu este corect", "app.utils.errors.default.api_error_even": "Trebuie să fie egal", "app.utils.errors.default.api_error_exclusion": "Este rezervat", "app.utils.errors.default.api_error_greater_than": "Este prea mic", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Este prea mic", "app.utils.errors.default.api_error_inclusion": "Nu este inclus în listă", "app.utils.errors.default.api_error_invalid": "Nu este valabil", "app.utils.errors.default.api_error_less_than": "Este prea mare", "app.utils.errors.default.api_error_less_than_or_equal_to": "Este prea mare", "app.utils.errors.default.api_error_not_a_number": "Nu este un număr", "app.utils.errors.default.api_error_not_an_integer": "Trebuie să fie un număr întreg", "app.utils.errors.default.api_error_other_than": "Nu este corect", "app.utils.errors.default.api_error_present": "Trebuie să fie gol", "app.utils.errors.default.api_error_too_long": "Este prea lung", "app.utils.errors.default.api_error_too_short": "Este prea scurt", "app.utils.errors.default.api_error_wrong_length": "<PERSON>ste lungimea greșită", "app.utils.errors.defaultapi_error_.odd": "Trebuie să fie par.", "app.utils.participationMethod.onSurveySubmission": "Vă mulțumim. Răspunsul dvs. a fost primit.", "containers.SiteMap.allProjects": "Toate proiectele", "containers.SiteMap.customPageSection": "Pagini personalizate", "containers.SiteMap.folderInfo": "Mai multe informații", "containers.SiteMap.homeSection": "General", "containers.SiteMap.pageContents": "Conținutul paginii", "containers.SiteMap.profilePage": "Pagina ta de profil", "containers.SiteMap.profileSettings": "Setările tale", "containers.SiteMap.projectEvents": "Evenimente", "containers.SiteMap.projectIdeas": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectInfo": "Informații", "containers.SiteMap.projectPoll": "Son<PERSON>j de tip poll", "containers.SiteMap.projectSurvey": "Chestionar", "containers.SiteMap.projectsArchived": "Proiecte arhivate", "containers.SiteMap.projectsCurrent": "<PERSON>iecte curente", "containers.SiteMap.projectsDraft": "Schițe de proiecte", "containers.SiteMap.projectsSection": "Proiectele {orgName}", "containers.SiteMap.signInPage": "Conectează-te", "containers.SiteMap.signUpPage": "Înscrie-te", "containers.SiteMap.siteMapDescription": "De pe această pagină, puteți naviga către orice conținut de pe platformă.", "containers.SiteMap.siteMapTitle": "Harta proiectelor de pe platforma de consultare civică a {orgName}", "containers.SiteMap.successStories": "Povești de succes", "containers.SiteMap.timeline": "Etapele proiectului", "containers.SiteMap.userSpaceSection": "<PERSON><PERSON><PERSON> t<PERSON>u", "containers.SubscriptionEndedPage.accessDenied": "Nu mai aveți acces", "containers.SubscriptionEndedPage.subscriptionEnded": "Această pagină este accesibilă numai pentru platformele cu un abonament activ."}
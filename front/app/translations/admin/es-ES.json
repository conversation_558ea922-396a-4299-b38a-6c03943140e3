{"UI.FormComponents.required": "requerido", "app.Admin.ManagementFeed.action": "Acción", "app.Admin.ManagementFeed.after": "Después de", "app.Admin.ManagementFeed.before": "<PERSON><PERSON> de", "app.Admin.ManagementFeed.changed": "Modificado", "app.Admin.ManagementFeed.created": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.date": "<PERSON><PERSON>", "app.Admin.ManagementFeed.deleted": "Eliminado", "app.Admin.ManagementFeed.folder": "Carpeta", "app.Admin.ManagementFeed.idea": "Idea", "app.Admin.ManagementFeed.in": "en el proyecto {project}", "app.Admin.ManagementFeed.item": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.key": "Clave", "app.Admin.ManagementFeed.managementFeedNudge": "El acceso al feed de gestión no está incluido en tu licencia actual. Ponte en contacto con tu gestor de GovSuccess para obtener más información.", "app.Admin.ManagementFeed.noActivityFound": "No se ha encontrado actividad", "app.Admin.ManagementFeed.phase": "Fase", "app.Admin.ManagementFeed.project": "proyectos", "app.Admin.ManagementFeed.projectReviewApproved": "Proyecto aprobado", "app.Admin.ManagementFeed.projectReviewRequested": "Revisión del proyecto solicitada", "app.Admin.ManagementFeed.title": "Feed de gestión", "app.Admin.ManagementFeed.user": "Usuario", "app.Admin.ManagementFeed.userPlaceholder": "Selecciona un usuario", "app.Admin.ManagementFeed.value": "C", "app.Admin.ManagementFeed.viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.warning": "Característica experimental: Una lista mínima de acciones seleccionadas realizadas por administradores o gestores en los últimos 30 días. No se incluyen todas las acciones.", "app.Admin.Moderation.managementFeed": "Feed de gestión", "app.Admin.Moderation.participationFeed": "Feed de participación", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "¿Estás seguro?", "app.components.Admin.Campaigns.clicked": "Cliqueado", "app.components.Admin.Campaigns.deleteCampaignButton": "Eliminar campaña", "app.components.Admin.Campaigns.deliveryStatus_accepted": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Re<PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_clicked": "Cliqueado", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "Esto muestra cuántos destinatarios hicieron clic en un enlace del correo electrónico. Ten en cuenta que algunos sistemas de seguridad pueden seguir los enlaces automáticamente para escanearlos, lo que puede dar lugar a falsos clics.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_failed": "Fallido", "app.components.Admin.Campaigns.deliveryStatus_opened": "<PERSON>bie<PERSON>o", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "Esto muestra cuántos destinatarios abrieron el correo electrónico. Ten en cuenta que algunos sistemas de seguridad (como Microsoft Defender) pueden precargar el contenido para escanearlo, lo que puede provocar falsas aperturas.", "app.components.Admin.Campaigns.deliveryStatus_sent": "Enviado", "app.components.Admin.Campaigns.draft": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.from": "De", "app.components.Admin.Campaigns.manageButtonLabel": "Administrar", "app.components.Admin.Campaigns.opened": "<PERSON>bie<PERSON>o", "app.components.Admin.Campaigns.project": "proyectos", "app.components.Admin.Campaigns.recipientsTitle": "Des<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.reply_to": "Responder a", "app.components.Admin.Campaigns.sent": "Enviado", "app.components.Admin.Campaigns.statsButton": "Estadísticas", "app.components.Admin.Campaigns.subject": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.to": "Para", "app.components.Admin.ImageCropper.cropFinalSentence": "Véase tamb<PERSON>: {link}.", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "Mantén el contenido importante dentro de las líneas de puntos para asegurarte de que siempre esté visible.", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "3:1 en móvil (sólo se muestra el área entre las líneas de puntos)", "app.components.Admin.ImageCropper.cropSentenceOne": "La imagen se recorta automáticamente:", "app.components.Admin.ImageCropper.cropSentenceTwo": "{aspect} en el escritorio (ancho completo mostrado)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "proporción recomendada", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Texto de cabecera para los usuarios registrados", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Advertencia: el color seleccionado no tiene un contraste suficientemente alto. Esto puede hacer que el texto que sea difícil de leer. Elige un color más oscuro para optimizar la legibilidad.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Añadir eventos a la barra de navegación", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "Cuando se activa, se añade un enlace a todos los eventos del proyecto en la barra de navegación.", "app.components.AdminPage.SettingsPage.eventsSection": "Eventos", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Sección personalizada de la página de inicio", "app.components.AnonymousPostingToggle.userAnonymity": "Anonimato del usuario", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Los usuarios podrán ocultar su identidad a otros usuarios, a los moderadores del proyecto y a los administradores. Estas contribuciones pueden seguir siendo moderadas.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Permitir a los usuarios participar de forma anónima", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Los usuarios podrán seguir optando por participar con su nombre real, pero tendrán la opción de enviar contribuciones de forma anónima si así lo desean. Todos los usuarios deberán seguir cumpliendo con los requisitos establecidos en la pestaña Derechos de Acceso para que sus aportes sean aceptados. Los datos del perfil del usuario que decida publicar de manera anónima no estarán disponibles cuando exporte los datos de participación.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Obtén más información sobre cómo funciona la opción de permitir que los usuarios participen de manera anónima en esta página {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Una vez que se añadan plazas adicionales, se incrementará su facturación. Póngase en contacto con su gestor de GovSuccess para obtener más información.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "¡Gracias por completar la encuesta! Te invitamos a volver a hacerla el próximo trimestre.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Descargar como pdf", "app.components.FormSync.downloadExcelTemplate": "Descargar una plantilla Excel", "app.components.FormSync.downloadExcelTemplateTooltip2": "Las plantillas de Excel no incluirán preguntas de clasificación, preguntas de matriz, preguntas de carga de archivos ni preguntas de introducción de datos cartográficos (punto, pinta ruta, pintar á<PERSON>, carga de archivos ESRI), ya que en este momento no son compatibles con la importación masiva.", "app.components.ProjectTemplatePreview.close": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.createProject": "<PERSON><PERSON>r proyecto", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Crea un proyecto basado en la plantilla ''{templateTitle}''", "app.components.ProjectTemplatePreview.goBack": "Volver atrás", "app.components.ProjectTemplatePreview.goBackTo": "Volver atrás a {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "Experto de Go Vocal", "app.components.ProjectTemplatePreview.infoboxLine1": "¿Quieres usar esta plantilla para tu proyecto de participación?", "app.components.ProjectTemplatePreview.infoboxLine2": "Comunícate con la persona responsable de la administración en tu organización o contacta a {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Carpeta del proyecto", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "La fecha seleccionada no es válida. Por favor, indica una fecha en el siguiente formato: AAAA-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Por favor, seleccione una fecha de inicio del proyecto", "app.components.ProjectTemplatePreview.projectStartDate": "Fecha de inicio de tú proyecto", "app.components.ProjectTemplatePreview.projectTitle": "Título de tú proyecto", "app.components.ProjectTemplatePreview.projectTitleError": "Por favor, escribe el título del proyecto", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Por favor, escribe el título del proyecto en todos los idiomas", "app.components.ProjectTemplatePreview.projectsOverviewPage": "página de resumen de proyectos", "app.components.ProjectTemplatePreview.responseError": "<PERSON><PERSON>, algo salió mal.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Ver más plantillas", "app.components.ProjectTemplatePreview.successMessage": "El proyecto fue creado con éxito!", "app.components.ProjectTemplatePreview.typeProjectName": "Escriba el nombre del proyecto", "app.components.ProjectTemplatePreview.useTemplate": "Usar plantilla", "app.components.SeatInfo.additionalSeats": "Plazas adicionales", "app.components.SeatInfo.additionalSeatsToolTip": "Muestra el número de plazas adicionales que ha adquirido además de las \"plazas incluidas\".", "app.components.SeatInfo.adminSeats": "Plazas de administrador", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} plazas de administrador incluidas", "app.components.SeatInfo.adminSeatsTooltip1": "Los administradores están a cargo de la plataforma y tienen derechos de gestión de todas las carpetas y proyectos. Puedes {visitHelpCenter} para obtener más información sobre los distintos roles.", "app.components.SeatInfo.currentAdminSeatsTitle": "Plazas de administrador actuales", "app.components.SeatInfo.currentManagerSeatsTitle": "Plazas de gestores actuales", "app.components.SeatInfo.includedAdminToolTip": "Muestra el número de plazas disponibles para administradores incluidas en el contrato anual.", "app.components.SeatInfo.includedManagerToolTip": "Muestra el número de plazas disponibles para directivos incluidas en el contrato anual.", "app.components.SeatInfo.includedSeats": "Plazas incluidas", "app.components.SeatInfo.managerSeats": "Plazas para managers", "app.components.SeatInfo.managerSeatsTooltip": "Los gestores de carpetas/proyectos pueden gestionar un número ilimitado de carpetas/proyectos. Puedes {visitHelpCenter} para obtener más información sobre las distintas funciones.", "app.components.SeatInfo.managersIncludedText": "{manager<PERSON>eat<PERSON>} asientos para gestores incluidos", "app.components.SeatInfo.remainingSeats": "Plazas restantes", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Número total de plazas", "app.components.SeatInfo.totalSeatsTooltip": "Muestra el número total de plazas de su plan y las plazas adicionales que ha adquirido.", "app.components.SeatInfo.usedSeats": "Plazas utilizadas", "app.components.SeatInfo.view": "<PERSON>er", "app.components.SeatInfo.visitHelpCenter": "visite nuestro centro de ayuda", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Tu plan tiene {adminSeatsIncluded}. <PERSON><PERSON>do hayas utilizado todas las plazas, se añadirán plazas adicionales en \"Plazas adicionales\".", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Tu plan tiene {managerSeatsIncluded}, elegibles para gestores de carpetas y gestores de proyectos. Cuando hayas utilizado todas las plazas, se añadirán plazas adicionales en \"Plazas adicionales\".", "app.components.UserSearch.addModerators": "<PERSON><PERSON><PERSON>", "app.components.UserSearch.searchUsers": "Búsqueda de usuarios", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Mensaje de error alternativo", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "<PERSON>r defecto, se mostrará a los usuarios el siguiente mensaje de error:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Personalizar el mensaje de error", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "<PERSON><PERSON><PERSON> ofrecer un mensaje alternativo a este mensaje para cada idioma utilizando el cuadro de texto \"Mensaje de error alternativo\" que aparece más abajo. Si dejas la caja de texto vacía, se mostrará el mensaje que incluimos por defecto.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "<PERSON><PERSON><PERSON>", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "Esto es lo que verán los participantes cuando no cumplan los requisitos de participación.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Guardar mensaje de error", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "No hay ninguna pregunta seleccionada. Por favor, selecciona primero una pregunta.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "Sin respuesta", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} respuestas", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "Pregunta de la encuesta", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} hasta ahora", "app.components.admin.DatePhasePicker.Input.openEnded": "<PERSON>bie<PERSON>o", "app.components.admin.DatePhasePicker.Input.selectDate": "Selecciona la fecha", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Borrar fecha final", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "<PERSON><PERSON>r fecha de inicio", "app.components.admin.Graphs": "No hay datos disponibles con los filtros actuales.", "app.components.admin.Graphs.noDataShort": "No hay datos disponibles.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Comentarios a lo largo del tiempo", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Publicaciones a lo largo del tiempo", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Reacciones a lo largo del tiempo", "app.components.admin.InputManager.onePost": "Una entrada", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "<PERSON><PERSON><PERSON> <PERSON> picks offline", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Ajuste de votos offline", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "Esta opción te permite incluir datos de participación de otras fuentes, como votos en persona o en papel:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Se diferenciará visualmente de los votos digitales.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "Afectará a los resultados finales de la votación.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "No se reflejará en los cuadros de mando de los datos de participación.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Si la misma idea u opción es parte de distintas fases de votación dentro de un mismo proyecto, por favor tenga en cuenta que el contador de votos offline por opción no le permite dividir votos offline sobre la misma idea en base a distintas fases. ", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "Primero debes introducir el número total de participantes offline.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Total de participantes offline", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "Para calcular los resultados correctos, necesitamos conocer la <b>cantidad total de participantes offline para esta fase</b>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Indica sólo los que participaron offline. ", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "Modificado por {name}", "app.components.admin.PostManager.PostPreview.assignee": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.cancelEdit": "Cancelar la edición", "app.components.admin.PostManager.PostPreview.currentStatus": "Estado actual", "app.components.admin.PostManager.PostPreview.delete": "Eliminar", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "¿Estás seguro de que deseas eliminar esta entrada? Esta acción no se puede deshacer.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "¿Estás seguro de que deseas eliminar esta entrada? La entrada se eliminará de todas las fases del proyecto y no se podrá recuperar.", "app.components.admin.PostManager.PostPreview.edit": "<PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.noOne": "<PERSON>", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "El número de veces que esto se ha incluido en los presupuestos participativos de otros participantes.", "app.components.admin.PostManager.PostPreview.picks": "Selecciones: {picksNumber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Contador de reacciones:", "app.components.admin.PostManager.PostPreview.save": "Guardar", "app.components.admin.PostManager.PostPreview.submitError": "Error", "app.components.admin.PostManager.addFeatureLayer": "Añadir capa de características", "app.components.admin.PostManager.addFeatureLayerInstruction": "Copia la URL de la capa de características alojada en ArcGIS Online y pégala en la siguiente entrada:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Añade una nueva capa de características al mapa", "app.components.admin.PostManager.addWebMap": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.addWebMapInstruction": "Copia el ID del portal de tu Mapa Web de ArcGIS Online y pégalo en la entrada de abajo:", "app.components.admin.PostManager.allPhases": "Todas las fases", "app.components.admin.PostManager.allProjects": "Todos los proyectos", "app.components.admin.PostManager.allStatuses": "Todos los estatus", "app.components.admin.PostManager.allTopics": "Todos los temas", "app.components.admin.PostManager.anyAssignment": "Cualquier asignación", "app.components.admin.PostManager.assignedTo": "{assigneeName}", "app.components.admin.PostManager.assignedToMe": "Asignado a mí", "app.components.admin.PostManager.assignee": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.authenticationError": "Se ha producido un error de autenticación al intentar obtener esta capa. Comprueba la URL y que tu clave API de Esri tiene acceso a esta capa.", "app.components.admin.PostManager.automatedStatusTooltipText": "Este estado se actualiza automáticamente cuando se cumplen las condiciones", "app.components.admin.PostManager.bodyTitle": "Descripción", "app.components.admin.PostManager.cancel": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel2": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.co-sponsors": "Copatrocinadores", "app.components.admin.PostManager.comments": "Comentarios", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "Esto significa que perderás todos los datos asociados a estas entradas, como comentarios, reacciones y votos. Esta acción no se puede deshacer.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "¿Estás seguro de que quieres borrar estas entradas?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "Eliminar tema", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "Estás intentando eliminar esta idea de una fase en la que ha recibido votos. Si lo haces, estos votos se perderán. ¿Estás seguro de que quieres eliminar esta idea de esta fase?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "Los votos asociados a esta idea se perderán", "app.components.admin.PostManager.components.goToInputManager": "<PERSON>r al gestor de aportes", "app.components.admin.PostManager.components.goToProposalManager": "<PERSON>r al gestor de propuestas", "app.components.admin.PostManager.contributionFormTitle": "Editar contribución", "app.components.admin.PostManager.cost": "<PERSON><PERSON>", "app.components.admin.PostManager.createInput": "<PERSON><PERSON><PERSON> entrada", "app.components.admin.PostManager.createInputsDescription": "Crear un nuevo conjunto de entradas a partir de un proyecto anterior", "app.components.admin.PostManager.currentLat": "Latitud actual", "app.components.admin.PostManager.currentLng": "<PERSON><PERSON><PERSON> actual", "app.components.admin.PostManager.currentZoomLevel": "<PERSON>vel de zoom actual", "app.components.admin.PostManager.defaultEsriError": "Se ha producido un error al intentar obtener esta capa. Comprueba tu conexión de red y que la URL es correcta.", "app.components.admin.PostManager.delete": "Eliminar", "app.components.admin.PostManager.deleteAllSelectedInputs": "Eliminar {count} entradas", "app.components.admin.PostManager.deleteConfirmation": "¿Está seguro de que quiere eliminar esta capa?", "app.components.admin.PostManager.dislikes": "No me gusta", "app.components.admin.PostManager.edit": "<PERSON><PERSON>", "app.components.admin.PostManager.editProjects": "Editar proyectos", "app.components.admin.PostManager.editStatuses": "Editar estados", "app.components.admin.PostManager.editTags": "Editar etiquetas", "app.components.admin.PostManager.editedPostSave": "Guardar", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "La importación de datos de Esri ArcGIS Online es una función adicional. Habla con tu gestor de GS para desbloquearla.", "app.components.admin.PostManager.esriSideError": "Se ha producido un error en la aplicación ArcGIS. Espera unos minutos y vuelve a intentarlo más tarde.", "app.components.admin.PostManager.esriWebMap": "Mapa Web Esri", "app.components.admin.PostManager.exportAllInputs": "Exportar todos los aportes (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Exportar todos los comentarios (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Exportar todos los comentarios a este proyecto", "app.components.admin.PostManager.exportInputsProjects": "Exportar los aportes de este proyecto (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Exportar los aportes seleccionados (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Exportar los comentarios para los aportes seleccionados(.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Exportar votos por entrada (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Exportar votos por usuario (.xslx)", "app.components.admin.PostManager.exports": "Exportar", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "<PERSON><PERSON><PERSON> puedes cargar datos cartográficos como capas GeoJSON o importándolos de ArcGIS Online. Elimina cualquier capa GeoJSON actual si deseas añadir una capa de características.", "app.components.admin.PostManager.featureLayerTooltop": "Puedes encontrar la URL de la capa de características en la parte derecha de la página del elemento en ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Elige cómo la gente verá tu nombre", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Explica este cambio de estado", "app.components.admin.PostManager.fileUploadError": "No se han podido subir uno o varios archivos. Compruebe el tamaño y el formato del archivo e inténtelo de nuevo.", "app.components.admin.PostManager.formTitle": "Editar idea", "app.components.admin.PostManager.generalApiError2": "Se ha producido un error al intentar obtener este elemento. Por favor, comprueba que la URL o el ID del Portal son correctos y que tienes acceso a este artículo.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Sólo puedes subir datos de mapas como capas GeoJSON o importándolos de ArcGIS Online. Elimina cualquier dato de ArcGIS si deseas cargar una capa GeoJSON.", "app.components.admin.PostManager.goToDefaultMapView": "Ir al centro del mapa por defecto y hacer zoom", "app.components.admin.PostManager.hiddenFieldsLink": "campos ocultos", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Sugerencia: a<PERSON><PERSON> {hiddenFieldsLink} al configurar su encuesta Typeform para llevar un registro de quién ha respondido a su encuesta.", "app.components.admin.PostManager.import2": "Importar", "app.components.admin.PostManager.importError": "El archivo seleccionado no pudo ser importado porque no es un archivo GeoJSON válido", "app.components.admin.PostManager.importEsriFeatureLayer": "Importar capa de características de Esri", "app.components.admin.PostManager.importEsriWebMap": "Importar Mapa Web Esri", "app.components.admin.PostManager.importInputs": "Importar entradas", "app.components.admin.PostManager.imported": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.initiativeFormTitle": "Editar iniciativa", "app.components.admin.PostManager.inputCommentsExportFileName": "comentarios_contribucion", "app.components.admin.PostManager.inputImportProgress": "{importedCount} de {totalCount} {totalCount, plural, one {se ha importado la entrada} other {se ha importado la entrada}}. La importación aún está en curso, vuelve más tarde.", "app.components.admin.PostManager.inputManagerHeader": "Aportes", "app.components.admin.PostManager.inputs": "aportes", "app.components.admin.PostManager.inputsExportFileName": "aporte", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Mostrar solo entradas que necesiten comentarios", "app.components.admin.PostManager.issueFormTitle": "<PERSON>ar problema", "app.components.admin.PostManager.latestFeedbackMode": "Utilice la actualización más reciente como explicación", "app.components.admin.PostManager.layerAdded": "Capa añadida correctamente", "app.components.admin.PostManager.likes": "Me gusta", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Al mover esta información fuera de su proyecto actual se perderá la información sobre sus fases asignadas. ¿Desea continuar?", "app.components.admin.PostManager.mapData": "Datos del mapa", "app.components.admin.PostManager.multipleInputs": "Entradas de {ideaCount}", "app.components.admin.PostManager.newFeedbackMode": "Escribir una nueva actualización para explicar este cambio", "app.components.admin.PostManager.noFilteredResults": "Los filtros que seleccionaste no arrojaron ningún resultado", "app.components.admin.PostManager.noInputs": "Todavía no hay entradas", "app.components.admin.PostManager.noInputsDescription": "Añade tu propia opinión o parte de un proyecto de participación anterior.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 entradas} one {1 entrada} other {# entradas}} se importarán del proyecto y fase seleccionados. La importación se ejecutará en segundo plano, y las entradas aparecerán en el gestor de entradas una vez finalizada.", "app.components.admin.PostManager.noOne": "<PERSON>", "app.components.admin.PostManager.noProject": "Ningún proyecto", "app.components.admin.PostManager.officialFeedbackModal.author": "Autor", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "Elige cómo aparecerá tu nombre", "app.components.admin.PostManager.officialFeedbackModal.description": "Proporcionar información oficial ayuda a mantener la transparencia del proceso y fomenta la confianza en la plataforma.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Se requiere autor", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Se requiere comentario", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Comentarios oficiales", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Explica el motivo del cambio de estatus", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Publicar comentarios", "app.components.admin.PostManager.officialFeedbackModal.skip": "Saltar esta vez", "app.components.admin.PostManager.officialFeedbackModal.title": "Explica tu decisión", "app.components.admin.PostManager.officialUpdateAuthor": "Elige cómo la gente verá tu nombre", "app.components.admin.PostManager.officialUpdateBody": "Explica este cambio de estado", "app.components.admin.PostManager.offlinePicks": "Selecciones offline", "app.components.admin.PostManager.offlineVotes": "Votos offline", "app.components.admin.PostManager.onlineVotes": "Votaciones en línea", "app.components.admin.PostManager.optionFormTitle": "Editar opción", "app.components.admin.PostManager.participants": "Participantes", "app.components.admin.PostManager.participatoryBudgettingPicks": "Recoge", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Selecciones en línea", "app.components.admin.PostManager.pbItemCountTooltip": "El número de veces que esto se ha incluido en los presupuestos participativos de otros participantes.", "app.components.admin.PostManager.petitionFormTitle": "<PERSON>ar <PERSON>", "app.components.admin.PostManager.postedIn": "Publicado en {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Editar proyecto", "app.components.admin.PostManager.projectsTab": "Proyectos", "app.components.admin.PostManager.projectsTabTooltipContent": "Puedes arrastrar y soltar publicaciones para moverlas de un proyecto a otro. Ten en cuenta que para los proyectos de línea de tiempo, sigues teniendo que agregar la publicación a una fase específica.", "app.components.admin.PostManager.proposalFormTitle": "Editar propuesta", "app.components.admin.PostManager.proposedBudgetTitle": "Presupuesto propuesto", "app.components.admin.PostManager.publication_date": "Publicado en", "app.components.admin.PostManager.questionFormTitle": "<PERSON><PERSON>", "app.components.admin.PostManager.reactions": "Reacciones", "app.components.admin.PostManager.resetFiltersButton": "Restablecer los filtros", "app.components.admin.PostManager.resetInputFiltersDescription": "Restablece los filtros para ver todas las entradas.", "app.components.admin.PostManager.saved": "Guardado", "app.components.admin.PostManager.screeningTooltip": "La pre-revisión de aportes previa a su publicación no está incluida en tu plan actual. Habla con tu Gestor de Éxito Gubernamental o con el administrador para desbloquearla.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "La pre-revisión de aportes está desactivada para esta fase. Ve a la configuración de la fase para activarla", "app.components.admin.PostManager.selectAPhase": "Selecciona una fase", "app.components.admin.PostManager.selectAProject": "Selecciona un proyecto", "app.components.admin.PostManager.setAsDefaultMapView": "Guardar el punto central y el nivel de zoom actual como valores predeterminados del mapa", "app.components.admin.PostManager.startFromPastInputs": "Partir de entradas pasadas", "app.components.admin.PostManager.statusChangeGenericError": "Se ha producido un error. Vuelva a intentarlo más tarde o póngase en contacto con el servicio de asistencia.", "app.components.admin.PostManager.statusChangeSave": "Modificar el estado", "app.components.admin.PostManager.statusesTab": "Estados", "app.components.admin.PostManager.statusesTabTooltipContent": "Cambia el estado de una publicación usando arrastrar y soltar. El autor original y otros colaboradores recibirán una notificación del cambio de estado.", "app.components.admin.PostManager.submitApiError": "Se ha producido un problema al enviar el formulario. Compruebe si hay algún error e inténtelo de nuevo.", "app.components.admin.PostManager.timelineTab": "Fases", "app.components.admin.PostManager.timelineTabTooltipText": "Arrastra y suelta las entradas para copiarlas en diferentes fases del proyecto.", "app.components.admin.PostManager.title": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.topicsTab": "<PERSON><PERSON>", "app.components.admin.PostManager.topicsTabTooltipText": "Agrega temas a un aporte usando arrastrar y soltar.", "app.components.admin.PostManager.view": "<PERSON>er", "app.components.admin.PostManager.votes": "Votos", "app.components.admin.PostManager.votesByInputExportFileName": "votos_por_entrada", "app.components.admin.PostManager.votesByUserExportFileName": "votos_por_usuario", "app.components.admin.PostManager.webMapAlreadyExists": "<PERSON><PERSON><PERSON> puedes añadir un Mapa Web cada vez. Elimina el actual para importar uno diferente.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "<PERSON><PERSON><PERSON> puedes cargar datos de mapas como capas GeoJSON o importándolos de ArcGIS Online. Elimina cualquier capa GeoJSON actual si deseas conectar un Mapa Web.", "app.components.admin.PostManager.webMapTooltip": "Puedes encontrar el ID del portal Mapa Web en tu página de elementos de ArcGIS Online, a la derecha.", "app.components.admin.PostManager.xDaysLeft": "{x, plural,=0 {Queda menos de un día} one {Queda un día} other {Quedan # días}}", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "<PERSON><PERSON><PERSON>", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "Sí, eliminar los resultados de la encuesta", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "No se puede deshacer", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Elimina los resultados de la encuesta", "app.components.admin.ProjectEdit.survey.downloadResults2": "Descargar los resultados de la encuesta", "app.components.admin.ReportExportMenu.FileName.fromFilter": "de", "app.components.admin.ReportExportMenu.FileName.groupFilter": "grupo", "app.components.admin.ReportExportMenu.FileName.projectFilter": "proyecto", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tema", "app.components.admin.ReportExportMenu.FileName.untilFilter": "hasta", "app.components.admin.ReportExportMenu.downloadPng": "Descargar como PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Descargar como SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Descargar Excel", "app.components.admin.SlugInput.regexError": "El slug sólo puede contener letras regulares, <PERSON><PERSON><PERSON><PERSON> (a-z), n<PERSON><PERSON><PERSON> (0-9) y guiones (-). El primer y el último carácter no pueden ser guiones, y los guiones consecutivos (-) no pueden ser utilizados.", "app.components.admin.TerminologyConfig.saveButton": "Guardar", "app.components.admin.commonGroundInputManager.title": "Titulo", "app.components.admin.seatSetSuccess.admin": "Administrador", "app.components.admin.seatSetSuccess.allDone": "Todo hecho", "app.components.admin.seatSetSuccess.close": "<PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.manager": "<PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.orderCompleted": "Pedido realizado", "app.components.admin.seatSetSuccess.reflectedMessage": "Los cambios en su plan se reflejarán en su próximo ciclo de facturación.", "app.components.admin.seatSetSuccess.rightsGranted": "Se han otorgado derechos {seatType}  a los usuarios seleccionados.", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "¿Estás seguro de que quieres eliminar todos los resultados de la encuesta?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "Este método de participación está en fase beta. Lo estamos desplegando gradualmente para recoger opiniones y mejorar la experiencia.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Recopilar opiniones sobre un documento es una función personalizada, y no está incluida en tu licencia actual. Ponte en contacto con tu Manager de GovSuccess para obtener más información.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Propuesta", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Se requiere el número de días", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "Número de días para alcanzar el número mínimo de votos", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Puede encontrar más información sobre cómo insertar un enlace para Google Forms en {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "art<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Idea", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Initiative", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "¿Cómo debería llamarse una entrada?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Respuesta", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Proporciona aquí el enlace a tu documento Konveio. Lee nuest<PERSON> {supportArticleLink} para obtener más información sobre cómo configurar un documento de Konveio.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "Esto no está incluido en tu plan actual. Ponte en contacto con tu Gestor de Éxito Gubernamental o administrador para desbloquearlo.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Se requiere un presupuesto máximo", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "El número máximo de votos por opción debe ser inferior o igual al número total de votos", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Se requiere un número máximo de votos", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "Mensajería", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "El presupuesto mínimo no puede ser mayor que el máximo", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Se requiere un presupuesto mínimo", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "El número mínimo de votos no puede ser mayor que el número máximo", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Se requiere un número mínimo de votos", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Falta la fecha final", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Falta la fecha de inicio", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Opción", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Pestaña Gestor de Entradas", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Configura las opciones de votación en la pestaña Gestor de entradas después de crear una fase.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Configura las opciones de votación en {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Opciones de participación", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "Participantes", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Petición", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Administradores y gestores", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Anotar documento:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} pueden participar en esta fase.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Comentario:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Mapa de consenso", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "<PERSON><PERSON><PERSON> fase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "<PERSON><PERSON>, eliminar esta fase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "¿Estás seguro que desea eliminar esta fase?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "Se eliminarán todos los datos relativos a esta fase. Esto no se puede deshacer.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Fase de anotación de documentos", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Todo el mundo", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "Fase de encuesta externa", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Fase de ideación", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "En fase de estudio de la plataforma", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Fase de información", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "Derechos mixtos", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "Sin fecha de finalización", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Fase de sondeo", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Fase de propuestas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>Reaccion<PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Inscrito en el evento:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Usuarios", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b>Enviar entradas:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>Haciendo una encuesta:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b><PERSON><PERSON> una encuesta:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Usuarios con correo electrónico confirmado", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b><PERSON><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Fase de voluntariado", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Votación:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Fase de votación", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "¿Quién puede participar?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Las aportaciones no serán visibles hasta que un administrador las revise y apruebe. Los autores no pueden editar las aportaciones después de que hayan sido revisadas o hayan reaccionado a ellas.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "<PERSON><PERSON>lo administradores", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "Cualquiera que tenga el enlace puede interactuar con el borrador del proyecto", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Aprobar permite a los Jefes de Proyecto publicar el proyecto.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "Aprobado por {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Archivado", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Editar descripción", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "Todo el mundo", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Grupos", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "Oculto", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Votantes offline", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Sólo los administradores{inFolder, select, true { o los Gestores de Carpetas} other {}} pueden publicar el proyecto", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 participante} other {{participantsCount} participantes}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Participantes en métodos integrados (por ejemplo, encuestas externas)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Seguidores de un proyecto", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Nota: Activar los permisos de participación anónima o abierta puede permitir que los usuarios participen varias veces, lo que daría lugar a datos de usuario engañosos o incompletos.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "Los participantes <b>no incluyen</b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Entre los participantes figuran:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Inscritos en el evento", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "Usuarios interactuando con los métodos de Go Vocal", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "A la espera de aprobación", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Se ha notificado a los revisores del proyecto.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "Público", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Publicar", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Publicado - Activo", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Publicado - Finalizado", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Actualizar enlace de vista previa del proyecto", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Regenera el enlace de previsualización del proyecto. Esto invalidará el enlace anterior.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Los enlaces antiguos dejarán de funcionar, pero puedes generar uno nuevo en cualquier momento.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "¿Estás seguro? Esto desactivará el enlace actual", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "<PERSON><PERSON>, actual<PERSON>r enlace", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Solicitar aprobación", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "El proyecto debe ser aprobado por un administrador{inFolder, select, true { o uno de los Gestores de Carpetas} other {}} antes de que puedas publicarlo. Haz clic en el botón de abajo para solicitar la aprobación.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Compartir", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "<PERSON><PERSON><PERSON> el enlace", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "Link copied", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Compartir enlaces privados no está incluido en tu plan actual. Habla con tu Gestor de Éxito Gubernamental o con el administrador para desbloquearlo.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Compartir este proyecto", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "<PERSON><PERSON><PERSON> tiene acceso", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "<PERSON>er", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Proyecto", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Propuesta", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "Pregunta", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Número mínimo de votos para ser considerado", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Se requiere un número mínimo de votos", "app.components.app.containers.AdminPage.ProjectEdit.report": "Reporte", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Exigir la pre-revisión de aportes antes de su publicación", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Con una línea de tiempo", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Tráfico", "app.components.formBuilder.cancelMethodChange1": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.changeMethodWarning1": "Cambiar de método puede llevar a ocultar cualquier dato de entrada generado o recibido mientras se utilizaba el método anterior.", "app.components.formBuilder.changingMethod1": "Cambio de método", "app.components.formBuilder.confirmMethodChange1": "Sí, continúa", "app.components.formBuilder.copySurveyModal.cancel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.description": "Esto copiará todas las preguntas y la lógica sin las respuestas.", "app.components.formBuilder.copySurveyModal.duplicate": "Dup<PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "No se han encontrado fases adecuadas en este proyecto", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "No se ha seleccionado ninguna fase. Por favor, selecciona primero una fase.", "app.components.formBuilder.copySurveyModal.noProject": "Ningún proyecto", "app.components.formBuilder.copySurveyModal.noProjectSelected": "No hay ningún proyecto seleccionado. Por favor, selecciona primero un proyecto.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "Ya has guardado los cambios de esta encuesta. Si duplicas otra encuesta, los cambios se perderán.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Fase de encuesta", "app.components.formBuilder.copySurveyModal.title": "Selecciona una encuesta para duplicar", "app.components.formBuilder.editWarningModal.addOrReorder": "Añadir o reordenar preguntas", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Tus datos de respuesta pueden ser inexactos", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Editar texto", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "¿Arreglar un error tipográfico? No afectará a tus datos de respuesta", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Perderás los datos de respuesta vinculados a esa pregunta", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Bo<PERSON>r una pregunta", "app.components.formBuilder.editWarningModal.exportYouResponses2": "exporta tus respuestas.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Advertencia: Podrías perder los datos de respuesta para siempre. Antes de continuar,", "app.components.formBuilder.editWarningModal.noCancel": "No, cancela", "app.components.formBuilder.editWarningModal.title4": "Editar encuesta en directo", "app.components.formBuilder.editWarningModal.yesContinue": "Sí, continúa", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "configuración de los derechos de acceso a esta encuesta", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "'Campos demográficos en el formulario de la encuesta' está activado. Cuando se muestre el formulario de la encuesta, las preguntas demográficas configuradas se añadirán en una nueva página inmediatamente antes del final de la encuesta. Estas preguntas pueden modificarse en la página {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "configuración de los derechos de acceso para esta fase.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "No se exigirá a los encuestados que se registren o inicien sesión para enviar las respuestas de la encuesta, lo que puede dar lugar a envíos duplicados", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "Al omitir el paso de registro/inicio de sesión, aceptas no recopilar información demográfica sobre los encuestados, lo que puede afectar a tus capacidades de análisis de datos.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "Esta encuesta está configurada para permitir el acceso a \"Cualquiera\" en la pestaña Derechos de acceso.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "Si quieres cambiarlo, puedes hacerlo en {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Estás haciendo las siguientes preguntas demográficas a los encuestados a través del paso de registro/inicio de sesión.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "Para agilizar la recopilación de información demográfica y garantizar su integración en tu base de datos de usuarios, te aconsejamos que incorpores cualquier pregunta demográfica directamente en el proceso de registro/inicio de sesión. Para ello, utiliza {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Pide a los usuarios que sigan áreas o temas", "app.components.onboarding.followHelperText": "Esto activa un paso en el proceso de registro en el que los usuarios podrán seguir las áreas o temas que selecciones a continuación", "app.components.onboarding.followPreferences": "<PERSON><PERSON><PERSON>", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} dentro del plan, {noOfAdditionalSeats} adicional", "app.components.seatsWithinPlan.seatsWithinPlanText": "Plazas dentro del plan", "app.containers.Admin.Campaigns.campaignFrom": "De:", "app.containers.Admin.Campaigns.campaignTo": "Para:", "app.containers.Admin.Campaigns.customEmails": "Correos electrónicos personalizados", "app.containers.Admin.Campaigns.customEmailsDescription": "Envía correos electrónicos personalizados y comprueba las estadísticas.", "app.containers.Admin.Campaigns.noAccess": "<PERSON><PERSON><PERSON>, pero parece que no tienes acceso a la sección de los correos electrónicos", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Correos electrónicos automatizados", "app.containers.Admin.Insights.tabReports": "Reportes", "app.containers.Admin.Invitations.a11y_removeInvite": "Eliminar invitación", "app.containers.Admin.Invitations.addToGroupLabel": "Añade estas personas a grupos manuales específicos", "app.containers.Admin.Invitations.adminLabel1": "Dar derechos de administrador a los invitados", "app.containers.Admin.Invitations.adminLabelTooltip": "<PERSON>uando se activa, las personas que reciben y aceptan su invitación también tendrán acceso a todas las configuraciones de administrador de la plataforma.", "app.containers.Admin.Invitations.configureInvitations": "3. Configurar las invitaciones", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "No hay invitaciones que calcen con tu búsqueda", "app.containers.Admin.Invitations.deleteInvite": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.deleteInviteConfirmation": "¿Estás seguro de que quieres eliminar esta invitación?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Al cancelar una invitación a una persona, podrá enviarla nuevamente más adelante.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. <PERSON><PERSON><PERSON> y llenar el modelo", "app.containers.Admin.Invitations.downloadTemplate": "Des<PERSON>gar el modelo", "app.containers.Admin.Invitations.email": "Correo electrónico", "app.containers.Admin.Invitations.emailListLabel": "Ingrese manualmente los correos de las personas que quieres invitar. Separa sus direcciones por una coma.", "app.containers.Admin.Invitations.exportInvites": "Exportar la lista de invitaciones", "app.containers.Admin.Invitations.fileRequirements": "Importante: No se puede eliminar ninguna columna del modelo de importación - deja las columnas no utilizadas vacías. Antes de importar el archivo, por favor, asegúrate de que no hay direcciones de correo electrónico duplicadas, ya que esto puede causar errores con las invitaciones.", "app.containers.Admin.Invitations.filetypeError": "Archivo de tipo incorrecto. Sólo XLSX son soportados.", "app.containers.Admin.Invitations.groupsPlaceholder": "No hay grupos seleccionados", "app.containers.Admin.Invitations.helmetDescription": "Invita usuarios a la plataforma", "app.containers.Admin.Invitations.helmetTitle": "Dashboard para administrar invitaciones", "app.containers.Admin.Invitations.importOptionsInfo": "Estas opciones se tomarán en cuenta sólo cuando no están definidas en el archivo de Excel., por favor visite {supportPageLink} para obtener más información.", "app.containers.Admin.Invitations.importTab": "Importar direcciones de correo electrónico", "app.containers.Admin.Invitations.invitationExpirationWarning": "Ten en cuenta que las invitaciones caducan a los 30 días. Transcurrido este plazo, puedes volver a enviarlas.", "app.containers.Admin.Invitations.invitationOptions": "Opciones de invitación", "app.containers.Admin.Invitations.invitationSubtitle": "Invitar a las personas que aún no se han registrado en la plataforma. Importa sus direcciones de correo electrónico colocándolas en la plantilla de importación o introduzce las direcciones de correo electrónico manualmente. Si lo desea, añade un mensaje personal, dé a las personas derechos adicionales o añádalos a un grupo manual.", "app.containers.Admin.Invitations.invitePeople": "Invitaciones", "app.containers.Admin.Invitations.inviteStatus": "Estado", "app.containers.Admin.Invitations.inviteStatusAccepted": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteStatusPending": "Pendiente", "app.containers.Admin.Invitations.inviteTextLabel": "Añadir un mensaje personal a la invitación", "app.containers.Admin.Invitations.invitedSince": "Invitado el", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Selecciona el lenguaje de la invitación", "app.containers.Admin.Invitations.moderatorLabel": "<PERSON> a estas personas permisos de administrador de proyecto", "app.containers.Admin.Invitations.moderatorLabelTooltip": "<PERSON>uando se activa, las personas que acepten tu invitación también tendrán derechos de administrador de proyecto sobre el (los) proyecto (s) seleccionado (s). Más información sobre el rol de administrador de proyecto {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "aquí", "app.containers.Admin.Invitations.name": "Nombre", "app.containers.Admin.Invitations.processing": "Envíando invitaciones. Espera...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "No hay proyecto (s) seleccionados", "app.containers.Admin.Invitations.save": "Envía tus invitaciones", "app.containers.Admin.Invitations.saveErrorMessage": "Se ha producido uno o más errores.\n      por lo tanto las invitaciones no se enviaron.\n      por favor, corrija el (los) error(es) enumerados a continuación y vuelva a intentarlo.", "app.containers.Admin.Invitations.saveSuccess": "¡Perfecto!", "app.containers.Admin.Invitations.saveSuccessMessage": "Invitación enviada con éxito.", "app.containers.Admin.Invitations.supportPage": "página de soporte", "app.containers.Admin.Invitations.supportPageLinkText": "Visita la página de apoyo", "app.containers.Admin.Invitations.tabAllInvitations": "Todas las invitaciones", "app.containers.Admin.Invitations.tabInviteUsers": "Invitar a la gente", "app.containers.Admin.Invitations.textTab": "Introducir los correos electrónicos", "app.containers.Admin.Invitations.unknownError": "Algo salió mal. Por favor Inténtalo más tarde.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Subar el modelo completo", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} si quieres más información sobre todas las columnas soportadas en el modelo de importación.", "app.containers.Admin.Moderation.all": "Todos", "app.containers.Admin.Moderation.belongsTo": "Pertenece a", "app.containers.Admin.Moderation.collapse": "Colapso", "app.containers.Admin.Moderation.comment": "Comentario", "app.containers.Admin.Moderation.commentDeletionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "Bo<PERSON>r", "app.containers.Admin.Moderation.confirmCommentDeletion": "¿Estás seguro de que quieres borrar este comentario? Esto es permanente y no se puede deshacer.", "app.containers.Admin.Moderation.content": "Contenido", "app.containers.Admin.Moderation.date": "<PERSON><PERSON>", "app.containers.Admin.Moderation.deleteComment": "Eliminar comentario", "app.containers.Admin.Moderation.goToComment": "A<PERSON>r este comentario en una nueva pestaña", "app.containers.Admin.Moderation.goToPost": "Abrir esta publicación en una nueva pestaña", "app.containers.Admin.Moderation.goToProposal": "<PERSON><PERSON>r esta propuesta en una nueva pestaña", "app.containers.Admin.Moderation.markFlagsError": "No se han podido marcar los ítems. Intentalo nuevamente.", "app.containers.Admin.Moderation.markNotSeen": "<PERSON>a {selectedItemsCount, plural, one {# item} other {# items}} como no visto ", "app.containers.Admin.Moderation.markSeen": "<PERSON>a {selectedItemsCount, plural, one {# item} other {# items}} como visto ", "app.containers.Admin.Moderation.moderationsTooltip": "Esta página te permite verificar rápidamente todas los aportes nuevos que se han agregado a tu plataforma, incluidas las publicaciones y los comentarios. Puedes marcar las publicaciones como \"vistas\" para que otros sepan qué aportes faltan por procesar.", "app.containers.Admin.Moderation.noUnviewedItems": "No hay elementos no vistos", "app.containers.Admin.Moderation.noViewedItems": "No hay elementos vistos", "app.containers.Admin.Moderation.pageTitle1": "Feed", "app.containers.Admin.Moderation.post": "Aporte", "app.containers.Admin.Moderation.profanityBlockerSetting": "Bloqueador de insultos", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Bloquear los aportes, propuestas y comentarios que contengan palabras ofensivas.", "app.containers.Admin.Moderation.project": "Proyecto", "app.containers.Admin.Moderation.read": "Visto", "app.containers.Admin.Moderation.readMore": "<PERSON><PERSON>", "app.containers.Admin.Moderation.removeFlagsError": "No se ha podido eliminar la(s) advertencia(s). Vuelve a intentarlo.", "app.containers.Admin.Moderation.rowsPerPage": "<PERSON>las por página", "app.containers.Admin.Moderation.settings": "Configuración", "app.containers.Admin.Moderation.settingsSavingError": "No se ha podido guardar. Intenta cambiar la configuración de nuevo.", "app.containers.Admin.Moderation.show": "Mostrar", "app.containers.Admin.Moderation.status": "Estado", "app.containers.Admin.Moderation.successfulUpdateSettings": "La configuración se ha actualizado correctamente.", "app.containers.Admin.Moderation.type": "Tipo", "app.containers.Admin.Moderation.unread": "No visto", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Esta página consta de las siguientes secciones. Puede activarlas/desactivarlas y editarlas según sea necesario.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Secciones", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "<PERSON><PERSON> p<PERSON><PERSON>a", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "No se muestra en la página", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Se muestra en la página", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Archivos adjuntos", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Añada archivos (máx. 50 MB) que estarán disponibles para su descarga desde la página.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Sección de información inferior", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Añada su propio contenido en la sección personalizable de la parte inferior de la página.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Lista de eventos", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Mostrar eventos relacionados con los proyectos.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Banner principal", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Personalice la imagen y el texto del banner de la página.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Lista de proyectos", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Muestra los proyectos en función de la configuración de su página. Puede previsualizar los proyectos que se mostrarán.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Sección de información superior", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Añada su propio contenido en la sección personalizable de la parte superior de la página.", "app.containers.Admin.PagesAndMenu.addButton": "Añadir a la barra de navegación", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Nombre en la barra de navegación", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "¿Está seguro de que quiere eliminar esta página? Este paso no podrá deshacerse. ", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Pon un título para todas las lenguas", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Otras páginas disponibles", "app.containers.Admin.PagesAndMenu.components.savePage": "Guardar página", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "Página guardada con éxito", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Archivos adjuntos (máximo 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "¡Perfecto", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Contenido", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "No se pueden guardar los archivos adjuntos", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Los archivos no deben superar los 50Mb. Los archivos añadidos se mostrarán en la parte inferior de esta página", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Archivos adjuntos guardados", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Archivos adjuntos | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Archivos adjuntos", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Guardar y habilitar los archivos adjuntos", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Guardar archivos adjuntos", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Proporcionar contenido para todos los idiomas", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "¡Perfecto", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Contenido", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "No se ha podido guardar la sección de información inferior", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Sección de información inferior guardada", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Sección de información inferior", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Guardar y habilitar la sección de información inferior", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Guardar la sección de información inferior", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "La creación de páginas personalizadas no está incluida en tu licencia actual. Ponte en contacto con tu gestor de GovSuccess para obtener más información al respecto.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Seleccione al menos una etiqueta", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "¡Perfecto", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "Por etiqueta(s)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Mostrar proyectos por etiqueta o área no forma parte de tu licencia actual. Ponte en contacto con tu Gestor de GovSuccess o adminstrador para obtener más información al respecto.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Contenido", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Editar página personalizada", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Proyectos vinculados", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Seleccione qué proyectos y eventos relacionados pueden mostrarse en la página.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Página creada con éxito", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "Página guardada con éxito", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Página personalizada guardada", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Título en la barra de navegación", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "<PERSON><PERSON><PERSON> página personalizada | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "<PERSON><PERSON><PERSON> página personalizada", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Configuración de la página", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Guardar página personalizada", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Seleccione un área", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Etiquetas seleccionadas", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "El slug sólo puede contener letras regulares, <PERSON><PERSON><PERSON><PERSON> (a-z), n<PERSON><PERSON><PERSON> (0-9) y guiones (-). El primer y el último carácter no pueden ser guiones, y los guiones consecutivos (-) no pueden ser utilizados.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Debe introducir un slug", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Introduzca un título en cada idioma", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Ingresar un título", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Ver página personalizada", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "Botón", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Editar página personalizada | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Contenido de la página", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "En los proyectos de una sola fase, si la fecha de finalización está vacía y no se rellena la descripción, no se mostrará una línea de tiempo en la página del proyecto.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "No hay proyectos disponibles basados en su {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Este proyecto no tiene ningún filtro de etiqueta o área, por lo que no se mostrará ningún proyecto.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Lista de proyectos | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "configuración de página", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Lista de proyectos", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Los siguientes proyectos se mostrarán en esta página en función de su {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "PREDETERMINADO", "app.containers.Admin.PagesAndMenu.deleteButton": "Eliminar", "app.containers.Admin.PagesAndMenu.editButton": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "¡Perfecto", "app.containers.Admin.PagesAndMenu.heroBannerError": "No se ha podido guardar el banner principal", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Banner principal guardado", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Guardar el banner principal", "app.containers.Admin.PagesAndMenu.homeTitle": "Página de inicio", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Proporcionar contenido para al menos un idioma", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "<PERSON><PERSON><PERSON> puedes a<PERSON><PERSON> hasta 5 elementos a la barra de navegación", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Páginas y menú | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Eliminar de la barra de navegación", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Guardar y habilitar el banner principal", "app.containers.Admin.PagesAndMenu.title": "Páginas y menú", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "¡Perfecto", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Contenido", "app.containers.Admin.PagesAndMenu.topInfoError": "No se ha podido guardar la sección de información superior", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Sección de información superior guardada", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Sección de información superior | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Sección de información superior", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Guardar y habilitar la sección de información superior", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Guardar la sección de información superior", "app.containers.Admin.PagesAndMenu.viewButton": "<PERSON>er", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "Edad", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Comunidad", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Resumen ejecutivo", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Indicadores de inclusión de alto nivel", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "La siguiente sección esboza los indicadores de inclusión, destacando tus progresos hacia el fomento de una plataforma de participación más inclusiva y representativa.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "participantes en este proyecto", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Indicadores de participación de alto nivel", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "La siguiente sección resume los indicadores clave de participación para el intervalo de tiempo seleccionado, proporcionando una visión general de las tendencias de participación y las métricas de rendimiento.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Proyectos", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "proyectos publicados", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Informe de la plataforma", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "Tus proyectos", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "La siguiente sección ofrece una visión general de los proyectos públicamente visibles que coinciden con el intervalo de tiempo seleccionado, los métodos más utilizados en estos proyectos y las métricas relativas a la cantidad total de participación.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Calendario de inscripciones", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Usuarios bloqueados", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Gestionar usuarios bloqueados.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Eliminar grupo", "app.containers.Admin.Users.GroupsHeader.editGroup": "Editar grupo", "app.containers.Admin.Users.GroupsPanel.admins": "Administradores", "app.containers.Admin.Users.GroupsPanel.allUsers": "Usuarios", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Grupos", "app.containers.Admin.Users.GroupsPanel.managers": "<PERSON><PERSON><PERSON> de proyecto", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Elementos asignados", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Administrar todos los usuarios inscritos en su plataforma. Crear grupos normales o inteligentes para segmentarlos.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Invitación pendiente", "app.containers.Admin.Users.admin": "Administrador", "app.containers.Admin.Users.assign": "<PERSON><PERSON><PERSON> a mis gastos", "app.containers.Admin.Users.assignedItems": "Elementos asignados para {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "Comprar un asiento adicional", "app.containers.Admin.Users.changeUserRights": "Modificar los derechos de los usuarios", "app.containers.Admin.Users.confirm": "Confirmar", "app.containers.Admin.Users.confirmAdminQuestion": "¿Está seguro de que quiere dar a {name} derechos de administrador de la plataforma?", "app.containers.Admin.Users.confirmNormalUserQuestion": "¿Está seguro de que quiere establecer a {name} como usuario normal?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "¿Estás seguro de que quieres configurar {name} como usuario normal? Tenga en cuenta que perderá los derechos de administrador de todos los proyectos y carpetas que tenga asignados tras la confirmación.", "app.containers.Admin.Users.deleteUser": "Eliminar este usuario", "app.containers.Admin.Users.email": "Correo electrónico", "app.containers.Admin.Users.folder": "Carpeta", "app.containers.Admin.Users.folderManager": "Administradores de carpetas", "app.containers.Admin.Users.helmetDescription": "Lista de usuarios de administración backoffice", "app.containers.Admin.Users.helmetTitle": "Administrador - panel de usuarios", "app.containers.Admin.Users.inviteUsers": "Invitar a usuarios", "app.containers.Admin.Users.joined": "Se unió a", "app.containers.Admin.Users.lastActive": "Último activo", "app.containers.Admin.Users.name": "Nombre", "app.containers.Admin.Users.noAssignedItems": "No hay elementos asignados", "app.containers.Admin.Users.options": "Opciones", "app.containers.Admin.Users.permissionToBuy": "Para dar a {name} derechos de administrador, necesita comprar 1 puesto adicional.", "app.containers.Admin.Users.platformAdmin": "Administrador de plataforma", "app.containers.Admin.Users.projectManager": "G<PERSON>nte de proyectos", "app.containers.Admin.Users.reachedLimitMessage": "Ha alcanzado el límite de puestos en su plan, se añadirá 1 puesto adicional para {name}.", "app.containers.Admin.Users.registeredUser": "Usuario registrado", "app.containers.Admin.Users.remove": "Eliminar", "app.containers.Admin.Users.removeModeratorFrom": "El usuario está moderando la carpeta a la que pertenece este proyecto. Elimina la asignación de \"{folderTitle}\" en su lugar.", "app.containers.Admin.Users.role": "Rol", "app.containers.Admin.Users.seeProfile": "Ver el perfil de este usuario", "app.containers.Admin.Users.selectPublications": "Selecciona proyectos o carpetas", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Escribe para buscar", "app.containers.Admin.Users.setAsAdmin": "Establecer como administrador", "app.containers.Admin.Users.setAsNormalUser": "Establecer como usuario normal", "app.containers.Admin.Users.setAsProjectModerator": "Establecer como jefe de proyecto", "app.containers.Admin.Users.setUserAsProjectModerator": "<PERSON><PERSON><PERSON> {name} como director del proyecto", "app.containers.Admin.Users.userBlockModal.allDone": "Todo hecho", "app.containers.Admin.Users.userBlockModal.blockAction": "Bloquear usuario", "app.containers.Admin.Users.userBlockModal.blockInfo1": "El contenido de este usuario no será eliminado a través de esta acción. No olvides moderar su contenido si es necesario.", "app.containers.Admin.Users.userBlockModal.blocked": "Bloqueado", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Este usuario está bloqueado desde {from}. El bloqueo dura hasta {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "¿Estás seguro de que quieres desbloquear {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} se bloquea hasta {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 día} other {{numberOfDays} días}}", "app.containers.Admin.Users.userBlockModal.header": "Bloquear usuario", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Motivo", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Esto se comunicará al usuario bloqueado.", "app.containers.Admin.Users.userBlockModal.subtitle1": "El usuario seleccionado no podrá acceder a la plataforma para {daysBlocked}. Si deseas revertir esta situación, puedes desbloquearlo de la lista de usuarios bloqueados.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Desb<PERSON>que<PERSON>", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON>, quiero desbloquear a este usuario", "app.containers.Admin.Users.userDeletionConfirmation": "¿Eliminar permanentemente este usuario?", "app.containers.Admin.Users.userDeletionFailed": "Error al borrar este usuario, por favor, inténtelo de nuevo.", "app.containers.Admin.Users.userDeletionProposalVotes": "Esto también borrará cualquier voto de este usuario en propuestas que aún estén abiertas a votación.", "app.containers.Admin.Users.userExportFileName": "usuarios_export", "app.containers.Admin.Users.userInsights": "Conocimiento del usuario", "app.containers.Admin.Users.youCantDeleteYourself": "No puedes eliminar tu propia cuenta a través de la página de administración de usuario", "app.containers.Admin.Users.youCantUnadminYourself": "No puedes terminar tu papel como administrador ahora", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Barómetro", "app.containers.Admin.communityMonitor.healthScore": "Índice de bienestar", "app.containers.Admin.communityMonitor.healthScoreDescription": "Esta puntuación es la media de todas las preguntas de la escala de sentimientos contestadas por los participantes en el periodo seleccionado.", "app.containers.Admin.communityMonitor.lastQuarter": "último trimestre", "app.containers.Admin.communityMonitor.liveMonitor": "Barómetro en directo", "app.containers.Admin.communityMonitor.noResults": "No hay resultados para este periodo.", "app.containers.Admin.communityMonitor.noSurveyResponses": "No hay respuestas a la encuesta", "app.containers.Admin.communityMonitor.participants": "Participantes", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Informes", "app.containers.Admin.communityMonitor.settings": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "El barómetro de satisfacción ciudadana está aceptando respuestas.", "app.containers.Admin.communityMonitor.settings.accessRights2": "Derechos de acceso", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "Después de que un usuario registre su asistencia a un evento, envíe un voto o vuelva a la página de un proyecto tras enviar una encuesta.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Los administradores del barómetro de satisfacción ciudadana pueden acceder y gestionar todos los ajustes y datos del barómetro.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Administradores del barómetro", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Los administradores pueden editar la encuesta y los permisos del barómetro de satisfacción ciudadana, ver las respuestas y crear informes.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "El valor de frecuencia por defecto es 100%.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Frecuencia de la ventana emergente (de 0 a 100)", "app.containers.Admin.communityMonitor.settings.management2": "Gestión", "app.containers.Admin.communityMonitor.settings.popup": "Ventana emergente", "app.containers.Admin.communityMonitor.settings.popupDescription3": "Periódicamente se muestra una ventana emergente a los residentes animándoles a rellenar el barómetro de satisfacción ciudadana. Puedes ajustar la frecuencia, que determina el porcentaje de residentes que verán aleatoriamente la ventana emergente cuando se cumplan las condiciones que se indican a continuación.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Configuración de la ventana emergente", "app.containers.Admin.communityMonitor.settings.preview": "Vista previa", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "El usuario no ha rellenado ya la encuesta en los 3 meses anteriores.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "El usuario no ha visto la ventana emergente en los 3 meses anteriores.", "app.containers.Admin.communityMonitor.settings.save": "Guardar", "app.containers.Admin.communityMonitor.settings.saved": "Guardado", "app.containers.Admin.communityMonitor.settings.settings": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.survey2": "Encuesta", "app.containers.Admin.communityMonitor.settings.surveySettings3": "Ajustes generales", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Al cargar la Página de Inicio o una Página Personalizada.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "<PERSON><PERSON><PERSON><PERSON> todos los datos de los usuarios", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "Todas las aportaciones de los usuarios a la encuesta se anonimizarán antes de ser registradas", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Los usuarios seguirán teniendo que cumplir los requisitos de participación de los \"Derechos de acceso\". Los datos del perfil de usuario no estarán disponibles en la exportación de datos de la encuesta.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "¿En qué condiciones puede aparecer la ventana emergente para los usuarios?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "¿Quiénes son los jefes de proyecto?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Total de respuestas a la encuesta", "app.containers.Admin.communityMonitor.upsell.aiSummary": "Resumen de la IA", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Activar barómetro de satisfacción", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Esta función no está incluida en tu plan actual. Habla con tu Gestor de Éxito Gubernamental o con el administrador para desbloquearla.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Índice de bienestar", "app.containers.Admin.communityMonitor.upsell.learnMore": "Conoce más", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Evolución del índice", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "El Barómetro de satisfacción te ayuda a mantenerte actualizado haciendo un seguimiento continuo de la opinión de los residentes, su satisfacción con los servicios y la vida en la comunidad.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Obtén índices claros, citas de residentes y un informe trimestral que puedes compartir con colegas o cargos electos.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Índices fáciles de leer que evolucionan con el tiempo", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Citas clave de residentes, resumidas por AI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Preguntas adaptadas al contexto de tu ciudad", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Residentes reclutados aleatoriamente en la plataforma", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Informes trimestrales en PDF, listos para compartir", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "Comprende cómo se siente tu comunidad antes de que crezcan los problemas", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Recuento", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Escritorio u otro", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Móvil", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tableta", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Tipos de dispositivos", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Tipo de dispositivo", "app.containers.Admin.earlyAccessLabel": "Acceso anticipado", "app.containers.Admin.earlyAccessLabelExplanation": "Se trata de una nueva función disponible en Acceso anticipado.", "app.containers.Admin.emails.addCampaign": "<PERSON><PERSON><PERSON> correo electrón<PERSON>", "app.containers.Admin.emails.addCampaignTitle": "Crear un nuevo correo electrónico", "app.containers.Admin.emails.allParticipantsInProject": "Todos los participantes en el proyecto", "app.containers.Admin.emails.allUsers": "Todos los usuarios", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Los correos electrónicos automatizados se envían automáticamente y se activan por las acciones de un usuario. Puedes desactivar algunos de ellos para todos los usuarios de tu plataforma. Los demás correos automatizados no se pueden desactivar porque son necesarios para el correcto funcionamiento de tu plataforma.", "app.containers.Admin.emails.automatedEmails": "Correos electrónicos automatizados", "app.containers.Admin.emails.automatedEmailsDigest": "El correo electrónico sólo se enviará si hay contenido", "app.containers.Admin.emails.automatedEmailsRecipients": "Usuarios que recibirán este correo", "app.containers.Admin.emails.automatedEmailsTriggers": "Suceso que desencadena este correo electrónico", "app.containers.Admin.emails.changeRecipientsButton": "Cambiar los destinatarios", "app.containers.Admin.emails.clickOnButtonForExamples": "Haz clic en el botón de abajo para consultar ejemplos de este correo electrónico en nuestra página de asistencia.", "app.containers.Admin.emails.confirmSendHeader": "¿Enviar correo a todos los usuarios?", "app.containers.Admin.emails.deleteButtonLabel": "Eliminar", "app.containers.Admin.emails.draft": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editButtonLabel": "<PERSON><PERSON>", "app.containers.Admin.emails.editCampaignTitle": "<PERSON><PERSON> camp<PERSON>", "app.containers.Admin.emails.editDisabledTooltip2": "Próximamente: Actualmente no se puede editar este correo electrónico.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Texto del botón", "app.containers.Admin.emails.editRegion_intro_multiloc": "Introducción", "app.containers.Admin.emails.editRegion_subject_multiloc": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editRegion_title_multiloc": "Titulo", "app.containers.Admin.emails.emailCreated": "Correo guardado como borrador", "app.containers.Admin.emails.emailUpdated": "Email actualizado correctamente", "app.containers.Admin.emails.emptyCampaignsDescription": "Conecta fácilmente con tus participantes enviándoles correos electrónicos. Elige con quién contactar y haz un seguimiento de tu compromiso.", "app.containers.Admin.emails.emptyCampaignsHeader": "Envía tu primer correo electrónico", "app.containers.Admin.emails.failed": "Fallido", "app.containers.Admin.emails.fieldBody": "Men<PERSON><PERSON>", "app.containers.Admin.emails.fieldBodyError": "Proporcionar un mensaje de correo electrónico", "app.containers.Admin.emails.fieldGroupContent": "Contenido del correo", "app.containers.Admin.emails.fieldReplyTo": "Respuestas deben ir a", "app.containers.Admin.emails.fieldReplyToEmailError": "Proporcionar una dirección de correo electrónico en el formato correcto, <NAME_EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Proporcionar una dirección de correo electrónico", "app.containers.Admin.emails.fieldReplyToTooltip": "Elige qué dirección de correo electrónico debe recibir respuestas directas de los usuarios.", "app.containers.Admin.emails.fieldSender": "De", "app.containers.Admin.emails.fieldSenderError": "Proporcionar el remitente del correo electrónico", "app.containers.Admin.emails.fieldSenderTooltip": "Elegir a quién los usuarios verán como remitente del correo electrónico.", "app.containers.Admin.emails.fieldSubject": "Respuesta", "app.containers.Admin.emails.fieldSubjectError": "Proporcionar un asunto del correo electrónico", "app.containers.Admin.emails.fieldSubjectTooltip": "Esto aparecerá en la línea de espuesta del correo electrónico y en general de la bandeja de entrada del usuario. Hazla clara y atractiva.", "app.containers.Admin.emails.fieldTo": "Para", "app.containers.Admin.emails.fieldToTooltip": "Elegir el tipo de usuarios que recibirán tus correos electrónicos.", "app.containers.Admin.emails.formSave": "Guardar como borrador", "app.containers.Admin.emails.formSaveAsDraft": "Guardar como borrador", "app.containers.Admin.emails.from": "De:", "app.containers.Admin.emails.groups": "Grupos", "app.containers.Admin.emails.helmetDescription": "Enviar emails manual a cierto grupo del ciudadano y activar campañas automatizadas", "app.containers.Admin.emails.nameVariablesInfo2": "<PERSON><PERSON>es hablar directamente a los ciudadanos usando las variables {firstName} {lastName}. <PERSON><PERSON> e<PERSON><PERSON><PERSON>, \"Estimado {firstName} {lastName},...\"", "app.containers.Admin.emails.previewSentConfirmation": "Un correo de vista previa ha sido enviado a tu correo", "app.containers.Admin.emails.previewTitle": "Vista previa", "app.containers.Admin.emails.regionMultilocError": "Indica un valor para todos los idiomas", "app.containers.Admin.emails.seeEmailHereText": "En cuanto se envíe un correo de este tipo, podrás consultarlo aquí.", "app.containers.Admin.emails.send": "Enviar", "app.containers.Admin.emails.sendNowButton": "<PERSON><PERSON><PERSON> ahora", "app.containers.Admin.emails.sendTestEmailButton": "Mandame un mail de prueba", "app.containers.Admin.emails.sendTestEmailTooltip2": "Cuando hagas clic en este enlace, se enviará un correo electrónico de prueba sólo a tu dirección de correo electrónico. Esto te permite comprobar qué aspecto tiene el correo en la \"vida real\".", "app.containers.Admin.emails.senderRecipients": "Remitente y destinatarios", "app.containers.Admin.emails.sending": "Enviando", "app.containers.Admin.emails.sent": "Enviado", "app.containers.Admin.emails.sentToUsers": "Son correos electrónicos enviados a los usuarios", "app.containers.Admin.emails.subject": "Asunto:", "app.containers.Admin.emails.supportButtonLabel": "Ver ejemplos en nuestra página de ayuda", "app.containers.Admin.emails.supportButtonLink2": "https://support.citizenlab.co/es/articles/7025887-crear-un-proyecto-de-encuesta-externa", "app.containers.Admin.emails.to": "Para:", "app.containers.Admin.emails.toAllUsers": "¿Quieres enviar este correo electrónico a todos los usuarios?", "app.containers.Admin.emails.viewExample": "<PERSON>er", "app.containers.Admin.ideas.import": "Importar", "app.containers.Admin.inspirationHub.AllProjects": "Todos los proyectos", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Monitor comunitario", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Anotación de documentos", "app.containers.Admin.inspirationHub.ExternalSurvey": "Encuesta externa", "app.containers.Admin.inspirationHub.Filters.Country": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Method": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Search": "Búsqueda", "app.containers.Admin.inspirationHub.Filters.Topic": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.population": "Población", "app.containers.Admin.inspirationHub.Highlighted": "Destacados", "app.containers.Admin.inspirationHub.Ideation": "Ideación", "app.containers.Admin.inspirationHub.Information": "Información", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Elige un país para ver los proyectos destacados", "app.containers.Admin.inspirationHub.PinnedProjects.country": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "No se han encontrado proyectos destacados para este país. Cambia el país para ver los proyectos destacados de otros países", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Cambia de país para ver más proyectos destacados", "app.containers.Admin.inspirationHub.Poll": "Mini-encuesta", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "<PERSON>bie<PERSON>o", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "<PERSON>r más...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Fase {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "Si no quieres que tu proyecto se incluya en el centro de inspiración, ponte en contacto con tu gestor de GovSuccess.", "app.containers.Admin.inspirationHub.Proposals": "Propuestas", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Ordenar por", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Participantes (primero los más bajos)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Participantes (primero los más altos)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "<PERSON>cha de inicio (la más antigua primero)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Fecha de inicio (la más reciente primero)", "app.containers.Admin.inspirationHub.Survey": "Encuesta", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Lista de los mejores proyectos alrededor del mundo.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Habla con otros profesionales y aprende de ellos.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filtra por método, tamaño de ciudad y país.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Habilitar el Centro de Inspiración", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Esta función no está incluida en tu plan actual. Habla con tu Gestor de Éxito Gubernamental o con el administrador para desbloquearla.", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "https://support.govocal.com/en/articles/11093736-using-the-inspiration-hub", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "Conoce más", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "El Centro de Inspiración te conecta a una lista seleccionada de proyectos de participación excepcionales en plataformas Go Vocal de todo el mundo. Aprende cómo otras ciudades llevan a cabo proyectos de éxito y habla con otros profesionales.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Únete a una red de profesionales pioneros de la democracia", "app.containers.Admin.inspirationHub.Volunteering": "Voluntariado", "app.containers.Admin.inspirationHub.Voting": "Votación", "app.containers.Admin.inspirationHub.commonGround": "Mapa de consenso", "app.containers.Admin.inspirationHub.filters": "filtros", "app.containers.Admin.inspirationHub.resetFilters": "Restablecer los filtros", "app.containers.Admin.inspirationHub.seemsLike": "Parece que no hay más proyectos. Prueba a cambiar {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "Editar campos de campaña", "app.containers.Admin.messaging.automated.variablesToolTip": "Puedes utilizar las siguientes variables en tu mensaje:", "app.containers.Admin.messaging.helmetTitle": "Mensajería", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "Este widget muestra a cada usuario proyectos <b>basados en sus preferencias de seguimiento</b>. Esto incluye proyectos que siguen, así como proyectos en los que siguen aportaciones, y proyectos relacionados con temas o áreas que les interesan.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "Este widget sólo se mostrará al usuario si hay proyectos en los que pueda participar. Si ves este mensaje, significa que tú (el administrador) no puedes participar en ningún proyecto en este momento. Este mensaje no será visible en la página de inicio real.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Abierto a la participación", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "Este widget mostrará proyectos en los que el usuario puede <b>realizar una acción para participar</b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Titulo", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "Para ti", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "Este widget sólo se mostrará al usuario si hay proyectos relevantes para él en función de sus preferencias de seguimiento. Si ves este mensaje, significa que tú (el administrador) no estás siguiendo nada en este momento. Este mensaje no será visible en la página de inicio real.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "Elementos seguidos", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Archivado", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Filtrar por", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Finalizado", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Terminado y archivado", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "No hay datos disponibles", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "Este widget muestra <b>los proyectos que están finalizados y/o archivados.</b>. \"Finalizados\" también incluye los proyectos que están en la última fase, y en los que la última fase es un informe.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Proyectos acabados", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "Lo que dijiste, lo que hicimos...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "Proporciona un nombre para todas las lenguas", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "El proyecto no puede estar vacío", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Nombre de la tendencia", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "proyectos", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "URL resultante", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "Guarda", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "<PERSON><PERSON><PERSON> proyecto", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "La barra de navegación sólo mostrará los proyectos a los que tengan acceso los usuarios.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "Este widget sólo estará visible en la página de inicio cuando el Barómetro de satisfacción esté aceptando respuestas.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Barómetro de satisfacción", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Descripción", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "Botón", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Titulo", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Importante:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Ejemplo de escala de sentimiento", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "Sin fecha de finalización", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "Pulsa escape para saltar el carrusel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Proyectos y carpetas (legado)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Título del proyecto", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} está trabajando actualmente en", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Texto del botón", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "¡Participa ya!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Descripción", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "<PERSON>a", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Selecciona un proyecto o carpeta", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Selecciona proyecto o carpeta", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Mostrar avatares", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Destacado", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Titulo", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "Comienza en {days} días", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Comienza en {weeks} semanas", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "hace {days} días", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "hace {weeks} semanas", "app.containers.Admin.project.Campaigns.campaignFrom": "De:", "app.containers.Admin.project.Campaigns.campaignTo": "Para:", "app.containers.Admin.project.Campaigns.customEmails": "Correos electrónicos personalizados", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Envía correos electrónicos personalizados y comprueba las estadísticas.", "app.containers.Admin.project.Campaigns.noAccess": "<PERSON><PERSON><PERSON>, pero parece que no tienes acceso a la sección de los correos electrónicos", "app.containers.Admin.project.emails.addCampaign": "<PERSON><PERSON><PERSON> correo electrón<PERSON>", "app.containers.Admin.project.emails.addCampaignTitle": "Crear un nuevo correo electrónico", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Todos los {participants} y seguidores del proyecto", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Esto incluye a los usuarios registrados que realizaron alguna acción en el proyecto. No se incluyen los usuarios no registrados o anonimizados.", "app.containers.Admin.project.emails.dateSent": "<PERSON><PERSON>", "app.containers.Admin.project.emails.deleteButtonLabel": "Bo<PERSON>r", "app.containers.Admin.project.emails.draft": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.editButtonLabel": "<PERSON><PERSON>", "app.containers.Admin.project.emails.editCampaignTitle": "<PERSON><PERSON> camp<PERSON>", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Conecta fácilmente con tus participantes enviándoles correos electrónicos. Elige con quién contactar y haz un seguimiento de tu compromiso.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Envía tu primer correo electrónico", "app.containers.Admin.project.emails.failed": "Fallido", "app.containers.Admin.project.emails.fieldBody": "Men<PERSON><PERSON>", "app.containers.Admin.project.emails.fieldBodyError": "Proporcionar un mensaje de correo electrónico", "app.containers.Admin.project.emails.fieldReplyTo": "Respuestas deben ir a", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Proporcione una dirección de correo electrónico en el formato correcto, <NAME_EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "Proporcione una dirección de correo electrónico", "app.containers.Admin.project.emails.fieldReplyToTooltip": "Elige qué dirección de correo electrónico debe recibir respuestas directas de los usuarios.", "app.containers.Admin.project.emails.fieldSender": "De", "app.containers.Admin.project.emails.fieldSenderError": "Proporcionar el remitente del correo electrónico", "app.containers.Admin.project.emails.fieldSenderTooltip": "Elegir a quién los usuarios verán como remitente del correo electrónico.", "app.containers.Admin.project.emails.fieldSubject": "Respuesta", "app.containers.Admin.project.emails.fieldSubjectError": "Proporcionar un asunto del correo electrónico", "app.containers.Admin.project.emails.fieldSubjectTooltip": "Esto aparecerá en la línea de espuesta del correo electrónico y en general de la bandeja de entrada del usuario. Hazla clara y atractiva.", "app.containers.Admin.project.emails.fieldTo": "Para", "app.containers.Admin.project.emails.formSave": "Guardar como borrador", "app.containers.Admin.project.emails.from": "De:", "app.containers.Admin.project.emails.helmetDescription": "Enviar correos electrónicos manuales a los participantes en el proyecto", "app.containers.Admin.project.emails.infoboxAdminText": "Desde la pestaña Mensajería del Proyecto sólo puedes enviar correos electrónicos a todos los participantes del proyecto.  Para enviar correos electrónicos a otros participantes o subconjuntos de usuarios, ve a la pestaña {link} .", "app.containers.Admin.project.emails.infoboxLinkText": "Plataforma de mensajería", "app.containers.Admin.project.emails.infoboxModeratorText": "Desde la pestaña Mensajería del Proyecto sólo puedes enviar correos electrónicos a todos los participantes del proyecto. Los administradores pueden enviar correos electrónicos a otros participantes o subconjuntos de usuarios a través de la pestaña Mensajería de la Plataforma.", "app.containers.Admin.project.emails.message": "Men<PERSON><PERSON>", "app.containers.Admin.project.emails.nameVariablesInfo2": "<PERSON><PERSON>es hablar directamente a los ciudadanos usando las variables {firstName} {lastName}. <PERSON><PERSON> e<PERSON><PERSON><PERSON>, \"Estimado {firstName} {lastName},...\"", "app.containers.Admin.project.emails.participants": "participantes en este proyecto", "app.containers.Admin.project.emails.previewSentConfirmation": "Un correo de vista previa ha sido enviado a tu correo", "app.containers.Admin.project.emails.previewTitle": "Vista previa", "app.containers.Admin.project.emails.projectParticipants": "Participantes en el proyecto", "app.containers.Admin.project.emails.recipients": "Des<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.send": "Enviar", "app.containers.Admin.project.emails.sendTestEmailButton": "Mandame un mail de prueba", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Envía este proyecto a la dirección de correo electrónico con la que estas conectado, para comprobar cómo se ve en la 'vida real'.", "app.containers.Admin.project.emails.senderRecipients": "Remitente y destinatarios", "app.containers.Admin.project.emails.sending": "Enviando", "app.containers.Admin.project.emails.sent": "Enviado", "app.containers.Admin.project.emails.sentToUsers": "Son correos electrónicos enviados a los usuarios", "app.containers.Admin.project.emails.status": "Rol", "app.containers.Admin.project.emails.subject": "Asunto:", "app.containers.Admin.project.emails.to": "Para:", "app.containers.Admin.project.messaging.helmetTitle": "Mensajería", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "Esta imagen forma parte de la ficha de la carpeta; la ficha que resume la carpeta y que se muestra, por ejemplo, en la página de inicio. Para más información sobre las resoluciones de imagen recomendadas, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Esta imagen se muestra en la parte superior de la página de la carpeta. Para más información sobre las resoluciones de imagen recomendadas, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "visite nuestro centro de soporte", "app.containers.Admin.projects.all.askPersonalData3": "Añadir campos para nombre y correo electrónico", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "Activar la vista de calendario", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "Esta función no está incluida en tu plan actual. Habla con tu Gestor de Éxito Gubernamental o con el administrador para desbloquearla.", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "Conoce más", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "Obtén una visión visual de los plazos de tus proyectos en nuestra vista de calendario. Identifica rápidamente qué proyectos y fases empiezan o terminan pronto y requieren acción.", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "Entender qué ocurre y cuándo", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Todas las preguntas se muestran en el PDF. Sin embargo, actualmente no se admite la importación a través de FormSync de lo siguiente: Imágenes, Etiquetas y Carga de archivos.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Todas las preguntas se muestran en el PDF. Sin embargo, actualmente no es posible importar las siguientes preguntas a través de FormSync: Preguntas de mapeo (marcar un punto, dibujar ruta y dibujar área), preguntas de clasificación, preguntas de matriz y preguntas de carga de archivos.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "Fin del formulario", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Inicio del formulario", "app.containers.Admin.projects.all.components.archived": "Archivado", "app.containers.Admin.projects.all.components.draft": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.components.manageButtonLabel": "Administrar", "app.containers.Admin.projects.all.copyProjectButton": "Copiar proyecto", "app.containers.Admin.projects.all.copyProjectError": "Se ha producido un error al copiar este proyecto. Inténtelo de nuevo más tarde.", "app.containers.Admin.projects.all.customiseEnd": "Personaliza el final del formulario.", "app.containers.Admin.projects.all.customiseStart": "Personaliza el inicio del formulario.", "app.containers.Admin.projects.all.deleteFolderButton1": "Eliminar carpeta", "app.containers.Admin.projects.all.deleteFolderConfirm": "¿Estás seguro de que quieres borrar esta carpeta? Todos los proyectos dentro de la carpeta también serán eliminados. Esta acción no puede deshacerse.", "app.containers.Admin.projects.all.deleteFolderError": "Algo pasó al eliminar esta carpeta. Por favor, intentalo más tarde.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Eliminar proyecto", "app.containers.Admin.projects.all.deleteProjectConfirmation": "¿Está seguro que desea eliminar este proyecto? Esto no se puede deshacer.", "app.containers.Admin.projects.all.deleteProjectError": "Error al eliminar este proyecto, por favor Inténtalo más tarde.", "app.containers.Admin.projects.all.exportAsPDF1": "Descargar formulario PDF", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Puedes combinar respuestas online y offline. Para cargar respuestas offline, ve a la pestaña \"Gestor de entradas\" de este proyecto, y haz clic en \"Importar\".", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Puedes combinar respuestas online y offline. Para cargar respuestas sin conexión, ve a la pestaña \"Encuesta\" de este proyecto y haz clic en \"Importar\".", "app.containers.Admin.projects.all.logicNotInPDF": "La lógica de la encuesta no se reflejará en el PDF descargado. Los encuestados en papel verán todas las preguntas de la encuesta.", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "Buscar carpetas", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "Se han cargado todas las carpetas", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Carpeta", "app.containers.Admin.projects.all.new.Folders.Table.managers": "Gestores", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# proyecto} other {# proyectos}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Estado", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Fecha de inicio del proyecto", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Encontrabilidad", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "Oculto", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "Público", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Carpetas", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filtrar por el método de participación en la fase actual", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "Ideación", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Información", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Método de participación", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Anotación de documentos", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Mapa de consenso", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "Encuesta", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "Mini-encuesta", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Peticiones", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Voluntariado", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Votación", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "Abierto a aportes", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informando", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "No iniciado", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Estado de participación", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Pasado", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "Pendiente de aprobación", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "Buscar proyectos", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Alfabéticamente (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Alfabéticamente (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Fase a punto de empezar o acabar", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc": "Creado recientemente (nuevo-viejo)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc": "Creado recientemente (antiguo-nuevo)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "Vistos recientemente", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Estado", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Administradores", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Grupos", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Visibilidad", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "Público", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "<PERSON><PERSON><PERSON> filt<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "Restablecer", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "No hay más filtros por añadir", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Administradores", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Se han cargado todos los proyectos", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "Cualquiera", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Archivado", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "Fase actual", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "Quedan {days}d", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d para empezar", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Encontrabilidad:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.end": "Final", "app.containers.Admin.projects.all.new.Projects.Table.ended": "Finalizado", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "<PERSON>iza hoy", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Grupos", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "Oculto", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "<PERSON><PERSON>do m<PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.manager": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}mo i<PERSON><PERSON><PERSON>a", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}m para empezar", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Siguiente fase:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "No asignado", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Fase", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "Prelanzamiento", "app.containers.Admin.projects.all.new.Projects.Table.project": "Proyecto", "app.containers.Admin.projects.all.new.Projects.Table.public": "Público", "app.containers.Admin.projects.all.new.Projects.Table.published": "Publicado", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Desplázate hacia abajo para cargar más", "app.containers.Admin.projects.all.new.Projects.Table.start": "<PERSON><PERSON>o", "app.containers.Admin.projects.all.new.Projects.Table.status": "Estado", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Estado:", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "Esta columna utiliza datos de participantes almacenados en caché. Para ver las cifras más recientes, consulta la pestaña \"Participantes\" del proyecto.", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Visibilidad", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Visibilidad:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} grupos", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} gestores", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "Quedan {years}y", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y para empezar", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Fase actual: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} días que quedan", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Carpeta: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "Sin fase actual", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "Sin fecha de finalización", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "No hay fases", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Fase {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Fases:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Proyecto", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "<PERSON><PERSON> de inicio: {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "Organizar proyectos", "app.containers.Admin.projects.all.new.calendar": "Calendario", "app.containers.Admin.projects.all.new.folders": "Carpetas", "app.containers.Admin.projects.all.new.projects": "Proyectos", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "No se ha podido cargar la línea de tiempo.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "El proyecto no tiene fecha de finalización", "app.containers.Admin.projects.all.new.timeline.project": "Proyecto", "app.containers.Admin.projects.all.notes": "Notas", "app.containers.Admin.projects.all.personalDataExplanation5": "Esta opción añadirá los campos de nombre, apellidos y correo electrónico al PDF. Al escanear el formulario en papel, utilizaremos esos datos para autogenerar una cuenta en la plataforma para el participante.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "Resumen de la IA", "app.containers.Admin.projects.project.analysis.Comments.comments": "Comentarios", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "El resumen de comentarios está disponible cuando hay 5 o más comentarios.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Resume los comentarios", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {Actualizar} =1 {1 comentario nuevo} other {# comentarios nuevos}}", "app.containers.Admin.projects.project.analysis.aiSummary": "Resumen de la IA", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "Se trata de contenido generado por IA. Puede que no sea 100% exacto. Por favor, revísalo y haz referencias cruzadas con las entradas reales para comprobar su exactitud. Ten en cuenta que es probable que la precisión mejore si se reduce el número de entradas seleccionadas.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Las notificaciones por correo sólo se envían a los participantes", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "Oculto", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "No indexado por los motores de búsqueda", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "No visible en la página de inicio ni en los widgets", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Sólo accesible a través de la URL", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "Público", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "Selecciona el nivel de encontrabilidad de este proyecto.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "Este proyecto es visible para todos los que tengan acceso, y aparecerá en la página de inicio y en los widgets.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Este proyecto estará oculto al público en general, y sólo será visible para quienes tengan el enlace.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "¿Quién puede encontrar este proyecto?", "app.containers.Admin.projects.project.ideas.analysisAction1": "<PERSON><PERSON><PERSON> aná<PERSON> de IA", "app.containers.Admin.projects.project.ideas.analysisText2": "Explora los resúmenes con IA y consulta las aportaciones individuales.", "app.containers.Admin.projects.project.ideas.importInputs": "Importar", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "Después de crear un informe, puedes elegir compartirlo con el público una vez iniciada la fase.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Crear una página más compleja para compartir información", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Crea un informe para:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "<PERSON><PERSON><PERSON> un informe", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Crea un informe para una fase anterior, o empieza desde cero.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "Este informe no es público. Para hacerlo público, activa el conmutador \"Visible\".", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "Esta fase ha comenzado, pero el informe aún no es público. Para hacerlo público, activa el conmutador \"Visible\".", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Empieza con una plantilla de fase", "app.containers.Admin.projects.project.information.ReportTab.report": "Reporte", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Comparte los resultados de una encuesta pasada o de una fase de ideación", "app.containers.Admin.projects.project.information.ReportTab.visible": "Visible", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "Este informe será público en cuanto comience la fase. Para que no sea público, desactiva el conmutador \"Visible\".", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "Este informe es actualmente público. Para que no sea público, desactiva el conmutador \"Visible\".", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "¿Estás seguro de que deseas eliminar esta entrada? Esta acción no se puede deshacer.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Añadir a la fase", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "Tienes que dar tu consentimiento antes de continuar", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "El formulario puede descargarse aquí.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "El formulario subido se creó con la sección \"Datos personales\".", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Lengua de formulario", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "Doy mi consentimiento para que se procese este archivo utilizando el analizador de formularios de Google Cloud", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Importar archivo Excel", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Por favor, sube un archivo para continuar", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "La plantilla puede descargarse aquí.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Sube", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Sube un <b>archivo Excel</b> (.xlsx) completo. Debe utilizar la plantilla proporcionada para este proyecto. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Sube un <b>archivo PDF de formularios escaneados</b>. Debe utilizar un formulario impreso de esta fase. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Utiliza este correo electrónico para el nuevo usuario", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Introduce un correo electrónico válido para crear una cuenta nueva", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Se creará una nueva cuenta para el autor con estos datos. Esta entrada se añadirá a la misma.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "Nombre", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Apellido", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Introduce una dirección de correo electrónico y/o un nombre y apellidos para asignar esta entrada a un autor. O desmarca la casilla de consentimiento.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Ya existe una cuenta asociada a este correo electrónico. Esta entrada se añadirá a ella.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Consentimiento del usuario (crear cuenta de usuario)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "Aprobado", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Aprueba todas las aportaciones", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Autor:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "Correo electrónico:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Se han producido errores durante la importación y algunas entradas no se han importado. Por favor, corrige los errores y vuelve a importar las entradas que falten.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Datos de formulario no válidos. Comprueba si hay errores en el formulario anterior.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Importar archivo Excel (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Importar", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Importar formularios escaneados (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Importar formularios escaneados", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "Entradas importadas", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importar. Este proceso puede tardar unos minutos.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "Esta aportación se importó de forma anónima.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} Las entradas se han importado y requieren aprobación.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} no se han podido aprobar las entradas. Comprueba si hay problemas de validación en cada entrada y confírmalos individualmente.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Localidad:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Todavía no hay nada que revisar. Haz clic en \"{importFile}\" para importar un archivo PDF que contenga formularios de entrada escaneados o un archivo Excel que contenga entradas.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Todavía no hay nada que revisar. Haz clic en \"{importFile}\" para importar un archivo Excel que contenga aportaciones.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "Aportes", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "No se puede visualizar el archivo importado. La visualización del archivo importado sólo está disponible para las importaciones de PDF.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Fase:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "La fase seleccionada no puede contener entradas. Por favor, selecciona otra.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Este proyecto no contiene fases que puedan contener ideas.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "Selecciona a qué fase quieres añadir estas entradas.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Importador de entradas", "app.containers.Admin.projects.project.participation.comments": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.inputs": "Entradas", "app.containers.Admin.projects.project.participation.participantsTimeline": "Calendario de participantes", "app.containers.Admin.projects.project.participation.reactions": "Reacciones", "app.containers.Admin.projects.project.participation.selectPeriod": "Selecciona el periodo", "app.containers.Admin.projects.project.participation.usersByAge": "Usuarios por edad", "app.containers.Admin.projects.project.participation.usersByGender": "Usuarios por género", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "Requerido", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "<PERSON><PERSON><PERSON> una pregunta", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "La capacidad de añadir o editar campos de usuario a nivel de fase no está incluida en tu licencia actual. Ponte en contacto con tu Gestor de GovSuccess para obtener más información al respecto.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} opciones", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "Estado del campo", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "Estas preguntas se añadirán como última página del formulario de la encuesta, porque se ha seleccionado \"¿Mostrar campos en la encuesta?\" en la configuración de la fase.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "No se harán preguntas adicionales.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Opcional", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Opcional - siempre activado porque está referenciado por el grupo", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "Eliminar campo", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Obligatorio - siempre activado porque está referenciado por el grupo", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Autentícate con {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Completa las siguientes preguntas extra", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Confirme su correo electrónico", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Datos devueltos por el método de verificación:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Introduce nombre, apellidos, correo electrónico y contraseña", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "Introduce tu correo electrónico", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "¿Con qué frecuencia deben verificarse los usuarios?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Verificación de identidad con {verificationMethod} (en función del grupo de usuarios)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "No se requiere ninguna acción para participar", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Utiliza grupos inteligentes para restringir la participación basándote en los datos verificados enumerados anteriormente", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Los usuarios deben haber sido verificados en los últimos 30 minutos.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Los usuarios deben haber sido verificados en los últimos {days} días.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "En los últimos 30 días", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "En los últimos 30 minutos", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "En los últimos 7 días", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "Una vez es suficiente", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Campos verificados:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} verificación", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Creación de cuenta", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Los participantes deben crear una cuenta completa con su nombre, correo electrónico confirmado y contraseña.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Los participantes deben crear una cuenta completa con su nombre, correo electrónico y contraseña.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Autenticación", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Confirmación por correo electrónico", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Los participantes deben confirmar su correo electrónico con un código de un solo uso.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Detección avanzada de spam", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "Esta función ayuda a evitar el envío de encuestas duplicadas por parte de usuarios que no han iniciado sesión, analizando las direcciones IP y los datos del dispositivo. Aunque no es tan precisa como exigir el inicio de sesión, puede ayudar a reducir el número de respuestas duplicadas.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Nota: En redes compartidas (como oficinas o Wi-Fi públicas), existe una pequeña posibilidad de que diferentes usuarios sean marcados como duplicados.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Activar la detección avanzada de spam", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Preguntas adicionales a los participantes", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "Cualquiera", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Cualquiera puede participar sin registrarse ni iniciar sesión.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Restablecer preguntas y grupos adicionales", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Restringir la participación a grupos de usuarios", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "Verificación SSO", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Los participantes deben verificar su identidad en {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Análisis de IA abierta", "app.containers.Admin.projects.project.survey.allFiles": "Todos los archivos", "app.containers.Admin.projects.project.survey.allResponses": "Todas las respuestas", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Precisión: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Se ha producido un error al generar el resumen de la IA. Intenta regenerarlo a continuación.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Análisis de IA abierta", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "Ocultar los resúmenes de esta pregunta", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "entradas seleccionadas", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Abrir acciones de análisis", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} nuevas respuestas", "app.containers.Admin.projects.project.survey.analysis.regenerate": "Regenera", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Mostrar información sobre IA", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "<PERSON>uedes resumir un máximo de 30 entradas a la vez en tu plan actual. Habla con tu gestor o administrador de GovSuccess para obtener más información.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Selecciona preguntas relacionadas para el análisis", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "¿Quieres incluir alguna otra pregunta relacionada en tu análisis de {question}?", "app.containers.Admin.projects.project.survey.cancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "Acepto utilizar OpenAI como procesador de datos para este proyecto", "app.containers.Admin.projects.project.survey.consentModalText1": "<PERSON> continua<PERSON>, aceptas el uso de OpenAI como procesador de datos para este proyecto.", "app.containers.Admin.projects.project.survey.consentModalText2": "Las API de OpenAI impulsan los resúmenes de texto automatizados y partes de la experiencia de etiquetado automatizado.", "app.containers.Admin.projects.project.survey.consentModalText3": "Sólo enviamos a las API de OpenAI lo que los usuarios escribieron en sus encuestas, ideas y comentarios, nunca ninguna información de su perfil.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI no utilizará estos datos para el posterior entrenamiento de sus modelos. Puedes encontrar más información sobre cómo gestiona OpenAI la privacidad de los datos en {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "aquí", "app.containers.Admin.projects.project.survey.consentModalTitle": "<PERSON>tes de continuar", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "No puedes entrar en el análisis antes de haber editado el formulario", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Bo<PERSON>r", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "¿Estás seguro de que quieres borrar este análisis? Esta acción no se puede deshacer.", "app.containers.Admin.projects.project.survey.explore": "Explora", "app.containers.Admin.projects.project.survey.followUpResponses": "Comentarios adicionales", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> de media", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Exportar como GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Exporta las respuestas a esta pregunta como un archivo GeoJSON. Para cada Característica GeoJSON, todas las respuestas a la encuesta del encuestado relacionado aparecerán en el objeto \"propiedades\" de esa Característica.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "<PERSON><PERSON><PERSON><PERSON> de<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} encuestados} one {{respondentCount} encuestados} other {{respondentCount} encuestados}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} elección} one {{numberChoices} elección} other {{numberChoices} elección}}", "app.containers.Admin.projects.project.survey.heatMap": "Mapa de calor", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13https://support.govocal.com/es/articles/7025887-crear-un-proyecto-de-encuesta-externa", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Más información sobre los mapas térmicos generados con Esri Smart Mapping.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "El mapa de calor se genera utilizando Esri Smart Mapping. Los mapas de calor son útiles cuando hay una gran cantidad de puntos de datos. Para menos puntos, puede ser mejor mirar directamente sólo los puntos de localización. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Vista del mapa de calor", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Oculto por la lógica", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "Cuando un usuario selecciona esta respuesta, la lógica salta todas las páginas hasta la página {pageNumber} ({numQuestionsSkipped} preguntas saltadas). Haz clic para ocultar o mostrar las páginas y preguntas omitidas.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "Cuando un usuario selecciona esta respuesta, la lógica salta al final de la encuesta ({numQuestionsSkipped} preguntas saltadas). Haz clic para ocultar o mostrar las páginas y preguntas omitidas.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "La lógica de esta página omite todas las páginas hasta la página {pageNumber} ({numQuestionsSkipped} preguntas omitidas). Haz clic para ocultar o mostrar las páginas y preguntas omitidas.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "La lógica de esta página salta al final de la encuesta ({numQuestionsSkipped} preguntas saltadas). Haz clic para ocultar o mostrar las páginas y preguntas omitidas.", "app.containers.Admin.projects.project.survey.newAnalysis": "Nuevo análisis", "app.containers.Admin.projects.project.survey.nextInsight": "Siguiente perspectiva", "app.containers.Admin.projects.project.survey.openAnalysis": "Análisis de IA abierta", "app.containers.Admin.projects.project.survey.otherResponses": "Otras respuestas", "app.containers.Admin.projects.project.survey.page": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.previousInsight": "Visión previa", "app.containers.Admin.projects.project.survey.responses": "Respuestas", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "El número de respuestas de esta página es inferior al número total de respuestas de la encuesta porque algunos encuestados no habrán visto esta página debido a la lógica de la encuesta.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "El número de respuestas a esta pregunta es inferior al número total de respuestas a la encuesta porque algunos encuestados no habrán visto esta pregunta debido a la lógica de la encuesta.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Escala de sentimiento", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Resume instantáneamente todas tus respuestas.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Habla con tus datos en lenguaje natural.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Obtén referencias a respuestas individuales de los resúmenes generados por la IA.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Consulta nuestra página {link} para obtener una visión completa.", "app.containers.Admin.projects.project.survey.upsell.button": "Desbloquea el análisis de IA", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Analiza los datos más rápido con IA", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "Esta función no está incluida en tu plan actual. Habla con tu Gestor de Éxito Gubernamental o con el administrador para desbloquearla.", "app.containers.Admin.projects.project.survey.viewAnalysis": "<PERSON>er", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Explora los resúmenes con IA y consulta las aportaciones individuales.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Selecciona el periodo", "app.containers.Admin.projects.project.traffic.trafficSources": "Fuentes de tráfico", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Hemos cambiado la forma en que recopilamos y mostramos los datos de los visitantes. Como resultado, los datos de los visitantes son más precisos y hay más tipos de datos disponibles, sin dejar de cumplir la GDPR. No empezamos a recopilar estos nuevos datos hasta noviembre de 2024, por lo que antes de esa fecha no hay datos disponibles.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Calendario de visitas", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Informe de la fase", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "<PERSON><PERSON><PERSON> alg<PERSON> texto sobre la fase", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Aquí tiene texto. Puede editarlo y darle formato utilizando el editor del panel de la derecha.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Participantes", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Resultados del proyecto", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Resumen del informe", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Añada el objetivo del proyecto, los métodos de participación utilizados y el resultado", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Visitantes", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Este informe contiene cambios no guardados. <PERSON>r favor, gu<PERSON><PERSON><PERSON> antes de imprimirlos.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "El título ya está ocupado", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "En comparación con los días anteriores {days}", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Ocultar estadísticas", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Tasa de participación", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Mostrar comparación con el último periodo", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "Primero tienes que seleccionar un intervalo de fechas.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Entradas", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Participación", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Mostrar comentarios", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "Mostrar entradas", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Mostrar votos", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "Votos", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Demografía", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Rango de fechas de inscripción", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Campo de inscripción", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Desconocido", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Usuarios: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "Estira", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Activa", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Archivado", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "Finalizado", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "<PERSON>bie<PERSON>o", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "Planificado", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Proyectos", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Estado de la publicación", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Publicado", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "Faltan los datos de este widget. Reconfigúralo o elimínalo para poder guardar el informe.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Índice de bienestar del barómetro", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "Trimestre", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "No se han encontrado fases adecuadas en este proyecto", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "No se ha seleccionado ninguna fase. Por favor, selecciona primero una fase.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "Ningún proyecto", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "No hay ningún proyecto seleccionado. Por favor, selecciona primero un proyecto.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "No puedes duplicar este informe porque contiene datos a los que no tienes acceso.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "No puedes editar este informe porque contiene datos a los que no tienes acceso.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "¿Estás seguro de que quieres borrar \"{reportName}\"? Esta acción no se puede deshacer.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "¿Estás seguro de que deseas eliminar esta entrada? Esta acción no se puede deshacer.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Eliminar", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Dup<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "Modificado {days, plural, no {# días} one {# día} other {# días}} hace", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Se ha producido un error al intentar crear este informe. Vuelva a intentarlo más tarde.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "<PERSON><PERSON><PERSON> con una página en blanco", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Empieza con una plantilla del barómetro de satisfacción", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "<PERSON><PERSON><PERSON><PERSON> del informe", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "<PERSON><PERSON><PERSON> un informe", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Personaliza tu informe y compártelo con las partes interesadas internas o con la comunidad como archivo PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "<PERSON><PERSON><PERSON> un informe", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Crea tu primer informe", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "No se ha seleccionado ningún proyecto", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Empieza con una plantilla de plataforma", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Imprimir en PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Empezar con una plantilla de proyecto", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Trimestre {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Plantilla de informe", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Ya existe un informe con este título. Elija otro título.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Selecciona trimestre", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Selecciona el año", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Compartir como PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Para compartir con todos, imprima el informe como PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Compartir como enlace web", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "Solo los usuarios administradores pueden acceder a este enlace web.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.contactToAccess": "La creación de un informe personalizado forma parte de la licencia Premium. Póngase en contacto con su Gerente de GovSuccess para obtener más información al respecto.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Todos los informes", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Informes del barómetro de satisfacción", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Estos informes están relacionados con el barómetro de satisfacción. Los informes se generan automáticamente cada trimestre.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "<PERSON><PERSON><PERSON> un informe", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Personalice su informe y compártalo con las partes interesadas internas o la comunidad con un enlace web.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "<PERSON><PERSON> informes aparecerán aquí.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Buscar informes", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Informes de progreso", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Estos son informes creados por tu Gestor de Éxito Gubernamental", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "<PERSON><PERSON> informes", "app.containers.Admin.reporting.deprecated": "DEPREDADO", "app.containers.Admin.reporting.helmetDescription": "Página de informe del administrador", "app.containers.Admin.reporting.helmetTitle": "Reportar", "app.containers.Admin.reporting.printPrepare": "Preparando para imprimir...", "app.containers.Admin.reporting.reportBuilder": "<PERSON><PERSON><PERSON><PERSON> de informes", "app.containers.Admin.reporting.reportHeader": "Encabezado del informe", "app.containers.Admin.reporting.warningBanner3": "Los gráficos y números de este informe sólo se actualizan automáticamente en esta página. Guarda el informe para actualizarlos en otras páginas.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Mapa de consenso", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Ideación", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Información", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Métodos utilizados", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "Encuesta", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "Consulta ciudadana", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "Anterior {days} días: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Propuestas", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "Encuesta externa", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Voluntariado", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Votación", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Gráfico", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "Tabla", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "<PERSON>er", "app.containers.Admin.surveyFormTab.downloads": "Descargas", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "Duplicar otra encuesta", "app.containers.Admin.surveyFormTab.editSurveyForm": "Editar formulario de encuesta", "app.containers.Admin.surveyFormTab.inputFormDescription": "Especifique qué información debe proporcionarse, añada breves descripciones o instrucciones para guiar las respuestas de los participantes y especifique si cada campo es opcional u obligatorio.", "app.containers.Admin.surveyFormTab.surveyForm": "Formulario de encuesta", "app.containers.Admin.tools.apiTokens.createTokenButton": "Crear nuevo token", "app.containers.Admin.tools.apiTokens.createTokenCancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Se ha creado tu ficha. Por favor, copia el siguiente {secret} y guárdalo de forma segura.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Crea un nuevo token para utilizarlo con nuestra API pública.", "app.containers.Admin.tools.apiTokens.createTokenError": "Proporciona un nombre para tu token", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Crear token", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>¡Importante!</b> <PERSON><PERSON><PERSON> puedes copiar este {secret} una vez. Si cierras esta ventana no podrás volver a verla.", "app.containers.Admin.tools.apiTokens.createTokenName": "Nombre", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Dale un nombre a tu token", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Tu token ha sido creado", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "Copia {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "¡Copiado!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Crear un nuevo token", "app.containers.Admin.tools.apiTokens.createdAt": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.delete": "Eliminar token", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "¿Estás seguro de que quieres borrar este token?", "app.containers.Admin.tools.apiTokens.description": "Gestiona tus tokens API para nuestra API pública. Para más información, consulta nuestra página {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Última vez que fue utilizado", "app.containers.Admin.tools.apiTokens.link": "Documentación API", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Nombre", "app.containers.Admin.tools.apiTokens.noTokens": "<PERSON>ún no tienes ningún token.", "app.containers.Admin.tools.apiTokens.title": "Tokens de la API pública", "app.containers.Admin.tools.esriDisabled": "La integración con Esri es una función adicional. Ponte en contacto con tu Gestor de GovSuccess si deseas más información al respecto.", "app.containers.Admin.tools.esriIntegration2": "Integración con Esri", "app.containers.Admin.tools.esriIntegrationButton": "Activar <PERSON>", "app.containers.Admin.tools.esriIntegrationDescription3": "Conecta tu cuenta Esri e importa datos de ArcGIS Online directamente a tus proyectos cartográficos.", "app.containers.Admin.tools.esriIntegrationImageAlt": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.esriKeyInputDescription": "Añade tu clave API de Esri para poder importar tus capas de mapas desde ArcGIS Online en las pestañas de mapas de los proyectos.", "app.containers.Admin.tools.esriKeyInputLabel": "Clave API de Esri", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Pega aquí la clave API", "app.containers.Admin.tools.esriMaps": "Mapas Esri", "app.containers.Admin.tools.esriSaveButtonError": "Se ha producido un error al guardar tu clave, inténtalo de nuevo.", "app.containers.Admin.tools.esriSaveButtonSuccess": "Clave API guardada", "app.containers.Admin.tools.esriSaveButtonText": "Tecla Guardar", "app.containers.Admin.tools.learnMore": "Saber más", "app.containers.Admin.tools.managePublicAPIKeys": "Gestionar claves API", "app.containers.Admin.tools.manageWidget": "Administrar widget", "app.containers.Admin.tools.manageWorkshops": "Gestionar talleres", "app.containers.Admin.tools.powerBIAPIImage": "Imagen de Power BI", "app.containers.Admin.tools.powerBIDescription": "Utiliza nuestras plantillas plug & play de Power BI para acceder a los datos de Go Vocal en tu espacio de trabajo de Microsoft Power BI.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI no forma parte de tu licencia. Ponte en contacto con tu Gestor de GovSuccess o administrador si quieres más información al respecto.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Descargar plantillas", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "Si pretendes utilizar tus datos de Go Vocal dentro de un flujo de datos de Power BI, esta plantilla te permitirá configurar un nuevo flujo de datos que conecte con tus datos de Go Vocal. Una vez que hayas descargado esta plantilla, primero debes encontrar y sustituir las siguientes cadenas ##CLIENT_ID## y ##CLIENT_SECRET## en la plantilla por tus credenciales de la API pública antes de cargarla en PowerBI.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Descargar plantilla de flujo de datos", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Plantilla de flujo de datos", "app.containers.Admin.tools.powerBITemplates.intro": "Nota: Para utilizar cualquiera de estas plantillas de Power BI, primero debes {link}.", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "crear un conjunto de credenciales para nuestra API pública", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "Esta plantilla creará un informe Power BI basado en tus datos de Go Vocal. Configurará todas las conexiones de datos a tu plataforma Go Vocal, creará el modelo de datos y algunos cuadros de mando predeterminados. Cuando abras la plantilla en Power BI, se te pedirá que introduzcas tus credenciales de la API pública. También tendrás que introducir la Url Base de tu plataforma, que es: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Descargar plantilla de informe", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Plantilla de informe", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "<PERSON>uedes encontrar más detalles sobre cómo utilizar tus datos de Go Vocal en Power BI en nuestra página {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Plantillas Power BI", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "Gestiona las credenciales para crear integraciones personalizadas en nuestra API pública.", "app.containers.Admin.tools.publicAPIDisabled1": "La API pública no forma parte de tu licencia actual. Ponte en contacto con tu Gestor de GovSuccess o administrador si quieres más información al respecto.", "app.containers.Admin.tools.publicAPIImage": "Imagen pública de la API", "app.containers.Admin.tools.publicAPITitle": "Acceso público a la API", "app.containers.Admin.tools.toolsLabel": "Herramientas", "app.containers.Admin.tools.widgetDescription": "<PERSON><PERSON><PERSON> crear un widget, personalizarlo y añadirlo a tu propio sitio web para atraer gente a esta plataforma.", "app.containers.Admin.tools.widgetImage": "<PERSON>n del widget", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "Celebra reuniones de vídeo en directo, facilita discusiones y debates de grupo simultáneos. Recoge aportaciones, vota y llega a consensos, igual que lo harías fuera de línea.", "app.containers.Admin.tools.workshopsImage": "Talleres imagen", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Talleres de deliberación en línea", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "total de usuarios en la plataforma", "app.containers.AdminPage.DashboardPage._blank": "desconocido", "app.containers.AdminPage.DashboardPage.allGroups": "Todos los grupos", "app.containers.AdminPage.DashboardPage.allProjects": "Todos los proyectos", "app.containers.AdminPage.DashboardPage.allTime": "Todas las fechas", "app.containers.AdminPage.DashboardPage.comments": "Comentarios", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Comentarios", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Se requiere un conjunto de datos base para medir la representación de los usuarios de la plataforma.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Próximamente", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Actualmente estamos trabajando en el panel {fieldName}, pronto estará disponible", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# de elemento está} other {# de elementos están}} ocultos en este gráfico. Cambie a {tableViewLink} para ver todos los datos.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} para el registro de usuarios", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} sobre {total} de usuarios incluidos ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Mostrar {numberOfHiddenItems} más", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Opcional", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "proporcione un conjunto de datos base.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Puntuación de representatividad:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Esta puntuación refleja la precisión con la que los datos de los usuarios de la plataforma reflejan la población total. Más información sobre {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Obligatorio", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Enviar datos base", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "vista de tabla", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Población total", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Usuarios", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Añadir un grupo de edad", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} y más", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Grupo de edad", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Grupo(s) de edad de {upperBound} y más no está(n) incluido(s).", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Grupo de edad {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Grupos de edad", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "y más", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Aplicar ejemplo de agrupación", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "<PERSON><PERSON><PERSON> todo", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "De", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Establezca los grupos de edad para alinearlos con su conjunto de datos base.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Ra<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Guardar", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "Para", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Editar grupos de edad", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Este elemento no se calculará.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "<PERSON>er menos", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Ver {numberOfHiddenItems} más...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Mes base (opcional)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Grupos de edad (Año de nacimiento)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Próximamente", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Completar", "app.containers.AdminPage.DashboardPage.components.Field.default": "Por defecto", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Rellene todas las opciones habilitadas o deshabilite las opciones que desee omitir del gráfico. Debe rellenar al menos una opción.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Incompleto", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Número de residentes totales", "app.containers.AdminPage.DashboardPage.components.Field.options": "Opciones", "app.containers.AdminPage.DashboardPage.components.Field.save": "Guardar", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Guardado", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "{setAgeGroupsLink} primero para empezar a introducir los datos base.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "establecer grupos de edad", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Tiempo medio de respuesta: {days} días", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Cantidad media de días para responder", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "comentario dado", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Estado de las entradas", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Entradas por estado", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Número de entradas", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Actualización oficial", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Porcentaje de entradas", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Tiempo de respuesta", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Estado", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Cambio de estado", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Total", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Editar datos base", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "cómo calculamos las puntuaciones de representatividad", "app.containers.AdminPage.DashboardPage.continuousType": "Continuo", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Total acumulado", "app.containers.AdminPage.DashboardPage.customDateRange": "Personalizado", "app.containers.AdminPage.DashboardPage.day": "día", "app.containers.AdminPage.DashboardPage.false": "falso", "app.containers.AdminPage.DashboardPage.female": "mujer", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Los 5 aportes principales por reacciones", "app.containers.AdminPage.DashboardPage.fromTo": "de {from} a {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Tablero de comandos para las actividades en la plataforma", "app.containers.AdminPage.DashboardPage.helmetTitle": "Página del tablero de administración", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Elija un recurso para mostrar por proyecto", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Elija un recurso para mostrar por tema", "app.containers.AdminPage.DashboardPage.inputs1": "Entradas", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Entradas por estado", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Seleccionar grupo de usuarios", "app.containers.AdminPage.DashboardPage.male": "masculino", "app.containers.AdminPage.DashboardPage.month": "mes", "app.containers.AdminPage.DashboardPage.noData": "No hay datos a mostrar", "app.containers.AdminPage.DashboardPage.noPhase": "No hay ninguna fase configurada para este proyecto", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "El número de participantes que publicaron aportaciones, reaccionaron o comentaron.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "No me gusta", "app.containers.AdminPage.DashboardPage.numberOfLikes": "Me gusta", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Número total de reacciones", "app.containers.AdminPage.DashboardPage.overview.management": "Gestión", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Proyectos y participación", "app.containers.AdminPage.DashboardPage.overview.showLess": "<PERSON><PERSON> menos", "app.containers.AdminPage.DashboardPage.overview.showMore": "Mostrar más", "app.containers.AdminPage.DashboardPage.participants": "Participantes", "app.containers.AdminPage.DashboardPage.participationPerProject": "Participación por proyecto", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Participación por tema", "app.containers.AdminPage.DashboardPage.perPeriod": "Por {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "30 días anteriores", "app.containers.AdminPage.DashboardPage.previous90Days": "90 días anteriores", "app.containers.AdminPage.DashboardPage.previousWeek": "Semana anterior", "app.containers.AdminPage.DashboardPage.previousYear": "<PERSON><PERSON> anterior", "app.containers.AdminPage.DashboardPage.projectType": "Tipo de proyecto : {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "Reacciones", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Este conjunto de datos base es necesario para calcular la representatividad de los usuarios de la plataforma en comparación con la población total.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "proporcione un conjunto de datos base.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Compruebe la representatividad de los usuarios de su plataforma en comparación con la población total, basándose en los datos recogidos durante el registro de los usuarios. Más información sobre {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Compruebe la representatividad de los usuarios de su plataforma en comparación con la población total, basándose en los datos recogidos durante el registro de los usuarios.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Representación de la comunidad", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Volver al panel", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Por el momento no se admite ninguno de los campos de registro habilitados.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Aquí puede mostrar/ocultar elementos en el panel e introducir los datos base. Aquí solo aparecerán los campos habilitados para {userRegistrationLink}.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Editar datos base", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "registro de usuario", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Enviar datos base", "app.containers.AdminPage.DashboardPage.resolutionday": "en días", "app.containers.AdminPage.DashboardPage.resolutionmonth": "en meses", "app.containers.AdminPage.DashboardPage.resolutionweek": "en semanas", "app.containers.AdminPage.DashboardPage.selectProject": "Seleccionar proyecto", "app.containers.AdminPage.DashboardPage.selectedProject": "filtro de proyecto actual", "app.containers.AdminPage.DashboardPage.selectedTopic": "filtro de tema actual", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Descubre lo que está sucediendo en la plataforma.", "app.containers.AdminPage.DashboardPage.tabOverview": "Descripción general", "app.containers.AdminPage.DashboardPage.tabReports": "Proyectos", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Representación", "app.containers.AdminPage.DashboardPage.tabUsers": "Usuarios", "app.containers.AdminPage.DashboardPage.timelineType": "Línea de tiempo", "app.containers.AdminPage.DashboardPage.titleDashboard": "Tablero de controles", "app.containers.AdminPage.DashboardPage.total": "Total", "app.containers.AdminPage.DashboardPage.totalForPeriod": "Este {period}", "app.containers.AdminPage.DashboardPage.true": "verdadero", "app.containers.AdminPage.DashboardPage.unspecified": "no se especifica", "app.containers.AdminPage.DashboardPage.users": "Usuarios", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Usuarios por edad", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Usuarios por zona geográfica", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Usuarios por género", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Registros", "app.containers.AdminPage.DashboardPage.week": "semana", "app.containers.AdminPage.FaviconPage.favicon": "Imagen que representa tu web", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Utilice una simple imagen ya que se mostrará muy poco. Debe ser un PNG cuadrado. Óptimamente, transparente. Si no, utilice un fondo blanco. Esto debe establecer una vez y cambiarse lo menos posible.", "app.containers.AdminPage.FaviconPage.save": "Guardar", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "<PERSON>go sali<PERSON> mal, por favor Inténtalo más tarde.", "app.containers.AdminPage.FaviconPage.saveSuccess": "¡Perfecto!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Los cambios han sido guardados.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Eliminar ", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Administradores de carpetas", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Los administradores de carpetas pueden editar la descripción de la carpeta, crear nuevos proyectos dentro de la carpeta y tener derechos de administración de proyectos sobre todos los proyectos dentro de la carpeta. No pueden eliminar proyectos y no tienen acceso a proyectos que no estén dentro de su carpeta. Puede {projectManagementInfoCenterLink} para encontrar más información sobre los derechos de gestión de proyectos.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "No hubo coincidencia", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "visitar nuestro Centro de Ayuda", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Búsqueda de usuarios", "app.containers.AdminPage.FoldersEdit.addToFolder": "Añadir a la carpeta", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Archivado", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Eliminar esta carpeta", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Descripción", "app.containers.AdminPage.FoldersEdit.draftStatus": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Agregar archivos a esta carpeta", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Archivos no deben ser mayores de 50 Mb. Los archivos agregados se visualizarán en la página de la carpeta.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Descripciones", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "No hay proyectos en esta carpeta. Vuelve a la pestaña principal de proyectos para crear y añadir nuevos proyectos.", "app.containers.AdminPage.FoldersEdit.folderName": "Nombre de carpeta", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Imagen del encabezado", "app.containers.AdminPage.FoldersEdit.multilocError": "Todos los campos de texto deben ser rellenados para cada idioma.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "No hay proyectos que puedas añadir a esta carpeta.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "<PERSON><PERSON> <PERSON> carpeta", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Derechos de acceso", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Proyectos de la carpeta", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Configuración", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Proyectos añadidos a esta carpeta", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Los proyectos que puedes añadir a esta carpeta", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "<PERSON>ja si esta carpeta es \"borrador\", \"publicado\" o \"archivado\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Publicado", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "<PERSON><PERSON><PERSON> <PERSON>", "app.containers.AdminPage.FoldersEdit.save": "Guardar", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Algo salió mal. Por favor Inténtalo más tarde.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "¡Éxito!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Los cambios han sido guardados.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Breve descripción", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "Se muestra en la página de inicio", "app.containers.AdminPage.FoldersEdit.statusLabel": "Estado de la carpeta", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Explica por qué los proyectos están unidos, define una identidad visual y comparte información.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Explica por qué los proyectos están unidos, define una identidad visual y comparte información.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Debes rellenar todos los campos de texto.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "<PERSON><PERSON><PERSON> una nueva carpeta", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Configuración", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "<PERSON>er <PERSON>a", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Personalice la imagen y el texto del banner principal.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Banner principal", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Guardar el banner principal", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "El Centro de Inspiración es un lugar donde puedes encontrar inspiración para tus proyectos navegando por proyectos de otras plataformas.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Edite los términos y condiciones y la política de privacidad de su plataforma. Las otras páginas, incluidas las páginas \"Acerca de\" y \"Preguntas frecuentes\", pueden editarse en la pestaña {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Políticas de la plataforma", "app.containers.AdminPage.PagesEdition.privacy-policy": "Política de privacidad", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Términos y condiciones", "app.containers.AdminPage.Project.confirmation.description": "Esta acción no se puede deshacer.", "app.containers.AdminPage.Project.confirmation.no": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.confirmation.title": "¿Estás seguro de que quieres restablecer el estado original, y resetear todos los datos de participación?", "app.containers.AdminPage.Project.confirmation.yes": "Resetear los datos de participación", "app.containers.AdminPage.Project.data.descriptionText1": "<PERSON>rra ideas, comentarios, votos, reacciones, respuestas a encuestas, respuestas a sondeos, voluntarios e inscritos en eventos. En el caso de las fases de votación, esta acción borrará los votos pero no las opciones.", "app.containers.AdminPage.Project.data.title": "Borrar todos los datos de participación de este proyecto", "app.containers.AdminPage.Project.resetParticipationData": "Resetear todos los datos de participación", "app.containers.AdminPage.Project.settings.accessRights": "Derechos de acceso", "app.containers.AdminPage.Project.settings.back": "Volver", "app.containers.AdminPage.Project.settings.data": "Datos", "app.containers.AdminPage.Project.settings.description": "Descripción", "app.containers.AdminPage.Project.settings.events": "Eventos", "app.containers.AdminPage.Project.settings.general": "General", "app.containers.AdminPage.Project.settings.projectTags": "Etiquetas de entrada permitidas", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Lista de proyectos en la plataforma", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Panel de control de proyectos", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Crea nuevos proyectos o gestiona los ya existentes.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Proyectos", "app.containers.AdminPage.ProjectDashboard.published": "Publicado", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Cerrar el panel de configuración", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Centro", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "<PERSON><PERSON> completo", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "Iz<PERSON>erda", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "Alineación del botón", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "Derecha", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Texto del botón", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Introduzca el texto del botón", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "Primario", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Tipo de botón", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Secundario", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "URL del botón", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Introduzca una URL para el botón", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Diseño de columnas", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Descripción completa", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Descripción en la página de inicio", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Se muestra en la ficha de proyecto en la página de inicio.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Se muestra en la página del proyecto. Describa claramente de que se trata el proyecto, qué esperas de los usuarios y lo que pueden esperar de usted.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Algo salió mal. Por favor inténtalo más tarde.", "app.containers.AdminPage.ProjectDescription.preview": "Vista previa", "app.containers.AdminPage.ProjectDescription.save": "Guardar", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Los cambios han sido guardados.", "app.containers.AdminPage.ProjectDescription.saved": "¡Guardado!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Crea un mensaje claro sobre el proyecto para tu audiencia. <PERSON><PERSON> tu proyecto con imágenes, videos y archivos adjuntos.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Descripción del proyecto", "app.containers.AdminPage.ProjectDescription.whiteSpace": "Espacio en blanco", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "<PERSON><PERSON><PERSON> borde", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "Altura vertical", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "Grande", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Media", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Pequeño", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "La latitud por defecto del punto central del mapa. Acepta un valor entre -90 y 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "La longitud por defecto del punto central del mapa. Acepta un valor entre -180 y 180.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "<PERSON><PERSON> capa", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "<PERSON><PERSON> capa", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Algo salió mal, por favor inténtalo más tarde", "app.containers.AdminPage.ProjectEdit.MapTab.here": "aquí", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Importar un archivo GeoJSON", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Default latitude", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Color de la capa", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Este color se aplica a todas las características dentro de la capa del mapa. El tamaño de los marcadores, el grosor de las líneas y la opacidad del relleno son fijos por defecto.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Icono del marcador", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Opcionalmente, seleccione un icono que se muestre en los marcadores. Haga clic en {url} para ver la lista de iconos que puede seleccionar.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Nombre de la capa", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Este nombre de capa se muestra en la leyenda del mapa", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Información sobre la capa", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Este texto se muestra como información de la herramienta al pasar por encima de las características de la capa en el mapa", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Capas", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "Actualmente soportamos archivos GeoJSON. Lea el {supportArticle} para obtener consejos sobre cómo convertir y ajustar el estilo de capas de los mapas.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Default longitude", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Centro y zoom del mapa por defecto", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Punto central y nivel de zoom por defecto del mapa. Ajuste manualmente los valores que aparecen a continuación, o haga clic en el botón {button} de la esquina superior derecha del mapa para guardar el punto central y el nivel de zoom actuales como valores predeterminados.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Personalice la vista del mapa, incluyendo la carga y el estilo de las capas del mapa y la configuración del centro del mapa y el nivel de zoom.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Configuración del mapa", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "Actualmente, la configuración del mapa se comparte en todas las fases, no puedes crear configuraciones de mapa diferentes por fase.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Eliminar la capa", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Guardar", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Guardar zoom", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-viewhttps://support.govocal.com/es/articles/7025887-crear-un-proyecto-de-encuesta-externa", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Capa sin nombre", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Nivel de zoom por defecto", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Establece el grado de ampliación del mapa por defecto. Elige un valor entre 0 y 20, donde 0 es un alejamiento total (todo el mundo es visible) y 20 es un acercamiento total (los bloques y edificios son visibles)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "<PERSON><PERSON><PERSON><PERSON> todos los datos de los usuarios", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Todas las aportaciones de los usuarios a la encuesta se anonimizarán antes de ser registradas", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Los usuarios seguirán teniendo que cumplir los requisitos de participación en la pestaña de acceso \"Derechos de acceso\". Los datos del perfil de usuario no estarán disponibles cuando exporte los resultados de la encuesta.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "Si activas esta opción, los campos de registro de usuario se mostrarán como la última página de la encuesta en lugar de como parte del proceso de registro.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Campos demográficos en el formulario de la encuesta", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "¿Mostrar campos demográficos en la encuesta?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "Lee más sobre cómo funciona el mecanismo que comparte resultados automáticamente en este artículo.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/es/articles/8124630-metodos-de-votacion-y-priorizacion-para-mejorar-la-toma-de-decisiones", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Compartir resultados automáticamente", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Los resultados de las votaciones se comparten en la plataforma y por correo electrónico a los participantes cuando finaliza la fase. Esto garantiza la transparencia del proceso por defecto.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Compartir resultados", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Añadir una opción de respuesta", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Añadir una pregunta de la encuesta", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Eliminar", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Eliminar", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Editar opción de respuesta", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Guardar opciones de respuesta", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Editar opciones de respuesta", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Exporta los resultados de la consulta", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "El número máximo de opciones es mayor que el número de opciones", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Opción múltiple", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "Sin opciones", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "La encuesta no será factible como está, todas las preguntas deben tener opciones", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Una sola opción", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Los encuestados sólo tienen una opción", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Gestiona las opciones de respuesta para: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Aquí puede crear preguntas de encuesta, establecer las opciones de respuesta para que los participantes elijan para cada pregunta, decidir si quiere que los participantes sólo puedan seleccionar una opción de respuesta (opción única) o múltiples opciones de respuesta (opción múltiple), y exportar los resultados de la encuesta. Puede crear varias preguntas de sondeo dentro de un mismo sondeo.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Guardar", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "Guardar", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "Guarda", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Una sola opción", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Ajustes y resultados de la encuesta", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Importar", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "Dar retroalimentación, asignar temas o copiar los aportes de la siguiente fase del proyecto.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "Gestiona las propuestas, haz comentarios y asigna temas.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Gestión de aportes", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "El compartir resultados está desactivado.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Los resultados de las votaciones no se compartirán al final de la fase, a menos que lo modifiques en la configuración de la fase.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Estos resultados se compartirán automáticamente cuando finalice la fase. Modifica la fecha de finalización de esta fase para cambiar cuándo se comparten los resultados.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Exportar los resultados de la encuesta (.xslx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Resul<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Aquí puede descargar los resultados de las encuestas Typeform dentro de este proyecto como un archivo de Excel.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "Formulario de encuesta", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Resultados de la encuesta", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Consulta", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Consulta las respuestas de la encuesta", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Añade una causa", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "¿Estás seguro?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Descripción", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Usa este espacio para explicar lo que se requiere de los voluntarios y lo que pueden esperar.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "No se pudo guardar porque el formulario tiene errores.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Imagen", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Eliminar", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Una causa es una acción o actividad a la que los ciudadanos pueden ofrecerse voluntariamente.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Editar la causa", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Añadir una descripción", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Añadir un título", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Exportar los voluntarios", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Una causa es una acción o actividad a la que los ciudadanos pueden ofrecerse voluntariamente.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "Nueva causa", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Guardar", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "<PERSON>qu<PERSON>, puedes establecer las causas para las que los usuarios pueden ser voluntarios y exportar los voluntarios.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Voluntariado", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {ningún voluntario} one {# voluntario} other {# voluntarios}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "asignación presupuestaria", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Asigna un presupuesto a las opciones y pide a los participantes que seleccionen sus opciones preferidas, ajustándose a un presupuesto total.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Asignación presupuestaria", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Permitir que los usuarios comenten puede sesgar el proceso de votación.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Vista por defecto de las opciones", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Acciones para los usuarios", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Selecciona qué acciones adicionales pueden realizar los usuarios.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Importe fijo", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "<PERSON> se deja vacío, por defecto será \"votar\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "Más información sobre cuándo utilizar <b> {voteTypeDescription} </b> en nuestro {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Máximo de votos por opción", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Número máximo de votos", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Puedes limitar el número de votos que un usuario puede emitir en total (con un máximo de un voto por opción).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "varios votos por opción", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Los usuarios reciben una cantidad de fichas para distribuir entre las opciones", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Varios votos por opción", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Número de votos por usuario", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Visión general del análisis de opciones", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Opciones para votar", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "Punt<PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "un voto por opción", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Los usuarios pueden elegir aprobar cualquiera de las opciones", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Un voto por opción", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Token", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "¿Cómo debe llamarse una votación?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "Por e<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, puntos, créditos de carbono...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "Por e<PERSON><PERSON><PERSON>, fi<PERSON>, punto, cré<PERSON>o de carbono...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "Voto", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Cada método de votación tiene diferentes configuraciones previas", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "Método de votación", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "El método de votación determina las reglas de cómo votan los usuarios", "app.containers.AdminPage.ProjectEdit.addNewInput": "Añadir una entrada", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "<PERSON><PERSON><PERSON> a<PERSON> tu proyecto a una carpeta ahora, o hacerlo más tarde en la configuración del proyecto", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Etiquetas de entrada permitidas", "app.containers.AdminPage.ProjectEdit.altText": "Texto alternativo", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Votación anónima", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "Cuando se habilita es imposible ver quién votó qué. Los usuarios necesitan una cuenta y sólo pueden votar una vez.", "app.containers.AdminPage.ProjectEdit.approved": "Aprobado", "app.containers.AdminPage.ProjectEdit.archived": "Archivado", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Los proyectos archivados siguen siendo visibles, pero no permiten participación", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Archivado", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Esta área no puede eliminarse porque se está utilizando para mostrar proyectos en la(s) siguiente(s) página(s) más personalizada(s). Tendrá que desvincular el área de la página o eliminar la página antes de poder eliminar el área.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Todas las localidades o zonas", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "El proyecto se mostrará en todos los filtros de área.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Filtro de área", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Se pueden filtrar los proyectos en la página de inicio utilizando áreas. Las áreas se pueden establecer {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "aquí", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Sin área específica", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "El proyecto no se mostrará cuando se filtre por área.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Selección", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "El proyecto se mostrará en el(los) filtro(s) de área seleccionado(s).", "app.containers.AdminPage.ProjectEdit.cardDisplay": "En una lista", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "Lo más discutido", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Añadir contenido a la encuesta", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Ya han empezado a llegar las respuestas a esta encuesta. Los cambios en la encuesta pueden dar lugar a la pérdida de datos y a datos incompletos en los archivos exportados.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Encuesta guardada correctamente", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "Encuesta", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "Ver encuesta", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Selecciona un método de votación, y haz que los usuarios prioricen entre algunas opciones diferentes.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Realizar un ejercicio de votación o priorización", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Crear un proyecto a partir de una plantilla", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "<PERSON>rear una encuesta externa", "app.containers.AdminPage.ProjectEdit.createInput": "Añadir nueva entrada", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "<PERSON><PERSON>r una encuesta en la plataforma", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Configurar una encuesta sin salir de nuestra plataforma.", "app.containers.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON><PERSON> una encuesta", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Prepara un cuestionario de opción múltiple.", "app.containers.AdminPage.ProjectEdit.createProject": "Nuevo proyecto", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Incrustar un formulario de Typeform, de Google, de SurveyXact, de Qualtrics o una encuesta de Enalyzer.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "<PERSON><PERSON><PERSON> escoger por defecto el orden en el cual se desplegarán en la página del proyecto principal.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Ordenar", "app.containers.AdminPage.ProjectEdit.departments": "Departamentos", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Descripción", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "Esto activará o desactivará el no gustar, pero el gustar seguirá activado. Te recomendamos que lo dejes desactivado a menos que estés realizando un análisis de opciones.", "app.containers.AdminPage.ProjectEdit.disabled": "Desactivado", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "<PERSON>úmer<PERSON> de 'no me gusta' por participante", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Activar 'no me gusta'", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Recolectar opiniones y comentarios sobre un documento", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Incrusta un PDF interactivo y recolecta comentarios y opiniones con Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Desactivado", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Activado", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Los borradores de proyectos están ocultos para todas las personas excepto para los administradores y gestores de proyectos asignados.", "app.containers.AdminPage.ProjectEdit.draft": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.draftStatus": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Administrar", "app.containers.AdminPage.ProjectEdit.enabled": "Activado", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Selecciona qué acciones participativas pueden realizar los usuarios.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Eventos", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Archivos adjuntos (máximo 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Los archivos agregados aparecerán en la página de información de proyecto.", "app.containers.AdminPage.ProjectEdit.filesTab": "Archivos", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Encuentra voluntarios", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Pide a los participantes que se ofrezcan como voluntarios para diversas actividades y causas.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "Como gestor de carpetas, puedes elegir una carpeta al crear el proyecto, pero sólo un administrador puede cambiarla después", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Texto alternativo de la imagen de la tarjeta de la carpeta", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Selecciona una carpeta a la que añadir este proyecto.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Contenido personalizado", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Ya han empezado a llegar envíos a este formulario. Los cambios en el formulario pueden dar lugar a la pérdida de datos y a datos incompletos en los archivos exportados.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Formulario guardado correctamente", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Fin de la encuesta", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Desde una plantilla", "app.containers.AdminPage.ProjectEdit.generalTab": "General", "app.containers.AdminPage.ProjectEdit.google_forms": "Formularios de Google", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Texto alternativo de la imagen de cabecera", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Imagen de encabezado", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NUEVO", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Proporciona información a los usuarios, o utiliza el generador de informes para compartir los resultados de las fases anteriores.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Compartir información o resultados", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Recopilar aportes y retroalimentaciones", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Crear o recopilar aportes, votos y/o comentarios. Elegir entre diferentes tipos de aportes: recopilación de ideas, análisis de opciones, preguntas y respuestas, identificación de problemas y más.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "¿Quién se encarga de procesar los aportes?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Todos los nuevos aportes en este proyecto serán asignados a esta persona. La persona asignada puede ser cambiada en {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Comentar los aportes", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Formulario para los aportes", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "sestión de aportes", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Gestión de aportes", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Envia tus aportes", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Reaccionar a los aportes", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Vista predeterminada", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Elija la vista predeterminada de los aportes de los participantes: como vista de tarjetas en una cuadrícula o puntos en un mapa. Los participantes pueden cambiar manualmente entre las dos visualizaciones.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Biblioteca de Inspiración", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Incrustar una dirección web de Konveio", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Número máximo de aportes en los que un participante puede darle a me gusta (en la ideación) o votar (en las propuestas)", "app.containers.AdminPage.ProjectEdit.limited": "Limitada", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Cargar más plantillas", "app.containers.AdminPage.ProjectEdit.mapDisplay": "En una mapa", "app.containers.AdminPage.ProjectEdit.mapTab": "Mapa", "app.containers.AdminPage.ProjectEdit.maxDislikes": "Máximo número de no me gusta", "app.containers.AdminPage.ProjectEdit.maxLikes": "Máximo número de <PERSON>", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "El número máximo de votos por opción debe ser inferior o igual al número total de votos", "app.containers.AdminPage.ProjectEdit.maximum": "Máximo", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Los participantes no pueden superar el presupuesto máximo al presentar su bolsa.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Exije a los participantes un presupuesto mínimo para presentar su bolsa (introduzca \"0\" si no desea establecer un mínimo).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "visitar nuestro Centro de Ayuda", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "¿Quiénes son los administradores de proyecto?", "app.containers.AdminPage.ProjectEdit.moreDetails": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "¿Necesitas inspiración? Explora proyectos similares de otras ciudades en {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "Enviar una propuesta", "app.containers.AdminPage.ProjectEdit.newIdea": "Nueva idea", "app.containers.AdminPage.ProjectEdit.newInitiative": "Añade una iniciativa", "app.containers.AdminPage.ProjectEdit.newIssue": "Agregar un problema", "app.containers.AdminPage.ProjectEdit.newOption": "Añadir una opción", "app.containers.AdminPage.ProjectEdit.newPetition": "Añadir una petición", "app.containers.AdminPage.ProjectEdit.newProject": "Nuevo proyecto", "app.containers.AdminPage.ProjectEdit.newProposal": "<PERSON><PERSON>dir una propuesta", "app.containers.AdminPage.ProjectEdit.newQuestion": "<PERSON><PERSON><PERSON> una pregunta", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Más reciente", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "No es una cantidad valida", "app.containers.AdminPage.ProjectEdit.noFolder": "No hay carpeta", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "— No hay carpeta —", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "No se encontraron plantillas", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Esto no puede estar vacío", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Proporcione el máximo número de votos permitidos por usuario", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Más antigua", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Solamente administradores pueden ver", "app.containers.AdminPage.ProjectEdit.optionNo": "No", "app.containers.AdminPage.ProjectEdit.optionYes": "Sí (seleccione la carpeta)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Método de participación", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "¿Qué quieres hacer?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Elegir cómo los usuarios pueden participar.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "Puede especificar quién puede realizar cada acción y hacer preguntas adicionales a los participantes para recabar más información.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Requisitos y preguntas para el usuario", "app.containers.AdminPage.ProjectEdit.pendingReview": "Pendiente de aprobación", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Derechos de acceso", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "Derechos de acceso", "app.containers.AdminPage.ProjectEdit.phaseEmails": "Notificaciones", "app.containers.AdminPage.ProjectEdit.pollTab": "Consulta ciudadana", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "Con más reacciones", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Imagen de proyecto", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Esta imagen forma parte de la tarjeta del proyecto; la tarjeta que resume el proyecto y se muestra, por ejemplo, en la página de inicio.\n\n    Para más información sobre las resoluciones de imagen recomendadas, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Carpeta", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Esta imagen se muestra en la parte superior de la página del proyecto.\n\n    Para más información sobre las resoluciones de imagen recomendadas, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Texto alternativo de la imagen de la tarjeta del proyecto", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Proporciona una breve descripción de la imagen para los usuarios con deficiencias visuales. Esto ayuda a los lectores de pantalla a transmitir de qué trata la imagen.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Administración de proyectos", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Los administradores de proyectos pueden editar los proyectos, gestionar los aportes y enviar por email a los participantes. Puedes encontrar más información sobre los derechos de quienes gestionan los proyectos en {moderationInfoCenterLink}.", "app.containers.AdminPage.ProjectEdit.projectName": "Nombre del proyecto", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Tipo de proyecto", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "No se puede cambiar el tipo de proyecto después de su creación. Proyectos con una línea de tiempo tienen un principio y final claro y pueden tener diferentes fases. Proyectos sin una línea de tiempo son continuos.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "El tipo de proyecto no puede ser cambiado más tarde.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "<PERSON><PERSON>e configurar el proyecto para que sea invisible para determinados usuarios.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Visibilidad del proyecto", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "¿Buscas el estado del proyecto? Ahora puedes cambiarlo en cualquier momento directamente desde la cabecera de la página del proyecto.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Los proyectos publicados son visibles para todos o para un subconjunto del grupo seleccionado.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Publicado", "app.containers.AdminPage.ProjectEdit.purposes": "Propósitos", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Aleat<PERSON>", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Resetear los datos de participación", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Error al guardar los datos. Por favor, inténtalo de nuevo.", "app.containers.AdminPage.ProjectEdit.saveProject": "Guardar", "app.containers.AdminPage.ProjectEdit.saveSuccess": "¡guardado!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "¡Su formulario ha sido guardado!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Buscar las plantillas", "app.containers.AdminPage.ProjectEdit.selectGroups": "Selecciona el(los) grupo(s)", "app.containers.AdminPage.ProjectEdit.setup": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.shareInformation": "Comparte información", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Encuesta rápida", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Configurar y personalizar tu proyecto.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "visite nuestro centro de soporte", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Añadir contenido a la encuesta", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# opciones} one {# opción} other {# opciones}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON><PERSON>, quiero irme", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Ya han empezado a llegar las respuestas a esta encuesta. Los cambios en la encuesta pueden dar lugar a la pérdida de datos y a datos incompletos en los archivos exportados.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "Carga del archivo", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "Volver", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Importar", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Importar", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "Puedes acceder a los resúmenes de IA de las preguntas de seguimiento de respuesta corta, respuesta larga y escala de sentimientos desde la pestaña IA de la barra lateral izquierda.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Escala lineal", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "<PERSON><PERSON><PERSON>a larga", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Opción múltiple - elija muchas", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Elección de imagen - el<PERSON> muchas", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "Nuevo envío", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Aún no hay respuestas a la encuesta", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Abierto a respuestas", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Abierto a respuestas", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Opcional", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "Si no se añade ninguna lógica, la encuesta seguirá su flujo normal. Si tanto la página como sus preguntas tienen lógica, la lógica de la pregunta tendrá prioridad. Asegúrate de que esto se ajusta al flujo de tu encuesta. Para más información, visita {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "Ubicación", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "¿Está seguro de que quiere salir?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Los cambios no se guardarán.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Ranking", "app.containers.AdminPage.ProjectEdit.survey.rating": "Valoración", "app.containers.AdminPage.ProjectEdit.survey.required2": "Obligatorio", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {respuestas} one {respuesta} other {respuestas}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# respuestas} one {# respuesta} other {# respuestas}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Opción múltiple - elija una", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Escala de opinión", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Carga de archivos shapefile de Esri", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Encuesta guardada correctamente", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Encuesta", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Respuestas a la encuesta", "app.containers.AdminPage.ProjectEdit.survey.text2": "Respuesta corta", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Total {count} de respuestas", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "Ver encuesta", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "<PERSON>er", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "URL incrustada", "app.containers.AdminPage.ProjectEdit.surveyService": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Elegir qué herramienta de encuesta desea incrustar. Toda la información se encuentra a {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-projecthttps://support.govocal.com/es/articles/7025887-crear-un-proyecto-de-encuesta-externa", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "aquí", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Esta etiqueta no puede eliminarse porque se está utilizando para mostrar proyectos en la(s) siguiente(s) página(s) personalizada(s). \nDeberá desvincular la etiqueta de la página o eliminar la página antes de poder eliminar la etiqueta.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Ajustes generales del proyecto", "app.containers.AdminPage.ProjectEdit.titleLabel": "Nombre del proyecto", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Elige un titulo que sea breve, atractivo y claro. Va ser mostrado en la vista desplegable del proyecto y en la tarjeta de proyecto en la página de inicio.", "app.containers.AdminPage.ProjectEdit.topicLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Seleccione {topicsCopy} para este proyecto. Los usuarios pueden utilizarlas para filtrar los proyectos por.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Presupuesto total", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Tendencias", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "<PERSON>", "app.containers.AdminPage.ProjectEdit.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Usar plantilla", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "Vista del proyecto", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Voluntariado", "app.containers.AdminPage.ProjectEdit.voteTermError": "Los términos de votación deben especificarse para todas las localizaciones", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, one {# grupos} other {# grupos}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Agregar un evento", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Información adicional", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Dirección 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Dirección de la calle del lugar del evento", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Dirección 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "Por ejemplo: Apartamento, Suite, Edificio", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Información adicional sobre la dirección que pueda ayudar a identificar el lugar, como el nombre del edificio, el número de planta, etc.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "Ver el artículo de apoyo", "app.containers.AdminPage.ProjectEvents.customButtonLink": "Enlace externo", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Añade un enlace a una URL externa (por ejemplo, un servicio de eventos o un sitio web de venta de entradas). Al establecer esto, se anulará el comportamiento predeterminado del botón de asistencia.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Texto del botón personalizado", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Cambia el texto del botón en un valor distinto de \"Registrarse\" cuando se configura una URL externa.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "<PERSON><PERSON>o", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Eliminar", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "¿Está seguro que desea eliminar este evento? ¡No hay manera de deshacer esto!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Descripción del evento", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Editar evento", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "Para enviar correos electrónicos a los inscritos directamente desde la plataforma, los administradores deben crear un grupo de usuarios en la pestaña {userTabLink} . {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "Fechas de evento", "app.containers.AdminPage.ProjectEvents.eventImage": "Imagen del evento", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Texto alternativo de la imagen del evento", "app.containers.AdminPage.ProjectEvents.eventLocation": "Lugar del evento", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Exportar inscritos", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Archivos adjuntos (máximo 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Los archivos adjuntos se mostrarán en la tarjeta del evento directamente.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Ubicación", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Máximo de inscritos", "app.containers.AdminPage.ProjectEvents.newEventTitle": "<PERSON>rear un nuevo evento", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Enlace al evento en línea", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "Si tu acto está en línea, añade aquí un enlace al mismo.", "app.containers.AdminPage.ProjectEvents.preview": "Vista previa", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Refinar la ubicación del mapa", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Refinar ubicación en el mapa", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "Puedes afinar dónde se muestra el marcador de localización de tu evento haciendo clic en el mapa de abajo.", "app.containers.AdminPage.ProjectEvents.register": "Regístrate", "app.containers.AdminPage.ProjectEvents.registerButton": "Botón de registro", "app.containers.AdminPage.ProjectEvents.registrant": "registrante", "app.containers.AdminPage.ProjectEvents.registrants": "inscritos", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Límite de inscripción", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Guardar", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "No podemos guardar los cambios, por favor, inténtelo de nuevo.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "¡guardado!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Los cambios han sido guardados.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "Buscar una ubicación", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Añadir eventos offline o reuniones que están vinculados al proyecto. ", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "<PERSON><PERSON><PERSON><PERSON> y fechas", "app.containers.AdminPage.ProjectEvents.titleEvents": "Eventos de proyecto", "app.containers.AdminPage.ProjectEvents.titleLabel": "Nombre del evento", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Enlaza el botón a una URL externa", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "Por defecto, se mostrará el botón de registro de eventos de la plataforma, que permite a los usuarios registrarse en un evento. Puedes cambiarlo para que enlace a una URL externa.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Limitar el número de inscritos en el evento", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Establece un número máximo de inscritos al evento. Si se alcanza el límite, no se aceptarán más inscripciones.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/users", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Usuarios", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "Añade archivos a tu proyecto", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Adjunta archivos de esta lista a tu proyecto, fases y eventos.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Añadir archivos como contexto a Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Añade archivos a tu proyecto Sensemaking para proporcionar contexto y perspectivas.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Próximamente", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Sincroniza encuestas, carga entrevistas y deja que la IA conecte los puntos de tus datos.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Sube cualquier archivo", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Utiliza la IA para analizar archivos", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Procesa transcripciones, etc.", "app.containers.AdminPage.ProjectFiles.addFiles": "Adjunta archivos (máximo 50MB)", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "Recomendaciones basadas en IA", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "Analiza los archivos cargados para sacar a la luz los temas clave.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Permite el análisis avanzado de estos archivos mediante el procesamiento de IA.", "app.containers.AdminPage.ProjectFiles.askButton": "Pregunta", "app.containers.AdminPage.ProjectFiles.categoryLabel": "Categoría", "app.containers.AdminPage.ProjectFiles.chooseFiles": "Elegir archivos", "app.containers.AdminPage.ProjectFiles.close": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Confirmar y subir", "app.containers.AdminPage.ProjectFiles.confirmDelete": "¿Estás seguro de que quieres eliminar este archivo?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "No se ha podido cargar el archivo markdown.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "No se ha podido cargar la vista previa del CSV.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "En las previsualizaciones CSV se muestran un máximo de 50 filas.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "El archivo CSV es demasiado grande para previsualizarlo.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "Eliminar archivo", "app.containers.AdminPage.ProjectFiles.description": "Descripción", "app.containers.AdminPage.ProjectFiles.done": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.downloadFile": "Descargar archivo", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Descargar archivo completo", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Arrastra y suelta aquí cualquier archivo o", "app.containers.AdminPage.ProjectFiles.editFile": "Editar archivo", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Descripción", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "El nombre del archivo no puede contener un punto.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "Nombre del archivo", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "El nombre del archivo es obligatorio.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Descargar archivo", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Vista previa", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "Este archivo no se subirá, ya que supera el límite máximo de 50 MB.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "Todos los archivos se han cargado correctamente", "app.containers.AdminPage.ProjectFiles.generatingPreview": "Generando vista previa...", "app.containers.AdminPage.ProjectFiles.info_sheet": "Información", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON>, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Entrevistas grabadas, reuniones del ayuntamiento", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "Por ejemplo, PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "Informes, documentos informativos", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "<PERSON><PERSON>, PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.interview": "Entrevista", "app.containers.AdminPage.ProjectFiles.maxFilesError": "<PERSON><PERSON><PERSON> puedes subir un máximo de {maxFiles} archivos a la vez.", "app.containers.AdminPage.ProjectFiles.meeting": "Reunión", "app.containers.AdminPage.ProjectFiles.noFilesFound": "No se han encontrado archivos.", "app.containers.AdminPage.ProjectFiles.other": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.policy": "Política", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "La vista previa aún no es compatible con este tipo de archivo.", "app.containers.AdminPage.ProjectFiles.report": "Informe", "app.containers.AdminPage.ProjectFiles.retryUpload": "Reintentar subida", "app.containers.AdminPage.ProjectFiles.save": "Guardar", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "Archivo actualizado correctamente.", "app.containers.AdminPage.ProjectFiles.searchFiles": "Buscar archivos", "app.containers.AdminPage.ProjectFiles.selectFileType": "Tipo de archivo", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Plan estratégico", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "<PERSON><PERSON><PERSON> puedes subir un máximo de {maxFiles} archivos a la vez.", "app.containers.AdminPage.ProjectFiles.unknown": "Desconocido", "app.containers.AdminPage.ProjectFiles.upload": "Sube", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# archivo} other {# archivos}} cargados con éxito, {numberOfErrors, plural, one {# error} other {# errores}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "Ver archivo", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Colapsar todos los campos", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Descripción del campo", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Editar formulario de entrada", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Activado", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Incluye este campo.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Algo salió mal, por favor inténtalo más tarde.", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Expandir todos los campos", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Formulario de aportes", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Especifique qué información debe proporcionarse, añada breves descripciones o instrucciones para guiar las respuestas de los participantes y especifique si cada campo es opcional u obligatorio.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Especifique qué información se debe entregar, añade breves descripciones o instrucciones para orientar las respuestas de los participantes y especifique si cada campo es opcional u obligatorio", "app.containers.AdminPage.ProjectIdeaForm.required": "Obligatorio", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Este campo debe ser llenado.", "app.containers.AdminPage.ProjectIdeaForm.save": "Guardar", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Los cambios han sido guardados con éxito.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Guardado!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "Ver formulario", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Correos electrónicos automatizados", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "<PERSON><PERSON>es configurar correos electrónicos activados a nivel de fase", "app.containers.AdminPage.ProjectTimeline.datesLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "Responde la consulta", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Encuesta", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "¿Estás seguro que desea eliminar esta fase?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Descripción de la fase", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "Editar etapa", "app.containers.AdminPage.ProjectTimeline.endDate": "Fecha de finalización", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Fecha de finalización", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Archivos adjuntos (máximo 50MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "<PERSON><PERSON><PERSON> una nueva fase", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "Esta fase no tiene una fecha de finalización predefinida.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "La compartición de resultados de algunos métodos (como los resultados de las votaciones) no se activará hasta que se seleccione una fecha de finalización.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "En cuanto añadas una fase después de ésta, añadirá una fecha de finalización a esta fase.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "No seleccionar una fecha de finalización implica que:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Vista previa", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "Guardar cambios", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Error al enviar el formulario, por favor, inténtelo de nuevo.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "¡guardado!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Los cambios han sido guardados con éxito.", "app.containers.AdminPage.ProjectTimeline.startDate": "Fecha de inicio", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Fecha de inicio", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "Botón", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "Tí<PERSON>lo de la encuesta", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Nombre de la fase", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Subir archivos adjuntos", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartición", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminología (filtro de la página principal)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "¿Cómo deben llamarse las etiquetas en el filtro de la página principal? Por ejemplo, etiquetas, categorías, departamentos, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "<PERSON><PERSON><PERSON> configurarse las etiquetas {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "aquí", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "T<PERSON>rm<PERSON> para una etiqueta (singular)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tema", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Término para varias etiquetas (plural)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "etiquetas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Agregar campo", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Añadir un nuevo campo de registro", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Agregar opción", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Formato de respuesta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Proporcionar un formato de respuesta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Opción de respuesta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Proporcionar una opción de respuesta para todos los idiomas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Guardar la opción de respuesta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Opción de respuesta guardada con éxito", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Opciones de respuesta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Campos", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Arrastre y suelte los campos para determinar el orden en que aparecen en el formulario de inscripción.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Campo predeterminado", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Eliminar", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Texto opcional mostrado bajo el campo de nombre en el formulario de inscripción.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Las opciones de respuesta para el lugar de residencia se pueden establecer en el {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Editar la opción de respuesta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Descripción", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Campo para el nombre", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Proporcionar un nombre de campo para todos los idiomas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Ajustes en los campos del formulario", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Sí-no (casilla de verificación)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "<PERSON><PERSON><PERSON>a larga", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Selección múltiple (seleccionar múltiple)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "<PERSON>or numérico", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Opción múltiple (seleccione una)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Respuesta corta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Pestaña de áreas geográficas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Campo oculto", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "¿Hacer obligatorio responder a este campo?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Campos personalizados", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Añadir la opción de respuesta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Eliminar", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "¿Estás seguro de que quieres eliminar esta opción de respuesta a la pregunta de registro? Respuestas de usuarios a esta opción se eliminarán permanentemente. Esta acción no se puede deshacer.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "¿Estás seguro de que quieres borrar esta pregunta de registro? Todas las respuestas que los usuarios hayan dado a esta pregunta se eliminarán permanentemente, y ya no se formulará en proyectos o propuestas. Esta acción no se puede deshacer.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Obligatorio", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Guardar campo", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Campo guardado con éxito", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "<PERSON><PERSON> columnas", "app.containers.AdminPage.SettingsPage.addAreaButton": "Añadir un área geográfica", "app.containers.AdminPage.SettingsPage.addTopicButton": "<PERSON><PERSON><PERSON> un tema", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Animal - ej. <PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Usuario - ej. <PERSON><PERSON> 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "Selecciona qué administradores recibirán notificaciones para aprobar proyectos. Po<PERSON> defecto, los administradores de carpetas aprueban todos los proyectos de sus carpetas.", "app.containers.AdminPage.SettingsPage.approvalSave": "Guardar", "app.containers.AdminPage.SettingsPage.approvalTitle": "Ajustes de aprobación del proyecto", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "¿Está seguro que desea eliminar este campo?", "app.containers.AdminPage.SettingsPage.areaTerm": "Término para una localidad/barrio/zona (singular)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "Localidad/barrio/zona", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Eliminar", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areasTerm": "Términos para múltiples localidades/barrios/zonas (plural)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "Localidad/barrio/zona", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Seleccione al menos un idioma.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatar<PERSON>", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Mostrar avatares", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Mostrar las fotos de perfil de los participantes y su número a los visitantes aún no registrados", "app.containers.AdminPage.SettingsPage.bannerHeader": "Texto de encabezado", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Texto de la cabecera para los visitantes no registrados", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Texto del subtítulo para los visitantes no registrados", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Texto de encabezado secundario", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Texto de banner", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Mostrar vista previa para", "app.containers.AdminPage.SettingsPage.brandingDescription": "Añada su logo y establezca los colores de la plataforma.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Imagen de marca de la plataforma", "app.containers.AdminPage.SettingsPage.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.chooseLayout": "Diseño", "app.containers.AdminPage.SettingsPage.color_primary": "Color primario", "app.containers.AdminPage.SettingsPage.color_secondary": "Color secundario", "app.containers.AdminPage.SettingsPage.color_text": "Color del texto", "app.containers.AdminPage.SettingsPage.colorsTitle": "Colores", "app.containers.AdminPage.SettingsPage.confirmHeader": "¿Estás seguro de que quieres borrar este tema?", "app.containers.AdminPage.SettingsPage.contentModeration": "Moderación de contenidos", "app.containers.AdminPage.SettingsPage.ctaHeader": "Botones", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Encabezado de página personalizado | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Personalizado", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Texto del botón", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Enlace al botón", "app.containers.AdminPage.SettingsPage.defaultTopic": "<PERSON><PERSON> predeterminado", "app.containers.AdminPage.SettingsPage.delete": "Eliminar", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Eliminar", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Esto eliminará el tema,  de todas las entradas existentes. Este cambio se aplicará en todos los proyectos.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Se pueden añadir temas para ayudar a categorizar los aportes. Aquí se puede añadir y eliminar temas que te gustaría utilizar en tu plataforma. Puede agregar los temas asociados a  proyectos específicos en {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Escritorio", "app.containers.AdminPage.SettingsPage.editFormTitle": "Editar area", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Editar un tema", "app.containers.AdminPage.SettingsPage.fieldDescription": "Descripción del área", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Esta descripción es sólo de colaboración interna con otros administradores, para tener un claro entendimiento de lo que se entiende por cada uno de las área.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Nombre del área", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Proporcionar un nombre de área para todos los idiomas", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "El nombre que elija para cada área será visible para los ciudadanos durante el registro y cuando filtren los proyectos.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Guardar etiqueta", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Nombre del tema", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Proporcionar un nombre de etiqueta para todos los idiomas", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "El nombre que elijas en los temas será visible para los ciudadanos.", "app.containers.AdminPage.SettingsPage.fixedRatio": "Proporción fija", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Este tipo de banner funciona mejor con imágenes que no deben recortarse, como imágenes con texto, un logotipo o elementos específicos que son importantes para sus ciudadanos. Este banner se sustituye por un cuadro sólido en el color primario cuando los usuarios inician sesión. Puede configurar este color en los ajustes generales. Puede encontrar más información sobre el uso recomendado de imágenes en nuestro {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "base de conocimientos", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "<PERSON><PERSON> completo", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Este banner se extiende a todo lo ancho para conseguir un gran efecto visual. La imagen intentará cubrir todo el espacio posible, haciendo que no siempre esté visible en todo momento. Puede combinar este banner con una sobreimpresión de cualquier color. Puede encontrar más información sobre el uso recomendado de las imágenes en nuestro {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "base de conocimientos", "app.containers.AdminPage.SettingsPage.header": "Banner de la página de inicio", "app.containers.AdminPage.SettingsPage.headerDescription": "Personalice la imagen y el texto del banner de la página de inicio.", "app.containers.AdminPage.SettingsPage.header_bg": "Imagen de banner", "app.containers.AdminPage.SettingsPage.helmetDescription": "Página de configuración del admin", "app.containers.AdminPage.SettingsPage.helmetTitle": "Página de configuración del admin", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Añada su propio contenido en la sección personalizable de la parte inferior de la página de inicio.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Encabezado de página de inicio | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Color de la imagen superpuesta", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Opacidad de la imagen superpuesta", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Detectar contenidos inapropiados", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Detectar automáticamente los contenidos inapropiados publicados en la plataforma.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "Mientras esta función esté activada, las aportaciones, propuestas y comentarios publicados por los participantes se revisarán automáticamente. Las entradas marcadas como de contenido potencialmente inapropiado no se bloquearán, pero se resaltarán para su revisión en la página {linkToActivityPage} .", "app.containers.AdminPage.SettingsPage.languages": "Idiomas", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Seleccione los idiomas en los que su plataforma está a disposición de usuarios. Fácilmente pueden elegir su idioma preferido de esta lista mediante un botón en la barra de navegación.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Actividad", "app.containers.AdminPage.SettingsPage.logo": "Logotipo", "app.containers.AdminPage.SettingsPage.noHeader": "Por favor, sube una imagen de encabezado", "app.containers.AdminPage.SettingsPage.no_button": "Sin botón", "app.containers.AdminPage.SettingsPage.organizationName": "Organización o Municipalidad nombre", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Indique el nombre de la organización o la ciudad para todas las lenguas.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Activar superposición", "app.containers.AdminPage.SettingsPage.phone": "Teléfono", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Configuración de la plataforma", "app.containers.AdminPage.SettingsPage.population": "Población", "app.containers.AdminPage.SettingsPage.populationMinError": "La población debe ser un número positivo.", "app.containers.AdminPage.SettingsPage.populationTooltip": "El número total de habitantes de tu territorio. Se utiliza para calcular la tasa de participación. Déjalo vacío si no procede.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Bloqueador de insultos", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Bloquear los aportes, propuestas y comentarios que contengan palabras ofensivas más comunes.", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Este texto aparece en la página de inicio, encima de los proyectos.", "app.containers.AdminPage.SettingsPage.projectsSettings": "Ajustes del proyecto", "app.containers.AdminPage.SettingsPage.projects_header": "Cabecera de los proyectos", "app.containers.AdminPage.SettingsPage.registrationFields": "Campos de registro", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Proporcione una breve descripción en la parte superior de su formulario de inscripción.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registro", "app.containers.AdminPage.SettingsPage.save": "Guardar", "app.containers.AdminPage.SettingsPage.saveArea": "Guardar área", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "<PERSON>go sali<PERSON> mal, por favor Inténtalo más tarde.", "app.containers.AdminPage.SettingsPage.saveSuccess": "¡guardado!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Los cambios han sido guardados.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Seleccionar aprobadores", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "Selecciona las áreas que se mostrarán a los usuarios para que las sigan tras el registro", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Selecciona los temas que se mostrarán a los usuarios para que los sigan después de registrarse", "app.containers.AdminPage.SettingsPage.settingsSavingError": "No se ha podido guardar. Intenta cambiar la configuración de nuevo.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Registrarse\"", "app.containers.AdminPage.SettingsPage.signed_in": "Botón para visitantes registrados", "app.containers.AdminPage.SettingsPage.signed_out": "Botón para visitantes no registrados", "app.containers.AdminPage.SettingsPage.signupFormText": "Texto de ayuda para el registro", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Añade una breve descripción en la parte superior del formulario de inscripción.", "app.containers.AdminPage.SettingsPage.statuses": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.step1": "Correo electrónico y contraseña paso", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Se muestra en la parte superior de la primera página del formulario de inscripción (nombre, correo electrónico, contraseña).", "app.containers.AdminPage.SettingsPage.step2": "Preguntas de registro paso", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Esto se muestra en la parte superior de la segunda página del formulario de inscripción (campos de inscripción adicionales).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Definir las áreas geográficas: los barrios, regiones,... Las áreas pueden ser vinculadas a proyectos y pueden utilizarse para crear grupos inteligentes y definir permisos de acceso del proyecto.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Agregue el nombre de su organización o ciudad, una dirección url de su sitio de Internet y los idiomas en que esta plataforma debe ponerse a disposición.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "El subtítulo excede el límite de máximo de 90 caracteres", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Especifique qué información se les pide a las personas que proporcionen de manera predeterminada antes de realizar cualquier acción en la plataforma. Puede cambiar esta opción predeterminada en la configuración de derechos de acceso de la fase del proyecto.\nPara que una pregunta se utilice en nuestra herramienta de informes, la pregunta debe ser de opción única. ", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminología", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "La configuración se ha actualizado correctamente.", "app.containers.AdminPage.SettingsPage.tabAreas1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabBranding": "Imagen de marca", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Estados de entrada", "app.containers.AdminPage.SettingsPage.tabPolicies": "Políticas", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Aprobación del proyecto", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Estado de las propuestas", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registro", "app.containers.AdminPage.SettingsPage.tabSettings": "General", "app.containers.AdminPage.SettingsPage.tabTopics2": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tableta", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "¿Cómo se deben llamar las áreas hacia los usuarios? por ejemplo, barrios, barrios, condados...", "app.containers.AdminPage.SettingsPage.titleAreas": "Localidades/Barrios/Zonas", "app.containers.AdminPage.SettingsPage.titleBasic": "Parametrización general", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "El título proporcionado excede el límite de 35 caracteres permitido como máximo", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Administrador del tema", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Este banner es especialmente útil con imágenes que no funcionan bien con el texto del título, subtítulo o botón. Estos elementos se desplazarán por debajo del banner. Encontrará más información sobre el uso recomendado de las imágenes en nuestro {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "base de conocimientos", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.urlError": "La URL no es válida.", "app.containers.AdminPage.SettingsPage.urlPatternError": "Introduzca una URL válida.", "app.containers.AdminPage.SettingsPage.urlTitle": "Sitio web", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Añadir la URL de la página web que desea vincular a esta plataforma. El vínculo será utilizado en el pie de página en la página de inicio.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "Elige cómo aparecerán en la plataforma los usuarios sin nombre en su perfil. Esto ocurrirá cuando configures los derechos de acceso de una fase como \"Confirmación por correo electrónico\". En todos los casos, al participar, los usuarios podrán actualizar el nombre de perfil que autogeneramos para ellos.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Visualización del nombre de usuario (sólo para usuarios con correo electrónico confirmado)", "app.containers.AdminPage.SideBar.administrator": "Administrador", "app.containers.AdminPage.SideBar.communityPlatform": "Plataforma comunitaria", "app.containers.AdminPage.SideBar.community_monitor": "Barómetro de satisfacción", "app.containers.AdminPage.SideBar.customerPortal": "Portal del cliente", "app.containers.AdminPage.SideBar.dashboard": "Tablero de controles", "app.containers.AdminPage.SideBar.emails": "Correo electrónico", "app.containers.AdminPage.SideBar.folderManager": "<PERSON><PERSON><PERSON> <PERSON>", "app.containers.AdminPage.SideBar.groups": "Grupos", "app.containers.AdminPage.SideBar.guide": "Guía", "app.containers.AdminPage.SideBar.inputManager": "Gestión de aportes", "app.containers.AdminPage.SideBar.insights": "Reportar", "app.containers.AdminPage.SideBar.inspirationHub": "Centro de inspiración", "app.containers.AdminPage.SideBar.knowledgeBase": "Base de conocimientos", "app.containers.AdminPage.SideBar.language": "Lengua", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Páginas y menú", "app.containers.AdminPage.SideBar.messaging": "Mensajería", "app.containers.AdminPage.SideBar.moderation": "Actividad", "app.containers.AdminPage.SideBar.notifications": "Notificaciones", "app.containers.AdminPage.SideBar.processing": "Tramitación", "app.containers.AdminPage.SideBar.projectManager": "<PERSON><PERSON> de proyecto", "app.containers.AdminPage.SideBar.projects": "Proyectos", "app.containers.AdminPage.SideBar.settings": "Configuración", "app.containers.AdminPage.SideBar.signOut": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.support": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.toPlatform": "A la plataforma", "app.containers.AdminPage.SideBar.tools": "Herramientas", "app.containers.AdminPage.SideBar.user.myProfile": "Mi perfil", "app.containers.AdminPage.SideBar.users": "Usuarios", "app.containers.AdminPage.SideBar.workshops": "Talleres", "app.containers.AdminPage.Topics.addTopics": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.browseTopics": "Buscar los temas", "app.containers.AdminPage.Topics.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.confirmHeader": "¿Estás seguro de que quieres eliminar este tema del proyecto?", "app.containers.AdminPage.Topics.delete": "Eliminar", "app.containers.AdminPage.Topics.deleteTopicLabel": "Eliminar", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Este tema ya no podrá ser utilizado en los proyectos.", "app.containers.AdminPage.Topics.inputForm": "Formulario de aportes", "app.containers.AdminPage.Topics.lastTopicWarning": "Se necesita al menos un tema. Si no deseas utilizar temas, puedes desactivarlos en la pestaña {ideaFormLink}.", "app.containers.AdminPage.Topics.projectTopicsDescription": "<PERSON><PERSON><PERSON> agregar y eliminar los temas que se pueden asignar a las entradas en este proyecto.", "app.containers.AdminPage.Topics.remove": "Eliminar", "app.containers.AdminPage.Topics.title": "Etiquetas de entrada permitidas", "app.containers.AdminPage.Topics.topicManager": "Administrador del tema", "app.containers.AdminPage.Topics.topicManagerInfo": "Si desea añadir temas adicionales al proyecto, puede hacerlo en el {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Agregar un grupo nuevo", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Nombre del grupo", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Proporcionar un nombre de grupo", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Crear un grupo manual", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "¿Qué tipo de grupo necesita?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/es/articles/7043801-uso-de-grupos-de-usuarios-inteligentes-y-manuales", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Guardar grupo", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Crear un grupo manual", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Crear un grupo de inteligente", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Más información sobre los grupos", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Especifique manualmente los usuarios que forman parte de este grupo.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Puedes especificar las condiciones que automáticamente y continuamente sumaran gente forma a este grupo.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Grupo manual", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Grupo Smart", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "No hay nadie en este grupo", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Ir a {allUsersLink} para agregar manualmente usuarios.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "No hay usuarios que coincidan con tu búsqueda", "app.containers.AdminPage.Users.GroupsPanel.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Exportar todo", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Exportar usuarios en grupo", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Exportar seleccionado", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "¿Estás seguro?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Error al agregar usuarios a los grupos, por favor, inténtelo de nuevo.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Eliminar de grupo", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "¿Borrar los usuarios seleccionados de este grupo?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Error al borrar este usuario, por favor, inténtelo de nuevo.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "<PERSON><PERSON><PERSON> usuario<PERSON> al grupo", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.add": "Agregar", "app.containers.AdminPage.groups.permissions.addAnswer": "<PERSON><PERSON><PERSON> respuest<PERSON>", "app.containers.AdminPage.groups.permissions.addQuestion": "Añadir preguntas demográficas", "app.containers.AdminPage.groups.permissions.answerChoices": "Opciones de respuesta", "app.containers.AdminPage.groups.permissions.answerFormat": "Formato de respuesta", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "Debe proporcionarse al menos una opción", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "Este usuario modera la carpeta que contiene este proyecto. Para eliminar sus derechos de moderador para este proyecto, puedes revocar sus derechos de carpeta o mover el proyecto a una carpeta diferente.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Crear una nueva pregunta", "app.containers.AdminPage.groups.permissions.createAQuestion": "<PERSON><PERSON><PERSON> una pregunta", "app.containers.AdminPage.groups.permissions.defaultField": "Campo por defecto", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Eliminar", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Eliminar", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Indica un título para todas las opciones", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "Sí-no (casilla de verificación)", "app.containers.AdminPage.groups.permissions.fieldType_date": "<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "<PERSON><PERSON><PERSON>a larga", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Sele<PERSON><PERSON> múl<PERSON>", "app.containers.AdminPage.groups.permissions.fieldType_number": "<PERSON>or numérico", "app.containers.AdminPage.groups.permissions.fieldType_select": "Opción múltiple (seleccione una)", "app.containers.AdminPage.groups.permissions.fieldType_text": "Respuesta corta", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Cambiar los permisos granulares no forma parte de tu licencia. Ponte en contacto con tu Gestor de GovSuccess para obtener más información al respecto.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "¿Está seguro que desea eliminar del proyecto a este grupo de usuarios?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Selecciona uno o varios grupos", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {Sin miembros} one {1 miembro} other {{count} miembros}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Rellene el título en todos los idiomas", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "¿Estás seguro?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "No encontramos administradores de proyecto", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "No se despliega información, debido a que no puedes realizar acciones en este proyecto.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Sólo los administradores pueden crear una pregunta nueva.", "app.containers.AdminPage.groups.permissions.option1": "Opción 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Invitación pendiente", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "¿Quién puede comentar en el documento?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "¿Quién puede inscribirse para asistir a un acto?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "¿Quién puede comentar en las aportaciones?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "¿Quién puede comentar las propuestas?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "¿Quién puede presentar una propuesta?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "¿Quién puede reaccionar a los aportes?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "¿Quién puede presentar aportaciones?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "¿Quién puede rellenar la encuesta/consulta ciudadana?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "¿Quién puede hacer la encuesta?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "¿Quién puede ser voluntario?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "¿Quién puede votar las propuestas?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "¿Quién puede votar?", "app.containers.AdminPage.groups.permissions.questionDescription": "Descripción de la pregunta", "app.containers.AdminPage.groups.permissions.questionTitle": "<PERSON><PERSON><PERSON><PERSON> de <PERSON> pregunta", "app.containers.AdminPage.groups.permissions.save": "Guardar", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Algo salió mal. Por favor Inténtalo más tarde.", "app.containers.AdminPage.groups.permissions.saveSuccess": "¡Perfecto!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Tus cambios han sido guardados.", "app.containers.AdminPage.groups.permissions.select": "Seleccione", "app.containers.AdminPage.groups.permissions.selectValueError": "Seleccione un tipo de respuesta", "app.containers.AdminPage.new.createAProject": "Crear un proyecto", "app.containers.AdminPage.new.fromScratch": "Desde el principio", "app.containers.AdminPage.phase.methodPicker.addOn1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "Inteligencia artificial", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Ayuda a los participantes a identificar puntos de acuerdo y desacuerdo.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Encontrar terreno común", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Incrusta un PDF interactivo y recolecta comentarios y opiniones con Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Recolectar opiniones y comentarios sobre un documento", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Insertar una encuesta de terceros", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "Encuesta externa", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Aprovecha la inteligencia colectiva de tus usuarios. Invítalos a enviar, debatir ideas y/o dar su opinión en un foro público.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Recoger aportaciones y opiniones en público", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Comparte información", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Carece de información en la plataforma basada en IA", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Carece de informes en la plataforma y de visualización y procesamiento de datos", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Enlace con el generador de informes de la plataforma", "app.containers.AdminPage.phase.methodPicker.logic1": "Lógica", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Amplia gama de tipos de preguntas", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Permite a los participantes subir ideas con un límite de tiempo y votos.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "Propuestas, peticiones o iniciativas", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "Encuesta rápida", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Prepara un breve cuestionario de opciones múltiples.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "Proporciona información a los usuarios, visualiza los resultados de otras fases, crea informes ricos en datos.", "app.containers.AdminPage.phase.methodPicker.survey1": "Encuesta", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Comprende las necesidades y la forma de pensar de tus usuarios mediante una amplia gama de tipos de preguntas privadas.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Opciones de la encuesta", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "<PERSON>rea una encuesta", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Pide a los usuarios que se ofrezcan voluntarios para actividades y causas o encuentra participantes para un panel.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Reclutar participantes o voluntarios", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Selecciona un método de votación, y haz que los usuarios prioricen entre algunas opciones diferentes.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Realizar un ejercicio de votación o priorización", "app.containers.AdminPage.projects.all.all": "Todos", "app.containers.AdminPage.projects.all.createProjectFolder": "Nueva carpeta", "app.containers.AdminPage.projects.all.existingProjects": "Proyectos existentes", "app.containers.AdminPage.projects.all.homepageWarning1": "Utiliza esta página para establecer el orden de los proyectos en el desplegable \"Todos los proyectos\" de la barra de navegación. Si utilizas los widgets \"Proyectos y carpetas publicados\" o \"Proyectos y carpetas (heredados)\" en tu página de inicio, el orden de los proyectos en estos widgets también vendrá determinado por el orden que establezcas aquí.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "Aquí aparecerán los proyectos en los que eres Jefe de Proyecto.", "app.containers.AdminPage.projects.all.noProjects": "No se han encontrado proyectos.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Sólo los administradores pueden crear carpetas de proyecto.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Proyectos y carpetas", "app.containers.AdminPage.projects.all.publishedTab": "Publicado", "app.containers.AdminPage.projects.all.searchProjects": "Buscar proyectos", "app.containers.AdminPage.projects.all.yourProjects": "Tus proyectos", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "Análisis de IA", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Precisión: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "Pregunta a", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "En lugar de resumir, puedes hacer preguntas relevantes a tus datos. Esta función no está incluida en tu plan actual. Habla con tu Gestor de Éxito Gubernamental o con el administrador para desbloquearla.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Haz una pregunta", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "Esta visión incluye las siguientes preguntas:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "<PERSON><PERSON><PERSON> pregunta", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "¿Estás seguro de que quieres borrar esta pregunta?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Bo<PERSON>r resumen", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "¿Estás seguro de que quieres borrar estos resúmenes?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Tus resúmenes de texto se mostrarán aquí, pero actualmente aún no tienes ninguno.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "Haz clic en el botón Auto-resumir de arriba para empezar.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "entradas seleccionadas", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Preguntar sobre menos entradas conduce a una mayor precisión. Reduce la selección actual de entradas utilizando etiquetas, filtros de búsqueda o demográficos.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "Preguntas para", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Pregunta para todas las aportaciones", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Valora la calidad de esta visión", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "Restaurar filtros", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Resume", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Resumen para", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Resumen para todas las entradas", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "¡G<PERSON>ias por tus comentarios!", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "La IA no puede procesar tantas entradas de una sola vez. Divídelas en grupos más pequeños.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "<PERSON>uedes resumir un máximo de 30 entradas a la vez en tu plan actual. Habla con tu gestor o administrador de GovSuccess para obtener más información.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "Co<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Nuestra plataforma te permite explorar los temas centrales, resumir los datos y examinar diversas perspectivas. Si buscas respuestas o perspectivas concretas, considera la posibilidad de utilizar la función \"Haz una pregunta\" para profundizar más allá del resumen.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "Aunque es poco frecuente, la IA puede generar ocasionalmente información que no estaba presente explícitamente en el conjunto de datos original.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Alucinaciones:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "La IA podría hacer más hincapié en determinados temas o ideas que en otros, lo que podría sesgar la interpretación global.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Exageración:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Nuestro sistema está optimizado para manejar 20-200 entradas bien definidas para obtener los resultados más precisos. A medida que el volumen de datos aumenta más allá de este rango, el resumen puede volverse más de alto nivel y generalizado. Esto no significa que la IA sea \"menos precisa\", sino que se centrará en tendencias y patrones más amplios. Para obtener una visión más matizada, recomendamos utilizar la función de (auto)etiquetado para segmentar conjuntos de datos más grandes en subconjuntos más pequeños y manejables.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Volumen de datos y precisión:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "Recomendamos utilizar los resúmenes generados por la IA como punto de partida para comprender grandes conjuntos de datos, pero no como la última palabra.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "Cómo trabajar con IA", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Añadir entradas seleccionadas a la etiqueta", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "Añadir etiqueta", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "Esta función no está incluida en tu plan actual. Habla con tu Gestor de Éxito Gubernamental o con el administrador para desbloquearla.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "Todas las entradas", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "Todas las entradas", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "Todos los temas", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "No, lo haré yo", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "¿Quieres asignar automáticamente entradas a tu etiqueta?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "Hay <b>mé<PERSON><PERSON> diferentes</b> para asignar automáticamente entradas a las etiquetas.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "U<PERSON><PERSON> <b>el botón de etiquetado automático</b> para iniciar el método que prefieras.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Haz clic en una etiqueta para asignarla a la entrada actualmente seleccionada.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "Sí, etiquetado automático", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Etiqueta automática", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Las etiquetas automáticas son derivadas automáticamente por el ordenador. Puedes cambiarlas o eliminarlas en todo momento.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Etiqueta automática", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Las entradas ya asociadas a estas etiquetas no se clasificarán de nuevo.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "La clasificación se basa únicamente en el nombre de la etiqueta. Elige palabras clave relevantes para obtener los mejores resultados.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Etiquetas: Por etiqueta", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "Tú creas las etiquetas y asignas manualmente algunas entradas a modo de ejemplo, el ordenador asigna el resto", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Etiquetas: <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "Similar a \"Etiquetas: por etiqueta\", pero con mayor precisión, ya que estás entrenando al sistema con buenos ejemplos.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "Tú creas las etiquetas, el ordenador asigna las entradas", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Etiquetas: Por etiqueta", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "Esto funciona bien cuando tienes un conjunto predefinido de etiquetas o cuando tu proyecto tiene un alcance limitado en cuanto a etiquetas.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Detecta las entradas con una proporción significativa de aversiones/disgustos", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Polémica", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Eliminar etiqueta", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "¿Estás seguro de que quieres eliminar esta etiqueta?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "No vuelvas a mostrar esto", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Editar un tema", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Selecciona un máximo de 9 etiquetas entre las que quieras que se distribuyan las entradas.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "La clasificación se basa en las entradas asignadas actualmente a las etiquetas. El ordenador intentará seguir tu ejemplo.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Etiquetas: <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "Aún no tienes ninguna etiqueta personalizada.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "El ordenador detecta automáticamente las etiquetas y las asigna a tus entradas.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Etiquetas: Totalmente automatizado", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "Funciona bien cuando tus proyectos abarcan una amplia gama de etiquetas. Un buen punto de partida.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "¿Cómo quieres etiquetar?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Entradas sin etiquetas", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Detectar la lengua de cada entrada", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "Lengua", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "Lanza", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "Sin filtros activos", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Utiliza etiquetas para subdividir y filtrar las entradas, con el fin de hacer resúmenes más precisos o específicos.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Etiquetas: Etiquetas de la plataforma", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Asigna las etiquetas existentes en la plataforma que el autor eligió al publicar", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Recomendado", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "Renombrar etiqueta", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Nombre de la tendencia", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "Guarda", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "Renombrar etiqueta", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Asigna un sentimiento positivo o negativo a cada entrada, derivado del texto", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Sen<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Detección de etiquetas", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Util<PERSON>r filtros actuales", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "¿Qué entradas quieres etiquetar?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Tarea de autoetiquetado", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Polémica", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "Personalizado", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Finalizado en", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "Fallido", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "<PERSON><PERSON> e<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "En curso", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "Por etiqueta", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "Lengua", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "Etiqueta PNL", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "No se han realizado recientemente tareas de IA", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Etiqueta de plataforma", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "En cola", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Sen<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Comenz<PERSON> en", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Con éxito", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Activado en", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "Por encima de", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "Todos", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "Autor", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "Abajo", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "Año de nacimiento", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Compromiso", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "Filtros de entrada", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "De", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "Aportes", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "Número de comentarios", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Número de reacciones", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "Cantidad de votos", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "Para", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.anonymous": "Aportación anónima", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Autores por edad", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Autores por domicilio", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Trabajos de fondo", "app.containers.AdminPage.projects.project.analysis.comments": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "La tabla de domicilios es demasiado grande para mostrarla", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "Ocultar respuestas vacías", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Respuestas", "app.containers.AdminPage.projects.project.analysis.end": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.filter": "Mostrar sólo entradas con este valor", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Ocultar respuestas sin respuesta", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Auto-insights", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Valores de columna", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "Hay {count} casos de esta combinación.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "No me gusta", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Explora", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "Aportaciones", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "Me gusta", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Siguiente mapa de calor", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Siguiente insight", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "No es un dato estadísticamente significativo.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Los Auto insights no están disponibles para proyectos con menos de 30 participantes.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "Participantes", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Mapa de calor anterior", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Insight previo", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "Valores de fila", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Insight estadísticamente significativo.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Resumir", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Etiquetas de análisis", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "Verdadero", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "Unidades", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "Ver todos los insights", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Ver auto-insights", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Entradas sin etiquetas", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "Se ha cargado un archivo shape no válido y no se puede mostrar.", "app.containers.AdminPage.projects.project.analysis.limit": "Límite", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "Pregunta principal", "app.containers.AdminPage.projects.project.analysis.manageInput": "Gestionar los aportes ciudadanos", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Gráfico si<PERSON>", "app.containers.AdminPage.projects.project.analysis.noAnswer": "Sin respuesta", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "No hay respuesta.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "No se ha cargado ningún shapefile.", "app.containers.AdminPage.projects.project.analysis.noInputs": "Ninguna entrada corresponde a tus filtros actuales", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Gráfico anterior", "app.containers.AdminPage.projects.project.analysis.reactions": "Reacciones", "app.containers.AdminPage.projects.project.analysis.remove": "Eliminar", "app.containers.AdminPage.projects.project.analysis.removeFilter": "Eliminar filtro", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "Eliminar filtro", "app.containers.AdminPage.projects.project.analysis.search": "Búsqueda", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Los archivos Shapefile se muestran aquí en formato GeoJSON. Por ello, es posible que los estilos del archivo original no se muestren correctamente.", "app.containers.AdminPage.projects.project.analysis.start": "<PERSON><PERSON>o", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Art<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "Desconocido", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "Ver todas las preguntas", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "Ver las preguntas seleccionadas", "app.containers.AdminPage.projects.project.analysis.votes": "Votos", "app.containers.AdminPage.widgets.copied": "Copiado en el portapapeles", "app.containers.AdminPage.widgets.copyToClipboard": "Copiar este código", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Copiar el código HTML", "app.containers.AdminPage.widgets.fieldAccentColor": "Color de acento", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Color de fondo del widget", "app.containers.AdminPage.widgets.fieldButtonText": "Texto del botón", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Inscr<PERSON><PERSON><PERSON> ahora", "app.containers.AdminPage.widgets.fieldFont": "Fuente", "app.containers.AdminPage.widgets.fieldFontDescription": "Debe ser una fuente válida de {googleFontsLink}. Deja en blanco para utilizar la fuente predeterminada.", "app.containers.AdminPage.widgets.fieldFontSize": "Tama<PERSON> de la fuente (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Subtítulo de cabecera", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "<PERSON><PERSON><PERSON> tener algo que decir", "app.containers.AdminPage.widgets.fieldHeaderText": "Texto de encabezado", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Nuestra plataforma de participación", "app.containers.AdminPage.widgets.fieldHeight": "Altura (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Número de entradas", "app.containers.AdminPage.widgets.fieldProjects": "Proyectos", "app.containers.AdminPage.widgets.fieldRelativeLink": "Enlaces a", "app.containers.AdminPage.widgets.fieldShowFooter": "Mostrar botón", "app.containers.AdminPage.widgets.fieldShowHeader": "Mostrar encabezado", "app.containers.AdminPage.widgets.fieldShowLogo": "Mostrar logo", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Color de fondo del sitio", "app.containers.AdminPage.widgets.fieldSort": "Ordenados por", "app.containers.AdminPage.widgets.fieldTextColor": "Color del texto", "app.containers.AdminPage.widgets.fieldTopics": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.homepage": "Página de inicio", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Copiar el siguiente fragmento de código HTML y pegarlo en el sitio web donde desea que el widget que aparezca.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Código del widget de HTML", "app.containers.AdminPage.widgets.previewTitle": "Vista previa", "app.containers.AdminPage.widgets.settingsTitle": "Configuración", "app.containers.AdminPage.widgets.sortNewest": "Más reciente", "app.containers.AdminPage.widgets.sortPopular": "Más votados", "app.containers.AdminPage.widgets.sortTrending": "Tendencias", "app.containers.AdminPage.widgets.subtitleWidgets": "<PERSON><PERSON><PERSON> crear un widget, personalizarlo y añadirlo a tu propio sitio web para atraer gente a esta plataforma.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Dimensiones", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Encabezado y pie de página", "app.containers.AdminPage.widgets.titleInputSelection": "Selección de entrada", "app.containers.AdminPage.widgets.titleStyle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Guardar", "app.containers.ContentBuilder.homepage.PageTitle": "Página de inicio", "app.containers.ContentBuilder.homepage.SaveError": "Algo ha ido mal al guardar la página de inicio.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "<PERSON><PERSON> columnas", "app.containers.ContentBuilder.homepage.bannerImage": "Imagen del encabezado", "app.containers.ContentBuilder.homepage.bannerSubtext": "Subtexto del banner", "app.containers.ContentBuilder.homepage.bannerText": "Texto de banner", "app.containers.ContentBuilder.homepage.button": "Botón", "app.containers.ContentBuilder.homepage.chooseLayout": "Diseño", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "En tu licencia actual no se incluye la personalización de otros ajustes que no sean la imagen y el texto del banner de la página de inicio. Ponte en contacto con tu Gestor de GovSuccess para obtener más información al respecto.", "app.containers.ContentBuilder.homepage.customized_button": "Personalizado", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Texto del botón", "app.containers.ContentBuilder.homepage.customized_button_url_label": "Enlace al botón", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Muestra los 3 próximos eventos en tu plataforma.", "app.containers.ContentBuilder.homepage.eventsDescription": "Muestra los 3 próximos eventos en tu plataforma.", "app.containers.ContentBuilder.homepage.fixedRatio": "Proporción fija", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Este tipo de banner funciona mejor con imágenes que no deben recortarse, como imágenes con texto, un logotipo o elementos específicos que son importantes para sus ciudadanos. Este banner se sustituye por un cuadro sólido en el color primario cuando los usuarios inician sesión. Puede configurar este color en los ajustes generales. Puede encontrar más información sobre el uso recomendado de imágenes en nuestro {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "base de conocimientos", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "<PERSON><PERSON> completo", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Este banner se extiende a todo lo ancho para conseguir un gran efecto visual. La imagen intentará cubrir todo el espacio posible, haciendo que no siempre esté visible en todo momento. Puede combinar este banner con una sobreimpresión de cualquier color. Puede encontrar más información sobre el uso recomendado de las imágenes en nuestro {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "base de conocimientos", "app.containers.ContentBuilder.homepage.imageOverlayColor": "Color de la imagen superpuesta", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Opacidad de la imagen superpuesta", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "URL no válida", "app.containers.ContentBuilder.homepage.no_button": "Sin botón", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Usuarios no registrados", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Activar superposición", "app.containers.ContentBuilder.homepage.projectsDescription": "Para configurar el orden en que se muestran tus proyectos, reordénalos en {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "Página de proyectos", "app.containers.ContentBuilder.homepage.registeredUsersView": "Usuarios", "app.containers.ContentBuilder.homepage.showAvatars": "Mostrar avatares", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Mostrar las fotos de perfil de los participantes y su número a los visitantes aún no registrados", "app.containers.ContentBuilder.homepage.sign_up_button": "Inscríbase", "app.containers.ContentBuilder.homepage.signedInDescription": "Así es como ven el banner los usuarios registrados.", "app.containers.ContentBuilder.homepage.signedOutDescription": "Así es como ven el banner los visitantes que no están registrados en la plataforma.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "Este banner es especialmente útil con imágenes que no funcionan bien con el texto del título, subtítulo o botón. Estos elementos se desplazarán por debajo del banner. Encontrará más información sobre el uso recomendado de las imágenes en nuestro {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "base de conocimientos", "app.containers.ContentBuilder.homepage.twoRowLayout": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "Altura de la incrustación (píxeles)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "Altura a la que quiere que aparezca su contenido incrustado en la página (en píxeles).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Breve descripción del contenido que está incrustando", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "Es útil proporcionar esta información para los usuarios que dependen de un lector de pantalla u otra tecnología de asistencia.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Añada su URL", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "URL completa del sitio web que desea incrustar.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Muestra el contenido de un sitio web externo en tu página en un iFrame HTML. Ten en cuenta que no todas las páginas pueden incrustarse. Si tienes problemas para incrustar una página, comprueba con el propietario de la página si está configurada para permitir la incrustación.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Visite nuestra página de soporte", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "Lo sentimos. Este contenido no pudo ser incrustado. {visitLinkMessage} para saber más.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/es/articles/7025826-personalizar-las-descripciones-de-los-proyectos-con-el-generador-de-contenidos#h_cf8a993642", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Introduce una dirección web válida, por ejemplo https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Incrustar Url", "app.containers.admin.ContentBuilder.accordionMultiloc": "Acordeón", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Abierto por defecto", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Texto", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Este es el contenido del acordeón expandible. Puede editarlo y darle formato utilizando el editor del panel de la derecha.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Nombre", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Título del acordeón", "app.containers.admin.ContentBuilder.buttonMultiloc": "Botón", "app.containers.admin.ContentBuilder.delete": "Eliminar", "app.containers.admin.ContentBuilder.error": "error", "app.containers.admin.ContentBuilder.errorMessage": "Hay un error en el contenido de {locale}, arregle el problema para poder guardar sus cambios", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Ocultar avatares de participación", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "Se trata de una encuesta trimestral continua que analiza tu opinión sobre la gobernanza y los servicios públicos.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Responde la consulta", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Ayúdanos a servirte mejor", "app.containers.admin.ContentBuilder.homepage.default": "por defecto", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "Eventos", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "Eventos", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Llamada a la acción", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Descripción", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "URL del botón principal", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Texto del botón principal", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "URL del botón secundario", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Texto del botón secundario", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Nombre", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Banner de la página de inicio", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Banner de la página de inicio", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Imagen y tarjetas de texto", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 columna", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Proyectos", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Habilita las propuestas en la sección \"Propuestas\" del panel de administración para desbloquearlas en la página de inicio", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Propuestas", "app.containers.admin.ContentBuilder.imageMultiloc": "Imagen", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Breve descripción de la imagen", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "A<PERSON>dir \"texto alternativo\" a las imágenes es importante para que los usuarios que utilizan lectores de pantalla puedan acceder a su plataforma.", "app.containers.admin.ContentBuilder.participationBox": "Caja de participación", "app.containers.admin.ContentBuilder.textMultiloc": "Texto", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 columnas", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 columna", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 columnas con 30 % y 60 % de ancho respectivamente", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 columnas con 60% y 30% de ancho respectivamente", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 columnas iguales", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "Entradas más reaccionadas", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Fase de ideación", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "No hay aportaciones disponibles para este proyecto o fase.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "Número de entradas", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Mostrar más", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Entradas totales: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Contraer texto largo", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "No hay aportaciones disponibles para este proyecto o fase.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Seleccionar fase", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Autor", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "Contenido", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "Reacciones", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "Votos", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "Aportes", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Nombre", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Tasa de registro", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Registros", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Calendario de participantes", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Ten en cuenta que las cifras de participación pueden no ser totalmente exactas, ya que algunos datos se recogen en una encuesta externa de la que no hacemos un seguimiento.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Gráfico", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "<PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.noData": "No hay datos disponibles para los filtros que ha seleccionado.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Fuentes de tráfico", "app.containers.admin.ReportBuilder.charts.users": "Usuarios", "app.containers.admin.ReportBuilder.charts.usersByAge": "Usuarios por edad", "app.containers.admin.ReportBuilder.charts.usersByGender": "Usuarios por género", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Cronología de visitantes", "app.containers.admin.ReportBuilder.managerLabel1": "<PERSON><PERSON> de proyecto", "app.containers.admin.ReportBuilder.periodLabel1": "Periodo", "app.containers.admin.ReportBuilder.projectLabel1": "proyectos", "app.containers.admin.ReportBuilder.quarterReport1": "Informe del barómetro de satisfacción: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "<PERSON><PERSON>o", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Comprar 1 asiento adicional", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Confirme", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "¿Seguro que quieres dar derechos de administrador a 1 persona?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Dar derechos de administrador", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Ha alcanzado el límite de asientos incluidos en su plan, se añadirán {noOfSeats} asientos adicionales {noOfSeats, plural, one {} other {asientos}}", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Agregar estado", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Los estados por defecto no se pueden borrar.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Eliminar", "app.containers.admin.ideaStatuses.all.editButtonLabel": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Editar estado", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "No se pueden borrar los estados actualmente asignados a una entrada. Puede eliminar/cambiar el estado de las entradas existentes en la pestaña {manageTab} .", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Este estado no puede ser eliminado o movido.", "app.containers.admin.ideaStatuses.all.manage": "Administrar", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Configurar estados de entrada personalizados no está incluido en tu plan actual. Habla con tu Gestor de Éxito Gubernamental o con el administrador para desbloquearlo.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Gestiona el estado que se puede asignar a las aportaciones de los participantes en un proyecto. El estado es visible públicamente y ayuda a mantener informados a los participantes.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Gestiona el estado que se puede asignar a las propuestas dentro de un proyecto. El estado es visible públicamente y ayuda a mantener informados a los participantes.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Editar estados de entrada", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Editar los estados de las propuestas", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Seleccionado para implementación o para siguientes pasos", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Aprobado", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Comentarios oficiales", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Contestado", "app.containers.admin.ideaStatuses.form.category": "Etiqueta", "app.containers.admin.ideaStatuses.form.categoryDescription": "Por favor, seleccione la etiqueta que mejor represente su estado. Esta selección ayudará a nuestra herramienta de análisis a procesar y analizar los mensajes con mayor precisión.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "No coincide con ninguna de las otras opciones", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldColor": "Color", "app.containers.admin.ideaStatuses.form.fieldDescription": "Descripción del Estado", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Proporcionar una descripción de estado para todos los idiomas", "app.containers.admin.ideaStatuses.form.fieldTitle": "Nombre del estado", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Proporcionar un nombre de estado para todos los idiomas", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Se ha aplicado con éxito", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Implementado", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "La propuesta no es elegible", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "No apto", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Inelegible o no seleccionado para avanzar", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "No seleccionado", "app.containers.admin.ideaStatuses.form.saveStatus": "Guardar estado", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Seleccionado para implementación o para siguientes pasos", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Bajo consideración", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Visto pero aún no procesado", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Visto", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Gestiona los aportes  y sus estados.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Administrar aportes ciudadanos de la plataforma de participación de {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Dar retroalimentación, agregar temas y trasladar los aportes de un proyecto a otro", "app.containers.admin.ideas.all.inputManagerPageTitle": "Gestión de aportes", "app.containers.admin.ideas.all.tabOverview": "Descripción general", "app.containers.admin.import.importInputs": "Importar entradas", "app.containers.admin.import.importNoLongerAvailable3": "Esta función ya no está disponible aquí. Para importar aportaciones a una fase de ideación, ve a la fase y selecciona \"Importar\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 plaza de administrador adicional} other {# plaza de administrador adicionales}} y {managerSeats, plural, one {¡1 plaza adicional de gerente} other {¡# plazas adicionales de gerente}} serán añadi<PERSON> por encima del límite.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {¡Se añadirá 1 plaza adicional de administrador por encima del límite} other {Se añadirán # plazas adicionales de administrador por encima del límite}}", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {¡Se agregará 1 plaza de gerente adicional sobre el límite} other {¡Se agregarán # plazas de gerente adicionales por encima del límite}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Confirmar y enviar las invitaciones", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Confirmar el impacto en el uso de las plazas", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Ha alcanzado el límite de plazas disponibles en su plan.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Los administradores y los gestores de este proyecto", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Sólo administradores y colaboradores", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Sólo los administradores de la plataforma, los gestores de carpetas y los gestores de proyectos pueden llevar a cabo la acción", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Cualquiera", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "Puede participar cualquier persona, incluidos los usuarios no registrados.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Algunos grupos de usuarios", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Los usuarios de determinados grupos de usuarios pueden participar. Puede gestionar los grupos de usuarios en la pestaña \"Usuarios\".", "app.containers.admin.project.permissions.viewingRightsTitle": "¿Quién puede ver este proyecto?", "app.containers.phaseConfig.enableSimilarInputDetection": "Activar la detección de aportes similares", "app.containers.phaseConfig.similarInputDetectionTitle": "Detección de aportes similares", "app.containers.phaseConfig.similarInputDetectionTooltip": "Mostrar a los participantes aportes similares mientras teclean para ayudar a evitar duplicidades.", "app.containers.phaseConfig.similarityThresholdBody": "Umbral de similitud (descripción del aporte)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "Controla lo similares que deben ser dos descripciones para ser marcadas como similares. Utiliza un valor entre 0 (estricto) y 1 (indulgente). Los valores más bajos devuelven menos coincidencias, pero más precisas.", "app.containers.phaseConfig.similarityThresholdTitle": "Umbral de similitud (título)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "Controla lo similares que deben ser dos títulos para ser marcados como similares. Utiliza un valor entre 0 (estricto) y 1 (indulgente). Los valores más bajos devuelven menos coincidencias, pero más precisas.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "Esta función está disponible como parte de una oferta de acceso anticipado hasta el 30 de junio de 2025. Si quieres seguir utilizándola después de esa fecha, ponte en contacto con tu Gestor de Éxito Gubernamental o con el administrador para hablar de las opciones de activación.", "app.containers.survey.sentiment.noAnswers2": "No hay respuestas por el momento.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {sin comentarios} one {1 comentario} other {# comentarios}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Los participantes son usuarios o visitantes que han participado en un proyecto, publicado o interactuado con una propuesta o asistido a eventos.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "Participantes", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Tasa de participación", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Porcentaje de visitantes que se convierten en participantes.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Total de participantes", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Campañas automatizadas", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Correos electrónicos automatizados", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "De {quantity} campañas", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Campañas", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Campañas personalizadas", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Correos electrónicos personalizados", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "Correo electrónico", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Total de correos electrónicos enviados", "app.modules.commercial.analytics.admin.components.Events.completed": "Completado", "app.modules.commercial.analytics.admin.components.Events.events": "Eventos", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Total de eventos añadidos", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Próximos", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Invitaciones", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Pendiente", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Total de invitaciones enviadas", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "<PERSON><PERSON> al Gestor de entradas", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Entradas", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Activa", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Proyectos no archivados y visibles en la tabla \"Activo\" de la página de inicio", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Archivado", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Anteproyectos", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Finalizado", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Aquí se cuentan todos los proyectos archivados y los proyectos activos del calendario que han finalizado", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Proyectos", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Total de proyectos", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "El número de proyectos visibles en la plataforma", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Nuevos registros", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Tasa de registro", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Porcentaje de visitantes que se convierten en usuarios registrados.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registros", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Registros totales", "app.modules.commercial.analytics.admin.components.Tab": "Visitantes", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Últimos 30 días", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Últimos 7 días", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Pageviews por visita", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Duración de la visita", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Visitantes", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Visitantes\" es el número de visitantes únicos. Si una persona visita la plataforma varias veces, solo se cuenta una vez.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Visitas", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Visitas\" es el número de sesiones. Si una persona visita la plataforma varias veces, se cuenta cada visita.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Ayer:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Recuento", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Idioma", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Número de visitantes", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Porcentaje de visitantes", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "haga clic aquí", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "Para ver la lista completa de referentes, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Visitantes", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Fuentes de tráfico", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Visitas", "app.modules.commercial.analytics.admin.components.totalParticipants": "Total de participantes", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Aún no hay datos de visitantes.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Hemos cambiado la forma en que recopilamos y mostramos los datos de los visitantes. Como resultado, los datos de los visitantes son más precisos y hay más tipos de datos disponibles, sin dejar de cumplir la GDPR. Aunque los datos utilizados para la cronología de las visitas se remontan a hace más tiempo, no empezamos a recopilar los datos para la \"Duración de la visita\", \"Páginas vistas por visita\" y los demás gráficos hasta noviembre de 2024, por lo que antes de esa fecha no hay datos disponibles. <PERSON>r tanto, si seleccionas datos anteriores a noviembre de 2024, ten en cuenta que algunos gráficos pueden estar vacíos o tener un aspecto extraño.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Envíos de correo electrónico a lo largo del tiempo", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Participantes a lo largo del tiempo", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Inscripciones a lo largo del tiempo", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Estadística", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Estadística general", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Visitas y visitantes en el tiempo", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Total en el periodo", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Recuento", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Idioma", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Campañas", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Entrada directa", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "Número de visitantes", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Porcentaje de visitas", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "Fuente de tráfico", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Fuentes de tráfico", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Motores de búsqueda", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Redes sociales", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "Redirecciones SSO", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Fuente de tráfico", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Número de visitas", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Sitios web", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Puede eliminar esta advertencia de contenido seleccionando este elemento y haciendo clic en el botón de eliminar en la parte superior. Entonces volverá a aparecer en las pestañas Visto o No visto", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Contenido inapropiado detectado automaticamente.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "No hay mensajes para tu revisión reportados por la comunidad o marcados como contenido inapropiado por nuestro sistema de Procesamiento de Lenguaje Natural", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Elimina {numberOfItems, plural, one {advertencia de contenido} other {# Advertencias de contenido}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Reportado como inapropiado por un usuario/a de la plataforma.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Advertencias sobre el contenido", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "<PERSON><PERSON><PERSON><PERSON> de informes", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Páginas que se muestran en la barra de navegación", "app.modules.navbar.admin.containers.addProject": "Añadir proyecto a la barra de navegación", "app.modules.navbar.admin.containers.createCustomPageButton": "<PERSON><PERSON><PERSON> página personalizada", "app.modules.navbar.admin.containers.deletePageConfirmation": "¿Está seguro de que quiere eliminar esta página? Este paso no podrá deshacerse. También puede eliminar la página de la barra de navegación si aún no está listo para eliminarla.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "<PERSON><PERSON><PERSON> puedes a<PERSON><PERSON> hasta 5 elementos a la barra de navegación", "app.modules.navbar.admin.containers.pageHeader": "Páginas y menú", "app.modules.navbar.admin.containers.pageSubtitle": "Su barra de navegación puede mostrar hasta cinco páginas además de las páginas de Inicio y de proyectos. Puede cambiar el nombre de los elementos del menú, reordenarlos y añadir páginas nuevas con su propio contenido.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widget", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Utiliza el icono ☰ de abajo para arrastrar el contenido de la IA a tu informe.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "No hay perspectivas de IA disponibles. Puedes crearlas en tu proyecto.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Ir al proyecto", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "Pregunta", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Seleccionar fase", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Desbloquea el análisis de IA", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Añade a tu informe información generada por IA", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Informa más rápido con IA", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Los informes con IA no están incluidos en tu plan actual. Habla con tu Gestor de Éxito Gubernamental para desbloquear esta función.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "Esto no está incluido en tu plan actual. Ponte en contacto con tu Gestor de Éxito Gubernamental o administrador para desbloquearlo.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Agrupar por campo de inscripción", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Agrupar por pregunta de la encuesta", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Modo Grupo", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Agrupa las respuestas de la encuesta por campos de registro (sexo, ubicación, edad, etc.) u otras preguntas de la encuesta.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "Pregunta", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Campo de inscripción", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Fase de encuesta", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "Pregunta de la encuesta", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "¿Estás seguro de que quieres borrar esto?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "Bo<PERSON>r", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "<PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Publica tu comentario", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "Guardar", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "Escribe tu comentario aquí", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Haz clic en los botones de abajo para seguir o dejar de seguir. El número de proyectos se muestra entre paréntesis.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "En tu zona", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "<PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Preferencias de seguimiento", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "Actualmente no hay proyectos activos dadas tus preferencias de seguimiento.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "Este widget muestra los proyectos asociados a las \"áreas\" que sigue el usuario. Ten en cuenta que tu plataforma puede utilizar un nombre diferente para las \"áreas\"; consulta la pestaña \"Áreas\" en la configuración de la plataforma. Si el usuario aún no sigue ninguna área, el widget mostrará las áreas disponibles para seguir. En este caso, el widget mostrará un máximo de 100 áreas.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "No hay proyectos publicados ni carpetas disponibles", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Proyectos y carpetas publicados", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "Este widget mostrará los proyectos y carpetas que estén publicados en ese momento, respetando el orden definido en la página de proyectos. Este comportamiento es el mismo que el de la pestaña \"activo\" del widget de proyectos \"heredado\".", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "No hay proyectos ni carpetas seleccionados", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Selecciona proyectos o carpetas", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Proyectos y carpetas seleccionados", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "Con este widget, puedes seleccionar y determinar el orden en el que quieres que se muestren los proyectos o carpetas a los usuarios.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} proyectos", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "visite nuestro centro de soporte", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Para más información sobre las resoluciones de imagen recomendadas, {supportPageLink}."}
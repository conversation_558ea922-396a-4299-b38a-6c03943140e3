{"UI.FormComponents.required": "Campo requerido", "app.Admin.ManagementFeed.action": "Ação", "app.Admin.ManagementFeed.after": "<PERSON><PERSON><PERSON> de", "app.Admin.ManagementFeed.before": "<PERSON><PERSON> de", "app.Admin.ManagementFeed.changed": "Modificado", "app.Admin.ManagementFeed.created": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.date": "Data", "app.Admin.ManagementFeed.deleted": "Excluído", "app.Admin.ManagementFeed.folder": "Pasta", "app.Admin.ManagementFeed.idea": "Ideia", "app.Admin.ManagementFeed.in": "no projeto {project}", "app.Admin.ManagementFeed.item": "<PERSON><PERSON>", "app.Admin.ManagementFeed.key": "Chave", "app.Admin.ManagementFeed.managementFeedNudge": "O acesso ao feed de gerenciamento não está incluído na sua licença atual. Entre em contato com o seu gerente do GovSuccess para saber mais sobre isso.", "app.Admin.ManagementFeed.noActivityFound": "Nenhuma atividade encontrada", "app.Admin.ManagementFeed.phase": "Fase", "app.Admin.ManagementFeed.project": "Projeto", "app.Admin.ManagementFeed.projectReviewApproved": "Projeto a<PERSON>ado", "app.Admin.ManagementFeed.projectReviewRequested": "Revisão do projeto solicitada", "app.Admin.ManagementFeed.title": "Feed de gerenciamento", "app.Admin.ManagementFeed.user": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.userPlaceholder": "Selecione um usuário", "app.Admin.ManagementFeed.value": "Valor", "app.Admin.ManagementFeed.viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.warning": "Recurso experimental: Uma lista mínima de ações selecionadas realizadas por administradores ou gerentes nos últimos 30 dias. Nem todas as ações estão incluídas.", "app.Admin.Moderation.managementFeed": "Feed de gerenciamento", "app.Admin.Moderation.participationFeed": "Feed de participação", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "Você tem certeza?", "app.components.Admin.Campaigns.clicked": "Clicado", "app.components.Admin.Campaigns.deleteCampaignButton": "Excluir campanha", "app.components.Admin.Campaigns.deliveryStatus_accepted": "<PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_bounced": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_clicked": "Clicado", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "<PERSON><PERSON> mostra quantos destinatários clicaram em um link no e-mail. Observe que alguns sistemas de segurança podem seguir links automaticamente para verificá-los, o que pode resultar em cliques falsos.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_failed": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_opened": "Abe<PERSON>o", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "Is<PERSON> mostra quantos destinatários abriram o e-mail. Observe que alguns sistemas de segurança (como o Microsoft Defender) podem pré-carregar o conteúdo para verificação, o que pode resultar em aberturas falsas.", "app.components.Admin.Campaigns.deliveryStatus_sent": "Enviado", "app.components.Admin.Campaigns.draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.from": "De", "app.components.Admin.Campaigns.manageButtonLabel": "Gerenciar", "app.components.Admin.Campaigns.opened": "Abe<PERSON>o", "app.components.Admin.Campaigns.project": "Projeto", "app.components.Admin.Campaigns.recipientsTitle": "Beneficiários", "app.components.Admin.Campaigns.reply_to": "Responder a", "app.components.Admin.Campaigns.sent": "Enviado", "app.components.Admin.Campaigns.statsButton": "Estatísticas", "app.components.Admin.Campaigns.subject": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.to": "Para", "app.components.Admin.ImageCropper.cropFinalSentence": "Consulte também: {link}.", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "Mantenha o conteúdo principal dentro das linhas pontilhadas para garantir que ele esteja sempre visível.", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "3:1 no celular (some<PERSON> a á<PERSON> entre as linhas pontilhadas é mostrada)", "app.components.Admin.ImageCropper.cropSentenceOne": "A imagem é cortada automaticamente:", "app.components.Admin.ImageCropper.cropSentenceTwo": "{aspect} na área de trabalho (largura total mostrada)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "<PERSON><PERSON><PERSON> recomenda<PERSON>", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Texto de cabeçalho para usuários registrados", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Aviso: a cor selecionada não tem contraste suficiente. <PERSON><PERSON> pode resultar em um texto difícil de ler. Escolha uma cor mais escura para otimizar a legibilidade.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Adicionar eventos à barra de navegação", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "<PERSON>uando ativado, um link para todos os eventos do projeto será adicionado à barra de navegação.", "app.components.AdminPage.SettingsPage.eventsSection": "Eventos", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Seção personalizada da página inicial", "app.components.AnonymousPostingToggle.userAnonymity": "Anonimato do usuário", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Os usuários poderão ocultar sua identidade de outros usuários, gerentes de projeto e administradores. Essas contribuições ainda podem ser moderadas.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Permitir que os usuários participem de forma anonima", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Os usuários ainda podem optar por participar com seu nome real, mas terão a opção de enviar contribuições anonimas se assim o desejarem. Todos os usuários ainda precisarão cumprir os requisitos definidos na guia Direitos de Acesso para que suas contribuições sejam aprovadas. Os dados do perfil do usuário não estarão disponíveis na exportação de dados da participação.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Saiba mais sobre o anonimato do usuário em nosso {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "artigo de suporte", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Depois que licenças adicionais forem adicionadas, sua fatura aumentará. Entre em contato com seu gestor GovSuccess para saber mais sobre isso.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Obrigado por você ter concluído a pesquisa! Você está convidado a respondê-la novamente no próximo trimestre.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Baixar como pdf", "app.components.FormSync.downloadExcelTemplate": "Faça o download de um modelo do Excel", "app.components.FormSync.downloadExcelTemplateTooltip2": "Os modelos do Excel não incluirão perguntas de classificação, perguntas de matriz, perguntas de upload de arquivo e perguntas de entrada de mapeamento (Drop Pin, Draw Route, Draw Area, upload de arquivo ESRI), pois elas não são compatíveis com a importação em massa no momento.", "app.components.ProjectTemplatePreview.close": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.createProject": "Criar um projeto", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Crie um projeto com base no modelo ''{templateTitle}''", "app.components.ProjectTemplatePreview.goBack": "Voltar", "app.components.ProjectTemplatePreview.goBackTo": "Volte para o {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "Especialista do Go Vocal", "app.components.ProjectTemplatePreview.infoboxLine1": "Você quer usar este modelo para o seu projeto de participação?", "app.components.ProjectTemplatePreview.infoboxLine2": "Entre em contato com a pessoa responsável da administração da sua cidade ou entre em contato com {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Pasta de projetos", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "A data selecionada é inválida. Forneça uma data no seguinte formato: AAAA-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Por favor selecione uma data de início do projeto", "app.components.ProjectTemplatePreview.projectStartDate": "Data de início do seu projeto", "app.components.ProjectTemplatePreview.projectTitle": "Título do seu projeto", "app.components.ProjectTemplatePreview.projectTitleError": "Digite um título de projeto", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Digite um título de projeto para todos os idiomas", "app.components.ProjectTemplatePreview.projectsOverviewPage": "página de visão geral dos projetos", "app.components.ProjectTemplatePreview.responseError": "<PERSON><PERSON> de<PERSON> errado, tente novamente mais tarde ou entre em <NAME_EMAIL>.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "<PERSON><PERSON><PERSON> ma<PERSON>", "app.components.ProjectTemplatePreview.successMessage": "O projeto foi criado com sucesso!", "app.components.ProjectTemplatePreview.typeProjectName": "Digite o nome do projeto", "app.components.ProjectTemplatePreview.useTemplate": "Usar este padrão ", "app.components.SeatInfo.additionalSeats": "Licenças Adicionais", "app.components.SeatInfo.additionalSeatsToolTip": "Is<PERSON> mostra o número de licenças adicionais que você comprou em cima de 'Licenças incluídas'.", "app.components.SeatInfo.adminSeats": "Licenças de administrador", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} licenças de administrador incluídas", "app.components.SeatInfo.adminSeatsTooltip1": "Os administradores são responsáveis pela plataforma e têm direitos de gerente para todas as pastas e projetos. Você pode {visitHelpCenter} para aprender mais sobre as diferentes funções.", "app.components.SeatInfo.currentAdminSeatsTitle": "Licenças de administrador atuais", "app.components.SeatInfo.currentManagerSeatsTitle": "Licenças de gestor atuais", "app.components.SeatInfo.includedAdminToolTip": "Is<PERSON> mostra o número de licenças disponíveis para administradores incluídos no contrato anual.", "app.components.SeatInfo.includedManagerToolTip": "Is<PERSON> mostra o número de licenças disponíveis para gestores incluídos no contrato anual.", "app.components.SeatInfo.includedSeats": "Licenças incluídas", "app.components.SeatInfo.managerSeats": "Licenças de gestores", "app.components.SeatInfo.managerSeatsTooltip": "Os gestores de pastas/projetos podem gerenciar um número ilimitado de pastas/projetos. Você pode {visitHelpCenter} para aprender mais sobre as diferentes funções.", "app.components.SeatInfo.managersIncludedText": "{managerSeats} licenças de gestores incluídas", "app.components.SeatInfo.remainingSeats": "Licenças restantes", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Total de licenças", "app.components.SeatInfo.totalSeatsTooltip": "Is<PERSON> mostra o número somado de licenças em seu plano e licenças adicionais que você comprou.", "app.components.SeatInfo.usedSeats": "Licenças usadas", "app.components.SeatInfo.view": "Visualização", "app.components.SeatInfo.visitHelpCenter": "visite nossa central de ajuda", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "O seu plano tem {adminSeatsIncluded}. Quando tiver utilizado todos as lican<PERSON>s, serão adicionados licenças extra em \"Licenças adicionais\".", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Seu plano tem {manager<PERSON><PERSON><PERSON><PERSON>nc<PERSON>}, elegível para gestores de pasta e gestores de projeto. De<PERSON>is de usar todas as licen<PERSON>s, licenças extras serão adicionados em 'Licenças adicionais'.", "app.components.UserSearch.addModerators": "<PERSON><PERSON><PERSON><PERSON>", "app.components.UserSearch.searchUsers": "Buscar usuários", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Mensagem de erro alternativa", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, a seguinte mensagem de erro será mostrada aos usuários:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Personalizar a mensagem de erro", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "Você pode substituir essa mensagem para cada idioma usando a caixa de texto \"Mensagem de erro alternativa\" abaixo. Se você deixar a caixa de texto vazia, a mensagem padrão será exibida.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "Mensagem de erro", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "Isso é o que os participantes verão quando não atenderem aos requisitos de participação.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "<PERSON><PERSON> mensagem de erro", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "Nenhuma pergunta foi selecionada. Selecione uma pergunta primeiro.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "Sem resposta", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} respostas", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "<PERSON><PERSON><PERSON> da pesquisa", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} até o momento", "app.components.admin.DatePhasePicker.Input.openEnded": "Abe<PERSON>o", "app.components.admin.DatePhasePicker.Input.selectDate": "Selecione a data", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Limpar data final", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Limpar data de início", "app.components.admin.Graphs": "Não há dados disponíveis com os filtros atuais.", "app.components.admin.Graphs.noDataShort": "Não há dados disponíveis.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Comentários ao longo do tempo", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Postagens ao longo do tempo", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Reações ao longo do tempo", "app.components.admin.InputManager.onePost": "1 entrada", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Ajuste de seleções off-line", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Ajuste de votos off-line", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "Essa opção permite que você inclua dados de participação de outras fontes, como votos presenciais ou em papel:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Ele será visualmente diferente dos votos digitais.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "<PERSON><PERSON> a<PERSON> os resultados finais da votação.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "Is<PERSON> não será refletido nos painéis de dados de participação.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Os votos off-line para uma opção só podem ser definidos uma vez em um projeto e são compartilhados entre todas as fases de um projeto.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "Você deve inserir o total de participantes off-line primeiro.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Total de participantes off-line", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "Para calcular os resultados corretos, precisamos saber a <b>quantidade total de participantes off-line para essa fase</b>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Indique apenas aqueles que participaram off-line.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "Modificado por {name}", "app.components.admin.PostManager.PostPreview.assignee": "Atribu<PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.cancelEdit": "Cancelar edição", "app.components.admin.PostManager.PostPreview.currentStatus": "Status atual", "app.components.admin.PostManager.PostPreview.delete": "Deletar", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Tem certeza de que deseja excluir esta entrada? Essa ação não pode ser desfeita.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Tem certeza de que deseja excluir esta entrada? A entrada será excluída de todas as fases do projeto e não pode ser recuperada.", "app.components.admin.PostManager.PostPreview.edit": "<PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.noOne": "Não atribuído", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "O número de vezes que isso foi incluído nos orçamentos participativos de outros participantes", "app.components.admin.PostManager.PostPreview.picks": "Escolhas: {picksN<PERSON>ber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Contagem de reações:", "app.components.admin.PostManager.PostPreview.save": "<PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.submitError": "Erro", "app.components.admin.PostManager.addFeatureLayer": "Adicionar camada de recurso", "app.components.admin.PostManager.addFeatureLayerInstruction": "Copie o URL da camada de recurso hospedada no ArcGIS Online e cole-o na entrada abaixo:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Adicionar uma nova camada de recurso ao mapa", "app.components.admin.PostManager.addWebMap": "Adicionar mapa da Web", "app.components.admin.PostManager.addWebMapInstruction": "Copie o ID do portal do seu Web Map do ArcGIS Online e cole-o na entrada abaixo:", "app.components.admin.PostManager.allPhases": "<PERSON><PERSON> as frases", "app.components.admin.PostManager.allProjects": "Todos os projetos", "app.components.admin.PostManager.allStatuses": "Todos os status", "app.components.admin.PostManager.allTopics": "Todos os tópicos", "app.components.admin.PostManager.anyAssignment": "<PERSON><PERSON><PERSON> tarefa", "app.components.admin.PostManager.assignedTo": "{assigneeName}", "app.components.admin.PostManager.assignedToMe": "Atribuído a mim", "app.components.admin.PostManager.assignee": "Atribu<PERSON><PERSON>", "app.components.admin.PostManager.authenticationError": "Ocorreu um erro de autenticação ao tentar obter esta camada. Verifique o URL e se sua chave de API da Esri tem acesso a essa camada.", "app.components.admin.PostManager.automatedStatusTooltipText": "Esse status é atualizado automaticamente quando as condições são atendidas", "app.components.admin.PostManager.bodyTitle": "Descrição", "app.components.admin.PostManager.cancel": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel2": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.co-sponsors": "Co-patrocinadores", "app.components.admin.PostManager.comments": "Comentários", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "Isto significa que perderá todos os dados associados a estas entradas, como comentários, reacções e votos. Esta ação não pode ser anulada.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Tem a certeza de que pretende eliminar estas entradas?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "Remover tópico", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "Está a tentar retirar esta ideia de uma fase em que recebeu votos. Se o fizer, esses votos perder-se-ão. Tem a certeza de que pretende remover esta ideia desta fase?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "Os votos associados a esta ideia perder-se-ão", "app.components.admin.PostManager.components.goToInputManager": "Ir para o gerenciador de entrada", "app.components.admin.PostManager.components.goToProposalManager": "Ir para o gerenciador de propostas", "app.components.admin.PostManager.contributionFormTitle": "Editar contribuição", "app.components.admin.PostManager.cost": "Custo", "app.components.admin.PostManager.createInput": "Criar entrada", "app.components.admin.PostManager.createInputsDescription": "Criar um novo conjunto de entradas de um projeto anterior", "app.components.admin.PostManager.currentLat": "Latitude atual", "app.components.admin.PostManager.currentLng": "Longitude atual", "app.components.admin.PostManager.currentZoomLevel": "Nível de zoom atual", "app.components.admin.PostManager.defaultEsriError": "Ocorreu um erro ao tentar obter essa camada. Verifique se você está conectado à rede e se o URL está correto.", "app.components.admin.PostManager.delete": "Deletar", "app.components.admin.PostManager.deleteAllSelectedInputs": "Excluir {count} entradas", "app.components.admin.PostManager.deleteConfirmation": "Tem certeza que deseja deletar esta página?", "app.components.admin.PostManager.dislikes": "Não curtidas", "app.components.admin.PostManager.edit": "<PERSON><PERSON>", "app.components.admin.PostManager.editProjects": "<PERSON>ar projeto", "app.components.admin.PostManager.editStatuses": "Editar status", "app.components.admin.PostManager.editTags": "Editar tags", "app.components.admin.PostManager.editedPostSave": "Guardar", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "A importação de dados do Esri ArcGIS Online é um recurso complementar. Fale com seu gerente de GS para desbloqueá-lo.", "app.components.admin.PostManager.esriSideError": "Ocorreu um erro no aplicativo ArcGIS. Aguarde alguns minutos e tente novamente mais tarde.", "app.components.admin.PostManager.esriWebMap": "Mapa da Web da Esri", "app.components.admin.PostManager.exportAllInputs": "Bai<PERSON>r <PERSON> as entradas de informações (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Baixar todos os comentários (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Baixar todos os comentários do projeto (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Exportar entradas para este projeto (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Exportar entradas selecionadas (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Exportar comentários para entradas selecionadas (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Exportar votos por entrada (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Exportar votos por usuário (.xslx)", "app.components.admin.PostManager.exports": "Baixar", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "Você só pode carregar dados de mapa como camadas GeoJSON ou importando do ArcGIS Online. <PERSON><PERSON><PERSON> todas as camadas GeoJSON atuais se você quiser adicionar uma camada de recurso.", "app.components.admin.PostManager.featureLayerTooltop": "Você pode encontrar o URL do Feature Layer no lado direito da página do item no ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Escolha como as pessoas verão seu nome", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Explique essa mudança de status", "app.components.admin.PostManager.fileUploadError": "Um ou mais ficheiros não foram carregados. Por favor verifique o tamanho e formato do ficheiro e tente novamente.", "app.components.admin.PostManager.formTitle": "<PERSON>ar ideia", "app.components.admin.PostManager.generalApiError2": "Ocorreu um erro ao tentar buscar este item. Verifique se o URL ou o ID do portal está correto e se você tem acesso a esse item.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Você só pode carregar dados de mapa como camadas GeoJSON ou importando do ArcGIS Online. Remova todos os dados do ArcGIS se você quiser carregar uma camada GeoJSON.", "app.components.admin.PostManager.goToDefaultMapView": "Acesse o mapa central e zoom padrão", "app.components.admin.PostManager.hiddenFieldsLink": "campo oculto", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Dica: adicione {hiddenFieldsLink} ao configurar seu questionário para acompanhar quem respondeu a pesquisa.", "app.components.admin.PostManager.import2": "Importação", "app.components.admin.PostManager.importError": "O arquivo selecionado não pôde ser importado porque não é um GeoJSON válido", "app.components.admin.PostManager.importEsriFeatureLayer": "Importar camada de característica Esri", "app.components.admin.PostManager.importEsriWebMap": "Importar o Esri Web Map", "app.components.admin.PostManager.importInputs": "Insumos de importação", "app.components.admin.PostManager.imported": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.initiativeFormTitle": "Editar iniciativa", "app.components.admin.PostManager.inputCommentsExportFileName": "inserir_coment<PERSON><PERSON>s", "app.components.admin.PostManager.inputImportProgress": "{importedCount} de {totalCount} {totalCount, plural, one {input has} other {inputs have}} been imported. A importação ainda está em andamento. Verifique novamente mais tarde.", "app.components.admin.PostManager.inputManagerHeader": "Informações", "app.components.admin.PostManager.inputs": "informações", "app.components.admin.PostManager.inputsExportFileName": "informação", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Mostrar apenas entradas que precisam de feedback", "app.components.admin.PostManager.issueFormTitle": "Editar edição", "app.components.admin.PostManager.latestFeedbackMode": "Use a atualização oficial mais recente", "app.components.admin.PostManager.layerAdded": "Camada adicionada com sucesso", "app.components.admin.PostManager.likes": "<PERSON>urt<PERSON>", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Afastar este input do seu projeto atual irá perder a informação sobre as suas fases atribuídas. Quer prosseguir?", "app.components.admin.PostManager.mapData": "Dados do mapa", "app.components.admin.PostManager.multipleInputs": "{ideaCount} entradas", "app.components.admin.PostManager.newFeedbackMode": "Escreva uma nova atualização para explicar esta mudança", "app.components.admin.PostManager.noFilteredResults": "Os filtros que você selecionou não retornaram nenhum resultado", "app.components.admin.PostManager.noInputs": "<PERSON><PERSON><PERSON>a entrada ainda", "app.components.admin.PostManager.noInputsDescription": "Você pode adicionar sua própria contribuição ou começar a partir de um projeto de participação anterior.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 entradas} one {1 entrada} other {# entradas}} serão importadas do projeto e da fase selecionados. A importação será executada em segundo plano, e as entradas aparecerão no gerenciador de entradas assim que ela for concluída.", "app.components.admin.PostManager.noOne": "Não atribuído", "app.components.admin.PostManager.noProject": "Nenhum projeto", "app.components.admin.PostManager.officialFeedbackModal.author": "Autor", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "Escolha como seu nome aparecerá", "app.components.admin.PostManager.officialFeedbackModal.description": "O fornecimento de feedback oficial ajuda a manter o processo transparente e gera confiança na plataforma.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "O autor é obrigatório", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Você precisa de feedback", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Feedback of<PERSON>al", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Explique o motivo da mudança de status", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Postar feedback", "app.components.admin.PostManager.officialFeedbackModal.skip": "Pule desta vez", "app.components.admin.PostManager.officialFeedbackModal.title": "Explique sua decisão", "app.components.admin.PostManager.officialUpdateAuthor": "Escolha como as pessoas verão seu nome", "app.components.admin.PostManager.officialUpdateBody": "Explique essa mudança de status", "app.components.admin.PostManager.offlinePicks": "Escolhas off-line", "app.components.admin.PostManager.offlineVotes": "Votações off-line", "app.components.admin.PostManager.onlineVotes": "Votações on-line", "app.components.admin.PostManager.optionFormTitle": "Opção de edição", "app.components.admin.PostManager.participants": "Participantes", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Escolhas on-line", "app.components.admin.PostManager.pbItemCountTooltip": "O número de vezes que isso foi incluído nos orçamentos participativos de outros participantes", "app.components.admin.PostManager.petitionFormTitle": "<PERSON><PERSON>", "app.components.admin.PostManager.postedIn": "Publicado em {projectLink}", "app.components.admin.PostManager.projectFormTitle": "<PERSON>ar projeto", "app.components.admin.PostManager.projectsTab": "Projetos", "app.components.admin.PostManager.projectsTabTooltipContent": "Você pode arrastar e soltar publicações para movê-las de um projeto para outro. Observe que, para projetos de linha do tempo, você ainda precisará adicionar a postagem a uma fase específica.", "app.components.admin.PostManager.proposalFormTitle": "<PERSON><PERSON> proposta", "app.components.admin.PostManager.proposedBudgetTitle": "Orçamento proposto", "app.components.admin.PostManager.publication_date": "Publicado em", "app.components.admin.PostManager.questionFormTitle": "<PERSON><PERSON>", "app.components.admin.PostManager.reactions": "Reações", "app.components.admin.PostManager.resetFiltersButton": "Redefinir filt<PERSON>", "app.components.admin.PostManager.resetInputFiltersDescription": "Reinicialize os filtros para ver todas as entradas.", "app.components.admin.PostManager.saved": "Salvo", "app.components.admin.PostManager.screeningTooltip": "A triagem não está incluída em seu plano atual. Fale com seu gerente de sucesso do governo ou administrador para desbloqueá-lo.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "A triagem está desativada para essa fase. Vá para a configuração da fase para ativá-la", "app.components.admin.PostManager.selectAPhase": "Selecione uma fase", "app.components.admin.PostManager.selectAProject": "Selecione um projeto", "app.components.admin.PostManager.setAsDefaultMapView": "Salvar o ponto central atual e o de nível zoom como mapa padrão", "app.components.admin.PostManager.startFromPastInputs": "Comece com entradas anteriores", "app.components.admin.PostManager.statusChangeGenericError": "Ocorreu um erro, tente novamente mais tarde ou entre em <NAME_EMAIL>.", "app.components.admin.PostManager.statusChangeSave": "Alterar status", "app.components.admin.PostManager.statusesTab": "Status", "app.components.admin.PostManager.statusesTabTooltipContent": "Altere o status de uma publicação usando arrastar e soltar. O autor original e outros colaboradores receberão uma notificação sobre o status alterado.", "app.components.admin.PostManager.submitApiError": "Houve um problema com a apresentação do formulário. Por favor, verifique se existem erros e tente novamente.", "app.components.admin.PostManager.timelineTab": "Fases", "app.components.admin.PostManager.timelineTabTooltipText": "<PERSON>rraste e solte as entradas para copiá-las para diferentes fases do projeto.", "app.components.admin.PostManager.title": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.topicsTab": "Tópicos", "app.components.admin.PostManager.topicsTabTooltipText": "Add tags to an input using drag and drop.", "app.components.admin.PostManager.view": "<PERSON>er", "app.components.admin.PostManager.votes": "Votos", "app.components.admin.PostManager.votesByInputExportFileName": "votos_por_entrada", "app.components.admin.PostManager.votesByUserExportFileName": "votos_por_usuário", "app.components.admin.PostManager.webMapAlreadyExists": "Você só pode adicionar um Web Map de cada vez. Remova o atual para importar um diferente.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Você só pode carregar dados de mapa como camadas GeoJSON ou importando do ArcGIS Online. Remo<PERSON> todas as camadas GeoJSON atuais se você desejar conectar um Web Map.", "app.components.admin.PostManager.webMapTooltip": "Você pode encontrar o ID do portal do Web Map na sua página de item do ArcGIS Online, no lado direito.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {<PERSON><PERSON> de um dia} one {Um dia} other {# dias}} restantes", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "<PERSON><PERSON><PERSON>", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "<PERSON><PERSON>, excluir os resultados da pesquisa", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "<PERSON><PERSON> não pode ser desfeito", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Excluir resultados da pesquisa", "app.components.admin.ProjectEdit.survey.downloadResults2": "Faça o download dos resultados da pesquisa", "app.components.admin.ReportExportMenu.FileName.fromFilter": "De", "app.components.admin.ReportExportMenu.FileName.groupFilter": "grupo", "app.components.admin.ReportExportMenu.FileName.projectFilter": "Projeto", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tópico", "app.components.admin.ReportExportMenu.FileName.untilFilter": "ate", "app.components.admin.ReportExportMenu.downloadPng": "Descarregar como imagem PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Descarregar como imagem SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Exportar para Excel", "app.components.admin.SlugInput.regexError": "O slug (última parte da URL da página do seu projeto) só pode conter letras minúsculas regulares (a-z), números (0-9) e hifens (-). O primeiro e o último caracteres não podem ser hifens. Hífens consecutivos (-) são proibidos", "app.components.admin.TerminologyConfig.saveButton": "<PERSON><PERSON>", "app.components.admin.commonGroundInputManager.title": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.admin": "Administradores", "app.components.admin.seatSetSuccess.allDone": "<PERSON><PERSON> pronto", "app.components.admin.seatSetSuccess.close": "<PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.manager": "Gerenciar", "app.components.admin.seatSetSuccess.orderCompleted": "Encomenda concluída", "app.components.admin.seatSetSuccess.reflectedMessage": "As alterações em seu plano serão refletidas em sua próxima fatura.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} direitos foram concedidos ao(s) usuário(s) selecionado(s).", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Você tem certeza de que deseja excluir todos os resultados do questionário?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "Esse método de participação está na versão beta. Estamos implementando-o gradualmente para obter feedback e aprimorar a experiência.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "A coleta de comentários sobre um documento é um recurso personalizado e não está incluído em sua licença atual. Entre em contato com seu gerente GovSuccess para saber mais sobre isso.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Proposta", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "O número de dias é necessário", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "Número de dias para atingir o número mínimo de votos", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Mais informações sobre como incorporar um link para os Formulários Google podem ser encontradas em {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "este artigo de apoio", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Ideia", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Iniciativa", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Como deve ser chamada uma entrada?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Problema", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Forneça o link para o seu documento Konveio aqui. Leia nosso {supportArticleLink} para obter mais informações sobre como configurar o Konveio.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "artigo de suporte", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "Isso não está incluído em seu plano atual. Entre em contato com seu gerente de sucesso do governo ou administrador para desbloqueá-lo.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "É necessário um orçamento máximo", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "O número máximo de votos por opção deve ser inferior ou igual ao número total de votos", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "É necessário um número máximo de votos", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "Mensagens", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "O orçamento mínimo não pode ser maior do que o orçamento máximo", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "É necessário um orçamento mínimo", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "O número mínimo de votos não pode ser superior ao número máximo", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "É necessário um número mínimo de votos", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Data final ausente", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Data de início ausente", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Opcao", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Aba do Gerenciador de entrada", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Configure as opções de votação na guia Gerenciador de entrada depois de criar uma fase.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Configurar as opções de votação no {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Opções de participação", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "Participantes", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Petição", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Administradores e gerentes", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Anotação de documento:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} você pode participar dessa fase.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Coment<PERSON><PERSON>:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Fase de aterramento comum", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Excluir fase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "<PERSON><PERSON>, exclua essa fase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "Você tem certeza de que deseja excluir essa fase?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "Todos os dados relacionados a essa fase serão excluídos. Isso não pode ser desfeito.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Fase de anotação de documentos", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Todos", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "Fase de pesquisa externa", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Fase de ideação", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "Em fase de pesquisa de plataforma", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Fase de informação", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "Direitos mistos", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "Sem data de término", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Fase de enquete", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Fase de propostas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>Reagir:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Registrado no evento:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Usuários registrados", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b>Enviar entradas:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>Você está fazendo uma enquete:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b>Fazer uma pesquisa:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Usuários com e-mails confirmados", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b><PERSON><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Fase de voluntariado", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Votação:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Fase de votação", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Quem pode participar?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "As entradas não ficarão visíveis até que um administrador as revise e aprove. Os autores não podem editar as entradas depois que elas forem examinadas ou reagidas.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "Somente administradores", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "Qualquer pessoa com o link pode interagir com o projeto preliminar", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "A aprovação permite que os gerentes de projeto publiquem o projeto.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "Aprovado por {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Arquivado", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Editar descri<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "Todos", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Grupos", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "Oculto", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Eleitores off-line", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Somente os administradores{inFolder, select, true { ou os gerentes de pasta} other {}} podem publicar o projeto.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 participante} other {{participantsCount} participantes}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Participantes de métodos incorporados (por exemplo, pesquisas externas)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Seguidores de um projeto", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Observação: a ativação de permissões de participação anônima ou aberta pode permitir que os usuários participem várias vezes, levando a dados de usuário enganosos ou incompletos.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "Os participantes <b>não incluem</b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Os participantes incluem:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Registrantes de eventos", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "Usuários interagindo com os métodos da Go Vocal", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "A<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Os revisores do projeto foram notificados.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "Público", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Publicar", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Publicado - Ativo", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Publicado - Finalizado", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Atualizar o link de visualização do projeto", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Gerar novamente o link de visualização do projeto. Isso <PERSON> o link anterior.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "<PERSON><PERSON> links antigos deixarão de funcionar, mas você pode gerar um novo a qualquer momento.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Você tem certeza? <PERSON><PERSON> desativar<PERSON> o link atual", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "<PERSON>m, atualize o link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Solicitar aprovação", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "O projeto deve ser aprovado por um administrador{inFolder, select, true { ou por um dos gerentes de pasta} other {}} antes que você possa publicá-lo. Clique no botão abaixo para solicitar a aprovação.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Configurações", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Compartilhar", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "Copiar link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "<PERSON> copiado", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "O compartilhamento de links privados não está incluído no seu plano atual. Fale com seu gerente de sucesso do governo ou administrador para desbloqueá-lo.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Compartilhe este projeto", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "Quem tem acesso", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "<PERSON>er", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Projeto", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Proposta", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Número mínimo de votos a serem considerados", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "É necessário um número mínimo de votos", "app.components.app.containers.AdminPage.ProjectEdit.report": "Relat<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Exigir a triagem de insumos", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Linha do tempo", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Tráfego", "app.components.formBuilder.cancelMethodChange1": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.changeMethodWarning1": "A mudança de métodos pode levar à ocultação de quaisquer dados de entrada gerados ou recebidos durante o uso do método anterior.", "app.components.formBuilder.changingMethod1": "Método de modificação", "app.components.formBuilder.confirmMethodChange1": "<PERSON>m, continue", "app.components.formBuilder.copySurveyModal.cancel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.description": "<PERSON><PERSON> cop<PERSON> todas as perguntas e a lógica sem as respostas.", "app.components.formBuilder.copySurveyModal.duplicate": "Dup<PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "Não foram encontradas fases apropriadas neste projeto", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "Nenhuma fase foi selecionada. Selecione uma fase primeiro.", "app.components.formBuilder.copySurveyModal.noProject": "Nenhum projeto", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Nenhum projeto foi selecionado. Por favor, selecione um projeto primeiro.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "Você já salvou as alterações nesta pesquisa. Se você duplicar outro questionário, as alterações serão perdidas.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Fase de pesquisa", "app.components.formBuilder.copySurveyModal.title": "Selecione uma pesquisa para duplicar", "app.components.formBuilder.editWarningModal.addOrReorder": "Adicionar ou reordenar perguntas", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Os seus dados de resposta podem ser incorretos", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Editar texto", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Corrigindo um erro de digitação? Isso não afetará seus dados de resposta", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Você perderá os dados de resposta vinculados a essa pergunta", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Excluir uma pergunta", "app.components.formBuilder.editWarningModal.exportYouResponses2": "exporte suas respostas.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Aviso: você poderá perder dados de resposta para sempre. Antes de continuar,", "app.components.formBuilder.editWarningModal.noCancel": "Não, cancelar", "app.components.formBuilder.editWarningModal.title4": "Editar pesquisa ao vivo", "app.components.formBuilder.editWarningModal.yesContinue": "<PERSON>m, continuar", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "configurações de direitos de acesso para esta pesquisa", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "'Campos demográficos no formulário de pesquisa' est<PERSON> ativado. Quando o formulário de pesquisa for exibido, todas as perguntas demográficas configuradas serão adicionadas em uma nova página imediatamente antes do final da pesquisa. Essas perguntas podem ser alteradas no site {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "configurações de direitos de acesso para essa fase.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Os respondentes da pesquisa não precisarão se inscrever ou fazer login para enviar as respostas da pesquisa, o que pode resultar em envios duplicados", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "Ao ignorar a etapa de inscrição/logon, você aceita não coletar informações demográficas sobre os respondentes do questionário, o que pode afetar seus recursos de análise de dados", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "Esse questionário está definido para permitir o acesso de \"Qualquer pessoa\" na guia Direitos de acesso.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "Se quiser alterar isso, você pode fazer isso no site {accessRightsSettingsLink}", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Você está fazendo as seguintes perguntas demográficas aos respondentes do questionário por meio da etapa de inscrição/logon.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "Para agilizar a coleta de informações demográficas e garantir sua integração ao seu banco de dados de usuários, recomendamos que você incorpore todas as perguntas demográficas diretamente no processo de inscrição/log-in. Para isso, use o endereço {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Pedir aos utilizadores para seguirem áreas ou tópicos", "app.components.onboarding.followHelperText": "Isto ativa um passo no processo de registo em que os utilizadores poderão seguir as áreas ou tópicos que selecionar abaixo", "app.components.onboarding.followPreferences": "<PERSON><PERSON><PERSON> as preferências", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} dentro do plano, {noOfAdditionalSeats} adicional", "app.components.seatsWithinPlan.seatsWithinPlanText": "Licenças dentro do plano", "app.containers.Admin.Campaigns.campaignFrom": "De:", "app.containers.Admin.Campaigns.campaignTo": "Para:", "app.containers.Admin.Campaigns.customEmails": "E-mails personalizados", "app.containers.Admin.Campaigns.customEmailsDescription": "Envie e-mails personalizados e verifique as estatísticas.", "app.containers.Admin.Campaigns.noAccess": "<PERSON><PERSON><PERSON><PERSON>, mas parece que você não tem acesso à seção de e-mails", "app.containers.Admin.Campaigns.tabAutomatedEmails": "E-mail automático", "app.containers.Admin.Insights.tabReports": "Relatórios", "app.containers.Admin.Invitations.a11y_removeInvite": "Remover convite", "app.containers.Admin.Invitations.addToGroupLabel": "Adicione essas pessoas a grupos manuais específicos", "app.containers.Admin.Invitations.adminLabel1": "Dar direitos de administração aos convidados", "app.containers.Admin.Invitations.adminLabelTooltip": "Quando você seleciona esta opção, as pessoas que você está convidando terão acesso a todas as configurações da sua plataforma.", "app.containers.Admin.Invitations.configureInvitations": "3. Configure os convites", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Não há convites que correspondam à sua pesquisa", "app.containers.Admin.Invitations.deleteInvite": "Cancelado", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Você tem certeza de que deseja excluir esse convite?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Cancelar o envio de um convite, permitirá que você reenvie um convite a esta pessoa.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "Baixe e preencha o modelo", "app.containers.Admin.Invitations.downloadTemplate": "Baixar modelo", "app.containers.Admin.Invitations.email": "E-mail", "app.containers.Admin.Invitations.emailListLabel": "Insira os endereços de e-mail na caixa abaixo, separados por vírgula", "app.containers.Admin.Invitations.exportInvites": "Exportar a lista de convites", "app.containers.Admin.Invitations.fileRequirements": "Importante: Não exclua nenhuma coluna do modelo de importação - as colunas que não forem usadas devem ser deixadas vazias. Antes de importar o arquivo, certifique-se de que não haja endereços de e-mail duplicados, para evitar erro no envio dos convites", "app.containers.Admin.Invitations.filetypeError": "Tipo de arquivo incorreto. Apenas arquivos XLSX são suportados.", "app.containers.Admin.Invitations.groupsPlaceholder": "Nenhum grupo selecionado", "app.containers.Admin.Invitations.helmetDescription": "Convide usuários para a plataforma", "app.containers.Admin.Invitations.helmetTitle": "Painel de convite de administrador", "app.containers.Admin.Invitations.importOptionsInfo": "Você pode adicionar uma mensagem pessoal ao convite, adicionar pessoas a um grupo ou conceder a elas direitos de administrador ou moderação. Visite {supportPageLink} para mais informações.", "app.containers.Admin.Invitations.importTab": "Importar endereços de e-mail", "app.containers.Admin.Invitations.invitationExpirationWarning": "Esteja ciente de que os convites expiram após 30 dias. Após esse período, você ainda poderá reenviá-los.", "app.containers.Admin.Invitations.invitationOptions": "Opções de convite", "app.containers.Admin.Invitations.invitationSubtitle": "Convide pessoas que ainda não estão cadastradas na plataforma. Importe seus endereços de e-mail colocando-os no modelo de importação ou insira os endereços de e-mail manualmente. Se desejar, adicione uma mensagem pessoal, dê direitos extras às pessoas ou adicione-as a um grupo manual.", "app.containers.Admin.Invitations.invitePeople": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteStatus": "Status", "app.containers.Admin.Invitations.inviteStatusAccepted": "<PERSON><PERSON>", "app.containers.Admin.Invitations.inviteStatusPending": "Pendente", "app.containers.Admin.Invitations.inviteTextLabel": "Adicione uma mensagem pessoal ao convite", "app.containers.Admin.Invitations.invitedSince": "Convidado em", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Selecione a linguagem do convite", "app.containers.Admin.Invitations.moderatorLabel": "Dê a essas pessoas direitos de gerenciamento de projetos", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Ao selecionar esta opção, as pessoas que você está convidando se tornarão gerente de projeto dos projetos selecionados. Você pode encontrar mais informações sobre os direitos de gerenciamento {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "Aqui", "app.containers.Admin.Invitations.name": "Nome", "app.containers.Admin.Invitations.processing": "Enviando convites. Por favor, espere...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Nenhum projeto selecionado", "app.containers.Admin.Invitations.save": "Envie seus convites", "app.containers.Admin.Invitations.saveErrorMessage": "Ocorreram um ou mais erros.\n      Portanto nenhum convite foi enviado.\n      Corrija o(s) erro(s) listado(s) abaixo e tente novamente.", "app.containers.Admin.Invitations.saveSuccess": "Sucesso!", "app.containers.Admin.Invitations.saveSuccessMessage": "Convites enviados com sucesso", "app.containers.Admin.Invitations.supportPage": "página de suporte", "app.containers.Admin.Invitations.supportPageLinkText": "Visite a página de suporte", "app.containers.Admin.Invitations.tabAllInvitations": "Todos os convites", "app.containers.Admin.Invitations.tabInviteUsers": "<PERSON><PERSON><PERSON> pessoas", "app.containers.Admin.Invitations.textTab": "Digite os endereços de e-mail", "app.containers.Admin.Invitations.unknownError": "Algo deu errado. Por favor, tente novamente mais tarde.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Carregue o seu arquivo completamente", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} se você quiser mais informações sobre todas as colunas com suporte no modelo de importação.", "app.containers.Admin.Moderation.all": "<PERSON><PERSON>", "app.containers.Admin.Moderation.belongsTo": "Pertence a", "app.containers.Admin.Moderation.collapse": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.comment": "Comentários", "app.containers.Admin.Moderation.commentDeletionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "Excluir", "app.containers.Admin.Moderation.confirmCommentDeletion": "Você tem certeza de que deseja excluir este comentário? Isso é permanente e não pode ser desfeito.", "app.containers.Admin.Moderation.content": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.date": "Data", "app.containers.Admin.Moderation.deleteComment": "Excluir coment<PERSON>", "app.containers.Admin.Moderation.goToComment": "Abra este comentário em uma nova aba", "app.containers.Admin.Moderation.goToPost": "Abra este em uma nova guia", "app.containers.Admin.Moderation.goToProposal": "Abra esta proposta em uma nova guia", "app.containers.Admin.Moderation.markFlagsError": "Não foi possível marcar o(s) item(itens). Tente novamente.", "app.containers.Admin.Moderation.markNotSeen": "Marque {selectedItemsCount, plural, um {# item} other {# itens}} como não visto", "app.containers.Admin.Moderation.markSeen": "Marque {selectedItemsCount, plural, um {# Item} other {# itens}} como visto", "app.containers.Admin.Moderation.moderationsTooltip": "Esta página permite que você verifique rapidamente todas as novas entradas que foram adicionadas à sua plataforma, incluindo postagens e comentários. Você pode marcar os posts como sendo 'vistos' para que outros saibam quais inputs ainda precisam ser processados.", "app.containers.Admin.Moderation.noUnviewedItems": "Não há itens não vistos", "app.containers.Admin.Moderation.noViewedItems": "Não há itens vistos", "app.containers.Admin.Moderation.pageTitle1": "Conte<PERSON><PERSON>", "app.containers.Admin.Moderation.post": "Informação", "app.containers.Admin.Moderation.profanityBlockerSetting": "Bloqueador de palavras impróprias", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Bloqueio de postagens contendo as palavras ofensivas mais comumente relatadas.", "app.containers.Admin.Moderation.project": "Projeto", "app.containers.Admin.Moderation.read": "Visto", "app.containers.Admin.Moderation.readMore": "Ler mais", "app.containers.Admin.Moderation.removeFlagsError": "Não foi possível remover o(s) aviso(s). Tente novamente.", "app.containers.Admin.Moderation.rowsPerPage": "<PERSON><PERSON> por página", "app.containers.Admin.Moderation.settings": "Configurações", "app.containers.Admin.Moderation.settingsSavingError": "Não foi possível salvar. <PERSON><PERSON> alterar as configurações novamente.", "app.containers.Admin.Moderation.show": "Mostrar", "app.containers.Admin.Moderation.status": "Status", "app.containers.Admin.Moderation.successfulUpdateSettings": "Configurações atualizadas com sucesso.", "app.containers.Admin.Moderation.type": "Tipo", "app.containers.Admin.Moderation.unread": "Não visto", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Esta página é constituída pelas seguintes seções. Pode ligá-las/desligá-las e editá-las conforme as suas necessidades.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Seções", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "<PERSON><PERSON> p<PERSON><PERSON>a", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Não mostrado na página", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Mostrado na página", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Anexos", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Adicionar a<PERSON> (máx. 50 MB) que estarão disponíveis para descarregar a partir da página.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Seção de informação", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Adicione o seu próprio conteúdo à seção personalizável na parte inferior da página inicial.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Lista de eventos", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Mostrar eventos relacionados com os projetos.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Bandeira do herói", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Personalize a imagem e o texto do banner da página inicial.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Lista de projetos", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Mostrar os projetos com base nas definições da sua página. Pode pré-visualizar os projetos que serão mostrados.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Seção de informação", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Adicione o seu próprio conteúdo à seção personalizável na parte inferior da página inicial.", "app.containers.Admin.PagesAndMenu.addButton": "Adicionar à barra de navegação", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Nome na barra de navegação", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Tem a certeza de que quer apagar esta página? Isto não pode ser desfeito.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Forneça um título para todos os idiomas", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Outras páginas disponíveis", "app.containers.Admin.PagesAndMenu.components.savePage": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "Página salva com sucesso", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Anexos (max. 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Sucesso", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Não foi possível salvar os anexos", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Os arquivos não devem ser maiores que 50 Mb. Os arquivos adicionados serão mostrados na página da pasta", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Anexos salvos", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Anexos | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Anexos", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Salvar e ativar anexos", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "<PERSON>var anexos", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Por favor, forneça o conteúdo para todas as línguas", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Sucesso", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Não foi possível salvar a seção de informação", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Secção de informação salva", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Seção de informação", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Salvar e ativar a seção de informação", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Salvar seção de informação", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "A criação de páginas personalizadas não está incluída em sua licença atual. Entre em contato com o seu gerente do GovSuccess para saber mais sobre isso.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Por favor selecione pelo menos uma etiqueta", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Sucesso", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "Por etiqueta(s)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "A exibição de projetos por tag ou área não faz parte de sua licença atual. Entre em contato com seu gerente do GovSuccess para saber mais sobre isso.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Editar página personalizada", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Projetos vinculados", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Selecione quais projetos e eventos relacionados podem ser exibidos na página.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Página criada com sucesso", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "Página salva com sucesso", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Páginas personalizada salva", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Título na barra de navegação", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "C<PERSON>r página personalizada | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Criar página personalizada", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Definições de página", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "<PERSON>var página personalizada", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Por favor seleccione uma área", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Etiquetas seleccionadas", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "O slug (última parte da URL da página do seu projeto) só pode conter letras minúsculas regulares (a-z), números (0-9) e hifens (-). O primeiro e o último caracteres não podem ser hifens. Hífens consecutivos (-) são proibidos", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Deve introduzir uma slug (conjunto único de palavras no final do endereço web, ou Url da página)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Introduzir um título em cada idioma", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Introduzir um título", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Ver página personalizada", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "Botão", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Editar página personalizada | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "Para projetos de fase única, se a data final estiver vazia e a descrição não estiver preenchida, não será exibida uma linha do tempo na página do projeto.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "Não há projetos disponíveis baseados no seu {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Este projeto não tem tag ou filtro de área, então nenhum projeto será exibido.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Lista de projetos | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "definições de página", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Lista de projetos", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "<PERSON><PERSON> <PERSON><PERSON>tes projetos serão apresentados nesta página com base no seu {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "PADRÃO", "app.containers.Admin.PagesAndMenu.deleteButton": "Deletar", "app.containers.Admin.PagesAndMenu.editButton": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Sucesso", "app.containers.Admin.PagesAndMenu.heroBannerError": "Não foi possível salvar o banner", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Banner salvo", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "<PERSON><PERSON> banner", "app.containers.Admin.PagesAndMenu.homeTitle": "Início", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Por favor, forneça o conteúdo pelo menos em um idioma", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Você só pode adicionar até 5 itens à barra de navegação", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Páginas & menu | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Remover da barra de navegação", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "<PERSON><PERSON> e ativar hero banner", "app.containers.Admin.PagesAndMenu.title": "Páginas & menu", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Sucesso", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.topInfoError": "Não foi possível salvar a seção de informação", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Seção de informação salva", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Seção de informação | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Seção de informação", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Salvar e ativar informação de sessão do topo da página", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Salvar seção de informação", "app.containers.Admin.PagesAndMenu.viewButton": "Visão", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "<PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Comunidade", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Resumo executivo", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Indicadores de inclusão de nível superior", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "A seção a seguir descreve os indicadores de inclusão, destacando o nosso progresso no sentido de promover uma plataforma de participação mais inclusiva e representativa.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "participantes", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Indicadores de participação de alto nível", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "A seção a seguir descreve os principais indicadores de participação para o intervalo de tempo selecionado, fornecendo uma visão geral das tendências de envolvimento e métricas de desempenho.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projetos", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "projetos publicados", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Relatório da plataforma", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "Seus projetos", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "A seção a seguir fornece uma visão geral dos projetos publicamente visíveis que se sobrepõem ao intervalo de tempo selecionado, os métodos mais usados nesses projetos e as métricas referentes ao valor total da participação.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Cronograma de registros", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Usuários bloqueados", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Gerenciar usuários bloqueados.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Apagar grupo", "app.containers.Admin.Users.GroupsHeader.editGroup": "Editar grupo", "app.containers.Admin.Users.GroupsPanel.admins": "Administradores", "app.containers.Admin.Users.GroupsPanel.allUsers": "Usuários", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Grupos", "app.containers.Admin.Users.GroupsPanel.managers": "Gerentes de projeto", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Itens atribuídos", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Crie grupos e adicione usuários.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "<PERSON><PERSON><PERSON> pen<PERSON>e", "app.containers.Admin.Users.admin": "Admin ", "app.containers.Admin.Users.assign": "Atribuir", "app.containers.Admin.Users.assignedItems": "Itens atribuídos para {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "Compre uma licença adicional", "app.containers.Admin.Users.changeUserRights": "Alterar direitos do usuário", "app.containers.Admin.Users.confirm": "Confirme", "app.containers.Admin.Users.confirmAdminQuestion": "Tem a certeza de que quer dar direitos de administrador da plataforma {name} ?", "app.containers.Admin.Users.confirmNormalUserQuestion": "Tem a certeza de que quer definir {name} como um utilizador normal?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Tem certeza de que deseja definir {name} como um usuário normal? Observe que eles perderão os direitos de gerente de todos os projetos e pastas aos quais foram atribuídos na confirmação.", "app.containers.Admin.Users.deleteUser": "Excluir este usuário", "app.containers.Admin.Users.email": "E-mail", "app.containers.Admin.Users.folder": "Pasta", "app.containers.Admin.Users.folderManager": "Gestor de pastas", "app.containers.Admin.Users.helmetDescription": "Lista de usuários na retaguarda do administrador", "app.containers.Admin.Users.helmetTitle": "Admin - <PERSON><PERSON> de <PERSON>", "app.containers.Admin.Users.inviteUsers": "<PERSON><PERSON><PERSON> us<PERSON>", "app.containers.Admin.Users.joined": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.lastActive": "Último ativo", "app.containers.Admin.Users.name": "Nome", "app.containers.Admin.Users.noAssignedItems": "Nenhum item atribuído", "app.containers.Admin.Users.options": "Opções", "app.containers.Admin.Users.permissionToBuy": "Para dar direitos de administrador a {name} , é necessário comprar 1 lugar adicional.", "app.containers.Admin.Users.platformAdmin": "Plataforma admin", "app.containers.Admin.Users.projectManager": "Gestor de projeto", "app.containers.Admin.Users.reachedLimitMessage": "Atingiu o limite de lugares dentro do seu plano, será acrescentado 1 lugar adicional para {name} .", "app.containers.Admin.Users.registeredUser": "Utilizador registado", "app.containers.Admin.Users.remove": "Remover", "app.containers.Admin.Users.removeModeratorFrom": "O usuário está moderando a pasta à qual esse projeto pertence. Em vez disso, remova a atribuição de \"{folderTitle}\".", "app.containers.Admin.Users.role": "Função", "app.containers.Admin.Users.seeProfile": "Veja o perfil deste usuário", "app.containers.Admin.Users.selectPublications": "Selecionar projetos ou pastas", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Digite para pesquisar", "app.containers.Admin.Users.setAsAdmin": "Definir como administrador", "app.containers.Admin.Users.setAsNormalUser": "Definir como utilizador normal", "app.containers.Admin.Users.setAsProjectModerator": "Definir como gerente de projeto", "app.containers.Admin.Users.setUserAsProjectModerator": "Designe {name} como gerente de projeto", "app.containers.Admin.Users.userBlockModal.allDone": "<PERSON><PERSON> feito", "app.containers.Admin.Users.userBlockModal.blockAction": "Utilizador de b<PERSON>o", "app.containers.Admin.Users.userBlockModal.blockInfo1": "O conteúdo deste usuário não será removido por meio desta ação. Não se esqueça de moderar o conteúdo, se necessário.", "app.containers.Admin.Users.userBlockModal.blocked": "Bloqueado", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Este usuário está bloqueado desde {from}. O banimento dura até {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Tem certeza de que deseja desbloquear {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} est<PERSON> bloqueado até {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 dia} other {{numberOfDays} dias}}", "app.containers.Admin.Users.userBlockModal.header": "Utilizador de b<PERSON>o", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Justificação", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "<PERSON><PERSON> será comunicado ao usuário bloqueado.", "app.containers.Admin.Users.userBlockModal.subtitle1": "O usuário selecionado não poderá fazer login na plataforma por {daysBlocked}. Se desejar reverter isso, você pode desbloqueá-los da lista de usuários bloqueados.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Desb<PERSON>que<PERSON>", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON>m, eu quero desbloquear este utilizador", "app.containers.Admin.Users.userDeletionConfirmation": "Remover permanentemente este usuário ?", "app.containers.Admin.Users.userDeletionFailed": "Ocorreu um erro ao excluir este usuário, tente novamente", "app.containers.Admin.Users.userDeletionProposalVotes": "<PERSON><PERSON> tamb<PERSON>m excluirá todos os votos desse usuário em propostas que ainda estão abertas para votação.", "app.containers.Admin.Users.userExportFileName": "exportar_usuário", "app.containers.Admin.Users.userInsights": "Insights do usuário", "app.containers.Admin.Users.youCantDeleteYourself": "Você não pode deletar sua própria conta através da página de administração do usuário", "app.containers.Admin.Users.youCantUnadminYourself": "Você não pode desistir de sua função de administrador agora", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Monitoramento da comunidade", "app.containers.Admin.communityMonitor.healthScore": "Pontuação de saúde", "app.containers.Admin.communityMonitor.healthScoreDescription": "Essa pontuação é a média de todas as perguntas da escala de sentimentos respondidas pelos participantes no período selecionado.", "app.containers.Admin.communityMonitor.lastQuarter": "último trimestre", "app.containers.Admin.communityMonitor.liveMonitor": "Monitoramento ao vivo", "app.containers.Admin.communityMonitor.noResults": "Não há resultados para esse período.", "app.containers.Admin.communityMonitor.noSurveyResponses": "Nenhuma resposta à pesquisa", "app.containers.Admin.communityMonitor.participants": "Participantes", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Relatórios", "app.containers.Admin.communityMonitor.settings": "Configurações", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "A Pesquisa do Monitor Comunitário está aceitando envios.", "app.containers.Admin.communityMonitor.settings.accessRights2": "Direitos de acesso", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "Depois que um usuário registra a participação em um evento, envia uma votação ou retorna a uma página de projeto após enviar uma pesquisa.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Os gerentes do Community Monitor podem acessar e gerenciar todas as configurações e dados do Community Monitor.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Gerentes de monitores comunitários", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Os gerentes podem editar o questionário e as permissões do Community Monitor, ver os dados de resposta e criar relatórios.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "O valor padrão da frequência é 100%.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Frequência de pop-up (0 a 100)", "app.containers.Admin.communityMonitor.settings.management2": "Gerenciamento", "app.containers.Admin.communityMonitor.settings.popup": "Popup", "app.containers.Admin.communityMonitor.settings.popupDescription3": "Um pop-up é exibido periodicamente para os usuários, incentivando-os a responder à Pesquisa do Community Monitor. Você pode ajustar a frequência que determina a porcentagem de usuários que verão o pop-up aleatoriam<PERSON> quando as condições descritas abaixo forem atendidas.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Configurações de pop-up", "app.containers.Admin.communityMonitor.settings.preview": "Prévia", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "O usuário ainda não preencheu a pesquisa nos últimos 3 meses.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "O usuário ainda não viu o pop-up nos últimos 3 meses.", "app.containers.Admin.communityMonitor.settings.save": "<PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.saved": "Salvo", "app.containers.Admin.communityMonitor.settings.settings": "Configurações", "app.containers.Admin.communityMonitor.settings.survey2": "Pesquisa", "app.containers.Admin.communityMonitor.settings.surveySettings3": "Configurações gerais", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Ao carregar a página inicial ou uma página personalizada.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "<PERSON><PERSON><PERSON><PERSON> todos os dados do usuário", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "<PERSON><PERSON> as informações dos usuários da pesquisa serão anonimizadas antes de serem registradas", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Os usuários ainda precisarão cumprir os requisitos de participação em \"Direitos de acesso\". Os dados do perfil do usuário não estarão disponíveis na exportação de dados da pesquisa.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "Em que condições o pop-up pode ser exibido para os usuários?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Quem são os gerentes?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Total de respostas da pesquisa", "app.containers.Admin.communityMonitor.upsell.aiSummary": "<PERSON><PERSON><PERSON> da IA", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Ativar o Community Monitor", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Esse recurso não está incluído em seu plano atual. Fale com seu gerente de sucesso do governo ou administrador para desbloqueá-lo.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Pontuação de saúde", "app.containers.Admin.communityMonitor.upsell.learnMore": "<PERSON><PERSON> mais", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Pontuação ao longo do tempo", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "O Community Monitor ajuda você a ficar à frente, monitorando a confiança dos residentes, a satisfação com os serviços e a vida comunitária - continuamente.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Obtenha pontuações claras, citações poderosas e um relatório trimestral que você pode compartilhar com colegas ou autoridades eleitas.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Pontuações fáceis de ler que evoluem com o tempo", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Principais citações de residentes, resumidas por IA", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Perguntas adaptadas ao contexto de sua cidade", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Residentes recrutados aleatoriamente na plataforma", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Relatórios trimestrais em PDF, prontos para serem compartilhados", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "Entenda como sua comunidade se sente antes que os problemas aumentem", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Contagem", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Desktop ou outro", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "<PERSON><PERSON><PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tablet", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Tipos de dispositivos", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Tipo de dispositivo", "app.containers.Admin.earlyAccessLabel": "<PERSON><PERSON> antecipado", "app.containers.Admin.earlyAccessLabelExplanation": "Esse é um recurso recém-lançado disponível no Acesso antecipado.", "app.containers.Admin.emails.addCampaign": "Criar e-mail", "app.containers.Admin.emails.addCampaignTitle": "Criar um novo e-mail", "app.containers.Admin.emails.allParticipantsInProject": "Todos os participantes do projeto", "app.containers.Admin.emails.allUsers": "Todos os usuários", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Emails automatizados são enviados automaticamente e acionados pelas ações de um usuário. Você pode desativar alguns deles para os usuários de sua plataforma. Os demais e-mails automatizados não podem ser desativados porque são necessários para o bom funcionamento de sua plataforma.", "app.containers.Admin.emails.automatedEmails": "E-mails automatizados", "app.containers.Admin.emails.automatedEmailsDigest": "O e-mail só será enviado se houver conteúdo", "app.containers.Admin.emails.automatedEmailsRecipients": "Usuários que receberão este e-mail", "app.containers.Admin.emails.automatedEmailsTriggers": "Evento que aciona este e-mail", "app.containers.Admin.emails.changeRecipientsButton": "Alterar des<PERSON>", "app.containers.Admin.emails.clickOnButtonForExamples": "Clique no botão abaixo para ver exemplos deste correio electrónico na nossa página de apoio.", "app.containers.Admin.emails.confirmSendHeader": "Enviar e-mail para todos os usuários?", "app.containers.Admin.emails.deleteButtonLabel": "Deletar", "app.containers.Admin.emails.draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editButtonLabel": "<PERSON><PERSON>", "app.containers.Admin.emails.editCampaignTitle": "<PERSON><PERSON>", "app.containers.Admin.emails.editDisabledTooltip2": "Em breve: Este e-mail não pode ser editado no momento.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Texto do botão", "app.containers.Admin.emails.editRegion_intro_multiloc": "Introdução", "app.containers.Admin.emails.editRegion_subject_multiloc": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editRegion_title_multiloc": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.emailCreated": "E-mail criado com sucesso em rascunho", "app.containers.Admin.emails.emailUpdated": "E-mail atualizado com sucesso", "app.containers.Admin.emails.emptyCampaignsDescription": "Conecte-se facilmente com seus participantes enviando-lhes e-mails. Escolha quem contatar e acompanhe seu envolvimento.", "app.containers.Admin.emails.emptyCampaignsHeader": "Envie seu primeiro e-mail", "app.containers.Admin.emails.failed": "<PERSON>ão entregue", "app.containers.Admin.emails.fieldBody": "Mensagem", "app.containers.Admin.emails.fieldBodyError": "Por favor, forneça um título de e-mail para todas as línguas", "app.containers.Admin.emails.fieldGroupContent": "Conteúdo do e-mail", "app.containers.Admin.emails.fieldReplyTo": "As respostas devem ir para", "app.containers.Admin.emails.fieldReplyToEmailError": "Introduzir um endereço de correio electrônico no formato correto, como <EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Forneça um endereço de e-mail", "app.containers.Admin.emails.fieldReplyToTooltip": "Você pode escolher para onde enviar respostas ao seu e-mail.", "app.containers.Admin.emails.fieldSender": "De", "app.containers.Admin.emails.fieldSenderError": "Fornecer um remetente do e-mail", "app.containers.Admin.emails.fieldSenderTooltip": "Você pode decidir quem os destinatários verão como remetente do e-mail.", "app.containers.Admin.emails.fieldSubject": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldSubjectError": "Por favor, forneça um título de e-mail para todas as línguas", "app.containers.Admin.emails.fieldSubjectTooltip": "Is<PERSON> será mostrado na linha de assunto do e-mail e na visão geral da caixa de entrada do usuário. Seja claro e atrativo.", "app.containers.Admin.emails.fieldTo": "Para:", "app.containers.Admin.emails.fieldToTooltip": "Você pode selecionar os grupos de usuários que receberão seu e-mail.", "app.containers.Admin.emails.formSave": "<PERSON><PERSON> como rascunho", "app.containers.Admin.emails.formSaveAsDraft": "<PERSON><PERSON> como rascunho", "app.containers.Admin.emails.from": "De:", "app.containers.Admin.emails.groups": "Grupos", "app.containers.Admin.emails.helmetDescription": "Envie e-mails manuais para determinados grupos de cidadãos e campanhas automatizadas ativas", "app.containers.Admin.emails.nameVariablesInfo2": "Você pode falar diretamente com os cidadãos usando as variáveis {firstName} {lastName}. <PERSON><PERSON> exemplo, \"Prezado {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "Um e-mail de visualização prévia foi enviado para o seu endereço de e-mail", "app.containers.Admin.emails.previewTitle": "Vista prévia", "app.containers.Admin.emails.regionMultilocError": "Forneça um valor para todos os idiomas", "app.containers.Admin.emails.seeEmailHereText": "Assim que um e-mail deste tipo for enviado, poderá consultá-lo aqui.", "app.containers.Admin.emails.send": "Enviar", "app.containers.Admin.emails.sendNowButton": "enviar agora", "app.containers.Admin.emails.sendTestEmailButton": "Envie-me um e-mail de teste", "app.containers.Admin.emails.sendTestEmailTooltip2": "Quando você clicar nesse link, um e-mail de teste será enviado somente para o seu endereço de e-mail. Isso permite que você verifique a aparência do e-mail na \"vida real\".", "app.containers.Admin.emails.senderRecipients": "Remetente e destinatários", "app.containers.Admin.emails.sending": "Enviando", "app.containers.Admin.emails.sent": "Enviado", "app.containers.Admin.emails.sentToUsers": "Estes são e-mails enviados aos usuários", "app.containers.Admin.emails.subject": "Assunto:", "app.containers.Admin.emails.supportButtonLabel": "Veja exemplos em nossa página de suporte", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "Para:", "app.containers.Admin.emails.toAllUsers": "Quer enviar este email para todos os usuários?", "app.containers.Admin.emails.viewExample": "Visualizar", "app.containers.Admin.ideas.import": "Importar", "app.containers.Admin.inspirationHub.AllProjects": "Todos os projetos", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Monitoramento da comunidade", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Anotação de documento", "app.containers.Admin.inspirationHub.ExternalSurvey": "Pesquisa externa", "app.containers.Admin.inspirationHub.Filters.Country": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Method": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Search": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Topic": "Tópico", "app.containers.Admin.inspirationHub.Filters.population": "População", "app.containers.Admin.inspirationHub.Highlighted": "Em destaque", "app.containers.Admin.inspirationHub.Ideation": "Ideação", "app.containers.Admin.inspirationHub.Information": "Informações", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Escolha um país para ver os projetos fixados", "app.containers.Admin.inspirationHub.PinnedProjects.country": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "Não foram encontrados projetos fixados para este país. Altere o país para ver os projetos fixados em outros países", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Mude o país para ver mais projetos fixados", "app.containers.Admin.inspirationHub.Poll": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "Abe<PERSON>o", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "<PERSON><PERSON> mais...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Fase {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "Se você não quiser que seu projeto seja incluído no centro de inspiração, entre em contato com o gerente do GovSuccess.", "app.containers.Admin.inspirationHub.Proposals": "Propostas", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Ordenar por", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Participantes (o menor primeiro)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Participantes (o mais alto primeiro)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Data de início (a mais antiga primeiro)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Data de início (a mais recente primeiro)", "app.containers.Admin.inspirationHub.Survey": "Pesquisa", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Lista selecionada dos melhores projetos em todo o mundo.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Converse com outros profissionais e aprenda com eles.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filtre por método, tamanho da cidade e país.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Ativar o Inspiration Hub", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Esse recurso não está incluído em seu plano atual. Fale com seu gerente de sucesso do governo ou administrador para desbloqueá-lo.", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "https://support.govocal.com/en/articles/11093736-using-the-inspiration-hub", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "<PERSON><PERSON> mais", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "O Inspiration Hub conecta você a um feed com curadoria de projetos de participação excepcionais nas plataformas Go Vocal em todo o mundo. Saiba como outras cidades realizam projetos bem-sucedidos e converse com outros profissionais.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Participe de uma rede de profissionais pioneiros em democracia", "app.containers.Admin.inspirationHub.Volunteering": "Voluntariado", "app.containers.Admin.inspirationHub.Voting": "Votação", "app.containers.Admin.inspirationHub.commonGround": "Pontos em comum", "app.containers.Admin.inspirationHub.filters": "filtros", "app.containers.Admin.inspirationHub.resetFilters": "Redefinir filt<PERSON>", "app.containers.Admin.inspirationHub.seemsLike": "Parece que não há mais projetos. Tente alterar o endereço {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "Editar campos de campanha", "app.containers.Admin.messaging.automated.variablesToolTip": "Você pode usar as seguintes variáveis em sua mensagem:", "app.containers.Admin.messaging.helmetTitle": "Mensagens", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "Esse widget mostra os projetos de cada usuário <b>com base em suas preferências de acompanhamento</b>. Isso inclui projetos que eles seguem, bem como projetos nos quais eles seguem entradas e projetos relacionados a tópicos ou áreas de seu interesse.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "Esse widget só será exibido para o usuário se houver projetos dos quais ele possa participar. Se você vir essa mensagem, significa que você (o administrador) não pode participar de nenhum projeto no momento. Essa mensagem não ficará visível na página inicial real.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Aberto à participação", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "Esse widget exibirá projetos nos quais o usuário pode <b>realizar uma ação para participar</b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "Para você", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "Esse widget só será exibido para o usuário se houver projetos relevantes para ele com base em suas preferências de acompanhamento. Se você vir essa mensagem, significa que você (o administrador) não está seguindo nada no momento. Essa mensagem não ficará visível na página inicial real.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "<PERSON><PERSON> seguidos", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Arquivado", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Filtrar por", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Terminado", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Finalizado e arquivado", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "Não há dados disponíveis", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "Esse widget mostra <b>projetos que foram concluídos e/ou arquivados</b>. \"Concluído\" também inclui projetos que estão na última fase e nos quais a última fase é um relatório.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Projetos concluídos", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "<PERSON>oc<PERSON> disse, nós fizemos...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "Forneça um nome para todos os idiomas", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "O projeto não pode estar vazio", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Nome", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Projeto", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "URL resultante", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Adicionar projeto", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "A barra de navegação mostrará apenas os projetos aos quais os usuários têm acesso.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "Esse widget só ficará visível na página inicial quando o Monitor da comunidade estiver aceitando respostas.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Monitor da comunidade", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Descrição", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "Botão", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Importante:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Exemplo de uma pergunta de pesquisa de sentimento", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "Sem data de término", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "Pressione escape para pular o carrossel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projetos e pastas (legado)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Título do projeto", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} está trabalhando atualmente em", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Texto do botão", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Participe agora!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Descrição", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "pasta", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Selecione um projeto ou pasta", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Selecione o projeto ou a pasta", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Mostrar avatares", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Destaque", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "A partir de {days} dias", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "A partir de {weeks} semanas", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} dias atrás", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} semanas atrás", "app.containers.Admin.project.Campaigns.campaignFrom": "De:", "app.containers.Admin.project.Campaigns.campaignTo": "Para:", "app.containers.Admin.project.Campaigns.customEmails": "E-mails personalizados", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Envie e-mails personalizados e verifique as estatísticas.", "app.containers.Admin.project.Campaigns.noAccess": "<PERSON><PERSON><PERSON><PERSON>, mas parece que você não tem acesso à seção de e-mails", "app.containers.Admin.project.emails.addCampaign": "Criar e-mail", "app.containers.Admin.project.emails.addCampaignTitle": "Nova campanha", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Todos os {participants} e seguidores do projeto", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Isso inclui usuários registrados que realizaram qualquer ação no projeto. Usuários não registrados ou anônimos não estão incluídos.", "app.containers.Admin.project.emails.dateSent": "Data de envio", "app.containers.Admin.project.emails.deleteButtonLabel": "Excluir", "app.containers.Admin.project.emails.draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.editButtonLabel": "<PERSON><PERSON>", "app.containers.Admin.project.emails.editCampaignTitle": "<PERSON><PERSON>", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Conecte-se facilmente com seus participantes enviando-lhes e-mails. Escolha quem contatar e acompanhe seu envolvimento.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Envie seu primeiro e-mail", "app.containers.Admin.project.emails.failed": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.fieldBody": "Mensagem de e-mail", "app.containers.Admin.project.emails.fieldBodyError": "Forneça uma mensagem de e-mail para todos os idiomas", "app.containers.Admin.project.emails.fieldReplyTo": "As respostas devem ser enviadas para", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Forneça um endereço de e-mail no formato correto, por exemplo, <EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "Forneça um endereço de e-mail", "app.containers.Admin.project.emails.fieldReplyToTooltip": "Escolha qual endereço de e-mail deve receber respostas diretas dos usuários em seu e-mail.", "app.containers.Admin.project.emails.fieldSender": "De", "app.containers.Admin.project.emails.fieldSenderError": "Forneça o remetente do e-mail", "app.containers.Admin.project.emails.fieldSenderTooltip": "Escolha quem os usuários verão como o remetente do e-mail.", "app.containers.Admin.project.emails.fieldSubject": "Assunto do e-mail", "app.containers.Admin.project.emails.fieldSubjectError": "Forneça um assunto de e-mail para todos os idiomas", "app.containers.Admin.project.emails.fieldSubjectTooltip": "Isso será mostrado na linha de assunto do e-mail e na visão geral da caixa de entrada do usuário. Deixe-o claro e envolvente.", "app.containers.Admin.project.emails.fieldTo": "Para", "app.containers.Admin.project.emails.formSave": "<PERSON><PERSON> como rascunho", "app.containers.Admin.project.emails.from": "De:", "app.containers.Admin.project.emails.helmetDescription": "Enviar e-mails manuais aos participantes do projeto", "app.containers.Admin.project.emails.infoboxAdminText": "Na guia Project Messaging (Mensagens do projeto), você só pode enviar e-mails a todos os participantes do projeto.  Para enviar e-mails a outros participantes ou subconjuntos de usuários, acesse a guia {link} .", "app.containers.Admin.project.emails.infoboxLinkText": "Mensagens da plataforma", "app.containers.Admin.project.emails.infoboxModeratorText": "Na guia Project Messaging (Mensagens do projeto), você só pode enviar e-mails a todos os participantes do projeto. Os administradores podem enviar e-mails para outros participantes ou subconjuntos de usuários por meio da guia Platform Messaging (Mensagens da plataforma).", "app.containers.Admin.project.emails.message": "Mensagem", "app.containers.Admin.project.emails.nameVariablesInfo2": "Você pode falar diretamente com os cidadãos usando as variáveis {firstName} {lastName}. <PERSON><PERSON> exemplo, \"Prezado {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "participantes", "app.containers.Admin.project.emails.previewSentConfirmation": "Um e-mail de visualização foi enviado para o seu endereço de e-mail", "app.containers.Admin.project.emails.previewTitle": "Prévia", "app.containers.Admin.project.emails.projectParticipants": "Participantes do projeto", "app.containers.Admin.project.emails.recipients": "Beneficiários", "app.containers.Admin.project.emails.send": "Enviar", "app.containers.Admin.project.emails.sendTestEmailButton": "Enviar uma visualização", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Envie este rascunho de e-mail para o endereço de e-mail com o qual você está conectado, para verificar como ele se parece na \"vida real\".", "app.containers.Admin.project.emails.senderRecipients": "Remetente e destinatários", "app.containers.Admin.project.emails.sending": "<PERSON><PERSON>", "app.containers.Admin.project.emails.sent": "Enviado", "app.containers.Admin.project.emails.sentToUsers": "Esses são e-mails enviados aos usuários", "app.containers.Admin.project.emails.status": "Status", "app.containers.Admin.project.emails.subject": "Assunto:", "app.containers.Admin.project.emails.to": "Para:", "app.containers.Admin.project.messaging.helmetTitle": "Mensagens", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "Esta imagem faz parte do cartão da pasta; o cartão que resume a pasta e é mostrado na página inicial, por exemplo. Para mais informações sobre as resoluções de imagem recomendadas, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Esta imagem é mostrada no topo da página da pasta. Para mais informações sobre as resoluções de imagem recomendadas, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "visite o nosso centro de apoio", "app.containers.Admin.projects.all.askPersonalData3": "Adicionar campos para nome e e-mail", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "Ativar a visualização do calendário", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "Esse recurso não está incluído em seu plano atual. Fale com seu gerente de sucesso do governo ou administrador para desbloqueá-lo.", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "<PERSON><PERSON> mais", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "Obtenha uma visão geral dos cronogramas de seus projetos em nossa visualização de calendário. Identifique rapidamente quais projetos e fases estão começando ou terminando em breve e precisam de ação.", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "Entenda o que está acontecendo e quando", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Todas as perguntas são exibidas no PDF. No entanto, no momento, não há suporte para importação via FormSync para os seguintes itens: Imagens, Tags e Upload de arquivos.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Todas as perguntas são exibidas no PDF. No entanto, as perguntas a seguir não são compatíveis com a importação via FormSync: Perguntas de mapeamento (drop pin, draw route e draw area), perguntas de classificação, perguntas de matriz e perguntas de upload de arquivo.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "Fim do formulário", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Início do formulário", "app.containers.Admin.projects.all.components.archived": "Arquivado", "app.containers.Admin.projects.all.components.draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.components.manageButtonLabel": "Gerenciar", "app.containers.Admin.projects.all.copyProjectButton": "Projeto de cópia", "app.containers.Admin.projects.all.copyProjectError": "Houve um erro ao copiar este projeto, por favor tente novamente mais tarde.", "app.containers.Admin.projects.all.customiseEnd": "Personalize o final do formulário.", "app.containers.Admin.projects.all.customiseStart": "Personalize o início do formulário.", "app.containers.Admin.projects.all.deleteFolderButton1": "Apagar pasta", "app.containers.Admin.projects.all.deleteFolderConfirm": "Tem certeza de que deseja excluir esta pasta? Todos os projetos da pasta também serão excluídos. Essa ação não pode ser desfeita.", "app.containers.Admin.projects.all.deleteFolderError": "Ocorreu um problema ao remover esta pasta. Por favor, tente novamente.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Eliminar projeto", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Tem certeza de que deseja excluir este projeto? Isto não pode ser desfeito.", "app.containers.Admin.projects.all.deleteProjectError": "Ocorreu um erro ao excluir este projeto, tente novamente mais tarde.", "app.containers.Admin.projects.all.exportAsPDF1": "Faça o download do formulário em PDF", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Você pode combinar respostas on-line e off-line. Para carregar respostas off-line, vá para a guia \"Gerenciador de entrada\" deste projeto e clique em \"Importar\".", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Você pode combinar respostas on-line e off-line. Para carregar respostas off-line, vá para a guia \"Survey\" (Pesquisa) desse projeto e clique em \"Import\" (Importar).", "app.containers.Admin.projects.all.logicNotInPDF": "A lógica da pesquisa não será refletida no PDF baixado. Os questionados em papel verão todas as perguntas da pesquisa.", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "Pesquisar pastas", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "<PERSON><PERSON> as pastas foram carregadas", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Pasta", "app.containers.Admin.projects.all.new.Folders.Table.managers": "G<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# projeto} other {# projetos}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Data de início do projeto", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Capacidade de descoberta", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "Oculto", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "Público", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Pastas", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filtrar pelo método de participação na fase atual", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "Ideação", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Informações", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Método de participação", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Anotação de documento", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Pontos em comum", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "Pesquisa", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Propostas", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Voluntariado", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Votação", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "Coleta de dados", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informar", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "Não iniciado", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Estado de participação", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Passado", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "Aprovação pendente", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "Projetos de pesquisa", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Em ordem alfabética (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Em ordem alfabética (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Fase que começa ou termina em breve", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc": "Criado <PERSON> (novo-antigo)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc": "Criado <PERSON> (antigo-novo)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "Visualizado recentemente", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Administradores", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Grupos", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Visibilidade", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "Público", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "<PERSON><PERSON><PERSON><PERSON> filtro", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "Não há mais filtros a serem adicionados", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Administradores", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Todos os projetos foram carregados", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "<PERSON><PERSON><PERSON> pessoa", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Arquivado", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "Fase atual", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d esquerda", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d para iniciar", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Capacidade de descoberta:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.end": "Fim", "app.containers.Admin.projects.all.new.Projects.Table.ended": "Terminou", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "<PERSON><PERSON><PERSON> hoje", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Grupos", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "Oculto", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "<PERSON><PERSON><PERSON> mais…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}sem esquerda", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}mo para começar", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Próxima fase:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "Não atribuído", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Fase", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "Pré-lançamento", "app.containers.Admin.projects.all.new.Projects.Table.project": "Projeto", "app.containers.Admin.projects.all.new.Projects.Table.public": "Público", "app.containers.Admin.projects.all.new.Projects.Table.published": "Publicado", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Role a tela para baixo para ver mais", "app.containers.Admin.projects.all.new.Projects.Table.start": "Início", "app.containers.Admin.projects.all.new.Projects.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Status:", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "Esta coluna usa dados de participantes armazenados em cache. Para ver os números mais recentes, verifique a guia \"Participants\" (Participantes) do projeto.", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Visibilidade", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Visibilidade:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} grupos", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} gere<PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}y esquerda", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y para iniciar", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Fase atual: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} dias restantes", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Pasta: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "<PERSON><PERSON><PERSON><PERSON> fase atual", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "Sem data de término", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "Sem fases", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Fase {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Fases:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Projeto", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Data de início: {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "Organizar projetos", "app.containers.Admin.projects.all.new.calendar": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.folders": "Pastas", "app.containers.Admin.projects.all.new.projects": "Projetos", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "Falha ao carregar a linha do tempo.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "O projeto não tem data de término", "app.containers.Admin.projects.all.new.timeline.project": "Projeto", "app.containers.Admin.projects.all.notes": "Notas", "app.containers.Admin.projects.all.personalDataExplanation5": "Essa opção adicionará os campos de nome, sobrenome e email ao PDF exportado. Ao carregar o formulário impresso, usaremos esses dados para gerar automaticamente uma conta para o respondente da pesquisa off-line.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "<PERSON><PERSON><PERSON> da IA", "app.containers.Admin.projects.project.analysis.Comments.comments": "Comentários", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "O resumo do comentário está disponível quando há 5 ou mais comentários.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {Atual<PERSON>r} =1 {1 novo comentário} other {# novos comentários}}", "app.containers.Admin.projects.project.analysis.aiSummary": "<PERSON><PERSON><PERSON> da IA", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "Este é um conteúdo gerado por IA. Ele pode não ser 100% preciso. Revise e faça referência cruzada com os inputs reais para verificar a precisão. Lembre-se de que a precisão provavelmente melhorará se o número de entradas selecionadas for reduzido.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Notificações por e-mail enviadas somente aos participantes", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "Oculto", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "Não é indexado pelos mecanismos de pesquisa", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Não visível na página inicial ou nos widgets", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Acessível somente por meio de URL direto", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "Público", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "Selecione o grau de descoberta desse projeto.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "Esse projeto fica visível para todos que têm acesso e será exibido na página inicial e nos widgets.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Esse projeto ficará oculto para o público em geral e só será visível para aqueles que tiverem o link.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Quem pode encontrar esse projeto?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Análise de IA aberta", "app.containers.Admin.projects.project.ideas.analysisText2": "Explore resumos com tecnologia de IA e visualize envios individuais.", "app.containers.Admin.projects.project.ideas.importInputs": "Importação", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "Depois de criar um relatório, você pode optar por compartilhá-lo com o público assim que a fase começar.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Criar uma página mais complexa para o compartilhamento de informações", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Crie um relatório para você:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "Criar um relatório", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Crie um relatório para uma fase anterior ou comece do zero.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "Este relatório não é público. Para torná-lo público, ative o botão de alternância \"Visível\".", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "Essa fase foi iniciada, mas o relatório ainda não é público. Para torná-lo público, ative o botão de alternância \"Visível\".", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Comece com um modelo de fase", "app.containers.Admin.projects.project.information.ReportTab.report": "Relat<PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Compartilhe os resultados de uma pesquisa anterior ou da fase de ideação", "app.containers.Admin.projects.project.information.ReportTab.visible": "Visível", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "Esse relatório se tornará público assim que a fase começar. Para que ele não seja público, desative o botão \"Visível\".", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "No momento, este relatório é público. Para torná-lo não público, desative a opção \"Visível\".", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Você tem certeza de que deseja excluir este relatório? Essa ação não pode ser desfeita.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Adicionar à fase", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "Você precisa consentir com isso antes de continuar", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "O formulário pode ser baixado aqui.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "O formulário carregado foi criado com a seção \"Dados pessoais\"", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Linguagem do formulário", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "Autorizo o processamento deste arquivo utilizando o Google Cloud Form Parser", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Importar arquivo do Excel", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Por favor, faça upload de um arquivo para continuar", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "O modelo pode ser baixado aqui.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Faça upload de um <b>arquivo Excel</b> (.xlsx) completo. Ele deve usar o modelo fornecido para este projeto. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Faça upload de um <b>arquivo PDF de formulários digitalizados</b>. Você deve usar um formulário impresso a partir desta fase. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Use este e-mail para o novo usuário", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Digite um e-mail válido para criar uma nova conta", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Uma nova conta será criada para o autor com esses detalhes. Essa entrada será adicionada a ela.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "Primeiro nome", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Sobrenome", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Digite um endereço de e-mail e/ou um nome e sobrenome para atribuir essa entrada a um autor. Ou desmarque a caixa de consentimento.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Já existe uma conta associada a esse e-mail. Essa entrada será adicionada a ela.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Consentimento do usuário (criar conta de usuário)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "<PERSON><PERSON><PERSON> as entradas", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Autor:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "E-mail:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Ocorreram erros durante a importação e alguns insumos não foram importados. Corrija os erros e importe novamente os insumos ausentes.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Dados de formulário inválidos. Verifique se há erros no formulário acima.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Importar arquivo do Excel (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Importar", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Importar formulários digitalizados (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Importar formulários digitalizados", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "Entradas importadas", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importação. Esse processo pode levar alguns minutos.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "Essa entrada foi importada de forma anônima.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} foram importados e precisam de aprovação.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} não foi possível aprovar as entradas. Verifique se há problemas de validação em cada entrada e confirme individualmente.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Localidade:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Nada para revisar ainda. Clique em \"{importFile}\" para importar um arquivo PDF contendo formulários de entrada digitalizados ou um arquivo Excel contendo entradas.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Você ainda não tem nada para revisar. Clique em \"{importFile}\" para importar um arquivo Excel que contenha entradas.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "Entrada", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Não é possível exibir o arquivo importado. A visualização de arquivos importados está disponível apenas para importações de PDF.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Fase:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "A fase selecionada não pode conter entradas. Selecione outra.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Este projeto não contém nenhuma fase que possa conter ideias.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "Selecione em qual fase você deseja adicionar essas entradas.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Importador de entradas", "app.containers.Admin.projects.project.participation.comments": "Comentários", "app.containers.Admin.projects.project.participation.inputs": "Entradas", "app.containers.Admin.projects.project.participation.participantsTimeline": "Linha do tempo dos participantes", "app.containers.Admin.projects.project.participation.reactions": "Reações", "app.containers.Admin.projects.project.participation.selectPeriod": "Selecione o período", "app.containers.Admin.projects.project.participation.usersByAge": "Usuários por idade", "app.containers.Admin.projects.project.participation.usersByGender": "Usuários por gênero", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "Adicionar uma pergunta", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "A capacidade de adicionar ou editar campos de usuário no nível de fase não está incluída na sua licença atual. Entre em contato com o seu gerente do GovSuccess para saber mais sobre isso.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} opç<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "Status do campo", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "Essas perguntas serão adicionadas como a última página do formulário de pesquisa, porque a opção \"Mostrar campos na pesquisa?\" foi selecionada nas configurações de fase.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "Não serão feitas perguntas adicionais.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Opcional", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Opcional - sempre ativado porque referenciado pelo grupo", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "Remover campo", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Obrigatório - sempre ativado porque referenciado pelo grupo", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Autenticar com {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Complete as perguntas extras abaixo", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Confirme seu e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Dados retornados do método de verificação:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Digite o nome, o sobrenome, o e-mail e a senha", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "Digite seu e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "Com que frequência os usuários devem ser verificados?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Verificação de identidade com {verificationMethod} (com base no grupo de usuários)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "Não são necessárias ações para participar", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Use grupos inteligentes para restringir a participação com base nos dados verificados listados acima", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Os usuários devem ter sido verificados nos últimos 30 minutos.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Os usuários devem ter sido verificados nos últimos {days} dias.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "Nos últimos 30 dias", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "Nos últimos 30 minutos", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "Nos últimos 7 dias", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "Uma vez é o suficiente", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Campos verificados:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} verificação", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Criação de conta", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Os participantes precisam criar uma conta completa com seu nome, e-mail confirmado e senha.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Os participantes precisam criar uma conta completa com seu nome, e-mail e senha.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Autenticação", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Confirmação por e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Os participantes precisam confirmar seu e-mail com um código único.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Detecção avançada de spam", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "Esse recurso ajuda a evitar envios duplicados de questionários de usuários desconectados, analisando endereços IP e dados de dispositivos. Embora não seja tão preciso quanto exigir login, ele pode ajudar a reduzir o número de respostas duplicadas.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Observação: Em redes compartilhadas (como escritórios ou Wi-Fi público), há uma pequena chance de que diferentes usuários possam ser marcados como duplicatas.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Ativar a detecção avançada de spam", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Perguntas extras feitas aos participantes", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Qualquer pessoa pode participar sem se inscrever ou fazer login.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Redefinir perguntas e grupos extras", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Restringir a participação a grupo(s) de usuários", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "Verificação de SSO", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Os participantes precisam verificar sua identidade em {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Análise de IA aberta", "app.containers.Admin.projects.project.survey.allFiles": "Todos os arquivos", "app.containers.Admin.projects.project.survey.allResponses": "<PERSON><PERSON> as respostas", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Precisão: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Ocorreu um erro ao gerar o resumo da IA. Tente gerá-lo novamente abaixo.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Análise de IA aberta", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "Ocultar resumos desta pergunta", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "entradas selecionadas", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Ações de análise abertas", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} novas respostas", "app.containers.Admin.projects.project.survey.analysis.regenerate": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Mostrar insights de IA", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "Você pode resumir um máximo de 30 entradas por vez em seu plano atual. Fale com seu gerente ou administrador do GovSuccess para saber mais.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Selecione questões relacionadas para análise", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "Deseja incluir alguma outra questão relacionada em sua análise de {question}?", "app.containers.Admin.projects.project.survey.cancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "Concordo em usar OpenAI como processador de dados para este projeto", "app.containers.Admin.projects.project.survey.consentModalText1": "Ao continuar, você concorda em usar OpenAI como processador de dados para este projeto.", "app.containers.Admin.projects.project.survey.consentModalText2": "As APIs OpenAI potencializam os resumos de texto automatizados e partes da experiência de marcação automatizada.", "app.containers.Admin.projects.project.survey.consentModalText3": "Enviamos apenas o que os usuários escreveram em suas pesquisas, ideias e comentários para as APIs OpenAI, nunca qualquer informação de seu perfil.", "app.containers.Admin.projects.project.survey.consentModalText4": "A OpenAI não utilizará estes dados para treinar os seus modelos. Para mais informações sobre a forma como a OpenAI lida com a privacidade dos dados, consultar {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "aqui", "app.containers.Admin.projects.project.survey.consentModalTitle": "<PERSON>tes de continuar", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "Não é possível inserir a análise antes de você ter editado o formulário", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Deletar", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Tem certeza de que deseja excluir esta entrada? Essa ação não pode ser desfeita.", "app.containers.Admin.projects.project.survey.explore": "Explorar", "app.containers.Admin.projects.project.survey.followUpResponses": "Respostas de acompanhamento", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> média", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Exportar como GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Exporte as respostas a essa pergunta como um arquivo GeoJSON. Para cada recurso GeoJSON, todas as respostas de pesquisa do entrevistado relacionado serão listadas no objeto \"propriedades\" desse recurso.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} respondentes} one {{respondentCount} respondentes} other {{respondentCount} respondentes}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} escolhas} one {{numberChoices} escolha} other {{numberChoices} escolhas}}", "app.containers.Admin.projects.project.survey.heatMap": "Mapa de calor", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Saiba mais sobre os mapas de calor gerados com o Esri Smart Mapping.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "O mapa de calor é gerado usando o Esri Smart Mapping. Os mapas de calor são úteis quando há uma grande quantidade de pontos de dados. Para um número menor de pontos, pode ser melhor examinar apenas os pontos de localização diretamente. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Visualização do mapa de calor", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Oculto pela lógica", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "Quando um usuário seleciona essa resposta, a lógica pula todas as páginas até a página {pageNumber} ({numQuestionsSkipped} perguntas puladas). Clique para ocultar ou mostrar as páginas e perguntas puladas.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "Quando um usuário seleciona essa resposta, a lógica pula para o final do questionário ({numQuestionsSkipped} questions skipped). Clique para ocultar ou mostrar as páginas e perguntas ignoradas.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "A lógica nesta página pula todas as páginas até a página {pageNumber} ({numQuestionsSkipped} questões puladas). Clique para ocultar ou exibir as páginas e perguntas puladas.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "A lógica nesta página pula para o final do questionário ({numQuestionsSkipped} perguntas puladas). Clique para ocultar ou mostrar as páginas e perguntas puladas.", "app.containers.Admin.projects.project.survey.newAnalysis": "Nova análise", "app.containers.Admin.projects.project.survey.nextInsight": "Próximo insight", "app.containers.Admin.projects.project.survey.openAnalysis": "Análise de IA aberta", "app.containers.Admin.projects.project.survey.otherResponses": "Outras respostas", "app.containers.Admin.projects.project.survey.page": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.previousInsight": "Insight anterior", "app.containers.Admin.projects.project.survey.responses": "Respostas", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "O número de respostas para essa página é menor do que o número total de respostas da pesquisa porque alguns respondentes não terão visto essa página devido à lógica da pesquisa.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "O número de respostas para essa pergunta é menor do que o número total de respostas da pesquisa porque alguns respondentes não viram essa pergunta devido à lógica da pesquisa.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Escala de sentimento", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Resuma instantaneamente todas as suas respostas.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Fale com seus dados em linguagem natural.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Obtenha referências a respostas individuais a partir de resumos gerados por IA.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Consulte nosso site {link} para obter uma visão geral completa.", "app.containers.Admin.projects.project.survey.upsell.button": "Desbloquear a análise de IA", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "artigo de apoio", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Analise os dados mais rapidamente com a IA", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "Esse recurso não está incluído em seu plano atual. Fale com seu gerente de Government Success Manager ou administrador para desbloqueá-lo.", "app.containers.Admin.projects.project.survey.viewAnalysis": "Visualizar", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Explore resumos com tecnologia de IA e visualize envios individuais.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Selecione o período", "app.containers.Admin.projects.project.traffic.trafficSources": "Fontes de tráfego", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Mudamos a forma como coletamos e exibimos os dados dos visitantes. Como resultado, os dados dos visitantes são mais precisos e mais tipos de dados estão disponíveis, sem deixar de estar em conformidade com o GDPR. Só começamos a coletar esses novos dados em novembro de 2024, portanto, antes disso, não há dados disponíveis.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Linha do tempo dos visitantes", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Relatório de fase", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Adicione algum texto sobre a fase", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Esse é o texto. Você pode editá-lo e formatá-lo usando o editor no painel à direita.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Participantes", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Resultados do projeto", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Resumo do relatório", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Acrescentar o objectivo do projeto, os métodos de participação utilizados, e o resultado", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Visitantes", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Este relatório contém alterações não salvas. Por favor, salve-o antes de imprimir.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "O título já está sendo usado", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "Em comparação com os dias anteriores {days}", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Ocultar estatísticas", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Taxa de participação", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Mostrar comparação com o último período", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "Você precisa selecionar um intervalo de datas primeiro.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "Comentários", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Entradas", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Participação", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "<PERSON>rar coment<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "Mostrar entradas", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Mostrar votos", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "Votos", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Dados demográficos", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Intervalo de datas de registro", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Campo de registro", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Desconhecido", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Usuários: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "Alongamento", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Ativo", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Arquivado", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "Terminado", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "Abe<PERSON>o", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "Planejado", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projetos", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Status da publicação", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Publicado", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "Os dados desse widget estão faltando. Reconfigure-o ou exclua-o para que você possa salvar o relatório.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Pontuação de saúde do monitor comunitário", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "Trimestre", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "Não foram encontradas fases apropriadas neste projeto", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "Nenhuma fase foi selecionada. Selecione uma fase primeiro.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "Nenhum projeto", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Nenhum projeto foi selecionado. Por favor, selecione um projeto primeiro.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "Você não pode duplicar esse relatório porque ele contém dados aos quais você não tem acesso.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "Você não pode editar esse relatório porque ele contém dados aos quais você não tem acesso.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Você tem certeza de que deseja excluir \"{reportName}\"? Essa ação não pode ser desfeita.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Você tem certeza de que deseja excluir este relatório? Essa ação não pode ser desfeita.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Deletar", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Dup<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "Modificado {days, plural, no {# dias} one {# dia} other {# dias}} atrás", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Ocorreu um erro ao tentar criar este relatório. Por favor, tente novamente mais tarde.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Comece com uma página em branco", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Comece com um modelo do Community Monitor", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Título do relatório", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "Criar um relatório", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Personalize seu relatório e compartilhe-o com as partes interessadas internas ou com a comunidade como um arquivo PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Criar um relatório", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Crie seu primeiro relatório", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "Nenhum projeto selecionado", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Comece com um modelo de plataforma", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Imprimir para PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Comece com um modelo de projeto", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Trimestre {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Modelo de relatório", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Já existe um relatório com este título. Por favor, escolha um título diferente.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Selecione o trimestre", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Selecione o ano", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Partilhar como PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Para partilhar com todos, imprimir o relatório em PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Partilhar como ligação web", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "Este link da web só pode ser acessado por usuários administradores.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Compartilhar", "app.containers.Admin.reporting.contactToAccess": "A criação de um relatório personalizado faz parte da licença premium. Contacte o seu GovSuccess Manager para saber mais sobre isso.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Todos os relatórios", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Relatórios do monitor comunitário", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Esses relatórios estão relacionados ao Community Monitor. Os relatórios são gerados automaticamente a cada trimestre.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Criar um relatório", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Personalize seu relatório e compartilhe com os usuários internos ou com a comunidade por meio da web.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Seus relatórios serão exibidos aqui.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Relatórios de pesquisa", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Relatórios de progresso", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Esses são relatórios criados pelo seu Gerente de sucesso do governo", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "Seus relatórios", "app.containers.Admin.reporting.deprecated": "DEPRECADO", "app.containers.Admin.reporting.helmetDescription": "Página de relatórios de administração", "app.containers.Admin.reporting.helmetTitle": "Relat<PERSON><PERSON>", "app.containers.Admin.reporting.printPrepare": "Preparar para imprimir...", "app.containers.Admin.reporting.reportBuilder": "Elaboração de Relatórios", "app.containers.Admin.reporting.reportHeader": "Cabeçalho do relatório", "app.containers.Admin.reporting.warningBanner3": "Os gráficos e números deste relatório são atualizados automaticamente apenas nesta página. Salve o relatório para atualizá-los em outras páginas.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Pontos em comum", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Ideação", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Informações", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Métodos utilizados", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "Pesquisa", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "Anterior {days} dias: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Propostas", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "Pesquisa externa", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Voluntariado", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Votação", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Gráfico", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "<PERSON>er", "app.containers.Admin.surveyFormTab.downloads": "Downloads", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "Duplicar outro <PERSON><PERSON><PERSON>", "app.containers.Admin.surveyFormTab.editSurveyForm": "Editar formulá<PERSON> de pesquisa", "app.containers.Admin.surveyFormTab.inputFormDescription": "Especifique quais informações devem ser fornecidas, adicione descrições curtas ou instruções para orientar as respostas dos participantes e especifique se cada campo é opcional ou obrigatório.", "app.containers.Admin.surveyFormTab.surveyForm": "Formulário de pesquisa", "app.containers.Admin.tools.apiTokens.createTokenButton": "Criar nova ficha", "app.containers.Admin.tools.apiTokens.createTokenCancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Seu token foi criado. Copie o endereço {secret} abaixo e guarde-o com segurança.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Criar uma nova ficha para utilizar com a nossa API pública.", "app.containers.Admin.tools.apiTokens.createTokenError": "Forneça um nome para o sua ficha", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "<PERSON><PERSON><PERSON> <PERSON>", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>Importante!</b> Você só pode copiar este {secret} uma vez. Se você fechar esta janela, não poderá vê-la novamente.", "app.containers.Admin.tools.apiTokens.createTokenName": "Nome", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Dê um nome à sua ficha", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "A sua ficha foi criado", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "Cópia {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Copiado!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Criar uma nova ficha", "app.containers.Admin.tools.apiTokens.createdAt": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.delete": "Apagar ficha", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Tem a certeza de que pretende apagar esta ficha?", "app.containers.Admin.tools.apiTokens.description": "Gerencie suas fichas de API para nossa API pública. Para obter mais informações, consulte nosso {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Última utilização", "app.containers.Admin.tools.apiTokens.link": "Documentação da API", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Nome", "app.containers.Admin.tools.apiTokens.noTokens": "Você ainda não tem fichas.", "app.containers.Admin.tools.apiTokens.title": "Fichas de API pública", "app.containers.Admin.tools.esriDisabled": "A integração com o Esri é um recurso complementar. Entre em contato com o seu gerente do GovSuccess se você quiser obter mais informações sobre isso.", "app.containers.Admin.tools.esriIntegration2": "Integração com o Esri", "app.containers.Admin.tools.esriIntegrationButton": "Habilitar <PERSON>", "app.containers.Admin.tools.esriIntegrationDescription3": "Conecte sua conta Esri e importe dados do ArcGIS Online diretamente para seus projetos de mapeamento.", "app.containers.Admin.tools.esriIntegrationImageAlt": "<PERSON><PERSON><PERSON><PERSON>sri", "app.containers.Admin.tools.esriKeyInputDescription": "Adicione sua chave Esri API para permitir a importação de suas camadas de mapa do ArcGIS Online nas guias de mapa em projetos.", "app.containers.Admin.tools.esriKeyInputLabel": "<PERSON><PERSON> da <PERSON>ri", "app.containers.Admin.tools.esriKeyInputPlaceholder": "<PERSON> a chave da API aqui", "app.containers.Admin.tools.esriMaps": "Mapas Esri", "app.containers.Admin.tools.esriSaveButtonError": "Ocorreu um erro ao salvar sua chave, tente novamente.", "app.containers.Admin.tools.esriSaveButtonSuccess": "Chave de API salva", "app.containers.Admin.tools.esriSaveButtonText": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.learnMore": "<PERSON><PERSON> mais", "app.containers.Admin.tools.managePublicAPIKeys": "Gerenciar chaves de API", "app.containers.Admin.tools.manageWidget": "Gerenciar ferramenta", "app.containers.Admin.tools.manageWorkshops": "Gerenciar seminários", "app.containers.Admin.tools.powerBIAPIImage": "Imagem do Power BI", "app.containers.Admin.tools.powerBIDescription": "Use nossos modelos plug & play do Power BI para acessar os dados do Go Vocal em seu Microsoft Power BI Workspace.", "app.containers.Admin.tools.powerBIDisabled1": "O Power BI não faz parte de sua licença. Entre em contato com o seu gerente do GovSuccess se você quiser obter mais informações sobre isso.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Baixar modelos", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "Se você pretende usar seus dados do Go Vocal em um fluxo de dados do Power BI, esse modelo permitirá que você configure um novo fluxo de dados que se conecte aos seus dados do Go Vocal. Depois de fazer o download desse modelo, você deve primeiro encontrar e substituir as seguintes cadeias de caracteres ##CLIENT_ID## e ##CLIENT_SECRET## no modelo com suas credenciais de API pública antes de fazer o upload para o PowerBI.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Baixar modelo de fluxo de dados", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Modelo de fluxo de dados", "app.containers.Admin.tools.powerBITemplates.intro": "Observação: para usar qualquer um desses modelos do Power BI, você deve primeiro acessar {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "criar um conjunto de credenciais para nossa API pública", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "Esse modelo criará um relatório do Power BI com base nos dados do Go Vocal que você possui. Você configurará todas as conexões de dados com a plataforma Go Vocal, criará o modelo de dados e alguns painéis padrão. Ao abrir o modelo no Power BI, você será solicitado a inserir suas credenciais de API pública. Você também precisará inserir o URL base da sua plataforma, que é: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Baixar modelo de relatório", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Modelo de relatório", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Mais detalhes sobre o uso dos dados do Go Vocal no Power BI podem ser encontrados em nosso site {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "artigo de apoio", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Modelos do Power BI", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "<PERSON><PERSON><PERSON><PERSON> as credenciais para criar integrações personalizadas em nossa API pública.", "app.containers.Admin.tools.publicAPIDisabled1": "A API pública não faz parte de sua licença atual. Entre em contato com o seu gerente do GovSuccess se você quiser obter mais informações sobre isso.", "app.containers.Admin.tools.publicAPIImage": "Imagem da API pública", "app.containers.Admin.tools.publicAPITitle": "Acesso público à API", "app.containers.Admin.tools.toolsLabel": "Ferramentas", "app.containers.Admin.tools.widgetDescription": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.Admin.tools.widgetImage": "<PERSON><PERSON> da ferramenta", "app.containers.Admin.tools.widgetTitle": "Ferramenta", "app.containers.Admin.tools.workshopsDescription": "Faça videoconferências ao vivo, facilite discussões e debates em grupo simultâneos. Reúna opiniões, vote e chegue a um consenso, assim como faria presencialmente.", "app.containers.Admin.tools.workshopsImage": "imagem dos seminários", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Seminários de deliberação online", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "total na plataforma", "app.containers.AdminPage.DashboardPage._blank": "desconhecido", "app.containers.AdminPage.DashboardPage.allGroups": "Todos os grupos", "app.containers.AdminPage.DashboardPage.allProjects": "Todos os projetos", "app.containers.AdminPage.DashboardPage.allTime": "Todas as datas", "app.containers.AdminPage.DashboardPage.comments": "Comentários", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Comentários", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "É necessário um conjunto de dados de base para medir a representação dos usuários da plataforma.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Em breve", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Estamos atualmentetraabalhando no painel de instrumentos {fieldName}, que estará disponível em breve", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# item é} other {# itens são}} escondidos neste gráfico. Mude para {tableViewLink} para ver todos os dados.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} para registo de usuários", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} a partir de {total} usuários incluídos ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Ver {numberOfHiddenItems} mais", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Campo opcional", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Por favor, forneça uma base de dados.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Relatório de representatividade:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Esta pontuação reflete a precisão com que os dados dos usuários da plataforma refletem a população total. Saiba mais sobre {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Campo requerido", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Submeter base de dados", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "visão de tabela", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "População total", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Usuários", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Adionar um grupo por idade", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} e mais", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Grupos etários", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "O(s) grupo(s) etário(s) de {upperBound} ou mais não está(ão) incluído(s).", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Grupo etário {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Grupos etários", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "e mais", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Aplicar agrupamento de exemplos", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "<PERSON><PERSON> tudo", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "De", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Defina grupos etários para se alinharem com o seu conjunto de dados base.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Gama", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "Para:", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Editar grupos etários", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Este item não será calculado.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "<PERSON>er menos", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Ver {numberOfHiddenItems} mais...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Mês base (opcional)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Grupos etários (Ano de nascimento)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Em breve", "app.containers.AdminPage.DashboardPage.components.Field.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.default": "Predefinição", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Por favor, preencha to<PERSON> as opçõ<PERSON> ativa<PERSON>, ou desative as opções que pretende omitir do gráfico. <PERSON>elo menos uma opção deve ser preenchida.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Incompleto", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Número total de residentes", "app.containers.AdminPage.DashboardPage.components.Field.options": "Opções", "app.containers.AdminPage.DashboardPage.components.Field.save": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Salvo", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Por favor {setAgeGroupsLink} para começa<PERSON>, introduza os dados da base.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "definir grupos etários", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Tempo médio de resposta: {days} dias", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Quantidade média de dias para responder", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "feedback dado", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Contribuições por status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Contribuições por status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Número de entradas", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Atualizações oficiais", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Percentagem de entradas", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Tempo de resposta", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Status alterado", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Total", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Editar base de dados", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "como calculamos as pontuações de representatividade", "app.containers.AdminPage.DashboardPage.continuousType": "Sem cronograma", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Total acumulado", "app.containers.AdminPage.DashboardPage.customDateRange": "Personalizado", "app.containers.AdminPage.DashboardPage.day": "dia", "app.containers.AdminPage.DashboardPage.false": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.female": "feminino", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "5 principais entradas por reações", "app.containers.AdminPage.DashboardPage.fromTo": "de {from} a {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Painel de controle para atividades na plataforma", "app.containers.AdminPage.DashboardPage.helmetTitle": "Página do painel de administração", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Escolha o recurso para mostrar por projeto", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Escolha o recurso para mostrar por tópico", "app.containers.AdminPage.DashboardPage.inputs1": "Entradas", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Entradas por status", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Selecionar grupo de usuários", "app.containers.AdminPage.DashboardPage.male": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.month": "mês", "app.containers.AdminPage.DashboardPage.noData": "Não há dados a serem mostrados", "app.containers.AdminPage.DashboardPage.noPhase": "Nenhuma fase configurada para este projeto", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "O número de participantes que publicaram contributos, reagiram ou comentaram.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "<PERSON><PERSON> gosta", "app.containers.AdminPage.DashboardPage.numberOfLikes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Total de reacções", "app.containers.AdminPage.DashboardPage.overview.management": "Gestão", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projetos & Participação", "app.containers.AdminPage.DashboardPage.overview.showLess": "Mostrar menos ideias", "app.containers.AdminPage.DashboardPage.overview.showMore": "<PERSON><PERSON> mais", "app.containers.AdminPage.DashboardPage.participants": "Participantes", "app.containers.AdminPage.DashboardPage.participationPerProject": "Participantes por projeto", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Participantes por tópicos", "app.containers.AdminPage.DashboardPage.perPeriod": "Por {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "30 dias anteriores", "app.containers.AdminPage.DashboardPage.previous90Days": "90 dias anteriores", "app.containers.AdminPage.DashboardPage.previousWeek": "Semana anterior", "app.containers.AdminPage.DashboardPage.previousYear": "Ano anterior", "app.containers.AdminPage.DashboardPage.projectType": "Tipo de projeto: {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "Reacções", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Este conjunto de dados é necessário para calcular a representatividade dos usuários da plataforma em comparação com a população total.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Por favor, forneça uma base de dados.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Veja quão representativos são o(a) s usuário(a) s da sua plataforma. Compare os seus usuários com os dados populacionais totais, utilizando os dados de inscritos coletados durante o registo do inscrição. Saiba como calculamos as pontuações de representatividade {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Veja quão representativo são os usuários da plataforma em comparação com a população total. O tipo de representação que é possível comparar depende de quais campos de registros você habilitou para inscrição dos usuários na plataforma.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Representação comunitária", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Voltar ao painel de controle", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "No momento, nenhum dos campos de registo ativados é suportado.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Aqui você pode introduzir a base de dados da população total. Lembrando que apenas os campos de registro habilitados no {userRegistrationLink} aparecerão aqui.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Editar base de dados", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "registro de usuário", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Submeter base de dados", "app.containers.AdminPage.DashboardPage.resolutionday": "em dias", "app.containers.AdminPage.DashboardPage.resolutionmonth": "em meses", "app.containers.AdminPage.DashboardPage.resolutionweek": "em semanas", "app.containers.AdminPage.DashboardPage.selectProject": "Selecionar projeto", "app.containers.AdminPage.DashboardPage.selectedProject": "filtro de projeto atual", "app.containers.AdminPage.DashboardPage.selectedTopic": "filtro de tópico atual", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Descubra o que está acontecendo em sua plataforma.", "app.containers.AdminPage.DashboardPage.tabOverview": "Visão geral", "app.containers.AdminPage.DashboardPage.tabReports": "Projetos", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Representação", "app.containers.AdminPage.DashboardPage.tabUsers": "Usuários", "app.containers.AdminPage.DashboardPage.timelineType": "Linha do tempo", "app.containers.AdminPage.DashboardPage.titleDashboard": "painel de controle", "app.containers.AdminPage.DashboardPage.total": "Total", "app.containers.AdminPage.DashboardPage.totalForPeriod": "Este {period}", "app.containers.AdminPage.DashboardPage.true": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.unspecified": "não especificado", "app.containers.AdminPage.DashboardPage.users": "Usuários", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Usuários por idade", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Usuários por área geográfica", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Usuários por gênero", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Inscrições", "app.containers.AdminPage.DashboardPage.week": "semana", "app.containers.AdminPage.FaviconPage.favicon": "Imagem que representa a internet", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Dicas para escolher uma imagem que representa a internet (favicon) selecione uma imagem simples, pois o tamanho da imagem mostrado é muito pequeno. A imagem deve ser salva como PNG e deve ser quadrada com um fundo transparente (ou um fundo branco se necessário). Seu favicon deve ser definido apenas uma vez, pois as alterações exigirão algum suporte técnico", "app.containers.AdminPage.FaviconPage.save": "<PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Alguma coisa saiu errado, por favor tente novamente mais tarde.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Sucesso!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Suas alterações foram salvas.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Deletar", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Gestores de pasta", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Os gerentes de pastas podem editar a descrição da pasta, criar novos projetos dentro da pasta e ter direitos de gerenciamento de projetos sobre todos os projetos dentro da pasta. Eles não podem excluir projetos e não têm acesso aos projetos que não estão dentro da pasta. Você pode {projectManagementInfoCenterLink} para encontrar mais informações sobre os direitos de gerenciamento de projetos.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Nenhuma correspondência foi encontrada", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "visite nossa Central de Ajuda", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Usuários de pesquisa", "app.containers.AdminPage.FoldersEdit.addToFolder": "Adicionar a pasta", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Arquivado", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Apagar esta pasta", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Descrição", "app.containers.AdminPage.FoldersEdit.draftStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Adicionar arquivos a esta pasta", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Os arquivos não devem ser maiores que 50 Mb. Os arquivos adicionados serão mostrados na página da pasta.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Descrições", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "Não há projetos nesta pasta. Volte para o separador principal Projetos para criar e adicionar projetos.", "app.containers.AdminPage.FoldersEdit.folderName": "Nome da pasta", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Imagem de cabeça<PERSON>ho", "app.containers.AdminPage.FoldersEdit.multilocError": "Todos os campos de texto devem ser preenchidos para todos os idiomas.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Não há projetos que você possa adicionar a esta pasta.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Imagem de cartão de pasta", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Permissões", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Projetos da pasta", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Minhas configurações", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projetos adicionais a esta pasta", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Projetos que você pode adicionar a esta pasta", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Escolha se esta pasta é \"rascunho\", \"publicado\" ou \"arquivado\"", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Publicados", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Remover da past", "app.containers.AdminPage.FoldersEdit.save": "<PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Alguma coisa deu errado. Por favor, tente novamente", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Sucesso!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Suas alterações foram salvas.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Breve descrição", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "Mostrar na página inicial", "app.containers.AdminPage.FoldersEdit.statusLabel": "Status de publicação", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Explique por que os projetos estão juntos, defina uma identidade visual e compartilhe informações.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Explique por que os projetos estão juntos, defina uma identidade visual e compartilhe informações.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Todos os campos de texto devem ser preenchidos.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Criar uma nova pasta", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Minhas configurações", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Ver pasta", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Personalize a imagem e o texto do banner da página inicial.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Hero banner", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "<PERSON><PERSON> banner", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "O Inspiration Hub é um local onde você pode encontrar inspiração para seus projetos navegando por projetos em outras plataformas.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Edite os termos e condições da sua plataforma e a política de privacidade. Outras páginas, incluindo as páginas Sobre e FAQ, podem ser editadas na aba {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Políticas da plataforma", "app.containers.AdminPage.PagesEdition.privacy-policy": "Política de Privacidade", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Termos e condições", "app.containers.AdminPage.Project.confirmation.description": "Essa ação não pode ser desfeita.", "app.containers.AdminPage.Project.confirmation.no": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.confirmation.title": "Você tem certeza de que deseja redefinir todos os dados de participação?", "app.containers.AdminPage.Project.confirmation.yes": "Redefinir todos os dados de participação", "app.containers.AdminPage.Project.data.descriptionText1": "<PERSON><PERSON>, coment<PERSON><PERSON><PERSON>, votos, rea<PERSON><PERSON><PERSON>, respostas a pesquisas, respostas a enquetes, voluntários e inscritos em eventos. No caso das fases de votação, essa ação limpará os votos, mas não as opções.", "app.containers.AdminPage.Project.data.title": "Limpar todos os dados de participação desse projeto", "app.containers.AdminPage.Project.resetParticipationData": "Redefinir todos os dados de participação", "app.containers.AdminPage.Project.settings.accessRights": "Direitos de acesso", "app.containers.AdminPage.Project.settings.back": "Voltar", "app.containers.AdminPage.Project.settings.data": "<PERSON><PERSON>", "app.containers.AdminPage.Project.settings.description": "Descrição", "app.containers.AdminPage.Project.settings.events": "Eventos", "app.containers.AdminPage.Project.settings.general": "G<PERSON>", "app.containers.AdminPage.Project.settings.projectTags": "Tags do projeto", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Lista de projetos na plataforma", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Painel de projetos", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Crie novos projetos ou gerencie os existentes.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projetos", "app.containers.AdminPage.ProjectDashboard.published": "Publicados", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Centro", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "Largura total", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "Restante", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "Alinhamento de botões", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "Certo", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Botão de texto", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Insira o texto para o botão", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Tipo de botão", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "Botão URL", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Insira um URL para o botão", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Disposição da coluna", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Descrição", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Descrição da página inicial", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Esta descrição será mostrada na visão geral do projeto na página inicial", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Esta descrição será mostrada na página de informações do projeto.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Alguma coisa deu errado. Por favor, tente novamente", "app.containers.AdminPage.ProjectDescription.preview": "Vista prévia", "app.containers.AdminPage.ProjectDescription.save": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Suas alterações foram salvas.", "app.containers.AdminPage.ProjectDescription.saved": "Salvo!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Fale aos seus usuários sobre o projeto e os convença a participar.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Descrição projeto", "app.containers.AdminPage.ProjectDescription.whiteSpace": "Espaço em branco", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "<PERSON><PERSON><PERSON> borda", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "Altura vertical", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "Grande", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Média", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Pequeno", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Latitude do ponto central do mapa predefinida. Aceita um valor entre -90 e 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "A longitude padrão do ponto central do mapa. Aceita um valor entre -180 e 180.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "<PERSON><PERSON> camada", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "<PERSON><PERSON> camada", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "<PERSON>go deu errado, tente novamente mais tarde", "app.containers.AdminPage.ProjectEdit.MapTab.here": "aqui", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Importar arquivo GeoJSON", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Latitude predefinida", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "<PERSON><PERSON> <PERSON> camada", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Esta cor é aplicada a todas as características dentro do mapa. Os tamanhos dos marcadores, larguras de linha e opacidade de preenchimento são fixados por padrão.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Ícone do marcador", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Opcionalmente, selecione um ícone que é exibido nos marcadores. Clique em {url} para ver a lista de ícones que você pode selecionar.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "<PERSON>me da camada", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Este nome de camada é mostrado na legenda do mapa", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Dica de camada", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Este texto é exibido como uma dica de ferramenta ao passar o mouse sobre os recursos da camada no mapa", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Camadas", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "Atualmente suportamos arquivos GeoJSON. Leia o {supportArticle} para dicas sobre como converter e editar as camadas de mapas.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Longitude predefinida", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Centro do mapa & zoom padrão", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "O ponto central e o nível de zoom padrão do mapa. Ajuste manualmente os valores abaixo ou clique no botão {button} no canto inferior esquerdo do mapa para salvar o ponto central e o nível de zoom atuais do mapa como valores padrão.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Personalize a visualização do mapa, incluindo o carregamento e o estilo das camadas e a definição do centro do mapa e do nível de zoom.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Configuração de mapa", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "A configuração do mapa é atualmente compartilhada entre as fases, você não pode criar configurações de mapa diferentes por fase.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Remover camada", "app.containers.AdminPage.ProjectEdit.MapTab.save": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Salvar zoom", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "artigo de apoio", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Camada sem nome", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Nível de zoom predefinido", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Defina o padrão para o zoom do mapa. Escolha um valor entre 0 e 20, em que 0 é totalmente ampliado (o mundo inteiro é visível) e 20 é totalmente focado (blocos e edifícios são visíveis)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Tornar anônimos todos os dados de usuário", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "<PERSON><PERSON> as entradas do questionário dos usuários se tornarão anônimas antes de serem registradas", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Os usuários ainda precisarão cumprir os requisitos de participação na guia de acesso 'Direitos de acesso'. Os dados do perfil do usuário não estarão disponíveis na exportação dos dados da pesquisa.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "Se você ativar essa opção, os campos de registro do usuário serão mostrados como a última página do questionário, em vez de como parte do processo de inscrição.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Campos demográficos no formulário de pesquisa", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Mostrar campos demográficos na pesquisa?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "Leia mais sobre como o compartilhamento automático funciona neste artigo.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Compartilhamento automático de resultados", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Os resultados da votação são compartilhados na plataforma e por e-mail para os participantes quando a fase termina. Is<PERSON> garante a transparência por padrão.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Compartilhamento de resultados", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Adicione uma opção de resposta", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Adicionar uma pergunta de pesquisa", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Cancelado", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Cancelado", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Deletar", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Deletar", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Editar opção de resposta", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Salvar opção de resposta", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Editar opção de resposta", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Exportar os resultados da sondagem", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "O número máximo de escolhas é maior que o número de opções", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Multipla escolha", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "Sem opções", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "A sondagem não será respondida como está, todas as perguntas devem ter opções de resposta", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Apenas uma opção", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Os entrevistados têm apenas uma escolha", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Gerenciar opções de resposta para: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "exportar_pesquisa", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Aqui você pode criar perguntas de pesquisa, definir as opções de resposta para os participantes escolherem para cada questão, decidir se quer que os participantes possam selecionar apenas uma opção de resposta (opção única) ou várias opções de resposta (opção múltipla) e exportar os resultados da pesquisa. É possível criar várias perguntas de pesquisa dentro de uma pesquisa.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Escolha única", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Configurações e resultados das sondagens", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Importação", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "Dê feedback, atribua tópicos ou copie entradas para a próxima fase do projeto.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "<PERSON><PERSON><PERSON><PERSON> prop<PERSON>, dê <PERSON> e atribua tópicos.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Gerenciamento de Informações", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "O compartilhamento de resultados está desativado.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Os resultados da votação não serão compartilhados no final da fase, a menos que você os modifique na configuração da fase.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Esses resultados serão compartilhados automaticamente quando a fase terminar. Modifique a data de término dessa fase para alterar quando os resultados serão compartilhados.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Exporte os resultados da pesquisa (.xslx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Resul<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "<PERSON><PERSON>, você pode baixar os resultados da(s) pesquisa(s) Typeform como um arquivo Excel.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "Formulário de pesquisa", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Resultados da pesquisa", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Pesquisa", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Consulte as respostas da pesquisa", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Adicionar causa", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Você tem certeza?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Descrição", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Use este espaço para explicar aos voluntários o que é exigido e o sobre o que eles podem esperar.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Não foi possível salvar porque o formulário contém erros", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Imagem", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Deletar", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Uma causa é uma ação ou atividade para a qual os cidadãos podem se voluntariar.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Editar causa", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Acrescentar uma descrição", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Acrescentar um título", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Exportar voluntários", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Uma causa é uma ação ou atividade para a qual os cidadãos podem se voluntariar.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "Nova causa", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "<PERSON><PERSON>, você pode definir as causas para as quais as pessoas podem se voluntariar e fazer o download dos voluntários.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Voluntariado", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {nenhum voluntá<PERSON>} one {# voluntário} other {# voluntários}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "alocação de orçamento", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Atribua um orçamento às opções e peça aos participantes que selecionem as suas opções preferidas que se enquadrem no orçamento total.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Alocação de orçamento", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Permitir que os usuários comentem pode influenciar o processo de votação.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Visualização padrão de opções", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Ações para usuários", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Selecione as ações adicionais que os usuários podem realizar.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Montante fixo", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "Se for deixada em branco, a predefinição será \"votar\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "Saiba mais sobre quando utilizar <b> {voteTypeDescription} </b> no nosso {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Número máximo de votos por opção", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Número máximo de votos", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Você pode limitar o número total de votos que um usuário pode dar (com um máximo de um voto por opção).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "votos múltiplos por opção", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Os utilizadores recebem uma quantidade de fichas para distribuir entre as opções", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Votos múltiplos por opção", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Número de votos por utilizador", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Síntese da análise de opções", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Opções para votar", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "Ponto", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "voto único por opção", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Os utilizadores podem optar por aprovar qualquer uma das opções", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Um voto por opção", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Token", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Como se deve chamar uma votação?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "Por exemplo, fi<PERSON>s, pontos, créditos de carbono...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "Por exemplo, ficha, ponto, crédito de carbono...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "Votação", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Cada método de votação tem pré-configurações diferentes", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "Método de votação", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "O método de votação determina as regras de como os usuários votam", "app.containers.AdminPage.ProjectEdit.addNewInput": "Adicionar um conteúdo", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "Você pode adicionar seu projeto a uma pasta agora ou fazer isso mais tarde nas configurações do projeto", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Etiqueta dos projetos", "app.containers.AdminPage.ProjectEdit.altText": "Texto alternativo", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "Quando ativado, é impossível saber quem votou em quem. Os usuários precisam de uma conta e só podem votar uma vez.", "app.containers.AdminPage.ProjectEdit.approved": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.archived": "Arquivado", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Os projetos arquivados ainda estão visíveis, mas não permitem mais participação", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Arquivado", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Esta área não pode ser excluída porque está sendo usada para exibir projetos na(s) seguinte(s) página(s) personalizada(s). Você precisará desvincular a área da página ou excluir a página antes de poder excluir a área.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "O projeto irá aparecer em cada filtro de área.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Filtro de área", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Você pode selecionar as áreas geográficas para as quais este projeto é relevante. As áreas podem ser definidas como {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "Aqui", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Nenhuma área específica", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "O projeto não será mostrado quando se filtrar por área.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Se<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "O projeto irá aparecer em cada filtro de área selecionado.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Na lista", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "<PERSON><PERSON> discutido", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Adici<PERSON><PERSON> con<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Os envios para esta pesquisa começaram a chegar. As alterações na pesquisa podem resultar em perda de dados e dados incompletos nos arquivos exportados.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Pesquisa salva com sucesso", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "Pesquisa", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "Ver pesquisa", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Seleccione um método de votação e peça aos utilizadores que definam prioridades entre algumas opções diferentes.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Realize um exercício de votação ou priorização", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Crie um projeto a partir de um modelo", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Criar uma pesquisa externa", "app.containers.AdminPage.ProjectEdit.createInput": "Adicionar nova entrada", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Criar uma enquete na plataforma", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Organizar uma enquete sem sair da nossa plataforma.", "app.containers.AdminPage.ProjectEdit.createPoll": "Criar uma pesquisa", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Configure um questionário de múltipla escolha", "app.containers.AdminPage.ProjectEdit.createProject": "Novo projeto", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Incorpore uma pesquisa Typeform, Google Form ou Enalyzer.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Você pode definir a ordem padrão para as postagens serem exibidas na página principal do projeto", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Ordenar", "app.containers.AdminPage.ProjectEdit.departments": "Departamentos", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Descrição", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "<PERSON><PERSON> at<PERSON> ou desativará o não gostar, mas o gostar ainda estará ativado. Recomendamos deixar essa opção desativada, a menos que você esteja realizando uma análise de opções.", "app.containers.AdminPage.ProjectEdit.disabled": "Desativado", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Número de não gostos por participante", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "<PERSON><PERSON><PERSON> gostar", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Coletar feedback em um documento", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Incorpore um PDF interativo e colete comentários e feedback com o Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Desativado", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "<PERSON><PERSON>do", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Os projetos de rascunho ficam ocultos para todas as pessoas, exceto para os administradores e gerentes de projeto designados.", "app.containers.AdminPage.ProjectEdit.draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.draftStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Gerenciar", "app.containers.AdminPage.ProjectEdit.enabled": "<PERSON><PERSON>do", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Selecionar as ações participativas que os usuários podem realizar.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Eventos", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Anexos (max. 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Os anexos são mostrados na página de informações do projeto.", "app.containers.AdminPage.ProjectEdit.filesTab": "<PERSON>r<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Encontrar voluntá<PERSON>s", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Peça aos participantes para serem voluntários em diversas atividades e causas", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "Como um gerenciador de pastas, você pode escolher uma pasta ao criar o projeto, mas somente um administrador pode alterá-la posteriormente", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Texto alternativo da imagem do cartão de pasta", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Selecione uma pasta para adicionar este projeto.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "<PERSON><PERSON><PERSON><PERSON> personalizado", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Começaram a chegar os pedidos para este formulário. Alterações ao formulário podem resultar em perda de dados e dados incompletos nos ficheiros exportados.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Formulário guardado com sucesso", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "<PERSON>m da pesquisa", "app.containers.AdminPage.ProjectEdit.fromATemplate": "De um modelo", "app.containers.AdminPage.ProjectEdit.generalTab": "G<PERSON>", "app.containers.AdminPage.ProjectEdit.google_forms": "Formulário do Google", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Texto alternativo da imagem do cabeçalho", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Imagem de cabeça<PERSON>ho", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NOVO", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Forneça informações aos usuários ou use o criador de relatórios para compartilhar resultados de fases anteriores.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Compartilhe informações ou resultados", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Colete comentários e feedback", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Criar ou recolher contributos, reacções e/ou comentários. Escolha entre diferentes tipos de contributos: recolha de ideias, análise de opções, perguntas e respostas, identificação de questões e muito mais.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Quem é o responsável pelo processamento das entradas?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "<PERSON><PERSON> as novas entradas neste projeto serão atribuídas a essa pessoa. Esta pessoa pode ser alterada em {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Comente sobre as entradas", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Formulário de entrada", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "gerenciamento de Informações", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Gerenciamento de Informações", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Enviar suas contribuições", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Reagir aos inputs", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Visualização padrão", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Escolha a visualização padrão das entradas do participante: cartões em uma visualização em grade ou pinos em um mapa. Os participantes podem alternar manualmente entre as duas visualizações.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Centro de inspiração", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Incorporar o URL do Konveio", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Number of likes per participant", "app.containers.AdminPage.ProjectEdit.limited": "Limitada", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Baixar mais modelos", "app.containers.AdminPage.ProjectEdit.mapDisplay": "No mapa", "app.containers.AdminPage.ProjectEdit.mapTab": "Mapa", "app.containers.AdminPage.ProjectEdit.maxDislikes": "Máximo de não gostos", "app.containers.AdminPage.ProjectEdit.maxLikes": "M<PERSON><PERSON><PERSON> go<PERSON>", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "O número máximo de votos por opção deve ser inferior ou igual ao número total de votos", "app.containers.AdminPage.ProjectEdit.maximum": "Máximo", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Os participantes não podem exceder este orçamento ao submeterem à sua cesta.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Formulários Microsoft", "app.containers.AdminPage.ProjectEdit.minimum": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Exigir aos participantes que cumpram um orçamento mínimo para enviar a sua cesta (insira '0' se não quiser definir um mínimo).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "visite nossa Central de Ajuda", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Quem são os gerentes de projeto?", "app.containers.AdminPage.ProjectEdit.moreDetails": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "Você precisa de inspiração? Explore projetos semelhantes de outras cidades no site {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "Adicionar uma contribuição", "app.containers.AdminPage.ProjectEdit.newIdea": "Nova ideia", "app.containers.AdminPage.ProjectEdit.newInitiative": "Adicionar uma iniciativa", "app.containers.AdminPage.ProjectEdit.newIssue": "Adicionar um problema", "app.containers.AdminPage.ProjectEdit.newOption": "Adicionar uma opção", "app.containers.AdminPage.ProjectEdit.newPetition": "Adicionar uma peti<PERSON>", "app.containers.AdminPage.ProjectEdit.newProject": "Novo projeto", "app.containers.AdminPage.ProjectEdit.newProposal": "Adicionar uma proposta", "app.containers.AdminPage.ProjectEdit.newQuestion": "Adicionar uma pergunta", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "<PERSON><PERSON> recentes", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Não é um valor válido", "app.containers.AdminPage.ProjectEdit.noFolder": "Sem pasta", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "- Nenhuma pasta -", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Nenhum modelo encontrado", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Não pode estar vazio", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Forneça o máximo número de votos permitidos por usuário", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "<PERSON><PERSON> ve<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Somente os administradores podem ver", "app.containers.AdminPage.ProjectEdit.optionNo": "Não", "app.containers.AdminPage.ProjectEdit.optionYes": "Sim (selecionar pasta)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Níveis de participação", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "O que você quer fazer?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Escolha como os usuários podem participar.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "Pode especificar quem pode realizar cada acção e fazer perguntas adicionais aos participantes para recolher mais informações.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Requisitos e perguntas dos participantes", "app.containers.AdminPage.ProjectEdit.pendingReview": "Aprovação pendente", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Direitos de acesso", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "Direitos de acesso", "app.containers.AdminPage.ProjectEdit.phaseEmails": "Notificações", "app.containers.AdminPage.ProjectEdit.pollTab": "Pesquisa", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "A maioria das reacções", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Imagem do Cartão do Projeto", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Esta imagem faz parte do cartão do projeto; o cartão que resume o projeto e é mostrado na página inicial, por exemplo.\n\n    Para mais informações sobre as resoluções de imagem recomendadas, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Pasta", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Esta imagem é mostrada no topo da página do projeto.\n\n    Para mais informações sobre as resoluções de imagem recomendadas, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Texto alternativo da imagem do cartão do projeto", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Forneça uma breve descrição da imagem para usuários com deficiência visual. Isso ajuda os leitores de tela a transmitir o conteúdo da imagem.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Gestão de projectos", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Os gerentes de projeto podem editar projetos, gerenciar entradas e enviar e-mail aos participantes. Você pode acessar {moderationInfoCenterLink} para encontrar mais informações sobre os direitos atribuídos aos gerentes de projeto.", "app.containers.AdminPage.ProjectEdit.projectName": "Nome do projeto", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Tipo do projeto", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Escolha se o projeto tem ou não um cronograma. Projetos com cronograma têm início e fim claros e podem ter diferentes fases. Projetos sem cronograma são contínuos", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "O tipo de projeto não pode ser alterado posteriormente.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "É possível definir o projecto para ser invisível para determinados utilizadores.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Visibilidade do projecto", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Você está procurando o status do projeto? Agora você pode alterá-lo a qualquer momento diretamente no cabeçalho da página do projeto.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Os projetos publicados são visíveis para todos ou para um subconjunto de grupos, se você os selecionar.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Publicados", "app.containers.AdminPage.ProjectEdit.purposes": "Objetivos", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Redefinir dados de participação", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Ocorreu um erro ao salvar seus dados. Por favor, tente novamente.", "app.containers.AdminPage.ProjectEdit.saveProject": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Sucesso!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Seu formulário foi salvo!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Pesquise os modelos", "app.containers.AdminPage.ProjectEdit.selectGroups": "Selecionar grupo (s)", "app.containers.AdminPage.ProjectEdit.setup": "Configuração", "app.containers.AdminPage.ProjectEdit.shareInformation": "Compartilhar informação", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Configure e personalize seu projeto.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "visite o nosso centro de apoio", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Adici<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> pesquisa", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}({choiceCount, plural, no {# escolhas} one {# escolha} other {# escolhas}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON>m, eu quero sair", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Os envios para esta pesquisa começaram a chegar. Alterações na pesquisa podem resultar em perda de dados e dados incompletos nos arquivos exportados.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "Carregamento de arquivos", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "Voltar", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Importação", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Importação", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "Os resumos de IA para perguntas de resposta curta, resposta longa e perguntas de acompanhamento de escala de sentimento podem ser acessados na guia IA na barra lateral esquerda.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Escala linear", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "<PERSON>sp<PERSON><PERSON> longa", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Escolha múl<PERSON> - escolha muitos", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Escolha da imagem - escolha muitas", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "Novo envio", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Ainda sem respostas à pesquisa", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Aberto a respostas", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Aberto para respostas", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Opcional", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "Se nenhuma lógica for adicionada, o questionário seguirá seu fluxo normal. Se tanto a página quanto as perguntas tiverem lógica, a lógica da pergunta terá precedência. Certifique-se de que isso esteja alinhado com o fluxo pretendido da pesquisa. Para obter mais informações, visite {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "Localização", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Tem a certeza de que quer sair?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "As suas alterações actuais não serão guardadas.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Classificação", "app.containers.AdminPage.ProjectEdit.survey.rating": "Classificação", "app.containers.AdminPage.ProjectEdit.survey.required2": "Obrigatório", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {respostas} one {resposta} other {respostas}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# respostas} one {# resposta} other {# respostas}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Escolha múl<PERSON> - escolha uma", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Escala linear de sentimentos", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Carregamento de shapefile do Esri", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Pesquisa salva com sucesso", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Pesquisa", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Respostas da pesquisa", "app.containers.AdminPage.ProjectEdit.survey.text2": "Resposta curta", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Total {count} respostas", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "Ver pesquisa", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "<PERSON>er", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "URL de incorporação", "app.containers.AdminPage.ProjectEdit.surveyService": "Serviço", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Você pode encontrar mais informações sobre como incorporar uma pesquisa {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "Aqui", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Esta área não pode ser excluída porque está sendo usada para exibir projetos na(s) seguinte(s) página(s) personalizada(s). Você precisará desvincular a área da página ou excluir a página antes de poder excluir a área.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Configurações gerais do projeto", "app.containers.AdminPage.ProjectEdit.titleLabel": "Nome do projeto", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Escolha um título curto, envolvente e claro. Ele será mostrado na visão geral da página inicial do projeto.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Tópicos", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Selecione {topicsCopy} para este projeto. Os usuários podem utilizá-los para filtrar projetos por.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Orçamento total", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Tendência", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Não atribuído", "app.containers.AdminPage.ProjectEdit.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Use este modelo", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "Visão do projeto", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Voluntariado", "app.containers.AdminPage.ProjectEdit.voteTermError": "Os termos de votação devem ser especificados para todas as localidades", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# grupos} one {# grupo} other {# grupos}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Adicione um evento", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Informações adicionais", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Endereço 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Endereço do local do evento", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Endereço 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "Por exemplo, Apt, Suite, <PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Informações adicionais de endereço que podem ajudar a identificar o local, como nome do edifício, número do andar, etc.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "Ver o artigo de apoio", "app.containers.AdminPage.ProjectEvents.customButtonLink": "Ligação externa", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Adicionar uma ligação a um URL externo (por exemplo, serviço de eventos ou sítio Web de emissão de bilhetes). A definição desta opção substitui o comportamento predefinido do botão de participação.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Texto personalizado do botão", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Defina o texto do botão como um valor diferente de \"Registrar\" quando um URL externo for definido.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Iniciar", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "Finalizar", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Deletar", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Tem certeza que deseja excluir este evento? Não há como desfazer isso!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Descrição do evento", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Editar evento", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "Para enviar e-mails aos inscritos diretamente da plataforma, os administradores devem criar um grupo de usuários na guia {userTabLink} . {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "Datas do evento", "app.containers.AdminPage.ProjectEvents.eventImage": "Imagem do evento", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Texto alternativo da imagem do evento", "app.containers.AdminPage.ProjectEvents.eventLocation": "Local do evento", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Registradores de exportação", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Anexos (max. 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Os anexos são mostrados abaixo da descrição do evento.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Localização", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Número máximo de registrantes", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Criar um novo evento", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Link do evento on-line", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "Se o seu evento for online, adicione um link para ele aqui.", "app.containers.AdminPage.ProjectEvents.preview": "Pré-visualização", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Refinar a localização do mapa", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Refinar a localização no mapa", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "Você pode refinar onde o marcador de local do evento é mostrado clicando no mapa abaixo.", "app.containers.AdminPage.ProjectEvents.register": "Registro", "app.containers.AdminPage.ProjectEvents.registerButton": "Botão Registrar", "app.containers.AdminPage.ProjectEvents.registrant": "registrador", "app.containers.AdminPage.ProjectEvents.registrants": "registrantes", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Limite de registro", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "Não foi possível salvar suas alterações, tente novamente.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Sucesso!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Suas alterações foram salvas.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "Procurar um local", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Vincule os próximos eventos a esses projetos e mostre-os na página de eventos do projeto", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Título e data", "app.containers.AdminPage.ProjectEvents.titleEvents": "Eventos de projeto", "app.containers.AdminPage.ProjectEvents.titleLabel": "Nome do projeto", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Vincular o botão a um URL externo", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "<PERSON>r padr<PERSON>, o botão de registro de evento na plataforma será exibido, permitindo que os usuários se registrem em um evento. Em vez disso, você pode alterar isso para vincular a um URL externo.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Limitar o número de inscritos no evento", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Defina um número máximo de inscritos no evento. Se o limite for atingido, nenhum outro registro será aceito.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/utilizadores", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Utilizadores", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "Adicione arquivos ao seu projeto", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Anexe arquivos dessa lista ao seu projeto, fases e eventos.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Adicionar arquivos como contexto ao Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Adicione arquivos ao seu projeto de Sensemaking para fornecer contexto e percepções.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Em breve", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Sincronize pesquisas, faça upload de entrevistas e deixe a IA conectar os pontos em seus dados.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Fazer upload de qualquer arquivo", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Use a IA para analisar arquivos", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Processar transcrições, etc.", "app.containers.AdminPage.ProjectFiles.addFiles": "Adicionar <PERSON>", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "Insights com tecnologia de IA", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "<PERSON><PERSON><PERSON> os arquivos carregados para revelar os principais tópicos.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Permita a análise avançada desses arquivos usando o processamento de IA.", "app.containers.AdminPage.ProjectFiles.askButton": "Pergun<PERSON>", "app.containers.AdminPage.ProjectFiles.categoryLabel": "Categoria", "app.containers.AdminPage.ProjectFiles.chooseFiles": "Selecionar arquivos", "app.containers.AdminPage.ProjectFiles.close": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Confirmar e fazer upload", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Você tem certeza de que deseja excluir esse arquivo?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "Não foi possível carregar o arquivo markdown.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "Não foi possível carregar a visualização do CSV.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "Você verá no máximo 50 linhas nas visualizações de CSV.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "O arquivo CSV é muito grande para ser visualizado.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "Excluir arquivo", "app.containers.AdminPage.ProjectFiles.description": "Descrição", "app.containers.AdminPage.ProjectFiles.done": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.downloadFile": "Baixar arquivo", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Baixar o arquivo completo", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Arraste e solte quaisquer arquivos aqui ou", "app.containers.AdminPage.ProjectFiles.editFile": "Editar arquivo", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Descrição", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "O nome do arquivo não pode conter um ponto.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "Nome do arquivo", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "O nome do arquivo é obrigatório.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Baixar arquivo", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Prévia", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "Esse arquivo não será carregado, pois excede o limite máximo de 50 MB.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "Todos os arquivos foram carregados com sucesso", "app.containers.AdminPage.ProjectFiles.generatingPreview": "Geração de visualização...", "app.containers.AdminPage.ProjectFiles.info_sheet": "Informações", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "Por exemplo, WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Entrevistas em áudio, gravações da prefeitura", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "Por exemplo, PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "Relat<PERSON><PERSON>s, documentos informativos", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "Por exemplo, PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "Imagens", "app.containers.AdminPage.ProjectFiles.interview": "Entrevista", "app.containers.AdminPage.ProjectFiles.maxFilesError": "Você só pode carregar um máximo de {maxFiles} arquivos por vez.", "app.containers.AdminPage.ProjectFiles.meeting": "Reunião", "app.containers.AdminPage.ProjectFiles.noFilesFound": "Nenhum arquivo foi encontrado.", "app.containers.AdminPage.ProjectFiles.other": "Outros", "app.containers.AdminPage.ProjectFiles.policy": "Política", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "A visualização ainda não é suportada para esse tipo de arquivo.", "app.containers.AdminPage.ProjectFiles.report": "Relat<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.retryUpload": "Repetir o upload", "app.containers.AdminPage.ProjectFiles.save": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "Arquivo atualizado com sucesso.", "app.containers.AdminPage.ProjectFiles.searchFiles": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.selectFileType": "Tipo de arquivo", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Plano estratégico", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "Você só pode carregar um máximo de {maxFiles} arquivos por vez.", "app.containers.AdminPage.ProjectFiles.unknown": "Desconhecido", "app.containers.AdminPage.ProjectFiles.upload": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# arquivo} other {# arquivos}} carregado com sucesso, {numberOfErrors, plural, one {# erro} other {# erros}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "<PERSON>ibir arquivo", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "<PERSON><PERSON><PERSON> todos os campos", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Descrição do campo", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Editar formulário de entrada", "app.containers.AdminPage.ProjectIdeaForm.enabled": "<PERSON><PERSON>do", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Inclua este campo.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "<PERSON>go deu errado, tente novamente mais tarde", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Expandir todos os campos", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Formulário de entrada", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Especificar que informação deve ser fornecida, acrescentar pequenas descrições ou instruções para orientar as respostas dos participantes e especificar se cada campo é opcional ou obrigatório.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Especifique quais informações devem ser fornecidas, adicione descrições curtas ou instruções para orientar as respostas dos participantes e especifique se cada campo é opcional ou obrigatório", "app.containers.AdminPage.ProjectIdeaForm.required": "Campo requerido", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Este campo deve ser preenchido", "app.containers.AdminPage.ProjectIdeaForm.save": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Suas alterações foram salvas com sucesso.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Salvo!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "<PERSON>er formul<PERSON>", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "E-mails automáticos", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Você pode configurar e-mails disparados em um nível de fase", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Datas", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "Responda à pesquisa", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Pesquisa", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Tem certeza de que deseja excluir esta fase?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Descrição da fase", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "<PERSON><PERSON> fase", "app.containers.AdminPage.ProjectTimeline.endDate": "Data final", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Data final", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Anexos (max. 50MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Criar uma nova fase", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "Essa fase não tem uma data final predefinida.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "O compartilhamento de resultados de alguns métodos (como resultados de votação) não será acionado até que uma data final seja selecionada.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "Assim que você adicionar uma fase após essa, ele adicionará uma data final a essa fase.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "Não selecionar uma data final para isso implica que:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Prévia", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Ocorreu um erro ao enviar o formulário, por favor tente novamente.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Salvo!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Suas alterações foram salvas com sucesso.", "app.containers.AdminPage.ProjectTimeline.startDate": "Data de início", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Data de início", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "Botão", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "<PERSON><PERSON><PERSON><PERSON> da pesquisa", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Nome da fase", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Carregar anexos", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartição", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologia (filtro da página inicial)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Como devem ser chamadas as etiquetas no filtro da primeira página? Por exemplo, etiquetas, categorias, departamentos etc.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "As etiquetas podem ser configuradas {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "aqui", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Nome de uma localidade/bairro/zona (singular)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tópico", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Termo para múltiplas etiquetas (plural)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "etiquetas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Adicionar campo", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Adicionar um novo campo de registro", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Adicionar <PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Formato de resposta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Fornecer um formato de resposta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Opção de resposta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Providencie uma opção de resposta em todos os idiomas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Salvar opção de resposta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Opção de resposta salva com sucesso", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Escolha da resposta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Campos", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Arrastar e largar os campos para determinar a ordem em que aparecem no formulário de inscrição.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Campo padrão", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Deletar", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Texto opcional mostrado sob o nome do campo no formulário de inscrição.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Answer choices for place of residence can be set in the {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Editar opção de resposta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Descrição", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Nome do campo", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Por favor, forneça um nome do campo para todos os idiomas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Configurações de campo", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Sim-não (caixa de seleção)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Data", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "<PERSON>sp<PERSON><PERSON> longa", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Múltipla escolha (múltipla seleção)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "<PERSON>or numérico", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Múltipla escolha (selecione uma)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Resposta curta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "<PERSON><PERSON><PERSON> de áreas geográficas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Campo oculto", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "A resposta obrigatória a este campo é necessária?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Campos personalizados", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Adicionar opção de resposta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Cancelado", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Deletar", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Tem a certeza de que pretende eliminar esta opção de resposta à pergunta de registo? Todos os registos que utilizadores específicos responderam com esta opção serão permanentemente eliminados. Esta acção não pode ser anulada.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Tem a certeza de que pretende apagar esta pergunta de registo? <PERSON><PERSON> as respostas que os utilizadores tenham dado a esta pergunta serão permanentemente eliminadas e deixarão de ser colocadas em projectos ou propostas. Esta acção não pode ser anulada.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Campo requerido", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "<PERSON><PERSON> campo", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Página salva com sucesso", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "<PERSON><PERSON> colu<PERSON>", "app.containers.AdminPage.SettingsPage.addAreaButton": "Adicione uma área geográfica", "app.containers.AdminPage.SettingsPage.addTopicButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Animal - por exemplo, gato elefante", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Usuário - por exemplo, Usuário 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "Selecione quais administradores receberão notificações para aprovar projetos. <PERSON><PERSON>, os gerentes de pasta são aprovadores de todos os projetos em suas pastas.", "app.containers.AdminPage.SettingsPage.approvalSave": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.approvalTitle": "Configurações de aprovação do projeto", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Tem certeza de que deseja excluir esta área?", "app.containers.AdminPage.SettingsPage.areaTerm": "Término de uma localidade/bairro/zona (singular)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "Localidade/bairro/zona", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Deletar", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areasTerm": "Término para múltiplas localidades/bairros/zonas (plural)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "Localidade/bairro/zona", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Seleccionar pelo menos uma língua.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatar<PERSON>", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "<PERSON><PERSON><PERSON> avat<PERSON>", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Mostrar fotografias de perfil dos participantes e número deles aos visitantes não registados", "app.containers.AdminPage.SettingsPage.bannerHeader": "Cabeçalho do texto", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Texto de cabeçalho para visitantes não registados", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Texto do subtítulo para visitantes não registrados", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Texto do subtítulo", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Texto do banner", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Mostrar pré-visualização para", "app.containers.AdminPage.SettingsPage.brandingDescription": "Adicione o logotipo e defina as cores da plataforma.", "app.containers.AdminPage.SettingsPage.brandingTitle": "<PERSON><PERSON> plataforma", "app.containers.AdminPage.SettingsPage.cancel": "Cancelado", "app.containers.AdminPage.SettingsPage.chooseLayout": "Layout", "app.containers.AdminPage.SettingsPage.color_primary": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.color_secondary": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.color_text": "Cor do texto", "app.containers.AdminPage.SettingsPage.colorsTitle": "Cores", "app.containers.AdminPage.SettingsPage.confirmHeader": "Tem certeza que deseja excluir este tópico?", "app.containers.AdminPage.SettingsPage.contentModeration": "Moderação de conteúdo", "app.containers.AdminPage.SettingsPage.ctaHeader": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Customizar o cabeçalho de página | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Personalizado", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Botão de texto", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Link para botão", "app.containers.AdminPage.SettingsPage.defaultTopic": "Tópico <PERSON>rão", "app.containers.AdminPage.SettingsPage.delete": "Deletar", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Deletar", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Isso excluirá o tópico, incluindo todas as entradas existentes. Esta mudança será aplicada em todos os projetos.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Os tópicos podem ser adicionados para ajudar a categorizar as entradas. Aqui você pode adicionar e excluir tópicos que gostaria de usar em sua plataforma. Você pode adicionar os tópicos a projetos específicos no {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Ambiente de trabalho", "app.containers.AdminPage.SettingsPage.editFormTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.fieldDescription": "Descrição da área", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Esta descrição é apenas para colaboração interna para os administradores e não é mostrada aos usuários", "app.containers.AdminPage.SettingsPage.fieldTitle": "Nome da área", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Por favor, forneça o conteúdo para todas as línguas", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "O nome que você escolher para cada área ficará visível para os cidadãos durante a inscrição e ao filtrar projetos", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Salvar etiqueta", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Nome dos tópicos", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Por favor, forneça o nome da etiqueta para todas as línguas", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "O nome que você escolher para cada tópico ficará visível para os usuários da plataforma.", "app.containers.AdminPage.SettingsPage.fixedRatio": "Relação fixa", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Este tipo de banner funciona melhor com imagens que não devem ser cortadas, tais como imagens com texto, um logotipo ou elementos específicos que são cruciais para os seus cidadãos. Este banner é substituído por uma caixa sólida na cor principal, quando os usuários estão inscritos. Pode definir esta cor nas configurações gerais. Mais informações sobre a utilização recomendada da imagem podem ser encontradas no nosso {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "base de conhecimentos", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Banner de largura total", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Esta faixa estende-se ao longo de toda a largura para um grande efeito visual. A imagem tentará cobrir o máximo de espaço possível, fazendo com que nem sempre seja visível em todo o momento. Pode combinar este banner com uma sobreposição de qualquer cor. Mais informação sobre a utilização recomendada da imagem pode ser encontrada no nosso {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "base de conhecimentos", "app.containers.AdminPage.SettingsPage.header": "Banner da página inicial", "app.containers.AdminPage.SettingsPage.headerDescription": "Personalize a imagem e o texto do banner da página inicial.", "app.containers.AdminPage.SettingsPage.header_bg": "Imagem do banner", "app.containers.AdminPage.SettingsPage.helmetDescription": "Página de configurações de administrador", "app.containers.AdminPage.SettingsPage.helmetTitle": "Página de configurações de administrador", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Adicione o seu próprio conteúdo à seção personalizável na parte inferior da página inicial.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Cabeçalho de página | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Cor de sobreposição do cabeçalho", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Opacidade de sobreposição de imagem", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Detectar conteúdo inade<PERSON>", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Detecção automática de conteúdo inadequado postado na plataforma.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "Enquanto esse recurso estiver ativado, as entradas, propostas e comentários publicados pelos participantes serão automaticamente revisados. As postagens sinalizadas como potencialmente contendo conteúdo inapropriado não serão bloqueadas, mas serão destacadas para revisão na página {linkToActivityPage} .", "app.containers.AdminPage.SettingsPage.languages": "Idiomas", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Você pode selecionar vários idiomas para ofereceraos usuários da plataforma. Você precisará criar conteúdos para cada idioma selecionado", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Atividade", "app.containers.AdminPage.SettingsPage.logo": "Logotipo", "app.containers.AdminPage.SettingsPage.noHeader": "Faça upload de uma imagem de cabeçalho", "app.containers.AdminPage.SettingsPage.no_button": "<PERSON><PERSON><PERSON> bot<PERSON>", "app.containers.AdminPage.SettingsPage.organizationName": "Nome da cidade ou organização", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Fornecer o nome de uma organização ou cidade para todas as línguas.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Habilitar sobreposição", "app.containers.AdminPage.SettingsPage.phone": "Telefone", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Configuração da plataforma", "app.containers.AdminPage.SettingsPage.population": "População", "app.containers.AdminPage.SettingsPage.populationMinError": "A população deve ser um número positivo.", "app.containers.AdminPage.SettingsPage.populationTooltip": "O número total de habitantes em seu território. Isso é usado para calcular a taxa de participação. Deixe em branco se não for aplicável.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Bloqueador de palavras impróprias", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Bloco de entrada, propostas e comentários contendo palavras impróprias mais comumente relatadas", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Este texto é apresentado na página inicial acima dos projetos.", "app.containers.AdminPage.SettingsPage.projectsSettings": "Configurações de projeto", "app.containers.AdminPage.SettingsPage.projects_header": "Cabeçalho de projetos", "app.containers.AdminPage.SettingsPage.registrationFields": "Campos de registro", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Forneça uma breve descrição na parte superior do seu formulário de inscrição.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Inscrição", "app.containers.AdminPage.SettingsPage.save": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveArea": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Alguma coisa saiu errado, por favor tente novamente mais tarde.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Sucesso!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Suas alterações foram salvas.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Selecionar aprovadores", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "Se<PERSON><PERSON>ar as á<PERSON>s que serão mostradas aos utilizadores a seguir após o registo", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Selecionar os tópicos que serão mostrados aos utilizadores a seguir após o registo", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Não foi possível salvar. <PERSON><PERSON> alterar as configurações novamente.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Crie seu login\"", "app.containers.AdminPage.SettingsPage.signed_in": "Botão para visitantes registados", "app.containers.AdminPage.SettingsPage.signed_out": "Texto de cabeçalho para visitantes não registados", "app.containers.AdminPage.SettingsPage.signupFormText": "Texto de ajuda para inscrição", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Adicione uma breve descrição na parte superior do formulário de inscrição.", "app.containers.AdminPage.SettingsPage.statuses": "Status", "app.containers.AdminPage.SettingsPage.step1": "E-mail e senha", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Isto é mostrado na parte superior da primeira página do formulário de inscrição (nome, e-mail, senha).", "app.containers.AdminPage.SettingsPage.step2": "Perguntas de inscrição passo", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Isto é mostrado na parte superior da segunda página do formulário de inscrição (campos adicionais de inscrição).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "De<PERSON>a as subdivisões geográficas de seu território.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Escolha como as pessoas verão seu nome, selecione os idiomas de sua plataforma e crie um link para seu site.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "A legenda fornecida excede o limite máximo de 90 caracteres permitido", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminologia", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Configurações atualizadas com sucesso.", "app.containers.AdminPage.SettingsPage.tabAreas1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabBranding": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Status de entrada", "app.containers.AdminPage.SettingsPage.tabPolicies": "Políticas", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Aprovação do projeto", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Status da proposta", "app.containers.AdminPage.SettingsPage.tabRegistration": "Inscrição", "app.containers.AdminPage.SettingsPage.tabSettings": "G<PERSON>", "app.containers.AdminPage.SettingsPage.tabTopics2": "Tags", "app.containers.AdminPage.SettingsPage.tabWidgets": "Ferramentas", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Como as <PERSON><PERSON>s devem ser chamadas pelos usuários? Por exemplo. bairros, vilas, comunidades, etc...", "app.containers.AdminPage.SettingsPage.titleAreas": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.titleBasic": "Configurações Gerais", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "O título fornecido excede o limite máximo de 35 caracteres permitido", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Administrador do tema", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Este banner é particularmente útil com imagens que não funcionam bem com o texto do título, subtítulo ou botão. Estes itens serão empurrados por baixo do banner. Mais informação sobre a utilização recomendada da imagem pode ser encontrada no nosso {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "base de conhecimentos", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.urlError": "O URL não é válido.", "app.containers.AdminPage.SettingsPage.urlPatternError": "Introduzir um URL válido.", "app.containers.AdminPage.SettingsPage.urlTitle": "Website", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Você pode adicionar um link para seu próprio site. Este link será usado na parte inferior da página inicial.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "Escolha como os usuários sem um nome no perfil aparecerão na plataforma. Isso ocorrerá quando você definir os direitos de acesso de uma fase como \"Confirmação por e-mail\". Em todos os casos, após a participação, os usuários poderão atualizar o nome do perfil que geramos automaticamente para eles.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Exibição do nome de usuário (somente para usuários com e-mail confirmado)", "app.containers.AdminPage.SideBar.administrator": "Administrador", "app.containers.AdminPage.SideBar.communityPlatform": "plataforma da comunidade", "app.containers.AdminPage.SideBar.community_monitor": "Monitoramento da comunidade", "app.containers.AdminPage.SideBar.customerPortal": "Portal do cliente", "app.containers.AdminPage.SideBar.dashboard": "painel de controle", "app.containers.AdminPage.SideBar.emails": "E-mail", "app.containers.AdminPage.SideBar.folderManager": "Gerenciador de pastas", "app.containers.AdminPage.SideBar.groups": "Grupos", "app.containers.AdminPage.SideBar.guide": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inputManager": "Gerenciamento de Informações", "app.containers.AdminPage.SideBar.insights": "Relat<PERSON><PERSON>", "app.containers.AdminPage.SideBar.inspirationHub": "Centro de inspiração", "app.containers.AdminPage.SideBar.knowledgeBase": "Base de dados", "app.containers.AdminPage.SideBar.language": "Idioma", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Páginas & menu", "app.containers.AdminPage.SideBar.messaging": "Mensagens", "app.containers.AdminPage.SideBar.moderation": "Atividade", "app.containers.AdminPage.SideBar.notifications": "Notificações", "app.containers.AdminPage.SideBar.processing": "Em processamento", "app.containers.AdminPage.SideBar.projectManager": "Gestor de projeto", "app.containers.AdminPage.SideBar.projects": "Projetos", "app.containers.AdminPage.SideBar.settings": "Minhas configurações", "app.containers.AdminPage.SideBar.signOut": "<PERSON><PERSON>", "app.containers.AdminPage.SideBar.support": "Suporte", "app.containers.AdminPage.SideBar.toPlatform": "Para a plataforma", "app.containers.AdminPage.SideBar.tools": "Ferramentas", "app.containers.AdminPage.SideBar.user.myProfile": "<PERSON><PERSON> perfil", "app.containers.AdminPage.SideBar.users": "Usuários", "app.containers.AdminPage.SideBar.workshops": "Seminário", "app.containers.AdminPage.Topics.addTopics": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.browseTopics": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.cancel": "Cancelado", "app.containers.AdminPage.Topics.confirmHeader": "Tem certeza de que deseja excluir este tópico do projeto?", "app.containers.AdminPage.Topics.delete": "Deletar", "app.containers.AdminPage.Topics.deleteTopicLabel": "Deletar", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Este tópico já não poderá ser adicionado a novos posts neste projeto.", "app.containers.AdminPage.Topics.inputForm": "Formulário de entrada", "app.containers.AdminPage.Topics.lastTopicWarning": "É necessário pelo menos um tópico. Se você não quiser usar tópicos, eles podem ser desabilitados na guia {ideaFormLink}.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Você pode adicionar e excluir os tópicos que podem ser atribuídos às entradas neste projeto.", "app.containers.AdminPage.Topics.remove": "Remover", "app.containers.AdminPage.Topics.title": "Etiqueta dos projetos", "app.containers.AdminPage.Topics.topicManager": "Administrador do tema", "app.containers.AdminPage.Topics.topicManagerInfo": "Se desejar adicionar tópicos de projeto adicionais, você pode fazê-lo no {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Adionar um novo grupo", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Nome do grupo", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Fornecer um nome para o grupo", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Criar um grupo manual", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Que tipo de grupo você precisa?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Salvar grupo", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Criar um grupo manual", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Criar um grupo inteligente", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Saiba mais sobre grupos", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Você pode selecionar usuários manualmente e adicioná-los a este grupo.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Você pode definir as condições e os usuários que atenderem serão automaticamente adicionados a este grupo", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Grupo manual", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Grupo inteligente", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Não há pessoas neste grupo", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Vá para {allUsersLink} para adicionar manualmente alguns usuários.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Não há usuários que correspondam à sua pesquisa", "app.containers.AdminPage.Users.GroupsPanel.select": "Selecionar", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Exportar tudo", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Exportar usuários no grupo", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Exportar selecionado", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Você tem certeza?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Ocorreu um erro ao adicionar usuários aos grupos, tente novamente.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Remover do grupo", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Excluir usuários selecionados deste grupo?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Ocorreu um erro ao excluir usuários do grupo, tente novamente.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Adicionar usuários ao grupo", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.add": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.addAnswer": "<PERSON><PERSON><PERSON><PERSON> resposta", "app.containers.AdminPage.groups.permissions.addQuestion": "Adicionar perguntas demográficas", "app.containers.AdminPage.groups.permissions.answerChoices": "Opções de resposta", "app.containers.AdminPage.groups.permissions.answerFormat": "Formato de resposta", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "Deve ser apresentada pelo menos uma opção", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "Esse usuário modera a pasta que contém esse projeto. Para remover os direitos de moderador desse projeto, você pode revogar seus direitos de pasta ou mover o projeto para uma pasta diferente.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Criar uma nova pergunta", "app.containers.AdminPage.groups.permissions.createAQuestion": "<PERSON><PERSON><PERSON> uma pergunta", "app.containers.AdminPage.groups.permissions.defaultField": "Campo por defeito", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Deletar", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Deletar", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Forneça um título para todas as opções", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "Sim-não (caixa de verificação)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Data", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "<PERSON>sp<PERSON><PERSON> longa", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Escol<PERSON> (seleccionar várias)", "app.containers.AdminPage.groups.permissions.fieldType_number": "<PERSON>or numérico", "app.containers.AdminPage.groups.permissions.fieldType_select": "Escol<PERSON> m<PERSON> (seleccionar uma)", "app.containers.AdminPage.groups.permissions.fieldType_text": "Resposta curta", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "A alteração de permissões granulares não faz parte da sua licença. Entre em contato com o seu gerente do GovSuccess para saber mais sobre isso.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Tem certeza de que deseja remover este grupo do projeto?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Selecione um ou mais grupos", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {Sem membros} one {1 membro} other {{count} membros}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "<PERSON><PERSON><PERSON> o título em todas as línguas", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Você tem certeza?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Administradores de projetos não encontrados", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Nada há informações, porque não há ações que o usuário possa realizar neste projeto.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Somente os administradores podem criar uma nova pergunta.", "app.containers.AdminPage.groups.permissions.option1": "Opção 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "<PERSON><PERSON><PERSON> pen<PERSON>e", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Quem pode fazer anotações no documento?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Quem pode se inscrever para participar de um evento?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Quem pode comentar os contributos?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Quem pode comentar as propostas?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Quem pode publicar uma proposta?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Quem pode reagir aos inputs?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Quem pode apresentar contributos?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Quem pode fazer a sondagem?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Quem pode responder ao inquérito?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Quem pode ser voluntário?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Quem pode votar nas propostas?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Quem pode votar?", "app.containers.AdminPage.groups.permissions.questionDescription": "Descrição da pergunta", "app.containers.AdminPage.groups.permissions.questionTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.save": "<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "<PERSON>go deu errado, por favor, tente novamente mais tarde.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Sucesso!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "As suas alterações foram salvas.", "app.containers.AdminPage.groups.permissions.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.selectValueError": "Seleccione um tipo de resposta", "app.containers.AdminPage.new.createAProject": "Criar um projeto", "app.containers.AdminPage.new.fromScratch": "Do zero", "app.containers.AdminPage.phase.methodPicker.addOn1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "Insights com tecnologia de IA", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Ajude os participantes a mostrar as concordâncias e discordâncias, uma ideia de cada vez.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Encontre um ponto em comum", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Incorpore um PDF interativo e colete comentários e feedback com o Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Coletar feedback sobre um documento", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Incorporar uma pesquisa de terceiros", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "Pesquisa externa", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Aproveite a inteligência coletiva de seus usuários. Convide-os a enviar, discutir ideias e/ou fornecer feedback em um fórum público.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Colete informações e feedback em público", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Compartilhe informações", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Falta de insights baseados em IA na plataforma", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Falta de relatórios na plataforma e de visualização e processamento de dados", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Link com o criador de relatórios na plataforma", "app.containers.AdminPage.phase.methodPicker.logic1": "Lógica", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Ampla variedade de tipos de perguntas", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Permita que os participantes carreguem ideias com um limite de tempo e de votos.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "Propostas, petições ou iniciativas", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "Pesquisa <PERSON>", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Prepare um questionário curto, de múltipla escolha.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "Forneça informações aos usuários, visualize resultados de outras fases e crie relatórios ricos em dados.", "app.containers.AdminPage.phase.methodPicker.survey1": "Pesquisa", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Entenda as necessidades e o pensamento dos seus usuários por meio de uma ampla variedade de tipos de perguntas particulares.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Opções de pesquisa", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Criar uma pesquisa", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Peça aos usuários que se voluntariem para atividades e causas ou encontre participantes para um painel.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Recrutar participantes ou voluntários", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Selecione um método de votação e faça com que os usuários priorizem entre algumas opções diferentes.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Conduzir um exercício de votação ou priorização", "app.containers.AdminPage.projects.all.all": "Todos", "app.containers.AdminPage.projects.all.createProjectFolder": "Nova pasta", "app.containers.AdminPage.projects.all.existingProjects": "Projetos existentes", "app.containers.AdminPage.projects.all.homepageWarning1": "Use esta página para definir a ordem dos projetos no menu suspenso \"Todos os projetos\" na barra de navegação. Se você estiver usando os widgets \"Projetos e pastas publicados\" ou \"Projetos e pastas (legado)\" na sua página inicial, a ordem dos projetos nesses widgets também será determinada pela ordem que você definir aqui.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "Os projetos em que você é gerente de projeto aparecerão aqui.", "app.containers.AdminPage.projects.all.noProjects": "Nenhum projeto foi encontrado.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Somente os administradores podem criar pastas de projeto.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projetos e pastas", "app.containers.AdminPage.projects.all.publishedTab": "Publicado", "app.containers.AdminPage.projects.all.searchProjects": "Projetos de pesquisa", "app.containers.AdminPage.projects.all.yourProjects": "Seus projetos", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "Análise de IA", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Precisão: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "Pergun<PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "Em vez de resumir, você pode fazer perguntas relevantes aos seus dados. Esse recurso não está incluído em seu plano atual. Fale com seu gerente de Government Success Manager ou administrador para desbloqueá-lo.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Faça uma pergunta", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "Essa percepção inclui as seguintes perguntas:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "Excluir pergunta", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "Tem certeza de que deseja excluir esta pergunta?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Excluir resumo", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Tem certeza de que deseja excluir estes resumos?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Seus resumos de texto serão exibidos aqui, mas você ainda não tem nenhum.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "Clique no botão Resumir automaticamente acima para começar.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "entradas selecionadas", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Fazer perguntas sobre menos entradas leva a uma maior precisão. Reduza a seleção de entrada atual usando tags, pesquisa ou filtros demográficos.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "Perguntas para", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "<PERSON><PERSON><PERSON> para todas as entradas", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Avalie a qualidade deste insight", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "Restaurar filtros", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Resumir", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Resumo para", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "<PERSON><PERSON><PERSON> para todas as entradas", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "<PERSON><PERSON><PERSON> pelo seu feedback", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "A IA não consegue processar tantas entradas de uma só vez. Divida-os em grupos menores.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "Você pode resumir um máximo de 30 entradas por vez em seu plano atual. Fale com seu gerente ou administrador do GovSuccess para saber mais.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "Eu entendo", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Nossa plataforma permite explorar os temas principais, resumir os dados e examinar várias perspectivas. Se você estiver procurando respostas ou insights específicos, considere usar o recurso \"Faça uma pergunta\" para se aprofundar além do resumo.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "<PERSON><PERSON><PERSON> raro, a IA pode ocasionalmente gerar informações que não estavam explicitamente presentes no conjunto de dados original.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Alucinações:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "A IA pode enfatizar determinados temas ou ideias mais do que outros, distorcendo potencialmente a interpretação geral.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Exagero:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Nosso sistema é otimizado para lidar com 20 a 200 entradas bem definidas para obter resultados mais precisos. À medida que o volume de dados aumenta além deste intervalo, o resumo pode tornar-se mais de alto nível e generalizado. Isto não significa que a IA se torne “menos precisa”, mas sim que se concentrará em tendências e padrões mais amplos. Para obter informações mais detalhadas, recomendamos usar o recurso de marcação (automática) para segmentar conjuntos de dados maiores em subconjuntos menores e mais gerenciáveis.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Volume e precisão de dados:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "Recomendamos o uso de resumos gerados por IA como ponto de partida para a compreensão de grandes conjuntos de dados, mas não como a palavra final.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "Como trabalhar com a IA", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Adicione entradas selecionadas à tag", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "Adicionar tag", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "Esse recurso não está incluído em seu plano atual. Fale com seu gerente de Government Success Manager ou administrador para desbloqueá-lo.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "<PERSON><PERSON> as entradas", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "<PERSON><PERSON> as entradas", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "<PERSON><PERSON> as tags", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "Não, eu farei isso", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Você deseja atribuir entradas automaticamente à sua tag?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "Existem <b>métodos diferentes</b> para atribuir entradas automaticamente às tags.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Utilize <b>o botão de etiqueta automática</b> para iniciar o seu método preferido.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Clique em uma tag para atribuí-la à entrada atualmente selecionada.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "Sim, tag automática", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Tag automática", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "As tags automáticas são derivadas automaticamente pelo computador. Você pode alterá-los ou removê-los a qualquer momento.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Tag automática", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "As entradas já associadas a essas tags não serão classificadas novamente.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "A classificação é baseada exclusivamente no nome da tag. Escolha palavras-chave relevantes para obter os melhores resultados.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Tags: Por etiqueta", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "Você cria as tags e atribui manualmente algumas entradas como exemplo, o computador atribui o resto", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Tags: Por exemplo", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "Semelhante a \"Tags: por etiqueta\", mas com maior precisão à medida que você treina o sistema com bons exemplos.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "Voc<PERSON> cria as tags, o computador atribui as entradas", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Tags: Por etiqueta", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "Isto funciona bem quando tem um conjunto predefinido de etiquetas ou quando o seu projeto tem um âmbito limitado em termos de etiquetas.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Detetar entradas com um rácio significativo de não gosto/gosto", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Polémica", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Excluir tag", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "Tem certeza de que deseja excluir esta tag?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Não voltar a mostrar isto", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Editar tag", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "Adicionar nome", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Seleccione o máximo de 9 etiquetas pelas quais pretende que as entradas sejam distribuídas.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "A classificação é baseada nas entradas atualmente atribuídas às etiquetas. O computador tentará seguir o seu exemplo.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Etiquetas: Por exemplo", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "Você ainda não tem nenhuma tag personalizada.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "O computador detecta automaticamente as etiquetas e atribui-as às suas entradas.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Etiquetas: Totalmente automatizado", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "Funciona bem quando os seus projectos abrangem uma vasta gama de etiquetas. Bom sítio para começar.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "Como é que quer etiquetar?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Entradas sem etiquetas", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Detetar a língua de cada entrada", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "Língua", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "Lançamento", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "Sem filtros activos", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Utilizar etiquetas para subdividir e filtrar as entradas, de modo a fazer resumos mais precisos ou direccionados.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "Outros", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Etiquetas: Etiquetas da plataforma", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Atribuir as etiquetas de plataforma existentes que o autor seleccionou ao publicar", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Recomendado", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "Mudar o nome da etiqueta", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Nome", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "Renomear tag", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "Selecionar tudo", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Atribuir um sentimento positivo ou negativo a cada entrada, derivado do texto", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Deteção de etiquetas", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Utilizar filtros actuais", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "Que entradas pretende marcar?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Tarefa de marcação automática", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Polémica", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "Personalizado", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Terminou em", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "Por exemplo", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "Em andamento", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "Por etiqueta", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "Linguagem", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "Tag NLP", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "Não foram efetuadas tarefas de IA recentes", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Tag da plataforma", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "Em fila", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Iniciado em", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Sucesso", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Tarefa de resumo", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Acionado em", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "Acima", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "Todos", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "Autor", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "Abaixo", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "Ano de nascimento", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Engajamento", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "De", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "Entrada", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "Número de comentários", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Número de reações", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "Número de votos", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "Para", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Adicionar à análise", "app.containers.AdminPage.projects.project.analysis.anonymous": "Entrada anônima", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Autores por idade", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Autores por domicílio", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Trabalhos em segundo plano", "app.containers.AdminPage.projects.project.analysis.comments": "Comentários", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "O gráfico de domicílio é muito grande para ser exibido", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "Ocultar respostas vazias", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Respostas", "app.containers.AdminPage.projects.project.analysis.end": "Fim", "app.containers.AdminPage.projects.project.analysis.filter": "Mostrar apenas entradas com este valor", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Ocultar respostas sem resposta", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Insights automáticos", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Valores de coluna", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "H<PERSON> {count} instâncias dessa combinação.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "<PERSON><PERSON> gosta", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Explorar", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "Entradas", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Próximo mapa de calor", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Próxima visão", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Não é uma percepção estatisticamente significativa.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Os insights automáticos não estão disponíveis para projetos com menos de 30 participantes.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "Participantes", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Mapa de calor anterior", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Visão anterior", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "Valores de linha", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Insight estatisticamente significativo.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Resumir", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Tags de análise", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "Unidades", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "<PERSON><PERSON> <PERSON><PERSON> as percep<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Exibir insights automáticos", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Entradas sem tags", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "Um shapefile inválido foi carregado e não pode ser exibido.", "app.containers.AdminPage.projects.project.analysis.limit": "Limite", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "<PERSON><PERSON><PERSON> principal", "app.containers.AdminPage.projects.project.analysis.manageInput": "Gerenciar a entrada", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Próximo g<PERSON>", "app.containers.AdminPage.projects.project.analysis.noAnswer": "Sem resposta", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "Você não obteve resposta.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "Nenhum shapefile foi carregado.", "app.containers.AdminPage.projects.project.analysis.noInputs": "Nenhuma entrada corresponde aos seus filtros atuais", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Gráfico anterior", "app.containers.AdminPage.projects.project.analysis.reactions": "Reações", "app.containers.AdminPage.projects.project.analysis.remove": "Remover", "app.containers.AdminPage.projects.project.analysis.removeFilter": "Remover filtro", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "Remover filtro", "app.containers.AdminPage.projects.project.analysis.search": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Os Shapefiles são exibidos no formato GeoJSON aqui. <PERSON><PERSON> isso, o estilo do arquivo original pode não ser exibido corretamente.", "app.containers.AdminPage.projects.project.analysis.start": "Iniciar", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Artigo de suporte", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "Desconhecido", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "<PERSON>er todas as perguntas", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "Exibir perguntas selecionadas", "app.containers.AdminPage.projects.project.analysis.votes": "Votos", "app.containers.AdminPage.widgets.copied": "Copiado para a área de transferência", "app.containers.AdminPage.widgets.copyToClipboard": "<PERSON>pie esse código", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Copie o código HTML", "app.containers.AdminPage.widgets.fieldAccentColor": "<PERSON>r <PERSON>", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Cor de fundo da ferramenta", "app.containers.AdminPage.widgets.fieldButtonText": "Botão de texto", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Se inscreva agora", "app.containers.AdminPage.widgets.fieldFont": "Fonte", "app.containers.AdminPage.widgets.fieldFontDescription": "Deve ser uma fonte existente de {googleFontsLink}.Deixe em branco para usar uma fonte pré determinada", "app.containers.AdminPage.widgets.fieldFontSize": "<PERSON><PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Cabeçalho do Subtítulo", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Você pode ter algo a dizer", "app.containers.AdminPage.widgets.fieldHeaderText": "Cabeçalho do texto", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Nossa plataforma de participação", "app.containers.AdminPage.widgets.fieldHeight": "Altura (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Número de entradas", "app.containers.AdminPage.widgets.fieldProjects": "Projetos", "app.containers.AdminPage.widgets.fieldRelativeLink": "Links para", "app.containers.AdminPage.widgets.fieldShowFooter": "Mostrar botão", "app.containers.AdminPage.widgets.fieldShowHeader": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldShowLogo": "Mostrar logotipo", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Cor de fundo do site", "app.containers.AdminPage.widgets.fieldSort": "Ordenado por", "app.containers.AdminPage.widgets.fieldTextColor": "Cor do texto", "app.containers.AdminPage.widgets.fieldTopics": "Tópicos", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Página inicial", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Você pode copiar este código HTML e colá-lo na parte do seu site onde deseja adicionar a ferramenta", "app.containers.AdminPage.widgets.htmlCodeTitle": "Código HTML da ferramenta", "app.containers.AdminPage.widgets.previewTitle": "Vista prévia", "app.containers.AdminPage.widgets.settingsTitle": "Minhas configurações", "app.containers.AdminPage.widgets.sortNewest": "<PERSON><PERSON> recentes", "app.containers.AdminPage.widgets.sortPopular": "<PERSON><PERSON> votados", "app.containers.AdminPage.widgets.sortTrending": "Tendência", "app.containers.AdminPage.widgets.subtitleWidgets": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.AdminPage.widgets.title": "Ferramentas", "app.containers.AdminPage.widgets.titleDimensions": "Dimensões", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Cabeçalho e rodapé", "app.containers.AdminPage.widgets.titleInputSelection": "Seleção de entrada", "app.containers.AdminPage.widgets.titleStyle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleWidgets": "Ferramentas", "app.containers.ContentBuilder.Save": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.PageTitle": "Página inicial", "app.containers.ContentBuilder.homepage.SaveError": "Algo deu errado ao salvar a página inicial.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "<PERSON><PERSON> colu<PERSON>", "app.containers.ContentBuilder.homepage.bannerImage": "Imagem do banner", "app.containers.ContentBuilder.homepage.bannerSubtext": "Subtexto do banner", "app.containers.ContentBuilder.homepage.bannerText": "Texto do banner", "app.containers.ContentBuilder.homepage.button": "Botão", "app.containers.ContentBuilder.homepage.chooseLayout": "Layout", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "A personalização de configurações que não sejam a imagem e o texto no banner da página inicial não está incluída em sua licença atual. Entre em contato com seu gerente do GovSuccess para saber mais sobre isso.", "app.containers.ContentBuilder.homepage.customized_button": "Personalizado", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Texto do botão", "app.containers.ContentBuilder.homepage.customized_button_url_label": "Link do botão", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Exibe os próximos 3 eventos futuros em sua plataforma.", "app.containers.ContentBuilder.homepage.eventsDescription": "Exibe os próximos 3 eventos futuros em sua plataforma.", "app.containers.ContentBuilder.homepage.fixedRatio": "Banner de proporção fixa", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Esse tipo de banner funciona melhor com imagens que não devem ser cortadas, como imagens com texto, um logotipo ou elementos específicos que são cruciais para seus cidadãos. Esse banner é substituído por uma caixa sólida na cor primária quando os usuários estão conectados. Você pode definir essa cor nas configurações gerais. Mais informações sobre o uso recomendado de imagens podem ser encontradas em nosso site {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "Base de conhecimento", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "Banner de largura total", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Esse banner se estende por toda a largura para proporcionar um excelente efeito visual. A imagem tentará cobrir o máximo de espaço possível, fazendo com que ela nem sempre esteja visível o tempo todo. Você pode combinar esse banner com uma sobreposição de qualquer cor. Você pode encontrar mais informações sobre o uso recomendado de imagens em nosso site {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "Base de conhecimento", "app.containers.ContentBuilder.homepage.imageOverlayColor": "Cor da sobreposição de imagem", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Opacidade da sobreposição de imagem", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "URL inválido", "app.containers.ContentBuilder.homepage.no_button": "<PERSON><PERSON><PERSON> bot<PERSON>", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Usuários não registrados", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Ativar sobreposição", "app.containers.ContentBuilder.homepage.projectsDescription": "Para configurar a ordem em que seus projetos são exibidos, reordene-os em {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "Página de projetos", "app.containers.ContentBuilder.homepage.registeredUsersView": "Usuários registrados", "app.containers.ContentBuilder.homepage.showAvatars": "<PERSON><PERSON><PERSON> avat<PERSON>", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Mostrar fotos de perfil dos participantes e o número deles para visitantes não registrados", "app.containers.ContentBuilder.homepage.sign_up_button": "Inscrever-se", "app.containers.ContentBuilder.homepage.signedInDescription": "É assim que os usuários cadastrados veem o banner.", "app.containers.ContentBuilder.homepage.signedOutDescription": "É assim que os visitantes que não estão cadastrados na plataforma veem o banner.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "Esse banner é particularmente útil com imagens que não funcionam bem com o texto do título, subtítulo ou botão. Esses itens serão colocados abaixo do banner. Mais informações sobre o uso recomendado de imagens podem ser encontradas em nosso site {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "Base de conhecimento", "app.containers.ContentBuilder.homepage.twoRowLayout": "<PERSON><PERSON> linhas", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "Altura de incorporação (pixels)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "Altura que você deseja que seu conteúdo incorporado apareça na página (em pixels).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Breve descrição do conteúdo que você está incorporando", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "É útil fornecer essas informações aos usuários que dependem de um leitor de tela ou de outra tecnologia de assistência.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Endereço do site", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "URL completo do site que você deseja incorporar.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Exibir conteúdo de um site externo em sua página em um iFrame HTML. Observe que nem todas as páginas podem ser incorporadas. Se você estiver tendo problemas para incorporar uma página, verifique com o proprietário da página se ela está configurada para permitir a incorporação.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Visite nossa página de suporte", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "<PERSON><PERSON><PERSON><PERSON>, não foi possível incorporar este conteúdo. {visitLinkMessage} para saber mais.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Digite um endereço da Web válido, por exemplo, https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Incorporar", "app.containers.admin.ContentBuilder.accordionMultiloc": "Complementos", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Aberto por padrão", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Texto", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Este é um conteúdo de acordeão expansível. Você pode editá-lo e formatá-lo usando o editor no painel à direita.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Título do complemento", "app.containers.admin.ContentBuilder.buttonMultiloc": "Botão", "app.containers.admin.ContentBuilder.delete": "Deletar", "app.containers.admin.ContentBuilder.error": "erro", "app.containers.admin.ContentBuilder.errorMessage": "Há um erro no conteúdo {locale}, por favor corrija o problema para poder guardar as suas alterações", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Ocultar avatares de participação", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "Esta é uma pesquisa trimestral e contínua que monitora como você se sente em relação à governança e aos serviços públicos.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Responda à pesquisa", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Ajude-nos a atender você melhor", "app.containers.admin.ContentBuilder.homepage.default": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "Eventos", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "Eventos", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Chamada para ação", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Descrição", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "URL do botão principal", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Texto do botão primário", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "URL do botão secundário", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Texto do botão secundário", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Banner da página inicial", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Banner da página inicial", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Cartões de imagem e texto", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 coluna", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projetos", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Habilite propostas na seção “Propostas” no painel de administração para desbloqueá-las na página inicial", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Propostas", "app.containers.admin.ContentBuilder.imageMultiloc": "Imagem", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Breve descrição da imagem", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "Adicionar “texto alternativo” às imagens é importante para tornar sua plataforma acessível aos usuários que usam leitores de tela.", "app.containers.admin.ContentBuilder.participationBox": "Caixa de participação", "app.containers.admin.ContentBuilder.textMultiloc": "Texto", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 colunas", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 colunas", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 colunas com 30% e 60% de largura respectivamente", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 colunas com 60% e 30% de largura respectivamente", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 colunas pares", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://exemplo.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "<PERSON><PERSON><PERSON> mais reagidas", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Fase de ideação", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "Não há entradas disponíveis para esse projeto ou fase.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "Número de entradas", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "<PERSON><PERSON> mais", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Total de entradas: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Recolher texto longo", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "Não há entradas disponíveis para esse projeto ou fase.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Selecionar fase", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Autor", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "Reações", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "Votos", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "Entrada", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Taxa de registro", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Registros", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "<PERSON><PERSON> temporal dos participantes", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Observe que os números de participação podem não ser totalmente precisos, pois alguns dados são capturados em uma pesquisa externa que não rastreamos.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Gráfico", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Intervalo de datas", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.noData": "Não há dados disponíveis para os filtros que seleccionou.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Fontes de tráfego", "app.containers.admin.ReportBuilder.charts.users": "Usuários", "app.containers.admin.ReportBuilder.charts.usersByAge": "Usuários por idade", "app.containers.admin.ReportBuilder.charts.usersByGender": "Usuários por gênero", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Linha do tempo do visitante", "app.containers.admin.ReportBuilder.managerLabel1": "G<PERSON>nte de projeto", "app.containers.admin.ReportBuilder.periodLabel1": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.projectLabel1": "Projeto", "app.containers.admin.ReportBuilder.quarterReport1": "Relatório do Monitor Comunitário: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Início", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Comprar 1 licença adicional", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Confirmar", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Tem a certeza de que quer dar direitos de gestor a uma pessoa?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Conceder dire<PERSON><PERSON> de gestor", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Você atingiu o limite de licenças incluídas em seu plano, 1 licença adicional será adicionada.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Adicionar status", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Os status padrão não podem ser excluídos.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Deletar", "app.containers.admin.ideaStatuses.all.editButtonLabel": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Adicionar status", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Os status atualmente atribuídos a uma entrada não podem ser excluídos. Você pode remover / alterar o status das entradas existentes na guia {manageTab}", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Este status não pode ser apagado ou movido.", "app.containers.admin.ideaStatuses.all.manage": "Gerenciar", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "A configuração de status de entrada personalizados não está incluída em seu plano atual. Fale com seu gerente de sucesso do governo ou administrador para desbloqueá-lo.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Gerencie o status que pode ser atribuído à entrada do participante em um projeto. O status é visível publicamente e ajuda a manter os participantes informados.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Gerencie o status que pode ser atribuído a propostas em um projeto. O status é visível publicamente e ajuda a manter os participantes informados.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Editar status de entrada", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Editar status de propostas", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Selecionado para implementação ou próximos passos", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Feedback oficial fornecido", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Respondido", "app.containers.admin.ideaStatuses.form.category": "Categoria", "app.containers.admin.ideaStatuses.form.categoryDescription": "Por favor selecione a categoria que melhor represente o seu status. Esta seleção ajudará a nossa ferramenta de análise a processar e analisar posts com mais precisão.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Não corresponder a nenhuma das outras opções", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "Outros", "app.containers.admin.ideaStatuses.form.fieldColor": "Cor", "app.containers.admin.ideaStatuses.form.fieldDescription": "Descrição do status", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Fornecer uma descrição do status para todas as línguas", "app.containers.admin.ideaStatuses.form.fieldTitle": "Status da página", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Por favor, forneça o nome para todas as línguas", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Implementado com sucesso", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Implementado", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "A proposta não é elegível", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "Inelegível", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Inelegível ou não selecionado para avançar", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Não Selecionado", "app.containers.admin.ideaStatuses.form.saveStatus": "Salvar status", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Selecionado para implementação ou próximos passos", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Sob consideração", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Visto mas ainda não processado", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Visualizado", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Gerenciar entradas e seus status.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Gerenciador de entrada | Plataforma de participação de {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Dê feedback, adicione tópicos e mova entradas de um projeto para outro", "app.containers.admin.ideas.all.inputManagerPageTitle": "Gerenciamento de Informações", "app.containers.admin.ideas.all.tabOverview": "Visão geral", "app.containers.admin.import.importInputs": "Importar entradas", "app.containers.admin.import.importNoLongerAvailable3": "Esse recurso não está mais disponível aqui. Para importar entradas para uma fase de ideação, vá até a fase e selecione \"Importar\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 lugar de administrador adicional} other {# lugares de administrador adicionais}} e {managerSeats, plural, one {1 lugar de administrador adicional} other {# lugares de administrador adicionais}} serão adicionados para além do limite.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 lugar de administrador adicional será adicionado para além do limite} other {# lugares de administrador adicionais serão adicionados para além do limite}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 lugar de director adicional será adicionado para além do limite} other {# lugares de administrador adicionais serão adicionados para além do limite}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Confirmar e enviar convites", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Confirme o impacto no uso da licença", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Você atingiu o limite de licenças disponíveis em seu plano.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Administradores e gerentes deste projeto", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Apenas administradores e colaboradores", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Apenas os administradores da plataforma, os gestores de pastas e os gestores de projectos podem realizar a acção", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "<PERSON><PERSON><PERSON> pessoa", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "<PERSON><PERSON><PERSON> p<PERSON>, incluindo utilizadores não registados, pode participar.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Certos grupos de usuários", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Os utilizadores de um ou mais grupos de utilizadores específicos podem participar. Pode gerir os grupos de utilizadores no separador \"Utilizadores\".", "app.containers.admin.project.permissions.viewingRightsTitle": "Quem pode ver este projeto?", "app.containers.phaseConfig.enableSimilarInputDetection": "Ativar detecção de entrada semelhante", "app.containers.phaseConfig.similarInputDetectionTitle": "Detecção de entrada semelhante", "app.containers.phaseConfig.similarInputDetectionTooltip": "Mostre aos participantes entradas semelhantes enquanto eles digitam para ajudar a evitar duplicatas.", "app.containers.phaseConfig.similarityThresholdBody": "Limite de similaridade (corpo)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "Isso controla o grau de semelhança entre duas descrições para que sejam marcadas como semelhantes. Use um valor entre 0 (rigoroso) e 1 (brando). Valores mais baixos retornam menos correspondências, porém mais precisas.", "app.containers.phaseConfig.similarityThresholdTitle": "Limite de similaridade (título)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "Isso controla o grau de semelhança entre dois títulos para que sejam marcados como semelhantes. Use um valor entre 0 (rigoroso) e 1 (brando). Valores mais baixos retornam menos correspondências, porém mais precisas.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "Esse recurso está disponível como parte de uma oferta de acesso antecipado até 30 de junho de 2025. Se você quiser continuar a usá-lo após essa data, entre em contato com seu gerente de sucesso do governo ou administrador para discutir as opções de ativação.", "app.containers.survey.sentiment.noAnswers2": "<PERSON>en<PERSON><PERSON> resposta até o momento.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 comentários} one {1 comentário} other {# comentários}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Os participantes são usuários ou visitantes que participaram de um projeto, publicaram ou interagiram com uma proposta ou compareceram a eventos.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "Participantes", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Taxa de participação", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Porcentagem de visitantes que se tornam participantes.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Total de participantes", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Campanhas automáticas", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "E-mail automático", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "A partir de {quantity} campanhas", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "<PERSON><PERSON><PERSON> customizadas", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "E-mails customizados", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "E-mail", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Total de e-mails enviados", "app.modules.commercial.analytics.admin.components.Events.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.events": "Eventos", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Total de eventos adicionados", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Próxima", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Pendente", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Total de convites enviados", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Ir para Gestão de Entradas", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Contribuições", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Ativo", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Projetos que não são arquivados e visíveis na tabela 'Ativo' da página inicial", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Arquivado", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Projetos preliminares", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Todos os projetos arquivados e projetos de linhas cronológicas ativas que tenham terminado são aqui contados", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projetos", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Total de projetos", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "O número de projetos visíveis na plataforma", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Novos inscritos", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Taxa de registro", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Porcentagem de visitantes que se tornam usuários registrados.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Inscrições", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Total de inscritos", "app.modules.commercial.analytics.admin.components.Tab": "Visitantes", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Durante 30 dias:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Durante 7 dias:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Visualizações da página por visita", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Dura<PERSON> da visita", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Visitantes", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Visitantes\" é o número de visitantes únicos. Se uma pessoa visita a plataforma várias vezes, são contados apenas uma vez.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Visitas", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Visitantes\" é o número de visitantes únicos. Se uma pessoa visita a plataforma várias vezes, são contados apenas uma vez.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Ontem:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Contagem", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Idioma", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Número de visitantes", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Porcentagem de visitas", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "Referenciador", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "clique aqui", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Referenciador", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "Para ver a lista completa de referências, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Visitantes", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Fontes de tráfego", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Visitas", "app.modules.commercial.analytics.admin.components.totalParticipants": "Total de participantes", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Ainda não há dados de visitantes.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Mudamos a forma como coletamos e exibimos os dados dos visitantes. <PERSON> resultado, os dados dos visitantes são mais precisos e mais tipos de dados estão disponíveis, sem deixar de estar em conformidade com o GDPR. Embora os dados usados para a linha do tempo dos visitantes sejam mais antigos, só começamos a coletar os dados para \"Duração da visita\", \"Visualizações de página por visita\" e os outros gráficos em novembro de 2024, portanto, antes disso, nenhum dado está disponível. Portanto, se você selecionar dados anteriores a novembro de 2024, saiba que alguns gráficos podem estar vazios ou ter uma aparência estranha.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Entregas por e-mail ao longo do tempo", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Participantes ao longo do tempo", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Inscrições ao longo do tempo", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Data", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Estatísticas", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Estatísticas globais", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Visitas e visitantes ao longo do tempo", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Total ao longo do período", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Contagem", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Idioma", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Entrada direta", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "Número de visitantes", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Percentagem de visitas", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "Referenciador", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Sites de referência", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Ferramentas de pesquisa", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Redes sociais", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "Redirecionamentos de SSO", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Fontes de tráfego", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Número de visitas", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Website", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Você pode remover este conteúdo selecionando este item e clicando no botão remover no topo. Ele irá então reaparecer nas abas Ver ou Não ver", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Conte<PERSON>do inapropriado auto-detectado.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Não há posts reportados para revisão pela comunidade ou sinalizados por conteúdo impróprio pelo nosso sistema de Processamento de Linguagem Natural", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Remover {numberOfItems, plural, um {aviso de conteúdo} other {# avisos de conteúdo}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Reportado como inapropriado por um usuário da plataforma.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Avisos de conteúdo", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Elaboração de Relatórios", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Páginas mostradas na sua barra de navegação", "app.modules.navbar.admin.containers.addProject": "Adicionar projeto à barra de navegação", "app.modules.navbar.admin.containers.createCustomPageButton": "Criar página personalizada", "app.modules.navbar.admin.containers.deletePageConfirmation": "Tem a certeza de que quer apagar esta página? Isto não pode ser desfeito. Também pode remover a página da barra de navegação se ainda não estiver pronta para apagar.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Você só pode adicionar até 5 itens à barra de navegação", "app.modules.navbar.admin.containers.pageHeader": "Páginas & menu", "app.modules.navbar.admin.containers.pageSubtitle": "A sua barra de navegação pode exibir até cinco páginas para além das páginas Home e de projetos. Você pode renomear itens do menu, reordenar e adicionar novas páginas com o seu próprio conteúdo.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "IA", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widgets", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Use o ícone ☰ abaixo para arrastar o conteúdo de IA para o seu relatório.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "Não há insights de IA disponíveis. Você pode criá-los em seu projeto.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Ir para o projeto", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Selecionar fase", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Desbloquear a análise de IA", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Inclua insights gerados por IA em seu relatório", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Faça relatórios mais rapidamente com a IA", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "A geração de relatórios com IA não está incluída em seu plano atual. Fale com seu gerente de sucesso do governo para desbloquear esse recurso.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "Isso não está incluído em seu plano atual. Entre em contato com seu gerente de sucesso do governo ou administrador para desbloqueá-lo.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Agrupar por campo de registro", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Agrupar por pergunta da pesquisa", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Modo de grupo", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Agrupe as respostas da pesquisa por campos de registro (sexo, local, idade, etc.) ou outras perguntas da pesquisa.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Campo de registro", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Fase de pesquisa", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "<PERSON><PERSON><PERSON> da pesquisa", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Você tem certeza de que deseja excluir isso?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "Excluir", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "<PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Publique seu comentário", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "<PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "Escreva seu comentário aqui", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Clique nos botões abaixo para seguir ou deixar de seguir. O número de projetos é mostrado entre parênteses.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "Em sua área", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "<PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "<PERSON><PERSON> as preferências", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "No momento, não há projetos ativos de acordo com suas preferências de acompanhamento.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "Esse widget mostra projetos associados às \"áreas\" que o usuário segue. Observe que sua plataforma pode usar um nome diferente para \"áreas\" - consulte a guia \"Áreas\" nas configurações da plataforma. Se o usuário ainda não segue nenhuma área, o widget mostrar<PERSON> as áreas disponíveis para seguir. <PERSON><PERSON><PERSON> caso, o widget mostrará um máximo de 100 áreas.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "Não há projetos ou pastas publicados disponíveis", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Projetos e pastas publicados", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "Esse widget exibirá os projetos e as pastas que estão publicados no momento, respeitando a ordem definida na página de projetos. Esse comportamento é o mesmo que o da guia \"ativo\" do widget de projetos \"legado\".", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "Nenhum projeto ou pasta selecionado", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Selecionar projetos ou pastas", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Projetos e pastas selecionados", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "Com esse widget, você pode selecionar e determinar a ordem em que deseja que os projetos ou pastas sejam exibidos aos usuários.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projetos", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "visite o nosso centro de apoio", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Para mais informações sobre as resoluções de imagem recomendadas, {supportPageLink}."}
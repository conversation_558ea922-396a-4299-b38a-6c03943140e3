{"UI.FormComponents.required": "obavezno", "app.Admin.ManagementFeed.action": "Action", "app.Admin.ManagementFeed.after": "After", "app.Admin.ManagementFeed.before": "Before", "app.Admin.ManagementFeed.changed": "Modified", "app.Admin.ManagementFeed.created": "Created", "app.Admin.ManagementFeed.date": "Date", "app.Admin.ManagementFeed.deleted": "Deleted", "app.Admin.ManagementFeed.folder": "Folder", "app.Admin.ManagementFeed.idea": "Idea", "app.Admin.ManagementFeed.in": "in project {project}", "app.Admin.ManagementFeed.item": "<PERSON><PERSON>", "app.Admin.ManagementFeed.key": "Key", "app.Admin.ManagementFeed.managementFeedNudge": "Accessing the management feed is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.Admin.ManagementFeed.noActivityFound": "No activity found", "app.Admin.ManagementFeed.phase": "Phase", "app.Admin.ManagementFeed.project": "Project", "app.Admin.ManagementFeed.projectReviewApproved": "Project approved", "app.Admin.ManagementFeed.projectReviewRequested": "Project review requested", "app.Admin.ManagementFeed.title": "Management feed", "app.Admin.ManagementFeed.user": "User", "app.Admin.ManagementFeed.userPlaceholder": "Select a user", "app.Admin.ManagementFeed.value": "Value", "app.Admin.ManagementFeed.viewDetails": "View details", "app.Admin.ManagementFeed.warning": "Experimental feature: A minimal list of selected actions performed by admins or managers in the last 30 days. Not all actions are included.", "app.Admin.Moderation.managementFeed": "Management feed", "app.Admin.Moderation.participationFeed": "Participation feed", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "Are you sure?", "app.components.Admin.Campaigns.clicked": "Clicked", "app.components.Admin.Campaigns.deleteCampaignButton": "Delete campaign", "app.components.Admin.Campaigns.deliveryStatus_accepted": "Accepted", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Bounced", "app.components.Admin.Campaigns.deliveryStatus_clicked": "Clicked", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "This shows how many recipients clicked a link in the email. Please note that some security systems may follow links automatically to scan them, which can result in false clicks.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "Delivered", "app.components.Admin.Campaigns.deliveryStatus_failed": "Failed", "app.components.Admin.Campaigns.deliveryStatus_opened": "Opened", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "This shows how many recipients opened the email. Please note that some security systems (like Microsoft Defender) may pre-load content for scanning, which can result in false opens.", "app.components.Admin.Campaigns.deliveryStatus_sent": "<PERSON><PERSON>", "app.components.Admin.Campaigns.draft": "Draft", "app.components.Admin.Campaigns.from": "From", "app.components.Admin.Campaigns.manageButtonLabel": "Manage", "app.components.Admin.Campaigns.opened": "Opened", "app.components.Admin.Campaigns.project": "Project", "app.components.Admin.Campaigns.recipientsTitle": "Recipients", "app.components.Admin.Campaigns.reply_to": "Reply-To", "app.components.Admin.Campaigns.sent": "<PERSON><PERSON>", "app.components.Admin.Campaigns.statsButton": "Statistics", "app.components.Admin.Campaigns.subject": "Subject", "app.components.Admin.Campaigns.to": "To", "app.components.Admin.ImageCropper.cropFinalSentence": "See also: {link}.", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "Keep key content inside the dotted lines to ensure it's always visible.", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "3:1 on mobile (only the area between the dotted lines is shown)", "app.components.Admin.ImageCropper.cropSentenceOne": "The image is cropped automatically:", "app.components.Admin.ImageCropper.cropSentenceTwo": "{aspect} on desktop (full width shown)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "preporučena proporcija", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Tekst zaglavlja za registrovane posetioce", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Upozorenje: izabrane boje nisu u međusobnom kontrastu. To može uticati na smanjenu čitljivost teksta. Odaberite tamniju boju da biste povećali čitljivost.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Dodajte događaje u navigacioni meni", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "<PERSON>da je <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veza do svih projektnih događaja biće dodata na traku za navigaciju.", "app.components.AdminPage.SettingsPage.eventsSection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Prilagođena sekcija početne stranice", "app.components.AnonymousPostingToggle.userAnonymity": "Анонимност корисника", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Users will be able to hide their identity from other users, project managers and admins. These contributions can still be moderated.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Дозволите корисницима да учествују анонимно", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Корисници и даље могу да изаберу да учествују са својим правим именом, али ће имати могућност да анонимно поднесу прилоге ако то желе. Сви корисници ће и даље морати да се придржавају услова постављениһ на картици „Права приступа“ да би могли да дају свој допринос. Подаци корисничког профила неће бити доступни при извозу података о учешћу.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Сазнајте више о анонимности корисника у нашој {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "чланак подршке", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "хттпс://суппорт.цитизенлаб.цо/ен/артицлес/7946486-енаблинг-анонимоус-партиципатион", "app.components.BillingWarning.billingWarning": "Када се додају додатна места, ваш обрачун ће бити повећан. Обратите се свом ГовСуццесс менаџеру да сазнате више о томе.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Thank you for completing the survey! You're welcome to take it again next quarter.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Download as pdf", "app.components.FormSync.downloadExcelTemplate": "Download an Excel template", "app.components.FormSync.downloadExcelTemplateTooltip2": "Excel templates will not include Ranking questions, Matrix questions, File upload questions and any mapping input questions (Drop Pin, Draw Route, Draw Area, ESRI file upload) as these are not supported for bulk importing at this time.", "app.components.ProjectTemplatePreview.close": "Zatvori", "app.components.ProjectTemplatePreview.createProject": "Kreiraj<PERSON> projekat", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Create a project based on the template ''{templateTitle}''", "app.components.ProjectTemplatePreview.goBack": "Povratak nazad", "app.components.ProjectTemplatePreview.goBackTo": "Povratak na {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "Go Vocal ekspert", "app.components.ProjectTemplatePreview.infoboxLine1": "Da li ovaj obrazac želite da koristite u vašem participativnom projektu?", "app.components.ProjectTemplatePreview.infoboxLine2": "Obratite se nadležnoj osobi u vašoj gradskoj administraciji, ili kontaktirajte {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Fascikla sa projektom", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Odabrani datum je neispravan. Molimo vas da odaberete datum u sledećem formatu YYYY-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Mo<PERSON>o vas da odredite datum početka projekta. ", "app.components.ProjectTemplatePreview.projectStartDate": "Datum početka vašeg projekta", "app.components.ProjectTemplatePreview.projectTitle": "Naziv vašeg projekta", "app.components.ProjectTemplatePreview.projectTitleError": "Unesite naziv projekta", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Molimo vas unesite naziv projekta na svim jezicima", "app.components.ProjectTemplatePreview.projectsOverviewPage": "pregled projekata", "app.components.ProjectTemplatePreview.responseError": "Something went wrong, try again later <NAME_EMAIL>.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Pogledajte j<PERSON>", "app.components.ProjectTemplatePreview.successMessage": "Projekat je uspeš<PERSON> kreiran!", "app.components.ProjectTemplatePreview.typeProjectName": "Unesite naziv projekta", "app.components.ProjectTemplatePreview.useTemplate": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.components.SeatInfo.additionalSeats": "Додатна седишта", "app.components.SeatInfo.additionalSeatsToolTip": "Ово показује број додатних седишта која сте купили на врху „Укључена седишта“.", "app.components.SeatInfo.adminSeats": "Административна места", "app.components.SeatInfo.adminSeatsIncludedText": "Укључено {adminSeats} административних места", "app.components.SeatInfo.adminSeatsTooltip1": "Администратори су задужени за платформу и имају менаџерска права за све фасцикле и пројекте. Можете {visitHelpCenter} да сазнате више о различитим улогама.", "app.components.SeatInfo.currentAdminSeatsTitle": "Тренутна административна места", "app.components.SeatInfo.currentManagerSeatsTitle": "Тренутна менаџерска места", "app.components.SeatInfo.includedAdminToolTip": "Ово показује број расположивих места за администраторе укључених у годишњи уговор.", "app.components.SeatInfo.includedManagerToolTip": "Ово показује број расположивих места за менаџере укључених у годишњи уговор.", "app.components.SeatInfo.includedSeats": "Укључена седишта", "app.components.SeatInfo.managerSeats": "Менаџерска места", "app.components.SeatInfo.managerSeatsTooltip": "Менаџери фасцикли/пројеката могу управљати неограниченим бројем фасцикли/пројеката. Можете {visitHelpCenter} да сазнате више о различитим улогама.", "app.components.SeatInfo.managersIncludedText": "Укључено {managerSeats} менаџерских места", "app.components.SeatInfo.remainingSeats": "Преостала места", "app.components.SeatInfo.rolesSupportPage": "хттпс://суппорт.цитизенлаб.цо/ен/артицлес/2672884-вхат-аре-тхе-дифферент-ролес-он-тхе-платформ", "app.components.SeatInfo.totalSeats": "Укупно места", "app.components.SeatInfo.totalSeatsTooltip": "Ово показује збир седишта у оквиру вашег плана и додатних места која сте купили.", "app.components.SeatInfo.usedSeats": "Половна седишта", "app.components.SeatInfo.view": "Поглед", "app.components.SeatInfo.visitHelpCenter": "посетите наш центар за помоћ", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Ваш план има {adminSeatsIncluded}. Када искористите сва седишта, додатна седишта ће бити додата под 'Додатна седишта'.", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Ваш план има {managerSeatsIncluded}, који испуњава услове за менаџере фасцикли и менаџере пројеката. Када искористите сва седишта, додатна седишта ће бити додата под 'Додатна седишта'.", "app.components.UserSearch.addModerators": "<PERSON><PERSON><PERSON><PERSON>", "app.components.UserSearch.searchUsers": "Pretražite korisnike", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternative error message", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "By default, the following error message will be shown to users:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Customize error message", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "You can overwrite this message for each language using the \"Alternative error message\" text box below. If you leave the text box empty, the default message will be shown.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "Error message", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "This is what participants will see when they don't meet the participation requirements.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Save error message", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "No question selected. Please select a question first.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "No answer", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} responses", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "Survey question", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} until now", "app.components.admin.DatePhasePicker.Input.openEnded": "Open ended", "app.components.admin.DatePhasePicker.Input.selectDate": "Select date", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Clear end date", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Clear start date", "app.components.admin.Graphs": "<PERSON><PERSON> dostu<PERSON>h podataka za trenutne filtere.", "app.components.admin.Graphs.noDataShort": "<PERSON><PERSON> podata<PERSON>.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Komentari tokom vremena", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Objave tokom vremena", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Реакције током времена", "app.components.admin.InputManager.onePost": "1 unos", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Offline picks adjustment", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Offline votes adjustment", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "This option allows you to include participation data from other sources, such as in-person or paper votes:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "It will be visually distinct from digital votes.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "It will affect the final vote results.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "It will not be reflected in participation data dashboards.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Offline votes for an option can only be set once in a project, and are shared between all phases of a project.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "You must enter the total offline participants first.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Total offline participants", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "In order to calculate the correct results, we need to know the <b>total amount of offline participants for this phase</b>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Please indicate only those that participated offline.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "Modified by {name}", "app.components.admin.PostManager.PostPreview.assignee": "Zadužena osoba", "app.components.admin.PostManager.PostPreview.cancelEdit": "Prekinite izmene", "app.components.admin.PostManager.PostPreview.currentStatus": "Trenutni status", "app.components.admin.PostManager.PostPreview.delete": "Izbriši", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Da li ste sigurni da želite da obrišete ovaj unos? Ova akcija ne može biti poništena. ", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Da li ste sigurni da želite da obrišete ovaj unos? Ukoliko to učinite, biće uklonjen iz svih projektnih faza i neće ga biti moguće povratiti. ", "app.components.admin.PostManager.PostPreview.edit": "Izmenite", "app.components.admin.PostManager.PostPreview.noOne": "Nedodeljeno", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Broj odabira ovog predloga u sklopu participativnih budžeta drugih učesnika", "app.components.admin.PostManager.PostPreview.picks": "<PERSON><PERSON><PERSON><PERSON>: {picksNumber} ", "app.components.admin.PostManager.PostPreview.reactionCounts": "Реакција се рачуна:", "app.components.admin.PostManager.PostPreview.save": "Sačuvajte", "app.components.admin.PostManager.PostPreview.submitError": "Greška", "app.components.admin.PostManager.addFeatureLayer": "Add feature layer", "app.components.admin.PostManager.addFeatureLayerInstruction": "Copy the URL of the feature layer hosted on ArcGIS Online and paste it in the input below:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Add a new feature layer to the map", "app.components.admin.PostManager.addWebMap": "Add Web Map", "app.components.admin.PostManager.addWebMapInstruction": "Copy the portal ID of your Web Map from ArcGIS Online and paste it in the input below:", "app.components.admin.PostManager.allPhases": "Sve faze", "app.components.admin.PostManager.allProjects": "Svi projekti", "app.components.admin.PostManager.allStatuses": "Svi statusi", "app.components.admin.PostManager.allTopics": "Sve teme", "app.components.admin.PostManager.anyAssignment": "<PERSON><PERSON>", "app.components.admin.PostManager.assignedTo": "{assigneeName}", "app.components.admin.PostManager.assignedToMe": "<PERSON><PERSON><PERSON><PERSON> meni", "app.components.admin.PostManager.assignee": "Zadužena osoba", "app.components.admin.PostManager.authenticationError": "An authentication error occured while trying to fetch this layer. Please check the URL and that your Esri API key has access to this layer.", "app.components.admin.PostManager.automatedStatusTooltipText": "This status updates automatically when conditions are met", "app.components.admin.PostManager.bodyTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel": "Cancel", "app.components.admin.PostManager.cancel2": "Cancel", "app.components.admin.PostManager.co-sponsors": "Co-sponsors", "app.components.admin.PostManager.comments": "Komentari", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "This means you will lose all data associated with these inputs, like comments, reactions and votes. This action cannot be undone.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Are you sure you want to delete these inputs?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "Remove topic", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "You are trying to remove this idea from a phase where it has received votes. If you do this, these votes will be lost. Are you sure you want to remove this idea from this phase?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "The votes associated with this idea will be lost", "app.components.admin.PostManager.components.goToInputManager": "Идите на менаџер уноса", "app.components.admin.PostManager.components.goToProposalManager": "Идите до менаџера предлога", "app.components.admin.PostManager.contributionFormTitle": "Уредите допринос", "app.components.admin.PostManager.cost": "Cost", "app.components.admin.PostManager.createInput": "Create input", "app.components.admin.PostManager.createInputsDescription": "Create a new set of inputs from a past project", "app.components.admin.PostManager.currentLat": "Centralna širina", "app.components.admin.PostManager.currentLng": "Centralna dužina", "app.components.admin.PostManager.currentZoomLevel": "<PERSON><PERSON>", "app.components.admin.PostManager.defaultEsriError": "An error occured while trying to fetch this layer. Please check your network connect and that the URL is correct.", "app.components.admin.PostManager.delete": "Izbriši", "app.components.admin.PostManager.deleteAllSelectedInputs": "<PERSON><PERSON><PERSON><PERSON> {count} unosa", "app.components.admin.PostManager.deleteConfirmation": "Da li ste sigurni da želite da obrišete ovaj sloj?", "app.components.admin.PostManager.dislikes": "Dislikes", "app.components.admin.PostManager.edit": "Izmenite", "app.components.admin.PostManager.editProjects": "Уредите пројекте", "app.components.admin.PostManager.editStatuses": "Уредите статусе", "app.components.admin.PostManager.editTags": "Уредите ознаке", "app.components.admin.PostManager.editedPostSave": "сачувати", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Importing data from Esri ArcGIS Online is an add-on feature. Talk to your GS manager to unlock it.", "app.components.admin.PostManager.esriSideError": "An error occured on the ArcGIS application. Please wait a few minutes and try again later.", "app.components.admin.PostManager.esriWebMap": "Esri Web Map", "app.components.admin.PostManager.exportAllInputs": "Izvezite sve unose (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Izvezite sve komentare (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Izvezite sve komentare iz ovog projekta (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Izvezite unose iz ovog projekta (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Izvezite odabrane unose (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Izvezite komentare iz odabranih unosa (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Export votes by input (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Export votes by user (.xslx)", "app.components.admin.PostManager.exports": "Izvodi", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "You may only upload map data as either GeoJSON layers or importing from ArcGIS Online. Please remove any current GeoJSON layers if you wish to add a Feature Layer.", "app.components.admin.PostManager.featureLayerTooltop": "You can find the Feature Layer URL on the right hand side of the item page on ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Odaberite kako će drugi videti vaše ime", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Objasnite promenu ovog statusa", "app.components.admin.PostManager.fileUploadError": "Отпремање једне или више датотека није успело. Проверите величину и формат датотеке и покушајте поново.", "app.components.admin.PostManager.formTitle": "Уредите идеју", "app.components.admin.PostManager.generalApiError2": "An error occured while trying to fetch this item. Please check that the URL or Portal ID is correct and you have access to this item.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "You may only upload map data as either GeoJSON layers or importing from ArcGIS Online. Please remove any ArcGIS data if you wish to upload a GeoJSON layer.", "app.components.admin.PostManager.goToDefaultMapView": "Idite na podrazumevani centar mape", "app.components.admin.PostManager.hiddenFieldsLink": "skrivena polja", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Savet: <PERSON><PERSON><PERSON><PERSON> {hiddenFieldsLink} pril<PERSON>m podešavanja vaše Typeform ankete kako biste mogli da pratite ko je na nju odgovorio.", "app.components.admin.PostManager.import2": "Import", "app.components.admin.PostManager.importError": "Odabrana datoteka nije mogla biti uvezena jer nije važeća GeoJSON datoteka", "app.components.admin.PostManager.importEsriFeatureLayer": "Import Esri Feature Layer", "app.components.admin.PostManager.importEsriWebMap": "Import Esri Web Map", "app.components.admin.PostManager.importInputs": "Import inputs", "app.components.admin.PostManager.imported": "Imported", "app.components.admin.PostManager.initiativeFormTitle": "Edit initiative", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputImportProgress": "{importedCount} out of {totalCount} {totalCount, plural, one {input has} other {inputs have}} been imported. The import is still in progress, please check back later.", "app.components.admin.PostManager.inputManagerHeader": "Objave", "app.components.admin.PostManager.inputs": "Objave", "app.components.admin.PostManager.inputsExportFileName": "unos", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Prikaži samo unose za koje su potrebne povratne informacije", "app.components.admin.PostManager.issueFormTitle": "Уреди проблем", "app.components.admin.PostManager.latestFeedbackMode": "Iskoristite poslednje zvanično obaveštenje", "app.components.admin.PostManager.layerAdded": "Layer added successfully", "app.components.admin.PostManager.likes": "<PERSON>s", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Pomeranje unosa sa trenutnog projekta dovešće do gubitka informacija o dodeljenim fazama. Da li želite da nastavite?", "app.components.admin.PostManager.mapData": "Map data", "app.components.admin.PostManager.multipleInputs": "{ideaCount} unosa", "app.components.admin.PostManager.newFeedbackMode": "Napišite obaveštenje kako biste objasnili ovu promenu", "app.components.admin.PostManager.noFilteredResults": "Filteri koje ste odabrali nisu dali rezultat", "app.components.admin.PostManager.noInputs": "No inputs yet", "app.components.admin.PostManager.noInputsDescription": "You add your own input or start from a past participation project.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 inputs} one {1 input} other {# inputs}} will be imported from the selected project and phase. The import will run in the background, and the inputs will appear in the input manager once it is complete.", "app.components.admin.PostManager.noOne": "Nedodeljeno", "app.components.admin.PostManager.noProject": "No project", "app.components.admin.PostManager.officialFeedbackModal.author": "Author", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "Choose how your name will appear", "app.components.admin.PostManager.officialFeedbackModal.description": "Providing official feedback helps keep the process transparent and builds trust in the platform.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Author is required", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Feedback is required", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Official feedback", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Explain the reason for the status change", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Post feedback", "app.components.admin.PostManager.officialFeedbackModal.skip": "Skip this time", "app.components.admin.PostManager.officialFeedbackModal.title": "Explain your decision", "app.components.admin.PostManager.officialUpdateAuthor": "Odaberite kako će drugi videti vaše ime", "app.components.admin.PostManager.officialUpdateBody": "Objasnite promenu ovog statusa", "app.components.admin.PostManager.offlinePicks": "Offline picks", "app.components.admin.PostManager.offlineVotes": "Offline votes", "app.components.admin.PostManager.onlineVotes": "Online votes", "app.components.admin.PostManager.optionFormTitle": "Измени опцију", "app.components.admin.PostManager.participants": "Participants", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Online picks", "app.components.admin.PostManager.pbItemCountTooltip": "Broj odabira ovog predloga u sklopu participativnih budžeta drugih učesnika", "app.components.admin.PostManager.petitionFormTitle": "Edit petition", "app.components.admin.PostManager.postedIn": "Објављено у {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Уреди пројекат", "app.components.admin.PostManager.projectsTab": "Projekti", "app.components.admin.PostManager.projectsTabTooltipContent": "Možete kliknuti i prevući objave kako biste ih premestili iz jednog projekta u drugi. Vodite računa o tome da ćete kod projekata sa vremenskim okvirima, ipak morati da dodate objavu u specifičnu fazu. ", "app.components.admin.PostManager.proposalFormTitle": "Edit proposal", "app.components.admin.PostManager.proposedBudgetTitle": "Предложени буџет", "app.components.admin.PostManager.publication_date": "Objavljeno dana", "app.components.admin.PostManager.questionFormTitle": "Уреди питање", "app.components.admin.PostManager.reactions": "Реакције", "app.components.admin.PostManager.resetFiltersButton": "Poništi filtere", "app.components.admin.PostManager.resetInputFiltersDescription": "Resetujte filtere kako biste videli sve unose.", "app.components.admin.PostManager.saved": "Sačuvan<PERSON>", "app.components.admin.PostManager.screeningTooltip": "Screening is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Screening is turned off for this phase. Go to phase setup to enable it", "app.components.admin.PostManager.selectAPhase": "Select a phase", "app.components.admin.PostManager.selectAProject": "Select a project", "app.components.admin.PostManager.setAsDefaultMapView": "Sačuvajte trenutnu centralnu tačku i nivo zumiranja prema mapi kao podrazumevane", "app.components.admin.PostManager.startFromPastInputs": "Start from past inputs", "app.components.admin.PostManager.statusChangeGenericError": "Do<PERSON><PERSON> je do greške. Molimo vas pokušajte kasnije <NAME_EMAIL>.", "app.components.admin.PostManager.statusChangeSave": "Promenite status", "app.components.admin.PostManager.statusesTab": "Statusi", "app.components.admin.PostManager.statusesTabTooltipContent": "Kliknite i prevucite kako biste promenili status posta. Autor i učesnici u diskusiji će biti obavešteni o ovoj promeni.", "app.components.admin.PostManager.submitApiError": "Дошло је до проблема при слању обрасца. Проверите да ли постоје грешке и покушајте поново.", "app.components.admin.PostManager.timelineTab": "Faze", "app.components.admin.PostManager.timelineTabTooltipText": "Kliknite na unose, a zatim ih prevucite u željene faze projekta. ", "app.components.admin.PostManager.title": "<PERSON><PERSON>", "app.components.admin.PostManager.topicsTab": "Teme", "app.components.admin.PostManager.topicsTabTooltipText": "Add tags to an input using drag and drop.", "app.components.admin.PostManager.view": "View", "app.components.admin.PostManager.votes": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votesByInputExportFileName": "votes_by_input", "app.components.admin.PostManager.votesByUserExportFileName": "votes_by_user", "app.components.admin.PostManager.webMapAlreadyExists": "You can only add one Web Map at a time. Remove the current one to import a different one.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "You may only upload map data as either GeoJSON layers or importing from ArcGIS Online. Please remove any current GeoJSON layers if you wish to connect a Web Map.", "app.components.admin.PostManager.webMapTooltip": "You can find the Web Map portal ID on your ArcGIS Online item page, on the right hand side.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {Мање од једног дана} one {Једног дана} other {# дана}} лево", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "Cancel", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "Yes, delete survey results", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "This cannot be undone", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Delete survey results", "app.components.admin.ProjectEdit.survey.downloadResults2": "Download survey results", "app.components.admin.ReportExportMenu.FileName.fromFilter": "od", "app.components.admin.ReportExportMenu.FileName.groupFilter": "grupa", "app.components.admin.ReportExportMenu.FileName.projectFilter": "projekat", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tema", "app.components.admin.ReportExportMenu.FileName.untilFilter": "dok", "app.components.admin.ReportExportMenu.downloadPng": "Preuzmi kao PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Preuzmi kao SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Preuzmi Excel", "app.components.admin.SlugInput.regexError": "Opis može da sadrži samo obična mala slova (a-z), br<PERSON><PERSON><PERSON> (0-9) i crtice (-). Crtica ne može da bude na mestu prvog i poslednjeg znaka. Crtice jedna za drugom (--) su zabranjene.", "app.components.admin.TerminologyConfig.saveButton": "Sačuvajte", "app.components.admin.commonGroundInputManager.title": "Title", "app.components.admin.seatSetSuccess.admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.allDone": "Завршено", "app.components.admin.seatSetSuccess.close": "Близу", "app.components.admin.seatSetSuccess.manager": "Менаџер", "app.components.admin.seatSetSuccess.orderCompleted": "Наруџба је завршена", "app.components.admin.seatSetSuccess.reflectedMessage": "Промене у вашем плану ће се одразити на следећи обрачунски циклус.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} права је додељено изабраним корисницима.", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Are you sure you want to delete all survey results?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "This participation method is in beta. We're gradually rolling it out to gather feedback and improve the experience.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Прикупљање повратниһ информација о документу је прилагођена функција и није укључена у вашу тренутну лиценцу. Обратите се свом ГовСуццесс менаџеру да сазнате више о томе.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Number of days is required", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "Number of days to reach minimum number of votes", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Više informacija o tome kako ugraditi link za Google Forms može se pronaći na {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "ovaj članak za podršku", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "I<PERSON>ja", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Initiative", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Kako bi ovaj unos trebao da se zove?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Problem", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Овде наведите везу до вашег Конвеио документа. Прочитајте наш {supportArticleLink} за више информација о подешавању Конвеио-а.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "чланак подршке", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "һттпс://суппорт.цитизенлаб.цо/ен/артицлес/7946532-ембеддинг-конвеио-пдф-доцументс-фор-цоллецтинг-феедбацк", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "This is not included in your current plan. Reach out to your Government Success Manager or admin to unlock it.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Maksimalan iznos budžeta je obavezan", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Maximum number of votes per option must be less than or equal to total number of votes", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Потребан је максималан број гласова", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "Messaging", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Minimalni iznos budžeta ne može biti veći od maksimalnog", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Minimalni iznos budžeta je obavezan", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Минимални број гласова не може бити већи од максималног", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Потребан је минималан број гласова", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Missing end date", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Missing start date", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Opcija", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Input Manager tab", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Configure the voting options in the Input manager tab after creating a phase.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Configure the voting options in the {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Participation options", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "Participants", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Petition", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Admins & managers", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Annotating document:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} can participate in this phase.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "Cancel", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Comment:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Common ground phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Delete phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "Yes, delete this phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "Are you sure you want to delete this phase?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "All data relating to this phase will be deleted. This cannot be undone.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Document annotation phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Everyone", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "External survey phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Ideation phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "In platform survey phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Information phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "Mixed rights", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "No end date", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Poll phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Proposals phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>React:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Registered for event:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Registered users", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b>Submit inputs:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>Taking poll:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b>Taking survey:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Users with confirmed emails", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>Volunteering:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Volunteering phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Voting:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Voting phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Who can participate?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Inputs won’t be visible until an admin reviews and approves them. Authors can’t edit inputs after they are screened or reacted on.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "Admins only", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "Anyone with the link can interact with the draft project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "Approve", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Approving allows Project Managers to publish the project.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "Approved by {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Archived", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Draft", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Edit description", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "Everyone", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Groups", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "Hidden", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Offline voters", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Only admins{inFolder, select, true { or the Folder Managers} other {}} can publish the project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 participant} other {{participantsCount} participants}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Participants in embedded methods (e.g., external surveys)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Followers of a project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Note: Enabling anonymous or open participation permissions may allow users to participate multiple times, leading to misleading or incomplete user data.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "Participants <b>do not include</b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Participants include:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Event registrants", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "Users interacting with Go Vocal methods", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "Waiting for approval", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Project reviewers have been notified.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "Public", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Publish", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Published - Active", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Published - Finished", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Refresh project preview link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Regenerate project preview link. This will invalidate the previous link.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Old links will stop working but you can generate a new one at any time.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Are you sure? This will disable the current link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "Cancel", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "Yes, refresh link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Request approval", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "The project must be approved by an admin{inFolder, select, true { or one of the Folder Managers} other {}} before you can publish it. Click the button below to request approval.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Settings", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Share", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "Copy link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "Link copied", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Sharing private links is not included on your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Share this project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "Who has access", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "View", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Projekti", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Proposal", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Minimum number of votes to be considered", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Minimum number of votes is required", "app.components.app.containers.AdminPage.ProjectEdit.report": "Report", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Require screening of inputs", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Timeline", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Traffic", "app.components.formBuilder.cancelMethodChange1": "Cancel", "app.components.formBuilder.changeMethodWarning1": "Changing methods can lead to hiding any input data generated or received while using the previous method.", "app.components.formBuilder.changingMethod1": "Changing method", "app.components.formBuilder.confirmMethodChange1": "Yes, continue", "app.components.formBuilder.copySurveyModal.cancel": "Cancel", "app.components.formBuilder.copySurveyModal.description": "This will copy all the questions and logic without the answers.", "app.components.formBuilder.copySurveyModal.duplicate": "Duplicate", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "No appropriate phases found in this project", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "No phase selected. Please select a phase first.", "app.components.formBuilder.copySurveyModal.noProject": "No project", "app.components.formBuilder.copySurveyModal.noProjectSelected": "No project selected. Please select a project first.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "You have already saved changes to this survey. If you duplicate another survey, the changes will be lost.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Survey phase", "app.components.formBuilder.copySurveyModal.title": "Select a survey to duplicate", "app.components.formBuilder.editWarningModal.addOrReorder": "Add or reorder questions", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Your response data may be inaccurate", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Edit text", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Fixing a typo? It won't affect your response data", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "You'll lose response data linked to that question", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Delete a question", "app.components.formBuilder.editWarningModal.exportYouResponses2": "export your responses.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Warning: You might lose response data forever. Before continuing,", "app.components.formBuilder.editWarningModal.noCancel": "No, cancel", "app.components.formBuilder.editWarningModal.title4": "Edit live survey", "app.components.formBuilder.editWarningModal.yesContinue": "Yes, continue", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "access rights settings for this survey", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "'Demographic fields in survey form' is enabled. When the survey form is displayed any configured demographic questions will be added on a new page immediately before the end of the survey. These questions can be changed in the {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "access rights settings for this phase.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Survey respondents will not be required to sign up or log in to submit survey answers, which may result in duplicate submissions", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "By skipping the sign up/log in step, you accept not to collect demographic information on survey respondents, which may impact your data analysis capabilities", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "This survey is set to allow access for \"Anyone\" under the Access Rights tab.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "If you wish to change this, you can do so in the {accessRightsSettingsLink}", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "You are asking the following demographic questions of survey respondents through the sign up/log in step.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "To streamline the collection of demographic information and ensure its integration into your user database, we advise incorporating any demographic questions directly into the sign-up/log-in process. To do so, please use the {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Ask users to follow areas or topics", "app.components.onboarding.followHelperText": "This activates a step in the registration process where users will be able to follow areas or topics that you select below", "app.components.onboarding.followPreferences": "Follow preferences", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} у оквиру плана, {noOfAdditionalSeats} додатно", "app.components.seatsWithinPlan.seatsWithinPlanText": "Седишта у оквиру плана", "app.containers.Admin.Campaigns.campaignFrom": "Od:", "app.containers.Admin.Campaigns.campaignTo": "Za:", "app.containers.Admin.Campaigns.customEmails": "Прилагођене е-поруке", "app.containers.Admin.Campaigns.customEmailsDescription": "Шаљите прилагођене имејлове и проверите статистику.", "app.containers.Admin.Campaigns.noAccess": "<PERSON><PERSON> na<PERSON> je, ali deluje da vam pristup email sekciji nije o<PERSON>en", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Automatska e-pošta", "app.containers.Admin.Insights.tabReports": "Reports", "app.containers.Admin.Invitations.a11y_removeInvite": "Remove invitation", "app.containers.Admin.Invitations.addToGroupLabel": "Dodajte ove osobe u željene manualne grupe korisnika", "app.containers.Admin.Invitations.adminLabel1": "<PERSON><PERSON><PERSON>ka prava", "app.containers.Admin.Invitations.adminLabelTooltip": "<PERSON>da odaberete ovu opciju, osobe koje ste pozvali će imati pristup svim podešavanjima vaše platforme.", "app.containers.Admin.Invitations.configureInvitations": "3. Podesite pozivnice", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Nisu pronađene pozivnice koje odgovaraju kriterijumima vaše pretrage", "app.containers.Admin.Invitations.deleteInvite": "Prekid", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Are you sure you want to delete this invitation?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Prekid procesa pozivanja će vam omogućiti da ovoj osobi ponovo pošaljete pozivnicu.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Preuzmite i popunite šablon", "app.containers.Admin.Invitations.downloadTemplate": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.email": "Е-p<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.emailListLabel": "Unesite email adrese u polje u nastavku, razdvajajući ih zarezom.", "app.containers.Admin.Invitations.exportInvites": "Izvezite listu sa pozivnicama", "app.containers.Admin.Invitations.fileRequirements": "Važno: Nemojte brisati kolone iz uvezenog šablona - ukoliko ih ne koristite, ostavite ih prazne. Pre nego što uvezete dokument, molimo vas da otklonite duple email adrese kako bi se izbegle greške prilikom slanja pozivnica.", "app.containers.Admin.Invitations.filetypeError": "Neispravan tip dokumenta. Podržani su samo XLSX dokumenti.", "app.containers.Admin.Invitations.groupsPlaceholder": "<PERSON>je odabrana grupa", "app.containers.Admin.Invitations.helmetDescription": "Pozovite korisnike na platformu", "app.containers.Admin.Invitations.helmetTitle": "Administracija pozivnica", "app.containers.Admin.Invitations.importOptionsInfo": "Uz pozivnicu možete dodati i personalizovanu poruku, dodati osobe u grupu ili im omogućiti pravo administracije ili moderacije. Posetite {supportPageLink} za više informacija.", "app.containers.Admin.Invitations.importTab": "Uvezite email adrese", "app.containers.Admin.Invitations.invitationExpirationWarning": "Be aware that invitations expire after 30 days. After this period, you can still resend them.", "app.containers.Admin.Invitations.invitationOptions": "Opcije pozivnica", "app.containers.Admin.Invitations.invitationSubtitle": "Pozovite ljude koji još uvek nisu registrovani na platformi. Uvezite njihove email adrese postavljanjem u šablon za uvoz ili ih unesite ručno. <PERSON><PERSON>, dodajte ličnu poruku, dajte korisnicima dodatna prava ili ih dodajte u manualnu grupu.", "app.containers.Admin.Invitations.invitePeople": "Pozivnice", "app.containers.Admin.Invitations.inviteStatus": "Status", "app.containers.Admin.Invitations.inviteStatusAccepted": "P<PERSON>h<PERSON>ć<PERSON>", "app.containers.Admin.Invitations.inviteStatusPending": "Na čekanju", "app.containers.Admin.Invitations.inviteTextLabel": "Dodajte personalizovanu poruku uz pozivnicu", "app.containers.Admin.Invitations.invitedSince": "<PERSON><PERSON><PERSON> dana", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Odaberite jezik pozivnice", "app.containers.Admin.Invitations.moderatorLabel": "Omogućite ovim ljudima da upravljaju projektom", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Ukoliko odaberete ovu opciju, pozvane osobe će postati menadžeri odabranih projekata. Možete pronaći više informacija o menadžerskim ovlašećenjima {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "ovde", "app.containers.Admin.Invitations.name": "Ime", "app.containers.Admin.Invitations.processing": "Pozivnice se šalju. Molimo sačekajte...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "<PERSON><PERSON> o<PERSON> projekata", "app.containers.Admin.Invitations.save": "Pošaljite vaše pozivnice", "app.containers.Admin.Invitations.saveErrorMessage": "Doš<PERSON> je do jedne ili više grešaka i pozivnice nisu poslate. Molimo vas da ispravite greške koje se nalaze u nastavku i pokušate ponovo. ", "app.containers.Admin.Invitations.saveSuccess": "Uspešno!", "app.containers.Admin.Invitations.saveSuccessMessage": "Pozivnice su uspešno poslate.", "app.containers.Admin.Invitations.supportPage": "podrška", "app.containers.Admin.Invitations.supportPageLinkText": "posetite stranicu sa <PERSON>škom", "app.containers.Admin.Invitations.tabAllInvitations": "Sve pozivnice", "app.containers.Admin.Invitations.tabInviteUsers": "Pozovite korisnike", "app.containers.Admin.Invitations.textTab": "Unesite email adrese", "app.containers.Admin.Invitations.unknownError": "Došlo je do greške. Pokušajte ponovo kasnije.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Postavite vaš finalni dokument", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} ako želite više informacija o podržanim kolonama u uvezenom šablonu.", "app.containers.Admin.Moderation.all": "Sve", "app.containers.Admin.Moderation.belongsTo": "Pripada", "app.containers.Admin.Moderation.collapse": "Proširi", "app.containers.Admin.Moderation.comment": "Komentariši", "app.containers.Admin.Moderation.commentDeletionCancelButton": "Cancel", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "Delete", "app.containers.Admin.Moderation.confirmCommentDeletion": "Are you sure you want to delete this comment? This is permanent and can't be undone.", "app.containers.Admin.Moderation.content": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.date": "Datum", "app.containers.Admin.Moderation.deleteComment": "Delete comment", "app.containers.Admin.Moderation.goToComment": "Otvori komentar u novom prozoru", "app.containers.Admin.Moderation.goToPost": "Otvori ovaj unos u novom tabu", "app.containers.Admin.Moderation.goToProposal": "Otvori ovaj predlog u novom tabu", "app.containers.Admin.Moderation.markFlagsError": "Označavanje stavki nije uspelo. Pokušajte ponovo.", "app.containers.Admin.Moderation.markNotSeen": "<PERSON><PERSON><PERSON><PERSON> {selectedItemsCount, plural, one {# stavku} other {# stavke}} kao ne<PERSON>", "app.containers.Admin.Moderation.markSeen": "<PERSON><PERSON><PERSON><PERSON> {selectedItemsCount, plural, one {# stavku} other {# stavke}} kao pro<PERSON>", "app.containers.Admin.Moderation.moderationsTooltip": "Ova stranica vam omogućuje da brzo proverite sve nove unose na platformi, uključujući objave i komentare. Unose možete obeležiti kao \"pregledane\", kako bi drugi mogli da prate koji od njih još uvek čekaju na obradu.", "app.containers.Admin.Moderation.noUnviewedItems": "<PERSON><PERSON><PERSON><PERSON> nema nepre<PERSON><PERSON>h stavki", "app.containers.Admin.Moderation.noViewedItems": "<PERSON><PERSON><PERSON><PERSON> nema pregledanih stavki", "app.containers.Admin.Moderation.pageTitle1": "Напајање", "app.containers.Admin.Moderation.post": "Objave", "app.containers.Admin.Moderation.profanityBlockerSetting": "Blo<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Blokirajte objave koje sadrže najčešće prijavljene uvredljive reči.", "app.containers.Admin.Moderation.project": "Projekti", "app.containers.Admin.Moderation.read": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.readMore": "Pročitajte više", "app.containers.Admin.Moderation.removeFlagsError": "Uklanjanje upozorenja nije uspelo. Pokušajte ponovo.", "app.containers.Admin.Moderation.rowsPerPage": "Redova po strani", "app.containers.Admin.Moderation.settings": "Podešavanja", "app.containers.Admin.Moderation.settingsSavingError": "Čuvanje nije uspelo. Pokušajte da ponovo izmenite podešavanja.", "app.containers.Admin.Moderation.show": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.status": "Status", "app.containers.Admin.Moderation.successfulUpdateSettings": "Podešavanja su uspešno ažurirana.", "app.containers.Admin.Moderation.type": "Vrsta", "app.containers.Admin.Moderation.unread": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Ova stranica sastoji se iz sledećih odeljaka. Možete da ih uključujete/isključujete i uređujete po potrebi.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "Prikaž<PERSON> stra<PERSON>u", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Nije prikazano na stranici", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Prikazano na stranici", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Do<PERSON>j<PERSON> da<PERSON> (maksimalno 50 MB) koje će biti dostupne za preuzimanje sa stranice.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Odeljak za informacije u dnu", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Dodajte sopstveni sadržaj prilagodljivoj sekciji u dnu stranice.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Izmenite", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Lista događaja", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Prikažite događaje vezane za projekte.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Prilagodite sliku i tekst banera na stranici.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Lista projekata", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Prikažite projekte na osnovu postavki svoje stranice. Možete da pregledate projekte koji će se prikazati.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Odeljak za informacije na vrhu", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Dodajte sopstveni sadržaj prilagodljivoj sekciji u vrhu stranice.", "app.containers.Admin.PagesAndMenu.addButton": "Dodaj u traku za navigaciju", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Ime u traci za navigaciju", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Da li ste sigurni da želite da izbrišete ovu stranicu? Ova radnja ne može se poništiti.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Provide a title for all languages", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Ostale dostupne stranice", "app.containers.Admin.PagesAndMenu.components.savePage": "Sačuvaj stranicu", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "Stranica je us<PERSON>š<PERSON> sa<PERSON>uvana", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Uspeh", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "<PERSON><PERSON> bilo moguće sačuvati priloge", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Datoteke ne bi trebalo da budu veće od 50Mb. Dodate datoteke biće prikazane u dnu ove stranice", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Datoteke | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Sačuvaj i omogući priloge", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Sačuvaj datoteke", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Obezbedite sadržaj za sve jezike", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Uspeh", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Odeljak za informacije u dnu nije sačuvan", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Odeljak za informacije u dnu sačuvan", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Odeljak za informacije u dnu", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Sačuvaj i omogući donju sekciju sa informacijama", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Sačuvaj odeljak za informacije u dnu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Creating custom pages is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Dodajte najmanje jednu kategori<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Uspeh", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "Po oblasti", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "Po oznaci(ama)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Displaying projects by tag or area is not part of your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Uredi prilagođenu stra<PERSON>u", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Povezani projekti", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Izaberite koji se projekti i povezani događaji mogu prikazati na stranici.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Stranica je uspeš<PERSON> kreirana", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "Stranica je us<PERSON>š<PERSON> sa<PERSON>uvana", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Prilagođena stranica je sačuvana", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Naslov u traci za navigaciju", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "<PERSON><PERSON><PERSON><PERSON> prilagođ<PERSON> stranicu | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Kreiraj prilagođenu stranicu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Postavke stranice", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Sačuvaj prilagođenu stranicu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Izaberite oblast", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Izabrana oblast", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "Opis može da sadrži samo obična mala slova (a-z), br<PERSON><PERSON><PERSON> (0-9) i crtice (-). Crtica ne može da bude na mestu prvog i poslednjeg znaka. Crtice jedna za drugom (--) su zabranjene.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Morate da unesete opis", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Unesite naziv na svim jezicima", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Pregledaj prilagođenu stranicu", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "<PERSON>redi prilago<PERSON> stra<PERSON> | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "<PERSON><PERSON><PERSON><PERSON> stranic<PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Izmenite", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "For single phase projects, if the end date is empty and the description is not filled in, a timeline will not be displayed on the project page.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "<PERSON>ema dostupnih projekata na osnovu vašeg {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Ovaj projekat nema filter za oznake ili oblasti, tako da neće biti prikazan nijedan projekat.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Lista projekata | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "postavke stranice", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Lista projekata", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Sledeći projekti biće prikazani na ovoj stranici na osnovu vašeg {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "PODRAZUMEVANO", "app.containers.Admin.PagesAndMenu.deleteButton": "Izbriši", "app.containers.Admin.PagesAndMenu.editButton": "Izmenite", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Uspeh", "app.containers.Admin.PagesAndMenu.heroBannerError": "<PERSON><PERSON> heroja nije <PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "<PERSON><PERSON> <PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> heroja", "app.containers.Admin.PagesAndMenu.homeTitle": "Početna", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Unesite sadržaj za najmanje jedan jezik", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "You can only add up to 5 items to the navigation bar", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "<PERSON><PERSON><PERSON> i meni | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Ukloni iz trake za navigaciju", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Sačuvaj i omogući baner heroja ", "app.containers.Admin.PagesAndMenu.title": "Stranice i meni", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Uspeh", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.topInfoError": "Odeljak za informacije na vrhu nije sačuvan", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Odeljak za informacije na vrhu sačuvan", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Gornja sekcija sa informacijama | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Odeljak za informacije na vrhu", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Sačuvaj i omogući gornju sekciju sa informacijama", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Sačuvaj odeljak za informacije na vrhu", "app.containers.Admin.PagesAndMenu.viewButton": "Prikaz", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "Age", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Community", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Executive summary", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Top-level inclusion indicators", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "The following section outlines inclusion indicators, highlighting your our progress towards fostering a more inclusive and representative participation platform.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "participants", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Top-level participation indicators", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "The following section outlines the key participation indicators for the selected time range, providing an overview of engagement trends and performance metrics.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projects", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "projects published", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Platform report", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "Your projects", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "The following section provides an overview of publicly visible projects that overlap with the selected time range, the most used methods in these projects, and metrics concerning the total amount of participation.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Registrations timeline", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Блокирани корисници", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Управљајте блокираним корисницима.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Obriši grupu", "app.containers.Admin.Users.GroupsHeader.editGroup": "Izmeni grupu", "app.containers.Admin.Users.GroupsPanel.admins": "Admins", "app.containers.Admin.Users.GroupsPanel.allUsers": "Registrovani korisnici", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Grupe", "app.containers.Admin.Users.GroupsPanel.managers": "Project managers", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Assigned items", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Dobijte pregled svih ljudi i organizacija koji su se registrovali na platformi. Dodajte odabrane korisnike u Manualne ili Smart grupe.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Pozivnica na čekanju", "app.containers.Admin.Users.admin": "Administrator", "app.containers.Admin.Users.assign": "Assign", "app.containers.Admin.Users.assignedItems": "Assigned items for {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "Купите једно додатно седиште", "app.containers.Admin.Users.changeUserRights": "Промените корисничка права", "app.containers.Admin.Users.confirm": "Potvrdi", "app.containers.Admin.Users.confirmAdminQuestion": "Da li ste sigurni da <PERSON><PERSON>te da {name} do<PERSON><PERSON> administratorska prava?", "app.containers.Admin.Users.confirmNormalUserQuestion": "Da li ste sigurni da ž<PERSON>te da {name} bude postavljen/a kao običan korisnik?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Да ли сте сигурни да желите да поставите {name} као обичан корисник? Имајте на уму да ће изгубити права менаџера за све пројекте и фасцикле којима су додељени након потврде.", "app.containers.Admin.Users.deleteUser": "Obrišite ovog korisnika", "app.containers.Admin.Users.email": "Е-p<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.folder": "Folder", "app.containers.Admin.Users.folderManager": "<PERSON><PERSON><PERSON><PERSON> fasci<PERSON>", "app.containers.Admin.Users.helmetDescription": "Korisnici u admin listi", "app.containers.Admin.Users.helmetTitle": "Admin - korisnički panel", "app.containers.Admin.Users.inviteUsers": "Позовите кориснике", "app.containers.Admin.Users.joined": "Joined", "app.containers.Admin.Users.lastActive": "Last active", "app.containers.Admin.Users.name": "Ime", "app.containers.Admin.Users.noAssignedItems": "No assigned items", "app.containers.Admin.Users.options": "Solucije", "app.containers.Admin.Users.permissionToBuy": "<PERSON><PERSON> bi {name} do<PERSON>/la <PERSON>ka prava, treba da kupite 1 dodatno sedište.", "app.containers.Admin.Users.platformAdmin": "Administrator platforme", "app.containers.Admin.Users.projectManager": "Menadžer projekta", "app.containers.Admin.Users.reachedLimitMessage": "Dostigli ste ograničenje broja sedišta za vaš plan, biće dodato 1 dodatno sedište za {name}.", "app.containers.Admin.Users.registeredUser": "Registrovani korisnik", "app.containers.Admin.Users.remove": "Remove", "app.containers.Admin.Users.removeModeratorFrom": "The user is moderating the folder this project belongs to. Remove assignment from \"{folderTitle}\" instead.", "app.containers.Admin.Users.role": "Role", "app.containers.Admin.Users.seeProfile": "<PERSON><PERSON><PERSON> profil ovog koris<PERSON>a", "app.containers.Admin.Users.selectPublications": "Select projects or folders", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Type to search", "app.containers.Admin.Users.setAsAdmin": "<PERSON><PERSON> kao <PERSON>a", "app.containers.Admin.Users.setAsNormalUser": "Postavi kao običnog korisnika", "app.containers.Admin.Users.setAsProjectModerator": "Set as project manager", "app.containers.Admin.Users.setUserAsProjectModerator": "Assign {name} as project manager", "app.containers.Admin.Users.userBlockModal.allDone": "Завршено", "app.containers.Admin.Users.userBlockModal.blockAction": "Блокирај корисника", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Садржај овог корисника неће бити уклоњен овом радњом. Не заборавите да модерирате њихов садржај ако је потребно.", "app.containers.Admin.Users.userBlockModal.blocked": "Блокирано", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Овај корисник је блокиран од {from}. Забрана траје до {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "Поништити, отказати", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Да ли сте сигурни да желите да деблокирате {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} је блокирано до {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 дан} other {{numberOfDays} дана}}", "app.containers.Admin.Users.userBlockModal.header": "Блокирај корисника", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Разлог", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Ово ће бити саопштено блокираном кориснику.", "app.containers.Admin.Users.userBlockModal.subtitle1": "Изабрани корисник неће моћи да се пријави на платформу за {daysBlocked}. Ако желите да ово вратите, можете их деблокирати са листе блокираних корисника.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Деблокирај", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "Да, желим да деблокирам овог корисника", "app.containers.Admin.Users.userDeletionConfirmation": "Trajno uklonite ovog korisnika?", "app.containers.Admin.Users.userDeletionFailed": "<PERSON><PERSON><PERSON> je do greške prilikom brisanja korisnika, molimo vas pokušajte kasnije.", "app.containers.Admin.Users.userDeletionProposalVotes": "This will also delete any votes by this user on proposals which are still open for voting.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.userInsights": "Увиди корисника", "app.containers.Admin.Users.youCantDeleteYourself": "Ne možete obrisati vlastiti nalog, putem administratorske stranice.", "app.containers.Admin.Users.youCantUnadminYourself": "U ovom trenutku, ne možete napustiti ulogu administratora", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Community monitor", "app.containers.Admin.communityMonitor.healthScore": "Health Score", "app.containers.Admin.communityMonitor.healthScoreDescription": "This score is the average of all sentiment-scale questions answered by participants for the period selected.", "app.containers.Admin.communityMonitor.lastQuarter": "last quarter", "app.containers.Admin.communityMonitor.liveMonitor": "Live monitor", "app.containers.Admin.communityMonitor.noResults": "No results for this period.", "app.containers.Admin.communityMonitor.noSurveyResponses": "No survey responses", "app.containers.Admin.communityMonitor.participants": "Participants", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Reports", "app.containers.Admin.communityMonitor.settings": "Settings", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Community Monitor Survey is accepting submissions.", "app.containers.Admin.communityMonitor.settings.accessRights2": "Access rights", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "After a user registers an event attendance, submits a vote, or returns to a project page after submitting a survey.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Community Monitor Managers can access and manage all Community Monitor settings and data.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Community Monitor Managers", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Managers can edit the Community Monitor survey & permissions, see response data and create reports.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "The default frequency value is 100%.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Popup frequency (0 to 100)", "app.containers.Admin.communityMonitor.settings.management2": "Management", "app.containers.Admin.communityMonitor.settings.popup": "Popup", "app.containers.Admin.communityMonitor.settings.popupDescription3": "A popup is periodically displayed to users encouraging them to complete the Community Monitor Survey. You can adjust the frequency which determines the percentage of users who will randomly see the popup when the conditions outlined below are met.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Popup settings", "app.containers.Admin.communityMonitor.settings.preview": "Preview", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "User has not already filled out the survey in the previous 3 months.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "User has not already seen the popup in the previous 3 months.", "app.containers.Admin.communityMonitor.settings.save": "Save", "app.containers.Admin.communityMonitor.settings.saved": "Saved", "app.containers.Admin.communityMonitor.settings.settings": "Settings", "app.containers.Admin.communityMonitor.settings.survey2": "Survey", "app.containers.Admin.communityMonitor.settings.surveySettings3": "General settings", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Upon loading the Homepage or a Custom Page.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonymize all user data", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "All of the survey's inputs from users will be anonymized before being recorded", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Users will still need to comply with participation requirements under the 'Access Rights'. User profile data will not be available in the survey data export.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "Under what conditions can the popup appear for users?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Who are the managers?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Total survey responses", "app.containers.Admin.communityMonitor.upsell.aiSummary": "AI summary", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Enable Community Monitor", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Health score", "app.containers.Admin.communityMonitor.upsell.learnMore": "Learn more", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Score over time", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "The Community Monitor helps you stay ahead by tracking resident trust, satisfaction with services, and community life — continuously.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Get clear scores, powerful quotes, and a quarterly report you can share with colleagues or elected officials.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Easy-to-read scores that evolve over time", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Key resident quotes, summarised by AI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Questions tailored to your city's context", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Residents recruited randomly on the platform", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Quarterly PDF reports, ready to share", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "Understand how your community feels before issues grow", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Count", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Desktop or Other", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Mobile", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tablet", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Device Types", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Device Type", "app.containers.Admin.earlyAccessLabel": "Early Access", "app.containers.Admin.earlyAccessLabelExplanation": "This is a newly released feature available in Early Access.", "app.containers.Admin.emails.addCampaign": "Create email", "app.containers.Admin.emails.addCampaignTitle": "Kreirajte novi email", "app.containers.Admin.emails.allParticipantsInProject": "All participants in project", "app.containers.Admin.emails.allUsers": "Registrovani korisnici", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Аутоматске е-поруке се аутоматски шаљу и покрећу се радњама корисника. Неке од њих можете искључити за све кориснике ваше платформе. Остале аутоматизоване е-поруке се не могу искључити јер су потребне за правилно функционисање ваше платформе.", "app.containers.Admin.emails.automatedEmails": "Аутоматизоване е-поруке", "app.containers.Admin.emails.automatedEmailsDigest": "Е-пошта ће бити послата само ако има садржаја", "app.containers.Admin.emails.automatedEmailsRecipients": "Корисници који ће добити ову е-пошту", "app.containers.Admin.emails.automatedEmailsTriggers": "Догађај који покреће ову е-пошту", "app.containers.Admin.emails.changeRecipientsButton": "Izmeni primaoce", "app.containers.Admin.emails.clickOnButtonForExamples": "Кликните на дугме испод да бисте проверили примере ове е-поште на нашој страници за подршку.", "app.containers.Admin.emails.confirmSendHeader": "Pošaljite svim korisnicima?", "app.containers.Admin.emails.deleteButtonLabel": "Izbriši", "app.containers.Admin.emails.draft": "Nacrt", "app.containers.Admin.emails.editButtonLabel": "Izmenite", "app.containers.Admin.emails.editCampaignTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editDisabledTooltip2": "Coming soon: This email cannot currently be edited.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Button text", "app.containers.Admin.emails.editRegion_intro_multiloc": "Introduction", "app.containers.Admin.emails.editRegion_subject_multiloc": "Subject", "app.containers.Admin.emails.editRegion_title_multiloc": "Title", "app.containers.Admin.emails.emailCreated": "Email successfully created in draft", "app.containers.Admin.emails.emailUpdated": "Email successfully updated", "app.containers.Admin.emails.emptyCampaignsDescription": "Easily connect with your participants by sending them emails. Choose who to contact and track your engagement.", "app.containers.Admin.emails.emptyCampaignsHeader": "Send your first email", "app.containers.Admin.emails.failed": "<PERSON><PERSON> us<PERSON>", "app.containers.Admin.emails.fieldBody": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldBodyError": "Navedite poruku e-pošte", "app.containers.Admin.emails.fieldGroupContent": "Email Content", "app.containers.Admin.emails.fieldReplyTo": "Odgovori stižu ovde", "app.containers.Admin.emails.fieldReplyToEmailError": "Navedite adresu e-pošte u ispravnom formatu, na primer, <EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Navedite adresu e-p<PERSON>šte", "app.containers.Admin.emails.fieldReplyToTooltip": "Možete izabrati gde želite da stižu odgovori na vaš email.", "app.containers.Admin.emails.fieldSender": "Od", "app.containers.Admin.emails.fieldSenderError": "Navedite pošiljaoca e-poruke", "app.containers.Admin.emails.fieldSenderTooltip": "Možete odrediti koga će primaoci videti kao pošiljaoca.", "app.containers.Admin.emails.fieldSubject": "<PERSON><PERSON>", "app.containers.Admin.emails.fieldSubjectError": "Navedite predmet poruke e-pošte", "app.containers.Admin.emails.fieldSubjectTooltip": "<PERSON>vo će biti prikazano kao email naslov. Nastojte da bude jasno i da privlači pažnju.", "app.containers.Admin.emails.fieldTo": "<PERSON>a", "app.containers.Admin.emails.fieldToTooltip": "Možete odabrati grupe korisnika koje će primiti vaš email.", "app.containers.Admin.emails.formSave": "Sačuvajte kao nacrt", "app.containers.Admin.emails.formSaveAsDraft": "Save as draft", "app.containers.Admin.emails.from": "Од:", "app.containers.Admin.emails.groups": "Grupe", "app.containers.Admin.emails.helmetDescription": "Manualno pošaljite mejlove ka grupama korisnika i aktivirajte automatizovane kampanje", "app.containers.Admin.emails.nameVariablesInfo2": "You can speak directly to citizens using the variables {firstName} {lastName}. E.g. \"Dear {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "Probni email je poslat na vašu adresu", "app.containers.Admin.emails.previewTitle": "Prikaz", "app.containers.Admin.emails.regionMultilocError": "Please provide a value for all languages", "app.containers.Admin.emails.seeEmailHereText": "Чим се пошаље емаил овог типа, моћи ћете да га проверите овде.", "app.containers.Admin.emails.send": "Pošalji", "app.containers.Admin.emails.sendNowButton": "Poša<PERSON><PERSON> sad", "app.containers.Admin.emails.sendTestEmailButton": "Pošalji mi probni email", "app.containers.Admin.emails.sendTestEmailTooltip2": "When you click this link, a test email will be sent to your email address only. This allows you to check what the email looks like in ‘real life’.", "app.containers.Admin.emails.senderRecipients": "Pošiljalac i primaoci", "app.containers.Admin.emails.sending": "Šalje se", "app.containers.Admin.emails.sent": "Poslato", "app.containers.Admin.emails.sentToUsers": "Ово су е-поруке које се шаљу корисницима", "app.containers.Admin.emails.subject": "Тема:", "app.containers.Admin.emails.supportButtonLabel": "Погледајте примере на нашој страници за подршку", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "До:", "app.containers.Admin.emails.toAllUsers": "Da li želite da pošaljete ovaj email svim korisnicima?", "app.containers.Admin.emails.viewExample": "Поглед", "app.containers.Admin.ideas.import": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.AllProjects": "All projects", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Community monitor", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Document annotation", "app.containers.Admin.inspirationHub.ExternalSurvey": "External survey", "app.containers.Admin.inspirationHub.Filters.Country": "Country", "app.containers.Admin.inspirationHub.Filters.Method": "Method", "app.containers.Admin.inspirationHub.Filters.Search": "Search", "app.containers.Admin.inspirationHub.Filters.Topic": "Topic", "app.containers.Admin.inspirationHub.Filters.population": "Population", "app.containers.Admin.inspirationHub.Highlighted": "Highlighted", "app.containers.Admin.inspirationHub.Ideation": "Ideation", "app.containers.Admin.inspirationHub.Information": "Information", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Please choose a country to see pinned projects", "app.containers.Admin.inspirationHub.PinnedProjects.country": "Country", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "No pinned projects found for this country. Change the country to see pinned projects for other countries", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Change the country to see more pinned projects", "app.containers.Admin.inspirationHub.Poll": "Poll", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "Open ended", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "Read more...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Phase {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "If you don't want your project to be included in the inspiration hub, reach out to your GovSuccess manager.", "app.containers.Admin.inspirationHub.Proposals": "Proposals", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Sort by", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Participants (lowest first)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Participants (highest first)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Start date (oldest first)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Start date (newest first)", "app.containers.Admin.inspirationHub.Survey": "Survey", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Curated list of the best projects around the globe.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Talk to, and learn from, fellow practitioners.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filter by method, city size & country.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Enable Inspiration Hub", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "https://support.govocal.com/en/articles/11093736-using-the-inspiration-hub", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "Learn more", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "The Inspiration Hub connects you to a curated feed of exceptional participation projects on Go Vocal platforms across the world. Learn how other cities run successful projects & talk to other practitioners.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Join a network of pioneering democracy practitioners", "app.containers.Admin.inspirationHub.Volunteering": "Volunteering", "app.containers.Admin.inspirationHub.Voting": "Voting", "app.containers.Admin.inspirationHub.commonGround": "Common ground", "app.containers.Admin.inspirationHub.filters": "filters", "app.containers.Admin.inspirationHub.resetFilters": "Reset filters", "app.containers.Admin.inspirationHub.seemsLike": "Seems like there are no more projects. Try changing the {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "Edit campaign fields", "app.containers.Admin.messaging.automated.variablesToolTip": "You can use the following variables in your message:", "app.containers.Admin.messaging.helmetTitle": "<PERSON><PERSON><PERSON> poruka", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "This widget shows each user projects <b>based on their follow preferences</b>. This includes projects that they follow, as well as projects where they follow inputs, and projects related to topics or areas that they are interested in.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "This widget will only be shown to the user if there are projects where they can participate. If you see this message, it means that you (the admin) cannot participate in any projects at this moment. This message will not be visible on the real homepage.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Open to participation", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "This widget will showcase projects where the user can currently <b>take an action to participate</b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Title", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "For you", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "This widget will only be shown to the user if there are projects relevant for them based on their follow preferences. If you see this message, it means that you (the admin) are not following anything at the moment. This message will not be visible on the real homepage.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "Followed items", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Archived", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Filter by", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Finished", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Finished and archived", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "No data available", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "This widget shows <b>projects that are finished and/or archived.</b>. \"Finished\" also includes projects that are in the last phase, and where the last phase is a report.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Finished projects", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "You said, we did...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "Provide a name for all languages", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "The project cannot be empty", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Name", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Project", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Resulting URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "Save", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Add project", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "The navigation bar will only show projects to which users have access.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "This widget will only be visible on the Homepage when the Community Monitor is accepting responses.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Community Monitor", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Description", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Title", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Important:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Example of a sentiment survey question", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "No end date", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "Press escape to skip carrousel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projects and folders (legacy)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Projects title", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} is currently working on", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Button text", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Participate now!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Description", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Please select a project or folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Select project or folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Show avatars", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Spotlight", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Title", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "Starting in {days} days", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Starting in {weeks} weeks", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} days ago", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} weeks ago", "app.containers.Admin.project.Campaigns.campaignFrom": "From:", "app.containers.Admin.project.Campaigns.campaignTo": "To:", "app.containers.Admin.project.Campaigns.customEmails": "Custom emails", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Send out custom emails and check statistics.", "app.containers.Admin.project.Campaigns.noAccess": "We're sorry, but it seems like you don't have access to the emails section", "app.containers.Admin.project.emails.addCampaign": "Create email", "app.containers.Admin.project.emails.addCampaignTitle": "New campaign", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "All {participants} and followers from the project", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "This includes registered users that performed any action in the project. Unregistered or anonymized users are not included.", "app.containers.Admin.project.emails.dateSent": "Date sent", "app.containers.Admin.project.emails.deleteButtonLabel": "Delete", "app.containers.Admin.project.emails.draft": "Draft", "app.containers.Admin.project.emails.editButtonLabel": "Edit", "app.containers.Admin.project.emails.editCampaignTitle": "Edit campaign", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Easily connect with your participants by sending them emails. Choose who to contact and track your engagement.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Send your first email", "app.containers.Admin.project.emails.failed": "Failed", "app.containers.Admin.project.emails.fieldBody": "Email Message", "app.containers.Admin.project.emails.fieldBodyError": "Provide an email message for all languages", "app.containers.Admin.project.emails.fieldReplyTo": "Replies should go to", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Provide an email address in the correct format, <NAME_EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "Provide an email address", "app.containers.Admin.project.emails.fieldReplyToTooltip": "Choose what email address should receive direct replies from users on your email.", "app.containers.Admin.project.emails.fieldSender": "From", "app.containers.Admin.project.emails.fieldSenderError": "Provide a sender of the email", "app.containers.Admin.project.emails.fieldSenderTooltip": "Choose whom users will see as the sender of the email.", "app.containers.Admin.project.emails.fieldSubject": "Email Subject", "app.containers.Admin.project.emails.fieldSubjectError": "Provide an email subject for all languages", "app.containers.Admin.project.emails.fieldSubjectTooltip": "This will be shown in the subject line of the email and in the user’s inbox overview. Make it clear and engaging.", "app.containers.Admin.project.emails.fieldTo": "To", "app.containers.Admin.project.emails.formSave": "Save as draft", "app.containers.Admin.project.emails.from": "From:", "app.containers.Admin.project.emails.helmetDescription": "Send out manual emails to project participants", "app.containers.Admin.project.emails.infoboxAdminText": "From the Project Messaging tab you can only email all project participants.  To email other participants or subsets of users go to the {link} tab.", "app.containers.Admin.project.emails.infoboxLinkText": "Platform Messaging", "app.containers.Admin.project.emails.infoboxModeratorText": "From the Project Messaging tab you can only email all project participants. Admins can send emails to other participants or subsets of users via the Platform Messaging tab.", "app.containers.Admin.project.emails.message": "Message", "app.containers.Admin.project.emails.nameVariablesInfo2": "You can speak directly to citizens using the variables {firstName} {lastName}. E.g. \"Dear {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "participants", "app.containers.Admin.project.emails.previewSentConfirmation": "A preview email has been sent to your email address", "app.containers.Admin.project.emails.previewTitle": "Preview", "app.containers.Admin.project.emails.projectParticipants": "Project participants", "app.containers.Admin.project.emails.recipients": "Recipients", "app.containers.Admin.project.emails.send": "Send", "app.containers.Admin.project.emails.sendTestEmailButton": "Send a preview", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Send this draft email to the email address with which you are logged in, to check how it looks like in ‘real life’.", "app.containers.Admin.project.emails.senderRecipients": "Sender and recipients", "app.containers.Admin.project.emails.sending": "Sending", "app.containers.Admin.project.emails.sent": "<PERSON><PERSON>", "app.containers.Admin.project.emails.sentToUsers": "These are emails sent to users", "app.containers.Admin.project.emails.status": "Status", "app.containers.Admin.project.emails.subject": "Subject:", "app.containers.Admin.project.emails.to": "To:", "app.containers.Admin.project.messaging.helmetTitle": "Messaging", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "<PERSON>va slika deo je kartice fascikle. Na primer, to je kartica koja rezimira fasciklu i prikazana je na početnoj stranici. Za više informacija o preporučenim rezolucijama slike, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Ova slika je prikazana u vrhu stranice sa fasciklama. Za više informacija o preporučenim rezolucijama slike, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "poseti naš centar za podršku", "app.containers.Admin.projects.all.askPersonalData3": "Add fields for name and email", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "Enable calendar view", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "Learn more", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "Get a visual overview of your project timelines in our calendar view. Quickly identify which projects and phases are starting or ending soon and require action.", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "Understand what is happening and when", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "All questions are shown on the PDF. However, the following are not currently supported for import via FormSync: Images, Tags and File Upload.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "All questions are shown on the PDF. However, the following are not currently supported for import via FormSync: Mapping questions (drop pin, draw route and draw area), ranking questions, matrix questions and file upload questions.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "End of the form", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Start of the form", "app.containers.Admin.projects.all.components.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.components.draft": "Nacrt", "app.containers.Admin.projects.all.components.manageButtonLabel": "Upravljajte", "app.containers.Admin.projects.all.copyProjectButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.copyProjectError": "Do<PERSON><PERSON> je do greške prilikom kopiranja projekta, pokušajte ponovo kasnije.", "app.containers.Admin.projects.all.customiseEnd": "Customise the end of the form.", "app.containers.Admin.projects.all.customiseStart": "Customise the start of the form.", "app.containers.Admin.projects.all.deleteFolderButton1": "Izbriši fasciklu", "app.containers.Admin.projects.all.deleteFolderConfirm": "Da li ste sigurni da želite da obrišete ovaj folder? Svi projekti u okviru njega će takođe biti obrisani. Ova aktivnost se ne može poništiti.", "app.containers.Admin.projects.all.deleteFolderError": "<PERSON><PERSON><PERSON> je do problema prilikom uklanjanja ovog foldera. Molimo vas pokušajte kasnije.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Izbriši projekat", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Da li ste sigurni da želite da obrišete ovaj projekat? Ova aktivnost se ne može poništiti.", "app.containers.Admin.projects.all.deleteProjectError": "<PERSON><PERSON><PERSON> je do greške prilikom brisanja ovog projekta, molimo vas pokušajte kasnije.", "app.containers.Admin.projects.all.exportAsPDF1": "Download PDF form", "app.containers.Admin.projects.all.itIsAlsoPossible1": "You can combine online and offline responses. To upload offline responses, go to the 'Input manager' tab of this project, and click 'Import'.", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "You can combine online and offline responses. To upload offline responses, go to the 'Survey' tab of this project, and click 'Import'.", "app.containers.Admin.projects.all.logicNotInPDF": "Survey logic will not be reflected in the downloaded PDF. Paper respondents will see all survey questions.", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "Search folders", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "All folders have been loaded", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Folder", "app.containers.Admin.projects.all.new.Folders.Table.managers": "Managers", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# project} other {# projects}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Project start date", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Discoverability", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "Hidden", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "Public", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Folders", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filter by the current phase participation method", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "Ideation", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Information", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Participation method", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Document annotation", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Common ground", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "Survey", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "Poll", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Proposals", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Volunteering", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Voting", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "Collecting data", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informing", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "Not started", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Participation state", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Past", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "Pending approval", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "Search projects", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Alphabetically (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Alphabetically (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "Manager", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Phase starting or ending soon", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc": "Recently created (new-old)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc": "Recently created (old-new)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "Recently viewed", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Admins", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Groups", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Visibility", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "Public", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "Add filter", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "Clear", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "No more filters to add", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Admins", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "All projects have been loaded", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "Anyone", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Archived", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "Current phase:", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d left", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d to start", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Discoverability:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Draft", "app.containers.Admin.projects.all.new.Projects.Table.end": "End", "app.containers.Admin.projects.all.new.Projects.Table.ended": "Ended", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "Ends today", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Groups", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "Hidden", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "Loading more…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "Manager", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}mo left", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}mo to start", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Next phase:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "Not assigned", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Phase", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "Pre-launch", "app.containers.Admin.projects.all.new.Projects.Table.project": "Project", "app.containers.Admin.projects.all.new.Projects.Table.public": "Public", "app.containers.Admin.projects.all.new.Projects.Table.published": "Published", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Scroll down to load more", "app.containers.Admin.projects.all.new.Projects.Table.start": "Start", "app.containers.Admin.projects.all.new.Projects.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Status:", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "This column uses cached participant data. To see the latest numbers, check the \"Participants\" tab of the project.", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Visibility", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Visibility:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} groups", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} managers", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}y left", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y to start", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Current phase: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} days left", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Folder: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "No current phase", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "No end date", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "No phases", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Phase {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Phases:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Project", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Start date: {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "Arrange projects", "app.containers.Admin.projects.all.new.calendar": "Calendar", "app.containers.Admin.projects.all.new.folders": "Folders", "app.containers.Admin.projects.all.new.projects": "Projects", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "Failed to load timeline.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Project has no end date", "app.containers.Admin.projects.all.new.timeline.project": "Project", "app.containers.Admin.projects.all.notes": "Notes", "app.containers.Admin.projects.all.personalDataExplanation5": "This option will add the first name, last name, and email fields to the exported PDF. Upon uploading the paper form, we will use that data to auto-generate an account for the offline survey respondent.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "AI Summary", "app.containers.Admin.projects.project.analysis.Comments.comments": "Comments", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "Comment summary is available when there are 5 or more comments.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Summarize comments", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {Refresh} =1 {1 new comment} other {# new comments}}", "app.containers.Admin.projects.project.analysis.aiSummary": "AI summary", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "This is AI-generated content. It may not be 100% accurate. Please review and cross-reference with the actual inputs for accuracy. Be aware that the accuracy is likely to improve if the number of selected inputs is reduced.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Email notifications only sent to participants", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "Hidden", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "Not indexed by search engines", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Not visible on the homepage or widgets", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Only accessible via direct URL", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "Public", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "Select how discoverable this project is.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "This project is visible to everyone who has access, and will appear on the homepage and in the widgets.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "This project will be hidden from the wider public, and will only be visible to those who have the link.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Who can find this project?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Open AI analysis", "app.containers.Admin.projects.project.ideas.analysisText2": "Explore AI-powered summaries and view individual submissions.", "app.containers.Admin.projects.project.ideas.importInputs": "Import", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "After creating a report, you can choose to share it with the public once the phase starts.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Create a more complex page for information sharing", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Create a report to:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "Create a report", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Create a report for a past phase, or start from scratch.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "This report is not public. To make it public, enable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "This phase has started, but the report is not public yet. To make it public, enable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Start with a phase template", "app.containers.Admin.projects.project.information.ReportTab.report": "Report", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Share results of a past survey or ideation phase", "app.containers.Admin.projects.project.information.ReportTab.visible": "Visible", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "This report will be public as soon as the phase starts. To make it not public, disable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "This report is currently public. To make it not public, disable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Are you sure you want to delete this report? This action cannot be undone.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Add to phase", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "You need to consent to this before you can continue", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "The form can be downloaded here.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "The uploaded form was created with the \"Personal data\" section", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Form language", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "I hereby consent to processing this file using the Google Cloud Form Parser", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Import Excel file", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Please upload a file to continue", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "The template can be downloaded here.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Upload", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Upload a completed <b>Excel file</b> (.xlsx). It must use the template provided for this project. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Upload a <b>PDF file of scanned forms</b>. It must use a form printed from this phase. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Use this email for the new user", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Enter a valid email to create a new account", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "A new account will be created for the author with these details. This input will be added to it.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "First name", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Last name", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Please enter an email address and/or a first name and last name to assign this input to an author. Or uncheck the consent box.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "There is already an account associated with this email. This input will be added to it.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "User consent (create user account)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "Approve", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Approve all inputs", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Author:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "Email:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Errors occurred during the import and some inputs have not imported. Please correct the errors and re-import any missing inputs.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Invalid form data. Check the form above for errors.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Import Excel file (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Import", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Import scanned forms (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Import scanned forms", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "Imported inputs", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importing. This process may take a few minutes.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "This input was imported anonymously.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} inputs have been imported and require approval.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} inputs could not be approved. Please check each input for validation issues and confirm individually.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Locale:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Nothing to review yet. Click \"{importFile}\" to import a PDF file containing scanned input forms or an Excel file containing inputs.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Nothing to review yet. Click \"{importFile}\" to import an Excel file containing inputs.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "Input", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "Page", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Cannot display the imported file. Imported file viewing is only available for PDF imports.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Phase:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "The selected phase cannot contain inputs. Please select another one.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "This project does not contain any phases that can contain ideas.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "Please select to which phase you want to add these inputs.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Input importer", "app.containers.Admin.projects.project.participation.comments": "Comments", "app.containers.Admin.projects.project.participation.inputs": "Inputs", "app.containers.Admin.projects.project.participation.participantsTimeline": "Participants timeline", "app.containers.Admin.projects.project.participation.reactions": "Reactions", "app.containers.Admin.projects.project.participation.selectPeriod": "Select period", "app.containers.Admin.projects.project.participation.usersByAge": "Users by age", "app.containers.Admin.projects.project.participation.usersByGender": "Users by gender", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "Required", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "Add a question", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "The ability to add or edit user fields at phase level is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} options", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "Field status", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "These questions will be added as the last page of the survey form, because 'Show fields in survey?' has been selected in phase settings.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "No extra questions will be asked.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Optional", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Optional - always enabled because referenced by group", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "Remove field", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Required - always enabled because referenced by group", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Authenticate with {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Complete the extra questions below", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Confirm your email", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Data returned from verification method:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Enter first name, last name, email and password", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "Enter your email", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "How recently should users be verified?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Identity verification with {verificationMethod} (based on user group)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "No actions are required to participate", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Use smart groups to restrict participation based on the verified data listed above", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Users must have been verified in the last 30 minutes.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Users must have been verified in the last {days} days.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "In the last 30 days", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "In the last 30 minutes", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "In the last 7 days", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "Once is enough", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Verified fields:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} verification", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Account creation", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Participants need to create a full account with their name, confirmed email and password.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Participants need to create a full account with their name, email and password.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Authentication", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "Edit", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Email confirmation", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Participants need to confirm their email with a one-time code.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Advanced spam detection", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "This feature helps prevent duplicate survey submissions from logged-out users by analyzing IP addresses and device data. While not as precise as requiring login, it can help to reduce the number of duplicate responses.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Note: On shared networks (like offices or public Wi-Fi), there is a small chance that different users could be flagged as duplicates.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Enable advanced spam detection", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Extra questions asked to participants", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "None", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Anyone can participate without signing up or logging in.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Reset extra questions and groups", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Restrict participation to user group(s)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO verification", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Participants need to verify their identify with {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Open AI analysis", "app.containers.Admin.projects.project.survey.allFiles": "All files", "app.containers.Admin.projects.project.survey.allResponses": "All responses", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Accuracy: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "There was an error generating the AI summary. Please try to regenerate it below.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Open AI analysis", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "<PERSON><PERSON> summaries for this question", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "inputs selected", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Open analysis actions", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} new responses", "app.containers.Admin.projects.project.survey.analysis.regenerate": "Regenerate", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Show AI insights", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "You can summarise a maximum of 30 inputs at a time on your current plan. Talk to your GovSuccess Manager or admin to unlock more.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Select related questions for analysis", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "Do you want to include any other related questions in your analysis of {question}?", "app.containers.Admin.projects.project.survey.cancel": "Cancel", "app.containers.Admin.projects.project.survey.consentModalButton": "Continue", "app.containers.Admin.projects.project.survey.consentModalCancel": "Cancel", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "I agree to using OpenAI as a data processor for this project", "app.containers.Admin.projects.project.survey.consentModalText1": "By continuing you agree to the using OpenAI as a data processor for this project.", "app.containers.Admin.projects.project.survey.consentModalText2": "The OpenAI APIs power the automated text summaries and parts of the automated tagging experience.", "app.containers.Admin.projects.project.survey.consentModalText3": "We only send what users wrote in their surveys, ideas and comments to the OpenAI APIs, never any information from their profile.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI will not use this data for further training of its models. More information on how OpenAI handles data privacy can be found {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "here", "app.containers.Admin.projects.project.survey.consentModalTitle": "Before you continue", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "You can't enter the analysis before you have edited the form", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Delete", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Are you sure you want to delete this analysis? This action cannot be undone.", "app.containers.Admin.projects.project.survey.explore": "Explore", "app.containers.Admin.projects.project.survey.followUpResponses": "Follow up responses", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> average", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Export as GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Export the responses to this question as a GeoJSON file. For each GeoJSON Feature, all of the related respondent's survey responses will be listed in that Feature's 'properties' object.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "Hide details", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} respondents} one {{respondentCount} respondent} other {{respondentCount} respondents}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "View details", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} choices} one {{numberChoices} choice} other {{numberChoices} choices}}", "app.containers.Admin.projects.project.survey.heatMap": "Heat map", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Learn more about heat maps generated using Esri Smart Mapping.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "The heat map is generated using Esri Smart Mapping. Heat maps are useful when there is a large amount of data points. For fewer points, it may be better to look at only the location points directly. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Heat map view", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Hidden by logic", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "When a user selects this answer logic skips all pages until page {pageNumber} ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "When a user selects this answer logic skips to the survey end ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "Logic on this page skips all pages until page {pageNumber} ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Logic on this page skips to the survey end ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.newAnalysis": "New analysis", "app.containers.Admin.projects.project.survey.nextInsight": "Next insight", "app.containers.Admin.projects.project.survey.openAnalysis": "Open AI analysis", "app.containers.Admin.projects.project.survey.otherResponses": "Other responses", "app.containers.Admin.projects.project.survey.page": "Page", "app.containers.Admin.projects.project.survey.previousInsight": "Previous insight", "app.containers.Admin.projects.project.survey.responses": "Responses", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "The number of responses for this page is lower than the total number of survey responses because some respondents will not have seen this page because of logic in the survey.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "The number of responses for this question is lower than the total number of survey responses because some respondents will not have seen this question because of logic in the survey.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Sentiment scale", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Instantly summarise all your responses.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Talk to your data in natural language.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Get references to individual responses from AI generated summaries.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Check our {link} for a full overview.", "app.containers.Admin.projects.project.survey.upsell.button": "Unlock AI analysis", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "support article", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Analyse data faster with AI", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.Admin.projects.project.survey.viewAnalysis": "View", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Explore AI-powered summaries and view individual submissions.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Select period", "app.containers.Admin.projects.project.traffic.trafficSources": "Traffic sources", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "We have changed the way we collect and display visitor data. As a result, visitor data is more accurate and more types of data are available, while still being GDPR compliant. We only started collecting this new data in November 2024, so before that no data is available.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Visitors timeline", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Phase report", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Add some text about the phase", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Ovo je neki tekst. Možete ga uređivati i formatirati pomoću uređivača na tabli sa desne strane.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Učesnici", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Rezultati projekta", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Rezime izveštaja", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Dodaj cilj projekta, korišćene metode učešća i ishod", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "This report contains unsaved changes. Please save before printing.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Title is already taken", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "Compared to previous {days} days", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Hide statistics", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Participation rate", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Show comparison with last period", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "You need to select a date range first.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "Comments", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Inputs", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Participation", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Show comments", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "Show inputs", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Show votes", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "Votes", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Demographics", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Registration date range", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Registration field", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Unknown", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Users: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Active", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Archived", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "Finished", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "Open-ended", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "Planned", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projects", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Publication status", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Objavljeno", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "The data for this widget is missing. Reconfigure or delete it to be able to save the report.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Community Monitor Health Score", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "Quarter", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "Year", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "No appropriate phases found in this project", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "No phase selected. Please select a phase first.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "<PERSON><PERSON> projekta", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "No project selected. Please select a project first.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "You cannot duplicate this report because it contains data that you don't have access to.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "You cannot edit this report because it contains data that you don't have access to.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Are you sure you want to delete \"{reportName}\"? This action cannot be undone.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Are you sure you want to delete this report? This action cannot be undone.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Izbriši", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Duplicate", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Izmenite", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "Modified {days, plural, no {# days} one {# day} other {# days}} ago", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Došlo je do greške prilikom pokušaja kreiranja ovog izveštaja. Pokušajte ponovo kasnije.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Započni praznom stranicom", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Start with a Community Monitor template", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Naziv izveštaja", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "Create a report", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Customise your report and share it with internal stakeholders or community as a PDF file.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Kreiraj izveštaj", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Create your first report", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "No project selected", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Start with a platform template", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Odštampaj u PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Započni šablonom projekta", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Quarter {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Šablon izveštaja", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Izveštaj sa ovim naslovom već postoji. Izaberite drugačiji naslov.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Select quarter", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Select year", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "<PERSON><PERSON><PERSON> kao <PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Radi delje<PERSON> sa svima, odštampajte izveštaj kao PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Deli kao veb vezu", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "<PERSON>va veb veza dostupna je samo korisnicima koji su administratori.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.contactToAccess": "Кreiranje prilagođenog izveštaja deo je premium licence. Za više informacija o tome obratite se svom menadžeru za GovSuccess.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "All reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Community monitor reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "These are reports are related to the Community Monitor. Reports are automatically generated every quarter.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Kreiraj izveštaj", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Prilagodite svoj izveštaj i podelite ga sa internim zainteresovanim stranama ili zajednicom putem veb veze.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Your reports will appear here.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Search reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Progress reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "These are reports created by your Government Success Manager", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "Your reports", "app.containers.Admin.reporting.deprecated": "DEPRECATED", "app.containers.Admin.reporting.helmetDescription": "Stranica sa izveštajima administratora", "app.containers.Admin.reporting.helmetTitle": "Izveštavanje", "app.containers.Admin.reporting.printPrepare": "Priprema za štampu...", "app.containers.Admin.reporting.reportBuilder": "Prijavi alatku za izradu", "app.containers.Admin.reporting.reportHeader": "Zaglavlje izveštaja", "app.containers.Admin.reporting.warningBanner3": "Graphs and numbers in this report only update automatically on this page. Save the report to update them on other pages.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Common ground", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Ideation", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Information", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Methods Used", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "Survey", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "Poll", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "Previous {days} days: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Proposals", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "External survey", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Volunteering", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Voting", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Chart", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "Table", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "View", "app.containers.Admin.surveyFormTab.downloads": "Downloads", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "Duplicate another survey", "app.containers.Admin.surveyFormTab.editSurveyForm": "Edit survey form", "app.containers.Admin.surveyFormTab.inputFormDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required.", "app.containers.Admin.surveyFormTab.surveyForm": "Survey form", "app.containers.Admin.tools.apiTokens.createTokenButton": "Креирајте нови токен", "app.containers.Admin.tools.apiTokens.createTokenCancel": "Поништити, отказати", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Your token has been created. Please copy the {secret} below and store it safely.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Направите нови токен који ћете користити са нашим јавним АПИ-јем.", "app.containers.Admin.tools.apiTokens.createTokenError": "Наведите име за свој токен", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Креирајте токен", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>Important!</b> You can only copy this {secret} once. If you close this window you will not be able to see it again.", "app.containers.Admin.tools.apiTokens.createTokenName": "Име", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Дајте свом токену име", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Ваш токен је креиран", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Близу", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "Copy {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Copied!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Креирајте нови токен", "app.containers.Admin.tools.apiTokens.createdAt": "Цреатед", "app.containers.Admin.tools.apiTokens.delete": "Избриши токен", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Да ли сте сигурни да желите да избришете овај токен?", "app.containers.Admin.tools.apiTokens.description": "Управљајте својим АПИ токенима за наш јавни АПИ. За више информација погледајте нашу {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Последње коришћено", "app.containers.Admin.tools.apiTokens.link": "АПИ документација", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Име", "app.containers.Admin.tools.apiTokens.noTokens": "Још увек немате ниједан токен.", "app.containers.Admin.tools.apiTokens.title": "Јавни АПИ токени", "app.containers.Admin.tools.esriDisabled": "The Esri integration is an add-on feature. Contact your GovSuccess Manager if you want more information on this.", "app.containers.Admin.tools.esriIntegration2": "Esri integration", "app.containers.Admin.tools.esriIntegrationButton": "Enable <PERSON>", "app.containers.Admin.tools.esriIntegrationDescription3": "Connect your Esri account and import data from ArcGIS Online directly into your mapping projects.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri logo", "app.containers.Admin.tools.esriKeyInputDescription": "Add your Esri API key to allow importing your map layers from ArcGIS Online in the map tabs in projects.", "app.containers.Admin.tools.esriKeyInputLabel": "Esri API key", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Paste API key here", "app.containers.Admin.tools.esriMaps": "Esri Maps", "app.containers.Admin.tools.esriSaveButtonError": "There was an error saving your key, please try again.", "app.containers.Admin.tools.esriSaveButtonSuccess": "API key saved", "app.containers.Admin.tools.esriSaveButtonText": "Save key", "app.containers.Admin.tools.learnMore": "Сазнајте више", "app.containers.Admin.tools.managePublicAPIKeys": "Управљајте АПИ кључевима", "app.containers.Admin.tools.manageWidget": "Управљај виџетом", "app.containers.Admin.tools.manageWorkshops": "Управљајте радионицама", "app.containers.Admin.tools.powerBIAPIImage": "Power BI image", "app.containers.Admin.tools.powerBIDescription": "Use our plug & play Power BI Templates to access Go Vocal data in your Microsoft Power BI Workspace.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI is not part of your license. Contact your GovSuccess Manager if you want more info on this.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Download templates", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "If you intend to use your Go Vocal data within a Power BI data flow, this template will allow you to set up a new data flow that connects to your Go Vocal data. Once you have downloaded this template you must first find and replace the following strings ##CLIENT_ID## and ##CLIENT_SECRET## in the template with your public API credentials before uploading to PowerBI.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Download data flow template", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Dataflow template", "app.containers.Admin.tools.powerBITemplates.intro": "Note: To use either of these Power BI templates, you must first {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "create a set of credentials for our public API", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "This template will create a Power BI report based on your Go Vocal data. It will set up all the data connections to your Go Vocal platform, create the data model and some default dashboards. When you open the template in Power BI you will be prompted to enter your public API credentials. You will also need to enter the Base Url for your platform, which is: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Download reporting template", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Report template", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Further details about using your Go Vocal data in Power BI can be found in our {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "support article", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Power BI templates", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "Управљајте акредитивима да бисте креирали прилагођене интеграције на нашем јавном АПИ-ју.", "app.containers.Admin.tools.publicAPIDisabled1": "The public API is not part of your current license. Contact your GovSuccess Manager if you want more info on this.", "app.containers.Admin.tools.publicAPIImage": "Слика јавног АПИ-ја", "app.containers.Admin.tools.publicAPITitle": "Јавни АПИ приступ", "app.containers.Admin.tools.toolsLabel": "<PERSON>ла<PERSON><PERSON>", "app.containers.Admin.tools.widgetDescription": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.Admin.tools.widgetImage": "Слика виџета", "app.containers.Admin.tools.widgetTitle": "Видгет", "app.containers.Admin.tools.workshopsDescription": "Одржавајте видео састанке уживо, водите симултане групне дискусије и дебате. Прикупите мишљење, гласајте и постигните консензус, баш као што бисте то урадили ван мреже.", "app.containers.Admin.tools.workshopsImage": "Слика радионица", "app.containers.Admin.tools.workshopsSupportLink": "хттпс://суппорт.цитизенлаб.цо/ен/артицлес/4155778-сеттинг-уп-ан-онлине-ворксхоп", "app.containers.Admin.tools.workshopsTitle": "Радионице за разматрање на мрежи", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "ukupno na platformi", "app.containers.AdminPage.DashboardPage._blank": "nepoznato", "app.containers.AdminPage.DashboardPage.allGroups": "Sve grupe", "app.containers.AdminPage.DashboardPage.allProjects": "Svi projekti", "app.containers.AdminPage.DashboardPage.allTime": "Sve vreme", "app.containers.AdminPage.DashboardPage.comments": "Komentari", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Komentari", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Osnovni skup podataka je obavezan za merenje reprezentacije korisnika platforme.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Trenutno radimo na komandnoj tabli {fieldName} i ona će uskoro biti dostupna", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {#arikal je} other {# artikli su}} sakriveni na ovom grafikonu. Promenite na {tableViewLink} za pregled svih podataka.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} za registraciju korisnika", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "Ukl<PERSON><PERSON><PERSON> je {known} od {total} korisnika ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "<PERSON><PERSON><PERSON><PERSON> jo<PERSON> {numberOfHiddenItems}", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Opcionalno", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Navedite osnovni skup podataka.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Rezultat reprezentativnosti:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Ovaj rezultat prikazuje koliko tačno podaci o korisnicima na platformi odražavaju ukupnu populaciju. Saznajte više o tome na {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Obavezno", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Prosledi osnovne podatke", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "tabel<PERSON>ni pregled", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Ukupna populacija", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "<PERSON><PERSON><PERSON> grupu", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} i stariji", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Starosna g<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Starosna grupa(-e) za {upperBound} i starije nije obuhvaćena.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Starosna grupa {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Starosne grupe", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "i stariji", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Primeni primer grupisanja", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Obriši sve", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "Od", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Postavite starosne grupe tako da budu usklađene sa vašim osnovnim skupom podataka.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Opseg", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Sačuvajte", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "<PERSON>a", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Uredi starosne grupe", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Ova stavka neće biti uračunata.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "Pogledajte manje", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "<PERSON><PERSON><PERSON> još {numberOfHiddenItems}...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Osnovni mesec (opcionalno)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "<PERSON><PERSON><PERSON> grup<PERSON> (godina rođenja)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Zav<PERSON>š<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.default": "Podra<PERSON>mevan<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Popunite sve omogućene opcije ili onemogućite opcije koje želite da izostavite iz grafikona. Potrebno je popuniti barem jednu opciju.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Nepotpuno", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "<PERSON><PERSON>pan broj rezidenata", "app.containers.AdminPage.DashboardPage.components.Field.options": "Solucije", "app.containers.AdminPage.DashboardPage.components.Field.save": "Sačuvajte", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Sačuvan<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Najpre {setAgeGroupsLink} da biste počeli sa unosom osnovnih podataka.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "postavi starosne grupe", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Prosečno vreme odgovora: {days} dana", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Prosečan broj dana za odgovor", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "date su povratne informacije", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Status unosa", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Unosi po statusu", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Zvanični ažurirani sadržaj", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Procenat unosa", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Vreme odgovora", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Status je promenjen", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Ukupno", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Izmeni osnovne podatke", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "kako mi izračunavamo rezultate reprezentativnosti", "app.containers.AdminPage.DashboardPage.continuousType": "Bez vremenskih okvira", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Ukupno kumulativno", "app.containers.AdminPage.DashboardPage.customDateRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.day": "dan", "app.containers.AdminPage.DashboardPage.false": "netačno", "app.containers.AdminPage.DashboardPage.female": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Топ 5 уноса по реакцијама", "app.containers.AdminPage.DashboardPage.fromTo": "od {from} do {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Pregled aktivnosti na platformi", "app.containers.AdminPage.DashboardPage.helmetTitle": "Administratorski panel", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Izaberite resurs za prikaz po projektu", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Izaberite resurs za prikaz po temi", "app.containers.AdminPage.DashboardPage.inputs1": "Inputs", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Inputs by status", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Izaberi korisničku grupu", "app.containers.AdminPage.DashboardPage.male": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.month": "mesec", "app.containers.AdminPage.DashboardPage.noData": "Nema podataka za prikaz", "app.containers.AdminPage.DashboardPage.noPhase": "Za ovaj projekat nisu podešene faze", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Број учесника који су објавили доприносе, реаговали или коментарисали.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "Не свиђа ми се", "app.containers.AdminPage.DashboardPage.numberOfLikes": "Свиђа", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Тоталне реакције", "app.containers.AdminPage.DashboardPage.overview.management": "Rukovodstvo", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projekti i učešće", "app.containers.AdminPage.DashboardPage.overview.showLess": "Prika<PERSON><PERSON> manje", "app.containers.AdminPage.DashboardPage.overview.showMore": "Prikaži više", "app.containers.AdminPage.DashboardPage.participants": "Participants", "app.containers.AdminPage.DashboardPage.participationPerProject": "Učešće po projektu", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Učešće po temi", "app.containers.AdminPage.DashboardPage.perPeriod": "U {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Prethodnih 30 dana", "app.containers.AdminPage.DashboardPage.previous90Days": "Prethodnih 90 dana", "app.containers.AdminPage.DashboardPage.previousWeek": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.previousYear": "<PERSON><PERSON><PERSON><PERSON> godine", "app.containers.AdminPage.DashboardPage.projectType": "Tip projekta: {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "Реакције", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Ovaj osnovni skup podataka neophodan je za izračunavanje reprezentativnosti korisnika platforme u poređenju sa ukupnom populacijom.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Navedite osnovni skup podataka.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Pogledajte u kojoj su meri korisnici vaše platforme reprezentativni u odnosu na ukupnu populaciju - na osnovu podataka sakupljenih za vreme registracije korisnika. Saznajte više o tome na {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Pogledajte u kojoj su meri korisnici vaše platforme reprezentativni u odnosu na ukupnu populaciju - na osnovu podataka sakupljenih za vreme registracije korisnika.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Reprezentacija zajednice", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Nazad na komandnu tablu", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Nijedno od omogućenih polja za registraciju trenutno nije podržano.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Ovde možete da prikažete/sakrijete stavke na komandnoj tabli i unesete osnovne podatke. Ovde će se pojaviti samo omogućena polja za {userRegistrationLink}.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Izmeni osnovne podatke", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "registracija korisnika", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Prosledi osnovne podatke", "app.containers.AdminPage.DashboardPage.resolutionday": "u Dani<PERSON>", "app.containers.AdminPage.DashboardPage.resolutionmonth": "u Mesecima", "app.containers.AdminPage.DashboardPage.resolutionweek": "u Nedeljama", "app.containers.AdminPage.DashboardPage.selectProject": "<PERSON><PERSON><PERSON><PERSON> projekat", "app.containers.AdminPage.DashboardPage.selectedProject": "trenutni filter projekta", "app.containers.AdminPage.DashboardPage.selectedTopic": "trenutni filter teme", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Saznajte šta se dešava na vašoj platformi", "app.containers.AdminPage.DashboardPage.tabOverview": "Pregled", "app.containers.AdminPage.DashboardPage.tabReports": "Projekti", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Reprezentacija", "app.containers.AdminPage.DashboardPage.tabUsers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.timelineType": "Vremenski okviri", "app.containers.AdminPage.DashboardPage.titleDashboard": "Komand<PERSON> panel", "app.containers.AdminPage.DashboardPage.total": "Ukupno", "app.containers.AdminPage.DashboardPage.totalForPeriod": "<PERSON><PERSON><PERSON> {period}", "app.containers.AdminPage.DashboardPage.true": "tačno", "app.containers.AdminPage.DashboardPage.unspecified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.users": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Korisnici po starosti", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Korisnici prema geografskoj oblasti", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Koris<PERSON><PERSON> prema polu", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Registracije", "app.containers.AdminPage.DashboardPage.week": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Saveti za izbor favicon slike: odaberite jednostavnu sliku, s obzirom da će veličina njenog prikaza biti veoma mala. Sliku bi trebalo sačuvati u PNG formatu, u obliku kvadrata sa transparentno pozadinom (ili belom). Vaš favicon bi trebao da bude postavljen samo jednom, jer će njegove izmene zahtevati određenu tehničku podršku. ", "app.containers.AdminPage.FaviconPage.save": "Sačuvajte", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "<PERSON><PERSON><PERSON> je do greš<PERSON>, molimo pokušajte kasnije", "app.containers.AdminPage.FaviconPage.saveSuccess": "Uspešno!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Vaše izmene su sačuvane.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Izbriši", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Folder menadžeri mogu menjati opis foldera, kreirati nove projekte u okviru foldera i imati upravljačka prava nad svim projektima koji se nalaze u fiolderu. Oni ne mogu brisati projekte i nemaju pristup projektima koji se nalaze van njihovog foldera. Možete {projectManagementInfoCenterLink} kako biste pronašli više informacija o ovlašćenjima projektnih menadžera.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Nisu pronađena <PERSON>", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "posetite naš Centar za podršku", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Pretražite korisnike", "app.containers.AdminPage.FoldersEdit.addToFolder": "Dodajte u folder", "app.containers.AdminPage.FoldersEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Obrišite ovaj folder", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Opis", "app.containers.AdminPage.FoldersEdit.draftStatus": "Nacrt", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Dodajte fajlove u folder", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Fajlovi ne bi trebalo da budu veći od 50Mb. Dodati fajlovi će se prikazivati na stranici foldera.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Opisi", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "U ovom folderu nema projekata. Vratite se na karticu Projekti kako biste kreirali i dodali projekte. ", "app.containers.AdminPage.FoldersEdit.folderName": "<PERSON><PERSON> fascikle", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Slika u zaglavlju", "app.containers.AdminPage.FoldersEdit.multilocError": "Sva polja za tekst moraju biti popunjena na svakom jeziku.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Nema projekata koje možete dodati u ovaj folder.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Slika kartice foldera", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Dozvole", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Projekti foldera", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Podešavanja", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projekti dodati u ovaj folder", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Projekti koje možete dodati u ovaj folder", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Odaberite da li je ovaj folder \"nacrt\", \"objavljen\" ili \"arhiviran\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Objavljeno", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Ukloni iz foldera", "app.containers.AdminPage.FoldersEdit.save": "Sačuvajte", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Do<PERSON><PERSON> je do greške. Molimo pokušajte kasnije.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Uspešno!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Vaše izmene su sačuvane.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Kratak opis", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "Prikazuje se na početnoj strani", "app.containers.AdminPage.FoldersEdit.statusLabel": "Status publikacije", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Objasnite kako su projekti međusobno povezani, definišite vizuelni identitet i podelite informacije.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Objasnite kako su projekti međusobno povezani, definišite vizuelni identitet i podelite informacije.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "All text fields must be filled in.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "<PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Kreirajte novi folder", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Podešavanja", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Pogledaj folder", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Prilagodite sliku banera heroja i tekst.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "<PERSON><PERSON>", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "<PERSON><PERSON><PERSON><PERSON><PERSON> heroja", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Inspiration Hub is a place where you can find inspiration for your projects by browsing through projects on other platforms.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Uredite uslove i odredbe i politiku privatnosti na vašoj platformi. Druge stranice, uključujući stranice About i Česta pitanja, mogu da se urede na kartici {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Politike platforme", "app.containers.AdminPage.PagesEdition.privacy-policy": "Politika privatnosti", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.confirmation.description": "This action cannot be undone.", "app.containers.AdminPage.Project.confirmation.no": "Cancel", "app.containers.AdminPage.Project.confirmation.title": "Are you sure you want to reset all participation data?", "app.containers.AdminPage.Project.confirmation.yes": "Reset all participation data", "app.containers.AdminPage.Project.data.descriptionText1": "Clear ideas, comments, votes, reactions, survey responses, poll responses, volunteers and event registrants. In the case of voting phases, this action will clear the votes but not the options.", "app.containers.AdminPage.Project.data.title": "Clear all participation data from this project", "app.containers.AdminPage.Project.resetParticipationData": "Reset all participation data", "app.containers.AdminPage.Project.settings.accessRights": "Access rights", "app.containers.AdminPage.Project.settings.back": "Back", "app.containers.AdminPage.Project.settings.data": "Data", "app.containers.AdminPage.Project.settings.description": "Description", "app.containers.AdminPage.Project.settings.events": "Events", "app.containers.AdminPage.Project.settings.general": "General", "app.containers.AdminPage.Project.settings.projectTags": "Project tags", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Lista projekata na ovoj platformi", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Komandna tabla sa projektima", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Kreirajte novi projekat ili upravljajte sa postojećim.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projekti", "app.containers.AdminPage.ProjectDashboard.published": "Objavljeno", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Zatvori tablu sa postavkama", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Center", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "Full width", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "Left", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "Button alignment", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "Right", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Button text", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Enter text for the button", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "Primary", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Button type", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Secondary", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "Button URL", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Enter a URL for the button", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "<PERSON><PERSON><PERSON> kolone", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Opis", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Opis početne stranice", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Ovaj opis se prikazuje na pregledu projekata u okviru početne stranice.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Ovaj opis se prikazuje na stranici za informacije u okviru projekta.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Do<PERSON><PERSON> je do greške. Molimo pokušajte kasnije.", "app.containers.AdminPage.ProjectDescription.preview": "Prikaz", "app.containers.AdminPage.ProjectDescription.save": "Sačuvajte", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Vaše izmene su sačuvane.", "app.containers.AdminPage.ProjectDescription.saved": "Sačuvano!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Saopštite korisnicima temu projekta i podstaknite ih da učestvuju u njemu.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Opis projekta", "app.containers.AdminPage.ProjectDescription.whiteSpace": "Belina", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Uključ<PERSON> grani<PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "Vertikalna visina", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Srednje", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Malo", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Prekid", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Podrazumevana širina centralne tačke mape. Prihvata vrednost između -90 i 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Podrazumevana geografska dužina centralne tačke mape. Prihvata vrednost između -90 i 90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Izmeni sloj mape", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Izmeni sloj", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Do<PERSON><PERSON> je do greške. Molimo pokušajte kasnije. ", "app.containers.AdminPage.ProjectEdit.MapTab.here": "ovde", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Uvezi GeoJSON fajl", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Podrazumevana geografska širina", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Boja sloja", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Sve funkcije u sloju će biti stilizovane ovom bojom. Ova boja će takođe prebrisati sve postojeće stilove u vašoj GeoJSON datoteci.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Opciono izaberite ikonu koja se prikazuje u oznakama. Kliknite na {url} da biste videli listu ikona koje možete izabrati.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Naziv sloja", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Naziv ovoga sloja prikazan je na legendi mape", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Opis sloja", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Ovaj tekst se prikazuje kao savet pri prelasku kursorom preko sloja na mapi", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Slojevi mape", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "Trenutno podržavamo GeoJSON datoteke. Pročitajte {supportArticle} za savete o tome kako da konvertujete i stilizujete slojeve mape.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Podrazumevana geografska dužina", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Podrazumevani centar mape i zumiranje", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "The default center point and zoom level of the map. Manually adjust the values below, or click on the {button} button in the bottom left corner of the map to save the current center point and zoom level of the map as the default values.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Prilagodite prikaz mape, uključujući otpremanje i oblikovanje slojeva mape i postavljanje centra mape i nivoa zumiranja.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Konfiguracija mape", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "The map configuration is currently shared across phases, you can't create different map configurations per phase.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Uklonite sloj", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Sačuvajte", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Save zoom", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "podrška (tekst)", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Unnamed layer", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "<PERSON><PERSON>e", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Podrazumevani nivo zumiranja karte. Prihvata vrednost između 1 i 17, pri čemu je 1 potpuno zumirana na gore (čitav svet je vidljiv), a 17 je potpuno zumirana ka tlu (vidljivi su blokovi i zgrade)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Анонимизирајте све корисничке податке", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Сви уноси корисника у анкету биће анонимизовани пре него што буду снимљени", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Корисници ће и даље морати да се придржавају услова за учешће на картици приступа „Права приступа“. Подаци корисничког профила неће бити доступни у извозу података анкете.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "If you enable this option, user registration fields will be shown as the last page in the survey instead of as part of the signup process.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Demographic fields in survey form", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Show demographic fields in survey?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "Read more about how auto-sharing works in this article.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Auto-share results", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Voting results are shared on the platform and via email to participants when the phase ends. This ensures transparency by default.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Result sharing", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Додајте опцију одговора", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "Поништити, отказати", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Prekid", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Prekid", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Izbriši", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Izbriši", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Измени опцију одговора", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Сачувајте опције одговора", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Измените опције одговора", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "Уреди питање", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Izvezite rezultate ankete", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Maks<PERSON>lan broj izbora je veći od broja opcija", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Višestruki izbor", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "Bez opcija odgovora", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "U ovom obliku anketa se neće moći popunjavati. Sva pitanja moraju imati dostupne odgovore. ", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Samo jedna opcija odgovora", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Učesnici u anketi imaju samo jednu opciju odgovora", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Управљајте опцијама одговора за: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Ovde možete da kreirate anketna pitanja, postavite izbore odgovora za učesnike, odlučite da li želite da učesnici mogu da izaberu samo jedan odgovora (jedan izbor) ili više odgovora (višestruki izbor) i da izvezete rezultate ankete. U jednoj anketi možete postaviti više pitanja.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Sačuvajte", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "Sačuvajte", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "сачувати", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Podešavanja i rezultati ankete", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "<PERSON>g<PERSON><PERSON><PERSON> maksim<PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "<PERSON><PERSON><PERSON> povratne informacije, dodelite teme ili kopirajte postove za sledeću fazu projekta.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "Manage proposals, give feedback and assign topics.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "Result sharing is turned off.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Voting results won't be shared at the end of the phase unless you modify it in the phase setup.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "These results will be automatically shared once the phase ends. Modify the end date of this phase to change when the results are shared.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Izvezite rezultate ankete (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Results", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Ovde možete preuzeti rezultate Typeform ankete u formi Excel dokumenta.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "Survey form", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Re<PERSON><PERSON><PERSON> ankete", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Upitnik", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Pogledajte odgovore na anketu", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Dodajte povod", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Da li ste sigurni", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Opis", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Koristite ovo da volonterima pojasnite šta se od njih zahteva kao i šta mogu da očekuju.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Forma sadrži greške i nije mogla biti sačuvana.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Slika", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Izbriši", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Izmenite", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Povod predstavlja akciju ili aktivnost u kojoj građani mogu uzeti volontersko učešće.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Izmeni povod", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "<PERSON><PERSON><PERSON> opis", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Izvezi spisak volontera", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Povod predstavlja akciju ili aktivnost u kojoj građani mogu uzeti volontersko učešće.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "Novi povod", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Sačuvajte", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Ovde možete podesiti povode u kojima ljudi mogu volontirati i preuzeti njihov spisak.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Volontiranje", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {nema volontera} one {# volonter} other {#volontera}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "budget allocation", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Доделите буџет опцијама и замолите учеснике да изаберу своје жељене опције које се уклапају у укупни буџет.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Буџетска издвајања", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Allowing users to comment can bias the voting process.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "Credit", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Default view of options", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Actions for users", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Select what additional actions users can take.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Fixed amount", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "If left empty, this will default to \"vote\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "Learn more about when to use <b> {voteTypeDescription} </b> in our {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Maximum votes per option", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Maximum amount of votes", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "You can limit the number votes a user can cast in total (with a maximum of one vote per option).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "multiple votes per option", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Users are given an amount of tokens to distribute between options", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Multiple votes per option", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Number of votes per user", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Option analysis overview", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Options to vote on", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "Point", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "single vote per option", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Users can chose to approve any of the options", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "One vote per option", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Token", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Unlimited", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "What should a vote be called?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "E.g. tokens, points, carbon credits...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "E.g. token, point, carbon credit...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "Vote", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Сваки метод гласања има различите предконфигурације", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "На<PERSON><PERSON>н гласања", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "Начин гласања одређује правила како корисници гласају", "app.containers.AdminPage.ProjectEdit.addNewInput": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "You can add your project to an folder now, or do it later in the project settings", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Dozvoljene kategorije unosa", "app.containers.AdminPage.ProjectEdit.altText": "Alt text", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Anonimno glasanje", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "<PERSON>da je ovo <PERSON>, nemo<PERSON>će je videti ko je i za šta glasao. Korisnici i dalje moraju imati nalog i mogu glasati samo jednom.", "app.containers.AdminPage.ProjectEdit.approved": "Approved", "app.containers.AdminPage.ProjectEdit.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Archived projects are still visible, but do not allow further participation", "app.containers.AdminPage.ProjectEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Ova oblast ne može se izbrisati zato što se koristi za prikaz projekata na sledećoj prilagođenijoj stranici(ama). Potrebno je da prekinete vezu između oblasti i stranice ili da izbrišete stranicu pre nego što izbrišete oblast.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Sva područja", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Projekat će se prikazati u svakom filteru područja.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projekti se mogu filtrirati na početnoj stranici pomoću oblasti. Oblasti se mogu postaviti na {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "ovde", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Nema određene oblasti", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Projekat se neće prikazati kod filtriranja po području.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Izbor", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Projekat će se prikazati u izabranom filteru(-ima) područja.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "U listi", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "Most discussed", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Add survey content", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Submissions to this survey have started to come in. Changes to the survey may result in data loss and incomplete data in the exported files.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Survey successfully saved", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "Survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "View survey", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Select a voting method, and have users prioritize between a few different options.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Спроведите гласање или вежбу одређивања приоритета", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Kreirajte projekat na osnovu šablona", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>u an<PERSON>u", "app.containers.AdminPage.ProjectEdit.createInput": "Add new input", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Kreiraj anketu u okviru plafome", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Postavite anketu bez napuštanja platforme.", "app.containers.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON><PERSON><PERSON><PERSON> an<PERSON>", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Podesite anketu sa više odgovora", "app.containers.AdminPage.ProjectEdit.createProject": "New project", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Povežite Typeform, Google Form ili Enalyzer anketu.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Možete podesiti podrazumevajući redosled postova koji se prikazuju na centralnoj strani projekta.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Sortiranje", "app.containers.AdminPage.ProjectEdit.departments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Opis", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "This will enable or disable disliking, but liking will still be enabled. We recommend leaving this disabled unless you are carrying out an option analysis.", "app.containers.AdminPage.ProjectEdit.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Број несвиђања по учеснику", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Омогући несвиђање", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Прикупите повратне информације о документу", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Уградите интерактивни ПДФ и прикупите коментаре и повратне информације уз Конвеио.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Draft projects are hidden for all people except admins and assigned project managers.", "app.containers.AdminPage.ProjectEdit.draft": "Nacrt", "app.containers.AdminPage.ProjectEdit.draftStatus": "Nacrt", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Upravljajte", "app.containers.AdminPage.ProjectEdit.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Изаберите које партиципативне радње корисници могу да предузму.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Prilozi će biti prikazani na stranici sa informacijama o projektu.", "app.containers.AdminPage.ProjectEdit.filesTab": "Files", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Pronađite volontere", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Zatražite od učesnika da volontiraju u različitim akcijama i povodima.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "As a folder manager, you can choose a folder when creating the project, but only an admin can change it afterward", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Folder card image alternative text", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Odaberite folder u koji ćete dodati ovaj projekat:", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Prilagođeni sadržaj", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Počelo je prosleđivanje ovih obrazaca. Promene u obrascu mogu dovesti do gubitka podataka i nepotpunih podataka u izveženim datotekama.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Obrazac je us<PERSON>š<PERSON> sa<PERSON>", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Na osnovu šablona", "app.containers.AdminPage.ProjectEdit.generalTab": "General<PERSON>", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Header image alt text", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Slika u zaglavlju", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NEW", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Provide information to users, or use the report builder to share results on past phases.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Share information or results", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "prikupljajte unose i povratne informacije", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Креирајте или прикупљајте уносе, реакције и/или коментаре. Бирајте између различитих типова уноса: прикупљање идеја, анализа опција, питања и одговора, идентификација проблема и још много тога.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Ko je odgovoran za obradu unosa?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Svi novi unosi u ovom projektu biće dodeljeni ovoj osobi. Ona se može promeniti u {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Forma unosa", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Реаговање на инпуте", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "<PERSON><PERSON><PERSON><PERSON><PERSON> pregled", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Odaberite podrazumevajući prikaz korisničkih unosa: kartice ili tačke na mapi. Učesnici mogu samostalno odlučivati o izboru ova dva prikaza.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Inspiration hub", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Угради Конвеио УРЛ", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Number of likes per participant", "app.containers.AdminPage.ProjectEdit.limited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Učitajte j<PERSON>š <PERSON>", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Na mapi", "app.containers.AdminPage.ProjectEdit.mapTab": "Mapa", "app.containers.AdminPage.ProjectEdit.maxDislikes": "Максимално несвиђања", "app.containers.AdminPage.ProjectEdit.maxLikes": "Максимум лајкова", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Maximum number of votes per option must be less than or equal to total number of votes", "app.containers.AdminPage.ProjectEdit.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Učesnici ne mogu premašiti ovaj budžet prilikom prijave odabranih projekata.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimum", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Zahtevajte od učesnika da ispune minimalni budžet prilikom slanja odabranih predloga (unesite '0' ako ne želite da postavite minimum).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "posetite naš Centar za podršku", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Who are the project managers?", "app.containers.AdminPage.ProjectEdit.moreDetails": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "Need inspiration? Explore similar projects from other cities in the {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "Add a contribution", "app.containers.AdminPage.ProjectEdit.newIdea": "New idea", "app.containers.AdminPage.ProjectEdit.newInitiative": "Add an initiative", "app.containers.AdminPage.ProjectEdit.newIssue": "Add an issue", "app.containers.AdminPage.ProjectEdit.newOption": "Add an option", "app.containers.AdminPage.ProjectEdit.newPetition": "Add a petition", "app.containers.AdminPage.ProjectEdit.newProject": "Novi projekat", "app.containers.AdminPage.ProjectEdit.newProposal": "Add a proposal", "app.containers.AdminPage.ProjectEdit.newQuestion": "Add a question", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Najskorije", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "<PERSON><PERSON> validan i<PERSON>", "app.containers.AdminPage.ProjectEdit.noFolder": "<PERSON><PERSON> fascikle", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "— No folder —", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "<PERSON>su pronađ<PERSON>", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Ovo ne može biti prazno", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "<PERSON><PERSON>o vas navedite maksimalan broj dozvoljenih glasova po korisniku", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Najstarije", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Samo administratori mogu da vide", "app.containers.AdminPage.ProjectEdit.optionNo": "Ne", "app.containers.AdminPage.ProjectEdit.optionYes": "Da (odaberite folder)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Nivoi participacije", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "<PERSON>ta želite da radite?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Odaberite kako korisnici učestvuju.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "Можете да одредите ко може да предузме сваку радњу и да поставите додатна питања учесницима како бисте прикупили више информација.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Захтеви и питања за учеснике", "app.containers.AdminPage.ProjectEdit.pendingReview": "Pending approval", "app.containers.AdminPage.ProjectEdit.permissionsTab": "<PERSON><PERSON><PERSON> pristupa", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "Access rights", "app.containers.AdminPage.ProjectEdit.phaseEmails": "Notifications", "app.containers.AdminPage.ProjectEdit.pollTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "Naj<PERSON>še reak<PERSON>", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Slika kartice projekta", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Ova slika deo je kartice projekta. Na primer, to je kartica koja rezimira projekat i prikazana je na početnoj stranici.\n\n    Za više informacija o preporučenim rezolucijama slike, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Folder", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Ova slika je prikazana u vrhu stranice sa projektima.\n\n    Za više informacija o preporučenim rezolucijama slike, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Project card image alternative text", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Provide a short description of the image for visually impaired users. This helps screen readers convey what the image is about.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Пројектни менаџмент", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Projektni menadžeri mogu vršiti izmene projekata, upravljati unosima i slati email obaveštenja korisnicima. Možete {moderationInfoCenterLink} kako biste pronašli više informacija o ovlašćenjima projektnih menadžera. ", "app.containers.AdminPage.ProjectEdit.projectName": "Naziv projekta", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Tip projekta", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Odaberite da li će ovaj projekat imati vremenske okvire. Ovakvi projekti imaju jasno naznačen početak i mogu obuhvatati različite faze. Projekti bez vremenskih okvira nemaju ograničenje u pogledu trajanja. ", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Tip projekta se ne može naknadno menja<PERSON>.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "Можете подесити да пројекат буде невидљив одређеним корисницима.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Видљивост пројекта", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Looking for the project status? Now you can change it at any time directly from the project page header.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Published projects are visible to everyone or a group subset if selected.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Objavljeno", "app.containers.AdminPage.ProjectEdit.purposes": "Svrha", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "<PERSON><PERSON><PERSON>č<PERSON>", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Reset participation data", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "<PERSON><PERSON><PERSON> je do greške prilikom čuvanja unosa. Molimo pokušajte ponovo.", "app.containers.AdminPage.ProjectEdit.saveProject": "Sačuvajte", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Sačuvano!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Vaša forma je sa<PERSON>vana!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.selectGroups": "Izabrane grupe", "app.containers.AdminPage.ProjectEdit.setup": "Setup", "app.containers.AdminPage.ProjectEdit.shareInformation": "Delite informacije", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Podesite i personalizujte vaš projekat", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "poseti naš centar za podršku", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Prekid", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# izbori} one {# izbor} other {# izbori}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "Да, желим да одем", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "Edit", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Submissions to this survey have started to come in. Changes to the survey may result in data loss and incomplete data in the exported files.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "File upload", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "Go back", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Import", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "AI summaries for short answer, long answer, and sentiment scale follow up questions can be accessed from the AI tab in the left sidebar.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "<PERSON><PERSON><PERSON> skala", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "Matrix", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "Long answer", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Višestruki izbor - izaberi više", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Image choice - choose many", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "New submission", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Još uvek nema odgovora na anketu", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Otvori za odgovore", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Open for responses", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Opcionalno", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "If no logic is added, the survey will follow its normal flow. If both the page and its questions have logic, the question logic will take precedence. Ensure this aligns with your intended survey flow. For more information, visit {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "Location", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "<PERSON><PERSON><PERSON> li zaista da napustite?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Ваше тренутне промене неће бити сачуване.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Ranking", "app.containers.AdminPage.ProjectEdit.survey.rating": "Rating", "app.containers.AdminPage.ProjectEdit.survey.required2": "Obavezno", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {responses} one {response} other {responses}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# responses} one {# response} other {# responses}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Višestruki izbor - izaberi jedan", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Sentiment linear scale", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Esri shapefile upload", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Anketa je us<PERSON>š<PERSON> sa<PERSON>", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Survey responses", "app.containers.AdminPage.ProjectEdit.survey.text2": "Short answer", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Ukupno {count} odgovora", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "<PERSON><PERSON> ankete", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "View", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Umetnite URL", "app.containers.AdminPage.ProjectEdit.surveyService": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Možete pronaći više informacija o integraciji ankete {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "ovde", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Ova oznaka ne može se izbrisati zato što se koristi za prikaz projekata na sledećoj prilagođenijoj stranici(ama). Potrebno je da prekinete vezu između oznake i stranice ili da izbrišete stranicu pre nego što izbrišete oznaku.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Generalna podešavanja projekta", "app.containers.AdminPage.ProjectEdit.titleLabel": "Naziv projekta", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Odaberite naziv koji je kratak, afirmativan i jasan. On će biti prikazan u padajućem meniju i projektnoj kartici na početnoj stranici.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Teme", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Izaberite {topicsCopy} za ovaj projekat. Korisnici ovo mogu da koriste za filtriranje projekata.", "app.containers.AdminPage.ProjectEdit.totalBudget": "<PERSON><PERSON><PERSON> b<PERSON>", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "<PERSON> <PERSON>u", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Nedodeljeno", "app.containers.AdminPage.ProjectEdit.unlimited": "Neo<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Use this template", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "Pogledajte projekat", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Volontiranje", "app.containers.AdminPage.ProjectEdit.voteTermError": "Vote terms must be specified for all locales", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# grupa} one {# grupa} other {# grupa}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Do<PERSON>j<PERSON>", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Additional information", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Address 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Street address of the event location", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Address 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "E.g. Apt, Suite, Building", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Additional address information that could help identify the location such as a building name, floor number, etc.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "See the support article", "app.containers.AdminPage.ProjectEvents.customButtonLink": "External link", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Add a link to an external URL (E.g. Event service or ticketing website). Setting this will override the default attendance button behavior.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Custom button text", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Set the button text to a value other than \"Register\" when an external URL is set.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Početak", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Izbriši", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Da li ste sigurni da želite da obrišete ovaj događaj? Ovo nije moguće poništiti!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Opis dog<PERSON>", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Izmenite", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Izmenite događaj", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "To email registrants directly from the platform, admins must create a user group in the {userTabLink} tab. {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "Event dates", "app.containers.AdminPage.ProjectEvents.eventImage": "Event image", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Event image alternative text", "app.containers.AdminPage.ProjectEvents.eventLocation": "Event location", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Export registrants", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Prilozi se prikazuju ispod opisa događaja.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Lokacija", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Maximum registrants", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Kreirajte novi događaj", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Online event link", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "If your event is online, add a link to it here.", "app.containers.AdminPage.ProjectEvents.preview": "Preview", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Refine map location", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Refine location on map", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "You can refine where your event location marker is shown by clicking on the map below.", "app.containers.AdminPage.ProjectEvents.register": "Register", "app.containers.AdminPage.ProjectEvents.registerButton": "Register button", "app.containers.AdminPage.ProjectEvents.registrant": "registrant", "app.containers.AdminPage.ProjectEvents.registrants": "registrants", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Registration limit", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Sačuvajte", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "Nismo mogli sačuvati izmene, molimo pokušajte kasnije.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Sačuvano!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Vaše izmene su sačuvane.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "Search for a location", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Povežite predstojeće događaje sa ovim projektom kako biste ih prikazali na njegovoj stranici.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Nazi i datumi", "app.containers.AdminPage.ProjectEvents.titleEvents": "Događaji u okviru projekta", "app.containers.AdminPage.ProjectEvents.titleLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Link the button to an external URL", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "By default, the in-platform event register button will be shown allowing users to register for an event. You can change this to link to an external URL instead.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Limit the number of event registrants", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Set a maximum number of event registrants. If the limit is reached, no further registrations will be accepted.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/users", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Users", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "Add files to your project", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Attach files from this list to your project, phases, and events.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Add files as context to Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Add files to your Sensemaking project to provide context and insights.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Coming soon", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Sync surveys, upload interviews, and let AI connect the dots across your data.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Upload any file", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Use AI to analyze files", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Process transcripts, etc.", "app.containers.AdminPage.ProjectFiles.addFiles": "Add files", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "AI-powered insights", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "Analyze uploaded files to surface key topics.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Allow advanced analytics of these files using AI processing.", "app.containers.AdminPage.ProjectFiles.askButton": "Ask", "app.containers.AdminPage.ProjectFiles.categoryLabel": "Category", "app.containers.AdminPage.ProjectFiles.chooseFiles": "Choose files", "app.containers.AdminPage.ProjectFiles.close": "Close", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Confirm and upload", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Are you sure you want to delete this file?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "Could not load markdown file.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "Could not load CSV preview.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "Maximum 50 rows are shown in CSV previews.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "CSV file is too large to preview.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "Delete file", "app.containers.AdminPage.ProjectFiles.description": "Description", "app.containers.AdminPage.ProjectFiles.done": "Done", "app.containers.AdminPage.ProjectFiles.downloadFile": "Download file", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Download full file", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Drag and drop any files here or", "app.containers.AdminPage.ProjectFiles.editFile": "Edit file", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Description", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "File name may not contain a dot.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "File Name", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "File name is required.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Download file", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Preview", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "This file will not be uploaded, as it exceeds the maximum limit of 50 MB.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "All files uploaded successfully", "app.containers.AdminPage.ProjectFiles.generatingPreview": "Generating preview...", "app.containers.AdminPage.ProjectFiles.info_sheet": "Information", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "E.g. WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Audio interviews, Town Hall recordings", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "E.g. PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "Reports, informational documents", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "E.g. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "Images", "app.containers.AdminPage.ProjectFiles.interview": "Interview", "app.containers.AdminPage.ProjectFiles.maxFilesError": "You can only upload a maximum of {maxFiles} files at a time.", "app.containers.AdminPage.ProjectFiles.meeting": "Meeting", "app.containers.AdminPage.ProjectFiles.noFilesFound": "No files found.", "app.containers.AdminPage.ProjectFiles.other": "Other", "app.containers.AdminPage.ProjectFiles.policy": "Policy", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Preview not yet supported for this file type.", "app.containers.AdminPage.ProjectFiles.report": "Report", "app.containers.AdminPage.ProjectFiles.retryUpload": "Retry upload", "app.containers.AdminPage.ProjectFiles.save": "Save", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "File updated successfully.", "app.containers.AdminPage.ProjectFiles.searchFiles": "Search files", "app.containers.AdminPage.ProjectFiles.selectFileType": "File type", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Strategic plan", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "You can only upload a maximum of {maxFiles} files at a time.", "app.containers.AdminPage.ProjectFiles.unknown": "Unknown", "app.containers.AdminPage.ProjectFiles.upload": "Upload", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# file} other {# files}} uploaded successfully, {numberOfErrors, plural, one {# error} other {# errors}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "View file", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Prikažite sva polja", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Opis polja", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Uredi obrazac za unos", "app.containers.AdminPage.ProjectIdeaForm.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Uključujući ovo polje.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Do<PERSON><PERSON> je do greške. Molimo pokušajte kasnije. ", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Proširite sva polja", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Forma unosa", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Navedite koje informacije treba uneti, dodajte kratke opise ili uputstva kao vodič za odgovore učesnika i navedite da li je svako polje opconalno ili obavezno.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Odredite koje informacije su potrebne, dodajte kratke opise ili uputstva kako biste usmerili odgovore učesnika i precizirali koje polje je obavezno, a koje opciono.", "app.containers.AdminPage.ProjectIdeaForm.required": "Obavezno", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Zahtevajte da ovo polje bude popunjeno", "app.containers.AdminPage.ProjectIdeaForm.save": "Sačuvajte", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Vaše izmene su uspešno sačuvane.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Sačuvano!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Automated emails", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "You can configure emails triggered on a phase level", "app.containers.AdminPage.ProjectTimeline.datesLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "Take the survey", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Survey", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Da li ste sigurni da želite da obrišete ovu fazu?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Opis faze", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "<PERSON>z<PERSON>i fazu", "app.containers.AdminPage.ProjectTimeline.endDate": "End date", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Završetak", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Kreirajte novu fazu", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "This phase doesn't have a predefined end date.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Some methods' results sharing (such as voting results) won't be triggered until an end date is selected.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "As soon as you add a phase after this one, it will add an end date to this phase.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "Not selecting an end date for this implies that:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Preview", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "Save changes", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "<PERSON><PERSON><PERSON> je do greške prilikom slanja forme, molimo vas pokušajte ponovo.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Sačuvano!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Vaše izmene su uspešno sačuvane.", "app.containers.AdminPage.ProjectTimeline.startDate": "Start date", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Početak", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "Survey title", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Naziv faze", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Upload attachments", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartition", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologija (filter naslovne stranice)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Kako bi kategorije u filteru naslovne stranice trebalo da glase? Na primer, ozna<PERSON>, kate<PERSON><PERSON><PERSON>, sektori...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Oznake mogu da se konfigurišu {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "ovde", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "<PERSON><PERSON><PERSON> za jed<PERSON> (jednina)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tema", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Termin za višestruke kategorije (množina)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "kategor<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Dodajte polje", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Dodajte novo registraciono polje", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Dodajte opciju", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Format odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Obezbedi format za odgovore", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Opcija odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Unesi opciju odgovora za sve jezike", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Sačuvaj opciju odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Opcija odgovora je uspeš<PERSON> sačuvana", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Opcije odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Prevucite i ubacite polja da biste odredili redosled u kojem se pojavljuju u obrascu za registraciju.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Podrazumevajuće polje", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Izbriši", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Opcioni tekst koji se prikazuje ispod naziva polja na formularu za registraciju.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Opcije izbora mesta prebivališta se mogu podesiti u {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Izmenite", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Izmeni opciju odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Opis", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Naziv polja", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Obezbedi naziv polja za sve jezike", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Podešavanja polja", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "<PERSON><PERSON><PERSON> (checkbox)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Datum", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Duži odgovor", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Mogućnost izbora (više odgovora)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Numerička vrednost", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Mogućnost izbora (odaberite jedan)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Kraći odgovor", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Kartica sa geografskim oblastima", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Skriveno polje", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Da li želite da odgovor na ovo polje bude obavezan?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Prilagođena polja", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Dodajte opciju odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Prekid", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Izbriši", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Да ли сте сигурни да желите да избришете ову опцију одговора на питање за регистрацију? Сви записи на које су одређени корисници одговорили овом опцијом биће трајно избрисани. Ова радња се не може опозвати.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Да ли сте сигурни да желите да избришете ово питање за регистрацију? Сви одговори које су корисници дали на ово питање биће трајно обрисани и више се неће постављати у пројектима или предлозима. Ова радња се не може опозвати.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Obavezno", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Sačuvaj polje", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Polje je us<PERSON>š<PERSON> sa<PERSON>o", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "<PERSON><PERSON> kolone", "app.containers.AdminPage.SettingsPage.addAreaButton": "Dodajte geografsku oblast", "app.containers.AdminPage.SettingsPage.addTopicButton": "Do<PERSON>j<PERSON> temu", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Animal - eg Elephant Cat", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "User - eg User 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "Select which admins will receive notifications to approve projects. Folder Managers are by default approvers for all projects within their folders.", "app.containers.AdminPage.SettingsPage.approvalSave": "Save", "app.containers.AdminPage.SettingsPage.approvalTitle": "Project approval settings", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Da li ste sigurni da želite da obrišete ovu oblast?", "app.containers.AdminPage.SettingsPage.areaTerm": "Naziv jedne oblasti (jednina)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "oblast", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Izbriši", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Izmenite", "app.containers.AdminPage.SettingsPage.areasTerm": "Naziv više oblasti (množina)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "oblasti", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Изаберите најмање један језик.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Prikaži avatare", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Prikaži profilne slike učesnika i koliko ih ima neregistrovanim posetiocima", "app.containers.AdminPage.SettingsPage.bannerHeader": "Tekst zaglavlja", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Tekst zaglavlja za neregistrovane posetioce", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Tekst podzaglavlja za neregistrovane posetioce", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Tekst podzaglavlja", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Tekst na baneru", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "<PERSON><PERSON><PERSON><PERSON> pregled za", "app.containers.AdminPage.SettingsPage.brandingDescription": "Dodajte svoj logotip i postavite boje platforme.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Brendiranje platforme", "app.containers.AdminPage.SettingsPage.cancel": "Prekid", "app.containers.AdminPage.SettingsPage.chooseLayout": "Ra<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.color_primary": "<PERSON><PERSON><PERSON> boja", "app.containers.AdminPage.SettingsPage.color_secondary": "<PERSON><PERSON><PERSON><PERSON> boja", "app.containers.AdminPage.SettingsPage.color_text": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.colorsTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.confirmHeader": "Da li ste sigurni da želite da obrišete ovu temu?", "app.containers.AdminPage.SettingsPage.contentModeration": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.ctaHeader": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Prilagodi zaglavlje stranice | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Tekst dugmeta", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.defaultTopic": "Podrazumeva<PERSON><PERSON><PERSON> tema", "app.containers.AdminPage.SettingsPage.delete": "Izbriši", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Izbriši", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "<PERSON>vo će obrisati temu zajedno sa svim postojećim unosima. Ova izmena će se primeniti na sve projekte.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Dodajte i izbrišite teme koje želite da koristite na svojoj platformi za kategorizaciju postova. Teme možete dodati određenim projektima u {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editFormTitle": "Izmeni oblast", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Izmenite", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "<PERSON>z<PERSON>i temu", "app.containers.AdminPage.SettingsPage.fieldDescription": "Opis oblasti", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Ovaj opis je namenjen internoj saradnji i ne prikazuje se korisnicima. ", "app.containers.AdminPage.SettingsPage.fieldTitle": "Naziv oblasti", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Navedi naziv područja za sve jezike", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Ime koje odaberete za svako područje može se koristiti kao opcija polja za registraciju i za filtriranje projekata na početnoj stranici.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Sačuvaj <PERSON>", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Naziv teme", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Obezbedi naziv oznake za sve jezike", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Imena svake teme će biti vidljiva korisnicima platforme.", "app.containers.AdminPage.SettingsPage.fixedRatio": "<PERSON><PERSON> <PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Ovaj tip banera najbolje je primeniti za slike koje se ne izrezuju, kao što su slike sa tekstom, logotip ili specifični elementi koji su ključni za vaše građane. Ovaj baner zamenjuje se neprozirnim poljem u osnovnoj boji kada su korisnici prijavljeni. Možete da postavite ovu boju u opštim postavkama. Više informacija o preporučenoj upotrebi slike možete pronaći na {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "baza znanja", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "<PERSON><PERSON> pune <PERSON>irine", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Ovaj baner širi se punom širinom za sjajan vizuelni efekat. Slika će pokušati da prekrije što je više mesta moguće, tako što će učiniti da ne bude vidljivo u svakom trenutku. Ovaj baner možete da kombinujete sa prekrivajućim elementom bilo koje boje. Više informacija o preporučenoj upotrebi slike možete pronaći na {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "baza znanja", "app.containers.AdminPage.SettingsPage.header": "Baner na početnoj stranici", "app.containers.AdminPage.SettingsPage.headerDescription": "Prilagodite sliku i tekst banera na početnoj stranici.", "app.containers.AdminPage.SettingsPage.header_bg": "Slika banera", "app.containers.AdminPage.SettingsPage.helmetDescription": "Stranica sa <PERSON>kim pod<PERSON>jima", "app.containers.AdminPage.SettingsPage.helmetTitle": "Stranica sa <PERSON>kim pod<PERSON>jima", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Dodajte sopstveni sadržaj prilagodljivoj sekciji u dnu početne stranice.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Zaglavlje matične stranice | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Boja sloja preko slike", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Prozirnost sloja preko slike", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Otkrijte neprik<PERSON>an <PERSON>", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Automatsko otkrivanje neprikladnog sadržaja objavljenog na platformi.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "While this feature is enabled, input, proposals and comments posted by participants will be automatically reviewed. Posts flagged as potentially containing inappropriate content will not be blocked, but will be highlighted for review on the {linkToActivityPage} page.", "app.containers.AdminPage.SettingsPage.languages": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Možete odabrati različite jezike koji će biti dostupni na vašoj platformi. Biće potrebno da kreirate sadržaj za svaki izabrani jezik.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Aktivnosti", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Molimo vas postavite header sliku", "app.containers.AdminPage.SettingsPage.no_button": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.organizationName": "Ime grada ili organizacije", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Наведите назив организације или град за све језике.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Omogući prekrivajući element", "app.containers.AdminPage.SettingsPage.phone": "Telefon", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Konfiguracija platforme", "app.containers.AdminPage.SettingsPage.population": "Population", "app.containers.AdminPage.SettingsPage.populationMinError": "Population must be a positive number.", "app.containers.AdminPage.SettingsPage.populationTooltip": "The total number of inhabitants on your territory. This is used to calculate the participation rate. Leave empty if not applicable.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Blo<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Blokirajte unos, predloge i komentare koji sadrže najčešće prijavljene uvredljive reči", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Ovaj tekst je prikazan na početnoj stranici iznad projekata.", "app.containers.AdminPage.SettingsPage.projectsSettings": "podešavanja projekta", "app.containers.AdminPage.SettingsPage.projects_header": "Zaglavlje projekata", "app.containers.AdminPage.SettingsPage.registrationFields": "Polja za registraciju", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Navedite kratak opis na vrhu vašeg obrasca za registraciju.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registracija", "app.containers.AdminPage.SettingsPage.save": "Sačuvajte", "app.containers.AdminPage.SettingsPage.saveArea": "Sačuvaj područje", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "<PERSON><PERSON><PERSON> je do greš<PERSON>, molimo pokušajte kasnije", "app.containers.AdminPage.SettingsPage.saveSuccess": "Sačuvano!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Vaše izmene su sačuvane.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Select approvers", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "Select the areas that will be shown to users to follow after registration", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Select the topics that will be shown to users to follow after registration", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Čuvanje nije uspelo. Pokušajte ponovo da promenite postavku.", "app.containers.AdminPage.SettingsPage.sign_up_button": "„Registruj se”", "app.containers.AdminPage.SettingsPage.signed_in": "Dugme za registrovane posetioce", "app.containers.AdminPage.SettingsPage.signed_out": "Dugme za neregistrovane posetioce", "app.containers.AdminPage.SettingsPage.signupFormText": "Pomoćni tekst za registraciju", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Dodajte kratak opis na vrhu obrasca za registraciju.", "app.containers.AdminPage.SettingsPage.statuses": "Statuses", "app.containers.AdminPage.SettingsPage.step1": "Korak za email i lozinku", "app.containers.AdminPage.SettingsPage.step1Tooltip": "<PERSON>vo je prikazano na vrhu prve stranice obrasca za registraciju (ime, e-pošta, lozinka).", "app.containers.AdminPage.SettingsPage.step2": "Korak za registraciona pitanja", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Ovo je prikazano na vrhu druge stranice obrasca za registraciju (dodatna polja za registraciju).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Definišite geografska područja koja biste želeli da koristite u okviru platforme, kao što su četvrti, mes<PERSON> zajed<PERSON>e, op<PERSON>tine ili okruzi. Ova geografska područja možete povezati sa projektima (kako bi se mogli filtrirati na pčetnoj stranici) ili zatražiti od učesnika da izaberu svoje prebivalište kao polje za registraciju u svrhe kreiranja pametnih grupa i definisanja prava pristupa.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Odaberite kako će korisnici videti vaše ime, odaberite jezike vaše platforme i postavite link do vaše web stranice.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Željeni podnaslov prelazi dozvoljenih 90 karaktera. ", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminologija", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Podešavanja su uspešno ažurirana.", "app.containers.AdminPage.SettingsPage.tabAreas1": "Подручја", "app.containers.AdminPage.SettingsPage.tabBranding": "Брендирање", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Статуси уноса", "app.containers.AdminPage.SettingsPage.tabPolicies": "Politike", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Project approval", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Proposal statuses", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registracija", "app.containers.AdminPage.SettingsPage.tabSettings": "General<PERSON>", "app.containers.AdminPage.SettingsPage.tabTopics2": "Tags", "app.containers.AdminPage.SettingsPage.tabWidgets": "Vidžet", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Definišite koju geografsku jedinicu želite da koristite za svoje projekte (npr. naselja, okruzi, opštine itd.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geografske oblasti", "app.containers.AdminPage.SettingsPage.titleBasic": "<PERSON><PERSON>ša<PERSON>", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Željeni naslov prelazi dozvoljenih 35 karaktera", "app.containers.AdminPage.SettingsPage.titleTopicManager": "<PERSON><PERSON><PERSON><PERSON> tema", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "O<PERSON><PERSON> baner konkretno je koristan za slike koje nemaju dobro rešenje za tekst naslova, titla ili dugmeta. Ove stavke biće spuštene ispod banera. Više informacija o preporučenoj upotrebi slike možete pronaći na {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "baza znanja", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON><PERSON> reda", "app.containers.AdminPage.SettingsPage.urlError": "URL nije validan", "app.containers.AdminPage.SettingsPage.urlPatternError": "Унесите важећи УРЛ.", "app.containers.AdminPage.SettingsPage.urlTitle": "Website", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "početna stranica.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "Choose how users without a name in their profile will appear in the platform. This will occur when you set the access rights for a phase to ‘Email confirmation’. In all cases, upon participation, users will be able to update the profile name we autogenerated for them.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "User name display (for users with email confirmed only)", "app.containers.AdminPage.SideBar.administrator": "Администратор", "app.containers.AdminPage.SideBar.communityPlatform": "Платформа заједнице", "app.containers.AdminPage.SideBar.community_monitor": "Community monitor", "app.containers.AdminPage.SideBar.customerPortal": "Customer portal", "app.containers.AdminPage.SideBar.dashboard": "Komand<PERSON> panel", "app.containers.AdminPage.SideBar.emails": "Email", "app.containers.AdminPage.SideBar.folderManager": "Управљач фасциклама", "app.containers.AdminPage.SideBar.groups": "Grupe", "app.containers.AdminPage.SideBar.guide": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inputManager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.insights": "Izveštavanje", "app.containers.AdminPage.SideBar.inspirationHub": "Inspiration hub", "app.containers.AdminPage.SideBar.knowledgeBase": "База знања", "app.containers.AdminPage.SideBar.language": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Stranice i meni", "app.containers.AdminPage.SideBar.messaging": "<PERSON><PERSON><PERSON> poruka", "app.containers.AdminPage.SideBar.moderation": "Aktivnosti", "app.containers.AdminPage.SideBar.notifications": "Обавештења", "app.containers.AdminPage.SideBar.processing": "Obrada", "app.containers.AdminPage.SideBar.projectManager": "Вођа пројекта", "app.containers.AdminPage.SideBar.projects": "Projekti", "app.containers.AdminPage.SideBar.settings": "Podešavanja", "app.containers.AdminPage.SideBar.signOut": "Одјава", "app.containers.AdminPage.SideBar.support": "Подршка", "app.containers.AdminPage.SideBar.toPlatform": "На платформу", "app.containers.AdminPage.SideBar.tools": "<PERSON>ла<PERSON><PERSON>", "app.containers.AdminPage.SideBar.user.myProfile": "Мој профил", "app.containers.AdminPage.SideBar.users": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.workshops": "Radionice", "app.containers.AdminPage.Topics.addTopics": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.browseTopics": "Pretraži teme", "app.containers.AdminPage.Topics.cancel": "Prekid", "app.containers.AdminPage.Topics.confirmHeader": "Da li ste sigurni da želite da obrišete ovu temu u sklopu projekta?", "app.containers.AdminPage.Topics.delete": "Izbriši", "app.containers.AdminPage.Topics.deleteTopicLabel": "Izbriši", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Ova tema više neće moći biti dodeljivana objavama u sklopu ovog projekta. ", "app.containers.AdminPage.Topics.inputForm": "Forma unosa", "app.containers.AdminPage.Topics.lastTopicWarning": "Potrebna je barem jedna tema. Ako ne <PERSON>eli<PERSON> da ih koristite, možete ih isključiti putem {ideaFormLink} kartice. \n", "app.containers.AdminPage.Topics.projectTopicsDescription": "Možete dodati ili obrisati teme koje se dodeljuju unosima u okviru ovog projekta.", "app.containers.AdminPage.Topics.remove": "Ukloni", "app.containers.AdminPage.Topics.title": "Dozvoljene kategorije unosa", "app.containers.AdminPage.Topics.topicManager": "<PERSON><PERSON><PERSON><PERSON> tema", "app.containers.AdminPage.Topics.topicManagerInfo": "<PERSON><PERSON> da dodate još proje<PERSON> te<PERSON>, to mož<PERSON> učiniti u {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Dodaj novu grupu", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Naziv grupe", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "<PERSON>vedi naziv grupe", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Kreirajte manualnu grupu", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Kakav tip grupe želite?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Sačuvajte grupu", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Kreirajte manualnu grupu", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Kreirajte smart grupu", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Saznajte više o grupama", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Možete odabrati korisnike koje želite da dodate u ovu grupu.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Možete odrediti uslove čije će ispunjenje automatski dodati korisnike u ovu grupu.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Manualna grupa", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Smart grupa", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Još uvek nema nikoga u ovoj grupi", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Pogledajte {allUsersLink} kako biste <PERSON>no dodali korisnike.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "<PERSON>ema korisnika koji se podudaraju sa vašom pretragom", "app.containers.AdminPage.Users.GroupsPanel.select": "Odaberite", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Export all", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Export users in group", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Export selected", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Da li ste sigurni", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Došlo je do greške prilikom dodavanja korisnika u grupe, molimo pokušajte kasnije.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Ukloni iz grupe", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Obrišite izabrane korisnike iz ove grupe?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Do<PERSON><PERSON> je do greške prilikom brisanja korisnika iz grupe, molimo pokušajte kasnije. ", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Add users to group", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.add": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.addAnswer": "Додајте одговор", "app.containers.AdminPage.groups.permissions.addQuestion": "Додајте демографска питања", "app.containers.AdminPage.groups.permissions.answerChoices": "Избори одговора", "app.containers.AdminPage.groups.permissions.answerFormat": "Формат одговора", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "Мора се обезбедити најмање један избор", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "This user moderates the folder containing this project. To remove their moderator rights for this project, you can either revoke their folder rights or move the project to a different folder.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Направите ново питање", "app.containers.AdminPage.groups.permissions.createAQuestion": "Направите питање", "app.containers.AdminPage.groups.permissions.defaultField": "Подразумевано поље", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Izbriši", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Izbriši", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Please provide a title for all choices", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "Да-не (поље за потврду)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Датум", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Дуг одговор", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Вишеструки избор (изаберите више)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Нумеричка вредност", "app.containers.AdminPage.groups.permissions.fieldType_select": "Вишеструки избор (изаберите један)", "app.containers.AdminPage.groups.permissions.fieldType_text": "Кратак одговор", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Changing granular permissions is not part of your license. Please contact your GovSuccess Manager to learn more about it.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Da li ste sigurni da želite da uklonite ovu grupu iz projekta?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Odaberite jednu ili više grupa", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {<PERSON><PERSON>} one {1 član} other {{count} <PERSON><PERSON><PERSON>}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Молимо попуните наслов на свим језицима", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Da li ste sigurni", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Nisu pronađeni projektni menadžeri", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "<PERSON><PERSON><PERSON>, jer nema akcija koje korisnici mogu preduzeti u ovom projektu.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Only admins can create a new question.", "app.containers.AdminPage.groups.permissions.option1": "Опција 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Pozivnica na čekanju", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Ко може да дода коментаре на документ?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Who can sign up to attend an event?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Ко може да коментарише уносе?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Ко може да коментарише предлоге?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Ко може да постави предлог?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Ко може да реагује на инпуте?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Ко може да поднесе инпуте?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Ко може да гласа?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Ко може да полаже анкету?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Who can volunteer?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Ко може да гласа о предлозима?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Who can vote?", "app.containers.AdminPage.groups.permissions.questionDescription": "О<PERSON>ис питања", "app.containers.AdminPage.groups.permissions.questionTitle": "Наслов питања", "app.containers.AdminPage.groups.permissions.save": "Sačuvajte", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "<PERSON><PERSON><PERSON> je do greš<PERSON>, molimo pokušajte kasnije.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Uspešno!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Vaše izmene su sačuvane.", "app.containers.AdminPage.groups.permissions.select": "Изаберите", "app.containers.AdminPage.groups.permissions.selectValueError": "Молимо изаберите тип одговора", "app.containers.AdminPage.new.createAProject": "Create a project", "app.containers.AdminPage.new.fromScratch": "From scratch", "app.containers.AdminPage.phase.methodPicker.addOn1": "Add on", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "AI-powered insights", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Help participants surface agreement and disagreement, one idea at a time.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Find common ground", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Embed an interactive PDF and collect comments and feedback with Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Collect feedback on a document", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Embed a third-party survey", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "External survey", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Tap into your users' collective intelligence. Invite them to submit, discuss ideas, and/or provide feedback in a public forum.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Collect input and feedback in public", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Share information", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Lacks in-platform AI-powered insights", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Lacks in-platform reporting and data visualisation and processing", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Link with in-platform report builder", "app.containers.AdminPage.phase.methodPicker.logic1": "Logic", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Wide range of question types", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Allow participants to upload ideas with a time and vote limit.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "Proposals, petitions or initiatives", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "Quick poll", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Set up a short, multiple-choice questionnaire.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "Provide information to users, visualise results from other phases and create data rich reports.", "app.containers.AdminPage.phase.methodPicker.survey1": "Survey", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Understand your users' needs and thinking via a wide range of private question types.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Survey options", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Create a survey", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Ask users to volunteer for activities and causes or find participants for a panel.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Recruit participants or volunteers", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Select a voting method, and have users prioritise between a few different options.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Conduct a voting or prioritization exercise", "app.containers.AdminPage.projects.all.all": "All", "app.containers.AdminPage.projects.all.createProjectFolder": "New folder", "app.containers.AdminPage.projects.all.existingProjects": "Postojeći projekti", "app.containers.AdminPage.projects.all.homepageWarning1": "Use this page to set the order of projects in the \"All projects\" dropdown in the navigation bar. If you are using the \"Published projects and folders\" or \"Projects and folders (legacy)\" widgets on your homepage, the order of projects in these widget will also be determined by the order you set here.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "Projects where you are a Project Manager will appear here.", "app.containers.AdminPage.projects.all.noProjects": "No projects found.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Only admins can create project folders.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projekti i folderi", "app.containers.AdminPage.projects.all.publishedTab": "Objavljeno", "app.containers.AdminPage.projects.all.searchProjects": "Search projects", "app.containers.AdminPage.projects.all.yourProjects": "Your projects", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "AI Analysis", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Accuracy: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "Ask", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "Instead of summarising, you can ask relevant questions to your data. This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Ask a question", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "This insight includes the following questions:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "Delete question", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "Are you sure you want to delete this question?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Delete summary", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Are you sure you want to delete these summaries?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Your text summaries will be displayed here, but you currently do not have any yet.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "Click the Auto-summarize button above to get started.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "inputs selected", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Asking questions about fewer inputs leads to a higher accuracy. Reduce the current input selection by using tags, search or demographic filters.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "Questions for", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Question for all inputs", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Rate the quality of this insight", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "Restore filters", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Summarize", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Summary for", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Summary for all inputs", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "Thank you for your feedback", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "The AI can’t process so many inputs in one go. Divide them into smaller groups.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "You can summarise a maximum of 30 inputs at a time on your current plan. Talk to your GovSuccess Manager or admin to unlock more.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "I understand", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Our platform enables you to explore the core themes, summarize the data, and examine various perspectives. If you are looking for specific answers or insights, consider using the \"Ask a Question\" feature to dive deeper beyond the summary.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "While rare, the AI might occasionally generate information that was not explicitly present in the original dataset.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Hallucinations:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "The AI might emphasize certain themes or ideas more than others, potentially skewing the overall interpretation.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Exaggeration:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Our system is optimized for handling 20-200 well-defined inputs for the most accurate results. As the volume of data increases beyond this range, the summary may become more high-level and generalized. This does not mean the AI becomes \"less accurate\", but rather that it will focus on broader trends and patterns. For more nuanced insights, we recommend using the (auto)-tagging feature to segment larger datasets into smaller, more manageable subsets.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Data Volume and Accuracy:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "We recommend using AI-generated summaries as a starting point for understanding large datasets, but not as the final word.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "How to work with AI", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Add selected inputs to tag", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "Add tag", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "All inputs", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "All inputs", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "All tags", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "No, I'll do it", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Do you want to automatically assign inputs to your tag?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "There are <b>different methods</b> to automatically assign inputs to tags.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Use <b>the auto-tag button</b> to launch your preferred method.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Click on a tag to assign it to the currently selected input.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "Yes, auto-tag", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Auto-tag", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Auto-tags are automatically derived by the computer. You can change or remove them at all times.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Auto-tag", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Inputs already associated with these tags will not be classified again.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "The classification is solely based on the name of the tag. Pick relevant keywords for the best results.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Tags: By label", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "You create the tags and manually assign a few inputs as an example, the computer assigns the rest", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Tags: By example", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "Similar to \"Tags: by label\" but with increased accuracy as you’re training the system with good examples.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "You create the tags, the computer assigns the inputs", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Tags: By label", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "This works well when you have a pre-defined set of tags or when your project has a limited scope in terms of tags.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Detect inputs with a significant dislikes/likes ratio", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Controversial", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Delete tag", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "Are you sure you want to delete this tag?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Don't show this again", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Edit tag", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "Add name", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Select maximum 9 tags you would like the inputs to be distributed between.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "The classification is based on the inputs currently assigned to the tags. The computer will try to follow your example.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Tags: By example", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "You do not have any custom tags yet.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "The computer automatically detects tags and assigns them to your inputs.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Tags: Fully automated", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "Works well when your projects covers a broad range of tags. Good place to start.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "How do you want to tag?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Inputs without tags", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Detect the language of each input", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "Language", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "Launch", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "No active filters", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Use tags to subdivide and filter the inputs, in order to make more accurate or targeted summaries.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "Other", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Tags: Platform tags", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Assign the existing platform tags that the author picked when posting", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Recommended", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "<PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "Cancel", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Name", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "Save", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "<PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "Select all", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Assign a positive or negative sentiment to each input, derived from the text", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Tag detection", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Use current filters", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "What inputs do you want to tag?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Autotagging task", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Controversial", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "Custom", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Ended at", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "Failed", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "By example", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "In progress", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "By label", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "Language", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "NLP tag", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "No recent AI tasks performed", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Platform tag", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "Queued", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Started at", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Succeeded", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Summarization task", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Triggered at", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "Above", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "All", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "Autor", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "Below", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "<PERSON><PERSON>ar", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Engagement", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "Filters", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "From", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "Gender", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "Input", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "Number of comments", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Number of reactions", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "Number of votes", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "To", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Add to analysis", "app.containers.AdminPage.projects.project.analysis.anonymous": "Anonymous input", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Authors by age", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Authors by domicile", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Background Jobs", "app.containers.AdminPage.projects.project.analysis.comments": "Comments", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "The domicile chart is too large to display", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "Hide empty answers", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Responses", "app.containers.AdminPage.projects.project.analysis.end": "End", "app.containers.AdminPage.projects.project.analysis.filter": "Only show inputs with this value", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Hide responses with no answer", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Auto-insights", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Column values", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "There are {count} instances of this combination.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "Dislikes", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Explore", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "False", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "Inputs", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON>s", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Next heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Next insight", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Not a statistically significant insight.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Auto insights are not available for projects with less than 30 participants.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "Participants", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Previous heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Previous insight", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "Row values", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Statistically significant insight.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Summarize", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Analysis tags", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "True", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "Units", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "View all insights", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "View auto-insights", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Inputs without tags", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "An invalid shapefile was uploaded and cannot be displayed.", "app.containers.AdminPage.projects.project.analysis.limit": "Limit", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "Main question", "app.containers.AdminPage.projects.project.analysis.manageInput": "Manage input", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Next graph", "app.containers.AdminPage.projects.project.analysis.noAnswer": "No answer", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "No answer provided.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "No shapefile uploaded.", "app.containers.AdminPage.projects.project.analysis.noInputs": "No inputs correspond to your current filters", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Previous graph", "app.containers.AdminPage.projects.project.analysis.reactions": "Reactions", "app.containers.AdminPage.projects.project.analysis.remove": "Remove", "app.containers.AdminPage.projects.project.analysis.removeFilter": "Remove filter", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "Remove filter", "app.containers.AdminPage.projects.project.analysis.search": "Search", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Shapefiles are displayed in GeoJSON format here. As such, styling in the original file may not display correctly.", "app.containers.AdminPage.projects.project.analysis.start": "Start", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Support Article", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "Unknown", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "View all questions", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "View selected questions", "app.containers.AdminPage.projects.project.analysis.votes": "Votes", "app.containers.AdminPage.widgets.copied": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.copyToClipboard": "Kopirajte ovaj kod", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Kopirajte HTML kod", "app.containers.AdminPage.widgets.fieldAccentColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> boja", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Boja pozadine vidžeta", "app.containers.AdminPage.widgets.fieldButtonText": "Tekst dugmeta", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Priključite se", "app.containers.AdminPage.widgets.fieldFont": "Font", "app.containers.AdminPage.widgets.fieldFontDescription": "Ovo mora biti postojeće ime sa {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "<PERSON><PERSON><PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "<PERSON><PERSON><PERSON><PERSON> header-a", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Iskažite svoj stav", "app.containers.AdminPage.widgets.fieldHeaderText": "Tekst header-a", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Naša participativna platforma", "app.containers.AdminPage.widgets.fieldHeight": "V<PERSON>na (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldProjects": "Projekti", "app.containers.AdminPage.widgets.fieldRelativeLink": "<PERSON> ka", "app.containers.AdminPage.widgets.fieldShowFooter": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldShowHeader": "<PERSON><PERSON><PERSON><PERSON> header", "app.containers.AdminPage.widgets.fieldShowLogo": "Prikaži logo", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "<PERSON><PERSON> pozadine sajta", "app.containers.AdminPage.widgets.fieldSort": "Sortirano po", "app.containers.AdminPage.widgets.fieldTextColor": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldTopics": "Teme", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Početna stranica", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Možete kopirati ovaj HTML kod i postaviti ga na deo sajta u kome želite da prikazujete vidžet.", "app.containers.AdminPage.widgets.htmlCodeTitle": "HTML kod vidžeta", "app.containers.AdminPage.widgets.previewTitle": "Prikaz", "app.containers.AdminPage.widgets.settingsTitle": "Podešavanja", "app.containers.AdminPage.widgets.sortNewest": "Najskorije", "app.containers.AdminPage.widgets.sortPopular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.sortTrending": "<PERSON> <PERSON>u", "app.containers.AdminPage.widgets.subtitleWidgets": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.AdminPage.widgets.title": "Vidžet", "app.containers.AdminPage.widgets.titleDimensions": "Dimenzije", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Header i footer", "app.containers.AdminPage.widgets.titleInputSelection": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleStyle": "Stil", "app.containers.AdminPage.widgets.titleWidgets": "Vidžet", "app.containers.ContentBuilder.Save": "Sačuvajte", "app.containers.ContentBuilder.homepage.PageTitle": "Homepage", "app.containers.ContentBuilder.homepage.SaveError": "Something went wrong while saving the homepage.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "Two columns", "app.containers.ContentBuilder.homepage.bannerImage": "Banner image", "app.containers.ContentBuilder.homepage.bannerSubtext": "Banner subtext", "app.containers.ContentBuilder.homepage.bannerText": "Banner text", "app.containers.ContentBuilder.homepage.button": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.chooseLayout": "Layout", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Customizing settings other than the image and text on the homepage banner is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.ContentBuilder.homepage.customized_button": "Custom", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Button text", "app.containers.ContentBuilder.homepage.customized_button_url_label": "Button link", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Displays the next 3 upcoming events on your platform.", "app.containers.ContentBuilder.homepage.eventsDescription": "Displays the next 3 upcoming events on your platform.", "app.containers.ContentBuilder.homepage.fixedRatio": "Fixed-ratio banner", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "This banner type works best with images that shouldn’t be cropped, such as images with text, a logo or specific elements that are crucial to your citizens. This banner is replaced with a solid box in the primary colour when users are signed in. You can set this colour in the general settings. More info on the recommended image usage can be found on our {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "knowledge base", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "Full-width banner", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "This banner stretches over the full width for a great visual effect. The image will try to cover as much space as possible, causing it to not always be visible at all times. You can combine this banner with an overlay of any colour. More info on the recommended image usage can be found on our {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "knowledge base", "app.containers.ContentBuilder.homepage.imageOverlayColor": "Image overlay color", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Image overlay opacity", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "Invalid URL", "app.containers.ContentBuilder.homepage.no_button": "No button", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Non-registered users", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Enable overlay", "app.containers.ContentBuilder.homepage.projectsDescription": "To configure the order in which your projects are displayed, reorder them on the {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "Projects page", "app.containers.ContentBuilder.homepage.registeredUsersView": "Registered users", "app.containers.ContentBuilder.homepage.showAvatars": "Display avatars", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Show profile pictures of participants and number of them to non-registered visitors", "app.containers.ContentBuilder.homepage.sign_up_button": "Sign up", "app.containers.ContentBuilder.homepage.signedInDescription": "This is how registered users see the banner.", "app.containers.ContentBuilder.homepage.signedOutDescription": "This is how visitors that are not registered on the platform see the banner.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "This banner is in particular useful with images that don't work well with text from the title, subtitle or button. These items will be pushed below the banner. More info on the recommended image usage can be found on our {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "knowledge base", "app.containers.ContentBuilder.homepage.twoRowLayout": "Two rows", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "Embed height (pixels)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "Height you want your embedded content to appear on the page (in pixels).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Short description of the content you are embedding", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "It is useful to provide this information for users who rely on a screen reader or other assistive technology.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Website address", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "Full URL of the website you want to embed.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Display content from an external website on your page in an HTML iFrame. Note that not every page can be embedded. If you are having trouble embedding a page, check with the owner of the page if it is configured to allow embedding.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Visit our support page", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "Sorry, this content could not be embedded. {visitLinkMessage} to learn more.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Enter a valid web address, for example https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Embed", "app.containers.admin.ContentBuilder.accordionMultiloc": "Accordion", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Open by default", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Text", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "This is expandable accordion content. You can edit and format it by using the editor in the panel on the right.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Title", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Accordion title", "app.containers.admin.ContentBuilder.buttonMultiloc": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.delete": "Izbriši", "app.containers.admin.ContentBuilder.error": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.errorMessage": "<PERSON><PERSON><PERSON> je do greške u sadržaju {locale}, rešite problem kako bi vaše promene bile sačuvane", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Hide participation avatars", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "This is a quarterly, ongoing survey that tracks how you feel about governance & public services.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Take the survey", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Help us serve you better", "app.containers.admin.ContentBuilder.homepage.default": "default", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "Events", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "Events", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Call to action", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Description", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "Primary button URL", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Primary button text", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "Secondary button URL", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Secondary button text", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Title", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Homepage banner", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Homepage banner", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Image & text cards", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 column", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projects", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Enable proposals in the “Proposals” section in the admin panel to unlock them in the homepage", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Proposals", "app.containers.admin.ContentBuilder.imageMultiloc": "Image", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Short description of the image", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "Adding \"alt text\" for images is important to make your platform accessible for users using screen readers.", "app.containers.admin.ContentBuilder.participationBox": "Participation Box", "app.containers.admin.ContentBuilder.textMultiloc": "Text", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 kolone", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 kolona", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 kolone sa 30% i 60% širine, tim redosledom", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 kolone sa 60% i 30% širine, tim redosledom", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 jednake kolone", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "Most reacted inputs", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Ideation phase", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "There are no inputs available for this project or phase.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "Number of inputs", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Prikaži više", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "<PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Total inputs: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Collapse long text", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "There are no inputs available for this project or phase.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Select phase", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Author", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "Content", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "Reactions", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "Votes", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "Input", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Title", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Registration rate", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Registrations", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Vremenska linija učesnika", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Please note that participation numbers may not be fully accurate as some data is captured in an external survey that we do not track.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Grafikon", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Opseg datuma", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "<PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.noData": "<PERSON><PERSON> dos<PERSON> podataka za filtere koje ste izabrali.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Izvori <PERSON>", "app.containers.admin.ReportBuilder.charts.users": "Users", "app.containers.admin.ReportBuilder.charts.usersByAge": "Korisnici po starosti", "app.containers.admin.ReportBuilder.charts.usersByGender": "Koris<PERSON><PERSON> prema polu", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Vremenska linija posetioca", "app.containers.admin.ReportBuilder.managerLabel1": "Project manager", "app.containers.admin.ReportBuilder.periodLabel1": "Period", "app.containers.admin.ReportBuilder.projectLabel1": "Project", "app.containers.admin.ReportBuilder.quarterReport1": "Community Monitor Report: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Start", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Купите 1 додатно седиште", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Потврди", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Да ли сте сигурни да желите да дате права менаџера 1 особи?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Дајте менаџерска права", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Достигли сте ограничење укључених места у оквиру вашег плана, {noOfSeats} додатна {noOfSeats, plural, one {сеат} other {сеатс}} биће додат.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Dodaj status", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Default statuses cannot be deleted.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Izbriši", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Izmenite", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Izmeni status", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Statusi koji su trenutno dodeljeni unosima ne mogu se izbrisati. Možete ukloniti/promeniti status sa postojećih unosa na kartici {manageTab}.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Ovaj status nije moguće izbrisati ili premestiti.", "app.containers.admin.ideaStatuses.all.manage": "Upravljajte", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Configuring custom input statuses is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Управљајте статусом који се може доделити уносу учесника у оквиру пројекта. Статус је јавно видљив и помаже у информисању учесника.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Manage the status that can be assigned to proposals within a project. The status is publicly visible and helps in keeping participants informed.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Измените статусе уноса", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Edit proposal statuses", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Odabrano za realizaciju ili sledeće korake", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Odobreno", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Official feedback provided", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Answered", "app.containers.admin.ideaStatuses.form.category": "Kategorija", "app.containers.admin.ideaStatuses.form.categoryDescription": "Izaberite kategoriju koja najbolje predstavlja vaš status. Ovaj izbor će pomoći našem analitičkom alatu da preciznije obrađuje i analizira postove.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Not matching any of the other options", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "Drugo", "app.containers.admin.ideaStatuses.form.fieldColor": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescription": "Opis statusa", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Navedite opis statusa za sve jezike", "app.containers.admin.ideaStatuses.form.fieldTitle": "Naziv statusa", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Navedite naziv statusa za sve jezike", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Uspešno realizovano", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Realizovano", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "Proposal is ineligible", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "Ineligible", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Neprihvatljivo ili nije odabrano za dalji proces", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.saveStatus": "Sačuvajte status", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Razmatra se za realizaciju u sledećim koracima", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Razmat<PERSON> se", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Pregledano ali nije obrađ<PERSON>", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Upravljajte unosima i njihovim statusima", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Menadžer unosa | Participativna platforma od {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Pružite povratne informacije, dodajte teme i premeštajte unose iz jednog projekta u drugi", "app.containers.admin.ideas.all.inputManagerPageTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideas.all.tabOverview": "Overview", "app.containers.admin.import.importInputs": "<PERSON><PERSON><PERSON>", "app.containers.admin.import.importNoLongerAvailable3": "This feature is no longer available here. To import inputs to an ideation phase, go to the phase and select \"Import\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 додатно администраторско место} other {# додатна администраторска места}} и {managerSeats, plural, one {1 додатно менаџерско место} other {# додатна менаџерска места}} биће додато преко ограничења.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 додатно администраторско место ће бити додато преко ограничења} other {# додатна администраторска места ће бити додата преко ограничења}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 додатно менаџерско место ће бити додато преко ограничења} other {# додатна менаџерска места ће бити додата преко ограничења}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Потврдите и пошаљите позивнице", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Потврдите утицај на коришћење седишта", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Достигли сте ограничење расположивих места у оквиру свог плана.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Administrators and the managers of this project", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Само администратори и сарадници", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Само администратори платформе, менаџери фасцикли и менаџери пројеката могу да предузму акцију", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Bilo ko", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "Свако укључујући нерегистроване кориснике може учествовати.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Određene grupe korisnika", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Корисници у одређеним групама корисника могу учествовати. Можете управљати корисничким групама на картици „Корисници“.", "app.containers.admin.project.permissions.viewingRightsTitle": "Ko može videti ovaj projekat?", "app.containers.phaseConfig.enableSimilarInputDetection": "Enable similar input detection", "app.containers.phaseConfig.similarInputDetectionTitle": "Similar input detection", "app.containers.phaseConfig.similarInputDetectionTooltip": "Show participants similar input while they type to help avoid duplicates.", "app.containers.phaseConfig.similarityThresholdBody": "Similarity threshold (body)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "This controls how similar two descriptions must be to be flagged as similar. Use a value between 0 (strict) and 1 (lenient). Lower values return fewer but more accurate matches.", "app.containers.phaseConfig.similarityThresholdTitle": "Similarity threshold (title)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "This controls how similar two titles must be to be flagged as similar. Use a value between 0 (strict) and 1 (lenient). Lower values return fewer but more accurate matches.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "This feature is available as part of an early access offering until June 30th, 2025. If you'd like to continue using it beyond that date, please reach out to your Government Success Manager or admin to discuss activation options.", "app.containers.survey.sentiment.noAnswers2": "No responses at this time.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 comments} one {1 comment} other {# comments}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Participants are users or visitors that have participated in a project, posted or interacted with a proposal or attended events.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "Participants", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Stopa učešća", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Percentage of visitors that become participants.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Ukupno učesnika", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Automatske kampanje", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automatska e-pošta", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "Od {quantity} kampanja", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Kampanje", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Prilagođene kampanje", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Prilagođena e-pošta", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "Email", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Ukupno poslatih poruka e-pošte", "app.modules.commercial.analytics.admin.components.Events.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "<PERSON><PERSON><PERSON><PERSON> dodati<PERSON> doga<PERSON>aja", "app.modules.commercial.analytics.admin.components.Events.upcoming": "U najavi", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "P<PERSON>h<PERSON>ć<PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Pozivnice", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Na čekanju", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Ukupno poslatih poziva", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Idi na menadžera unosa", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Aktivni projekti", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Projekti koji nisu arhivirani i vidljivi su na tabeli 'Aktivni' na početnoj stranici", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Nacrti projekata", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Svi arhivirani projekti i aktivni projekti na vremenskoj liniji koji su završeni računaju se ovde", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projekti", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Ukupno projekata", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "B<PERSON>j projekata koji su vidljivi na platformi", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Nove registracije", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Stopa registracija", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Percentage of visitors that become registered users.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registracije", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Ukupno registracija", "app.modules.commercial.analytics.admin.components.Tab": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Poslednjih 30 dana:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Poslednjih 7 dana:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "<PERSON><PERSON>i stranice po poseti", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "„Posetioci” označava broj jedinstvenih posetilaca. Ako osoba poseti platformu više puta, računa se samo jednom.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Posete", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "„Posete” označava broj sesija. Ako je osoba posetila platformu više puta, ra<PERSON><PERSON> se svaka poseta.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Juče:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Count", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Procenat <PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "klikni ovde", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Upućivači", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "<PERSON><PERSON> pregled cele liste upućivavča, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Izvori <PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Posete", "app.modules.commercial.analytics.admin.components.totalParticipants": "Total participants", "app.modules.commercial.analytics.admin.containers.visitors.noData": "There is no visitor data yet.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "We have changed the way we collect and display visitor data. As a result, visitor data is more accurate and more types of data are available, while still being GDPR compliant. While the data used for the visitors timeline goes back longer, we only started collecting the data for the \"Visit duration\", \"Pageviews per visit\" and the other graphs in November 2024, so before that no data is available. Therefore, if you select data before November 2024, be aware that some graphs might be empty or look odd.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Isporuka e-pošte tokom vremena", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Participants over time", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Registracije tokom vremena", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Datum", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistika", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Ukupna statistika", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Posete i posetioci tokom vremena", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Ukupno u periodu", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Kampanje", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "Number of visitors", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Procenat poseta", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Referrer websites", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Pretraživači", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Društvene m<PERSON>že", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO redirects", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "<PERSON><PERSON>", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Ovu zastavicu možete ukloniti odabirom ove stavke i klikom na dugme za uklanjanje pri vrhu. Ona će se ponovo pojaviti na karticama pregledanog i nepregledanog sadržaja", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Automatski je otkriven neprikladan sadržaj.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "<PERSON>ema postova prijavljenih za pregled od strane zajednice niti označenih neprikladnim sadržajem od strane našeg sistema za obradu prirodnog jezika", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Ukloni {numberOfItems, plural, one {upozorenje sadr<PERSON>} other {# upozorenja sadržaja}}\n", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Prijavljeno kao neprikladno od strane korisnika platforme.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Upozorenja o sadržaju", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Prijavi alatku za izradu", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Stranice prikazane u traci za navigaciju", "app.modules.navbar.admin.containers.addProject": "Add project to navbar", "app.modules.navbar.admin.containers.createCustomPageButton": "Kreiraj prilagođenu stranicu", "app.modules.navbar.admin.containers.deletePageConfirmation": "Da li ste sigurni da želite da izbrišete ovu stranicu? Ova radnja ne može se poništiti. Takođe možete da uklonite stranicu iz trake za navigaciju, ako još uvek niste spremni da je izbrišete.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "You can only add up to 5 items to the navigation bar", "app.modules.navbar.admin.containers.pageHeader": "Stranice i meni", "app.modules.navbar.admin.containers.pageSubtitle": "U vašoj traci za navigaciju može biti prikazano do pet stranica, pored Početne i projektnih stranica. Možete da preimenujete stavke menija, preuređujete i dodajete nove stranice sa sopstvenim sadržajem.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widgets", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Use the ☰ icon below to drag AI content into your report.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "There are no available AI insights. You can create them in your project.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Go to project", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "Question", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Select phase", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Unlock AI analysis", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Pull AI-generated insights into your report", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Report faster with AI", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Reporting with AI is not included in your current plan. Talk to your Government Success Manager to unlock this feature.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "This is not included in your current plan. Reach out to your Government Success Manager or admin to unlock it.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Group by registration field", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Group by survey question", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Group mode", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Group survey responses by registration fields (gender, location, age, etc) or other survey questions.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "None", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "Question", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Registration field", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Survey phase", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "Survey question", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Are you sure you want to delete this?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "Cancel", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "Delete", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "Edit", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Post your comment", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "Save", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "Write your comment here", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Click on the buttons below to follow or unfollow. The number of projects is shown in brackets.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "In your area", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "Done", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Follow preferences", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "There are currently no active projects given your follow preferences.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "This widget shows projects associated with the \"areas\" the user follows. Note that your platform might use a different name for \"areas\"- see the \"Areas\" tab in the platform settings. If the user does not follow any areas yet, the widget will show the available areas to follow. In this case the widget will show a maximum of 100 areas.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "No published projects or folders available", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Published projects and folders", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "This widget will case the projects and folders that are currently published, respecting the ordering defined on the projects page. This behavior is the same as the \"active\" tab of the \"legacy\" projects widget.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "No projects or folders selected", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Select projects or folders", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Selected projects and folders", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "With this widget, you can select and determine the order in which you want projects or folders to show to users.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projects", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "poseti naš centar za podršku", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Za više informacija o preporučenim rezolucijama slike, {supportPageLink}."}
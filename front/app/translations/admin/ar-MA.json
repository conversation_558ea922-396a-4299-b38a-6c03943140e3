{"UI.FormComponents.required": "مطلوب", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "نص ترويسة المستخدمين المسجلين", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "تحذير: اللون الذي اخترته ليس بالتباين الكافي. ما يعني أن النص ستصعُب قراءته. اختر لونًا أغمق لتحسين قابلية القراءة.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "إضافة أحداث إلى شريط التنقل", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "عند تمكين هذه الميزة، سيُضاف إلى شريط التنقل رابط يؤدي إلى جميع أحداث المشروع.", "app.components.AdminPage.SettingsPage.eventsSection": "الأحداث", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "قسم قابل للتخصيص في الصفحة الرئيسية", "app.components.ProjectTemplatePreview.close": "إغلاق", "app.components.ProjectTemplatePreview.createProject": "أنشئ مشروعًا", "app.components.ProjectTemplatePreview.goBack": "العودة", "app.components.ProjectTemplatePreview.goBackTo": "العودة إلى {goBackLink}.", "app.components.ProjectTemplatePreview.infoboxLine1": "هل ترغب في استخدام هذا القالب لمشروع المشاركة الخاص بك؟", "app.components.ProjectTemplatePreview.infoboxLine2": "تواصل مع الشخص المسؤول في إدارة مدينتك، أو تواصل مع {link}.", "app.components.ProjectTemplatePreview.projectFolder": "م<PERSON><PERSON><PERSON> المشاريع", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "التاريخ المحدد غير صالح. يُرجى تحديد تاريخ بالتنسيق التالي: سنة-شهر-يوم", "app.components.ProjectTemplatePreview.projectNoStartDateError": "يُرجى تحديد تاريخ بدء المشروع", "app.components.ProjectTemplatePreview.projectStartDate": "تاريخ بدء مشروعك", "app.components.ProjectTemplatePreview.projectTitle": "عنوان مشروعك", "app.components.ProjectTemplatePreview.projectTitleError": "اكتب عنوان مشروعك", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "يُرجى كتابة عنوان المشروع لجميع اللغات", "app.components.ProjectTemplatePreview.projectsOverviewPage": "صفحة النظرة العامة على المشاريع", "app.components.ProjectTemplatePreview.responseError": "حدث خطأ ما، حاول مجددًا في وقت لاحق، أو تواصل مع <EMAIL>.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "شا<PERSON><PERSON> المزيد من القوالب", "app.components.ProjectTemplatePreview.successMessage": "تم إنشاء المشروع بنجاح!", "app.components.ProjectTemplatePreview.typeProjectName": "اكتب اسم المشروع", "app.components.ProjectTemplatePreview.useTemplate": "استخدم هذا القالب", "app.components.UserSearch.addModerators": "إضافة", "app.components.UserSearch.searchUsers": "ابحث في المستخدمين", "app.components.admin.Graphs": "لا تتوفر أي بيانات باستخدام عوامل التصفية الحالية.", "app.components.admin.Graphs.noDataShort": "لا تتوفر أي بيانات.", "app.components.admin.InputManager.onePost": "مُدخل 1 ", "app.components.admin.PostManager.PostPreview.assignee": "المعيّن إليه", "app.components.admin.PostManager.PostPreview.cancelEdit": "إلغاء التعديل", "app.components.admin.PostManager.PostPreview.currentStatus": "الحالة الحالية", "app.components.admin.PostManager.PostPreview.delete": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "هل أنت مُتأكد من حذف هذا المُدخل؟ لا يُمكن إلغاء هذا الإجراء. ", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "هل أنت مُتأكد من حذف هذا المُدخل؟ سيتم حذف المُدخل من جميع مراحل المشروع ولا يُمكن استعادته. ", "app.components.admin.PostManager.PostPreview.edit": "تعديل", "app.components.admin.PostManager.PostPreview.noOne": "غير مُعيّن", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "عدد المرات التي تمّ فيها إدراج هذا في الميزانيات القائمة على المشاركة لمُشاركين آخرين ", "app.components.admin.PostManager.PostPreview.picks": "الاختيارات: {picksNumber}", "app.components.admin.PostManager.PostPreview.save": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.submitError": "خطأ", "app.components.admin.PostManager.allPhases": "جميع المراحل", "app.components.admin.PostManager.allProjects": "جميع المشاريع", "app.components.admin.PostManager.allStatuses": "جميع الحالات", "app.components.admin.PostManager.allTopics": "جميع الموضوعات", "app.components.admin.PostManager.anyAssignment": "<PERSON>ي مهمة", "app.components.admin.PostManager.assignedTo": "{assigneeName}", "app.components.admin.PostManager.assignedToMe": "معيّن إلي", "app.components.admin.PostManager.assignee": "المعيّن إليه", "app.components.admin.PostManager.comments": "التعليقات", "app.components.admin.PostManager.currentLat": "دائرة العرض المركزية", "app.components.admin.PostManager.currentLng": "<PERSON><PERSON> الطول المركزي", "app.components.admin.PostManager.currentZoomLevel": "مستوى التكبير/التصغير", "app.components.admin.PostManager.delete": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.deleteAllSelectedInputs": "حذف {count} مُدخلات", "app.components.admin.PostManager.deleteConfirmation": "هل تريد بالت<PERSON>كيد حذف هذه الطبقة؟", "app.components.admin.PostManager.edit": "تعديل", "app.components.admin.PostManager.exportAllInputs": "استكشف جميع المُدخلات (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "تصدير جميع التعليقات (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "تصدير جميع التعليقات لهذا المشروع (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "استكشف مُدخلات هذا المشروع (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "استكشف المُدخلات المُختارة (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "استكشف التعليقات للمُدخلات المُختارة (.xslx)", "app.components.admin.PostManager.exports": "التصديرات", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "اختر كيف يمكن للآخرين رؤية اسمك", "app.components.admin.PostManager.feedbackBodyPlaceholder": "فسّر سبب تغيير هذه الحالة", "app.components.admin.PostManager.goToDefaultMapView": "انتقال إلى مركز الخريطة الافتراضي", "app.components.admin.PostManager.hiddenFieldsLink": "حقول مخفية", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "نصيحة: أضِ<PERSON> {hiddenFieldsLink} عند إعداد استبيان Typeform لمعرفة من أجاب عليه.", "app.components.admin.PostManager.importError": "تعذر استيراد الملف المحدد؛ لأنه ليس ملف GeoJSON صالحًا", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputManagerHeader": "مُدخلات", "app.components.admin.PostManager.inputs": "مُدخلات", "app.components.admin.PostManager.inputsExportFileName": "المدخلات", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "اعرض المُدخلات التي تحتاج إلي آراء ومقترحات تقييمية فقط", "app.components.admin.PostManager.latestFeedbackMode": "استخدم أحدث تحديث رسمي", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "سيؤدي نقل هذا الإدخال من مشروعه الحالي إلى فقدان المعلومات عن مرحلته المعيَّنة، فهل تريد المتابعة؟", "app.components.admin.PostManager.multipleInputs": "{ideaCount} مُدخلات", "app.components.admin.PostManager.newFeedbackMode": "اكتب تحديثًا جديدًا لتفسير هذا التغيير", "app.components.admin.PostManager.noFilteredResults": "لم تحصد المُرشحات التي اخترتها أي نتائج ", "app.components.admin.PostManager.noOne": "غير مُعيّن", "app.components.admin.PostManager.officialUpdateAuthor": "اختر كيف يمكن للآخرين رؤية اسمك", "app.components.admin.PostManager.officialUpdateBody": "فسّر سبب تغيير هذه الحالة", "app.components.admin.PostManager.participatoryBudgettingPicks": "الاختيارات", "app.components.admin.PostManager.pbItemCountTooltip": "عدد المرات التي تمّ فيها إدراج هذا في الميزانيات القائمة على المشاركة لمُشاركين آخرين ", "app.components.admin.PostManager.projectsTab": "المشاريع", "app.components.admin.PostManager.projectsTabTooltipContent": "يُمكنك سحب وإسقاط المنشورات لتحريكها من مشروع لآخر. لاحظ أنك لا تزال تحتاج إلى إضافة المنشور إلى مرحلة مُحددة في المشروعات الموجودة بالجدول الزمني.", "app.components.admin.PostManager.publication_date": "تم النشر في", "app.components.admin.PostManager.resetFiltersButton": "إعادة تعيين عوامل التصفية", "app.components.admin.PostManager.resetInputFiltersDescription": "أعد ضبط المُرشحات لتعرض جميع المُدخلات. ", "app.components.admin.PostManager.saved": "تم الحفظ", "app.components.admin.PostManager.setAsDefaultMapView": "حفظ نقطة المركز الحالية ومستوى التكبير/التصغير باعتبارها إعدادين افتراضيين للخريطة", "app.components.admin.PostManager.statusChangeGenericError": "حدث خطأ ما، يُرجى المحاولة مرة أخرى لاحقًا أو التواصل مع <EMAIL>.", "app.components.admin.PostManager.statusChangeSave": "غيّر الحالة", "app.components.admin.PostManager.statusesTab": "الحالات", "app.components.admin.PostManager.statusesTabTooltipContent": "قُم بتغيير حالة المنشور باستخدام السحب والإسقاط. سيتلقى المؤلف الأصلي والمُساهمون إشعاراً بتغيير الحالة. ", "app.components.admin.PostManager.timelineTab": "المراحل", "app.components.admin.PostManager.timelineTabTooltipText": "قُم بسحب وإسقاط المُدخلات لتنسخهم إلى مراحل مشروع مُختلفة. ", "app.components.admin.PostManager.title": "العنوان", "app.components.admin.PostManager.topicsTab": "الموضوعات", "app.components.admin.PostManager.topicsTabTooltipText": "قُم بإضافة مواضيع إلى مُدخل باستخدام السحب والإسقاط. ", "app.components.admin.PostManager.votes": "الأصوات", "app.components.admin.ReportExportMenu.FileName.fromFilter": "من", "app.components.admin.ReportExportMenu.FileName.groupFilter": "مجموعة", "app.components.admin.ReportExportMenu.FileName.projectFilter": "مشروع", "app.components.admin.ReportExportMenu.FileName.topicFilter": "موضوع", "app.components.admin.ReportExportMenu.FileName.untilFilter": "ح<PERSON><PERSON>", "app.components.admin.ReportExportMenu.downloadPng": "تنزيل كـ PNG", "app.components.admin.ReportExportMenu.downloadSvg": "تنزيل كـ SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "تنزيل إكسل", "app.components.admin.SlugInput.regexError": "لا يمكن أن يحتوي معرِّف الصفحة إلا على حروف أبجدية إنجليزية منتظمة وبحالة أحرف صغيرة (من a إلى z) وأرقام (من 0 إلى 9) وواصلات (-). لا يمكن استخدام واصلة في موضع الحرف الأول أو الأخير. يُحظر استخدام وصلات متتابعة (--).", "app.components.admin.TerminologyConfig.saveButton": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "مساهمة ", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "يمكنك العثور في {googleFormsTooltipLink} على مزيد من المعلومات حول كيفية تضمين رابط بشأن نماذج Google.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "مقالة الدعم هذه", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "الفكرة", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "بماذا ينبغي تسمية أحد المدخلات؟", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "مشكلة", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "ي<PERSON><PERSON> إ<PERSON><PERSON>ا<PERSON> حد أقصى للميزانية", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "لا يمكن أن يكون الحد الأدنى للميزانية أكبر من الحد الأقصى للميزانية", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "ي<PERSON><PERSON> إ<PERSON><PERSON>ا<PERSON> حد أدنى للميزانية", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "خيار", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "مشروع", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "سؤال", "app.containers.Admin.Campaigns.campaignFrom": "من:", "app.containers.Admin.Campaigns.campaignTo": "إلى:", "app.containers.Admin.Campaigns.noAccess": "نعتذر، ولكن يبدو أنه ليس بإمكانك الوصول إلى قسم الرسائل الإلكترونية", "app.containers.Admin.Campaigns.tabAutomatedEmails": "رسائل إلكترونية تلقائية", "app.containers.Admin.Invitations.addToGroupLabel": "أضِف هؤلاء الأشخاص إلى مجموعات مستخدمين يدوية محددة", "app.containers.Admin.Invitations.adminLabelTooltip": "عند تحديد هذا الخيار، سيتمكن الأشخاص الذين تدعوهم من الوصول إلى جميع إعدادات منصتك.", "app.containers.Admin.Invitations.configureInvitations": "3. تهيئة الدعوات", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "لا توجد دعوات تطابق بحثك", "app.containers.Admin.Invitations.deleteInvite": "إلغاء", "app.containers.Admin.Invitations.deleteInviteTooltip": "سيسمح لك إلغاء الدعوة بإعادة إرسال دعوة إلى هذا الشخص.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. تنزيل وتعبئة القالب", "app.containers.Admin.Invitations.downloadTemplate": "تنزيل القالب", "app.containers.Admin.Invitations.email": "الب<PERSON>يد الإلكتروني", "app.containers.Admin.Invitations.emailListLabel": "أدخل عناوين البريد الإلكتروني مفصولة بفواصل في المربع أدناه.", "app.containers.Admin.Invitations.exportInvites": "تصدير قائمة الدعوة", "app.containers.Admin.Invitations.fileRequirements": "هام: لا تقم بحذف أي عمود من قالب الاستيراد- ويجب ترك الأعمدة التي لم يتم استخدامها فارغة. قبل استيراد الملف، يرجى التأكد من عدم وجود عناوين بريد إلكتروني مكررة، حيث قد يتسبب ذلك في حدوث أخطاء في الدعوات.", "app.containers.Admin.Invitations.filetypeError": "نوع ملف غير صحيح. فقط ملفات XLSX مدعومة.", "app.containers.Admin.Invitations.groupsPlaceholder": "لم يتم تحديد مجموعة", "app.containers.Admin.Invitations.helmetDescription": "ادعُ المستخدمين إلى المنصة", "app.containers.Admin.Invitations.helmetTitle": "اللوحة الرئيسية لدعوات المسؤول", "app.containers.Admin.Invitations.importOptionsInfo": "يمكنك إضافة رسالة شخصية إلى الدعوة، أو إضافة أشخاص إلى مجموعة أو منحهم حقوق المسؤول أو المُشرف. لمزيد من المعلومات قم بزيارة {supportPageLink}.", "app.containers.Admin.Invitations.importTab": "استيراد عناوين البريد الإلكتروني", "app.containers.Admin.Invitations.invitationOptions": "خيارات الدعوة", "app.containers.Admin.Invitations.invitationSubtitle": "ادعُ الأشخاص الذين لم يتم تسجيلهم على المنصة بعد. قم باستيراد عناوين البريد الإلكتروني الخاصة بهم عن طريق وضعها في قالب الاستيراد أو بإدخال عناوين البريد الإلكتروني يدويًا. إذا رغبت في ذلك، يمكنك إضافة رسالة شخصية أو منح الأشخاص حقوقًا إضافية أو إضافتهم إلى مجموعة يدوية.", "app.containers.Admin.Invitations.invitePeople": "الدعوات", "app.containers.Admin.Invitations.inviteStatus": "الحالة", "app.containers.Admin.Invitations.inviteStatusAccepted": "مقبولة", "app.containers.Admin.Invitations.inviteStatusPending": "قيد الانتظار", "app.containers.Admin.Invitations.inviteTextLabel": "أضِف رسالة شخصية إلى الدعوة", "app.containers.Admin.Invitations.invitedSince": "تمت الدعوة في", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "حدّد لغة الدعوة", "app.containers.Admin.Invitations.moderatorLabel": "امنح هؤلاء الأشخاص حقوق إدارة المشروع", "app.containers.Admin.Invitations.moderatorLabelTooltip": "عند تحديد هذا الخيار، سيتولى الأشخاص المدعوون منصب \"مدير المشروع\" للمشاريع المحددة. للمزيد من المعلومات حول حقوق الإدارة زُر الرابط التالي {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "هنا", "app.containers.Admin.Invitations.name": "الاسم", "app.containers.Admin.Invitations.processing": "إرسال الدعوات جارٍ. يُرجى الانتظار...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "لم يتم تحديد أي مشروع/ مشاريع", "app.containers.Admin.Invitations.save": "أرسل دعواتك", "app.containers.Admin.Invitations.saveErrorMessage": "حدث خطأ واحد أو أكثر.\n      لذلك لم يتم إرسال أي دعوة.\n      يُرجى تصحيح الخطأ/ الأخطاء المُدرجة أدناه والمحاولة مجددًا.", "app.containers.Admin.Invitations.saveSuccess": "نجاح!", "app.containers.Admin.Invitations.saveSuccessMessage": "تم إرسال الدعوة بنجاح.", "app.containers.Admin.Invitations.supportPage": "صفحة الدعم", "app.containers.Admin.Invitations.supportPageLinkText": "زُر صفحة الدعم", "app.containers.Admin.Invitations.tabAllInvitations": "كل الدعوات", "app.containers.Admin.Invitations.tabInviteUsers": "ادعُ الناس", "app.containers.Admin.Invitations.textTab": "أدخل عناوين البريد الإلكتروني", "app.containers.Admin.Invitations.unknownError": "حدث خطأ ما. يُرجى المحاولة مجددًا في وقت لاحق.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. تحميل ملفك المكتمل", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} إذا كنت تريد معرفة المزيد من المعلومات حول جميع الأعمدة المدعومة في قالب الاستيراد.", "app.containers.Admin.Moderation.all": "الكل", "app.containers.Admin.Moderation.belongsTo": "ينتمي إلى", "app.containers.Admin.Moderation.collapse": "طي", "app.containers.Admin.Moderation.comment": "تعليق", "app.containers.Admin.Moderation.content": "المحتوى", "app.containers.Admin.Moderation.date": "التاريخ", "app.containers.Admin.Moderation.goToComment": "فتح هذا التعليق في علامة تبويب جديدة", "app.containers.Admin.Moderation.goToPost": "فتح هذا المنشور في علامة تبويب جديدة", "app.containers.Admin.Moderation.goToProposal": "فتح هذا المقترح في علامة تبويب جديدة", "app.containers.Admin.Moderation.markFlagsError": "تعذر وضع علامة على هذا العنصر (العناصر). أعد المحاولة.", "app.containers.Admin.Moderation.markNotSeen": "وضع علامة على {selectedItemsCount, plural, one {# عنصر} other {# عنصر/عناصر}} باعتباره/باعتبارها غير مرئي/مرئية", "app.containers.Admin.Moderation.markSeen": "وضع علامة على {selectedItemsCount, plural, one {# عنصر} other {# عنصر/عناصر}} باعتباره/باعتبارها مرئيًا/مرئية", "app.containers.Admin.Moderation.moderationsTooltip": "تتيح لك هذه الصفحة التحقق سريعًا من جميع المنشورات الجديدة التي تم نشرها في منصتك، بما فيها الأفكار والتعليقات. يمكنك تمييز منشورات بعلامة \"مرئية\" بحيث يعرف الآخرون المنشورات التي لا تزال بحاجة إلى معالجة.", "app.containers.Admin.Moderation.noUnviewedItems": "لا توجد عناصر لم تتم مشاهدتها", "app.containers.Admin.Moderation.noViewedItems": "لا توجد عناصر تمت مشاهدتها", "app.containers.Admin.Moderation.post": "منشور", "app.containers.Admin.Moderation.profanityBlockerSetting": "مانع البذاءات", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "حظر المنشورات التي تحتوي على الألفاظ المسيئة الأكثر شيوعًا المبلَّغ عنها.", "app.containers.Admin.Moderation.project": "المشروع", "app.containers.Admin.Moderation.read": "تمت مشاهدته", "app.containers.Admin.Moderation.readMore": "اقر<PERSON> المزيد", "app.containers.Admin.Moderation.removeFlagsError": "تعذر إزالة التحذير (التحذيرات). أعد المحاولة.", "app.containers.Admin.Moderation.rowsPerPage": "الصفوف لكل صفحة", "app.containers.Admin.Moderation.settings": "الإعدادات", "app.containers.Admin.Moderation.settingsSavingError": "تعذر الحفظ. جرِّب تغيير الإعداد مجددًا.", "app.containers.Admin.Moderation.show": "اعرض", "app.containers.Admin.Moderation.status": "الحالة", "app.containers.Admin.Moderation.successfulUpdateSettings": "تم تحديث الإعدادات بنجاح.", "app.containers.Admin.Moderation.type": "النوع", "app.containers.Admin.Moderation.unread": "لم تتم مشاهدته", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "تتكون هذه الصفحة من الأقسام التالية. يمكنك تشغيلها أو إيقاف تشغيلها وتعديلها حسب الحاجة.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "الأقسام", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "عرض الصفحة", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "عدم إظهار في الصفحة", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "إظهار في الصفحة", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "المُرفقات", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "أضف الملفات التي ستتوفر للتنزيل من الصفحة (بحد أقصى 50 ميجابايت).", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "قسم المعلومات السفلي", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "أض<PERSON> محتواك الخاص إلى القسم القابل للتخصيص في الجزء السفلي من الصفحة.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "تعديل", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "قائمة الأحداث", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "إظهار الأحداث المتعلقة بالمشاريع.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "البانر الرئيسي", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "أضف طابعك الشخصي على صورة ونص البانر.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "قائمة المشاريع", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "إظهار المشاريع حسب إعدادات صفحتك. يمكنك معاينة المشاريع التي سيتم إظهارها.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "قسم المعلومات العلوي", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "أض<PERSON> محتواك الخاص إلى القسم القابل للتخصيص في الجزء العلوي من الصفحة.", "app.containers.Admin.PagesAndMenu.addButton": "إضافة إلى شريط التنقل", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "الاسم في شريط التنقل", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "هل تريد بالتأكيد حذف هذه الصفحة؟ لا يمكن التراجع عن هذا الإجراء.", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "صفحات متوفرة أخرى", "app.containers.Admin.PagesAndMenu.components.savePage": "حف<PERSON> الصفحة", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "تم حفظ الصفحة بنجاح.", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "المُرفقات (بحجم أقصى 50 ميغابايت)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "نجاح", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "المحتوى", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "تعذر حفظ المرفقات", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "ينبغي ألا يزيد حجم الملفات عن 50 ميجابايت. ستظهر الملفات المضافة في الجزء السفلي من هذه الصفحة.", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "تم حفظ المرفقات", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "المرفقات | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "المُرفقات", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "حفظ المرفقات وتمكينها", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "ح<PERSON><PERSON> المرفقات", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "أد<PERSON><PERSON> المحتوى بجميع اللغات", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "نجاح", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "المحتوى", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "تعذر حفظ قسم المعلومات السفلي", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "تم حفظ قسم المعلومات السفلي", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "قسم المعلومات السفلي", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "حفظ قسم المعلومات السفلي وتمكينه", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "حفظ قسم المعلومات السفلي", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "يرجى تحديد وسم واحد على الأقل", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "نجاح", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "<PERSON>س<PERSON> المنطقة", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "حس<PERSON> الوسم (الوسوم)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "المحتوى", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "تعديل الصفحة المخصصة", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "المشاريع المرتبطة", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "حدد المشاريع والأحداث ذات الصلة التي يمكن عرضها في الصفحة.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "تم إنشاء الصفحة بنجاح.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "تم حفظ الصفحة بنجاح.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "تم حفظ الصفحة المخصصة.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "العنوان في شريط التنقل", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "إنشاء صفحة مخصصة | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "إنشاء صفحة مخصصة", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "بلا", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "إعدادات الصفحة", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "حفظ الصفحة المخصصة", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "ير<PERSON>ى تحديد منطقة", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "منطقة محددة", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "وسوم محددة", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "لا يمكن أن يحتوي معرِّف الصفحة إلا على حروف أبجدية إنجليزية منتظمة وبحالة أحرف صغيرة (من a إلى z) وأرقام (من 0 إلى 9) وواصلات (-). لا يمكن استخدام واصلة في موضع الحرف الأول أو الأخير. يُحظر استخدام وصلات متتابعة (--).", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "يج<PERSON> إدخال معرِّف الصفحة", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "العنوان", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "أدخل عنوانًا بكل لغة", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "أدخل عنوانًا", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "عرض الصفحة المخصصة", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "زر", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "تعديل الصفحة المخصصة | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "مح<PERSON>وى الصفحة", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "تعديل", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "لا تتوفر مشاريع مستندة إلى {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "لا يتضمن هذا المشروع أي عامل تصفية حسب الوسم أو المنطقة؛ لذا، لن يتم عرض أي مشاريع.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "قائمة المشاريع | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "إعدادات الصفحة", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "قائمة المشاريع", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "سيتم إظهار المشاريع التالية في هذه الصفحة حسب {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "افتراضي", "app.containers.Admin.PagesAndMenu.deleteButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.editButton": "تعديل", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "نجاح", "app.containers.Admin.PagesAndMenu.heroBannerError": "تعذر حفظ البانر الرئيسي", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "تم حفظ البانر الرئيسي", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "حفظ البانر الرئيسي", "app.containers.Admin.PagesAndMenu.homeTitle": "الرئيسية", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "أد<PERSON><PERSON> المحتوى بلغة واحدة على الأقل", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "الصفحات والقائمة | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "إزالة من شريط التنقل", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "حفظ البانر الرئيسي وتمكينه", "app.containers.Admin.PagesAndMenu.title": "صفحات وقائمة", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "نجاح", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "المحتوى", "app.containers.Admin.PagesAndMenu.topInfoError": "تعذر حفظ قسم المعلومات العلوي", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "تم حفظ قسم المعلومات العلوي", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "قسم المعلومات العلوي | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "قسم المعلومات العلوي", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "حفظ قسم المعلومات العلوي وتمكينه", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "حفظ قسم المعلومات العلوي", "app.containers.Admin.PagesAndMenu.viewButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "حذ<PERSON> المجموعة", "app.containers.Admin.Users.GroupsHeader.editGroup": "تعديل المجموعة", "app.containers.Admin.Users.GroupsPanel.allUsers": "المستخدمون", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "المجموعات", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "أنشئ مجموعات وأضِف المستخدمين إليها.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "الدعوة معلقة", "app.containers.Admin.Users.admin": "المسؤول", "app.containers.Admin.Users.deleteUser": "احذف هذا المستخدم", "app.containers.Admin.Users.email": "الب<PERSON>يد الإلكتروني", "app.containers.Admin.Users.helmetDescription": "قائمة المستخدم في مكتب المسؤول للدعم", "app.containers.Admin.Users.helmetTitle": "المسؤول - لوحة المستخدمين الرئيسية", "app.containers.Admin.Users.name": "الاسم", "app.containers.Admin.Users.options": "الخيارات", "app.containers.Admin.Users.seeProfile": "شا<PERSON><PERSON> الملف التعريفي لهذا المستخدم", "app.containers.Admin.Users.userDeletionConfirmation": "أتود إزالة هذا المستخدم نهائيًا؟", "app.containers.Admin.Users.userDeletionFailed": "حدث خطأ أثناء حذف هذا المستخدم، يُرجى المحاولة مجددًا.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.youCantDeleteYourself": "لا يمكنك حذف حسابك الخاص عبر صفحة المسؤول للمستخدمين", "app.containers.Admin.Users.youCantUnadminYourself": "لا يمكنك التخلي عن دورك كمسؤول الآن", "app.containers.Admin.emails.addCampaignTitle": "أنشئ بريدًا إلكترونيًا جديدًا", "app.containers.Admin.emails.allUsers": "جميع المستخدمين", "app.containers.Admin.emails.changeRecipientsButton": "غيّر المستلمين", "app.containers.Admin.emails.confirmSendHeader": "إرسال الرسالة الإلكترونية إلى كل المستخدمين؟", "app.containers.Admin.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.draft": "مسوّدة", "app.containers.Admin.emails.editButtonLabel": "تعديل", "app.containers.Admin.emails.editCampaignTitle": "تعديل الحملة", "app.containers.Admin.emails.failed": "فشل", "app.containers.Admin.emails.fieldBody": "الرسالة", "app.containers.Admin.emails.fieldBodyError": "إدخال رسالة بريد إلكتروني", "app.containers.Admin.emails.fieldReplyTo": "أرسل الردود إلى", "app.containers.Admin.emails.fieldReplyToEmailError": "إدخال عنوان بريد إلكتروني بالصيغة الصحيحة، مثل: <EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "إدخال عنوان بريد إلكتروني", "app.containers.Admin.emails.fieldReplyToTooltip": "يمكنك اختيار عنوان البريد الإلكتروني الذي سيتلقى جميع الردود مباشرة من المستخدمين.", "app.containers.Admin.emails.fieldSender": "من", "app.containers.Admin.emails.fieldSenderError": "إدخال مرسل لرسالة البريد الإلكتروني", "app.containers.Admin.emails.fieldSenderTooltip": "يمكنك تحديد المُرسل الذي سيراه مستلمو الرسالة الإلكترونية.", "app.containers.Admin.emails.fieldSubject": "الموضوع", "app.containers.Admin.emails.fieldSubjectError": "إدخال موضوع لرسالة البريد الإلكتروني", "app.containers.Admin.emails.fieldSubjectTooltip": "سيظهر هذا في حقل الموضوع في الرسالة الإلكترونية وفي النظرة العامة على صندوق الوارد للمستخدم. اجعله واضحًا وجذابًا.", "app.containers.Admin.emails.fieldTo": "إ<PERSON><PERSON>", "app.containers.Admin.emails.fieldToTooltip": "يمكنك تحديد مجموعات المستخدمين التي ستتلقى رسالتك الإلكترونية.", "app.containers.Admin.emails.formSave": "حفظ كمسوّدة", "app.containers.Admin.emails.groups": "المجموعات", "app.containers.Admin.emails.helmetDescription": "أرسل الرسائل الإلكترونية إلى مجموعة مواطنين محددة وفعّل الحملات التلقائية", "app.containers.Admin.emails.previewSentConfirmation": "تم إرسال رسالة معاينة إلكترونية إلى عنوان بريدك الإلكتروني", "app.containers.Admin.emails.previewTitle": "معاينة", "app.containers.Admin.emails.send": "إرسال", "app.containers.Admin.emails.sendNowButton": "أرسل الآن", "app.containers.Admin.emails.sendTestEmailButton": "أرسل لي رسالة اختبار إلكترونية", "app.containers.Admin.emails.senderRecipients": "المُرسل والمستلمون", "app.containers.Admin.emails.sending": "إرسال", "app.containers.Admin.emails.sent": "تم الإرسال", "app.containers.Admin.emails.toAllUsers": "هل ترغب في إرسال هذه الرسالة الإلكترونية إلى جميع المستخدمين؟", "app.containers.Admin.ideas.import": "استيراد", "app.containers.Admin.messaging.helmetTitle": "رسائل", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "هذه الصورة جزء من بطاقة المجلد؛ وهي البطاقة التي تلخص المجلد وتظهر في الصفحة الرئيسية على سبيل المثال. لمزيد من المعلومات حول مستويات الدقة الموصى بها للصور، {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "تظهر هذه الصورة في الجزء العلوي من صفحة المجلد. لمزيد من المعلومات حول مستويات الدقة الموصى بها للصور، {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "زيارة مركز الدعم التابع لنا", "app.containers.Admin.projects.all.components.archived": "مؤرشف", "app.containers.Admin.projects.all.components.draft": "مسوّدة", "app.containers.Admin.projects.all.components.manageButtonLabel": "إدارة", "app.containers.Admin.projects.all.deleteFolderConfirm": "هل تريد بالتأكيد حذف هذا المجلد؟ سيتم أيضًا حذف جميع المشاريع داخل المجلد. لا يمكن التراجع عن هذا الإجراء.", "app.containers.Admin.projects.all.deleteFolderError": "حدث خطأ أثناء إزالة هذا المجلد. يُرجى المحاولة مجددًا.", "app.containers.Admin.projects.all.deleteProjectConfirmation": "هل تريد حذف هذا المشروع بالتأكيد؟ هذا إجراء لا يمكن التراجع عنه.", "app.containers.Admin.projects.all.deleteProjectError": "حد<PERSON> خطأ أثناء حذف هذا المشروع، يُرجى المحاولة مجددًا في وقت لاحق.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "يشير إلى نص معين. يمكنك تعديله وتهيئته باستخدام المحرر في اللوحة على اليمين.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "المشاركون", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "نتائج المشروع", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "ملخص التقرير", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "أض<PERSON> <PERSON><PERSON><PERSON> المشروع وطرق المشاركة المتبعة والنتيجة", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "زوار", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "العنوان مأخوذ بالفعل", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "لا يوجد مشروع", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "لا يمكنك تكرار هذا التقرير لأنه يحتوي على بيانات لا يمكنك الوصول إليها.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "هل أنت متأكد أنك تريد حذف \"{reportName}\"؟ لا يمكن التراجع عن هذا الإجراء.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "هل أنت متأكد أنك تريد حذف هذا التقرير؟ لا يمكن التراجع عن هذا الإجراء.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "ين<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "تعديل", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "تم التعديل {days, plural, no {# يوم} one {# يوم} other {# يوم}} منذ", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "حد<PERSON> خطأ عند محاولة إنشاء هذا التقرير. يرجى إعادة المحاولة لاحقًا.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "البدء بصفحة فارغة", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "عنوان التقرير", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "إنشاء تقرير", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "طباعة إلى PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "البدء بنموذج مشروع", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "نموذج تقرير", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "يوجد بالفعل تقرير بهذا العنوان. يرجى اختيار عنوان مختلف.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "إتاحة للمشاركة بتنسيق PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "للإتاحة للمشاركة مع الجميع، اطبع التقرير بتنسيق PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "إتاحة للمشاركة كرابط ويب", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "لا يمكن الوصول إلى رابط الويب هذا إلا من قِبل المستخدمين المسؤولين", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "مشاركة", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "جميع التقارير", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "إنشاء تقرير", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "أضفِ طابعك الشخصي على التقرير وأتحه للمشاركة مع الأطراف المعنية الداخلية أو المجتمع عبر رابط ويب.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "ستظهر تقاريرك هنا.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "تقارير البحث", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "تقارير مرحلية", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "هذه هي التقارير التي أنشأها مدير النجاح الحكومي الخاص بك", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "التقارير الخاصة بك", "app.containers.Admin.reporting.helmetDescription": "صفحة تقارير المسؤول", "app.containers.Admin.reporting.helmetTitle": "تقديم بلاغات", "app.containers.Admin.reporting.printPrepare": "جارٍ التحضير للطباعة...", "app.containers.Admin.reporting.reportBuilder": "أداة إنشاء التقرير", "app.containers.Admin.reporting.reportHeader": "ترويسة التقرير", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "الإجمالي على المنصة", "app.containers.AdminPage.DashboardPage._blank": "غير معروف", "app.containers.AdminPage.DashboardPage.allGroups": "كل المجموعات", "app.containers.AdminPage.DashboardPage.allProjects": "جميع المشاريع", "app.containers.AdminPage.DashboardPage.allTime": "كل الأوقات", "app.containers.AdminPage.DashboardPage.comments": "التعليقات", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "التعليقات", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "مطلوب مجموعة بيانات أساسية لقياس الطابع التمثيلي لمستخدمي المنصة.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "تتوفر قريبًا", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "نعمل حاليًا على إعداد لوحة معلومات {fieldName}، وستتوفر قريبًا", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "هناك {numberOfHiddenItems, plural, one {# عنصر} other {# عنصر/عناصر}} مختفي/مختفية في هذا الرسم البياني. بدِّل إلى {tableViewLink} لعرض كل البيانات.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} لتسجيل المستخدمين", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "تم تضمين {known} من أصل {total} مستخدم/مستخدمين ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "إظهار {numberOfHiddenItems} أخرى", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "اختياري", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "يرجى توفير مجموعة بيانات أساسية.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "مجموع درجات الطابع التمثيلي:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "يعكس مجموع الدرجات هذا إلى أي مدى من الدقة تشير بيانات مستخدمي المنصة إلى إجمالي المجتمع الإحصائي. اعرف المزيد عن {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "مطلوب", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "إرسال البيانات الأساسية", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "طريقة عرض الجدول", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "إجمالي المجتمع الإحصائي", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "المستخدمون", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "إضافة فئة عمرية", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} فما فوق", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "الفئة العمرية", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "لم يتم تضمين الفئة (الفئات) العمرية {upperBound} فما فوق.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "الفئة العمرية {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "الفئات العمرية", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "فما فوق", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "تطبيق الفئات النموذجية", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "<PERSON><PERSON><PERSON> ال<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "من", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "عيِّن فئات عمرية لتوفيقها مع مجموعة بياناتك الأساسية.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "النطاق", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "إ<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "تعديل الفئات العمرية", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "لم يتم احتساب هذا العنصر.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "شا<PERSON><PERSON> أقل", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "عرض {numberOfHiddenItems} أخرى...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "الشهر الأساسي (اختياري)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "الفئات العمرية (سنة الميلاد)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "تتوفر قريبًا", "app.containers.AdminPage.DashboardPage.components.Field.complete": "مكتمل", "app.containers.AdminPage.DashboardPage.components.Field.default": "افتراضي", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "يرجى تعبئة جميع الخيارات الممكَّنة، أو تعطيل الخيارات التي تريد حذفها من الرسم البياني. يجب تعبئة خيار واحد على الأقل.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "<PERSON>ير مكتمل", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "إجمالي عدد النزلاء", "app.containers.AdminPage.DashboardPage.components.Field.options": "الخيارات", "app.containers.AdminPage.DashboardPage.components.Field.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.saved": "تم الحفظ", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "يرجى {setAgeGroupsLink} أولاً لبدء إدخال البيانات الأساسية.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "تعيين فئات عمرية", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "متوسط مدة الرد: {days} يوم/أيام", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "متوسط عدد الأ<PERSON>ا<PERSON> قبل الرد", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "تم تقديم ملاحظات", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "حالة المدخلات", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "المدخلات حسب الحالة", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "<PERSON><PERSON><PERSON> المدخلات", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "تحديث رسمي", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "نسبة المدخلات", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "مدة الرد", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "الحالة", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "تم تغيير الحالة", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "الإجمالي", "app.containers.AdminPage.DashboardPage.components.editBaseData": "تعديل البيانات الأساسية", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "الطريقة التي نتبعها لحساب مجموع درجات الطابع التمثيلي", "app.containers.AdminPage.DashboardPage.continuousType": "دون جدول زمني", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "المجموع التراكمي", "app.containers.AdminPage.DashboardPage.customDateRange": "مخصص", "app.containers.AdminPage.DashboardPage.day": "يوم", "app.containers.AdminPage.DashboardPage.false": "<PERSON>ا<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.female": "<PERSON>ن<PERSON>ى", "app.containers.AdminPage.DashboardPage.fromTo": "من {from} <PERSON><PERSON><PERSON> {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "اللوحة الرئيسية للأنشطة على المنصة", "app.containers.AdminPage.DashboardPage.helmetTitle": "صفحة اللوحة الرئيسية للمسؤول", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "اختر موردًا لعرضه بحسب المشروع", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "اختر موردًا لعرضه بحسب الموضوع", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "تحديد مجموعة مستخدمين", "app.containers.AdminPage.DashboardPage.male": "ذكر", "app.containers.AdminPage.DashboardPage.month": "شهر", "app.containers.AdminPage.DashboardPage.noData": "لا توجد بيانات لعرضها", "app.containers.AdminPage.DashboardPage.noPhase": "لم تتم تهيئة أي مرحلة لهذا المشروع", "app.containers.AdminPage.DashboardPage.overview.management": "الإدارة", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "المشاريع والمشاركة", "app.containers.AdminPage.DashboardPage.overview.showLess": "<PERSON>ر<PERSON> أقل", "app.containers.AdminPage.DashboardPage.overview.showMore": "اعرض المزيد", "app.containers.AdminPage.DashboardPage.participationPerProject": "المشاركة لكل مشروع", "app.containers.AdminPage.DashboardPage.participationPerTopic": "المشاركة لكل موضوع", "app.containers.AdminPage.DashboardPage.perPeriod": "لكل {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "الـ 30 يومًا السابقة", "app.containers.AdminPage.DashboardPage.previous90Days": "الـ 90 يومًا السابقة", "app.containers.AdminPage.DashboardPage.previousWeek": "الأسبوع الماضي", "app.containers.AdminPage.DashboardPage.previousYear": "السنة الماضية", "app.containers.AdminPage.DashboardPage.projectType": "نوع المشروع: {projectType}", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "مجموعة البيانات الأساسية هذه مطلوبة لحساب الطابع التمثيلي لمستخدمي المنصة مقارنة بإجمالي المجتمع الإحصائي.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "يرجى توفير مجموعة بيانات أساسية.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "اعرف الطابع التمثيلي لمستخدمي منصتك مقارنة بإجمالي بيانات المجتمع الإحصائي باستخدام البيانات المجمَّعة أثناء تسجيل المستخدمين. اعرف المزيد عن {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "اعرف الطابع التمثيلي لمستخدمي منصتك مقارنة بإجمالي بيانات المجتمع الإحصائي باستخدام البيانات المجمَّعة أثناء تسجيل المستخدمين.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "الطابع التمثيلي للمجتمع", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "عودة إلى لوحة المعلومات", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "لا توجد أي حقول تسجيل مضمنة مدعومة حتى الآن.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "يمكنك هنا إظهار/إخفاء عناصر في لوحة المعلومات وإدخال التاريخ الأساسي. لن تظهر هنا سوى الحقول الممكَّنة بشأن {userRegistrationLink}.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "تعديل البيانات الأساسية", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "تسجيل المستخدم", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "إرسال البيانات الأساسية", "app.containers.AdminPage.DashboardPage.resolutionday": "بالأيام", "app.containers.AdminPage.DashboardPage.resolutionmonth": "بالأشهر", "app.containers.AdminPage.DashboardPage.resolutionweek": "بالأسابيع", "app.containers.AdminPage.DashboardPage.selectProject": "تحديد مشروع", "app.containers.AdminPage.DashboardPage.selectedProject": "عامل تصفية المشروع الحالي", "app.containers.AdminPage.DashboardPage.selectedTopic": "عامل تصفية الموضوع الحالي", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "اكتشف ما يحدث على منصتك.", "app.containers.AdminPage.DashboardPage.tabOverview": "نظرة عامة", "app.containers.AdminPage.DashboardPage.tabReports": "المشاريع", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "الطابع التمثيلي", "app.containers.AdminPage.DashboardPage.tabUsers": "المستخدمون", "app.containers.AdminPage.DashboardPage.timelineType": "الجدول الزمني", "app.containers.AdminPage.DashboardPage.titleDashboard": "اللوحة الرئيسية", "app.containers.AdminPage.DashboardPage.total": "الإجمالي", "app.containers.AdminPage.DashboardPage.totalForPeriod": "هذه {period}", "app.containers.AdminPage.DashboardPage.true": "صحيح", "app.containers.AdminPage.DashboardPage.unspecified": "<PERSON>ي<PERSON> محدد", "app.containers.AdminPage.DashboardPage.users": "المستخدمون", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "المستخدمون بحسب العمل", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "المستخدمون بحسب المنطقة الجغرافية", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "المستخدمون بحسب الجنس", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "التسجيلات", "app.containers.AdminPage.DashboardPage.week": "أسبوع", "app.containers.AdminPage.FaviconPage.favicon": "أيقونة المفضلة", "app.containers.AdminPage.FaviconPage.faviconExplaination": "نصائح لاختيار صورة لأيقونة المفضلة: حدّد صورة بسيطة، حيثُ أن حجم الصورة الظاهرة صغير جدًا. يجب حفظ الصورة بتنسيق PNG، ويجب أن تكون مربعة الشكل بخلفية شفافة (أو خلفية بيضاء إذا لزم الأمر). ويجب إعداد أيقونة المفضلة مرة واحدة فقط؛ لأن إجراء التغييرات سيتطلب بعض الدعم الفني.", "app.containers.AdminPage.FaviconPage.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "حدث خطأ ما، يُرجى المحاولة مجددًا في وقت لاحق.", "app.containers.AdminPage.FaviconPage.saveSuccess": "نجاح!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "تم حفظ التغييرات.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "إضافة", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "مديرو المجلد", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "باستطاعة مديري المجلد تعديل وصف المجلد وإنشاء مشاريع جديدة داخل المجلد والحصول على حقوق إدارة جميع المشاريع داخل المجلد. ولا يمكنهم حذف مشاريع وليس لديهم حق وصول إلى مشاريع ليست داخل مجلدهم. يمكنك {projectManagementInfoCenterLink} للعثور على مزيد من المعلومات حول حقوق إدارة المشاريع.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "لم يتم العثور على توافق", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "زُر مركز المساعدة الخاص بنا", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "ابحث في المستخدمين", "app.containers.AdminPage.FoldersEdit.addToFolder": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> مجلد", "app.containers.AdminPage.FoldersEdit.archivedStatus": "مؤرشف", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "احذف هذا المجلد", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "الوصف", "app.containers.AdminPage.FoldersEdit.draftStatus": "مسوّدة", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "أضِف ملفا<PERSON> إلى هذا المجلد", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "يج<PERSON> ألا يزيد حجم الملفات عن 50 ميغابايت. ستظهر الملفات المُضافة في صفحة المجلد.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "الأوصاف", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "لا توجد مشاريع في هذا المجلد. ارجع إلى علامة تبويب المشاريع الرئيسية لإنشاء مشاريع وإضافتها.", "app.containers.AdminPage.FoldersEdit.folderName": "اسم المجلد", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "صورة الترويسة", "app.containers.AdminPage.FoldersEdit.multilocError": "يجب تعبئة جميع الحقول النصية لكل لغة.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "لا توجد مشاريع يمكنك إضافتها إلى هذا المجلد.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "صورة بطاقة المجلد", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "حقوق الوصول", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "مشاريع المجلد", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "الإعدادات", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "المشاريع المُضافة إلى هذا المجلد", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "المشاريع التي يمكنك إضافتها إلى هذا المجلد", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "اختر ما إذا كان هذا المجلد \"مسوّدة\"، أو \"منشور\"، أو \"مؤرشف\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "منشور", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "إزالة من المجلد", "app.containers.AdminPage.FoldersEdit.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "حدث خطأ ما. يُرجى المحاولة مجددًا.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "نجاح!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "تم حفظ التغييرات.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "وصف قصير", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "يظهر في الصفحة الرئيسية", "app.containers.AdminPage.FoldersEdit.statusLabel": "حالة المشاركة", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "فسّر سبب ترابط المشاريع معًا، وحدد الهوية المرئية وشارك المعلومات.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "فسّر سبب ترابط المشاريع معًا، وحدد الهوية المرئية وشارك المعلومات.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "العنوان", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "أنشئ مجلدًا جديدًا", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "الإعدادات", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "اعرض المجلد", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "أضف طابعك الشخصي على صورة ونص البانر الرئيسي.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "البانر الرئيسي", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "حفظ البانر الرئيسي", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "عدِّل الشروط والأحكام وسياسة الخصوصية في منصتك. يمكن تعديل صفحات أخرى، بما فيها الصفحتان \"نبذة\" و\"أسئلة مكررة\"، في علامة التبويب {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "سياسات المنصة", "app.containers.AdminPage.PagesEdition.privacy-policy": "سياسة الخصوصية", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "الشروط والأحكام", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "قائمة بالمشاريع على المنصة", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "اللوحة الرئيسية للمشاريع", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "أنشئ مشاريع جديدة أو إدِر المشاريع الحالية.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "المشاريع", "app.containers.AdminPage.ProjectDashboard.published": "تم النشر", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "إغلاق لوحة الإعدادات", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "مخطط أعمدة", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "الوصف", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "وصف الصفحة الرئيسية", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "يظهر هذا الوصف في النظرة العامة على المشروع في الصفحة الرئيسية.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "يظهر هذا الوصف في صفحة المعلومات الخاصة بالمشروع.", "app.containers.AdminPage.ProjectDescription.errorMessage": "حدث خطأ ما. يُرجى المحاولة مجددًا.", "app.containers.AdminPage.ProjectDescription.preview": "معاينة", "app.containers.AdminPage.ProjectDescription.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "تم حفظ التغييرات.", "app.containers.AdminPage.ProjectDescription.saved": "تم الحفظ!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "أخبر المستخدمين عن فحوى المشروع وأقنعهم بالمشاركة.", "app.containers.AdminPage.ProjectDescription.titleDescription": "وصف المشروع", "app.containers.AdminPage.ProjectDescription.whiteSpace": "مسافة بيضاء", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "تضمين حد", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "الارتفاع الرأسي", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "كبير", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "متوسط", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "صغير", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "إلغاء", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "دائرة العرض الافتراضية لنقطة مركز الخريطة. يمكن قبول قيمة بين -90 و90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "خط الطول الافتراضي لنقطة مركز الخريطة. يمكن قبول قيمة بين -90 و90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "تعديل طبقة الخريطة", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "تعديل الطبقة", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "حدث خطأ ما، يُرجى المحاولة مجددًا في وقت لاحق", "app.containers.AdminPage.ProjectEdit.MapTab.here": "هنا", "app.containers.AdminPage.ProjectEdit.MapTab.import": "استيراد ملف GeoJSON", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "دائرة العرض الافتراضية", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "لون الطبقة", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "سيتم تصميم جميع الميزات في هذه الطبقة باستخدام هذا اللون. وسيحل هذا اللون أيضًا محل أي نمط حالي في ملف GeoJSON التابع لك.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "رمز أداة التحديد", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "حدد الر<PERSON>ز الذي تريد أن يظهر في أدوات التحديد. انقر على {url} للاطلاع على قائمة الرموز التي يمكنك تحديدها.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "اسم الطبقة", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "يظهر اسم الطبقة هذا في وسيلة إيضاح الخريطة", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "تلميح أداة الطبقة", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "يظهر هذا النص على هيئة تلميح أداة عند تمرير مؤشر الماوس فوق ميزات الطبقة على الخريطة", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "طبقات الخريطة", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "<PERSON>ط الطول الافتراضي", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "المركز ومستوى التكبير/التصغير الافتراضيان للخريطة", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "خصص طريقة عرض الخريطة، بما في ذلك تحميل طبقات وتصميمها وتعيين مركز الخريطة ومستوى تكبيرها/تصغيرها.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "تهيئة الخريطة", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "إزالة طبقة", "app.containers.AdminPage.ProjectEdit.MapTab.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "مقالة دعم", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "مستوى تكبير/تصغير الخريطة", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "مستوى التكبير/التصغير الافتراضي للخريطة. يمكن قبول قيمة بين 1 و17، حيث تشير 1 إلى التصغير الكامل (إمكانية رؤية العالم بكامله) و17 إلى التكبير الكامل (إمكانية رؤية المربعات السكنية والمباني)", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "إضافة سؤال استطلاع", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "إلغاء", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "إلغاء", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "تصدير نتائج الاستطلاع", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "الح<PERSON> الأق<PERSON>ى لعدد الاختيارات أكبر من عدد الخيارات", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "مت<PERSON><PERSON><PERSON> الخيارات", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "لا توجد خيارات", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "لن يتم الرد على الاستطلاع كما هو، إذ يجب أن تحتوي جميع الأسئلة على خيارات", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "خيار وا<PERSON>د فقط", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "يمتلك المشاركون في الاستطلاع خيارًا واحدًا فقط", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "هنا يمكنك إنشاء أسئلة استطلاع، وتعيين خيارات الإجابة للمشاركين للاختيار من بينها لكل سؤال، وتحديد ما إذا كنت تريد أن يتمكن المشاركون فقط من تحديد خيار إجابة واحد (اختيار واحد) أو خيارات متعددة للإجابة (اختيار من متعدد)، وتصدير نتائج الاستطلاع. يمكنك إنشاء أسئلة استطلاع متعددة في استطلاع واحد.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "خيار واحد", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "إعدادات ونتائج الاستطلاع", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "<PERSON><PERSON> <PERSON><PERSON><PERSON>اطئ", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "قدِّم ملاحظات أو أضف وسومًا أو انسخ منشورات إلى مرحلة المشروع التالية.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "مدير الفكرة", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "تصدير نتائج الاستبيان (.xslx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "هنا، يمكنك تنزيل نتائج استبيان/ استبيانات Typeform ضمن هذا المشروع كملف Excel.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "نتائج الاستبيان", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "الاستبيان", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "استشِر إجابات الاستبيان", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "أضِف قضية", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "هل أنت متأكد؟", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "الوصف", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "استخدم هذا لتفسير الأمور المطلوبة من المتطوعين وكذلك ما يمكن لهم توقعه.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "تعذّر الحفظ لاحتواء النموذج على أخطاء.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "صورة", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "العنوان", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "تعديل", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "القضية هي إجراء أو نشاط يستطيع المواطنون التطوع من أجله.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "عدّل القضية", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "إضافة وصف", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "إضافة عنوان", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "تصدير المتطوعين", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "القضية هي إجراء أو نشاط يستطيع المواطنون التطوع من أجله.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "قضية جديدة", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "هنا، يمكنك إعداد القضايا التي يمكن للأشخاص التطوع فيها ويمكنك أيضًا تنزيل قائمة بأسماء المتطوعين.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "التطوع", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {لا يوجد متطوعين} one {# متطوع} other {# من المتطوعين}}", "app.containers.AdminPage.ProjectEdit.addNewInput": "إضافة مدخل", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "وسوم المشروع", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "استطلاع مجهول", "app.containers.AdminPage.ProjectEdit.archived": "مؤرشف", "app.containers.AdminPage.ProjectEdit.archivedStatus": "مؤرشف", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "لا يمكن حذف هذه المنطقة؛ لأنها مستخدمة لعرض مشاريع في الصفحة (الصفحات) المخصصة التالية. سيتعين عليك إلغاء ربط المنطقة من الصفحة أو حذف الصفحة قبل حذف المنطقة.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "جميع المناطق", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "سيظهر المشروع في كل عامل تصفية منطقة على حدة.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "عامل تصفية المنطقة", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "يمكن تصفية المشاريع في مناطق استخدام الصفحة الرئيسية. يمكن تعيين مناطق {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "هنا", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "لا توجد منطقة معينة", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "لن يظهر المشروع عند التصفية حسب المنطقة.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "المجموعة المُختارة", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "سيظهر المشروع في عامل أو عوامل تصفية المناطق المحددة.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "في قائمة", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "أنشئ مشروعًا من قالب", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "إنشاء استبيان خارجي", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "إنشاء استبيان داخل المنصة", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "قم بإعداد استبيان بدون مغادرة المنصة.", "app.containers.AdminPage.ProjectEdit.createPoll": "إنشاء استطلاع", "app.containers.AdminPage.ProjectEdit.createPollDescription": "قم بإعداد استقصاء متعدد الاختيارات.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "يمكنك تعيين الترتيب الافتراضي للمنشورات المطلوب عرضها في صفحة المشروع الرئيسية.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "فرز", "app.containers.AdminPage.ProjectEdit.departments": "الأقسام", "app.containers.AdminPage.ProjectEdit.descriptionTab": "الوصف", "app.containers.AdminPage.ProjectEdit.disabled": "تم التعطيل", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "تم التعطيل", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "تم التمكين", "app.containers.AdminPage.ProjectEdit.draft": "مسوّدة", "app.containers.AdminPage.ProjectEdit.draftStatus": "مسوّدة", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "إدارة", "app.containers.AdminPage.ProjectEdit.enabled": "تم التمكين", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "الأحداث", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "المُرفقات (بحجم أقصى 50 ميغابايت)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "تُعرض المُرفقات في صفحة المعلومات الخاصة بالمشروع.", "app.containers.AdminPage.ProjectEdit.findVolunteers": "بح<PERSON> عن متطوعين", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "اطلب من مشاركين التطوع في أنشطة وقضايا.", "app.containers.AdminPage.ProjectEdit.folderSelectError": "حدد مجلدًا لإضافة هذا المشروع إليه.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "م<PERSON><PERSON><PERSON><PERSON> مخصص", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Submissions to this form have started to come in. Changes to the form may result in data loss and incomplete data in the exported files.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "تم حفظ النموذج بنجاح.", "app.containers.AdminPage.ProjectEdit.fromATemplate": "من قالب", "app.containers.AdminPage.ProjectEdit.generalTab": "عام", "app.containers.AdminPage.ProjectEdit.google_forms": "نماذج جوجل", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "صورة الترويسة", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "تجميع المدخلات والملاحظات", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "من المسؤول عن معالجة المنشورات؟", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "سيتم تعيين جميع المدخلات الجديدة في هذا المشروع إلى هذا الشخص. يمكنك تغيير المعيَّن إليه في {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "تعليق", "app.containers.AdminPage.ProjectEdit.inputFormTab": "شكل الفكرة", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "مدير الفكرة", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "مدير الفكرة", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "نشر", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "طريقة العرض الافتراضية", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "اختر طريقة العرض الافتراضية لعرض المدخلات: بطاقات بطريقة عرض الشبكة أو دبابيس على خريطة. باستطاعة المشاركين التبديل بين طريقتي العرض يدويًا.", "app.containers.AdminPage.ProjectEdit.limited": "م<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "تحميل المزيد من القوالب", "app.containers.AdminPage.ProjectEdit.mapDisplay": "على الخريطة", "app.containers.AdminPage.ProjectEdit.mapTab": "الخريطة", "app.containers.AdminPage.ProjectEdit.maximum": "ال<PERSON><PERSON> الأقصى", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "لا يمكن أن يتجاوز المشاركون هذه الميزانية عند إرسال سلاَّتهم.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "نماذج Microsoft", "app.containers.AdminPage.ProjectEdit.minimum": "ال<PERSON><PERSON> الأدنى", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "يجب أن يستوفي المشاركون حدًا أدنى للميزانية لإرسال سلاَّتهم (أدخل \"0\" إذا كنت تود تعيين حد أدنى).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "زُر مركز المساعدة الخاص بنا", "app.containers.AdminPage.ProjectEdit.moreDetails": "المزيد من التفاصيل", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.containers.AdminPage.ProjectEdit.newProject": "مشروع جديد", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "الأحدث", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "ليست قيمة صالحة", "app.containers.AdminPage.ProjectEdit.noFolder": "لا يوجد مجلد", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "لم يتم العثور على قوالب", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "لا يمكن أن يكون هذا فارغًا", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "يرجى تقديم الحد الأقصى لعدد الأصوات المسموح بها لكل مستخدم", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "الأقدم", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "يستطيع المسؤول عرضه فقط", "app.containers.AdminPage.ProjectEdit.optionNo": "لا", "app.containers.AdminPage.ProjectEdit.optionYes": "نعم (تحديد مجلد)", "app.containers.AdminPage.ProjectEdit.participationLevels": "مستويات المشاركة", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "ماذا تريد أن تفعل؟", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "اختر كيف يمكن للمستخدمين المشاركة.", "app.containers.AdminPage.ProjectEdit.permissionsTab": "حقوق الوصول", "app.containers.AdminPage.ProjectEdit.pollTab": "الاستطلاع", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "صورة بطاقة المشروع", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "هذه الصورة جزء من بطاقة المشروع؛ وهي البطاقة التي تلخص المشروع وتظهر في الصفحة الرئيسية على سبيل المثال.\n\n    لمزيد من المعلومات حول مستويات الدقة الموصى بها للصور، {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "تظهر هذه الصورة في الجزء العلوي من صفحة المشروع.\n\n    لمزيد من المعلومات حول مستويات الدقة الموصى بها للصور، {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "باستطاعة مديري المشاريع تعديل مشاريع وإدارة منشورات وإرسال رسائل إلكترونية إلى مشاركين. يمكنك {moderationInfoCenterLink} للعثور على مزيد من المعلومات حول الحقوق المعيَّنة لمديري المشاريع.", "app.containers.AdminPage.ProjectEdit.projectName": "اسم المشروع", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "نوع المشروع", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "اختر ما إذا كان المشروع يحتوي على جدول زمني أم لا. المشاريع ذات الجدول الزمني لها بداية ونهاية واضحتان ويمكن أن تمر بمراحل مختلفة. والمشاريع التي تفتقر إلى الجدول الزمني متواصلة.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "لا يمكن تغيير نوع المشروع لاحقًا.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "منشور", "app.containers.AdminPage.ProjectEdit.purposes": "الأغراض", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "عشوائي", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "حد<PERSON> خطأ أثناء حفظ بياناتك، يُرجى المحاولة مجددًا.", "app.containers.AdminPage.ProjectEdit.saveProject": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.saveSuccess": "تم الحفظ!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "تم حفظ نموذجك!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "البحث في القوالب", "app.containers.AdminPage.ProjectEdit.selectGroups": "حدّد مجموعة/ مجموعات", "app.containers.AdminPage.ProjectEdit.shareInformation": "إتاحة المعلومات للمشاركة", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "استبيان سريع", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "قم بإعداد مشروعك وتخصيصه.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "زيارة مركز الدعم التابع لنا", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "إلغاء", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "هل تريد المغادرة بالتأكيد؟", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "URL مضمن", "app.containers.AdminPage.ProjectEdit.surveyService": "الخدمة", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "يمكنك معرفة المزيد من المعلومات حول كيفية تضمين الاستبيان {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "هنا", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "لا يمكن حذف هذا الوسم؛ لأنه مستخدم لعرض مشاريع في الصفحة (الصفحات) المخصصة التالية. سيتعين عليك إلغاء ربط الوسم من الصفحة أو حذف الصفحة قبل حذف الوسم.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "إعدادات المشروع العامة", "app.containers.AdminPage.ProjectEdit.titleLabel": "اسم المشروع", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "اختر عنوانًا قصيرًا وواضحًا وجذابًا. سيظهر في النظرة العامة المنسدلة وعلى بطاقات المشروع في الصفحة الرئيسية.", "app.containers.AdminPage.ProjectEdit.topicLabel": "الموضوعات", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "حدد {topicsCopy} لهذا المشروع. وباستطاعة المستخدمين استخدامها لتصفية مشاريع.", "app.containers.AdminPage.ProjectEdit.totalBudget": "الميزانية الإجمالية", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "الرائجة", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "غير مُعيّن", "app.containers.AdminPage.ProjectEdit.unlimited": "<PERSON>ير محدود", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "استخدم هذا القالب", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "اعرض المشروع", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "التطوع", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# مجموعات} one {# مجموعة} other {# من المجموعات}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "أضِف حدثًا", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "البدء", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "الانتهاء", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "هل تريد حذف هذا الحدث بالتأكيد؟ لا يمكن التراجع عن هذا الإجراء!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "وصف الحدث", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "تعديل", "app.containers.AdminPage.ProjectEvents.editEventTitle": "تعديل الحدث", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "المُرفقات (بحجم أقصى 50 ميغابايت)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "تظهر المرفقات أسفل وصف الحدث.", "app.containers.AdminPage.ProjectEvents.locationLabel": "الموقع", "app.containers.AdminPage.ProjectEvents.newEventTitle": "أنشئ حدثًا جديدًا", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "تعذّر حفظ التغييرات، يُرجى المحاولة مجددًا.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "تم الحفظ!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "تم حفظ التغييرات.", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "اربط الأحداث القادمة بهذا المشروع واعرضها على صفحة الأحداث الخاصة بالمشروع.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "العنوان والتواريخ", "app.containers.AdminPage.ProjectEvents.titleEvents": "أحداث المشروع", "app.containers.AdminPage.ProjectEvents.titleLabel": "اسم المشروع", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "طي جميع الحقول", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "وصف الحقل", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "تعديل نموذج الإدخال", "app.containers.AdminPage.ProjectIdeaForm.enabled": "تم التمكين", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "قم بتضمين هذا الحقل.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "حدث خطأ ما، يُرجى المحاولة مجددًا في وقت لاحق", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "توسيع جميع الحقول", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "شكل الفكرة", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "حدد المعلومات التي ينبغي توفيرها أو إضافة أوصاف موجزة أو تعليمات إليها لتوجيه استجابات المشاركين وتحديد إذا كان كل حقل اختياريًا أو إلزاميًا.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "حد<PERSON> المعلومات التي ينبغي توفيرها أو إضافة أوصاف موجزة أو تعليمات إليها لتوجيه استجابات المشاركين وتحديد إذا كان كل حقل اختياريًا أو إلزاميًا", "app.containers.AdminPage.ProjectIdeaForm.required": "مطلوب", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "المطلوب", "app.containers.AdminPage.ProjectIdeaForm.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "تم حفظ تغييراتك بنجاح.", "app.containers.AdminPage.ProjectIdeaForm.saved": "تم الحفظ!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "عرض النموذج", "app.containers.AdminPage.ProjectTimeline.datesLabel": "التواريخ", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "هل تريد حذف هذه المرحلة بالتأكيد؟", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "وصف المرحلة", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "تاريخ الانتهاء", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "المُرفقات (بحجم أقصى 50 ميغابايت)", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "حدث خطأ أثناء إرسال النموذج، يُرجى المحاولة مجددًا.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "تم الحفظ!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "تم حفظ تغييراتك بنجاح.", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "تاريخ البدء", "app.containers.AdminPage.ProjectTimeline.titleLabel": "اسم المرحلة", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_إعادة تقسيم", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "المصطلح (عامل تصفية الصفحة الرئيسية)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "ما التسمية التي ينبغي إطلاقها على عامل تصفية الصفحة الأمامية؟ على سبيل المثال: وسوم، فئات، أقسام، ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "قد تكون الوسوم {topicManagerLink} مهيأة.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "هنا", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "مصطلح لوسم واحد (مفر<PERSON>)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "موضوع", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "مصطلح لوسوم متعددة (جمع)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "وسوم", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "إضافة حقل", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "يضيف", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "أضِف خيارًا", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "صيغة الإجابة", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "إدخال صيغة إجابة", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "خيار الإجابة", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "إدخال خيار إجابة لكل اللغات", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "حفظ خيار الإجابة", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "تم حفظ خيار الإجابة بنجاح", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "خيارات الإجابة", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "الحقول", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "اسحب الحقول وأسقطها لتحديد الترتيب التي تظهر به في نموذج تسجيل الاشتراك.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "الحقل الافتراضي", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "نص اختياري يظهر أسفل اسم الحقل في نموذج تسجيل الاشتراك.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "يمكن تعيين خيارات الإجابة لمكان الإقامة في {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "تعديل", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "تعديل خيار الإجابة", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "الوصف", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "اسم الحقل", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "إدخال اسم حقل لكل اللغات", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "إعدادات الحقل", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "نعم-لا (مربع اختيار)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "التاريخ", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "إجابة مطولة", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "اختيار متعدد (تحديد إجابات متعددة)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "قيمة رقمية", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "اختيار متعدد (تحديد إجابة واحدة)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "إجابة مختصرة", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "المناطق الجغرافية", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "<PERSON><PERSON><PERSON> مخفي", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "هل تريد جعل الإجابة عن هذا الحقل إلزامية؟", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "الحقول المخصصة", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "إضافة خيار إجابة", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "إلغاء", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "مطلوب", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "<PERSON><PERSON><PERSON> الحقل", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "تم حفظ الحقل بنجاح", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "عمودان", "app.containers.AdminPage.SettingsPage.addAreaButton": "أضِف منطقة جغرافية", "app.containers.AdminPage.SettingsPage.addTopicButton": "أضِف موضوعًا", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "هل تريد حذف هذه المنطقة بالتأكيد؟", "app.containers.AdminPage.SettingsPage.areaTerm": "مصطلح لمنطقة واحدة (مفرد)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "منطقة", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "تعديل", "app.containers.AdminPage.SettingsPage.areasTerm": "مصطلح لمناطق متعددة (جمع)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "مناطق", "app.containers.AdminPage.SettingsPage.avatarsTitle": "صور رمزية", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "عرض صور رمزية", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "إظهار صور ملفات تعريف المشاركين وعددها لزائرين غير مسجَّلين", "app.containers.AdminPage.SettingsPage.bannerHeader": "نص الترويسة", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "نص الترويسة لزائرين غير مسجَّلين", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "نص الترويسة الفرعية لزائرين غير مسجَّلين", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "نص الترويسة الفرعية", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "نص البانر", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "إظهار معاينة من أجل", "app.containers.AdminPage.SettingsPage.brandingDescription": "أضف شعارك وعيِّن ألوان المنصة.", "app.containers.AdminPage.SettingsPage.brandingTitle": "العلامة التجارية للمنصة", "app.containers.AdminPage.SettingsPage.cancel": "إلغاء", "app.containers.AdminPage.SettingsPage.chooseLayout": "مخطط", "app.containers.AdminPage.SettingsPage.color_primary": "لون أساسي", "app.containers.AdminPage.SettingsPage.color_secondary": "اللون الثانوي", "app.containers.AdminPage.SettingsPage.color_text": "لون النص", "app.containers.AdminPage.SettingsPage.colorsTitle": "ألوان", "app.containers.AdminPage.SettingsPage.confirmHeader": "هل تريد حذف هذا الموضوع بالتأكيد؟", "app.containers.AdminPage.SettingsPage.contentModeration": "الإش<PERSON><PERSON><PERSON> ع<PERSON> المحتوى", "app.containers.AdminPage.SettingsPage.ctaHeader": "أزرار", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "ترويسة الصفحة المخصصة | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "مخصصة", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "نص الزر", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "رابط زر", "app.containers.AdminPage.SettingsPage.defaultTopic": "موضوع افتراضي", "app.containers.AdminPage.SettingsPage.delete": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "سيؤدي هذا إلى حذف الوسم من جميع المنشورات الحالية. سيتم تطبيق هذا التغيير على جميع المشاريع.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "أضف واحذف الوسوم التي تود استخدامها في منصتك لتصنيف المنشورات في فئات. يمكنك إضافة الوسوم إلى مشاريع معينة في {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "سط<PERSON>", "app.containers.AdminPage.SettingsPage.editFormTitle": "تعديل المنطقة", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "تعديل", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "تعديل الموضوع", "app.containers.AdminPage.SettingsPage.fieldDescription": "وصف المنطقة", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "هذا الوصف مخصص فقط للتعاون الداخلي ولا يتم عرضه للمستخدمين.", "app.containers.AdminPage.SettingsPage.fieldTitle": "اسم المنطقة", "app.containers.AdminPage.SettingsPage.fieldTitleError": "إدخال اسم منطقة لكل اللغات", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "سيكون الاسم الذي تختاره لكل منطقة مرئيًا للمواطنين أثناء التسجيل وعند التصفية للبحث عن المشاريع.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "<PERSON><PERSON><PERSON> الوسم", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "اسم الموضوع", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "إدخال اسم وسم لكل اللغات", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "سيكون الاسم الذي تختاره لكل موضوع مرئيًا لمستخدمي المنصة.", "app.containers.AdminPage.SettingsPage.fixedRatio": "بانر النسبة الثابتة", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "يعمل البانر من هذا النوع على أكمل وجه مع الصور التي ينبغي عدم اقتصاصها: كالصور المصحوبة بنص أو شعار أو عناصر محددة تحظى بأهمية بالغة لمواطنيك. وبدلاً من هذا البانر، يتم استخدام مربع بلون خالص من الألوان الأساسية عندما يكون المستخدمون مسجَّلي الدخول. يمكنك تعيين هذا اللون في الإعدادات العامة. يمكن العثور على مزيد من المعلومات حول الاستخدام الموصى به للصور عبر {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "قاعدة المعارف", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "لافتة بالعرض الكامل", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "يتمدد هذا البانر فوق العرض الكامل لمؤثر مرئي رائع. ستحاول الصورة تغطية أكبر مساحة ممكنة؛ لتصبح غير مرئية دائمًا في جميع الأوقات. يمكنك الجمع بين هذا البانر وتداخل أي لون. يمكن العثور على مزيد من المعلومات حول الاستخدام الموصى به للصور عبر {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "قاعدة المعارف", "app.containers.AdminPage.SettingsPage.header": "لافتة الصفحة الرئيسية", "app.containers.AdminPage.SettingsPage.headerDescription": "أضف طابعك الشخصي على صورة ونص بانر الصفحة الرئيسية.", "app.containers.AdminPage.SettingsPage.header_bg": "صورة اللافتة", "app.containers.AdminPage.SettingsPage.helmetDescription": "صفحة إعدادات المسؤول", "app.containers.AdminPage.SettingsPage.helmetTitle": "صفحة إعدادات المسؤول", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "أضف محتواك الخاص إلى القسم القابل للتخصيص في الجزء السفلي من الصفحة الرئيسية.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "ترويسة الصفحة الرئيسية | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "لون تراكب الصور", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "شفافية تراكب الصور", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "اكتشاف محتوى غير لائق", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "اكتشاف تلقائي لمحتوى غير لائق منشور في المنصة.", "app.containers.AdminPage.SettingsPage.languages": "اللغات", "app.containers.AdminPage.SettingsPage.languagesTooltip": "يمكنك تحديد عدة لغات للمنصة الخاصة بك. وسيتعين عليك إنشاء محتوى لكل لغة محددة.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "النشاط", "app.containers.AdminPage.SettingsPage.logo": "الشعار", "app.containers.AdminPage.SettingsPage.noHeader": "يُرجى تحميل صورة للترويسة", "app.containers.AdminPage.SettingsPage.no_button": "لا يوجد زر", "app.containers.AdminPage.SettingsPage.organizationName": "اسم المدينة أو المؤسسة", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "تمكين التداخل", "app.containers.AdminPage.SettingsPage.phone": "هاتف", "app.containers.AdminPage.SettingsPage.platformConfiguration": "تهيئة المنصة", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "مانع البذاءات", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "حظر المدخلات والمقترحات والتعليقات التي تحتوي على الألفاظ المسيئة الأكثر شيوعًا المبلَّغ عنها", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "يظهر هذا النص في الصفحة الرئيسية أعلى المشاريع.", "app.containers.AdminPage.SettingsPage.projectsSettings": "إعدادات المشروع", "app.containers.AdminPage.SettingsPage.projects_header": "ترويسة المشاريع", "app.containers.AdminPage.SettingsPage.registrationFields": "مجالات التسجيل", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "أدخل وصفًا موجزًا في الجزء العلوي من نموذج التسجيل.", "app.containers.AdminPage.SettingsPage.registrationTitle": "التسجيل", "app.containers.AdminPage.SettingsPage.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveArea": "ح<PERSON><PERSON> المنطقة", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "حدث خطأ ما، يُرجى المحاولة مجددًا في وقت لاحق.", "app.containers.AdminPage.SettingsPage.saveSuccess": "تم الحفظ!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "تم حفظ التغييرات.", "app.containers.AdminPage.SettingsPage.settingsSavingError": "تعذر الحفظ. جرِّب تغيير الإعداد مجددًا.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"تسجيل الاشتراك\"", "app.containers.AdminPage.SettingsPage.signed_in": "زر للزائرين المسجَّلين", "app.containers.AdminPage.SettingsPage.signed_out": "زر للزائرين غير المسجَّلين", "app.containers.AdminPage.SettingsPage.signupFormText": "نص مساعد التسجيل", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "أضف وصفًا موجزًا في الجزء العلوي من نموذج تسجيل الاشتراك.", "app.containers.AdminPage.SettingsPage.step1": "خطوة البريد الإلكتروني وكلمة المرور", "app.containers.AdminPage.SettingsPage.step1Tooltip": "تظهر في الجزء العلوي من الصفحة الأولى لنموذج تسجيل الاشتراك (الاسم، البريد الإلكتروني، كلمة المرور).", "app.containers.AdminPage.SettingsPage.step2": "خطوة أسئلة التسجيل", "app.containers.AdminPage.SettingsPage.step2Tooltip": "تظهر في الجزء العلوي من الصفحة الثانية لنموذج تسجيل الاشتراك (حقول تسجيل إضافية).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "حد<PERSON> التقسيمات الفرعية الجغرافية لمنطقتك.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "اختر كيفية رؤية الناس لاسمك، وحدّد لغات منصتك واربطها بموقعك الإلكتروني.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "يتجاوز النص الحد الأقصى المسموح به لعدد الأحرف وهو 90 حرفًا", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "حدد المعلومات المطلوب من الزائرين تقديمها لتسجيل الاشتراك.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "المصطلح", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "تم تحديث الإعدادات بنجاح.", "app.containers.AdminPage.SettingsPage.tabPolicies": "السياسات", "app.containers.AdminPage.SettingsPage.tabRegistration": "التسجيل", "app.containers.AdminPage.SettingsPage.tabSettings": "عام", "app.containers.AdminPage.SettingsPage.tabWidgets": "عنصر الواجهة", "app.containers.AdminPage.SettingsPage.tablet": "جهاز لوحي (تاب<PERSON>ت)", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "كيف ستتم تسمية المناطق بالنسبة للمستخدمين؟ مثال: أحياء، مقاطعات...إلخ.", "app.containers.AdminPage.SettingsPage.titleAreas": "المناطق الجغرافية", "app.containers.AdminPage.SettingsPage.titleBasic": "الإعدادات العامة", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "يتجاوز النص الحد الأقصى المسموح به لعدد الأحرف وهو 35 حرفًا", "app.containers.AdminPage.SettingsPage.titleTopicManager": "مدير الموضوع", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "يفيد هذا البانر بصفة خاصة مع الصور التي لا تعمل جيدًا مع نص العنوان أو العنوان الفرعي أو الزر. سيتم دفع هذه العناصر أسفل البانر. يمكن العثور على مزيد من المعلومات حول الاستخدام الموصى به للصور عبر {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "قاعدة المعارف", "app.containers.AdminPage.SettingsPage.twoRowLayout": "صفان", "app.containers.AdminPage.SettingsPage.urlError": "عنوان الـ URL غير صالح.", "app.containers.AdminPage.SettingsPage.urlTitle": "الموقع الإلكتروني", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "يمكنك إضافة رابط إلى موقعك الإلكتروني. سيتم استخدام هذا الرابط في الجزء السفلي من الصفحة الرئيسية.", "app.containers.AdminPage.SideBar.dashboard": "اللوحة الرئيسية", "app.containers.AdminPage.SideBar.emails": "الرسائل الإلكترونية", "app.containers.AdminPage.SideBar.groups": "المجموعات", "app.containers.AdminPage.SideBar.guide": "الدليل", "app.containers.AdminPage.SideBar.inputManager": "مدير الفكرة", "app.containers.AdminPage.SideBar.insights": "تقديم بلاغات", "app.containers.AdminPage.SideBar.menu": "صفحات وقائمة", "app.containers.AdminPage.SideBar.messaging": "رسائل", "app.containers.AdminPage.SideBar.moderation": "النشاط", "app.containers.AdminPage.SideBar.processing": "معالجة", "app.containers.AdminPage.SideBar.projects": "المشاريع", "app.containers.AdminPage.SideBar.settings": "الإعدادات", "app.containers.AdminPage.SideBar.users": "المستخدمون", "app.containers.AdminPage.SideBar.workshops": "ورشات العمل", "app.containers.AdminPage.Topics.addTopics": "إضافة", "app.containers.AdminPage.Topics.browseTopics": "تصفح المواضيع", "app.containers.AdminPage.Topics.cancel": "إلغاء", "app.containers.AdminPage.Topics.confirmHeader": "هل تريد حذف موضوع المشروع هذا بالتأكيد؟", "app.containers.AdminPage.Topics.delete": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.deleteTopicLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "لم يعد ممكنًا إضافة هذا الوسم إلى منشورات في هذا المشروع.", "app.containers.AdminPage.Topics.inputForm": "شكل الفكرة", "app.containers.AdminPage.Topics.lastTopicWarning": "يلزم وجود موضوع واحد على الأقل. إذا كنت لا تود استخدام موضوعات، يُمكن تعطيلها في علامة التبويب {ideaFormLink}. ", "app.containers.AdminPage.Topics.projectTopicsDescription": "يُمكنك إضافة وحذف المواضيع التي يُمكن تعيينها إلى مُدخلات في هذا المشروع. ", "app.containers.AdminPage.Topics.remove": "إزالة", "app.containers.AdminPage.Topics.title": "وسوم المشروع", "app.containers.AdminPage.Topics.topicManager": "مدير الموضوع", "app.containers.AdminPage.Topics.topicManagerInfo": "إذا كنت ترغب في إضافة موضوعات مشروع إضافية، فيمكنك القيام بذلك في {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "أضِ<PERSON> مجموعة جديدة", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "اسم المجموعة", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "إدخال اسم مجموعة", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "أنشئ مجموعة يدوية", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "ما نوع المجموعة التي تحتاجها؟", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "حفظ المجموعة", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "أنشئ مجموعة يدوية", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "أنشئ مجموعة ذكية", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "اعرف المزي<PERSON> حول المجموعات", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "يمكنك تحديد المستخدمين من النظرة العامة وإضافتهم إلى هذه المجموعة.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "يمكنك تحديد الشروط، بحيث تتم إضافة المستخدمين الذين يستوفون الشروط تلقائيًا إلى هذه المجموعة.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "مجموعة يدوية", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "مجموعة ذكية", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "لا يوجد أحد في هذه المجموعة بعد", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "اذهب إلى {allUsersLink} لإضافة المستخدمين يدويًا.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "لا يوجد مستخدمون يطابقون بحثك", "app.containers.AdminPage.Users.GroupsPanel.select": "تحديد", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "هل أنت متأكد؟", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "حدث خطأ أثناء إضافة مستخدمين إلى المجموعات، يُرجى المحاولة مجددًا.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "إزالة من المجموعة", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "حذف المستخدمين المحددين من هذه المجموعة؟", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "حدث خطأ أثناء حذف المستخدمين من المجموعة، يُرجى المحاولة مجددًا.", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "إضافة", "app.containers.AdminPage.groups.permissions.add": "إضافة", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "هل تريد حذف هذه المجموعة من المشروع بالتأكيد؟", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "تحديد مجموعة واحدة أو أكثر", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {لا يوجد أعضاء} one {عضو واحد} other {{count} من الأعضاء}}", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "هل أنت متأكد؟", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "لم يتم العثور على مديري المشاريع", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "لم يُعرض شيء، إذ لا توجد إجراءات يمكن للمستخدم اتخاذها في هذا المشروع.", "app.containers.AdminPage.groups.permissions.pendingInvitation": "دعوة قيد الانتظار", "app.containers.AdminPage.groups.permissions.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "حدث خطأ ما، يُرجى المحاولة مجددًا في وقت لاحق.", "app.containers.AdminPage.groups.permissions.saveSuccess": "نجاح!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "تم حفظ التغييرات.", "app.containers.AdminPage.projects.all.existingProjects": "المشاريع الحالية", "app.containers.AdminPage.projects.all.projectsAndFolders": "المشاريع والمجلدات", "app.containers.AdminPage.widgets.copied": "تم النسخ إلى الحافظة", "app.containers.AdminPage.widgets.copyToClipboard": "انسخ هذا الرمز", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "انسخ رمز HTML", "app.containers.AdminPage.widgets.fieldAccentColor": "لون التمييز", "app.containers.AdminPage.widgets.fieldBackgroundColor": "لون خلفية عنصر الواجهة", "app.containers.AdminPage.widgets.fieldButtonText": "نص الزر", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "انضم الآن", "app.containers.AdminPage.widgets.fieldFont": "الخط", "app.containers.AdminPage.widgets.fieldFontDescription": "يجب أن يكون اسم الخط موجودًا في {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "حجم الخط (بكسل)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "العنوان الفرعي للترويسة", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "يمكنك التعبير عن رأيك", "app.containers.AdminPage.widgets.fieldHeaderText": "نص الترويسة", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "منصة المشاركة الخاصة بنا", "app.containers.AdminPage.widgets.fieldHeight": "الارتفاع (بكسل)", "app.containers.AdminPage.widgets.fieldInputsLimit": "عدد المُدخلات", "app.containers.AdminPage.widgets.fieldProjects": "المشاريع", "app.containers.AdminPage.widgets.fieldRelativeLink": "يربط بـ", "app.containers.AdminPage.widgets.fieldShowFooter": "أ<PERSON>ه<PERSON> الزر", "app.containers.AdminPage.widgets.fieldShowHeader": "أظهر الترويسة", "app.containers.AdminPage.widgets.fieldShowLogo": "أظهر الشعار", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "لون خلفية الموقع", "app.containers.AdminPage.widgets.fieldSort": "تم الفرز بحسب", "app.containers.AdminPage.widgets.fieldTextColor": "لون النص", "app.containers.AdminPage.widgets.fieldTopics": "الموضوعات", "app.containers.AdminPage.widgets.fieldWidth": "العرض", "app.containers.AdminPage.widgets.homepage": "الصفحة الرئيسية", "app.containers.AdminPage.widgets.htmlCodeExplanation": "يمكنك نسخ رمز HTML هذا ولصقه في موقعك الإلكتروني حيثُ تريد أن يظهر عنصر الواجهة.", "app.containers.AdminPage.widgets.htmlCodeTitle": "رمز HTML لعنصر الواجهة", "app.containers.AdminPage.widgets.previewTitle": "معاينة", "app.containers.AdminPage.widgets.settingsTitle": "الإعدادات", "app.containers.AdminPage.widgets.sortNewest": "الأحدث", "app.containers.AdminPage.widgets.sortPopular": "الأكثر تصويتًا", "app.containers.AdminPage.widgets.sortTrending": "الرائج", "app.containers.AdminPage.widgets.subtitleWidgets": "يمكنك إنشاء عنصر واجهة وتخصيصه وإضافته إلى موقعك الإلكتروني لجذب الناس إلى هذه المنصة.", "app.containers.AdminPage.widgets.title": "عنصر الواجهة", "app.containers.AdminPage.widgets.titleDimensions": "الأبعاد", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "الترويسة والتذييل", "app.containers.AdminPage.widgets.titleInputSelection": "اختيار المُدخل ", "app.containers.AdminPage.widgets.titleStyle": "النمط", "app.containers.AdminPage.widgets.titleWidgets": "عنصر الواجهة", "app.containers.ContentBuilder.Save": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.delete": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.error": "خطأ", "app.containers.admin.ContentBuilder.errorMessage": "يو<PERSON><PERSON> خطأ في محتوى {locale}، يرجى حل المشكلة لتتمكن من حفظ التغييرات التي أجريتها", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 أعمدة", "app.containers.admin.ContentBuilder.twoColumnLayout": "عمودان", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "عمودان بعرض 30% و60% على الترتيب", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "عمودان بعرض 60% و30% على الترتيب", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "عمودان متساويان", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "اعرض المزيد", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "العنوان", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "المخطط الزمني للمشاركين", "app.containers.admin.ReportBuilder.charts.analyticsChart": "مخطط", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "نطاق التاريخ", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "العنوان", "app.containers.admin.ReportBuilder.charts.noData": "لا تتوفر أي بيانات لعوامل التصفية التي حددتها.", "app.containers.admin.ReportBuilder.charts.trafficSources": "مصادر حركة المرور", "app.containers.admin.ReportBuilder.charts.usersByAge": "المستخدمون بحسب العمل", "app.containers.admin.ReportBuilder.charts.usersByGender": "المستخدمون بحسب الجنس", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "المخطط الزمني للزائر", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "إضافة حالة", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editButtonLabel": "تعديل", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "تعديل الحالة", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "لا يمكن حذف الحالات المعيَّنة حاليًا لمدخلات المشاركين. يمكنك إزالة الحالة أو تغييرها من المدخلات الحالية في علامة التبويب {manageTab}.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "لا يمكن حذف هذه الحالة أو نقلها.", "app.containers.admin.ideaStatuses.all.manage": "إدارة", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "محدد للتن<PERSON>يذ أو الخطوات التالية", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "معت<PERSON>د", "app.containers.admin.ideaStatuses.form.category": "الفئة", "app.containers.admin.ideaStatuses.form.categoryDescription": "يرجى تحديد الفئة الأنسب للتعبير عن حالتك. من شأن هذا التحديد مساعدة أداة التحليلات التابعة لنا في معالجة المنشورات وتحليلها بمزيد من الدقة.", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "آخر", "app.containers.admin.ideaStatuses.form.fieldColor": "اللون", "app.containers.admin.ideaStatuses.form.fieldDescription": "وصف الحالة", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "إدخال وصف حالة لكل اللغات", "app.containers.admin.ideaStatuses.form.fieldTitle": "اسم الحالة", "app.containers.admin.ideaStatuses.form.fieldTitleError": "إدخال اسم حالة لكل اللغات", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "تم التنفيذ بنجاح", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "منفَّذ", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "غير مؤهل أو غير محدد للمضي قدمًا", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "<PERSON>ي<PERSON> محدد", "app.containers.admin.ideaStatuses.form.saveStatus": "ح<PERSON><PERSON> الحالة", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "مدروس للتنفيذ أو الخطوات التالية", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "<PERSON>يد البحث", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "معروض لكن لم يُعالج بعد", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "معروض", "app.containers.admin.ideas.all.inputManagerMetaDescription": "إدارة المدخلات وحالاتها.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "مدير الفكرة", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "تقديم ملاحظات وإضافة وسوم ونقل مدخلات من مشروع إلى آخر", "app.containers.admin.import.importInputs": "استيراد مدخلات", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "أي شخص", "app.containers.admin.project.permissions.permissionsSelectionLabel": "مجموعات مستخدمين محددة", "app.containers.admin.project.permissions.viewingRightsTitle": "من يمكنه رؤية هذا المشروع؟", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "معدل المشاركة", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "إجمالي المشاركين", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "حملات مؤتمتة", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "رسائل إلكترونية تلقائية", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "من {quantity} حملة/حملات", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "حم<PERSON><PERSON>ت", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "حملات مخصصة", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "عناوين بريد إلكتروني مخصصة", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "الرسائل الإلكترونية", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "إجمالي رسائل البريد الإلكتروني المرسلة", "app.modules.commercial.analytics.admin.components.Events.completed": "المكتملة", "app.modules.commercial.analytics.admin.components.Events.events": "الأحداث", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "إجمالي الأحداث المضافة", "app.modules.commercial.analytics.admin.components.Events.upcoming": "القادمة", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "مقبولة", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "الدعوات", "app.modules.commercial.analytics.admin.components.Invitations.pending": "قيد الانتظار", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "إجمالي الدعوات المرسلة", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "انتقال إلى مدير المدخلات", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "مدخلات", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "النشط", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "المشاريع التي لم تتم أرشفتها والمرئية في الجدول \"النشط\" بالصفحة الرئيسية", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "مؤرشف", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "مسوّدات المشاريع", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "مكتمل", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "تُحتسب هنا جميع المشاريع المؤرشفة والمشاريع ذات المخططات الزمنية النشطة التي جرى إتمامها", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "المشاريع", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "إجمالي المشاريع", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "عد<PERSON> المشاريع المرئية في المنصة", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "تسجيلات جديدة", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "معدل التسجيل", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "التسجيلات", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "إجمالي التسجيلات", "app.modules.commercial.analytics.admin.components.Tab": "زوار", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "آخر 30 يومًا:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "آخر 7 أيام:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "مشاهدات الصفحة في كل زيارة", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "مدة الزيارة", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "زوار", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"الزوار\" هو عدد الزائرين المتفردين. إذا زار شخص المنصة عدة مرات، فسيتم إحصاؤه مرة واحدة فقط.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "الزيارات", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"الزيارات\" هو عدد جلسات العمل. إذا زار شخص المنصة عدة مرات، فسيتم إحصاء كل زيارة.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "أمس:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "اللغة", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "ع<PERSON><PERSON> الزائرين", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "نسبة الزائرين", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "مُحيل", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "انقر هنا", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "مُحيلون", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "لعرض قائمة المُحيلين الكاملة, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "زوار", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "مصادر حركة المرور", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "الزيارات", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "عمليات تسليم البريد الإلكتروني بمرور الوقت", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "تسجيلات بمرور الوقت", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "التاريخ", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "إحصاءات", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "إحصاءات عامة", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "الزيارات والزائرون بمرور الوقت", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "إجمالي الفترة الإضافية", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "اللغة", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "حم<PERSON><PERSON>ت", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "إدخال مباشر", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "نسبة الزيارات", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "محركات بحث", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "شبكات تواصل اجتماعي", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "مصدر حركة المرور", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "<PERSON><PERSON><PERSON> الزيارات", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "مواقع ويب", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "يمكنك إزالة العلامة الموضوعة على هذا المحتوى من خلال تحديد هذا العنصر والنقر على زر الإزالة في الجزء العلوي. وسيظهر عندئذٍ في علامة التبويب \"مرئية\" أو \"غير مرئية\"", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "تم اكتشاف محتوى غير لائق تلقائيًا.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "لا توجد منشورات تم الإبلاغ عنها لمراجعتها من قِبل المجتمع أو تم وضع علامات عليها بواسطة نظام معالجة اللغات الطبيعية بسبب محتوى غير لائق", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "إزالة {numberOfItems, plural, one {تحذير بشأن المحتوى} other {# تحذير/تحذيرات بشأن المحتوى}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "تم الإبلاغ عنه كغير لائق من أحد مستخدمي المنصة.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "تحذيرات المحتوى", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "أداة إنشاء التقرير", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "صفحات معروضة في شريط التنقل", "app.modules.navbar.admin.containers.createCustomPageButton": "إنشاء صفحة مخصصة", "app.modules.navbar.admin.containers.deletePageConfirmation": "هل تريد بالتأكيد حذف هذه الصفحة؟ لا يمكن التراجع عن هذا الإجراء. يمكنك أيضًا إزالة الصفحة من شريط التنقل إذا لم تكن قد حذفتها حتى الآن.", "app.modules.navbar.admin.containers.pageHeader": "صفحات وقائمة", "app.modules.navbar.admin.containers.pageSubtitle": "باستطاعة شريط التنقل عرض ما يصل إلى خمس صفحات إضافة إلى الصفحة الرئيسية وصفحات المشاريع. يمكنك إعادة تسمية عناصر قائمة وإعادة ترتيب صفحات جديدة تتضمن المحتوى التابع لك وإضافتها.", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "زيارة مركز الدعم التابع لنا", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "لمزيد من المعلومات حول مستويات الدقة الموصى بها للصور، {supportPageLink}."}
{"UI.FormComponents.required": "wym<PERSON>e", "app.Admin.ManagementFeed.action": "Działanie", "app.Admin.ManagementFeed.after": "Po", "app.Admin.ManagementFeed.before": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.changed": "Zmodyfikowany", "app.Admin.ManagementFeed.created": "Utworzony", "app.Admin.ManagementFeed.date": "Data", "app.Admin.ManagementFeed.deleted": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.folder": "Folder", "app.Admin.ManagementFeed.idea": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.in": "w projekcie {project}", "app.Admin.ManagementFeed.item": "<PERSON><PERSON><PERSON>ja", "app.Admin.ManagementFeed.key": "<PERSON><PERSON>cz", "app.Admin.ManagementFeed.managementFeedNudge": "Dostęp do kanału zarządzania nie jest objęty Twoją obecną licencją. Skontaktuj się ze swoim menedżerem GovSuccess, aby dowiedzieć się więcej na ten temat.", "app.Admin.ManagementFeed.noActivityFound": "Nie znaleziono aktywności", "app.Admin.ManagementFeed.phase": "Faza", "app.Admin.ManagementFeed.project": "Projekt", "app.Admin.ManagementFeed.projectReviewApproved": "Projekt zatwierdzony", "app.Admin.ManagementFeed.projectReviewRequested": "Wymagany przegląd projektu", "app.Admin.ManagementFeed.title": "Kanał zarządzania", "app.Admin.ManagementFeed.user": "Użytkownik", "app.Admin.ManagementFeed.userPlaceholder": "Wybierz użytkownika", "app.Admin.ManagementFeed.value": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.viewDetails": "Zobacz szczegóły", "app.Admin.ManagementFeed.warning": "Funkcja eksperymentalna: Minimalna lista wybranych działań wykonanych przez administratorów lub menedżerów w ciągu ostatnich 30 dni. Nie wszystkie działania są uwzględnione.", "app.Admin.Moderation.managementFeed": "Kanał zarządzania", "app.Admin.Moderation.participationFeed": "Kanał uczestnictwa", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "<PERSON><PERSON><PERSON> pewien?", "app.components.Admin.Campaigns.clicked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deleteCampaignButton": "Usuń kampanię", "app.components.Admin.Campaigns.deliveryStatus_accepted": "Zaakceptowany", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Odbity", "app.components.Admin.Campaigns.deliveryStatus_clicked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "<PERSON><PERSON><PERSON><PERSON>, ilu odbiorców kliknęło link w wiadomości e-mail. <PERSON><PERSON><PERSON><PERSON><PERSON>, że niektóre systemy bezpieczeństwa mogą automatycznie podążać za linkami, aby je <PERSON><PERSON><PERSON>, co może skutkować fałszywymi kliknięciami.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "Dostarczone", "app.components.Admin.Campaigns.deliveryStatus_failed": "<PERSON>e powiodło się", "app.components.Admin.Campaigns.deliveryStatus_opened": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "To poka<PERSON><PERSON>, ilu od<PERSON><PERSON><PERSON> otworzyło wiadomość e-mail. <PERSON><PERSON><PERSON><PERSON><PERSON>, że niektóre systemy bezpieczeństwa (takie jak Microsoft Defender) mogą wstępnie załadować zawarto<PERSON> do skanowania, co może skutkować fałszywymi otwarciami.", "app.components.Admin.Campaigns.deliveryStatus_sent": "Wysłany", "app.components.Admin.Campaigns.draft": "<PERSON><PERSON><PERSON> rob<PERSON>", "app.components.Admin.Campaigns.from": "Od", "app.components.Admin.Campaigns.manageButtonLabel": "Zarządzaj", "app.components.Admin.Campaigns.opened": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.project": "Projekt", "app.components.Admin.Campaigns.recipientsTitle": "Od<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.reply_to": "Reply-To", "app.components.Admin.Campaigns.sent": "Wysłany", "app.components.Admin.Campaigns.statsButton": "Statystyki", "app.components.Admin.Campaigns.subject": "Przedmiot", "app.components.Admin.Campaigns.to": "Do", "app.components.Admin.ImageCropper.cropFinalSentence": "Zobacz także: {link}.", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "Utrzymuj klu<PERSON>ową zawartość wewnątrz przerywanych linii, aby z<PERSON><PERSON> była widoczna.", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "3:1 na urządzeniach mobilnych (wyświetlany jest tylko obszar między przerywanymi liniami)", "app.components.Admin.ImageCropper.cropSentenceOne": "Obraz zostanie automatycznie przycięty:", "app.components.Admin.ImageCropper.cropSentenceTwo": "{aspect} na pulpicie (pokazano pełną szerokość)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "zalecana rozdzielcz<PERSON>ść", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Tekst nagłówka dla zarejestrowanych użytkowników", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Ostrzeżenie: wybrany kolor nie ma wystarczająco wysokiego kontrastu. <PERSON>ż<PERSON> to s<PERSON><PERSON><PERSON><PERSON><PERSON>, że tekst będzie trudny do odczytania. Wybierz ciemniejszy kolor, aby <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Dodaj wydarzenia do paska nawigacji", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "Po włączeniu tej opcji do paska nawigacji zostanie dodany link do wszystkich wydarzeń związanych z projektem.", "app.components.AdminPage.SettingsPage.eventsSection": "Wyd<PERSON>zen<PERSON>", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Część konfigurowalna na stronie głównej", "app.components.AnonymousPostingToggle.userAnonymity": "An<PERSON><PERSON><PERSON><PERSON>ć użytkownika", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Użytkownicy będą mogli ukryć swoją to<PERSON><PERSON><PERSON> przed innymi użytkownikami, kierownikami projektów i administratorami. Wkłady te mogą być nadal moderowane.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Pozwól użytkownikom uczestniczyć anonimowo", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Użytkownicy mogą nadal korzystać ze swojego prawdziwego imienia i nazwiska, ale będą mieli możliwość anonimowego przesyłania treści, jeśli zdecydują się to zrobić. Wszyscy użytkownicy nadal będą musieli spełniać wymagania określone w zakładce Prawa dostępu, aby ich wkłady zostały przekazane. Dane profilu użytkownika nie będą dostępne w eksporcie danych uczestnictwa.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Dowiedz się więcej o anonimowości użytkowników na naszej stronie {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Po dodaniu doda<PERSON>, <PERSON>je rozliczenie zostanie zwiększone. Skontaktuj się ze swoim menedżerem GovSuccess, aby dowiedzieć się więcej na ten temat.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Dziękujemy za wypełnienie ankiety! Możesz wziąć w niej udział ponownie w przyszłym kwartale.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Pobierz jako pdf", "app.components.FormSync.downloadExcelTemplate": "Pobierz szablon Excel", "app.components.FormSync.downloadExcelTemplateTooltip2": "Szablony Excel nie będą zawierać pytań rankingowych, pytań dotyczących macierzy, pytań dotyczących przesyłania plików ani żadnych pytań dotyczących mapowania (Drop Pin, Draw Route, Draw Area, ESRI file upload), ponieważ nie są one obecnie obsługiwane w przypadku importu zbiorczego.", "app.components.ProjectTemplatePreview.close": "Zamknij", "app.components.ProjectTemplatePreview.createProject": "Stwórz projekt", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Utwórz projekt na podstawie szablonu ''{templateTitle}''", "app.components.ProjectTemplatePreview.goBack": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.goBackTo": "<PERSON><PERSON><PERSON><PERSON> do {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "Go Vocal ekspert", "app.components.ProjectTemplatePreview.infoboxLine1": "<PERSON><PERSON> ch<PERSON>z skorzystać z tego szablonu w swoim projekcie?", "app.components.ProjectTemplatePreview.infoboxLine2": "Skontaktuj się z osobą odpowiedzialną w administracji miasta lub skontaktuj się z {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Folder projektu", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Wybrana data jest niewprawidłowa. Proszę podać datę w następującym formacie: YYYYY-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "<PERSON><PERSON><PERSON> wy<PERSON>ć datę rozpoczęcia projektu", "app.components.ProjectTemplatePreview.projectStartDate": "Data rozpoczęcia projektu", "app.components.ProjectTemplatePreview.projectTitle": "Tytuł twojego projektu", "app.components.ProjectTemplatePreview.projectTitleError": "Wpisz tytuł projektu", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "<PERSON><PERSON>ę wpisać tytuł projektu we wszystkich językach", "app.components.ProjectTemplatePreview.projectsOverviewPage": "strona przeglądu projektów", "app.components.ProjectTemplatePreview.responseError": "Coś poszło nie tak, spróbuj ponownie później lub skontaktuj się z <EMAIL>.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Zobacz więcej szablonów", "app.components.ProjectTemplatePreview.successMessage": "Projekt został pomyślnie stworzony!", "app.components.ProjectTemplatePreview.typeProjectName": "Wpisz nazwę projektu", "app.components.ProjectTemplatePreview.useTemplate": "Uż<PERSON>j tego s<PERSON>lonu", "app.components.SeatInfo.additionalSeats": "Dodatkowe <PERSON>", "app.components.SeatInfo.additionalSeatsToolTip": "Pokazuje ona liczbę dodatkowych miejsc, które zostały zakupione oprócz \"Miejsc wliczonych w cenę\".", "app.components.SeatInfo.adminSeats": "<PERSON><PERSON><PERSON> d<PERSON>", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} w zestawie fotele administratora", "app.components.SeatInfo.adminSeatsTooltip1": "<PERSON><PERSON> są odpowiedzialni za platformę i mają prawa zarządcze do wszystkich folderów i projektów. Możesz {visitHelpCenter} , aby dowiedzieć się więcej o różnych rolach.", "app.components.SeatInfo.currentAdminSeatsTitle": "Aktualne mi<PERSON> d<PERSON>ów", "app.components.SeatInfo.currentManagerSeatsTitle": "Obecne miejsca k<PERSON>owników", "app.components.SeatInfo.includedAdminToolTip": "Pokazuje liczbę dostępnych miejsc dla administratorów objętych rocznym kontraktem.", "app.components.SeatInfo.includedManagerToolTip": "Pokazuje liczbę dostępnych miejsc dla menedżerów objętych rocznym kontraktem.", "app.components.SeatInfo.includedSeats": "Dołączone siedzenia", "app.components.SeatInfo.managerSeats": "<PERSON><PERSON><PERSON> d<PERSON> k<PERSON>ownik<PERSON>", "app.components.SeatInfo.managerSeatsTooltip": "Menedżerowie folderów/projektów mogą zarządzać nieograniczoną liczbą folderów/projektów. Możesz {visitHelpCenter} , aby dowiedzieć się więcej o różnych rolach.", "app.components.SeatInfo.managersIncludedText": "{managerSeats} w zestawie fotele menedżerskie", "app.components.SeatInfo.remainingSeats": "Pozostałe miejsca", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "<PERSON><PERSON><PERSON>", "app.components.SeatInfo.totalSeatsTooltip": "Pokazuje on zsumowaną liczbę miejsc w ramach Twojego planu oraz dodatkowe miejsca, które zakupiłeś.", "app.components.SeatInfo.usedSeats": "Siedzenia używane", "app.components.SeatInfo.view": "<PERSON><PERSON><PERSON><PERSON>", "app.components.SeatInfo.visitHelpCenter": "odwiedź nasze centrum pomocy", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Twój plan ma {adminSeatsIncluded}. <PERSON>dy wykorzystasz wszystkie miejsca, dodatkowe miejsca zostaną dodane w zakładce \"Dodatkowe miejsca\".", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Twój plan ma {manager<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}, kwalifikuje się do kierowników folderów i kierowników projektów. Gdy wykorzystasz wszystkie miejsca, dodatkowe miejsca zostaną dodane w sekcji \"Dodatkowe miejsca\".", "app.components.UserSearch.addModerators": "<PERSON><PERSON><PERSON>", "app.components.UserSearch.searchUsers": "Przeszukaj użytkowników", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternatywny komunikat o błędzie", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "Domyślnie użytkownikom wyświetlany jest następujący komunikat o błędzie:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Dostosuj komunikat o błędzie", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "Możesz zastąpić ten komunikat dla każdego języka, korzystając z pola tekstowego \"Alternatywny komunikat o błędzie\" poniżej. Je<PERSON><PERSON> pozostawisz pole tekstowe puste, wyświetlony zostanie domyślny komunikat.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "Komunikat o błędzie", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "To właśnie zobaczą uczestnicy, którzy nie spełniają wymogów uczestnictwa.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Zapisz komunikat o błędzie", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "Nie wybrano żadnego pytania. Wybierz pytanie jako pier<PERSON>.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "<PERSON><PERSON> o<PERSON><PERSON>wiedzi", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} o<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} aż do teraz", "app.components.admin.DatePhasePicker.Input.openEnded": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.DatePhasePicker.Input.selectDate": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datę końcową", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Jasna data rozpoczęcia", "app.components.admin.Graphs": "Brak danych dla obecnie stosowanych filtrów.", "app.components.admin.Graphs.noDataShort": "<PERSON><PERSON> danych.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Uwagi na przestrzeni czasu", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Stanowiska w czasie", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Reakcje w czasie", "app.components.admin.InputManager.onePost": "1 wkład", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Dostosowanie typów offline", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Dostosowanie głosów offline", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "Ta opcja umożliwia uwzględnienie danych dotyczących uczestnictwa z innych źródeł, takich jak głosowania osobiste lub papierowe:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Będzie on wizualnie odróżniał się od głosów cyfrowych.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "Wpłynie to na ostateczne wyniki głosowania.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "<PERSON><PERSON> b<PERSON> to odzwierciedlone w pulpitach nawigacyjnych danych dotyczących uczestnictwa.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Głosy offline dla opcji można ustawić tylko raz w projekcie i są one współdzielone między wszystkimi fazami projektu.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "Najpierw musisz wprowadzić całkowitą liczbę uczestników offline.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Łączna liczba uczestników offline", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>e wyniki, mus<PERSON><PERSON> z<PERSON> <b>całkowitą liczbę uczestników offline w tej fazie</b>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Wskaż tylko tych, którzy uczestniczyli offline.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "Zmodyfikowany przez {name}", "app.components.admin.PostManager.PostPreview.assignee": "Moderator", "app.components.admin.PostManager.PostPreview.cancelEdit": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.currentStatus": "Aktualny status", "app.components.admin.PostManager.PostPreview.delete": "Usuń", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Czy na pewno chcesz usunąć ten wkład? Tej akcji nie można cofnąć.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Czy na pewno chcesz usunąć ten wkład? Zostanie on usunięty ze wszystkich etapów projektu i nie będzie mógł być odzyskany.", "app.components.admin.PostManager.PostPreview.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.noOne": "Nieprzypisany", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Liczba przypadków, w których zostało to uwzględnione w budżetach obywatelskich innych uczestników", "app.components.admin.PostManager.PostPreview.picks": "Wybrane: {picks<PERSON><PERSON>ber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Liczy się reakcja:", "app.components.admin.PostManager.PostPreview.save": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.submitError": "Błąd", "app.components.admin.PostManager.addFeatureLayer": "<PERSON><PERSON>j <PERSON>w<PERSON> funk<PERSON>", "app.components.admin.PostManager.addFeatureLayerInstruction": "Skopiuj adres URL warstwy funkcji hostowanej w ArcGIS Online i wklej go w polu poniżej:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Dodaj nową warstwę funkcji do mapy", "app.components.admin.PostManager.addWebMap": "Dodaj map<PERSON>ową", "app.components.admin.PostManager.addWebMapInstruction": "Skopiuj identyfikator portalu swojej mapy internetowej z ArcGIS Online i wklej go w polu poniżej:", "app.components.admin.PostManager.allPhases": "Wszystkie etapy", "app.components.admin.PostManager.allProjects": "Wszystkie projekty", "app.components.admin.PostManager.allStatuses": "Wszystkie statusy", "app.components.admin.PostManager.allTopics": "Wszystkie tematy", "app.components.admin.PostManager.anyAssignment": "Wszystkie", "app.components.admin.PostManager.assignedTo": "{assigneeName}", "app.components.admin.PostManager.assignedToMe": "Przydzielony do mnie", "app.components.admin.PostManager.assignee": "Moderator", "app.components.admin.PostManager.authenticationError": "Wystąpił błąd uwierzytelniania podczas próby pobrania tej warstwy. Sprawdź adres URL i czy Twój klucz API Esri ma dostęp do tej warstwy.", "app.components.admin.PostManager.automatedStatusTooltipText": "Status ten aktualizuje się automatycznie po spełnieniu określonych warunków", "app.components.admin.PostManager.bodyTitle": "Opis", "app.components.admin.PostManager.cancel": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel2": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.co-sponsors": "Współsponsorzy", "app.components.admin.PostManager.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "<PERSON><PERSON>za to, że utracisz wszystkie dane powiązane z tymi danymi wejściowymi, takie jak komentarze, reakcje i głosy. Tego działania nie można cofnąć.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "<PERSON>zy na pewno chcesz usunąć te wejścia?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "<PERSON><PERSON><PERSON> temat", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "Próbujesz usunąć ten pomysł z fazy, w której otrzymał on głosy. <PERSON><PERSON><PERSON> to zrobisz, głosy te zostaną utracone. Czy na pewno chcesz usunąć ten pomysł z tej fazy?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "Głosy związane z tym pomysłem zostaną utracone", "app.components.admin.PostManager.components.goToInputManager": "Przejdź do menedżera wprowadzania danych", "app.components.admin.PostManager.components.goToProposalManager": "Przejdź do menedżera propozycji", "app.components.admin.PostManager.contributionFormTitle": "Edycja wkładu", "app.components.admin.PostManager.cost": "Koszt", "app.components.admin.PostManager.createInput": "Utw<PERSON>rz dane we<PERSON>", "app.components.admin.PostManager.createInputsDescription": "Utwórz nowy zestaw danych wejściowych z poprzedniego projektu.", "app.components.admin.PostManager.currentLat": "Aktualna <PERSON> geograficzna", "app.components.admin.PostManager.currentLng": "Aktualna dł<PERSON>ć geograficzna", "app.components.admin.PostManager.currentZoomLevel": "Aktualny poziom powiększenia", "app.components.admin.PostManager.defaultEsriError": "Wystąpił błąd podczas próby pobrania tej warstwy. Sprawdź połączenie sieciowe i poprawność adresu URL.", "app.components.admin.PostManager.delete": "Usuń", "app.components.admin.PostManager.deleteAllSelectedInputs": "<PERSON><PERSON><PERSON> {count} inic<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.deleteConfirmation": "<PERSON>zy na pewno chcesz usunąć tę warstwę?", "app.components.admin.PostManager.dislikes": "<PERSON>e lubi", "app.components.admin.PostManager.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.editProjects": "<PERSON><PERSON><PERSON><PERSON> proje<PERSON>", "app.components.admin.PostManager.editStatuses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.editTags": "<PERSON><PERSON><PERSON><PERSON> tagi", "app.components.admin.PostManager.editedPostSave": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Importowanie danych z Esri ArcGIS Online jest funkcją dodatkową. Porozmawiaj z menedżerem GS, aby ją od<PERSON><PERSON><PERSON><PERSON>.", "app.components.admin.PostManager.esriSideError": "Wystąpił błąd w aplikacji ArcGIS. Poczekaj kilka minut i spróbuj ponownie później.", "app.components.admin.PostManager.esriWebMap": "Mapa internetowa Esri", "app.components.admin.PostManager.exportAllInputs": "Eksport wszystkich inicjatyw (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Eksport wszystkich komentarzy (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Eksport wszystkich komentarzy do tego projektu (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Eksport inicjatyw dla tego pomysłu (.xlsx)", "app.components.admin.PostManager.exportSelectedInputs": "Eksport wybranych inicjatyw (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Eksport komentarzy dla wybranych inicjatyw (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Eksportuj głosy według danych wejściowych (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Eksportuj głosy według użytkownika (.xslx)", "app.components.admin.PostManager.exports": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "<PERSON>ż<PERSON><PERSON> przesyłać dane map tylko jako warstwy GeoJSON lub importować je z ArcGIS Online. Usuń wszystkie bieżące warstwy GeoJSON, je<PERSON><PERSON> ch<PERSON>z dodać warstwę funkcji.", "app.components.admin.PostManager.featureLayerTooltop": "Możesz znaleźć adres URL warstwy funkcji po prawej stronie strony elementu w ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, jak wyświ<PERSON><PERSON> będzie Twoje imię", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Wyjaśnij zmianę statusu", "app.components.admin.PostManager.fileUploadError": "Nie udało się załadować jednego lub więcej plików. Proszę sprawdzić rozmiar i format pliku i spróbować ponownie.", "app.components.admin.PostManager.formTitle": "Pomysł na edycję", "app.components.admin.PostManager.generalApiError2": "Wystąpił błąd podczas próby pobrania tego elementu. Sprawdź, czy adres URL lub identyfikator portalu jest poprawny i czy masz dostęp do tego elementu.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Możes<PERSON> przesyłać dane map tylko jako warstwy GeoJSON lub importowa<PERSON> je z ArcGIS Online. Jeśli chcesz przesłać warstwę GeoJSON, usuń wszelkie dane ArcGIS.", "app.components.admin.PostManager.goToDefaultMapView": "Przejdź do domyślnego wyśrodkowania mapy i powiększenia", "app.components.admin.PostManager.hiddenFieldsLink": "ukryte pola", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Podpowiedź: dodaj {hiddenFieldsLink} podczas tworzenia ankiety, aby w<PERSON>, kto na nią odpowiedział.", "app.components.admin.PostManager.import2": "Import", "app.components.admin.PostManager.importError": "Wybrany plik nie mógł zostać zaimportowany, poni<PERSON><PERSON><PERSON> nie jest to poprawny plik GeoJSON", "app.components.admin.PostManager.importEsriFeatureLayer": "Importuj warstwę cech Esri", "app.components.admin.PostManager.importEsriWebMap": "Importuj mapę internetową Esri", "app.components.admin.PostManager.importInputs": "<PERSON><PERSON>rt<PERSON><PERSON> da<PERSON>", "app.components.admin.PostManager.imported": "I<PERSON>rt<PERSON>ne", "app.components.admin.PostManager.initiativeFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputCommentsExportFileName": "wklad_komentarze", "app.components.admin.PostManager.inputImportProgress": "{importedCount} z {totalCount} {totalCount, plural, one {wejście ma} other {wejścia mają}} zostały zaimportowane. Import jest nadal w toku, sprawdź później.", "app.components.admin.PostManager.inputManagerHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputsExportFileName": "wklad", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Pokaż tylko te inicjatywy, które wymagają informacji zwrotnej", "app.components.admin.PostManager.issueFormTitle": "<PERSON><PERSON><PERSON><PERSON>u", "app.components.admin.PostManager.latestFeedbackMode": "Użyj najnowszej oficjalnej aktualizacji", "app.components.admin.PostManager.layerAdded": "Warstwa dodana p<PERSON>ślnie", "app.components.admin.PostManager.likes": "<PERSON><PERSON>", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Przeniesienie tego wkładu poza jego aktualny projekt spowoduje utratę informacji o przypisanych mu etapach. <PERSON><PERSON> ch<PERSON> k<PERSON>?", "app.components.admin.PostManager.mapData": "<PERSON> mapy", "app.components.admin.PostManager.multipleInputs": "{ideaCount} inicjatyw", "app.components.admin.PostManager.newFeedbackMode": "Napisz nową aktualizację, aby wyja<PERSON><PERSON>ć tę zmianę", "app.components.admin.PostManager.noFilteredResults": "Brak wyników dla wybranych filtrów", "app.components.admin.PostManager.noInputs": "<PERSON>rak danych wejściowych", "app.components.admin.PostManager.noInputsDescription": "<PERSON><PERSON><PERSON><PERSON> dodać własny wkład lub roz<PERSON>ć od poprzedniego projektu.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 wejś<PERSON>} one {1 wejście} other {# wejścia}} zostaną zaimportowane z wybranego projektu i fazy. Import będzie działał w tle, a dane wejściowe pojawią się w menedżerze danych wejściowych po jego zakończeniu.", "app.components.admin.PostManager.noOne": "Nieprzypisany", "app.components.admin.PostManager.noProject": "Brak projektu", "app.components.admin.PostManager.officialFeedbackModal.author": "Autor", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "<PERSON><PERSON><PERSON><PERSON> sposób wyświetlania swojego imienia", "app.components.admin.PostManager.officialFeedbackModal.description": "Przekazywanie oficjalnych opinii pomaga zachować przejrzystość procesu i buduje zaufanie do platformy.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Autor jest wymagany", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Wymagana jest informacja zwrotna", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Oficjalna informacja zwrotna", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Wyjaśnij powód zmiany statusu", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "<PERSON>rz<PERSON><PERSON><PERSON><PERSON> opini<PERSON>", "app.components.admin.PostManager.officialFeedbackModal.skip": "Pomiń ten czas", "app.components.admin.PostManager.officialFeedbackModal.title": "Wyjaśnij swoją decyzję", "app.components.admin.PostManager.officialUpdateAuthor": "<PERSON><PERSON><PERSON><PERSON>, jak wyświ<PERSON><PERSON> będzie Twoje imię", "app.components.admin.PostManager.officialUpdateBody": "Wyjaśnij zmianę statusu", "app.components.admin.PostManager.offlinePicks": "Wybór offline", "app.components.admin.PostManager.offlineVotes": "Głosowania offline", "app.components.admin.PostManager.onlineVotes": "Głosowania online", "app.components.admin.PostManager.optionFormTitle": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participants": "Uczestnicy", "app.components.admin.PostManager.participatoryBudgettingPicks": "Wy<PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Wybór online", "app.components.admin.PostManager.pbItemCountTooltip": "Liczba przypadków, w których zostało to uwzględnione w budżetach obywatelskich innych uczestników", "app.components.admin.PostManager.petitionFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.postedIn": "Posted in {projectLink}", "app.components.admin.PostManager.projectFormTitle": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.components.admin.PostManager.projectsTab": "Projekty", "app.components.admin.PostManager.projectsTabTooltipContent": "Możesz przeciągać i umieszczać posty, aby prz<PERSON> je z jednego projektu do drugiego. <PERSON><PERSON>wadze, że w przypadku projektów na osi czasu, nadal będziesz musiał dodać post do określonego etapu.", "app.components.admin.PostManager.proposalFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.proposedBudgetTitle": "Proponowany budżet", "app.components.admin.PostManager.publication_date": "Opublikowano", "app.components.admin.PostManager.questionFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.resetFiltersButton": "Usuń filtry", "app.components.admin.PostManager.resetInputFiltersDescription": "Zresetuj filtry, aby zob<PERSON><PERSON><PERSON> wszystkie inicjatywy.", "app.components.admin.PostManager.saved": "Zapisane", "app.components.admin.PostManager.screeningTooltip": "Screening nie jest uwzględniony w Twoim obecnym planie. Porozmawiaj ze swoim Government Success Managerem lub <PERSON>em, aby j<PERSON> od<PERSON><PERSON><PERSON>.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Ekranowanie jest wyłączone dla tej fazy. Przejdź do konfiguracji fazy, aby ją włą<PERSON>yć", "app.components.admin.PostManager.selectAPhase": "Wybierz fazę", "app.components.admin.PostManager.selectAProject": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.components.admin.PostManager.setAsDefaultMapView": "Zapisz aktualny punkt wyśrodkowania mapy", "app.components.admin.PostManager.startFromPastInputs": "Zacznij od danych wejściowych z przeszłości", "app.components.admin.PostManager.statusChangeGenericError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pro<PERSON><PERSON> spróbuj ponownie później lub skontaktuj się z <EMAIL>.", "app.components.admin.PostManager.statusChangeSave": "Zmiana statusu", "app.components.admin.PostManager.statusesTab": "Statusy", "app.components.admin.PostManager.statusesTabTooltipContent": "Zmień status postu metodą \"przeciągnij i upuść\". Autor postu i inni, którzy się pod nim wypowiadali otrzymają powiadomienie o zmienionym statusie.", "app.components.admin.PostManager.submitApiError": "Wystąpił problem z przesłaniem formularza. <PERSON><PERSON><PERSON> spraw<PERSON>, czy nie ma błędów i spróbować ponownie.", "app.components.admin.PostManager.timelineTab": "Etapy", "app.components.admin.PostManager.timelineTabTooltipText": "Przeciągnij i upuść inicjatywy, aby skopiować je do różnych etapów projektu.", "app.components.admin.PostManager.title": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.topicsTab": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.topicsTabTooltipText": "Add tags to an input using drag and drop.", "app.components.admin.PostManager.view": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votes": "Głosy", "app.components.admin.PostManager.votesByInputExportFileName": "votes_by_input", "app.components.admin.PostManager.votesByUserExportFileName": "votes_by_user", "app.components.admin.PostManager.webMapAlreadyExists": "Jednocześnie możesz dodać tylko jedną mapę sieci Web. Usuń bieżącą mapę, aby zaimportować inną.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Moż<PERSON><PERSON> przesy<PERSON>ć dane mapy tylko jako warstwy GeoJSON lub importować je z ArcGIS Online. Jeśli chcesz podłączyć mapę internetową, usuń wszystkie bieżące warstwy GeoJSON.", "app.components.admin.PostManager.webMapTooltip": "Możesz znaleźć identyfikator portalu Web Map na stronie elementu ArcGIS Online, po prawej stronie.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {<PERSON><PERSON><PERSON> jeden dzie<PERSON>} one {<PERSON><PERSON> d<PERSON>} other {# days}} left", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "<PERSON><PERSON><PERSON>", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "Tak, usuń wyniki ankiety", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "<PERSON>e można tego co<PERSON>", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Usuń wyniki ankiety", "app.components.admin.ProjectEdit.survey.downloadResults2": "Pobierz wyniki ankiety", "app.components.admin.ReportExportMenu.FileName.fromFilter": "od", "app.components.admin.ReportExportMenu.FileName.groupFilter": "grupa", "app.components.admin.ReportExportMenu.FileName.projectFilter": "projekt", "app.components.admin.ReportExportMenu.FileName.topicFilter": "temat", "app.components.admin.ReportExportMenu.FileName.untilFilter": "do", "app.components.admin.ReportExportMenu.downloadPng": "Pobierz jako P<PERSON>", "app.components.admin.ReportExportMenu.downloadSvg": "Pobierz jako SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Pobierz Excel", "app.components.admin.SlugInput.regexError": "<PERSON>rl może zawiera<PERSON> tylko z<PERSON>, małe litery (a-z), <PERSON><PERSON><PERSON><PERSON> (0-9) i myślniki (-). Pierwszy i ostatni znak nie mogą być myślnikami. Kolejne myślniki (--) są niedozwolone.", "app.components.admin.TerminologyConfig.saveButton": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.commonGroundInputManager.title": "<PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.admin": "Administrator", "app.components.admin.seatSetSuccess.allDone": "Wszystko zrobione", "app.components.admin.seatSetSuccess.close": "Zamknij", "app.components.admin.seatSetSuccess.manager": "Kierownik", "app.components.admin.seatSetSuccess.orderCompleted": "Zamówienie zrealizowane", "app.components.admin.seatSetSuccess.reflectedMessage": "Zmiany w Twoim planie zostaną odzwierciedlone w następnym cyklu rozliczeniowym.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} uprawnienia zostały przyznane wybranemu użytkownikowi (użytkownikom).", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "<PERSON>zy na pewno chcesz usunąć wszystkie wyniki ankiety?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "Ta metoda uczestnictwa jest w fazie beta. Stopniowo wprowadzamy ją w celu zebrania opinii i ulepszenia doświadczenia.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Zbieranie opinii na temat dokumentu jest funkcją niestandardową i nie jest objęte Twoją obecną licencją. Skontaktuj się ze swoim menedżerem GovSuccess, aby dowiedzieć się więcej na ten temat.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Wymagana liczba dni", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "Liczba dni do osiągnięcia minimalnej liczby głosów", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "<PERSON><PERSON><PERSON><PERSON>j informacji o tym, jak <PERSON> link dla Google Forms można znaleźć w {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "ten artykuł pomocniczy", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Inicjatywa", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Jak nazwać wkład?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Potrzeba", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Podaj link do swojego dokumentu Konveio tutaj. Przeczytaj naszą stronę {supportArticleLink} , aby u<PERSON><PERSON>ć więcej informacji na temat konfigurowania Konveio.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "<PERSON>e jest to uwzględnione w Twoim obecnym planie. Skontaktuj się z Government Success Managerem lub administratorem, aby ją od<PERSON><PERSON><PERSON>.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "<PERSON><PERSON><PERSON><PERSON> jest maksym<PERSON>ny budżet", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Maksymalna liczba głosów na opcję musi być mniejsza lub równa całkowitej liczbie głosów.", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Wymagana jest maksymalna liczba głosów", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Budżet minimalny nie może być większy niż budżet maksymalny", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "<PERSON><PERSON><PERSON><PERSON> jest bud<PERSON><PERSON>ny", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Minimalna liczba głosów nie może być większa niż maksymalna.", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "<PERSON><PERSON><PERSON>a jest minimalna liczba głosów", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Brakująca data końcowa", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Brakująca data rozpoczęcia", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "<PERSON><PERSON> wprowadzania", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Skonfiguruj opcje głosowania na karcie Menedżer wprowadzania po utworzeniu fazy.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Skonfiguruj opcje głosowania na stronie {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Opcje uczestnictwa", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "Uczestnicy", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Administratorzy i menedżerowie", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Dokument z adnotacjami:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} m<PERSON><PERSON> w tej fazie.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Komentarz:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Wspólna faza uziemienia", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Usuń fazę", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "Tak, usuń tę fazę", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "<PERSON>zy na pewno chcesz usunąć tę fazę?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "Wszystkie dane dotyczące tej fazy zostaną usunięte. Nie można tego cofnąć.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Faza adnot<PERSON> do<PERSON>u", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Wsz<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "Faza badania zewnętrznego", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Faza pomysłu", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "W fazie badania platformy", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Faza informacyjna", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "<PERSON><PERSON><PERSON>e", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "<PERSON>rak daty końcowej", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Faza głosowania", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Faza propozycji", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>React:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Zarejestrowany na wydarzenie:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Zarejestrowani użytkownicy", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane we<PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b><PERSON><PERSON> udział w ankiecie:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b><PERSON><PERSON> udział w ankiecie:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Użytkownicy z potwierdzonymi adresami e-mail", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>Wolontariat:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Faza wolontariatu", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Głosowanie:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Faza głosowania", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Kto może wziąć udział?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "<PERSON><PERSON>y nie bę<PERSON><PERSON> w<PERSON>, dopóki administrator ich nie sprawdzi i nie zatwierdzi. Autorzy nie mogą edytować wpisów po ich sprawdzeniu lub zareagowaniu na nie.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "Tylko <PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "<PERSON><PERSON><PERSON>, kto posiada łącze, może wejść w interakcję z wersją roboczą projektu", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "Zatwierdź", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Zatwierdzenie umożliwia kierownikom projektów opublikowanie projektu.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "Zatwierdzono przez {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Zarchiwizowane", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "<PERSON><PERSON><PERSON> rob<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "<PERSON><PERSON><PERSON>j opis", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "Wsz<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Grupy", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "<PERSON>k<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Wyborcy offline", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "<PERSON>lk<PERSON>zy{inFolder, select, true { lub men<PERSON><PERSON><PERSON>wi<PERSON>} other {}} mogą publikować projekt.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 uczestnik} other {{participantsCount} uczestnicy}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Uczestnicy metod wbudowanych (np. ankiet zewnętrznych)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Obserwujący projekt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Uwaga: Włączenie uprawnień do anonimowego lub otwartego uczestnictwa może pozwolić użytkownikom na wielokrotne uczestnictwo, prowadząc do mylących lub niekompletnych danych użytkownika.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "Uczest<PERSON><PERSON> <b>nie o<PERSON><PERSON><PERSON><PERSON><PERSON></b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Wśród uczestników są:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Rejestrujący wydarzenie", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "Użytkownicy korzystający z metod Go Vocal", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "Oczekiwanie na zatwierdzenie", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Recenzenci projektu zostali powiadomieni.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "Publiczny", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Opublikowany - Aktywny", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Opublikowano - Zakończono", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Odśwież łącze podglądu projektu", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Wygeneruj ponownie łącze podglądu projektu. Spowoduje to unieważnienie poprzedniego łącza.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Stare linki przestaną d<PERSON>, ale możesz wygenerować nowe w dowolnym momencie.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Jesteś pewien? Spowoduje to wyłączenie bieżącego łącza", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "Tak, odśwież link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Poproś o zatwierdzenie", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "Projekt musi zostać zatwierdzony przez administratora{inFolder, select, true { lub jednego z menedżerów folderów} other {}} zanim będziesz mógł go opublikować. Kliknij poniższy przycisk, aby <PERSON> o zatwierdzenie.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Ustawienia", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Udostępnij", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "Skopiuj link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Udostępnianie prywatnych linków nie jest uwzględnione w Twoim obecnym planie. Porozmawiaj ze swoim Government Success Managerem lub <PERSON>em, aby j<PERSON> od<PERSON><PERSON>.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Udostępnij ten projekt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "<PERSON><PERSON> ma dost<PERSON>p", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Projekt", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Propozycja", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Minimalna liczba głosów do uwzględnienia", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "<PERSON><PERSON><PERSON>a jest minimalna liczba głosów", "app.components.app.containers.AdminPage.ProjectEdit.report": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Wymagaj sprawdzania danych wejściowych", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "<PERSON><PERSON> drogowy", "app.components.formBuilder.cancelMethodChange1": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.changeMethodWarning1": "Zmiana metody może prowadzić do ukrycia wszelkich danych wejściowych wygenerowanych lub odebranych podczas korzystania z poprzedniej metody.", "app.components.formBuilder.changingMethod1": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.confirmMethodChange1": "Tak, kontynuuj", "app.components.formBuilder.copySurveyModal.cancel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.description": "S<PERSON>woduje to skopiowanie wszystkich pytań i logiki bez odpowiedzi.", "app.components.formBuilder.copySurveyModal.duplicate": "Duplikat", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "W tym projekcie nie znaleziono odpowiednich faz", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "Nie wybrano żadnej fazy. Najpierw wybierz fazę.", "app.components.formBuilder.copySurveyModal.noProject": "Brak projektu", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Nie wybrano żadnego projektu. Wybierz projekt jako pier<PERSON>.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "Zapisałeś już zmiany w tej ankiecie. Je<PERSON>li powielisz inną ankietę, zmiany zostaną utracone.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Faza bad<PERSON>", "app.components.formBuilder.copySurveyModal.title": "Wybierz ankietę do powielenia", "app.components.formBuilder.editWarningModal.addOrReorder": "Dodaj lub zmień kolejność pytań", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "<PERSON>je dane odpowiedzi mogą być niedokładne", "app.components.formBuilder.editWarningModal.changeQuestionText2": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Poprawiasz literówkę? Nie wpłynie to na Twoje dane odpowiedzi", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Utracisz dane odpowiedzi powiązane z tym pytaniem", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Usuń pytanie", "app.components.formBuilder.editWarningModal.exportYouResponses2": "eksportuj swoje odpowiedzi.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Ostrzeżenie: <PERSON><PERSON><PERSON><PERSON> utracić dane odpowiedzi na zawsze. Przed kontynuowaniem,", "app.components.formBuilder.editWarningModal.noCancel": "<PERSON><PERSON>, anuluj", "app.components.formBuilder.editWarningModal.title4": "Edytuj ankietę na żywo", "app.components.formBuilder.editWarningModal.yesContinue": "Tak, kontynuuj", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "ustawienia praw dostępu dla tej ankiety", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "Op<PERSON>ja \"Pola demograficzne w formularzu ankiety\" jest włącz<PERSON>. Po wyświetleniu formularza ankiety wszelkie skonfigurowane pytania demograficzne zostaną dodane na nowej stronie bezpośrednio przed zakończeniem ankiety. Pytania te można zmienić na stronie {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "ustawienia praw dostępu dla tej fazy.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Respondenci ankiety nie będą musieli się rejestrować ani logować, aby prz<PERSON><PERSON> odpowiedzi na pytania ankiety, co może skutkować powielaniem zgłoszeń.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "Pomijając krok rejestracji/zalogowania, zgadzasz się nie zbierać informacji demograficznych o respondentach ankiety, co może mieć wpływ na możliwości analizy danych.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "Ta ankieta jest ustawiona tak, aby z<PERSON><PERSON> na dostęp dla \"każdego\" w zakładce Prawa dostępu.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "<PERSON><PERSON><PERSON> to zmienić, <PERSON><PERSON><PERSON><PERSON><PERSON> to zrobić na stronie {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Zadajesz następujące pytania demograficzne respondentom ankiety na etapie rejestracji/zalogowania.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "Aby uspra<PERSON>ić gromadzenie informacji demograficznych i zapewnić ich integrację z bazą danych użytkowników, zalecamy włączenie wszelkich pytań demograficznych bezpośrednio do procesu rejestracji / logowania. Aby to zrobić, skorzystaj z {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Poproś użytkowników o śledzenie obszarów lub tematów", "app.components.onboarding.followHelperText": "Aktywuje to krok w procesie rejestracji, w którym użytkownicy będą mogli śledzić obszary lub tematy, które wybierzesz poniżej", "app.components.onboarding.followPreferences": "Postępuj zgodnie z preferencjami", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} w ramach planu, {noOfAdditionalSeats} dodatkowe", "app.components.seatsWithinPlan.seatsWithinPlanText": "Miejsca w ramach planu", "app.containers.Admin.Campaigns.campaignFrom": "Od:", "app.containers.Admin.Campaigns.campaignTo": "Do:", "app.containers.Admin.Campaigns.customEmails": "Własne e-maile", "app.containers.Admin.Campaigns.customEmailsDescription": "Wysyłaj niestandardowe wiadomości e-mail i sprawdzaj statystyki.", "app.containers.Admin.Campaigns.noAccess": "<PERSON><PERSON><PERSON><PERSON> nam, ale wygląda na to, że nie masz dostępu do działu poczty elektronicznej", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Zautomatyzowane wiadomości e-mail", "app.containers.Admin.Insights.tabReports": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.a11y_removeInvite": "Us<PERSON>ń zaproszenie", "app.containers.Admin.Invitations.addToGroupLabel": "Dodaj te osoby do określonych samodzielnie zdefiniowanych grup użytkowników", "app.containers.Admin.Invitations.adminLabel1": "<PERSON><PERSON><PERSON> zaproszonym osobom prawa administratora", "app.containers.Admin.Invitations.adminLabelTooltip": "Po wybraniu tej opcji zaproszone osoby będą miały dostęp do wszystkich ustawień Twojej platformy.", "app.containers.Admin.Invitations.configureInvitations": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Nie ma żadnych zaproszeń spełniających Twoje kryteria przeszukiwania", "app.containers.Admin.Invitations.deleteInvite": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.deleteInviteConfirmation": "<PERSON>zy na pewno chcesz usunąć to zaproszenie?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Anulowanie zaproszenia pozwoli Ci na ponowne wysłanie zaproszenia do tej osoby.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Pobierz i uzupełnij szablon", "app.containers.Admin.Invitations.downloadTemplate": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.email": "E-mail", "app.containers.Admin.Invitations.emailListLabel": "Wpisz adresy e-mail oddzielone przecinkami w polu poniżej.", "app.containers.Admin.Invitations.exportInvites": "Eksport listy zaproszeń", "app.containers.Admin.Invitations.fileRequirements": "Ważne: W celu prawidłowego wysłania zaproszeń nie można usunąć żadnej kolumny z szablonu importu. Nieużywane kolumny należy pozostawić puste.", "app.containers.Admin.Invitations.filetypeError": "Nieprawidłowy typ pliku. Obsługiwane są tylko pliki XLSX.", "app.containers.Admin.Invitations.groupsPlaceholder": "Nie wybrano żadnej grupy", "app.containers.Admin.Invitations.helmetDescription": "Zaproś użytkowników na platformę", "app.containers.Admin.Invitations.helmetTitle": "Panel administracyjny zaproszeń", "app.containers.Admin.Invitations.importOptionsInfo": "Te opcje będą brane pod uwagę tylko wtedy, gdy nie zostaną zdefiniowane w pliku Excel.\n      Więcej informacji można znaleźć na stronie {supportPageLink}.", "app.containers.Admin.Invitations.importTab": "Zaimportuj adresy e-mail", "app.containers.Admin.Invitations.invitationExpirationWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON>, że zaproszenia tracą ważność po 30 dniach. Po tym okresie możesz je wysłać ponownie.", "app.containers.Admin.Invitations.invitationOptions": "<PERSON><PERSON><PERSON> zaproszeń", "app.containers.Admin.Invitations.invitationSubtitle": "<PERSON><PERSON><PERSON><PERSON>so<PERSON>, kt<PERSON>re nie są jeszcze zarejestrowane na platformie. Zaimportuj ich adresy e-mail, umieszczając je w szablonie do importu lub wprowadź je ręcznie. W razie potrzeby dodaj osobistą w<PERSON>, daj użytkownikom dodatkowe uprawnienia lub dodaj ich do grupy.", "app.containers.Admin.Invitations.invitePeople": "Zaproszenia", "app.containers.Admin.Invitations.inviteStatus": "Status", "app.containers.Admin.Invitations.inviteStatusAccepted": "Przyjęte", "app.containers.Admin.Invitations.inviteStatusPending": "Oczek<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteTextLabel": "Dodaj osobistą wiadomość do zaproszenia", "app.containers.Admin.Invitations.invitedSince": "Zaproszony", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Wybierz język zaproszenia", "app.containers.Admin.Invitations.moderatorLabel": "Ud<PERSON><PERSON> tym osobom uprawnień menadżera projektu", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Po wybraniu tej opcji o<PERSON>, które zostaną zaproszone, staną się menadżerami wybranych projektów. Więcej informacji o zarządzaniu {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "tutaj", "app.containers.Admin.Invitations.name": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.processing": "Wysyłanie zaproszeń. Proszę zaczekaj...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "<PERSON>e wybrano żadnego projektu(-ów)", "app.containers.Admin.Invitations.save": "Wyślij swoje zaproszenia", "app.containers.Admin.Invitations.saveErrorMessage": "Wystą<PERSON>ł jeden lub więcej błędów.\n      Dlatego nie wysłano żadnych zaproszeń.\n      Proszę popraw poniższe błędy wymienione i spróbuj ponownie.", "app.containers.Admin.Invitations.saveSuccess": "Sukces!", "app.containers.Admin.Invitations.saveSuccessMessage": "Pomyślne wysłanie zaproszenia.", "app.containers.Admin.Invitations.supportPage": "strona wsparcia", "app.containers.Admin.Invitations.supportPageLinkText": "O<PERSON>wi<PERSON><PERSON> stronę pomocy technicznej", "app.containers.Admin.Invitations.tabAllInvitations": "Wszystkie zaproszenia", "app.containers.Admin.Invitations.tabInviteUsers": "<PERSON><PERSON><PERSON><PERSON> lud<PERSON>", "app.containers.Admin.Invitations.textTab": "Wprowadź adresy e-mail", "app.containers.Admin.Invitations.unknownError": "Coś poszło nie tak. Proszę spróbuj ponownie później.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uzupełniony plik", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} je<PERSON><PERSON> ch<PERSON>z uzyskać więcej informacji o wszystkich obsługiwanych kolumnach w szablonie importu.", "app.containers.Admin.Moderation.all": "Wszystkie", "app.containers.Admin.Moderation.belongsTo": "Należy do", "app.containers.Admin.Moderation.collapse": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.comment": "Komentarz", "app.containers.Admin.Moderation.commentDeletionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "Usuń", "app.containers.Admin.Moderation.confirmCommentDeletion": "<PERSON>zy na pewno chcesz usunąć ten komentarz? To jest trwałe i nie można tego cofnąć.", "app.containers.Admin.Moderation.content": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.date": "Data", "app.containers.Admin.Moderation.deleteComment": "Us<PERSON>ń komentarz", "app.containers.Admin.Moderation.goToComment": "Otwórz ten komentarz w nowej karcie", "app.containers.Admin.Moderation.goToPost": "Otwórz ten post w nowej karcie", "app.containers.Admin.Moderation.goToProposal": "Otwórz tą propozycję w nowej karcie", "app.containers.Admin.Moderation.markFlagsError": "Nie można zaznaczyć elementu(ów). Spróbuj ponownie.", "app.containers.Admin.Moderation.markNotSeen": "Zaznacz {selectedItemsCount, plural, one {# element} few {# elementy} many {# elementów} other {# elementy}} as not seen", "app.containers.Admin.Moderation.markSeen": "Zaznacz {selectedItemsCount, plural, one {# element} few {# elementy} many {# elementy} other {# elementy}} jako <PERSON>", "app.containers.Admin.Moderation.moderationsTooltip": "Ta strona pozwala na szybkie sprawdzenie wszystkich nowych inicjatyw, które zostały dodane do Twojej platformy, w tym postów i komentarzy. Moż<PERSON>z oznaczyć posty jako \"przejrzane\", aby udzielić informacji zwrotnej.", "app.containers.Admin.Moderation.noUnviewedItems": "Nie ma żadnych nieobejrzanych elementów", "app.containers.Admin.Moderation.noViewedItems": "Nie ma żadnych obejrzanych elementów", "app.containers.Admin.Moderation.pageTitle1": "Pokarm", "app.containers.Admin.Moderation.post": "<PERSON>", "app.containers.Admin.Moderation.profanityBlockerSetting": "Blokada przekleństw", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Blokuj posty zawierające najczęściej zgłaszane obraźliwe słowa.", "app.containers.Admin.Moderation.project": "Projekt", "app.containers.Admin.Moderation.read": "Prz<PERSON>rz<PERSON>", "app.containers.Admin.Moderation.readMore": "Przeczytaj więcej", "app.containers.Admin.Moderation.removeFlagsError": "<PERSON><PERSON> można usun<PERSON> ostrzeżenia(-ń). Spróbuj ponownie.", "app.containers.Admin.Moderation.rowsPerPage": "Wierszy na stronie", "app.containers.Admin.Moderation.settings": "Ustawienia", "app.containers.Admin.Moderation.settingsSavingError": "Nie można zapisać. Spróbuj ponownie zmienić ustawienia.", "app.containers.Admin.Moderation.show": "Po<PERSON><PERSON>", "app.containers.Admin.Moderation.status": "Status", "app.containers.Admin.Moderation.successfulUpdateSettings": "Ustawienia zostały pomyślnie zaktualizowane.", "app.containers.Admin.Moderation.type": "<PERSON><PERSON>", "app.containers.Admin.Moderation.unread": "Nieprz<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Ta strona składa się z następujących sekcji. Możesz je włączyć/wyłączyć i edytować według potrzeb.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ę", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "<PERSON>e pokazane na stronie", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Pokazane na stronie", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Załączniki", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "<PERSON><PERSON><PERSON> (max. 50 MB), kt<PERSON><PERSON> b<PERSON><PERSON><PERSON> dostępne do pobrania ze strony.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Stopka strony głównej", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Dodaj własną treść do konfigurowalnej sekcji na dole strony.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Lista wydarzeń", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Pokaż wydarzenia związane z projektami.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "<PERSON><PERSON> s<PERSON> g<PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Dostosuj obraz i tekst banera strony.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Lista projektów", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Pokaż projekty w oparciu o Twoje ustawienia strony. Moż<PERSON>z podejrzeć projekty, które zostaną pokazane.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Sekcja górna", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Dodaj własną treść do konfigurowalnej sekcji na górze strony.", "app.containers.Admin.PagesAndMenu.addButton": "Dodaj do paska nawigacyjnego", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Nazwa w pasku nawigacyjnym", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "<PERSON>zy na pewno chcesz usunąć ten projekt? Tej operacji nie da się cofnąć.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Podaj tytuł dla wszystkich języków", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> strony", "app.containers.Admin.PagesAndMenu.components.savePage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "Strona została pomyślnie zapisana", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Załączniki (maks. 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Sukces", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Nie można zapisać załączników", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Pliki nie powinny być większe niż 50Mb. Dodane pliki będą widoczne na dole tej strony", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Załączniki zapisane", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Załączniki | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Załączniki", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Zapisz i włącz załączniki", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Zapisz załączniki", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Podaj tre<PERSON> dla wszystkich języków", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Sukces", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Nie można zapisać dolnej części informacji", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Zapisane", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Stopka strony głównej", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Zapisz i włącz dolną sekcję informacyjną", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Tworzenie niestandardowych stron nie jest objęte Twoją obecną licencją. Skontaktuj się ze swoim GovSuccess Managerem, aby dowiedzieć się więcej na ten temat.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Proszę wybrać co najmniej jeden tag", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Sukces", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "Po obszarze", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "<PERSON> tagu(ach)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Wyświetlanie projektów według tagów lub obszarów nie jest częścią Twojej obecnej licencji. Skontaktuj się ze swoim menedżerem GovSuccess, aby dowiedzieć się więcej na ten temat.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> ni<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Powiązane projekty", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "<PERSON><PERSON><PERSON><PERSON>, które projekty i powiązane wydarzenia mogą być wyświetlane na stronie.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Strona została pomyślnie utworzona", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "Strona została pomyślnie zapisana", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Niestandardowa strona zapisana", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Tytuł w pasku nawigacyjnym", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Utwórz własną stronę | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Utwórz włas<PERSON>ą stronę", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "Brak", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Us<PERSON>wi<PERSON> strony", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "<PERSON><PERSON><PERSON>z stronę własną", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "<PERSON><PERSON><PERSON> wy<PERSON> obszar", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Wybrany obszar", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "<PERSON>y<PERSON>ne tagi", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "<PERSON>rl może zawiera<PERSON> tylko z<PERSON>, małe litery (a-z), <PERSON><PERSON><PERSON><PERSON> (0-9) i myślniki (-). Pierwszy i ostatni znak nie mogą być myślnikami. Kolejne myślniki (--) są niedozwolone.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Mu<PERSON>z wpisać url", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Wprowadź tytuł w każdym języku", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Wprowadź tytuł", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Wyświetlanie strony niestandardowej", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "Przycisk", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> ni<PERSON> | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> strony", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "W przypadku projektów jednoetapowych, je<PERSON><PERSON> data zakończenia jest pusta, a opis nie jest wypeł<PERSON>ny, oś czasu nie będzie wyświetlana na stronie projektu.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "Brak dostępnych projektów na podstawie Twojej {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Ten projekt nie ma filtra tagów ani obszaru, więc żadne projekty nie zostaną wyświetlone.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Lista projektów | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "usta<PERSON><PERSON> strony", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Lista projektów", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Następujące projekty zostaną pokazane na tej stronie na podstawie Twojego {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "DOMYŚLNIE", "app.containers.Admin.PagesAndMenu.deleteButton": "Usuń", "app.containers.Admin.PagesAndMenu.editButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Sukces", "app.containers.Admin.PagesAndMenu.heroBannerError": "<PERSON><PERSON> udało się zapisać", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Zapisane", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.homeTitle": "Strona główna", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Podaj tre<PERSON>ć w przynajmniej jednym języku", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Możesz dodać maksymalnie 5 elementów do paska nawigacji", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Strony i menu | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Usuń z nawigacji", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Zapisz i włącz baner bohaterski", "app.containers.Admin.PagesAndMenu.title": "Strony i menu", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Sukces", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.topInfoError": "Nie można zapisać górnej części informacji", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Zapisana górna sekcja informacyjna", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Górna sekcja informacyjna | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Sekcja górna", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Zapisz i włącz sekcję Top info", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Zapisz górną sekcję informacyjną", "app.containers.Admin.PagesAndMenu.viewButton": "Widok", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "<PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Wspólnota", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Streszczenie", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Wskaźniki włączenia na najwyższym poziomie", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "Poniższa sekcja przedstawia wskaźniki inkluzywności, podkreślając nasze postępy w kierunku wspierania bardziej inkluzywnej i reprezentatywnej platformy uczestnictwa.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "uczestnicy", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Wskaźniki uczestnictwa na najwyższym poziomie", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "Poniższa sekcja przedstawia kluczowe wskaźniki uczestnictwa w wybranym przedziale czasowym, zapewniając przegląd trendów zaangażowania i wskaźników wydajności.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projekty", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "opublikowane projekty", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Raport platformy", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "<PERSON>je projekty", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "Poniższa sekcja zawiera przegląd publicznie widocznych projektów, które pokrywają się z wybranym zakresem czasowym, najczęściej używane metody w tych projektach oraz wskaźniki dotyczące całkowitej kwoty uczestnictwa.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Harmonogram rejestracji", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Zablokowani użytkownicy", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Zarządzaj zablokowanymi użytkownikami.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Usuń grupę", "app.containers.Admin.Users.GroupsHeader.editGroup": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.admins": "<PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.allUsers": "Użytkownicy", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Grupy", "app.containers.Admin.Users.GroupsPanel.managers": "Kierownicy projektów", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Przypisane elementy", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Utwórz grupę i dodaj do nich użytkowników.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Zaproszenie w toku", "app.containers.Admin.Users.admin": "Administrator", "app.containers.Admin.Users.assign": "Prz<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.assignedItems": "<PERSON><PERSON><PERSON><PERSON><PERSON> pozycje dla {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "<PERSON><PERSON> jedno dodatkowe miej<PERSON>ce", "app.containers.Admin.Users.changeUserRights": "Zmiana uprawnień użytkowników", "app.containers.Admin.Users.confirm": "Po<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.confirmAdminQuestion": "<PERSON>zy na pewno chcesz nadać {name} prawa administratora platformy?", "app.containers.Admin.Users.confirmNormalUserQuestion": "<PERSON>zy na pewno chcesz ustawić {name} jako zwykłego użytkownika?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "<PERSON>zy na pewno chcesz ustawić {name} jako zwykłego użytkownika? <PERSON><PERSON><PERSON> pami<PERSON>, że stracą oni prawa kierownika do wszystkich projektów i folderów, do których są przypisani w momencie potwierdzenia.", "app.containers.Admin.Users.deleteUser": "Usuń tego użytkownika", "app.containers.Admin.Users.email": "E-mail", "app.containers.Admin.Users.folder": "Folder", "app.containers.Admin.Users.folderManager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.helmetDescription": "Lista użytkowników w panelu administracyjnym", "app.containers.Admin.Users.helmetTitle": "Admin - panel administracyjny", "app.containers.Admin.Users.inviteUsers": "Zaproś użytkowników", "app.containers.Admin.Users.joined": "Dołączyłeś", "app.containers.Admin.Users.lastActive": "Ostatni aktywny", "app.containers.Admin.Users.name": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.noAssignedItems": "Brak przypisanych elementów", "app.containers.Admin.Users.options": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.permissionToBuy": "<PERSON><PERSON> {name} prawa administratora, należy wykupić 1 dodatkowe miejsce.", "app.containers.Admin.Users.platformAdmin": "Administrator platformy", "app.containers.Admin.Users.projectManager": "Kierownik projektu", "app.containers.Admin.Users.reachedLimitMessage": "Osiągnąłeś limit miejsc w ramach swojego planu, 1 dodatkowe miejsce za {name} zostanie dodane.", "app.containers.Admin.Users.registeredUser": "Zarejestrowany użytkownik", "app.containers.Admin.Users.remove": "Usuń", "app.containers.Admin.Users.removeModeratorFrom": "Użytkownik moderuje folder, do którego należy ten projekt. Zamiast tego usuń przypisanie z \"{folderTitle}\".", "app.containers.Admin.Users.role": "Rola", "app.containers.Admin.Users.seeProfile": "Zobacz profil tego użytkownika", "app.containers.Admin.Users.selectPublications": "<PERSON><PERSON>bierz projekty lub foldery", "app.containers.Admin.Users.selectPublicationsPlaceholder": "<PERSON><PERSON><PERSON>, aby wys<PERSON>", "app.containers.Admin.Users.setAsAdmin": "Ustawienie jako administrator", "app.containers.Admin.Users.setAsNormalUser": "Ustawienie jako zwykły użytkownik", "app.containers.Admin.Users.setAsProjectModerator": "Ustaw jako kierownik projektu", "app.containers.Admin.Users.setUserAsProjectModerator": "<PERSON><PERSON><PERSON><PERSON><PERSON> {name} jako kierownika projektu", "app.containers.Admin.Users.userBlockModal.allDone": "Wszystko zrobione", "app.containers.Admin.Users.userBlockModal.blockAction": "Zablokuj użytkownika", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Zawartość tego użytkownika nie zostanie usunięta przez tę akcję. Nie zapomnij o moderowaniu ich treści w razie potrzeby.", "app.containers.Admin.Users.userBlockModal.blocked": "Zablokowany", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Ten użytkownik jest zablokowany od {from}. Ban trwa do {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Czy na pewno chcesz odblokować {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} jest zab<PERSON><PERSON><PERSON><PERSON> do czasu {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 dzie<PERSON>} other {{numberOfDays} dni}}", "app.containers.Admin.Users.userBlockModal.header": "Zablokuj użytkownika", "app.containers.Admin.Users.userBlockModal.reasonLabel": "U<PERSON>adnienie", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Zostanie to zakomunikowane zablokowanemu użytkownikowi.", "app.containers.Admin.Users.userBlockModal.subtitle1": "Wybrany użytkownik nie będzie mógł się zalogować na platformę {daysBlocked}. <PERSON><PERSON><PERSON> to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mo<PERSON><PERSON><PERSON> odblokować go z listy zablokowanych użytkowników.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Odblokuj", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "Tak, ch<PERSON>ę odblokować tego użytkownika", "app.containers.Admin.Users.userDeletionConfirmation": "Usunąć tego użytkownika na stałe?", "app.containers.Admin.Users.userDeletionFailed": "Podczas usuwania tego użytkownika wystąpił błąd, proszę spróbuj ponownie.", "app.containers.Admin.Users.userDeletionProposalVotes": "Spowoduje to również usunięcie wszystkich głosów oddanych przez tego użytkownika na propozycje, które są nadal otwarte do głosowania.", "app.containers.Admin.Users.userExportFileName": "uzytkownicy_eksport", "app.containers.Admin.Users.userInsights": "Spostrzeżenia użytkowników", "app.containers.Admin.Users.youCantDeleteYourself": "<PERSON><PERSON> m<PERSON><PERSON><PERSON><PERSON> własnego konta z panelu administratora", "app.containers.Admin.Users.youCantUnadminYourself": "<PERSON>e moż<PERSON><PERSON> ze swojej roli administratora", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Monitor społeczności", "app.containers.Admin.communityMonitor.healthScore": "Wynik zdrowotny", "app.containers.Admin.communityMonitor.healthScoreDescription": "Wynik ten jest średnią wszystkich pytań w skali nastrojów, na które uczestnicy odpowiedzieli w wybranym okresie.", "app.containers.Admin.communityMonitor.lastQuarter": "ostatni kwartał", "app.containers.Admin.communityMonitor.liveMonitor": "Monitor na żywo", "app.containers.Admin.communityMonitor.noResults": "Brak wyników za ten okres.", "app.containers.Admin.communityMonitor.noSurveyResponses": "Brak odpowiedzi w ankiecie", "app.containers.Admin.communityMonitor.participants": "Uczestnicy", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings": "Ustawienia", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Ankieta Monitora Społeczności przyjmuje zgłoszenia.", "app.containers.Admin.communityMonitor.settings.accessRights2": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "Po zarejestrowaniu przez użytkownika udziału w wydarzeniu, oddaniu głosu lub powrocie na stronę projektu po przesłaniu ankiety.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Menedżerowie monitorów społeczności mają dostęp do wszystkich ustawień i danych monitorów społeczności oraz mogą nimi zarz<PERSON>d<PERSON>.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Menedżerowie monitorów społeczności", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Menedżerowie mogą edytować ankietę i uprawnienia Community Monitor, przeglądać dane odpowiedzi i tworzyć raporty.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "Domyś<PERSON><PERSON> wartością częstotliwości jest 100%.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wyska<PERSON><PERSON><PERSON><PERSON><PERSON> okienek (od 0 do 100)", "app.containers.Admin.communityMonitor.settings.management2": "Zarządzanie", "app.containers.Admin.communityMonitor.settings.popup": "Wyskakują<PERSON> okienko", "app.containers.Admin.communityMonitor.settings.popupDescription3": "Wyskakujące okienko jest okresowo wyświetlane użytkownikom, zachęcając ich do wypełnienia ankiety Monitora Społeczności. <PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON><PERSON><PERSON><PERSON>, która określa procent <PERSON>, którzy losowo zobaczą wyska<PERSON>j<PERSON>ce okienko, gdy spełnione zostaną warunki opisane poniżej.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Ustawienia wyskakujących okienek", "app.containers.Admin.communityMonitor.settings.preview": "Podgląd", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "Użytkownik nie wypełnił jeszcze ankiety w ciągu ostatnich 3 miesięcy.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "Użytkownik nie widział wyskakującego okienka w ciągu ostatnich 3 miesięcy.", "app.containers.Admin.communityMonitor.settings.save": "Oszczędzaj", "app.containers.Admin.communityMonitor.settings.saved": "Zapisane", "app.containers.Admin.communityMonitor.settings.settings": "Ustawienia", "app.containers.Admin.communityMonitor.settings.survey2": "Ankieta", "app.containers.Admin.communityMonitor.settings.surveySettings3": "Ustawienia ogólne", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Po załadowaniu strony głównej lub strony niestandardowej.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonimizuj wszystkie dane użytkownika", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "Wszystkie dane wprowadzane do ankiety przez użytkowników zostaną zanonimizowane przed ich zarejestrowaniem", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Użytkownicy nadal będą musieli spełniać wymagania dotyczące uczestnictwa w ramach \"Praw dostępu\". Dane profilu użytkownika nie będą dostępne w eksporcie danych ankiety.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "W jakich warunkach wyskakujące okienko może być wyświetlane użytkownikom?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Kim są mened<PERSON>e?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Całkowita liczba odpowiedzi w ankiecie", "app.containers.Admin.communityMonitor.upsell.aiSummary": "Podsumowanie AI", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Włącz monitor społeczności", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Ta funkcja nie jest uwzględniona w Twoim bieżącym planie. Porozmawiaj ze swoim Government Success Managerem lub administratorem, aby ją od<PERSON><PERSON><PERSON>.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Wynik zdrowotny", "app.containers.Admin.communityMonitor.upsell.learnMore": "Dowiedz się więcej", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Wynik w czasie", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "Monitor Społeczności pomoże Ci pozostać na czele dzięki ciągłemu śledzeniu zaufania mieszkańców, zadowolenia z usług i życia społeczności.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Uzyskaj przejrzyste wyniki, mocne cytaty i kwartalny raport, który moż<PERSON>z udostępnić współpracownikom lub wybranym urzędnikom.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Łatwe do odczytania wyniki, które ewoluują w czasie", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Kluczowe cytaty mieszkańców, podsumowane przez AI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Pytania dostosowane do kontekstu Twojego miasta", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Mieszkańcy rekrutowani losowo na platformie", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Kwartalne raporty PDF, gotowe do udostępnienia", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "<PERSON><PERSON><PERSON><PERSON>, jak czuje się <PERSON> s<PERSON>, zanim pojawią się <PERSON>y", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Licz", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Komputer stacjonarny lub inny", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Telefon komórkowy", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tablet", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Typy urządzeń", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Typ urządzenia", "app.containers.Admin.earlyAccessLabel": "Wczesny dostęp", "app.containers.Admin.earlyAccessLabelExplanation": "Jest to nowa funkcja dostępna we wczesnym dostępie.", "app.containers.Admin.emails.addCampaign": "Utwórz wiadomość e-mail", "app.containers.Admin.emails.addCampaignTitle": "Napisz nową wiadomość e-mail", "app.containers.Admin.emails.allParticipantsInProject": "Wszyscy uczestnicy projektu", "app.containers.Admin.emails.allUsers": "Wszyscy użytkownicy", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "E-maile automatyczne są wysyłane automatycznie i są wyzwalane przez działania użytkownika. Niektóre z nich możesz wyłączyć dla wszystkich użytkowników swojej platformy. Innych automatycznych wiadomości nie można wyłączyć, ponieważ są one niezbędne do prawidłowego funkcjonowania twojej platformy.", "app.containers.Admin.emails.automatedEmails": "Automatyczne wiadomości e-mail", "app.containers.Admin.emails.automatedEmailsDigest": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć e-mail zostanie wysłana tylko w<PERSON>y, g<PERSON> z<PERSON><PERSON> tre<PERSON>ć", "app.containers.Admin.emails.automatedEmailsRecipients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>y, którzy otrzymają tę wiadomość e-mail", "app.containers.Admin.emails.automatedEmailsTriggers": "Zdarzenie uruchamiające tę wiadomość e-mail", "app.containers.Admin.emails.changeRecipientsButton": "Zmiana odbiorców", "app.containers.Admin.emails.clickOnButtonForExamples": "Kliknij poniższy przycisk, aby sprawd<PERSON>ć przykłady tego e-maila na naszej stronie wsparcia.", "app.containers.Admin.emails.confirmSendHeader": "Email do wszystkich użytkowników?", "app.containers.Admin.emails.deleteButtonLabel": "Usuń", "app.containers.Admin.emails.draft": "Szkic", "app.containers.Admin.emails.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editCampaignTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editDisabledTooltip2": "<PERSON><PERSON> wkrótce: Ten e-mail nie może być obecnie edytowany.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Tekst przycisku", "app.containers.Admin.emails.editRegion_intro_multiloc": "Wprowadzenie", "app.containers.Admin.emails.editRegion_subject_multiloc": "Przedmiot", "app.containers.Admin.emails.editRegion_title_multiloc": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.emailCreated": "Wiadomość e-mail została pomyślnie utworzona w wersji roboczej", "app.containers.Admin.emails.emailUpdated": "Email został pomyślnie zaktualizowany", "app.containers.Admin.emails.emptyCampaignsDescription": "Łatwo nawiązuj kontakt z uczestnikami, wysyłając im wiadomości e-mail. <PERSON><PERSON><PERSON><PERSON>, z kim chcesz się skontaktować i śledź swoje zaangażowanie.", "app.containers.Admin.emails.emptyCampaignsHeader": "Wyślij swoją pierwszą wiadomość e-mail", "app.containers.Admin.emails.failed": "<PERSON><PERSON> udało się", "app.containers.Admin.emails.fieldBody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldBodyError": "Podaj w<PERSON> e-mail dla wszystkich języków", "app.containers.Admin.emails.fieldGroupContent": "<PERSON><PERSON><PERSON><PERSON> wiadomości e-mail", "app.containers.Admin.emails.fieldReplyTo": "<PERSON><PERSON><PERSON>wi<PERSON><PERSON> powinny trafić do", "app.containers.Admin.emails.fieldReplyToEmailError": "Podaj adres e-mail w prawidłowym formacie, np. <EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Podaj adres e-mail", "app.containers.Admin.emails.fieldReplyToTooltip": "Możesz wybrać miej<PERSON>ce, do którego będą wysyłane odpowiedzi na Twój e-mail.", "app.containers.Admin.emails.fieldSender": "Od", "app.containers.Admin.emails.fieldSenderError": "Podaj nadawcę wiadomości e-mail", "app.containers.Admin.emails.fieldSenderTooltip": "<PERSON><PERSON><PERSON><PERSON>, kto będzie nadawcą wysyłanej wiadomości.", "app.containers.Admin.emails.fieldSubject": "<PERSON><PERSON>", "app.containers.Admin.emails.fieldSubjectError": "Podaj temat wiadomości dla wszystkich języków", "app.containers.Admin.emails.fieldSubjectTooltip": "To zostanie pokazane w temacie wiadomości e-mail oraz w przeglądzie skrzynki odbiorczej użytkownika. Zadbaj o jasność. Zaangażuj odbiorcę treścią zwięzłego tytułu.", "app.containers.Admin.emails.fieldTo": "Do", "app.containers.Admin.emails.fieldToTooltip": "Możesz wybrać grupy użytkowników, które otrzymają Twojego maila.", "app.containers.Admin.emails.formSave": "Zapisz jako s<PERSON>c", "app.containers.Admin.emails.formSaveAsDraft": "Zap<PERSON>z jako wersję roboczą", "app.containers.Admin.emails.from": "Od:", "app.containers.Admin.emails.groups": "Grupy", "app.containers.Admin.emails.helmetDescription": "Ręczne wysyłanie e-maili do określonych grup obywateli oraz aktywne kampanie automatyczne", "app.containers.Admin.emails.nameVariablesInfo2": "Możesz zwracać się bezpośrednio do obywateli za pomocą zmiennych {firstName} {lastName}. Np. \"Drogi {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "Na Twój adres e-mail został wysłany e-mail podglądowy", "app.containers.Admin.emails.previewTitle": "Podgląd", "app.containers.Admin.emails.regionMultilocError": "<PERSON><PERSON><PERSON> dla wszystkich języków", "app.containers.Admin.emails.seeEmailHereText": "<PERSON>dy tylko wiadomość tego typu zostanie wysłana, będziesz mógł ją sprawdzić tutaj.", "app.containers.Admin.emails.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sendNowButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> teraz", "app.containers.Admin.emails.sendTestEmailButton": "<PERSON><PERSON><PERSON><PERSON>j mi <PERSON>a testowego", "app.containers.Admin.emails.sendTestEmailTooltip2": "Po kliknięciu tego łącza testowa wiadomość e-mail zostanie wysłana wyłącznie na Twój adres e-mail. Dzięki temu moż<PERSON><PERSON>, jak wygląda wiadomość e-mail w \"prawdziwym życiu\".", "app.containers.Admin.emails.senderRecipients": "Nadawca i odbiorcy", "app.containers.Admin.emails.sending": "Wysyłanie", "app.containers.Admin.emails.sent": "Wysłano", "app.containers.Admin.emails.sentToUsers": "Są to wiadomości e-mail wysyłane do użytkowników", "app.containers.Admin.emails.subject": "Temat:", "app.containers.Admin.emails.supportButtonLabel": "Zobacz przykłady na naszej stronie wsparcia", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "Do:", "app.containers.Admin.emails.toAllUsers": "<PERSON><PERSON> ch<PERSON>z wysłać tę wiadomość do wszystkich użytkowników?", "app.containers.Admin.emails.viewExample": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.ideas.import": "Import", "app.containers.Admin.inspirationHub.AllProjects": "Wszystkie projekty", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Monitor społeczności", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Adnotacja do dokumentu", "app.containers.Admin.inspirationHub.ExternalSurvey": "Ankieta zewnętrzna", "app.containers.Admin.inspirationHub.Filters.Country": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Method": "Metoda", "app.containers.Admin.inspirationHub.Filters.Search": "Szukaj", "app.containers.Admin.inspirationHub.Filters.Topic": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.population": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Highlighted": "Wyróżnione", "app.containers.Admin.inspirationHub.Ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Information": "Informacje", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "<PERSON><PERSON><PERSON><PERSON> kraj, aby zobaczyć przypięte projekty", "app.containers.Admin.inspirationHub.PinnedProjects.country": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "Nie znaleziono przypiętych projektów dla tego kraju. Zmień kraj, aby zob<PERSON><PERSON>ć przypięte projekty dla innych krajów", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Zmień kraj, aby zob<PERSON><PERSON> więcej przypiętych projektów", "app.containers.Admin.inspirationHub.Poll": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "Czytaj więcej...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Faza {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "<PERSON><PERSON><PERSON> nie <PERSON>, aby Twój projekt znalazł się w centrum inspiracji, skontaktuj się ze swoim menedżerem GovSuccess.", "app.containers.Admin.inspirationHub.Proposals": "Pro<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Uczestnicy (najpierw najniższa liczba)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Uczestnicy (najpierw najwyższa pozycja)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Data rozpoczęcia (najpierw najstarsza)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Data rozpoczęcia (najpierw najnowsza)", "app.containers.Admin.inspirationHub.Survey": "Ankieta", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Wyselekcjonowana lista najlepszych projektów z całego świata.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Rozmawiaj z innymi praktykami i ucz się od nich.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filtruj według metody, wielkości miasta i kraju.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Włącz centrum inspiracji", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Ta funkcja nie jest uwzględniona w Twoim bieżącym planie. Porozmawiaj ze swoim Government Success Managerem lub administratorem, aby ją od<PERSON><PERSON><PERSON>.", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "https://support.govocal.com/en/articles/11093736-using-the-inspiration-hub", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "Dowiedz się więcej", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "Inspiration Hub łączy Cię z wyselekcjonowanym kanałem wyjątkowych projektów uczestnictwa na platformach Go Vocal na całym świecie. <PERSON><PERSON><PERSON> si<PERSON>, jak inne miasta prowadzą udane projekty i porozmawiaj z innymi praktykami.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Dołącz do sieci pionierskich praktyków demokracji", "app.containers.Admin.inspirationHub.Volunteering": "Wolontariat", "app.containers.Admin.inspirationHub.Voting": "Głosowanie", "app.containers.Admin.inspirationHub.commonGround": "Wspólna płaszczyzna", "app.containers.Admin.inspirationHub.filters": "filtry", "app.containers.Admin.inspirationHub.resetFilters": "Zresetuj filtry", "app.containers.Admin.inspirationHub.seemsLike": "Wygląda na to, że nie ma więcej projektów. Spróbuj zmienić {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "<PERSON><PERSON><PERSON><PERSON> pola kampanii", "app.containers.Admin.messaging.automated.variablesToolTip": "W wiadomości możesz użyć następujących zmiennych:", "app.containers.Admin.messaging.helmetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "Ten widżet pokazuje każdemu użytkownikowi projekty <b>oparte na jego preferencjach obserwowania</b>. Obejmuje to projekty, które obserwują, a także projekty, w których śledzą dane wejściowe, oraz projekty związane z tematami lub obszarami, któ<PERSON>mi są zainteresowani.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "Ten widżet zostanie wyświetlony użytkownikowi tylko wtedy, gdy ist<PERSON>j<PERSON> projekty, w kt<PERSON><PERSON><PERSON> może on uczestniczyć. <PERSON><PERSON>li zobaczysz ten komunikat, oznac<PERSON> to, <PERSON><PERSON> <PERSON> (administrator) nie możesz w tej chwili uczestniczyć w żadnych projektach. Ta wiadomość nie będzie widoczna na prawdziwej stronie głównej.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Otwarty na uczestnictwo", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "Ten widżet będzie prezentował projekty, w których użytkownik może obecnie <b>pod<PERSON><PERSON><PERSON> d<PERSON>, aby wzi<PERSON><PERSON> ud<PERSON>ł</b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "Ten widżet zostanie wyświetlony użytkownikowi tylko wtedy, gdy istniej<PERSON> odpowiednie dla niego projekty w oparciu o jego preferencje śledzenia. <PERSON><PERSON><PERSON> zobaczysz ten komunikat, oznacza to, <PERSON><PERSON> <PERSON> (administrator) nie obserwujesz obecnie żadnego projektu. Ta wiadomość nie będzie widoczna na prawdziwej stronie głównej.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "Śledzone elementy", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Zarchiwizowane", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Zakończony", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Zakończone i zarchiwizowane", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "<PERSON><PERSON> <PERSON><PERSON><PERSON> danych", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "Ten widżet pokazuje <b>proje<PERSON><PERSON>, które zostały ukończone i/lub zarchiwizowane.</b>\"Zakończone\" obejmuje również projekty, które znajdują się w ostatniej fazie i w których ostatnią fazą jest raport.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Ukończone projekty", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "Powiedział<PERSON>ś, zrobiliśmy...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "Podaj nazwę dla wszystkich języków", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "Projekt nie może być pusty", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Nazwa", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Projekt", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Wynikowy adres URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "Oszczędzaj", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "<PERSON><PERSON><PERSON> projekt", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "Na pasku nawigacyjnym wyświetlane będą tylko te projekty, do których użytkownicy mają dostęp.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "Widżet ten będzie widoczny na stronie głównej tylko wtedy, gdy <PERSON> Społeczności akceptuje odpowiedzi.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Monitor społeczności", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Opis", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "Przycisk", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Ważne:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Przykład pytania ankiety dotyczącej nastrojów", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "<PERSON>rak daty końcowej", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "<PERSON><PERSON><PERSON><PERSON><PERSON> escape, aby p<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projekty i foldery (starsze wersje)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Ty<PERSON>ł projektu", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} pracuje obecnie nad", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Tekst przycisku", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "<PERSON>ź udział już teraz!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Opis", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "<PERSON><PERSON><PERSON>rz projekt lub folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "<PERSON><PERSON><PERSON>rz projekt lub folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Pokaż awatary", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Reflektor", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "<PERSON><PERSON><PERSON><PERSON><PERSON> za {days} dni", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Od {weeks} tygodni", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} dni temu", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} tygodnie temu", "app.containers.Admin.project.Campaigns.campaignFrom": "Od:", "app.containers.Admin.project.Campaigns.campaignTo": "Do:", "app.containers.Admin.project.Campaigns.customEmails": "Niestandardowe wiadomości e-mail", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Wysyłaj niestandardowe wiadomości e-mail i sprawdzaj statystyki.", "app.containers.Admin.project.Campaigns.noAccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ale wygląda na to, że nie masz dostępu do sekcji e-maili", "app.containers.Admin.project.emails.addCampaign": "Utwórz wiadomość e-mail", "app.containers.Admin.project.emails.addCampaignTitle": "Nowa kampania", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Wszystkie {participants} i obserwujący z projektu", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Obejmuje to zarejestrowanych użytkowników, którzy wykonali jakąkolwiek akcję w projekcie. Niezarejestrowani lub anonimowi użytkownicy nie są uwzględniani.", "app.containers.Admin.project.emails.dateSent": "Data wysłania", "app.containers.Admin.project.emails.deleteButtonLabel": "Usuń", "app.containers.Admin.project.emails.draft": "<PERSON><PERSON><PERSON> rob<PERSON>", "app.containers.Admin.project.emails.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.editCampaignTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Łatwo nawiązuj kontakt z uczestnikami, wysyłając im wiadomości e-mail. <PERSON><PERSON><PERSON><PERSON>, z kim chcesz się skontaktować i śledź swoje zaangażowanie.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Wyślij swoją pierwszą wiadomość e-mail", "app.containers.Admin.project.emails.failed": "<PERSON>e powiodło się", "app.containers.Admin.project.emails.fieldBody": "Wiadomość e-mail", "app.containers.Admin.project.emails.fieldBodyError": "Zapewnij wiadomość e-mail dla wszystkich języków", "app.containers.Admin.project.emails.fieldReplyTo": "Odpowiedzi należy przesyłać na adres", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Podaj adres e-mail w poprawnym formacie, na przykład <EMAIL>.", "app.containers.Admin.project.emails.fieldReplyToError": "Podaj adres e-mail", "app.containers.Admin.project.emails.fieldReplyToTooltip": "<PERSON><PERSON><PERSON><PERSON> adres e-mail, na który użytkownicy mają otrzymywać bezpośrednie odpowiedzi.", "app.containers.Admin.project.emails.fieldSender": "Od", "app.containers.Admin.project.emails.fieldSenderError": "Podaj nadawcę wiadomości e-mail", "app.containers.Admin.project.emails.fieldSenderTooltip": "<PERSON><PERSON><PERSON><PERSON>, kogo użytkownicy będą widzieć jako nadawcę wiadomości e-mail.", "app.containers.Admin.project.emails.fieldSubject": "<PERSON><PERSON> e-mail", "app.containers.Admin.project.emails.fieldSubjectError": "Podaj temat wiadomości e-mail dla wszystkich języków", "app.containers.Admin.project.emails.fieldSubjectTooltip": "Będzie to widoczne w temacie wiadomości e-mail i w przeglądzie skrzynki odbiorczej użytkownika. Uczyń go jasnym i angażującym.", "app.containers.Admin.project.emails.fieldTo": "Do", "app.containers.Admin.project.emails.formSave": "Zap<PERSON>z jako wersję roboczą", "app.containers.Admin.project.emails.from": "Od:", "app.containers.Admin.project.emails.helmetDescription": "Wysyłaj ręcznie wiadomości e-mail do uczestników projektu", "app.containers.Admin.project.emails.infoboxAdminText": "Z karty Wiadomości projektu moż<PERSON>z wysyłać wiadomości e-mail tylko do wszystkich uczestników projektu.  Aby wysłać wiadomość e-mail do innych uczestników lub podzbiorów użytkowników, przejdź do karty {link} .", "app.containers.Admin.project.emails.infoboxLinkText": "Platforma komunikacyjna", "app.containers.Admin.project.emails.infoboxModeratorText": "Z karty Wiadomości projektu możesz wysyłać wiadomości e-mail tylko do wszystkich uczestników projektu. <PERSON><PERSON> mogą wysyłać wiadomości e-mail do innych uczestników lub podzbiorów użytkowników za pośrednictwem karty Wiadomości platformy.", "app.containers.Admin.project.emails.message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.nameVariablesInfo2": "Możesz zwracać się bezpośrednio do obywateli za pomocą zmiennych {firstName} {lastName}. Np. \"Drogi {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "uczestnicy", "app.containers.Admin.project.emails.previewSentConfirmation": "Wiadomość z podglądem została wysłana na Twój adres e-mail", "app.containers.Admin.project.emails.previewTitle": "Podgląd", "app.containers.Admin.project.emails.projectParticipants": "Uczestnicy projektu", "app.containers.Admin.project.emails.recipients": "Od<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sendTestEmailButton": "<PERSON><PERSON>ś<PERSON>j podgląd", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Wyś<PERSON>j tę wersję roboczą wiadomości e-mail na adres e-mail, za pomocą którego jeste<PERSON>, a<PERSON>, jak wygląda ona w \"prawdziwym życiu\".", "app.containers.Admin.project.emails.senderRecipients": "Nadawca i odbiorcy", "app.containers.Admin.project.emails.sending": "Wysyłanie", "app.containers.Admin.project.emails.sent": "Wysłany", "app.containers.Admin.project.emails.sentToUsers": "Są to wiadomości e-mail wysyłane do użytkowników", "app.containers.Admin.project.emails.status": "Status", "app.containers.Admin.project.emails.subject": "Temat:", "app.containers.Admin.project.emails.to": "Do:", "app.containers.Admin.project.messaging.helmetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "Ten obraz jest częścią karty folderu; karty, kt<PERSON>ra podsumowuje folder i jest wyświetlana na przykład na stronie głównej. Więcej informacji na temat zalecanych rozdzielczości obrazu można znaleźć na stronie {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Ten obraz jest wyświetlany na górze strony folderu. Więcej informacji na temat zalecanych rozdzielczości obrazu można znaleźć pod adresem {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "odwiedź nasze centrum wsparcia", "app.containers.Admin.projects.all.askPersonalData3": "Dodaj pola na imię i adres e-mail", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "Włącz widok kalendarza", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "Ta funkcja nie jest uwzględniona w Twoim bieżącym planie. Porozmawiaj ze swoim Government Success Managerem lub administratorem, aby ją od<PERSON><PERSON><PERSON>.", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "Dowiedz się więcej", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "Uzyskaj wizualny przegląd harmonogramów swoich projektów w naszym widoku kalendarza. Szybko zidentyfikuj projekty i fazy, które wkrótce się rozpoczną lub zakończą i wymagają działania.", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "<PERSON><PERSON><PERSON><PERSON>, co się dzieje i kiedy", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Wszystkie pytania są wyświetlane w pliku PDF. Jednak następujące elementy nie są obecnie obsługiwane w przypadku importu za pośrednictwem FormSync: <PERSON><PERSON><PERSON>, tagi i przesyłanie plików.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Wszystkie pytania są wyświetlane w pliku PDF. Jednak następujące elementy nie są obecnie obsługiwane w przypadku importu za pośrednictwem FormSync: Pytania dotyczące mapowania (up<PERSON><PERSON><PERSON> pinezkę, narysuj trasę i narysuj obszar), pytania rankingowe, pytania dotyczące macierzy i pytania dotyczące przesyłania plików.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "Koniec formularza", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Początek formularza", "app.containers.Admin.projects.all.components.archived": "Zarchiwizowany", "app.containers.Admin.projects.all.components.draft": "Szkic", "app.containers.Admin.projects.all.components.manageButtonLabel": "Zarządzaj", "app.containers.Admin.projects.all.copyProjectButton": "Kopia projektu", "app.containers.Admin.projects.all.copyProjectError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd przy kopiowaniu tego projektu, pro<PERSON><PERSON> spróbować ponownie później.", "app.containers.Admin.projects.all.customiseEnd": "Dostosuj zakończenie formularza.", "app.containers.Admin.projects.all.customiseStart": "Dostosuj początek formularza.", "app.containers.Admin.projects.all.deleteFolderButton1": "Usuń folder", "app.containers.Admin.projects.all.deleteFolderConfirm": "Czy na pewno chcesz usunąć ten folder? Wszystkie projekty znajdujące się w tym folderze zostaną również usunięte. Ta akcja nie może zostać cofnięta.", "app.containers.Admin.projects.all.deleteFolderError": "Wystąpił problem z usunięciem tego folderu. Proszę spróbuj jeszcze raz.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.containers.Admin.projects.all.deleteProjectConfirmation": "<PERSON>zy na pewno chcesz usunąć ten projekt? Tej operacji nie da się cofnąć.", "app.containers.Admin.projects.all.deleteProjectError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas usuwania tego projektu, pro<PERSON><PERSON> spróbować ponownie później.", "app.containers.Admin.projects.all.exportAsPDF1": "Pobierz formularz PDF", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Możesz łączyć odpowiedzi online i offline. Aby przesłać odpowiedzi offline, przejdź <PERSON> zakładki \"Menedżer danych wejściowych\" tego projektu i kliknij \"Importuj\".", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Możesz łączyć odpowiedzi online i offline. Aby przesłać odpowiedzi offline, przejdź do zakładki \"Ankieta\" tego projektu i kliknij \"Importuj\".", "app.containers.Admin.projects.all.logicNotInPDF": "Logika ankiety nie zostanie odzwierciedlona w pobranym pliku PDF. Respondenci papierowi zobaczą wszystkie pytania ankiety.", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "<PERSON>ys<PERSON><PERSON> foldery", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "Wszystkie foldery zostały załadowane", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Folder", "app.containers.Admin.projects.all.new.Folders.Table.managers": "Men<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# projekt} other {# projekty}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Data rozpoczęcia projektu", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "<PERSON>k<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "Publiczny", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Foldery", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filtruj według metody udziału w bieżącej fazie", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Informacje", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Metoda uczestnictwa", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Adnotacja do dokumentu", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Wspólna płaszczyzna", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "Ankieta", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Pro<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Wolontariat", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Głosowanie", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "Gromad<PERSON><PERSON> da<PERSON>ch", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informowanie", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "W oczekiwaniu na zatwierdzenie", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "Wyszukaj projekty", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Alfabetycznie (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Alfabetycznie (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "Kierownik", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Faza rozpoczynająca się lub kończąca wkrótce", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc": "Ostatnio utworzony (nowy-stary)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc": "Ostatnio utworzone (stare-nowe)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "Ostatnio <PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Grupy", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "Publiczny", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "<PERSON><PERSON><PERSON> filtr", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "Nie musisz już dodawać żadnych filtrów", "app.containers.Admin.projects.all.new.Projects.Table.admins": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Wszystkie projekty zostały załadowane", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "Ktokolwiek", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Zarchiwizowane", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "Faza bieżąca", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}lewy", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d, aby r<PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "<PERSON><PERSON><PERSON> rob<PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.end": "Koniec", "app.containers.Admin.projects.all.new.Projects.Table.ended": "Zakończony", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "Kończy się dzisiaj", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Grupy", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "<PERSON>k<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "<PERSON><PERSON><PERSON><PERSON><PERSON> więcej…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "Kierownik", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}nie lewo", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}mo to start", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Następna faza:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Faza", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "Przed uruchomieniem", "app.containers.Admin.projects.all.new.Projects.Table.project": "Projekt", "app.containers.Admin.projects.all.new.Projects.Table.public": "Publiczny", "app.containers.Admin.projects.all.new.Projects.Table.published": "Opublikowano", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Przewiń w dół, aby załadować więcej", "app.containers.Admin.projects.all.new.Projects.Table.start": "Rozpocznij", "app.containers.Admin.projects.all.new.Projects.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Status:", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "Ta kolumna wykorzystuje buforowane dane uczestników. <PERSON><PERSON> z<PERSON><PERSON><PERSON> najn<PERSON> dane, sprawd<PERSON> zakład<PERSON>ę \"Uczestnicy\" w projekcie.", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "W<PERSON>cz<PERSON>ść:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} grupy", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} men<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}y lewy", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y, aby r<PERSON>", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Aktualna faza: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} dni do końca", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Folder: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "Brak bieżącej fazy", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "<PERSON>rak daty końcowej", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "Brak faz", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Faza {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Fazy:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Projekt", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Data rozpoczęcia: {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "Organizuj projekty", "app.containers.Admin.projects.all.new.calendar": "Kalendarz", "app.containers.Admin.projects.all.new.folders": "Foldery", "app.containers.Admin.projects.all.new.projects": "Projekty", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "<PERSON>e udało się załadować osi czasu.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Projekt nie ma daty zakończenia", "app.containers.Admin.projects.all.new.timeline.project": "Projekt", "app.containers.Admin.projects.all.notes": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.personalDataExplanation5": "Ta opcja doda pola imienia, nazwiska i adresu e-mail do wyeksportowanego pliku PDF. Po przesłaniu papierowego formularza użyjemy tych danych do automatycznego wygenerowania konta dla respondenta ankiety offline.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "Podsumowanie AI", "app.containers.Admin.projects.project.analysis.Comments.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "Podsumowanie komentarzy jest dos<PERSON><PERSON><PERSON><PERSON>, gdy jest ich 5 lub wię<PERSON>j.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Podsumuj komentarze", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} =1 {1 nowy komentarz} other {# nowe komentarze}}", "app.containers.Admin.projects.project.analysis.aiSummary": "Podsumowanie AI", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "To jest zawart<PERSON><PERSON><PERSON> generowana przez sztuczną inteligencję. Może ona nie być w 100% dokładna. Sprawdź i porównaj z rzeczywistymi danymi wejściowymi pod kątem dokładności. <PERSON><PERSON><PERSON><PERSON><PERSON>, że dokładno<PERSON>ć prawdopodobnie poprawi się, jeśli liczba wybranych danych wejściowych zostanie zmniejszona.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Powiadomienia e-mail wysyłane tylko do uczestników", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "<PERSON>k<PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "<PERSON><PERSON> indek<PERSON>wane przez wyszukiwarki", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Niewidoczne na stronie głównej lub w widżetach", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Dostęp tylko przez bezpośredni adres URL", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "Publiczny", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "<PERSON><PERSON><PERSON><PERSON>, w jakim stopniu ten projekt jest wykrywalny.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "Projekt ten jest widoczny dla każdego, kto ma do niego dos<PERSON>, i pojawi się na stronie głównej oraz w widżetach.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Ten projekt będzie ukryty przed szerszą publicznością i będzie widoczny tylko dla tych, którzy mają link.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Kto może znaleźć ten projekt?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Otwarta analiza sztucznej inteligencji", "app.containers.Admin.projects.project.ideas.analysisText2": "Przeglądaj podsumowania oparte na sztucznej inteligencji i przeglądaj poszczególne zgłoszenia.", "app.containers.Admin.projects.project.ideas.importInputs": "Import", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "Po utworzeniu raportu możesz udostępnić go publicznie po rozpoczęciu fazy.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Utwórz bardziej złożoną stronę do udostępniania informacji", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Utwórz raport do:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "<PERSON><PERSON><PERSON><PERSON><PERSON> raport", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Utwórz raport dla poprzedniej fazy lub zacznij od zera.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "Ten raport nie jest publiczny. Aby go <PERSON><PERSON><PERSON><PERSON><PERSON>, włącz przełącznik \"Widoczny\".", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "Ta faza została roz<PERSON>, ale raport nie jest jeszcze dostępny publicznie. Aby go <PERSON><PERSON><PERSON><PERSON><PERSON>, włącz przełącznik \"Widoczny\".", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Zacznij od szablonu fazy", "app.containers.Admin.projects.project.information.ReportTab.report": "<PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Podziel się wynikami wcześniejszej ankiety lub fazy tworzenia pomysłów.", "app.containers.Admin.projects.project.information.ReportTab.visible": "Widoczny", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "Ten raport zostanie upubliczniony zaraz po rozpoczęciu fazy. Aby go nie upublicznia<PERSON>, wyłącz przełącznik \"Widoczny\".", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "Ten raport jest obecnie publiczny. Aby go wyłączyć, wyłącz przełącznik \"Widoczny\".", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "<PERSON>zy na pewno chcesz usunąć ten raport? Tej czynn<PERSON>ści nie można cofnąć.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Dodaj do fazy", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "Musisz wyrazić na to zgodę, zanim będziesz mógł kontynuować", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "Formularz można pobrać tutaj.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "Przesłany formularz został utworzony z sekcją \"Dane osobowe\"", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Język formularza", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "Niniejszym wyrażam zgodę na przetwarzanie tego pliku przy użyciu Google Cloud Form Parser", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Importuj plik Excel", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aby kontynuować", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "Szablon można pobrać tutaj.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Prześ<PERSON>j", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Prz<PERSON><PERSON><PERSON><PERSON> w<PERSON> <b>plik Excel</b> (.xlsx). Musisz użyć szablonu dostarczonego dla tego projektu. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Prześlij <b>plik PDF zeskanowanych formularzy</b>. Musisz użyć formularza wydrukowanego z tej fazy. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Użyj tej wiadomości e-mail dla nowego użytkownika", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Wprowadź prawidłowy adres e-mail, aby utworz<PERSON>ć nowe konto", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Zostanie utworzone nowe konto dla autora z tymi danymi. Ten wpis zostanie do niego dodany.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Nazwisko", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Wprowadź adres e-mail i/lub imię i nazwisko, aby przypisać te dane do autora. Możesz też odznaczyć pole zgody.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Istnieje już konto powiązane z tym adresem e-mail. Ten wpis zostanie do niego dodany.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Zgoda użytkownika (utwórz konto użytkownika)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "Zatwierdź", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Zatwierdź wszystkie dane wejściowe", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Autor:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "Email:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Podczas importu wystąpiły błędy i niektóre dane wejściowe nie zostały zaimportowane. Popraw błędy i ponownie zaimportuj brakujące dane wejściowe.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Nieprawidłowe dane formularza. Sprawdź powyższy formularz pod kątem błędów.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Importuj plik Excel (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Import", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Importuj zeskanowane formularze (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Importuj zeskanowane formularze", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "Importowane we<PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importowanie. Proces ten może potrwać kilka minut.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "Dane te zostały zaimportowane anonimowo.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} zostały zaimportowane i wymagają zatwierdzenia.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} dane wejściowe nie mogły zostać zatwierdzone. Sprawdź każde dane wejściowe pod kątem problemów z walidacją i potwierdź je indywidualnie.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Lokalizacja:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Nie ma jeszcze nic do sprawdzenia. Kliknij \"{importFile}\", aby z<PERSON><PERSON><PERSON>ować plik PDF zawierający zeskanowane formularze wejściowe lub plik Excel zawierający dane wejściowe.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Nie ma jeszcze nic do sprawdzenia. Kliknij \"{importFile}\", a<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> plik Excel zawierający dane wejściowe.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "<PERSON>jś<PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "Strona", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Nie można wyświetlić zaimportowanego pliku. Wyświetlanie zaimportowanego pliku jest dostępne tylko w przypadku importu plików PDF.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Faza:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "Wybrana faza nie może zawierać wejść. Wybierz inną fazę.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Ten projekt nie zawiera żadnych faz, które mogą zawierać pomysły.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "<PERSON><PERSON><PERSON><PERSON> fazę, do której chcesz dodać te wejścia.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Importer danych wejściowych", "app.containers.Admin.projects.project.participation.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.participantsTimeline": "Oś czasu uczestników", "app.containers.Admin.projects.project.participation.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.selectPeriod": "<PERSON><PERSON>bierz okres", "app.containers.Admin.projects.project.participation.usersByAge": "Użytkownicy według wieku", "app.containers.Admin.projects.project.participation.usersByGender": "Użytkownicy według płci", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "Możliwość dodawania lub edytowania pól użytkownika na poziomie fazy nie jest objęta Twoją obecną licencją. Skontaktuj się ze swoim GovSuccess Managerem, aby dowiedzieć się więcej na ten temat.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} opcje", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "Status pola", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "Pytania te zostaną dodane jako ostatnia strona formularza ankiety, poniew<PERSON>ż opcja \"Pokaż pola w ankiecie?\" została wybrana w ustawieniach fazy.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "<PERSON>e będą zadawane żadne dodatkowe pytania.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Opcjonalnie", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Opcjonalne - zawsze włączone, ponieważ odwołuje się do niego grupa", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "<PERSON><PERSON><PERSON> pole", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Wymagane - z<PERSON><PERSON> wł<PERSON>, ponieważ odwołuje się do niego grupa", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Uwierzytelnij się za pomocą {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Wypełnij dodatkowe pytania poniżej", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Potwierdź swój adres e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Dane zwrócone z metody weryfikacji:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Wprow<PERSON>ź imię, naz<PERSON><PERSON>, adres e-mail i hasło.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "Wprowadź swój adres e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "Jak niedawno użytkownicy powinni zostać zweryfikowani?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Weryfikacja tożsamości za pomocą {verificationMethod} (na podstawie grupy użytkowników)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "Uczestnictwo nie wymaga żadnych działań", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Użyj inteligentnych grup, aby ograniczyć uczestnictwo w oparciu o zweryfikowane dane wymienione powyżej.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Użytkownicy musieli zostać zweryfikowani w ciągu ostatnich 30 minut.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Użytkownicy muszą zostać zweryfikowani w ciągu ostatnich {days} dni.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "W ciągu ostatnich 30 dni", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "W ciągu ostatnich 30 minut", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "W ciągu ostatnich 7 dni", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "Raz wystarczy", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Zweryfikowane pola:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Tworz<PERSON>e konta", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Uczestnicy muszą utworzyć pełne konto z imieniem i nazwiskiem, potwierdzonym adresem e-mail i hasłem.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Uczestnicy muszą utworzyć pełne konto z nazwą, adresem e-mail i hasłem.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Uwierzytelnianie", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Potwierdzenie e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Uczestnicy muszą potwierdzić swój adres e-mail za pomocą jednorazowego kodu.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wykrywanie spamu", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "Ta funkcja pomaga zapobiegać duplikowaniu ankiet od wylogowanych użytkowników poprzez analizę adresów IP i danych urządzenia. <PERSON><PERSON>ż nie jest to tak pre<PERSON>zy<PERSON>e, jak wymaganie logowania, może pomóc zmniejszyć liczbę zduplikowanych odpowiedzi.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Uwaga: W sieciach współdzielonych (takich jak biura lub publiczne Wi-Fi) istnieje niewielka szansa, że różni użytkownicy mogą zostać oznaczeni jako duplikaty.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Włącz zaawansowane wykrywanie spamu", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Dodatkowe pytania zadawane uczestnikom", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "Brak", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "<PERSON><PERSON><PERSON> może wziąć udział bez rejestracji lub logowania.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Zresetuj dodatkowe pytania i grupy", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Ogranicz uczestnictwo do grup użytkowników", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "Weryfikacja SSO", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Uczestnicy muszą zweryfikować swoją tożsa<PERSON>ć na stronie {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Otwarta analiza sztucznej inteligencji", "app.containers.Admin.projects.project.survey.allFiles": "Wszystkie pliki", "app.containers.Admin.projects.project.survey.allResponses": "Wszystkie odpowiedzi", "app.containers.Admin.projects.project.survey.analysis.accuracy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Wystąpił błąd podczas generowania podsumowania AI. Spróbuj je ponownie wygenerować poniżej.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Otwarta analiza sztucznej inteligencji", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "<PERSON><PERSON><PERSON><PERSON>wania dla tego pytania", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "w<PERSON><PERSON><PERSON> we<PERSON>", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Otwarte działania analityczne", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} nowe o<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.analysis.regenerate": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Pokaż spostrzeżenia dotyczące sztucznej inteligencji", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "W ramach bieżącego planu możesz podsumować maksymalnie 30 danych wejściowych naraz. Porozmawiaj ze swoim GovSuccess Managerem lub administratorem, aby odblokować więcej.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Wybierz powiązane pytania do analizy", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "<PERSON><PERSON>z uwzgl<PERSON>dnić inne powiązane pytania w swojej analizie {question}?", "app.containers.Admin.projects.project.survey.cancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "Zgadzam się na wykorzystanie OpenAI jako podmiotu przetwarzającego dane w tym projekcie", "app.containers.Admin.projects.project.survey.consentModalText1": "Kontynuując wyrażasz zgodę na wykorzystanie OpenAI jako podmiotu przetwarzającego dane w ramach tego projektu.", "app.containers.Admin.projects.project.survey.consentModalText2": "Interfejsy API OpenAI zasilają zautomatyzowane podsumowania tekstowe i części zautomatyzowanego tagowania.", "app.containers.Admin.projects.project.survey.consentModalText3": "<PERSON><PERSON><PERSON><PERSON><PERSON> tylko to, co użytkownicy napisali w swoich ankietach, pomysłach i komentarzach do interfejsów API OpenAI, nigdy żadnych informacji z ich profilu.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI nie będzie wykorzystywać tych danych do dalszego szkolenia swoich modeli. Więcej informacji na temat tego, jak OpenAI traktuje p<PERSON>wat<PERSON> danych, można znaleźć na stronie {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "tutaj", "app.containers.Admin.projects.project.survey.consentModalTitle": "Zanim przejdziesz dalej", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "Nie możesz wprowadzić analizy przed edycją formularza", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Usuń", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "<PERSON>zy na pewno chcesz usunąć tę analizę? Tej czynno<PERSON>ci nie można cofnąć.", "app.containers.Admin.projects.project.survey.explore": "Eksploruj", "app.containers.Admin.projects.project.survey.followUpResponses": "Dalsze odpowiedzi", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> średnia", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Eksportuj jako GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Wyeksportuj odpowiedzi na to pytanie jako plik GeoJSON. Dla każdego elementu GeoJSON, wszystkie powiązane odpowiedzi respondenta zostaną wymienione w obiekcie \"właściwości\" tego elementu.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "<PERSON>k<PERSON>j <PERSON>egóły", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} respondenci} one {{respondentCount} respondenci} other {{respondentCount} respondenci}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "Zobacz szczegóły", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} wybory} one {{numberChoices} wybór} other {{numberChoices} wybory}}", "app.containers.Admin.projects.project.survey.heatMap": "Mapa cieplna", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Dowiedz się więcej o mapach ciepła generowanych za pomocą Esri Smart Mapping.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "Mapa cieplna jest generowana przy użyciu Esri Smart Mapping. Mapy ciepła są przydatne w przypadku dużej liczby punktów danych. W przypadku mniejszej liczby punktów lepiej jest przyjrzeć się bezpośrednio tylko punktom lokalizacji. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Widok mapy ciepła", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Ukryte przez logikę", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "Gdy użytkownik wybierze tę odpowiedź, logika pomija wszystkie strony aż do strony {pageNumber} ({numQuestionsSkipped} pominięte pytania). <PERSON><PERSON><PERSON><PERSON>, aby ukryć lub wyświetlić pominięte strony i pytania.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "Gdy użytkownik wybierze tę odpowiedź, logika przeskoczy do końca ankiety ({numQuestionsSkipped} pominięte pytania). <PERSON><PERSON><PERSON><PERSON>, aby ukryć lub wyświetlić pominięte strony i pytania.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "Logika na tej stronie pomija wszystkie strony aż do strony {pageNumber} ({numQuestionsSkipped} pytania pominięte). <PERSON><PERSON><PERSON><PERSON>, aby ukryć lub wyświetlić pominięte strony i pytania.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Logika na tej stronie przeskakuje do końca ankiety ({numQuestionsSkipped} pominięte pytania). K<PERSON><PERSON>j, aby ukryć lub wyświetlić pominięte strony i pytania.", "app.containers.Admin.projects.project.survey.newAnalysis": "Nowa analiza", "app.containers.Admin.projects.project.survey.nextInsight": "Następny wgląd", "app.containers.Admin.projects.project.survey.openAnalysis": "Otwarta analiza sztucznej inteligencji", "app.containers.Admin.projects.project.survey.otherResponses": "<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.page": "Strona", "app.containers.Admin.projects.project.survey.previousInsight": "Poprzedni wgląd", "app.containers.Admin.projects.project.survey.responses": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "Liczba odpowiedzi dla tej strony jest niższa niż całkowita liczba odpowiedzi w ankiecie, ponieważ niektórzy respondenci nie widzieli tej strony z powodu logiki w ankiecie.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "Liczba odpowiedzi na to pytanie jest niższa niż całkowita liczba odpowiedzi w ankiecie, ponieważ niektórzy respondenci nie widzieli tego pytania z powodu logiki w ankiecie.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Skala nastrojów", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Błyskawicznie podsumuj wszystkie swoje odpowiedzi.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Rozmawiaj ze swoimi danymi w języku naturalnym.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Uzyskaj odniesienia do poszczególnych odpowiedzi z podsumowań wygenerowanych przez sztuczną inteligencję.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Sprawdź nasz {link} , aby uzyskać pełny przegląd.", "app.containers.Admin.projects.project.survey.upsell.button": "Odblokuj analizę AI", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Anali<PERSON>j dane szybciej dzięki sztucznej inteligencji", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "Ta funkcja nie jest uwzględniona w Twoim bieżącym planie. Porozmawiaj ze swoim Government Success Managerem lub administratorem, aby ją od<PERSON><PERSON><PERSON>.", "app.containers.Admin.projects.project.survey.viewAnalysis": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Przeglądaj podsumowania oparte na sztucznej inteligencji i przeglądaj poszczególne zgłoszenia.", "app.containers.Admin.projects.project.traffic.selectPeriod": "<PERSON><PERSON>bierz okres", "app.containers.Admin.projects.project.traffic.trafficSources": "Źródła ruchu", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Zmieniliśmy sposób gromadzenia i wyświetlania danych odwiedzających. W rezultacie dane odwiedzających są dokładniejsze i dostępnych jest więcej rodzajów danych, przy jednoczesnym zachowaniu zgodności z RODO. Zaczęliśmy gromadzić te nowe dane dopiero w listopadzie 2024 r., więc wcześniej żadne dane nie były dostępne.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "<PERSON><PERSON> c<PERSON>u odwi<PERSON>za<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Raport z fazy", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Dodaj tekst o fazie", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "To jest prz<PERSON>ła<PERSON>wy tekst. Można go edytować i formatować za pomocą edytora w panelu po prawej stronie.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Uczestnicy", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Wyniki projektu", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Streszczenie sprawozdania", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Dodaj cel projektu, zastosowane metody uczestnictwa oraz wynik.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Odwied<PERSON>j<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Ten raport zawiera niezapisane zmiany. Zapisz go przed wydrukowaniem.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Tytuł jest już z<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "W porównaniu do poprzednich dni {days}", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Ukryj statystyki", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Wskaźnik uczestnictwa", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Pokaż porównanie z ostatnim okresem", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "Najpierw musisz wybrać zakres dat.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Uczestnictwo", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Pokaż komentarze", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "<PERSON><PERSON><PERSON> dane we<PERSON>cio<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Pokaż głosy", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "Głosy", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Dane demografic<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "<PERSON><PERSON><PERSON> dat rejestracji", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "<PERSON> rejestracji", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Użytkownicy: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "Rozciąganie", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Aktywny", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Zarchiwizowane", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "Zakończony", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "Planowane", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projekty", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Status publikacji", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Opublikowano", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "Brakuje danych dla tego widżetu. Skonfiguruj go ponownie lub usuń, aby mó<PERSON> z<PERSON> raport.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Wynik monitora zdrowia społeczności", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "Rok", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "W tym projekcie nie znaleziono odpowiednich faz", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "Nie wybrano żadnej fazy. Najpierw wybierz fazę.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "Brak projektu", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Nie wybrano żadnego projektu. Wybierz projekt jako pier<PERSON>.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "<PERSON>e mo<PERSON><PERSON><PERSON> pow<PERSON> tego raportu, poni<PERSON><PERSON><PERSON> on dane, do których nie masz dostę<PERSON>.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "<PERSON><PERSON> mo<PERSON><PERSON><PERSON> tego raportu, poni<PERSON><PERSON><PERSON> on dane, do których nie masz dost<PERSON>.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "<PERSON>zy na pewno chcesz usunąć \"{reportName}\"? Tej akcji nie można cofnąć.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "<PERSON>zy na pewno chcesz usunąć ten raport? Tej czynn<PERSON>ści nie można cofnąć.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Usuń", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Duplikat", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {days, plural, no {# dni} one {# dni} other {# dni}} temu", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Wystąpił błąd podczas próby utworzenia tego raportu. Proszę spróbować ponownie później.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Zacznij od pustej strony", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Zacznij od szablonu Community Monitor", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "<PERSON><PERSON><PERSON><PERSON><PERSON> raport", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Dostosuj swój raport i udostępnij go wewnętrznym interesariuszom lub społeczności jako plik PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> raportu", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Utwórz swój pier<PERSON> raport", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "Nie wybrano żadnego projektu", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Zacznij od szablonu platformy", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Dr<PERSON>j do <PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Zacznij z szablonem projektu", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Kwartał {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Szablon raportu", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Raport o takim tytule już istnieje. Proszę wybrać inny tytuł.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Wybierz rok", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Udostępnij jako PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "<PERSON><PERSON> podzie<PERSON> się ze wszystkimi, wygeneruj raport PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Udostępnij jako łącze internetowe", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "To łącze internetowe jest dostępne tylko dla użytkowników z uprawnieniami administratora.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Podziel się", "app.containers.Admin.reporting.contactToAccess": "Tworzenie niestandardowych raportów jest częścią licencji premium. Skontaktuj się ze swoim GovSuccess Managerem, aby dowied<PERSON><PERSON> się więcej na ten temat.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Wszystkie raporty", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Raporty monitora społeczności", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "<PERSON>ą to raporty związane z programem Community Monitor. Raporty są generowane automatycznie co kwartał.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "<PERSON><PERSON><PERSON><PERSON><PERSON> raportu", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Dostosuj swój raport i podziel się nim z wewnętrznymi interesariuszami lub społecznością za pomocą linku internetowego.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Twoje raporty pojawią się tutaj.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "<PERSON><PERSON><PERSON> w<PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Raporty z postępów", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Są to raporty utworzone przez Twojego menedżera ds. sukcesów w administracji publicznej", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "<PERSON><PERSON> raporty", "app.containers.Admin.reporting.deprecated": "USUNIĘTO", "app.containers.Admin.reporting.helmetDescription": "Strona raportowania administratora", "app.containers.Admin.reporting.helmetTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.printPrepare": "Przygotowanie do druku...", "app.containers.Admin.reporting.reportBuilder": "<PERSON><PERSON> raportu", "app.containers.Admin.reporting.reportHeader": "Nagłów<PERSON> raportu", "app.containers.Admin.reporting.warningBanner3": "Wykresy i liczby w tym raporcie aktualizują się automatycznie tylko na tej stronie. Zapisz raport, aby zak<PERSON>alizo<PERSON> je na innych stronach.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Wspólna płaszczyzna", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Informacje", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Zastosowane metody", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "Ankieta", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "<PERSON><PERSON><PERSON>ni {days} dni: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Pro<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "Ankieta zewnętrzna", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Wolontariat", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Głosowanie", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.surveyFormTab.downloads": "Pliki do pobrania", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "<PERSON><PERSON>el inną ankietę", "app.containers.Admin.surveyFormTab.editSurveyForm": "<PERSON><PERSON><PERSON><PERSON> <PERSON>rz an<PERSON>", "app.containers.Admin.surveyFormTab.inputFormDescription": "<PERSON><PERSON><PERSON><PERSON>, jakie informacje <PERSON>, dodaj krótkie opisy lub instrukcje, aby p<PERSON><PERSON> odpowiedziami uczestników i określ, czy każde pole jest opcjonalne czy wymagane.", "app.containers.Admin.surveyFormTab.surveyForm": "<PERSON><PERSON> an<PERSON>", "app.containers.Admin.tools.apiTokens.createTokenButton": "Utwórz nowy token", "app.containers.Admin.tools.apiTokens.createTokenCancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Twój token został utworzony. Skopiuj poniższy adres {secret} i przechowuj go w bezpiecznym miejscu.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Utwórz nowy token do użycia z naszym publicznym API.", "app.containers.Admin.tools.apiTokens.createTokenError": "Podaj nazwę dla swojego tokena", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Utwórz token", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b><PERSON><PERSON><PERSON><PERSON>!</b> <PERSON><PERSON><PERSON><PERSON> skopiować tę stronę {secret} tylko raz. <PERSON><PERSON><PERSON> to okno, nie b<PERSON><PERSON><PERSON><PERSON> mógł zobaczyć go ponownie.", "app.containers.Admin.tools.apiTokens.createTokenName": "Nazwa", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Nadaj swojemu tokenowi nazwę", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Twój token został utworzony", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Zamknij", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "<PERSON><PERSON><PERSON><PERSON> {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Skopiowane!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Utwórz nowy token", "app.containers.Admin.tools.apiTokens.createdAt": "Utworzony", "app.containers.Admin.tools.apiTokens.delete": "Us<PERSON>ń token", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "<PERSON>zy na pewno chcesz usunąć ten token?", "app.containers.Admin.tools.apiTokens.description": "Zarządzaj tokenami API dla naszego publicznego interfejsu API. Aby uzy<PERSON> więcej informacji, odwiedź naszą stronę {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Ostatnio używany", "app.containers.Admin.tools.apiTokens.link": "Dokumentacja API", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Nazwa", "app.containers.Admin.tools.apiTokens.noTokens": "<PERSON>e masz jeszcze żadnych tokenów.", "app.containers.Admin.tools.apiTokens.title": "Publiczne tokeny API", "app.containers.Admin.tools.esriDisabled": "Integracja z Esri jest funkcją dodatkową. Skontaktuj się ze swoim GovSuccess Managerem, jeśli ch<PERSON>z uzyskać więcej informacji na ten temat.", "app.containers.Admin.tools.esriIntegration2": "Integracja z Esri", "app.containers.Admin.tools.esriIntegrationButton": "Włącz Esri", "app.containers.Admin.tools.esriIntegrationDescription3": "Połącz swoje konto Esri i importuj dane z ArcGIS Online bezpośrednio do swoich projektów mapowania.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Logo Esri", "app.containers.Admin.tools.esriKeyInputDescription": "Dodaj swój klucz API Esri, aby umożliwić importowanie warstw map z ArcGIS Online w zakładkach map w projektach.", "app.containers.Admin.tools.esriKeyInputLabel": "Klucz API Esri", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Wklej tutaj klucz <PERSON>", "app.containers.Admin.tools.esriMaps": "<PERSON><PERSON>", "app.containers.Admin.tools.esriSaveButtonError": "Wys<PERSON><PERSON><PERSON>ł błąd podczas zapisywania klucza, spróbuj ponownie.", "app.containers.Admin.tools.esriSaveButtonSuccess": "Zapisany klucz API", "app.containers.Admin.tools.esriSaveButtonText": "Zapisz klucz", "app.containers.Admin.tools.learnMore": "Dowiedz się więcej", "app.containers.Admin.tools.managePublicAPIKeys": "Zarządzaj kluczami API", "app.containers.Admin.tools.manageWidget": "Zarządzaj widżetem", "app.containers.Admin.tools.manageWorkshops": "Zarządza<PERSON>", "app.containers.Admin.tools.powerBIAPIImage": "Obraz Power BI", "app.containers.Admin.tools.powerBIDescription": "Skorzystaj z naszych szablonów Power BI typu plug & play, aby u<PERSON><PERSON>ć dostęp do danych Go Vocal w obszarze roboczym Microsoft Power BI.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI nie jest cz<PERSON>ścią Twojej licencji. Skontaktuj się ze swoim GovSuccess Managerem, jeśli chcesz uzyskać więcej informacji na ten temat.", "app.containers.Admin.tools.powerBIDownloadTemplates": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "Je<PERSON><PERSON> zamierzasz używać danych Go Vocal w przepływie danych Power BI, ten szablon pozwoli ci skonfigurować nowy przepływ danych, kt<PERSON>ry łączy się z danymi Go Vocal. Po pobraniu tego szablonu musisz najpierw znaleźć i zastąpić następujące ciągi ##CLIENT_ID## i ##CLIENT_SECRET## w szablonie swoimi publicznymi danymi uwierzytelniającymi API przed przesłaniem do PowerBI.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Pobierz szablon przepływu danych", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Szablon przepływu danych", "app.containers.Admin.tools.powerBITemplates.intro": "Uwaga: Aby uż<PERSON>ć któregokolwiek z tych szablonów Power BI, musisz najpierw {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "utwórz zestaw poświadczeń dla naszego publicznego API", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "Ten szablon utworzy raport Power BI na podstawie twoich danych Go Vocal. Skonfiguruje wszystkie połączenia danych z twoją platformą Go Vocal, utworzy model danych i niektóre domyślne pulpity nawigacyjne. Po otwarciu szablonu w Power BI zostaniesz poproszony o wprowadzenie poświadczeń publicznego API. Będziesz także musiał wprowadzić bazowy adres URL swojej platformy, który brzmi: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "<PERSON><PERSON><PERSON> szablon raportu", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Szablon raportu", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Więcej informacji na temat korzystania z danych Go Vocal w Power BI można znaleźć na naszej stronie {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Szablony Power BI", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "Zarządzaj danymi uwierzytelniającymi, aby <PERSON><PERSON><PERSON><PERSON> niestandardowe integracje z naszym publicznym interfejsem API.", "app.containers.Admin.tools.publicAPIDisabled1": "Publiczny interfejs API nie jest częścią Twojej obecnej licencji. Skontaktuj się ze swoim GovSuccess Managerem, jeśli ch<PERSON>z uzy<PERSON>ć więcej informacji na ten temat.", "app.containers.Admin.tools.publicAPIImage": "Publiczny obraz API", "app.containers.Admin.tools.publicAPITitle": "Publiczny dostęp do API", "app.containers.Admin.tools.toolsLabel": "Narzędzia", "app.containers.Admin.tools.widgetDescription": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.Admin.tools.widgetImage": "O<PERSON>z widżetu", "app.containers.Admin.tools.widgetTitle": "Widżet", "app.containers.Admin.tools.workshopsDescription": "Organizuj spotkania wideo na żywo, ułatwiaj jednoczesne dyskusje grupowe i debaty. <PERSON><PERSON><PERSON> opinie, głosuj i osiągaj konsensus, tak jak w trybie offline.", "app.containers.Admin.tools.workshopsImage": "Obraz warsztatów", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Warsztaty deliberacyjne online", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "łączna liczba użytkowników na platformie", "app.containers.AdminPage.DashboardPage._blank": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.allGroups": "Wszystkie grupy", "app.containers.AdminPage.DashboardPage.allProjects": "Wszystkie projekty", "app.containers.AdminPage.DashboardPage.allTime": "Cały czas", "app.containers.AdminPage.DashboardPage.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Do pomiaru reprezentatywności użytkowników platformy potrzebny jest podstawowy zbiór danych.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Już wkrótce", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Obecnie pracujemy nad panelem {fieldName}, bę<PERSON><PERSON> ona dostępna wkrótce", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# element jest ukryty} few {# elementy są ukryte} many {# elementów jest ukrytych} other {# elementy są}} w tym wykresie. Zmień na {tableViewLink} , aby wy<PERSON><PERSON><PERSON><PERSON> wszystkie dane.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} do rejestracji użytkownika", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} spośród {total} użytkowników włączonych ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Pokaż {numberOfHiddenItems} więcej", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Opcjonalnie", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Proszę podać podstawowy zbiór danych.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Wynik reprezent<PERSON><PERSON><PERSON><PERSON>:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Ten wynik pokazuje, jak dokładnie dane dotyczące użytkowników platformy odzwierciedlają całą populację. Dowiedz się więcej o {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Prz<PERSON><PERSON><PERSON><PERSON> dane podstawowe", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "widok tabeli", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Użytkownicy", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Dodaj grupę wiekową", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} i powyżej", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Grupa wiekowa", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Bez uwzględnienia grupy wiekowej ({upperBound} i powyżej).", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Grupa wiekowa {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "<PERSON><PERSON><PERSON> wiekowe", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "i nad", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Zastosuj przykładowe grupowanie", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wszystko", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "Od", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Ustaw grupy wiekowe tak, aby były zgodne z bazowym zbiorem danych.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "Do", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "<PERSON><PERSON><PERSON><PERSON> grupy wiekowe", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Ta pozycja nie zostanie przeliczona.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "Zobacz mniej", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Pokaż {numberOfHiddenItems} więcej...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "<PERSON><PERSON><PERSON><PERSON> bazowy (opcjonalnie)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Grupy wiekowe (rok urodzenia)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Już wkrótce", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Zakończ", "app.containers.AdminPage.DashboardPage.components.Field.default": "Domyślnie", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Wypełnij wszystkie włączone opcje lub wyłącz opcje, które chcesz pominąć na wykresie. Należy wypełnić co najmniej jedną opcję.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Niekompletne", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Liczba mieszkańców ogółem", "app.containers.AdminPage.DashboardPage.components.Field.options": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Zapisane", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Prosimy najpierw o {setAgeGroupsLink} , a<PERSON> wprowadzanie danych bazowych.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "ustaw grupy wiekowe", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Średni czas reakcji: {days} dni", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Średnia ilość dni na odpowiedź", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "udzielona informacja zwrotna", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Status wejścia", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "<PERSON>jś<PERSON> według statusu", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Liczba we<PERSON>ść", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Oficjalna aktualizacja", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "<PERSON><PERSON> re<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Status zmieniony", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Razem", "app.containers.AdminPage.DashboardPage.components.editBaseData": "<PERSON><PERSON><PERSON><PERSON> danych podstawowych", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "jak oblic<PERSON>y wyniki reprezentatywności", "app.containers.AdminPage.DashboardPage.continuousType": "Ciągły (bez osi czasu)", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Skumulowane razem", "app.containers.AdminPage.DashboardPage.customDateRange": "Własna", "app.containers.AdminPage.DashboardPage.day": "dzień", "app.containers.AdminPage.DashboardPage.false": "fałsz", "app.containers.AdminPage.DashboardPage.female": "kobieta", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Top 5 wejść <PERSON><PERSON><PERSON> reakcji", "app.containers.AdminPage.DashboardPage.fromTo": "od {from} do {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Panel administracyjny do zarządzania aktywnością na platformie", "app.containers.AdminPage.DashboardPage.helmetTitle": "Strona panelu administracyjnego", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "<PERSON><PERSON><PERSON><PERSON> mater<PERSON>ł do pokazania według projektu", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "<PERSON><PERSON><PERSON><PERSON> mater<PERSON>ł do pokazania według tematu", "app.containers.AdminPage.DashboardPage.inputs1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "<PERSON>jś<PERSON> według statusu", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Wybierz grupę użytkowników", "app.containers.AdminPage.DashboardPage.male": "mężczyzna", "app.containers.AdminPage.DashboardPage.month": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.noData": "Nie ma żadnych danych do pokazania", "app.containers.AdminPage.DashboardPage.noPhase": "Brak skonfigurowanego etapu dla tego projektu", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Liczba uczestników, którzy opublikowali dane wej<PERSON>ciowe, zareagowali lub skomentowali.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "<PERSON>e lubi", "app.containers.AdminPage.DashboardPage.numberOfLikes": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Całkowita liczba reakcji", "app.containers.AdminPage.DashboardPage.overview.management": "Zarządzanie", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projekty i uczestnictwo", "app.containers.AdminPage.DashboardPage.overview.showLess": "Pokaż mniej", "app.containers.AdminPage.DashboardPage.overview.showMore": "Pokaż więcej", "app.containers.AdminPage.DashboardPage.participants": "Uczestnicy", "app.containers.AdminPage.DashboardPage.participationPerProject": "Uczestnictwo wg projektu", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Uczestnictwo wg tematu", "app.containers.AdminPage.DashboardPage.perPeriod": "Wg {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Poprzednie 30 dni", "app.containers.AdminPage.DashboardPage.previous90Days": "Poprzednie 90 dni", "app.containers.AdminPage.DashboardPage.previousWeek": "Poprzedni tydzień", "app.containers.AdminPage.DashboardPage.previousYear": "Poprzedni rok", "app.containers.AdminPage.DashboardPage.projectType": "Rodzaj projektu : {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Ten bazowy zbiór danych jest niezbędny do obliczenia reprezentatywności użytkowników platformy w stosunku do całej populacji.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Proszę podać podstawowy zbiór danych.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jak reprezentatywni są użytkownicy Twojej platformy. Porównaj swoich użytkowników z danymi dotyczącymi całej populacji, korzystając z danych zbieranych podczas rejestracji użytkowników. <PERSON><PERSON><PERSON> si<PERSON>, jak obliczamy wyniki reprezentatywności {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jak reprezentatywni są użytkownicy Twojej platformy. Porównaj swoich użytkowników z danymi dotyczącymi całej populacji, korzystając z danych zbieranych podczas rejestracji użytkowników. <PERSON><PERSON><PERSON>ę, jak obliczamy wyniki reprezentatywności.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Reprezentatywność w społeczności", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Powrót do panelu administracyjnego", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Obecnie nie są obsługiwane żadne z włączonych pól rejestracji.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "W tym miejscu można poka<PERSON>/ukryć elementy na pulpicie nawigacyjnym i wprowadzić dane podstawowe. W tym miejscu będą wyświetlane tylko pola włączone dla {userRegistrationLink}.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "<PERSON><PERSON><PERSON><PERSON> danych podstawowych", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "rejestracja użytkownika", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Prz<PERSON><PERSON><PERSON><PERSON> dane podstawowe", "app.containers.AdminPage.DashboardPage.resolutionday": "w dniach", "app.containers.AdminPage.DashboardPage.resolutionmonth": "w miesią<PERSON>ch", "app.containers.AdminPage.DashboardPage.resolutionweek": "w tygodniach", "app.containers.AdminPage.DashboardPage.selectProject": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.containers.AdminPage.DashboardPage.selectedProject": "aktualny filtr projektu", "app.containers.AdminPage.DashboardPage.selectedTopic": "aktualny filtr tematu", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "<PERSON><PERSON><PERSON>, co dzieje się na Twojej platformie.", "app.containers.AdminPage.DashboardPage.tabOverview": "Przegląd", "app.containers.AdminPage.DashboardPage.tabReports": "Projekty", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.tabUsers": "Użytkownicy", "app.containers.AdminPage.DashboardPage.timelineType": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.titleDashboard": "Statystyki", "app.containers.AdminPage.DashboardPage.total": "Razem", "app.containers.AdminPage.DashboardPage.totalForPeriod": "Ten {period}", "app.containers.AdminPage.DashboardPage.true": "prawda", "app.containers.AdminPage.DashboardPage.unspecified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.users": "Użytkownicy", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Użytkownicy według wieku", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Użytkownicy według obszaru geograficznego", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Użytkownicy według płci", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Rejestracje", "app.containers.AdminPage.DashboardPage.week": "tydzień", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Wskazówki dotyczące wyboru favicon - ikony na zakładce przeglądarki: wybierz prosty obraz, ponieważ pokazany obraz jest bardzo mały. Obrazek powinien być zapisany jako PNG i powinien być kwadratowy z przezroczystym tłem (lub w razie potrzeby białym tłem). Twój favicon powinien być ustawiony tylko raz, ponieważ zmiany będą wymagały pewnego wsparcia technicznego.", "app.containers.AdminPage.FaviconPage.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Coś poszło nie tak, proszę spróbuj ponownie później.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Sukces!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Twoje zmiany zostały zapisane.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Usuń", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Menadżerowi<PERSON>", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Menedżerowie folderów mogą edytować opis foldera, tworz<PERSON>ć nowe projekty w obrębie folderu oraz posiadać uprawnienia do zarządzania wszystkimi projektami w obrębie folderu. Nie mogą oni usuwać projektów i nie mają dostępu do projektów, które nie znajdują się w ich folderze. Aby uzyskać więcej informacji o uprawnieniach do zarządzania projektami, moż<PERSON>z wybrać opcję {projectManagementInfoCenterLink}.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Nie znaleziono dopasowania", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "odwiedź nasze Centrum Pomocy", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Przeszukaj użytkowników", "app.containers.AdminPage.FoldersEdit.addToFolder": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Zarchiwizowany", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Usuń ten folder", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Opis", "app.containers.AdminPage.FoldersEdit.draftStatus": "Szkic", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Dodaj pliki do tego folderu", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Pliki nie powinny być większe niż 50Mb. Dodane pliki zostaną wyświetlone na stronie folderu.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Opisy", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "W tym folderze nie ma żadnych projektów. Wróć do zakładki \"Projekty\", aby utworzyć i dodać projekty.", "app.containers.AdminPage.FoldersEdit.folderName": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Obrazek z nagłówka", "app.containers.AdminPage.FoldersEdit.multilocError": "Wszystkie pola tekstowe muszą być wypełnione dla każdego języka.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "<PERSON>e ma projektów, które można dodać do tego folderu.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Uprawnienia", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Projekty w folderze", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Ustawienia", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projekty dodane do tego folderu", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Proje<PERSON><PERSON>, które można dodać do tego folderu", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "<PERSON><PERSON><PERSON><PERSON>, czy ten folder to \"szkic\", \"opublikowane\" czy \"archiwum\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Opublikowany", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Us<PERSON>ń z folderu", "app.containers.AdminPage.FoldersEdit.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Coś poszło nie tak. Proszę spróbuj ponownie.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Sukces!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Twoje zmiany zostały zapisane.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Krótki opis", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "Pokazywane na stronie głównej", "app.containers.AdminPage.FoldersEdit.statusLabel": "Status publikacji", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON>czego projekty są połączone, okre<PERSON><PERSON> stronę wizualną i poinformuj.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON>czego projekty są połączone, okre<PERSON><PERSON> stronę wizualną i poinformuj.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Wszystkie pola tekstowe muszą zostać wypełnione.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Utwórz nowy folder", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Ustawienia", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Zobacz folder", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Dostosuj obraz i tekst banera głównego.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "<PERSON><PERSON> s<PERSON> g<PERSON>", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Inspiration Hub to mi<PERSON><PERSON><PERSON>, w którym możesz znaleźć inspirację dla swoich projektów, przeglądając projekty na innych platformach.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" i \"Politykę prywatności\" swojej platformy. <PERSON><PERSON> stron<PERSON>, w tym strony \"O nas\" i \"FAQ\", mogą być edytowane w zakładce {navigationLink} .", "app.containers.AdminPage.PagesEdition.policiesTitle": "Polityka platformy", "app.containers.AdminPage.PagesEdition.privacy-policy": "Polityka p<PERSON>watności", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.confirmation.description": "Tego działania nie można co<PERSON>.", "app.containers.AdminPage.Project.confirmation.no": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.confirmation.title": "Czy na pewno chcesz zresetować wszystkie dane dotyczące uczestnictwa?", "app.containers.AdminPage.Project.confirmation.yes": "Zresetuj wszystkie dane dotyczące uczestnictwa", "app.containers.AdminPage.Project.data.descriptionText1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pomy<PERSON>ły, k<PERSON>ntarz<PERSON>, głosy, re<PERSON><PERSON><PERSON>, odpowiedzi na ankiety, odpowiedzi na ankiety, wolontariuszy i zarejestrowanych uczestników wydarzeń. W przypadku faz głosowania ta akcja wyczyści głosy, ale nie opcje.", "app.containers.AdminPage.Project.data.title": "Wyczyść wszystkie dane dotyczące uczestnictwa w tym projekcie", "app.containers.AdminPage.Project.resetParticipationData": "Zresetuj wszystkie dane dotyczące uczestnictwa", "app.containers.AdminPage.Project.settings.accessRights": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.back": "Powró<PERSON>", "app.containers.AdminPage.Project.settings.data": "<PERSON>", "app.containers.AdminPage.Project.settings.description": "Opis", "app.containers.AdminPage.Project.settings.events": "Wyd<PERSON>zen<PERSON>", "app.containers.AdminPage.Project.settings.general": "Ogólne", "app.containers.AdminPage.Project.settings.projectTags": "Tagi projektu", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Lista projektów na platformie", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Panel projektów", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Utwórz nowy projekt lub zarządzaj istniejącymi.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projekty", "app.containers.AdminPage.ProjectDashboard.published": "Opublikowane", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Zamknij panel ustawień", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Centrum", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "<PERSON><PERSON>ł<PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "Le<PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "Wyrównanie przycisków", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "Prawo", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Tekst przycisku", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Wprowadź tekst dla przycisku", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "Podstawowy", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Typ przycisku", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Drugorzędny", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "Przycisk URL", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Wprowadź adres URL przycisku", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Opis", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Opis strony głównej", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Ten opis znajduje się w przeglądzie projektu na stronie głównej.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Ten opis znajduje się na stronie informacyjnej projektu.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Coś poszło nie tak. Proszę spróbuj ponownie.", "app.containers.AdminPage.ProjectDescription.preview": "Podgląd", "app.containers.AdminPage.ProjectDescription.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Twoje zmiany zostały zapisane.", "app.containers.AdminPage.ProjectDescription.saved": "Zapisane!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Powiedz użytkownikom o co chodzi w projekcie i przekonaj ich do udziału.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Opis projektu", "app.containers.AdminPage.ProjectDescription.whiteSpace": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Uwzględnij grani<PERSON>ę", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> pionowo", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Średnie", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Ma<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Domyślna szerokość geograficzna punktu środkowego mapy. Przyjmuje wartość pomiędzy -90 a 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Domyślna długość geograficzna punktu środkowego mapy. Przyjmuje wartość pomiędzy -180 a 180.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Coś poszło nie tak, proszę spróbuj ponownie później", "app.containers.AdminPage.ProjectEdit.MapTab.here": "tutaj", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Importuj plik GeoJSON", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Domyślna szerokość geograficzna", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Ten kolor jest stosowany do wszystkich elementów w obrębie warstwy mapy. Roz<PERSON>ry markerów, szerokość linii i poziom przezroczystości są ustalone domyślnie.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Ikona znacznika", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "<PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> jest wyświetlana w znacznikach. Kliknij przycisk {url}, aby w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> listę ikon, które można wybrać.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Nazwa warstwy", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Nazwa tej warstwy jest wyświetlana w legendzie mapy", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Etykieta warstwy", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Ten tekst jest wyświetlany jako podpowiedź podczas najechania kursorem na warstwy na mapie", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Warstwy", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "Obecnie obsługujemy pliki GeoJSON. Przeczytaj {supportArticle}, aby do<PERSON><PERSON>, jak konwertować i stylizować warstwy mapy.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Domyślna długość geograficzna środka", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Domyślny środek mapy", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Domyślny punkt środkowy i poziom powiększenia mapy. Ręcznie dostosuj poniższe wartości lub kliknij przycisk {button} w lewym dolnym rogu mapy, aby zapisać bieżący punkt środkowy i poziom powiększenia mapy jako wartości domyślne.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Dostosuj widok mapy, załaduj i stylizuj warstwy mapy oraz ustaw środek mapy i poziom powiększenia.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Konfiguracja mapy", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "Konfiguracja mapy jest obecnie współdzielona między fazami, nie możesz tworzyć różnych konfiguracji mapy na fazę.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Us<PERSON>ń warstwę", "app.containers.AdminPage.ProjectEdit.MapTab.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Zapisz powiększenie", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Nienazwana warstwa", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Domyślny poziom powiększenia", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "<PERSON><PERSON><PERSON>, jak bardzo mapa jest domyślnie powiększona. <PERSON><PERSON>bierz wartoś<PERSON> pomiędzy 0 a 20, gdzie 0 oznacza całkowite pomniejszenie (cały świat jest widoczny), a 20 oznacza całkowite przybliżenie (bloki i budynki są widoczne)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Anonimizuj wszystkie dane użytkownika", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Wszystkie dane wprowadzane do ankiety przez użytkowników zostaną zanonimizowane przed ich zarejestrowaniem", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Użytkownicy nadal będą musieli spełniać wymagania dotyczące uczestnictwa w zakładce \"Prawa dostępu\". Dane profilu użytkownika nie będą dostępne w eksporcie danych ankiety.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "<PERSON><PERSON><PERSON> włączysz tę opcję, pola rejestracji użytkowników będą wyświetlane jako ostatnia strona ankiety zamiast jako cz<PERSON> procesu rejestracji.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Pola demograficzne w formularzu ankiety", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Pokaż pola demograficzne w ankiecie?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "Przeczytaj więcej o tym, jak działa auto-sharing w tym artykule.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Automatyczne udostępnianie wyników", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Wyniki głosowania są udostępniane na platformie i za pośrednictwem wiadomości e-mail do uczestników po zakończeniu etapu. Zapewnia to domyśln<PERSON> przejrz<PERSON><PERSON>.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Udostępnianie wyników", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "<PERSON><PERSON><PERSON> o<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Usuń", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Usuń", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Edycja opcji odpowiedzi", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Zapisz opcje odpowiedzi", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Edycja opcji odpowiedzi", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Eksport wyników badania", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Maksymalna liczba wyborów jest większa niż liczba dostępnych opcji", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Wybór wielokrotny", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "<PERSON>rak opcji", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "Wszystkie pytania muszą mieć możliwość wyboru odpowiedzi", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "<PERSON><PERSON><PERSON> jedna od<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Respondenci mają tylko jeden wybór", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Zarządzaj opcjami odpowiedzi dla: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "badanie_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Tutaj moż<PERSON>z utworzyć pytania, us<PERSON><PERSON><PERSON> odpowiedzi do wyboru dla uczestników dla każdego pytania, zdecydować czy chcesz aby uczestnicy mogli wybrać tylko jedną od<PERSON><PERSON><PERSON> (pojedynczy wybór) czy wiele odpowiedzi (wielokrotny wybór), oraz wyeksportować wyniki ankiety. Możesz utworzyć wiele pytań w ramach jednej ankiety.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "<PERSON><PERSON> w<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Ustawienia i wyniki badań", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "<PERSON>ł<PERSON> maksimum", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "Udzielaj informacji zwrotnych, przypisuj tematy lub kopiuj inicjatywy do następnego etapu projektu.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "Zarządzaj propozycjami, udzielaj informacji zwrotnych i przydzielaj tematy.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Zarządzanie inicjatywami", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "Udostępnianie wyników jest wyłączone.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Wyniki głosowania nie zostaną udostępnione na koniec fazy, chyba że zmodyfikujesz je w ustawieniach fazy.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Wyniki te zostaną automatycznie udostępnione po zakończeniu etapu. Zmodyfikuj datę zakończenia tej fazy, aby z<PERSON> termin udostępniania wyników.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Eksport wyników ankiety (.xslx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Wyniki", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Tutaj możesz pobrać wyniki ankiety (ankiet) Typeform w ramach tego projektu jako plik Excel.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "<PERSON><PERSON> an<PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Wyniki ankiety", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Ankieta", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Skonsultuj odpowiedzi na pytania zawarte w ankiecie", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "<PERSON>zy na pewno?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Opis", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "<PERSON><PERSON><PERSON><PERSON>, a<PERSON> <PERSON><PERSON><PERSON>, co jest wymagane od wolontariuszy i czego mogą oczekiwać.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "<PERSON><PERSON> m<PERSON>, ponieważ formularz zawiera błędy.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Obrazek", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Usuń", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Sprawa jest działaniem lub czy<PERSON>, do której obywatele mogą się zgłosić.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "<PERSON><PERSON><PERSON> opis", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Eksport wolontariuszy", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Sprawa jest działaniem lub czy<PERSON>, do której obywatele mogą się zgłosić.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "Nowa sprawa", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Tutaj można ustawić sprawy, do których obywatele mogą zgłaszać się na ochotnika oraz pobrać listę wolontariuszy.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Wolontariat", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {brak wolontariuszy} one {# wolontariusz} few {# wolontariuszy} many {# wolontariuszy} other {# wolontariusze}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "alokacja budżetu", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Przypisz budżet do opcji i poproś uczestników o wybranie preferowanych opcji, które mieszczą się w całkowitym budżecie.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Alokacja budżetu", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Umożliwienie użytkownikom komentowania może wpłynąć na proces głosowania.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Domyślny widok opcji", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Działania dla użytkowników", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "<PERSON><PERSON><PERSON><PERSON>, jakie dodatkowe działania mogą podjąć użytkownicy.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Stała kwota", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "<PERSON><PERSON><PERSON> poz<PERSON><PERSON>e puste, <PERSON><PERSON><PERSON><PERSON><PERSON> będzie to \"vote\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "<PERSON><PERSON><PERSON> się więcej o tym, kiedy u<PERSON> <b> {voteTypeDescription} </b> na naszej stronie {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Maksymalna liczba głosów na opcję", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Maksymalna liczba głosów", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "<PERSON><PERSON><PERSON><PERSON> liczbę głosów, które użytkownik może oddać łącznie (maksymalnie jeden głos na opcję).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "Wiele głosów na opcję", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Użytkownicy otrzymują pewną liczbę tokenów do rozdysponowania między opcje", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Wiele głosów na opcję", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Liczba głosów na użytkownika", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Przegląd analizy opcji", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Opcje do głosowania", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "Pojedynczy głos na opcję", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Użytkownicy mogą zatwierdzić dowolną z opcji", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Jeden głos na opcję", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Token", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Bez ograniczeń", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Jak powinno nazywać się głosowanie?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "Np. <PERSON>, punkty, kred<PERSON>y wę<PERSON>lowe...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "Np. <PERSON>, punkt, kred<PERSON> węglowy...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Każda metoda głosowania ma różne konfiguracje wstępne", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "Metoda głosowania", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "Metoda głosowania określa zasady głosowania użytkowników", "app.containers.AdminPage.ProjectEdit.addNewInput": "<PERSON><PERSON><PERSON> w<PERSON>d", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "<PERSON><PERSON><PERSON><PERSON> swój projekt do folderu teraz lub zro<PERSON>ć to później w ustawieniach projektu", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Tagi dozwolone", "app.containers.AdminPage.ProjectEdit.altText": "Tekst alternatywny", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "Kiedy ta opcja jest włączona, nie można zob<PERSON>zy<PERSON>, kto na co głosował. Użytkownicy nadal potrzebują konta i mogą głosować tylko raz.", "app.containers.AdminPage.ProjectEdit.approved": "Zatwierdzony", "app.containers.AdminPage.ProjectEdit.archived": "Zarchiwizowany", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Zarchiwizowane projekty są nadal widoczne, ale nie pozwalają na dalsze uczestnictwo.", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Zarchiwizowany", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "<PERSON><PERSON> obszaru nie można usun<PERSON>, p<PERSON><PERSON><PERSON><PERSON> jest on używany do wyświetlania projektów na kolejnych stronach niestandardowych. Zanim będzie można usunąć obszar, trzeba będzie odłączyć go od strony lub usunąć stronę.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Wszystkie obszary", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Projekt będzie widoczny na każdym filtrze obszaru.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "<PERSON><PERSON>r obs<PERSON>u", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projekty można filtrować na stronie głównej za pomocą obszarów. Obszary można ustawiać {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "tutaj", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Brak określonego obszaru", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Projekt nie będzie wyświetlany podczas filtrowania według obszaru.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Wy<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Projekt zostanie wyświetlony na wybranym filtrze (filtrach) obszaru.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Na liście", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "Najczęściej omawiane", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Zaczęły napływać zgłoszenia do tej ankiety. Zmiany w ankiecie mogą spowodować utratę danych i niekompletne dane w wyeksportowanych plikach.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Ankieta została pomyślnie zapisana", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "Ankieta", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "Wyświetl ankietę", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Wybierz metodę głosowania i poproś użytkowników o ustalenie priorytetów między kilkoma różnymi opcjami.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Przeprowadź głosowanie lub ustal priorytety", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Utwórz projekt na podstawie szablonu", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Create an external survey", "app.containers.AdminPage.ProjectEdit.createInput": "Dodaj nowe wejście", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Tworzenie ankiety Go Vocal", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Skonfiguruj ankietę bez opuszczania naszej platformy.", "app.containers.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Formularz wielokrotnego wyboru.", "app.containers.AdminPage.ProjectEdit.createProject": "Nowy projekt", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "<PERSON><PERSON><PERSON> ankiety Typeform, Google Form lub Enalyzer.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Mo<PERSON><PERSON>z ustawić domyślną kolejność wyświetlania postów na stronie głównej projektu.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Sort<PERSON>nie", "app.containers.AdminPage.ProjectEdit.departments": "Departamenty", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Opis", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "W ten sposób włączysz lub wył<PERSON><PERSON><PERSON><PERSON> nielubienie, ale lubienie będzie nadal włączone. Zalecamy pozostawienie tej opcji wyłączonej, chyba że przeprowadzasz analizę opcji.", "app.containers.AdminPage.ProjectEdit.disabled": "Wyłączone", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Liczba polubień na uczestnika", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Włącz nielubienie", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Zbierz opinie na temat dokumentu", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Osadź interaktywny plik PDF i zbieraj komentarze i opinie za pomocą Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Wyłączone", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Włączony", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Wersje robocze projektów są ukryte dla wszystkich osób z wyjątkiem administratorów i przypisanych kierowników projektów.", "app.containers.AdminPage.ProjectEdit.draft": "Szkic", "app.containers.AdminPage.ProjectEdit.draftStatus": "Szkic", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Zarządzaj", "app.containers.AdminPage.ProjectEdit.enabled": "Włączony", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "<PERSON><PERSON><PERSON><PERSON>, jakie działania partycypacyjne mogą podejmować użytkownicy.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Wyd<PERSON>zen<PERSON>", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Załączniki (maks. 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Załączniki są na stronie informacyjnej projektu.", "app.containers.AdminPage.ProjectEdit.filesTab": "Pliki", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Znajd<PERSON> wolontariuszy", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Poproś uczestników o zgłoszenie się w charakterze wolontariusza do działań i spraw.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "<PERSON>ako menedżer folderów możesz wybrać folder podczas tworzenia projektu, ale tylko administrator może go później zmienić", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Tekst alternatywny obrazu karty folderu", "app.containers.AdminPage.ProjectEdit.folderSelectError": "<PERSON><PERSON><PERSON><PERSON> folder, do którego chcesz dodać ten projekt.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> własna", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Zaczęły napływać zgłoszenia do tego formularza. Zmiany w formularzu mogą spowodować utratę danych i niepełne dane w eksportowanych plikach.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Formularz został pomyślnie zapisany", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "<PERSON>nie<PERSON>", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Z szablonu", "app.containers.AdminPage.ProjectEdit.generalTab": "Ogólne", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Tekst alternatywny obrazu nagłówka", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Obrazek z nagłówka", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NOWOŚĆ", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Przekazuj informacje użytkownikom lub korzystaj z narzędzia do tworzenia raportów, aby udostępniać wyniki poprzednich faz.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Udostępniaj informacje lub wyniki", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Zbieranie inicjatyw i informacji zwrotnych", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Tw<PERSON>rz lub zbieraj dane wej<PERSON>cio<PERSON>, re<PERSON><PERSON><PERSON> i/lub komentarze. Wybieraj spośród różnych rodzajów danych wejściowych: zbieranie pomysłów, analiza opcji, pytania i odpowiedzi, identyfikacja kwestii i inne.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Kto jest odpowiedzialny za przetwarzanie inicjatyw?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Wszystkie nowe inicjatywy w ten projekt będą przypisane do tej osoby. Osoba ta może zostać zmieniona w {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Komentowanie pomysłów", "app.containers.AdminPage.ProjectEdit.inputFormTab": "<PERSON><PERSON> p<PERSON>ł<PERSON>", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "zarządzanie inicjatywami", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Zarządzanie pomysłami", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Przesyłanie pomysłów", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Reagowanie na dane wejściowe", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Widok domyślny", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Wybierz domyślny widok inicjatyw użytkowników: karty w widoku siatki lub pinezki na mapie. Uczestnicy mogą ręcznie przełączać się pomiędzy oboma widokami.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Centrum inspiracji", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Osadź adres URL Konveio", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Number of likes per participant", "app.containers.AdminPage.ProjectEdit.limited": "Ograniczona", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Załaduj więcej szablonów", "app.containers.AdminPage.ProjectEdit.mapDisplay": "<PERSON> <PERSON>ie", "app.containers.AdminPage.ProjectEdit.mapTab": "Mapa", "app.containers.AdminPage.ProjectEdit.maxDislikes": "Maks<PERSON>alna liczba polubień", "app.containers.AdminPage.ProjectEdit.maxLikes": "Maks<PERSON>alna liczba polubień", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Maksymalna liczba głosów na opcję musi być mniejsza lub równa całkowitej liczbie głosów.", "app.containers.AdminPage.ProjectEdit.maximum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Uczestnicy nie mogą przekroczyć tego budżetu przy przesyłaniu koszyka.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimalnie", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Wymagaj od uczestników spełnienia minimalnego budżetu, aby wys<PERSON><PERSON> swój koszyk (wpisz '0' je<PERSON>li nie ch<PERSON>z ustalać minimum).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "odwiedź nasze Centrum Pomocy", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Kim są kierownicy projektów?", "app.containers.AdminPage.ProjectEdit.moreDetails": "Więcej szczegółów", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.containers.AdminPage.ProjectEdit.needInspiration": "Potrzebujesz inspiracji? Poznaj podobne projekty z innych miast na {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "<PERSON><PERSON><PERSON> w<PERSON>d", "app.containers.AdminPage.ProjectEdit.newIdea": "Nowy pomysł", "app.containers.AdminPage.ProjectEdit.newInitiative": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newIssue": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newPetition": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newProject": "Nowy projekt", "app.containers.AdminPage.ProjectEdit.newProposal": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Nieodpowiednia kwota", "app.containers.AdminPage.ProjectEdit.noFolder": "<PERSON><PERSON> <PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "- <PERSON><PERSON><PERSON> -", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Nie znaleziono żadnych szablonów", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "To nie może by<PERSON> puste", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Proszę podać maksymalną liczbę głosów na użytkownika", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Tylko administratorzy mogą prz<PERSON>", "app.containers.AdminPage.ProjectEdit.optionNo": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.optionYes": "Tak (wybierz folder)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Poziomy uczestnictwa", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Co chcesz zrobić?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "<PERSON><PERSON><PERSON>rz sposób udziału użytkowników.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "<PERSON><PERSON><PERSON><PERSON>, kto może podj<PERSON>ć poszczególne działania, a także zadawać uczestnikom dodatkowe pytania, aby zebrać więcej informacji.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Wymagania i pytania dotyczące uczestników", "app.containers.AdminPage.ProjectEdit.pendingReview": "W oczekiwaniu na zatwierdzenie", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Uprawnienia", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.phaseEmails": "Powiadomienia", "app.containers.AdminPage.ProjectEdit.pollTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reakcji", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Obraz / zdjęcie karty projektu", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Ten obraz jest częścią karty projektu; kart<PERSON>, kt<PERSON>ra podsumowuje projekt i jest wyświetlana na przykład na stronie głównej.\n\n    Aby uzyskać więcej informacji na temat zalecanych rozdzielczości obrazu, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Folder", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Ten obraz jest wyświetlany na górze strony projektu.\n\n    Więcej informacji na temat zalecanych rozdzielczości obrazu można znaleźć na stronie {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Tekst alternatywny obrazu karty projektu", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Podaj krótki opis obrazu dla użytkowników niedowidzących. Pomoże to czytnikom ekranu prz<PERSON>, czego dotyczy obraz.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Zarządzanie projektem", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Kierownicy projektów mogą edytować projekty, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> inicjatywami i e-mailami uczestników. Możesz {moderationInfoCenterLink}, aby znaleźć więcej informacji na temat uprawnień nadanych kierownikom projektów.", "app.containers.AdminPage.ProjectEdit.projectName": "Nazwa projektu", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Typ projektu", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Zdecyduj, czy projekt ma mieć oś czasu. Projekty z osią czasu mają wyraźny początek i koniec i mogą mieć różne etapy. Projekty bez osi czasu są ciągłe.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Typ projektu nie może być później zmieniony.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "<PERSON><PERSON><PERSON><PERSON> ustawić projekt tak, aby był niewidoczny dla niektórych użytkowników.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć projektu", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Szukasz statusu projektu? Teraz możesz go zmienić w dowolnym momencie bezpośrednio z nagłówka strony projektu.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Opublikowane projekty są widoczne dla wszystkich lub dla wybranej grupy.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Opublikowany", "app.containers.AdminPage.ProjectEdit.purposes": "Sprawy", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Zresetuj dane dotyczące uczestnictwa", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Podczas zapisywania danych wystąpił błąd. Proszę spróbuj jeszcze raz.", "app.containers.AdminPage.ProjectEdit.saveProject": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Zapisane!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Twój formularz został zapisany!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Przes<PERSON><PERSON> szablony", "app.containers.AdminPage.ProjectEdit.selectGroups": "<PERSON><PERSON><PERSON><PERSON> (grupy)", "app.containers.AdminPage.ProjectEdit.setup": "Konfiguracja", "app.containers.AdminPage.ProjectEdit.shareInformation": "Podziel się informacjami", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Ankieta Snap", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Skonfiguruj i spersonalizuj Twój projekt.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "odwiedź nasze centrum wsparcia", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# wybór} one {# wybór} other {# wybór}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "Tak, chcę odejść", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Zaczęły napływać zgłoszenia do tej ankiety. Zmiany w ankiecie mogą spowodować utratę danych i niekompletne dane w wyeksportowanych plikach.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "Przesyłanie plików", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Import", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "Podsumowania AI dla kró<PERSON><PERSON><PERSON> od<PERSON>wiedzi, dł<PERSON><PERSON> odpowiedzi i pytań uzupełniających ze skalą sentymentu są dostępne w zakładce AI na lewym pasku bocznym.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Skala liniowa", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "Mat<PERSON>ca", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "Długa odpowiedź", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Wybór wielokrotny - wybierz wiele", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Wybór obrazu - wybierz wiele", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "Nowe zgłoszenie", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Brak jeszcze odpowiedzi na ankietę", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> od<PERSON>", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Otwarte na odpowiedzi", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Opcjonalnie", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "<PERSON><PERSON><PERSON> nie zostanie dodana żadna logika, ankieta będzie przebiegać zgodnie z normalnym tokiem. <PERSON><PERSON><PERSON> zarów<PERSON> strona, jak i pytania mają logikę, logika pytania będzie miała pierwszeństwo. <PERSON>ew<PERSON><PERSON> si<PERSON>, że jest to zgodne z zamierzonym przepływem ankiety. Aby uzyskać więcej informacji, odwied<PERSON> stronę {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "Lokalizacja", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "<PERSON><PERSON><PERSON>, że ch<PERSON> wy<PERSON>?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Twoje bieżące zmiany nie zostaną zapisane.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Ranking", "app.containers.AdminPage.ProjectEdit.survey.rating": "Ocena", "app.containers.AdminPage.ProjectEdit.survey.required2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {od<PERSON><PERSON><PERSON><PERSON>} one {odpowi<PERSON><PERSON>} other {odpowied<PERSON>}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# odpowiedzi} one {# odpowiedzi} other {# odpowiedzi}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Wybór wielokrotny - wybierz jedną", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Liniowa skala nastrojów", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Przesyłanie plików Esri shapefile", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Ankieta została pomyślnie zapisana", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Ankieta", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Odpowiedzi w ankiecie", "app.containers.AdminPage.ProjectEdit.survey.text2": "Krótka odpowiedź", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Og<PERSON><PERSON><PERSON> {count} o<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Zamieść URL", "app.containers.AdminPage.ProjectEdit.surveyService": "Dostawca ankiety", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Możesz znaleźć więcej informacji na temat zamieszczania ankiet na {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "tutaj", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Ten tag nie może zosta<PERSON> usunięty, poniew<PERSON>ż jest używany do wyświetlania projektów na kolejnych stronach niestandardowych. \nBędziesz musiał odłączyć tag od strony lub usunąć stronę zanim będziesz mógł usunąć tag.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Ogólne ustawienia projektu", "app.containers.AdminPage.ProjectEdit.titleLabel": "Nazwa projektu", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>, k<PERSON><PERSON><PERSON> jest krótki, wciągający i jasny. Zostanie on pokazany w przeglądzie rozwijanym oraz na kartach projektu na stronie głównej.", "app.containers.AdminPage.ProjectEdit.topicLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "W<PERSON><PERSON>rz {topicsCopy} dla tego projektu. Użytkownicy mogą używać ich do filtrowania projektów.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Budżet całkowity", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "<PERSON> czasie", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Nieprzypisany", "app.containers.AdminPage.ProjectEdit.unlimited": "Nieograniczony", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Uż<PERSON>j tego s<PERSON>lonu", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "<PERSON>obacz projekt", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Wolontariat", "app.containers.AdminPage.ProjectEdit.voteTermError": "Warunki głosowania muszą być określone dla wszystkich lokalizacji", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, one {# grupa} few {# grupy} many {# grup} other {# grupy}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Dodatkowe informacje", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Adres 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Adres lokalizacji wydarzenia", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Adres 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "Np. mi<PERSON><PERSON><PERSON>, apartament, budynek", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Dodatkowe informacje adresowe, które mogą pomóc w identyfikacji lokalizacji, takie jak nazwa budynku, numer piętra itp.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "<PERSON><PERSON><PERSON><PERSON> artykuł pomocy technicznej", "app.containers.AdminPage.ProjectEvents.customButtonLink": "Link zew<PERSON>ętrzny", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Dodaj link do zewnętrznego adresu URL (np. usługi wydarzenia lub strony sprzedaży biletów). Ustawienie tego zastąpi domyślne zachowanie przycisku obecności.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Niestandardowy tekst przycisku", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Ustaw tekst przycisku na wartość inną niż \"Zarejestruj się\", gdy ustawi<PERSON> jest zewnętrzny adres URL.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Początek", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "Koniec", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Usuń", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "<PERSON><PERSON><PERSON>, ż<PERSON> ch<PERSON> to wyd<PERSON>zenie? Nie da się tego cofnąć!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Opis wyd<PERSON>ia", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.editEventTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "<PERSON><PERSON> w<PERSON> e-maile do rejestrujących się bezpośrednio z platformy, <PERSON><PERSON> muszą utworzyć grupę użytkowników w zakładce {userTabLink} . {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.eventImage": "Obraz zdarzenia", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Tekst alternatywny obrazu zdarzenia", "app.containers.AdminPage.ProjectEvents.eventLocation": "Miejsce wydarzenia", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Zarejestrowane podmioty eksportujące", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Załączniki (maks. 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Załączniki są poniżej opisu zdarzenia.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Lokalizacja", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Maksymalna liczba zarejestrowanych osób", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Utwórz nowe wydarzenie", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Link do wydarzenia online", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "<PERSON><PERSON><PERSON> Twoje wydarzenie jest dostępne online, dodaj link do niego tutaj.", "app.containers.AdminPage.ProjectEvents.preview": "Podgląd", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Doprecyzuj lokalizację na mapie", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Określ lokalizację na mapie", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "<PERSON><PERSON><PERSON><PERSON>, gdzie wyświetlany jest znacznik lokalizacji wydarzenia, klikając poniższą mapę.", "app.containers.AdminPage.ProjectEvents.register": "Zarejestruj się", "app.containers.AdminPage.ProjectEvents.registerButton": "Przycisk rejestracji", "app.containers.AdminPage.ProjectEvents.registrant": "rejestruj<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.registrants": "rejestruj<PERSON><PERSON> się", "app.containers.AdminPage.ProjectEvents.registrationLimit": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "<PERSON><PERSON> m<PERSON> z<PERSON>, spróbuj jeszcze raz.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Zapisane!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Twoje zmiany zostały zapisane.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "Wyszukaj lokalizację", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Powiąż nadchodzące wydarzenia z tym projektem i pokaż je w zakładce wydarzeń projektu.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Tytuł i daty", "app.containers.AdminPage.ProjectEvents.titleEvents": "Wydarzenia w projekcie", "app.containers.AdminPage.ProjectEvents.titleLabel": "Nazwa zdarzenia", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Połącz przycisk z zewnętrznym adresem URL", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "Domyślnie przycisk rejestracji wydarzenia na platformie będzie wyświetlany, umożliwiając użytkownikom zarejestrowanie się na wydarzenie. Moż<PERSON>z to zmienić, aby zamiast tego połączyć się z zewnętrznym adresem URL.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Ogranicz liczbę zarejestrowanych uczestników wydarzenia", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Ustaw maksymalną liczbę zarejestrowanych uczestników wydarzenia. Jeśli limit zostanie osiągnięty, dalsze rejestracje nie będą akceptowane.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/users", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Użytkownicy", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "Dodaj pliki do swojego projektu", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Dołącz pliki z tej listy do swojego projektu, faz i wydarzeń.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Dodaj pliki jako kontekst do Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Dodaj pliki do swojego projektu Sensemaking, aby zapewnić kontekst i spostrzeżenia.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Już wkrótce", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Synchronizuj an<PERSON>, przesyłaj wywiady i pozwól sztucznej inteligencji łączyć kropki w <PERSON>ich danych.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Prześlij dowolny plik", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Użyj sztucznej inteligencji do analizy plików", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Przetwarzaj transkrypcje itp.", "app.containers.AdminPage.ProjectFiles.addFiles": "<PERSON><PERSON>j pliki", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "Spostrzeżenia oparte na sztucznej inteligencji", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "<PERSON><PERSON><PERSON><PERSON> prz<PERSON>ła<PERSON> pliki, aby zidentyfikować kluczowe tematy.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Zezwalaj na zaawansowaną analizę tych plików przy użyciu przetwarzania AI.", "app.containers.AdminPage.ProjectFiles.askButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.categoryLabel": "Kategoria", "app.containers.AdminPage.ProjectFiles.chooseFiles": "Wybierz pliki", "app.containers.AdminPage.ProjectFiles.close": "Zamknij", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Potwierdź i prześlij", "app.containers.AdminPage.ProjectFiles.confirmDelete": "<PERSON>zy na pewno chcesz usunąć ten plik?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "<PERSON>e można załadować pliku markdown.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "Nie można załadować podglądu CSV.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "W podglądzie CSV wyświetlanych jest maksymalnie 50 wierszy.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "Plik CSV jest zbyt duży do podglądu.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "Us<PERSON>ń plik", "app.containers.AdminPage.ProjectFiles.description": "Opis", "app.containers.AdminPage.ProjectFiles.done": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.downloadFile": "Pobierz plik", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Pobierz pełny plik", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Przeciągnij i upuść tutaj dowolne pliki lub", "app.containers.AdminPage.ProjectFiles.editFile": "<PERSON><PERSON><PERSON><PERSON> plik", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Opis", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "Nazwa pliku nie może zawierać kropki.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "Nazwa pliku", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "Nazwa pliku jest wymagana.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Pobierz plik", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Podgląd", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "Ten plik nie zostanie przesłany, ponieważ przekracza maksymalny limit 50 MB.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "Wszystkie pliki zostały przesłane pomyślnie", "app.containers.AdminPage.ProjectFiles.generatingPreview": "Generowanie podglądu...", "app.containers.AdminPage.ProjectFiles.info_sheet": "Informacje", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "Np. WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Wywiady audio, nagrania z ratusza", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "Np. PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "<PERSON><PERSON><PERSON>, dokumenty informacyjne", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "Np. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.interview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.maxFilesError": "Jednocześnie możesz przesłać maksymalnie {maxFiles} plików.", "app.containers.AdminPage.ProjectFiles.meeting": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.noFilesFound": "Nie znaleziono żadnych plików.", "app.containers.AdminPage.ProjectFiles.other": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.policy": "Polityka", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Podgląd nie jest jeszcze obsługiwany dla tego typu pliku.", "app.containers.AdminPage.ProjectFiles.report": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.retryUpload": "Ponów próbę przesyłania", "app.containers.AdminPage.ProjectFiles.save": "Oszczędzaj", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "Plik został pomyślnie zaktualizowany.", "app.containers.AdminPage.ProjectFiles.searchFiles": "Wyszukaj pliki", "app.containers.AdminPage.ProjectFiles.selectFileType": "Typ pliku", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Plan strategiczny", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "Jednocześnie możesz przesłać maksymalnie {maxFiles} plików.", "app.containers.AdminPage.ProjectFiles.unknown": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.upload": "Prześ<PERSON>j", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# plik} other {# pliki}} prz<PERSON><PERSON><PERSON> p<PERSON>, {numberOfErrors, plural, one {# błąd} other {# błędy}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "Wyświetl plik", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Ukryj wszystkie pola", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Opis pola", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Edycja formularza wejściowego", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Włączony", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to pole.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Coś poszło nie tak, proszę spróbuj ponownie później", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Rozwiń wszystkie pola", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "<PERSON><PERSON> p<PERSON>ł<PERSON>", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "<PERSON><PERSON><PERSON><PERSON>, jakie informacje <PERSON>, dodaj krótkie opisy lub instrukcje, aby <PERSON><PERSON><PERSON> odpowiedzi uczestników i określ, czy każde pole jest opcjonalne czy wymagane.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "<PERSON><PERSON><PERSON><PERSON>, jakie informacje <PERSON>, podaj krótkie opisy lub instrukcje, aby pomóc w odpowiedzi uczestników i określ, które pole jest opcjonalne, a które wymagane", "app.containers.AdminPage.ProjectIdeaForm.required": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Wymagaj wypełnienia tego pola.", "app.containers.AdminPage.ProjectIdeaForm.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Twoje zmiany zostały pomyślnie zapisane.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Zapisane!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "Wyświetl formularz", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Zautomatyzowane wiadomości e-mail", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "<PERSON><PERSON><PERSON><PERSON> skonfigurować wiadomości e-mail wyzwalane na poziomie fazy", "app.containers.AdminPage.ProjectTimeline.datesLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "<PERSON>ź udział w ankiecie", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Ankieta", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "<PERSON>zy na pewno usunąć ten etap?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Opis etapu", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "<PERSON><PERSON><PERSON><PERSON> etap", "app.containers.AdminPage.ProjectTimeline.endDate": "Data zakończenia", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Data końcowa", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Załączniki (maks. 50MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Stwórz nowy etap", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "Ta faza nie ma z góry określonej daty zakończenia.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Udostępnianie wyników niektórych metod (takich jak wyniki głosowania) nie zostanie uruchomione, dopóki nie zostanie wybrana data zakończenia.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "Jak tylko dodasz fazę po tej, zostanie do niej dodana data zakończenia.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "Brak wyboru daty zakończenia oznacza, że:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Podgląd", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "Zapisz zmiany", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł błąd przy przesyłaniu formularza, proszę spróbuj ponownie.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Zapisane!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Twoje zmiany zostały pomyślnie zapisane.", "app.containers.AdminPage.ProjectTimeline.startDate": "Data rozpoczęcia", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Data rozpoczęcia", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "Przycisk", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Nazwa etapu", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Prześlij załączniki", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_podzial", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologia (filtr na pierwszej stronie)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Jak powinny nazywać się tagi w filtrze na stronie? Np. tagi, kategorie, działy, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Tagi mogą by<PERSON> skonfigurow<PERSON> {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "tutaj", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Termin na jeden znacznik (w liczbie pojedynczej)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "temat", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "<PERSON><PERSON><PERSON> wiele tagów (liczba mnoga)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "tagi", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "<PERSON><PERSON><PERSON> pole", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "<PERSON><PERSON><PERSON> nowe pole rejestracji", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Format odpowiedzi", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Podaj format odpowiedzi", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "<PERSON><PERSON><PERSON> odpowiedzi", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Wprowadź opcję odpowiedzi dla wszystkich języków", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Zapisz opcję odpowiedzi", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Opcja odpowiedzi pomyślnie zapisana", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Pola", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Przeciągnij i upuść pola, aby <PERSON><PERSON><PERSON> k<PERSON>, w jakiej pojawią się one w formularzu zapisu.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Pole domyślne", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Usuń", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Opcjonalny tekst wyświetlany pod nazwą pola na formularzu rejestracji.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Opcje odpowiedzi na pytanie o miejsce zamieszkania można ustawić w polu: {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Edycja opcji odpowiedzi", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Opis", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Nazwa pola", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Podaj nazwę pola dla wszystkich języków", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Ustawienia pola", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Pole wyboru \"TAK - NIE\"", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Data", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Długa odpowiedź", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Wielokrotny wybór (możliwość zaznaczenia wielu)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "<PERSON><PERSON><PERSON><PERSON> liczbowa", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Wielokrotny wybór (wybór jednego)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Krótka odpowiedź", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "zakładka obszarów geograficznych", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "<PERSON><PERSON><PERSON><PERSON> pole", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "<PERSON><PERSON> o<PERSON><PERSON> na to pole jest wymagana?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Własne pola", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "<PERSON><PERSON><PERSON> o<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Usuń", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Czy na pewno chcesz usunąć tę opcję odpowiedzi na pytanie rejestracyjne? Wszystkie rekordy, w których dany użytkownik udzielił odpowiedzi za pomocą tej opcji, zostaną trwale usunięte. Tego działania nie można cofnąć.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Czy na pewno chcesz usunąć to pytanie rejestracyjne? Wszystkie odpowiedzi udzielone przez użytkowników na to pytanie zostaną trwale usunięte i nie będzie ono już zadawane w projektach lub propozycjach. Tego działania nie można cofnąć.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "<PERSON><PERSON><PERSON><PERSON> pole", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Pole pomyślnie zapisane", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "<PERSON><PERSON><PERSON> kol<PERSON>", "app.containers.AdminPage.SettingsPage.addAreaButton": "Dodaj obszar geograficzny", "app.containers.AdminPage.SettingsPage.addTopicButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Zwierzę - np. słoń, kot", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Użytkownik - np. Użytkownik 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "<PERSON><PERSON><PERSON><PERSON>, którzy administratorzy będą otrzymywać powiadomienia o zatwierdzeniu projektów. Menedżerowie folderów domyślnie zatwierdzają wszystkie projekty w swoich folderach.", "app.containers.AdminPage.SettingsPage.approvalSave": "Oszczędzaj", "app.containers.AdminPage.SettingsPage.approvalTitle": "Ustawienia zatwierdzania projektu", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "<PERSON><PERSON><PERSON>, że ch<PERSON>z usunąć ten obszar?", "app.containers.AdminPage.SettingsPage.areaTerm": "<PERSON><PERSON><PERSON> dla jed<PERSON>go o<PERSON> (pojedynczy)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "obszar", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Usuń", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areasTerm": "<PERSON><PERSON>in dla wielu obszarów (liczba mnoga)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "obszary", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Wybierz co najmniej jeden język.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Awatary", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "<PERSON><PERSON>ś<PERSON><PERSON><PERSON> awatary", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Pokaż zdjęcia profilowe uczestników i ich liczbę niezarejestrowanym go<PERSON>ciom", "app.containers.AdminPage.SettingsPage.bannerHeader": "Tekst nagłówka", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Tekst nagłówka dla niezarejestrowanych użytkowników", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Tekst pod nagłówkiem dla niezarejestrowanych gości", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Tekst pod nagłówkiem", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "<PERSON><PERSON><PERSON> baneru", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Po<PERSON>ż podgląd dla", "app.containers.AdminPage.SettingsPage.brandingDescription": "Dodaj swoje logo i ustaw kolory platformy.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Branding", "app.containers.AdminPage.SettingsPage.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.chooseLayout": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.color_primary": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.color_secondary": "<PERSON>i kolor", "app.containers.AdminPage.SettingsPage.color_text": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.colorsTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.confirmHeader": "<PERSON><PERSON><PERSON>, że ch<PERSON>z usunąć ten temat?", "app.containers.AdminPage.SettingsPage.contentModeration": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.ctaHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Własny nagłówek strony | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "<PERSON><PERSON><PERSON>ne", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Tekst przycisku", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Przycisk linku", "app.containers.AdminPage.SettingsPage.defaultTopic": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.delete": "Usuń", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Usuń", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Spowoduje to usunięcie tematu, również z wszystkich istniejących inicjatyw. Ta zmiana będzie miała zastosowanie do wszystkich projektów.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Tematy mogą być dodawane w celu ułatwienia kategoryzacji inicjatyw. Tutaj możesz dodawać i usuwać tematy, których chciałbyś używać na swojej platformie. Możesz dodawać tematy do konkretnych projektów w {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Pulpit", "app.containers.AdminPage.SettingsPage.editFormTitle": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "app.containers.AdminPage.SettingsPage.fieldDescription": "Opis obszaru", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Ten opis jest przeznaczony tylko do współpracy wewnętrznej i nie jest pokazywany użytkownikom.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Nazwa obszaru", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Podaj nazwę obszaru dla wszystkich języków", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Nazwa, którą wybierzesz dla każdego obszaru będzie widoczna dla mieszkańców podczas rejestracji i podczas filtrowania projektów.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Zapisz tag", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "<PERSON><PERSON><PERSON> tematu", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Podaj nazwę tagu dla wszystkich języków", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>ą wybierzesz dla każdego temat<PERSON>, bę<PERSON>zie widoczna dla użytkowników platformy.", "app.containers.AdminPage.SettingsPage.fixedRatio": "Baner o stałej proporcji", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Ten typ banera działa najlepiej z obrazami, kt<PERSON>re nie powinny być przy<PERSON>, takimi jak obrazy z tekstem, logo lub konkretne elementy, które są kluczowe dla obywateli. Ten baner jest zastępowany solidnym pudełkiem w kolorze podstawowym, gdy użytkownicy są zalogowani. Możesz ustawić ten kolor w ustawieniach ogólnych. Więcej informacji na temat zalecanego użycia obrazów można znaleźć na naszej stronie {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "baza wiedzy", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Baner o pełnej szerokości", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Ten baner rozciąga się na całą szerokość dla wspaniałego efektu wizualnego. Obraz będzie starał się pokryć jak najwięcej miej<PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, że nie zawsze będzie widoczny przez cały czas. Możesz połączyć ten baner z nakładką w dowolnym kolorze. Więcej informacji na temat zalecanego użycia obrazu można znaleźć na naszej stronie {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "baza wiedzy", "app.containers.AdminPage.SettingsPage.header": "Baner na stronie głównej", "app.containers.AdminPage.SettingsPage.headerDescription": "Dostosuj obraz i tekst banera strony głównej.", "app.containers.AdminPage.SettingsPage.header_bg": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.helmetDescription": "Strona ustawień administracyjnych", "app.containers.AdminPage.SettingsPage.helmetTitle": "Strona ustawień administracyjnych", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Dodaj własne treści do dostosowywalnej sekcji na dole strony głównej.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Nagłówek strony głównej | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "<PERSON><PERSON> o<PERSON>", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nakładki obrazu", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Wykrywanie nieodpowiednic<PERSON> t<PERSON>", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Automatyczne wykrywanie nieodpowiednich treści umieszczanych na platformie.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "Gdy ta funkcja jest włą<PERSON>, dane we<PERSON>, propozycje i komentarze publikowane przez uczestników będą automatycznie sprawdzane. Posty oznaczone jako potencjalnie zawierające nieodpowiednie treści nie będą blokowane, ale zostaną wyróżnione do sprawdzenia na stronie {linkToActivityPage} .", "app.containers.AdminPage.SettingsPage.languages": "Języki", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Możesz wybrać wiele języków dla swojej platformy. Oznacza to dodanie treści dla każdego wybranego języka.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Aktywn<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Proszę prześlij obraz nagłówka", "app.containers.AdminPage.SettingsPage.no_button": "Brak przycisku", "app.containers.AdminPage.SettingsPage.organizationName": "Nazwa organizacji, miasto lub gmina", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Podaj nazwę organizacji lub miasto dla wszystkich języków.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Włączenie nakładania", "app.containers.AdminPage.SettingsPage.phone": "Telefon", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Konfiguracja platformy", "app.containers.AdminPage.SettingsPage.population": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.populationMinError": "Populacja musi być licz<PERSON>ą dodatnią.", "app.containers.AdminPage.SettingsPage.populationTooltip": "Całkowita liczba mieszkańców na Twoim terytorium. Służy do obliczania wskaźnika uczestnictwa. Pozostaw puste, jeśli nie dotyczy.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Blokada przekleństw", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Blokuj dane wejściowe, propozycje i komentarze zawierające najczęściej zgłaszane obraźliwe słowa", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Tekst ten jest wyświetlany na stronie głównej nad projektami.", "app.containers.AdminPage.SettingsPage.projectsSettings": "ustawienia projektu", "app.containers.AdminPage.SettingsPage.projects_header": "Nagłówek projektu", "app.containers.AdminPage.SettingsPage.registrationFields": "Pola re<PERSON>ji", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Podaj krótki opis w górnej części formularza rejestracyjnego.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Rejestracja", "app.containers.AdminPage.SettingsPage.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveArea": "<PERSON><PERSON><PERSON>z o<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Coś poszło nie tak, proszę spróbuj ponownie później.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Zapisane!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Twoje zmiany zostały zapisane.", "app.containers.AdminPage.SettingsPage.selectApprovers": "<PERSON><PERSON><PERSON>rz osoby zatwierdzające", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "<PERSON><PERSON><PERSON><PERSON>, kt<PERSON>re będą wyświetlane użytkownikom po rejestracji.", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "<PERSON><PERSON><PERSON><PERSON>, kt<PERSON>re będą wyświetlane użytkownikom po rejestracji.", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Nie można zapisać. Spróbuj ponownie zmienić ustawienia.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Zarejestruj się\"", "app.containers.AdminPage.SettingsPage.signed_in": "Przycisk dla zarejestrowanych", "app.containers.AdminPage.SettingsPage.signed_out": "Przycisk dla niezarejestrowanych", "app.containers.AdminPage.SettingsPage.signupFormText": "Tekst pomocniczy rejestracji", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Dodaj krótki opis w górnej części formularza zapisu.", "app.containers.AdminPage.SettingsPage.statuses": "Statusy", "app.containers.AdminPage.SettingsPage.step1": "Krok 1: e-mail i hasło", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Widoczne na górze pierwszej strony formularza rejestracji (nazwa, email, hasło).", "app.containers.AdminPage.SettingsPage.step2": "Krok 2: pytania dotyczące rejestracji", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Widoczne na górze drugiej strony formularza rejestracyjnego (dodatkowe pola rejestracyjne).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Określ podziały geograficzne swojego terytorium.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "<PERSON><PERSON><PERSON><PERSON>, jak w<PERSON><PERSON><PERSON><PERSON><PERSON> będzie Twoja nazwa, wybierz języki Twojej platformy i link do Twojej strony.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Podany podtytuł przekracza maksymalny limit znaków wynoszący 90", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminologia", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Ustawienia zostały pomyślnie zaktualizowane.", "app.containers.AdminPage.SettingsPage.tabAreas1": "O<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabBranding": "Branding", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabPolicies": "Polityka", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Zatwierdzenie projektu", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "<PERSON>y propozy<PERSON>", "app.containers.AdminPage.SettingsPage.tabRegistration": "Rejestracja", "app.containers.AdminPage.SettingsPage.tabSettings": "Ogólne", "app.containers.AdminPage.SettingsPage.tabTopics2": "Tagi", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "<PERSON>akie nazwy powinny mieć obszary? Np. dzielnice, osiedla, departamenty, ...", "app.containers.AdminPage.SettingsPage.titleAreas": "Obszary geograficzne", "app.containers.AdminPage.SettingsPage.titleBasic": "Ustawienia główne", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Podany tytuł przekracza maksymalny dozwolony limit 35 znaków", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Zarządzanie temata<PERSON>", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Baner ten jest w szczególności przydatny w przypadku obrazów, które nie współpracują dobrze z tekstem z tytułu, napisu lub przycisku. Elementy te zostaną upchnięte poniżej banera. Więcej informacji na temat zalecanego użycia obrazów można znaleźć na naszej stronie {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "baza wiedzy", "app.containers.AdminPage.SettingsPage.twoRowLayout": "Dwa rzędy", "app.containers.AdminPage.SettingsPage.urlError": "Adres URL jest błędny.", "app.containers.AdminPage.SettingsPage.urlPatternError": "Wprowadź prawidłowy adres URL.", "app.containers.AdminPage.SettingsPage.urlTitle": "Strona internetowa", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Możesz dodać link do swojej własnej strony internetowej. Link ten zostanie użyty na dole strony głównej.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "<PERSON><PERSON><PERSON><PERSON>, w jaki spos<PERSON>b użytkownicy bez nazwy w profilu będą wyświetlani na platformie. Nastąpi to, gdy ustawisz prawa dostępu dla etapu na \"Potwierdzenie e-mailem\". We wszystki<PERSON> przypadkach, po wzięciu udziału, użytkownicy będą mogli zaktualizować nazwę profilu, którą wygenerowaliśmy dla nich automatycznie.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Wyświetlanie nazwy użytkownika (tylko dla użytkowników z potwierdzonym adresem e-mail)", "app.containers.AdminPage.SideBar.administrator": "Administrator", "app.containers.AdminPage.SideBar.communityPlatform": "Platforma wspólnotowa", "app.containers.AdminPage.SideBar.community_monitor": "Monitor społeczności", "app.containers.AdminPage.SideBar.customerPortal": "Portal klienta", "app.containers.AdminPage.SideBar.dashboard": "Statystyki", "app.containers.AdminPage.SideBar.emails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.folderManager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.groups": "Grupy", "app.containers.AdminPage.SideBar.guide": "Przewodnik", "app.containers.AdminPage.SideBar.inputManager": "Pomysły", "app.containers.AdminPage.SideBar.insights": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inspirationHub": "Centrum inspiracji", "app.containers.AdminPage.SideBar.knowledgeBase": "<PERSON><PERSON> wiedzy", "app.containers.AdminPage.SideBar.language": "Język", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Strony i menu", "app.containers.AdminPage.SideBar.messaging": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.moderation": "Aktywn<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.notifications": "Powiadomienia", "app.containers.AdminPage.SideBar.processing": "Przetwarzanie", "app.containers.AdminPage.SideBar.projectManager": "Kierownik projektu", "app.containers.AdminPage.SideBar.projects": "Projekty", "app.containers.AdminPage.SideBar.settings": "Ustawienia", "app.containers.AdminPage.SideBar.signOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.support": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.toPlatform": "Do platformy", "app.containers.AdminPage.SideBar.tools": "Narzędzia", "app.containers.AdminPage.SideBar.user.myProfile": "<PERSON><PERSON><PERSON> profil", "app.containers.AdminPage.SideBar.users": "Użytkownicy", "app.containers.AdminPage.SideBar.workshops": "Warsztaty", "app.containers.AdminPage.Topics.addTopics": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.browseTopics": "Przeglądaj tematy", "app.containers.AdminPage.Topics.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.confirmHeader": "<PERSON><PERSON><PERSON>, że ch<PERSON>z usunąć ten temat?", "app.containers.AdminPage.Topics.delete": "Usuń", "app.containers.AdminPage.Topics.deleteTopicLabel": "Usuń", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Ten temat nie będzie już mógł być wykorzystywany w projektach.", "app.containers.AdminPage.Topics.inputForm": "<PERSON><PERSON>", "app.containers.AdminPage.Topics.lastTopicWarning": "W<PERSON><PERSON>y jest co najmniej jeden temat. <PERSON><PERSON><PERSON> nie chcesz używać tematów, moż<PERSON>z je wyłączyć wzakładce {ideaFormLink}.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Możesz dodawać i usuwać tematy, które mogą być przypisane do inicjatyw w tym projekcie.", "app.containers.AdminPage.Topics.remove": "Usuń", "app.containers.AdminPage.Topics.title": "Tagi dozwolone", "app.containers.AdminPage.Topics.topicManager": "Zarządzanie temata<PERSON>", "app.containers.AdminPage.Topics.topicManagerInfo": "<PERSON><PERSON><PERSON> ch<PERSON>z dodać dodatkowe tematy projektu, mo<PERSON><PERSON>z to zrobić w polu {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "<PERSON><PERSON>j nową grupę", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Nazwa grupy", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Podaj nazwę grupy", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Utwórz grupę ręcznie", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Jakiego rodzaju grupy potrzebujesz?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Zapisz grupę", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Utwórz grupę ręcznie", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Utwórz inteligentną grupę", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Dowiedz się więcej na temat grup", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Możesz wybrać użytkowników z listy i dodać ich do tej grupy.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Mo<PERSON><PERSON>z zdefinio<PERSON>ć warunki, a użytkownicy, którzy je spełnią, zostaną automatycznie dodani do tej grupy.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Grupa ręczna", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Inteligentna grupa", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Nie ma jeszcze nikogo w tej grupie", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Przejdź do {allUsersLink}, aby rę<PERSON>nie dodać niektórych użytkowników.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Nie ma użytkowników, spełniających Twoje kryteria wyszukiwania", "app.containers.AdminPage.Users.GroupsPanel.select": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Eksportuj wszystko", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Eksportuj użytkowników w grupie", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Eksportuj wybrane", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "<PERSON>zy na pewno?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Podczas dodawania użytkowników do grup wystąpił błąd, proszę spróbuj ponownie.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Usuń z grupy", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Usunąć wybranych użytkowników z tej grupy?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Podczas usuwania użytkowników z grupy wystąpił błąd, proszę spróbuj ponownie.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Dodaj użytkowników do grupy", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.add": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.addAnswer": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.addQuestion": "Dodaj pytania demograficzne", "app.containers.AdminPage.groups.permissions.answerChoices": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.answerFormat": "Format odpowiedzi", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "Należy zapewnić co najmniej jeden wybór", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "Ten użytkownik moderuje folder zawierający ten projekt. Aby odebrać mu prawa moderatora tego projektu, mo<PERSON><PERSON>z odebrać mu prawa do folderu lub przenieść projekt do innego folderu.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Utwórz nowe pytanie", "app.containers.AdminPage.groups.permissions.createAQuestion": "Utwórz pytanie", "app.containers.AdminPage.groups.permissions.defaultField": "Pole domyślne", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Usuń", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Usuń", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Podaj tytuł dla wszystkich opcji", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "<PERSON><PERSON><PERSON><PERSON><PERSON> (pole wyboru)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Data", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Długa odpowiedź", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Wybór wielokrotny (wybierz kilka)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Wartość numeryczna", "app.containers.AdminPage.groups.permissions.fieldType_select": "Wybór wielokrotny (wybierz jedną)", "app.containers.AdminPage.groups.permissions.fieldType_text": "Krótka odpowiedź", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Zmiana uprawnień szczegółowych nie jest częścią Twojej licencji. Skontaktuj się ze swoim GovSuccess Managerem, aby dowiedzieć się więcej na ten temat.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "<PERSON>zy na pewno chcesz usunąć tę grupę z projektu?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "<PERSON><PERSON><PERSON><PERSON> jedną lub więcej grup", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {<PERSON><PERSON> członków} one {1 członek} few {{count} członków} many {{count} członków} other {{count} członków}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Proszę wypełnić tytuł we wszystkich językach", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "<PERSON>zy na pewno?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Nie znaleziono menadżerów projektu", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "<PERSON>c nie jest poka<PERSON>e, poniew<PERSON>ż nie ma żadnych spraw, do których użytkownik może się zgł<PERSON>ić w tym projekcie.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Tylko administratorzy mogą tworzyć nowe pytania.", "app.containers.AdminPage.groups.permissions.option1": "Opcja 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Zaproszenie oczekujące", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "<PERSON>to może dodawać adnotacje do dokumentu?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "<PERSON>to może zapisać się na wydarzenie?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Kto może komentować dane wejściowe?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Kto może zgłaszać uwagi do propozycji?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Kto może zamieścić propozycję?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Kto może reagować na dane wejściowe?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Kto może zgłaszać dane wejściowe?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Kto może wziąć udział w ankiecie?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Kto może wziąć udział w badaniu?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "<PERSON>to może zostać wolontariuszem?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Kto może głosować nad propozycjami?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Kto może głosować?", "app.containers.AdminPage.groups.permissions.questionDescription": "<PERSON>is pytania", "app.containers.AdminPage.groups.permissions.questionTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Coś poszło nie tak, proszę spróbuj ponownie później.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Sukces!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Twoje zmiany zostały zapisane.", "app.containers.AdminPage.groups.permissions.select": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.selectValueError": "Prosz<PERSON> wybrać rodzaj odpowiedzi", "app.containers.AdminPage.new.createAProject": "Utwórz projekt", "app.containers.AdminPage.new.fromScratch": "<PERSON><PERSON> pod<PERSON>w", "app.containers.AdminPage.phase.methodPicker.addOn1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "Spostrzeżenia oparte na sztucznej inteligencji", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Pomóż uczestnikom ujawnić zgodę i brak zgody, po jednym pomyśle na raz.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Znajdź wspólną płaszczyznę", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Osadź interaktywny plik PDF i zbieraj komentarze i opinie za pomocą Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Zbierz opinie na temat dokumentu", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "<PERSON><PERSON><PERSON>ć ankietę innej firmy", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "Ankieta zewnętrzna", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Wykorzystaj zbiorową inteligencję użytkowników. Zaproś ich do przesyłania, omawiania pomysłów i/lub przekazywania opinii na forum publicznym.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Zbieraj informacje i opinie publicznie", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Udostępniaj informacje", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Brak informacji opartych na sztucznej inteligencji na platformie", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Brak raportowania na platformie oraz wizualizacji i przetwarzania danych", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Połącz z narzędziem do tworzenia raportów na platformie", "app.containers.AdminPage.phase.methodPicker.logic1": "Logika", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Szeroki zakres typów pytań", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Pozwól uczestnikom na przesyłanie pomysłów z limitem czasu i głosów.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "<PERSON><PERSON><PERSON>, petycje lub inic<PERSON>wy", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "Szybka an<PERSON>", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Przygotuj krótki kwestionariusz wielokrotnego wyboru.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "Dostarczaj informacje użytkownikom, wizualizuj wyniki z innych faz i twórz bogate w dane raporty.", "app.containers.AdminPage.phase.methodPicker.survey1": "Ankieta", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Poznaj potrzeby i sposób myślenia użytkowników dzięki szerokiej gamie prywatnych typów pytań.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Utw<PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Poproś użytkowników o zgłaszanie się na ochotnika do działań i spraw lub znajdź uczestników panelu.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Rekrutuj uczestników lub wolontariuszy", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Wybierz metodę głosowania i poproś użytkowników o ustalenie priorytetów między kilkoma różnymi opcjami.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Przeprowadź głosowanie lub ustal priorytety", "app.containers.AdminPage.projects.all.all": "Wszystkie", "app.containers.AdminPage.projects.all.createProjectFolder": "Nowy folder", "app.containers.AdminPage.projects.all.existingProjects": "Istniejące projekty", "app.containers.AdminPage.projects.all.homepageWarning1": "Użyj tej strony, aby ustawić kolejność projektów w rozwijanym menu \"Wszystkie projekty\" na pasku nawigacyjnym. Jeśli korzystasz z widżetów \"Opublikowane projekty i foldery\" lub \"Projekty i foldery (starsze)\" na stronie głównej, kolejność projektów w tych widżetach będzie również określona przez kolejność ustawioną tutaj.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "<PERSON><PERSON><PERSON><PERSON>, w któ<PERSON>ch jesteś kierownikiem projektu, poja<PERSON><PERSON> się tutaj.", "app.containers.AdminPage.projects.all.noProjects": "Nie znaleziono żadnych projektów.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Tylko administratorzy mogą tworzyć foldery projektów.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projekty i foldery", "app.containers.AdminPage.projects.all.publishedTab": "Opublikowano", "app.containers.AdminPage.projects.all.searchProjects": "Wyszukaj projekty", "app.containers.AdminPage.projects.all.yourProjects": "<PERSON>je projekty", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "Analiza AI", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "<PERSON><PERSON><PERSON>, m<PERSON><PERSON><PERSON><PERSON> odpowiednie pytania do swoich danych. Ta funkcja nie jest uwzględniona w Twoim bieżącym planie. Porozmawiaj ze swoim Government Success Managerem lub <PERSON>em, aby ją od<PERSON><PERSON><PERSON>.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "Ten wgląd obejmuje następujące pytania:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "Usuń pytanie", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "<PERSON>zy na pewno chcesz usunąć to pytanie?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "<PERSON>zy na pewno chcesz usunąć te podsumowania?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Twoje podsumowania tekstowe będą wyświetlane tutaj, ale obecnie nie masz jeszcze żadnych.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "Kliknij przycisk automatycznego podsumowania powyżej, a<PERSON> r<PERSON><PERSON>.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "w<PERSON><PERSON><PERSON> we<PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Zadawanie pytań dotyczących mniejszej liczby danych wejściowych prowadzi do większej dokładności. Ogranicz bieżący wybór danych wejściowych za pomocą tagów, wyszukiwania lub filtrów demograficznych.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "Pytania do", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Pytanie dotyczące wszystkich danych wejściowych", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Oceń jakość tego spostrzeżenia", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> filtry", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Podsum<PERSON>j", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Podsumowanie dla", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Podsumowanie dla wszystkich wejść", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "Dziękujemy za Twoją opinię", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "Sztuczna inteligencja nie może przetworzyć tak wielu danych wejściowych za jednym razem. Podziel je na mniejsze grupy.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "W ramach bieżącego planu możesz podsumować maksymalnie 30 danych wejściowych naraz. Porozmawiaj ze swoim GovSuccess Managerem lub administratorem, aby odblokować więcej.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Nasza platforma umożliwia eksplorowanie głównych tematów, podsumowywanie danych i badanie różnych perspektyw. <PERSON><PERSON><PERSON> szukasz konkretnych odpowiedzi lub spostrz<PERSON>, roz<PERSON>ż skorzystanie z funkcji \"Zadaj pytanie\", aby zanurz<PERSON>ć się głębiej poza podsumowanie.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "<PERSON><PERSON><PERSON>, sztuczna inteligencja może czasami generować informacje, które nie były wyraźnie obecne w oryginalnym zestawie danych.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Halucynacje:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "Sztuczna inteligencja może podkreślać pewne tematy lub pomysły bardziej niż inne, potencjalnie wypaczając ogólną interpretację.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Przesada:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Nasz system jest zoptymalizowany do obsługi 20-200 dobrze zdefiniowanych danych wejściowych w celu uzyskania najdokładniejszych wyników. Wraz ze wzrostem ilości danych poza ten zakres, podsumowanie może stać się bardziej ogólne i uogólnione. Nie oznacza to, że sztuczna inteligencja staje się \"mniej dokładna\", ale raczej, że skupi się na szerszych trendach i wzorcach. Aby uzyskać bardziej szczegółowe informacje, zalecamy korzystanie z funkcji (automatycznego) tagowania w celu segmentacji większych zbiorów danych na mniejsze, łatwiejsze w zarządzaniu podzbiory.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Ilość i dokładność danych:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "Zalecamy korzystanie z podsumowań generowanych przez sztuczną inteligencję jako punktu wyjścia do zrozumienia dużych zbiorów danych, ale nie jako ostatecznego słowa.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "Jak pracowa<PERSON> ze sztuczną inteligencją", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Dodaj wybrane wejścia do tagu", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "<PERSON><PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "Ta funkcja nie jest uwzględniona w Twoim bieżącym planie. Porozmawiaj ze swoim Government Success Managerem lub administratorem, aby ją od<PERSON><PERSON><PERSON>.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "Wszystkie wejścia", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "Wszystkie wejścia", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "Wszystkie tagi", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "<PERSON><PERSON>, ja to zro<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "<PERSON><PERSON> chcesz automatycznie przypisywać wejścia do swojego tagu?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "Istnieje <b>r<PERSON><PERSON><PERSON><PERSON> metod</b> automatycznego przypisywania danych wejściowych do tagów.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Użyj <b>przycisku automatycznego tagowania</b> , aby u<PERSON><PERSON><PERSON><PERSON> preferowaną metodę.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "<PERSON><PERSON><PERSON><PERSON> znacznik, aby prz<PERSON><PERSON> go do aktualnie wybranego wejścia.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "Tak, automatyczne oznaczanie", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Automatyczne oznaczanie", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Tagi automatyczne są automatycznie tworzone przez komputer. Możesz je zmienić lub usunąć w dowolnym momencie.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Automatyczne oznaczanie", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Wejścia już powiązane z tymi tagami nie będą ponownie klasyfikowane.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "Klasyfikacja opiera się wyłącznie na nazwie tagu. Wybierz odpowiednie słowa kluczowe, aby uzyskać najlepsze wyniki.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Tagi: <PERSON><PERSON><PERSON><PERSON> etykiety", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "Tworzysz tagi i ręcznie przypisujesz kilka wejść jako przykład, komputer przypisuje resztę", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Tagi: Na przykład", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "Podobne do \"Tagi: według etykiety\", ale ze zwiększoną dokładnością, ponieważ trenujesz system z dobrymi przykładami.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "<PERSON><PERSON><PERSON><PERSON>z<PERSON>i, komputer przypisuje wejścia", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Tagi: <PERSON><PERSON><PERSON><PERSON> etykiety", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "<PERSON><PERSON>ła to dobrze, gdy masz wstępnie zdefiniowany zestaw tagów lub gdy Twój projekt ma ograniczony zakres pod względem tagów.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Wykrywaj dane wejściowe ze znaczącym stosunkiem niechęci do sympatii.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Kontrowersyjny", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Usuń znacznik", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "<PERSON>zy na pewno chcesz usunąć ten tag?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "<PERSON>e pokazuj tego więcej", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "<PERSON><PERSON><PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "Dodaj nazwę", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Wybierz maksymalnie 9 tagów, mię<PERSON>zy które chcesz rozdzielić wejścia.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "Klasyfikacja jest oparta na danych wejściowych aktualnie przypisanych do tagów. Komputer spróbuje podążać za twoim przykładem.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Tagi: Na przykład", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "<PERSON>e masz jeszcze żadnych niestandardowych tagów.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "Komputer automatycznie wykrywa znaczniki i przypisuje je do twoich wejść.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Tagi: W pełni zautomatyzowany", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "D<PERSON>ła dobrze, gdy twoje projekty obejmują szeroki zakres tagów. Dobre miejsce do rozpoczęcia.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "Jak ch<PERSON>z się oznaczyć?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Wejścia bez znaczników", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Wykryj język każdego wejścia", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "Język", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "<PERSON>ru<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "Brak aktywnych filtrów", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Używaj tagów do dzielenia i filtrowania danych wejściowych, aby tworzyć dokładniejsze lub bardziej ukierunkowane podsumowania.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Tagi: Tagi platformy", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Przypisz istniejące tagi platformy, które autor wybrał podczas publikowania.", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Zalecane", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "Zmień nazwę tagu", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Nazwa", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "Oszczędzaj", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "Zmień nazwę tagu", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "<PERSON><PERSON><PERSON><PERSON> wszystko", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Przypisz pozytywny lub negatywny sentyment do każdego wejścia na podstawie tekstu.", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Nastroje", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Wykrywanie znaczników", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Użyj aktualnych filtrów", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "<PERSON><PERSON><PERSON> dane wejściowe chcesz oznaczyć?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Zadanie automatycznego tagowania", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Kontrowersyjny", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "Niestandardowy", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Zakończono na", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "<PERSON>e powiodło się", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "Na przykład", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "W toku", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "Według etykiety", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "Język", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "Znacznik NLP", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "Brak ostatnio wykonanych zadań AI", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Znacznik platformy", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "W kolejce", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Nastroje", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Rozpoczęty o godz.", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Udało się", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "<PERSON><PERSON><PERSON>wu<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "<PERSON><PERSON><PERSON><PERSON><PERSON> przy", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "Powyżej", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "Wszystkie", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "Autor", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "Poniżej", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "Rok urodzenia", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "Miejsce zamieszkania", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Zaangażowanie", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "Filtry", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "Od", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "<PERSON>jś<PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "Liczba komentarzy", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Liczba reakcji", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "Liczba głosów", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "Do", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Dodaj do analizy", "app.containers.AdminPage.projects.project.analysis.anonymous": "Anonimowy wkład", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Autorzy według wieku", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Autorzy według miejsca zamieszkania", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "<PERSON><PERSON><PERSON> w tle", "app.containers.AdminPage.projects.project.analysis.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "<PERSON><PERSON><PERSON> miejsca zamieszkania jest zbyt duży, aby go wyświ<PERSON>lić", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "<PERSON><PERSON><PERSON><PERSON> od<PERSON>", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.end": "Koniec", "app.containers.AdminPage.projects.project.analysis.filter": "Wyświetlaj tylko wejścia z tą wartością", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Ukryj od<PERSON>wiedzi bez odpowiedzi", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Automatyczne podglądy", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Wartości kolumn", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "Is<PERSON><PERSON><PERSON> {count} przypadków tej kombinacji.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "<PERSON>e lubi", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Eksploruj", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "Fałsz", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Następna mapa cieplna", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Następny wgląd", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "<PERSON>e jest to istotne statystycznie spostrzeżenie.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Automatyczny wgląd nie jest dostępny dla projektów z mniej niż 30 uczestnikami.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "Uczestnicy", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Poprzednia mapa cieplna", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Poprzedni wgląd", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "Wartości wierszy", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Statystycznie istotny wgląd.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Podsum<PERSON>j", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Tagi analizy", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "Prawda", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "Jednostki", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "Zobacz wszystkie spostrzeżenia", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Wyświetl automatyczne statystyki", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Wejścia bez znaczników", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "Przesłano nieprawidłowy plik shapefile i nie można go wyświetlić.", "app.containers.AdminPage.projects.project.analysis.limit": "Limit", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "Główne pytanie", "app.containers.AdminPage.projects.project.analysis.manageInput": "Zarządzaj danymi wejściowymi", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Następny wykres", "app.containers.AdminPage.projects.project.analysis.noAnswer": "<PERSON><PERSON> o<PERSON><PERSON>wiedzi", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "<PERSON><PERSON> ud<PERSON><PERSON> odpowiedzi.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "Nie załadowano pliku shapefile.", "app.containers.AdminPage.projects.project.analysis.noInputs": "Żadne wejścia nie odpowiadają twoim aktualnym filtrom", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Poprzedni wykres", "app.containers.AdminPage.projects.project.analysis.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.remove": "Usuń", "app.containers.AdminPage.projects.project.analysis.removeFilter": "Usuń filtr", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "Usuń filtr", "app.containers.AdminPage.projects.project.analysis.search": "Szukaj", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Pliki Shapefiles są tutaj wyświetlane w formacie GeoJSON. W związku z tym stylizacja w oryginalnym pliku może nie być wyświetlana poprawnie.", "app.containers.AdminPage.projects.project.analysis.start": "Rozpocznij", "app.containers.AdminPage.projects.project.analysis.supportArticle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "Zobacz wszystkie pytania", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "Wyświetl wybrane pytania", "app.containers.AdminPage.projects.project.analysis.votes": "Głosy", "app.containers.AdminPage.widgets.copied": "Skopiowany do schowka", "app.containers.AdminPage.widgets.copyToClipboard": "Skopiuj kod", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Skopiuj kod HTML", "app.containers.AdminPage.widgets.fieldAccentColor": "<PERSON><PERSON> widgetu", "app.containers.AdminPage.widgets.fieldBackgroundColor": "<PERSON><PERSON> tła widżetu", "app.containers.AdminPage.widgets.fieldButtonText": "Tekst przycisku", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Przyłącz się teraz", "app.containers.AdminPage.widgets.fieldFont": "Czcionka", "app.containers.AdminPage.widgets.fieldFontDescription": "To musi być istniejąca nazwa czcionki w {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "Roz<PERSON><PERSON> (piksele)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Podtytuł nagłówka", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "<PERSON><PERSON>ł<PERSON><PERSON> p<PERSON>ł", "app.containers.AdminPage.widgets.fieldHeaderText": "Tekst nagłówka", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Nasza platforma uczestnictwa", "app.containers.AdminPage.widgets.fieldHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON> (w pixelach)", "app.containers.AdminPage.widgets.fieldInputsLimit": "<PERSON><PERSON><PERSON><PERSON> inicjatyw", "app.containers.AdminPage.widgets.fieldProjects": "Projekty", "app.containers.AdminPage.widgets.fieldRelativeLink": "Prowadzi do", "app.containers.AdminPage.widgets.fieldShowFooter": "Pokaż przycisk", "app.containers.AdminPage.widgets.fieldShowHeader": "Pokaż nagłówek", "app.containers.AdminPage.widgets.fieldShowLogo": "Pokaż logo", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "<PERSON><PERSON> tła strony", "app.containers.AdminPage.widgets.fieldSort": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldTextColor": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldTopics": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Strona główna", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Możesz skopiować ten kod HTML i wkleić go w tej części swojej strony, w której chcesz dodać swój widżet.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Kod HTML widżetu", "app.containers.AdminPage.widgets.previewTitle": "Podgląd", "app.containers.AdminPage.widgets.settingsTitle": "Ustawienia", "app.containers.AdminPage.widgets.sortNewest": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.sortPopular": "Najwięcej głosów", "app.containers.AdminPage.widgets.sortTrending": "<PERSON> czasie", "app.containers.AdminPage.widgets.subtitleWidgets": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Rozmiar", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Nagłówek i stopka", "app.containers.AdminPage.widgets.titleInputSelection": "Wybór inicjatyw", "app.containers.AdminPage.widgets.titleStyle": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.PageTitle": "Strona główna", "app.containers.ContentBuilder.homepage.SaveError": "Coś poszło nie tak podczas zapisywania strony głównej.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "<PERSON><PERSON><PERSON> kol<PERSON>", "app.containers.ContentBuilder.homepage.bannerImage": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.bannerSubtext": "Podtekst banera", "app.containers.ContentBuilder.homepage.bannerText": "<PERSON><PERSON><PERSON> banera", "app.containers.ContentBuilder.homepage.button": "Przycisk", "app.containers.ContentBuilder.homepage.chooseLayout": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Twoja obecna licencja nie obejmuje dostosowywania ustawień innych niż obraz i tekst na banerze strony głównej. Skontaktuj się ze swoim menedżerem GovSuccess, aby dowiedzieć się więcej na ten temat.", "app.containers.ContentBuilder.homepage.customized_button": "Niestandardowy", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Tekst przycisku", "app.containers.ContentBuilder.homepage.customized_button_url_label": "Przycisk łącza", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Wyświetla 3 najbliższe wydarzenia na Twojej platformie.", "app.containers.ContentBuilder.homepage.eventsDescription": "Wyświetla 3 najbliższe wydarzenia na Twojej platformie.", "app.containers.ContentBuilder.homepage.fixedRatio": "Baner o stałym współczynniku", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Ten typ banera działa najlepiej z obrazami, kt<PERSON>re nie powinny by<PERSON> p<PERSON>, takimi jak obrazy z tekstem, logo lub okre<PERSON>lonymi elementami, które są kluczowe dla twoich obywateli. Ten baner jest zastępowany jednolitą ramką w kolorze podstawowym, gdy użytkownicy są zalogowani. Możesz ustawić ten kolor w ustawieniach ogólnych. Więcej informacji na temat zalecanego wykorzystania obrazów znajdziesz na naszej stronie {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "baza wiedzy", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "Baner o pełnej szerokości", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Ten baner rozciąga się na całą szerokość, zapewniając świetny efekt wizualny. Obraz będzie starał się pokryć jak najwięcej miejsca, przez co nie zawsze będzie widoczny przez cały czas. Możesz połączyć ten baner z nakładką w dowolnym kolorze. Więcej informacji na temat zalecanego użycia obrazu znajdziesz na naszej stronie {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "baza wiedzy", "app.containers.ContentBuilder.homepage.imageOverlayColor": "<PERSON><PERSON> o<PERSON>", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "<PERSON><PERSON><PERSON>d<PERSON> obrazu", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "Nieprawidłowy adres URL", "app.containers.ContentBuilder.homepage.no_button": "Brak przycisku", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Niezarejestrowani użytkownicy", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "<PERSON><PERSON><PERSON><PERSON> nakładk<PERSON>", "app.containers.ContentBuilder.homepage.projectsDescription": "<PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kolejno<PERSON>ć wyświetlania projektów, zmień ich kolejność na stronie {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "Strona projektów", "app.containers.ContentBuilder.homepage.registeredUsersView": "Zarejestrowani użytkownicy", "app.containers.ContentBuilder.homepage.showAvatars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>ary", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Pokaż zdjęcia profilowe uczestników i ich liczbę niezarejestrowanym odwiedzającym.", "app.containers.ContentBuilder.homepage.sign_up_button": "Zarejestruj się", "app.containers.ContentBuilder.homepage.signedInDescription": "W ten sposób zarejestrowani użytkownicy widzą baner.", "app.containers.ContentBuilder.homepage.signedOutDescription": "W ten sposób odwiedza<PERSON>, kt<PERSON><PERSON>y nie są zarejestrowani na platformie, widz<PERSON> baner.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "Baner ten jest szczególnie przydatny w przypadku obrazów, które nie współgrają dobrze z tekstem tytułu, podtytułu lub przycisku. Elementy te zostaną przesunięte poniżej banera. Więcej informacji na temat zalecanego użycia obrazów można znaleźć na naszej stronie {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "baza wiedzy", "app.containers.ContentBuilder.homepage.twoRowLayout": "Dwa rzędy", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> (piksele)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, na jakiej osadzona zawartość ma być wyświetlana na stronie (w pikselach).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Krótki opis treści, k<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "Podanie tych informacji jest przydatne dla użytkowników korzystających z czytnika ekranu lub innych technologii wspomagających.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "<PERSON><PERSON> strony <PERSON>ow<PERSON>", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "Pełny adres URL strony internetowej, którą chcesz osadzić.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Wyświetlaj zawartość zewnętrznej witryny na swojej stronie w ramce HTML iFrame. <PERSON><PERSON><PERSON><PERSON><PERSON>, że nie każda strona może zostać osadzona. <PERSON><PERSON><PERSON> masz problemy z osadzeniem strony, sprawd<PERSON> u jej w<PERSON>la, czy jest ona skonfigurowana tak, by <PERSON><PERSON><PERSON><PERSON> na osadzanie.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Odwiedź naszą stronę wsparcia", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nie można osadzić tej treści. {visitLinkMessage} , aby do<PERSON><PERSON><PERSON> się więcej.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Wprowadź prawidłowy adres internetowy, na przykład https://example.com.", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Osadzony", "app.containers.admin.ContentBuilder.accordionMultiloc": "Akordeon", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Otwórz domyślnie", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Tekst", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Jest to r<PERSON><PERSON><PERSON> z<PERSON>ość akordeonu. Możesz ją edytować i formatować za pomocą edytora w panelu po prawej stronie.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.buttonMultiloc": "Przycisk", "app.containers.admin.ContentBuilder.delete": "Usuń", "app.containers.admin.ContentBuilder.error": "błąd", "app.containers.admin.ContentBuilder.errorMessage": "Wystą<PERSON>ł błąd w treści {locale}, <PERSON><PERSON><PERSON> ten błąd, aby móc z<PERSON> z<PERSON>y", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Ukryj awatary uczestnictwa", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "Jest to <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, jak oceniasz zarządzanie i usługi publiczne.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "<PERSON>ź udział w ankiecie", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Pomóż nam lepiej Ci służyć", "app.containers.admin.ContentBuilder.homepage.default": "domyślny", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "Wyd<PERSON>zen<PERSON>", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "Wyd<PERSON>zen<PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Wezwanie do działania", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Opis", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "Adres URL przycisku głównego", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Tekst przycisku głównego", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "Adres URL przycisku pomocniczego", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Tekst przycisku pomocniczego", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "<PERSON><PERSON> s<PERSON> g<PERSON>", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "<PERSON><PERSON> s<PERSON> g<PERSON>", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Karty graficzne i tekstowe", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 kolumna", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projekty", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Włącz propozycje w sekcji \"Propozycje\" w panelu administracyjnym, aby odblokować je na stronie głównej.", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Pro<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageMultiloc": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Krótki opis obrazu", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "Dodanie \"tekstu alternatywnego\" do obrazów jest ważne, aby Twoja platforma była dostępna dla użytkowników korzystających z czytników ekranu.", "app.containers.admin.ContentBuilder.participationBox": "Skrzynka uczestnictwa", "app.containers.admin.ContentBuilder.textMultiloc": "Tekst", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 kolumny", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 kolumny", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 kolumny o szerokości 30% i 60% szerokości", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 kolumny o szerokości 60% i 30% szerokości", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 kolumny parzyste", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "Najbardziej reaktywne wejścia", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Faza pomysłu", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "Nie ma dostępnych danych wejściowych dla tego projektu lub etapu.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "Liczba we<PERSON>ść", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Pokaż więcej", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Łączna liczba wejść: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Zwiń długi tekst", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "Nie ma dostępnych danych wejściowych dla tego projektu lub etapu.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Wybierz fazę", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Autor", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "Głosy", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "<PERSON>jś<PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Stawka rejestracyjna", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Rejestracje", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Oś czasu uczestników", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "<PERSON><PERSON><PERSON><PERSON><PERSON>, że liczba uczestników może nie być w pełni dokładna, poniew<PERSON>ż niektóre dane są rejestrowane w zewnętrznej an<PERSON>cie, której nie śledzimy.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "<PERSON><PERSON><PERSON> dat", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.noData": "Dla wybranych filtrów nie ma dostępnych danych.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Źródła ruchu", "app.containers.admin.ReportBuilder.charts.users": "Użytkownicy", "app.containers.admin.ReportBuilder.charts.usersByAge": "Użytkownicy według wieku", "app.containers.admin.ReportBuilder.charts.usersByGender": "Użytkownicy według płci", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "<PERSON><PERSON> o<PERSON>", "app.containers.admin.ReportBuilder.managerLabel1": "Kierownik projektu", "app.containers.admin.ReportBuilder.periodLabel1": "<PERSON><PERSON>", "app.containers.admin.ReportBuilder.projectLabel1": "Projekt", "app.containers.admin.ReportBuilder.quarterReport1": "Raport Monitora Społeczności: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Rozpocznij", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Kup 1 dodatkowe miejsce", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Po<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Czy na pewno chcesz nadać 1 osobie prawa zarządcy?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Nadaj uprawnienia menedżera", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Osiągnąłeś limit uwzględnionych miejsc w ramach swojego planu, {noOfSeats} dodatkowe {noOfSeats, plural, one {miejsca} other {miejsca}} zost<PERSON>ą dodane.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Dodaj status", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Domyślnych statusów nie można usunąć.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Usuń", "app.containers.admin.ideaStatuses.all.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Dodaj status", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Statystyki aktualnie przypisane do danej inicjatywy nie mogą zostać usunięte. Moż<PERSON>z <PERSON>un<PERSON>ć/z<PERSON><PERSON><PERSON> stan dla istniejących inicjatyw w zakładce {manageTab}.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Status ten nie może być usunięty ani przeniesiony.", "app.containers.admin.ideaStatuses.all.manage": "Zarządzaj", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Konfigurowanie niestandardowych statusów wprowadzania danych nie jest uwzględnione w Twoim bieżącym planie. Porozmawiaj ze swoim Government Success Managerem lub administratorem, aby ją od<PERSON><PERSON>.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Zarządzaj <PERSON>em, który można przypisać do wkładu uczestników w ramach projektu. Status jest widoczny publicznie i pomaga w informowaniu uczestników.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Zarządzaj <PERSON>em, który można przypisać do propozycji w ramach projektu. Status jest widoczny publicznie i pomaga w informowaniu uczestników.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> we<PERSON>", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Edytuj statusy propozycji", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Wybrane do realizacji lub następnego etapu", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Zatwierdzone", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Przekazano oficjalną informację zwrotną", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>ź", "app.containers.admin.ideaStatuses.form.category": "Kategoria", "app.containers.admin.ideaStatuses.form.categoryDescription": "<PERSON>sz<PERSON> wy<PERSON>ć kategor<PERSON>, która najlepiej odzwierciedla Twój status. Wybór ten pomoże naszemu narzędziu analitycznemu dokładniej przetwarzać i analizować dane.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Nie pasuje do żadnej z pozostałych opcji", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldColor": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescription": "Opis statusu", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Podaj opis statusu dla wszystkich języków", "app.containers.admin.ideaStatuses.form.fieldTitle": "Nazwa statusu", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Podaj nazwę statusu dla wszystkich języków", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Pomyślnie wdrożony", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "Wniosek nie kwalifikuje się", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "<PERSON>e kwalifikuje się", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Niekwalifikujący się lub niewybrany do dalszego etapu", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "<PERSON><PERSON> w<PERSON>", "app.containers.admin.ideaStatuses.form.saveStatus": "Zapisz status", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Rozważany do wdrożenia lub następnego etapu", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Rozpatrywany", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ale jeszcze nie rozpatrzony", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Prz<PERSON>rz<PERSON>", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Zarządzaj inicjatywami i i ich statusami.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Zarządzanie inicjatywami | Platforma partycypacyjna {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Przekazuj informacje zwrotne, dodawaj tematy i przenoś inicjatywy z jednego projektu do drugiego", "app.containers.admin.ideas.all.inputManagerPageTitle": "Zarządzanie inicjatywami", "app.containers.admin.ideas.all.tabOverview": "Przegląd", "app.containers.admin.import.importInputs": "<PERSON><PERSON>rt<PERSON><PERSON> da<PERSON>", "app.containers.admin.import.importNoLongerAvailable3": "Ta funkcja nie jest już dostępna tutaj. <PERSON><PERSON> dane wejściowe do fazy tworzenia pomysłów, przejdź do fazy i wybierz opcję \"Importuj\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 dodatkowe miejsce dla administratora} other {# dodatkowe miejsca dla administratorów}} i {managerSeats, plural, one {1 dodatkowe miejsce dla menedżera} other {# dodatkowe miejsca dla menedżerów}} zostaną dodane ponad limit.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 dodatkowe miejsce dla administratora zostanie dodane ponad limit} other {# dodatkowe miejsca dla administratorów zostaną dodane ponad limit}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 dodatkowe miejsce dla menedżera zostanie dodane ponad limit} other {# dodatkowe miejsca dla menedżerów zostaną dodane ponad limit}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Potwierdzenie i wysłanie zaproszeń", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Potwierdzić wpływ na wykorzystanie fotelików", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Osiągnąłeś limit dostępnych miejsc w ramach swojego planu.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Administratorzy i kierownicy tego projektu", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Tylko administratorzy i współpracownicy", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Tylko administratorzy platformy, kierownicy folderów i kierownicy projektów mogą podjąć akcję", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Ka<PERSON><PERSON>", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "W konkursie może wziąć ud<PERSON>ł ka<PERSON>, także niezarejestrowani użytkownicy.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Niektóre grupy użytkowników", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Użytkownicy należący do określonej grupy (grup) użytkowników mogą brać w nich udział. Grupami użytkowników można zarządzać w zakładce \"Użytkownicy\".", "app.containers.admin.project.permissions.viewingRightsTitle": "Kto może zobaczyć ten projekt?", "app.containers.phaseConfig.enableSimilarInputDetection": "Włącz wykrywanie podobnych wejść", "app.containers.phaseConfig.similarInputDetectionTitle": "Wykrywanie podobnych danych wejściowych", "app.containers.phaseConfig.similarInputDetectionTooltip": "Pokaż uczestnikom podobne dane wejściowe podczas pisania, aby uniknąć duplikatów.", "app.containers.phaseConfig.similarityThresholdBody": "Pr<PERSON>g <PERSON> (ciało)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> to, jak podobne muszą być dwa opisy, aby zostały oznaczone jako podobne. Użyj wartości od 0 (ścisłe) do 1 (łagodne). Niższe wartości zwracają mniej, ale dokładniejsze dopasowania.", "app.containers.phaseConfig.similarityThresholdTitle": "Próg <PERSON>ń<PERSON>wa (tytuł)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> to, jak podobne muszą być dwa tytuły, aby zostały oznaczone jako podobne. Użyj wartości od 0 (ścisłe) do 1 (łagodne). Niższe wartości zwracają mniej, ale dokładniejszych dopasowań.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "Ta funkcja jest dostępna w ramach oferty wczesnego dostępu do 30 czerwca 2025 roku. <PERSON><PERSON><PERSON> ch<PERSON>z nadal z niej korzystać po tym terminie, skontaktuj się ze swoim Government Success Managerem lub <PERSON>em, aby o<PERSON><PERSON><PERSON> opcje aktywacji.", "app.containers.survey.sentiment.noAnswers2": "Na chwilę obecną brak odpowiedzi.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 komentarzy} one {1 komentarz} other {# komentarzy}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Uczestnicy to użytkownicy lub odwiedzający, którzy wzięli udział w projekcie, opublikowali lub weszli w interakcję z propozycją lub uczestniczyli w wydarzeniach.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "Uczestnicy", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Wskaźnik uczestnictwa", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Odsetek odwiedzających, którzy stają się uczestnikami.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Uczestnicy ogółem", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Zautomatyzowane wiadomości e-mail", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "Z kampanii {quantity}", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "<PERSON><PERSON>ani<PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Niestandardowe e-maile", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Całkowita liczba wysłanych e-maili", "app.modules.commercial.analytics.admin.components.Events.completed": "Zakończono", "app.modules.commercial.analytics.admin.components.Events.events": "Wyd<PERSON>zen<PERSON>", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Łącznie dodane wydarzenia", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Nadchodzące", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Przyjęte", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Zaproszenia", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Oczek<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Całkowita liczba wysłanych zaproszeń", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Przejdź do \"Zarządzanie inicjatywami\"", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Aktywny", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Proje<PERSON><PERSON>, które nie są zarchiwizowane i widoczne w tabeli 'Zarchiwizowane' na stronie głównej", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Zarchiwizowany", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Projekty robocze", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Zakończone", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Wszystkie zarchiwizowane projekty i aktywne projekty na osi czasu, które zostały zakończone, są zliczane tutaj", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projekty", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Wszystkie projekty", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Liczba projektów, które są widoczne na platformie", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Nowe rejestracje", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Wskaźnik rejestracji", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "<PERSON><PERSON>, którzy stają się zarejestrowanymi użytkownikami.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Rejestracje", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Rejestracje <PERSON>", "app.modules.commercial.analytics.admin.components.Tab": "Odwied<PERSON>j<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Ostatnie 30 dni:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Ostatnie 7 dni:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Odsłony na odwiedziny", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Czas trwania odwiedzin", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Odwied<PERSON>j<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Visitors\" to liczba unikalnych odwiedzających. <PERSON><PERSON><PERSON> dana osoba odwiedza platformę wielokrotnie, jest liczona tylko raz.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Odwiedziny", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Odwiedziny\" to liczba sesji. <PERSON><PERSON><PERSON> osoba odwiedziła platformę wielokrotnie, każda wizyta jest liczona.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Wczoraj:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Licz", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Język", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Liczba odwiedzających", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Odsetek odwiedzających", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "Odsyłacz", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "k<PERSON><PERSON><PERSON> tutaj", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Odsyłacze", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> pełn<PERSON> listę osó<PERSON>, {referrerListButton}.", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Odwied<PERSON>j<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Źródła ruchu", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Odwiedziny", "app.modules.commercial.analytics.admin.components.totalParticipants": "Uczestnicy ogółem", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Nie ma jeszcze danych dotyczących odwiedzających.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Zmieniliśmy sposób gromadzenia i wyświetlania danych odwiedzających. W rezultacie dane odwiedzających są dokładniejsze i dostępnych jest więcej rodzajów danych, przy jednoczesnym zachowaniu zgodności z RODO. Podczas gdy dane używane na osi czasu odwiedzających sięgają wstecz dłużej, zacz<PERSON>liś<PERSON> gromadzić dane dla \"Czasu trwania wizyty\", \"Odsłony na wizytę\" i innych wykresów dopiero w listopadzie 2024 r., Więc wcześniej żadne dane nie są dostępne. Dlatego jeśli wybierzesz dane sprzed listopada 2024 r., pamiętaj, że niektóre wykresy mogą być puste lub wyglądać dziwnie.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Dostawy wiadomości e-mail w czasie", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Uczestnicy na przestrzeni czasu", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Rejestracje w czasie", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Data", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statystyka", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Ogólne statystyki", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Wizyty i odwiedzający w czasie", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Ogółem w okresie", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Hrabia", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Język", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "<PERSON><PERSON>ani<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Bezpośrednie wejście", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "Liczba odwiedzających", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Odsetek wizyt", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "Polecający", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Witryny odsyłające", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "<PERSON><PERSON><PERSON>wark<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "<PERSON><PERSON><PERSON> s<PERSON>ściowe", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "Przekierowania SSO", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Źródło ruchu", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Liczba wizyt", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Strony internetowe", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Moż<PERSON>z usunąć tę flagę, wybierając ten element i klikając przycisk Usuń u góry. Pojawi się on ponownie w zakładkach Przejrzane lub Nieprzejrzane", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "<PERSON><PERSON><PERSON><PERSON> nieod<PERSON>wied<PERSON> tre<PERSON>.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Nie ma żadnych postów zgłoszonych do przeglądu przez społeczność lub oznaczonych jako nieodpowiednie treści przez nasz system przetwarzania języka naturalnego.", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Usuń {numberOfItems, plural, one {ostrzeżenie o zawartości} few {# ostrzeżenia o zawartości} many {# ostrzeżeń o zawartości} other {# ostrzeżenia o zawartości}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Zgłoszone jako nieodpowiednie przez użytkownika platformy.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Ostrzeżenia dotyczące treści", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "<PERSON><PERSON> raportu", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Strony wyświetlane na pasku nawigacyjnym", "app.modules.navbar.admin.containers.addProject": "Dodaj projekt do paska nawigacyjnego", "app.modules.navbar.admin.containers.createCustomPageButton": "Utwórz włas<PERSON>ą stronę", "app.modules.navbar.admin.containers.deletePageConfirmation": "<PERSON>zy na pewno chcesz usunąć tę stronę? Nie można tego cofnąć. Możesz również usunąć stronę z paska nawigacyjnego, jeśli nie jesteś jeszcze gotowy, aby ją usuną<PERSON>.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Możesz dodać maksymalnie 5 elementów do paska nawigacji", "app.modules.navbar.admin.containers.pageHeader": "Strony i menu", "app.modules.navbar.admin.containers.pageSubtitle": "Twój pasek nawigacyjny może wyświetlać do pięciu stron oprócz strony głównej i stron projektów. Możesz zmieniać nazwy pozycji menu, zmie<PERSON>ć kolejność i dodawać nowe strony z własną zawartością.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widżety", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Użyj ikony ☰ poniżej, aby prz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zawartość AI do raportu.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "Nie ma dostępnych spostrzeżeń AI. Możesz je utworzyć w swoim projekcie.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Przejdź do projektu", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "<PERSON><PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Wybierz fazę", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Odblokuj analizę AI", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Umieść w raporcie spostrzeżenia wygenerowane przez sztuczną inteligencję", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Raportuj szybciej dzięki sztucznej inteligencji", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Raportowanie za pomocą sztucznej inteligencji nie jest uwzględnione w Twoim obecnym planie. Porozmawiaj ze swoim menedżerem ds. sukcesów w administracji publicznej, aby odblokować tę funkcję.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "<PERSON>e jest to uwzględnione w Twoim obecnym planie. Skontaktuj się z Government Success Managerem lub administratorem, aby ją od<PERSON><PERSON><PERSON>.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Grupuj według pola rejestracji", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Grupuj według pytań ankiety", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "<PERSON><PERSON> grupowy", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Grupuj odpowiedzi ankietowe według pól rejestracji (płeć, lokalizacja, wiek itp.) lub innych pytań ankietowych.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "Brak", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "<PERSON><PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "<PERSON> rejestracji", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Faza bad<PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "<PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "<PERSON>zy na pewno chcesz to usunąć?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "Usuń", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "<PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Opublikuj swój komentarz", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "Oszczędzaj", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "Napisz swój komentarz tutaj", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Kliknij poniższe przyciski, aby ś<PERSON><PERSON><PERSON> lub przestać śledzić. Liczba projektów jest podana w nawiasach.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "<PERSON> <PERSON><PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Postępuj zgodnie z preferencjami", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "Obecnie nie ma aktywnych projektów, biorąc pod uwagę Twoje preferencje śledzenia.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "Ten widżet pokazuje projekty powiązane z \"obszarami\", które śledzi użytkownik. <PERSON><PERSON><PERSON><PERSON><PERSON>, że Twoja platforma może używać innej nazwy dla \"obszarów\" - z<PERSON><PERSON><PERSON> <PERSON><PERSON> \"Obszary\" w ustawieniach platformy. Jeśli użytkownik nie obserwuje jeszcze żadnych obszarów, widżet pokaże dostępne obszary do obserwowania. W tym przypadku widżet pokaże maksymalnie 100 obszarów.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "Brak opublikowanych projektów lub folderów", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Opublikowane projekty i foldery", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "Ten widżet wyświetli projekty i foldery, które są aktualnie opublikowane, zgodnie z kolejnością zdefiniowaną na stronie projektów. To zachowanie jest takie samo jak w przypadku zakładki \"aktywne\" widżetu \"starsze\" projekty.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "Nie wybrano żadnych projektów ani folderów", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "<PERSON><PERSON>bierz projekty lub foldery", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Wybrane projekty i foldery", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "Za pomocą tego widżetu możesz wybrać i określić kolejn<PERSON>, w jakiej projekty lub foldery mają być wyświetlane użytkownikom.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projekty", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "odwiedź nasze centrum wsparcia", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Więcej informacji na temat zalecanych rozdzielczości obrazu można znaleźć na stronie {supportPageLink}."}
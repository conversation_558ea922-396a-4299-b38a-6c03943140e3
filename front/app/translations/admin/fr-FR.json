{"UI.FormComponents.required": "obligatoire", "app.Admin.ManagementFeed.action": "Action", "app.Admin.ManagementFeed.after": "<PERSON><PERSON>", "app.Admin.ManagementFeed.before": "Avant", "app.Admin.ManagementFeed.changed": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.created": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.date": "Date", "app.Admin.ManagementFeed.deleted": "Supprimé", "app.Admin.ManagementFeed.folder": "Dossier", "app.Admin.ManagementFeed.idea": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.in": "dans le projet {project}", "app.Admin.ManagementFeed.item": "Objet", "app.Admin.ManagementFeed.key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.managementFeedNudge": "L'accès au journal d'audit n'est pas inclus dans votre licence actuelle. Contactez votre Spécialiste en participation pour en savoir plus.", "app.Admin.ManagementFeed.noActivityFound": "Aucune activité trouvée", "app.Admin.ManagementFeed.phase": "Phase", "app.Admin.ManagementFeed.project": "Projet", "app.Admin.ManagementFeed.projectReviewApproved": "Projet approuvé", "app.Admin.ManagementFeed.projectReviewRequested": "<PERSON><PERSON><PERSON> de révision d'un projet", "app.Admin.ManagementFeed.title": "Journal d'audit", "app.Admin.ManagementFeed.user": "Utilisa<PERSON>ur", "app.Admin.ManagementFeed.userPlaceholder": "Sélectionnez un utilisateur", "app.Admin.ManagementFeed.value": "<PERSON><PERSON>", "app.Admin.ManagementFeed.viewDetails": "Voir les détails", "app.Admin.ManagementFeed.warning": "Fonctionnalité expérimentale : une liste des actions effectuées par les administrateurs ou les gestionnaires au cours des 30 derniers jours. Tous les types d'actions ne sont pas inclus.", "app.Admin.Moderation.managementFeed": "Journal d'audit", "app.Admin.Moderation.participationFeed": "Flux de participation", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "Êtes-vous sûr(e) ?", "app.components.Admin.Campaigns.clicked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deleteCampaignButton": "Supprimer la campagne", "app.components.Admin.Campaigns.deliveryStatus_accepted": "Accepté", "app.components.Admin.Campaigns.deliveryStatus_bounced": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_clicked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "Il indique le nombre de destinataires ayant cliqué sur un lien dans le courrier électronique. Veuillez noter que certains systèmes de sécurité peuvent suivre les liens automatiquement pour les analyser, ce qui peut entraîner de faux clics.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "Distribué ", "app.components.Admin.Campaigns.deliveryStatus_failed": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_opened": "Ouvert", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "Il indique le nombre de destinataires ayant ouvert l'e-mail. <PERSON><PERSON><PERSON>z noter que certains systèmes de sécurité (comme Microsoft Defender) peuvent précharger le contenu à analyser, ce qui peut entraîner de fausses ouvertures.", "app.components.Admin.Campaigns.deliveryStatus_sent": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.draft": "Brouillon", "app.components.Admin.Campaigns.from": "De", "app.components.Admin.Campaigns.manageButtonLabel": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.opened": "Ouvert", "app.components.Admin.Campaigns.project": "Projet", "app.components.Admin.Campaigns.recipientsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.reply_to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.sent": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.statsButton": "Statistiques", "app.components.Admin.Campaigns.subject": "Sujet", "app.components.Admin.Campaigns.to": "À", "app.components.Admin.ImageCropper.cropFinalSentence": "Voir aussi : {link}.", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "Conservez le contenu essentiel à l’intérieur des pointillés pour qu’il reste toujours visible.", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "3:1 sur mobile (seule la zone entre les pointillés est visible)", "app.components.Admin.ImageCropper.cropSentenceOne": "L'image est recadrée automatiquement :", "app.components.Admin.ImageCropper.cropSentenceTwo": "{aspect} sur ordinateur (largeur entièrement visible)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "ratio recommandé", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Texte de la bannière pour les utilisateurs connectés", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Attention : la couleur que vous avez sélectionné n’est pas assez contrastée. <PERSON>la peut entraîner des problèmes de lecture. Choisissez une couleur plus foncée pour optimiser la lisibilité.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Ajouter des événements à la barre de navigation", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "Ajouter un lien vers tous les événements du projet dans la barre de navigation", "app.components.AdminPage.SettingsPage.eventsSection": "Événements", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Section personnalisée de la page d’accueil", "app.components.AnonymousPostingToggle.userAnonymity": "Anonymat de l'utilisateur", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Les auteurs pourront masquer leur identité aux autres utilisateurs, aux gestionnaires de projet et aux administrateurs. <PERSON><PERSON><PERSON><PERSON>, leurs contributions pourront toujours être modérées.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Permettre aux utilisateurs de participer de façon anonyme", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Les utilisateurs pourront choisir de participer avec leur vrai nom ou de manière anonyme s'ils le souhaitent. Cependant, tous les utilisateurs devront toujours se conformer aux critères de participation définis dans l'onglet « Droits d'accès » pour pouvoir soumettre leurs contributions. Il est important de noter que dans le cas d'une participation anonyme, les données du profil utilisateur ne seront pas incluses dans l'exportation des données de participation.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Pour en savoir plus sur la participation anonyme, consultez notre {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "article d'assistance", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/fr/articles/7946486-activer-la-participation-anonyme", "app.components.BillingWarning.billingWarning": "Si des sièges supplémentaires sont ajoutés, votre facturation sera adaptée. Contactez votre Spécialiste en participation Go Vocal pour en savoir plus.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Merci d'avoir complété l'enquête ! N'hésitez pas à y participer à nouveau au prochain trimestre.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Télécharger au format PDF", "app.components.FormSync.downloadExcelTemplate": "Télécharger un modèle Excel", "app.components.FormSync.downloadExcelTemplateTooltip2": "Les modèles Excel n’incluent pas les questions de classement, les questions matricielles, les questions de téléchargement de fichiers et les questions cartographiques (placer un repère, dessiner un tracé, délimiter une zone, téléchargement de fichier ESRI), car elles ne sont actuellement pas prises en charge pour l’importation en masse.", "app.components.ProjectTemplatePreview.close": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.createProject": "Créer un projet", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "<PERSON><PERSON>er un projet basé sur le modèle ''{templateTitle}''.", "app.components.ProjectTemplatePreview.goBack": "Retour", "app.components.ProjectTemplatePreview.goBackTo": "Retour à la page {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "Expert Go Vocal", "app.components.ProjectTemplatePreview.infoboxLine1": "Vous voulez utiliser ce modèle pour votre projet de participation ?", "app.components.ProjectTemplatePreview.infoboxLine2": "Adressez-vous à la personne responsable de l'administration de votre ville ou contactez un {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Dossier du projet", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "La date sélectionnée n'est pas valide. Veuillez fournir une date dans le format suivant : AAAA-MM-JJ", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Veuillez choisir une date de début de projet", "app.components.ProjectTemplatePreview.projectStartDate": "La date de début de votre projet", "app.components.ProjectTemplatePreview.projectTitle": "Le titre de votre projet", "app.components.ProjectTemplatePreview.projectTitleError": "Veuillez taper le titre de votre projet", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Veuillez saisir un titre de projet pour toutes les langues", "app.components.ProjectTemplatePreview.projectsOverviewPage": "page d'aperçu des projets", "app.components.ProjectTemplatePreview.responseError": "Oups... Une erreur est survenue.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Voir plus de modèles", "app.components.ProjectTemplatePreview.successMessage": "Le projet a été créé avec succès !", "app.components.ProjectTemplatePreview.typeProjectName": "Ta<PERSON>z le nom du projet", "app.components.ProjectTemplatePreview.useTemplate": "Utiliser le modèle", "app.components.SeatInfo.additionalSeats": "Sièges supplémentaires", "app.components.SeatInfo.additionalSeatsToolTip": "Ceci indique le nombre de sièges supplémentaires que vous avez achetés en plus des \"Sièges inclus\".", "app.components.SeatInfo.adminSeats": "Sièges administrateurs", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} sièges administrateurs", "app.components.SeatInfo.adminSeatsTooltip1": "Les administrateurs sont responsables de la plateforme et ont des droits de gestion sur tous les dossiers et projets. <PERSON><PERSON> pouvez {visitHelpCenter} pour en savoir plus sur les différents rôles.", "app.components.SeatInfo.currentAdminSeatsTitle": "Sièges administrateurs actuels", "app.components.SeatInfo.currentManagerSeatsTitle": "Sièges gestionnaires actuels", "app.components.SeatInfo.includedAdminToolTip": "Nombre de sièges pour les administrateurs inclus dans le contrat annuel.", "app.components.SeatInfo.includedManagerToolTip": "Nombre de sièges pour les gestionnaires inclus dans le contrat annuel.", "app.components.SeatInfo.includedSeats": "Sièges inclus", "app.components.SeatInfo.managerSeats": "Sièges gestionnaires", "app.components.SeatInfo.managerSeatsTooltip": "Les gestionnaires de dossiers/projets peuvent gérer un nombre illimité de dossiers/projets. V<PERSON> pouvez {visitHelpCenter} pour en savoir plus sur les différents rôles.", "app.components.SeatInfo.managersIncludedText": "{managerSeats} sièges gestionnaires", "app.components.SeatInfo.remainingSeats": "Sièges restants", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Nombre total de sièges", "app.components.SeatInfo.totalSeatsTooltip": "Ceci correspond à la somme des sièges de votre plan et des sièges supplémentaires que vous avez achetés.", "app.components.SeatInfo.usedSeats": "Sièges utilisés", "app.components.SeatInfo.view": "Voir", "app.components.SeatInfo.visitHelpCenter": "consulter notre centre d'aide", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Votre plan inclut {adminSeatsIncluded}. Lorsque vous aurez utilisé tous les sièges, les sièges supplémentaires seront ajoutés sous \"Sièges supplémentaires\".", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Votre plan inclut {managerSeatsIncluded}, éligibles pour les gestionnaires de dossiers et de projets. Si tous les sièges sont utilisés, les sièges supplémentaires seront ajoutés sous \"Sièges supplémentaires\".", "app.components.UserSearch.addModerators": "Ajouter", "app.components.UserSearch.searchUsers": "Recherchez des utilisateurs", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Message d'erreur personnalisé", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, le message d'erreur suivant sera affiché aux utilisateurs :", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Personnaliser le message d'erreur", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "Vous pouvez adapter ce message pour chaque langue à l'aide de la zone de texte « Message d'erreur personnalisé » ci-dessous. Si vous laissez la zone de texte vide, le message par défaut sera affiché.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "Message d'erreur", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "Message affiché aux participants qui ne remplissent pas les conditions de participation.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Enregistrer le message d'erreur", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "Veuillez d'abord sélectionner une question.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "Pas de réponse", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} réponses", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "Question d'enquête", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} jusqu'à maintenant", "app.components.admin.DatePhasePicker.Input.openEnded": "<PERSON><PERSON><PERSON>", "app.components.admin.DatePhasePicker.Input.selectDate": "Sélectionnez une date", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Effacer la date de fin", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Effacer la date de début", "app.components.admin.Graphs": "Aucune donnée disponible avec les filtres actuels.", "app.components.admin.Graphs.noDataShort": "Pas de données disponibles.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Commentaires", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Postes au fil du temps", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Réactions dans le temps", "app.components.admin.InputManager.onePost": "1 contribution", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Nombre de sélections hors ligne", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Nombre de votes hors ligne", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "Cette option vous permet d'inclure des données de participation provenant d'autres sources, telles que des votes en personne ou sur papier :", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Ils seront différenciés visuellement des votes en ligne.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "Cela affectera les résultats du vote.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "Cela n'affectera pas les tableaux de bord relatifs aux données de participation.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Le nombre de votes hors ligne pour une option ne peut être défini qu'à l'échelle du projet et est partagé entre toutes les phases du projet.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "V<PERSON> de<PERSON> d'abord indiquer le nombre total de participants hors ligne.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Nombre de participants hors ligne", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "Pour pouvoir calculer les résultats, nous avons besoin de connaître le <b>nombre total de participants hors ligne pour cette phase</b>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Veuillez uniquement indiquer le nombre de participants hors ligne.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "Modifié par {name}", "app.components.admin.PostManager.PostPreview.assignee": "Attribué", "app.components.admin.PostManager.PostPreview.cancelEdit": "Annuler la modification", "app.components.admin.PostManager.PostPreview.currentStatus": "Statut actuel", "app.components.admin.PostManager.PostPreview.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Êtes-vous sûr de vouloir supprimer cette contribution ? Cette action ne peut pas être annulée.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Êtes-vous sûr de vouloir supprimer cette contribution ? La contribution sera supprimée de toutes les phases du projet et ne pourra pas être récupérée.", "app.components.admin.PostManager.PostPreview.edit": "Modifier", "app.components.admin.PostManager.PostPreview.noOne": "Non attribué", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Le nombre de fois que cela a été inclus dans les budgets participatifs des autres participants", "app.components.admin.PostManager.PostPreview.picks": "Sélections: {picks<PERSON><PERSON>ber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Compte des réactions :", "app.components.admin.PostManager.PostPreview.save": "Enregistrer", "app.components.admin.PostManager.PostPreview.submitError": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.addFeatureLayer": "Ajouter une couche d'entités", "app.components.admin.PostManager.addFeatureLayerInstruction": "Copiez l'URL de la couche d'entités hébergée sur ArcGIS Online et collez-la dans le champ ci-dessous :", "app.components.admin.PostManager.addFeatureLayerTooltip": "A<PERSON><PERSON>z une nouvelle couche d'entités à la carte", "app.components.admin.PostManager.addWebMap": "Ajouter une Web Map", "app.components.admin.PostManager.addWebMapInstruction": "Copiez l'ID de portail de votre Web Map depuis ArcGIS Online et collez-le dans le champ ci-dessous :", "app.components.admin.PostManager.allPhases": "Toutes les phases", "app.components.admin.PostManager.allProjects": "Tous les projets", "app.components.admin.PostManager.allStatuses": "Tous les statuts", "app.components.admin.PostManager.allTopics": "Toutes les étiquettes", "app.components.admin.PostManager.anyAssignment": "Toutes les attributions", "app.components.admin.PostManager.assignedTo": "{assigneeName}", "app.components.admin.PostManager.assignedToMe": "Attribué à moi", "app.components.admin.PostManager.assignee": "Responsable", "app.components.admin.PostManager.authenticationError": "Une erreur d'authentification s'est produite lors de la récupération de cette couche. Veuillez vérifier l'URL et assurez-vous que votre clé API Esri a accès à cette couche.", "app.components.admin.PostManager.automatedStatusTooltipText": "Ce statut est mis à jour automatiquement lorsque les conditions sont satisfaites", "app.components.admin.PostManager.bodyTitle": "Description", "app.components.admin.PostManager.cancel": "Annuler", "app.components.admin.PostManager.cancel2": "Annuler", "app.components.admin.PostManager.co-sponsors": "Coparrainants", "app.components.admin.PostManager.comments": "Commentaires", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "<PERSON><PERSON> signifie que vous perdrez toutes les données associées à ces contributions, comme les commentaires, les réactions et les votes. Cette action ne peut pas être annulée.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Êtes-vous sûr de vouloir supprimer ces contributions ?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "Supprimer l'étiquette", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "Vous essayez de retirer cette idée d'une phase où elle a reçu des votes. Si vous le faites, ces votes seront perdus. Êtes-vous sûr(e) de vouloir retirer cette idée de cette phase ?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "Les votes associés à cette idée seront perdus", "app.components.admin.PostManager.components.goToInputManager": "Aller dans le gestionnaire de contributions", "app.components.admin.PostManager.components.goToProposalManager": "Aller dans le gestionnaire de propositions", "app.components.admin.PostManager.contributionFormTitle": "Modifiez la <PERSON>", "app.components.admin.PostManager.cost": "Coût", "app.components.admin.PostManager.createInput": "<PERSON><PERSON><PERSON> une contribution", "app.components.admin.PostManager.createInputsDescription": "<PERSON><PERSON>r les contributions d'un autre projet", "app.components.admin.PostManager.currentLat": "Latitude par défaut", "app.components.admin.PostManager.currentLng": "Longitude par défaut", "app.components.admin.PostManager.currentZoomLevel": "Niveau de zoom actuel", "app.components.admin.PostManager.defaultEsriError": "Une erreur s'est produite lors de la récupération de cette couche. Veuillez vérifier l'URL ainsi que votre connexion réseau.", "app.components.admin.PostManager.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.deleteAllSelectedInputs": "Supprimer {count} contributions", "app.components.admin.PostManager.deleteConfirmation": "Êtes-vous sûr de vouloir supprimer cette couche ?", "app.components.admin.PostManager.dislikes": "<PERSON><PERSON>", "app.components.admin.PostManager.edit": "Modifier", "app.components.admin.PostManager.editProjects": "Modifier les projets", "app.components.admin.PostManager.editStatuses": "Modifier les statuts", "app.components.admin.PostManager.editTags": "Modifier les étiquettes", "app.components.admin.PostManager.editedPostSave": "Enregistrer", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "L'import de données depuis Esri ArcGIS Online fait partie d'une extension. Adressez-vous à votre Spécialiste en participation Go Vocal pour l'activer.", "app.components.admin.PostManager.esriSideError": "Une erreur s'est produite avec l'application ArcGIS. Veuillez réessayer dans quelques minutes.", "app.components.admin.PostManager.esriWebMap": "Web Map Esri", "app.components.admin.PostManager.exportAllInputs": "Exportez toutes les contributions (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Exporter tous les commentaires (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Exporter tous les commentaires pour ce project (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Exportes les contributions pour ce projet (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Exportez les contributions sélectionnées (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Exportez les commentaires pour les contributions sélectionnées (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Exporter les votes par contribution (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Exporter les votes par utilisateur (.xslx)", "app.components.admin.PostManager.exports": "Exports", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "Pour l'import des données cartographiques, vous devez choisir entre les couches GeoJSON ou l'import depuis ArcGIS Online. Veuillez supprimer toutes les couches GeoJSON existantes si vous souhaitez ajouter une couche d'entités depuis ArcGIS.", "app.components.admin.PostManager.featureLayerTooltop": "Vous pouvez trouver l'URL de la couche d'entités sur la page d'élément correspondante sur ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Choisissez comment les autres voient votre nom", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Expliquez ce changement de statut", "app.components.admin.PostManager.fileUploadError": "Un ou plusieurs fichiers n'ont pas pu être téléversés. Veuillez vérifier la taille et le format du fichier et réessayer.", "app.components.admin.PostManager.formTitle": "Modifier l'idée", "app.components.admin.PostManager.generalApiError2": "Une erreur s'est produite lors de la récupération de cet élément. Veuillez vérifier que l'URL et l'ID de portail sont corrects et que vous avez accès à cet élément.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Pour l'import des données cartographiques, vous devez choisir entre les couches GeoJSON ou l'import depuis ArcGIS Online. Veuillez supprimer toutes les données ArcGIS existantes si vous souhaitez ajouter une couche GeoJSON.", "app.components.admin.PostManager.goToDefaultMapView": "Aller à la centralisation et au niveau de zoom par défaut", "app.components.admin.PostManager.hiddenFieldsLink": "champ caché", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Conseil : ajoutez un {hiddenFieldsLink} lors de la configuration de votre enquête Typeform pour savoir qui y a répondu.", "app.components.admin.PostManager.import2": "Importer", "app.components.admin.PostManager.importError": "Le fichier sélectionné n'a pas pu être importé car il ne s'agit pas d'un fichier GeoJSON valide", "app.components.admin.PostManager.importEsriFeatureLayer": "Importer une couche d'entités Esri", "app.components.admin.PostManager.importEsriWebMap": "Importer une Web Map Esri", "app.components.admin.PostManager.importInputs": "Importer des contributions", "app.components.admin.PostManager.imported": "Importé", "app.components.admin.PostManager.initiativeFormTitle": "Modifier l'initiative", "app.components.admin.PostManager.inputCommentsExportFileName": "commentaires_contributions", "app.components.admin.PostManager.inputImportProgress": "{importedCount}/{totalCount} {totalCount, plural, one {contribution a été importée} other {contributions ont été importées}}. L'importation est toujours en cours.", "app.components.admin.PostManager.inputManagerHeader": "Contributions", "app.components.admin.PostManager.inputs": "Contributions", "app.components.admin.PostManager.inputsExportFileName": "contributions", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "A<PERSON>iche<PERSON> uniquement les contributions nécessitant un retour", "app.components.admin.PostManager.issueFormTitle": "Modifiez le problème", "app.components.admin.PostManager.latestFeedbackMode": "Utiliser la dernière mise à jour comme explication", "app.components.admin.PostManager.layerAdded": "<PERSON><PERSON> a<PERSON>", "app.components.admin.PostManager.likes": "Pour", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Si vous déplacez cette entrée hors de son projet actuel, vous perdrez les informations sur les phases qui lui ont été attribuées. Voulez-vous continuer ?", "app.components.admin.PostManager.mapData": "Données cartographiques", "app.components.admin.PostManager.multipleInputs": "{ideaCount} contributions", "app.components.admin.PostManager.newFeedbackMode": "Écrivez une nouvelle mise à jour pour expliquer ce changement", "app.components.admin.PostManager.noFilteredResults": "Les filtres que vous avez sélectionnés n'ont renvoyé aucun résultat", "app.components.admin.PostManager.noInputs": "Pas encore de contributions", "app.components.admin.PostManager.noInputsDescription": "Ajoutez une nouvelle contribution ou importez-en depuis une autre phase.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {Cette phase ne contient aucune contribution à importer.} one {1 contribution sera importée. L'importation s'effectuera en arrière-plan, et la contribution apparaîtra dans le gestionnaire de contributions une fois celle-ci terminée.} other {# contributions seront importées. L'importation s'effectuera en arrière-plan, et les contributions apparaîtront dans le gestionnaire de contributions une fois celle-ci terminée.}}", "app.components.admin.PostManager.noOne": "Non attribué", "app.components.admin.PostManager.noProject": "Aucun projet", "app.components.admin.PostManager.officialFeedbackModal.author": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "Choisissez la façon dont votre nom apparaîtra", "app.components.admin.PostManager.officialFeedbackModal.description": "Le fait de fournir un retour d'information officiel contribue à la transparence du processus et renforce la confiance dans la plateforme.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "L'auteur est obligatoire", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Un retour d'information est nécessaire", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Retour d'information officiel", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Expliquez la raison du changement de statut", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Publier le feedback", "app.components.admin.PostManager.officialFeedbackModal.skip": "Passez cette fois-ci", "app.components.admin.PostManager.officialFeedbackModal.title": "Expliquez votre décision", "app.components.admin.PostManager.officialUpdateAuthor": "Choisissez comment les autres voient votre nom", "app.components.admin.PostManager.officialUpdateBody": "Expliquez ce changement de statut", "app.components.admin.PostManager.offlinePicks": "<PERSON><PERSON> hors ligne", "app.components.admin.PostManager.offlineVotes": "Votes hors ligne", "app.components.admin.PostManager.onlineVotes": "Votes en ligne", "app.components.admin.PostManager.optionFormTitle": "Modifiez l'option", "app.components.admin.PostManager.participants": "Participants", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Choix en ligne", "app.components.admin.PostManager.pbItemCountTooltip": "Le nombre de fois que cela a été inclus dans les budgets participatifs des autres participants", "app.components.admin.PostManager.petitionFormTitle": "Modifier la pétition", "app.components.admin.PostManager.postedIn": "<PERSON><PERSON><PERSON> {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Modifier le projet", "app.components.admin.PostManager.projectsTab": "Projets", "app.components.admin.PostManager.projectsTabTooltipContent": "Vous pouvez faire glisser et déposer des articles pour les déplacer d'un projet à un autre. Notez que pour les projets de chronologie, vous devrez toujours ajouter la publication à une phase spécifique.", "app.components.admin.PostManager.proposalFormTitle": "Modifier la proposition", "app.components.admin.PostManager.proposedBudgetTitle": "Budget proposé", "app.components.admin.PostManager.publication_date": "<PERSON><PERSON><PERSON> le", "app.components.admin.PostManager.questionFormTitle": "Modifier la question", "app.components.admin.PostManager.reactions": "Réactions", "app.components.admin.PostManager.resetFiltersButton": "Réinitialiser", "app.components.admin.PostManager.resetInputFiltersDescription": "Réinitialisez les filtres pour voir toutes les contributions.", "app.components.admin.PostManager.saved": "Enregistré", "app.components.admin.PostManager.screeningTooltip": "La révision des contributions n'est pas inclus dans votre plan actuel. Adressez-vous à votre Conseiller en Participation ou à votre administrateur pour le débloquer.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "La révision des contributions est désactivé pour cette phase. Allez à la configuration de la phase pour l'activer", "app.components.admin.PostManager.selectAPhase": "Sélectionnez une phase", "app.components.admin.PostManager.selectAProject": "Sélectionnez un projet", "app.components.admin.PostManager.setAsDefaultMapView": "Sauvegarder la centralisation et le niveau de zoom actuel comme les paramètres par défaut", "app.components.admin.PostManager.startFromPastInputs": "Importer depuis une autre phase", "app.components.admin.PostManager.statusChangeGenericError": "Il y a eu une erreur, veuil<PERSON><PERSON> réessayer plus tard <NAME_EMAIL>.", "app.components.admin.PostManager.statusChangeSave": "Modifier le statut", "app.components.admin.PostManager.statusesTab": "Statuts", "app.components.admin.PostManager.statusesTabTooltipContent": "Modifiez le statut d'un article en le glissant et déposant. L'auteur d'origine et les autres contributeurs recevront une notification de modification de statut.", "app.components.admin.PostManager.submitApiError": "Un problème est survenu lors de l'envoi du formulaire. Veuillez vérifier s'il y a des erreurs et réessayer.", "app.components.admin.PostManager.timelineTab": "Phases", "app.components.admin.PostManager.timelineTabTooltipText": "Faites glisser et déposez les contributions pour les copier dans différentes phases du projet.", "app.components.admin.PostManager.title": "Titre", "app.components.admin.PostManager.topicsTab": "Étiquettes", "app.components.admin.PostManager.topicsTabTooltipText": "Ajoutez des étiquettes à une contribution simplement en les glissant.", "app.components.admin.PostManager.view": "Voir", "app.components.admin.PostManager.votes": "Votes", "app.components.admin.PostManager.votesByInputExportFileName": "votes_par_contribution", "app.components.admin.PostManager.votesByUserExportFileName": "votes_par_utilisateur", "app.components.admin.PostManager.webMapAlreadyExists": "Vous ne pouvez utiliser qu'une seule Web Map à la fois. Veuillez supprimer la Web Map actuelle pour en importer une autre.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Pour l'import des données cartographiques, vous devez choisir entre les couches GeoJSON ou l'import depuis ArcGIS Online. Veuillez supprimer toutes les couches GeoJSON existantes si vous souhaitez importer une Web Map.", "app.components.admin.PostManager.webMapTooltip": "Vous pouvez trouver l'ID de portail de votre Web Map sur la page d'élément correspondante sur ArcGIS Online.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {Moins d'un jour restant} one {Un jour restant} other {# jours restants}}", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "Annuler", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "<PERSON><PERSON>, supprimer les résultats de l'enquête", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "Cette action est irréversible", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Supprimer les résultats de l'enquête", "app.components.admin.ProjectEdit.survey.downloadResults2": "Télécharger les résultats de l'enquête", "app.components.admin.ReportExportMenu.FileName.fromFilter": "de ", "app.components.admin.ReportExportMenu.FileName.groupFilter": "groupe", "app.components.admin.ReportExportMenu.FileName.projectFilter": "projet", "app.components.admin.ReportExportMenu.FileName.topicFilter": "étiquette", "app.components.admin.ReportExportMenu.FileName.untilFilter": "jusqu'à", "app.components.admin.ReportExportMenu.downloadPng": "Télécharger au format PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Télécharger au format SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Télécharger Excel", "app.components.admin.SlugInput.regexError": "Le slug ne peut contenir que des lettres minuscules (a-z), des chiffres (0-9) et des traits d'union (-). Le premier et le dernier caractère ne peuvent pas être des traits d'union. Les traits d'union consécutifs (--) ne sont pas autorisés.", "app.components.admin.TerminologyConfig.saveButton": "Enregistrer", "app.components.admin.commonGroundInputManager.title": "Titre", "app.components.admin.seatSetSuccess.admin": "Administrateur", "app.components.admin.seatSetSuccess.allDone": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.close": "<PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.manager": "Gestionnaire", "app.components.admin.seatSetSuccess.orderCompleted": "Commande finalisée", "app.components.admin.seatSetSuccess.reflectedMessage": "Les modifications apportées à votre plan seront prises en compte lors de votre prochain cycle de facturation.", "app.components.admin.seatSetSuccess.rightsGranted": "Les droits {seatType} ont été accordés au(x) utilisateur(s) sélectionné(s).", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Êtes-vous sûr de vouloir supprimer tous les résultats de l'enquête ?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "Cette méthode de participation est en version bêta. Nous la déployons progressivement afin de recueillir vos commentaires et de continuer à l'améliorer.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Récolter des commentaires sur un document est une fonctionnalité qui n'est pas incluse dans votre licence actuelle. Contactez votre Spécialiste en participation Go Vocal pour en savoir plus à ce sujet.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Suggestion", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Le champ « Nombre de jours » est obligatoire", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "Nombre de jours pour atteindre le nombre minimum de votes", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Vous trouverez de plus amples informations sur la manière d'intégrer un lien pour Google Forms dans {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "cet article de support", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Initiative", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Comment doit-on appeler une contribution ?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Problème", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Insérez le lien de votre document Konveio ici. Consultez notre {supportArticleLink} pour obtenir plus d'informations sur la configuration de Konveio.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "article d'assistance", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "Cette fonctionnalité n'est pas incluse dans votre plan actuel. Adressez-vous à votre Spécialiste en participation Go Vocal ou à votre administrateur pour la débloquer.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Un budget maximum est requis", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Le nombre maximum de votes par option doit être inférieur ou égal au nombre total de votes", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Un nombre maximum de votes est requis", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "Messagerie", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Le budget minimum ne peut pas être supérieur au budget maximum", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Un budget minimum est requis", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Le nombre minimum de votes ne peut pas être supérieur au nombre maximum", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Un nombre minimum de votes est requis", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Date de fin manquante", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Date de début manquante", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Option", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Gestion des contributions", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Configurez les options de vote dans l'onglet « Gestion des contributions » après avoir créé une phase.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Configurez les options de vote dans l'onglet {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Options de participation", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "Participants", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Pétition", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Administrateurs et gestionnaires", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Annoter le document :</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} peuvent participer à cette phase.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "Annuler", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Commentaire :</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Phase de consensus", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Supprimer la phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "<PERSON><PERSON>, supprimez cette phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "Êtes-vous sûr de vouloir supprimer cette phase ?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "Toutes les données relatives à cette phase seront supprimées. Cette opération ne peut être annulée.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Phase d'annotation de documents", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Tous les utilisateurs", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "Phase d'enquête externe", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Phase d'idéation", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "Phase d'enquête", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Phase d'information", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "Permissions mixtes", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "Pas de date de fin", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Phase de sondage", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Phase de propositions", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>Réagir :</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Inscription à l'événement :</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Utilisateurs enregistrés", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b><PERSON><PERSON><PERSON><PERSON> des contributions :</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b><PERSON><PERSON><PERSON>nd<PERSON> au sondage :</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b><PERSON><PERSON><PERSON><PERSON><PERSON> à l'enquête :</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Utilisateurs avec une adresse e-mail confirmée", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>Volontariat :</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Phase de volontariat", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Voter :</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Phase de vote", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Qui peut participer ?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Les contributions ne seront pas visibles tant qu'un administrateur ne les aura pas examinées et approuvées. Les auteurs ne peuvent pas modifier les contributions une fois qu'elles ont été examinées et approuvé ou qu'elles ont une réaction.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "Administrateurs uniquement", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "Toute personne avec le lien peut interagir avec le projet comme s'il était publié", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "Approuver", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "L'approbation du projet donne aux gestionnaires du projet le droit de le publier.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "Approuvé par {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Archivé", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Brouillon", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Modifier la description", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "Tous les utilisateurs", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Groupes", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "Caché", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Votants hors ligne", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Seuls les administrateurs{inFolder, select, true { ou les gestionnaires du dossier} other {}} peuvent publier le projet", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 participant} other {{participantsCount} participants}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Les utilisateurs interagissant avec les méthodes intégrées (par exemple, enquêtes externes)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Les followers d'un projet", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Remarque : L'activation de la participation anonyme ou sans compte utilisateur peut permettre aux utilisateurs de participer plusieurs fois, ce qui pourrait conduire des résultats trompeurs ou non représentatifs.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "Les participants <b>n'incluent pas</b> :", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Les participants incluent :", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Personnes inscrites", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "Utilisateurs interagissant avec les méthodes natives de Go Vocal", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "En attente d'approbation", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Les personnes chargées de la relecture des projets ont été notifiées.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "Public", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Publier", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Publié - Actif", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Regénérer le lien", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Générer un nouveau lien. Cela invalidera le lien actuel.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Les anciens liens cesseront de fonctionner, mais vous pouvez en générer un nouveau à tout moment.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Êtes-vous sûr ? <PERSON>la d<PERSON>ra le lien actuel", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "Annuler", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "<PERSON><PERSON>, rafra<PERSON><PERSON>r le lien", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Demander l'approbation", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "Le projet doit être approuvé par un administrateur{inFolder, select, true { ou l'un des gestionnaires du dossier} other {}} avant de pouvoir le publier.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Paramètres", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Partager", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "Copier le lien", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "<PERSON>n copié", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Les liens de partage privés ne sont pas inclus dans votre plan actuel. Contactez votre Spécialiste en participation Go Vocal ou votre administrateur pour les activer.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Partager ce projet", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "Qui a accès", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "Voir", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Projet", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Proposition", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "Question", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Nombre minimum de votes requis pour être pris en considération", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Le champ « Nombre minimum de votes » est obligatoire", "app.components.app.containers.AdminPage.ProjectEdit.report": "Rapport", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Exiger la révision des contributions", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Ligne du temps", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Trafic", "app.components.formBuilder.cancelMethodChange1": "Annuler", "app.components.formBuilder.changeMethodWarning1": "Un changement de méthode peut rendre inaccessible les contributions reçues avec la méthode actuellement utilisée.", "app.components.formBuilder.changingMethod1": "Changement de méthode", "app.components.formBuilder.confirmMethodChange1": "<PERSON><PERSON>, <PERSON>r", "app.components.formBuilder.copySurveyModal.cancel": "Annuler", "app.components.formBuilder.copySurveyModal.description": "Cela copiera les questions et la logique, mais pas les réponses.", "app.components.formBuilder.copySurveyModal.duplicate": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "Aucune phase compatible n'a été trouvée pour ce projet", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "Aucune phase n'a été sélectionnée. Veuillez d'abord sélectionner une phase.", "app.components.formBuilder.copySurveyModal.noProject": "Aucun projet", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Aucun projet n'a été sélectionné. Veuillez d'abord sélectionner un projet.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "Vous avez déjà apporté des modifications à cette enquête. Si vous dupliquez une autre enquête, ces modifications seront perdues.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Phase d'enquête", "app.components.formBuilder.copySurveyModal.title": "Sélectionnez une enquête à dupliquer", "app.components.formBuilder.editWarningModal.addOrReorder": "A<PERSON><PERSON> ou ré<PERSON>onner les questions", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Les données de réponse pourraient être incorrectes", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Modifier le texte", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Vous corrigez une faute de frappe ? Cela n'affectera pas les réponses déjà été soumises", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Vous perdrez les réponses données à cette question", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Supprimer une question", "app.components.formBuilder.editWarningModal.exportYouResponses2": "exportez vos réponses.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Attention : Vous pourriez perdre définitivement les données de réponse. Avant de poursuivre,", "app.components.formBuilder.editWarningModal.noCancel": "Non, annuler", "app.components.formBuilder.editWarningModal.title4": "Modifier une enquête en cours", "app.components.formBuilder.editWarningModal.yesContinue": "<PERSON><PERSON>, <PERSON>r", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "les droits d'accès pour cette enquête", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "L'option « Inclure les champs démographiques dans l'enquête » est activée. Quand le formulaire d'enquête s'affiche, les questions démographiques configurées sont ajoutées sur une nouvelle page à la fin de l'enquête. Ces questions peuvent être modifiées dans {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "droits d'accès", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Les personnes interrogées ne seront pas obligées de s'inscrire ou de se connecter pour répondre à l'enquête, ce qui peut entraîner des doublons.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "En sautant l'étape d'inscription ou de connexion, vous choisissez de ne pas collecter les informations démographiques des répondants, ce qui peut restreindre votre capacité à analyser les données", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "Cette enquête est ouverte à tout le monde.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "Si vous souhaitez modifier cela, vous pouvez le faire à l'adresse {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Vous demandez les informations démographiques suivantes aux répondants de l'enquête lors de leur inscription ou de leur connexion.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "Pour simplifier la collecte des informations démographiques et garantir leur intégration dans votre base de données utilisateur, nous vous conseillons d'inclure directement ces questions dans le processus d'inscription ou de connexion. Pour ce faire, veuillez ajuster les paramètres des {accessRightsSettingsLink} pour cette phase.", "app.components.onboarding.askFollowPreferences": "Demander aux utilisateurs de suivre des sujets ou des zones", "app.components.onboarding.followHelperText": "<PERSON><PERSON> active une étape dans le processus d'enregistrement où les utilisateurs pourront suivre les domaines ou les sujets que vous sélectionnez ci-dessous.", "app.components.onboarding.followPreferences": "Préférences de suivi", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} inclus dans le plan, {noOfAdditionalSeats} supplémentaire(s)", "app.components.seatsWithinPlan.seatsWithinPlanText": "Sièges inclus dans le plan", "app.containers.Admin.Campaigns.campaignFrom": "De :", "app.containers.Admin.Campaigns.campaignTo": "Vers : ", "app.containers.Admin.Campaigns.customEmails": "E-mails personnalisés", "app.containers.Admin.Campaigns.customEmailsDescription": "Envoyez des e-mails personnalisés et vérifiez les statistiques.", "app.containers.Admin.Campaigns.noAccess": "Nous sommes désolés, mais il semble que vous n’ayez pas accès à la section e-mails", "app.containers.Admin.Campaigns.tabAutomatedEmails": "E-mails automatisés", "app.containers.Admin.Insights.tabReports": "Rapports", "app.containers.Admin.Invitations.a11y_removeInvite": "Supprimer l'invitation", "app.containers.Admin.Invitations.addToGroupLabel": "Ajouter ces personnes à des groupes d'utilisateurs manuels spécifiques", "app.containers.Admin.Invitations.adminLabel1": "Attribuer aux invités les droits d'administration", "app.containers.Admin.Invitations.adminLabelTooltip": "Une fois activée, les personnes qui reçoivent et qui acceptent vos invitations auront également accès aux paramètres de la plateforme.", "app.containers.Admin.Invitations.configureInvitations": "3. <PERSON><PERSON>gu<PERSON> les invitations", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Il n'y a pas d'invitation correspondant à votre recherche", "app.containers.Admin.Invitations.deleteInvite": "Annuler", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Êtes-vous sûr de vouloir supprimer cette invitation ?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Annuler une invitation vous permettra de renvoyer une invitation à ces personnes.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Télécharger et remplir le modèle", "app.containers.Admin.Invitations.downloadTemplate": "Télécharger le modèle", "app.containers.Admin.Invitations.email": "Adresse e-mail", "app.containers.Admin.Invitations.emailListLabel": "Entrez à la main les adresses e-mail des individus que vous souhaitez inviter. Séparer chaque adresse par une virgule.", "app.containers.Admin.Invitations.exportInvites": "Exporter la liste d'invitations", "app.containers.Admin.Invitations.fileRequirements": "Important: Aucune colonne ne peut être supprimée du modèle d'importation - laissez les colonnes non utilisées vides. Assurez-vous qu'il n'y a pas de doublons avant d'importer votre fichier d'adresses mails. <PERSON><PERSON> pourrait causer des erreurs dans l'envoi des invitations.", "app.containers.Admin.Invitations.filetypeError": "Le type du fichier est incorrect. Seuls les fichiers XLSX sont acceptés.", "app.containers.Admin.Invitations.groupsPlaceholder": "Pas de groupe sélectionné", "app.containers.Admin.Invitations.helmetDescription": "Invitez des utilisateurs sur la plateforme", "app.containers.Admin.Invitations.helmetTitle": "Gestion des invitations", "app.containers.Admin.Invitations.importOptionsInfo": "Ces options ne seront prises en compte que si elles ne sont pas définies dans le fichier Excel.\n      Veuillez visiter cette {supportPageLink} pour plus d'information.", "app.containers.Admin.Invitations.importTab": "Importer des adresses e-mail", "app.containers.Admin.Invitations.invitationExpirationWarning": "Sachez que les invitations expirent après 30 jours. Passé ce délai, vous pourrez toujours les renvoyer.", "app.containers.Admin.Invitations.invitationOptions": "Options d'invitation", "app.containers.Admin.Invitations.invitationSubtitle": "Invitez les personnes qui ne sont pas encore inscrites sur la plateforme. Importez leurs adresses électroniques en les plaçant dans le modèle d'importation ou saisissez les adresses électroniques manuellement. Si vous le souhaitez, ajoutez un message personnel, donnez des droits supplémentaires aux personnes ou ajoutez-les à un groupe manuel.", "app.containers.Admin.Invitations.invitePeople": "Invitations", "app.containers.Admin.Invitations.inviteStatus": "Statut", "app.containers.Admin.Invitations.inviteStatusAccepted": "Acceptée", "app.containers.Admin.Invitations.inviteStatusPending": "En attente", "app.containers.Admin.Invitations.inviteTextLabel": "Ajouter un message personnel à l'invitation", "app.containers.Admin.Invitations.invitedSince": "<PERSON><PERSON><PERSON> le", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Choisissez la langue de l'invitation", "app.containers.Admin.Invitations.moderatorLabel": "Donner à ces personnes des droits d’administrateur projet", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Une fois activée, les personnes qui reçoivent et qui acceptent vos invitations recevront également des droits d'administrateur projet pour un ou plusieurs projets. Pour plus d'information sur ce rôle d'administrateur projet {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "ici", "app.containers.Admin.Invitations.name": "Nom", "app.containers.Admin.Invitations.processing": "Invitations en cours d'envoi. Veuillez patienter...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Pas de projet sélectionné", "app.containers.Admin.Invitations.save": "Envoyer vos invitations", "app.containers.Admin.Invitations.saveErrorMessage": "Une ou plusieurs erreurs se sont produites.\n      Aucune invitation n'a donc été envoyée pour le moment.\n      Veuillez corriger les erreurs ci-dessous et essayer à nouveau.", "app.containers.Admin.Invitations.saveSuccess": "Envoyé !", "app.containers.Admin.Invitations.saveSuccessMessage": "Les invitations ont été envoyées avec succès.", "app.containers.Admin.Invitations.supportPage": "page d'aide", "app.containers.Admin.Invitations.supportPageLinkText": "Visitez la page de soutien", "app.containers.Admin.Invitations.tabAllInvitations": "Toutes les invitations", "app.containers.Admin.Invitations.tabInviteUsers": "Inviter des gens", "app.containers.Admin.Invitations.textTab": "Entrez les adresses e-mail", "app.containers.Admin.Invitations.unknownError": "Une erreur est survenue. Veuillez réessayer.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. <PERSON><PERSON><PERSON> votre mod<PERSON> complé<PERSON>", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} si vous voulez plus d'informations sur toutes les colonnes prises en charge dans le modèle d'importation.", "app.containers.Admin.Moderation.all": "Tous", "app.containers.Admin.Moderation.belongsTo": "Appartient à", "app.containers.Admin.Moderation.collapse": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.comment": "Commentaire", "app.containers.Admin.Moderation.commentDeletionCancelButton": "Annuler", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.confirmCommentDeletion": "Voulez-vous vraiment supprimer ce commentaire ? Cette action est définitive et irréversible.", "app.containers.Admin.Moderation.content": "Contenu", "app.containers.Admin.Moderation.date": "Date", "app.containers.Admin.Moderation.deleteComment": "Supp<PERSON><PERSON> le commentaire", "app.containers.Admin.Moderation.goToComment": "Ouv<PERSON>r ce commentaire dans un nouvel onglet", "app.containers.Admin.Moderation.goToPost": "<PERSON><PERSON><PERSON><PERSON><PERSON> cette contribution dans un nouvel onglet", "app.containers.Admin.Moderation.goToProposal": "Ouv<PERSON>r cette proposition dans un nouvel onglet", "app.containers.Admin.Moderation.markFlagsError": "Impossible de marquer le-s contenu-s. Essayez à nouveau.", "app.containers.Admin.Moderation.markNotSeen": "Marquer {selectedItemsCount, plural, one {# contenu} other {# contenus}} comme non vu-s", "app.containers.Admin.Moderation.markSeen": "Marquer {selectedItemsCount, plural, one {# contenu} other {# contenus}} comme vu-s", "app.containers.Admin.Moderation.moderationsTooltip": "Cette page vous permet de vérifier rapidement tous les nouveaux contenus publiés votre plateforme, dont les contributions et les commentaires. Vous pouvez marquer les articles comme étant « vus » afin que les autres administrateurs sachent quelles contributions doivent encore être traitées.", "app.containers.Admin.Moderation.noUnviewedItems": "Il n'y a pas d'éléments non vus", "app.containers.Admin.Moderation.noViewedItems": "Il n'y a pas d'éléments vus", "app.containers.Admin.Moderation.pageTitle1": "Flux", "app.containers.Admin.Moderation.post": "Contribution", "app.containers.Admin.Moderation.profanityBlockerSetting": "Bloquer les contenus inappropriés", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Bloquer les contenus contenant les injures les plus fréquemment signalées.", "app.containers.Admin.Moderation.project": "Projet", "app.containers.Admin.Moderation.read": "Vu", "app.containers.Admin.Moderation.readMore": "Voir plus", "app.containers.Admin.Moderation.removeFlagsError": "Impossible de supprimer l'alerte/les alertes. Essayez à nouveau.", "app.containers.Admin.Moderation.rowsPerPage": "Eléments par page", "app.containers.Admin.Moderation.settings": "Paramètres", "app.containers.Admin.Moderation.settingsSavingError": "Impossible de sauvegarder. Essayez à nouveau de modifier les paramètres.", "app.containers.Admin.Moderation.show": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.status": "Statut", "app.containers.Admin.Moderation.successfulUpdateSettings": "Les paramètres ont été mis à jour avec succès.", "app.containers.Admin.Moderation.type": "Type", "app.containers.Admin.Moderation.unread": "Non vu", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Cette page se compose des sections suivantes. Vous pouvez les activer/désactiver et les modifier selon vos besoins.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Sections", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "Voir la page", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Non affiché sur la page", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Affiché sur la page", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Pièces jointes", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Ajou<PERSON>z des fichiers (50 Mo maximum) qui pourront être téléchargés à partir de la page.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Section d'information inférieure", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "A<PERSON><PERSON>z votre propre contenu dans la section personnalisable située en bas de la page.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Modifier", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Liste des événements", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Afficher les événements liés aux projets.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Bannière principale", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Personnalisez l'image et le texte de la bannière de la page.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Liste des projets", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Affichez les projets en fonction des paramètres de votre page. V<PERSON> pouvez prévisualiser les projets qui seront affichés.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Section d'information supérieure", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "A<PERSON><PERSON>z votre propre contenu dans la section personnalisable située en haut de la page.", "app.containers.Admin.PagesAndMenu.addButton": "Ajouter à la barre de navigation", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Nom dans la barre de navigation", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Êtes-vous sûr de vouloir supprimer cette page ? Cette suppression ne peut être annulée.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Veuillez fournir un titre dans chacune des langues", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Autres pages disponibles", "app.containers.Admin.PagesAndMenu.components.savePage": "Sauvegarder la page", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "Page sauvegardée avec succès", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Pièces jointes (max. 50Mo)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Enregistré !", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Contenu", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Impossible d'enregistrer les pièces jointes", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Les fichiers ne doivent pas dépasser 50 Mo. Les fichiers ajoutés seront affichés en bas de cette page", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Pièces jointes sauvegardées", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Pièces jointes | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Pièces jointes", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Enregistrer et activer les pièces jointes", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Sauvegarder les pièces jointes", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Veuillez fournir du contenu pour toutes les langues.", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Enregistré !", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Contenu", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Impossible de sauvegarder la section d'information inférieure", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Sauvegarde de la section d'information inférieure", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Section d'information inférieure", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Enregistrer et activer la section d'information inférieure", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Sauvegarder la section d'information inférieure", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "La création de pages personnalisées n'est pas incluse dans votre licence actuelle. Contactez votre Spécialiste en participation Go Vocal pour en savoir plus.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Veuillez sélectionner au moins un tag", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Enregistré !", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "Par zone", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "Par balise(s)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "L'affichage des projets par étiquette ou zone n'est pas inclus dans votre licence actuelle. Contactez votre Spécialiste en participation pour en savoir plus.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Contenu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Modifier une page personnalisée", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Projets liés", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Sélectionnez les projets et les événements associés qui peuvent être affichés sur la page.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Page créée avec succès", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "Page sauvegardée avec succès", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Page personnalisée enregistrée", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Titre dans la barre de navigation", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "<PERSON><PERSON>er une page personnalisée | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "<PERSON><PERSON><PERSON> une page personnalisée", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "Aucun", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Paramètres de la page", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Sauvegarder la page personnalisée", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Veuillez sélectionner une zone", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Zone sélectionnée", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Tags sélectionnés", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "Le slug ne peut contenir que des lettres minuscules (a-z), des chiffres (0-9) et des traits d'union (-). Le premier et le dernier caractère ne peuvent pas être des traits d'union. Les traits d'union consécutifs (--) ne sont pas autorisés.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "<PERSON><PERSON> de<PERSON> entrer un slug", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Titre", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Saisissez un titre dans chaque langue", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Entrer un titre", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Afficher la page personnalisée", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "Bouton", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Modifier la page personnalisée | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Contenu de la page", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Modifier", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "Pour les projets ne comportant qu'une seule phase, si la phase n'a pas de date de fin et que la description est vide, la ligne du temps ne sera pas affichée sur la page du projet.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "Aucun projet disponible basé sur votre {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Ce projet n'a pas de filtre de balise ou de zone, donc aucun projet ne sera affiché.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Liste des projets | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "paramètres de page", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Liste des projets", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Les projets suivants seront affichés sur cette page en fonction de votre {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "PAR DÉFAUT", "app.containers.Admin.PagesAndMenu.deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.editButton": "Modifier", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Enregistré !", "app.containers.Admin.PagesAndMenu.heroBannerError": "Impossible de sauvegarder la bannière principale", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Bannière principale sauvegardée", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Sauvegarder la bannière principale", "app.containers.Admin.PagesAndMenu.homeTitle": "Accueil", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Fournir du contenu pour au moins une langue", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Vous ne pouvez ajouter que 5 éléments maximum à la barre de navigation", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Pages et menu | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Supprimer de la barre de navigation", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Enregistrer et activer la bannière principale", "app.containers.Admin.PagesAndMenu.title": "Pages et menu", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Enregistré !", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Contenu", "app.containers.Admin.PagesAndMenu.topInfoError": "Impossible de sauvegarder la section d'information supérieure", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Sauvegarde de la section d'information supérieure", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Section d'information supérieure | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Section d'information supérieure", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Enregistrer et activer la section d'information supérieure", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Sauvegarder la section d'information supérieure", "app.containers.Admin.PagesAndMenu.viewButton": "Voir", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "Age", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Communauté", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Résumé", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Indicateurs d'inclusion", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "Cette section présente des indicateurs d'inclusion qui donnent un aperçu de notre progression vers une plateforme de participation plus ouverte et représentative.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "participants", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Indicateurs de participation", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "Cette section présente les principaux indicateurs de participation pour la période sélectionnée, offrant ainsi un aperçu des performances en matière de participation.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projets", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "projets publiés", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Rapport de plateforme", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "Vos projets", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "Cette section donne une vue d'ensemble des projets en cours pour la période sélectionnée, des méthodes utilisées pour ces projets, ainsi que des statistiques pour chaque type de participation.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Inscriptions", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Utilisateurs bloqués", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "<PERSON><PERSON><PERSON> les utilisateurs bloqués.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Supprimer le groupe", "app.containers.Admin.Users.GroupsHeader.editGroup": "Modifier le groupe", "app.containers.Admin.Users.GroupsPanel.admins": "Administrateurs", "app.containers.Admin.Users.GroupsPanel.allUsers": "Tous les utilisateurs", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Groupes", "app.containers.Admin.Users.GroupsPanel.managers": "Gestionnaires de projet", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Attributions", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "<PERSON><PERSON><PERSON> tous les utilisateurs qui se sont inscrits sur votre plateforme. Créez des groupes manuellement ou de manière automatique pour les segmenter.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Invitation en attente", "app.containers.Admin.Users.admin": "Administrateur", "app.containers.Admin.Users.assign": "Ajouter", "app.containers.Admin.Users.assignedItems": "Attributions pour {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "Acheter un siège supplémentaire", "app.containers.Admin.Users.changeUserRights": "Modifier les droits d'utilisateur", "app.containers.Admin.Users.confirm": "Confirmer", "app.containers.Admin.Users.confirmAdminQuestion": "Êtes-vous certain(e) de vouloir attribuer à {name} les droits d'administration de la plateforme ?", "app.containers.Admin.Users.confirmNormalUserQuestion": "Êtes-vous certain(e) de vouloir définir {name} comme un utilisateur normal ?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Êtes-vous sûr(e) de vouloir assigner à {name} le statut d'utilisateur normal ? En cas de confirmation, cette personne perdra ses droits d'administrateur sur tous les projets et dossiers qui lui ont été assignés.", "app.containers.Admin.Users.deleteUser": "Supprimer cet utilisateur", "app.containers.Admin.Users.email": "E-mail", "app.containers.Admin.Users.folder": "Dossier", "app.containers.Admin.Users.folderManager": "Gestionnaire de dossier.", "app.containers.Admin.Users.helmetDescription": "Liste des utilisateurs", "app.containers.Admin.Users.helmetTitle": "Gestion des utilisateurs", "app.containers.Admin.Users.inviteUsers": "Invitez des utilisateurs", "app.containers.Admin.Users.joined": "Inscription", "app.containers.Admin.Users.lastActive": "Dernière activité", "app.containers.Admin.Users.name": "Nom", "app.containers.Admin.Users.noAssignedItems": "Aucune attribution", "app.containers.Admin.Users.options": "Options", "app.containers.Admin.Users.permissionToBuy": "Pour attribuer les droits d'administrateur à {name}, vous devez acheter 1 poste supplémentaire.", "app.containers.Admin.Users.platformAdmin": "Administrateur de la plate-forme", "app.containers.Admin.Users.projectManager": "Gestionnaire de projet", "app.containers.Admin.Users.reachedLimitMessage": "Vous avez atteint la limite de postes dans votre plan, 1 poste supplémentaire pour {name} sera a<PERSON>.", "app.containers.Admin.Users.registeredUser": "Utilisateur inscrit", "app.containers.Admin.Users.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.removeModeratorFrom": "L'utilisateur modère le dossier auquel appartient ce projet. Supprimez l'attribution de « {folderTitle} » à la place.", "app.containers.Admin.Users.role": "R<PERSON><PERSON>", "app.containers.Admin.Users.seeProfile": "Voir le profil de cet utilisateur", "app.containers.Admin.Users.selectPublications": "Sélectionnez des projets ou des dossiers", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Tapez pour rechercher", "app.containers.Admin.Users.setAsAdmin": "Définir comme administrateur", "app.containers.Admin.Users.setAsNormalUser": "Définir comme utilisateur normal", "app.containers.Admin.Users.setAsProjectModerator": "Définir en tant que gestionnaire de projet", "app.containers.Admin.Users.setUserAsProjectModerator": "Définir les attributions de {name}", "app.containers.Admin.Users.userBlockModal.allDone": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.blockAction": "Bloquer l'utilisateur", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Cette action ne supprimera pas le contenu de cet utilisateur. N'oubliez pas de modérer le contenu de l'utilisateur si nécessaire.", "app.containers.Admin.Users.userBlockModal.blocked": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Le compte de cet utilisateur a été bloqué depuis le {from}. L'interdiction dure jusqu'au {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "Annuler", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Êtes-vous sûr de vouloir débloquer {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} est bloqué(e) jusqu'au {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 jour} other {{numberOfDays} jours}}", "app.containers.Admin.Users.userBlockModal.header": "Bloquer l'utilisateur", "app.containers.Admin.Users.userBlockModal.reasonLabel": "<PERSON>son", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "<PERSON>la sera communiqué à l'utilisateur bloqué.", "app.containers.Admin.Users.userBlockModal.subtitle1": "L'utilisateur sélectionné ne pourra pas se connecter à la plateforme pendant {daysBlocked}. Si vous souhaitez annuler cette action, vous pouvez le débloquer depuis la liste des utilisateurs bloqués.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Débloquer", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON>, je veux débloquer cet utilisateur", "app.containers.Admin.Users.userDeletionConfirmation": "Supprimer définitivement cet utilisateur ?", "app.containers.Admin.Users.userDeletionFailed": "Une erreur s'est produite lors de la suppression de cet utilisateur, ve<PERSON><PERSON><PERSON>.", "app.containers.Admin.Users.userDeletionProposalVotes": "Cela supprimera également les votes de cet utilisateur sur les propositions qui sont encore ouvertes au vote.", "app.containers.Admin.Users.userExportFileName": "utilisateur_export", "app.containers.Admin.Users.userInsights": "Statistiques utilisateurs", "app.containers.Admin.Users.youCantDeleteYourself": "Vous ne pouvez pas supprimer votre propre compte via le panneau administrateur", "app.containers.Admin.Users.youCantUnadminYourself": "Vous ne pouvez pas supprimer votre rôle d'administrateur vous-même", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Observatoire de communauté", "app.containers.Admin.communityMonitor.healthScore": "Indice de sant<PERSON>", "app.containers.Admin.communityMonitor.healthScoreDescription": "Ce score correspond à la moyenne des réponses données aux questions à échelle de sentiment pendant la période sélectionnée.", "app.containers.Admin.communityMonitor.lastQuarter": "dernier trimestre", "app.containers.Admin.communityMonitor.liveMonitor": "En direct", "app.containers.Admin.communityMonitor.noResults": "Aucun résultat pour cette période.", "app.containers.Admin.communityMonitor.noSurveyResponses": "Aucune réponse à l'enquête", "app.containers.Admin.communityMonitor.participants": "Participants", "app.containers.Admin.communityMonitor.quarterChartLabel": "T{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "T{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Rapports", "app.containers.Admin.communityMonitor.settings": "Paramètres", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "L'enquête de l'Observatoire est active.", "app.containers.Admin.communityMonitor.settings.accessRights2": "Droits d'accès", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "Après qu'un utilisateur a voté, s'est inscrit à un événement, ou est revenu sur la page du projet après avoir répondu à une enquête.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Les gestionnaires de l'Observatoire de communauté peuvent accéder à toutes les données et paramètres de l'Observatoire.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Gestionnaires de l'Observatoire", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Les gestionnaires peuvent gérer les permissions de l'Observatoire, modifier l’enquête de suivi communautaire, accéder aux résultats et créer des rapports sur base de ces données.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "La fréquence par défaut est de 100 %.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Fréquence (0 à 100)", "app.containers.Admin.communityMonitor.settings.management2": "Dr<PERSON><PERSON> d'administration", "app.containers.Admin.communityMonitor.settings.popup": "<PERSON><PERSON>tre modale", "app.containers.Admin.communityMonitor.settings.popupDescription3": "Une fenêtre modale s'affiche périodiquement pour inviter les utilisateurs à répondre l'enquête. Vous pouvez ajuster la fréquence d'affichage, qui détermine le pourcentage d'utilisateurs qui verront apparaître cette fenêtre modale de façon aléatoire lorsque les conditions décrites ci-dessous sont remplies.", "app.containers.Admin.communityMonitor.settings.popupSettings": "<PERSON><PERSON>tre modale", "app.containers.Admin.communityMonitor.settings.preview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "L'utilisateur n'a pas déjà répondu à l'enquête au cours des 3 derniers mois.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "L'enquête n'a pas été proposée à l'utilisateur au cours des 3 derniers mois.", "app.containers.Admin.communityMonitor.settings.save": "Enregistrer", "app.containers.Admin.communityMonitor.settings.saved": "Enregistré", "app.containers.Admin.communityMonitor.settings.settings": "Paramètres", "app.containers.Admin.communityMonitor.settings.survey2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.surveySettings3": "Paramètres généraux", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Au chargement de la page d'accueil ou d'une page personnalisée.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonymiser toutes les données utilisateur", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "Toutes les réponses des utilisateurs seront anonymisées avant d'être enregistrées", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Les utilisateurs anonymes doivent malgré tout respecter les critères de participation définis dans les « Droits d'accès ». Les données des profils utilisateurs ne seront pas incluses dans l'export des données de l'enquête.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "Dans quelles conditions la fenêtre modale peut-elle être affichée aux utilisateurs ?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Qui sont les gestionnaires ?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Nombre total de réponses", "app.containers.Admin.communityMonitor.upsell.aiSummary": "Résumé de l'IA", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Activer l'Observatoire de communauté", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Cette fonctionnalité n'est pas incluse dans votre plan actuel. Adressez-vous à votre Spécialiste en participation Go Vocal ou à votre administrateur pour la débloquer.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Indice de sant<PERSON>", "app.containers.Admin.communityMonitor.upsell.learnMore": "En savoir plus", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Évolution de l'indice", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "L'Observatoire de communauté vous aide à garder une longueur d'avance grâce au suivi continu de la confiance des résidents, de leur satisfaction vis-à-vis des services et de la vie communautaire.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Accédez à des scores clairs, des citations percutantes et un rapport trimestriel à partager avec vos collègues ou les représentants élus.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Des scores faciles à interpréter et leur évolution dans le temps", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Des témoignages de résidents, résumées par IA", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Des questions adaptées au contexte de votre ville", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Des résidents sélectionnés aléatoirement sur la plateforme", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Des rapports PDF trimestriels, prêts à partager", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "Prenez le pouls de votre communauté et anticipez les problèmes", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Nombre", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Ordinateur ou autre", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Téléphone portable", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tablette", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Types d'appareils", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Type d'appareil", "app.containers.Admin.earlyAccessLabel": "Accès anticipé", "app.containers.Admin.earlyAccessLabelExplanation": "Il s'agit d'une nouvelle fonctionnalité disponible en accès anticipé.", "app.containers.Admin.emails.addCampaign": "<PERSON><PERSON>er un e-mail", "app.containers.Admin.emails.addCampaignTitle": "Créer un nouvel email", "app.containers.Admin.emails.allParticipantsInProject": "Tous les participants du projet", "app.containers.Admin.emails.allUsers": "Tous les utilisateurs", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Des e-mails automatisés sont envoyés automatiquement et sont déclenchés par les actions d'un utilisateur. Vous pouvez désactiver certains d'entre eux pour tous les utilisateurs de votre plateforme. Les autres e-mails automatisés ne peuvent pas être désactivés car ils sont nécessaires au bon fonctionnement de votre plateforme.", "app.containers.Admin.emails.automatedEmails": "Campagnes e-mail automatisées", "app.containers.Admin.emails.automatedEmailsDigest": "L'email ne sera envoyé que s'il y a du contenu", "app.containers.Admin.emails.automatedEmailsRecipients": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.automatedEmailsTriggers": "Événement qui déclenche cet e-mail", "app.containers.Admin.emails.changeRecipientsButton": "Changer les bénéficiaires", "app.containers.Admin.emails.clickOnButtonForExamples": "Cliquez sur le bouton ci-dessous pour consulter des exemples de cet e-mail sur notre page d'assistance.", "app.containers.Admin.emails.confirmSendHeader": "<PERSON><PERSON><PERSON> à tous les utilisateurs ?", "app.containers.Admin.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.draft": "Brouillon", "app.containers.Admin.emails.editButtonLabel": "Modifier", "app.containers.Admin.emails.editCampaignTitle": "Modifier campagne", "app.containers.Admin.emails.editDisabledTooltip2": "Bientôt disponible : Cet e-mail ne peut pas être édité pour le moment.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Texte du bouton", "app.containers.Admin.emails.editRegion_intro_multiloc": "Introduction", "app.containers.Admin.emails.editRegion_subject_multiloc": "Sujet", "app.containers.Admin.emails.editRegion_title_multiloc": "Titre", "app.containers.Admin.emails.emailCreated": "Email créé avec succès dans le brouillon", "app.containers.Admin.emails.emailUpdated": "E-mail mis à jour avec succès", "app.containers.Admin.emails.emptyCampaignsDescription": "Communiquez facilement avec vos participants en leur envoyant des e-mails. Choisissez les destinataires et suivez leur engagement.", "app.containers.Admin.emails.emptyCampaignsHeader": "Envoyez votre premier e-mail", "app.containers.Admin.emails.failed": "Échec", "app.containers.Admin.emails.fieldBody": "Message", "app.containers.Admin.emails.fieldBodyError": "Envoyer un e-mail", "app.containers.Admin.emails.fieldGroupContent": "Contenu de l'e-mail", "app.containers.Admin.emails.fieldReplyTo": "Les réponses devraient aller à", "app.containers.Admin.emails.fieldReplyToEmailError": "Fournissez une adresse e-mail au format correct, <NAME_EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Fournir une adresse e-mail", "app.containers.Admin.emails.fieldReplyToTooltip": "Choisissez quelle adresse email recevra directement toutes les réponses des utilisateurs.", "app.containers.Admin.emails.fieldSender": "De ", "app.containers.Admin.emails.fieldSenderError": "Fournir un expéditeur de l'e-mail", "app.containers.Admin.emails.fieldSenderTooltip": "Choisissez quels utilisateurs seront vu en tant qu'expéditeur de cette email.", "app.containers.Admin.emails.fieldSubject": "Sujet", "app.containers.Admin.emails.fieldSubjectError": "Indiquer l'objet de l'e-mail", "app.containers.Admin.emails.fieldSubjectTooltip": "<PERSON>ci sera visible dans la champ \"objet\" de l'email et dans la boîte emails des utilisateurs. Assurez que c'est clair et qu'il pousse à l'action.", "app.containers.Admin.emails.fieldTo": "À", "app.containers.Admin.emails.fieldToTooltip": "Choisissez quel(s) groupe(s) d'utilisateurs recevera votre email.", "app.containers.Admin.emails.formSave": "Enregistrer comme brouillon", "app.containers.Admin.emails.formSaveAsDraft": "Enregistrer comme brouillon", "app.containers.Admin.emails.from": "De :", "app.containers.Admin.emails.groups": "Groupes", "app.containers.Admin.emails.helmetDescription": "Envoyer des courriels manuels à certains groupe de citoyens et activez les campagnes automatisées", "app.containers.Admin.emails.nameVariablesInfo2": "Vous pouvez vous adresser directement aux citoyens en utilisant les variables {firstName} {lastName}. Par exemple « cher , {firstName} {lastName} ... »", "app.containers.Admin.emails.previewSentConfirmation": "Un email de prévisualisation a été envoyé à votre adresse e-mail", "app.containers.Admin.emails.previewTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.regionMultilocError": "Veuillez indiquer une valeur pour toutes les langues", "app.containers.Admin.emails.seeEmailHereText": "Une fois qu'un e-mail de ce type sera envoyé, vous pourrez le consulter ici.", "app.containers.Admin.emails.send": "Envoyer", "app.containers.Admin.emails.sendNowButton": "Envoyer maintenant", "app.containers.Admin.emails.sendTestEmailButton": "Envoyer moi un email de test", "app.containers.Admin.emails.sendTestEmailTooltip2": "Envoyer ce brouillon à une adresse email qui vous appartient pour vérifier à quoi ressemble l'email dans la \"vraie vie\".", "app.containers.Admin.emails.senderRecipients": "Expéditeur et destinataires", "app.containers.Admin.emails.sending": "En cours d'envoi", "app.containers.Admin.emails.sent": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sentToUsers": "Il s'agit d'e-mails envoyés aux utilisateurs", "app.containers.Admin.emails.subject": "Objet :", "app.containers.Admin.emails.supportButtonLabel": "Consultez les exemples sur notre page de support", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/fr/articles/7042664-modifier-les-parametres-des-notifications-automatiques-par-courrier-electronique", "app.containers.Admin.emails.to": "À :", "app.containers.Admin.emails.toAllUsers": "Vou<PERSON>z-vous envoyer ce courriel à tous les utilisateurs ?", "app.containers.Admin.emails.viewExample": "Voir", "app.containers.Admin.ideas.import": "Importer", "app.containers.Admin.inspirationHub.AllProjects": "Tous les projets", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Observatoire de communauté", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Annotation de document", "app.containers.Admin.inspirationHub.ExternalSurvey": "<PERSON>q<PERSON><PERSON><PERSON> externe", "app.containers.Admin.inspirationHub.Filters.Country": "Pays", "app.containers.Admin.inspirationHub.Filters.Method": "Méthode", "app.containers.Admin.inspirationHub.Filters.Search": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Topic": "Thème", "app.containers.Admin.inspirationHub.Filters.population": "Population", "app.containers.Admin.inspirationHub.Highlighted": "En vedette", "app.containers.Admin.inspirationHub.Ideation": "Idéation", "app.containers.Admin.inspirationHub.Information": "Information", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Veuillez choisir un pays pour voir les projets épinglés", "app.containers.Admin.inspirationHub.PinnedProjects.country": "Pays", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "Aucun projet n'a encore épinglé pour ce pays. Essayez de choisir un autre pays.", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Changez le pays pour voir plus de projets épinglés", "app.containers.Admin.inspirationHub.Poll": "Sondage", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "Lire la suite...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Phase {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "Si vous ne souhaitez pas que votre projet soit inclus dans le Pôle d'inspiration, contactez votre Spécialiste en participation GoVocal.", "app.containers.Admin.inspirationHub.Proposals": "Propositions", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Trier par", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Nombre de participants (croissant)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Nombre de participants (décroissant)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Date de début (croissant)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Date de début (décroissant)", "app.containers.Admin.inspirationHub.Survey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Une sélection de projets de qualité du monde entier.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Partagez vos expériences et apprenez d'autres professionnels dans le domaine.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filtrez par méthode de participation, pays et taille de la ville.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "<PERSON><PERSON> Pôle d'inspiration", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Cette fonctionnalité n'est pas incluse dans votre plan actuel. Adressez-vous à votre Spécialiste en participation Go Vocal ou à votre administrateur pour la débloquer.", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "https://support.govocal.com/en/articles/11093736-using-the-inspiration-hub", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "En savoir plus", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "Le Pôle d'inspiration vous donne accès à une sélection de projets participatifs provenant des plateformes Go Vocal du monde entier. Découvrez comment d'autres villes abordent la participation citoyenne et échangez des idées avec vos pairs.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Rejoignez un réseau de praticiens de la démocratie participative", "app.containers.Admin.inspirationHub.Volunteering": "Volontariat", "app.containers.Admin.inspirationHub.Voting": "Vote", "app.containers.Admin.inspirationHub.commonGround": "Consensus", "app.containers.Admin.inspirationHub.filters": "filtres", "app.containers.Admin.inspirationHub.resetFilters": "Réinitialiser les filtres", "app.containers.Admin.inspirationHub.seemsLike": "C'est tout pour le moment. Si vous souhaitez voir d'autres projets, essayez de changer les {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "Modifier les champs de la campagne e-mail", "app.containers.Admin.messaging.automated.variablesToolTip": "Vous pouvez utiliser les variables suivantes dans votre message :", "app.containers.Admin.messaging.helmetTitle": "Messagerie", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "Ce widget affiche une sélection personnalisée de projets pour chaque utilisateur <b>en fonction du contenu suivi sur la plateforme</b>. Cela inclut les projets suivis directement, les projets dont ils suivent des contributions spécifiques, ainsi que les projets associés aux sujets ou aux zones géographiques d'intérêt.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "Ce widget ne sera visible par l'utilisateur que s'il y a des projets auxquels il peut participer. Vous voyez ce message (en tant qu'administrateur) car il n'y a actuellement aucun projet ouvert à la participation pour vous. Ce message n'apparaîtra pas sur la page d'accueil en ligne.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Ouvert à la participation", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "Ce widget mettra en avant les projets auxquels l'utilisateur peut actuellement <b>participer</b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Titre", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "Pour vous", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "Ce widget ne sera visible par l'utilisateur que s'il y a des projets pertinents pour lui en fonction du contenu qu'il suit sur la plateforme. Vous voyez ce message, en tant qu'administrateur, car vous ne suivez actuellement aucun projet (directement ou indirectement). Ce message n'apparaîtra pas sur la page d'accueil en ligne.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "Contenu suivi", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Archivés", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Filtrer par", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Te<PERSON>in<PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Terminés et archivés", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "<PERSON><PERSON><PERSON> donnée disponible", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "Ce widget affiche les <b>projets terminés ou archivés</b>. Les projets « terminés » incluent également ceux qui sont dans leur dernière phase et que cette dernière est un rapport.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Projets terminés", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "La boucle est bouclée", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "Fournissez un nom dans toutes les langues", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "Le projet ne peut pas être vide", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Nom", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Projet", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "Enregistrer", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Ajouter un projet", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "Le projet sera affiché dans la barre de navigation uniquement si les utilisateurs y ont accès.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "Ce widget n\"est visible sur la page d'accueil que lorsque l'enquête de l'Observatoire de communauté est active.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Observatoire de communauté", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Description", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "Bouton", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Titre", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Important :", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Exemple de question à échelle de sentiment", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "Pas de date de fin", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "Appuyez sur Échap (Esc) pour passer le carrousel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projets et dossiers (ancienne version)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Titre du projet", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} travaille actuellement sur", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Texte du bouton", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Participez !", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Description", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "dossier", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Veuillez sélectionner un projet ou un dossier", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Sélectionnez un projet ou un dossier", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Afficher les avatars", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Spotlight", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Titre", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "Commence dans {days} jours", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Commence dans {weeks} semaines", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "Il y a {days} jours", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "Il y a {weeks} semaines", "app.containers.Admin.project.Campaigns.campaignFrom": "De :", "app.containers.Admin.project.Campaigns.campaignTo": "À :", "app.containers.Admin.project.Campaigns.customEmails": "E-mails personnalisés", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Envoyez des e-mails personnalisés et consultez leurs statistiques.", "app.containers.Admin.project.Campaigns.noAccess": "Désolé, il semble que vous n'ayez pas accès à la section des e-mails", "app.containers.Admin.project.emails.addCampaign": "<PERSON><PERSON>er un e-mail", "app.containers.Admin.project.emails.addCampaignTitle": "Nouvelle campagne d'e-mails", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Tous les {participants} et les followers du projet", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Sont compris ici les utilisateurs enregistrés ayant effectué une action dans le projet. Les utilisateurs non enregistrés ou anonymisés ne sont pas inclus.", "app.containers.Admin.project.emails.dateSent": "Date d'envoi", "app.containers.Admin.project.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.draft": "Brouillon", "app.containers.Admin.project.emails.editButtonLabel": "Modifier", "app.containers.Admin.project.emails.editCampaignTitle": "Modifier campagne", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Communiquez facilement avec vos participants en leur envoyant des e-mails. Choisissez les destinataires et suivez leur engagement.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Envoyez votre premier e-mail", "app.containers.Admin.project.emails.failed": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.fieldBody": "Message", "app.containers.Admin.project.emails.fieldBodyError": "Fournis<PERSON>z le contenu de l'e-mail dans toutes les langues", "app.containers.Admin.project.emails.fieldReplyTo": "Les réponses doivent être envoyées à", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Spécifiez une adresse e-mail au format correct (par exemple, <EMAIL>)", "app.containers.Admin.project.emails.fieldReplyToError": "Spécifiez une adresse e-mail", "app.containers.Admin.project.emails.fieldReplyToTooltip": "Choisissez l'adresse e-mail qui recevra les réponses directes à votre e-mail.", "app.containers.Admin.project.emails.fieldSender": "De", "app.containers.Admin.project.emails.fieldSenderError": "Spécifiez un expéditeur pour l'e-mail", "app.containers.Admin.project.emails.fieldSenderTooltip": "Choisissez qui apparaîtra comme expéditeur de l'e-mail.", "app.containers.Admin.project.emails.fieldSubject": "Sujet", "app.containers.Admin.project.emails.fieldSubjectError": "Indiquer l'objet de l'e-mail", "app.containers.Admin.project.emails.fieldSubjectTooltip": "Ceci apparaîtra dans la ligne d'objet de l'e-mail et dans l'aperçu de la boîte de réception de l'utilisateur. Utilisez un titre clair et accrocheur.", "app.containers.Admin.project.emails.fieldTo": "À", "app.containers.Admin.project.emails.formSave": "Enregistrer comme brouillon", "app.containers.Admin.project.emails.from": "De :", "app.containers.Admin.project.emails.helmetDescription": "Envoyez des e-mails aux participants du projet", "app.containers.Admin.project.emails.infoboxAdminText": "Depuis l'onglet Messagerie du projet, vous pouvez uniquement envoyer des e-mails aux participants du projet. Pour envoyer des e-mails à d'autres participants ou à des sous-ensembles d'utilisateurs, rendez-vous sur l'onglet {link}.", "app.containers.Admin.project.emails.infoboxLinkText": "Messagerie", "app.containers.Admin.project.emails.infoboxModeratorText": "Depuis l'onglet Messagerie du projet, vous pouvez uniquement envoyer des e-mails aux participants du projet. Les administrateurs peuvent envoyer des e-mails à d'autres participants ou à des sous-ensembles d'utilisateurs via l'onglet Messagerie de la plateforme.", "app.containers.Admin.project.emails.message": "Message", "app.containers.Admin.project.emails.nameVariablesInfo2": "Vous pouvez vous adresser directement aux citoyens en utilisant les variables {firstName} {lastName}. Par exemple, « Cher {firstName} {lastName}, ... »", "app.containers.Admin.project.emails.participants": "participants", "app.containers.Admin.project.emails.previewSentConfirmation": "Un e-mail de prévisualisation a été envoyé à votre adresse e-mail", "app.containers.Admin.project.emails.previewTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.projectParticipants": "Participants du projet", "app.containers.Admin.project.emails.recipients": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.send": "Envoyer", "app.containers.Admin.project.emails.sendTestEmailButton": "Envoyer un aperçu", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Envoyez le brouillon de cet e-mail à l'adresse e-mail avec laquelle vous êtes connecté(e) pour vérifier son rendu.", "app.containers.Admin.project.emails.senderRecipients": "Expéditeur et destinataires", "app.containers.Admin.project.emails.sending": "Envoi en cours", "app.containers.Admin.project.emails.sent": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sentToUsers": "Ces e-mails ont été envoyés aux utilisateurs", "app.containers.Admin.project.emails.status": "Statuts", "app.containers.Admin.project.emails.subject": "Objet :", "app.containers.Admin.project.emails.to": "À :", "app.containers.Admin.project.messaging.helmetTitle": "Messagerie", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "Cette image fait partie de la carte du dossier ; la carte qui résume le dossier et qui est affichée sur la page d'accueil par exemple. Pour plus d'informations sur les résolutions d'image recommandées, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Cette image est affichée en haut de la page du dossier. Pour plus d'informations sur les résolutions d'image recommandées, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "visitez notre centre d'assistance", "app.containers.Admin.projects.all.askPersonalData3": "Inclure les champs pour le nom et l'adresse e-mail", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "Activer l'affichage du calendrier", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "Cette fonctionnalité n'est pas incluse dans votre plan actuel. Adressez-vous à votre Spécialiste en participation Go Vocal ou à votre administrateur pour la débloquer.", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "En savoir plus", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "Obtenez une vue d'ensemble des échéances de vos projets grâce à notre calendrier. Identifiez rapidement les projets et les phases qui commencent ou se terminent bientôt et qui nécessitent une action.", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "Comprendre ce qui se passe et quand", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Le PDF contient toutes les questions. Toutefois, les types de questions suivants ne sont pas encore pris en charge par l’importation via FormSync : images, étiquettes et téléchargement de fichiers.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Le PDF contient toutes les questions. Toute<PERSON>is, les types de questions suivants ne sont pas encore pris en charge par l’importation via FormSync : questions cartographiques (placer un repère, dessiner un tracé et délimiter une zone), questions de classement, questions matricielles et questions de téléchargement de fichiers.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "Fin du formulaire", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Début du formulaire", "app.containers.Admin.projects.all.components.archived": "Archivé", "app.containers.Admin.projects.all.components.draft": "Brouillon", "app.containers.Admin.projects.all.components.manageButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.copyProjectButton": "Copie du projet", "app.containers.Admin.projects.all.copyProjectError": "Une erreur s'est produite lors de la copie de ce projet, veuillez réessayer plus tard.", "app.containers.Admin.projects.all.customiseEnd": "Personnaliser la fin du formulaire.", "app.containers.Admin.projects.all.customiseStart": "Personnaliser le début du formulaire.", "app.containers.Admin.projects.all.deleteFolderButton1": "Supp<PERSON>er le dossier", "app.containers.Admin.projects.all.deleteFolderConfirm": "Êtes-vous certain. e de vouloir supprimer ce dossier ? Tous les projets contenus dans ce dossier seront également supprimés. Vous ne pourrez pas annuler cette action.", "app.containers.Admin.projects.all.deleteFolderError": "Il y a eu un problème de suppression de ce dossier. Veuillez réessayer.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Supprimer le projet", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Êtes-vous sûr de que vouloir supprimer ce projet de manière définitive ?", "app.containers.Admin.projects.all.deleteProjectError": "Une erreur est survenue lors de la suppression du projet, veuil<PERSON>z réessayer.", "app.containers.Admin.projects.all.exportAsPDF1": "Télécharger le formulaire PDF", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Vous pouvez combiner des réponses en ligne et hors ligne. Pour importer des réponses hors ligne, rendez-vous dans l'onglet « Gestion des contributions » de ce projet, puis cliquez sur « Importer ».", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Vous pouvez combiner des réponses en ligne et hors ligne. Pour importer des réponses hors ligne, rendez-vous dans l'onglet « Enquête » de ce projet, puis cliquez sur « Importer ».", "app.containers.Admin.projects.all.logicNotInPDF": "Il n'est pas possible de reproduire les sauts logiques dans le PDF téléchargé. Les répondants sur papier verront donc toutes les questions de l'enquête.", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "Tous les dossiers ont été chargés", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Dossier", "app.containers.Admin.projects.all.new.Folders.Table.managers": "Gestionnaires", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# project} other {# projets}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Statut", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Début du projet", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Découvrabilité", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "Caché", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "Public", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Dossiers", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filtrer par méthode de participation de la phase en cours", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "Idéation", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Information", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Méthode de participation", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Annotation de document", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Consensus", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "Sondage", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Propositions", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Volontariat", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Vote", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "Ouvert aux contributions", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Information", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "À venir", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "État de participation", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "En attente d'approbation", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "Rechercher des projets", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Alphabétique (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Alphabétique (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "Gestionnaire", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Début ou fin de phase proche", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON> (new-old)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc": "Récemment créé (ancien-nouveau)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "Consultés récemment", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Statuts", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Administrateurs", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Groupes", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Visibilité", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "Public", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "Ajouter un filtre", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "Aucun autre filtre à ajouter", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Administrateurs", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Tous les projets ont été chargés", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "<PERSON>ut le monde", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Archivé", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "Phase en cours", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "débute dans {days} jours", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "encore {days} jours", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Découvrabilité :", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Brouillon", "app.containers.Admin.projects.all.new.Projects.Table.end": "Fin", "app.containers.Admin.projects.all.new.Projects.Table.ended": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "Se termine aujourd'hui", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Groupes", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "Caché", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "Chargement en cours…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "Gestionnaire", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "encore {months} mois", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "débute dans {months} mois", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Phase suivante :", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "Non assigné", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Phase", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "Pré-lancement", "app.containers.Admin.projects.all.new.Projects.Table.project": "Projet", "app.containers.Admin.projects.all.new.Projects.Table.public": "Public", "app.containers.Admin.projects.all.new.Projects.Table.published": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Faites défiler pour charger la suite", "app.containers.Admin.projects.all.new.Projects.Table.start": "D<PERSON>but", "app.containers.Admin.projects.all.new.Projects.Table.status": "Statut", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Statut :", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "Cette colonne affiche des données de participants mises en cache. Pour consulter les chiffres les plus récents, consultez l’onglet « Participants » du projet.", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Visibilité", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Visibilité :", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} groupes", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} gestionnaires", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "encore {years} ans", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "débute dans {years} ans", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Phase actuelle : {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} jours restants", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Dossier : {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "Aucune phase en cours", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "Pas de date de fin", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "Aucune phase", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Phase {number} : {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Phases :", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Projet", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Date de début : {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "Organiser des projets", "app.containers.Admin.projects.all.new.calendar": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.folders": "Dossiers", "app.containers.Admin.projects.all.new.projects": "Projets", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "Le chargement de la ligne du temps a échoué.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Le projet n'a pas de date de fin", "app.containers.Admin.projects.all.new.timeline.project": "Projet", "app.containers.Admin.projects.all.notes": "Notes", "app.containers.Admin.projects.all.personalDataExplanation5": "Cette option ajoutera les champs prénom, nom et adresse e-mail au PDF exporté. Ces informations permettront de créer automatiquement un compte pour les participants hors ligne lors de l’import des formulaires papier numérisés.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "Résumé", "app.containers.Admin.projects.project.analysis.Comments.comments": "Commentaires", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "La fonction résumé est uniquement disponible à partir de 5 commentaires.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Résumer les commentaires", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {<PERSON><PERSON><PERSON><PERSON><PERSON>} =1 {1 nouveau commentaire} other {# nouveaux commentaires}}", "app.containers.Admin.projects.project.analysis.aiSummary": "Résumé de l'IA", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "Ce contenu a été généré par un modèle d'intelligence artificielle. Il peut ne pas être totalement exact. Nous vous recommandons de le vérifier en le recoupant avec les contributions d'origine. Notez que la précision est susceptible de s'améliorer si le nombre de contributions sélectionnées est réduit.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Les notifications par e-mail sont envoyées uniquement aux participants", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "Caché", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "Non référencé par les moteurs de recherche", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Non visible sur la page d'accueil ni dans les widgets", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Uniquement accessible via l'URL direct", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "Public", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "Sélectionnez le degré de découvrabilité de ce projet.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "Ce projet est visible par tous ceux qui y ont accès et apparaîtra sur la page d'accueil ainsi que dans les widgets.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Ce projet ne sera pas diffusé publiquement et ne sera accessible qu’aux personnes disposant du lien.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Qui peut trouver ce projet ?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Accéder à l'outil d'analyse IA", "app.containers.Admin.projects.project.ideas.analysisText2": "Explorez les résumés générés par l'IA et consultez les contributions individuelles.", "app.containers.Admin.projects.project.ideas.importInputs": "Importer", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "Après avoir créé un rapport, vous pouvez choisir de le partager avec le public dès que le début de la phase.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "créer une page plus élaborée pour le partage d'informations.", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Créez un rapport pour :", "app.containers.Admin.projects.project.information.ReportTab.createReport": "<PERSON><PERSON><PERSON> un rapport", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Générez un rapport basé sur une phase précédente ou commencez avec une page blanche.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "Ce rapport n'est pas public. Pour le rendre public, activez l'option « Publié ».", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "Cette phase a débuté, mais le rapport n'est pas encore public. Pour le rendre public, activez l'option « Publié ».", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Rapport de phase", "app.containers.Admin.projects.project.information.ReportTab.report": "Rapport", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "partager les résultats d'une enquête ou d'une phase de vote passée ;", "app.containers.Admin.projects.project.information.ReportTab.visible": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "Ce rapport sera publiquement visible dès le début de la phase. <PERSON><PERSON> le masquer, désactivez l'option « Publié ».", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "Ce rapport est actuellement public. Po<PERSON> le masquer, désactivez l'option « Publié ».", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Êtes-vous sûr de vouloir supprimer ce rapport ? Cette action ne peut pas être annulée.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Ajouter à la phase", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "Vous devez donner votre accord avant de pouvoir continuer", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "Le formulaire peut être téléchargé ici.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "Le formulaire téléchargé a été créé avec la section \"Données personnelles\"", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Langue du formulaire", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "Je consens au traitement de ce fichier à l'aide de Google Cloud Form Parser", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Importer un fichier Excel", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Veuillez télécharger un fichier pour continuer", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "Le modèle peut être téléchargé ici.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Télécharger", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Téléchargez un <b><PERSON><PERSON><PERSON> Excel</b> (.xlsx) complété. Il doit utiliser le modèle fourni pour ce projet. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Téléchargez un <b>fichier PDF de formulaires scannés</b>. Les formulaires scannés doivent correspondre au modèle généré pour cette phase. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Utiliser cette adresse e-mail pour le nouvel utilisateur", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Entrez une adresse e-mail valide pour créer un nouveau compte", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Un nouveau compte utilisateur sera créé pour l'auteur avec les détails suivants. Cette contribution lui sera attribuée.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "Prénom", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Nom", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Veuillez saisir une adresse e-mail ou un nom et un prénom pour attribuer cette contribution à un auteur, ou bien décochez la case de consentement.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Il existe déjà un compte associé à cette adresse e-mail. Cette contribution sera associée à ce compte.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Consentement de l'utilisateur (création d'un compte utilisateur)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "Approuver", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Approuver toutes les contributions", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Auteur:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "Adresse e-mail :", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Des erreurs se sont produites lors de l'importation et certaines contributions n'ont pas pu être importées. Veuillez corriger les erreurs et réimporter les contributions manquantes.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Les données du formulaire ne sont pas valides. Veuillez vérifier que le formulaire ci-dessus ne contient pas d'erreurs.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Importer un fichier Excel (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Importer", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Importer des formulaires numérisés (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Importer des formulaires numérisés", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "Entrées importées", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importation en cours. Ce processus peut prendre quelques minutes.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "<PERSON>tte contribution a été importée de manière anonyme.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} contributions ont été importées et doivent encore être approuvées.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} n'ont pas pu être approuvées. Veuillez vérifier chaque entrée pour les problèmes de validation et confirmer individuellement.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Langue :", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Rien à vérifier pour le moment. Cliquez sur \"{importFile}\" pour importer un fichier PDF ou Excel contenant les contributions.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Aucune contribution à vérifier pour le moment. Cliquez sur « {importFile} » pour importer un fichier Excel contenant des contributions.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "Contribution", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "Page", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Impossible d'afficher le fichier importé. La visualisation des fichiers importés n'est disponible que pour les fichiers au format PDF.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Phase :", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "La phase sélectionnée ne peut pas contenir d'idées. Veuillez en sélectionner une autre.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Ce projet ne contient aucune phase pouvant contenir des idées.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "Veuillez sélectionner la phase à laquelle vous souhaitez ajouter ces contributions.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Importateur de contributions", "app.containers.Admin.projects.project.participation.comments": "Commentaires", "app.containers.Admin.projects.project.participation.inputs": "Entrées", "app.containers.Admin.projects.project.participation.participantsTimeline": "Participants", "app.containers.Admin.projects.project.participation.reactions": "Réactions", "app.containers.Admin.projects.project.participation.selectPeriod": "Sélectionnez la période", "app.containers.Admin.projects.project.participation.usersByAge": "Utilisateurs par âge", "app.containers.Admin.projects.project.participation.usersByGender": "Utilisateurs par genre", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "Requis", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "Ajouter une question", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "La configuration de champs utilisateur au niveau de la phase n'est pas inclus dans votre licence actuelle. Contactez votre Spécialiste en participation Go Vocal pour en savoir plus.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} options", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "Statut du champ", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "Ces questions seront ajoutées comme dernière page du formulaire d'enquête, car « Inclure les champs démographiques dans l'enquête » a été sélectionné dans les paramètres de la phase.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "Aucune question supplémentaire ne sera posée.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Optionnel", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Optionnel - toujours activé car référencé par groupe", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "<PERSON><PERSON><PERSON><PERSON> le champ", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Obligatoire - toujours activée car référencée par groupe", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Authentifiez-vous avec {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Répondez aux questions supplémentaires ci-dessous", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Confirmez votre adresse e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Données fournies par la méthode de vérification :", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Entrez votre prénom, nom, adresse e-mail et mot de passe", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "Entrez votre adresse e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "À quelle fréquence les utilisateurs doivent-ils être vérifiés ?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Vérification de l'identité à l'aide de {verificationMethod} (en fonction du groupe d'utilisateurs)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "Aucune action n'est requise pour participer", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Utilisez des \"groupes intelligents\" pour restreindre la participation en fonction des critères ci-dessus", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Les utilisateurs doivent avoir été vérifiés au cours des 30 dernières minutes.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Les utilisateurs doivent avoir été vérifiés au cours des derniers {days} jours.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "Au cours des 30 derniers jours", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "Dans les 30 dernières minutes", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "Au cours des 7 derniers jours", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "Une fois suffit", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Champs vérifiés :", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "Vérification {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Création d'un compte", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Les participants doivent créer un compte complet avec leur nom, une adresse e-mail confirmée et un mot de passe.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Les participants doivent créer un compte complet avec leur nom, une adresse e-mail et un mot de passe.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Authentification", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "Modifier", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Confirmation de l'adresse e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Les participants doivent confirmer leur adresse e-mail avec un code à usage unique.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Détection avancée des spams", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "Cette fonctionnalité permet d’éviter que des utilisateurs non connectés répondent plusieurs fois à l'enquête, en analysant les adresses IP et les données liées à l’appareil. Bien qu’elle soit moins précise que l’obligation de se connecter, elle peut aider à limiter les réponses multiples.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Remarque : Sur les réseaux partagés, tels que ceux des bureaux ou les réseaux Wi-Fi publics, il existe un faible risque que différents utilisateurs soient signalés par erreur comme des doublons.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Activer la détection avancée des spams", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Questions supplémentaires posées aux participants", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "Aucune", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Tout le monde peut participer sans s'inscrire ni se connecter.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Réinitialiser les questions supplémentaires et les groupes", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Restreindre la participation à certains groupes d'utilisateurs", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "Vérification SSO", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Les participants doivent vérifier leur identité via {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Accéder à l'outil d'analyse IA", "app.containers.Admin.projects.project.survey.allFiles": "To<PERSON> les fichiers", "app.containers.Admin.projects.project.survey.allResponses": "Toutes les réponses", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Précision : {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Une erreur s'est produite lors de la génération du résumé IA. Veuillez essayer de le régénérer ci-dessous.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Accéder à l'outil d'analyse IA", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "Masquer les résumés pour cette question", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "entrées sélectionnées", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Accéder aux outils d'analyse", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} nouvelles réponses", "app.containers.Admin.projects.project.survey.analysis.regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Afficher les analyses IA", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "Votre plan actuel vous permet de résumer un maximum de 30 contributions à la fois. Adressez-vous à votre Spécialiste en participation Go Vocal ou à votre administrateur pour augmenter cette limite.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Sélectionner les questions à analyser", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "Voulez-vous ajouter d'autres questions liées à votre analyse de {question} ?", "app.containers.Admin.projects.project.survey.cancel": "Annuler", "app.containers.Admin.projects.project.survey.consentModalButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCancel": "Annuler", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "J'accepte qu'OpenAI soit utilisé comme processeur de données pour ce projet", "app.containers.Admin.projects.project.survey.consentModalText1": "En continuant, vous acceptez que l'OpenAI soit utilisée comme processeur de données pour ce projet.", "app.containers.Admin.projects.project.survey.consentModalText2": "Les API d'OpenAI sont employées pour la création de résumés automatisés et interviennent dans divers éléments de l'expérience de classification automatique des contributions.", "app.containers.Admin.projects.project.survey.consentModalText3": "Nous n'envoyons aux API d'OpenAI que le contenu rédigé par les utilisateurs dans leurs enquêtes, idées et commentaires. En aucun cas, des données issues de leur profil utilisateur ne sont partagées.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI n'exploitera pas ces données afin d'améliorer ses modèles ultérieurs. Pour de plus amples détails sur la gestion de la confidentialité des données par OpenAI, veuillez consulter le lien suivant : {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "ici", "app.containers.Admin.projects.project.survey.consentModalTitle": "Avant de poursuivre", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "Vous ne pouvez pas accéder à l'analyse avant d'avoir sauvegardé le formulaire", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Annuler", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Êtes-vous sûr de vouloir supprimer cette analyse ? Cette action ne peut pas être annulée.", "app.containers.Admin.projects.project.survey.explore": "Explorer", "app.containers.Admin.projects.project.survey.followUpResponses": "Réponses de suivi", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> en moyenne", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Exporter au format GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Exportez les réponses à cette question dans un fichier GeoJSON. Chaque réponse apparaîtra sous la forme d'une entité GeoJSON. Pour chaque entité, toutes les autres réponses de cette personne aux autres questions de l'enquête seront aussi listées dans l'objet \"properties\" de cette entité.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "Masquer les détails", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} répondant} one {{respondentCount} répondant} other {{respondentCount} répondants}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "Voir les détails", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} choix} one {{numberChoices} choix} other {{numberChoices} choix}}", "app.containers.Admin.projects.project.survey.heatMap": "Carte thermique", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "En savoir plus sur les cartes thermiques générées à l'aide de Esri Smart Mapping.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "La carte thermique est générée à l'aide de Esri Smart Mapping. Les cartes thermiques sont particulièrement utiles lorsque la densité de points est élevée. Lorsque le nombre de points est plus restreint, il peut être plus intéressant de visualiser les points directement. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Vue carte thermique", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Caché par la logique", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "Si cette réponse est sélectionnée, le répondant sera directement redirigé vers la page {pageNumber} (en passant {numQuestionsSkipped} questions). Cliquez pour afficher ou masquer les pages et questions ignorées.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "Si cette réponse est sélectionnée, le répondant sera directement redirigé vers la fin de l'enquête (en passant {numQuestionsSkipped} questions). Cliquez pour afficher ou masquer les pages et questions ignorées.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "La règle logique de cette page redirige directement vers la page {pageNumber} (en passant {numQuestionsSkipped} questions). Cliquez pour afficher ou masquer les pages et questions ignorées.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "La règle logique de cette page redirige directement vers la fin de l'enquête (en passant {numQuestionsSkipped} questions). Cliquez pour afficher ou masquer les pages et questions ignorées.", "app.containers.Admin.projects.project.survey.newAnalysis": "Nouvelle analyse", "app.containers.Admin.projects.project.survey.nextInsight": "Insight suivant", "app.containers.Admin.projects.project.survey.openAnalysis": "Accéder à l'outil d'analyse IA", "app.containers.Admin.projects.project.survey.otherResponses": "Autres réponses", "app.containers.Admin.projects.project.survey.page": "Page", "app.containers.Admin.projects.project.survey.previousInsight": "Insight précédent", "app.containers.Admin.projects.project.survey.responses": "Réponses", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "Le nombre de réponses pour cette page est inférieur au nombre total de réponses à l'enquête car certains répondants n'ont pas vu cette page en raison des règles logiques de l'enquête.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "Le nombre de réponses à cette question est inférieur au nombre total de réponses à l'enquête parce que certains répondants n'ont pas vu cette question en raison de la logique de l'enquête.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Échelle de sentiment", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Résumez automatiquement toutes vos réponses.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Questionnez vos données en langage naturel.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Étoffez les résumés générés par l'IA avec des références directes aux réponses individuelles.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Consultez notre {link} pour un aperçu complet.", "app.containers.Admin.projects.project.survey.upsell.button": "<PERSON><PERSON><PERSON><PERSON><PERSON> l'Assistant IA", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "article d'assistance", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/fr/articles/8316692-analyse-soutenue-par-l-intelligence-artificielle", "app.containers.Admin.projects.project.survey.upsell.title": "Analysez les réponses aux enquêtes plus rapidement avec l'Assistant IA", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "Cette fonctionnalité n'est pas incluse dans votre plan actuel. Adressez-vous à votre GovSuccess ou à votre administrateur pour la débloquer.", "app.containers.Admin.projects.project.survey.viewAnalysis": "Voir", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Explorez les résumés générés par l'IA et consultez les contributions individuelles.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Sélectionnez la période", "app.containers.Admin.projects.project.traffic.trafficSources": "Sources de trafic", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Nous avons modifié la façon dont nous collectons et affichons les données relatives aux visiteurs. En conséquence, les données sur les visiteurs sont plus précises et davantage d'informations sont disponibles, tout en restant conformes au RGPD. Nous avons commencé à collecter ces nouvelles données seulement en novembre 2024, les données ne sont donc pas disponibles avant cette date.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Visiteurs", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Rapport de phase", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Ajoutez un texte introductif ou une explication à propos de la phase", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Voici du texte. V<PERSON> pouvez le modifier et le mettre en forme en utilisant l'éditeur dans le panneau de droite.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Participants", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Résultats du projet", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Résumé du rapport", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "<PERSON><PERSON><PERSON>z le but du projet, les méthodes de participation utilisées et le résultat", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Visiteurs", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Ce rapport contient des modifications non enregistrées. Veuillez le sauvegarder avant de l'imprimer.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Ce titre est déjà utilisé", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "comparé aux {days} jours précédents", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Masquer les statistiques", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Taux de participation", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Afficher la comparaison avec la période précédente", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "<PERSON><PERSON> de<PERSON> d'abord sélect<PERSON>ner une période.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "Commentaires", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Contributions", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Participation", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Afficher les commentaires", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "<PERSON><PERSON><PERSON><PERSON> les contributions", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Affiche<PERSON> les votes", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "Votes", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Démographie", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Date d'enregistrement", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Champ du profil utilisateur", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Inconnu", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Utilisateurs : {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Actif", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Archivé", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "Planifié", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projets", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Statut de publication", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "Aucune donnée n'est disponible pour ce widget. Reconfigurez-le ou supprimez-le pour pouvoir enregistrer le rapport.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Observatoire de communauté", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "Trimestre", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "T4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "T1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "T3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "T2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "Aucune phase appropriée n'a été trouvée dans ce projet", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "Aucune phase n'a été sélectionnée. Veuillez d'abord sélectionner une phase.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "Aucun projet", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Aucun projet n'a été sélectionné. Veuillez d'abord sélectionner un projet.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "Vous ne pouvez pas dupliquer ce rapport car il contient des données auxquelles vous n'avez pas accès.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "Vous ne pouvez pas modifier ce rapport, car il contient des données auxquelles vous n'avez pas accès.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Êtes-vous sûr de vouloir supprimer « {reportName} » ? Cette action est irréversible.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Êtes-vous sûr de vouloir supprimer ce rapport ? Cette action est irréversible.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Modifier", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "Modifié il y a {days, plural, no {# jour} one {# jour} other {# jours}}", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Une erreur s'est produite lors de la création de ce rapport. Veuillez réessayer plus tard.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Page blanche", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Rapport de l'Observatoire de communauté", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Titre", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "<PERSON><PERSON><PERSON> un rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Personnalisez votre rapport et partagez-le avec vos collaborateurs ou la communauté au format PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "<PERSON><PERSON><PERSON> un rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Créez votre premier rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "Aucun projet sélectionné", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Rapport de plateforme", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Imprimer en PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Rapport de projet", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Trimestre {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Un rapport portant ce titre existe déjà. Veuillez choisir un autre titre.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Sélectionnez le trimestre", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Sélectionnez l'année", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Partager au format PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Pour le partager avec tout le monde, imprimez le rapport au format PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Partager comme lien web", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "Ce lien web est uniquement accessible aux utilisateurs de l'administration.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Partager", "app.containers.Admin.reporting.contactToAccess": "La création d'un rapport personnalisé fait partie de la licence premium. Contactez votre responsable GovSuccess pour en savoir plus.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Tous", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Rapports de l'Observatoire de communauté", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Ces rapports trimestriels sont automatiquement créés par l'Observatoire de communauté.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "<PERSON><PERSON><PERSON> un rapport", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Personnalisez votre rapport et partagez-le avec les parties prenantes internes ou la communauté avec un lien web.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Vos rapports apparaîtront ici.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Rapports d'avancement", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Ces rapports ont été créés par votre Government Success Manager", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "Vos rapports", "app.containers.Admin.reporting.deprecated": "FONCTIONNALITÉ OBSOLÈTE", "app.containers.Admin.reporting.helmetDescription": "Page de rapport de l'administrateur", "app.containers.Admin.reporting.helmetTitle": "Rapports", "app.containers.Admin.reporting.printPrepare": "Préparation de l'impression...", "app.containers.Admin.reporting.reportBuilder": "Rapports", "app.containers.Admin.reporting.reportHeader": "En-tête du rapport", "app.containers.Admin.reporting.warningBanner3": "Les graphiques et les chiffres de ce rapport ne se mettent à jour automatiquement que sur cette page. Enregistrez le rapport pour les actualiser sur les autres pages.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Consensus", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Idéation", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Information", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Méthodes de participation utilisées", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "Sondage", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "{days} jours précédents : {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Propositions", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "<PERSON>q<PERSON><PERSON><PERSON> externe", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Volontariat", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Vote", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Graphique", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "<PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "Voir", "app.containers.Admin.surveyFormTab.downloads": "Téléchargements", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "<PERSON>p<PERSON><PERSON> une autre enquête", "app.containers.Admin.surveyFormTab.editSurveyForm": "Modifier le formulaire", "app.containers.Admin.surveyFormTab.inputFormDescription": "Précisez les informations à fournir, ajou<PERSON>z de courtes descriptions ou instructions pour guider les réponses des participants, et indiquez si chaque champ est facultatif ou obligatoire.", "app.containers.Admin.surveyFormTab.surveyForm": "Formulaire de l'enquête", "app.containers.Admin.tools.apiTokens.createTokenButton": "<PERSON><PERSON>er un nouveau jeton", "app.containers.Admin.tools.apiTokens.createTokenCancel": "Annuler", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Votre jeton a été créé avec succès. Copiez le {secret} ci-dessous et conservez-le en lieu sûr.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Créez un nouveau jeton à utiliser avec notre API publique.", "app.containers.Admin.tools.apiTokens.createTokenError": "Donnez un nom à votre jeton", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "<PERSON><PERSON><PERSON> un jeton", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>Important !</b> Vous ne pourrez consulter ce {secret} qu'une seule fois. Une fois cette page fermée, il ne sera plus accessible.", "app.containers.Admin.tools.apiTokens.createTokenName": "Nom", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Donnez un nom à votre jeton", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Votre jeton a été créé", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "<PERSON><PERSON><PERSON> {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Copié !", "app.containers.Admin.tools.apiTokens.createTokenTitle": "<PERSON><PERSON>er un nouveau jeton", "app.containers.Admin.tools.apiTokens.createdAt": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.delete": "<PERSON><PERSON><PERSON><PERSON> le jeton", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Êtes-vous sûr de vouloir supprimer ce jeton ?", "app.containers.Admin.tools.apiTokens.description": "<PERSON><PERSON><PERSON> vos jetons d'API pour notre API publique. Pour plus d'informations, consultez notre site {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Dernière utilisation", "app.containers.Admin.tools.apiTokens.link": "Documentation de l'API", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Nom", "app.containers.Admin.tools.apiTokens.noTokens": "Vous n'avez pas encore de jetons.", "app.containers.Admin.tools.apiTokens.title": "Jetons d'API publique", "app.containers.Admin.tools.esriDisabled": "L'intégration Esri est un add-on. Contactez votre responsable GovSuccess si vous souhaitez plus d'informations à ce sujet.", "app.containers.Admin.tools.esriIntegration2": "Intégration Esri", "app.containers.Admin.tools.esriIntegrationButton": "<PERSON><PERSON>", "app.containers.Admin.tools.esriIntegrationDescription3": "Connectez votre compte Esri et importez vos couches cartographiques depuis ArcGIS Online directement dans vos projets.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Logo Esri", "app.containers.Admin.tools.esriKeyInputDescription": "Ajoutez votre clé API Esri pour permettre l'importation de vos couches cartographiques depuis ArcGIS Online depuis l'onglet \"Carte\" de vos projets.", "app.containers.Admin.tools.esriKeyInputLabel": "Clé API Esri", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Collez la clé API ici", "app.containers.Admin.tools.esriMaps": "<PERSON><PERSON>", "app.containers.Admin.tools.esriSaveButtonError": "Une erreur s'est produite lors de l'enregistrement de votre clé, veuillez réessayer.", "app.containers.Admin.tools.esriSaveButtonSuccess": "Clé API enregistrée", "app.containers.Admin.tools.esriSaveButtonText": "Enregistrer la clé", "app.containers.Admin.tools.learnMore": "En savoir plus", "app.containers.Admin.tools.managePublicAPIKeys": "<PERSON><PERSON>rer les clés API", "app.containers.Admin.tools.manageWidget": "<PERSON><PERSON><PERSON> le widget", "app.containers.Admin.tools.manageWorkshops": "G<PERSON>rer les ateliers numériques", "app.containers.Admin.tools.powerBIAPIImage": "Image Power BI", "app.containers.Admin.tools.powerBIDescription": "Utilisez nos modèles Power BI prêts à l'emploi pour accéder aux données de Go Vocal dans votre espace de travail Microsoft Power BI.", "app.containers.Admin.tools.powerBIDisabled1": "L'intégration avec Power BI n'est pas incluse dans votre licence actuelle. Contactez votre Spécialiste en participation pour en savoir plus.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Télécharger les modèles", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "Si vous souhaitez d'utiliser vos données Go Vocal dans un flux de données Power BI, ce modèle vous permettra de configurer un nouveau flux de données qui se connecte à vos données Go Vocal. Une fois que vous avez téléchargé ce modèle, vous devez d'abord trouver et remplacer les chaînes suivantes ##CLIENT_ID## et ##CLIENT_SECRET## dans le modèle avec vos identifiants pour l'API publique avant de charger le template dans Power BI.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Téléchargez le modèle de flux de données", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Modèle de flux de données", "app.containers.Admin.tools.powerBITemplates.intro": "Remarque : Pour utiliser l'un de ces modèles Power BI, vous devez d'abord {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "créer des identifiants pour notre API publique", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "Ce modèle permet de créer un rapport Power BI basé sur vos données Go Vocal. Il configurera toutes les connexions de données à votre plateforme, créera le modèle de données, et quelques tableaux de bord par défaut. Lorsque vous ouvrirez le modèle dans Power BI, vous serez invité(e) à saisir vos identifiants pour l'API publique de Go Vocal, ainsi que l'URL de base de votre plateforme, à savoir : {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Téléchargez le modèle de rapport", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "<PERSON><PERSON><PERSON><PERSON> de rapport", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Pour plus de détails sur l'utilisation de vos données Go Vocal dans Power BI, consultez notre site {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "article de support", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/fr/articles/8512834-utilisez-les-donnees-de-citizenlab-dans-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Modèles Power BI", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "<PERSON><PERSON><PERSON> les clés d'accès nécessaires pour créer des intégrations personnalisées avec notre API publique.", "app.containers.Admin.tools.publicAPIDisabled1": "L'API publique n'est pas incluse dans votre licence actuelle. Contactez votre Spécialiste en participation pour en savoir plus.", "app.containers.Admin.tools.publicAPIImage": "Image de l'API publique", "app.containers.Admin.tools.publicAPITitle": "Accès à l'API publique", "app.containers.Admin.tools.toolsLabel": "Outils", "app.containers.Admin.tools.widgetDescription": "Vous pouvez créer un widget, le personnaliser et l'ajouter à votre propre site web pour attirer les internautes sur cette plateforme.", "app.containers.Admin.tools.widgetImage": "Image du widget", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "Organisez des réunions vidéo en ligne pour faciliter des discussions et des débats de groupe simultanés. Recueillez des avis, organisez des votes et parvenez à un consensus, tout comme vous le feriez hors ligne.", "app.containers.Admin.tools.workshopsImage": "Image des ateliers numériques", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/fr/articles/4155778-comment-mettre-en-place-un-atelier-numerique", "app.containers.Admin.tools.workshopsTitle": "Ateliers de délibération en ligne", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "nombre total sur la plate-forme", "app.containers.AdminPage.DashboardPage._blank": "inconnu", "app.containers.AdminPage.DashboardPage.allGroups": "Tous les groupes", "app.containers.AdminPage.DashboardPage.allProjects": "{tenant<PERSON><PERSON>, select, frw {Toutes les communes} other {Tous les projets}}", "app.containers.AdminPage.DashboardPage.allTime": "Jusqu’à maintenant", "app.containers.AdminPage.DashboardPage.comments": "Commentaires", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Commentaires", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Un ensemble de données de base est nécessaire pour mesurer la représentation des utilisateurs de la plateforme.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Prochainement", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Nous travaillons actuellement sur le tableau de bord {fieldName}, il sera bientôt disponible", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# élément est} other {# éléments sont}} cachés dans ce graphique. Passez à {tableViewLink} pour afficher toutes les données.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} pour l'enregistrement de l'utilisateur", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} sur {total} utilisateurs inclus ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Afficher {numberOfHiddenItems} plus", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Optionnel", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Veuillez fournir un ensemble de données de base.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Score de représentativité :", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Ce score reflète la précision avec laquelle les données des utilisateurs de la plateforme reflètent la population totale. En savoir plus sur {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Requis", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "<PERSON><PERSON><PERSON><PERSON> donn<PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "vue du tableau", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Population totale", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Utilisateurs", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Ajouter un groupe d'âge", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} et plus", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Groupe d’âge", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Le(s) groupe(s) d'âge de {upperBound} et plus ne sont pas inclus.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Groupe d'âge {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Groupes d'âge", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "et plus", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Appliquer l'exemple de regroupement", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Tout effacer", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "De ", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Définissez les groupes d'âge pour les aligner sur votre ensemble de données de base.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Enregistrer", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "À", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Modifier les groupes d'âge", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Cet article ne sera pas calculé.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "Voir moins", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Afficher {numberOfHiddenItems} plus", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "<PERSON><PERSON> (facultatif)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Groupes d'âge (<PERSON>ée de naissance)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Prochainement", "app.containers.AdminPage.DashboardPage.components.Field.complete": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.default": "<PERSON><PERSON> <PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "<PERSON><PERSON><PERSON><PERSON> remplir toutes les options activées, ou désactiver les options que vous ne souhaitez pas voir apparaître dans le graphique. Au moins une option doit être remplie.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Incomplet", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Nombre total de résidents", "app.containers.AdminPage.DashboardPage.components.Field.options": "Options", "app.containers.AdminPage.DashboardPage.components.Field.save": "Enregistrer", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Enregistré", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Veuillez d'abord {setAgeGroupsLink} pour commencer à saisir les données de base.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "définir des groupes d'âge", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Temps de réponse moyen : {days} jours", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Nombre moyen de jours pour répondre", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "feedback donné", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Statut de l'entrée", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Entrées par statut", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Nombre d'entrées", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Mise à jour officielle", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Pourcentage d'entrées", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Temps de réponse", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Statut", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Statut modifié", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Total", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Modifier les données de base", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "comment nous calculons les scores de représentativité", "app.containers.AdminPage.DashboardPage.continuousType": "Sans une ligne de temps", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Total cumulé", "app.containers.AdminPage.DashboardPage.customDateRange": "Personnalisés", "app.containers.AdminPage.DashboardPage.day": "jour", "app.containers.AdminPage.DashboardPage.false": "faux", "app.containers.AdminPage.DashboardPage.female": "Fé<PERSON>n", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Les 5 principales contributions par réactions", "app.containers.AdminPage.DashboardPage.fromTo": "de {from} à {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Tableau de bord des activités de la plateforme", "app.containers.AdminPage.DashboardPage.helmetTitle": "Tableau de bord administrateur", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Choisir une ressource à afficher par projet", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Choisissez une ressource à afficher par sujet", "app.containers.AdminPage.DashboardPage.inputs1": "Contributions", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Contributions par statut", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Sélectionner le groupe d'utilisateurs", "app.containers.AdminPage.DashboardPage.male": "masculin", "app.containers.AdminPage.DashboardPage.month": "mois", "app.containers.AdminPage.DashboardPage.noData": "Il n'y a pas de données à afficher", "app.containers.AdminPage.DashboardPage.noPhase": "Aucune phase configurée pour ce projet", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Le nombre de participants qui ont posté des contributions, réagi ou commenté.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "Nombre de « je n'aime pas »", "app.containers.AdminPage.DashboardPage.numberOfLikes": "Nombre de « j'aime »", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Total des réactions", "app.containers.AdminPage.DashboardPage.overview.management": "Gestion", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projets et participation", "app.containers.AdminPage.DashboardPage.overview.showLess": "<PERSON><PERSON> moins", "app.containers.AdminPage.DashboardPage.overview.showMore": "Afficher plus d'idées", "app.containers.AdminPage.DashboardPage.participants": "Participants", "app.containers.AdminPage.DashboardPage.participationPerProject": "Participation par projet", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Participation par étiquette", "app.containers.AdminPage.DashboardPage.perPeriod": "Par {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "30 derniers jours", "app.containers.AdminPage.DashboardPage.previous90Days": "90 derniers jours", "app.containers.AdminPage.DashboardPage.previousWeek": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.previousYear": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.projectType": "Type de projet : {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "Réactions", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Ce jeu de données de base est nécessaire pour calculer la représentativité des utilisateurs de la plateforme par rapport à la population totale.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Veuillez fournir un ensemble de données de base.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Découvrez la représentativité des utilisateurs de votre plateforme par rapport à la population totale - sur la base des données collectées lors de l'enregistrement des utilisateurs. En savoir plus sur {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Découvrez la représentativité des utilisateurs de votre plateforme par rapport à la population totale - sur la base des données collectées lors de l'enregistrement des utilisateurs.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Représentation de la communauté", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Retour au tableau de bord", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Aucun des champs d'enregistrement activés n'est pris en charge pour le moment.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "<PERSON><PERSON>, vous pouvez afficher/masquer les éléments du tableau de bord et saisir les données de base. Seuls les champs activés pour {userRegistrationLink} apparaîtront ici.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Modifier les données de base", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "enregistrement de l’utilisateur", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Soumettre les données de base", "app.containers.AdminPage.DashboardPage.resolutionday": "par jour", "app.containers.AdminPage.DashboardPage.resolutionmonth": "par mois", "app.containers.AdminPage.DashboardPage.resolutionweek": "par semaine", "app.containers.AdminPage.DashboardPage.selectProject": "Sélectionnez le projet", "app.containers.AdminPage.DashboardPage.selectedProject": "filtre actuel du projet", "app.containers.AdminPage.DashboardPage.selectedTopic": "filtre d'étiquettes actuel", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Découvrez ce qui se passe sur votre plateforme.", "app.containers.AdminPage.DashboardPage.tabOverview": "Vue d'ensemble", "app.containers.AdminPage.DashboardPage.tabReports": "Projets", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Représentation", "app.containers.AdminPage.DashboardPage.tabUsers": "Utilisateurs", "app.containers.AdminPage.DashboardPage.timelineType": "Avec une ligne de temps", "app.containers.AdminPage.DashboardPage.titleDashboard": "Tableau de bord", "app.containers.AdminPage.DashboardPage.total": "Total", "app.containers.AdminPage.DashboardPage.totalForPeriod": "{period}", "app.containers.AdminPage.DashboardPage.true": "vrai", "app.containers.AdminPage.DashboardPage.unspecified": "indéterminé", "app.containers.AdminPage.DashboardPage.users": "Utilisateurs", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Utilisateurs par âge", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Utilisateurs par localité", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Utilisateurs par genre", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Inscriptions", "app.containers.AdminPage.DashboardPage.week": "semaine", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "L'image doit être assez simple pour rester de bonne qualité en très petite taille. Elle doit être un carré, au format PNG. Vous pouvez y utiliser de la transparence. Si vous n'en utilisez pas, privilégiez un fond blanc. Il vous faut paramétrer cette image une seule fois, elle devrait être modifiée aussi peu que possible.", "app.containers.AdminPage.FaviconPage.save": "Enregistrer", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Une erreur est survenue. Veuillez réessayer.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Envoyé !", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "L'image a bien été enregistrée.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Ajouter", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Administrateurs de dossiers", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Les administrateurs de dossiers peuvent modifier la description du dossier, créer de nouveaux projets dans le dossier et avoir des droits d'administration sur tous les projets du dossier. Ils ne peuvent pas supprimer de projets et n'ont pas accès aux projets qui ne se trouvent pas dans leur dossier. Vous pouvez trouver plus d'informations sur les droits d'administration de projet via {projectManagementInfoCenterLink}.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Aucun résultat trouvé", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "visiter notre centre d'aide", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Recherchez des utilisateurs", "app.containers.AdminPage.FoldersEdit.addToFolder": "Ajouter au dossier", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Archivé", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Supprimer ce dossier", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Description", "app.containers.AdminPage.FoldersEdit.draftStatus": "Brouillon", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Ajouter des fichiers à ce dossier", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Les fichiers ne doivent pas dépasser 50Mb. Les fichiers ajoutés seront affichés sur la page du dossier.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Descriptions", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "Il n'y a pas de projets dans ce dossier. Retournez à l'onglet principal Projets pour créer et ajouter des projets.", "app.containers.AdminPage.FoldersEdit.folderName": "Nom du dossier", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Image de couverture", "app.containers.AdminPage.FoldersEdit.multilocError": "Tous les champs de texte doivent être remplis pour chaque langue.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Il n'y a pas de projets que vous pouvez ajouter à ce dossier.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Image du dossier", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Autorisations", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Projets du dossier", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Paramètres", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projets ajoutés à ce dossier", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Projets que vous pouvez ajouter à ce dossier", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Choi<PERSON><PERSON>z si ce dossier est \"brouillon\", \"publié\" ou \"archivé\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "<PERSON><PERSON><PERSON> du dossier", "app.containers.AdminPage.FoldersEdit.save": "Enregistrer", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Une erreur est survenue. Veuillez réessayer.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Succès !", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Vos modifications ont été enregistrées.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Description succincte", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "Montrée sur la page d'accueil", "app.containers.AdminPage.FoldersEdit.statusLabel": "Statut du dossier", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Expliquer pourquoi ces projets sont communs, définir une identité visuelle et partager des informations.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Expliquer pourquoi ces projets sont communs, définir une identité visuelle et partager des informations.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Tous les champs de texte doivent être remplis.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Titre", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "<PERSON><PERSON>er un nouveau dossier", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Paramètres", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Voir le dossier", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Personnalisez l'image et le texte de la bannière principale", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Bannière principale", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Sauvegarder la bannière principale", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Le Pôle d'inspiration est un espace où vous pouvez trouver des idées pour vos projets en explorant des projets d'autres plateformes.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Modifiez les conditions générales et la politique de confidentialité de votre plateforme. D'autres pages, notamment les pages À propos et FAQ, peuvent être modifiées dans l'onglet {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Politiques de la plateforme", "app.containers.AdminPage.PagesEdition.privacy-policy": "Politique de confidentialité", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Conditions générales", "app.containers.AdminPage.Project.confirmation.description": "Cette action est irréversible.", "app.containers.AdminPage.Project.confirmation.no": "Annuler", "app.containers.AdminPage.Project.confirmation.title": "Êtes-vous certain de vouloir supprimer toutes les données de participation ?", "app.containers.AdminPage.Project.confirmation.yes": "Supprimer toutes les données de participation", "app.containers.AdminPage.Project.data.descriptionText1": "Supprimez les idées, commentaires, votes, réactions, réponses aux enquêtes et sondages, ainsi que les inscriptions en tant que volontaire ou aux événements. Cependant, cette action ne supprimera pas les options associées aux phases de vote (uniquement les votes, réactions et commentaires qui leurs sont associés).", "app.containers.AdminPage.Project.data.title": "Effacer toutes les données de participation de ce projet", "app.containers.AdminPage.Project.resetParticipationData": "Supprimer toutes les données de participation", "app.containers.AdminPage.Project.settings.accessRights": "Droits d'accès", "app.containers.AdminPage.Project.settings.back": "Retour", "app.containers.AdminPage.Project.settings.data": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.description": "Description", "app.containers.AdminPage.Project.settings.events": "Événements", "app.containers.AdminPage.Project.settings.general": "Général", "app.containers.AdminPage.Project.settings.projectTags": "Étiquettes du projet", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Liste des {tenant<PERSON>ame, select, frw {communes} other {projets}} sur la plateforme", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Gestion des {tenant<PERSON>ame, select, frw {communes} other {projets}}", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "<PERSON><PERSON>ez de nouveaux projets ou gérez des projets existants.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "{tenant<PERSON><PERSON>, select, frw {Communes} other {Projets}}", "app.containers.AdminPage.ProjectDashboard.published": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "<PERSON><PERSON><PERSON> le panneau des paramètres", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Centre", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "<PERSON><PERSON>e largeur", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "G<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "Alignement du bouton", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Texte du bouton", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Entrez le texte du bouton", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "Primaire", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Type de bouton", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Secondaire", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "URL du bouton", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Entrez une URL pour le bouton", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Mise en page des colonnes", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Description complète", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Description sur la page d'accueil", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Visible sur la vignette projet sur la page d'accueil.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Visible sur la page du projet. In<PERSON>quez clairement sur quoi porte le projet, ce que vous attendez des utilisateurs and ce qu'ils peuvent attendre de vous.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Une erreur est survenue. Veuillez réessayer.", "app.containers.AdminPage.ProjectDescription.preview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.save": "Enregistrer", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Vos modifications ont été sauvegardées.", "app.containers.AdminPage.ProjectDescription.saved": "Enregistré !", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Ecrivez un message clair au sujet de votre projet. Enrichissez votre ce texte avec des images, des vidéos et des pièces jointes.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Description du projet", "app.containers.AdminPage.ProjectDescription.whiteSpace": "Espace blanc", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Afficher un trait de séparation", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "Grand", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Annuler", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "La latitude par défaut de la centralisation de la carte accepte une valeur comprise entre -90 et 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "La longitude par défaut de la centralisation de la carte accepte une valeur comprise entre -180 et 180.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Modifier la couche", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Modifier la couche", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Une erreur est survenue. Veuillez réessayer.", "app.containers.AdminPage.ProjectEdit.MapTab.here": "ici", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Importer un fichier GeoJSON", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Latitude par défaut", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "<PERSON><PERSON><PERSON> de la couche", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Cette couleur est appliquée à tous les éléments de la couche cartographique. La taille des marqueurs, la largeur des lignes et l'opacité du remplissage sont fixées par défaut.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Icône des marqueurs", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Optionnel: sélectionnez une icône pour les marqueurs. Cliquez sur {url} pour voir la liste des icônes que vous pouvez sélectionner.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Nom de la couche", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Le nom de cette couche est indiqué dans la légende de la carte", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Information de la couche", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Ce texte s'affiche comme bulle d'information lors du survol des éléments de la couche sur la carte", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Couches", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "Nous supportons actuellement les fichiers GeoJSON. Lisez cet {supportArticle} pour obtenir des conseils sur la manière de convertir et de paramétrer des couches cartographiques.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Longitude par défaut", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Centralisation et niveau de zoom par défaut de la carte", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Le point central et le niveau de zoom par défaut de la carte. <PERSON><PERSON><PERSON>z manuellement les valeurs ci-dessous, ou cliquez sur le bouton {button} dans le coin inférieur gauche de la carte pour enregistrer de nouvelles valeurs par défaut pour le point central et le niveau de zoom.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Personnalisez la visualisation de la carte, notamment en téléchargeant et en paramétrant des couches cartographiques ainsi qu'en définissant la centralisation et le niveau de zoom.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Configuration de la carte", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "La configuration de la carte est partagée par toutes les phases du projet. Il n'est donc actuellement pas possible de créer des configurations spécifiques par phase.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Supp<PERSON>er la couche", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Enregistrer", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Enregistrer le zoom", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "article de support", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/fr/articles/7022129-collecte-de-commentaires-et-de-reactions-liste-et-vue-cartographique", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Couche sans nom", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Niveau de zoom par défaut", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Définissez le niveau de zoom de la carte par défaut. Choisissez une valeur entre 0 et 20, où 0 est entièrement dézoomé (le monde entier est visible) et 20 est entièrement zoomé (les blocs d'habitations et les bâtiments sont visibles)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Anonymiser toutes les données des utilisateurs", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Toutes les réponses fournies par les utilisateurs dans le cadre de l'enquête seront anonymisées avant d'être enregistrées", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Les utilisateurs devront toujours se conformer aux critères de participation tels que définis dans l'onglet « Droits d'accès ». Par ailleurs lors de l'exportation des données de l'enquête, les données du profil utilisateur ne seront pas incluses.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "Si vous activez cette option, les champs d'inscription des utilisateurs seront affichés comme dernière page de l'enquête au lieu de faire partie du processus d'inscription.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Champs démographiques", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Inclure les champs démographiques dans l'enquête", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "Consultez cet article pour en savoir plus sur le fonctionnement du partage automatique.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/fr/articles/8124630-methodes-de-vote-et-de-hierarchisation-pour-une-meilleure-prise-de-decision#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Partage automatique des résultats", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "<PERSON>r d<PERSON><PERSON><PERSON>, les résultats du vote sont partagés sur la plateforme et envoyés par e-mail aux participants à la fin de la phase.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Partage des résultats", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Ajouter une option de réponse", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Ajouter une question de sondage", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "Annuler", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Annuler", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Annuler", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Modifier l'option de réponse", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Sauvegarder les options de réponse", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Modifier les options de réponse", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "Modifier la question", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Exporter les résultats du sondage", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Le nombre maximum de choix est supérieur au nombre d'options", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Réponses multiples", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "Pas d'options", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "Le sondage ne pourra pas être répondu tel quel, toutes les questions doivent avoir des options.", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Une seule réponse", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Les répondants au sondage n'ont qu'un seul choix", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "G<PERSON>rer les options de réponse pour : {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "sondage_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Vous pouvez y créer des questions de sondage, définir les choix de réponse parmi lesquels les participants peuvent choisir pour chaque question, décider si vous souhaitez que les participants ne puissent choisir qu'un seul choix de réponse (choix unique) ou plusieurs choix de réponse (choix multiple), et exporter les résultats du sondage. Vous pouvez créer plusieurs questions de sondage au sein d'un même sondage.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Enregistrer", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "Enregistrer", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Une seule réponse", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Paramètres et résultats des sondages", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "<PERSON><PERSON><PERSON><PERSON> maximum", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Importer", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "Rédigez des commentaires, filtrez par étiquette ou basculez les contributions dans la phase suivante", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "Explorez et gérez les propositions soumises en donnant des retours, en ajoutant des étiquettes ou en modifiant leur statut.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Gestion des contributions", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "Le partage des résultats est désactivé.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Les résultats du vote ne seront pas partagés à la fin de la phase. Ce réglage peut être modifié dans les paramètres de la phase.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Ces résultats seront partagés automatiquement une fois la phase terminée. Modifiez la date de fin de cette phase pour changer la date à laquelle les résultats seront communiqués.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Exporter les résultats de l'enquête (.xslx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Résultats", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "<PERSON><PERSON>, vous pouvez télécharger un fichier Excel contenant les résultats des enquêtes présentes dans le projet. Pour l'instant, seules les enquêtes Typeform sont disponibles.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "Formulaire de l'enquête", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Résultats de l'enquête", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Consulter les réponses à l’enquête", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Ajouter une cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "E<PERSON>-vous sûr ?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Expliquez ici ce que vous attendez des volontaires.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "<PERSON>uvegard<PERSON> impossible, le formulaire contient des erreurs.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Image", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Titre", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Modifier", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Une cause est une action ou une activité pour laquelle les citoyens peuvent se porter volontaires.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Modifier la cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Ajouter une description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Ajouter un titre", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Export des volontaires", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Une cause est une action ou une activité pour laquelle les citoyens peuvent se porter volontaires.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "Nouvelle cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Enregistrer", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Vous pouvez modifier ici les causes pour lesquelles les utilisateurs peuvent se porter volontaires et exporter la liste des volontaires.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Se porter volontaire", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {pas de volontaires} one {# volontaire} other {# volontaires}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "l'allocation d'un budget", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Définissez un coût pour chacune des options et invitez les participants à sélectionner leurs options préférées en respectant un budget total.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Allocation d'un budget", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Autoriser les utilisateurs à commenter peut influencer le processus de vote.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "Crédit", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Vue par défaut des options", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Actions utilisateurs", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Sélectionnez les actions supplémentaires que les utilisateurs peuvent effectuer.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Montant fixe", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "Si non spécifié, le terme utilisé par défaut est \"vote\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "Pour en apprendre davantage sur quand utiliser <b> {voteTypeDescription} </b> , consultez notre article sur l''{optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Nombre maximum de votes par option", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Nombre maximum de votes", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Vous pouvez limiter le nombre total de votes qu'un utilisateur peut exprimer (avec un maximum d'un vote par option).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "plusieurs votes par option", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Les utilisateurs reçoivent un certain nombre de jetons à répartir entre les différentes options", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Plusieurs votes par option", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Nombre de votes par utilisateur", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Analyse d'options", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Options de vote", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "Point", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "un seul vote par option", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Les utilisateurs peuvent choisir une ou plusieurs options sans pondération", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Un vote par option", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Illimité", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Quel terme utiliser pour désigner un vote ?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "\"jetons\", \"points\", ou \"crédits carbone\" par exemple", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "\"jeton\", \"point\", ou \"crédit carbone\" par exemple", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "Vote", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Chaque méthode de vote a différentes pré-configurations", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "La méthode de vote détermine les règles selon lesquelles les utilisateurs votent", "app.containers.AdminPage.ProjectEdit.addNewInput": "Ajouter une entrée", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "Vous pouvez ajouter votre projet à un dossier maintenant, ou le faire plus tard dans les paramètres du projet", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Etiquettes autorisées", "app.containers.AdminPage.ProjectEdit.altText": "Texte alternatif", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Sondage anonyme", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "Lorsqu'il est activé, il est impossible de voir qui a voté sur quoi. Les utilisateurs ont toujours besoin d'un compte et ne peuvent voter qu'une seule fois.", "app.containers.AdminPage.ProjectEdit.approved": "Approu<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.archived": "Archivé", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Les projets archivés sont toujours visibles, mais ne permettent plus de participer.", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Archivé", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Cette zone ne peut pas être supprimée car elle est utilisée pour afficher des projets sur la ou les pages personnalisées suivantes. Vous devrez dissocier la zone de la page ou supprimer la page avant de pouvoir supprimer la zone.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Pas de lieu en particulier", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Le projet apparaîtra sur chaque filtre de zone.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Filtre de zone", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Les projets peuvent être filtrés sur la page d'accueil à l'aide de zones. Les zones peuvent être définies {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "ici", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Pas de zone spécifique", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Le projet n'apparaît pas lors du filtrage par zone.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Sélectionnez un lieu", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Le projet sera affiché sur le(s) filtre(s) de la zone sélectionnée.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Dans une liste", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "Les plus discutés", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Questions", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Le sondage a déjà reçu des réponses. Toute modification apportée au sondage à ce stade est susceptible de cause des pertes de données et des fichiers exportés incomplets.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Enquête enregistrée avec succès", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/fr/articles/6673873-creer-une-enquete-interne-sur-la-plate-forme", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "Voir l'enquête", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Choisissez une méthode de vote et demandez aux utilisateurs d'établir un ordre de priorité entre différentes options.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Organiser un vote ou un exercice de priorisation", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "<PERSON><PERSON>er un projet à partir d'un modèle", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "<PERSON><PERSON><PERSON> une enquête externe", "app.containers.AdminPage.ProjectEdit.createInput": "Ajouter une nouvelle contribution", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "<PERSON><PERSON>ez une enquête sur la plateforme", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Met<PERSON>z en place une enquête sans quitter notre plateforme.", "app.containers.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON><PERSON> un sondage", "app.containers.AdminPage.ProjectEdit.createPollDescription": "C<PERSON>ez un questionnaire à choix multiples.", "app.containers.AdminPage.ProjectEdit.createProject": "Nouveau projet", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Intégrez une enquête Typeform, Google Form, SurveyXact, Qualtrics ou Enalyzer.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Vous pouvez définir l'ordre par défaut d'affichage des publications sur la page principale du projet.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.departments": "Départements", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Description", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "Cette option active ou désactive le bouton \"Je n'aime pas\". (Le bouton \"J'aime\" restera activé.) Nous vous recommandons de laisser cette option désactivée, sauf si vous effectuez une analyse des options.", "app.containers.AdminPage.ProjectEdit.disabled": "Désactivée", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Nombre de \"je n'aime pas\" par participant", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "<PERSON><PERSON><PERSON> les \"je n'aime pas\"", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Recueillir des commentaires sur un document", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Incorporez un PDF interactif et recueillez des commentaires et des retours avec Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Désactivée", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Activée", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Les projets en brouillon sont accessibles uniquement aux administrateurs et aux gestionnaires de projet concernés.", "app.containers.AdminPage.ProjectEdit.draft": "Brouillon", "app.containers.AdminPage.ProjectEdit.draftStatus": "Brouillon", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabled": "Activée", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Sélectionnez les actions que les utilisateurs peuvent effectuer.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Événements", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Pièces jointes (max. 50Mo)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Les fichiers ajoutés seront visibles sur la page \"information\" du projet.", "app.containers.AdminPage.ProjectEdit.filesTab": "Fichiers", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Inviter à participer", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Demandez aux participants de se porter volontaires pour des activités et des causes.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "En tant que gestionnaire de dossiers, vous pouvez choisir un dossier lors de la création du projet, mais seul un administrateur peut le modifier par la suite", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Texte alternatif de l'image de la carte du dossier", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Sélectionnez un dossier pour y ajouter ce projet.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Les soumissions de ce formulaire ont commencé à arriver. Les modifications apportées au formulaire peuvent entraîner une perte de données et des données incomplètes dans les fichiers exportés.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Le formulaire a été sauvegardé avec succès", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Fin de l'enquête", "app.containers.AdminPage.ProjectEdit.fromATemplate": "A partir d'un modèle", "app.containers.AdminPage.ProjectEdit.generalTab": "Général", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Texte alternatif de l'image d'en-tête", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Image de couverture", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NOUVEAU", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Fournissez des informations aux utilisateurs ou utilisez l'éditeur de rapports pour partager les résultats des phases précédentes.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Communiquez des informations ou des résultats", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Recueillir des contributions et des commentaires", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "<PERSON><PERSON>ez ou collectez des contributions, des votes et/ou des commentaires. Choisissez entre différents types de contributions : collecte d'idées, analyse d'options, questions et réponses, identification des problèmes, etc.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Qui est responsable du traitement des contributions ?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Toutes les nouvelles contributions dans ce projet seront attribuées à cette personne. Le cessionnaire peut être modifié dans {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Commenter les contributions", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Formulaire de contribution", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "gestion des contributions", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Gestion des contributions", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Soumettre des contributions", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Réagir aux contributions", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Vue par défaut", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Choisissez la vue par défaut des contributions des participants : cartes dans une vue en grille ou épingles sur une carte. Les participants peuvent basculer manuellement entre les deux vues.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Pôle d'inspiration", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "URL d'intégration Konveio", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Nombre de \"j'aime\" par participant", "app.containers.AdminPage.ProjectEdit.limited": "Limité", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Voir plus de modèles", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Sur une carte", "app.containers.AdminPage.ProjectEdit.mapTab": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxDislikes": "Nombre maximum de \"je n'aime pas\"", "app.containers.AdminPage.ProjectEdit.maxLikes": "Nombre maximum de \"j'aime\"", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Le nombre maximum de votes par option doit être inférieur ou égal au nombre total de votes.", "app.containers.AdminPage.ProjectEdit.maximum": "Maximum", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Les participants ne peuvent pas dépasser ce budget lors de la soumission de leur panier.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimum", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Demandez aux participants qu'ils respectent un budget minimum pour soumettre leur panier (entrez '0' si vous ne souhaitez pas fixer de minimum).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "visiter notre centre d'aide", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Qui sont les gestionnaires de projet ?", "app.containers.AdminPage.ProjectEdit.moreDetails": "Plus de détails", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "En manque d'inspiration ? Explorez des projets similaires d'autres villes dans l'{inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "Ajouter une contribution", "app.containers.AdminPage.ProjectEdit.newIdea": "Ajouter une contribution", "app.containers.AdminPage.ProjectEdit.newInitiative": "Ajouter une initiative", "app.containers.AdminPage.ProjectEdit.newIssue": "Ajoutez un problème", "app.containers.AdminPage.ProjectEdit.newOption": "Ajouter une option", "app.containers.AdminPage.ProjectEdit.newPetition": "Ajouter une pétition", "app.containers.AdminPage.ProjectEdit.newProject": "Nouveau projet", "app.containers.AdminPage.ProjectEdit.newProposal": "Ajouter une proposition", "app.containers.AdminPage.ProjectEdit.newQuestion": "Ajouter une question", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Plus récentes", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Montant non valide", "app.containers.AdminPage.ProjectEdit.noFolder": "Pas de dossier", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "— <PERSON><PERSON> de dossier —", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "<PERSON><PERSON><PERSON> mod<PERSON>le trouvé", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "<PERSON><PERSON> ne peut pas être vide", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Veuillez fournir le nombre maximum de votes autorisé par utilisateur", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Plus anciennes", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Seulement visible aux administrateurs", "app.containers.AdminPage.ProjectEdit.optionNo": "Non", "app.containers.AdminPage.ProjectEdit.optionYes": "<PERSON><PERSON> (sélectionner le dossier)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Niveaux de participation", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Qu'est-ce que vous souhaitez faire ?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Choisissez comment les citoyens peuvent participer .", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "Vous pouvez définir qui est autorisé à effectuer chaque action et poser des questions supplémentaires aux participants pour recueillir plus d'informations.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Conditions de participation & questions", "app.containers.AdminPage.ProjectEdit.pendingReview": "En attente d'approbation", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Droits d'accès", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "Droits d'accès", "app.containers.AdminPage.ProjectEdit.phaseEmails": "Notifications", "app.containers.AdminPage.ProjectEdit.pollTab": "Sondage", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "Plus de réactions", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Image de la vignette du projet", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Cette image fait partie de la fiche du projet ; la fiche qui résume le projet et qui est affichée sur la page d'accueil par exemple.\n\n    Pour plus d'informations sur les résolutions d'image recommandées, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Dossier", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Cette image est affichée en haut de la page du projet.\n\n    Pour plus d'informations sur les résolutions d'image recommandées, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Texte alternatif de l'image de la carte du projet", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Fournis<PERSON>z une courte description de l'image, destinée à être lue à voix haute par les lecteurs d'écran pour les utilisateurs malvoyants.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Gestionnaires de projet", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Les gestionnaires de projet peuvent éditer des projets, gérer les contributions et envoyer des e-mails aux participants. Vous pouvez {moderationInfoCenterLink} pour plus d'informations sur les droits attribués aux gestionnaires de projet.", "app.containers.AdminPage.ProjectEdit.projectName": "Nom du projet", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Type de projet", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Choisisssez si vous souhaitez ou ne souhaitez pas une ligne du temps pour votre projet. Les projets avec une ligne du temps un début et une fin. Les projets sans ligne du temps sont dits \"continus\".", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Le type de projet ne peut pas être modifié ultérieurement.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "Vous pouvez masquer le projet à certains utilisateurs.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Visibilité du projet", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Vous cherchez le statut du projet ? Vous pouvez désormais le modifier directement depuis l'en-tête de la page du projet.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Les projets publiés sont visibles par tous les utilisateurs ou certains groupes d'utilisateurs en fonction de leur configuration.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.purposes": "Buts et objectifs", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Aléatoire", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Supprimer les données de participation", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Une erreur est survenue lors de l'enregistrement de vos données. Veuillez réessayer.", "app.containers.AdminPage.ProjectEdit.saveProject": "Enregistrer", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Enregistré !", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Votre projet a été enregistré !", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Rechercher dans les modèles", "app.containers.AdminPage.ProjectEdit.selectGroups": "Choisir les groupes", "app.containers.AdminPage.ProjectEdit.setup": "Configuration", "app.containers.AdminPage.ProjectEdit.shareInformation": "Partager l'information", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "<PERSON><PERSON><PERSON><PERSON><PERSON> instantan<PERSON>", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "<PERSON><PERSON><PERSON> et personnalisez votre projet.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "visitez notre centre d'assistance", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Questions", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Annuler", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# choix} one {# choix} other {# choix}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON><PERSON>, je veux quitter", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "Modifier", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Les réponses à ce sondage commencent à arriver. Des modifications apportées au sondage pourraient entraîner des pertes de données et des données incomplètes dans les fichiers exportés.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "Téléchargement de fichier", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "Retour", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Importer", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Importer", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "Les résumés générés par l'IA pour les questions à réponse courte, à réponse longue et à échelle de sentiment peuvent être consultés dans l'onglet IA dans la barre latérale gauche.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Échelle linéaire", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "Réponse longue", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Choix multiple - choisissez-en plusieurs", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Choix multiple (Images)", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "Nouvelle soumission", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Pas encore de réponses à l'enquête", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Ouvert aux réponses", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Ouvert aux réponses", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Facultatif", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "Si aucune règle logique n'est ajoutée, l'enquête suivra son cours normal. Si une page et ses questions comportent des règles logiques, celles associées aux questions prévaudront. Assurez-vous que cela correspond au comportement souhaité pour votre enquête. Pour plus d'informations, consultez {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Voulez-vous vraiment quitter?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Tes modifications actuelles ne seront pas sauvegardées.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Classement", "app.containers.AdminPage.ProjectEdit.survey.rating": "Évaluation", "app.containers.AdminPage.ProjectEdit.survey.required2": "Requis", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {réponse} one {réponse} other {réponses}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# réponse} one {# réponse} other {# réponses}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Choix multiple - choisissez-en un", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Échelle linéaire de sentiment", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Téléchargement de fichier de formes Esri", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "<PERSON><PERSON><PERSON><PERSON><PERSON> sauve<PERSON> avec succès", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Réponses à l'enquête", "app.containers.AdminPage.ProjectEdit.survey.text2": "Réponse courte", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Total {count} réponses", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "Voir l'enquête", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "Voir", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "URL incorporée", "app.containers.AdminPage.ProjectEdit.surveyService": "Service", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Choisissez quel outil d'enquête vous souhaitez utiliser. Des informations supplémentaires peuvent être trouvées {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/fr/articles/7025887-creation-d-un-projet-d-enquete-externe", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "ici", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Cette balise ne peut pas être supprimée car elle est utilisée pour afficher des projets sur la ou les pages personnalisées suivantes\nVous devrez dissocier la balise de la page, ou supprimer la page avant de pouvoir supprimer la balise.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Paramètres généraux du projet", "app.containers.AdminPage.ProjectEdit.titleLabel": "Nom du projet", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Choisissez un titre court, attractif et explicite. Il s’affichera dans la liste déroulante avec les autres projets en haut de la page d’accueil.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Étiquettes", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Sélectionnez {topicsCopy} pour ce projet. Les utilisateurs peuvent utiliser ces critères pour filtrer les projets.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Budget total", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Populaires", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Non attribué", "app.containers.AdminPage.ProjectEdit.unlimited": "Illimité", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Utiliser le modèle", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "Visualiser le projet", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Se porter volontaire", "app.containers.AdminPage.ProjectEdit.voteTermError": "La terminologie doit être définie pour toutes les langues", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# groupes} one {# groupe} other {# groupes}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Ajouter un événement", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Informations complémentaires", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Adresse 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Adresse de l'événement", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Adresse 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "Par exemple : étage, bâtiment, salle de réunion, etc.", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Des informations complémentaires concernant l'adresse qui pourraient faciliter l'identification de l'emplacement, comme un nom de bâtiment, un numéro d'étage, etc.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/fr/articles/5481527-ajouter-des-evenements-a-votre-plateforme", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "Consultez notre article de support", "app.containers.AdminPage.ProjectEvents.customButtonLink": "Lien externe", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Ajoutez un lien vers une URL externe (par exemple, un service d'événement ou un site de billetterie). <PERSON><PERSON> remplacera le comportement par défaut du bouton de participation.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Texte personnalisé du bouton", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Si vous avez configuré le bouton pour qu'il redirige vers une page externe, vous pouvez modifier le texte du bouton. Pa<PERSON> d<PERSON><PERSON><PERSON>, le bouton est labellisé « S'inscrire ».", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "D<PERSON>but", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "Fin", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Êtes-vous sûr de que vouloir supprimer définitivement cet événement ? ", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Description de l'événement", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Modifier", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Modifier l’événement", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "Pour envoyer des e-mails aux personnes inscrites, les administrateurs doivent créer un groupe d'utilisateurs dans l'onglet {userTabLink}. {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "Dates de l'événement", "app.containers.AdminPage.ProjectEvents.eventImage": "Image de l'événement", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Texte alternatif de l'image de l'événement", "app.containers.AdminPage.ProjectEvents.eventLocation": "Lieu de l'événement", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Exporter les inscriptions", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Pièces jointes (max. 50Mo)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Les pièces jointes seront directement visibles sur les évènements.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Localisation", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Nombre maximum d'inscriptions", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Créer un nouvel événement", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Lien vers l'événement en ligne", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "Si votre événement est en ligne, ajoutez un lien vers celui-ci ici.", "app.containers.AdminPage.ProjectEvents.preview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Affiner la localisation sur la carte", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Affiner la localisation sur la carte", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "<PERSON><PERSON> pouvez affiner la position du marqueur en cliquant sur la carte ci-dessous.", "app.containers.AdminPage.ProjectEvents.register": "S'inscrire", "app.containers.AdminPage.ProjectEvents.registerButton": "Bouton d'inscription", "app.containers.AdminPage.ProjectEvents.registrant": "inscrit", "app.containers.AdminPage.ProjectEvents.registrants": "inscrits", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Limite d'inscription", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Enregistrer", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "Vos modifications n'ont pas pu être enregistrées, veuil<PERSON>z réessayer.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Enregistré !", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Vos modifications ont été enregistrées.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "Rechercher un lieu", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Ajoutez ici des événements « hors ligne » ou des réunions qui sont liées à votre projet.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Titre et dates", "app.containers.AdminPage.ProjectEvents.titleEvents": "Evénements du projet", "app.containers.AdminPage.ProjectEvents.titleLabel": "Nom de l'événement", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Rediriger le bouton vers une URL externe", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "Si vous préf<PERSON>rez gérer les inscriptions via un autre outil, vous pouvez modifier le bouton pour qu'il redirige vers une page externe.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Limiter le nombre d'inscriptions", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Limitez le nombre d'inscriptions à l'événement. Une fois cette limite atteinte, il ne sera plus possible de s'inscrire.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/users", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Utilisateurs", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "Ajouter des fichiers à votre projet", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Joignez les fichiers de cette liste à votre projet, à ses phases et à ses événements.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Ajouter des fichiers comme contexte pour l'analyse IA", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Ajoutez des fichiers à votre projet Sensemaking pour fournir un contexte et des informations.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Bientôt disponible", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Importez vos enquêtes et vos interviews, et laissez l'IA détecter les tendances dans vos données.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Téléversez tout type de fichier", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Utilisez l'IA pour analyser les fichiers", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Générer des transcriptions, etc.", "app.containers.AdminPage.ProjectFiles.addFiles": "Ajouter des fichiers", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "Analyses IA", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "Utilisez l'IA afin d'identifier rapidement les informations importantes de vos fichiers.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Autoriser l'utilisation de l'IA pour analyser ces fichiers.", "app.containers.AdminPage.ProjectFiles.askButton": "Poser une question", "app.containers.AdminPage.ProjectFiles.categoryLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.chooseFiles": "Choisir des fichiers", "app.containers.AdminPage.ProjectFiles.close": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Con<PERSON>rm<PERSON> et téléverser", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Êtes-vous sûr de vouloir supprimer ce fichier ?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "Impossible de charger le fichier Markdown.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "Impossible de charger l'aperçu CSV.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "L’aperçu CSV affiche au maximum 50 lignes.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "Le fichier CSV est trop volumineux pour être prévisualisé.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "<PERSON><PERSON><PERSON><PERSON> le fichier", "app.containers.AdminPage.ProjectFiles.description": "Description", "app.containers.AdminPage.ProjectFiles.done": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.downloadFile": "Télécharger le fichier", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Télécharger le fichier", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Glissez-<PERSON><PERSON><PERSON><PERSON> vos fichiers ici ou", "app.containers.AdminPage.ProjectFiles.editFile": "Modifier le fichier", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Description", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "Le nom du fichier ne peut pas contenir de point.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "Nom du fichier", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "Le nom du fichier est requis.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Télécharger le fichier", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "Ce fichier dépasse la taille maximale de 50 Mo.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "Tous les fichiers ont été téléversés avec succès", "app.containers.AdminPage.ProjectFiles.generatingPreview": "Génération de l'aperçu en cours...", "app.containers.AdminPage.ProjectFiles.info_sheet": "Information", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "Ex. WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Entretiens audio, enregistrements de conseils municipaux", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "Ex. PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "Rapports, documents d'information", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "Ex. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "Images", "app.containers.AdminPage.ProjectFiles.interview": "Interview", "app.containers.AdminPage.ProjectFiles.maxFilesError": "Vous ne pouvez téléverser que {maxFiles} fichiers à la fois.", "app.containers.AdminPage.ProjectFiles.meeting": "Notes de réunion", "app.containers.AdminPage.ProjectFiles.noFilesFound": "<PERSON><PERSON><PERSON> fichier trouvé.", "app.containers.AdminPage.ProjectFiles.other": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.policy": "Politique", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "L'aperçu n'est pas encore disponible pour ce type de fichier.", "app.containers.AdminPage.ProjectFiles.report": "Rapport", "app.containers.AdminPage.ProjectFiles.retryUpload": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.save": "Enregistrer", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "<PERSON><PERSON>er mis à jour.", "app.containers.AdminPage.ProjectFiles.searchFiles": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.selectFileType": "Type de fichier", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Plan stratégique", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "Vous ne pouvez téléverser que {maxFiles} fichiers à la fois.", "app.containers.AdminPage.ProjectFiles.unknown": "Inconnu", "app.containers.AdminPage.ProjectFiles.upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# fichier téléchargé} other {# fichiers téléchargés}} avec succès, {numberOfErrors, plural, one {# erreur} other {# erreurs}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "Voir le fichier", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Réduire tous les champs", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Description du champ", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Modifier le formulaire de saisie", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Activé", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Incluez ce champ.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Une erreur est survenue. Veuillez réessayer.", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "<PERSON><PERSON><PERSON><PERSON> tous les champs", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Formulaire de contribution", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Précisez quelles informations doivent être fournies, ajoutez de courtes descriptions ou instructions pour guider les réponses des participants et indiquez si chaque champ est facultatif ou obligatoire.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Spécifiez les informations à fournir, ajou<PERSON>z de brèves descriptions ou des instructions pour guider les réponses des participants et spécifiez si chaque champ est facultatif ou obligatoire", "app.containers.AdminPage.ProjectIdeaForm.required": "Obligatoire", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Exigez que ce champ soit rempli.", "app.containers.AdminPage.ProjectIdeaForm.save": "Enregistrer", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Vos modifications ont été enregistrées avec succès.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Enregistré !", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "Voir le formulaire", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "E-mails automatisés", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "<PERSON><PERSON> pou<PERSON> configurer les e-mails envoyés pour chaque phase individuellement", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Dates", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "Répondre à l'enquête", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Êtes-vous sûr de vouloir supprimer cette étape ?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Description de la phase", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "Modifier la phase", "app.containers.AdminPage.ProjectTimeline.endDate": "Date de fin", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Date de fin", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Pièces jointes (max. 50Mo)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Créer une nouvelle phase", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "Cette phase n'a pas de date de fin prédéfinie.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "La publication des résultats de certaines méthodes (comme les résultats des votes) ne sera déclenchée qu'une fois qu'une date de fin aura été sélectionnée.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "Si vous ajoutez une nouvelle phase après celle-ci, une date de fin sera automatiquement ajoutée à cette phase.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "Le fait de ne pas sélectionner de date de fin implique que :", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "Enregistrer les modifications", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Une erreur est survenue lors de la soumission du formulaire, veuil<PERSON><PERSON> réessayer.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Enregistré !", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Vos modifications ont été enregistrées avec succès.", "app.containers.AdminPage.ProjectTimeline.startDate": "Date de début", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Date de début", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "Bouton", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "Titre de l'enquête", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Nom de la phase", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Télécharger des pièces jointes", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartition", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologie (filtre de la page d'accueil)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Comment appeler les balises dans le filtre de la page d'accueil ? Par exemple, balises, catégories, départements, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Les étiquettes peuvent être configurées {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "ici", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Terme pour une seule étiquette (singulier)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "étiquette", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Terme désignant des balises multiples (pluriel)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "balises", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Ajouter un champ", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Ajouter un champ d'inscription", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Ajouter une option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Format de réponse", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Fournir un format de réponse", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Option de réponse", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Fournir une option de réponse pour toutes les langues", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Sauvegarder l'option de réponse", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Option de réponse sauvegardée avec succès", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Options de réponses", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Faites glisser et déposez les champs pour déterminer l'ordre dans lequel ils apparaissent dans le formulaire d'inscription.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Champ par défaut", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Texte facultatif figurant sous le champ dans le formulaire d'inscription.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Les options de réponses pour le lieu de résidence peuvent être paramétrées dans {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Modifier", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Modifier l'option de réponse", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Description", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Nom du champ", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Fournir un nom de champ pour toutes les langues", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Paramètres du champ", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "<PERSON><PERSON>-non (case à cocher)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Date", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Réponse longue", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Choix multiple (plusieurs options possibles)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Valeur numérique", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Choix multiple (une seule option possible)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Réponse courte", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Onglet des zones géographiques", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "<PERSON>mp caché", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Rendre la réponse à ce champ obligatoire ?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Ajouter une option de réponse", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Annuler", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Êtes-vous certain(e) de vouloir supprimer cette option de réponse pour la question d'inscription ? Cela entraînera la suppression permanente de toutes les réponses associées que les utilisateurs ont données en choisissant cette option. Cette action est irréversible.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Êtes-vous certain(e) de vouloir supprimer cette question d'inscription ? Toutes les réponses fournies par les utilisateurs seront définitivement supprimées, et elle ne sera plus posée dans les projets ainsi que dans la fonctionnalité \"Propositions\". Cette action est irréversible.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Obligatoire", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "<PERSON>uve<PERSON><PERSON> le champ", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Champ sauvegardé avec succès", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "Deux colonnes", "app.containers.AdminPage.SettingsPage.addAreaButton": "Ajouter une zone géographique", "app.containers.AdminPage.SettingsPage.addTopicButton": "Ajouter une étiquette", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Animal — « é<PERSON>phant » ou « chat » par exemple", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Utilisateur — « Utilisateur 123456 » par exemple", "app.containers.AdminPage.SettingsPage.approvalDescription": "Sélectionnez les administrateurs qui recevront les notifications pour vérifier et approuver les projets. Pour les projets dans un dossier, les notifications seront également envoyées aux gestionnaires du dossier.", "app.containers.AdminPage.SettingsPage.approvalSave": "Enregistrer", "app.containers.AdminPage.SettingsPage.approvalTitle": "Révisions des projets", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Êtes-vous sûr(e) de vouloir supprimer ce lieu ?", "app.containers.AdminPage.SettingsPage.areaTerm": "Terme pour une zone (singulier)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "lieu", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Modifier", "app.containers.AdminPage.SettingsPage.areasTerm": "Terme pour plusieurs zones (pluriel)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "lieux", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Sélectionnez au moins une langue.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Afficher les avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Mont<PERSON> les photos de profil des participants et leur nombre aux visiteurs non inscrits", "app.containers.AdminPage.SettingsPage.bannerHeader": "Texte d'entête", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Texte de la bannière pour les utilisateurs non-connectés", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Sous-texte de la bannière bannière pour les utilisateurs non-connectés", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Texte du sous-titre", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Texte de la bannière", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Afficher l’aperçu pour", "app.containers.AdminPage.SettingsPage.brandingDescription": "Ajoutez votre logo et définissez les couleurs de la plate-forme.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Identité de la plateforme", "app.containers.AdminPage.SettingsPage.cancel": "Annuler", "app.containers.AdminPage.SettingsPage.chooseLayout": "Mise en page", "app.containers.AdminPage.SettingsPage.color_primary": "Couleur principale", "app.containers.AdminPage.SettingsPage.color_secondary": "<PERSON>uleur secondaire", "app.containers.AdminPage.SettingsPage.color_text": "Couleur du texte", "app.containers.AdminPage.SettingsPage.colorsTitle": "Couleurs", "app.containers.AdminPage.SettingsPage.confirmHeader": "Êtes-vous sûr de vouloir supprimer cette étiquette ?", "app.containers.AdminPage.SettingsPage.contentModeration": "Modération du contenu", "app.containers.AdminPage.SettingsPage.ctaHeader": "Boutons", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "En-tête de page personnalisé | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Personnalisés", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Texte du bouton", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "<PERSON>n du bouton", "app.containers.AdminPage.SettingsPage.defaultTopic": "Étiquette par défaut", "app.containers.AdminPage.SettingsPage.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Cela supprimera le sujet, y compris de toutes les contributions existantes. Ce changement s'appliquera à tous les projets.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Des étiquettes peuvent être ajoutées pour aider à catégoriser les contributions. <PERSON>ci, vous pouvez ajouter et supprimer des étiquettes que vous souhaitez utiliser sur votre plateforme. Vous pouvez ajouter les étiquettes à des projets spécifiques dans {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Bureau", "app.containers.AdminPage.SettingsPage.editFormTitle": "Modifier le lieu", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Modifier", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Modifier une étiquette", "app.containers.AdminPage.SettingsPage.fieldDescription": "Description", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Cette description existe seulement pour la collaboration interne, pour améliorer la comprhésion de chacun des administrateurs. Elle permet d’avoir une meilleure compréhension de ce qu'il se passe par lieu ou par quartier.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Nom", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Fournir un nom de zone pour toutes les langues", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Le nom que vous choisissez pour caractériser un lieu ou un quartier sera visible pour les utilisateurs au cours de l’inscription et lors du filtrage des projets.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Sauvegarder le tag", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Nom de l'étiquette", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Fournir un nom de tag pour toutes les langues", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Le nom que vous choisissez pour chaque étiquette sera visible pour les participants.", "app.containers.AdminPage.SettingsPage.fixedRatio": "Proportions fixes", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Ce type de bannière fonctionne mieux avec les images qui ne doivent pas être recadrées, comme les images contenant du texte, un logo ou des éléments spécifiques qui sont cruciaux pour vos citoyens. Cette bannière est remplacée par une boîte solide de la couleur primaire lorsque les utilisateurs sont connectés. Vous pouvez définir cette couleur dans les paramètres généraux. Vous trouverez de plus amples informations sur l'utilisation recommandée des images sur notre site {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "base de connaissance", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "<PERSON><PERSON>e largeur", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Cette bannière s'étend sur toute la largeur pour un effet visuel exceptionnel. L'image essaie de couvrir le plus d'espace possible, ce qui fait qu'elle n'est pas toujours visible à tout moment. Vous pouvez combiner cette bannière avec une superposition de n'importe quelle couleur. Vous trouverez plus d'informations sur l'utilisation recommandée de l'image sur notre site {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "base de connaissance", "app.containers.AdminPage.SettingsPage.header": "Banni<PERSON> de la page d'accueil", "app.containers.AdminPage.SettingsPage.headerDescription": "Personnalisez l'image et le texte de la bannière de la page d'accueil.", "app.containers.AdminPage.SettingsPage.header_bg": "Image de la bannière", "app.containers.AdminPage.SettingsPage.helmetDescription": "Admin-dashboard", "app.containers.AdminPage.SettingsPage.helmetTitle": "Paramètres administrateur", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "A<PERSON><PERSON>z votre propre contenu dans la section personnalisable située au bas de la page d'accueil.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "En-tête de page d'accueil | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Couleur du filtre de la bannière", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Opacité du filtre de la bannière", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Détecter les contenus inappropriés", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Détecter automatiquement les contenus inappropriés publiés sur la plateforme.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "Lorsque cette fonctionnalité est activée, le contenu des contributions, des propositions et des commentaires soumis par les participants sera analysé automatiquement. Les publications détectées comme potentiellement inappropriées ne seront pas bloquées, mais seront signalées pour examen sur la page {linkToActivityPage}.", "app.containers.AdminPage.SettingsPage.languages": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Sélectionnez les langues dans lesquelles vous souhaitez que votre plateforme soit disponible. Ils peuvent facilement choisir leur langue préférée à partir de cette liste via un bouton dans la barre de navigation.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Activité", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Veuillez insérer une image de couverture", "app.containers.AdminPage.SettingsPage.no_button": "Aucun bouton", "app.containers.AdminPage.SettingsPage.organizationName": "Nom de l'organisation ou de la commune", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "In<PERSON>quez le nom de l'organisation ou la ville pour toutes les langues.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "<PERSON><PERSON> le filtre", "app.containers.AdminPage.SettingsPage.phone": "Téléphone", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Configuration de la plateforme", "app.containers.AdminPage.SettingsPage.population": "Population", "app.containers.AdminPage.SettingsPage.populationMinError": "La population doit être un nombre positif.", "app.containers.AdminPage.SettingsPage.populationTooltip": "Population cible (généralement, le nombre d'habitants de la ville ou de la région). Cette information est utilisée pour calculer le taux de participation. Laissez ce champ vide si non applicable.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Bloqueur de contenu inapproprié", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Bloquer les contributions, propositions et commentaires contenant les mots offensants les plus fréquemment signalés", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Ce texte s'affiche sur la page d'accueil au dessus des projets.", "app.containers.AdminPage.SettingsPage.projectsSettings": "paramètres du projet", "app.containers.AdminPage.SettingsPage.projects_header": "Bannière du projet", "app.containers.AdminPage.SettingsPage.registrationFields": "Champs d'inscription", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Fournissez une brève description en haut de votre formulaire d'inscription.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Inscription", "app.containers.AdminPage.SettingsPage.save": "Enregistrer", "app.containers.AdminPage.SettingsPage.saveArea": "Sauvegarder la zone", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Une erreur est survenue. Veuillez réessayer.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Enregistré !", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Vos modifications ont été enregistrées.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Sélectionnez les réviseurs de projet", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "Sélectionnez les zones géographiques qui seront proposées aux utilisateurs lors de l'inscription", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Sélectionnez les thèmes qui seront présentés aux utilisateurs après l'inscription.", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Impossible de sauvegarder. Essayez à nouveau de modifier le paramètre.", "app.containers.AdminPage.SettingsPage.sign_up_button": "« S'inscrire »", "app.containers.AdminPage.SettingsPage.signed_in": "Bouton pour les visiteurs enregistrés", "app.containers.AdminPage.SettingsPage.signed_out": "Bouton pour les visiteurs non-enregistrés", "app.containers.AdminPage.SettingsPage.signupFormText": "Texte d'aide à l'inscription", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "A<PERSON><PERSON>z un bref mot d'explication en haut du formulaire d'inscription.", "app.containers.AdminPage.SettingsPage.statuses": "Statuts", "app.containers.AdminPage.SettingsPage.step1": "Email et mot de passe étape", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Ce message s'affiche en haut de la première page du formulaire d'inscription (nom, adresse mail, mot de passe).", "app.containers.AdminPage.SettingsPage.step2": "Questions d'inscription étape", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Ce message s'affiche en haut de la deuxième page du formulaire d'inscription (champs d'inscription supplémentaires).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Définissez les zones géographiques de la plateforme (par exemple : quartiers, arrondissements ou localités). Ces zones ont plusieurs fonctions. Elles peuvent être utilisées pour labelliser les projets, permettant ainsi la recherche par zone géographique. Les participants peuvent également être invités à indiquer leur zone de résidence lors de leur inscription. Cela permet la création de groupes d'utilisateurs sur base du lieu de résidence, groupes qui peuvent notamment être utilisés pour configurer l'accès et la visibilité des projets.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Ajou<PERSON>z le nom de votre organisation ou de votre commune, une Url permettant d'accéder à votre site Internet et les langues dans lesquelles cette plateforme devra être traduite.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Le texte dépasse la limite des 90 caractères autorisés", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Précisez les informations que les personnes sont invitées à fournir lors de leur inscription.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminologie", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Les paramètres ont été mis à jour avec succès.", "app.containers.AdminPage.SettingsPage.tabAreas1": "Zones géographiques", "app.containers.AdminPage.SettingsPage.tabBranding": "Identité visuelle", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Statuts des contributions", "app.containers.AdminPage.SettingsPage.tabPolicies": "Politiques", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Révision de projets", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Statuts des propositions", "app.containers.AdminPage.SettingsPage.tabRegistration": "Inscription", "app.containers.AdminPage.SettingsPage.tabSettings": "Général", "app.containers.AdminPage.SettingsPage.tabTopics2": "Étiquettes", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablette", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Comment les zones doivent-elles être appelées à l'intention des utilisateurs ? p. ex. quartiers, quartiers, comtés, ...", "app.containers.AdminPage.SettingsPage.titleAreas": "Zones géographiques", "app.containers.AdminPage.SettingsPage.titleBasic": "Paramètres généraux", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Le texte dépasse la limite des 35 caractères autorisés", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Gestion des étiquettes", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Cette bannière est particulièrement utile avec les images qui ne fonctionnent pas bien avec le texte du titre, du sous-titre ou du bouton. Ces éléments seront poussés en dessous de la bannière. Vous trouverez de plus amples informations sur l'utilisation recommandée des images sur notre site {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "base de connaissance", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON><PERSON> rang<PERSON>", "app.containers.AdminPage.SettingsPage.urlError": "Cet URL n'est pas valide.", "app.containers.AdminPage.SettingsPage.urlPatternError": "Saisissez une URL valide.", "app.containers.AdminPage.SettingsPage.urlTitle": "Site internet", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Ajoutez l’URL du site que vous souhaitez lier à cette plateforme. Ce site sera accessible au bas de page d’accueil.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "Choisissez comment afficher les utilisateurs sans nom sur la plateforme. Cela peut se produire lorsque vous sélectionnez l'option « Confirmation de l'e-mail » comme condition d'accès à une phase. Quel que soit le paramètre choisi, les utilisateurs auront toujours la possibilité de remplacer le nom de profil généré automatiquement lorsqu'ils participent.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Affichage du nom d'utilisateur (concerne uniquement les utilisateurs dont l'e-mail est confirmé)", "app.containers.AdminPage.SideBar.administrator": "Administrateur", "app.containers.AdminPage.SideBar.communityPlatform": "Communauté <PERSON>", "app.containers.AdminPage.SideBar.community_monitor": "Observatoire", "app.containers.AdminPage.SideBar.customerPortal": "Portail client", "app.containers.AdminPage.SideBar.dashboard": "Tableau de bord", "app.containers.AdminPage.SideBar.emails": "Campagnes Email", "app.containers.AdminPage.SideBar.folderManager": "Gestionnaire de dossiers", "app.containers.AdminPage.SideBar.groups": "Groupes", "app.containers.AdminPage.SideBar.guide": "Guide", "app.containers.AdminPage.SideBar.inputManager": "Gestion des contributions", "app.containers.AdminPage.SideBar.insights": "Reporting", "app.containers.AdminPage.SideBar.inspirationHub": "Pôle d'inspiration", "app.containers.AdminPage.SideBar.knowledgeBase": "Base de connaissances", "app.containers.AdminPage.SideBar.language": "<PERSON><PERSON>", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com/fr/", "app.containers.AdminPage.SideBar.menu": "Pages et menu", "app.containers.AdminPage.SideBar.messaging": "Messagerie", "app.containers.AdminPage.SideBar.moderation": "Activité", "app.containers.AdminPage.SideBar.notifications": "Notifications", "app.containers.AdminPage.SideBar.processing": "Traitement", "app.containers.AdminPage.SideBar.projectManager": "Gestionnaire de projets", "app.containers.AdminPage.SideBar.projects": "{tenant<PERSON><PERSON>, select, frw {Communes} other {Projets}}", "app.containers.AdminPage.SideBar.settings": "Paramètres", "app.containers.AdminPage.SideBar.signOut": "Se déconnecter", "app.containers.AdminPage.SideBar.support": "Aide", "app.containers.AdminPage.SideBar.toPlatform": "Vers la plateforme", "app.containers.AdminPage.SideBar.tools": "Outils", "app.containers.AdminPage.SideBar.user.myProfile": "Mon profil", "app.containers.AdminPage.SideBar.users": "Utilisateurs", "app.containers.AdminPage.SideBar.workshops": "Ateliers", "app.containers.AdminPage.Topics.addTopics": "Ajouter", "app.containers.AdminPage.Topics.browseTopics": "Parcourir les étiquettes", "app.containers.AdminPage.Topics.cancel": "Annuler", "app.containers.AdminPage.Topics.confirmHeader": "Êtes-vous sûr de vouloir supprimer cette étiquette ?", "app.containers.AdminPage.Topics.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.deleteTopicLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Ce sujet ne pourra plus être utilisé dans ce projet.", "app.containers.AdminPage.Topics.inputForm": "Formulaire de contribution", "app.containers.AdminPage.Topics.lastTopicWarning": "Au moins une étiquette est requise. Si vous ne souhaitez pas utiliser d'étiquettes, ils peuvent être désactivées dans l'onglet {ideaFormLink}.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Vous pouvez ajouter et supprimer les rubriques pouvant être affectées aux contributions de ce projet.", "app.containers.AdminPage.Topics.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.title": "Etiquettes autorisées", "app.containers.AdminPage.Topics.topicManager": "Gestion des étiquettes", "app.containers.AdminPage.Topics.topicManagerInfo": "Si vous souhaitez ajouter des étiquettes supplémentaires, vous pouvez le faire dans le {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Ajouter un nouveau groupe", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Nom du groupe", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Fournir un nom de groupe", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Créer un groupe manuel", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "De quel type de groupe avez-vous besoin ?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/fr/articles/7043801-utilisation-des-groupes-d-utilisateurs-intelligents-et-manuels", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Enregistrer le groupe", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Créer un groupe manuel", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Créer un groupe automatique", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "En savoir plus sur les groupes", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Vous choisissez manuellement quels sont les utilisateurs qui font partie de ce groupe.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Vous spécifiez des conditions qui ajoutent les utilisateurs au groupe de façon automatique et continue.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Groupe manuel", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Groupe automatique", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Il n'y a personne dans ce groupe pour l'instant", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Allez à la rubrique {allUsersLink} pour ajouter manuellement des utilisateurs.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Aucun utilisateur ne correspond à votre recherche", "app.containers.AdminPage.Users.GroupsPanel.select": "Choi<PERSON>", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Exporter", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Exporter", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Exporter la sélection", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Êtes-vous sûr(e) ?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Une erreur s'est produite lors de l'ajout des utilisateurs au(x) groupe(s), ve<PERSON><PERSON><PERSON> rées<PERSON>er.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Supprimer du groupe", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Supprimer les utilisateurs sélectionnés de ce groupe ?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Une erreur s'est produite lors de la suppression des utilisateurs de ce groupe, veuil<PERSON>z réessayer.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Ajouter la sélection à un groupe", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Ajouter", "app.containers.AdminPage.groups.permissions.add": "Ajouter", "app.containers.AdminPage.groups.permissions.addAnswer": "Ajouter une option", "app.containers.AdminPage.groups.permissions.addQuestion": "Ajouter des questions démographiques", "app.containers.AdminPage.groups.permissions.answerChoices": "Options de réponse", "app.containers.AdminPage.groups.permissions.answerFormat": "Format de réponse", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "Au moins une option doit être définie", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "Cet utilisateur modère le dossier contenant ce projet. Pour lui retirer ses droits de modération pour ce projet, vous pouvez soit lui retirer ses droits sur le dossier, soit déplacer le projet dans un autre dossier.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "<PERSON><PERSON>er une nouvelle question", "app.containers.AdminPage.groups.permissions.createAQuestion": "<PERSON><PERSON>er une question", "app.containers.AdminPage.groups.permissions.defaultField": "Champ par défaut", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Veuillez fournir un titre pour toutes les options", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "<PERSON><PERSON>-non (case à cocher)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Date", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Réponse longue", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Choix multiple (plusieurs réponses)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Valeur numérique", "app.containers.AdminPage.groups.permissions.fieldType_select": "Choix multiple (une seule réponse)", "app.containers.AdminPage.groups.permissions.fieldType_text": "Réponse courte", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Le système de permissions granulaires n'est pas inclus dans votre licence. Veuillez contacter votre Spécialiste en participation pour en savoir plus.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Êtes-vous sûr de que vouloir supprimer ce groupe du projet ?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Sélectionnez un ou plusieurs groupes", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {Aucun membre} one {un membre} other {{count} membres}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Veuillez fournir le titre dans toutes les langues", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "E<PERSON>-vous sûr ?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Administrateurs projet introuvables", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "<PERSON><PERSON> n'est montré, car il n'y a pas d'actions que l'utilisateur peut entreprendre dans ce projet.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Seuls les administrateurs peuvent créer une nouvelle question.", "app.containers.AdminPage.groups.permissions.option1": "Option 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Invitation en attente", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Qui peut annoter le document ?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Qui peut s'inscrire à un événement ?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Qui peut commenter les contributions ?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Qui peut commenter les propositions ?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Qui peut poster une proposition ?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Qui peut réagir aux contributions ?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Qui peut soumettre des contributions ?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Qui peut participer au sondage ?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Qui peut participer à l'enquête ?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Qui peut se porter volontaire ?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Qui peut voter pour des propositions ?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Qui peut voter ?", "app.containers.AdminPage.groups.permissions.questionDescription": "Description", "app.containers.AdminPage.groups.permissions.questionTitle": "Titre", "app.containers.AdminPage.groups.permissions.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Une erreur est survenue. Veuillez réessayer.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Succès !", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Vos modifications ont été enregistrées.", "app.containers.AdminPage.groups.permissions.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.selectValueError": "Veuillez sélectionner un type de réponse", "app.containers.AdminPage.new.createAProject": "Créer un projet", "app.containers.AdminPage.new.fromScratch": "À partir de zéro", "app.containers.AdminPage.phase.methodPicker.addOn1": "Extension", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "Analyses IA", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Facilitez l’émergence des points d’accord et de désaccord entre les participants, une idée à la fois.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Dégager un consensus", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Incorporez un PDF interactif et recueillez des commentaires avec Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Recueillir des commentaires sur un document", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Intégrer une enquête d'un service tiers", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "<PERSON>q<PERSON><PERSON><PERSON> externe", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Faites appel à l'intelligence collective de vos utilisateurs. Invitez-les à proposer des idées, à en débattre ou à partager leur opinion.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Recueillir des contributions et des commentaires", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Communiquer des informations", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Ne s'intègre pas avec les outils d'analyse IA de la plateforme", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Absence d'outils de reporting, de traitement et de visualisation des données intégrés à la plateforme", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Lien avec l'éditeur de rapports intégré à la plateforme", "app.containers.AdminPage.phase.methodPicker.logic1": "Logique", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Nombreux types de questions", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Invitez les participants à soumettre leurs propositions, qui devront recevoir un certain nombre de soutiens dans un délai imparti.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "Recueillir des propositions ou des pétitions", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "Sondage rapide", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "<PERSON><PERSON>ez un court questionnaire à choix multiples.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "Partagez des informations avec les utilisateurs à l'aide de rapports détaillés qui présentent les résultats des autres phases.", "app.containers.AdminPage.phase.methodPicker.survey1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Composez une enquête (avec réponses privées) en utilisant une gamme variée de types de questions pour approfondir votre compréhension des opinions et des attentes de vos utilisateurs.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Options de l'enquête", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "<PERSON><PERSON><PERSON> une enquête", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Demandez aux utilisateurs de se porter volontaires pour des activités et des causes, ou trouvez des participants pour un panel.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "<PERSON><PERSON><PERSON><PERSON> des participants ou des bénévoles", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Choisissez une méthode de vote et demandez aux utilisateurs d'établir un ordre de priorité entre différentes options.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Organiser un vote ou un exercice de priorisation", "app.containers.AdminPage.projects.all.all": "Tous", "app.containers.AdminPage.projects.all.createProjectFolder": "Nouveau dossier", "app.containers.AdminPage.projects.all.existingProjects": "Projets existants", "app.containers.AdminPage.projects.all.homepageWarning1": "Cette page permet de définir l'ordre des projets dans le menu déroulant \"Tous les projets\" de la barre de navigation. Si vous utilisez les widgets \"Projets et dossiers publiés\" ou \"Projets et dossiers (anciens)\" sur votre page d'accueil, l'ordre des projets dans ces widgets sera également déterminé par l'ordre que vous définissez ici.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "Les projets dont vous êtes le gestionnaire apparaîtront ici.", "app.containers.AdminPage.projects.all.noProjects": "Aucun projet n'a été trouvé.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Seuls les administrateurs peuvent créer des dossiers.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projets et dossiers", "app.containers.AdminPage.projects.all.publishedTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.all.searchProjects": "Rechercher des projets", "app.containers.AdminPage.projects.all.yourProjects": "Vos projets", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "Analyse IA", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Précision : {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "So<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "En plus des résumés automatiques, il est également possible de poser des questions spécifiques concernant vos résultats. Toutefois, cette fonctionnalité n'est pas incluse dans votre plan actuel. Adressez-vous à votre Spécialiste en participation Go Vocal ou à votre administrateur pour la débloquer.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Poser une question", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "Cette analyse inclut les questions suivantes :", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "Supprimer la question", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "Êtes-vous sûr de vouloir supprimer cette question ?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Su<PERSON><PERSON>er le résumé", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Êtes-vous sûr de vouloir supprimer ces résumés ?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Vos résumés s'afficheront ici, mais pour l'instant, vous n'en avez encore généré aucun.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "Cliquez sur le bouton « Résumé automatique » ci-dessus pour commencer.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "contributions sélectionnées", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Poser des questions sur un nombre restreint de contributions permet généralement d'obtenir une plus grande précision. N'hésitez pas à essayer les différents filtres disponibles pour affiner votre sélection.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "Question sur", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Question sur toutes les contributions", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Évaluez la qualité de cet insight", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "Restaurer les filtres", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Résumer", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Résumé pour", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Résumé de toutes les contributions", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "<PERSON>rci pour votre retour ", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "L'IA ne peut pas traiter autant de contributions en une seule fois. Divisez-les en groupes plus petits.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "Votre plan actuel vous permet de résumer un maximum de 30 contributions à la fois. Adressez-vous à votre Spécialiste en participation Go Vocal ou à votre administrateur pour augmenter cette limite.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "Je comprends", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Notre plateforme vous permet d'explorer les thèmes principaux, de résumer les données et de les examiner sous différents angles. Si vous recherchez des réponses précises, pensez à utiliser la fonction « Poser une question ».", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "Bien que rare, l'IA peut parfois générer des informations qui n'étaient pas présentes dans les données originales.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Hallucinations :", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "L'IA peut également mettre en avant certains thèmes ou idées plus que d'autres, ce qui pourrait biaiser l'interprétation globale.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Exagération :", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Notre système est conçu pour traiter de manière optimale entre 20 et 200 contributions. Au-delà de ce seuil, à mesure que le volume de données augmente, le résumé peut devenir plus général et global. Cela ne signifie pas que l'IA devient \"moins précise\", mais plutôt qu'elle se concentre sur des tendances plus générales. Pour des insights plus nuancés, nous vous recommandons d'utiliser la fonction d'(auto)-étiquetage pour segmenter les ensembles de données plus imposants en sous-ensembles plus gérables.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Volume de données et précision :", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "Nous vous recommandons d'utiliser les résumés générés par l'IA comme point de départ pour votre analyse d'un grand nombre de soumissions, mais pas comme une vérité absolue.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "Comment travailler avec l'IA", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Ajouter l'étiquette aux contributions sélectionnées", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "Ajouter l'étiquette", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "Cette fonctionnalité n'est pas incluse dans votre plan actuel. Adressez-vous à votre Spécialiste en participation Go Vocal ou à votre administrateur pour la débloquer.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "Toutes les contributions", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "Toutes les contributions", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "Toutes les étiquettes", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "Non, je le ferai", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Souhaitez-vous auto-étiqueter les contributions avec cette nouvelle étiquette ?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "Il existe <b>différentes méthodes</b> pour étiqueter les contributions automatiquement.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Utilisez <b>le bouton « Auto-étiquetage »</b> pour lancer la méthode de votre choix.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Cliquez sur une étiquette pour l'ajouter à la contribution sélectionnée.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "O<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Auto-étiqueter", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "L'auto-étiquetage ajoutera automatiquement des étiquettes à vos contributions. Vous pourrez modifier (ou supprimer) celles-ci à tout moment.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Étiquetage automatique", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Les contributions déjà associées à ces étiquettes ne seront pas classées à nouveau.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "La classification repose uniquement sur le nom de l'étiquette. Choisissez des mots-clés pertinents pour obtenir les meilleurs résultats.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Thèmes — par intitulé", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "Vous créez les étiquettes que vous ajoutez à quelques contributions à titre d'exemple, l'IA étiquette le reste", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Thèmes — par l'exemple", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "Similaire à \"Thèmes – par l'exemple\" mais avec une précision accrue car vous entraînez le système avec de bons exemples.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "Vous créez les étiquettes, l'IA les ajoute automatiquement aux contributions", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Thèmes — par intitulé", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "Fonctionne bien lorsque vous disposez d'un ensemble prédéfini d'étiquettes ou lorsque le projet se concentre sur des sujets précis.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Détecte les contributions présentant un ratio de like-dislike important", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Controversé", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Supprimer l'étiquette", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "Êtes-vous sûr de vouloir supprimer cette étiquette ?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Ne plus afficher", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Modifier l'étiquette", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "Un nom est requis.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Sélectionnez jusqu'à 9 étiquettes parmi lesquelles vous souhaitez répartir les contributions.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "La classification se fait en se basant sur les contributions actuellement étiquetées. L'IA essaiera de suivre votre exemple.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Thèmes — par l'exemple", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "Vous n'avez pas encore d'étiquettes personnalisées.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "L'IA infère automatiquement des étiquettes et les ajoute à vos contributions.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Thèmes — entièrement automatisé", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "Fonctionne bien lorsque vos projets abordent un large éventail de sujets. Un bon point de départ.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "Comment voulez-vous étiqueter ?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Contributions sans étiquettes", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Détecter la langue de chaque contribution", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "Aucun filtre actif", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Utilisez des étiquettes pour subdiviser et filtrer les contributions afin de créer des résumés plus précis ou ciblés.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Étiquettes de la plateforme", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Importe les étiquettes de la plateforme choisies par l'auteur lors de la publication", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Recommandé", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "Renommer l'étiquette", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "Annuler", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Nom", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "Enregistrer", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "Renommer l'étiquette", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Classe les contributions en fonction de leur sentiment (positif ou négatif) déterminé à partir du texte", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Détection des étiquettes", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Utiliser les filtres actuels", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "Quelles contributions souhaitez-vous étiqueter ?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Tâche d'auto-étiquetage", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Controversé", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "Échec", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "Par exemple", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "En cours", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "Par intitulé", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "Étiquette NLP", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "Aucune tâche d'IA récente n'a été effectuée", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Étiquette de la plateforme", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "En attente", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Su<PERSON>ès", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Tâche de synthèse", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "Au-dessus", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "Tous", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "En dessous", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "<PERSON><PERSON> de naissance", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "Localité", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Engagement", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "Filtres", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "<PERSON> ", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "Genre", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "Contribution", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "Nombre de commentaires", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Nombre de réactions", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "Nombre de votes", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "Au", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Ajouter à l'analyse", "app.containers.AdminPage.projects.project.analysis.anonymous": "Contribution anonyme", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Auteurs par âge", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Auteurs par domicile", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Tâches IA en arrière-plan", "app.containers.AdminPage.projects.project.analysis.comments": "Commentaires", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "Le tableau des domiciles est trop grand pour être affiché", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "Cacher les réponses vides", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Réponses", "app.containers.AdminPage.projects.project.analysis.end": "Fin", "app.containers.AdminPage.projects.project.analysis.filter": "<PERSON><PERSON><PERSON><PERSON> uniquement les contributions ayant cette valeur", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Cacher les réponses vides", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "<PERSON><PERSON><PERSON> c<PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Colonnes", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "Il y a {count} occurrences de cette combinaison.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "Dislikes", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Explorer", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "Faux", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "Contributions", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON>s", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Carte thermique suivante", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Insight suivant", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Cet écart n'est pas statistiquement significatif.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Les insights automatiques ne sont pas disponibles pour les projets comptant moins de 30 participants.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "Participants", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Carte thermique précédente", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Insight précédent", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Insight statistiquement significatif.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Résumer", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Étiquettes d'analyse", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "Vrai", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "Unités", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "Voir tous les insights", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Voir les analyses croisées", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Contributions sans étiquettes", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "Le fichier de formes téléchargé est invalide et ne peut pas être affiché.", "app.containers.AdminPage.projects.project.analysis.limit": "Limite", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "Question principale", "app.containers.AdminPage.projects.project.analysis.manageInput": "<PERSON><PERSON><PERSON> les contributions", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Graphique suivant", "app.containers.AdminPage.projects.project.analysis.noAnswer": "Pas de réponse", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "Aucune réponse n'a été fournie.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "Aucun fichier de formes n'a été téléchargé.", "app.containers.AdminPage.projects.project.analysis.noInputs": "Aucune contribution ne correspond aux filtres actuels", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Graphique précédent", "app.containers.AdminPage.projects.project.analysis.reactions": "Réactions", "app.containers.AdminPage.projects.project.analysis.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.removeFilter": "En<PERSON>er le filtre", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "En<PERSON>er le filtre", "app.containers.AdminPage.projects.project.analysis.search": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Les fichiers Shapefiles sont affichés ici au format GeoJSON. Il se peut donc que le style du fichier original ne s'affiche pas correctement.", "app.containers.AdminPage.projects.project.analysis.start": "D<PERSON>but", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Article de support", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "Inconnu", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "Voir toutes les questions", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "Voir les questions sélectionnées", "app.containers.AdminPage.projects.project.analysis.votes": "Votes", "app.containers.AdminPage.widgets.copied": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.copyToClipboard": "Copier ce code", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Copiez le code HTML", "app.containers.AdminPage.widgets.fieldAccentColor": "Couleur des accents", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Couleur d’arrière-plan du widget", "app.containers.AdminPage.widgets.fieldButtonText": "Texte du bouton", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Rejoignez-nous", "app.containers.AdminPage.widgets.fieldFont": "Police", "app.containers.AdminPage.widgets.fieldFontDescription": "Doit être un nom de police valide disponible par l’intermédiaire de {googleFontsLink}. Laisser vide pour utiliser la police par défaut.", "app.containers.AdminPage.widgets.fieldFontSize": "Taille de police (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Sous-titre de l’en-tête", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Vous avez votre mot à dire", "app.containers.AdminPage.widgets.fieldHeaderText": "Texte de l’en-tête", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Notre plateforme de participation", "app.containers.AdminPage.widgets.fieldHeight": "<PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Nombre de contributions", "app.containers.AdminPage.widgets.fieldProjects": "{tenant<PERSON><PERSON>, select, frw {Communes} other {Projets}}", "app.containers.AdminPage.widgets.fieldRelativeLink": "Liens vers", "app.containers.AdminPage.widgets.fieldShowFooter": "<PERSON><PERSON><PERSON><PERSON> le bouton", "app.containers.AdminPage.widgets.fieldShowHeader": "Aff<PERSON>r en-tête", "app.containers.AdminPage.widgets.fieldShowLogo": "Afficher le logo", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Couleur d'arrière-plan du site", "app.containers.AdminPage.widgets.fieldSort": "Trié par", "app.containers.AdminPage.widgets.fieldTextColor": "Couleur du texte", "app.containers.AdminPage.widgets.fieldTopics": "Étiquettes", "app.containers.AdminPage.widgets.fieldWidth": "Largeur (px)", "app.containers.AdminPage.widgets.homepage": "Page d’accueil", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Copiez l’extrait suivant de code HTML et collez-le sur le site Web où vous souhaitez que le widget apparaisse.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Code du widget HTML", "app.containers.AdminPage.widgets.previewTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.settingsTitle": "Paramètres", "app.containers.AdminPage.widgets.sortNewest": "Plus récent", "app.containers.AdminPage.widgets.sortPopular": "Plus votés", "app.containers.AdminPage.widgets.sortTrending": "Tendance", "app.containers.AdminPage.widgets.subtitleWidgets": "Vous pouvez créer un widget, le personnaliser et l'ajouter à votre propre site pour attirer des visiteurs vers cette plateforme.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Dimensions", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "En-tête et pied", "app.containers.AdminPage.widgets.titleInputSelection": "Sélection des contributions", "app.containers.AdminPage.widgets.titleStyle": "Style", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Enregistrer", "app.containers.ContentBuilder.homepage.PageTitle": "Page d’accueil", "app.containers.ContentBuilder.homepage.SaveError": "Un problème s'est produit lors de l'enregistrement de la page d'accueil.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "Deux colonnes", "app.containers.ContentBuilder.homepage.bannerImage": "Image de couverture", "app.containers.ContentBuilder.homepage.bannerSubtext": "Sous-texte de la bannière", "app.containers.ContentBuilder.homepage.bannerText": "Texte de la bannière", "app.containers.ContentBuilder.homepage.button": "Bouton", "app.containers.ContentBuilder.homepage.chooseLayout": "Mise en page", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Les options de personnalisation de la bannière de la page d'accueil — à l'exception de l'image et du texte — ne sont pas incluses dans votre licence actuelle. Contactez votre Spécialiste en participation Go Vocal pour en savoir plus.", "app.containers.ContentBuilder.homepage.customized_button": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Texte du bouton", "app.containers.ContentBuilder.homepage.customized_button_url_label": "<PERSON>n du bouton", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Affiche les 3 prochains événements à venir sur votre plateforme.", "app.containers.ContentBuilder.homepage.eventsDescription": "Affiche les 3 prochains événements à venir sur votre plateforme.", "app.containers.ContentBuilder.homepage.fixedRatio": "Bannière aux proportions fixes", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Ce type de bannière fonctionne mieux avec des images qui ne doivent pas être recadrées, comme des images avec du texte ou un logo. Cette bannière est remplacée par un bloc de couleur unie dans la couleur principale pour les utilisateurs sont connectés. Vous pouvez définir cette couleur dans les paramètres généraux. Consultez notre {link} pour plus de recommandations sur l'utilisation des images.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "base de connaissances", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "<PERSON><PERSON>e largeur", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Cette bannière s'étend sur toute la largeur pour un effet visuel optimal. L'image essaiera de couvrir autant d'espace que possible, ce qui peut la rendre invisible à certains moments. Vous pouvez combiner cette bannière avec un filtre de couleur. Consultez notre {link} pour plus de recommandations sur l'utilisation des images.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "base de connaissances", "app.containers.ContentBuilder.homepage.imageOverlayColor": "Couleur du filtre de la bannière", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Opacité du filtre de la bannière", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/fr/articles/1346397-quelles-sont-les-tailles-et-dimensions-recommandees-des-images-sur-la-plate-forme", "app.containers.ContentBuilder.homepage.invalidUrl": "URL invalide", "app.containers.ContentBuilder.homepage.no_button": "Aucun bouton", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Visiteurs", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "<PERSON><PERSON> le filtre", "app.containers.ContentBuilder.homepage.projectsDescription": "Vous pouvez ajuster l'ordre dans lequel vos projets sont affichés en les réorganisant sur la page des {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "projets", "app.containers.ContentBuilder.homepage.registeredUsersView": "Utilisateurs enregistrés", "app.containers.ContentBuilder.homepage.showAvatars": "Afficher les avatars", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Afficher les photos de profil des utilisateurs, ainsi que leur nombre, aux visiteurs non connectés", "app.containers.ContentBuilder.homepage.sign_up_button": "S'inscrire", "app.containers.ContentBuilder.homepage.signedInDescription": "Configuration de la bannière pour les utilisateurs connectés.", "app.containers.ContentBuilder.homepage.signedOutDescription": "C'est ainsi que les visiteurs qui ne sont pas inscrits sur la plateforme voient la bannière.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "Cette bannière est particulièrement adaptée lorsque l'image ne s'accorde pas bien avec le texte du titre ou du sous-titre, ou avec le bouton. Ces éléments seront placés sous la bannière. Consultez nos recommandations sur l'utilisation des images sur notre {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "base de connaissances", "app.containers.ContentBuilder.homepage.twoRowLayout": "Bannière en deux rangées", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "<PERSON>ur du contenu (pixels)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "La hauteur du contenu qui sera intégré à la page (en pixels).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Brève description du contenu que vous intégrez", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "Ces informations sont utiles pour les utilisateurs qui utilisent un lecteur d'écran ou une autre technologie d'assistance.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "URL du site web", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "URL complète du site web que vous souhaitez intégrer.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Affichez le contenu d'un site externe sur votre page via une iFrame HTML. Notez que certaines pages ne peuvent pas être intégrées. Si vous rencontrez des difficultés pour intégrer une page, vérifiez auprès du propriétaire de la page si elle est configurée pour permettre l'intégration.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Visitez notre page d'assistance", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "<PERSON><PERSON><PERSON><PERSON>, ce contenu n'a pas pu être intégré. {visitLinkMessage} pour en savoir plus.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Saisissez une URL valide, par exemple https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "URL", "app.containers.admin.ContentBuilder.accordionMultiloc": "Section accordéon", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Ou<PERSON><PERSON>r par défaut", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Texte", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Il s'agit d'une section en accordéon qui permet à l'utilisateur de plier et déplier son contenu. Vous pouvez modifier et formater cette section en utilisant l'éditeur disponible dans le panneau de droite.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Titre", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Titre de la section", "app.containers.admin.ContentBuilder.buttonMultiloc": "Bouton", "app.containers.admin.ContentBuilder.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.error": "erreur", "app.containers.admin.ContentBuilder.errorMessage": "Il y a une erreur sur le contenu {locale}, ve<PERSON><PERSON><PERSON> corriger le problème pour pouvoir sauvegarder vos modifications", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Masquer les avatars des participants", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "Il s'agit d'une enquête trimestrielle continue qui recueille votre avis sur la gouvernance et les services publics.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Répondre à l'enquête", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Aidez-nous à améliorer nos services", "app.containers.admin.ContentBuilder.homepage.default": "par défaut", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "Événements", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "Événements", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Appel à l'action (call-to-action)", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Description", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "URL du bouton principal", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Texte du bouton principal", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "URL du bouton secondaire", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Texte du bouton secondaire", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Titre", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Banni<PERSON> de la page d'accueil", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Banni<PERSON> de la page d'accueil", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Cartes d'images et de texte", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 colonne", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projets", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Activez les propositions dans la section \"Propositions\" du panneau d'administration pour ajouter l'onglet sur la page d'accueil", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Propositions", "app.containers.admin.ContentBuilder.imageMultiloc": "Image", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Brève description de l'image", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "Ajouter du texte alternatif aux images est essentiel pour rendre votre plateforme accessible aux utilisateurs utilisant des lecteurs d'écran.", "app.containers.admin.ContentBuilder.participationBox": "Boîte de <PERSON>", "app.containers.admin.ContentBuilder.textMultiloc": "Texte", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 colonnes", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 colonnes", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 colonnes avec respectivement 30% a et 60% de largeur", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 colonnes avec respectivement 60% a et 30% de largeur", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 colonnes égales", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "Contributions avec le plus de réactions", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Phase d'idéation", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "Il n'y a aucune contribution disponible pour ce projet ou cette phase.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "Nombre de contributions", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Afficher plus d'idées", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Titre", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Nombre total de contributions : {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Limiter le taille de la vue", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "Il n'y a aucune contribution disponible pour ce projet ou cette phase.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Sélectionnez la phase", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "Contenu", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "Réactions", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "Votes", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "Contribution", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Titre", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Taux d'inscription", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Inscriptions", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Participants", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Veuillez noter que les chiffres de participation peuvent être inférieurs à la réalité, car les données de participation à des enquêtes externes ne peuvent être mesurées.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Graphique", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Plage de dates", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Titre", "app.containers.admin.ReportBuilder.charts.noData": "Il n'y a pas de données disponibles pour les filtres que vous avez sélectionnés.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Sources de trafic", "app.containers.admin.ReportBuilder.charts.users": "Utilisateurs", "app.containers.admin.ReportBuilder.charts.usersByAge": "Utilisateurs par âge", "app.containers.admin.ReportBuilder.charts.usersByGender": "Utilisateurs par genre", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Visiteurs", "app.containers.admin.ReportBuilder.managerLabel1": "Gestionnaire de projets", "app.containers.admin.ReportBuilder.periodLabel1": "Période", "app.containers.admin.ReportBuilder.projectLabel1": "Projet", "app.containers.admin.ReportBuilder.quarterReport1": "Rapport de l'Observatoire de communauté : T{quarter}-{year}", "app.containers.admin.ReportBuilder.start1": "D<PERSON>but", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Acheter 1 siège supplémentaire", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Confirmer", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Êtes-vous sûr de vouloir accorder les droits de gestionnaire à une personne ?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Accorder les droits de gestionnaire", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Vous avez atteint la limite de sièges inclus dans votre plan, 1 siège supplémentaire sera ajouté.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Ajouter un statut", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Les statuts par défaut ne peuvent pas être supprimés.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Modifier", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Modifier le statut", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Les états actuellement attribués à une contribution ne peuvent pas être supprimés. Vous pouvez supprimer/modifier le statut des contributions existantes dans l'onglet {manageTab}.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Ce statut ne peut être ni supprimé ni déplacé.", "app.containers.admin.ideaStatuses.all.manage": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "La configuration des statuts personnalisés n'est pas incluse dans votre plan actuel. Contactez votre Government Success Manager ou votre administrateur pour l'activer.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "<PERSON><PERSON><PERSON> les différents statuts pouvant être attribués aux contributions des participants au sein d'un projet. Ces statuts sont visibles publiquement et aident à tenir les participants informés.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "<PERSON><PERSON><PERSON> les différents statuts pouvant être attribués aux propositions au sein d’un projet. Ces statuts sont visibles publiquement et permettent de tenir les participants informés.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Modifier les statuts des contributions", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Modifier les statuts des propositions", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Sélectionnés pour la mise en œuvre ou les prochaines étapes", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Approu<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Retour d'information officiel fourni", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Répondu", "app.containers.admin.ideaStatuses.form.category": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.categoryDescription": "Veuillez sélectionner la catégorie qui correspond le mieux à votre statut. Cette sélection permettra à notre outil d'analyse de traiter et d'analyser plus précisément les contributions.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Ne correspond à aucune des autres options", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldColor": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescription": "Description du statut", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Fournir une description du statut pour toutes les langues", "app.containers.admin.ideaStatuses.form.fieldTitle": "Nom du statut", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Fournir un nom de statut pour toutes les langues", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Mise en place réussie", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Mis en place", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "La proposition est inéligible", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "Inéligible", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Non éligible ou non sélectionnée pour l'étape suivante", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Non sélectionnée", "app.containers.admin.ideaStatuses.form.saveStatus": "Sauvegarder le statut", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Sélectionnée pour la mise en œuvre ou les prochaines étapes", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "En cours d'examen", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Vue mais pas encore traitée", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "<PERSON><PERSON>", "app.containers.admin.ideas.all.inputManagerMetaDescription": "<PERSON><PERSON><PERSON> les contributionet leurs statuts.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Gestionnaire de contributions | Plateforme de participation de {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Rédigez des commentaires, filtrez par étiquette ou basculez les contributions dans la phase suivante", "app.containers.admin.ideas.all.inputManagerPageTitle": "Gestion des contributions", "app.containers.admin.ideas.all.tabOverview": "Vue d'ensemble", "app.containers.admin.import.importInputs": "Entrées d'importation", "app.containers.admin.import.importNoLongerAvailable3": "Cette fonctionnalité n'est plus disponible ici. Pour importer des contributions dans une phase d'idéation, rendez-vous sur la page de la phase et sélectionnez \"Importer\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 siège administrateur supplémentaire} other {# sièges administrateurs supplémentaires}} et {managerSeats, plural, one {1 siège gestionnaire supplémentaire} other {# sièges gestionnaires supplémentaires}} seront ajoutés au-delà de la limite.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 siège administrateur supplémentaire sera ajouté} other {# sièges administrateurs supplémentaires seront ajoutés}} au-delà de la limite.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 siège gestionnaire supplémentaire sera ajouté} other {# sièges gestionnaires supplémentaires seront ajoutés}} au-delà de la limite.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Confirmer et envoyer les invitations", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Confirmer l'impact sur l'utilisation des sièges", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Vous avez atteint la limite des sièges inclus dans votre plan.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Les administrateurs et les gestionnaires de ce projet", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Administrateurs et gestionnaires uniquement", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Seuls les administrateurs de la plateforme et les gestionnaires de projets ou de dossiers peuvent effectuer l'action", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "<PERSON>ut le monde", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "Tout le monde, y compris les utilisateurs non enregistrés, peut participer.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Certains groupes d'utilisateurs", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Les membres de certains groupes d’utilisateurs peuvent participer. <PERSON><PERSON> pou<PERSON> gérer les groupes d’utilisateurs dans l’onglet « Utilisateurs ».", "app.containers.admin.project.permissions.viewingRightsTitle": "Qui peut voir ce projet ?", "app.containers.phaseConfig.enableSimilarInputDetection": "Activer la détection de contributions similaires", "app.containers.phaseConfig.similarInputDetectionTitle": "Détection de contributions similaires", "app.containers.phaseConfig.similarInputDetectionTooltip": "Montrer aux participants les contributions similaires pendant la saisie pour éviter les doublons.", "app.containers.phaseConfig.similarityThresholdBody": "Seuil de similarité (texte)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "Définit le seuil de similarité à partir duquel deux descriptions sont considérées comme similaires. Utilisez une valeur comprise entre 0 (strict) et 1 (permissif). Des valeurs plus faibles génèrent moins de suggestions, mais plus pertinentes.", "app.containers.phaseConfig.similarityThresholdTitle": "Seuil de similarité (titre)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "Définit le seuil de similarité à partir duquel deux titres sont considérés comme similaires. Utilisez une valeur comprise entre 0 (strict) et 1 (permissif). Des valeurs plus faibles génèrent moins de suggestions, mais plus pertinentes.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "Cette fonctionnalité est disponible dans le cadre de notre offre d'accès anticipé jusqu'au 30 juin 2025. Si vous souhaitez continuer à l'utiliser après cette date, veuillez contacter votre spécialiste en participation Go Vocal afin de discuter des options d'activation.", "app.containers.survey.sentiment.noAnswers2": "Aucune réponse pour le moment.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 commentaire} one {1 commentaire} other {# commentaires}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Les participants sont des utilisateurs ou des visiteurs qui ont participé à un projet, publié ou interagi avec une proposition ou assisté à des événements.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "Participants", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Taux de participation", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Pourcentage de visiteurs qui participent.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Total des participants", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Campagnes automatisées", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "E-mails automatisés", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "Des campagnes {quantity}", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Campagnes", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Campagnes personnalisées", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "E-mails personnalisés", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "Campagnes Email", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Total des e-mails envoyés", "app.modules.commercial.analytics.admin.components.Events.completed": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.events": "Evénements", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Total des événements ajoutés", "app.modules.commercial.analytics.admin.components.Events.upcoming": "À venir", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Accepté", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Invitations", "app.modules.commercial.analytics.admin.components.Invitations.pending": "En attente", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Total des invitations envoyées", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Aller au gestionnaire d'entrée", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Entrées", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Actifs", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Les projets qui ne sont pas archivés et qui sont visibles dans le tableau \"Actif\" de la page d'accueil", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Archivés", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Ébauches de projets", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Tous les projets archivés et les projets actifs de la ligne de temps qui sont terminés sont comptabilisés ici", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "{tenant<PERSON><PERSON>, select, frw {Communes} other {Projets}}", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Total des projets", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Le nombre de projets qui sont visibles sur la plateforme", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Nouvelles inscriptions", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Taux d'inscription", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Pourcentage de visiteurs qui créent un compte utilisateur.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Inscriptions", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Total des inscriptions", "app.modules.commercial.analytics.admin.components.Tab": "Visiteurs", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Les 30 derniers jours :", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Les 7 derniers jours :", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Pages vues par visite", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "<PERSON><PERSON><PERSON> de <PERSON> visite", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Visiteurs", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "« Visiteurs » est le nombre de visiteurs uniques. Si une personne visite la plateforme plusieurs fois, elle n'est comptée qu'une seule fois.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Visites", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "« Visites » correspond au nombre de sessions. Si une personne a visité la plateforme plusieurs fois, chaque visite est comptée.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Hier :", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Nombre", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Nombre de visiteurs", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Pourcentage de visiteurs", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "cliquez ici", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "Pour consulter la liste complète des référents, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Visiteurs", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Sources de trafic", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Visites", "app.modules.commercial.analytics.admin.components.totalParticipants": "Nombre total de participants", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Il n'y a pas encore de données sur les visiteurs.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Nous avons modifié la façon dont nous collectons et présentons les données des visiteurs. En conséquence, ces données sont plus précises et davantage de types d’informations sont disponibles, tout en restant conformes au RGPD. Bien que l’historique des visiteurs remonte à plus loin, nous n’avons commencé à collecter les données pour la « Durée de la visite », les « Pages vues par visite » et les autres graphiques qu’à partir de novembre 2024. Par conséquent, aucune donnée n’est disponible avant cette date. Si vous sélectionnez une période antérieure à novembre 2024, certains graphiques pourraient être vides ou sembler incohérents.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Livraisons d'e-mails dans le temps", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Participants", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Inscriptions au fil du temps", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Date", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistique", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Statistiques générales", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Visites et visiteurs au fil du temps", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Total sur la période", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Campagnes", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Entrée directe", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "Nombre de visiteurs", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Pourcentage de visites", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Sites web référents", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Moteurs de recherche", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Réseaux sociaux", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "Redirections SSO", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Source de trafic", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Nombre de visites", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Sites web", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Vous pouvez supprimer l'alerte en sélectionnant la publication et en cliquant sur le bouton 'supprimer' en haut. La publication réapparaîtra alors dans les onglets Vus ou Non vus.", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Contenu inapproprié détecté.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Aucune publication n'a été signalée par les utilisateurs, aucun contenu n'a été considéré comme inapproprié par notre Intelligence Artificielle d'analyse sémantique du langage", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Supprimer {numberOfItems, plural, one {contenu signalé} other {# contenus signalés}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Contenu signalé par un utilisateur.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "<PERSON><PERSON><PERSON>", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Éditeur de rapports", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Pages affichées dans votre barre de navigation", "app.modules.navbar.admin.containers.addProject": "Ajouter un projet à la barre de navigation", "app.modules.navbar.admin.containers.createCustomPageButton": "<PERSON><PERSON><PERSON> une page personnalisée", "app.modules.navbar.admin.containers.deletePageConfirmation": "Êtes-vous sûr de vouloir supprimer cette page ? Cette suppression ne peut être annulée. Vous pouvez également supprimer la page de la barre de navigation si vous n'êtes pas encore prêt à la supprimer.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Vous ne pouvez ajouter que 5 éléments maximum à la barre de navigation", "app.modules.navbar.admin.containers.pageHeader": "Pages et menu", "app.modules.navbar.admin.containers.pageSubtitle": "Votre barre de navigation peut afficher jusqu'à cinq pages en plus des pages d'accueil et des projets. Vous pouvez renommer les éléments du menu, les réorganiser et ajouter de nouvelles pages avec votre propre contenu.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "IA", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widgets", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Utilisez l'icône ☰ ci-dessous pour faire glisser du contenu IA dans votre rapport.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "Aucune analyse IA n'est disponible. Vous pouvez en créer dans votre projet.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Aller au projet", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "Question", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Sélectionnez une phase", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> l'Assistant IA", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Intégrez des analyses générées par l'IA dans votre rapport", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Créez des rapports plus rapidement avec l'IA", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "L'assistance IA pour la création de rapports n'est pas incluse dans votre plan actuel. Adressez-vous à votre Spécialiste en participation Go Vocal pour activer cette fonctionnalité.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "Cette fonctionnalité n'est pas incluse dans votre plan actuel. Adressez-vous à votre Spécialiste en participation Go Vocal ou à votre administrateur pour la débloquer.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Grouper par champ du profil utilisateur", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Question", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Agrégation", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Regroupez les réponses par champ utilisateur (sexe, localisation, âge, etc.) ou par d'autres questions de l'enquête.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "Aucun", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "Question", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Champ du profil utilisateur", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Phase d'enquête", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "Question d'enquête", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Êtes-vous sûr de vouloir le supprimer ?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "Annuler", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "<PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "Modifier", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "<PERSON>ez votre commentaire", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "Enregistrer", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "Écrivez votre commentaire ici", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Cliquez sur les boutons ci-dessous pour suivre ou ne plus suivre les projets. Le nombre de projets est indiqué entre parenthèses.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "Dans votre région", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "<PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Préférences de suivi", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "Il n'y a actuellement aucun projet actif correspondant à vos préférences de suivi.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "Ce widget affiche les projets associés aux « zones » suivies par l’utilisateur. Notez que votre plateforme peut être configurée pour utiliser un autre terme que « zones » ; consultez l’onglet « Zones géographiques » dans les paramètres de la plateforme. Si l’utilisateur ne suit encore aucune zone, le widget lui proposera les zones disponibles à suivre (jusqu’à 100 maximum).", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "Aucun projet ou dossier publié disponible", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Projets et dossiers publiés", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "Ce widget affichera les projets et dossiers publiés, selon l'ordre défini sur la page des projets. Ce comportement est similaire à celui de l'onglet \"Actif\" de l'ancienne version du widget de projets.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "Aucun projet ou dossier sélectionné", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Sélectionnez des projets ou des dossiers", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Projets et dossiers sélectionnés", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "Ce widget vous permet de sélectionner manuellement les projets et dossiers que vous souhaitez afficher et de déterminer leur ordre d'affichage.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projets", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "visitez notre centre d'assistance", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Pour plus d'informations sur les résolutions d'image recommandées, {supportPageLink}."}
{"UI.FormComponents.required": "piumasaqaataavoq", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Atuisut nalu<PERSON>ars<PERSON> o<PERSON>at", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Mianersoqqussut: qali<PERSON><PERSON>, illit toqqa<PERSON>t, ni<PERSON><PERSON><PERSON><PERSON><PERSON> qalipaateqarpoq. Tamaattumik allanneqartoq atuaruminaassinnaavoq. Qalipaat taarnerusoq toqqaruk atuarluarneqarsinnaaniassammat.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Pisussat Events Navigationslinjemut ilanngutiguk", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "<PERSON><PERSON> at<PERSON>, sulinium<PERSON>t tunngasut tamarmik navigationslinjemut ilanngunneqassapput.", "app.components.AdminPage.SettingsPage.eventsSection": "Pisimasut", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Qupperneq Hjem brugerdefiner-itut immikkoortinneqarpoq", "app.components.ProjectTemplatePreview.close": "Close", "app.components.ProjectTemplatePreview.createProject": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.goBack": "Go back", "app.components.ProjectTemplatePreview.goBackTo": "Uunga {goBackLink} uterit.", "app.components.ProjectTemplatePreview.infoboxLine1": "Peqataanermut suliami una immersugassaq atussaviuk?", "app.components.ProjectTemplatePreview.infoboxLine2": "Il<PERSON><PERSON><PERSON><PERSON><PERSON>ni allaffisso<PERSON><PERSON> imaluunniit {link} attavigiuk.", "app.components.ProjectTemplatePreview.projectFolder": "Project folder", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "<PERSON><PERSON><PERSON> to<PERSON> atorsinnaan<PERSON>ilaq. Ullorsiut una atorlugu ulloq allaguk: ÅÅÅÅ-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Suliniutip <PERSON>", "app.components.ProjectTemplatePreview.projectStartDate": "<PERSON><PERSON> su<PERSON> a<PERSON>", "app.components.ProjectTemplatePreview.projectTitle": "<PERSON><PERSON> su<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.projectTitleError": "Suliniutip taaguutaa allaguk", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Suliniutivit taaguutaa oqaatsit tamaasa atorlugit allaguk", "app.components.ProjectTemplatePreview.projectsOverviewPage": "suliniummik malinnaanermi qupperneq", "app.components.ProjectTemplatePreview.responseError": "Ups, ajutoortoqarpoq.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Skabelonit arlallit takukkit", "app.components.ProjectTemplatePreview.successMessage": "Suliniut pilers<PERSON>qa<PERSON> iluats<PERSON>!", "app.components.ProjectTemplatePreview.typeProjectName": "Suliniutip suunera allaguk", "app.components.ProjectTemplatePreview.useTemplate": "Skabeloni una atoruk", "app.components.UserSearch.addModerators": "Ilannguguk", "app.components.UserSearch.searchUsers": "Atuisunik u<PERSON>tuinerit allaguk...", "app.components.admin.Graphs": "Filterit atuuttut atorlugit paa<PERSON>su<PERSON>saqanngilaq.", "app.components.admin.Graphs.noDataShort": "No data available.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Comments over time", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Posts over time", "app.components.admin.InputManager.onePost": "1 input", "app.components.admin.PostManager.PostPreview.assignee": "Tigusisoq", "app.components.admin.PostManager.PostPreview.cancelEdit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.currentStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON> killiffik", "app.components.admin.PostManager.PostPreview.delete": "Delete", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Ilanngus<PERSON><PERSON> massuma peernissaa ilumut a<PERSON>? Taamaaliorneq allanngorteqqinneqarsinnaangilaq.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Ilanngussa<PERSON> massuma peernissaa ilumut a<PERSON>? Ilanngussaq suliat immikkoortuini tamani peerneqassaaq uteteqqissinnaajunnaarluni.", "app.components.admin.PostManager.PostPreview.edit": "Edit", "app.components.admin.PostManager.PostPreview.noOne": "Ilanngunneqanngilaq", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Peqataasut <PERSON><PERSON><PERSON>i uuma il<PERSON>ata amer<PERSON>a", "app.components.admin.PostManager.PostPreview.picks": "Toqqagaq: {picksNumber}", "app.components.admin.PostManager.PostPreview.save": "Toqqoruk", "app.components.admin.PostManager.PostPreview.submitError": "Kukkuneq", "app.components.admin.PostManager.allPhases": "Nikeriarfiit tamarmik", "app.components.admin.PostManager.allProjects": "Suliniutit tamarmik", "app.components.admin.PostManager.allStatuses": "Status-it tamarmik", "app.components.admin.PostManager.allTopics": "Tag-it tamarmik", "app.components.admin.PostManager.anyAssignment": "Aqutsisoq kin<PERSON>it", "app.components.admin.PostManager.assignedTo": "Uunga tunniunneqarpoq {assigneeName}", "app.components.admin.PostManager.assignedToMe": "Uannut tun<PERSON>neqa<PERSON>oq", "app.components.admin.PostManager.assignee": "Assignee", "app.components.admin.PostManager.bodyTitle": "Description", "app.components.admin.PostManager.comments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.contributionFormTitle": "Edit contribution", "app.components.admin.PostManager.currentLat": "Qeqqa<PERSON> si<PERSON>", "app.components.admin.PostManager.currentLng": "Qeq<PERSON><PERSON> ta<PERSON>", "app.components.admin.PostManager.currentZoomLevel": "Zoom-niveau", "app.components.admin.PostManager.delete": "Delete", "app.components.admin.PostManager.deleteAllSelectedInputs": "<PERSON><PERSON><PERSON> {count} <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.deleteConfirmation": "Ilumoorpit qaleriiaaniittoq taanna peer<PERSON>?", "app.components.admin.PostManager.edit": "Edit", "app.components.admin.PostManager.editedPostSave": "Save", "app.components.admin.PostManager.exportAllInputs": "Ilanngussat tamaasa nuutikkit (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Oqaaseqaatit tamaasa nuutikkit (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Pilersaarummut tassunga oqaaseqaatigineqartut nuutikkit (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Suliniummut uunga ilanngussat nuutikkit (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Ilanngussat toqqakkat nuutikkit (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Ilanngus<PERSON>ut toq<PERSON> oqa<PERSON>t nuutikkit (.xslx)", "app.components.admin.PostManager.exports": "N<PERSON>utitsineq", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Inuit atit qanoq takus<PERSON>eraat toqqaruk", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Killiffiup allannguutaa una nassuiaatigiuk", "app.components.admin.PostManager.fileUploadError": "One or more files failed to upload. Please check the file size and format and try again.", "app.components.admin.PostManager.formTitle": "Edit idea", "app.components.admin.PostManager.goToDefaultMapView": "<PERSON><PERSON><PERSON> ass<PERSON> qe<PERSON><PERSON>", "app.components.admin.PostManager.hiddenFieldsLink": "hidden fields", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Tip: add {hiddenFieldsLink} when setting up your Typeform survey to keep track of who has responded to your survey.", "app.components.admin.PostManager.importError": "<PERSON><PERSON> n<PERSON>, pissutigalugu GeoJSON-fil-itut aku<PERSON>gin<PERSON>i", "app.components.admin.PostManager.inputCommentsExportFileName": "ilann<PERSON><PERSON><PERSON>_oqaase<PERSON>t", "app.components.admin.PostManager.inputManagerHeader": "Ilanngussat", "app.components.admin.PostManager.inputs": "Ilanngussat", "app.components.admin.PostManager.inputsExportFileName": "input", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Ilanngussat utertitsiffigineqartussat kisiisa takutikkit", "app.components.admin.PostManager.issueFormTitle": "Edit issue", "app.components.admin.PostManager.latestFeedbackMode": "Opdatering kingulleq pisortatigoortoq nassuiaatitut atoruk", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Moving this input away from its current project will lose the information about its assigned phases. Do you want to proceed?", "app.components.admin.PostManager.multipleInputs": "{ideaCount} isumassarsiat", "app.components.admin.PostManager.newFeedbackMode": "Nutaamik opdaterinngiliorit allannguut nassuiaaserniarlugu", "app.components.admin.PostManager.noFilteredResults": "The filters you selected did not return any results", "app.components.admin.PostManager.noOne": "Unassigned", "app.components.admin.PostManager.officialUpdateAuthor": "Choose how people will see your name", "app.components.admin.PostManager.officialUpdateBody": "Explain this status change", "app.components.admin.PostManager.optionFormTitle": "Edit option", "app.components.admin.PostManager.participatoryBudgettingPicks": "Qin<PERSON>ineq", "app.components.admin.PostManager.pbItemCountTooltip": "The number of times this has been included in other participants' participatory budgets", "app.components.admin.PostManager.postedIn": "Posted in {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Edit project", "app.components.admin.PostManager.projectsTab": "Suliniutit ", "app.components.admin.PostManager.projectsTabTooltipContent": "Ilanngussat nusussinnaavatit i<PERSON>arlugillu, taamaasillutillu pilersaarummiit pilersaarummut nuutissinnaallugit Malugalugu, ilanngussaq pilersaarutinut piffissalikkanut aalajangersimasumut ilanngussagakku.", "app.components.admin.PostManager.proposedBudgetTitle": "Proposed budget", "app.components.admin.PostManager.publication_date": "Uunga saqqum<PERSON>inneqa<PERSON>oq", "app.components.admin.PostManager.questionFormTitle": "Edit question", "app.components.admin.PostManager.resetFiltersButton": "Immikkoortitikkat aallaqqaataanit aallartittussangorlugu aaqqikkit", "app.components.admin.PostManager.resetInputFiltersDescription": "Filterit nulstilikkit input tamaasa takuniarukkit.", "app.components.admin.PostManager.saved": "Toqqoqqapput", "app.components.admin.PostManager.setAsDefaultMapView": "Maannakkut qeqqa aamma zoomniveau toqqoruk kortimut standardindstillingitut", "app.components.admin.PostManager.statusChangeGenericError": "Kukkusoqarpoq. Uatsilaaq misileeqqigit imaluunniit support attavigiuk.", "app.components.admin.PostManager.statusChangeSave": "Killiffik allanngortiguk", "app.components.admin.PostManager.statusesTab": "Status", "app.components.admin.PostManager.statusesTabTooltipContent": "Ilanngussami status allanngortinniarukku nusussinnaavat i<PERSON>arlugulu. Allattoq ilanngussaqartullu allat nalunaarfigin<PERSON>pput, status allannguuteqartoq.", "app.components.admin.PostManager.submitApiError": "There was an issue submitting the form. Please check for any errors and try again.", "app.components.admin.PostManager.timelineTab": "Timeline", "app.components.admin.PostManager.timelineTabTooltipText": "Ilanngussat nusukkit <PERSON>, pilersaarutinut immikkoortunut assigiinngitsunut kopeerniarukkit.", "app.components.admin.PostManager.title": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.topicsTab": "Pineqartut", "app.components.admin.PostManager.topicsTabTooltipText": "Tags-inik input-imut <PERSON>, nusullugu iperarlugulu.", "app.components.admin.PostManager.votes": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {Less than a day} one {One day} other {# days}} left", "app.components.admin.ReportExportMenu.FileName.fromFilter": "uanngaanniit", "app.components.admin.ReportExportMenu.FileName.groupFilter": "eqimattat", "app.components.admin.ReportExportMenu.FileName.projectFilter": "suliniut", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tag", "app.components.admin.ReportExportMenu.FileName.untilFilter": "tikillugu", "app.components.admin.ReportExportMenu.downloadPng": "PNG-inngorlugu aaguk", "app.components.admin.ReportExportMenu.downloadSvg": "SVG-inngorlugu aaguk", "app.components.admin.ReportExportMenu.downloadXlsx": "<PERSON><PERSON>", "app.components.admin.SlugInput.regexError": "Uani slugimi ta<PERSON>aat naqinnerit minnerit naliginnaasut (a-z), kisi<PERSON><PERSON><PERSON> (0-9) a<PERSON><PERSON> kit<PERSON> (-) atorneqarsinnaapput. Naqinneq siulleq kingullerlu killiffilersuutaassanngilaq. Killifilersuutit tulleriit (--) inerteqqutaapput.", "app.components.admin.TerminologyConfig.saveButton": "Save", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Ilanngussaq", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Google Forms-imut linki qanoq ilillugu atassusernissaa pillugu suli paasissutissanik ujarlerneq uani {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "una ikiuutissatut allaatigisaq", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Isumassarsiaq", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Suna qulequta<PERSON>a?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Annerpaamik missing<PERSON><PERSON><PERSON>t allanneqassapput", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Minnerpaamik missingersuutit annerpaamik missingersuutinit annerusinnaanngillat", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Minnerpaamik missing<PERSON><PERSON><PERSON><PERSON> allanneqassapput", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Qinigassaq", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Suliniut", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "Apeqqut", "app.containers.Admin.Campaigns.campaignFrom": "Uannga:", "app.containers.Admin.Campaigns.campaignTo": "Uunga:", "app.containers.Admin.Campaigns.noAccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kisianni soorluuna e-mailernissannut isersinnaanngitsutit", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Emailit automatiseerikkat", "app.containers.Admin.Invitations.addToGroupLabel": "Inuit uku atuisut gruppiannut a<PERSON>jangersimasumut nammineq aaqqissukkamut ilanngukkit", "app.containers.Admin.Invitations.adminLabel1": "Give invitees admin rights", "app.containers.Admin.Invitations.adminLabelTooltip": "<PERSON> to<PERSON> to<PERSON>, inuit peqataaq<PERSON>, quppernerni inissiissutinut tamanut isersinnaalissapput.", "app.containers.Admin.Invitations.configureInvitations": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> na<PERSON>q<PERSON>ussakkit", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Peqataaqqusissutinik ujakkannut assingusunik soqanngilaq", "app.containers.Admin.Invitations.deleteInvite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.deleteInviteTooltip": "Peqataaqqusinermik unitsitsinikkut inummut tassunga peqataaqqusissummik nutaamik nassiusseqqissinnaassaatit.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. <PERSON><PERSON><PERSON><PERSON> a<PERSON>uk immersorlugulu", "app.containers.Admin.Invitations.downloadTemplate": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.email": "E-mail", "app.containers.Admin.Invitations.emailListLabel": "Inuit peqataaqquniakkavit mailadressii allakkit Adressit uniffik atorlugu immikkoortitikkit.", "app.containers.Admin.Invitations.exportInvites": "Peqataaqqusissutit tamaasa nuutikkit", "app.containers.Admin.Invitations.fileRequirements": "Pingaaruteqarpoq: Peqataaqqusissutit eqqortumik nassiunneqassappata ammut tulleriiaat skabelonimit peerneqassanngillat. Ammut tulleriiaat atorneqanngitsut imaqanngiinnassapput.", "app.containers.Admin.Invitations.filetypeError": "<PERSON><PERSON><PERSON> su<PERSON> kuk<PERSON>uuvoq. XLSX-fiilit ta<PERSON>t atorn<PERSON>qarsinnaapp<PERSON>.", "app.containers.Admin.Invitations.groupsPlaceholder": "Gruppinik toqqagaqanngilatit", "app.containers.Admin.Invitations.helmetDescription": "<PERSON><PERSON><PERSON><PERSON> qupperner<PERSON>t peqataaqqukkit", "app.containers.Admin.Invitations.helmetTitle": "Aqutsisut peqataaqqusissutaannut dashboardi", "app.containers.Admin.Invitations.importOptionsInfo": "Toqqagassat taakku Excel-fiilimi nass<PERSON>qareersimanngikkunik aatsaat eqqarsaatigineqassapput.", "app.containers.Admin.Invitations.importTab": "Emailadressit nuukkit", "app.containers.Admin.Invitations.invitationOptions": "Peqataaqqusissusiornissamut <PERSON>", "app.containers.Admin.Invitations.invitationSubtitle": "Inuit quppernermut qaqugukkulluunniit peqataaqqukkit. Peqataaqqusissummik Illit logo-nnik assiliartalimmik, quppernermut nalunaarsinnaanerinik aperisumik mailisissapput.", "app.containers.Admin.Invitations.invitePeople": "Inuit emailikkut peqataaqqukkit", "app.containers.Admin.Invitations.inviteStatus": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteStatusAccepted": "Accepted", "app.containers.Admin.Invitations.inviteStatusPending": "Utaqqineqarpoq", "app.containers.Admin.Invitations.inviteTextLabel": "<PERSON><PERSON> p<PERSON><PERSON><PERSON>issutip allannertassaanik allagit.", "app.containers.Admin.Invitations.invitedSince": "Peqataaqqusat", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Peqataaqqusissummi oqaaseq sorleq atussanersoq toqqaruk", "app.containers.Admin.Invitations.moderatorLabel": "Inuit uku suliniummi aqutsisutut pisinnaatitaaffilikkit", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Una toqqa<PERSON> to<PERSON>, peqa<PERSON><PERSON><PERSON><PERSON><PERSON>ut suliniummi(tini) toqqakkami(ni) suliniummi aqutsisutut pisinnaatitaaffilerneqassapput. Suliniummi aqutsisutut pisinnaatitaaffiit pillugit paasissutissat amerlanerusut {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "here", "app.containers.Admin.Invitations.name": "Ateq", "app.containers.Admin.Invitations.processing": "Peqataaqqussutit nassiutilerput. Utaqqilaarit...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Suliniummik to<PERSON>qanngilaq", "app.containers.Admin.Invitations.save": "Peqataaqqusissutinik nassiussigit", "app.containers.Admin.Invitations.saveErrorMessage": "Peqataaqqusissutit ajortoqarnera pissutigalugu nassiunneqanngillat. Kukkunerit ataaniittut aaqqilaarigit misileqqillugulu.", "app.containers.Admin.Invitations.saveSuccess": "iluatsilluarpoq!", "app.containers.Admin.Invitations.saveSuccessMessage": "Peqataaqqusissutit nassiunneqarput.", "app.containers.Admin.Invitations.supportPage": "qupperneq ikiorsiivik", "app.containers.Admin.Invitations.supportPageLinkText": "Qupperneq ikiorsiivik alakkaruk", "app.containers.Admin.Invitations.tabAllInvitations": "Peqataaqqusissutit tamarmik", "app.containers.Admin.Invitations.tabInviteUsers": "Atuisut peqa<PERSON>aq<PERSON>uk<PERSON>t", "app.containers.Admin.Invitations.textTab": "Mailadressit allakkit", "app.containers.Admin.Invitations.unknownError": "<PERSON><PERSON><PERSON><PERSON> il<PERSON>git<PERSON>orpoq. Ungasinngitsumi misilee<PERSON>a", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Skabeloni immersoriigaq ikkuguk", "app.containers.Admin.Invitations.visitSupportPage": "importskabelonimi ammut tulleriiaat atorneqarsinnaasut pillugit annertunerusumik paasisaqarusukkuit {supportPageLink}.", "app.containers.Admin.Moderation.all": "Tamakkerlutik", "app.containers.Admin.Moderation.belongsTo": "Uunga atavoq", "app.containers.Admin.Moderation.collapse": "Pertiterlugu", "app.containers.Admin.Moderation.comment": "Oqaaseqaat", "app.containers.Admin.Moderation.content": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.date": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.goToComment": "Oqaaseqaat una quppernermi nutaami ammaruk", "app.containers.Admin.Moderation.goToPost": "Il<PERSON><PERSON><PERSON><PERSON> una quppernermi nutaami ammaruk", "app.containers.Admin.Moderation.goToProposal": "<PERSON><PERSON><PERSON>uut una quppernermi nutaami ammaruk", "app.containers.Admin.Moderation.markFlagsError": "Elementi(t) nalunaaqutsinngitsoorpaa(i). Misileqqiguk.", "app.containers.Admin.Moderation.markNotSeen": "{selectedItemsCount, plural, one {# item} other {# items}} takuneqanngitsutut nalunaaqutseruk", "app.containers.Admin.Moderation.markSeen": "{selectedItemsCount, plural, one {# item} other {# items}} takuneqareersutut nalunaaqutseruk", "app.containers.Admin.Moderation.moderationsTooltip": "Qup<PERSON><PERSON><PERSON> uani qupperninni ilanngussat saqqummiunneqartut nutaat tamaasa sukkasuumik takusinnaavatit Ilanngussat 'takuneqareerpoq'-tut nalunaaqutsikkit, allat suut suli suliassaasut takusinnaaniassammatigit.", "app.containers.Admin.Moderation.noUnviewedItems": "Elementinik suli takuneqanngitsunik soqanngilaq", "app.containers.Admin.Moderation.noViewedItems": "Elementinik takuneqareersunik soqanngilaq", "app.containers.Admin.Moderation.post": "Allagarsiussaq", "app.containers.Admin.Moderation.profanityBlockerSetting": "Oqaasipilunnik blokeriineq", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Ilanngussat oqaasipilunnik nalunaarutigineqarajunnerpaanik imallit blokerikkit.", "app.containers.Admin.Moderation.project": "Suliniut", "app.containers.Admin.Moderation.read": "Takuneqareerpoq", "app.containers.Admin.Moderation.readMore": "<PERSON><PERSON><PERSON> mere", "app.containers.Admin.Moderation.removeFlagsError": "Mianersoqqussutit peerneqanngitsoorput Misileqqiguk.", "app.containers.Admin.Moderation.rowsPerPage": "Tulleriiaat quppernikkaarlugit", "app.containers.Admin.Moderation.settings": "Settings", "app.containers.Admin.Moderation.settingsSavingError": "Toqqunngitsoorpoq. Inissiissutit allanngorteqqillugit misiliguk.", "app.containers.Admin.Moderation.show": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.status": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.successfulUpdateSettings": "Inissiissutit nutarterput.", "app.containers.Admin.Moderation.type": "<PERSON>ussusaa", "app.containers.Admin.Moderation.unread": "Takuneqanngilaq", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Quppeneq una immikkoortunik tulliuttunik imaqarpoq. Pisariaqartitat naapertorlugu atuutsilersillugillu atorunnaarsissinnaavatit.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Sections", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "View page", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Not shown on page", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Shown on page", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Attachments", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "<PERSON><PERSON><PERSON> (annerpaamik 50mb), qupper<PERSON><PERSON><PERSON> aaneqarsinn<PERSON><PERSON><PERSON>anngus<PERSON>.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Bottom info section", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "<PERSON><PERSON><PERSON>nerup naqqani nammineq aaqqissuunneqarsinnaasumi ilanngukkusutatit ilanngukkit.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Edit", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Pisussanut allattorsimaffik", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Show events related to the projects.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Hero banner", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Quppernermi banneri allannertassarlu aaqqi<PERSON>ukkit.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Suliniutinut allattorsimaffik", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Show the projects based on your page settings. You can preview the projects that will be shown.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Top info section", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> qulaani nammineq aaqqissuunneqarsinnaasumi ilanngukkusutatit ilanngukkit.", "app.containers.Admin.PagesAndMenu.addButton": "Iserfiusinnaasut ilak<PERSON>t", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Navigationslinjemi ta<PERSON>a allaguk", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Ilumut qupperneq una peerniarpiuk? Uterteqqissinnaanavianngilaq.", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Quppernerit taku<PERSON>", "app.containers.Admin.PagesAndMenu.components.savePage": "Save page", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "<PERSON> successfully saved", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Attachments (max 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Kakkiussat toq<PERSON>orneqanngitsoorput", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Fiilit 50mb-mit annertunerussanngillat. <PERSON>ilit ilanngus<PERSON> quppernerup uumma ataani nuisassapput", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Kakkiussat toqqorneqarput", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Kakkiussat | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Attachments", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Kakkiussat to<PERSON>ukkit", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Kakkiussat to<PERSON>ukkit", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Oqaatsit tamarmik imassaannik ikkussuigit", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Paasissutissanut atugassi<PERSON> ataaniittoq toqq<PERSON>aan<PERSON>ilaq", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Paasissutissanut atugassiaq ataaniittoq toqqorneqarpoq", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Naq<PERSON>ni paa<PERSON>sutis<PERSON>ut atugassi<PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Save and enable bottom info section", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Paasissutissanut atugassi<PERSON> ataaniittoq toqqoruk", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Please select at least one tag", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "By area", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "By tag(s)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Qupperneq nammineq aaqqissuunneqarsinn<PERSON>soq allanngortiguk", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Linked projects", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Select which projects and related events can be displayed on the page.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> pilers<PERSON><PERSON><PERSON><PERSON> il<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Qupperneq nammineq aaqqissuunneqarsinn<PERSON>q toqq<PERSON>oq", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Title in navigation bar", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Quppernermik nammineq aaqqissuunneqarsinnaasumik pilersitsigit | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Quppernermik nammineq aaqqissuunneqarsinnaasumik pilersitsigit", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "None", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON> iniss<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Qupperneq nammineq aaqqissuunneqarsinn<PERSON>q toqq<PERSON>uk", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Please select an area", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Selected area", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Selected tags", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "The slug can only contain regular, lowercase letters (a-z), numbers (0-9) and hyphens (-). The first and last characters cannot be hyphens. Consecutive hyphens (--) are forbidden.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Slug-imik i<PERSON>kus<PERSON>ariaqarputit", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Title", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Oqaatsini tamani qulequtsiigit", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Enter a title", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "View custom page", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Qupperneq nammineq aaqqissuunneqars<PERSON>soq allanngortiguk | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Page content", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Edit", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "No available projects based on your {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "This project has no tag or area filter, so no projects will be displayed.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Projects list | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "page settings", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Projects list", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "The following projects will be shown on this page based on your {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "NALINGINNAASOQ", "app.containers.Admin.PagesAndMenu.deleteButton": "Delete", "app.containers.Admin.PagesAndMenu.editButton": "Edit", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Success", "app.containers.Admin.PagesAndMenu.heroBannerError": "Banner iseqqaar<PERSON>mmiittoq toqq<PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Banner iseqqaar<PERSON>mmiittoq toqq<PERSON>q", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Banner iseqqa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uk", "app.containers.Admin.PagesAndMenu.homeTitle": "Home", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Provide content for at least one language", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Quppernerit toqqagassallu | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Navigationslinjemiit peeruk", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Save and enable hero banner", "app.containers.Admin.PagesAndMenu.title": "Quppernit & toqqagassat", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Success", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.topInfoError": "Paasissutissanut atugassiaq qulaaniittoq toqq<PERSON>q", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Paasissutissanut atugassiaq qulaaniittoq toqq<PERSON>rpoq", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Paasissutissat qulaaniittut | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Paasissutissanut atugassiaq qulaaniittoq", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Save and enable top info section", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Paasissutissanut atugassiaq qulaaniittoq toqq<PERSON>uk", "app.containers.Admin.PagesAndMenu.viewButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Blocked users", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Manage blocked users.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Gruppe peeruk", "app.containers.Admin.Users.GroupsHeader.editGroup": "Gruppe allanngortiteruk", "app.containers.Admin.Users.GroupsPanel.allUsers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Gruppit", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Inuit suliniaqatigiiffiillu quppernermi nalunaarsimasut tamakkiisumik takusinnaalikkit. Atuisut toqqakkat gruppinut nammineq aaqqissukkanut smart gruppinulluunniit ilanngukkit.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Invitation pending", "app.containers.Admin.Users.admin": "Aqutsisoq", "app.containers.Admin.Users.deleteUser": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.email": "E-mail", "app.containers.Admin.Users.helmetDescription": "Aqutsisuni atuisunik allattorsimaffik", "app.containers.Admin.Users.helmetTitle": "Aqutsisut - atuisunut dashboardi", "app.containers.Admin.Users.name": "Ateq", "app.containers.Admin.Users.options": "Options", "app.containers.Admin.Users.permissionToBuy": "To give {name} admin rights, you need to buy 1 additional seat.", "app.containers.Admin.Users.seeProfile": "<PERSON>il <PERSON>", "app.containers.Admin.Users.userBlockModal.allDone": "All done", "app.containers.Admin.Users.userBlockModal.blockAction": "Block user", "app.containers.Admin.Users.userBlockModal.blockInfo1": "The content of this user won't be removed through this action. Don't forget to moderate their content if needed.", "app.containers.Admin.Users.userBlockModal.blocked": "Blocked", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "This user has been blocked since {from}. The ban lasts until {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "Cancel", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Are you sure you want to unblock {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} is blocked until {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 day} other {{numberOfDays} days}}", "app.containers.Admin.Users.userBlockModal.header": "Block user", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Reason", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "This will be comunicated to the blocked user.", "app.containers.Admin.Users.userBlockModal.subtitle1": "The selected user won't be able to log in to the platform for {daysBlocked}. If you wish to revert this, you can unblock them from the list of blocked users.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Unblock", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "Yes, I want to unblock this user", "app.containers.Admin.Users.userDeletionConfirmation": "Una atuisoq piivis<PERSON>?", "app.containers.Admin.Users.userDeletionFailed": "<PERSON><PERSON><PERSON><PERSON> uuma <PERSON> a<PERSON>rt<PERSON>rp<PERSON>q. Inussiarnersumik misileeqqigit.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.youCantDeleteYourself": "Aqutsisut quppernerat atorlugu nammineq atuisuuffiit <PERSON>ng<PERSON>t", "app.containers.Admin.Users.youCantUnadminYourself": "Aqutsisutut atuuffiit massakkut atorunnaarsissinnaanngilat", "app.containers.Admin.emails.addCampaignTitle": "Create a new email", "app.containers.Admin.emails.allUsers": "Registered users", "app.containers.Admin.emails.changeRecipientsButton": "Tigusisussat allanngortitikkit", "app.containers.Admin.emails.confirmSendHeader": "Atuisut tamaasa emailerfigissavigit?", "app.containers.Admin.emails.deleteButtonLabel": "Delete", "app.containers.Admin.emails.draft": "Allaqqitassiaq", "app.containers.Admin.emails.editButtonLabel": "Edit", "app.containers.Admin.emails.editCampaignTitle": "Paasititsiniaaneq allanngortiteruk", "app.containers.Admin.emails.failed": "Kukkuvoq", "app.containers.Admin.emails.fieldBody": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldBodyError": "Provide an email message for all languages", "app.containers.Admin.emails.fieldReplyTo": "<PERSON><PERSON><PERSON><PERSON> uunga nassi<PERSON>", "app.containers.Admin.emails.fieldReplyToEmailError": "Provide an email address in the correct format, <NAME_EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "E-mail addressi allaguk", "app.containers.Admin.emails.fieldReplyToTooltip": "Emailimut akissutit sumut nassiuttassanerlugit toqqa<PERSON>.", "app.containers.Admin.emails.fieldSender": "Uanngaanniit", "app.containers.Admin.emails.fieldSenderError": "Provide a sender of the email", "app.containers.Admin.emails.fieldSenderTooltip": "Tigusisussat kina nassiussisutut takussaneraat a<PERSON>inn<PERSON>.", "app.containers.Admin.emails.fieldSubject": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldSubjectError": "Provide an email subject for all languages", "app.containers.Admin.emails.fieldSubjectTooltip": "Taanna e-mailimi sammisap allass<PERSON>i atuisullu allagarsiaanni takuneqarsinnaassaaq. Ersarissumik tiguaasinnaasumillu allagit.", "app.containers.Admin.emails.fieldTo": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldToTooltip": "Atuisut gruppii e-mailimik pissarsisussat toqqarsinnaavatit", "app.containers.Admin.emails.formSave": "Allaqqitassiatut toqqoruk", "app.containers.Admin.emails.groups": "Groups", "app.containers.Admin.emails.helmetDescription": "E-mailit atuisut gruppiinut nammineerlutit nassiutikkit paasititsiniaanerillu automatiskit atuutsilerlugit", "app.containers.Admin.emails.previewSentConfirmation": "E-maili ta<PERSON>sutissiaq emailadressinnut nassiunneqarpoq", "app.containers.Admin.emails.previewTitle": "Takussutissiaq", "app.containers.Admin.emails.send": "Nassiutiguk", "app.containers.Admin.emails.sendNowButton": "Massak<PERSON><PERSON>", "app.containers.Admin.emails.sendTestEmailButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.senderRecipients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aamma tigusisoq", "app.containers.Admin.emails.sending": "Nassiutilerpoq", "app.containers.Admin.emails.sent": "<PERSON><PERSON>", "app.containers.Admin.emails.toAllUsers": "At<PERSON><PERSON>ut nalunaarsukkanut tamanut e-maili una nassiutissaviuk?", "app.containers.Admin.ideas.import": "Import", "app.containers.Admin.messaging.helmetTitle": "Messaging", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "This image is part of the folder card; the card that summarizes the folder and is shown on the homepage for example. For more information on recommended image resolutions, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "This image is shown at the top of the folder page. For more information on recommended image resolutions, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "visit our support center", "app.containers.Admin.projects.all.components.archived": "Allagaasivimmut inissinneqarpoq", "app.containers.Admin.projects.all.components.draft": "Draft", "app.containers.Admin.projects.all.components.manageButtonLabel": "Manage", "app.containers.Admin.projects.all.copyProjectButton": "Copy project", "app.containers.Admin.projects.all.copyProjectError": "There was an error copying this project, please try again later.", "app.containers.Admin.projects.all.deleteFolderButton1": "Delete folder", "app.containers.Admin.projects.all.deleteFolderConfirm": "Ilumoortumik mappi una peerniarpiuk? Suliniutit mappimiittut tamarmik aamma peerneqassapput. Una iliuuseq uterteqqinneqarsinnaanngilaq.", "app.containers.Admin.projects.all.deleteFolderError": "Mappip uumma <PERSON> a<PERSON>rt<PERSON>rpoq. Misileqqiguk.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Delete project", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Ilumoortumik suliniut una peerniarpiuk? Uterteqqinneqarsinnaanngilaq.", "app.containers.Admin.projects.all.deleteProjectError": "Suliniutip uuma <PERSON> a<PERSON>q. Kingusinnerusukku<PERSON> mi<PERSON>.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "This is some text. You can edit and format it by using the editor in the panel on the right.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Participants", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Project results", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Report summary", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Add the goal of the project, participation methods used, and the outcome", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Visitors", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Title is already taken", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "No project", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "You cannot duplicate this report because it contains data that you don't have access to.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "You cannot edit this report because it contains data that you don't have access to.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Are you sure you want to delete \"{reportName}\"? This action cannot be undone.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Are you sure you want to delete this report? This action cannot be undone.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Delete", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Duplicate", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Edit", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "Modified {days, plural, no {# days} one {# day} other {# days}} ago", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "An error occurred when trying to create this report. Please try again later.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Start with a blank page", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Report title", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Create a report", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Print to PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Start with a project template", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Report template", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "A report with this title already exists. Please pick a different title.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Share as PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "To share with everyone, print the report as a PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Share as web link", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "This web link is only accessible to admin users.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Share", "app.containers.Admin.reporting.contactToAccess": "Creating a custom report is part of the premium license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "All reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Create a report", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Customise your report and share it with internal stakeholders or community with a web link.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Your reports will appear here.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Search reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Progress reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "These are reports created by your Government Success Manager", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "Your reports", "app.containers.Admin.reporting.helmetDescription": "Admin reporting page", "app.containers.Admin.reporting.helmetTitle": "Reporting", "app.containers.Admin.reporting.printPrepare": "Preparing to print...", "app.containers.Admin.reporting.reportBuilder": "Report builder", "app.containers.Admin.reporting.reportHeader": "Report header", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "qupperner<PERSON> atuisut amer<PERSON>at", "app.containers.AdminPage.DashboardPage._blank": "ilisimaneqanngilaq", "app.containers.AdminPage.DashboardPage.allGroups": "Gruppit tamarmik", "app.containers.AdminPage.DashboardPage.allProjects": "Suliniutit tamarmik", "app.containers.AdminPage.DashboardPage.allTime": "<PERSON><PERSON><PERSON>t tamarmik", "app.containers.AdminPage.DashboardPage.comments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Platforminik atuisut sinniisaat takutinniarlugit uuttorniarlugillu paasissutissanik tunngaviusunik pisariaqartitsisoqassaaq.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Qani<PERSON><PERSON> ta<PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Massakkut {fieldName}-ip dashboardia sulissutigaarput, qani<PERSON><PERSON> atorn<PERSON>qarsinnaalerumaarpoq", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# element er} other {# elementer er}} uani grafimi ersinngitsut. Paasissutissat tamaasa takorusukkukkit {tableViewLink}-imut nuuguk.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "Atuisunik nalunaarsuinermut {requiredOrOptional}", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} uannga {total} atuisut ilaatillugit ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "{numberOfHiddenItems}-it nuisikkit", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Nammineq <PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Paasissutissat tunngaviusut allakkit.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Assigiissuseqassutsimut:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Inernerit uku atuisunut paasissutissat innuttaasut sinnerinut sanilliullugit qanoq eqqortiginersut takussusissavaat. {representativenessArticleLink} pillugu paasissutissat amerlanerusut pissarsiarikkit.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Required", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Paasissutissat tunngaviusut nassiutikkit", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "tabelimik takussutissiaq", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Innuttaa<PERSON>t tamarmik", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Users", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Ukioqatigiiaat ilanngutikkit", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} a<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Ukioqatigiiaat", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Ukioqatigiiaat {upperBound} aamma uto<PERSON>u ilanngun<PERSON>qanngillat.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Ukioqatigiiaat {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Ukioqatigiiaat", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Eqimattakkaarinermi assersuutit atukkit", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "<PERSON><PERSON><PERSON> piik<PERSON>t", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "From", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Tunngaviusumik paasissutissanut tulluarsarniarlugit ukioqatigiiaat toqqakkit.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Save", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "To", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Ukioqatigiiaat aaqqissukkit", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Tamanna naats<PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "See less", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "{numberOfHiddenItems} flere…-it allat takukkit...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Qaa<PERSON>mmut (toqqarneqarsinnaasoq)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Ukioqatigiiaat (Ukioq inunngorfik)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Coming soon", "app.containers.AdminPage.DashboardPage.components.Field.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.default": "Nalinginnaasoq", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Grafimit atorniakkatit immersukkit imaluunniit tassannga atorunnaarsikkusutatit atorunnaarsikkit. Periarfissaq minnerpaamik ataaseq immersorneqassaaq.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Naammassineqanngitsoq", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Najugaqartut amerlassusaat katillugit", "app.containers.AdminPage.DashboardPage.components.Field.options": "Options", "app.containers.AdminPage.DashboardPage.components.Field.save": "Save", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Saved", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Tunngaviusumik paasissutissat nalunaarsorniarlugit {setAgeGroupsLink} toqqakkit.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "ukioqatigiiaat to<PERSON>t", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Avg. response time: {days} days", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Average amount of days to respond", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "Feedback given", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Input status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Inputs by status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Number of inputs", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Official update", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Percentage of inputs", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Response time", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Status changed", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Total", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Paasissutissat tunngaviusut allanngortitikkit", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "imatut assigiissuseqassutsimut inernerit naatsorsortarpavut", "app.containers.AdminPage.DashboardPage.continuousType": "Without a timeline", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Katillugit amerlassusaat", "app.containers.AdminPage.DashboardPage.customDateRange": "Atuisumit inissitsitigaq", "app.containers.AdminPage.DashboardPage.day": "dag", "app.containers.AdminPage.DashboardPage.false": "eqq<PERSON>ng<PERSON>q", "app.containers.AdminPage.DashboardPage.female": "arna<PERSON>", "app.containers.AdminPage.DashboardPage.fromTo": "from {from} to {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Qup<PERSON>nermi sammisassanut dashboardi", "app.containers.AdminPage.DashboardPage.helmetTitle": "Aqutsisunut dashboard<PERSON>ut qupperneq", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Pick resource to show by project", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Pick resource to show by topic", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Atuisoqatigiikkaat to<PERSON>t", "app.containers.AdminPage.DashboardPage.male": "an<PERSON>t", "app.containers.AdminPage.DashboardPage.month": "må<PERSON>", "app.containers.AdminPage.DashboardPage.noData": "There is no data to be shown.", "app.containers.AdminPage.DashboardPage.noPhase": "No phase created for this project", "app.containers.AdminPage.DashboardPage.overview.management": "Management", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projects & Participation", "app.containers.AdminPage.DashboardPage.overview.showLess": "Show less", "app.containers.AdminPage.DashboardPage.overview.showMore": "Show more", "app.containers.AdminPage.DashboardPage.participationPerProject": "Suliniutini at<PERSON> peqa<PERSON>q", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Participation per tag", "app.containers.AdminPage.DashboardPage.perPeriod": "Per {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "<PERSON><PERSON>ut 30-t <PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.previous90Days": "Ullut 90-it king<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.previousWeek": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "app.containers.AdminPage.DashboardPage.previousYear": "Ukioq <PERSON>eq", "app.containers.AdminPage.DashboardPage.projectType": "Project type : {projectType}", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Uku tunngaviusumik paasissutissat quppernermi atuisut innuttaasut sinnerinut sanilliullugit assigiissuseqassuseq naatsorsussagaani pisariaqarput.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Please provide a base dataset.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Atuisutit innuttaasut sinnerinut sanilliullugit qanoq assigiissuseqartiginersut takuuk - atuisunik nalunaarsuinermi paasissutissat katersorneqartut tunngavigalugit. {representativenessArticleLink} pillugu paasissutissat amerlanerusut pissarsiarikkit.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Atuisutit innuttaasut sinnerinut sanilliullugit qanoq assigiissuteqartiginersut takuuk - atuisunik nalunaarsuinermi paasissutissat katersorneqartut tunngavigalugit.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "<PERSON><PERSON><PERSON><PERSON><PERSON> sin<PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Utimut dashboardimut", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Nalunaarsukkat atorneqartut maanna i<PERSON>fartorneqanngillat.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Uani dashboardimi ilai toqq<PERSON>innaavatit/takutissinnaavatit paasissutissallu tunngaviusut ikkussuullugit. Uunga atugassat {userRegistrationLink} kisimik uani takutinneqassapput.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Edit base data", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "at<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Submit base data", "app.containers.AdminPage.DashboardPage.resolutionday": "ullunngorlugit", "app.containers.AdminPage.DashboardPage.resolutionmonth": "qaammatinngorlugit", "app.containers.AdminPage.DashboardPage.resolutionweek": "sapaatit akunninngorlugit", "app.containers.AdminPage.DashboardPage.selectProject": "Select project", "app.containers.AdminPage.DashboardPage.selectedProject": "suliniummut filteri atuuttoq", "app.containers.AdminPage.DashboardPage.selectedTopic": "tag-inut filteri atuuttoq", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON> su<PERSON> takuuk.", "app.containers.AdminPage.DashboardPage.tabOverview": "Overview", "app.containers.AdminPage.DashboardPage.tabReports": "Suliniutit ", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "<PERSON><PERSON><PERSON><PERSON>qa<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.tabUsers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.timelineType": "Tidslinje", "app.containers.AdminPage.DashboardPage.titleDashboard": "Dashboardi", "app.containers.AdminPage.DashboardPage.total": "Total", "app.containers.AdminPage.DashboardPage.totalForPeriod": "This {period}", "app.containers.AdminPage.DashboardPage.true": "eqqorpoq", "app.containers.AdminPage.DashboardPage.unspecified": "allassimanngilaq", "app.containers.AdminPage.DashboardPage.users": "Users", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Atuisut ukiui naapertorlugit", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Atuisut sumiiffii naapertorlugit", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Atuisut suiaassusii naapertorlugit", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.week": "uge", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Assimik faviconimik toqqaanissamut siunnersuut: assimik takujuminartumik toq<PERSON>t, asseq ersersitaq mikisuaraasussaammat. Asseq PNG-tut toqqorneqassaaq kipparissuulluni akimullu ersittumik tunuliaqutaqarluni (imaluunniit pisariaqassappat qaqortumik). Faviconit ataasiaannarluni inissinneqassaaq, allannguinissaq teknikkikkut ikiorserneqarnissamik pisariaqartitsiviussammat.", "app.containers.AdminPage.FaviconPage.save": "Toqqoruk", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, king<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.containers.AdminPage.FaviconPage.saveSuccess": "iluatsilluarpoq!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Ilannguguk", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Delete", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Mappinik aqutsisut", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Mappinik aqutsisut mappip nassuiaataanik allanngortiterisinnaapput, mappimi suliniutinik nutaanik pilersitsillutik kiisalu mappini suliniutinik tamanik aqutsisutut pisinnaatitaaffeqartitaallutik. Suliniutinik peersisinnaanngillat, suliniutinullu mappiminniinngitsunut isersinnaanngillat. {projectManagementInfoCenterLink} atorlugu suliniutinik aqutsinermik pisinnaatitaaffiit pillugit paasissutissat amerlanerusut nanisinnaavatit.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Assingusumik na<PERSON>qanngilaq", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "qupperneq ikiorsiivik alakkaruk", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.addToFolder": "<PERSON><PERSON><PERSON><PERSON> il<PERSON>", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Allagaasivimmut inissinneqarpoq", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Mappi una peeruk", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.draftStatus": "Draft", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Mappimut uunga fiilinik ilanngussigit", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Fiilit 50 Mb-imit annertunerussanngillat. Fiilit ilanngussat mappillu quppernermi ersissapput.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Descriptions", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "Mappimi uani su<PERSON>uteqanngilaq Suliniutinik pilersitsillutillu ilanngussiniaruit quppernermut pingaarnermut \"Suliniutit\"-nut uterit.", "app.containers.AdminPage.FoldersEdit.folderName": "Folder name", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Header image", "app.containers.AdminPage.FoldersEdit.multilocError": "Oqaatsinut tamanut allaffissiat tamarmik immersorneqassapput.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Suliniutinik mappimut uunga ilanngussinnaasannik soqanngilaq.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Mappekortip assitaa", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "A<PERSON><PERSON>issutit", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Suliniutinut mappit", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Settings", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Suliniutit mappimut uunga ilanngunneqartut", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Suliniutit mappimut uunga il<PERSON>gus<PERSON>asatit", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Mappi una sorliunersoq toqqaruk \"allaqqitassiaq\", \"saqqummiussaq\" imaluunniit \"toqqorsivimmiittoq\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.save": "Toqqoruk", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "iluatsilluarpoq!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "<PERSON><PERSON><PERSON> na<PERSON><PERSON> nassuiarnera", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "iseqqa<PERSON><PERSON>mmi nui<PERSON>q", "app.containers.AdminPage.FoldersEdit.statusLabel": "Saqqummersinneqarner<PERSON> killiffia", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Suliniutit qanoq ataqatigiinnersut nassuiaruk, ilisarnaammik ersittumik nassuiaagit paasissutissanillu avitseqatiginnillutit.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Explain why the projects belong together, define a visual identity and share information.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Mappimik nutaamik pilersitsigit", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Settings", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "<PERSON><PERSON>", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "<PERSON><PERSON><PERSON> is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bannerip allannera tullua<PERSON>.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> banneri", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Save hero banner", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Nittartakkanni Atugassarititaasut allanngortikkit. Quppernerit allat, tassunga ilaallutik Una pillugu a<PERSON>ma Apeqqutit akulikitsumik apeqqutigineqartartut, fane-kkut {navigationLink} allanngortinneqarsinnaapput.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Nittartakkami politikkit", "app.containers.AdminPage.PagesEdition.privacy-policy": "Nammineq inuunermut politikki", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Atugassarititaasut", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Quppernermi suliniutinik allattorsimaffik", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Suliniutinut dashboardi", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Suliniutinik nutaanik pilersitsigit imaluunniit suliniutit pioreersut allanngortitikkit.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Suliniutit ", "app.containers.AdminPage.ProjectDashboard.published": "Published", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Indstillingspanel matujuk", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Ammut tulleri<PERSON>at ilusaat", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Iseqqaar<PERSON><PERSON>ut nassui<PERSON>t", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Iseq<PERSON><PERSON><PERSON><PERSON>i su<PERSON> kortimi er<PERSON>.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Suliniutip quppernerani ersissaaq Suliniut sumut tunnganersoq, atuisunnit suut naatsorsuutiginerlugit kiisalu ilinnit suut naatsorsuutigissaneraat ersarissumik nassuiaruk.", "app.containers.AdminPage.ProjectDescription.errorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.preview": "Preview", "app.containers.AdminPage.ProjectDescription.save": "Toqqoruk", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectDescription.saved": "Saved!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Qupperninniittunut suna oqa<PERSON>utigerusunnerlugu a<PERSON>jangeruk. Suliniutit allanngortiteruk, assinik, videonik, fiilinillu ilanngussanik amerlasuunik immerlugu,... Paasissutissat tamakku isersimasunut suliniutivit sumut tunnganera pillugu paasissutissiissapput.", "app.containers.AdminPage.ProjectDescription.titleDescription": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpace": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "Portussuseq", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "Annertuvoq", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Medium", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Annikippoq", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "<PERSON><PERSON><PERSON> assingata qeqqata allorniusaa sanimukartoq na<PERSON>soq -90 og 90-illu akornanniissaaq.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "<PERSON><PERSON><PERSON> assingata qeqqata allorniusaa tukimukartoq na<PERSON>inn<PERSON>soq -90 og 90-illu akornanniissaaq.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Nunap assingata quleriiaannera allanngortiguk", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Quleriiaaq allanngortiteruk", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectEdit.MapTab.here": "here", "app.containers.AdminPage.ProjectEdit.MapTab.import": "GeoJSON-<PERSON><PERSON> n<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Allorniusaq sanimukartoq na<PERSON>q", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Quleriaap qalipa<PERSON>a", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Quleriiaamiittut tamarmik imatut qalipaaserneqassapput. Qalipaatip uumma GeoJSON-fi<PERSON><PERSON> qalipaatit tamaasa qallissavai.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Tikkuutip il<PERSON>rnaataa", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Ilisarnaammik tikkuutini nuisasussamik toqqaagit. {url} tooruk ilisarnaatit toqqarsinnaasatit takuniarukkit.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Quleriiaap aqqa", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Quleriiaap uuma aqqa nunap assinganik nassuiaammi takune<PERSON>voq", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Qule<PERSON><PERSON> nassui<PERSON>t", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Una allataq, nunap assingani quleriaat atuuffiisa qulaanni tikkuut inissikkaangakku nassuiaatitut nuisassaaq", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Nunap assingani qule<PERSON>at", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Allorniusaq tukimu<PERSON>toq na<PERSON>inn<PERSON>soq", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "<PERSON><PERSON><PERSON> assingata qeqqa qani<PERSON> (zoom)", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "<PERSON><PERSON><PERSON> assingata nuisin<PERSON><PERSON><PERSON>, nunap assingani quleriiaanik ikkussineq ilusilersuinerlu aamma nunap assingata qeqqanik inissiineq qanillisaatillu qanissusaa ilanngullugit.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Nunap assinganik aaqqi<PERSON>q", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Save", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "support artikeli", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "<PERSON><PERSON><PERSON> assingani qanillis<PERSON>tip qaniss<PERSON>a", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "<PERSON><PERSON><PERSON> assingani qanillisaatip qanissusaa naling<PERSON>sumik atuuttoq. Kisitsisit 1-ip 17-illu <PERSON>, 1-imi <PERSON><PERSON><PERSON> (nunarsuaq tamarmi nuisalluni) 17-im<PERSON><PERSON> qani<PERSON> (blokkit illullu nuisallutik)", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Isumasiuinermut apeqqummik ilanngussigit", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Cancel", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Cancel", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Delete", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Delete", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Isumasiuinermi inernerit nuutikkit", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Toqqakkat amerlassusaat toqqagassat amerlassusaanniit amerlanerupput", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "A<PERSON><PERSON><PERSON><PERSON>qqagassalik", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "No options", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "Apeqqutit tamarmik akissutitut toqqagassartaqassapput", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Ataasiinnarmik toqqagassalik", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Isumasiuinermi a<PERSON>qa<PERSON>ut ataasiinnarmik toqqagassaqarput", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Uani isumasiuinermi apeqq<PERSON>t, peqataasut apeqqutinut tamanut akissutitut toqqagassaat inissitsiterlugit, peqataasut ataasiinnarmik akissutitut toqqagassaqarsinnaanersut (ataasiinnarmik toqqagassalik) imaluunniit arlalinnik akissutitut toqqagassaqarsinnaanersut (arlalinnik toqqagassalik) kissaateqarnerlutit aalajangerlugu kiisalu isumasiuinermi inernerit nuutiterlugit. Isumasiuinermi ataatsimi arlalinnik apeqqusiorsinnaavutit.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Save", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Ataasiinnarmik toqqagassalik", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Isumasiuinermi inissiissutit inernerillu", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Amerlanerpaaffik kukkuvoq", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "<PERSON><PERSON><PERSON><PERSON>iv<PERSON><PERSON><PERSON>t, tag-inik ilanngussigit imaluunniit ilanngussat suliniummi immikkoortup tullianut assilikkit.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Input manager", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Inernerit nuutikkit (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Suliniummi uani Typeformimik misissuinermi inernerit Excel-fiilinngorlugit uani aasinnaavatit.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Misissuinermi inernerit", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Survey", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Misissuinermi a<PERSON>sutit takukkit", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Are you sure?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Uani kaju<PERSON>sutsiminnik suleqataasunut suut piumasaqaataanersut kiisalu suut naatsorsuutigisinnaaneraat nassuiaruk.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Immersugassaq kukkuneqarmat toq<PERSON>orn<PERSON>qa<PERSON>gitsoorpoq.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Image", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Title", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Delete", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "<PERSON><PERSON><PERSON>, peqa<PERSON><PERSON><PERSON> kajumissutsiminnik nalunaarfigisinnaasaat.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Suliaq allanngortiteruk", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Add a description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Add a title", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Kajumissutsiminnik sulisut nuutikkit", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "A cause is an action or activity that participants can volunteer for.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Save", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Uani suliat peqataasut nalunaarfigisinnaasaat pilersissinnaavat kiisalu kajumissutsiminnik suliaqartussat aallugit.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Ka<PERSON>mis<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {no volunteers} one {# volunteer} other {# volunteers}}", "app.containers.AdminPage.ProjectEdit.addNewInput": "Add an input", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Suliniutit tag-itai", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Kinaassutsimik isertuussisitsilluni isumasiuineq", "app.containers.AdminPage.ProjectEdit.archived": "Archived", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Archived", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "This area cannot be deleted because it is being used to display projects on the following more custom page(s). You will need to unlink the area from the page, or delete the page before you can delete the area.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Immikkoortut tamarmik", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Suliniut immikkoortuni tamani takutinneqarpoq.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Suliassaqarf<PERSON>t", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Immikkoortut ikiorsiullugit suliniut nittartakkami filtererneqarsinnaapput. Immikkoortut uani {areasLabelTooltipLink} inississorneqarsinnaapput.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Immikkoortoq a<PERSON>q", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Suliniut suliassaqarfiit naapertorlugit filtreereerlugit nuissanngilaq.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Toq<PERSON>aneq", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Suliniut suliassaqarfinni toqqakkani immikkoortitani nuissaaq.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "<PERSON><PERSON>p assinga", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Suliniummik skabelonimeersumik pilersitsigit", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Create an external survey", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Misissuinermik nittartakkamiittumik pilersitsigit", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Nittartagarput qimanngikkaluarlugu misissuinermik pilersitsigit.", "app.containers.AdminPage.ProjectEdit.createPoll": "Isumasiuinermik pilersitsigit", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Apersuinermi immersugassamik arlalinnik toqqagassalimmik pilersitsigit.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Suliniutit qupperneranni pingaarnermi ilanngussat nuisanerisa nalinginnaasumik tulleriiaarneri inississorsinnaavatit.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Sorting", "app.containers.AdminPage.ProjectEdit.departments": "Immikkoortortaqarfiit", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Description", "app.containers.AdminPage.ProjectEdit.disabled": "Disabled", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Disabled", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Enabled", "app.containers.AdminPage.ProjectEdit.draft": "Draft", "app.containers.AdminPage.ProjectEdit.draftStatus": "Draft", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEdit.enabled": "Enabled", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Events", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Kakkiussinerit (annerpaamik 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Fiilit 50 Mb-imit annertunerussanngillat. <PERSON>ilit ilanngussat suliniummut paasissutissanut quppernermi takuneqarsinnaapput.", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Kajumissutsiminnik sulisunik ujarlerit", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Peqataasut sammisaqartitsinerni sulianiluunniit kajumissutsiminnik suleqataarusunnersut aperikkit.", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Mappimik suliniummik ilisiffigerusutannik toqqaagit.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Custom content", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Submissions to this form have started to come in. Changes to the form may result in data loss and incomplete data in the exported files.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Form successfully saved", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Survey end", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Skabelonimit", "app.containers.AdminPage.ProjectEdit.generalTab": "General", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Header image", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Inputit utertitsiviginninnerillu katitikkit", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "<PERSON>na <PERSON><PERSON><PERSON><PERSON><PERSON>liarinninnissamik a<PERSON>ua?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Suliniummi inputit nutaat tamarmik inummut uunga tunniunneqassapput. Tunineqartoq uani {ideaManagerLink} allanngortinneqarsinnaavoq.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Input form", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "input manager", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Input manager", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Ilanngussat nassiutilerput", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "<PERSON><PERSON>inn<PERSON><PERSON><PERSON><PERSON> is<PERSON>", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Inputit nuisinniarukkit nalinginnaasumik isikkuanik toqqa<PERSON>t: nunap assinga grid-itut nuisasoq imaluunniit nunap assingani kapuutit atorlugit. Peqataasut nammineerlutik isikkui marluk taakku taarsertarsinnaavaat.", "app.containers.AdminPage.ProjectEdit.limited": "Killeqarpoq", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Skabelonit amerlanerusut aakkit", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Map", "app.containers.AdminPage.ProjectEdit.mapTab": "Map", "app.containers.AdminPage.ProjectEdit.maximum": "Amerlanerpaaffik", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Peqataasut kooriminnik nassiussigaangamik missingiut qaangersinnaanngilaat.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Peqataasut koorimik nassiussissagunik missingiummik minnerpaaffimmik eqquutsitsinissaat piumasaqaatigiuk (minnerpaaffiliusserusunngikkuit '0' tooruk).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "visit our Help Center", "app.containers.AdminPage.ProjectEdit.moreDetails": "Immikkuualuttut amerlanerusut", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.newProject": "<PERSON><PERSON><PERSON>q", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Most recent", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> atorsinnaanngilaq", "app.containers.AdminPage.ProjectEdit.noFolder": "No folder", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Skabeloninik nassaartoqanngilaq", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Suliniutip taaguutissaanik allagit", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Kisitsit atorsinnaanngilaq", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Oldest", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Aqutsisunuin<PERSON>q <PERSON>q", "app.containers.AdminPage.ProjectEdit.optionNo": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.optionYes": "Aappi (mappemik toqqaagit)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Peqataanerup <PERSON>", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Sulererusuppit?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "<PERSON><PERSON><PERSON>t qanoq peqataasinna<PERSON>rs<PERSON> to<PERSON>.", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Iserfigisinnaasanut pisinnaatitaaffiit", "app.containers.AdminPage.ProjectEdit.pollTab": "Poll", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Sul<PERSON>um<PERSON>t kortip assiliartaa", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "This image is part of the project card; the card that summarizes the project and is shown on the homepage for example.\n\n    For more information on recommended image resolutions, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "This image is shown at the top of the project page.\n\n    For more information on recommended image resolutions, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Suliniummi aqutsisup suliniutit allanngortitersinnaavai, ilanngussat aaqqissuullugit kiisalu peqataasunut mailinik nassiussilluni. {moderationInfoCenterLink} atorlugu suliniutinik aqutsisutut pisinnaatitaaffiit pillugit paasissutissat amerlanerusut nanisinnaavatit.", "app.containers.AdminPage.ProjectEdit.projectName": "Suliniutip taaguutaa", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Suliassap <PERSON>", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Suliniutit piffissalius<PERSON><PERSON> ersarissumik aallartiffeqarlutillu naammassiffeqarput, assigiinngitsunillu killiffeqarsinnaasarlutik. Suliniutit piffissaliussartaqanngitsutit ingerlaavartuupput.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Suliniutip suussusaa kingusinnerusukkut allanngortissinnaanngilat.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Published", "app.containers.AdminPage.ProjectEdit.purposes": "Si<PERSON>rtaq", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Random", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Ajortoqarpoq. Inussiarnersumik misileeqqigit.", "app.containers.AdminPage.ProjectEdit.saveProject": "Toqqoruk", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Success!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Immersukkat toqq<PERSON>!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Skabeloninik ujaasigit", "app.containers.AdminPage.ProjectEdit.selectGroups": "Gruppi(t) toqqaruk(kkit)", "app.containers.AdminPage.ProjectEdit.shareInformation": "Paasissutissanik avitseqatiginnigit", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Snap Survey", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Suliniummik pilersitsigit ilinnullu tulluarsarlugu.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "visit our support center", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Add survey content", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Cancel", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# choices} one {# choice} other {# choices}})", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Linear scale", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Multiple choice - choose many", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "No survey responses yet", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Open for responses", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Optional", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Are you sure you want to leave?", "app.containers.AdminPage.ProjectEdit.survey.required2": "Required", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Multiple choice - choose one", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Survey successfully saved", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Survey", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Total {count} responses", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "View survey", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "URL embed-eruk", "app.containers.AdminPage.ProjectEdit.surveyService": "<PERSON><PERSON><PERSON><PERSON><PERSON>ineq", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Qanoq ilillutit misissuineq embed-ersinnaanerlugu uani {surveyServiceTooltipLink} paasisaqarnerusinnaavutit.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "This tag cannot be deleted because it is being used to display projects on the following more custom page(s). \nYou will need to unlink the tag from the page, or delete the page before you can delete the tag.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Suliniummut inissiissutit", "app.containers.AdminPage.ProjectEdit.titleLabel": "Title", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Qulequttamik naatsumik tiguaasinnaasumik ersarissumillu toqqaagit Q<PERSON>qutaq takussutissiami drop-downimi kiisalu iseqqaarfimmi suliniummut kortimi takuneqarsinnaavoq.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Tags", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Suliniummut {topicsCopy}-mik toqqa<PERSON>t. Atuisut suliniutinik filteriiniarlutik taakku atorsinnaavaat.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Missingersuutit tama<PERSON>", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Trending", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Unassigned", "app.containers.AdminPage.ProjectEdit.unlimited": "Killeqanngilaq", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Immersugassaq una atoruk\n", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "Suliniut nuisiguk", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Nammineq piumassutsimik sulineq", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# groups can view} one {# group can view} other {# groups can view}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Pisussamik ilanngussigit", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Aallartiffik", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Delete", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Ilumoortumik pisussaq una peerniarpiuk? Uterteqqinneqarsinnaanngilaq!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Pisussamik nass<PERSON>t", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Pisussaq allanngortiteruk", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Kakkiussinerit (annerpaamik 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "<PERSON><PERSON>t kakkiussat nassuiaatip ataani takune<PERSON>.", "app.containers.AdminPage.ProjectEvents.locationLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Pisussamik nutaamik pilersitsigit", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Toqqoruk", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "Allanng<PERSON><PERSON><PERSON><PERSON> to<PERSON>, mi<PERSON>leqqilaaruk.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Success!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Piffissami aggersumi pisussat suliniummut atassusikkit suliniummullu quppernermi nuisillugit.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON> ullullu", "app.containers.AdminPage.ProjectEvents.titleEvents": "Suliniummi pisussat", "app.containers.AdminPage.ProjectEvents.titleLabel": "Pisussap aqqa", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Immersugassat tamaasa matukkit", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Immersugassamut nassuiaat", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Edit input form", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Enabled", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Immersugassaq una ilannguguk.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Immersugassat tamaasa allisikkit", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Input form", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Paasissutissat suut ílanngunneqassanersut allaguk, peqataasut akissutissaannik siunnersorniarlugit naatsunik nassuiaasiorit ilitsersuusiorlutilluunniit kiisalu immersugassap immersornissaa nammineq toqqagassaanersoq imaluunniit piumasaqaataanersoq allaguk", "app.containers.AdminPage.ProjectIdeaForm.required": "Piumasaqaataavoq", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Immersugassap uuma immersorneqarnissaa piumasaqaatinngortiguk.", "app.containers.AdminPage.ProjectIdeaForm.save": "Toqqoruk", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Saved!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "View form", "app.containers.AdminPage.ProjectTimeline.datesLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Ilumoortumik killiffik una peerniarpiuk?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> nass<PERSON>t", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "End Date", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Kakkiussinerit (annerpaamik 50MB)", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Immersukkap nassi<PERSON> k<PERSON>, mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Saved!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Killiffiup aqqa", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartition", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Taaguutit (iseqqaarfimmi filteri)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Tag-it iseqqaar<PERSON>mmi filterimiittut qanoq atserne<PERSON>ppat? Ass. Tag-it, kate<PERSON>it, immikkoortortat, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Tag-it naleqqussarneqarsinnaapput {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "here", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Tag-imut ataats<PERSON> ta<PERSON> (ataasersiut)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tag", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Tag-inut arlalinnut ta<PERSON> (qasseersiut)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "tags", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "<PERSON><PERSON>i <PERSON>annguguk", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Feltimik nalunaarfissamik nutaamik ilanngussigit", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Toq<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "<PERSON><PERSON><PERSON><PERSON> il<PERSON> to<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Akissutitut toqqagassaq", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Oqaatsinut tamanut a<PERSON>git", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Akissutissatut toqqa<PERSON>sa<PERSON> toqq<PERSON>uk", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Akissutissatut toqqa<PERSON>sap toqqorn<PERSON> ilua<PERSON>ip<PERSON>q", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Answer choices", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Immersugassat", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "<PERSON><PERSON><PERSON>arsorneqarnermi immersugassat nikisinnerisigut qanoq tulleriiaassanersut a<PERSON>.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Nammineq allataq immersugassami nalu<PERSON><PERSON><PERSON><PERSON> feltip aqqata ataan<PERSON>.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Najugaqarfimmut akissutitut toqqagassat uani {geographicAreasTabLink} inissinneqarsinnaapput.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Akissutitut toqqagassaq allanngortiteruk", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Feltimut inissiissutit", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Aappi-naamik (krydsligassaq)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Date", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>ooq", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Arlalinnik toqqagassartalik (arlalinnik toq<PERSON>)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Nalinga kisitsisinngorlugu", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Arlalinn<PERSON> toq<PERSON>gas<PERSON>talik (ataatsimik toq<PERSON>)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Akis<PERSON>t naatsoq", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Nunap immikkoort<PERSON>ut qupperneq", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Hidden field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Feltip uuma immersornissaa pium<PERSON>gor<PERSON>uk?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Feltit naleqqussakkit", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Akissutitut toqqagassamik ilanngussigit", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Delete", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Piumasaqaataavoq", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "<PERSON><PERSON><PERSON> il<PERSON>", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "Kolonnit marluk", "app.containers.AdminPage.SettingsPage.addAreaButton": "Nunap immikkoortortaanik ilanngussigit", "app.containers.AdminPage.SettingsPage.addTopicButton": "Add tag", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Ilumoortumik nunap immikkoortua una peerniarpiuk?", "app.containers.AdminPage.SettingsPage.areaTerm": "Immikkoortumut ataatsimut ta<PERSON> (ataasersiut)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "immikkoortoq", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.areasTerm": "Immikkoortunut arlalinnut ta<PERSON> (qasseersiut)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "immikkoortut", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Select at least one language.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Avatars nittaruk", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Kiinnap assii takutikkit a<PERSON>ma nalu<PERSON>arsorneqaratik pulaarlutik peqataasut amerlassusii", "app.containers.AdminPage.SettingsPage.bannerHeader": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Isersimasunut nalu<PERSON>arsugaanngitsunut allataq headeri", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Isersimasunut nalu<PERSON>arsugaanngitsunut qulequtaq headerip ataaniittoq", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Qanoq isikko<PERSON>rnersoq takuuk", "app.containers.AdminPage.SettingsPage.brandingDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> qali<PERSON> to<PERSON>.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Quppernermik ni<PERSON>rsa<PERSON>q", "app.containers.AdminPage.SettingsPage.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.chooseLayout": "Ilusaa", "app.containers.AdminPage.SettingsPage.color_primary": "Qalipaat pinga<PERSON>neq", "app.containers.AdminPage.SettingsPage.color_secondary": "Q<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.color_text": "Allatap qalipaataa", "app.containers.AdminPage.SettingsPage.colorsTitle": "Q<PERSON>paati<PERSON>", "app.containers.AdminPage.SettingsPage.confirmHeader": "Are you sure you want to delete this tag?", "app.containers.AdminPage.SettingsPage.contentModeration": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.ctaHeader": "Attataasat", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Quppernermi headeri nammineq aaqqissuunneqa<PERSON> | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Custom", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Linkimut attataasaq", "app.containers.AdminPage.SettingsPage.defaultTopic": "Tag nalinginnaasoq", "app.containers.AdminPage.SettingsPage.delete": "Delete", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Tag-i ilanngussanik tamanit peerne<PERSON>aq. Allannguut suliniutinut tamanut atuuti<PERSON>aq.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Tagginik qupperninni ilanngussanik kategoriseeriiniarlutit atorusutannik ilanngussillutillu piiaagit. {adminProjectsLink}-imi suliniuti<PERSON> aalajangersimasunut tag-inik ilanngussisinnaavutit.", "app.containers.AdminPage.SettingsPage.desktop": "Desktop", "app.containers.AdminPage.SettingsPage.editFormTitle": "Immikkoortoq allanngortiteruk", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Tag allanngortiteruk", "app.containers.AdminPage.SettingsPage.fieldDescription": "Immikkoortumut nassuiaat", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Nass<PERSON><PERSON>t taanna taamaallaat su<PERSON><PERSON>mi sule<PERSON>ti<PERSON>t atuga<PERSON>aavoq, atuisunuunngitsoq.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Immik<PERSON>ortup aqqa", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Oqaatsinut tamanut sumiiffimmik atsiigit", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Aqqit immikkoortunut atukkatit, feltitut nalunaarfissatut kiisalu suliniutit iseqqaarfimmi filterernissaannut atorneqarsinnaapput.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Tag toqqoruk", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Tag name", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Oqaatsinut tamanut tag-imik at<PERSON>t", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Aqqit tag-inut atukkatit quppernermi atuisunit tamanit takuneqa<PERSON>innaassapput", "app.containers.AdminPage.SettingsPage.fixedRatio": "Fixed ratio", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "This banner type works best with images that shouldn’t be cropped, such as images with text, a logo or specific elements that are crucial to your citizens. This banner is replaced with a solid box in the primary colour when users are signed in. You can set this colour in the general settings. More info on the recommended image usage can be found on our {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Banner-it silis<PERSON><PERSON><PERSON> tama<PERSON>ut", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "This banner stretches over the full width for a great visual effect. The image will try to cover as much space as possible, causing it to not always be visible at all times. You can combine this banner with an overlay of any colour. More info on the recommended image usage can be found on our {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> banneri", "app.containers.AdminPage.SettingsPage.headerDescription": "<PERSON><PERSON><PERSON> is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bannerip allannera tullua<PERSON>.", "app.containers.AdminPage.SettingsPage.header_bg": "Bannerip-assitaa", "app.containers.AdminPage.SettingsPage.helmetDescription": "Qupperneq aqutsisunut inissiissutinik imalik", "app.containers.AdminPage.SettingsPage.helmetTitle": "Admin settings page", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataani nammineq imarisanik ikkussigit.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Iseqqaarfimmi headeri | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Billed<PERSON>lter farve", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Billedgennemsigtighed", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Imarisanik naapertuutinngitsunik ujaasigit", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Imarisat naapertuutinngitsut quppernermut ilanngunneqartut automatiskimik paasisakkit.", "app.containers.AdminPage.SettingsPage.languages": "Oqaatsit", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Oqaatsinik arlalinnik assigiinngitsunik, atuisunnut neqeroorutigerusutannik toqqaasinnaavutit. Oqaatsit toqqakkavit tamarmik imassaannik immikkut sanassaatit.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Activity", "app.containers.AdminPage.SettingsPage.logo": "Ilisarnaat", "app.containers.AdminPage.SettingsPage.noHeader": "Bannerip assitas<PERSON>anik ikkussilaarit (1440 x 480 px)", "app.containers.AdminPage.SettingsPage.no_button": "Attataasaqanngilaq", "app.containers.AdminPage.SettingsPage.organizationName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> inger<PERSON>tigiiffiulluunniit aqqa", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Provide an organization name or city for all languages.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Enable overlay", "app.containers.AdminPage.SettingsPage.phone": "Oqarasuaat", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Quppernermik na<PERSON>q<PERSON>aaneq", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Profanity blocker", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "<PERSON><PERSON><PERSON><PERSON>, siunnersuutit oqaaseqaatillu oqaasipilunnik nalunaarutigineqarajunnerpaanik imallit blokerikkit", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Allataq una iseqqaarfimmi suliniutit qulaanni nuisatinneqassaaq.", "app.containers.AdminPage.SettingsPage.projectsSettings": "sulinium<PERSON>t inissiissutit", "app.containers.AdminPage.SettingsPage.projects_header": "<PERSON><PERSON><PERSON><PERSON><PERSON> banneri", "app.containers.AdminPage.SettingsPage.registrationFields": "Feltit nalunaarfissat", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Ilanngunnissamut immersuiffissanni qullermiittumi naatsumik nassuiaasiorit.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Na<PERSON><PERSON>arsuineq", "app.containers.AdminPage.SettingsPage.save": "Toqqoruk", "app.containers.AdminPage.SettingsPage.saveArea": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Success!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Couldn't save. Try changing the setting again.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Nalunaarsorit\"", "app.containers.AdminPage.SettingsPage.signed_in": "Alakkarterisunut nalu<PERSON>arsorsima<PERSON>ut attataasaq", "app.containers.AdminPage.SettingsPage.signed_out": "Alakkarterisunut nalunaarsorsimanngitsunut attataasaq", "app.containers.AdminPage.SettingsPage.signupFormText": "Allataq nalu<PERSON>ars<PERSON>t ikiuut", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Na<PERSON>naarsornissamut immersugassami nassuiaammik naatsumik ilanngussigit.", "app.containers.AdminPage.SettingsPage.step1": "<PERSON><PERSON><PERSON> mailimik isissuti<PERSON><PERSON><PERSON> all<PERSON>", "app.containers.AdminPage.SettingsPage.step1Tooltip": "<PERSON>anna nalu<PERSON>mut immersuga<PERSON><PERSON> quppernerani siullermi qullerpaajulluni nuisassaaq (ateq, e-mail, isissutissaq).", "app.containers.AdminPage.SettingsPage.step2": "Killiffik nalunaarsornermi apeqqutinik allaffissaq", "app.containers.AdminPage.SettingsPage.step2Tooltip": "<PERSON>anna nalu<PERSON>ornissamut immersuga<PERSON><PERSON> quppernerani tullermi qullerpaajulluni nuisassaaq (nalunaarsornissamut feltit allat).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Nunami immikkoortunik qupperninni atorusutannik nassuiaagit, soorlu illoqarfiup immikkoortui nunalluunniit immikkoortui. Nunami immikkoortut taakku suliniutinut atassusersinnaavatit (quppernermi iserfissamiittartumi filtererneqarsinnaapput) imaluunniit peqataasut smart gruppiliorniarunik kiisalu iserfigisinnaasanut pisinnaatitaaffiit nassuiarniarunikkit immikkoortoq najugaqarfigisartik feltitut nalunaarfissatut atussagaat qinnuigalugit.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Inuit ingerlatseqatigiiffivit aqqa qanoq takus<PERSON>raat a<PERSON>, qupperninni oqaatsit toqqakkit nittartakkannullu linkiliorlutit.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Oqaasertaliussaq naqinnernik atorneqarsinnaasunik qaangiivoq (naqinnerit 90-it)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Inuit nalunaaraangamik paasissutissanik sunik tunniussinissaannik qinnuigineqartassanersut allagit.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Settings updated successfully.", "app.containers.AdminPage.SettingsPage.tabPolicies": "Politikkit", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registration", "app.containers.AdminPage.SettingsPage.tabSettings": "<PERSON><PERSON>inn<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Nunami immikkoortoq suna suliniutinni atorniarnerlugu nassui<PERSON>k (ass. illoqa<PERSON><PERSON><PERSON> immikkoortui, nunap immikkoortui il.il.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geographic areas", "app.containers.AdminPage.SettingsPage.titleBasic": "Inissiissutit", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Qulequtarititaq naqinnernik atorneqarsinnaasunik qaangiivoq (naqinnerit 35-it)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Tag manager", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "This banner is in particular useful with images that don’t work well with text from the title, subtitle or button. These items will be pushed below the banner. More info on the recommended image usage can be found on our {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.twoRowLayout": "Sanileriit marluk", "app.containers.AdminPage.SettingsPage.urlError": "URL atorsinnaanngilaq", "app.containers.AdminPage.SettingsPage.urlPatternError": "Enter a valid URL.", "app.containers.AdminPage.SettingsPage.urlTitle": "Nittartagaq", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Nittartakkannut linkimik ilanngussisinnaavutit. Linki iseqqaarfimmi allerpaami at<PERSON>.", "app.containers.AdminPage.SideBar.dashboard": "Dashboard", "app.containers.AdminPage.SideBar.emails": "Emailit", "app.containers.AdminPage.SideBar.groups": "Groups", "app.containers.AdminPage.SideBar.guide": "Ilitsersuut", "app.containers.AdminPage.SideBar.inputManager": "Input manager", "app.containers.AdminPage.SideBar.insights": "Nalunaarusiorneq", "app.containers.AdminPage.SideBar.menu": "Pages & menu", "app.containers.AdminPage.SideBar.messaging": "Messaging", "app.containers.AdminPage.SideBar.moderation": "Activity", "app.containers.AdminPage.SideBar.processing": "Suliarineqarpoq", "app.containers.AdminPage.SideBar.projects": "Suliniutit ", "app.containers.AdminPage.SideBar.settings": "Settings", "app.containers.AdminPage.SideBar.users": "Users", "app.containers.AdminPage.SideBar.workshops": "Workshoppit", "app.containers.AdminPage.Topics.addTopics": "Ilannguguk", "app.containers.AdminPage.Topics.browseTopics": "Tag-it misissuataakkit", "app.containers.AdminPage.Topics.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.confirmHeader": "Ilumoortumik tag-i una peerniarpiuk?", "app.containers.AdminPage.Topics.delete": "Delete", "app.containers.AdminPage.Topics.deleteTopicLabel": "Delete", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Tag-i una suliniummi ilanngussanut nutaanut ilanngun<PERSON>rsinnaajun<PERSON>ar<PERSON>.", "app.containers.AdminPage.Topics.inputForm": "Input form", "app.containers.AdminPage.Topics.lastTopicWarning": "Minnerpaamik ataatsimik tag-eqarnissaa piumasaqaataavoq. Tag-it atorusunngikkukkit quppernermi uani {ideaFormLink} atorunnaarsinneqarsinnaapput.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Tag-it suliniummi uani ilann<PERSON>ut atassuserneqarsinnaa<PERSON>t ilanngullugillu piiarsinnaavatit.", "app.containers.AdminPage.Topics.remove": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.title": "Project tags", "app.containers.AdminPage.Topics.topicManager": "Tag manager", "app.containers.AdminPage.Topics.topicManagerInfo": "Suliniummut tag-it amerlanerusut ilanngukkusukkukkit uani {topicManagerLink} taamaaliorsinnaavutit.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Gruppimik nutaamik ilanngussigit", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Gruppip aqqa", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Gruppi atseruk", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Gruppimik nammineq aaqqissukkamik pilersitsigit", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Gruppi qanoq ittoq pisa<PERSON>?", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Gruppi <PERSON>", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Create a manual group", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Create a smart group", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Gruppit pillugit paasisaqarnerugit", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Atuisut ta<PERSON>sutissiami toqqarsinnaavatit gruppimullu uunga ilanngullugit.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Piumasaqaatit nassuiarsinnaavatit, atuisullu piumasaqaatinik taakkuninnga eqquutsitsisut gruppimut tassunga automatiskimik ilanngunneqassapput.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Gruppi nammineq a<PERSON>", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Smart gruppi", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Gruppi una inuttaqanngilaq", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Atuisunik nammineerlutit ilanngussiniaruit {allUsersLink} iserfigiuk.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Atuisumik(nik) ujakkannut assingusumik(nik) soqanngilaq", "app.containers.AdminPage.Users.GroupsPanel.select": "Toqqaruk", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Ilumoortumik gruppi una peerniarpiuk?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Atuisut gruppinut ilanngun<PERSON>ini a<PERSON>, mi<PERSON>leqqilaaruk.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Gruppimiit <PERSON>", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "At<PERSON><PERSON>t toq<PERSON>t gruppimiit peerniarpigit?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Atuisut gruppimiit peer<PERSON><PERSON><PERSON>, mi<PERSON>leqqilaaruk.", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Ilannguguk", "app.containers.AdminPage.groups.permissions.add": "Add", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Delete", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Delete", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Ilumoortumik gruppi una suliniummit peerniarpiuk?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Gruppimik ataatsimik arlalinnilluunniit to<PERSON>t", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {No members} one {1 member} other {{count} members}}", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Are you sure?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Suliniummi aqutsisut na<PERSON>ari<PERSON>qanngilat", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "<PERSON><PERSON>soq suliniummi uani il<PERSON>qa<PERSON>ngim<PERSON> nui<PERSON>.", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Qaaqqus<PERSON>ut uta<PERSON>q", "app.containers.AdminPage.groups.permissions.save": "Save", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Success!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.projects.all.existingProjects": "Suliniutit pioreersut", "app.containers.AdminPage.projects.all.projectsAndFolders": "Suliniutit mappillu", "app.containers.AdminPage.widgets.copied": "Clipboardimut assilineqarpoq", "app.containers.AdminPage.widgets.copyToClipboard": "Kode una assiliuk", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "HTML-kode assiliuk", "app.containers.AdminPage.widgets.fieldAccentColor": "Qalipaat illuatungerisaq", "app.containers.AdminPage.widgets.fieldBackgroundColor": "<PERSON><PERSON><PERSON><PERSON>t tunuliaquttatut qali<PERSON>aq", "app.containers.AdminPage.widgets.fieldButtonText": "Button text", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldFont": "Naqinnerit ilusaat", "app.containers.AdminPage.widgets.fieldFontDescription": "{googleFontsLink}-imit naqinnerit ilusaat pioreersoq atorn<PERSON>.", "app.containers.AdminPage.widgets.fieldFontSize": "Naqinnerit angissusaat (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Oqaaseqarsinnaati<PERSON>", "app.containers.AdminPage.widgets.fieldHeaderText": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Peqa<PERSON>atits<PERSON><PERSON><PERSON> quppernerput", "app.containers.AdminPage.widgets.fieldHeight": "Portussuseq (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Ilanngussat amerlassusaat", "app.containers.AdminPage.widgets.fieldProjects": "Suliniutit ", "app.containers.AdminPage.widgets.fieldRelativeLink": "<PERSON><PERSON><PERSON> linki", "app.containers.AdminPage.widgets.fieldShowFooter": "Tuugassaq nui<PERSON>guk", "app.containers.AdminPage.widgets.fieldShowHeader": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldShowLogo": "Ilisarnaat nuisiguk", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>t tunuliaquttatut qali<PERSON>aq", "app.containers.AdminPage.widgets.fieldSort": "Una malillugu immikkoortiterneqarpoq", "app.containers.AdminPage.widgets.fieldTextColor": "Text color", "app.containers.AdminPage.widgets.fieldTopics": "Pineqartut", "app.containers.AdminPage.widgets.fieldWidth": "Silissusaa", "app.containers.AdminPage.widgets.homepage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.htmlCodeExplanation": "HTML-kode una assilisinnaavat nittartakkannilu widgetiliiffigerusutanni inissillugu.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Widget HTML kode", "app.containers.AdminPage.widgets.previewTitle": "Preview", "app.containers.AdminPage.widgets.settingsTitle": "Settings", "app.containers.AdminPage.widgets.sortNewest": "Most recent", "app.containers.AdminPage.widgets.sortPopular": "Most voted", "app.containers.AdminPage.widgets.sortTrending": "Trending", "app.containers.AdminPage.widgets.subtitleWidgets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, naleqqussariarlugulu quppernermi inunnut ikiuutissatut nittartakkannut il<PERSON>ugu.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Angissutsit", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Header & Footer", "app.containers.AdminPage.widgets.titleInputSelection": "Input<PERSON><PERSON>", "app.containers.AdminPage.widgets.titleStyle": "Iluseq", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Save", "app.containers.admin.ContentBuilder.delete": "Delete", "app.containers.admin.ContentBuilder.error": "error", "app.containers.admin.ContentBuilder.errorMessage": "Uuma iluani {locale} imai k<PERSON>. Allannguutitit toqqorumallugit a<PERSON>qiguk", "app.containers.admin.ContentBuilder.threeColumnLayout": "Ammut tulleri<PERSON>at pingasut", "app.containers.admin.ContentBuilder.twoColumnLayout": "Ammut tulleri<PERSON>at marluk", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "Ammut tulleriiaat marluk 30%-imik 60%-imillu silis<PERSON>it", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "Ammut tulleriiaat marluk 60%-imik 30%-imillu silis<PERSON>it", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "Ammut tulleriiaat marluk naligiit", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Show more", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Title", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Participants timeline", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Chart", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Date range", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Title", "app.containers.admin.ReportBuilder.charts.noData": "There is no data available for the filters you have selected.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Traffic sources", "app.containers.admin.ReportBuilder.charts.usersByAge": "Users by age", "app.containers.admin.ReportBuilder.charts.usersByGender": "Users by gender", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Visitor timeline", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Delete", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Edit", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Statusi allanngortiteruk", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Statusit massakkut peqataasut inputiinut atassuserneqarnikut peerneqarsinnaanngillat. Quppernermi {manageTab}-imi statusit, inputinit pioreersunit peersinnaavatit/allanngortissinnaavatit.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Una statusi peerneqarsinnaan<PERSON> nuunneqarsinnaanngilaq.", "app.containers.admin.ideaStatuses.all.manage": "Manage", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Atuutilersitsinissamut imaluunniit ingerlariaqqinnissa<PERSON>t to<PERSON>q", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Akuerineqarpoq", "app.containers.admin.ideaStatuses.form.category": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.categoryDescription": "Kategoriimik statusinnut tulluarnerpaamik toqqaagit <PERSON>rpit misissuinermut atortorput ilanngussanik pitsaanerusumik suliarinnissinnaanissaanik misissuisinnaanissaanillu i<PERSON>a.", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "Other", "app.containers.admin.ideaStatuses.form.fieldColor": "Qalipaat", "app.containers.admin.ideaStatuses.form.fieldDescription": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Oqaatsinut tamanut statusimut nassuiaasio<PERSON>", "app.containers.admin.ideaStatuses.form.fieldTitle": "<PERSON><PERSON> a<PERSON>", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Oqaatsinut tamanut statusimik atsiigit", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Atuutilersinneqarpoq", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Atuutilerpoq", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Atorunnaarsinneqartut imaluunniit suliareqqitassatut toqqa<PERSON>t", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Toqqarneqanngilaq", "app.containers.admin.ideaStatuses.form.saveStatus": "Save status", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Atuutilersitassatut ingerlariaqqinnermulluunnit eqqarsaatersuutigineqartoq", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Isumaliutersuutigineqarpoq", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Takuneqartoq sulili suliarineqanngitsoq", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Takoriigaq", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Inputit taakkulu statusitaat aaqqissuutikkit.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Input manager | {orgName}-ip peqa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> quppernera", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "U<PERSON>titsiv<PERSON><PERSON><PERSON>t, tag-inik ilanngussigit imaluunniit suliniummiit suliniummut allamut inputimik nuussigit", "app.containers.admin.import.importInputs": "Import inputs", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Anyone", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Selection", "app.containers.admin.project.permissions.viewingRightsTitle": "<PERSON><PERSON><PERSON>t suliniut una ta<PERSON>?", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Participation rate", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Total participants", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Automated campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automated emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "From {quantity} campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Custom campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Custom emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "Emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Total emails sent", "app.modules.commercial.analytics.admin.components.Events.completed": "Completed", "app.modules.commercial.analytics.admin.components.Events.events": "Events", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Total events added", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Upcoming", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Accepted", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Invitations", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Pending", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Total invites sent", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Go to Input Manager", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Inputs", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Active", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Projects that are not archived and visible on the 'Active' table on the home page", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Archived", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Draft projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Finished", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "All archived projects and active timeline projects that have finished are counted here", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Total projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "The number of projects that are visible on the platform", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "New registrations", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Registration rate", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registrations", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Total registrations", "app.modules.commercial.analytics.admin.components.Tab": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Last 30 days:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Last 7 days:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Pageviews per visit", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Visit duration", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Visitors\" is the number of unique visitors. If a person visits the platform multiple times, they are only counted once.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Visits", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Visits\" is the number of sessions. If a person visited the platform multiple times, each visit is counted.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Yesterday:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Language", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Number of visitors", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Percentage of visitors", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "click here", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Referrers", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "To view the full list of referrers, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Traffic sources", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Visits", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Email deliveries over time", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Registrations over time", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Date", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistic", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Overall statistics", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Visits and visitors over time", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Total over period", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Count", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Language", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Campaigns", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Direct entry", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Percentage of visits", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Search engines", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Social networks", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Traffic source", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Number of visits", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Websites", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Erfalasoq imarisanut nakkutilliinermut atortoq una elementip uumap toqqarneratigut toortaatillu <PERSON>uk-ip qullerpaamiittup toorneratigut peersinnaavat. Kingunerani quppernerni Ta<PERSON>reerpo<PERSON>-mi imaluunniit <PERSON>laq-mi nueqqissaaq", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Imarisanik naapertuutinngitsunik nassaartoqarpoq.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Ilanngussanik misissugassatut nalunaaruteqartoqanngilaq imaluunniit Natural Language Processing-systemitsinnit imarisatut naapertuutinngitsutut nalunaaqutsigaqarani", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Peeruk {numberOfItems, plural, one {content warning} other {# content warnings}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Atuisumit naapertuutinngitsut nalunaarutigineqarpoq.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "<PERSON><PERSON><PERSON><PERSON> pillugit mi<PERSON>", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Report builder", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Quppernerit illit iserfigisinnaasanni ta<PERSON>inn<PERSON>it", "app.modules.navbar.admin.containers.createCustomPageButton": "Create custom page", "app.modules.navbar.admin.containers.deletePageConfirmation": "Ilumut qupperneq una peerniarpiuk? Uterteqqissinnaanavianngilaq. Peernissaanut piareersimanngikkuit qupperneq navigationslinjemiit peersinnaavat.", "app.modules.navbar.admin.containers.pageHeader": "Pages & menu", "app.modules.navbar.admin.containers.pageSubtitle": "Iserfigisinnaasannik toqqaavissartalik Angerlarit aamma Suliniutit saniatigut tallimat tikillugit saqqummertinneqarsinnaapput. Toqqagassani taaguutit allanngortissinnaavatit nuussinnaallugillu aamma nammineq imaritikkusutannik quppernernik ikkussisinnaavutit.", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "visit our support center", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "For more information on recommended image resolutions, {supportPageLink}."}
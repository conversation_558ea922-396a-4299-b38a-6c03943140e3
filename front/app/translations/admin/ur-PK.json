{"UI.FormComponents.required": "مطل<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.action": "ایکشن", "app.Admin.ManagementFeed.after": "<PERSON><PERSON> بعد", "app.Admin.ManagementFeed.before": "اس سے پہلے", "app.Admin.ManagementFeed.changed": "ترمیم شدہ", "app.Admin.ManagementFeed.created": "بنایا", "app.Admin.ManagementFeed.date": "تاریخ", "app.Admin.ManagementFeed.deleted": "حذف کر دیا گیا۔", "app.Admin.ManagementFeed.folder": "فولڈر", "app.Admin.ManagementFeed.idea": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.in": "پروجیکٹ {project}میں", "app.Admin.ManagementFeed.item": "آئٹم", "app.Admin.ManagementFeed.key": "چابی", "app.Admin.ManagementFeed.managementFeedNudge": "مینجمنٹ فیڈ تک رسائی آپ کے موجودہ لائسنس میں شامل نہیں ہے۔ اس کے بارے میں مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.Admin.ManagementFeed.noActivityFound": "کوئی سرگرمی نہیں ملی", "app.Admin.ManagementFeed.phase": "مر<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.project": "پروجیکٹ", "app.Admin.ManagementFeed.projectReviewApproved": "پروجیکٹ کی منظوری دی گئی۔", "app.Admin.ManagementFeed.projectReviewRequested": "پروجیکٹ کا جائزہ لینے کی درخواست کی گئی۔", "app.Admin.ManagementFeed.title": "مینجمنٹ فیڈ", "app.Admin.ManagementFeed.user": "صارف", "app.Admin.ManagementFeed.userPlaceholder": "ایک صارف منتخب کریں۔", "app.Admin.ManagementFeed.value": "قدر", "app.Admin.ManagementFeed.viewDetails": "تفصیلات دیکھیں", "app.Admin.ManagementFeed.warning": "تجرباتی خصوصیت: پچھلے 30 دنوں میں منتظمین یا مینیجرز کے ذریعہ انجام دیے گئے منتخب اعمال کی ایک کم سے کم فہرست۔ تمام اعمال شامل نہیں ہیں۔", "app.Admin.Moderation.managementFeed": "مینجمنٹ فیڈ", "app.Admin.Moderation.participationFeed": "شرکت فیڈ", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "کیا آپ کو یقین ہے؟", "app.components.Admin.Campaigns.clicked": "کلک کیا۔", "app.components.Admin.Campaigns.deleteCampaignButton": "مہم کو حذف کریں۔", "app.components.Admin.Campaigns.deliveryStatus_accepted": "قبول کر لیا", "app.components.Admin.Campaigns.deliveryStatus_bounced": "اچھال دیا", "app.components.Admin.Campaigns.deliveryStatus_clicked": "کلک کیا۔", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "اس سے پتہ چلتا ہے کہ کتنے وصول کنندگان نے ای میل میں ایک لنک پر کلک کیا۔ براہ کرم نوٹ کریں کہ کچھ سیکیورٹی سسٹم لنکس کو اسکین کرنے کے لیے خود بخود پیروی کر سکتے ہیں، جس کے نتیجے میں غلط کلکس ہو سکتے ہیں۔", "app.components.Admin.Campaigns.deliveryStatus_delivered": "پہنچایا", "app.components.Admin.Campaigns.deliveryStatus_failed": "ناکام", "app.components.Admin.Campaigns.deliveryStatus_opened": "کھول دیا", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "اس سے پتہ چلتا ہے کہ کتنے وصول کنندگان نے ای میل کھولی۔ براہ کرم نوٹ کریں کہ کچھ سیکیورٹی سسٹمز (جیسے Microsoft Defender) اسکیننگ کے لیے مواد کو پہلے سے لوڈ کر سکتے ہیں، جس کے نتیجے میں غلط اوپن ہو سکتے ہیں۔", "app.components.Admin.Campaigns.deliveryStatus_sent": "بھیجا", "app.components.Admin.Campaigns.draft": "مسودہ", "app.components.Admin.Campaigns.from": "سے", "app.components.Admin.Campaigns.manageButtonLabel": "انتظام کریں۔", "app.components.Admin.Campaigns.opened": "کھول دیا", "app.components.Admin.Campaigns.project": "پروجیکٹ", "app.components.Admin.Campaigns.recipientsTitle": "وصول کنندگان", "app.components.Admin.Campaigns.reply_to": "کو جواب دیں۔", "app.components.Admin.Campaigns.sent": "بھیجا", "app.components.Admin.Campaigns.statsButton": "شماریات", "app.components.Admin.Campaigns.subject": "موضوع", "app.components.Admin.Campaigns.to": "کو", "app.components.Admin.ImageCropper.cropFinalSentence": "یہ بھی دیکھیں: {link}۔", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "کلیدی مواد کو نقطے والی لائنوں کے اندر رکھیں تاکہ یہ یقینی بنایا جا سکے کہ یہ ہمیشہ نظر آتا ہے۔", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "موبائل پر 3:1 (صرف نقطے والی لائنوں کے درمیان کا علاقہ دکھایا گیا ہے)", "app.components.Admin.ImageCropper.cropSentenceOne": "تصویر خود بخود کٹ جاتی ہے:", "app.components.Admin.ImageCropper.cropSentenceTwo": "ڈیسک ٹاپ پر {aspect} (مکمل چوڑائی دکھائی گئی)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.cizenlab.co/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "تجویز کردہ تناسب", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "رجسٹرڈ صارفین کے لیے ہیڈر کا متن", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "انتباہ: آپ نے جو رنگ منتخب کیا ہے اس میں کافی زیادہ تضاد نہیں ہے۔ اس کے نتیجے میں ایسا متن ہو سکتا ہے جسے پڑھنا مشکل ہو۔ پڑھنے کی اہلیت کو بہتر بنانے کے لیے گہرا رنگ منتخب کریں۔", "app.components.AdminPage.SettingsPage.eventsPageSetting": "نیویگیشن بار میں ایونٹس شامل کریں۔", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "فعال ہونے پر، تمام پروجیکٹ ایونٹس کا لنک نیویگیشن بار میں شامل کر دیا جائے گا۔", "app.components.AdminPage.SettingsPage.eventsSection": "واقعات", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "ہوم پیج حسب ضرورت سیکشن", "app.components.AnonymousPostingToggle.userAnonymity": "صارف کا نام ظاہر نہ کرنا", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "صارفین دوسرے صارفین، پروجیکٹ مینیجرز اور منتظمین سے اپنی شناخت چھپا سکیں گے۔ ان شراکتوں کو اب بھی معتدل کیا جا سکتا ہے۔", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "صارفین کو گمنامی میں حصہ لینے کی اجازت دیں۔", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "صارفین اب بھی اپنے اصلی نام کے ساتھ حصہ لینے کا انتخاب کر سکتے ہیں، لیکن اگر وہ ایسا کرنے کا انتخاب کرتے ہیں تو ان کے پاس گمنام طور پر شراکت جمع کرانے کا اختیار ہوگا۔ تمام صارفین کو اب بھی رسائی کے حقوق کے ٹیب میں مقرر کردہ تقاضوں کی تعمیل کرنے کی ضرورت ہوگی تاکہ ان کی شراکتیں گزر سکیں۔ شرکت کے ڈیٹا ایکسپورٹ پر صارف کا پروفائل ڈیٹا دستیاب نہیں ہوگا۔", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "ہمارے {supportArticle}میں صارف کی گمنامی کے بارے میں مزید جانیں۔", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "سپورٹ مضمون", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "ایک بار اضافی سیٹیں شامل ہونے کے بعد، آپ کی بلنگ میں اضافہ ہو جائے گا۔ اس کے بارے میں مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "سروے مکمل کرنے کے لیے آپ کا شکریہ! اگلی سہ ماہی میں اسے دوبارہ لینے کے لیے آپ کا استقبال ہے۔", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "پی ڈی ایف کے طور پر ڈاؤن لوڈ کریں۔", "app.components.FormSync.downloadExcelTemplate": "ایکسل ٹیمپلیٹ ڈاؤن لوڈ کریں۔", "app.components.FormSync.downloadExcelTemplateTooltip2": "ایکسل ٹیمپلیٹس میں درجہ بندی کے سوالات، میٹرکس کے سوالات، فائل اپ لوڈ کے سوالات اور کوئی بھی میپنگ ان پٹ سوالات (ڈراپ پن، ڈرا روٹ، ڈرا ایریا، ESRI فائل اپ لوڈ) شامل نہیں ہوں گے کیونکہ یہ اس وقت بلک امپورٹنگ کے لیے تعاون یافتہ نہیں ہیں۔", "app.components.ProjectTemplatePreview.close": "بند", "app.components.ProjectTemplatePreview.createProject": "پروجیکٹ بنائیں", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "ٹیمپلیٹ ''{templateTitle}'' کی بنیاد پر ایک پروجیکٹ بنائیں", "app.components.ProjectTemplatePreview.goBack": "واپس جاؤ", "app.components.ProjectTemplatePreview.goBackTo": "{goBackLink}پر واپس جائیں۔", "app.components.ProjectTemplatePreview.govocalExpert": "آواز کے ماہر پر جائیں۔", "app.components.ProjectTemplatePreview.infoboxLine1": "کیا آپ اس ٹیمپلیٹ کو اپنی شرکت کے منصوبے کے لیے استعمال کرنا چاہتے ہیں؟", "app.components.ProjectTemplatePreview.infoboxLine2": "اپنے شہر کی انتظامیہ کے ذمہ دار شخص سے رابطہ کریں، یا {link}سے رابطہ کریں۔", "app.components.ProjectTemplatePreview.projectFolder": "پروجیکٹ فولڈر", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "منتخب کردہ تاریخ غلط ہے۔ براہ کرم درج ذیل فارمیٹ میں تاریخ فراہم کریں: YYYY-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "براہ کرم پروجیکٹ کے لیے آغاز کی تاریخ منتخب کریں۔", "app.components.ProjectTemplatePreview.projectStartDate": "آپ کے پروجیکٹ کی شروعات کی تاریخ", "app.components.ProjectTemplatePreview.projectTitle": "آپ کے پروجیکٹ کا عنوان", "app.components.ProjectTemplatePreview.projectTitleError": "براہ کرم پروجیکٹ کا عنوان ٹائپ کریں۔", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "براہ کرم تمام زبانوں کے لیے پروجیکٹ کا عنوان ٹائپ کریں۔", "app.components.ProjectTemplatePreview.projectsOverviewPage": "منصوبوں کا جائزہ صفحہ", "app.components.ProjectTemplatePreview.responseError": "افوہ، کچھ غلط ہو گیا۔", "app.components.ProjectTemplatePreview.seeMoreTemplates": "مزید ٹیمپلیٹس دیکھیں", "app.components.ProjectTemplatePreview.successMessage": "پروجیکٹ کامیابی کے ساتھ بنایا گیا!", "app.components.ProjectTemplatePreview.typeProjectName": "پروجیکٹ کا نام ٹائپ کریں۔", "app.components.ProjectTemplatePreview.useTemplate": "اس سانچے کو استعمال کریں۔", "app.components.SeatInfo.additionalSeats": "اضافی نشستیں", "app.components.SeatInfo.additionalSeatsToolTip": "یہ 'شامل نشستوں' کے اوپر آپ کی خریدی گئی اضافی نشستوں کی تعداد کو ظاہر کرتا ہے۔", "app.components.SeatInfo.adminSeats": "ایڈمن کی نشستیں", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} منتظم نشستیں شامل ہیں۔", "app.components.SeatInfo.adminSeatsTooltip1": "منتظمین پلیٹ فارم کے انچارج ہیں اور ان کے پاس تمام فولڈرز اور پروجیکٹس کے مینیجر کے حقوق ہیں۔ مختلف کرداروں کے بارے میں مزید جاننے کے لیے آپ {visitHelpCenter} کر سکتے ہیں۔", "app.components.SeatInfo.currentAdminSeatsTitle": "موجودہ منتظم نشستیں", "app.components.SeatInfo.currentManagerSeatsTitle": "موجودہ مینیجر کی نشستیں", "app.components.SeatInfo.includedAdminToolTip": "یہ سالانہ معاہدے میں شامل منتظمین کے لیے دستیاب نشستوں کی تعداد کو ظاہر کرتا ہے۔", "app.components.SeatInfo.includedManagerToolTip": "یہ سالانہ معاہدے میں شامل مینیجرز کے لیے دستیاب نشستوں کی تعداد کو ظاہر کرتا ہے۔", "app.components.SeatInfo.includedSeats": "نشستیں شامل ہیں۔", "app.components.SeatInfo.managerSeats": "مینیجر کی نشستیں", "app.components.SeatInfo.managerSeatsTooltip": "فولڈر/پروجیکٹ مینیجر لامحدود تعداد میں فولڈرز/پروجیکٹس کا انتظام کر سکتے ہیں۔ مختلف کرداروں کے بارے میں مزید جاننے کے لیے آپ {visitHelpCenter} کر سکتے ہیں۔", "app.components.SeatInfo.managersIncludedText": "{managerSeats} مینیجر سیٹیں شامل ہیں۔", "app.components.SeatInfo.remainingSeats": "باقی نشستیں۔", "app.components.SeatInfo.rolesSupportPage": "https://support.cizenlab.co/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "کل سیٹیں۔", "app.components.SeatInfo.totalSeatsTooltip": "یہ آپ کے پلان میں سیٹوں کی مجموعی تعداد اور آپ کی خریدی ہوئی اضافی سیٹوں کو دکھاتا ہے۔", "app.components.SeatInfo.usedSeats": "استعمال شدہ نشستیں۔", "app.components.SeatInfo.view": "دیکھیں", "app.components.SeatInfo.visitHelpCenter": "ہمارے امدادی مرکز پر جائیں۔", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "آپ کے منصوبے میں {adminSeatsIncluded}ہے۔ ایک بار جب آپ تمام نشستیں استعمال کر لیں گے، اضافی نشستیں 'اضافی نشستیں' کے تحت شامل کی جائیں گی۔", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "آپ کے پلان میں {managerSeatsIncluded}ہے، جو فولڈر مینیجرز اور پروجیکٹ مینیجرز کے لیے اہل ہے۔ ایک بار جب آپ تمام نشستیں استعمال کر لیں گے، اضافی نشستیں 'اضافی نشستیں' کے تحت شامل کی جائیں گی۔", "app.components.UserSearch.addModerators": "شامل کریں۔", "app.components.UserSearch.searchUsers": "صارفین کو تلاش کرنے کے لیے ٹائپ کریں...", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "متبادل غلطی کا پیغام", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "پہلے سے طے شدہ طور پر، مندرجہ ذیل غلطی کا پیغام صارفین کو دکھایا جائے گا:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "غلطی کے پیغام کو حسب ضرورت بنائیں", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "آپ ذیل میں \"متبادل ایرر میسج\" ٹیکسٹ باکس کا استعمال کرتے ہوئے ہر زبان کے لیے اس پیغام کو اوور رائٹ کر سکتے ہیں۔ اگر آپ ٹیکسٹ باکس کو خالی چھوڑ دیتے ہیں، تو ڈیفالٹ پیغام دکھایا جائے گا۔", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "غلطی کا پیغام", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "یہ وہی ہے جو شرکاء دیکھیں گے جب وہ شرکت کی ضروریات کو پورا نہیں کرتے ہیں۔", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "غلطی کا پیغام محفوظ کریں۔", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "کوئی سوال منتخب نہیں کیا گیا۔ براہ کرم پہلے ایک سوال منتخب کریں۔", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "کوئی جواب نہیں۔", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} جوابات", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "سروے کا سوال", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} اب تک", "app.components.admin.DatePhasePicker.Input.openEnded": "کھلا ختم ہوا۔", "app.components.admin.DatePhasePicker.Input.selectDate": "تاریخ منتخب کریں۔", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "اختتامی تاریخ صاف کریں۔", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "آغاز کی تاریخ صاف کریں۔", "app.components.admin.Graphs": "موجودہ فلٹرز کے ساتھ کوئی ڈیٹا دستیاب نہیں ہے۔", "app.components.admin.Graphs.noDataShort": "کوئی ڈیٹا دستیاب نہیں ہے۔", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "وقت کے ساتھ تبصرے", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "وقت کے ساتھ پوسٹس", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "وقت کے ساتھ رد عمل", "app.components.admin.InputManager.onePost": "1 ان پٹ", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "آف لائن پکس ایڈجسٹمنٹ", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "آف لائن ووٹ ایڈجسٹمنٹ", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "یہ اختیار آپ کو دوسرے ذرائع سے شرکت کا ڈیٹا شامل کرنے کی اجازت دیتا ہے، جیسے کہ ذاتی یا کاغذی ووٹ:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "یہ ڈیجیٹل ووٹوں سے بصری طور پر الگ ہوگا۔", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "اس کا اثر حتمی ووٹوں کے نتائج پر پڑے گا۔", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "یہ شرکت کے ڈیٹا ڈیش بورڈز میں ظاہر نہیں ہوگا۔", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "کسی اختیار کے لیے آف لائن ووٹ کسی پروجیکٹ میں صرف ایک بار سیٹ کیے جاسکتے ہیں، اور پروجیکٹ کے تمام مراحل کے درمیان شیئر کیے جاتے ہیں۔", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "آپ کو پہلے کل آف لائن شرکاء کو درج کرنا ہوگا۔", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "کل آف لائن شرکاء", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "درست نتائج کا حساب لگانے کے لیے، ہمیں اس مرحلے کے لیے آف لائن شرکاء کی <b>کل رقم جاننا ہو گی</b>۔", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "براہ کرم صرف ان لوگوں کی نشاندہی کریں جنہوں نے آف لائن حصہ لیا تھا۔", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "{name}کے ذریعے ترمیم شدہ", "app.components.admin.PostManager.PostPreview.assignee": "تفویض کرنے والا", "app.components.admin.PostManager.PostPreview.cancelEdit": "ترمیم منسوخ کریں۔", "app.components.admin.PostManager.PostPreview.currentStatus": "موجودہ حیثیت", "app.components.admin.PostManager.PostPreview.delete": "حذف کریں۔", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "کیا آپ واقعی اس ان پٹ کو حذف کرنا چاہتے ہیں؟ اس کارروائی کو کالعدم نہیں کیا جا سکتا۔", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "کیا آپ واقعی اس ان پٹ کو حذف کرنا چاہتے ہیں؟ ان پٹ کو پروجیکٹ کے تمام مراحل سے حذف کر دیا جائے گا اور اسے بازیافت نہیں کیا جا سکتا۔", "app.components.admin.PostManager.PostPreview.edit": "ترمیم کریں۔", "app.components.admin.PostManager.PostPreview.noOne": "<PERSON>یر تفویض کردہ", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "دوسرے شرکاء کے شراکتی بجٹ میں اس کو جتنی بار شامل کیا گیا ہے۔", "app.components.admin.PostManager.PostPreview.picks": "چنتا ہے: {picksNumber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "رد عمل کا شمار:", "app.components.admin.PostManager.PostPreview.save": "محفوظ کریں۔", "app.components.admin.PostManager.PostPreview.submitError": "خرا<PERSON>ی", "app.components.admin.PostManager.addFeatureLayer": "فیچر پرت شامل کریں۔", "app.components.admin.PostManager.addFeatureLayerInstruction": "ArcGIS آن لائن پر ہوسٹ کردہ فیچر لیئر کا URL کاپی کریں اور اسے نیچے ان پٹ میں چسپاں کریں:", "app.components.admin.PostManager.addFeatureLayerTooltip": "نقشے میں ایک نئی خصوصیت کی پرت شامل کریں۔", "app.components.admin.PostManager.addWebMap": "ویب نقشہ شامل کریں۔", "app.components.admin.PostManager.addWebMapInstruction": "ArcGIS آن لائن سے اپنے ویب میپ کی پورٹل آئی ڈی کاپی کریں اور اسے نیچے ان پٹ میں چسپاں کریں:", "app.components.admin.PostManager.allPhases": "تمام مراحل", "app.components.admin.PostManager.allProjects": "تمام منصوبے", "app.components.admin.PostManager.allStatuses": "تمام سٹیٹس", "app.components.admin.PostManager.allTopics": "تمام ٹیگز", "app.components.admin.PostManager.anyAssignment": "کوئی بھی منتظم", "app.components.admin.PostManager.assignedTo": "{assigneeName}کو تفویض کیا گیا۔", "app.components.admin.PostManager.assignedToMe": "مجھے تفویض کیا گیا۔", "app.components.admin.PostManager.assignee": "تفویض کرنے والا", "app.components.admin.PostManager.authenticationError": "اس پرت کو بازیافت کرنے کی کوشش کے دوران ایک توثیق کی خرابی پیش آگئی۔ براہ کرم URL چیک کریں اور یہ کہ آپ کی Esri API کلید کو اس پرت تک رسائی حاصل ہے۔", "app.components.admin.PostManager.automatedStatusTooltipText": "شرائط پوری ہونے پر یہ حیثیت خود بخود اپ ڈیٹ ہو جاتی ہے۔", "app.components.admin.PostManager.bodyTitle": "تفصیل", "app.components.admin.PostManager.cancel": "منسوخ کریں۔", "app.components.admin.PostManager.cancel2": "منسوخ کریں۔", "app.components.admin.PostManager.co-sponsors": "شریک سپانسرز", "app.components.admin.PostManager.comments": "تبصرے", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "اس کا مطلب ہے کہ آپ ان پٹ سے وابستہ تمام ڈیٹا سے محروم ہو جائیں گے، جیسے تبصرے، ردعمل اور ووٹ۔ اس کارروائی کو کالعدم نہیں کیا جا سکتا۔", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "کیا آپ واقعی ان ان پٹس کو حذف کرنا چاہتے ہیں؟", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "موضوع کو ہٹا دیں۔", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "آپ اس خیال کو ایک ایسے مرحلے سے ہٹانے کی کوشش کر رہے ہیں جہاں اسے ووٹ ملے ہیں۔ اگر آپ ایسا کریں گے تو یہ ووٹ ضائع ہو جائیں گے۔ کیا آپ واقعی اس خیال کو اس مرحلے سے ہٹانا چاہتے ہیں؟", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "اس خیال سے جڑے ووٹ ضائع ہو جائیں گے۔", "app.components.admin.PostManager.components.goToInputManager": "ان پٹ مینیجر پر جائیں۔", "app.components.admin.PostManager.components.goToProposalManager": "پروپوزل مینیجر کے پاس جائیں۔", "app.components.admin.PostManager.contributionFormTitle": "شراکت میں ترمیم کریں۔", "app.components.admin.PostManager.cost": "لاگت", "app.components.admin.PostManager.createInput": "ان پٹ بنائیں", "app.components.admin.PostManager.createInputsDescription": "ماضی کے پروجیکٹ سے ان پٹ کا ایک نیا سیٹ بنائیں", "app.components.admin.PostManager.currentLat": "مرکز طول بلد", "app.components.admin.PostManager.currentLng": "مرکز طول البلد", "app.components.admin.PostManager.currentZoomLevel": "زوم لیول", "app.components.admin.PostManager.defaultEsriError": "اس پرت کو بازیافت کرنے کی کوشش کے دوران ایک خرابی پیش آگئی۔ براہ کرم اپنا نیٹ ورک کنیکٹ چیک کریں اور یہ کہ URL درست ہے۔", "app.components.admin.PostManager.delete": "حذف کریں۔", "app.components.admin.PostManager.deleteAllSelectedInputs": "{count} پوسٹس کو حذف کریں۔", "app.components.admin.PostManager.deleteConfirmation": "کیا آپ واقعی اس پرت کو حذف کرنا چاہتے ہیں؟", "app.components.admin.PostManager.dislikes": "ناپسندیدگی", "app.components.admin.PostManager.edit": "ترمیم کریں۔", "app.components.admin.PostManager.editProjects": "منصوبوں میں ترمیم کریں۔", "app.components.admin.PostManager.editStatuses": "حالتوں میں ترمیم کریں۔", "app.components.admin.PostManager.editTags": "ٹیگز میں ترمیم کریں۔", "app.components.admin.PostManager.editedPostSave": "محفوظ کریں۔", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Esri ArcGIS آن لائن سے ڈیٹا درآمد کرنا ایک اضافی خصوصیت ہے۔ اسے غیر مقفل کرنے کے لیے اپنے GS مینیجر سے بات کریں۔", "app.components.admin.PostManager.esriSideError": "ArcGIS ایپلیکیشن میں ایک خرابی پیش آگئی۔ براہ کرم چند منٹ انتظار کریں اور بعد میں دوبارہ کوشش کریں۔", "app.components.admin.PostManager.esriWebMap": "<PERSON>sri ویب کا نقشہ", "app.components.admin.PostManager.exportAllInputs": "تمام پوسٹس برآمد کریں (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "تمام تبصرے برآمد کریں (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "اس پروجیکٹ کے لیے تبصرے برآمد کریں (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "اس پروجیکٹ میں پوسٹس برآمد کریں (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "منتخب پوسٹس برآمد کریں (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "منتخب پوسٹس کے لیے تبصرے برآمد کریں (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "ان پٹ کے ذریعے ووٹ برآمد کریں (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "صارف کے ذریعے ووٹ برآمد کریں (.xslx)", "app.components.admin.PostManager.exports": "برآمدات", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "آپ نقشہ کا ڈیٹا صرف GeoJSON تہوں کے طور پر اپ لوڈ کر سکتے ہیں یا ArcGIS آن لائن سے درآمد کر سکتے ہیں۔ اگر آپ فیچر لیئر شامل کرنا چاہتے ہیں تو براہ کرم موجودہ GeoJSON پرتوں کو ہٹا دیں۔", "app.components.admin.PostManager.featureLayerTooltop": "آپ آرکی جی آئی ایس آن لائن پر آئٹم کے صفحے کے دائیں جانب فیچر لیئر کا URL تلاش کر سکتے ہیں۔", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "منتخب کریں کہ لوگ آپ کا نام کیسے دیکھیں گے۔", "app.components.admin.PostManager.feedbackBodyPlaceholder": "اس سٹیٹس کی تبدیلی کی وضاحت کریں۔", "app.components.admin.PostManager.fileUploadError": "ایک یا زیادہ فائلیں اپ لوڈ کرنے میں ناکام ہوگئیں۔ براہ کرم فائل کا سائز اور فارمیٹ چیک کریں اور دوبارہ کوشش کریں۔", "app.components.admin.PostManager.formTitle": "آئیڈیا میں ترمیم کریں۔", "app.components.admin.PostManager.generalApiError2": "اس آئٹم کو حاصل کرنے کی کوشش کے دوران ایک خرابی پیش آگئی۔ براہ کرم چیک کریں کہ یو آر ایل یا پورٹل آئی ڈی درست ہے اور آپ کو اس آئٹم تک رسائی حاصل ہے۔", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "آپ نقشہ کا ڈیٹا صرف GeoJSON تہوں کے طور پر اپ لوڈ کر سکتے ہیں یا ArcGIS آن لائن سے درآمد کر سکتے ہیں۔ اگر آپ GeoJSON پرت اپ لوڈ کرنا چاہتے ہیں تو براہ کرم کوئی بھی ArcGIS ڈیٹا ہٹا دیں۔", "app.components.admin.PostManager.goToDefaultMapView": "پہلے سے طے شدہ نقشہ کے مرکز پر جائیں۔", "app.components.admin.PostManager.hiddenFieldsLink": "پوشیدہ فیلڈز", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "ٹپ: اگر آپ Typeform استعمال کر رہے ہیں، تو {hiddenFieldsLink} شامل کریں تاکہ یہ معلوم ہو سکے کہ آپ کے سروے میں کس نے جواب دیا ہے۔", "app.components.admin.PostManager.import2": "درآمد کریں۔", "app.components.admin.PostManager.importError": "منتخب فائل کو درآمد نہیں کیا جا سکا کیونکہ یہ درست GeoJSON فائل نہیں ہے۔", "app.components.admin.PostManager.importEsriFeatureLayer": "ایسری فیچر لیئر درآمد کریں۔", "app.components.admin.PostManager.importEsriWebMap": "ایسری ویب کا نقشہ درآمد کریں۔", "app.components.admin.PostManager.importInputs": "ان پٹ درآمد کریں۔", "app.components.admin.PostManager.imported": "در<PERSON><PERSON><PERSON> شدہ", "app.components.admin.PostManager.initiativeFormTitle": "پہل میں ترمیم کریں۔", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputImportProgress": "{totalCount} میں سے {importedCount} {totalCount, plural, one {ان پٹ میں} other {ان پٹ کو}} درآمد کیا گیا ہے۔ درآمد ابھی بھی جاری ہے، براہ کرم بعد میں دوبارہ چیک کریں۔", "app.components.admin.PostManager.inputManagerHeader": "ان پٹ", "app.components.admin.PostManager.inputs": "ان پٹ", "app.components.admin.PostManager.inputsExportFileName": "ان پٹ", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "صرف وہ پوسٹیں دکھائیں جن پر رائے درکار ہو۔", "app.components.admin.PostManager.issueFormTitle": "مسئلہ میں ترمیم کریں۔", "app.components.admin.PostManager.latestFeedbackMode": "تازہ ترین موجودہ آفیشل اپ ڈیٹ کو وضاحت کے طور پر استعمال کریں۔", "app.components.admin.PostManager.layerAdded": "پرت کامیابی کے ساتھ شامل ہو گئی۔", "app.components.admin.PostManager.likes": "پسند کرتا ہے۔", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "اس ان پٹ کو اس کے موجودہ پروجیکٹ سے دور کرنے سے اس کے تفویض کردہ مراحل کے بارے میں معلومات ختم ہو جائیں گی۔ کیا آپ آگے بڑھنا چاہتے ہیں؟", "app.components.admin.PostManager.mapData": "نقشہ کا ڈیٹا", "app.components.admin.PostManager.multipleInputs": "{ideaCount} پوسٹس", "app.components.admin.PostManager.newFeedbackMode": "اس تبدیلی کی وضاحت کے لیے ایک نیا اپ ڈیٹ لکھیں۔", "app.components.admin.PostManager.noFilteredResults": "آپ کے منتخب کردہ فلٹرز نے کوئی نتیجہ نہیں دیا۔", "app.components.admin.PostManager.noInputs": "ابھی تک کوئی ان پٹ نہیں ہے۔", "app.components.admin.PostManager.noInputsDescription": "آپ اپنا ان پٹ شامل کرتے ہیں یا ماضی میں شرکت کے منصوبے سے شروع کرتے ہیں۔", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 ان پٹ} one {1 ان پٹ} other {# ان پٹ}} منتخب پروجیکٹ اور مرحلے سے درآمد کیے جائیں گے۔ درآمد پس منظر میں چلے گی، اور مکمل ہونے کے بعد ان پٹ مینیجر میں ظاہر ہوں گے۔", "app.components.admin.PostManager.noOne": "<PERSON>یر تفویض کردہ", "app.components.admin.PostManager.noProject": "کوئی پروجیکٹ نہیں۔", "app.components.admin.PostManager.officialFeedbackModal.author": "مصنف", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "منتخب کریں کہ آپ کا نام کیسے ظاہر ہوگا۔", "app.components.admin.PostManager.officialFeedbackModal.description": "آفیشل فیڈ بیک فراہم کرنے سے عمل کو شفاف رکھنے میں مدد ملتی ہے اور پلیٹ فارم پر اعتماد پیدا ہوتا ہے۔", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "مصنف درکار ہے۔", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "رائے درکار ہے۔", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "سرکاری رائے", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "حیثیت کی تبدیلی کی وجہ بیان کریں۔", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "تاثرات پوسٹ کریں۔", "app.components.admin.PostManager.officialFeedbackModal.skip": "اس بار چھوڑ دیں۔", "app.components.admin.PostManager.officialFeedbackModal.title": "اپنے فیصلے کی وضاحت کریں۔", "app.components.admin.PostManager.officialUpdateAuthor": "منتخب کریں کہ لوگ آپ کا نام کیسے دیکھیں گے۔", "app.components.admin.PostManager.officialUpdateBody": "اس سٹیٹس کی تبدیلی کی وضاحت کریں۔", "app.components.admin.PostManager.offlinePicks": "آف لائن انتخاب", "app.components.admin.PostManager.offlineVotes": "آف لائن ووٹ", "app.components.admin.PostManager.onlineVotes": "آن لائن ووٹ", "app.components.admin.PostManager.optionFormTitle": "آپشن میں ترمیم کریں۔", "app.components.admin.PostManager.participants": "شرکاء", "app.components.admin.PostManager.participatoryBudgettingPicks": "چنتا ہے۔", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "آن لائن چنتا ہے۔", "app.components.admin.PostManager.pbItemCountTooltip": "دوسرے شرکاء کے شراکتی بجٹ میں اس کو جتنی بار شامل کیا گیا ہے۔", "app.components.admin.PostManager.petitionFormTitle": "درخواست میں ترمیم کریں۔", "app.components.admin.PostManager.postedIn": "{projectLink}میں پوسٹ کیا گیا۔", "app.components.admin.PostManager.projectFormTitle": "پروجیکٹ میں ترمیم کریں۔", "app.components.admin.PostManager.projectsTab": "پروجیکٹس", "app.components.admin.PostManager.projectsTabTooltipContent": "آپ پوسٹس کو ایک پروجیکٹ سے دوسرے پروجیکٹ میں منتقل کرنے کے لیے گھسیٹ کر چھوڑ سکتے ہیں۔ نوٹ کریں کہ ٹائم لائن پروجیکٹس کے لیے، آپ کو اب بھی پوسٹ کو ایک مخصوص مرحلے میں شامل کرنے کی ضرورت ہوگی۔", "app.components.admin.PostManager.proposalFormTitle": "تجویز میں ترمیم کریں۔", "app.components.admin.PostManager.proposedBudgetTitle": "مجوزہ بجٹ", "app.components.admin.PostManager.publication_date": "پر شائع ہوا۔", "app.components.admin.PostManager.questionFormTitle": "سوال میں ترمیم کریں۔", "app.components.admin.PostManager.reactions": "<PERSON><PERSON>", "app.components.admin.PostManager.resetFiltersButton": "فلٹرز کو دوبارہ ترتیب دیں۔", "app.components.admin.PostManager.resetInputFiltersDescription": "تمام ان پٹ دیکھنے کے لیے فلٹرز کو ری سیٹ کریں۔", "app.components.admin.PostManager.saved": "محفوظ کیا گیا۔", "app.components.admin.PostManager.screeningTooltip": "اسکریننگ آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے حکومتی کامیابی کے مینیجر یا منتظم سے بات کریں۔", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "اس مرحلے کے لیے اسکریننگ بند ہے۔ اسے فعال کرنے کے لیے فیز سیٹ اپ پر جائیں۔", "app.components.admin.PostManager.selectAPhase": "ایک مرحلہ منتخب کریں۔", "app.components.admin.PostManager.selectAProject": "ایک پروجیکٹ منتخب کریں۔", "app.components.admin.PostManager.setAsDefaultMapView": "موجودہ سینٹر پوائنٹ اور زوم لیول کو بطور نقشہ ڈیفالٹ محفوظ کریں۔", "app.components.admin.PostManager.startFromPastInputs": "ماضی کے ان پٹ سے شروع کریں۔", "app.components.admin.PostManager.statusChangeGenericError": "ایک خرابی تھی، براہ کرم بعد میں دوبارہ کوشش کریں یا سپورٹ سے رابطہ کریں۔", "app.components.admin.PostManager.statusChangeSave": "حیثیت تبدیل کریں۔", "app.components.admin.PostManager.statusesTab": "حیثیت", "app.components.admin.PostManager.statusesTabTooltipContent": "ڈریگ اینڈ ڈراپ کا استعمال کرتے ہوئے پوسٹ کی حیثیت تبدیل کریں۔ اصل مصنف اور دیگر تعاون کنندگان کو تبدیل شدہ حیثیت کی اطلاع موصول ہوگی۔", "app.components.admin.PostManager.submitApiError": "فارم جمع کرانے میں ایک مسئلہ تھا۔ براہ کرم کسی خامی کی جانچ کریں اور دوبارہ کوشش کریں۔", "app.components.admin.PostManager.timelineTab": "ٹائم لائن", "app.components.admin.PostManager.timelineTabTooltipText": "پوسٹس کو مختلف پروجیکٹ کے مراحل میں کاپی کرنے کے لیے گھسیٹیں اور چھوڑیں۔", "app.components.admin.PostManager.title": "عنوان", "app.components.admin.PostManager.topicsTab": "ٹی<PERSON>ز", "app.components.admin.PostManager.topicsTabTooltipText": "Add tags to an input using drag and drop.", "app.components.admin.PostManager.view": "دیکھیں", "app.components.admin.PostManager.votes": "ووٹ", "app.components.admin.PostManager.votesByInputExportFileName": "ووٹس_بذریعہ_ان پٹ", "app.components.admin.PostManager.votesByUserExportFileName": "ووٹس_بائی_صارف", "app.components.admin.PostManager.webMapAlreadyExists": "آپ ایک وقت میں صرف ایک ویب نقشہ شامل کر سکتے ہیں۔ ایک مختلف درآمد کرنے کے لیے موجودہ کو ہٹا دیں۔", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "آپ نقشہ کا ڈیٹا صرف GeoJSON تہوں کے طور پر اپ لوڈ کر سکتے ہیں یا ArcGIS آن لائن سے درآمد کر سکتے ہیں۔ اگر آپ ویب میپ کو جوڑنا چاہتے ہیں تو براہ کرم موجودہ GeoJSON پرتوں کو ہٹا دیں۔", "app.components.admin.PostManager.webMapTooltip": "آپ ویب میپ پورٹل ID کو اپنے ArcGIS آن لائن آئٹم کے صفحے پر، دائیں جانب تلاش کر سکتے ہیں۔", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {ایک دن سے بھی کم} one {ایک دن} other {# دن}} باقی", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "منسوخ کریں۔", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "ہاں، سروے کے نتائج کو حذف کریں۔", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "اسے کالعدم نہیں کیا جا سکتا", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "سروے کے نتائج کو حذف کریں۔", "app.components.admin.ProjectEdit.survey.downloadResults2": "سروے کے نتائج ڈاؤن لوڈ کریں۔", "app.components.admin.ReportExportMenu.FileName.fromFilter": "سے", "app.components.admin.ReportExportMenu.FileName.groupFilter": "گروپ", "app.components.admin.ReportExportMenu.FileName.projectFilter": "پروجیکٹ", "app.components.admin.ReportExportMenu.FileName.topicFilter": "ٹیگ", "app.components.admin.ReportExportMenu.FileName.untilFilter": "تک", "app.components.admin.ReportExportMenu.downloadPng": "PNG کے بطور ڈاؤن لوڈ کریں۔", "app.components.admin.ReportExportMenu.downloadSvg": "SVG کے بطور ڈاؤن لوڈ کریں۔", "app.components.admin.ReportExportMenu.downloadXlsx": "ایکسل ڈاؤن لوڈ کریں۔", "app.components.admin.SlugInput.regexError": "سلگ میں صرف باقاعدہ، چھوٹے حروف (az)، نمبر (0-9) اور ہائفن (-) شامل ہوسکتے ہیں۔ پہلے اور آخری حروف ہائفن نہیں ہو سکتے۔ لگاتار ہائفنز (--) منع ہیں۔", "app.components.admin.TerminologyConfig.saveButton": "محفوظ کریں۔", "app.components.admin.commonGroundInputManager.title": "عنوان", "app.components.admin.seatSetSuccess.admin": "ایڈمن", "app.components.admin.seatSetSuccess.allDone": "سب ہو گیا", "app.components.admin.seatSetSuccess.close": "بند", "app.components.admin.seatSetSuccess.manager": "مینیجر", "app.components.admin.seatSetSuccess.orderCompleted": "آرڈر مکمل ہو گیا۔", "app.components.admin.seatSetSuccess.reflectedMessage": "آپ کے پلان میں تبدیلیاں آپ کے اگلے بلنگ سائیکل پر ظاہر ہوں گی۔", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} حقوق منتخب صارف (صارفین) کو دیے گئے ہیں۔", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "کیا آپ واقعی سروے کے تمام نتائج کو حذف کرنا چاہتے ہیں؟", "app.components.app.containers.AdminPage.ProjectEdit.beta": "بیٹا", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "شرکت کا یہ طریقہ بیٹا میں ہے۔ ہم فیڈ بیک اکٹھا کرنے اور تجربے کو بہتر بنانے کے لیے اسے بتدریج رول آؤٹ کر رہے ہیں۔", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "کسی دستاویز پر تاثرات جمع کرنا ایک حسب ضرورت خصوصیت ہے، اور یہ آپ کے موجودہ لائسنس میں شامل نہیں ہے۔ اس کے بارے میں مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "شراکت", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "دنوں کی تعداد درکار ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "ووٹوں کی کم از کم تعداد تک پہنچنے کے لیے دنوں کی تعداد", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "گوگل فارمز کے لیے لنک کو ایمبیڈ کرنے کے بارے میں مزید معلومات {googleFormsTooltipLink}میں مل سکتی ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "یہ سپورٹ آرٹیکل", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "پہل", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "ان پٹ کو کیا کہا جائے؟", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "تبصرہ", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "اپنے Konveio دستاویز کا لنک یہاں فراہم کریں۔ Konveio کو ترتیب دینے کے بارے میں مزید معلومات کے لیے ہمارا {supportArticleLink} پڑھیں۔", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "سپورٹ مضمون", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "یہ آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے سرکاری کامیابی کے مینیجر یا منتظم سے رابطہ کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "زیادہ سے زیادہ بجٹ درکار ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "فی اختیار ووٹوں کی زیادہ سے زیادہ تعداد ووٹوں کی کل تعداد سے کم یا اس کے برابر ہونی چاہیے۔", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "زیادہ سے زیادہ ووٹوں کی ضرورت ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "پیغام رسانی", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "کم از کم بجٹ زیادہ سے زیادہ بجٹ سے بڑا نہیں ہو سکتا", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "کم از کم بجٹ درکار ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "ووٹوں کی کم از کم تعداد زیادہ سے زیادہ تعداد سے زیادہ نہیں ہو سکتی", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "ووٹوں کی کم از کم تعداد درکار ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "ختم ہونے کی تاریخ غائب ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "شروع کی تاریخ غائب ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "آپشن", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "ان پٹ مینیجر ٹیب", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "ایک مرحلہ بنانے کے بعد ان پٹ مینیجر ٹیب میں ووٹنگ کے اختیارات کو ترتیب دیں۔", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "ووٹنگ کے اختیارات کو {optionsPageLink}میں ترتیب دیں۔", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "شرکت کے اختیارات", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "شرکاء", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "پٹیشن", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "ایڈمنز اور مینیجرز", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>تشریحی دستاویز:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} اس مرحلے میں حصہ لے سکتے ہیں۔", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "منسوخ کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>تبصرہ:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "مشترکہ زمینی مرحلہ", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "مرحلہ حذف کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "ہاں، اس مرحلے کو حذف کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "کیا آپ واقعی اس مرحلے کو حذف کرنا چاہتے ہیں؟", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "اس مرحلے سے متعلق تمام ڈیٹا کو حذف کر دیا جائے گا۔ اسے کالعدم نہیں کیا جا سکتا۔", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "دستاویز کی تشریح کا مرحلہ", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "ہر کوئی", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "بیرونی سروے کا مرحلہ", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "<PERSON><PERSON>ال کا مرحلہ", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "پلیٹ فارم سروے کے مرحلے میں", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "معلومات کا مرحلہ", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "مخلوط حقوق", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "کوئی اختتامی تاریخ نہیں۔", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "رائے شماری کا مرحلہ", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "تجاویز کا مرحلہ", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>ر<PERSON> عمل:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>ایونٹ کے لیے رجسٹرڈ:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "رجسٹرڈ صارفین", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b>ان پٹ جمع کروائیں:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>رائے شماری لینا:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b>سروے کرنا:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "تصدیق شدہ ای میلز والے صارفین", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>رضاکارانہ خدمات:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "رضاکارانہ مرحلہ", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>ووٹنگ:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "ووٹنگ کا مرحلہ", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "کون حصہ لے سکتا ہے؟", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "ان پٹس اس وقت تک نظر نہیں آئیں گے جب تک کہ کوئی منتظم ان کا جائزہ نہیں لے گا اور ان کی منظوری نہیں دے گا۔ مصنفین ان پٹ کو اسکرین کرنے یا ان پر ردعمل ظاہر کرنے کے بعد ان میں ترمیم نہیں کر سکتے۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "صرف ایڈمنز", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "کوئی بھی جس کے پاس لنک ہے مسودہ پروجیکٹ کے ساتھ تعامل کر سکتا ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "منظور کرو", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "منظوری پروجیکٹ مینیجرز کو پروجیکٹ شائع کرنے کی اجازت دیتی ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "{name}سے منظور شدہ", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "محف<PERSON><PERSON> شدہ", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "مسودہ", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "تفصیل میں ترمیم کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "ہر کوئی", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "گروپس", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "پوشیدہ", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "آف لائن ووٹرز", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "صرف ایڈمنز{inFolder, select, true { یا فولڈر مینیجر} other {}} پروجیکٹ کو شائع کر سکتے ہیں", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 شریک} other {{participantsCount} شرکاء}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "سرایت شدہ طریقوں میں حصہ لینے والے (مثال کے طور پر، بیرونی سروے)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "کسی پروجیکٹ کے پیروکار", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "نوٹ: گمنام یا کھلی شرکت کی اجازتوں کو فعال کرنے سے صارفین کو متعدد بار شرکت کرنے کی اجازت مل سکتی ہے، جس سے صارف کا ڈیٹا گمراہ کن یا نامکمل ہو سکتا ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "شرکاء <b>شامل نہیں ہیں</b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "شرکاء میں شامل ہیں:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "ایونٹ رجسٹر کرنے والے", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "گو ووکل طریقوں کے ساتھ تعامل کرنے والے صارفین", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "منظوری کا انتظار ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "پروجیکٹ کا جائزہ لینے والوں کو مطلع کر دیا گیا ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "عوا<PERSON>ی", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "شائع کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "شائع شدہ - فعال", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "شائع - ختم", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "پروجیکٹ کا پیش نظارہ لنک ریفریش کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "پروجیکٹ کا پیش نظارہ لنک دوبارہ بنائیں۔ یہ پچھلے لنک کو غلط کر دے گا۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "پرانے لنکس کام کرنا چھوڑ دیں گے لیکن آپ کسی بھی وقت نیا بنا سکتے ہیں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "کیا آپ کو یقین ہے؟ یہ موجودہ لنک کو غیر فعال کر دے گا۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "منسوخ کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "ہاں، لنک ریفریش کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "منظوری کی درخواست کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "پروجیکٹ کو شائع کرنے سے پہلے منتظم{inFolder, select, true { یا فولڈر مینیجر} other {}} سے منظور شدہ ہونا ضروری ہے۔ منظوری کی درخواست کرنے کے لیے نیچے دیئے گئے بٹن پر کلک کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "ترتیبات", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "شیئر کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "لنک کاپی کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "لنک کاپی ہو گیا۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "نجی لنکس کا اشتراک آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے حکومتی کامیابی کے مینیجر یا منتظم سے بات کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "اس پروجیکٹ کو شیئر کریں۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "جس کی رسائی ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "دیکھیں", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "پروجیکٹ", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "تجو<PERSON>ز", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "سوال", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "ووٹوں کی کم از کم تعداد پر غور کیا جائے۔", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "ووٹوں کی کم از کم تعداد درکار ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.report": "رپورٹ", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "آدانوں کی اسکریننگ کی ضرورت ہے۔", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "ٹائم لائن", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "ٹریفک", "app.components.formBuilder.cancelMethodChange1": "منسوخ کریں۔", "app.components.formBuilder.changeMethodWarning1": "طریقوں کو تبدیل کرنے سے پچھلا طریقہ استعمال کرتے ہوئے پیدا یا موصول ہونے والے کسی بھی ان پٹ ڈیٹا کو چھپایا جا سکتا ہے۔", "app.components.formBuilder.changingMethod1": "طریقہ بدلنا", "app.components.formBuilder.confirmMethodChange1": "ہاں، جاری رکھیں", "app.components.formBuilder.copySurveyModal.cancel": "منسوخ کریں۔", "app.components.formBuilder.copySurveyModal.description": "یہ جوابات کے بغیر تمام سوالات اور منطق کاپی کر دے گا۔", "app.components.formBuilder.copySurveyModal.duplicate": "نقل", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "اس منصوبے میں کوئی مناسب مراحل نہیں ملے", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "کوئی مرحلہ منتخب نہیں کیا گیا۔ براہ کرم پہلے ایک مرحلہ منتخب کریں۔", "app.components.formBuilder.copySurveyModal.noProject": "کوئی پروجیکٹ نہیں۔", "app.components.formBuilder.copySurveyModal.noProjectSelected": "کوئی پروجیکٹ منتخب نہیں کیا گیا۔ براہ کرم پہلے ایک پروجیکٹ منتخب کریں۔", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "آپ پہلے ہی اس سروے میں تبدیلیاں محفوظ کر چکے ہیں۔ اگر آپ دوسرے سروے کو نقل کرتے ہیں، تو تبدیلیاں ضائع ہو جائیں گی۔", "app.components.formBuilder.copySurveyModal.surveyPhase": "سروے کا مرحلہ", "app.components.formBuilder.copySurveyModal.title": "نقل کرنے کے لیے ایک سروے کا انتخاب کریں۔", "app.components.formBuilder.editWarningModal.addOrReorder": "سوالات شامل کریں یا دوبارہ ترتیب دیں۔", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "آپ کا جوابی ڈیٹا غلط ہو سکتا ہے۔", "app.components.formBuilder.editWarningModal.changeQuestionText2": "متن میں ترمیم کریں۔", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "ٹائپنگ کی غلطی کو ٹھیک کرنا؟ یہ آپ کے جوابی ڈیٹا کو متاثر نہیں کرے گا۔", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "آپ اس سوال سے منسلک جوابی ڈیٹا سے محروم ہو جائیں گے۔", "app.components.formBuilder.editWarningModal.deteleAQuestion": "ایک سوال حذف کریں۔", "app.components.formBuilder.editWarningModal.exportYouResponses2": "اپنے جوابات برآمد کریں۔", "app.components.formBuilder.editWarningModal.loseDataWarning3": "انتباہ: آپ جوابی ڈیٹا کو ہمیشہ کے لیے کھو سکتے ہیں۔ جاری رکھنے سے پہلے،", "app.components.formBuilder.editWarningModal.noCancel": "نہیں، منسوخ کریں۔", "app.components.formBuilder.editWarningModal.title4": "لائیو سروے میں ترمیم کریں۔", "app.components.formBuilder.editWarningModal.yesContinue": "ہاں، جاری رکھیں", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "اس سروے کے لیے حقوق کی ترتیبات تک رسائی حاصل کریں۔", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "'سروے فارم میں آبادیاتی فیلڈز' فعال ہے۔ جب سروے فارم ظاہر ہوتا ہے تو سروے کے اختتام سے فوراً پہلے کسی بھی ترتیب شدہ آبادیاتی سوالات کو ایک نئے صفحہ پر شامل کر دیا جائے گا۔ ان سوالات کو {accessRightsSettingsLink}میں تبدیل کیا جا سکتا ہے۔", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "اس مرحلے کے لیے حقوق کی ترتیبات تک رسائی حاصل کریں۔", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "سروے کے جواب دہندگان کو سروے کے جوابات جمع کرانے کے لیے سائن اپ کرنے یا لاگ ان کرنے کی ضرورت نہیں ہوگی، جس کے نتیجے میں ڈپلیکیٹ گذارشات ہو سکتی ہیں۔", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "سائن اپ/لاگ ان مرحلہ کو چھوڑ کر، آپ سروے کے جواب دہندگان پر آبادیاتی معلومات اکٹھا نہ کرنے کو قبول کرتے ہیں، جو آپ کے ڈیٹا کے تجزیہ کی صلاحیتوں کو متاثر کر سکتی ہے۔", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "یہ سروے رسائی کے حقوق کے ٹیب کے تحت \"کسی بھی\" کے لیے رسائی کی اجازت دینے کے لیے ترتیب دیا گیا ہے۔", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "اگر آپ اسے تبدیل کرنا چاہتے ہیں، تو آپ اسے {accessRightsSettingsLink}میں کر سکتے ہیں۔", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "آپ سائن اپ/لاگ ان مرحلہ کے ذریعے سروے کے جواب دہندگان سے درج ذیل آبادیاتی سوالات پوچھ رہے ہیں۔", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "آبادیاتی معلومات کے مجموعہ کو ہموار کرنے اور آپ کے صارف کے ڈیٹا بیس میں اس کے انضمام کو یقینی بنانے کے لیے، ہم کسی بھی آبادیاتی سوالات کو براہ راست سائن اپ/لاگ ان کے عمل میں شامل کرنے کا مشورہ دیتے ہیں۔ ایسا کرنے کے لیے، براہ کرم {accessRightsSettingsLink}استعمال کریں۔", "app.components.onboarding.askFollowPreferences": "صارفین سے علاقوں یا عنوانات کی پیروی کرنے کو کہیں۔", "app.components.onboarding.followHelperText": "یہ رجسٹریشن کے عمل میں ایک قدم کو چالو کرتا ہے جہاں صارف ان علاقوں یا عنوانات کی پیروی کر سکیں گے جنہیں آپ نیچے منتخب کرتے ہیں۔", "app.components.onboarding.followPreferences": "ترجیحات پر عمل کریں۔", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} منصوبے کے اندر، {noOfAdditionalSeats} اضافی", "app.components.seatsWithinPlan.seatsWithinPlanText": "منصوبہ بندی کے اندر نشستیں", "app.containers.Admin.Campaigns.campaignFrom": "منجانب:", "app.containers.Admin.Campaigns.campaignTo": "کو:", "app.containers.Admin.Campaigns.customEmails": "حسب ضرورت ای میلز", "app.containers.Admin.Campaigns.customEmailsDescription": "حسب ضرورت ای میلز بھیجیں اور اعدادوشمار چیک کریں۔", "app.containers.Admin.Campaigns.noAccess": "ہمیں افسوس ہے، لیکن ایسا لگتا ہے کہ آپ کو ای میلز کے سیکشن تک رسائی نہیں ہے۔", "app.containers.Admin.Campaigns.tabAutomatedEmails": "<PERSON>و<PERSON><PERSON>ار ای میلز", "app.containers.Admin.Insights.tabReports": "رپورٹس", "app.containers.Admin.Invitations.a11y_removeInvite": "دعوت نامہ ہٹا دیں۔", "app.containers.Admin.Invitations.addToGroupLabel": "ان لوگوں کو مخصوص دستی صارف گروپوں میں شامل کریں۔", "app.containers.Admin.Invitations.adminLabel1": "مدعو کرنے والوں کو ایڈمن کے حقوق دیں۔", "app.containers.Admin.Invitations.adminLabelTooltip": "جب آپ اس اختیار کو منتخب کرتے ہیں، تو جن لوگوں کو آپ مدعو کر رہے ہیں انہیں آپ کے پلیٹ فارم کی تمام ترتیبات تک رسائی حاصل ہوگی۔", "app.containers.Admin.Invitations.configureInvitations": "3. دعوت ناموں کو ترتیب دیں۔", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "آپ کی تلاش سے مماثل کوئی دعوت نامے نہیں ہیں۔", "app.containers.Admin.Invitations.deleteInvite": "حذف کریں۔", "app.containers.Admin.Invitations.deleteInviteConfirmation": "کیا آپ واقعی اس دعوت کو حذف کرنا چاہتے ہیں؟", "app.containers.Admin.Invitations.deleteInviteTooltip": "دعوت نامہ منسوخ کرنے سے آپ اس شخص کو دوبارہ دعوت نامہ بھیج سکیں گے۔", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. ٹیمپلیٹ کو ڈاؤن لوڈ اور پُر کریں۔", "app.containers.Admin.Invitations.downloadTemplate": "ٹیمپلیٹ ڈاؤن لوڈ کریں۔", "app.containers.Admin.Invitations.email": "ای میل", "app.containers.Admin.Invitations.emailListLabel": "دستی طور پر ان لوگوں کے ای میل پتے درج کریں جنہیں آپ مدعو کرنا چاہتے ہیں۔ ہر ایک پتے کو کوما سے الگ کریں۔", "app.containers.Admin.Invitations.exportInvites": "تمام دعوت نامے برآمد کریں۔", "app.containers.Admin.Invitations.fileRequirements": "اہم: دعوت نامے درست طریقے سے بھیجنے کے لیے، درآمدی ٹیمپلیٹ سے کوئی کالم نہیں ہٹایا جا سکتا۔ غیر استعمال شدہ کالموں کو خالی چھوڑ دیں۔", "app.containers.Admin.Invitations.filetypeError": "فائل کی غلط قسم۔ صرف XLSX فائلیں تعاون یافتہ ہیں۔", "app.containers.Admin.Invitations.groupsPlaceholder": "کوئی گروپ منتخب نہیں کیا گیا۔", "app.containers.Admin.Invitations.helmetDescription": "صارفین کو پلیٹ فارم پر مدعو کریں۔", "app.containers.Admin.Invitations.helmetTitle": "ایڈمن دعوتی ڈیش بورڈ", "app.containers.Admin.Invitations.importOptionsInfo": "ان اختیارات کو صرف اس صورت میں مدنظر رکھا جائے گا جب ایکسل فائل میں ان کی وضاحت نہ کی گئی ہو۔\n      مزید معلومات کے لیے براہ کرم {supportPageLink} ملاحظہ کریں۔", "app.containers.Admin.Invitations.importTab": "ای میل پتے درآمد کریں۔", "app.containers.Admin.Invitations.invitationExpirationWarning": "آگاہ رہیں کہ دعوت نامے 30 دن کے بعد ختم ہو جاتے ہیں۔ اس مدت کے بعد بھی آپ انہیں دوبارہ بھیج سکتے ہیں۔", "app.containers.Admin.Invitations.invitationOptions": "دعوت کے اختیارات", "app.containers.Admin.Invitations.invitationSubtitle": "لوگوں کو کسی بھی وقت پلیٹ فارم پر مدعو کریں۔ انہیں آپ کے لوگو کے ساتھ ایک غیر جانبدار دعوتی ای میل موصول ہوتا ہے، جس میں انہیں پلیٹ فارم پر رجسٹر کرنے کے لیے کہا جاتا ہے۔", "app.containers.Admin.Invitations.invitePeople": "لوگوں کو ای میل کے ذریعے مدعو کریں۔", "app.containers.Admin.Invitations.inviteStatus": "حیثیت", "app.containers.Admin.Invitations.inviteStatusAccepted": "قبول کر لیا", "app.containers.Admin.Invitations.inviteStatusPending": "زیر التواء", "app.containers.Admin.Invitations.inviteTextLabel": "اختیاری طور پر ایک پیغام ٹائپ کریں جو دعوتی میل میں شامل کیا جائے گا۔", "app.containers.Admin.Invitations.invitedSince": "مدعو کیا۔", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.cizenlab.co/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "دعوت کی زبان منتخب کریں۔", "app.containers.Admin.Invitations.moderatorLabel": "ان لوگوں کو پروجیکٹ مینجمنٹ کے حقوق دیں۔", "app.containers.Admin.Invitations.moderatorLabelTooltip": "جب آپ اس اختیار کو منتخب کرتے ہیں تو، مدعو افراد کو منتخب پروجیکٹ (پروجیکٹ) کے لیے پروجیکٹ مینیجر کے حقوق تفویض کیے جائیں گے۔ پروجیکٹ مینیجر کے حقوق کے بارے میں مزید معلومات {moderatorLabelTooltipLink}۔", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.cizenlab.co/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "یہاں", "app.containers.Admin.Invitations.name": "نام", "app.containers.Admin.Invitations.processing": "دعوت نامے بھیجنا۔ برائے مہربانی انتظار کریں...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "کوئی پروجیکٹ منتخب نہیں کیا گیا۔", "app.containers.Admin.Invitations.save": "دعوت نامے بھیجیں۔", "app.containers.Admin.Invitations.saveErrorMessage": "ایک یا زیادہ غلطیاں ہوئیں اور دعوت نامے نہیں بھیجے گئے۔ براہ کرم ذیل میں درج غلطی(غلطیوں) کو درست کریں اور دوبارہ کوشش کریں۔", "app.containers.Admin.Invitations.saveSuccess": "کامیابی!", "app.containers.Admin.Invitations.saveSuccessMessage": "دعوت نامہ کامیابی کے ساتھ بھیج دیا گیا۔", "app.containers.Admin.Invitations.supportPage": "سپورٹ صفحہ", "app.containers.Admin.Invitations.supportPageLinkText": "سپورٹ پیج پر جائیں۔", "app.containers.Admin.Invitations.tabAllInvitations": "تمام دعوتیں۔", "app.containers.Admin.Invitations.tabInviteUsers": "صارفین کو مدعو کریں۔", "app.containers.Admin.Invitations.textTab": "دستی طور پر ای میل پتے درج کریں۔", "app.containers.Admin.Invitations.unknownError": "کچھ غلط ہو گیا۔ براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.Admin.Invitations.uploadCompletedFile": "2. اپنی مکمل شدہ ٹیمپلیٹ فائل اپ لوڈ کریں۔", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} اگر آپ امپورٹ ٹیمپلیٹ میں تمام معاون کالموں کے بارے میں مزید معلومات چاہتے ہیں۔", "app.containers.Admin.Moderation.all": "تمام", "app.containers.Admin.Moderation.belongsTo": "سے تعلق رکھتا ہے۔", "app.containers.Admin.Moderation.collapse": "کم دیکھیں", "app.containers.Admin.Moderation.comment": "تبصرہ", "app.containers.Admin.Moderation.commentDeletionCancelButton": "منسوخ کریں۔", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "حذف کریں۔", "app.containers.Admin.Moderation.confirmCommentDeletion": "کیا آپ واقعی یہ تبصرہ حذف کرنا چاہتے ہیں؟ یہ مستقل ہے اور اسے کالعدم نہیں کیا جا سکتا۔", "app.containers.Admin.Moderation.content": "مواد", "app.containers.Admin.Moderation.date": "تاریخ", "app.containers.Admin.Moderation.deleteComment": "تبصرہ حذف کریں۔", "app.containers.Admin.Moderation.goToComment": "اس تبصرہ کو ایک نئے ٹیب میں کھولیں۔", "app.containers.Admin.Moderation.goToPost": "اس پوسٹ کو ایک نئے ٹیب میں کھولیں۔", "app.containers.Admin.Moderation.goToProposal": "اس تجویز کو ایک نئے ٹیب میں کھولیں۔", "app.containers.Admin.Moderation.markFlagsError": "آئٹمز کو نشان زد نہیں کیا جا سکا۔ دوبارہ کوشش کریں۔", "app.containers.Admin.Moderation.markNotSeen": "نشان زد کریں {selectedItemsCount, plural, one {# آئٹم} other {# آئٹمز}} جیسا کہ نہیں دیکھا گیا", "app.containers.Admin.Moderation.markSeen": "نشان زد کریں {selectedItemsCount, plural, one {# آئٹم} other {# آئٹمز}} جیسا کہ دیکھا گیا ہے", "app.containers.Admin.Moderation.moderationsTooltip": "یہ صفحہ آپ کو تمام نئی پوسٹس کو فوری طور پر چیک کرنے کی اجازت دیتا ہے جو آپ کے پلیٹ فارم پر شائع ہوئی ہیں، بشمول خیالات اور تبصرے۔ آپ پوسٹس کو 'دیکھے گئے' کے طور پر نشان زد کر سکتے ہیں تاکہ دوسروں کو معلوم ہو کہ ابھی کس چیز پر کارروائی کی ضرورت ہے۔", "app.containers.Admin.Moderation.noUnviewedItems": "کوئی ان دیکھی اشیاء نہیں ہیں۔", "app.containers.Admin.Moderation.noViewedItems": "کوئی دیکھی ہوئی اشیاء نہیں ہیں۔", "app.containers.Admin.Moderation.pageTitle1": "کھانا کھلانا", "app.containers.Admin.Moderation.post": "پوسٹ", "app.containers.Admin.Moderation.profanityBlockerSetting": "بے حرمتی روکنے والا", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "ایسی پوسٹس کو بلاک کریں جن میں سب سے زیادہ جارحانہ الفاظ کی اطلاع دی گئی ہو۔", "app.containers.Admin.Moderation.project": "پروجیکٹ", "app.containers.Admin.Moderation.read": "دیکھا", "app.containers.Admin.Moderation.readMore": "مزید پڑھیں", "app.containers.Admin.Moderation.removeFlagsError": "تنبیہ (انتباہات) کو ہٹایا نہیں جا سکا۔ دوبارہ کوشش کریں۔", "app.containers.Admin.Moderation.rowsPerPage": "قطار فی صفحہ", "app.containers.Admin.Moderation.settings": "ترتیبات", "app.containers.Admin.Moderation.settingsSavingError": "محفوظ نہیں کر سکا۔ ترتیب کو دوبارہ تبدیل کرنے کی کوشش کریں۔", "app.containers.Admin.Moderation.show": "دکھائیں۔", "app.containers.Admin.Moderation.status": "حیثیت", "app.containers.Admin.Moderation.successfulUpdateSettings": "ترتیبات کامیابی کے ساتھ اپ ڈیٹ ہو گئیں۔", "app.containers.Admin.Moderation.type": "قسم", "app.containers.Admin.Moderation.unread": "نہیں دیکھا", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "یہ صفحہ درج ذیل حصوں پر مشتمل ہے۔ آپ انہیں آن/آف کر سکتے ہیں اور ضرورت کے مطابق ان میں ترمیم کر سکتے ہیں۔", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "سیک<PERSON><PERSON>ز", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "صفحہ دیکھیں", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "صفحہ پر نہیں دکھایا گیا ہے۔", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "صفحہ پر دکھایا گیا ہے۔", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "منسلکات", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "فائلیں شامل کریں (زیادہ سے زیادہ 50 ایم بی) جو صفحہ سے ڈاؤن لوڈ کرنے کے لیے دستیاب ہوں گی۔", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "نیچے معلوماتی سیکشن", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "صفحہ کے نیچے حسب ضرورت سیکشن میں اپنا مواد شامل کریں۔", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "ترمیم کریں۔", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "واقعات کی فہرست", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "منصوبوں سے متعلق واقعات دکھائیں۔", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "ہیرو بینر", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "صفحہ بینر کی تصویر اور متن کو حسب ضرورت بنائیں۔", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "منصوبوں کی فہرست", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "اپنے صفحہ کی ترتیبات کی بنیاد پر پروجیکٹس دکھائیں۔ آپ دکھائے جانے والے پروجیکٹس کا جائزہ لے سکتے ہیں۔", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "سب سے اوپر معلومات سیکشن", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "صفحہ کے اوپری حصے میں حسب ضرورت سیکشن میں اپنا مواد شامل کریں۔", "app.containers.Admin.PagesAndMenu.addButton": "navbar میں شامل کریں۔", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "navbar میں نام", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "کیا آپ واقعی اس صفحہ کو حذف کرنا چاہتے ہیں؟ اسے کالعدم نہیں کیا جا سکتا۔", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "تمام زبانوں کے لیے ایک عنوان فراہم کریں۔", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "دیگر دستیاب صفحات", "app.containers.Admin.PagesAndMenu.components.savePage": "صفحہ محفوظ کریں۔", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "صفحہ کامیابی کے ساتھ محفوظ ہو گیا۔", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "منسلکات (زیادہ سے زیادہ 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "کامیابی", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "مواد", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "منسلکات کو محفوظ نہیں کیا جا سکا", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "فائلیں 50Mb سے بڑی نہیں ہونی چاہئیں۔ شامل کی گئی فائلیں اس صفحہ کے نیچے دکھائی جائیں گی۔", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "منسلکات محفوظ ہو گئے۔", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "منسلکات | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "منسلکات", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "منسلکات کو محفوظ اور فعال کریں۔", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "منسلکات کو محفوظ کریں۔", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "تمام زبانوں کے لیے مواد فراہم کریں۔", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "کامیابی", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "مواد", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "نیچے کی معلومات والے حصے کو محفوظ نہیں کیا جا سکا", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "نیچے کا معلوماتی سیکشن محفوظ ہو گیا۔", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "نیچے معلوماتی سیکشن", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "نیچے کی معلومات کے سیکشن کو محفوظ اور فعال کریں۔", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "نیچے کی معلومات والے حصے کو محفوظ کریں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "حسب ضرورت صفحات بنانا آپ کے موجودہ لائسنس میں شامل نہیں ہے۔ اس کے بارے میں مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "براہ کرم کم از کم ایک ٹیگ منتخب کریں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "کامیابی", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "علاقے کے لحاظ سے", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "بذریعہ ٹیگ", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "پروجیکٹس کو ٹیگ یا ایریا کے لحاظ سے ڈسپلے کرنا آپ کے موجودہ لائسنس کا حصہ نہیں ہے۔ اس کے بارے میں مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "مواد", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "حسب ضرورت صفحہ میں ترمیم کریں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "منسلک منصوبے", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "منتخب کریں کہ کون سے منصوبے اور متعلقہ واقعات صفحہ پر دکھائے جا سکتے ہیں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "صفحہ کامیابی کے ساتھ بن گیا۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "صفحہ کامیابی کے ساتھ محفوظ ہو گیا۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "حسب ضرورت صفحہ محفوظ ہو گیا۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "نیویگیشن بار میں عنوان", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "حسب ضرورت صفحہ بنائیں | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "حسب ضرورت صفحہ بنائیں", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "کوئی نہیں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "صف<PERSON><PERSON> کی ترتیبات", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "حسب ضرورت صفحہ محفوظ کریں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "براہ کرم ایک علاقہ منتخب کریں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "من<PERSON><PERSON><PERSON> علاقہ", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "منتخب کردہ ٹیگز", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "سلگ میں صرف باقاعدہ، چھوٹے حروف (az)، نمبر (0-9) اور ہائفن (-) شامل ہوسکتے ہیں۔ پہلے اور آخری حروف ہائفن نہیں ہو سکتے۔ لگاتار ہائفنز (--) منع ہیں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "آپ کو ایک سلگ داخل کرنا ہوگا۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "عنوان", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "ہر زبان میں ایک عنوان درج کریں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "ایک عنوان درج کریں۔", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "حسب ضرورت صفحہ دیکھیں", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "بٹن", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "حسب ضرورت صفحہ میں ترمیم کریں | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "صفحہ کا مواد", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "ترمیم کریں۔", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "سنگل فیز پروجیکٹس کے لیے، اگر اختتامی تاریخ خالی ہے اور تفصیل نہیں بھری گئی ہے، تو پروجیکٹ کے صفحہ پر ٹائم لائن نہیں دکھائی جائے گی۔", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "آپ کے {pageSettingsLink}پر مبنی کوئی پروجیکٹ دستیاب نہیں ہے۔", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "اس پروجیکٹ میں کوئی ٹیگ یا ایریا فلٹر نہیں ہے، اس لیے کوئی پروجیکٹ نہیں دکھایا جائے گا۔", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "منصوبوں کی فہرست | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "صف<PERSON><PERSON> کی ترتیبات", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "منصوبوں کی فہرست", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "آپ کے {pageSettingsLink}کی بنیاد پر درج ذیل پروجیکٹس اس صفحہ پر دکھائے جائیں گے۔", "app.containers.Admin.PagesAndMenu.defaultTag": "ڈیفالٹ", "app.containers.Admin.PagesAndMenu.deleteButton": "حذف کریں۔", "app.containers.Admin.PagesAndMenu.editButton": "ترمیم کریں۔", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "کامیابی", "app.containers.Admin.PagesAndMenu.heroBannerError": "ہیرو بینر محفوظ نہیں ہو سکا", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "ہیرو کا بینر محفوظ ہو گیا۔", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "ہیرو بینر کو بچائیں۔", "app.containers.Admin.PagesAndMenu.homeTitle": "گھر", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "کم از کم ایک زبان کے لیے مواد فراہم کریں۔", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "آپ نیویگیشن بار میں صرف 5 آئٹمز تک شامل کر سکتے ہیں۔", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "صفحات اور مینو | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "navbar سے ہٹا دیں۔", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "ہیرو بینر کو محفوظ اور فعال کریں۔", "app.containers.Admin.PagesAndMenu.title": "صفحات اور مینو", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "کامیابی", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "مواد", "app.containers.Admin.PagesAndMenu.topInfoError": "سرفہرست معلومات والے حصے کو محفوظ نہیں کیا جا سکا", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "سرفہرست معلومات کا سیکشن محفوظ ہو گیا۔", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "سب سے اوپر معلومات سیکشن | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "سب سے اوپر معلومات سیکشن", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "محفوظ کریں اور ٹاپ انفارمیشن سیکشن کو فعال کریں۔", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "سب سے اوپر معلومات سیکشن کو محفوظ کریں", "app.containers.Admin.PagesAndMenu.viewButton": "دیکھیں", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "برادری", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "ایگزیکٹو خلاصہ", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "اعلی درجے کی شمولیت کے اشارے", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "مندرجہ ذیل حصے میں شمولیت کے اشاریوں کا خاکہ پیش کیا گیا ہے، جو مزید جامع اور نمائندہ شرکت کے پلیٹ فارم کو فروغ دینے کی طرف آپ کی ہماری پیش رفت کو نمایاں کرتا ہے۔", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "شرکاء", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "اعلی سطحی شرکت کے اشارے", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "مندرجہ ذیل سیکشن منتخب وقت کی حد کے لیے شرکت کے اہم اشاریوں کا خاکہ پیش کرتا ہے، جو مصروفیت کے رجحانات اور کارکردگی کے میٹرکس کا جائزہ فراہم کرتا ہے۔", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "پروجیکٹس", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "شائع شدہ منصوبوں", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "پلیٹ فارم رپورٹ", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "آپ کے پروجیکٹس", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "مندرجہ ذیل سیکشن عوامی طور پر نظر آنے والے پروجیکٹس کا ایک جائزہ فراہم کرتا ہے جو منتخب وقت کی حد، ان پروجیکٹس میں سب سے زیادہ استعمال شدہ طریقے، اور شرکت کی کل رقم سے متعلق میٹرکس کے ساتھ اوورلیپ ہوتے ہیں۔", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "رجسٹریشن کی ٹائم لائن", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "مسدود صارفین", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "مسدود صارفین کا نظم کریں۔", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "گروپ کو حذف کریں۔", "app.containers.Admin.Users.GroupsHeader.editGroup": "گروپ میں ترمیم کریں۔", "app.containers.Admin.Users.GroupsPanel.admins": "ایڈمنز", "app.containers.Admin.Users.GroupsPanel.allUsers": "رجسٹرڈ صارفین", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "گروپس", "app.containers.Admin.Users.GroupsPanel.managers": "پروجیکٹ مینیجرز", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "تفویض کردہ اشیاء", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "پلیٹ فارم پر رجسٹر ہونے والے تمام لوگوں اور تنظیموں کا ایک جائزہ حاصل کریں۔ مینوئل گروپس یا اسمارٹ گروپس میں صارفین کا انتخاب شامل کریں۔", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "دعوت نامہ زیر التواء ہے۔", "app.containers.Admin.Users.admin": "ایڈمن", "app.containers.Admin.Users.assign": "تفویض کریں۔", "app.containers.Admin.Users.assignedItems": "{name}کے لیے تفویض کردہ آئٹمز", "app.containers.Admin.Users.buyOneAditionalSeat": "ایک اضافی سیٹ خریدیں۔", "app.containers.Admin.Users.changeUserRights": "صارف کے حقوق کو تبدیل کریں۔", "app.containers.Admin.Users.confirm": "تصدیق کریں۔", "app.containers.Admin.Users.confirmAdminQuestion": "کیا آپ واقعی {name} پلیٹ فارم کے منتظم کے حقوق دینا چاہتے ہیں؟", "app.containers.Admin.Users.confirmNormalUserQuestion": "کیا آپ واقعی {name} کو ایک عام صارف کے طور پر سیٹ کرنا چاہتے ہیں؟", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "کیا آپ واقعی {name} کو ایک عام صارف کے طور پر سیٹ کرنا چاہتے ہیں؟ براہ کرم نوٹ کریں کہ وہ ان تمام پراجیکٹس اور فولڈرز کے مینیجر کے حقوق سے محروم ہو جائیں گے جو انہیں تصدیق پر تفویض کیے گئے ہیں۔", "app.containers.Admin.Users.deleteUser": "صارف کو حذف کریں۔", "app.containers.Admin.Users.email": "ای میل", "app.containers.Admin.Users.folder": "فولڈر", "app.containers.Admin.Users.folderManager": "فولڈر مینیجر", "app.containers.Admin.Users.helmetDescription": "منتظم میں صارف کی فہرست", "app.containers.Admin.Users.helmetTitle": "ایڈمن - صار<PERSON>ین کا ڈیش بورڈ", "app.containers.Admin.Users.inviteUsers": "صارفین کو مدعو کریں۔", "app.containers.Admin.Users.joined": "شامل ہو گئے۔", "app.containers.Admin.Users.lastActive": "آخری فعال", "app.containers.Admin.Users.name": "نام", "app.containers.Admin.Users.noAssignedItems": "کوئی تفویض کردہ آئٹمز نہیں ہیں۔", "app.containers.Admin.Users.options": "اختیارات", "app.containers.Admin.Users.permissionToBuy": "{name} منتظم کے حقوق دینے کے لیے، آپ کو 1 اضافی سیٹ خریدنی ہوگی۔", "app.containers.Admin.Users.platformAdmin": "پلیٹ فارم ایڈمن", "app.containers.Admin.Users.projectManager": "پروجیکٹ مینیجر", "app.containers.Admin.Users.reachedLimitMessage": "آپ اپنے پلان میں سیٹوں کی حد تک پہنچ گئے ہیں، {name} کے لیے 1 اضافی سیٹ شامل کی جائے گی۔", "app.containers.Admin.Users.registeredUser": "رجسٹرڈ صارف", "app.containers.Admin.Users.remove": "ہٹا دیں۔", "app.containers.Admin.Users.removeModeratorFrom": "صارف اس فولڈر کو ماڈریٹ کر رہا ہے جس کا یہ پروجیکٹ ہے۔ اس کی بجائے اسائنمنٹ کو \"{folderTitle}\" سے ہٹا دیں۔", "app.containers.Admin.Users.role": "کردار", "app.containers.Admin.Users.seeProfile": "پروفائل دیکھیں", "app.containers.Admin.Users.selectPublications": "پروجیکٹس یا فولڈرز کو منتخب کریں۔", "app.containers.Admin.Users.selectPublicationsPlaceholder": "تلاش کرنے کے لیے ٹائپ کریں۔", "app.containers.Admin.Users.setAsAdmin": "ایڈمن کے طور پر سیٹ کریں۔", "app.containers.Admin.Users.setAsNormalUser": "عام صارف کے طور پر سیٹ کریں۔", "app.containers.Admin.Users.setAsProjectModerator": "پروجیکٹ مینیجر کے طور پر سیٹ کریں۔", "app.containers.Admin.Users.setUserAsProjectModerator": "پروجیکٹ مینیجر کے طور پر {name} کو تفویض کریں۔", "app.containers.Admin.Users.userBlockModal.allDone": "سب ہو گیا", "app.containers.Admin.Users.userBlockModal.blockAction": "صارف کو مسدود کریں۔", "app.containers.Admin.Users.userBlockModal.blockInfo1": "اس صارف کا مواد اس کارروائی کے ذریعے نہیں ہٹایا جائے گا۔ اگر ضرورت ہو تو ان کے مواد کو معتدل کرنا نہ بھولیں۔", "app.containers.Admin.Users.userBlockModal.blocked": "مسدود", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "اس صارف کو {from}سے بلاک کر دیا گیا ہے۔ پابندی {to}تک رہے گی۔", "app.containers.Admin.Users.userBlockModal.cancel": "منسوخ کریں۔", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "کیا آپ واقعی {name}کو غیر مسدود کرنا چاہتے ہیں؟", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} {date}تک مسدود ہے۔", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 دن} other {{numberOfDays} دن}}", "app.containers.Admin.Users.userBlockModal.header": "صارف کو مسدود کریں۔", "app.containers.Admin.Users.userBlockModal.reasonLabel": "وجہ", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "یہ بلاک شدہ صارف کو مطلع کیا جائے گا۔", "app.containers.Admin.Users.userBlockModal.subtitle1": "منتخب صارف {daysBlocked}کے لیے پلیٹ فارم میں لاگ ان نہیں ہو سکے گا۔ اگر آپ اسے واپس کرنا چاہتے ہیں، تو آپ انہیں بلاک شدہ صارفین کی فہرست سے ان بلاک کر سکتے ہیں۔", "app.containers.Admin.Users.userBlockModal.unblockAction": "غیر مسدود کریں۔", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "ہاں، میں اس صارف کو غیر مسدود کرنا چاہتا ہوں۔", "app.containers.Admin.Users.userDeletionConfirmation": "اس صارف کو مستقل طور پر ہٹائیں؟", "app.containers.Admin.Users.userDeletionFailed": "اس صارف کو حذف کرتے وقت ایک خرابی پیش آگئی، براہ کرم دوبارہ کوشش کریں۔", "app.containers.Admin.Users.userDeletionProposalVotes": "یہ اس صارف کے کسی بھی ووٹ کو ان تجاویز پر بھی حذف کر دے گا جو ابھی ووٹنگ کے لیے کھلے ہیں۔", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.userInsights": "صارف کی بصیرت", "app.containers.Admin.Users.youCantDeleteYourself": "آپ صارف کے منتظم صفحہ کے ذریعے اپنا اکاؤنٹ حذف نہیں کر سکتے", "app.containers.Admin.Users.youCantUnadminYourself": "اب آپ بطور ایڈمن اپنا کردار ترک نہیں کر سکتے", "app.containers.Admin.communityMonitor.communityMonitorLabel": "کمیونٹی مانیٹر", "app.containers.Admin.communityMonitor.healthScore": "ہیلتھ سکور", "app.containers.Admin.communityMonitor.healthScoreDescription": "یہ اسکور ان تمام جذباتی پیمانے پر سوالات کی اوسط ہے جو شرکاء نے منتخب کردہ مدت کے لیے جواب دیے ہیں۔", "app.containers.Admin.communityMonitor.lastQuarter": "آخری سہ ماہی", "app.containers.Admin.communityMonitor.liveMonitor": "لائیو مانیٹر", "app.containers.Admin.communityMonitor.noResults": "اس مدت کے لیے کوئی نتیجہ نہیں نکلا۔", "app.containers.Admin.communityMonitor.noSurveyResponses": "کوئی سروے کے جوابات نہیں۔", "app.containers.Admin.communityMonitor.participants": "شرکاء", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "رپورٹس", "app.containers.Admin.communityMonitor.settings": "ترتیبات", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "کمیونٹی مانیٹر سروے گذارشات قبول کر رہا ہے۔", "app.containers.Admin.communityMonitor.settings.accessRights2": "رسائی کے حقوق", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "کسی صارف کے ایونٹ میں حاضری کے اندراج کے بعد، ووٹ جمع کروانے کے بعد، یا سروے جمع کروانے کے بعد پروجیکٹ کے صفحہ پر واپس آجاتا ہے۔", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "کمیونٹی مانیٹر مینیجر تمام کمیونٹی مانیٹر کی ترتیبات اور ڈیٹا تک رسائی اور ان کا نظم کر سکتے ہیں۔", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "کمیونٹی مانیٹر مینیجرز", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "مینیجر کمیونٹی مانیٹر کے سروے اور اجازتوں میں ترمیم کر سکتے ہیں، جوابی ڈیٹا دیکھ سکتے ہیں اور رپورٹس بنا سکتے ہیں۔", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "پہلے سے طے شدہ تعدد کی قدر 100% ہے۔", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "پاپ اپ فریکوئنسی (0 سے 100)", "app.containers.Admin.communityMonitor.settings.management2": "انتظام", "app.containers.Admin.communityMonitor.settings.popup": "پاپ اپ", "app.containers.Admin.communityMonitor.settings.popupDescription3": "ایک پاپ اپ وقتاً فوقتاً صارفین کو دکھایا جاتا ہے جو انہیں کمیونٹی مانیٹر سروے مکمل کرنے کی ترغیب دیتا ہے۔ آپ تعدد کو ایڈجسٹ کر سکتے ہیں جو ان صارفین کے فیصد کا تعین کرتی ہے جو نیچے بیان کردہ شرائط پوری ہونے پر تصادفی طور پر پاپ اپ دیکھیں گے۔", "app.containers.Admin.communityMonitor.settings.popupSettings": "پاپ اپ کی ترتیبات", "app.containers.Admin.communityMonitor.settings.preview": "پیش نظارہ", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "صارف نے پچھلے 3 مہینوں میں پہلے ہی سروے کو پُر نہیں کیا ہے۔", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "صارف نے پچھلے 3 مہینوں میں پہلے ہی پاپ اپ نہیں دیکھا ہے۔", "app.containers.Admin.communityMonitor.settings.save": "محفوظ کریں۔", "app.containers.Admin.communityMonitor.settings.saved": "محفوظ کیا گیا۔", "app.containers.Admin.communityMonitor.settings.settings": "ترتیبات", "app.containers.Admin.communityMonitor.settings.survey2": "سروے", "app.containers.Admin.communityMonitor.settings.surveySettings3": "عمومی ترتیبات", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "ہوم پیج یا حسب ضرورت صفحہ لوڈ کرنے پر۔", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "صارف کے تمام ڈیٹا کو گمنام کریں۔", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "ریکارڈ کیے جانے سے پہلے صارفین کی جانب سے سروے کے تمام ان پٹس کو گمنام رکھا جائے گا۔", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "صارفین کو اب بھی 'رسائی حقوق' کے تحت شرکت کے تقاضوں کی تعمیل کرنے کی ضرورت ہوگی۔ سروے ڈیٹا ایکسپورٹ میں صارف کا پروفائل ڈیٹا دستیاب نہیں ہوگا۔", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "کن حالات میں صارفین کے لیے پاپ اپ ظاہر ہو سکتا ہے؟", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "مینیجرز کون ہیں؟", "app.containers.Admin.communityMonitor.totalSurveyResponses": "سروے کے کل جوابات", "app.containers.Admin.communityMonitor.upsell.aiSummary": "AI کا خلاصہ", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "کمیونٹی مانیٹر کو فعال کریں۔", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "یہ خصوصیت آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے حکومتی کامیابی کے مینیجر یا منتظم سے بات کریں۔", "app.containers.Admin.communityMonitor.upsell.healthScore": "صحت کا سکور", "app.containers.Admin.communityMonitor.upsell.learnMore": "مزید جانیں", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "وقت کے ساتھ اسکور کریں۔", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "کمیونٹی مانیٹر رہائشیوں کے اعتماد، خدمات سے اطمینان، اور کمیونٹی کی زندگی کو مسلسل ٹریک کر کے آپ کو آگے رہنے میں مدد کرتا ہے۔", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "واضح سکور، طاقتور اقتباسات، اور سہ ماہی رپورٹ حاصل کریں جسے آپ ساتھیوں یا منتخب عہدیداروں کے ساتھ شیئر کر سکتے ہیں۔", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "پڑھنے میں آسان اسکور جو وقت کے ساتھ ساتھ تیار ہوتے ہیں۔", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "کلیدی رہائشی اقتباسات، جس کا خلاصہ AI نے کیا ہے۔", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "آپ کے شہر کے سیاق و سباق کے مطابق سوالات", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "رہائشیوں کو پلیٹ فارم پر تصادفی طور پر بھرتی کیا گیا۔", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "سہ ماہی پی ڈی ایف رپورٹس، اشتراک کے لیے تیار ہیں۔", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "سمجھیں کہ مسائل بڑھنے سے پہلے آپ کی کمیونٹی کیسا محسوس ہوتا ہے۔", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "شمار", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "ڈیسک ٹاپ یا دیگر", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "موبائل", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "گو<PERSON>ی", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "ڈیوائس کی اقسام", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "ڈیوائس کی قسم", "app.containers.Admin.earlyAccessLabel": "ابتدائی رسائی", "app.containers.Admin.earlyAccessLabelExplanation": "یہ ایک نئی جاری کردہ خصوصیت ہے جو ابتدائی رسائی میں دستیاب ہے۔", "app.containers.Admin.emails.addCampaign": "ای میل بنائیں", "app.containers.Admin.emails.addCampaignTitle": "ایک نیا ای میل بنائیں", "app.containers.Admin.emails.allParticipantsInProject": "پروجیکٹ میں تمام شرکاء", "app.containers.Admin.emails.allUsers": "رجسٹرڈ صارفین", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "خودکار ای میلز خود بخود بھیجے جاتے ہیں اور صارف کے اعمال سے متحرک ہوتے ہیں۔ آپ ان میں سے کچھ کو اپنے پلیٹ فارم کے تمام صارفین کے لیے بند کر سکتے ہیں۔ دیگر خودکار ای میلز کو بند نہیں کیا جا سکتا کیونکہ وہ آپ کے پلیٹ فارم کے مناسب کام کے لیے درکار ہیں۔", "app.containers.Admin.emails.automatedEmails": "<PERSON>و<PERSON><PERSON>ار ای میلز", "app.containers.Admin.emails.automatedEmailsDigest": "ای میل صرف اس صورت میں بھیجا جائے گا جب مواد موجود ہو۔", "app.containers.Admin.emails.automatedEmailsRecipients": "وہ صارفین جو یہ ای میل وصول کریں گے۔", "app.containers.Admin.emails.automatedEmailsTriggers": "واقعہ جو اس ای میل کو متحرک کرتا ہے۔", "app.containers.Admin.emails.changeRecipientsButton": "وصول کنندگان کو تبدیل کریں۔", "app.containers.Admin.emails.clickOnButtonForExamples": "ہمارے سپورٹ پیج پر اس ای میل کی مثالیں چیک کرنے کے لیے نیچے والے بٹن پر کلک کریں۔", "app.containers.Admin.emails.confirmSendHeader": "تمام صارفین کو ای میل کریں؟", "app.containers.Admin.emails.deleteButtonLabel": "حذف کریں۔", "app.containers.Admin.emails.draft": "مسودہ", "app.containers.Admin.emails.editButtonLabel": "ترمیم کریں۔", "app.containers.Admin.emails.editCampaignTitle": "مہم میں ترمیم کریں۔", "app.containers.Admin.emails.editDisabledTooltip2": "جلد آرہا ہے: اس ای میل میں فی الحال ترمیم نہیں کی جا سکتی ہے۔", "app.containers.Admin.emails.editRegion_button_text_multiloc": "بٹن کا متن", "app.containers.Admin.emails.editRegion_intro_multiloc": "تعارف", "app.containers.Admin.emails.editRegion_subject_multiloc": "موضوع", "app.containers.Admin.emails.editRegion_title_multiloc": "عنوان", "app.containers.Admin.emails.emailCreated": "ای میل کامیابی کے ساتھ ڈرافٹ میں بن گئی۔", "app.containers.Admin.emails.emailUpdated": "ای میل کامیابی کے ساتھ اپ ڈیٹ ہو گئی۔", "app.containers.Admin.emails.emptyCampaignsDescription": "اپنے شرکاء کو ای میلز بھیج کر ان سے آسانی سے جڑیں۔ منتخب کریں کہ کس سے رابطہ کرنا ہے اور اپنی مصروفیت کو ٹریک کرنا ہے۔", "app.containers.Admin.emails.emptyCampaignsHeader": "اپنا پہلا ای میل بھیجیں۔", "app.containers.Admin.emails.failed": "ناکام", "app.containers.Admin.emails.fieldBody": "پیغام", "app.containers.Admin.emails.fieldBodyError": "تمام زبانوں کے لیے ایک ای میل پیغام فراہم کریں۔", "app.containers.Admin.emails.fieldGroupContent": "ای میل مواد", "app.containers.Admin.emails.fieldReplyTo": "جوابات کو جانا چاہیے۔", "app.containers.Admin.emails.fieldReplyToEmailError": "درست فارمیٹ میں ایک ای میل پتہ فراہم کریں، مثال کے طور پر <EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "ایک ای میل ایڈریس فراہم کریں۔", "app.containers.Admin.emails.fieldReplyToTooltip": "آپ اپنے ای میل پر جوابات بھیجنے کا انتخاب کر سکتے ہیں۔", "app.containers.Admin.emails.fieldSender": "سے", "app.containers.Admin.emails.fieldSenderError": "ای میل بھیجنے والے کو فراہم کریں۔", "app.containers.Admin.emails.fieldSenderTooltip": "آپ فیصلہ کر سکتے ہیں کہ وصول کنندگان کو ای میل بھیجنے والے کے طور پر کون نظر آئے گا۔", "app.containers.Admin.emails.fieldSubject": "ای میل کا موضوع", "app.containers.Admin.emails.fieldSubjectError": "تمام زبانوں کے لیے ای میل کا موضوع فراہم کریں۔", "app.containers.Admin.emails.fieldSubjectTooltip": "یہ ای میل کی سبجیکٹ لائن میں اور صارف کے ان باکس کے جائزہ میں دکھایا جائے گا۔ اسے واضح اور پرکشش بنائیں۔", "app.containers.Admin.emails.fieldTo": "کو", "app.containers.Admin.emails.fieldToTooltip": "آپ ان صارف گروپوں کو منتخب کر سکتے ہیں جنہیں آپ کا ای میل موصول ہوگا۔", "app.containers.Admin.emails.formSave": "ڈرافٹ کے طور پر محفوظ کریں۔", "app.containers.Admin.emails.formSaveAsDraft": "ڈرافٹ کے طور پر محفوظ کریں۔", "app.containers.Admin.emails.from": "منجانب:", "app.containers.Admin.emails.groups": "گروپس", "app.containers.Admin.emails.helmetDescription": "یوزر گروپس کو دستی ای میلز بھیجیں اور خودکار مہمات کو چالو کریں۔", "app.containers.Admin.emails.nameVariablesInfo2": "آپ متغیرات کا استعمال کرتے ہوئے شہریوں سے براہ راست بات کر سکتے ہیں {firstName} {lastName}۔ مثال کے طور پر \"پیارے {firstName} {lastName}، ...\"", "app.containers.Admin.emails.previewSentConfirmation": "ایک پیش نظارہ ای میل آپ کے ای میل پتے پر بھیج دیا گیا ہے۔", "app.containers.Admin.emails.previewTitle": "پیش نظارہ", "app.containers.Admin.emails.regionMultilocError": "براہ کرم تمام زبانوں کے لیے ایک قدر فراہم کریں۔", "app.containers.Admin.emails.seeEmailHereText": "جیسے ہی اس قسم کا ای میل بھیجا جائے گا آپ اسے یہاں چیک کر سکیں گے۔", "app.containers.Admin.emails.send": "بھیجیں۔", "app.containers.Admin.emails.sendNowButton": "ابھی بھیجیں۔", "app.containers.Admin.emails.sendTestEmailButton": "مجھے ایک ٹیسٹ ای میل بھیجیں۔", "app.containers.Admin.emails.sendTestEmailTooltip2": "جب آپ اس لنک پر کلک کریں گے، ایک ٹیسٹ ای میل صرف آپ کے ای میل ایڈریس پر بھیجا جائے گا۔ یہ آپ کو یہ چیک کرنے کی اجازت دیتا ہے کہ 'حقیقی زندگی' میں ای میل کیسی دکھتی ہے۔", "app.containers.Admin.emails.senderRecipients": "بھیجنے والے اور وصول کنندگان", "app.containers.Admin.emails.sending": "بھیج رہا ہے۔", "app.containers.Admin.emails.sent": "بھیجا", "app.containers.Admin.emails.sentToUsers": "یہ صارفین کو بھیجی گئی ای میلز ہیں۔", "app.containers.Admin.emails.subject": "موضوع:", "app.containers.Admin.emails.supportButtonLabel": "ہمارے سپورٹ پیج پر مثالیں دیکھیں", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "کو:", "app.containers.Admin.emails.toAllUsers": "کیا آپ یہ ای میل تمام رجسٹرڈ صارفین کو بھیجنا چاہتے ہیں؟", "app.containers.Admin.emails.viewExample": "دیکھیں", "app.containers.Admin.ideas.import": "درآمد کریں۔", "app.containers.Admin.inspirationHub.AllProjects": "تمام منصوبے", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "کمیونٹی مانیٹر", "app.containers.Admin.inspirationHub.DocumentAnnotation": "دستاویز کی تشریح", "app.containers.Admin.inspirationHub.ExternalSurvey": "بیرونی سروے", "app.containers.Admin.inspirationHub.Filters.Country": "ملک", "app.containers.Admin.inspirationHub.Filters.Method": "طریقہ", "app.containers.Admin.inspirationHub.Filters.Search": "تلاش کریں۔", "app.containers.Admin.inspirationHub.Filters.Topic": "موضوع", "app.containers.Admin.inspirationHub.Filters.population": "آبادی", "app.containers.Admin.inspirationHub.Highlighted": "نمایاں کیا گیا۔", "app.containers.Admin.inspirationHub.Ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Information": "معلومات", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "پن کیے ہوئے پروجیکٹس دیکھنے کے لیے براہ کرم ایک ملک منتخب کریں۔", "app.containers.Admin.inspirationHub.PinnedProjects.country": "ملک", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "اس ملک کے لیے کوئی پن شدہ پروجیکٹ نہیں ملا۔ دوسرے ممالک کے لیے پن کیے ہوئے پروجیکٹس دیکھنے کے لیے ملک کو تبدیل کریں۔", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "مزید پن لگائے ہوئے پروجیکٹس دیکھنے کے لیے ملک کو تبدیل کریں۔", "app.containers.Admin.inspirationHub.Poll": "رائے شماری", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "کھلا ختم ہوا۔", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "مزید پڑھیں...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "مرحلہ {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "اگر آپ نہیں چاہتے ہیں کہ آپ کا پروجیکٹ انسپیریشن ہب میں شامل ہو تو اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.Admin.inspirationHub.Proposals": "تجاو<PERSON>ز", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "ترتیب دیں", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "شرکاء (سب سے کم پہلے)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "شرکاء (سب سے زیادہ پہلے)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "تاریخ آغاز (سب سے قدیم پہلے)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "تاریخ آغاز (سب سے تازہ ترین)", "app.containers.Admin.inspirationHub.Survey": "سروے", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "دنیا بھر کے بہترین پروجیکٹس کی تیار کردہ فہرست۔", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "ساتھی پریکٹیشنرز سے بات کریں، اور ان سے سیکھیں۔", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "طریقہ، شہر کے سائز اور ملک کے لحاظ سے فلٹر کریں۔", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Inspiration Hub کو فعال کریں۔", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "یہ خصوصیت آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے حکومتی کامیابی کے مینیجر یا منتظم سے بات کریں۔", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "https://support.govocal.com/en/articles/11093736-using-the-inspiration-hub", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "مزید جانیں", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "The Inspiration Hub آپ کو دنیا بھر میں Go Vocal پلیٹ فارمز پر غیر معمولی شرکت کے پروجیکٹس کی تیار کردہ فیڈ سے جوڑتا ہے۔ جانیں کہ دوسرے شہر کیسے کامیاب پروجیکٹ چلاتے ہیں اور دوسرے پریکٹیشنرز سے بات کریں۔", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "جمہوریت کے علمبرداروں کے نیٹ ورک میں شامل ہوں۔", "app.containers.Admin.inspirationHub.Volunteering": "رضاکارانہ", "app.containers.Admin.inspirationHub.Voting": "ووٹنگ", "app.containers.Admin.inspirationHub.commonGround": "مشتر<PERSON>ہ زمین", "app.containers.Admin.inspirationHub.filters": "فل<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.resetFilters": "فلٹرز کو دوبارہ ترتیب دیں۔", "app.containers.Admin.inspirationHub.seemsLike": "ایسا لگتا ہے کہ مزید پروجیکٹس نہیں ہیں۔ {filters}کو تبدیل کرنے کی کوشش کریں۔", "app.containers.Admin.messaging.automated.editModalTitle": "مہم کے شعبوں میں ترمیم کریں۔", "app.containers.Admin.messaging.automated.variablesToolTip": "آپ اپنے پیغام میں درج ذیل متغیرات استعمال کر سکتے ہیں:", "app.containers.Admin.messaging.helmetTitle": "پیغام رسانی", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "یہ ویجیٹ ہر صارف کے منصوبوں کو دکھاتا ہے <b>ان کی پیروی کی ترجیحات کی بنیاد پر</b>۔ اس میں وہ پروجیکٹس شامل ہیں جن کی وہ پیروی کرتے ہیں، نیز وہ پروجیکٹ جہاں وہ ان پٹس کی پیروی کرتے ہیں، اور ان موضوعات یا شعبوں سے متعلق پروجیکٹس جن میں وہ دلچسپی رکھتے ہیں۔", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "یہ ویجیٹ صارف کو صرف اس صورت میں دکھایا جائے گا جب ایسے منصوبے ہوں جہاں وہ حصہ لے سکیں۔ اگر آپ کو یہ پیغام نظر آتا ہے تو اس کا مطلب ہے کہ آپ (ایڈمن) اس وقت کسی پروجیکٹ میں حصہ نہیں لے سکتے۔ یہ پیغام اصلی ہوم پیج پر نظر نہیں آئے گا۔", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "شرکت کے لیے کھلا ہے۔", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "یہ ویجیٹ ان پروجیکٹس کی نمائش کرے گا جہاں صارف فی الحال <b>حصہ لینے کے لیے ایک کارروائی کر سکتا ہے</b>۔", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "عنوان", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "آپ کے لیے", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "یہ ویجیٹ صارف کو صرف اس صورت میں دکھایا جائے گا جب ان کی پیروی کی ترجیحات کی بنیاد پر ان کے لیے متعلقہ منصوبے ہوں۔ اگر آپ کو یہ پیغام نظر آتا ہے تو اس کا مطلب ہے کہ آپ (ایڈمن) اس وقت کسی چیز کی پیروی نہیں کر رہے ہیں۔ یہ پیغام اصلی ہوم پیج پر نظر نہیں آئے گا۔", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "آئٹمز کی پیروی کی۔", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "محف<PERSON><PERSON> شدہ", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "کے لحاظ سے فلٹر کریں۔", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "ختم", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "مکمل اور محفوظ شدہ", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "کوئی ڈیٹا دستیاب نہیں ہے۔", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "یہ ویجیٹ <b>منصوبوں کو دکھاتا ہے جو مکمل اور/یا محفوظ شدہ ہیں۔</b>\"ختم\" میں وہ منصوبے بھی شامل ہیں جو آخری مرحلے میں ہیں، اور جہاں آخری مرحلہ ایک رپورٹ ہے۔", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "تکمیل شدہ منصوبے", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "تم نے کہا ہم نے...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "تمام زبانوں کے لیے ایک نام فراہم کریں۔", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "پروجیکٹ خالی نہیں ہو سکتا", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "نام", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "پروجیکٹ", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "نتی<PERSON><PERSON> خیز URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "محفوظ کریں۔", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "پروجیکٹ شامل کریں۔", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "نیویگیشن بار صرف وہ پروجیکٹ دکھائے گا جن تک صارفین کو رسائی حاصل ہے۔", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "یہ ویجیٹ ہوم پیج پر تب ہی نظر آئے گا جب کمیونٹی مانیٹر جوابات قبول کر رہا ہو۔", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "کمیونٹی مانیٹر", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "تفصیل", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "بٹن", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "عنوان", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "اہم:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "جذباتی سروے کے سوال کی مثال", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "کوئی اختتامی تاریخ نہیں۔", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "کیروسل کو چھوڑنے کے لیے escape دبائیں۔", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "پروجیکٹس اور فولڈرز (وراثت)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "پروجیکٹس کا عنوان", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} فی الحال کام کر رہا ہے۔", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "بٹن کا متن", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "ابھی شرکت کریں!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "تفصیل", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "فولڈر", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "براہ کرم ایک پروجیکٹ یا فولڈر منتخب کریں۔", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "پروجیکٹ یا فولڈر منتخب کریں۔", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "اوتار دکھائیں۔", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "اسپاٹ لائٹ", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "عنوان", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "{days} دنوں میں شروع ہو رہا ہے۔", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "{weeks} ہفتوں میں شروع ہو رہا ہے۔", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} دن پہلے", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} ہفتے پہلے", "app.containers.Admin.project.Campaigns.campaignFrom": "منجانب:", "app.containers.Admin.project.Campaigns.campaignTo": "کو:", "app.containers.Admin.project.Campaigns.customEmails": "حسب ضرورت ای میلز", "app.containers.Admin.project.Campaigns.customEmailsDescription": "حسب ضرورت ای میلز بھیجیں اور اعدادوشمار چیک کریں۔", "app.containers.Admin.project.Campaigns.noAccess": "ہمیں افسوس ہے، لیکن ایسا لگتا ہے کہ آپ کو ای میلز کے سیکشن تک رسائی نہیں ہے۔", "app.containers.Admin.project.emails.addCampaign": "ای میل بنائیں", "app.containers.Admin.project.emails.addCampaignTitle": "نئی مہم", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "پروجیکٹ کے سبھی {participants} اور پیروکار", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "اس میں وہ رجسٹرڈ صارفین شامل ہیں جنہوں نے پروجیکٹ میں کوئی کارروائی کی۔ غیر رجسٹرڈ یا گمنام صارفین شامل نہیں ہیں۔", "app.containers.Admin.project.emails.dateSent": "بھیجنے کی تاریخ", "app.containers.Admin.project.emails.deleteButtonLabel": "حذف کریں۔", "app.containers.Admin.project.emails.draft": "مسودہ", "app.containers.Admin.project.emails.editButtonLabel": "ترمیم کریں۔", "app.containers.Admin.project.emails.editCampaignTitle": "مہم میں ترمیم کریں۔", "app.containers.Admin.project.emails.emptyCampaignsDescription": "اپنے شرکاء کو ای میلز بھیج کر ان سے آسانی سے جڑیں۔ منتخب کریں کہ کس سے رابطہ کرنا ہے اور اپنی مصروفیت کو ٹریک کرنا ہے۔", "app.containers.Admin.project.emails.emptyCampaignsHeader": "اپنا پہلا ای میل بھیجیں۔", "app.containers.Admin.project.emails.failed": "ناکام", "app.containers.Admin.project.emails.fieldBody": "ای میل پیغام", "app.containers.Admin.project.emails.fieldBodyError": "تمام زبانوں کے لیے ایک ای میل پیغام فراہم کریں۔", "app.containers.Admin.project.emails.fieldReplyTo": "جوابات کو جانا چاہیے۔", "app.containers.Admin.project.emails.fieldReplyToEmailError": "درست فارمیٹ میں ایک ای میل پتہ فراہم کریں، مثال کے طور پر <EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "ایک ای میل ایڈریس فراہم کریں۔", "app.containers.Admin.project.emails.fieldReplyToTooltip": "منتخب کریں کہ کون سا ای میل پتہ آپ کے ای میل پر صارفین سے براہ راست جوابات وصول کرے۔", "app.containers.Admin.project.emails.fieldSender": "سے", "app.containers.Admin.project.emails.fieldSenderError": "ای میل بھیجنے والے کو فراہم کریں۔", "app.containers.Admin.project.emails.fieldSenderTooltip": "منتخب کریں کہ صارفین کس کو ای میل بھیجنے والے کے طور پر دیکھیں گے۔", "app.containers.Admin.project.emails.fieldSubject": "ای میل کا موضوع", "app.containers.Admin.project.emails.fieldSubjectError": "تمام زبانوں کے لیے ای میل کا موضوع فراہم کریں۔", "app.containers.Admin.project.emails.fieldSubjectTooltip": "یہ ای میل کی سبجیکٹ لائن میں اور صارف کے ان باکس کے جائزہ میں دکھایا جائے گا۔ اسے واضح اور پرکشش بنائیں۔", "app.containers.Admin.project.emails.fieldTo": "کو", "app.containers.Admin.project.emails.formSave": "ڈرافٹ کے طور پر محفوظ کریں۔", "app.containers.Admin.project.emails.from": "منجانب:", "app.containers.Admin.project.emails.helmetDescription": "پروجیکٹ کے شرکاء کو دستی ای میلز بھیجیں۔", "app.containers.Admin.project.emails.infoboxAdminText": "پروجیکٹ میسجنگ ٹیب سے آپ صرف پروجیکٹ کے تمام شرکاء کو ای میل کر سکتے ہیں۔ دیگر شرکاء یا صارفین کے ذیلی سیٹوں کو ای میل کرنے کے لیے {link} ٹیب پر جائیں۔", "app.containers.Admin.project.emails.infoboxLinkText": "پلیٹ فارم میسجنگ", "app.containers.Admin.project.emails.infoboxModeratorText": "پروجیکٹ میسجنگ ٹیب سے آپ صرف پروجیکٹ کے تمام شرکاء کو ای میل کر سکتے ہیں۔ منتظمین پلیٹ فارم میسجنگ ٹیب کے ذریعے دیگر شرکاء یا صارفین کے ذیلی سیٹوں کو ای میلز بھیج سکتے ہیں۔", "app.containers.Admin.project.emails.message": "پیغام", "app.containers.Admin.project.emails.nameVariablesInfo2": "آپ متغیرات کا استعمال کرتے ہوئے شہریوں سے براہ راست بات کر سکتے ہیں {firstName} {lastName}۔ مثال کے طور پر \"پیارے {firstName} {lastName}، ...\"", "app.containers.Admin.project.emails.participants": "شرکاء", "app.containers.Admin.project.emails.previewSentConfirmation": "ایک پیش نظارہ ای میل آپ کے ای میل پتے پر بھیج دیا گیا ہے۔", "app.containers.Admin.project.emails.previewTitle": "پیش نظارہ", "app.containers.Admin.project.emails.projectParticipants": "پروجیکٹ کے شرکاء", "app.containers.Admin.project.emails.recipients": "وصول کنندگان", "app.containers.Admin.project.emails.send": "بھیجیں۔", "app.containers.Admin.project.emails.sendTestEmailButton": "ایک پیش نظارہ بھیجیں۔", "app.containers.Admin.project.emails.sendTestEmailTooltip": "یہ ڈرافٹ ای میل اس ای میل ایڈریس پر بھیجیں جس سے آپ لاگ ان ہیں، یہ چیک کرنے کے لیے کہ یہ 'حقیقی زندگی' میں کیسا لگتا ہے۔", "app.containers.Admin.project.emails.senderRecipients": "بھیجنے والے اور وصول کنندگان", "app.containers.Admin.project.emails.sending": "بھیج رہا ہے۔", "app.containers.Admin.project.emails.sent": "بھیجا", "app.containers.Admin.project.emails.sentToUsers": "یہ صارفین کو بھیجی گئی ای میلز ہیں۔", "app.containers.Admin.project.emails.status": "حیثیت", "app.containers.Admin.project.emails.subject": "موضوع:", "app.containers.Admin.project.emails.to": "کو:", "app.containers.Admin.project.messaging.helmetTitle": "پیغام رسانی", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "یہ تصویر فولڈر کارڈ کا حصہ ہے۔ وہ کارڈ جو فولڈر کا خلاصہ کرتا ہے اور مثال کے طور پر ہوم پیج پر دکھایا جاتا ہے۔ تجویز کردہ تصویری قراردادوں کے بارے میں مزید معلومات کے لیے، {supportPageLink}۔", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "یہ تصویر فولڈر کے صفحے کے اوپری حصے میں دکھائی گئی ہے۔ تجویز کردہ تصویری قراردادوں کے بارے میں مزید معلومات کے لیے، {supportPageLink}۔", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.cizenlab.co/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "ہمارے سپورٹ سینٹر پر جائیں۔", "app.containers.Admin.projects.all.askPersonalData3": "نام اور ای میل کے لیے فیلڈز شامل کریں۔", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "کیلنڈر ویو کو فعال کریں۔", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "یہ خصوصیت آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے حکومتی کامیابی کے مینیجر یا منتظم سے بات کریں۔", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "مزید جانیں", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "ہمارے کیلنڈر کے منظر میں اپنے پروجیکٹ کی ٹائم لائنز کا بصری جائزہ حاصل کریں۔ فوری طور پر شناخت کریں کہ کون سے منصوبے اور مراحل جلد شروع یا ختم ہو رہے ہیں اور کارروائی کی ضرورت ہے۔", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "سمجھیں کہ کیا ہو رہا ہے اور کب ہو رہا ہے۔", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "تمام سوالات پی ڈی ایف پر دکھائے گئے ہیں۔ تاہم، درج ذیل کو فی الحال FormSync کے ذریعے درآمد کرنے کے لیے تعاون یافتہ نہیں ہے: امیجز، ٹیگز اور فائل اپ لوڈ۔", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "تمام سوالات پی ڈی ایف پر دکھائے گئے ہیں۔ تاہم، درج ذیل کو فی الحال FormSync کے ذریعے درآمد کرنے کے لیے تعاون یافتہ نہیں ہے: نقشہ سازی کے سوالات (ڈراپ پن، ڈرا روٹ اور ڈرا ایریا)، درجہ بندی کے سوالات، میٹرکس کے سوالات اور فائل اپ لوڈ کے سوالات۔", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "فارم کا اختتام", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "فارم کا آغاز", "app.containers.Admin.projects.all.components.archived": "محف<PERSON><PERSON> شدہ", "app.containers.Admin.projects.all.components.draft": "مسودہ", "app.containers.Admin.projects.all.components.manageButtonLabel": "ترمیم کریں۔", "app.containers.Admin.projects.all.copyProjectButton": "پروجیکٹ کاپی کریں۔", "app.containers.Admin.projects.all.copyProjectError": "اس پروجیکٹ کو کاپی کرنے میں ایک خامی تھی، براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.Admin.projects.all.customiseEnd": "فارم کے آخر کو حسب ضرورت بنائیں۔", "app.containers.Admin.projects.all.customiseStart": "فارم کے آغاز کو حسب ضرورت بنائیں۔", "app.containers.Admin.projects.all.deleteFolderButton1": "فولڈر کو حذف کریں۔", "app.containers.Admin.projects.all.deleteFolderConfirm": "کیا آپ واقعی اس فولڈر کو حذف کرنا چاہتے ہیں؟ فولڈر کے اندر موجود تمام پراجیکٹس کو بھی حذف کر دیا جائے گا۔ اس کارروائی کو کالعدم نہیں کیا جا سکتا۔", "app.containers.Admin.projects.all.deleteFolderError": "اس فولڈر کو ہٹانے میں ایک مسئلہ تھا۔ براہ کرم دوبارہ کوشش کریں۔", "app.containers.Admin.projects.all.deleteProjectButtonFull": "پروجیکٹ کو حذف کریں۔", "app.containers.Admin.projects.all.deleteProjectConfirmation": "کیا آپ واقعی اس پروجیکٹ کو حذف کرنا چاہتے ہیں؟ اسے کالعدم نہیں کیا جا سکتا۔", "app.containers.Admin.projects.all.deleteProjectError": "اس پروجیکٹ کو حذف کرنے میں ایک خامی تھی، براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.Admin.projects.all.exportAsPDF1": "پی ڈی ایف فارم ڈاؤن لوڈ کریں۔", "app.containers.Admin.projects.all.itIsAlsoPossible1": "آپ آن لائن اور آف لائن جوابات کو یکجا کر سکتے ہیں۔ آف لائن جوابات اپ لوڈ کرنے کے لیے، اس پروجیکٹ کے 'ان پٹ مینیجر' ٹیب پر جائیں، اور 'درآمد کریں' پر کلک کریں۔", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "آپ آن لائن اور آف لائن جوابات کو یکجا کر سکتے ہیں۔ آف لائن جوابات اپ لوڈ کرنے کے لیے، اس پروجیکٹ کے 'سروے' ٹیب پر جائیں، اور 'درآمد کریں' پر کلک کریں۔", "app.containers.Admin.projects.all.logicNotInPDF": "ڈاؤن لوڈ کردہ پی ڈی ایف میں سروے کی منطق کی عکاسی نہیں کی جائے گی۔ کاغذی جواب دہندگان سروے کے تمام سوالات دیکھیں گے۔", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "فولڈرز تلاش کریں۔", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "تمام فولڈرز لوڈ ہو چکے ہیں۔", "app.containers.Admin.projects.all.new.Folders.Table.folder": "فولڈر", "app.containers.Admin.projects.all.new.Folders.Table.managers": "مین<PERSON><PERSON><PERSON>ز", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# پروجیکٹ} other {# پروجیکٹ}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "حیثیت", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "پروجیکٹ شروع ہونے کی تاریخ", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "دریافت کرنے کی صلاحیت", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "پوشیدہ", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "عوا<PERSON>ی", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "فولڈرز", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "موجودہ مرحلے میں شرکت کے طریقہ سے فلٹر کریں۔", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "معلومات", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "شرکت کا طریقہ", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "دستاویز کی تشریح", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "مشتر<PERSON>ہ زمین", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "سروے", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "رائے شماری", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "تجاو<PERSON>ز", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "رضاکارانہ", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "ووٹنگ", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "ڈیٹا اکٹھا کرنا", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "مطلع کرنا", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "شروع نہیں ہوا۔", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "شرکت کی حالت", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "ماضی", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "زیر التواء منظوری", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "پروجیکٹس تلاش کریں۔", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "حروف تہجی کے لحاظ سے (az)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "حروف تہجی کے لحاظ سے (za)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "مینیجر", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "جلد شروع یا ختم ہونے والا مرحلہ", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc": "حال ہی میں بنائے گئے (نئے پرانے)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc": "حال ہی میں بنایا گیا (پرانا نیا)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "حال ہی میں دیکھا گیا۔", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "حیثیت", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "ایڈمنز", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "گروپس", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "مرئیت", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "عوا<PERSON>ی", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "فلٹر شامل کریں۔", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "صاف", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "شامل کرنے کے لیے مزید فلٹرز نہیں ہیں۔", "app.containers.Admin.projects.all.new.Projects.Table.admins": "ایڈمنز", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "تمام منصوبے لوڈ ہو چکے ہیں۔", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "کوئی بھی", "app.containers.Admin.projects.all.new.Projects.Table.archived": "محف<PERSON><PERSON> شدہ", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "موجودہ مرحلہ", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d بائیں", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d شروع کرنے کے لیے", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "دریافت کی اہلیت:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "مسودہ", "app.containers.Admin.projects.all.new.Projects.Table.end": "ختم", "app.containers.Admin.projects.all.new.Projects.Table.ended": "ختم ہوا۔", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "آج ختم ہو رہا ہے۔", "app.containers.Admin.projects.all.new.Projects.Table.groups": "گروپس", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "پوشیدہ", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "مزید…لوڈ ہو رہا ہے۔", "app.containers.Admin.projects.all.new.Projects.Table.manager": "مینیجر", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}ماہ باقی", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "شروع کرنے کے لیے {months}mo", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "اگلا مرحلہ:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "تفویض نہیں کیا گیا۔", "app.containers.Admin.projects.all.new.Projects.Table.phase": "مر<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "پری لانچ", "app.containers.Admin.projects.all.new.Projects.Table.project": "پروجیکٹ", "app.containers.Admin.projects.all.new.Projects.Table.public": "عوا<PERSON>ی", "app.containers.Admin.projects.all.new.Projects.Table.published": "شائع شدہ", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "مزید لوڈ کرنے کے لیے نیچے سکرول کریں۔", "app.containers.Admin.projects.all.new.Projects.Table.start": "شروع کریں۔", "app.containers.Admin.projects.all.new.Projects.Table.status": "حیثیت", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "حیثیت:", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "یہ کالم کیش شدہ شریک ڈیٹا استعمال کرتا ہے۔ تازہ ترین نمبرز دیکھنے کے لیے، پروجیکٹ کے \"شرکاء\" ٹیب کو چیک کریں۔", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "مرئیت", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "مرئیت:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} گروپس", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} مینی<PERSON><PERSON>ز", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}y بائیں", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "شروع کرنے کے لیے {years}y", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "موجودہ مرحلہ: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} دن باقی ہیں۔", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "فولڈر: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "کوئی موجودہ مرحلہ نہیں۔", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "کوئی اختتامی تاریخ نہیں۔", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "کوئی مرحلہ نہیں۔", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "مرحلہ {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "مراحل:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "پروجیکٹ", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "تاریخ آغاز: {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "منصوبوں کو ترتیب دیں۔", "app.containers.Admin.projects.all.new.calendar": "کیلنڈر", "app.containers.Admin.projects.all.new.folders": "فولڈرز", "app.containers.Admin.projects.all.new.projects": "پروجیکٹس", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "ٹائم لائن لوڈ کرنے میں ناکام۔", "app.containers.Admin.projects.all.new.timeline.noEndDay": "پروجیکٹ کی کوئی آخری تاریخ نہیں ہے۔", "app.containers.Admin.projects.all.new.timeline.project": "پروجیکٹ", "app.containers.Admin.projects.all.notes": "نوٹس", "app.containers.Admin.projects.all.personalDataExplanation5": "یہ آپشن برآمد شدہ پی ڈی ایف میں پہلا نام، آخری نام، اور ای میل فیلڈز شامل کر دے گا۔ کاغذی فارم اپ لوڈ کرنے پر، ہم اس ڈیٹا کو آف لائن سروے کے جواب دہندہ کے لیے ایک اکاؤنٹ خودکار بنانے کے لیے استعمال کریں گے۔", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "AI کا خلاصہ", "app.containers.Admin.projects.project.analysis.Comments.comments": "تبصرے", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "تبصرے کا خلاصہ دستیاب ہوتا ہے جب 5 یا زیادہ تبصرے ہوتے ہیں۔", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "تبصرے کا خلاصہ کریں۔", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {تازہ کریں} =1 {1 نیا تبصرہ} other {# نئے تبصرے}}", "app.containers.Admin.projects.project.analysis.aiSummary": "AI کا خلاصہ", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "یہ AI سے تیار کردہ مواد ہے۔ یہ 100% درست نہیں ہو سکتا۔ براہ کرم جائزہ لیں اور درستگی کے لیے اصل آدانوں کے ساتھ کراس حوالہ دیں۔ آگاہ رہیں کہ اگر منتخب کردہ آدانوں کی تعداد کم کردی جاتی ہے تو درستگی بہتر ہونے کا امکان ہے۔", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "ای میل اطلاعات صرف شرکاء کو بھیجی جاتی ہیں۔", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "پوشیدہ", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "سرچ انجنوں کے ذریعہ انڈیکس نہیں کیا گیا ہے۔", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "ہوم پیج یا ویجٹ پر نظر نہیں آتا", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "صرف براہ راست URL کے ذریعے قابل رسائی", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "عوا<PERSON>ی", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "منتخب کریں کہ یہ پروجیکٹ کتنا قابل دریافت ہے۔", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "یہ پروجیکٹ ہر اس شخص کو نظر آتا ہے جسے رسائی حاصل ہے، اور ہوم پیج پر اور ویجٹس میں ظاہر ہوگا۔", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "یہ پروجیکٹ وسیع تر عوام سے پوشیدہ رہے گا، اور صرف ان لوگوں کو نظر آئے گا جن کے پاس لنک ہے۔", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "کون اس منصوبے کو تلاش کر سکتا ہے؟", "app.containers.Admin.projects.project.ideas.analysisAction1": "AI تجزیہ کھولیں۔", "app.containers.Admin.projects.project.ideas.analysisText2": "AI سے چلنے والے خلاصے دریافت کریں اور انفرادی گذارشات دیکھیں۔", "app.containers.Admin.projects.project.ideas.importInputs": "درآمد کریں۔", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "رپورٹ بنانے کے بعد، مرحلہ شروع ہونے کے بعد آپ اسے عوام کے ساتھ شیئر کرنے کا انتخاب کر سکتے ہیں۔", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "معلومات کے اشتراک کے لیے مزید پیچیدہ صفحہ بنائیں", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "اس کے لیے ایک رپورٹ بنائیں:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "ایک رپورٹ بنائیں", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "پچھلے مرحلے کے لیے ایک رپورٹ بنائیں، یا شروع سے شروع کریں۔", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "یہ رپورٹ پبلک نہیں ہے۔ اسے عوامی بنانے کے لیے، \"مرئی\" ٹوگل کو فعال کریں۔", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "یہ مرحلہ شروع ہو چکا ہے لیکن رپورٹ ابھی منظر عام پر نہیں آئی۔ اسے عوامی بنانے کے لیے، \"مرئی\" ٹوگل کو فعال کریں۔", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "فیز ٹیمپلیٹ کے ساتھ شروع کریں۔", "app.containers.Admin.projects.project.information.ReportTab.report": "رپورٹ", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "ماضی کے سروے یا آئیڈییشن مرحلے کے نتائج کا اشتراک کریں۔", "app.containers.Admin.projects.project.information.ReportTab.visible": "نظر آنے والا", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "مرحلہ شروع ہوتے ہی یہ رپورٹ پبلک کر دی جائے گی۔ اسے عوامی نہ بنانے کے لیے، \"مرئی\" ٹوگل کو غیر فعال کریں۔", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "یہ رپورٹ فی الحال پبلک ہے۔ اسے عوامی نہ بنانے کے لیے، \"مرئی\" ٹوگل کو غیر فعال کریں۔", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "کیا آپ واقعی اس رپورٹ کو حذف کرنا چاہتے ہیں؟ اس کارروائی کو کالعدم نہیں کیا جا سکتا۔", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "مرحلے میں شامل کریں۔", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "جاری رکھنے سے پہلے آپ کو اس کے لیے رضامندی کی ضرورت ہے۔", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "فارم یہاں سے ڈاؤن لوڈ کیا جا سکتا ہے۔", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "اپ لوڈ کردہ فارم کو \"ذاتی ڈیٹا\" سیکشن کے ساتھ بنایا گیا تھا۔", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "فارم کی زبان", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "میں اس کے ذریعے Google Cloud Form Parser کا استعمال کرتے ہوئے اس فائل پر کارروائی کرنے کی رضامندی دیتا ہوں۔", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "ایکسل فائل درآمد کریں۔", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "جاری رکھنے کے لیے براہ کرم ایک فائل اپ لوڈ کریں۔", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "ٹیمپلیٹ یہاں سے ڈاؤن لوڈ کیا جا سکتا ہے۔", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "اپ لوڈ کریں۔", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "ایک مکمل شدہ <b>Excel فائل</b> (.xlsx) اپ لوڈ کریں۔ اسے اس پروجیکٹ کے لیے فراہم کردہ ٹیمپلیٹ کا استعمال کرنا چاہیے۔ {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "اسکین شدہ فارمز کی <b>PDF فائل اپ لوڈ کریں</b>۔ اسے اس مرحلے سے پرنٹ شدہ فارم کا استعمال کرنا چاہیے۔ {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "نئے صارف کے لیے یہ ای میل استعمال کریں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "نیا اکاؤنٹ بنانے کے لیے ایک درست ای میل درج کریں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "ان تفصیلات کے ساتھ مصنف کے لیے ایک نیا اکاؤنٹ بنایا جائے گا۔ یہ ان پٹ اس میں شامل کیا جائے گا۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "پہلا نام", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "آخری نام", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "کسی مصنف کو یہ ان پٹ تفویض کرنے کے لیے براہ کرم ایک ای میل پتہ اور/یا پہلا نام اور آخری نام درج کریں۔ یا رضامندی والے باکس سے نشان ہٹا دیں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "اس ای میل کے ساتھ پہلے سے ہی ایک اکاؤنٹ وابستہ ہے۔ یہ ان پٹ اس میں شامل کیا جائے گا۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "صارف کی رضامندی (صارف کا اکاؤنٹ بنائیں)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "منظور کرو", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "تمام ان پٹس کو منظور کریں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "مصنف:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "ای میل:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "درآمد کے دوران غلطیاں ہوئیں اور کچھ ان پٹ درآمد نہیں ہوئے ہیں۔ براہ کرم غلطیوں کو درست کریں اور کوئی گمشدہ ان پٹ دوبارہ درآمد کریں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "غلط فارم ڈیٹا۔ غلطیوں کے لیے اوپر کا فارم چیک کریں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "ایکسل فائل درآمد کریں (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "درآمد کریں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "اسکین شدہ فارمز درآمد کریں (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "اسکین شدہ فارمز درآمد کریں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "امپورٹڈ ان پٹس", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "درآمد کرنا۔ اس عمل میں چند منٹ لگ سکتے ہیں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "یہ ان پٹ گمنام طور پر درآمد کیا گیا تھا۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} ان پٹ درآمد کیے گئے ہیں اور منظوری کی ضرورت ہے۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} ان پٹس کو منظور نہیں کیا جا سکا۔ براہ کرم توثیق کے مسائل کے لیے ہر ان پٹ کو چیک کریں اور انفرادی طور پر تصدیق کریں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "مقامی:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "ابھی تک جائزہ لینے کے لیے کچھ نہیں ہے۔ اسکین شدہ ان پٹ فارمز پر مشتمل پی ڈی ایف فائل یا ان پٹ پر مشتمل ایکسل فائل درآمد کرنے کے لیے \"{importFile}\" پر کلک کریں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "ابھی تک جائزہ لینے کے لیے کچھ نہیں ہے۔ ان پٹ پر مشتمل ایکسل فائل درآمد کرنے کے لیے \"{importFile}\" پر کلک کریں۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "ان پٹ", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "ص<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "درآمد شدہ فائل کو ظاہر نہیں کیا جا سکتا۔ درآمد شدہ فائل دیکھنا صرف پی ڈی ایف کی درآمدات کے لیے دستیاب ہے۔", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "مرحلہ:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "منتخب شدہ مرحلے میں ان پٹ شامل نہیں ہو سکتے۔ براہ کرم کوئی اور منتخب کریں۔", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "اس منصوبے میں کوئی بھی مرحلہ نہیں ہے جس میں خیالات شامل ہوسکتے ہیں۔", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "براہ کرم منتخب کریں کہ آپ ان پٹ کو کس مرحلے میں شامل کرنا چاہتے ہیں۔", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "ان پٹ درآمد کنندہ", "app.containers.Admin.projects.project.participation.comments": "تبصرے", "app.containers.Admin.projects.project.participation.inputs": "ان پٹ", "app.containers.Admin.projects.project.participation.participantsTimeline": "شرکاء کی ٹائم لائن", "app.containers.Admin.projects.project.participation.reactions": "<PERSON><PERSON>", "app.containers.Admin.projects.project.participation.selectPeriod": "مدت منتخب کریں۔", "app.containers.Admin.projects.project.participation.usersByAge": "عمر کے لحاظ سے صارفین", "app.containers.Admin.projects.project.participation.usersByGender": "جنس کے لحاظ سے صارفین", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "درکار ہے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "ایک سوال شامل کریں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "فیز لیول پر یوزر فیلڈز کو شامل کرنے یا ان میں ترمیم کرنے کی صلاحیت آپ کے موجودہ لائسنس میں شامل نہیں ہے۔ اس کے بارے میں مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} اختیارات", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "فیلڈ کی حیثیت", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "یہ سوالات سروے فارم کے آخری صفحے کے طور پر شامل کیے جائیں گے، کیونکہ 'سروے میں فیلڈز دکھائیں؟' مرحلے کی ترتیبات میں منتخب کیا گیا ہے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "کوئی اضافی سوال نہیں پوچھا جائے گا۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "اختیاری", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "اختیاری - ہمیشہ فعال ہوتا ہے کیونکہ گروپ کے ذریعہ حوالہ دیا جاتا ہے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "فیلڈ کو ہٹا دیں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "مطلوبہ - ہمیشہ فعال کیونکہ گروپ کے ذریعہ حوالہ دیا گیا ہے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "{verificationMethod}کے ساتھ تصدیق کریں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "ذیل میں اضافی سوالات کو مکمل کریں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "اپنے ای میل کی تصدیق کریں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "توثیقی طریقہ سے ڈیٹا واپس کیا گیا:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "پہلا نام، آخری نام، ای میل اور پاس ورڈ درج کریں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "اپنا ای میل درج کریں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "حال ہی میں صارفین کی تصدیق کتنی ہونی چاہیے؟", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "{verificationMethod} کے ساتھ شناخت کی تصدیق (صارف گروپ کی بنیاد پر)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "حصہ لینے کے لیے کسی کارروائی کی ضرورت نہیں ہے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "اوپر درج تصدیق شدہ ڈیٹا کی بنیاد پر شرکت کو محدود کرنے کے لیے اسمارٹ گروپس کا استعمال کریں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "صارفین کو آخری 30 منٹ میں تصدیق شدہ ہونا ضروری ہے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "گزشتہ {days} دنوں میں صارفین کی تصدیق ہونی چاہیے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "پچھلے 30 دنوں میں", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "آخری 30 منٹ میں", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "پچھلے 7 دنوں میں", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "ایک بار کافی ہے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "تصدیق شدہ فیلڈز:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} توثیق", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "اکاؤنٹ بنانا", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "شرکاء کو اپنے نام، تصدیق شدہ ای میل اور پاس ورڈ کے ساتھ ایک مکمل اکاؤنٹ بنانا ہوگا۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "شرکاء کو اپنے نام، ای میل اور پاس ورڈ کے ساتھ ایک مکمل اکاؤنٹ بنانا ہوگا۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "تصدیق", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "ترمیم کریں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "ای میل کی تصدیق", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "شرکاء کو ایک بار کے کوڈ کے ساتھ اپنے ای میل کی تصدیق کرنی ہوگی۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "اعلی درجے کی اسپام کا پتہ لگانا", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "یہ خصوصیت IP پتوں اور ڈیوائس ڈیٹا کا تجزیہ کرکے لاگ آؤٹ صارفین کی جانب سے سروے کی نقل جمع کرنے کو روکنے میں مدد کرتی ہے۔ اگرچہ لاگ ان کی ضرورت کے طور پر درست نہیں، یہ ڈپلیکیٹ جوابات کی تعداد کو کم کرنے میں مدد کر سکتا ہے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "نوٹ: مشترکہ نیٹ ورکس پر (جیسے دفاتر یا عوامی Wi-Fi)، اس بات کا بہت کم امکان ہے کہ مختلف صارفین کو ڈپلیکیٹ کے طور پر جھنڈا لگایا جا سکتا ہے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "اعلی درجے کی اسپام کا پتہ لگانے کو فعال کریں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "شرکاء سے اضافی سوالات پوچھے گئے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "کوئی نہیں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "کوئی بھی سائن اپ یا لاگ ان کیے بغیر حصہ لے سکتا ہے۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "اضافی سوالات اور گروپس کو دوبارہ ترتیب دیں۔", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "شرکت کو صارف گروپوں تک محدود کریں", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "ایس ایس او کی تصدیق", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "شرکاء کو {verificationMethod}کے ساتھ اپنی شناخت کی تصدیق کرنی ہوگی۔", "app.containers.Admin.projects.project.survey.aiAnalysis2": "AI تجزیہ کھولیں۔", "app.containers.Admin.projects.project.survey.allFiles": "تمام فائلیں۔", "app.containers.Admin.projects.project.survey.allResponses": "تمام جوابات", "app.containers.Admin.projects.project.survey.analysis.accuracy": "درستگی: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "AI کا خلاصہ بنانے میں ایک خرابی تھی۔ براہ کرم ذیل میں اسے دوبارہ تخلیق کرنے کی کوشش کریں۔", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "AI تجزیہ کھولیں۔", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "اس سوال کے خلاصے چھپائیں۔", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "ان پٹ کو منتخب کیا گیا ہے۔", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "تجزیہ کے اعمال کو کھولیں۔", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} نئے جوابات", "app.containers.Admin.projects.project.survey.analysis.regenerate": "دوبارہ پیدا کرنا", "app.containers.Admin.projects.project.survey.analysis.showInsights": "AI بصیرت دکھائیں۔", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "آپ اپنے موجودہ پلان پر ایک وقت میں زیادہ سے زیادہ 30 ان پٹ کا خلاصہ کر سکتے ہیں۔ مزید غیر مقفل کرنے کے لیے اپنے GovSuccess مینیجر یا منتظم سے بات کریں۔", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "تجزیہ کے لیے متعلقہ سوالات کا انتخاب کریں۔", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "کیا آپ {question}کے اپنے تجزیہ میں کوئی اور متعلقہ سوالات شامل کرنا چاہتے ہیں؟", "app.containers.Admin.projects.project.survey.cancel": "منسوخ کریں۔", "app.containers.Admin.projects.project.survey.consentModalButton": "جاری رکھیں", "app.containers.Admin.projects.project.survey.consentModalCancel": "منسوخ کریں۔", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "میں اس پروجیکٹ کے لیے OpenAI کو ڈیٹا پروسیسر کے طور پر استعمال کرنے سے اتفاق کرتا ہوں۔", "app.containers.Admin.projects.project.survey.consentModalText1": "جاری رکھ کر آپ OpenAI کو اس پروجیکٹ کے ڈیٹا پروسیسر کے طور پر استعمال کرنے سے اتفاق کرتے ہیں۔", "app.containers.Admin.projects.project.survey.consentModalText2": "OpenAI APIs خودکار متن کے خلاصے اور خودکار ٹیگنگ کے تجربے کے حصوں کو طاقت دیتے ہیں۔", "app.containers.Admin.projects.project.survey.consentModalText3": "ہم صرف وہی بھیجتے ہیں جو صارفین نے اپنے سروے، خیالات اور تبصروں میں لکھا ہے، OpenAI APIs کو ان کے پروفائل سے کوئی معلومات نہیں ملتی ہیں۔", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI اس ڈیٹا کو اپنے ماڈلز کی مزید تربیت کے لیے استعمال نہیں کرے گا۔ OpenAI ڈیٹا پرائیویسی کو کیسے ہینڈل کرتا ہے اس کے بارے میں مزید معلومات {link}پر مل سکتی ہے۔", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "یہاں", "app.containers.Admin.projects.project.survey.consentModalTitle": "اس سے پہلے کہ آپ جاری رکھیں", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "فارم میں ترمیم کرنے سے پہلے آپ تجزیہ درج نہیں کر سکتے", "app.containers.Admin.projects.project.survey.deleteAnalysis": "حذف کریں۔", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "کیا آپ واقعی اس تجزیہ کو حذف کرنا چاہتے ہیں؟ اس کارروائی کو کالعدم نہیں کیا جا سکتا۔", "app.containers.Admin.projects.project.survey.explore": "دریافت کریں۔", "app.containers.Admin.projects.project.survey.followUpResponses": "جوابات کی پیروی کریں۔", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> اوسط", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "GeoJSON کے بطور برآمد کریں۔", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "اس سوال کے جوابات کو GeoJSON فائل کے طور پر برآمد کریں۔ ہر GeoJSON فیچر کے لیے، تمام متعلقہ جواب دہندگان کے سروے کے جوابات اس فیچر کے 'پراپرٹیز' آبجیکٹ میں درج ہوں گے۔", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "تفصیلات چھپائیں۔", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} جواب دہندگان} one {{respondentCount} جواب دہندگان} other {{respondentCount} جواب دہندگان}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "تفصیلات دیکھیں", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} انتخاب} one {{numberChoices} انتخاب} other {{numberChoices} انتخاب}}", "app.containers.Admin.projects.project.survey.heatMap": "گرمی کا نقشہ", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "ایسری اسمارٹ میپنگ کا استعمال کرتے ہوئے تیار کردہ ہیٹ میپس کے بارے میں مزید جانیں۔", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "گرمی کا نقشہ ایسری اسمارٹ میپنگ کا استعمال کرتے ہوئے تیار کیا گیا ہے۔ جب ڈیٹا پوائنٹس کی ایک بڑی مقدار موجود ہو تو ہیٹ میپس مفید ہوتے ہیں۔ کم پوائنٹس کے لیے، صرف لوکیشن پوائنٹس کو براہ راست دیکھنا بہتر ہوگا۔ {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "حرارتی نقشہ کا نظارہ", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- منطق سے پوشیدہ", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "جب کوئی صارف اس جواب کو منتخب کرتا ہے تو منطق تمام صفحات کو صفحہ {pageNumber} تک چھوڑ دیتی ہے ({numQuestionsSkipped} سوالات کو چھوڑ دیا گیا)۔ چھوڑے گئے صفحات اور سوالات کو چھپانے یا دکھانے کے لیے کلک کریں۔", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "جب کوئی صارف اس جواب کو منتخب کرتا ہے تو منطق سروے کے اختتام پر چلی جاتی ہے ({numQuestionsSkipped} سوالات چھوڑے گئے)۔ چھوڑے گئے صفحات اور سوالات کو چھپانے یا دکھانے کے لیے کلک کریں۔", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "اس صفحہ پر منطق تمام صفحات کو صفحہ {pageNumber} تک چھوڑ دیتی ہے ({numQuestionsSkipped} سوالات چھوڑے گئے)۔ چھوڑے گئے صفحات اور سوالات کو چھپانے یا دکھانے کے لیے کلک کریں۔", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "اس صفحہ پر منطق سروے کے اختتام پر جاتی ہے ({numQuestionsSkipped} سوالات چھوڑے گئے)۔ چھوڑے گئے صفحات اور سوالات کو چھپانے یا دکھانے کے لیے کلک کریں۔", "app.containers.Admin.projects.project.survey.newAnalysis": "نیا تجزیہ", "app.containers.Admin.projects.project.survey.nextInsight": "اگلی بصیرت", "app.containers.Admin.projects.project.survey.openAnalysis": "AI تجزیہ کھولیں۔", "app.containers.Admin.projects.project.survey.otherResponses": "دوسرے جوابات", "app.containers.Admin.projects.project.survey.page": "ص<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.previousInsight": "پچھلی بصیرت", "app.containers.Admin.projects.project.survey.responses": "جوابات", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "اس صفحہ کے جوابات کی تعداد سروے کے جوابات کی کل تعداد سے کم ہے کیونکہ کچھ جواب دہندگان نے سروے میں منطق کی وجہ سے یہ صفحہ نہیں دیکھا ہوگا۔", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "اس سوال کے جوابات کی تعداد سروے کے جوابات کی کل تعداد سے کم ہے کیونکہ کچھ جواب دہندگان نے سروے میں منطق کی وجہ سے یہ سوال نہیں دیکھا ہوگا۔", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "جذبات کا پیمانہ", "app.containers.Admin.projects.project.survey.upsell.bullet1": "اپنے تمام جوابات کا فوری خلاصہ کریں۔", "app.containers.Admin.projects.project.survey.upsell.bullet2": "اپنے ڈیٹا سے قدرتی زبان میں بات کریں۔", "app.containers.Admin.projects.project.survey.upsell.bullet3": "AI سے تیار کردہ خلاصوں سے انفرادی جوابات کے حوالے حاصل کریں۔", "app.containers.Admin.projects.project.survey.upsell.bullet4": "مکمل جائزہ کے لیے ہمارا {link} چیک کریں۔", "app.containers.Admin.projects.project.survey.upsell.button": "AI تجزیہ کو غیر مقفل کریں۔", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "سپورٹ مضمون", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "AI کے ساتھ تیزی سے ڈیٹا کا تجزیہ کریں۔", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "یہ خصوصیت آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے حکومتی کامیابی کے مینیجر یا منتظم سے بات کریں۔", "app.containers.Admin.projects.project.survey.viewAnalysis": "دیکھیں", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "AI سے چلنے والے خلاصے دریافت کریں اور انفرادی گذارشات دیکھیں۔", "app.containers.Admin.projects.project.traffic.selectPeriod": "مدت منتخب کریں۔", "app.containers.Admin.projects.project.traffic.trafficSources": "ٹریفک ذرائع", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "ہم نے وزیٹر کے ڈیٹا کو جمع کرنے اور ڈسپلے کرنے کا طریقہ بدل دیا ہے۔ نتیجے کے طور پر، وزیٹر کا ڈیٹا زیادہ درست ہے اور ڈیٹا کی مزید اقسام دستیاب ہیں، جب کہ وہ ابھی تک GDPR کے مطابق ہے۔ ہم نے یہ نیا ڈیٹا نومبر 2024 میں اکٹھا کرنا شروع کیا تھا، اس لیے اس سے پہلے کوئی ڈیٹا دستیاب نہیں ہے۔", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "زائرین کی ٹائم لائن", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "فیز رپورٹ", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "مرحلے کے بارے میں کچھ متن شامل کریں۔", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "یہ کچھ متن ہے۔ آپ دائیں جانب پینل میں ایڈیٹر کا استعمال کرکے اس میں ترمیم اور فارمیٹ کرسکتے ہیں۔", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "شرکاء", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "پروجیکٹ کے نتائج", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "رپورٹ کا خلاصہ", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "پروجیکٹ کا مقصد، شرکت کے استعمال کے طریقے، اور نتیجہ شامل کریں۔", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "زائرین", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "یہ رپورٹ غیر محفوظ شدہ تبدیلیوں پر مشتمل ہے۔ براہ کرم پرنٹ کرنے سے پہلے محفوظ کریں۔", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "عنوان پہلے ہی لیا جا چکا ہے۔", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "پچھلے {days} دنوں کے مقابلے", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "اعدادوشمار چھپائیں۔", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "شرکت کی شرح", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "پچھلی مدت کے ساتھ موازنہ دکھائیں۔", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "آپ کو پہلے تاریخ کی حد منتخب کرنے کی ضرورت ہے۔", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "تبصرے", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "ان پٹ", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "شرکت", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "تبصرے دکھائیں۔", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "ان پٹ دکھائیں۔", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "ووٹ دکھائیں۔", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "ووٹ", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "ڈیموگرافکس", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "رجسٹریشن کی تاریخ کی حد", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "رجسٹریشن کا میدان", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "نامعلوم", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "صارفین: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "کھینچنا", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "فعال", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "محف<PERSON><PERSON> شدہ", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "ختم", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "کھلا ہوا", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "منصوبہ بندی کی", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "پروجیکٹس", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "اشاعت کی حیثیت", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "شائع شدہ", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "اس ویجیٹ کا ڈیٹا غائب ہے۔ رپورٹ کو محفوظ کرنے کے لیے اسے دوبارہ ترتیب دیں یا حذف کریں۔", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "کمیونٹی مانیٹر ہیلتھ سکور", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "کوارٹر", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "سال", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "اس منصوبے میں کوئی مناسب مراحل نہیں ملے", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "کوئی مرحلہ منتخب نہیں کیا گیا۔ براہ کرم پہلے ایک مرحلہ منتخب کریں۔", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "کوئی پروجیکٹ نہیں۔", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "کوئی پروجیکٹ منتخب نہیں کیا گیا۔ براہ کرم پہلے ایک پروجیکٹ منتخب کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "آپ اس رپورٹ کی نقل نہیں بنا سکتے کیونکہ اس میں ڈیٹا ہے جس تک آپ کی رسائی نہیں ہے۔", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "آپ اس رپورٹ میں ترمیم نہیں کر سکتے کیونکہ اس میں ڈیٹا ہے جس تک آپ کی رسائی نہیں ہے۔", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "کیا آپ واقعی \"{reportName}\" کو حذف کرنا چاہتے ہیں؟ اس کارروائی کو کالعدم نہیں کیا جا سکتا۔", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "کیا آپ واقعی اس رپورٹ کو حذف کرنا چاہتے ہیں؟ اس کارروائی کو کالعدم نہیں کیا جا سکتا۔", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "حذف کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "نقل", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "ترمیم کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "ترمیم شدہ {days, plural, no {# دن} one {# دن} other {# دن}} پہلے", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "اس رپورٹ کو بنانے کی کوشش کرتے وقت ایک خرابی پیش آگئی۔ براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "خالی صفحہ سے شروع کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "کمیونٹی مانیٹر ٹیمپلیٹ کے ساتھ شروع کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "رپورٹ کا عنوان", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "ایک رپورٹ بنائیں", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "اپنی رپورٹ کو حسب ضرورت بنائیں اور اسے پی ڈی ایف فائل کے طور پر اندرونی اسٹیک ہولڈرز یا کمیونٹی کے ساتھ شیئر کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "ایک رپورٹ بنائیں", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "اپنی پہلی رپورٹ بنائیں", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "کوئی پروجیکٹ منتخب نہیں کیا گیا۔", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "پلیٹ فارم ٹیمپلیٹ کے ساتھ شروع کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "پی ڈی ایف میں پرنٹ کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "پروجیکٹ ٹیمپلیٹ کے ساتھ شروع کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "سہ ماہی {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "رپورٹ ٹیمپلیٹ", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "اس عنوان کے ساتھ ایک رپورٹ پہلے سے موجود ہے۔ براہ کرم ایک مختلف عنوان منتخب کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "چوتھائی منتخب کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "سال منتخب کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "پی ڈی ایف کے بطور شیئر کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "سب کے ساتھ اشتراک کرنے کے لیے، رپورٹ کو بطور PDF پرنٹ کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "ویب لنک کے بطور شئیر کریں۔", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "یہ ویب لنک صرف ایڈمن صارفین کے لیے قابل رسائی ہے۔", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "شیئر کریں۔", "app.containers.Admin.reporting.contactToAccess": "اپنی مرضی کے مطابق رپورٹ بنانا پریمیم لائسنس کا حصہ ہے۔ اس کے بارے میں مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "تمام رپورٹس", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "کمیونٹی مانیٹر رپورٹس", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "یہ رپورٹس کمیونٹی مانیٹر سے متعلق ہیں۔ رپورٹس خود بخود ہر سہ ماہی میں تیار ہوتی ہیں۔", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "ایک رپورٹ بنائیں", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "اپنی رپورٹ کو حسب ضرورت بنائیں اور اسے ویب لنک کے ساتھ اندرونی اسٹیک ہولڈرز یا کمیونٹی کے ساتھ شیئر کریں۔", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "آپ کی رپورٹس یہاں ظاہر ہوں گی۔", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "رپورٹس تلاش کریں۔", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "پیش رفت رپورٹس", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "یہ آپ کے حکومتی کامیابی کے مینیجر کے ذریعہ تیار کردہ رپورٹس ہیں۔", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "آپ کی رپورٹس", "app.containers.Admin.reporting.deprecated": "فرسودہ", "app.containers.Admin.reporting.helmetDescription": "ایڈمن رپورٹنگ صفحہ", "app.containers.Admin.reporting.helmetTitle": "رپورٹنگ", "app.containers.Admin.reporting.printPrepare": "پرنٹ کرنے کی تیاری ہو رہی ہے...", "app.containers.Admin.reporting.reportBuilder": "رپورٹ بلڈر", "app.containers.Admin.reporting.reportHeader": "رپورٹ ہیڈر", "app.containers.Admin.reporting.warningBanner3": "اس رپورٹ میں گراف اور نمبرز صرف اس صفحہ پر خود بخود اپ ڈیٹ ہوتے ہیں۔ رپورٹ کو دوسرے صفحات پر اپ ڈیٹ کرنے کے لیے محفوظ کریں۔", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "مشتر<PERSON>ہ زمین", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "کونویو", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "معلومات", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "استعمال شدہ طریقے", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "سروے", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "رائے شماری", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "پچھلے {days} دن: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "تجاو<PERSON>ز", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "بیرونی سروے", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "رضاکارانہ", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "ووٹنگ", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "چارٹ", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "دیکھیں", "app.containers.Admin.surveyFormTab.downloads": "ڈاؤن لوڈز", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "ایک اور سروے کی نقل بنائیں", "app.containers.Admin.surveyFormTab.editSurveyForm": "سروے فارم میں ترمیم کریں۔", "app.containers.Admin.surveyFormTab.inputFormDescription": "وضاحت کریں کہ کون سی معلومات فراہم کی جانی چاہیے، مختصر وضاحتیں یا ہدایات شامل کریں تاکہ شرکاء کے جوابات کی رہنمائی کی جا سکے اور یہ واضح کریں کہ آیا ہر فیلڈ اختیاری ہے یا مطلوب ہے۔", "app.containers.Admin.surveyFormTab.surveyForm": "سروے فارم", "app.containers.Admin.tools.apiTokens.createTokenButton": "نیا ٹوکن بنائیں", "app.containers.Admin.tools.apiTokens.createTokenCancel": "منسوخ کریں۔", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "آپ کا ٹوکن بن گیا ہے۔ براہ کرم نیچے {secret} کاپی کریں اور اسے محفوظ طریقے سے اسٹور کریں۔", "app.containers.Admin.tools.apiTokens.createTokenDescription": "ہمارے عوامی API کے ساتھ استعمال کرنے کے لیے ایک نیا ٹوکن بنائیں۔", "app.containers.Admin.tools.apiTokens.createTokenError": "اپنے ٹوکن کے لیے ایک نام فراہم کریں۔", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "ٹوکن بنائیں", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>اہم!</b> آپ اس {secret} کو صرف ایک بار کاپی کر سکتے ہیں۔ اگر آپ اس ونڈو کو بند کرتے ہیں تو آپ اسے دوبارہ نہیں دیکھ پائیں گے۔", "app.containers.Admin.tools.apiTokens.createTokenName": "نام", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "اپنے ٹوکن کو ایک نام دیں۔", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "آپ کا ٹوکن بن گیا ہے۔", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "بند", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "کاپی کریں {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "کاپی کیا گیا!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "ایک نیا ٹوکن بنائیں", "app.containers.Admin.tools.apiTokens.createdAt": "بنایا", "app.containers.Admin.tools.apiTokens.delete": "ٹوکن حذف کریں۔", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "کیا آپ واقعی اس ٹوکن کو حذف کرنا چاہتے ہیں؟", "app.containers.Admin.tools.apiTokens.description": "ہمارے عوامی API کے لیے اپنے API ٹوکنز کا نظم کریں۔ مزید معلومات کے لیے، ہمارا {link}دیکھیں۔", "app.containers.Admin.tools.apiTokens.lastUsedAt": "آخری بار استعمال کیا گیا۔", "app.containers.Admin.tools.apiTokens.link": "API دستاویزات", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "نام", "app.containers.Admin.tools.apiTokens.noTokens": "آپ کے پاس ابھی تک کوئی ٹوکن نہیں ہے۔", "app.containers.Admin.tools.apiTokens.title": "عوامی API ٹوکنز", "app.containers.Admin.tools.esriDisabled": "Esri انضمام ایک اضافی خصوصیت ہے۔ اگر آپ اس بارے میں مزید معلومات چاہتے ہیں تو اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.Admin.tools.esriIntegration2": "ایسری انضمام", "app.containers.Admin.tools.esriIntegrationButton": "Esri کو فعال کریں۔", "app.containers.Admin.tools.esriIntegrationDescription3": "اپنے Esri اکاؤنٹ کو جوڑیں اور ArcGIS آن لائن سے براہ راست اپنے میپنگ پروجیکٹس میں ڈیٹا درآمد کریں۔", "app.containers.Admin.tools.esriIntegrationImageAlt": "ایسری لوگو", "app.containers.Admin.tools.esriKeyInputDescription": "پراجیکٹس میں نقشہ ٹیبز میں ArcGIS آن لائن سے اپنی نقشہ کی تہوں کو درآمد کرنے کی اجازت دینے کے لیے اپنی Esri API کلید شامل کریں۔", "app.containers.Admin.tools.esriKeyInputLabel": "Esri <PERSON> کلید", "app.containers.Admin.tools.esriKeyInputPlaceholder": "API کلید یہاں چسپاں کریں۔", "app.containers.Admin.tools.esriMaps": "ایسری نقشے", "app.containers.Admin.tools.esriSaveButtonError": "آپ کی کلید محفوظ کرنے میں ایک خامی تھی، براہ کرم دوبارہ کوشش کریں۔", "app.containers.Admin.tools.esriSaveButtonSuccess": "API کلید محفوظ ہو گئی۔", "app.containers.Admin.tools.esriSaveButtonText": "کلید محفوظ کریں۔", "app.containers.Admin.tools.learnMore": "مزید جانیں", "app.containers.Admin.tools.managePublicAPIKeys": "API کیز کا نظم کریں۔", "app.containers.Admin.tools.manageWidget": "ویجیٹ کا نظم کریں۔", "app.containers.Admin.tools.manageWorkshops": "ورکشاپس کا انتظام کریں۔", "app.containers.Admin.tools.powerBIAPIImage": "پاور BI تصویر", "app.containers.Admin.tools.powerBIDescription": "اپنے Microsoft Power BI ورک اسپیس میں Go Vocal ڈیٹا تک رسائی حاصل کرنے کے لیے ہمارے پلگ اینڈ پلے پاور BI ٹیمپلیٹس کا استعمال کریں۔", "app.containers.Admin.tools.powerBIDisabled1": "پاور BI آپ کے لائسنس کا حصہ نہیں ہے۔ اگر آپ اس بارے میں مزید معلومات چاہتے ہیں تو اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.Admin.tools.powerBIDownloadTemplates": "ٹیمپلیٹس ڈاؤن لوڈ کریں۔", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "اگر آپ اپنے Go Vocal ڈیٹا کو Power BI ڈیٹا فلو کے اندر استعمال کرنے کا ارادہ رکھتے ہیں، تو یہ ٹیمپلیٹ آپ کو ایک نیا ڈیٹا فلو ترتیب دینے کی اجازت دے گا جو آپ کے Go Vocal ڈیٹا سے جڑتا ہے۔ اس ٹیمپلیٹ کو ڈاؤن لوڈ کرنے کے بعد آپ کو PowerBI پر اپ لوڈ کرنے سے پہلے اپنے عوامی API اسناد کے ساتھ ٹیمپلیٹ میں درج ذیل اسٹرنگ ##CLIENT_ID## اور ##CLIENT_SECRET## کو تلاش کرنا اور تبدیل کرنا ہوگا۔", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "ڈیٹا فلو ٹیمپلیٹ ڈاؤن لوڈ کریں۔", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "ڈیٹا فلو ٹیمپلیٹ", "app.containers.Admin.tools.powerBITemplates.intro": "نوٹ: ان پاور BI ٹیمپلیٹس میں سے کسی کو استعمال کرنے کے لیے، آپ کو پہلے {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "ہمارے عوامی API کے لیے اسناد کا ایک سیٹ بنائیں", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "یہ ٹیمپلیٹ آپ کے Go Vocal ڈیٹا کی بنیاد پر Power BI رپورٹ بنائے گا۔ یہ آپ کے گو ووکل پلیٹ فارم پر تمام ڈیٹا کنکشن قائم کرے گا، ڈیٹا ماڈل اور کچھ ڈیفالٹ ڈیش بورڈ بنائے گا۔ جب آپ پاور BI میں ٹیمپلیٹ کھولیں گے تو آپ کو اپنے عوامی API کی اسناد داخل کرنے کے لیے کہا جائے گا۔ آپ کو اپنے پلیٹ فارم کے لیے بیس یو آر ایل بھی داخل کرنے کی ضرورت ہوگی، جو یہ ہے: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "رپورٹنگ ٹیمپلیٹ ڈاؤن لوڈ کریں۔", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "رپورٹ ٹیمپلیٹ", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Power BI میں آپ کے Go Vocal ڈیٹا کو استعمال کرنے کے بارے میں مزید تفصیلات ہمارے {link}میں مل سکتی ہیں۔", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "سپورٹ مضمون", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.cizenlab.co/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "پاور BI ٹیمپلیٹس", "app.containers.Admin.tools.powerBITitle": "پاور BI", "app.containers.Admin.tools.publicAPIDescription": "ہمارے عوامی API پر حسب ضرورت انٹیگریشنز بنانے کے لیے اسناد کا نظم کریں۔", "app.containers.Admin.tools.publicAPIDisabled1": "عوامی API آپ کے موجودہ لائسنس کا حصہ نہیں ہے۔ اگر آپ اس بارے میں مزید معلومات چاہتے ہیں تو اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.Admin.tools.publicAPIImage": "عوامی API تصویر", "app.containers.Admin.tools.publicAPITitle": "عوامی API رسائی", "app.containers.Admin.tools.toolsLabel": "اوزار", "app.containers.Admin.tools.widgetDescription": "لوگوں کو اس پلیٹ فارم کی طرف راغب کرنے کے لیے آپ ایک ویجیٹ بنا سکتے ہیں، اسے اپنی مرضی کے مطابق بنا سکتے ہیں اور اسے اپنی ویب سائٹ میں شامل کر سکتے ہیں۔", "app.containers.Admin.tools.widgetImage": "ویجیٹ کی تصویر", "app.containers.Admin.tools.widgetTitle": "ویجیٹ", "app.containers.Admin.tools.workshopsDescription": "لائیو ویڈیو میٹنگز منعقد کریں، بیک وقت گروپ ڈسکشنز اور ڈیبیٹس کی سہولت فراہم کریں۔ ان پٹ جمع کریں، ووٹ دیں اور اتفاق رائے تک پہنچیں، بالکل اسی طرح جیسے آپ آف لائن ہوں گے۔", "app.containers.Admin.tools.workshopsImage": "ورکشاپ کی تصویر", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "آن لائن مباحثے کی ورکشاپس", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "پلیٹ فارم پر کل صارفین", "app.containers.AdminPage.DashboardPage._blank": "نامعلوم", "app.containers.AdminPage.DashboardPage.allGroups": "تمام گروپس", "app.containers.AdminPage.DashboardPage.allProjects": "تمام پروجیکٹس", "app.containers.AdminPage.DashboardPage.allTime": "ہر وقت", "app.containers.AdminPage.DashboardPage.comments": "تبصرے", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "تبصرے", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "پلیٹ فارم کے صارفین کی نمائندگی کی پیمائش کرنے کے لیے ایک بیس ڈیٹا سیٹ کی ضرورت ہے۔", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "جلد آرہا ہے۔", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "ہم فی الحال {fieldName} ڈیش بورڈ پر کام کر رہے ہیں، یہ جلد ہی دستیاب ہو جائے گا۔", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# آئٹم ہے} other {# آئٹمز}} اس گراف میں پوشیدہ ہیں۔ تمام ڈیٹا دیکھنے کے لیے {tableViewLink} میں تبدیل کریں۔", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "صارف کی رجسٹریشن کے لیے {requiredOrOptional}", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{total} صارفین میں سے {known} شامل ہیں ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "{numberOfHiddenItems} مزید دکھائیں۔", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "اختیاری", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "براہ کرم ایک بنیادی ڈیٹا سیٹ فراہم کریں۔", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "نمائندہ اسکور:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "یہ اسکور اس بات کی عکاسی کرتا ہے کہ پلیٹ فارم صارف کا ڈیٹا کل آبادی کی کتنی درست عکاسی کرتا ہے۔ {representativenessArticleLink}کے بارے میں مزید جانیں۔", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "درکار ہے۔", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "بیس ڈیٹا جمع کروائیں۔", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "میز کا منظر", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "کل آبادی", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "صارفین", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "عمر کا ایک گروپ شامل کریں۔", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} اور اس سے زیادہ", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "عمر کا گروپ", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "{upperBound} اور اس سے زیادہ عمر کے گروپ (زبانیں) شامل نہیں ہیں۔", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "عمر کا گروپ {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "عمر کے گروپس", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "اور زیادہ", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "مثال گروپ بندی کا اطلاق کریں۔", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "سب صاف کریں۔", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "سے", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "اپنے بیس ڈیٹاسیٹ کے ساتھ سیدھ میں لانے کے لیے عمر کے گروپس سیٹ کریں۔", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "رینج", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "محفوظ کریں۔", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "کو", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "عمر کے گروپوں میں ترمیم کریں۔", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "اس شے کا حساب نہیں لیا جائے گا۔", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "کم دیکھیں", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "{numberOfHiddenItems} مزید دیکھیں...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "بنیادی مہینہ (اختیاری)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "عمر کے گروپ (سال پیدائش)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "جلد آرہا ہے۔", "app.containers.AdminPage.DashboardPage.components.Field.complete": "م<PERSON><PERSON>ل", "app.containers.AdminPage.DashboardPage.components.Field.default": "طے شدہ", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "براہ کرم تمام فعال کردہ اختیارات کو پُر کریں، یا ان اختیارات کو غیر فعال کریں جنہیں آپ گراف سے خارج کرنا چاہتے ہیں۔ کم از کم ایک آپشن پُر کرنا ضروری ہے۔", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "نامکمل", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "کل رہائشیوں کی تعداد", "app.containers.AdminPage.DashboardPage.components.Field.options": "اختیارات", "app.containers.AdminPage.DashboardPage.components.Field.save": "محفوظ کریں۔", "app.containers.AdminPage.DashboardPage.components.Field.saved": "محفوظ کیا گیا۔", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "براہ کرم بنیادی ڈیٹا داخل کرنا شروع کرنے کے لیے پہلے {setAgeGroupsLink} ۔", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "عمر کے گروپ مقرر کریں", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "اوسط جواب کا وقت: {days} دن", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "جواب دینے کے لیے دنوں کی اوسط رقم", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "رائے دی گئی۔", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "ان پٹ کی حیثیت", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "حیثیت کے لحاظ سے ان پٹ", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "ان پٹ کی تعداد", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "سرکاری اپ ڈیٹ", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "ان پٹ کا فیصد", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "جوابی وقت", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "حیثیت", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "حیثیت بدل گئی۔", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "کل", "app.containers.AdminPage.DashboardPage.components.editBaseData": "بیس ڈیٹا میں ترمیم کریں۔", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "ہم نمائندگی کے اسکور کا حساب کیسے لگاتے ہیں۔", "app.containers.AdminPage.DashboardPage.continuousType": "ٹائم لائن کے بغیر", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "مجموعی کل", "app.containers.AdminPage.DashboardPage.customDateRange": "حسب ضرورت", "app.containers.AdminPage.DashboardPage.day": "دن", "app.containers.AdminPage.DashboardPage.false": "جھوٹا", "app.containers.AdminPage.DashboardPage.female": "خاتون", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "رد عمل کے لحاظ سے سرفہرست 5 ان پٹ", "app.containers.AdminPage.DashboardPage.fromTo": "{from} سے {to}تک", "app.containers.AdminPage.DashboardPage.helmetDescription": "پلیٹ فارم پر سرگرمیوں کے لیے ڈیش بورڈ", "app.containers.AdminPage.DashboardPage.helmetTitle": "ایڈمن ڈیش بورڈ صفحہ", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "پروجیکٹ کے ذریعے دکھانے کے لیے وسائل کا انتخاب کریں۔", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "ٹیگ کے ذریعے دکھانے کے لیے ایک وسیلہ چنیں۔", "app.containers.AdminPage.DashboardPage.inputs1": "ان پٹ", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "حیثیت کے لحاظ سے ان پٹ", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "صارف گروپ منتخب کریں۔", "app.containers.AdminPage.DashboardPage.male": "مرد", "app.containers.AdminPage.DashboardPage.month": "م<PERSON><PERSON><PERSON>ہ", "app.containers.AdminPage.DashboardPage.noData": "دکھانے کے لیے کوئی ڈیٹا نہیں ہے۔", "app.containers.AdminPage.DashboardPage.noPhase": "اس منصوبے کے لیے کوئی مرحلہ نہیں بنایا گیا۔", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "شرکاء کی تعداد جنہوں نے ان پٹ پوسٹ کیے، ردعمل ظاہر کیا یا تبصرہ کیا۔", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "ناپسندیدگی", "app.containers.AdminPage.DashboardPage.numberOfLikes": "پسند کرتا ہے۔", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "ک<PERSON> رد عمل", "app.containers.AdminPage.DashboardPage.overview.management": "انتظام", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "پروجیکٹس اور شرکت", "app.containers.AdminPage.DashboardPage.overview.showLess": "کم دکھائیں۔", "app.containers.AdminPage.DashboardPage.overview.showMore": "مزید دکھائیں", "app.containers.AdminPage.DashboardPage.participants": "شرکاء", "app.containers.AdminPage.DashboardPage.participationPerProject": "فی پروجیکٹ شرکت", "app.containers.AdminPage.DashboardPage.participationPerTopic": "شرکت فی ٹیگ", "app.containers.AdminPage.DashboardPage.perPeriod": "فی {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "پچھلے 30 دن", "app.containers.AdminPage.DashboardPage.previous90Days": "پچھلے 90 دن", "app.containers.AdminPage.DashboardPage.previousWeek": "پچھلا ہفتہ", "app.containers.AdminPage.DashboardPage.previousYear": "پچھلے سال", "app.containers.AdminPage.DashboardPage.projectType": "پروجیکٹ کی قسم: {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "اس بیس ڈیٹاسیٹ کو کل آبادی کے مقابلے پلیٹ فارم کے صارفین کی نمائندگی کا حساب لگانے کی ضرورت ہے۔", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "براہ کرم ایک بنیادی ڈیٹا سیٹ فراہم کریں۔", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "دیکھیں کہ آپ کے پلیٹ فارم کے صارفین کی کل آبادی کے مقابلے میں کس طرح نمائندہ ہے - صارف کے رجسٹریشن کے دوران جمع کیے گئے ڈیٹا کی بنیاد پر۔ {representativenessArticleLink}کے بارے میں مزید جانیں۔", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "دیکھیں کہ آپ کے پلیٹ فارم کے صارفین کی کل آبادی کے مقابلے میں کس طرح نمائندہ ہے - صارف کے رجسٹریشن کے دوران جمع کیے گئے ڈیٹا کی بنیاد پر۔", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "کمیونٹی کی نمائندگی", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "ڈیش بورڈ پر واپس جائیں۔", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "فعال رجسٹریشن فیلڈز میں سے کوئی بھی اس وقت تعاون یافتہ نہیں ہے۔", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "یہاں آپ ڈیش بورڈ پر آئٹمز دکھا/چھپا سکتے ہیں اور بنیادی ڈیٹا درج کر سکتے ہیں۔ {userRegistrationLink} کے لیے صرف فعال فیلڈز یہاں ظاہر ہوں گی۔", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "بیس ڈیٹا میں ترمیم کریں۔", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "صارف کی رجسٹریشن", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "بیس ڈیٹا جمع کروائیں۔", "app.containers.AdminPage.DashboardPage.resolutionday": "دنوں میں", "app.containers.AdminPage.DashboardPage.resolutionmonth": "مہینوں میں", "app.containers.AdminPage.DashboardPage.resolutionweek": "ہفتوں میں", "app.containers.AdminPage.DashboardPage.selectProject": "پروجیکٹ منتخب کریں۔", "app.containers.AdminPage.DashboardPage.selectedProject": "موجودہ پروجیکٹ فلٹر", "app.containers.AdminPage.DashboardPage.selectedTopic": "موجودہ ٹیگ فلٹر", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "دریافت کریں کہ آپ کے پلیٹ فارم پر کیا ہو رہا ہے۔", "app.containers.AdminPage.DashboardPage.tabOverview": "جا<PERSON>زہ", "app.containers.AdminPage.DashboardPage.tabReports": "رپورٹس", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "نمائندگی", "app.containers.AdminPage.DashboardPage.tabUsers": "صارفین", "app.containers.AdminPage.DashboardPage.timelineType": "ٹائم لائن", "app.containers.AdminPage.DashboardPage.titleDashboard": "ڈیش بورڈ", "app.containers.AdminPage.DashboardPage.total": "کل", "app.containers.AdminPage.DashboardPage.totalForPeriod": "یہ {period}", "app.containers.AdminPage.DashboardPage.true": "سچ", "app.containers.AdminPage.DashboardPage.unspecified": "<PERSON><PERSON>ر متعینہ", "app.containers.AdminPage.DashboardPage.users": "صارفین", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "عمر کے لحاظ سے صارفین", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "جغرافیائی علاقے کے لحاظ سے صارفین", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "جنس کے لحاظ سے صارفین", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "رجسٹر<PERSON><PERSON><PERSON>ز", "app.containers.AdminPage.DashboardPage.week": "ہ<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.favicon": "فیویکان", "app.containers.AdminPage.FaviconPage.faviconExplaination": "فیویکون امیج کو منتخب کرنے کے لیے تجاویز: ایک سادہ تصویر منتخب کریں، کیونکہ دکھائی گئی تصویر کا سائز بہت چھوٹا ہے۔ تصویر کو PNG کے بطور محفوظ کیا جانا چاہیے، اور شفاف پس منظر کے ساتھ مربع ہونا چاہیے (یا اگر ضروری ہو تو سفید پس منظر)۔ آپ کا فیویکن صرف ایک بار سیٹ کیا جانا چاہیے کیونکہ تبدیلیوں کو کچھ تکنیکی مدد کی ضرورت ہوگی۔", "app.containers.AdminPage.FaviconPage.save": "محفوظ کریں۔", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "کچھ غلط ہو گیا، براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.AdminPage.FaviconPage.saveSuccess": "کامیابی!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "آپ کی تبدیلیاں محفوظ ہو گئی ہیں۔", "app.containers.AdminPage.FolderPermissions.addFolderManager": "شامل کریں۔", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "حذف کریں۔", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "فولڈر مینیجرز", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "فولڈر مینیجر فولڈر کی تفصیل میں ترمیم کر سکتے ہیں، فولڈر کے اندر نئے پروجیکٹس بنا سکتے ہیں، اور فولڈر کے اندر موجود تمام پروجیکٹس پر پروجیکٹ مینجمنٹ کے حقوق حاصل کر سکتے ہیں۔ وہ پراجیکٹس کو ڈیلیٹ نہیں کر سکتے اور ان کو ان پراجیکٹس تک رسائی نہیں ہے جو ان کے فولڈر میں نہیں ہیں۔ پراجیکٹ مینجمنٹ کے حقوق کے بارے میں مزید معلومات حاصل کرنے کے لیے آپ {projectManagementInfoCenterLink} پر جا سکتے ہیں۔", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.cizenlab.co/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "کوئی مماثلت نہیں ملی", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "ہمارا امدادی مرکز ملاحظہ کریں۔", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "صارفین کو تلاش کریں۔", "app.containers.AdminPage.FoldersEdit.addToFolder": "فولڈر میں شامل کریں۔", "app.containers.AdminPage.FoldersEdit.archivedStatus": "محف<PERSON><PERSON> شدہ", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "اس فولڈر کو حذف کریں۔", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "تفصیل", "app.containers.AdminPage.FoldersEdit.draftStatus": "مسودہ", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "اس فولڈر میں فائلیں شامل کریں۔", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "فائلیں 50Mb سے بڑی نہیں ہونی چاہئیں۔ شامل کی گئی فائلیں فولڈر کے صفحے پر دکھائی جائیں گی۔", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "تفصیل", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "اس فولڈر میں کوئی پروجیکٹ نہیں ہے۔ پروجیکٹس بنانے اور شامل کرنے کے لیے مین پروجیکٹس ٹیب پر واپس جائیں۔", "app.containers.AdminPage.FoldersEdit.folderName": "فولڈر کا نام", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "ہیڈر کی تصویر", "app.containers.AdminPage.FoldersEdit.multilocError": "ہر زبان کے لیے تمام ٹیکسٹ فیلڈز کو بھرنا چاہیے۔", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "کوئی پروجیکٹ نہیں ہے جسے آپ اس فولڈر میں شامل کر سکتے ہیں۔", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "فولڈر کارڈ کی تصویر", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "اجازتیں", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "فولڈر پروجیکٹس", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "ترتیبات", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "اس فولڈر میں شامل پروجیکٹس", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "پروجیکٹ جو آپ اس فولڈر میں شامل کر سکتے ہیں۔", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "منتخب کریں کہ آیا یہ فولڈر \"ڈرافٹ\"، \"شائع شدہ\" یا \"آرکائیو شدہ\" ہے۔", "app.containers.AdminPage.FoldersEdit.publishedStatus": "شائع شدہ", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "فولڈر سے ہٹا دیں۔", "app.containers.AdminPage.FoldersEdit.save": "محفوظ کریں۔", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "کچھ غلط ہو گیا، براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.AdminPage.FoldersEdit.saveSuccess": "کامیابی!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "آپ کی تبدیلیاں محفوظ ہو گئی ہیں۔", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "مختصر تفصیل", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "ہوم پیج پر دکھایا گیا ہے۔", "app.containers.AdminPage.FoldersEdit.statusLabel": "اشاعت کی حیثیت", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "وضاحت کریں کہ پروجیکٹس ایک ساتھ کیوں ہیں، ایک بصری شناخت کی وضاحت کریں اور معلومات کا اشتراک کریں۔", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "وضاحت کریں کہ پروجیکٹس ایک ساتھ کیوں ہیں، ایک بصری شناخت کی وضاحت کریں اور معلومات کا اشتراک کریں۔", "app.containers.AdminPage.FoldersEdit.textFieldsError": "تمام ٹیکسٹ فیلڈز کو بھرنا ضروری ہے۔", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "عنوان", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "ایک نیا فولڈر بنائیں", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "ترتیبات", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "فولڈر دیکھیں", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "ہیرو بینر کی تصویر اور متن کو حسب ضرورت بنائیں۔", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "ہیرو بینر", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "ہیرو بینر کو بچائیں۔", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Inspiration Hub ایک ایسی جگہ ہے جہاں آپ دوسرے پلیٹ فارمز پر پروجیکٹس کو براؤز کرکے اپنے پروجیکٹس کے لیے انسپائریشن حاصل کرسکتے ہیں۔", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "اپنے پلیٹ فارم کی شرائط و ضوابط اور رازداری کی پالیسی میں ترمیم کریں۔ دیگر صفحات بشمول کے بارے میں اور اکثر پوچھے گئے سوالات کے صفحات کو {navigationLink} ٹیب میں ترمیم کیا جا سکتا ہے۔", "app.containers.AdminPage.PagesEdition.policiesTitle": "پلیٹ فارم کی پالیسیاں", "app.containers.AdminPage.PagesEdition.privacy-policy": "رازداری کی پالیسی", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "شرائط و ضوابط", "app.containers.AdminPage.Project.confirmation.description": "اس کارروائی کو کالعدم نہیں کیا جا سکتا۔", "app.containers.AdminPage.Project.confirmation.no": "منسوخ کریں۔", "app.containers.AdminPage.Project.confirmation.title": "کیا آپ واقعی شرکت کے تمام ڈیٹا کو دوبارہ ترتیب دینا چاہتے ہیں؟", "app.containers.AdminPage.Project.confirmation.yes": "شرکت کا تمام ڈیٹا ری سیٹ کریں۔", "app.containers.AdminPage.Project.data.descriptionText1": "واضح خیالات، تبصرے، ووٹ، رد عمل، سروے کے جوابات، رائے شماری کے جوابات، رضاکاروں اور ایونٹ کے اندراج کرنے والے۔ ووٹنگ کے مراحل کے معاملے میں، یہ عمل ووٹوں کو صاف کرے گا لیکن اختیارات نہیں.", "app.containers.AdminPage.Project.data.title": "اس پروجیکٹ سے شرکت کا تمام ڈیٹا صاف کریں۔", "app.containers.AdminPage.Project.resetParticipationData": "شرکت کا تمام ڈیٹا ری سیٹ کریں۔", "app.containers.AdminPage.Project.settings.accessRights": "رسائی کے حقوق", "app.containers.AdminPage.Project.settings.back": "پیچھے", "app.containers.AdminPage.Project.settings.data": "ڈیٹا", "app.containers.AdminPage.Project.settings.description": "تفصیل", "app.containers.AdminPage.Project.settings.events": "واقعات", "app.containers.AdminPage.Project.settings.general": "جن<PERSON><PERSON>", "app.containers.AdminPage.Project.settings.projectTags": "پروجیکٹ ٹیگز", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "پلیٹ فارم پر منصوبوں کی فہرست", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "پروجیکٹس ڈیش بورڈ", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "نئے پروجیکٹس بنائیں یا موجودہ پروجیکٹس کا نظم کریں۔", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "پروجیکٹس", "app.containers.AdminPage.ProjectDashboard.published": "شائع شدہ", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "ترتیبات کا پینل بند کریں۔", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "مر<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "پوری چوڑائی", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "بائیں", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "بٹن کی سیدھ", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "ٹھیک ہے۔", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "بٹن کا متن", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "بٹن کے لیے متن درج کریں۔", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "پرائمری", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "بٹن کی قسم", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "ثانوی", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "بٹن URL", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "بٹن کے لیے ایک URL درج کریں۔", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "کالم لے آؤٹ", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "تفصیل", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "ہوم پیج کی تفصیل", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "ہوم پیج پر پروجیکٹ کارڈ پر دکھایا گیا ہے۔", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "پروجیکٹ کے صفحے پر دکھایا گیا ہے۔ واضح طور پر بیان کریں کہ پروجیکٹ کیا ہے، آپ اپنے صارفین سے کیا توقع رکھتے ہیں اور وہ آپ سے کیا توقع کر سکتے ہیں۔", "app.containers.AdminPage.ProjectDescription.errorMessage": "کچھ غلط ہو گیا، براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.AdminPage.ProjectDescription.preview": "پیش نظارہ", "app.containers.AdminPage.ProjectDescription.save": "محفوظ کریں۔", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "آپ کی تبدیلیاں محفوظ ہو گئی ہیں۔", "app.containers.AdminPage.ProjectDescription.saved": "محفوظ کیا گیا!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "فیصلہ کریں کہ آپ اپنے سامعین کو کون سا پیغام دینا چاہتے ہیں۔ اپنے پروجیکٹ میں ترمیم کریں اور اسے امیجز، ویڈیوز، فائل اٹیچمنٹ،… کے ساتھ افزودہ کریں یہ معلومات دیکھنے والوں کو یہ سمجھنے میں مدد کرتی ہے کہ آپ کا پروجیکٹ کیا ہے۔", "app.containers.AdminPage.ProjectDescription.titleDescription": "پروجیکٹ کی تفصیل", "app.containers.AdminPage.ProjectDescription.whiteSpace": "سفید جگہ", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "بارڈر شامل کریں۔", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "عمودی اونچائی", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "بڑا", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "درمیانہ", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "چھوٹا", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "ترمیم منسوخ کریں۔", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "نقشہ کے مرکز نقطہ کا طے شدہ عرض بلد۔ -90 اور 90 کے درمیان ایک قدر قبول کرتا ہے۔", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "نقشہ کے مرکز نقطہ کا ڈیفالٹ طول البلد۔ -90 اور 90 کے درمیان ایک قدر قبول کرتا ہے۔", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "نقشہ کی پرت میں ترمیم کریں۔", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "پرت میں ترمیم کریں۔", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "کچھ غلط ہو گیا، براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.AdminPage.ProjectEdit.MapTab.here": "یہاں", "app.containers.AdminPage.ProjectEdit.MapTab.import": "GeoJSON فائل درآمد کریں۔", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "طے شدہ عرض بلد", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "پرت کا رنگ", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "پرت میں موجود تمام فیچرز کو اس رنگ کے ساتھ اسٹائل کیا جائے گا۔ یہ رنگ آپ کی GeoJSON فائل میں موجود کسی بھی اسٹائلنگ کو بھی اوور رائٹ کر دے گا۔", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "مارکر آئیکن", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "اختیاری طور پر ایک آئیکن منتخب کریں جو مارکر میں ظاہر ہو۔ آئیکنز کی فہرست دیکھنے کے لیے {url} پر کلک کریں جنہیں آپ منتخب کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "پرت کا نام", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "اس پرت کا نام نقشہ کے لیجنڈ پر دکھایا گیا ہے۔", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "پرت ٹول ٹِپ", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "نقشے پر پرت کی خصوصیات پر منڈلاتے وقت یہ متن ٹول ٹپ کے طور پر ظاہر ہوتا ہے۔", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "نقشے کی پرتیں۔", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "ہم فی الحال GeoJSON فائلوں کو سپورٹ کرتے ہیں اور ArcGIS آن لائن سے فیچر لیئرز اور ویب میپس درآمد کرتے ہیں۔ نقشہ کی تہوں کو شامل کرنے، تبدیل کرنے اور اسٹائل کرنے کے طریقے کے بارے میں تجاویز کے لیے {supportArticle} پڑھیں۔", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "طے شدہ طول البلد", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "پہلے سے طے شدہ مرکز کا نقشہ بنائیں اور زوم کریں۔", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "نقشہ کا ڈیفالٹ سینٹر پوائنٹ اور زوم لیول۔ نیچے کی قدروں کو دستی طور پر ایڈجسٹ کریں، یا نقشے کے نچلے بائیں کونے میں {button} بٹن پر کلک کریں تاکہ موجودہ سینٹر پوائنٹ اور نقشے کے زوم لیول کو بطور ڈیفالٹ اقدار محفوظ کریں۔", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "نقشہ کے منظر کو اپنی مرضی کے مطابق بنائیں، بشمول نقشے کی پرتوں کو اپ لوڈ کرنا اور اسٹائل کرنا اور نقشہ کا مرکز اور زوم لیول سیٹ کرنا۔", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "نقشہ کی ترتیب", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "نقشہ کی ترتیب فی الحال تمام مراحل میں شیئر کی گئی ہے، آپ فی فیز مختلف نقشے کی ترتیب نہیں بنا سکتے۔", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "پرت کو ہٹا دیں۔", "app.containers.AdminPage.ProjectEdit.MapTab.save": "محفوظ کریں۔", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "زوم کو محفوظ کریں۔", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "سپورٹ مضمون", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.cizenlab.co/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "بے نام پرت", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "نقشہ زوم کی سطح", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "نقشہ کا ڈیفالٹ زوم لیول۔ 1 اور 17 کے درمیان ایک قدر کو قبول کرتا ہے، جہاں 1 کو مکمل طور پر زوم آؤٹ کیا جاتا ہے (پوری دنیا نظر آتی ہے) اور 17 کو مکمل طور پر زوم کیا جاتا ہے (بلاک اور عمارتیں نظر آتی ہیں)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "صارف کے تمام ڈیٹا کو گمنام کریں۔", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "ریکارڈ کیے جانے سے پہلے صارفین کی جانب سے سروے کے تمام ان پٹس کو گمنام رکھا جائے گا۔", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "صارفین کو اب بھی رسائی کے 'حقوق تک رسائی' ٹیب کے تحت شرکت کی ضروریات کی تعمیل کرنے کی ضرورت ہوگی۔ سروے ڈیٹا ایکسپورٹ میں صارف کا پروفائل ڈیٹا دستیاب نہیں ہوگا۔", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "اگر آپ اس آپشن کو فعال کرتے ہیں تو، صارف کے رجسٹریشن کی فیلڈز سائن اپ کے عمل کے حصے کے بجائے سروے میں آخری صفحہ کے طور پر دکھائی دیں گی۔", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "سروے کی شکل میں آبادیاتی فیلڈز", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "سروے میں ڈیموگرافک فیلڈز دکھائیں؟", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "اس مضمون میں خودکار اشتراک کیسے کام کرتا ہے اس کے بارے میں مزید پڑھیں۔", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "خودکار اشتراک کے نتائج", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "ووٹنگ کے نتائج پلیٹ فارم پر اور ای میل کے ذریعے شرکاء کو اس وقت شیئر کیے جاتے ہیں جب مرحلہ ختم ہوتا ہے۔ یہ طے شدہ طور پر شفافیت کو یقینی بناتا ہے۔", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "نتیجہ کا اشتراک", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "جواب کا اختیار شامل کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "رائے شماری کا سوال شامل کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "منسوخ کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "منسوخ کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "منسوخ کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "حذف کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "حذف کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "جواب کے آپشن میں ترمیم کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "جواب کے اختیارات محفوظ کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "جواب کے اختیارات میں ترمیم کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "سوال میں ترمیم کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "رائے شماری کے نتائج برآمد کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "انتخاب کی زیادہ سے زیادہ تعداد اختیارات کی تعداد سے زیادہ ہے۔", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "متعدد انتخاب", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "کوئی اختیارات نہیں۔", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "تمام سوالات کے جوابات کا انتخاب ہونا چاہیے۔", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "صرف ایک آپشن", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "پول کے جواب دہندگان کے پاس صرف ایک انتخاب ہے۔", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "اس کے لیے جواب کے اختیارات کا نظم کریں: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "یہاں آپ رائے شماری کے سوالات تشکیل دے سکتے ہیں، شرکاء کے لیے ہر سوال کے لیے جواب کے انتخاب کا انتخاب کر سکتے ہیں، فیصلہ کر سکتے ہیں کہ آیا آپ چاہتے ہیں کہ شرکاء صرف ایک جواب کا انتخاب (سنگل چوائس) یا ایک سے زیادہ جواب کے انتخاب (متعدد انتخاب) کو منتخب کر سکیں، اور برآمد کریں۔ رائے شماری کے نتائج آپ ایک رائے شماری کے اندر متعدد پول سوالات تشکیل دے سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "محفوظ کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "محفوظ کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "محفوظ کریں۔", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "وا<PERSON><PERSON> انتخاب", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "پولز کی ترتیبات اور نتائج", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "زیادہ سے زیادہ غلط", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "درآمد کریں۔", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "فیڈ بیک دیں، ٹیگز شامل کریں یا اگلے پروجیکٹ فیز میں پوسٹس کاپی کریں۔", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "تجاویز کا نظم کریں، رائے دیں اور عنوانات تفویض کریں۔", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "ان پٹ مینیجر", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "نتائج کا اشتراک بند ہے۔", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "ووٹنگ کے نتائج فیز کے اختتام پر شیئر نہیں کیے جائیں گے جب تک کہ آپ فیز سیٹ اپ میں اس میں ترمیم نہیں کرتے۔", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "مرحلہ ختم ہونے کے بعد یہ نتائج خود بخود شیئر کیے جائیں گے۔ نتائج کا اشتراک ہونے پر اس مرحلے کی آخری تاریخ میں تبدیلی کریں۔", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "سروے کے نتائج برآمد کریں (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "نتائج", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "یہاں، آپ ایکسل فائل کے طور پر اس پروجیکٹ کے اندر Typeform سروے کے نتائج ڈاؤن لوڈ کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "سروے فارم", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "سروے کے نتائج", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "سروے", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "سروے کے جوابات سے مشورہ کریں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "وجہ شامل کریں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "کیا آپ کو یقین ہے؟", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "تفصیل", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "رضاکاروں سے کیا ضرورت ہے اور وہ کیا توقع کر سکتے ہیں اس کی وضاحت کے لیے اس کا استعمال کریں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "محفوظ نہیں کیا جا سکا کیونکہ فارم میں خرابیاں ہیں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "تصویر", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "عنوان", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "حذف کریں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "ترمیم کریں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "ایک وجہ ایک عمل یا سرگرمی ہے جس کے لیے شرکاء رضاکارانہ طور پر کام کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "وجہ میں ترمیم کریں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "ایک تفصیل شامل کریں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "ایک عنوان شامل کریں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "رضاکاروں کو برآمد کریں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "ایک وجہ ایک عمل یا سرگرمی ہے جس کے لیے شرکاء رضاکارانہ طور پر کام کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "نئی وجہ", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "محفوظ کریں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "یہاں، آپ ان وجوہات کو ترتیب دے سکتے ہیں جن کے لیے صارفین رضاکارانہ طور پر کام کر سکتے ہیں اور رضاکاروں کو ڈاؤن لوڈ کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "رضاکارانہ", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {کوئی شریک نہیں} one {# شریک} other {# شرکاء}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "بجٹ مختص", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "آپشنز کے لیے بجٹ تفویض کریں اور شرکاء سے اپنے پسندیدہ آپشنز کو منتخب کرنے کے لیے کہیں جو کل بجٹ میں فٹ ہوں۔", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "بجٹ مختص کرنا", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "صارفین کو تبصرہ کرنے کی اجازت دینے سے ووٹنگ کے عمل میں تعصب ہو سکتا ہے۔", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "کریڈٹ", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "اختیارات کا ڈیفالٹ منظر", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "صارفین کے لیے اقدامات", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "منتخب کریں کہ صارفین کیا اضافی اقدامات کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "مقررہ رقم", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "اگر خالی چھوڑ دیا جائے تو یہ \"ووٹ\" کے لیے ڈیفالٹ ہو جائے گا۔", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "ہمارے {optionAnalysisArticleLink}میں <b> {voteTypeDescription} </b> استعمال کرنے کے بارے میں مزید جانیں۔", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "فی آپشن زیادہ سے زیادہ ووٹ", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "ووٹوں کی زیادہ سے زیادہ تعداد", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "آپ ان ووٹوں کی تعداد کو محدود کر سکتے ہیں جو صارف کل ڈال سکتا ہے (زیادہ سے زیادہ ایک ووٹ فی آپشن کے ساتھ)۔", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "فی اختیار ایک سے زیادہ ووٹ", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "صارفین کو اختیارات کے درمیان تقسیم کرنے کے لیے ٹوکن کی ایک رقم دی جاتی ہے۔", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "فی اختیار ایک سے زیادہ ووٹ", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "فی صارف ووٹوں کی تعداد", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "اختیاری تجزیہ کا جائزہ", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "ووٹ دینے کے اختیارات", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "نقطہ", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "ایک ووٹ فی آپشن", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "صارفین کسی بھی آپشن کو منظور کرنے کا انتخاب کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "ایک ووٹ فی آپشن", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "ٹوکن", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "لا محدود", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "ووٹ کو کیا کہا جائے؟", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "مثلاً ٹوکن، پوائنٹس، کاربن کریڈٹس...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "مثلاً ٹوکن، پوائنٹ، کاربن کریڈٹ...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "ووٹ", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "ووٹنگ کے ہر طریقہ میں مختلف پری کنفیگریشنز ہوتی ہیں۔", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "ووٹنگ کا طریقہ", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "ووٹنگ کا طریقہ صارفین کے ووٹ دینے کے اصولوں کا تعین کرتا ہے۔", "app.containers.AdminPage.ProjectEdit.addNewInput": "ایک ان پٹ شامل کریں۔", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "آپ اپنے پروجیکٹ کو ابھی ایک فولڈر میں شامل کر سکتے ہیں، یا اسے بعد میں پروجیکٹ کی ترتیبات میں کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "پروجیکٹ ٹیگز", "app.containers.AdminPage.ProjectEdit.altText": "Alt متن", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "گمنام پولنگ", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "فعال ہونے پر یہ دیکھنا ناممکن ہے کہ کس نے کس پر ووٹ دیا۔ صارفین کو اب بھی ایک اکاؤنٹ کی ضرورت ہے اور وہ صرف ایک بار ووٹ دے سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.approved": "منظور شدہ", "app.containers.AdminPage.ProjectEdit.archived": "محف<PERSON><PERSON> شدہ", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "محفوظ شدہ پروجیکٹس اب بھی دکھائی دے رہے ہیں، لیکن مزید شرکت کی اجازت نہیں دیتے", "app.containers.AdminPage.ProjectEdit.archivedStatus": "محف<PERSON><PERSON> شدہ", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "اس علاقے کو حذف نہیں کیا جا سکتا کیونکہ یہ مندرجہ ذیل مزید حسب ضرورت صفحہ (صفحات) پر پروجیکٹس کو ظاہر کرنے کے لیے استعمال ہو رہا ہے۔ آپ کو صفحہ سے علاقے کا لنک ختم کرنے کی ضرورت ہوگی، یا اس سے پہلے کہ آپ علاقے کو حذف کر سکیں صفحہ کو حذف کریں۔", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "تمام علاقے", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "پروجیکٹ ہر علاقے کے فلٹر پر ظاہر ہوگا۔", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "ایریا فلٹر", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "علاقوں کا استعمال کرتے ہوئے ہوم پیج پر پروجیکٹس کو فلٹر کیا جا سکتا ہے۔ علاقوں کو سیٹ کیا جا سکتا ہے {areasLabelTooltipLink}۔", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "یہاں", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "کوئی مخصوص علاقہ نہیں۔", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "علاقے کے لحاظ سے فلٹر کرنے پر پروجیکٹ نہیں دکھایا جائے گا۔", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "انتخاب", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "پروجیکٹ منتخب ایریا فلٹر (فلٹر) پر ظاہر ہوگا۔", "app.containers.AdminPage.ProjectEdit.cardDisplay": "کارڈز", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "سب سے زیادہ زیر بحث", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "سروے کا مواد شامل کریں۔", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "اس سروے کی گذارشات آنا شروع ہو گئی ہیں۔ سروے میں تبدیلیوں کے نتیجے میں ڈیٹا ضائع ہو سکتا ہے اور برآمد شدہ فائلوں میں ڈیٹا نامکمل ہو سکتا ہے۔", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "سروے کامیابی کے ساتھ محفوظ ہو گیا۔", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "سروے", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "سروے دیکھیں", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "ووٹنگ کا طریقہ منتخب کریں، اور صارفین کو چند مختلف اختیارات کے درمیان ترجیح دیں۔", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "ووٹنگ یا ترجیحی مشق کا انعقاد کریں۔", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "ٹیمپلیٹ سے پروجیکٹ بنائیں", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "ایک بیرونی سروے ایمبیڈ کریں۔", "app.containers.AdminPage.ProjectEdit.createInput": "نیا ان پٹ شامل کریں۔", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "ایک درون پلیٹ فارم سروے بنائیں", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "ہمارا پلیٹ فارم چھوڑے بغیر ایک سروے ترتیب دیں۔", "app.containers.AdminPage.ProjectEdit.createPoll": "ایک پول بنائیں", "app.containers.AdminPage.ProjectEdit.createPollDescription": "ایک سے زیادہ انتخابی سوالنامہ مرتب کریں۔", "app.containers.AdminPage.ProjectEdit.createProject": "نیا پروجیکٹ", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "ایک Typeform، Google Form، Enalyzer، SurveyXact، Qualtrics، SmartSurvey، Snap Survey یا Microsoft Forms سروے ایمبیڈ کریں۔", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "آپ مرکزی پراجیکٹ کے صفحہ پر ظاہر ہونے والی پوسٹس کے لیے ڈیفالٹ آرڈر سیٹ کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.defaultSorting": "چھانٹنا", "app.containers.AdminPage.ProjectEdit.departments": "محکمے", "app.containers.AdminPage.ProjectEdit.descriptionTab": "تفصیل", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "یہ ناپسندیدگی کو فعال یا غیر فعال کر دے گا، لیکن پسند کرنا پھر بھی فعال رہے گا۔ ہم تجویز کرتے ہیں کہ اسے غیر فعال چھوڑ دیں جب تک کہ آپ اختیار کا تجزیہ نہیں کر رہے ہیں۔", "app.containers.AdminPage.ProjectEdit.disabled": "معذور", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "فی شریک نا پسند کی تعداد", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "ناپسندیدگی کو فعال کریں۔", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "کسی دستاویز پر رائے جمع کریں۔", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "ایک انٹرایکٹو پی ڈی ایف ایمبیڈ کریں اور Konveio کے ساتھ تبصرے اور تاثرات جمع کریں۔", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "معذور", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "فعال", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "ایڈمنز اور تفویض کردہ پروجیکٹ مینیجرز کے علاوہ ڈرافٹ پروجیکٹس تمام لوگوں کے لیے پوشیدہ ہیں۔", "app.containers.AdminPage.ProjectEdit.draft": "مسودہ", "app.containers.AdminPage.ProjectEdit.draftStatus": "مسودہ", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "ترمیم کریں۔", "app.containers.AdminPage.ProjectEdit.enabled": "فعال", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "منتخب کریں کہ صارفین کون سے شراکتی اقدامات کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.enalyzer": "اینالائزر", "app.containers.AdminPage.ProjectEdit.eventsTab": "واقعات", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "منسلکات (زیادہ سے زیادہ 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "فائلیں 50Mb سے بڑی نہیں ہونی چاہئیں۔ شامل کی گئی فائلیں پروجیکٹ کی معلومات کے صفحے پر دکھائی جائیں گی۔", "app.containers.AdminPage.ProjectEdit.filesTab": "فائلیں", "app.containers.AdminPage.ProjectEdit.findVolunteers": "رضاکاروں کو تلاش کریں۔", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "شرکاء سے سرگرمیوں اور وجوہات کے لیے رضاکارانہ طور پر کام کرنے کو کہیں۔", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "ایک فولڈر مینیجر کے طور پر، آپ پروجیکٹ بناتے وقت ایک فولڈر منتخب کر سکتے ہیں، لیکن بعد میں صرف ایک منتظم ہی اسے تبدیل کر سکتا ہے۔", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "فولڈر کارڈ کی تصویر کا متبادل متن", "app.containers.AdminPage.ProjectEdit.folderSelectError": "اس پروجیکٹ کو شامل کرنے کے لیے ایک فولڈر منتخب کریں۔", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "حسب ضرورت مواد", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "اس فارم کی جمع آوریاں آنا شروع ہو گئی ہیں۔ فارم میں تبدیلی کے نتیجے میں ڈیٹا ضائع ہو سکتا ہے اور برآمد شدہ فائلوں میں ڈیٹا نامکمل ہو سکتا ہے۔", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "فارم کامیابی کے ساتھ محفوظ ہو گیا۔", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "سروے ختم", "app.containers.AdminPage.ProjectEdit.fromATemplate": "ایک ٹیمپلیٹ سے", "app.containers.AdminPage.ProjectEdit.generalTab": "جن<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.google_forms": "گوگل فارمز", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "ہیڈر امیج Alt ٹیکسٹ", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "ہیڈر کی تصویر", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.cizenlab.co/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "نیا", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "صارفین کو معلومات فراہم کریں، یا گزشتہ مراحل پر نتائج بانٹنے کے لیے رپورٹ بلڈر کا استعمال کریں۔", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "معلومات یا نتائج کا اشتراک کریں۔", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "ان پٹ اور آراء جمع کریں۔", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "ان پٹ، رد عمل اور/یا تبصرے بنائیں یا جمع کریں۔ مختلف قسم کے آدانوں میں سے انتخاب کریں: آئیڈیا کلیکشن، آپشن کا تجزیہ، سوال و جواب، مسئلے کی شناخت اور مزید۔", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "پوسٹس پر کارروائی کا ذمہ دار کون ہے؟", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "اس پروجیکٹ میں تمام نئے ان پٹ اس شخص کو تفویض کیے جائیں گے۔ تفویض کرنے والے کو {ideaManagerLink}میں تبدیل کیا جا سکتا ہے۔", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "پوسٹس پر تبصرہ کرنا", "app.containers.AdminPage.ProjectEdit.inputFormTab": "ان پٹ فارم", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "ان پٹ مینیجر", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "ان پٹ مینیجر", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "پوسٹس جمع کرانا", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "آدانوں پر ردعمل", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "پہلے سے طے شدہ منظر", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "ان پٹ دکھانے کے لیے ڈیفالٹ ویو کا انتخاب کریں: گرڈ ویو میں کارڈز یا نقشے پر پن۔ شرکاء دستی طور پر دونوں آراء کے درمیان سوئچ کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.inspirationHub": "حوصلہ افزائی کا مرکز", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Konveio URL کو ایمبیڈ کریں۔", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "ان پٹ کی زیادہ سے زیادہ تعداد جو ایک حصہ لینے والا پسند کر سکتا ہے (خیال میں) یا ووٹ دے سکتا ہے (تجاویز میں)", "app.containers.AdminPage.ProjectEdit.limited": "م<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "مزید ٹیمپلیٹس لوڈ کریں۔", "app.containers.AdminPage.ProjectEdit.mapDisplay": "نقشہ", "app.containers.AdminPage.ProjectEdit.mapTab": "نقشہ", "app.containers.AdminPage.ProjectEdit.maxDislikes": "زیادہ سے زیادہ ناپسندیدگی", "app.containers.AdminPage.ProjectEdit.maxLikes": "زیادہ سے زیادہ لائکس", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "فی اختیار ووٹوں کی زیادہ سے زیادہ تعداد ووٹوں کی کل تعداد سے کم یا اس کے برابر ہونی چاہیے۔", "app.containers.AdminPage.ProjectEdit.maximum": "زیادہ سے زیادہ", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "اپنی ٹوکری جمع کرواتے وقت شرکاء اس بجٹ سے زیادہ نہیں ہو سکتے۔", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "مائیکروسافٹ فارم", "app.containers.AdminPage.ProjectEdit.minimum": "کم از کم", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "شرکاء سے مطالبہ کریں کہ وہ اپنی ٹوکری جمع کرانے کے لیے کم از کم بجٹ کو پورا کریں (اگر آپ کم از کم سیٹ نہیں کرنا چاہتے تو '0' درج کریں)۔", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "ہمارا امدادی مرکز ملاحظہ کریں۔", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "پروجیکٹ مینیجرز کون ہیں؟", "app.containers.AdminPage.ProjectEdit.moreDetails": "مزید تفصیلات", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.cizenlab.co/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "پریرتا کی ضرورت ہے؟ {inspirationHubLink}میں دوسرے شہروں سے ملتے جلتے پروجیکٹس دریافت کریں۔", "app.containers.AdminPage.ProjectEdit.newContribution": "ایک شراکت شامل کریں۔", "app.containers.AdminPage.ProjectEdit.newIdea": "نیا خیال", "app.containers.AdminPage.ProjectEdit.newInitiative": "ایک پہل شامل کریں۔", "app.containers.AdminPage.ProjectEdit.newIssue": "ایک مسئلہ شامل کریں۔", "app.containers.AdminPage.ProjectEdit.newOption": "ایک آپشن شامل کریں۔", "app.containers.AdminPage.ProjectEdit.newPetition": "ایک درخواست شامل کریں۔", "app.containers.AdminPage.ProjectEdit.newProject": "نیا پروجیکٹ", "app.containers.AdminPage.ProjectEdit.newProposal": "ایک تجویز شامل کریں۔", "app.containers.AdminPage.ProjectEdit.newQuestion": "ایک سوال شامل کریں۔", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "تازہ ترین", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "درست رقم نہیں ہے۔", "app.containers.AdminPage.ProjectEdit.noFolder": "کوئی فولڈر نہیں۔", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "- کوئی فولڈر نہیں -", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "کوئی ٹیمپلیٹس نہیں ملے", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "براہ کرم پروجیکٹ کا عنوان درج کریں۔", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "درست نمبر نہیں ہے۔", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "سب سے پرانا", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "صرف ایڈمن کے لیے نظر آتا ہے۔", "app.containers.AdminPage.ProjectEdit.optionNo": "نہیں", "app.containers.AdminPage.ProjectEdit.optionYes": "ہاں (فولڈر منتخب کریں)", "app.containers.AdminPage.ProjectEdit.participationLevels": "شرکت کی سطح", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "آپ کیا کرنا چاہتے ہیں؟", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "منتخب کریں کہ صارفین کس طرح حصہ لے سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "آپ وضاحت کر سکتے ہیں کہ ہر ایکشن کون لے سکتا ہے، اور مزید معلومات اکٹھا کرنے کے لیے شرکاء سے اضافی سوالات پوچھ سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "شرکاء کی ضروریات اور سوالات", "app.containers.AdminPage.ProjectEdit.pendingReview": "زیر التوا منظوری", "app.containers.AdminPage.ProjectEdit.permissionsTab": "رسائی کے حقوق", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "رسائی کے حقوق", "app.containers.AdminPage.ProjectEdit.phaseEmails": "اطلاعات", "app.containers.AdminPage.ProjectEdit.pollTab": "رائے شماری", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "زیادہ تر ردعمل", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "پروجیکٹ کارڈ کی تصویر", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "یہ تصویر پروجیکٹ کارڈ کا حصہ ہے۔ وہ کارڈ جو پروجیکٹ کا خلاصہ کرتا ہے اور مثال کے طور پر ہوم پیج پر دکھایا گیا ہے۔\n\n    تجویز کردہ تصویری قراردادوں کے بارے میں مزید معلومات کے لیے، {supportPageLink}۔", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "فولڈر", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "یہ تصویر پروجیکٹ کے صفحے کے اوپری حصے میں دکھائی گئی ہے۔\n\n    تجویز کردہ تصویری قراردادوں کے بارے میں مزید معلومات کے لیے، {supportPageLink}۔", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "پروجیکٹ کارڈ کی تصویر کا متبادل متن", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "بصارت سے محروم صارفین کے لیے تصویر کی مختصر تفصیل فراہم کریں۔ اس سے اسکرین ریڈرز کو یہ بتانے میں مدد ملتی ہے کہ تصویر کس بارے میں ہے۔", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "پراجیکٹ مینجمنٹ", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "پروجیکٹ مینیجر پروجیکٹس میں ترمیم کرسکتے ہیں، پوسٹس کا نظم کرسکتے ہیں اور شرکاء کو ای میل کرسکتے ہیں۔ آپ پروجیکٹ مینیجرز کو تفویض کردہ حقوق کے بارے میں مزید معلومات حاصل کرنے کے لیے {moderationInfoCenterLink} کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.projectName": "پروجیکٹ کا نام", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "پروجیکٹ کی قسم", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "ٹائم لائن والے پروجیکٹس کا آغاز اور اختتام واضح ہوتا ہے اور اس کے مختلف مراحل ہو سکتے ہیں۔ بغیر ٹائم لائن کے منصوبے مسلسل چل رہے ہیں۔", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "پروجیکٹ کی قسم کو بعد میں تبدیل نہیں کیا جا سکتا۔", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "آپ پروجیکٹ کو مخصوص صارفین کے لیے پوشیدہ ہونے کے لیے سیٹ کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "پروجیکٹ کی مرئیت", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "پروجیکٹ کی حیثیت تلاش کر رہے ہیں؟ اب آپ اسے کسی بھی وقت براہ راست پروجیکٹ پیج ہیڈر سے تبدیل کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "شائع شدہ پراجیکٹس سب کو نظر آتے ہیں یا گروپ سب سیٹ اگر منتخب کیا جاتا ہے۔", "app.containers.AdminPage.ProjectEdit.publishedStatus": "شائع شدہ", "app.containers.AdminPage.ProjectEdit.purposes": "مقا<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.qualtrics": "کوالٹرکس", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "بے ترتیب", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "شرکت کا ڈیٹا ری سیٹ کریں۔", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "آپ کا ڈیٹا محفوظ کرتے وقت ایک خرابی پیش آگئی۔ براہ کرم دوبارہ کوشش کریں۔", "app.containers.AdminPage.ProjectEdit.saveProject": "محفوظ کریں۔", "app.containers.AdminPage.ProjectEdit.saveSuccess": "کامیابی!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "آپ کا فارم محفوظ ہو گیا ہے!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "ٹیمپلیٹس تلاش کریں۔", "app.containers.AdminPage.ProjectEdit.selectGroups": "گروپ منتخب کریں", "app.containers.AdminPage.ProjectEdit.setup": "سیٹ اپ", "app.containers.AdminPage.ProjectEdit.shareInformation": "معلومات شیئر کریں۔", "app.containers.AdminPage.ProjectEdit.smart_survey": "اسمارٹ سروے", "app.containers.AdminPage.ProjectEdit.snap_survey": "سنیپ سروے", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "اپنے پروجیکٹ کو ترتیب دیں اور ذاتی بنائیں۔", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "ہمارے سپورٹ سینٹر پر جائیں۔", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "سروے کا مواد شامل کریں۔", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "منسوخ کریں۔", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# انتخاب} one {# انتخاب} other {# انتخاب}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "ہاں، میں جانا چاہتا ہوں۔", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "ترمیم کریں۔", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "اس سروے کی گذارشات آنا شروع ہو گئی ہیں۔ سروے میں تبدیلیوں کے نتیجے میں ڈیٹا ضائع ہو سکتا ہے اور برآمد شدہ فائلوں میں ڈیٹا نامکمل ہو سکتا ہے۔", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "فائل اپ لوڈ کریں۔", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "واپس جاؤ", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "درآمد کریں۔", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "درآمد کریں۔", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "مختصر جواب، طویل جواب، اور جذباتی پیمانے پر فالو اپ سوالات کے لیے AI خلاصے بائیں سائڈبار میں موجود AI ٹیب سے حاصل کیے جا سکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "لکیری پیمانہ", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "میٹرکس", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "لمبا جواب", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "متعدد انتخاب - بہت سے منتخب کریں۔", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "تصویر کا انتخاب - بہت سے منتخب کریں۔", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "نئی جمع کرائی", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "ابھی تک کوئی سروے کے جوابات نہیں ہیں۔", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "جوابات کے لیے کھولیں۔", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "جوابات کے لیے کھولیں۔", "app.containers.AdminPage.ProjectEdit.survey.optional2": "اختیاری", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "اگر کوئی منطق شامل نہیں کی جاتی ہے، تو سروے اپنے معمول کے بہاؤ کی پیروی کرے گا۔ اگر صفحہ اور اس کے سوالات دونوں میں منطق ہے تو سوال کی منطق کو ترجیح دی جائے گی۔ یقینی بنائیں کہ یہ آپ کے مطلوبہ سروے کے بہاؤ کے مطابق ہے۔ مزید معلومات کے لیے {supportPageLink}پر جائیں۔", "app.containers.AdminPage.ProjectEdit.survey.point": "مقام", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "کیا آپ واقعی چھوڑنا چاہتے ہیں؟", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "آپ کی موجودہ تبدیلیاں محفوظ نہیں ہوں گی۔", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "در<PERSON><PERSON> بندی", "app.containers.AdminPage.ProjectEdit.survey.rating": "در<PERSON><PERSON> بندی", "app.containers.AdminPage.ProjectEdit.survey.required2": "درکار ہے۔", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {جوابات} one {جواب} other {جوابات}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# جوابات} one {# جواب} other {# جوابات}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "متعدد انتخاب - ایک کا انتخاب کریں۔", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "جذباتی لکیری پیمانہ", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "ایسری شکل فائل اپ لوڈ کریں۔", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "سروے کامیابی کے ساتھ محفوظ ہو گیا۔", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.cizenlab.co/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "سروے", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "سروے کے جوابات", "app.containers.AdminPage.ProjectEdit.survey.text2": "مخت<PERSON>ر جواب", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "کل {count} جوابات", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "سروے دیکھیں", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "دیکھیں", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "یو آر ایل ایمبیڈ کریں۔", "app.containers.AdminPage.ProjectEdit.surveyService": "سروس", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "آپ سروے کو ایمبیڈ کرنے کے طریقے کے بارے میں مزید معلومات حاصل کر سکتے ہیں {surveyServiceTooltipLink}۔", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "یہاں", "app.containers.AdminPage.ProjectEdit.survey_monkey": "سروے بندر", "app.containers.AdminPage.ProjectEdit.survey_xact": "سروے ایکسیکٹ", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "اس ٹیگ کو حذف نہیں کیا جا سکتا کیونکہ یہ مندرجہ ذیل مزید حسب ضرورت صفحہ (صفحات) پر پروجیکٹس کو دکھانے کے لیے استعمال ہو رہا ہے۔ \nآپ کو صفحہ سے ٹیگ کا لنک ختم کرنا ہوگا، یا ٹیگ کو حذف کرنے سے پہلے صفحہ کو حذف کرنا ہوگا۔", "app.containers.AdminPage.ProjectEdit.titleGeneral": "منصوبے کے لیے عمومی ترتیبات", "app.containers.AdminPage.ProjectEdit.titleLabel": "عنوان", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "ایک ایسا عنوان منتخب کریں جو مختصر، دلکش اور واضح ہو۔ یہ ڈراپ ڈاؤن جائزہ میں اور ہوم پیج پر پروجیکٹ کارڈز پر دکھایا جائے گا۔", "app.containers.AdminPage.ProjectEdit.topicLabel": "ٹی<PERSON>ز", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "اس پروجیکٹ کے لیے {topicsCopy} کو منتخب کریں۔ صارفین ان کا استعمال کرکے پروجیکٹس کو فلٹر کرسکتے ہیں۔", "app.containers.AdminPage.ProjectEdit.totalBudget": "کل بجٹ", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "ٹرینڈنگ", "app.containers.AdminPage.ProjectEdit.typeform": "ٹائپ فارم", "app.containers.AdminPage.ProjectEdit.unassigned": "<PERSON>یر تفویض کردہ", "app.containers.AdminPage.ProjectEdit.unlimited": "لا محدود", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "ٹیمپلیٹ استعمال کریں۔", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "پروجیکٹ دیکھیں", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "رضاکارانہ", "app.containers.AdminPage.ProjectEdit.voteTermError": "تمام لوکلز کے لیے ووٹ کی شرائط کا تعین ہونا چاہیے۔", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# گروپ دیکھ سکتے ہیں} one {# گروپ دیکھ سکتے ہیں} other {# گروپ دیکھ سکتے ہیں}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "ایک واقعہ شامل کریں۔", "app.containers.AdminPage.ProjectEvents.additionalInformation": "اضافی معلومات", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "پتہ 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "تقریب کے مقام کا گلی کا پتہ", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "پتہ 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "مثلاً اپٹ، سویٹ، بلڈنگ", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "ایڈریس کی اضافی معلومات جو مقام کی شناخت میں مدد کر سکتی ہے جیسے کہ عمارت کا نام، منزل نمبر وغیرہ۔", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.cizenlab.co/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "سپورٹ مضمون دیکھیں", "app.containers.AdminPage.ProjectEvents.customButtonLink": "بیرونی لنک", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "ایک بیرونی یو آر ایل کا لنک شامل کریں (مثال کے طور پر ایونٹ سروس یا ٹکٹنگ ویب سائٹ)۔ اسے ترتیب دینے سے حاضری کے بٹن کے ڈیفالٹ رویے کو اوور رائیڈ کر دیا جائے گا۔", "app.containers.AdminPage.ProjectEvents.customButtonText": "حسب ضرورت بٹن کا متن", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "بیرونی URL سیٹ ہونے پر بٹن کے متن کو \"رجسٹر\" کے علاوہ کسی قدر پر سیٹ کریں۔", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "شروع کریں۔", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "ختم", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "حذف کریں۔", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "کیا آپ واقعی اس ایونٹ کو حذف کرنا چاہتے ہیں؟ اس کو کالعدم کرنے کا کوئی طریقہ نہیں ہے!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "واقعہ کی تفصیل", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "ترمیم کریں۔", "app.containers.AdminPage.ProjectEvents.editEventTitle": "ایونٹ میں ترمیم کریں۔", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "براہ راست پلیٹ فارم سے رجسٹر کرنے والوں کو ای میل کرنے کے لیے، منتظمین کو {userTabLink} ٹیب میں ایک صارف گروپ بنانا چاہیے۔ {supportArticleLink}", "app.containers.AdminPage.ProjectEvents.eventDates": "ایونٹ کی تاریخیں۔", "app.containers.AdminPage.ProjectEvents.eventImage": "واقعہ کی تصویر", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "ایونٹ کی تصویر کا متبادل متن", "app.containers.AdminPage.ProjectEvents.eventLocation": "واقعہ کا مقام", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "رجسٹروں کو برآمد کریں۔", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "منسلکات (زیادہ سے زیادہ 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "منسلکات واقعہ کی تفصیل کے نیچے دکھائے گئے ہیں۔", "app.containers.AdminPage.ProjectEvents.locationLabel": "مقام", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "زیادہ سے زیادہ اندراج کرنے والے", "app.containers.AdminPage.ProjectEvents.newEventTitle": "ایک نیا ایونٹ بنائیں", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "آن لائن ایونٹ کا لنک", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "اگر آپ کا ایونٹ آن لائن ہے تو اس کا لنک یہاں شامل کریں۔", "app.containers.AdminPage.ProjectEvents.preview": "پیش نظارہ", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "نقشہ کے مقام کو بہتر بنائیں", "app.containers.AdminPage.ProjectEvents.refineOnMap": "نقشے پر مقام کو بہتر بنائیں", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "آپ ذیل کے نقشے پر کلک کر کے اس بات کو بہتر کر سکتے ہیں کہ آپ کا ایونٹ لوکیشن مارکر کہاں دکھایا گیا ہے۔", "app.containers.AdminPage.ProjectEvents.register": "رجسٹر کریں۔", "app.containers.AdminPage.ProjectEvents.registerButton": "رجسٹر بٹن", "app.containers.AdminPage.ProjectEvents.registrant": "رجسٹر کرنے والا", "app.containers.AdminPage.ProjectEvents.registrants": "رجسٹر کرنے والے", "app.containers.AdminPage.ProjectEvents.registrationLimit": "رجسٹریشن کی حد", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "محفوظ کریں۔", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "ہم آپ کی تبدیلیاں محفوظ نہیں کر سکے، براہ کرم دوبارہ کوشش کریں۔", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "کامیابی!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "آپ کی تبدیلیاں محفوظ ہو گئی ہیں۔", "app.containers.AdminPage.ProjectEvents.searchForLocation": "ایک مقام تلاش کریں۔", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "آنے والے ایونٹس کو اس پروجیکٹس سے لنک کریں اور انہیں پروجیکٹ کے ایونٹ پیج پر دکھائیں۔", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "عنوان اور تاریخیں۔", "app.containers.AdminPage.ProjectEvents.titleEvents": "پروجیکٹ کے واقعات", "app.containers.AdminPage.ProjectEvents.titleLabel": "واقعہ کا نام", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "بٹن کو ایک بیرونی URL سے لنک کریں۔", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "پہلے سے طے شدہ طور پر، ان پلیٹ فارم ایونٹ رجسٹر بٹن دکھایا جائے گا جو صارفین کو ایونٹ کے لیے رجسٹر کرنے کی اجازت دیتا ہے۔ اس کی بجائے آپ اسے کسی بیرونی URL سے لنک کرنے کے لیے تبدیل کر سکتے ہیں۔", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "ایونٹ رجسٹر کرنے والوں کی تعداد کو محدود کریں۔", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "ایونٹ رجسٹر کرنے والوں کی زیادہ سے زیادہ تعداد مقرر کریں۔ اگر حد ہو جاتی ہے تو مزید رجسٹریشن قبول نہیں کی جائے گی۔", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/ایڈمن/صارفین", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "صارفین", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "اپنے پروجیکٹ میں فائلیں شامل کریں۔", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "اس فہرست سے فائلوں کو اپنے پروجیکٹ، مراحل اور واقعات سے منسلک کریں۔", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "سینس میکنگ میں سیاق و سباق کے طور پر فائلیں شامل کریں۔", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "سیاق و سباق اور بصیرت فراہم کرنے کے لیے اپنے Sensemaking پروجیکٹ میں فائلیں شامل کریں۔", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "جلد آرہا ہے۔", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "سروے کی مطابقت پذیری کریں، انٹرویوز اپ لوڈ کریں، اور AI کو اپنے ڈیٹا پر نقطوں کو جوڑنے دیں۔", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "کوئی بھی فائل اپ لوڈ کریں۔", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "فائلوں کا تجزیہ کرنے کے لیے AI کا استعمال کریں۔", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "پروسیس ٹرانسکرپٹس، وغیرہ", "app.containers.AdminPage.ProjectFiles.addFiles": "فائلیں شامل کریں۔", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "AI سے چلنے والی بصیرتیں۔", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "اپ لوڈ کردہ فائلوں کا تجزیہ کریں اور کلیدی عنوانات کو سامنے رکھیں۔", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "AI پروسیسنگ کا استعمال کرتے ہوئے ان فائلوں کے جدید تجزیات کی اجازت دیں۔", "app.containers.AdminPage.ProjectFiles.askButton": "پوچھو", "app.containers.AdminPage.ProjectFiles.categoryLabel": "زمرہ", "app.containers.AdminPage.ProjectFiles.chooseFiles": "فائلوں کا انتخاب کریں۔", "app.containers.AdminPage.ProjectFiles.close": "بند", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "تصدیق کریں اور اپ لوڈ کریں۔", "app.containers.AdminPage.ProjectFiles.confirmDelete": "کیا آپ واقعی اس فائل کو حذف کرنا چاہتے ہیں؟", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "مارک ڈاؤن فائل لوڈ نہیں ہو سکی۔", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "CSV پیش نظارہ لوڈ نہیں ہو سکا۔", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "زیادہ سے زیادہ 50 قطاریں CSV پیش نظارہ میں دکھائی جاتی ہیں۔", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "پیش منظر کے لیے CSV فائل بہت بڑی ہے۔", "app.containers.AdminPage.ProjectFiles.deleteFile2": "فائل کو حذف کریں۔", "app.containers.AdminPage.ProjectFiles.description": "تفصیل", "app.containers.AdminPage.ProjectFiles.done": "ہو گیا", "app.containers.AdminPage.ProjectFiles.downloadFile": "فائل ڈاؤن لوڈ کریں۔", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "مکمل فائل ڈاؤن لوڈ کریں۔", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "کسی بھی فائل کو یہاں گھسیٹیں اور چھوڑیں یا", "app.containers.AdminPage.ProjectFiles.editFile": "فائل میں ترمیم کریں۔", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "تفصیل", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "فائل کے نام میں ڈاٹ نہیں ہوسکتا ہے۔", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "فائل کا نام", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "فائل کا نام درکار ہے۔", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "فائل ڈاؤن لوڈ کریں۔", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "پیش نظارہ", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "یہ فائل اپ لوڈ نہیں کی جائے گی، کیونکہ یہ 50 MB کی زیادہ سے زیادہ حد سے زیادہ ہے۔", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "تمام فائلیں کامیابی کے ساتھ اپ لوڈ ہو گئیں۔", "app.containers.AdminPage.ProjectFiles.generatingPreview": "پیش منظر تیار ہو رہا ہے...", "app.containers.AdminPage.ProjectFiles.info_sheet": "معلومات", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "مثال کے طور پر WAV، MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "آڈیو انٹرویوز، ٹاؤن ہال کی ریکارڈنگ", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "مثال کے طور پر PDF، DOCX، PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "رپورٹس، معلوماتی دستاویزات", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "جیسے PNG، JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "ا<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.interview": "انٹرویو", "app.containers.AdminPage.ProjectFiles.maxFilesError": "آپ ایک وقت میں زیادہ سے زیادہ صرف {maxFiles} فائلیں اپ لوڈ کر سکتے ہیں۔", "app.containers.AdminPage.ProjectFiles.meeting": "ملاقات", "app.containers.AdminPage.ProjectFiles.noFilesFound": "کوئی فائل نہیں ملی۔", "app.containers.AdminPage.ProjectFiles.other": "دیگر", "app.containers.AdminPage.ProjectFiles.policy": "پالی<PERSON>ی", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "اس فائل کی قسم کے لیے پیش نظارہ ابھی تک تعاون یافتہ نہیں ہے۔", "app.containers.AdminPage.ProjectFiles.report": "رپورٹ", "app.containers.AdminPage.ProjectFiles.retryUpload": "دوبارہ اپ لوڈ کرنے کی کوشش کریں۔", "app.containers.AdminPage.ProjectFiles.save": "محفوظ کریں۔", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "فائل کامیابی کے ساتھ اپ ڈیٹ ہو گئی۔", "app.containers.AdminPage.ProjectFiles.searchFiles": "فائلیں تلاش کریں۔", "app.containers.AdminPage.ProjectFiles.selectFileType": "فائل کی قسم", "app.containers.AdminPage.ProjectFiles.strategic_plan": "اسٹریٹجک منصوبہ", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "آپ ایک وقت میں زیادہ سے زیادہ صرف {maxFiles} فائلیں اپ لوڈ کر سکتے ہیں۔", "app.containers.AdminPage.ProjectFiles.unknown": "نامعلوم", "app.containers.AdminPage.ProjectFiles.upload": "اپ لوڈ کریں۔", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# فائل} other {# فائلیں}} کامیابی کے ساتھ اپ لوڈ ہو گئیں، {numberOfErrors, plural, one {# ایرر} other {# غلطیاں}}۔", "app.containers.AdminPage.ProjectFiles.viewFile": "فائل دیکھیں", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "تمام فیلڈز کو سکیڑیں۔", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "فیلڈ کی تفصیل", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "ان پٹ فارم میں ترمیم کریں۔", "app.containers.AdminPage.ProjectIdeaForm.enabled": "فعال", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "اس فیلڈ کو شامل کریں۔", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "کچھ غلط ہو گیا، براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "تمام فیلڈز کو پھیلائیں۔", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "ان پٹ فارم", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "وضاحت کریں کہ کون سی معلومات فراہم کی جانی چاہیے، مختصر وضاحتیں یا ہدایات شامل کریں تاکہ شرکاء کے جوابات کی رہنمائی کی جا سکے اور یہ واضح کریں کہ آیا ہر فیلڈ اختیاری ہے یا مطلوب ہے۔", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "وضاحت کریں کہ کون سی معلومات فراہم کی جانی چاہیے، مختصر وضاحتیں یا ہدایات شامل کریں تاکہ شرکاء کے جوابات کی رہنمائی کی جا سکے اور یہ واضح کریں کہ آیا ہر فیلڈ اختیاری ہے یا مطلوب", "app.containers.AdminPage.ProjectIdeaForm.required": "درکار ہے۔", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "اس فیلڈ کو پُر کرنے کی ضرورت ہے۔", "app.containers.AdminPage.ProjectIdeaForm.save": "محفوظ کریں۔", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "آپ کی تبدیلیاں محفوظ ہو گئی ہیں۔", "app.containers.AdminPage.ProjectIdeaForm.saved": "محفوظ کیا گیا!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "فارم دیکھیں", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "<PERSON>و<PERSON><PERSON>ار ای میلز", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "آپ فیز لیول پر متحرک ای میلز کو کنفیگر کر سکتے ہیں۔", "app.containers.AdminPage.ProjectTimeline.datesLabel": "تاریخیں", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "سروے میں حصہ لیں۔", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "سروے", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "کیا آپ واقعی اس مرحلے کو حذف کرنا چاہتے ہیں؟", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "مرحلے کی تفصیل", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "فیز میں ترمیم کریں۔", "app.containers.AdminPage.ProjectTimeline.endDate": "اختتامی تاریخ", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "اختتامی تاریخ", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "منسلکات (زیادہ سے زیادہ 50MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "نیا مرحلہ", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "اس مرحلے کی کوئی پہلے سے طے شدہ اختتامی تاریخ نہیں ہے۔", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "کچھ طریقوں کے نتائج کا اشتراک (جیسے ووٹنگ کے نتائج) کو اس وقت تک متحرک نہیں کیا جائے گا جب تک کہ ایک اختتامی تاریخ منتخب نہ ہو جائے۔", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "جیسے ہی آپ اس کے بعد ایک مرحلہ شامل کریں گے، یہ اس مرحلے میں ایک اختتامی تاریخ کا اضافہ کر دے گا۔", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "اس کے لیے آخری تاریخ کا انتخاب نہ کرنے کا مطلب یہ ہے کہ:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "پیش نظارہ", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "تبدیلیاں محفوظ کریں۔", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "فارم جمع کرانے میں ایک خامی تھی، براہ کرم دوبارہ کوشش کریں۔", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "محفوظ کیا گیا!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "آپ کی تبدیلیاں محفوظ ہو گئی ہیں۔", "app.containers.AdminPage.ProjectTimeline.startDate": "تاریخ شروع", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "تاریخ شروع", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "بٹن", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "سروے کا عنوان", "app.containers.AdminPage.ProjectTimeline.titleLabel": "فیز کا نام", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "منسلکات اپ لوڈ کریں۔", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_دوبارہ تقسیم", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "اصطلاحات (ہوم پیج فلٹر)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "صفحہ اول کے فلٹر میں ٹیگز کو کیسے بلایا جائے؟ مثال کے طور پر ٹیگز، زمرہ جات، محکمے، ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "ٹیگز کو کنفیگر کیا جا سکتا ہے {topicManagerLink}۔", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "یہاں", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "ایک ٹیگ کے لیے اصطلاح (واحد)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "ٹیگ", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "متعدد ٹیگز کے لیے اصطلاح (کثرت)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "ٹی<PERSON>ز", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "فیلڈ شامل کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "ایک نیا رجسٹریشن فیلڈ شامل کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "آپشن شامل کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "جواب کی شکل", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "جواب کا فارمیٹ فراہم کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "جواب کا آپشن", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "تمام زبانوں کے لیے جواب کا اختیار فراہم کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "جواب کا آپشن محفوظ کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "جواب کا آپشن کامیابی سے محفوظ ہو گیا۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "انتخاب کے جوابات دیں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "فیلڈز", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "اس ترتیب کا تعین کرنے کے لیے فیلڈز کو گھسیٹیں اور چھوڑیں جس میں وہ سائن اپ فارم میں نظر آتے ہیں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "پہلے سے طے شدہ فیلڈ", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "حذف کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "اختیاری متن سائن اپ فارم پر فیلڈ کے نام کے نیچے دکھایا گیا ہے۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "رہائش کی جگہ کے جواب کے انتخاب {geographicAreasTabLink}میں سیٹ کیے جا سکتے ہیں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "ترمیم کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "جواب کے آپشن میں ترمیم کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "تفصیل", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "فیلڈ کا نام", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "تمام زبانوں کے لیے فیلڈ کا نام فراہم کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "فیلڈ کی ترتیبات", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "ہاں-نہیں (چیک باکس)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "تاریخ", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "لمبا جواب", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "متعدد انتخاب (متعدد کو منتخب کریں)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "عد<PERSON>ی قدر", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "متعدد انتخاب (ایک کو منتخب کریں)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "مخت<PERSON>ر جواب", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "جغرافیائی علاقوں کا ٹیب", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "پوشیدہ میدان", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "اس فیلڈ کا جواب دینا ضروری بنائیں؟", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "حسب ضرورت فیلڈز", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "جواب کا اختیار شامل کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "منسوخ کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "حذف کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "کیا آپ واقعی رجسٹریشن کے اس سوال کے جواب کے آپشن کو حذف کرنا چاہتے ہیں؟ تمام ریکارڈز جن کا جواب مخصوص صارفین نے اس اختیار کے ساتھ دیا ہے وہ مستقل طور پر حذف ہو جائیں گے۔ اس کارروائی کو کالعدم نہیں کیا جا سکتا۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "کیا آپ واقعی اس رجسٹریشن سوال کو حذف کرنا چاہتے ہیں؟ تمام جوابات جو صارفین نے اس سوال کے لیے دیے ہیں انہیں مستقل طور پر حذف کر دیا جائے گا، اور اب یہ پروجیکٹس یا تجاویز میں نہیں پوچھا جائے گا۔ اس کارروائی کو کالعدم نہیں کیا جا سکتا۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "درکار ہے۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "فیلڈ کو محفوظ کریں۔", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "فیلڈ کامیابی کے ساتھ محفوظ ہو گئی۔", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "دو کالم", "app.containers.AdminPage.SettingsPage.addAreaButton": "ایک جغرافیائی علاقہ شامل کریں۔", "app.containers.AdminPage.SettingsPage.addTopicButton": "ٹیگ شامل کریں۔", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "جانور - مثال کے طور پر ہاتھی بلی", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "صارف - جیسے صارف 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "منتخب کریں کہ کون سے منتظمین کو پروجیکٹس کی منظوری کے لیے اطلاعات موصول ہوں گی۔ فولڈر مینیجر اپنے فولڈرز کے اندر موجود تمام پروجیکٹس کے لیے بطور ڈیفالٹ منظوری دینے والے ہوتے ہیں۔", "app.containers.AdminPage.SettingsPage.approvalSave": "محفوظ کریں۔", "app.containers.AdminPage.SettingsPage.approvalTitle": "پروجیکٹ کی منظوری کی ترتیبات", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "کیا آپ واقعی اس علاقے کو حذف کرنا چاہتے ہیں؟", "app.containers.AdminPage.SettingsPage.areaTerm": "ایک علاقے کے لیے اصطلاح (واحد)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "علا<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "حذف کریں۔", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "ترمیم کریں۔", "app.containers.AdminPage.SettingsPage.areasTerm": "متعدد علاقوں کے لیے اصطلاح (کثرت)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "علاقوں", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "کم از کم ایک زبان منتخب کریں۔", "app.containers.AdminPage.SettingsPage.avatarsTitle": "اوتار", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "اوتار دکھائیں۔", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "غیر رجسٹرڈ مہمانوں کو شرکاء کی پروفائل تصویریں اور ان کی تعداد دکھائیں۔", "app.containers.AdminPage.SettingsPage.bannerHeader": "ہیڈر کا متن", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "غیر رجسٹرڈ زائرین کے لیے ہیڈر کا متن", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "غیر رجسٹرڈ زائرین کے لیے ذیلی ہیڈر کا متن", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "ذیلی ہیڈر کا متن", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "بینر کا متن", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "کے لیے پیش نظارہ دکھائیں۔", "app.containers.AdminPage.SettingsPage.brandingDescription": "اپنا لوگو شامل کریں اور پلیٹ فارم کے رنگ سیٹ کریں۔", "app.containers.AdminPage.SettingsPage.brandingTitle": "پلیٹ فارم برانڈنگ", "app.containers.AdminPage.SettingsPage.cancel": "منسوخ کریں۔", "app.containers.AdminPage.SettingsPage.chooseLayout": "لے آؤٹ", "app.containers.AdminPage.SettingsPage.color_primary": "بنیادی رنگ", "app.containers.AdminPage.SettingsPage.color_secondary": "ثانوی رنگ", "app.containers.AdminPage.SettingsPage.color_text": "متن کا رنگ", "app.containers.AdminPage.SettingsPage.colorsTitle": "رنگ", "app.containers.AdminPage.SettingsPage.confirmHeader": "کیا آپ واقعی اس ٹیگ کو حذف کرنا چاہتے ہیں؟", "app.containers.AdminPage.SettingsPage.contentModeration": "مواد کی اعتدال پسندی۔", "app.containers.AdminPage.SettingsPage.ctaHeader": "بٹن", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "حسب ضرورت صفحہ ہیڈر | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "حسب ضرورت", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "بٹن کا متن", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "بٹن لنک", "app.containers.AdminPage.SettingsPage.defaultTopic": "ڈیفالٹ ٹیگ", "app.containers.AdminPage.SettingsPage.delete": "حذف کریں۔", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "حذف کریں۔", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "یہ تمام موجودہ پوسٹس سے ٹیگ کو حذف کر دے گا۔ یہ تبدیلی تمام منصوبوں پر لاگو ہوگی۔", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "وہ ٹیگز شامل کریں اور حذف کریں جنہیں آپ پوسٹس کی درجہ بندی کرنے کے لیے اپنے پلیٹ فارم پر استعمال کرنا چاہتے ہیں۔ آپ {adminProjectsLink}میں مخصوص پروجیکٹس میں ٹیگز شامل کر سکتے ہیں۔", "app.containers.AdminPage.SettingsPage.desktop": "ڈیسک ٹاپ", "app.containers.AdminPage.SettingsPage.editFormTitle": "ایریا میں ترمیم کریں۔", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "ترمیم کریں۔", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "ٹیگ میں ترمیم کریں۔", "app.containers.AdminPage.SettingsPage.fieldDescription": "علاقے کی تفصیل", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "یہ تفصیل صرف اندرونی تعاون کے لیے ہے اور صارفین کو نہیں دکھائی جاتی ہے۔", "app.containers.AdminPage.SettingsPage.fieldTitle": "علاقے کا نام", "app.containers.AdminPage.SettingsPage.fieldTitleError": "تمام زبانوں کے لیے علاقے کا نام فراہم کریں۔", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "ہر علاقے کے لیے آپ جو نام منتخب کرتے ہیں اسے رجسٹریشن فیلڈ آپشن کے طور پر اور ہوم پیج پر پروجیکٹس کو فلٹر کرنے کے لیے استعمال کیا جا سکتا ہے۔", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "ٹیگ محفوظ کریں۔", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "ٹیگ کا نام", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "تمام زبانوں کے لیے ٹیگ کا نام فراہم کریں۔", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "ہر ٹیگ کے لیے آپ جو نام منتخب کریں گے وہ پلیٹ فارم کے صارفین کو نظر آئے گا۔", "app.containers.AdminPage.SettingsPage.fixedRatio": "مقررہ تناسب", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "بینر کی یہ قسم ان تصاویر کے ساتھ بہترین کام کرتی ہے جنہیں کاٹا نہیں جانا چاہیے، جیسے متن والی تصاویر، لوگو یا مخصوص عناصر جو آپ کے شہریوں کے لیے اہم ہیں۔ جب صارفین سائن ان ہوتے ہیں تو اس بینر کو بنیادی رنگ میں ایک ٹھوس باکس سے بدل دیا جاتا ہے۔ آپ اس رنگ کو عام ترتیبات میں سیٹ کر سکتے ہیں۔ تجویز کردہ تصویر کے استعمال کے بارے میں مزید معلومات ہمارے {link}پر مل سکتی ہیں۔", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "علم کی بنیاد", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "پوری چوڑائی", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "یہ بینر ایک بہترین بصری اثر کے لیے پوری چوڑائی پر پھیلا ہوا ہے۔ تصویر زیادہ سے زیادہ جگہ کا احاطہ کرنے کی کوشش کرے گی، جس کی وجہ سے یہ ہر وقت نظر نہیں آتی۔ آپ اس بینر کو کسی بھی رنگ کے اوورلے کے ساتھ جوڑ سکتے ہیں۔ تجویز کردہ تصویر کے استعمال کے بارے میں مزید معلومات ہمارے {link}پر مل سکتی ہیں۔", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "علم کی بنیاد", "app.containers.AdminPage.SettingsPage.header": "ہوم پیج بینر", "app.containers.AdminPage.SettingsPage.headerDescription": "ہوم پیج بینر کی تصویر اور متن کو حسب ضرورت بنائیں۔", "app.containers.AdminPage.SettingsPage.header_bg": "بینر کی تصویر", "app.containers.AdminPage.SettingsPage.helmetDescription": "ایڈمن کی ترتیبات کا صفحہ", "app.containers.AdminPage.SettingsPage.helmetTitle": "ایڈمن کی ترتیبات کا صفحہ", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "ہوم پیج کے نیچے حسب ضرورت سیکشن میں اپنا مواد شامل کریں۔", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "ہوم پیج ہیڈر | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "تصویر کا اوورلے رنگ", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "تصویری اوورلے کی دھندلاپن", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.cizenlab.co/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "نامناسب مواد کا پتہ لگائیں۔", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "پلیٹ فارم پر پوسٹ کردہ نامناسب مواد کا خود بخود پتہ لگائیں۔", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "اس خصوصیت کے فعال ہونے کے دوران، شرکاء کی طرف سے پوسٹ کردہ ان پٹ، تجاویز اور تبصروں کا خود بخود جائزہ لیا جائے گا۔ ممکنہ طور پر نامناسب مواد پر مشتمل پوسٹس کو بلاک نہیں کیا جائے گا، لیکن {linkToActivityPage} صفحہ پر نظرثانی کے لیے نمایاں کیا جائے گا۔", "app.containers.AdminPage.SettingsPage.languages": "زبانیں", "app.containers.AdminPage.SettingsPage.languagesTooltip": "آپ متعدد زبانیں منتخب کر سکتے ہیں جن میں آپ اپنا پلیٹ فارم صارفین کو پیش کرنا چاہتے ہیں۔ آپ کو ہر منتخب زبان کے لیے مواد بنانے کی ضرورت ہوگی۔", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "سرگرمی", "app.containers.AdminPage.SettingsPage.logo": "لوگو", "app.containers.AdminPage.SettingsPage.noHeader": "براہ کرم ایک ہیڈر امیج اپ لوڈ کریں۔", "app.containers.AdminPage.SettingsPage.no_button": "کوئی بٹن نہیں۔", "app.containers.AdminPage.SettingsPage.organizationName": "شہر یا تنظیم کا نام", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "تمام زبانوں کے لیے تنظیم کا نام یا شہر فراہم کریں۔", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "اوورلے کو فعال کریں۔", "app.containers.AdminPage.SettingsPage.phone": "فون", "app.containers.AdminPage.SettingsPage.platformConfiguration": "پلیٹ فارم کی ترتیب", "app.containers.AdminPage.SettingsPage.population": "آبادی", "app.containers.AdminPage.SettingsPage.populationMinError": "آبادی ایک مثبت نمبر ہونی چاہیے۔", "app.containers.AdminPage.SettingsPage.populationTooltip": "آپ کے علاقے کے باشندوں کی کل تعداد۔ اس کا استعمال شرکت کی شرح کا حساب لگانے کے لیے کیا جاتا ہے۔ اگر قابل اطلاق نہ ہو تو خالی چھوڑ دیں۔", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "بے حرمتی روکنے والا", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "ان پٹ، تجاویز اور تبصروں کو بلاک کریں جن میں سب سے زیادہ اطلاع دی گئی جارحانہ الفاظ شامل ہوں۔", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "یہ متن پروجیکٹس کے اوپر ہوم پیج پر دکھایا گیا ہے۔", "app.containers.AdminPage.SettingsPage.projectsSettings": "منصوبے کی ترتیبات", "app.containers.AdminPage.SettingsPage.projects_header": "پروجیکٹس ہیڈر", "app.containers.AdminPage.SettingsPage.registrationFields": "رجسٹریشن فیلڈز", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "اپنے رجسٹریشن فارم کے اوپری حصے میں ایک مختصر تفصیل فراہم کریں۔", "app.containers.AdminPage.SettingsPage.registrationTitle": "رجسٹریشن", "app.containers.AdminPage.SettingsPage.save": "محفوظ کریں۔", "app.containers.AdminPage.SettingsPage.saveArea": "علاقہ محفوظ کریں۔", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "کچھ غلط ہو گیا، براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.AdminPage.SettingsPage.saveSuccess": "کامیابی!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "آپ کی تبدیلیاں محفوظ ہو گئی ہیں۔", "app.containers.AdminPage.SettingsPage.selectApprovers": "منظور کنندگان کو منتخب کریں۔", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "ان علاقوں کو منتخب کریں جو صارفین کو رجسٹریشن کے بعد فالو کرنے کے لیے دکھائے جائیں گے۔", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "وہ عنوانات منتخب کریں جو صارفین کو رجسٹریشن کے بعد فالو کرنے کے لیے دکھائے جائیں گے۔", "app.containers.AdminPage.SettingsPage.settingsSavingError": "محفوظ نہیں کر سکا۔ ترتیب کو دوبارہ تبدیل کرنے کی کوشش کریں۔", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"سائن اپ کریں\"", "app.containers.AdminPage.SettingsPage.signed_in": "رجسٹرڈ زائرین کے لیے بٹن", "app.containers.AdminPage.SettingsPage.signed_out": "غیر رجسٹرڈ زائرین کے لیے بٹن", "app.containers.AdminPage.SettingsPage.signupFormText": "رجسٹریشن مددگار کا متن", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "سائن اپ فارم کے اوپری حصے میں ایک مختصر تفصیل شامل کریں۔", "app.containers.AdminPage.SettingsPage.statuses": "سٹیٹس", "app.containers.AdminPage.SettingsPage.step1": "ای میل اور پاس ورڈ مرحلہ", "app.containers.AdminPage.SettingsPage.step1Tooltip": "یہ سائن اپ فارم (نام، ای میل، پاس ورڈ) کے پہلے صفحہ کے اوپر دکھایا گیا ہے۔", "app.containers.AdminPage.SettingsPage.step2": "رجسٹریشن کے سوالات کا مرحلہ", "app.containers.AdminPage.SettingsPage.step2Tooltip": "یہ سائن اپ فارم کے دوسرے صفحہ کے اوپر دکھایا گیا ہے (اضافی رجسٹریشن فیلڈز)۔", "app.containers.AdminPage.SettingsPage.subtitleAreas": "ان جغرافیائی علاقوں کی وضاحت کریں جنہیں آپ اپنے پلیٹ فارم کے لیے استعمال کرنا چاہیں گے، جیسے محلے، بورو یا اضلاع۔ آپ ان جغرافیائی علاقوں کو پروجیکٹس کے ساتھ منسلک کر سکتے ہیں (لینڈنگ پیج پر فلٹر کیا جا سکتا ہے) یا شرکاء سے اپنے رہائشی علاقے کو رجسٹریشن فیلڈ کے طور پر منتخب کرنے کے لیے کہہ سکتے ہیں تاکہ اسمارٹ گروپس بنائیں اور رسائی کے حقوق کی وضاحت کریں۔", "app.containers.AdminPage.SettingsPage.subtitleBasic": "منتخب کریں کہ لوگ آپ کی تنظیم کا نام کیسے دیکھیں گے، اپنے پلیٹ فارم کی زبانیں منتخب کریں اور اپنی ویب سائٹ سے لنک کریں۔", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "فراہم کردہ ذیلی عنوان زیادہ سے زیادہ اجازت شدہ حروف کی حد سے زیادہ ہے (90 حروف)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "اصطلاحات", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "ترتیبات کامیابی کے ساتھ اپ ڈیٹ ہو گئیں۔", "app.containers.AdminPage.SettingsPage.tabAreas1": "علاقے", "app.containers.AdminPage.SettingsPage.tabBranding": "برانڈنگ", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "ان پٹ سٹیٹس", "app.containers.AdminPage.SettingsPage.tabPolicies": "پالیسیاں", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "پروجیکٹ کی منظوری", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "تجویز کے حالات", "app.containers.AdminPage.SettingsPage.tabRegistration": "رجسٹریشن", "app.containers.AdminPage.SettingsPage.tabSettings": "جن<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabTopics2": "ٹی<PERSON>ز", "app.containers.AdminPage.SettingsPage.tabWidgets": "ویجیٹ", "app.containers.AdminPage.SettingsPage.tablet": "گو<PERSON>ی", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "وضاحت کریں کہ آپ اپنے پروجیکٹس کے لیے کون سی جغرافیائی اکائی استعمال کرنا چاہیں گے (مثلاً محلے، اضلاع، بورو وغیرہ)", "app.containers.AdminPage.SettingsPage.titleAreas": "جغرافیائی علاقے", "app.containers.AdminPage.SettingsPage.titleBasic": "عمومی ترتیبات", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "فراہم کردہ عنوان زیادہ سے زیادہ اجازت شدہ حروف کی حد سے زیادہ ہے (35 حروف)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "ٹیگ مینیجر", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "یہ بینر خاص طور پر ان تصاویر کے لیے مفید ہے جو ٹائٹل، سب ٹائٹل یا بٹن کے متن کے ساتھ اچھی طرح کام نہیں کرتی ہیں۔ ان اشیاء کو بینر کے نیچے دھکیل دیا جائے گا۔ تجویز کردہ تصویر کے استعمال کے بارے میں مزید معلومات ہمارے {link}پر مل سکتی ہیں۔", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "علم کی بنیاد", "app.containers.AdminPage.SettingsPage.twoRowLayout": "دو قطاریں۔", "app.containers.AdminPage.SettingsPage.urlError": "URL درست نہیں ہے۔", "app.containers.AdminPage.SettingsPage.urlPatternError": "ایک درست URL درج کریں۔", "app.containers.AdminPage.SettingsPage.urlTitle": "ویب سائٹ", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "آپ اپنی ویب سائٹ پر ایک لنک شامل کر سکتے ہیں۔ یہ لنک ہوم پیج کے نیچے استعمال کیا جائے گا۔", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "منتخب کریں کہ ان کے پروفائل میں نام کے بغیر صارف پلیٹ فارم میں کیسے ظاہر ہوں گے۔ یہ اس وقت ہوگا جب آپ رسائی کے حقوق کو 'ای میل کی تصدیق' پر ایک مرحلے کے لیے سیٹ کریں گے۔ تمام معاملات میں، شرکت کرنے پر، صارفین اس پروفائل کے نام کو اپ ڈیٹ کر سکیں گے جو ہم نے خود ان کے لیے تیار کیا تھا۔", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "صارف نام ڈسپلے (صرف تصدیق شدہ ای میل والے صارفین کے لیے)", "app.containers.AdminPage.SideBar.administrator": "ایڈمنسٹریٹر", "app.containers.AdminPage.SideBar.communityPlatform": "کمیونٹی پلیٹ فارم", "app.containers.AdminPage.SideBar.community_monitor": "کمیونٹی مانیٹر", "app.containers.AdminPage.SideBar.customerPortal": "کسٹمر پورٹل", "app.containers.AdminPage.SideBar.dashboard": "ڈیش بورڈ", "app.containers.AdminPage.SideBar.emails": "ای میلز", "app.containers.AdminPage.SideBar.folderManager": "فولڈر مینیجر", "app.containers.AdminPage.SideBar.groups": "گروپس", "app.containers.AdminPage.SideBar.guide": "گائیڈ", "app.containers.AdminPage.SideBar.inputManager": "ان پٹ مینیجر", "app.containers.AdminPage.SideBar.insights": "رپورٹنگ", "app.containers.AdminPage.SideBar.inspirationHub": "حوصلہ افزائی کا مرکز", "app.containers.AdminPage.SideBar.knowledgeBase": "علم کی بنیاد", "app.containers.AdminPage.SideBar.language": "زبان", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "صفحات اور مینو", "app.containers.AdminPage.SideBar.messaging": "پیغام رسانی", "app.containers.AdminPage.SideBar.moderation": "سرگرمی", "app.containers.AdminPage.SideBar.notifications": "اطلاعات", "app.containers.AdminPage.SideBar.processing": "پروسیسنگ", "app.containers.AdminPage.SideBar.projectManager": "پروجیکٹ مینیجر", "app.containers.AdminPage.SideBar.projects": "پروجیکٹس", "app.containers.AdminPage.SideBar.settings": "ترتیبات", "app.containers.AdminPage.SideBar.signOut": "سائن آؤٹ کریں۔", "app.containers.AdminPage.SideBar.support": "حما<PERSON>ت", "app.containers.AdminPage.SideBar.toPlatform": "پلیٹ فارم پر", "app.containers.AdminPage.SideBar.tools": "اوزار", "app.containers.AdminPage.SideBar.user.myProfile": "میرا پروفائل", "app.containers.AdminPage.SideBar.users": "صارفین", "app.containers.AdminPage.SideBar.workshops": "ورکشاپس", "app.containers.AdminPage.Topics.addTopics": "شامل کریں۔", "app.containers.AdminPage.Topics.browseTopics": "ٹیگز براؤز کریں۔", "app.containers.AdminPage.Topics.cancel": "منسوخ کریں۔", "app.containers.AdminPage.Topics.confirmHeader": "کیا آپ واقعی اس پروجیکٹ ٹیگ کو حذف کرنا چاہتے ہیں؟", "app.containers.AdminPage.Topics.delete": "حذف کریں۔", "app.containers.AdminPage.Topics.deleteTopicLabel": "حذف کریں۔", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "یہ ٹیگ اب اس پروجیکٹ میں نئی پوسٹس میں شامل نہیں کیا جا سکے گا۔", "app.containers.AdminPage.Topics.inputForm": "ان پٹ فارم", "app.containers.AdminPage.Topics.lastTopicWarning": "کم از کم ایک ٹیگ درکار ہے۔ اگر آپ ٹیگز استعمال نہیں کرنا چاہتے ہیں تو انہیں {ideaFormLink} ٹیب میں غیر فعال کیا جا سکتا ہے۔", "app.containers.AdminPage.Topics.projectTopicsDescription": "آپ ان ٹیگز کو شامل اور حذف کر سکتے ہیں جو اس پروجیکٹ میں پوسٹس کے لیے تفویض کیے جا سکتے ہیں۔", "app.containers.AdminPage.Topics.remove": "ہٹا دیں۔", "app.containers.AdminPage.Topics.title": "پروجیکٹ ٹیگز", "app.containers.AdminPage.Topics.topicManager": "ٹیگ مینیجر", "app.containers.AdminPage.Topics.topicManagerInfo": "اگر آپ اضافی پروجیکٹ ٹیگز شامل کرنا چاہتے ہیں، تو آپ {topicManagerLink}میں ایسا کر سکتے ہیں۔", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "ایک نیا گروپ شامل کریں۔", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "گروپ کا نام", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "گروپ کا نام بتائیں", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "ایک دستی گروپ بنائیں", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "آپ کو کس قسم کے گروپ کی ضرورت ہے؟", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "گروپ کو محفوظ کریں۔", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "ایک دستی گروپ بنائیں", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "ایک سمارٹ گروپ بنائیں", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "گروپس کے بارے میں مزید جانیں۔", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "آپ جائزہ سے صارفین کو منتخب کر کے انہیں اس گروپ میں شامل کر سکتے ہیں۔", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "آپ شرائط کی وضاحت کر سکتے ہیں اور شرائط پر پورا اترنے والے صارفین خود بخود اس گروپ میں شامل ہو جاتے ہیں۔", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "دستی گروپ", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "سمارٹ گروپ", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "اس گروپ میں ابھی تک کوئی نہیں ہے۔", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "کچھ صارفین کو دستی طور پر شامل کرنے کے لیے {allUsersLink} پر جائیں۔", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "کوئی صارف آپ کی تلاش سے مماثل نہیں ہے۔", "app.containers.AdminPage.Users.GroupsPanel.select": "منتخب کریں۔", "app.containers.AdminPage.Users.UsersGroup.exportAll": "تمام برآمد کریں۔", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "گروپ میں صارفین کو برآمد کریں۔", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "منتخب کردہ برآمد", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "کیا آپ واقعی اس گروپ کو حذف کرنا چاہتے ہیں؟", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "صارفین کو گروپس میں شامل کرتے وقت ایک خرابی پیش آگئی، براہ کرم دوبارہ کوشش کریں۔", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "گروپ سے نکال دیں۔", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "اس گروپ سے منتخب صارفین کو حذف کریں؟", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "گروپ سے صارفین کو حذف کرتے وقت ایک خرابی پیش آگئی، براہ کرم دوبارہ کوشش کریں۔", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "صارفین کو گروپ میں شامل کریں۔", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "شامل کریں۔", "app.containers.AdminPage.groups.permissions.add": "شامل کریں۔", "app.containers.AdminPage.groups.permissions.addAnswer": "جواب شامل کریں۔", "app.containers.AdminPage.groups.permissions.addQuestion": "آبادیاتی سوالات شامل کریں۔", "app.containers.AdminPage.groups.permissions.answerChoices": "انتخاب کے جوابات دیں۔", "app.containers.AdminPage.groups.permissions.answerFormat": "جواب کی شکل", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "کم از کم ایک انتخاب فراہم کرنا ضروری ہے۔", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "یہ صارف اس پروجیکٹ پر مشتمل فولڈر کو ماڈریٹ کرتا ہے۔ اس پروجیکٹ کے لیے ان کے ماڈریٹر کے حقوق کو ہٹانے کے لیے، آپ یا تو ان کے فولڈر کے حقوق منسوخ کر سکتے ہیں یا پروجیکٹ کو کسی دوسرے فولڈر میں منتقل کر سکتے ہیں۔", "app.containers.AdminPage.groups.permissions.createANewQuestion": "ایک نیا سوال بنائیں", "app.containers.AdminPage.groups.permissions.createAQuestion": "ایک سوال بنائیں", "app.containers.AdminPage.groups.permissions.defaultField": "پہلے سے طے شدہ فیلڈ", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "حذف کریں۔", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "حذف کریں۔", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "براہ کرم تمام انتخاب کے لیے ایک عنوان فراہم کریں۔", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "ہاں-نہیں (چیک باکس)", "app.containers.AdminPage.groups.permissions.fieldType_date": "تاریخ", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "لمبا جواب", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "متعدد انتخاب (متعدد کو منتخب کریں)", "app.containers.AdminPage.groups.permissions.fieldType_number": "عد<PERSON>ی قدر", "app.containers.AdminPage.groups.permissions.fieldType_select": "متعدد انتخاب (ایک کو منتخب کریں)", "app.containers.AdminPage.groups.permissions.fieldType_text": "مخت<PERSON>ر جواب", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "دانے دار اجازتوں کو تبدیل کرنا آپ کے لائسنس کا حصہ نہیں ہے۔ اس کے بارے میں مزید جاننے کے لیے براہ کرم اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "کیا آپ واقعی اس گروپ کو پروجیکٹ سے ہٹانا چاہتے ہیں؟", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "ایک یا زیادہ گروپ منتخب کریں۔", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {کوئی ممبر نہیں} one {1 ممبر} other {{count} ممبران}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "براہ کرم تمام زبانوں میں عنوان بھریں۔", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "کیا آپ کو یقین ہے؟", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "پروجیکٹ مینیجر نہیں ملے", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "کچھ بھی نہیں دکھایا گیا ہے، کیوں کہ صارف اس پروجیکٹ میں کوئی کارروائی نہیں کر سکتا۔", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "صرف ایڈمن ہی نیا سوال بنا سکتے ہیں۔", "app.containers.AdminPage.groups.permissions.option1": "آپشن 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "زیر التواء دعوت", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "کون دستاویز کی تشریح کر سکتا ہے؟", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "ایک تقریب میں شرکت کے لیے کون سائن اپ کر سکتا ہے؟", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "ان پٹ پر کون تبصرہ کر سکتا ہے؟", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "تجاویز پر کون تبصرہ کر سکتا ہے؟", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "کون ایک تجویز پوسٹ کر سکتا ہے؟", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "ان پٹ پر کون رد عمل ظاہر کر سکتا ہے؟", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "کون ان پٹ جمع کر سکتا ہے؟", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "رائے شماری کون لے سکتا ہے؟", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "سروے کون لے سکتا ہے؟", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "کون رضاکارانہ خدمات انجام دے سکتا ہے؟", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "تجاویز پر کون ووٹ دے سکتا ہے؟", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "کون ووٹ دے سکتا ہے؟", "app.containers.AdminPage.groups.permissions.questionDescription": "سوال کی تفصیل", "app.containers.AdminPage.groups.permissions.questionTitle": "سوال کا عنوان", "app.containers.AdminPage.groups.permissions.save": "محفوظ کریں۔", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "کچھ غلط ہو گیا، براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.AdminPage.groups.permissions.saveSuccess": "کامیابی!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "آپ کی تبدیلیاں محفوظ ہو گئی ہیں۔", "app.containers.AdminPage.groups.permissions.select": "منتخب کریں۔", "app.containers.AdminPage.groups.permissions.selectValueError": "براہ کرم جواب کی قسم منتخب کریں۔", "app.containers.AdminPage.new.createAProject": "ایک پروجیکٹ بنائیں", "app.containers.AdminPage.new.fromScratch": "شروع سے", "app.containers.AdminPage.phase.methodPicker.addOn1": "شامل کریں۔", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "AI سے چلنے والی بصیرتیں۔", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "ایک وقت میں ایک خیال، اتفاق اور اختلاف کو ظاہر کرنے میں شرکاء کی مدد کریں۔", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "مشترکہ زمین تلاش کریں۔", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "ایک انٹرایکٹو پی ڈی ایف ایمبیڈ کریں اور Konveio کے ساتھ تبصرے اور تاثرات جمع کریں۔", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "کسی دستاویز پر رائے جمع کریں۔", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "فریق ثالث کا سروے شامل کریں۔", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "بیرونی سروے", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "اپنے صارفین کی اجتماعی ذہانت کو ٹیپ کریں۔ انہیں عوامی فورم میں پیش کرنے، خیالات پر تبادلہ خیال، اور/یا تاثرات فراہم کرنے کے لیے مدعو کریں۔", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "عوام میں ان پٹ اور آراء جمع کریں۔", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "معلومات شیئر کریں۔", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "پلیٹ فارم میں AI سے چلنے والی بصیرت کا فقدان ہے۔", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "ان پلیٹ فارم رپورٹنگ اور ڈیٹا ویژولائزیشن اور پروسیسنگ کا فقدان ہے۔", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "ان پلیٹ فارم رپورٹ بلڈر کے ساتھ لنک کریں۔", "app.containers.AdminPage.phase.methodPicker.logic1": "منطق", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "سوالات کی اقسام کی وسیع رینج", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "شرکاء کو وقت اور ووٹ کی حد کے ساتھ آئیڈیاز اپ لوڈ کرنے دیں۔", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "تجاویز، درخواستیں یا اقدام", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "فوری سروے", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "ایک مختصر، کثیر انتخابی سوالنامہ ترتیب دیں۔", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "صارفین کو معلومات فراہم کریں، دوسرے مراحل سے نتائج کا تصور کریں اور ڈیٹا سے بھرپور رپورٹس بنائیں۔", "app.containers.AdminPage.phase.methodPicker.survey1": "سروے", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "نجی سوالات کی وسیع اقسام کے ذریعے اپنے صارفین کی ضروریات اور سوچ کو سمجھیں۔", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "سروے کے اختیارات", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "ایک سروے بنائیں", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "صارفین سے سرگرمیوں اور وجوہات کے لیے رضاکارانہ طور پر کام کرنے یا پینل کے لیے شرکاء تلاش کرنے کو کہیں۔", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "شرکاء یا رضاکاروں کو بھرتی کریں۔", "app.containers.AdminPage.phase.methodPicker.votingDescription": "ووٹنگ کا طریقہ منتخب کریں، اور صارفین کو چند مختلف اختیارات کے درمیان ترجیح دیں۔", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "ووٹنگ یا ترجیحی مشق کا انعقاد کریں۔", "app.containers.AdminPage.projects.all.all": "تمام", "app.containers.AdminPage.projects.all.createProjectFolder": "نیا فولڈر", "app.containers.AdminPage.projects.all.existingProjects": "موجودہ منصوبے", "app.containers.AdminPage.projects.all.homepageWarning1": "نیویگیشن بار میں \"تمام پروجیکٹس\" ڈراپ ڈاؤن میں پروجیکٹس کی ترتیب ترتیب دینے کے لیے اس صفحہ کا استعمال کریں۔ اگر آپ اپنے ہوم پیج پر \"شائع شدہ پروجیکٹس اور فولڈرز\" یا \"پروجیکٹس اور فولڈرز (لیگیسی)\" ویجیٹس استعمال کر رہے ہیں، تو ان ویجیٹ میں پروجیکٹس کی ترتیب بھی آپ کے یہاں ترتیب دیے گئے ترتیب سے متعین ہوگی۔", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "پروجیکٹس جہاں آپ پروجیکٹ مینیجر ہیں وہ یہاں ظاہر ہوں گے۔", "app.containers.AdminPage.projects.all.noProjects": "کوئی پروجیکٹ نہیں ملا۔", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "صرف ایڈمن ہی پروجیکٹ فولڈر بنا سکتے ہیں۔", "app.containers.AdminPage.projects.all.projectsAndFolders": "پروجیکٹس اور فولڈرز", "app.containers.AdminPage.projects.all.publishedTab": "شائع شدہ", "app.containers.AdminPage.projects.all.searchProjects": "پروجیکٹس تلاش کریں۔", "app.containers.AdminPage.projects.all.yourProjects": "آپ کے پروجیکٹس", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "AI تجزیہ", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "درستگی: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "پوچھو", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "خلاصہ کرنے کے بجائے، آپ اپنے ڈیٹا سے متعلقہ سوالات پوچھ سکتے ہیں۔ یہ خصوصیت آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے حکومتی کامیابی کے مینیجر یا منتظم سے بات کریں۔", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "ایک سوال پوچھیں۔", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "اس بصیرت میں درج ذیل سوالات شامل ہیں:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "سوال کو حذف کریں۔", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "کیا آپ واقعی اس سوال کو حذف کرنا چاہتے ہیں؟", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "خلاصہ حذف کریں۔", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "کیا آپ واقعی ان خلاصوں کو حذف کرنا چاہتے ہیں؟", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "آپ کے متن کے خلاصے یہاں دکھائے جائیں گے، لیکن فی الحال آپ کے پاس کوئی نہیں ہے۔", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "شروع کرنے کے لیے اوپر خودکار خلاصہ بٹن پر کلک کریں۔", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "ان پٹ کو منتخب کیا گیا ہے۔", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "کم ان پٹ کے بارے میں سوالات پوچھنا ایک اعلی درستگی کی طرف جاتا ہے۔ ٹیگز، سرچ یا ڈیموگرافک فلٹرز استعمال کرکے موجودہ ان پٹ سلیکشن کو کم کریں۔", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "کے لیے سوالات", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "تمام ان پٹ کے لیے سوال", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "اس بصیرت کے معیار کی درجہ بندی کریں۔", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "فلٹرز کو بحال کریں۔", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "خلاصہ کریں۔", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "کے لیے خلاصہ", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "تمام ان پٹ کے لیے خلاصہ", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "آپ کی رائے کا شکریہ", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "AI ایک بار میں اتنے زیادہ ان پٹ پر کارروائی نہیں کر سکتا۔ انہیں چھوٹے گروپوں میں تقسیم کریں۔", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "آپ اپنے موجودہ پلان پر ایک وقت میں زیادہ سے زیادہ 30 ان پٹ کا خلاصہ کر سکتے ہیں۔ مزید غیر مقفل کرنے کے لیے اپنے GovSuccess مینیجر یا منتظم سے بات کریں۔", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "میں سمجھتا ہوں۔", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "ہمارا پلیٹ فارم آپ کو بنیادی تھیمز کو دریافت کرنے، ڈیٹا کا خلاصہ کرنے اور مختلف نقطہ نظر کو جانچنے کے قابل بناتا ہے۔ اگر آپ مخصوص جوابات یا بصیرتیں تلاش کر رہے ہیں، تو خلاصہ سے زیادہ گہرائی میں جانے کے لیے \"ایک سوال پوچھیں\" کی خصوصیت کو استعمال کرنے پر غور کریں۔", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "نایاب ہونے کے باوجود، AI کبھی کبھار ایسی معلومات پیدا کر سکتا ہے جو اصل ڈیٹاسیٹ میں واضح طور پر موجود نہیں تھی۔", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "ہیلوسینیشن:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "AI کچھ تھیمز یا نظریات پر دوسروں کے مقابلے میں زیادہ زور دے سکتا ہے، ممکنہ طور پر مجموعی تشریح کو کم کرتا ہے۔", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "مبا<PERSON><PERSON>ہ آرائی:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "ہمارا نظام درست ترین نتائج کے لیے 20-200 اچھی طرح سے متعین کردہ ان پٹس کو ہینڈل کرنے کے لیے بہتر بنایا گیا ہے۔ جیسے جیسے ڈیٹا کا حجم اس حد سے بڑھتا ہے، خلاصہ زیادہ اعلیٰ سطحی اور عمومی شکل اختیار کر سکتا ہے۔ اس کا مطلب یہ نہیں ہے کہ AI \"کم درست\" ہو جائے گا، بلکہ یہ کہ یہ وسیع تر رجحانات اور نمونوں پر توجہ مرکوز کرے گا۔ مزید باریک بینی کے لیے، ہم بڑے ڈیٹاسیٹس کو چھوٹے، زیادہ قابل انتظام ذیلی سیٹوں میں تقسیم کرنے کے لیے (آٹو-ٹیگنگ فیچر) استعمال کرنے کی تجویز کرتے ہیں۔", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "ڈیٹا کا حجم اور درستگی:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "ہم بڑے ڈیٹا سیٹس کو سمجھنے کے لیے AI سے تیار کردہ خلاصے کو ایک نقطہ آغاز کے طور پر استعمال کرنے کی تجویز کرتے ہیں، لیکن حتمی لفظ کے طور پر نہیں۔", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "AI کے ساتھ کیسے کام کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "ٹیگ میں منتخب ان پٹ شامل کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "ٹیگ شامل کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "یہ خصوصیت آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے حکومتی کامیابی کے مینیجر یا منتظم سے بات کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "تمام ان پٹ", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "تمام ان پٹ", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "تمام ٹیگز", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "نہیں، میں کروں گا۔", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "کیا آپ اپنے ٹیگ کو خودکار طور پر ان پٹ تفویض کرنا چاہتے ہیں؟", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "<b>مختلف طریقے ہیں</b> ٹیگز کو خود بخود ان پٹ تفویض کرنے کے لیے۔", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "اپنا پسندیدہ طریقہ شروع کرنے کے لیے <b>آٹو ٹیگ بٹن</b> استعمال کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "فی الحال منتخب کردہ ان پٹ کو تفویض کرنے کے لیے ٹیگ پر کلک کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "ہاں، آٹو ٹیگ", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "آٹو ٹیگ", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "آٹو ٹیگ خود بخود کمپیوٹر کے ذریعے اخذ کیے جاتے ہیں۔ آپ انہیں ہر وقت تبدیل یا ہٹا سکتے ہیں۔", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "آٹو ٹیگ", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "ان ٹیگز کے ساتھ پہلے سے منسلک ان پٹ کو دوبارہ درجہ بندی نہیں کیا جائے گا۔", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "درجہ بندی مکمل طور پر ٹیگ کے نام پر مبنی ہے۔ بہترین نتائج کے لیے متعلقہ مطلوبہ الفاظ کا انتخاب کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "ٹیگز: لیبل کے ذریعے", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "آپ ٹیگز بناتے ہیں اور مثال کے طور پر چند ان پٹ کو دستی طور پر تفویض کرتے ہیں، کمپیوٹر باقی تفویض کرتا ہے", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "ٹیگز: مثال کے طور پر", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "\"ٹیگز: بذریعہ لیبل\" کی طرح لیکن بڑھتی ہوئی درستگی کے ساتھ کیونکہ آپ اچھی مثالوں کے ساتھ سسٹم کو تربیت دے رہے ہیں۔", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "آپ ٹیگ بناتے ہیں، کمپیوٹر ان پٹ کو تفویض کرتا ہے۔", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "ٹیگز: لیبل کے ذریعے", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "یہ تب اچھا کام کرتا ہے جب آپ کے پاس پہلے سے طے شدہ ٹیگز ہوں یا جب آپ کے پروجیکٹ میں ٹیگز کے معاملے میں محدود گنجائش ہو۔", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "نمایاں ناپسندیدگی/پسندیدگی کے تناسب کے ساتھ ان پٹس کا پتہ لگائیں۔", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "متنازعہ", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "ٹیگ حذف کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "کیا آپ واقعی اس ٹیگ کو حذف کرنا چاہتے ہیں؟", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "اسے دوبارہ مت دکھائیں۔", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "ٹیگ میں ترمیم کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "نام شامل کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "زیادہ سے زیادہ 9 ٹیگز منتخب کریں جن کے درمیان آپ ان پٹ تقسیم کرنا چاہتے ہیں۔", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "درجہ بندی فی الحال ٹیگز کو تفویض کردہ ان پٹس پر مبنی ہے۔ کمپیوٹر آپ کی مثال پر عمل کرنے کی کوشش کرے گا۔", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "ٹیگز: مثال کے طور پر", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "آپ کے پاس ابھی تک کوئی حسب ضرورت ٹیگ نہیں ہیں۔", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "کمپیوٹر خود بخود ٹیگز کا پتہ لگاتا ہے اور انہیں آپ کے ان پٹ کو تفویض کرتا ہے۔", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "ٹیگز: مکمل طور پر خودکار", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "جب آپ کے پروجیکٹس ٹیگز کی ایک وسیع رینج کا احاطہ کرتے ہیں تو اچھی طرح سے کام کرتا ہے۔ شروع کرنے کے لیے اچھی جگہ۔", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "آپ کیسے ٹیگ کرنا چاہتے ہیں؟", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "ٹیگز کے بغیر ان پٹ", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "ہر ان پٹ کی زبان کا پتہ لگائیں۔", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "زبان", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "لانچ کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "کوئی فعال فلٹرز نہیں ہیں۔", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "مزید درست یا ٹارگٹڈ خلاصے بنانے کے لیے، ان پٹ کو ذیلی تقسیم اور فلٹر کرنے کے لیے ٹیگز کا استعمال کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.other": "دیگر", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "ٹیگز: پلیٹ فارم ٹیگز", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "موجودہ پلیٹ فارم ٹیگز کو تفویض کریں جو مصنف نے پوسٹ کرتے وقت اٹھایا تھا۔", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "تجویز کردہ", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "ٹیگ کا نام تبدیل کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "منسوخ کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "نام", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "محفوظ کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "ٹیگ کا نام تبدیل کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "سبھی کو منتخب کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "متن سے اخذ کردہ ہر ان پٹ کو مثبت یا منفی جذبات تفویض کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "جذ<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "ٹیگ کا پتہ لگانا", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "موجودہ فلٹرز استعمال کریں۔", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "آپ کن ان پٹ کو ٹیگ کرنا چاہتے ہیں؟", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "آٹو ٹیگنگ کا کام", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "متنازعہ", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "حسب ضرورت", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "پر ختم ہوا۔", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "ناکام", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "مثال کے طور پر", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "جاری ہے۔", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "لیبل کے ذریعے", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "زبان", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "این ایل پی ٹیگ", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "کوئی حالیہ AI کام انجام نہیں دیا گیا۔", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "پلیٹ فارم ٹیگ", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "قطار میں لگ گیا۔", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "جذ<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "پر شروع ہوا۔", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "کامیاب ہو گیا۔", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "خلاصہ کا کام", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "پر متحرک ہوا۔", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "او<PERSON>ر", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "تمام", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "مصنف", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "نیچے", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "سال پیدائش", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "ڈومیسائل", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "مصروفیت", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "فل<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "سے", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "جن<PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "ان پٹ", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "تبصروں کی تعداد", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "رد عمل کی تعداد", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "ووٹوں کی تعداد", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "کو", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "تجزیہ میں شامل کریں۔", "app.containers.AdminPage.projects.project.analysis.anonymous": "گمنام ان پٹ", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "عمر کے لحاظ سے مصنفین", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "ڈومیسائل کے لحاظ سے مصنفین", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "پس منظر کی نوکریاں", "app.containers.AdminPage.projects.project.analysis.comments": "تبصرے", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "ڈومیسائل چارٹ ڈسپلے کے لیے بہت بڑا ہے۔", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "خالی جوابات چھپائیں۔", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "جوابات", "app.containers.AdminPage.projects.project.analysis.end": "ختم", "app.containers.AdminPage.projects.project.analysis.filter": "صرف اس قدر کے ساتھ ان پٹ دکھائیں۔", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "بغیر کسی جواب کے جوابات چھپائیں۔", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "خودکار بصیرتیں۔", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "کالم کی قدریں۔", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "اس مجموعہ کی {count} مثالیں ہیں۔", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "ناپسندیدگی", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "دریافت کریں۔", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "جھوٹا۔", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "ان پٹ", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "پسند کرتا ہے۔", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "اگلا ہیٹ میپ", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "اگلی بصیرت", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "شماریاتی لحاظ سے اہم بصیرت نہیں ہے۔", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "30 سے کم شرکاء والے پروجیکٹس کے لیے آٹو بصیرتیں دستیاب نہیں ہیں۔", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "شرکاء", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "پچھلا ہیٹ میپ", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "پچھلی بصیرت", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "قطار کی قدریں۔", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "شماریاتی لحاظ سے اہم بصیرت۔", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "خلاصہ کریں۔", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "تجزیہ ٹیگز", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "سچ ہے۔", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "یونٹس", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "تمام بصیرتیں دیکھیں", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "خودکار بصیرتیں دیکھیں", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "ٹیگز کے بغیر ان پٹ", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "ایک غلط شکل فائل اپ لوڈ کی گئی تھی اور اسے ڈسپلے نہیں کیا جا سکتا۔", "app.containers.AdminPage.projects.project.analysis.limit": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "اہم سوال", "app.containers.AdminPage.projects.project.analysis.manageInput": "ان پٹ کا نظم کریں۔", "app.containers.AdminPage.projects.project.analysis.nextGraph": "اگلا گراف", "app.containers.AdminPage.projects.project.analysis.noAnswer": "کوئی جواب نہیں۔", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "کوئی جواب نہیں دیا۔", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "کوئی شکل فائل اپ لوڈ نہیں ہوئی۔", "app.containers.AdminPage.projects.project.analysis.noInputs": "کوئی ان پٹ آپ کے موجودہ فلٹرز سے مطابقت نہیں رکھتا", "app.containers.AdminPage.projects.project.analysis.previousGraph": "پچھلا گراف", "app.containers.AdminPage.projects.project.analysis.reactions": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.remove": "ہٹا دیں۔", "app.containers.AdminPage.projects.project.analysis.removeFilter": "فلٹر کو ہٹا دیں۔", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "فلٹر کو ہٹا دیں۔", "app.containers.AdminPage.projects.project.analysis.search": "تلاش کریں۔", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* شیپ فائلز یہاں GeoJSON فارمیٹ میں دکھائے جاتے ہیں۔ اس طرح، اصل فائل میں اسٹائل صحیح طریقے سے ظاہر نہیں ہوسکتا ہے۔", "app.containers.AdminPage.projects.project.analysis.start": "شروع کریں۔", "app.containers.AdminPage.projects.project.analysis.supportArticle": "سپورٹ آرٹیکل", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "نامعلوم", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "تمام سوالات دیکھیں", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "منتخب سوالات دیکھیں", "app.containers.AdminPage.projects.project.analysis.votes": "ووٹ", "app.containers.AdminPage.widgets.copied": "کلپ بورڈ پر کاپی ہو گیا۔", "app.containers.AdminPage.widgets.copyToClipboard": "اس کوڈ کو کاپی کریں۔", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "HTML کوڈ کاپی کریں۔", "app.containers.AdminPage.widgets.fieldAccentColor": "لہجے کا رنگ", "app.containers.AdminPage.widgets.fieldBackgroundColor": "ویجیٹ کے پس منظر کا رنگ", "app.containers.AdminPage.widgets.fieldButtonText": "بٹن کا متن", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "ابھی شامل ہوں۔", "app.containers.AdminPage.widgets.fieldFont": "فونٹ", "app.containers.AdminPage.widgets.fieldFontDescription": "یہ {googleFontsLink}سے ایک موجودہ فونٹ نام ہونا چاہیے۔", "app.containers.AdminPage.widgets.fieldFontSize": "فونٹ سائز (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "ہیڈر سب ٹائٹل", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "آپ ایک بات کہہ سکتے ہیں۔", "app.containers.AdminPage.widgets.fieldHeaderText": "ہیڈر کا عنوان", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "ہماری شرکت کا پلیٹ فارم", "app.containers.AdminPage.widgets.fieldHeight": "اونچائی (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "پوسٹس کی تعداد", "app.containers.AdminPage.widgets.fieldProjects": "پروجیکٹس", "app.containers.AdminPage.widgets.fieldRelativeLink": "کے لنکس", "app.containers.AdminPage.widgets.fieldShowFooter": "بٹن دکھائیں۔", "app.containers.AdminPage.widgets.fieldShowHeader": "ہیڈر دکھائیں۔", "app.containers.AdminPage.widgets.fieldShowLogo": "لوگو دکھائیں۔", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "سائٹ کے پس منظر کا رنگ", "app.containers.AdminPage.widgets.fieldSort": "ترتیب دیا گیا", "app.containers.AdminPage.widgets.fieldTextColor": "متن کا رنگ", "app.containers.AdminPage.widgets.fieldTopics": "ٹی<PERSON>ز", "app.containers.AdminPage.widgets.fieldWidth": "چوڑائی", "app.containers.AdminPage.widgets.homepage": "<PERSON>و<PERSON> پیج", "app.containers.AdminPage.widgets.htmlCodeExplanation": "آپ اس HTML کوڈ کو کاپی کر کے اپنی ویب سائٹ کے اس حصے پر چسپاں کر سکتے ہیں جہاں آپ اپنا ویجیٹ شامل کرنا چاہتے ہیں۔", "app.containers.AdminPage.widgets.htmlCodeTitle": "ویجیٹ HTML کوڈ", "app.containers.AdminPage.widgets.previewTitle": "پیش نظارہ", "app.containers.AdminPage.widgets.settingsTitle": "ترتیبات", "app.containers.AdminPage.widgets.sortNewest": "تازہ ترین", "app.containers.AdminPage.widgets.sortPopular": "مق<PERSON>ول", "app.containers.AdminPage.widgets.sortTrending": "ٹرینڈنگ", "app.containers.AdminPage.widgets.subtitleWidgets": "لوگوں کو اس پلیٹ فارم کی طرف راغب کرنے کے لیے آپ ایک ویجیٹ بنا سکتے ہیں، اسے اپنی مرضی کے مطابق بنا سکتے ہیں اور اسے اپنی ویب سائٹ میں شامل کر سکتے ہیں۔", "app.containers.AdminPage.widgets.title": "ویجیٹ", "app.containers.AdminPage.widgets.titleDimensions": "طول و عرض", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "ہیڈر اور فوٹر", "app.containers.AdminPage.widgets.titleInputSelection": "ان پٹ سلیکشن", "app.containers.AdminPage.widgets.titleStyle": "انداز", "app.containers.AdminPage.widgets.titleWidgets": "ویجیٹ", "app.containers.ContentBuilder.Save": "محفوظ کریں۔", "app.containers.ContentBuilder.homepage.PageTitle": "<PERSON>و<PERSON> پیج", "app.containers.ContentBuilder.homepage.SaveError": "ہوم پیج کو محفوظ کرتے وقت کچھ غلط ہو گیا۔", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "دو کالم", "app.containers.ContentBuilder.homepage.bannerImage": "بینر کی تصویر", "app.containers.ContentBuilder.homepage.bannerSubtext": "بینر ذیلی متن", "app.containers.ContentBuilder.homepage.bannerText": "بینر کا متن", "app.containers.ContentBuilder.homepage.button": "بٹن", "app.containers.ContentBuilder.homepage.chooseLayout": "لے آؤٹ", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "ہوم پیج بینر پر تصویر اور متن کے علاوہ ترتیبات کو حسب ضرورت بنانا آپ کے موجودہ لائسنس میں شامل نہیں ہے۔ اس کے بارے میں مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.ContentBuilder.homepage.customized_button": "حسب ضرورت", "app.containers.ContentBuilder.homepage.customized_button_text_label": "بٹن کا متن", "app.containers.ContentBuilder.homepage.customized_button_url_label": "بٹن لنک", "app.containers.ContentBuilder.homepage.events.eventsDescription": "آپ کے پلیٹ فارم پر اگلے 3 آنے والے ایونٹس دکھاتا ہے۔", "app.containers.ContentBuilder.homepage.eventsDescription": "آپ کے پلیٹ فارم پر اگلے 3 آنے والے ایونٹس دکھاتا ہے۔", "app.containers.ContentBuilder.homepage.fixedRatio": "فکسڈ ریشو بینر", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "بینر کی یہ قسم ان تصاویر کے ساتھ بہترین کام کرتی ہے جنہیں کاٹا نہیں جانا چاہیے، جیسے متن والی تصاویر، لوگو یا مخصوص عناصر جو آپ کے شہریوں کے لیے اہم ہیں۔ جب صارفین سائن ان ہوتے ہیں تو اس بینر کو بنیادی رنگ میں ایک ٹھوس باکس سے بدل دیا جاتا ہے۔ آپ اس رنگ کو عام ترتیبات میں سیٹ کر سکتے ہیں۔ تجویز کردہ تصویر کے استعمال کے بارے میں مزید معلومات ہمارے {link}پر مل سکتی ہیں۔", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "علم کی بنیاد", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "پوری چوڑائی والا بینر", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "یہ بینر ایک زبردست بصری اثر کے لیے پوری چوڑائی پر پھیلا ہوا ہے۔ تصویر زیادہ سے زیادہ جگہ کا احاطہ کرنے کی کوشش کرے گی، جس کی وجہ سے یہ ہر وقت نظر نہیں آتی۔ آپ اس بینر کو کسی بھی رنگ کے اوورلے کے ساتھ جوڑ سکتے ہیں۔ تجویز کردہ تصویر کے استعمال کے بارے میں مزید معلومات ہمارے {link}پر مل سکتی ہیں۔", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "علم کی بنیاد", "app.containers.ContentBuilder.homepage.imageOverlayColor": "تصویر کا اوورلے رنگ", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "تصویری اوورلے کی دھندلاپن", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.cizenlab.co/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "غلط URL", "app.containers.ContentBuilder.homepage.no_button": "کوئی بٹن نہیں۔", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "غیر رجسٹرڈ صارفین", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "اوورلے کو فعال کریں۔", "app.containers.ContentBuilder.homepage.projectsDescription": "اس ترتیب کو ترتیب دینے کے لیے جس میں آپ کے پروجیکٹس دکھائے جاتے ہیں، انہیں {link}پر دوبارہ ترتیب دیں۔", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "پروجیکٹس کا صفحہ", "app.containers.ContentBuilder.homepage.registeredUsersView": "رجسٹرڈ صارفین", "app.containers.ContentBuilder.homepage.showAvatars": "اوتار دکھائیں۔", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "غیر رجسٹرڈ مہمانوں کو شرکاء کی پروفائل تصویریں اور ان کی تعداد دکھائیں۔", "app.containers.ContentBuilder.homepage.sign_up_button": "سائن اپ کریں۔", "app.containers.ContentBuilder.homepage.signedInDescription": "رجسٹرڈ صارفین بینر کو اس طرح دیکھتے ہیں۔", "app.containers.ContentBuilder.homepage.signedOutDescription": "اس طرح وہ زائرین جو پلیٹ فارم پر رجسٹرڈ نہیں ہیں وہ بینر دیکھتے ہیں۔", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "یہ بینر خاص طور پر ان تصاویر کے لیے مفید ہے جو ٹائٹل، سب ٹائٹل یا بٹن کے متن کے ساتھ اچھی طرح کام نہیں کرتی ہیں۔ ان اشیاء کو بینر کے نیچے دھکیل دیا جائے گا۔ تجویز کردہ تصویر کے استعمال کے بارے میں مزید معلومات ہمارے {link}پر مل سکتی ہیں۔", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "علم کی بنیاد", "app.containers.ContentBuilder.homepage.twoRowLayout": "دو قطاریں۔", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "سرایت کی اونچائی (پکسل)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "اونچائی آپ چاہتے ہیں کہ آپ کا سرایت شدہ مواد صفحہ پر ظاہر ہو (پکسلز میں)۔", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "آپ جس مواد کو سرایت کر رہے ہیں اس کی مختصر تفصیل", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "یہ معلومات ان صارفین کے لیے فراہم کرنا مفید ہے جو اسکرین ریڈر یا دیگر معاون ٹیکنالوجی پر انحصار کرتے ہیں۔", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "ویب سائٹ کا پتہ", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "جس ویب سائٹ کو آپ ایمبیڈ کرنا چاہتے ہیں اس کا مکمل URL۔", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "کسی بیرونی ویب سائٹ سے مواد کو اپنے صفحہ پر HTML iFrame میں ڈسپلے کریں۔ نوٹ کریں کہ ہر صفحہ کو سرایت نہیں کیا جا سکتا۔ اگر آپ کو کسی صفحہ کو سرایت کرنے میں پریشانی ہو رہی ہے، تو صفحہ کے مالک سے معلوم کریں کہ آیا یہ سرایت کرنے کی اجازت دینے کے لیے ترتیب دیا گیا ہے۔", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "ہمارے سپورٹ پیج پر جائیں۔", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "معذرت، یہ مواد سرایت نہیں ہو سکا۔ مزید جاننے کے لیے {visitLinkMessage} ۔", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "ایک درست ویب ایڈریس درج کریں، مثال کے طور پر https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "ایمبیڈ", "app.containers.admin.ContentBuilder.accordionMultiloc": "ایکارڈین", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "بطور ڈیفالٹ کھولیں۔", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "متن", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "یہ قابل توسیع ایکارڈین مواد ہے۔ آپ دائیں جانب پینل میں ایڈیٹر کا استعمال کرکے اس میں ترمیم اور فارمیٹ کرسکتے ہیں۔", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "عنوان", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "ایکارڈین عنوان", "app.containers.admin.ContentBuilder.buttonMultiloc": "بٹن", "app.containers.admin.ContentBuilder.delete": "حذف کریں۔", "app.containers.admin.ContentBuilder.error": "غلطی", "app.containers.admin.ContentBuilder.errorMessage": "{locale} مواد میں ایک خرابی ہے، براہ کرم اپنی تبدیلیوں کو محفوظ کرنے کے لیے مسئلہ کو ٹھیک کریں۔", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "شرکت کے اوتار چھپائیں۔", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "یہ ایک سہ ماہی، جاری سروے ہے جو اس بات کا پتہ لگاتا ہے کہ آپ گورننس اور عوامی خدمات کے بارے میں کیسا محسوس کرتے ہیں۔", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "سروے میں حصہ لیں۔", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "آپ کی بہتر خدمت کرنے میں ہماری مدد کریں۔", "app.containers.admin.ContentBuilder.homepage.default": "پہلے سے طے شدہ", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "واقعات", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "واقعات", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "کال ٹو ایکشن", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "تفصیل", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "بنیادی بٹن URL", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "بنیادی بٹن کا متن", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "ثانوی بٹن URL", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "ثانوی بٹن کا متن", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "عنوان", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "ہوم پیج بینر", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "ہوم پیج بینر", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "تصویر اور ٹیکسٹ کارڈز", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 کالم", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "پروجیکٹس", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "ہوم پیج میں ان لاک کرنے کے لیے ایڈمن پینل میں \"تجاویز\" سیکشن میں تجاویز کو فعال کریں۔", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "تجاو<PERSON>ز", "app.containers.admin.ContentBuilder.imageMultiloc": "تصویر", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "تصویر کی مختصر تفصیل", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "آپ کے پلیٹ فارم کو اسکرین ریڈرز استعمال کرنے والے صارفین کے لیے قابل رسائی بنانے کے لیے تصاویر کے لیے \"Alt text\" شامل کرنا ضروری ہے۔", "app.containers.admin.ContentBuilder.participationBox": "شرکت خانہ", "app.containers.admin.ContentBuilder.textMultiloc": "متن", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 کالم", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 کالم", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "بالترتیب 30% اور 60% چوڑائی والے 2 کالم", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "بالترتیب 60% اور 30% چوڑائی والے 2 کالم", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 برابر کالم", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "سب سے زیادہ ردعمل کا اظہار کیا گیا آدانوں", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "<PERSON><PERSON>ال کا مرحلہ", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "اس پروجیکٹ یا مرحلے کے لیے کوئی ان پٹ دستیاب نہیں ہے۔", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "ان پٹ کی تعداد", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "مزید دکھائیں", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "عنوان", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "کل ان پٹ: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "طویل متن کو سکیڑیں۔", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "اس پروجیکٹ یا مرحلے کے لیے کوئی ان پٹ دستیاب نہیں ہے۔", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "مرحلہ منتخب کریں۔", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "مصنف", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "مواد", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "<PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "ووٹ", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "ان پٹ", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "عنوان", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "رجسٹریشن کی شرح", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "رجسٹر<PERSON><PERSON><PERSON>ز", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "شرکاء کی ٹائم لائن", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "براہ کرم نوٹ کریں کہ شرکت کے نمبر مکمل طور پر درست نہیں ہو سکتے ہیں کیونکہ کچھ ڈیٹا ایک بیرونی سروے میں لیا گیا ہے جسے ہم ٹریک نہیں کرتے ہیں۔", "app.containers.admin.ReportBuilder.charts.analyticsChart": "چارٹ", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "تاریخ کی حد", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "عنوان", "app.containers.admin.ReportBuilder.charts.noData": "آپ کے منتخب کردہ فلٹرز کے لیے کوئی ڈیٹا دستیاب نہیں ہے۔", "app.containers.admin.ReportBuilder.charts.trafficSources": "ٹریفک ذرائع", "app.containers.admin.ReportBuilder.charts.users": "صارفین", "app.containers.admin.ReportBuilder.charts.usersByAge": "عمر کے لحاظ سے صارفین", "app.containers.admin.ReportBuilder.charts.usersByGender": "جنس کے لحاظ سے صارفین", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "وزیٹر ٹائم لائن", "app.containers.admin.ReportBuilder.managerLabel1": "پروجیکٹ مینیجر", "app.containers.admin.ReportBuilder.periodLabel1": "مدت", "app.containers.admin.ReportBuilder.projectLabel1": "پروجیکٹ", "app.containers.admin.ReportBuilder.quarterReport1": "کمیونٹی مانیٹر کی رپورٹ: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "شروع کریں۔", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "1 اضافی سیٹ خریدیں۔", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "تصدیق کریں۔", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "کیا آپ واقعی 1 شخص مینیجر کے حقوق دینا چاہتے ہیں؟", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "مینیجر کو حقوق دیں۔", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "آپ اپنے پلان میں شامل سیٹوں کی حد کو پہنچ گئے ہیں، 1 اضافی سیٹ شامل کی جائے گی۔", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "حیثیت شامل کریں۔", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "پہلے سے طے شدہ سٹیٹس کو حذف نہیں کیا جا سکتا۔", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "حذف کریں۔", "app.containers.admin.ideaStatuses.all.editButtonLabel": "ترمیم کریں۔", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "حالت میں ترمیم کریں۔", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "فی الحال شریک ان پٹ کو تفویض کردہ اسٹیٹس کو حذف نہیں کیا جا سکتا۔ آپ {manageTab} ٹیب میں موجودہ ان پٹ سے اسٹیٹس کو ہٹا یا تبدیل کر سکتے ہیں۔", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "اس حیثیت کو حذف یا منتقل نہیں کیا جاسکتا۔", "app.containers.admin.ideaStatuses.all.manage": "ترمیم کریں۔", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "حسب ضرورت ان پٹ سٹیٹس کو ترتیب دینا آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے حکومتی کامیابی کے مینیجر یا منتظم سے بات کریں۔", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "اس اسٹیٹس کا نظم کریں جو کسی پروجیکٹ کے اندر شریک ان پٹ کو تفویض کیا جا سکتا ہے۔ اسٹیٹس عوامی طور پر نظر آتا ہے اور شرکاء کو باخبر رکھنے میں مدد کرتا ہے۔", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "اس حیثیت کا نظم کریں جو کسی پروجیکٹ کے اندر تجاویز کو تفویض کی جاسکتی ہے۔ اسٹیٹس عوامی طور پر نظر آتا ہے اور شرکاء کو باخبر رکھنے میں مدد کرتا ہے۔", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "ان پٹ سٹیٹس میں ترمیم کریں۔", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "تجویز کے حالات میں ترمیم کریں۔", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "عمل درآمد یا اگلے اقدامات کے لیے منتخب کیا گیا۔", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "منظور شدہ", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "آفیشل فیڈ بیک فراہم کیا گیا۔", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "جواب دیا۔", "app.containers.admin.ideaStatuses.form.category": "زمرہ", "app.containers.admin.ideaStatuses.form.categoryDescription": "براہ کرم وہ زمرہ منتخب کریں جو آپ کی حیثیت کی بہترین نمائندگی کرے۔ اس انتخاب سے ہمارے تجزیاتی ٹول کو پوسٹس پر مزید درست طریقے سے کارروائی اور تجزیہ کرنے میں مدد ملے گی۔", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "دوسرے اختیارات میں سے کسی سے مماثل نہیں۔", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "دیگر", "app.containers.admin.ideaStatuses.form.fieldColor": "رنگ", "app.containers.admin.ideaStatuses.form.fieldDescription": "اسٹیٹس کی تفصیل", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "تمام زبانوں کے لیے اسٹیٹس کی تفصیل فراہم کریں۔", "app.containers.admin.ideaStatuses.form.fieldTitle": "حیثیت کا نام", "app.containers.admin.ideaStatuses.form.fieldTitleError": "تمام زبانوں کے لیے اسٹیٹس کا نام فراہم کریں۔", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "کامیابی سے عمل میں لایا گیا۔", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "لاگو کیا", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "تجویز نا اہل ہے۔", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "نااہل", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "آگے بڑھنے کے لیے نااہل یا منتخب نہیں کیا گیا۔", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "منتخب نہیں کیا گیا۔", "app.containers.admin.ideaStatuses.form.saveStatus": "اسٹیٹس کو محفوظ کریں۔", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "عمل درآمد یا اگلے اقدامات کے لیے غور کیا جاتا ہے۔", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "زی<PERSON> غور", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "دیکھا گیا لیکن ابھی تک کارروائی نہیں ہوئی۔", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "دیکھا گیا", "app.containers.admin.ideas.all.inputManagerMetaDescription": "ان پٹ اور ان کے سٹیٹس کا نظم کریں۔", "app.containers.admin.ideas.all.inputManagerMetaTitle": "ان پٹ مینیجر | {orgName}کا شرکت کا پلیٹ فارم", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "رائے دیں، ٹیگز شامل کریں اور ان پٹ کو ایک پروجیکٹ سے دوسرے پروجیکٹ میں منتقل کریں۔", "app.containers.admin.ideas.all.inputManagerPageTitle": "ان پٹ مینیجر", "app.containers.admin.ideas.all.tabOverview": "جا<PERSON>زہ", "app.containers.admin.import.importInputs": "ان پٹ درآمد کریں۔", "app.containers.admin.import.importNoLongerAvailable3": "یہ خصوصیت اب یہاں دستیاب نہیں ہے۔ آئیڈییشن مرحلے میں ان پٹ درآمد کرنے کے لیے، فیز پر جائیں اور \"درآمد\" کو منتخب کریں۔", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 اضافی ایڈمن سیٹ} other {# اضافی ایڈمن سیٹیں}} اور {managerSeats, plural, one {1 اضافی مینیجر سیٹ} other {# اضافی منیجر سیٹیں}} حد سے زیادہ شامل کی جائیں گی۔", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 اضافی ایڈمن سیٹ حد سے زیادہ شامل کی جائے گی} other {# اضافی ایڈمن سیٹیں حد سے زیادہ شامل کی جائیں گی}}۔", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 اضافی مینیجر سیٹ حد سے زیادہ شامل کی جائے گی} other {# اضافی مینیجر سیٹیں حد سے زیادہ شامل کی جائیں گی}}۔", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "تصدیق کریں اور دعوت نامے بھیجیں۔", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "سیٹ کے استعمال پر اثر کی تصدیق کریں۔", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "آپ اپنے پلان کے اندر دستیاب سیٹوں کی حد تک پہنچ گئے ہیں۔", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "منتظمین اور اس منصوبے کے منتظمین", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "صرف منتظمین اور معاونین", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "صرف پلیٹ فارم ایڈمنز، فولڈر مینیجرز اور پروجیکٹ مینیجر ہی کارروائی کر سکتے ہیں۔", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "کوئی بھی", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "غیر رجسٹرڈ صارفین سمیت کوئی بھی حصہ لے سکتا ہے۔", "app.containers.admin.project.permissions.permissionsSelectionLabel": "انتخاب", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "مخصوص صارف گروپ (زبانیں) کے صارفین حصہ لے سکتے ہیں۔ آپ \"صارفین\" ٹیب میں صارف گروپس کا نظم کر سکتے ہیں۔", "app.containers.admin.project.permissions.viewingRightsTitle": "اس منصوبے کو کون دیکھ سکتا ہے؟", "app.containers.phaseConfig.enableSimilarInputDetection": "ملتے جلتے ان پٹ کا پتہ لگانے کو فعال کریں۔", "app.containers.phaseConfig.similarInputDetectionTitle": "اسی طرح کے ان پٹ کا پتہ لگانا", "app.containers.phaseConfig.similarInputDetectionTooltip": "ڈپلیکیٹس سے بچنے میں مدد کے لیے شرکاء کو ٹائپ کرتے وقت ایک جیسا ان پٹ دکھائیں۔", "app.containers.phaseConfig.similarityThresholdBody": "مماثل<PERSON> کی حد (جسم)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "یہ کنٹرول کرتا ہے کہ دو وضاحتوں کو ایک جیسے کے طور پر جھنڈا لگانے کے لیے کتنی مماثلت ہونی چاہیے۔ 0 (سخت) اور 1 (نرم) کے درمیان قدر استعمال کریں۔ نچلی قدریں کم لیکن زیادہ درست مماثلتیں دیتی ہیں۔", "app.containers.phaseConfig.similarityThresholdTitle": "مماث<PERSON><PERSON> کی حد (عنوان)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "یہ کنٹرول کرتا ہے کہ دو عنوانات کو مماثل کے طور پر جھنڈا لگانے کے لیے کتنے ملتے جلتے ہونے چاہئیں۔ 0 (سخت) اور 1 (نرم) کے درمیان قدر استعمال کریں۔ نچلی قدریں کم لیکن زیادہ درست مماثلتیں دیتی ہیں۔", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "یہ خصوصیت 30 جون 2025 تک ابتدائی رسائی کی پیشکش کے حصے کے طور پر دستیاب ہے۔ اگر آپ اس تاریخ کے بعد بھی اس کا استعمال جاری رکھنا چاہتے ہیں، تو براہ کرم ایکٹیویشن کے اختیارات پر بات کرنے کے لیے اپنے سرکاری کامیابی کے مینیجر یا منتظم سے رابطہ کریں۔", "app.containers.survey.sentiment.noAnswers2": "اس وقت کوئی جواب نہیں ہے۔", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 تبصرے} one {1 تبصرہ} other {# تبصرے}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "شرکاء وہ صارف یا زائرین ہیں جنہوں نے کسی پروجیکٹ میں حصہ لیا ہے، کسی پروپوزل کے ساتھ پوسٹ کیا یا بات چیت کی ہے یا ایونٹس میں شرکت کی ہے۔", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "شرکاء", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "شرکت کی شرح", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "ان زائرین کا فیصد جو شرکت کرتے ہیں۔", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "کل شرکاء", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "خودکار مہمات", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "<PERSON>و<PERSON><PERSON>ار ای میلز", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "{quantity} مہمات سے", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "مہمات", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "حسب ضرورت مہمات", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "حسب ضرورت ای میلز", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "ای میلز", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "کل ای میلز بھیجے گئے۔", "app.modules.commercial.analytics.admin.components.Events.completed": "م<PERSON><PERSON>ل", "app.modules.commercial.analytics.admin.components.Events.events": "واقعات", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "کل واقعات شامل کیے گئے۔", "app.modules.commercial.analytics.admin.components.Events.upcoming": "آنے والا", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "قبول کر لیا", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "دعوتیں", "app.modules.commercial.analytics.admin.components.Invitations.pending": "زیر التواء", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "کل دعوت نامے بھیجے گئے۔", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "ان پٹ مینیجر پر جائیں۔", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "ان پٹ", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "فعال", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "وہ پروجیکٹ جو محفوظ شدہ دستاویزات نہیں ہیں اور ہوم پیج پر 'ایکٹو' ٹیبل پر نظر آتے ہیں۔", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "محف<PERSON><PERSON> شدہ", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "ڈرافٹ پروجیکٹس", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "ختم", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "تمام آرکائیو شدہ پروجیکٹس اور فعال ٹائم لائن پروجیکٹس جو مکمل ہو چکے ہیں یہاں شمار کیے جاتے ہیں۔", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "پروجیکٹس", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "کل پروجیکٹس", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "پلیٹ فارم پر نظر آنے والے منصوبوں کی تعداد", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "نئی رجسٹریشنز", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "رجسٹریشن کی شرح", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "ان زائرین کا فیصد جو رجسٹرڈ صارفین بن جاتے ہیں۔", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "رجسٹر<PERSON><PERSON><PERSON>ز", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "کل رجسٹریشنز", "app.modules.commercial.analytics.admin.components.Tab": "زائرین", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "آخری 30 دن:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "آخری 7 دن:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "صفحہ کے ملاحظات فی وزٹ", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "دورہ کا دورانیہ", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "زائرین", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"زائرین\" منفرد زائرین کی تعداد ہے۔ اگر کوئی شخص متعدد بار پلیٹ فارم کا دورہ کرتا ہے، تو اسے صرف ایک بار شمار کیا جاتا ہے۔", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "دورے", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"ملاقات\" سیشنز کی تعداد ہے۔ اگر کوئی شخص متعدد بار پلیٹ فارم کا دورہ کرتا ہے، تو ہر وزٹ کو شمار کیا جاتا ہے۔", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "کل:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "شمار", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "زبان", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "زائرین کی تعداد", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "زائرین کا فیصد", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "حوالہ دینے والا", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "یہاں کلک کریں", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "حوالہ دینے والے", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "حوالہ دہندگان کی مکمل فہرست دیکھنے کے لیے، {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "زائرین", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "ٹریفک ذرائع", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "دورے", "app.modules.commercial.analytics.admin.components.totalParticipants": "کل شرکاء", "app.modules.commercial.analytics.admin.containers.visitors.noData": "ابھی تک کوئی وزیٹر ڈیٹا نہیں ہے۔", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "ہم نے وزیٹر کے ڈیٹا کو جمع کرنے اور ڈسپلے کرنے کا طریقہ بدل دیا ہے۔ نتیجے کے طور پر، وزیٹر کا ڈیٹا زیادہ درست ہے اور ڈیٹا کی مزید اقسام دستیاب ہیں، جب کہ وہ ابھی تک GDPR کے مطابق ہے۔ جب کہ وزٹرز کی ٹائم لائن کے لیے استعمال ہونے والا ڈیٹا طویل عرصے تک چلا جاتا ہے، ہم نے نومبر 2024 میں صرف \"وزٹ کی مدت\"، \"صفحہ کے ملاحظات\" اور دیگر گرافس کے لیے ڈیٹا اکٹھا کرنا شروع کیا، لہذا اس سے پہلے کوئی ڈیٹا دستیاب نہیں ہے۔ لہذا، اگر آپ نومبر 2024 سے پہلے کا ڈیٹا منتخب کرتے ہیں، تو آگاہ رہیں کہ کچھ گراف خالی ہو سکتے ہیں یا عجیب لگ سکتے ہیں۔", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "وقت کے ساتھ ای میل کی ترسیل", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "وقت کے ساتھ شرکاء", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "وقت کے ساتھ رجسٹریشن", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "تاریخ", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "شماریات", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "مجموعی اعدادوشمار", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "وقت کے ساتھ وزٹرز اور زائرین", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "کل اوور پیریڈ", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "شمار", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "زبان", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "مہمات", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "براہ راست داخلہ", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "زائرین کی تعداد", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "دوروں کا فیصد", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "حوالہ دینے والا", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "حوالہ دینے والی ویب سائٹس", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "سرچ انجن", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "سوشل نیٹ ورکس", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO ری ڈائریکٹ", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "ٹریفک کا ذریعہ", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "دوروں کی تعداد", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "ویب سائٹس", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "آپ اس آئٹم کو منتخب کرکے اور سب سے اوپر ہٹانے کے بٹن پر کلک کرکے اس مواد کے جھنڈے کو ہٹا سکتے ہیں۔ اس کے بعد یہ دیکھا یا نہیں دیکھا گیا ٹیبز میں دوبارہ ظاہر ہوگا۔", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "نامناسب مواد کا خود بخود پتہ چلا۔", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "ہمارے نیچرل لینگویج پروسیسنگ سسٹم کی طرف سے کمیونٹی کی طرف سے جائزے کے لیے کوئی پوسٹ رپورٹ نہیں کی گئی ہے یا نامناسب مواد کے لیے جھنڈا لگایا گیا ہے", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "ہٹائیں {numberOfItems, plural, one {مواد کی وارننگ} other {# مواد کی وارننگ}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "پلیٹ فارم کے صارف کے ذریعہ نامناسب کے طور پر رپورٹ کیا گیا۔", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "مواد کی وارننگز", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "رپورٹ بلڈر", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "آپ کے نیویگیشن بار پر دکھائے گئے صفحات", "app.modules.navbar.admin.containers.addProject": "navbar میں پروجیکٹ شامل کریں۔", "app.modules.navbar.admin.containers.createCustomPageButton": "حسب ضرورت صفحہ بنائیں", "app.modules.navbar.admin.containers.deletePageConfirmation": "کیا آپ واقعی اس صفحہ کو حذف کرنا چاہتے ہیں؟ اسے کالعدم نہیں کیا جا سکتا۔ اگر آپ ابھی تک اسے حذف کرنے کے لیے تیار نہیں ہیں تو آپ نیویگیشن بار سے بھی صفحہ کو ہٹا سکتے ہیں۔", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "آپ نیویگیشن بار میں صرف 5 آئٹمز تک شامل کر سکتے ہیں۔", "app.modules.navbar.admin.containers.pageHeader": "صفحات اور مینو", "app.modules.navbar.admin.containers.pageSubtitle": "آپ کا نیویگیشن بار ہوم اور پروجیکٹ کے صفحات کے علاوہ پانچ صفحات تک ڈسپلے کر سکتا ہے۔ آپ مینو آئٹمز کا نام تبدیل کر سکتے ہیں، دوبارہ ترتیب دے سکتے ہیں اور اپنے مواد کے ساتھ نئے صفحات شامل کر سکتے ہیں۔", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "اے آئی", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "وجیٹس", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "AI مواد کو اپنی رپورٹ میں گھسیٹنے کے لیے نیچے ☰ آئیکن استعمال کریں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "کوئی AI بصیرت دستیاب نہیں ہے۔ آپ انہیں اپنے پروجیکٹ میں بنا سکتے ہیں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "پروجیکٹ پر جائیں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "سوال", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "مرحلہ منتخب کریں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "AI تجزیہ کو غیر مقفل کریں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "AI سے تیار کردہ بصیرت کو اپنی رپورٹ میں کھینچیں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "AI کے ساتھ تیزی سے رپورٹ کریں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "AI کے ساتھ رپورٹنگ آپ کے موجودہ پلان میں شامل نہیں ہے۔ اس خصوصیت کو غیر مقفل کرنے کے لیے اپنے حکومتی کامیابی کے مینیجر سے بات کریں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "یہ آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے سرکاری کامیابی کے مینیجر یا منتظم سے رابطہ کریں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "رجسٹریشن فیلڈ کے لحاظ سے گروپ کریں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "سروے کے سوال کے لحاظ سے گروپ کریں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "گروپ موڈ", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "رجسٹریشن فیلڈز (جن<PERSON>، مقام، عمر، وغیرہ) یا سروے کے دیگر سوالات کے ذریعے گروپ سروے کے جوابات۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "کوئی نہیں۔", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "سوال", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "رجسٹریشن کا میدان", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "سروے کا مرحلہ", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "سروے کا سوال", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "کیا آپ واقعی اسے حذف کرنا چاہتے ہیں؟", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "منسوخ کریں۔", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "حذف کریں۔", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "ترمیم کریں۔", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "اپنا تبصرہ پوسٹ کریں۔", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "محفوظ کریں۔", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "اپنا تبصرہ یہاں لکھیں۔", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "فالو یا ان فالو کرنے کے لیے نیچے دیے گئے بٹنوں پر کلک کریں۔ منصوبوں کی تعداد بریکٹ میں دکھائی گئی ہے۔", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "اپنے علاقے میں", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "ہو گیا", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "ترجیحات پر عمل کریں۔", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "آپ کی پیروی کی ترجیحات کے پیش نظر فی الحال کوئی فعال پروجیکٹ نہیں ہے۔", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "یہ ویجیٹ ان \"علاقوں\" سے وابستہ منصوبوں کو دکھاتا ہے جن کی صارف پیروی کرتا ہے۔ نوٹ کریں کہ آپ کا پلیٹ فارم \"علاقوں\" کے لیے مختلف نام استعمال کر سکتا ہے - پلیٹ فارم کی ترتیبات میں \"علاقوں\" ٹیب کو دیکھیں۔ اگر صارف ابھی تک کسی علاقے کی پیروی نہیں کرتا ہے، تو ویجیٹ پیروی کرنے کے لیے دستیاب علاقوں کو دکھائے گا۔ اس صورت میں ویجیٹ زیادہ سے زیادہ 100 علاقے دکھائے گا۔", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "کوئی شائع شدہ پروجیکٹ یا فولڈر دستیاب نہیں ہیں۔", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "شائع شدہ پروجیکٹس اور فولڈرز", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "یہ ویجیٹ ان پروجیکٹس اور فولڈرز کو کیس کرے گا جو فی الحال شائع ہورہے ہیں، پروجیکٹس کے صفحہ پر بیان کردہ ترتیب کا احترام کرتے ہوئے۔ یہ رویہ \"وراثت\" پروجیکٹس ویجیٹ کے \"فعال\" ٹیب جیسا ہے۔", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "کوئی پروجیکٹ یا فولڈر منتخب نہیں کیا گیا۔", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "پروجیکٹس یا فولڈرز کو منتخب کریں۔", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "منتخب کردہ پراجیکٹس اور فولڈرز", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "اس ویجیٹ کے ساتھ، آپ اس ترتیب کو منتخب کر سکتے ہیں اور اس کا تعین کر سکتے ہیں جس میں آپ صارفین کو پروجیکٹ یا فولڈر دکھانا چاہتے ہیں۔", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} پروجیکٹس", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "ہمارے سپورٹ سینٹر پر جائیں۔", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.cizenlab.co/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "تجویز کردہ تصویری قراردادوں کے بارے میں مزید معلومات کے لیے، {supportPageLink}۔"}
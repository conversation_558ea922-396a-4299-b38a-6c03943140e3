{"UI.FormComponents.required": "crwdns209066:0crwdne209066:0", "app.Admin.ManagementFeed.action": "crwdns2322890:0crwdne2322890:0", "app.Admin.ManagementFeed.after": "crwdns2322892:0crwdne2322892:0", "app.Admin.ManagementFeed.before": "crwdns2322894:0crwdne2322894:0", "app.Admin.ManagementFeed.changed": "crwdns2322896:0crwdne2322896:0", "app.Admin.ManagementFeed.created": "crwdns2322898:0crwdne2322898:0", "app.Admin.ManagementFeed.date": "crwdns2322900:0crwdne2322900:0", "app.Admin.ManagementFeed.deleted": "crwdns2322902:0crwdne2322902:0", "app.Admin.ManagementFeed.folder": "crwdns2322904:0crwdne2322904:0", "app.Admin.ManagementFeed.idea": "crwdns2322906:0crwdne2322906:0", "app.Admin.ManagementFeed.in": "crwdns2322908:0{project}crwdne2322908:0", "app.Admin.ManagementFeed.item": "crwdns2322910:0crwdne2322910:0", "app.Admin.ManagementFeed.key": "crwdns2322912:0crwdne2322912:0", "app.Admin.ManagementFeed.managementFeedNudge": "crwdns3848677:0crwdne3848677:0", "app.Admin.ManagementFeed.noActivityFound": "crwdns2322914:0crwdne2322914:0", "app.Admin.ManagementFeed.phase": "crwdns2322916:0crwdne2322916:0", "app.Admin.ManagementFeed.project": "crwdns2322918:0crwdne2322918:0", "app.Admin.ManagementFeed.projectReviewApproved": "crwdns3848599:0crwdne3848599:0", "app.Admin.ManagementFeed.projectReviewRequested": "crwdns3848601:0crwdne3848601:0", "app.Admin.ManagementFeed.title": "crwdns2322920:0crwdne2322920:0", "app.Admin.ManagementFeed.user": "crwdns2322922:0crwdne2322922:0", "app.Admin.ManagementFeed.userPlaceholder": "crwdns2322924:0crwdne2322924:0", "app.Admin.ManagementFeed.value": "crwdns2322926:0crwdne2322926:0", "app.Admin.ManagementFeed.viewDetails": "crwdns2322928:0crwdne2322928:0", "app.Admin.ManagementFeed.warning": "crwdns2322930:0crwdne2322930:0", "app.Admin.Moderation.managementFeed": "crwdns2322932:0crwdne2322932:0", "app.Admin.Moderation.participationFeed": "crwdns2322934:0crwdne2322934:0", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "crwdns2223324:0crwdne2223324:0", "app.components.Admin.Campaigns.clicked": "crwdns2223326:0crwdne2223326:0", "app.components.Admin.Campaigns.deleteCampaignButton": "crwdns2223328:0crwdne2223328:0", "app.components.Admin.Campaigns.deliveryStatus_accepted": "crwdns2223330:0crwdne2223330:0", "app.components.Admin.Campaigns.deliveryStatus_bounced": "crwdns2223332:0crwdne2223332:0", "app.components.Admin.Campaigns.deliveryStatus_clicked": "crwdns2223334:0crwdne2223334:0", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "crwdns5050021:0crwdne5050021:0", "app.components.Admin.Campaigns.deliveryStatus_delivered": "crwdns2223338:0crwdne2223338:0", "app.components.Admin.Campaigns.deliveryStatus_failed": "crwdns2223340:0crwdne2223340:0", "app.components.Admin.Campaigns.deliveryStatus_opened": "crwdns2223342:0crwdne2223342:0", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "crwdns5050023:0crwdne5050023:0", "app.components.Admin.Campaigns.deliveryStatus_sent": "crwdns2223344:0crwdne2223344:0", "app.components.Admin.Campaigns.draft": "crwdns2223346:0crwdne2223346:0", "app.components.Admin.Campaigns.from": "crwdns5049579:0crwdne5049579:0", "app.components.Admin.Campaigns.manageButtonLabel": "crwdns2223348:0crwdne2223348:0", "app.components.Admin.Campaigns.opened": "crwdns2223350:0crwdne2223350:0", "app.components.Admin.Campaigns.project": "crwdns2223352:0crwdne2223352:0", "app.components.Admin.Campaigns.recipientsTitle": "crwdns2223354:0crwdne2223354:0", "app.components.Admin.Campaigns.reply_to": "crwdns5049581:0crwdne5049581:0", "app.components.Admin.Campaigns.sent": "crwdns2223356:0crwdne2223356:0", "app.components.Admin.Campaigns.statsButton": "crwdns2223358:0crwdne2223358:0", "app.components.Admin.Campaigns.subject": "crwdns4760337:0crwdne4760337:0", "app.components.Admin.Campaigns.to": "crwdns5049583:0crwdne5049583:0", "app.components.Admin.ImageCropper.cropFinalSentence": "crwdns5049495:0{link}crwdne5049495:0", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "crwdns5049497:0crwdne5049497:0", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "crwdns5049499:0crwdne5049499:0", "app.components.Admin.ImageCropper.cropSentenceOne": "crwdns5049501:0crwdne5049501:0", "app.components.Admin.ImageCropper.cropSentenceTwo": "crwdns5049503:0{aspect}crwdne5049503:0", "app.components.Admin.ImageCropper.imageSupportPageURL": "crwdns209068:0crwdne209068:0", "app.components.Admin.ImageCropper.infoLinkText": "crwdns209072:0crwdne209072:0", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "crwdns209074:0crwdne209074:0", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "crwdns209076:0crwdne209076:0", "app.components.AdminPage.SettingsPage.eventsPageSetting": "crwdns209078:0crwdne209078:0", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "crwdns209080:0crwdne209080:0", "app.components.AdminPage.SettingsPage.eventsSection": "crwdns209082:0crwdne209082:0", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "crwdns209084:0crwdne209084:0", "app.components.AnonymousPostingToggle.userAnonymity": "crwdns604483:0crwdne604483:0", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "crwdns2400470:0crwdne2400470:0", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "crwdns604487:0crwdne604487:0", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "crwdns604489:0crwdne604489:0", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "crwdns667397:0{supportArticle}crwdne667397:0", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "crwdns667399:0crwdne667399:0", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "crwdns667401:0crwdne667401:0", "app.components.BillingWarning.billingWarning": "crwdns487651:0crwdne487651:0", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "crwdns4305444:0crwdne4305444:0", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "crwdns4607285:0crwdne4607285:0", "app.components.FormSync.downloadExcelTemplate": "crwdns4607287:0crwdne4607287:0", "app.components.FormSync.downloadExcelTemplateTooltip2": "crwdns4760303:0crwdne4760303:0", "app.components.ProjectTemplatePreview.close": "crwdns209096:0crwdne209096:0", "app.components.ProjectTemplatePreview.createProject": "crwdns209098:0crwdne209098:0", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "crwdns4996297:0{templateTitle}crwdne4996297:0", "app.components.ProjectTemplatePreview.goBack": "crwdns209102:0crwdne209102:0", "app.components.ProjectTemplatePreview.goBackTo": "crwdns209104:0{goBackLink}crwdne209104:0", "app.components.ProjectTemplatePreview.govocalExpert": "crwdns2504048:0crwdne2504048:0", "app.components.ProjectTemplatePreview.infoboxLine1": "crwdns209106:0crwdne209106:0", "app.components.ProjectTemplatePreview.infoboxLine2": "crwdns209108:0{link}crwdne209108:0", "app.components.ProjectTemplatePreview.projectFolder": "crwdns209110:0crwdne209110:0", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "crwdns209112:0crwdne209112:0", "app.components.ProjectTemplatePreview.projectNoStartDateError": "crwdns209114:0crwdne209114:0", "app.components.ProjectTemplatePreview.projectStartDate": "crwdns209116:0crwdne209116:0", "app.components.ProjectTemplatePreview.projectTitle": "crwdns209118:0crwdne209118:0", "app.components.ProjectTemplatePreview.projectTitleError": "crwdns209120:0crwdne209120:0", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "crwdns209122:0crwdne209122:0", "app.components.ProjectTemplatePreview.projectsOverviewPage": "crwdns209124:0crwdne209124:0", "app.components.ProjectTemplatePreview.responseError": "crwdns209126:0crwdne209126:0", "app.components.ProjectTemplatePreview.seeMoreTemplates": "crwdns209128:0crwdne209128:0", "app.components.ProjectTemplatePreview.successMessage": "crwdns209130:0crwdne209130:0", "app.components.ProjectTemplatePreview.typeProjectName": "crwdns209132:0crwdne209132:0", "app.components.ProjectTemplatePreview.useTemplate": "crwdns209134:0crwdne209134:0", "app.components.SeatInfo.additionalSeats": "crwdns487653:0crwdne487653:0", "app.components.SeatInfo.additionalSeatsToolTip": "crwdns487655:0crwdne487655:0", "app.components.SeatInfo.adminSeats": "crwdns487657:0crwdne487657:0", "app.components.SeatInfo.adminSeatsIncludedText": "crwdns487659:0{adminSeats}crwdne487659:0", "app.components.SeatInfo.adminSeatsTooltip1": "crwdns487661:0{visitHelpCenter}crwdne487661:0", "app.components.SeatInfo.currentAdminSeatsTitle": "crwdns487663:0crwdne487663:0", "app.components.SeatInfo.currentManagerSeatsTitle": "crwdns487665:0crwdne487665:0", "app.components.SeatInfo.includedAdminToolTip": "crwdns487667:0crwdne487667:0", "app.components.SeatInfo.includedManagerToolTip": "crwdns487669:0crwdne487669:0", "app.components.SeatInfo.includedSeats": "crwdns487671:0crwdne487671:0", "app.components.SeatInfo.managerSeats": "crwdns487673:0crwdne487673:0", "app.components.SeatInfo.managerSeatsTooltip": "crwdns487675:0{visitHelpCenter}crwdne487675:0", "app.components.SeatInfo.managersIncludedText": "crwdns487677:0{managerSeats}crwdne487677:0", "app.components.SeatInfo.remainingSeats": "crwdns487679:0crwdne487679:0", "app.components.SeatInfo.rolesSupportPage": "crwdns487681:0crwdne487681:0", "app.components.SeatInfo.totalSeats": "crwdns487683:0crwdne487683:0", "app.components.SeatInfo.totalSeatsTooltip": "crwdns487685:0crwdne487685:0", "app.components.SeatInfo.usedSeats": "crwdns487687:0crwdne487687:0", "app.components.SeatInfo.view": "crwdns487689:0crwdne487689:0", "app.components.SeatInfo.visitHelpCenter": "crwdns487691:0crwdne487691:0", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "crwdns487693:0{adminSeatsIncluded}crwdne487693:0", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "crwdns487695:0{managerSeatsIncluded}crwdne487695:0", "app.components.UserSearch.addModerators": "crwdns209136:0crwdne209136:0", "app.components.UserSearch.searchUsers": "crwdns209140:0crwdne209140:0", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "crwdns2999221:0crwdne2999221:0", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "crwdns2999223:0crwdne2999223:0", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "crwdns2999225:0crwdne2999225:0", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "crwdns2999227:0crwdne2999227:0", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "crwdns2999229:0crwdne2999229:0", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "crwdns2999231:0crwdne2999231:0", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "crwdns2999233:0crwdne2999233:0", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "crwdns1667668:0crwdne1667668:0", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "crwdns1761140:0crwdne1761140:0", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "crwdns1667670:0{count}crwdne1667670:0", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "crwdns1667674:0crwdne1667674:0", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "crwdns1667676:0{date}crwdne1667676:0", "app.components.admin.DatePhasePicker.Input.openEnded": "crwdns3251029:0crwdne3251029:0", "app.components.admin.DatePhasePicker.Input.selectDate": "crwdns3251031:0crwdne3251031:0", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "crwdns3933067:0crwdne3933067:0", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "crwdns3933069:0crwdne3933069:0", "app.components.admin.Graphs": "crwdns209148:0crwdne209148:0", "app.components.admin.Graphs.noDataShort": "crwdns209150:0crwdne209150:0", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "crwdns209152:0crwdne209152:0", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "crwdns209154:0crwdne209154:0", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "crwdns777189:0crwdne777189:0", "app.components.admin.InputManager.onePost": "crwdns209160:0crwdne209160:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "crwdns3549619:0crwdne3549619:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "crwdns3549621:0crwdne3549621:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "crwdns3549623:0crwdne3549623:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "crwdns3549625:0crwdne3549625:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "crwdns3549627:0crwdne3549627:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "crwdns3549629:0crwdne3549629:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "crwdns3549631:0crwdne3549631:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "crwdns3549633:0crwdne3549633:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "crwdns3549635:0crwdne3549635:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "crwdns3549637:0crwdne3549637:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "crwdns3549639:0crwdne3549639:0", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "crwdns3549641:0{name}crwdne3549641:0", "app.components.admin.PostManager.PostPreview.assignee": "crwdns209162:0crwdne209162:0", "app.components.admin.PostManager.PostPreview.cancelEdit": "crwdns209164:0crwdne209164:0", "app.components.admin.PostManager.PostPreview.currentStatus": "crwdns209166:0crwdne209166:0", "app.components.admin.PostManager.PostPreview.delete": "crwdns209168:0crwdne209168:0", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "crwdns209172:0crwdne209172:0", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "crwdns209174:0crwdne209174:0", "app.components.admin.PostManager.PostPreview.edit": "crwdns209176:0crwdne209176:0", "app.components.admin.PostManager.PostPreview.noOne": "crwdns209178:0crwdne209178:0", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "crwdns209180:0crwdne209180:0", "app.components.admin.PostManager.PostPreview.picks": "crwdns209182:0{picksNumber}crwdne209182:0", "app.components.admin.PostManager.PostPreview.reactionCounts": "crwdns777191:0crwdne777191:0", "app.components.admin.PostManager.PostPreview.save": "crwdns209184:0crwdne209184:0", "app.components.admin.PostManager.PostPreview.submitError": "crwdns209186:0crwdne209186:0", "app.components.admin.PostManager.addFeatureLayer": "crwdns1919686:0crwdne1919686:0", "app.components.admin.PostManager.addFeatureLayerInstruction": "crwdns1919688:0crwdne1919688:0", "app.components.admin.PostManager.addFeatureLayerTooltip": "crwdns1919690:0crwdne1919690:0", "app.components.admin.PostManager.addWebMap": "crwdns1919692:0crwdne1919692:0", "app.components.admin.PostManager.addWebMapInstruction": "crwdns1919694:0crwdne1919694:0", "app.components.admin.PostManager.allPhases": "crwdns209190:0crwdne209190:0", "app.components.admin.PostManager.allProjects": "crwdns209192:0crwdne209192:0", "app.components.admin.PostManager.allStatuses": "crwdns209194:0crwdne209194:0", "app.components.admin.PostManager.allTopics": "crwdns209196:0crwdne209196:0", "app.components.admin.PostManager.anyAssignment": "crwdns209198:0crwdne209198:0", "app.components.admin.PostManager.assignedTo": "crwdns209200:0{assigneeName}crwdne209200:0", "app.components.admin.PostManager.assignedToMe": "crwdns209202:0crwdne209202:0", "app.components.admin.PostManager.assignee": "crwdns209204:0crwdne209204:0", "app.components.admin.PostManager.authenticationError": "crwdns1919696:0crwdne1919696:0", "app.components.admin.PostManager.automatedStatusTooltipText": "crwdns2997291:0crwdne2997291:0", "app.components.admin.PostManager.bodyTitle": "crwdns353628:0crwdne353628:0", "app.components.admin.PostManager.cancel": "crwdns4747451:0crwdne4747451:0", "app.components.admin.PostManager.cancel2": "crwdns1919698:0crwdne1919698:0", "app.components.admin.PostManager.co-sponsors": "crwdns3238321:0crwdne3238321:0", "app.components.admin.PostManager.comments": "crwdns209210:0crwdne209210:0", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "crwdns1091372:0crwdne1091372:0", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "crwdns1091374:0crwdne1091374:0", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "crwdns3848715:0crwdne3848715:0", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "crwdns1091376:0crwdne1091376:0", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "crwdns1091378:0crwdne1091378:0", "app.components.admin.PostManager.components.goToInputManager": "crwdns748825:0crwdne748825:0", "app.components.admin.PostManager.components.goToProposalManager": "crwdns748827:0crwdne748827:0", "app.components.admin.PostManager.contributionFormTitle": "crwdns353630:0crwdne353630:0", "app.components.admin.PostManager.cost": "crwdns777193:0crwdne777193:0", "app.components.admin.PostManager.createInput": "crwdns4747453:0crwdne4747453:0", "app.components.admin.PostManager.createInputsDescription": "crwdns4747455:0crwdne4747455:0", "app.components.admin.PostManager.currentLat": "crwdns209212:0crwdne209212:0", "app.components.admin.PostManager.currentLng": "crwdns209214:0crwdne209214:0", "app.components.admin.PostManager.currentZoomLevel": "crwdns209216:0crwdne209216:0", "app.components.admin.PostManager.defaultEsriError": "crwdns1919700:0crwdne1919700:0", "app.components.admin.PostManager.delete": "crwdns209218:0crwdne209218:0", "app.components.admin.PostManager.deleteAllSelectedInputs": "crwdns209222:0{count}crwdne209222:0", "app.components.admin.PostManager.deleteConfirmation": "crwdns209224:0crwdne209224:0", "app.components.admin.PostManager.dislikes": "crwdns777195:0crwdne777195:0", "app.components.admin.PostManager.edit": "crwdns209236:0crwdne209236:0", "app.components.admin.PostManager.editProjects": "crwdns667343:0crwdne667343:0", "app.components.admin.PostManager.editStatuses": "crwdns667345:0crwdne667345:0", "app.components.admin.PostManager.editTags": "crwdns667347:0crwdne667347:0", "app.components.admin.PostManager.editedPostSave": "crwdns353632:0crwdne353632:0", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "crwdns1919702:0crwdne1919702:0", "app.components.admin.PostManager.esriSideError": "crwdns1919704:0crwdne1919704:0", "app.components.admin.PostManager.esriWebMap": "crwdns1919706:0crwdne1919706:0", "app.components.admin.PostManager.exportAllInputs": "crwdns209238:0crwdne209238:0", "app.components.admin.PostManager.exportIdeasComments": "crwdns209240:0crwdne209240:0", "app.components.admin.PostManager.exportIdeasCommentsProjects": "crwdns209242:0crwdne209242:0", "app.components.admin.PostManager.exportInputsProjects": "crwdns209250:0crwdne209250:0", "app.components.admin.PostManager.exportSelectedInputs": "crwdns209256:0crwdne209256:0", "app.components.admin.PostManager.exportSelectedInputsComments": "crwdns209258:0crwdne209258:0", "app.components.admin.PostManager.exportVotesByInput": "crwdns2234452:0crwdne2234452:0", "app.components.admin.PostManager.exportVotesByUser": "crwdns2234454:0crwdne2234454:0", "app.components.admin.PostManager.exports": "crwdns209260:0crwdne209260:0", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "crwdns1919708:0crwdne1919708:0", "app.components.admin.PostManager.featureLayerTooltop": "crwdns1919710:0crwdne1919710:0", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "crwdns209262:0crwdne209262:0", "app.components.admin.PostManager.feedbackBodyPlaceholder": "crwdns209264:0crwdne209264:0", "app.components.admin.PostManager.fileUploadError": "crwdns353634:0crwdne353634:0", "app.components.admin.PostManager.formTitle": "crwdns353636:0crwdne353636:0", "app.components.admin.PostManager.generalApiError2": "crwdns1919712:0crwdne1919712:0", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "crwdns1919714:0crwdne1919714:0", "app.components.admin.PostManager.goToDefaultMapView": "crwdns209266:0crwdne209266:0", "app.components.admin.PostManager.hiddenFieldsLink": "crwdns209268:0crwdne209268:0", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "crwdns209270:0crwdne209270:0", "app.components.admin.PostManager.hiddenFieldsTip": "crwdns209272:0{hiddenFieldsLink}crwdne209272:0", "app.components.admin.PostManager.import2": "crwdns1919716:0crwdne1919716:0", "app.components.admin.PostManager.importError": "crwdns209274:0crwdne209274:0", "app.components.admin.PostManager.importEsriFeatureLayer": "crwdns1919718:0crwdne1919718:0", "app.components.admin.PostManager.importEsriWebMap": "crwdns1919720:0crwdne1919720:0", "app.components.admin.PostManager.importInputs": "crwdns4747457:0crwdne4747457:0", "app.components.admin.PostManager.imported": "crwdns1159992:0crwdne1159992:0", "app.components.admin.PostManager.initiativeFormTitle": "crwdns3324813:0crwdne3324813:0", "app.components.admin.PostManager.inputCommentsExportFileName": "crwdns209282:0crwdne209282:0", "app.components.admin.PostManager.inputImportProgress": "crwdns4747459:0importedCount={importedCount}crwdnd4747459:0totalCount={totalCount}crwdnd4747459:0totalCount={totalCount}crwdne4747459:0", "app.components.admin.PostManager.inputManagerHeader": "crwdns209284:0crwdne209284:0", "app.components.admin.PostManager.inputs": "crwdns209286:0crwdne209286:0", "app.components.admin.PostManager.inputsExportFileName": "crwdns209288:0crwdne209288:0", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "crwdns209290:0crwdne209290:0", "app.components.admin.PostManager.issueFormTitle": "crwdns353638:0crwdne353638:0", "app.components.admin.PostManager.latestFeedbackMode": "crwdns209292:0crwdne209292:0", "app.components.admin.PostManager.layerAdded": "crwdns1919722:0crwdne1919722:0", "app.components.admin.PostManager.likes": "crwdns777197:0crwdne777197:0", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "crwdns209294:0crwdne209294:0", "app.components.admin.PostManager.mapData": "crwdns1919724:0crwdne1919724:0", "app.components.admin.PostManager.multipleInputs": "crwdns209298:0{ideaCount}crwdne209298:0", "app.components.admin.PostManager.newFeedbackMode": "crwdns209300:0crwdne209300:0", "app.components.admin.PostManager.noFilteredResults": "crwdns209302:0crwdne209302:0", "app.components.admin.PostManager.noInputs": "crwdns4747461:0crwdne4747461:0", "app.components.admin.PostManager.noInputsDescription": "crwdns4747463:0crwdne4747463:0", "app.components.admin.PostManager.noOfInputsToImport": "crwdns4747465:0count={count}crwdne4747465:0", "app.components.admin.PostManager.noOne": "crwdns209306:0crwdne209306:0", "app.components.admin.PostManager.noProject": "crwdns4747467:0crwdne4747467:0", "app.components.admin.PostManager.officialFeedbackModal.author": "crwdns2984451:0crwdne2984451:0", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "crwdns2984453:0crwdne2984453:0", "app.components.admin.PostManager.officialFeedbackModal.description": "crwdns2984455:0crwdne2984455:0", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "crwdns2984457:0crwdne2984457:0", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "crwdns2984459:0crwdne2984459:0", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "crwdns2984461:0crwdne2984461:0", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "crwdns2984463:0crwdne2984463:0", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "crwdns2984465:0crwdne2984465:0", "app.components.admin.PostManager.officialFeedbackModal.skip": "crwdns2984467:0crwdne2984467:0", "app.components.admin.PostManager.officialFeedbackModal.title": "crwdns2984469:0crwdne2984469:0", "app.components.admin.PostManager.officialUpdateAuthor": "crwdns209308:0crwdne209308:0", "app.components.admin.PostManager.officialUpdateBody": "crwdns209310:0crwdne209310:0", "app.components.admin.PostManager.offlinePicks": "crwdns3549643:0crwdne3549643:0", "app.components.admin.PostManager.offlineVotes": "crwdns3549645:0crwdne3549645:0", "app.components.admin.PostManager.onlineVotes": "crwdns3549647:0crwdne3549647:0", "app.components.admin.PostManager.optionFormTitle": "crwdns353640:0crwdne353640:0", "app.components.admin.PostManager.participants": "crwdns777199:0crwdne777199:0", "app.components.admin.PostManager.participatoryBudgettingPicks": "crwdns209314:0crwdne209314:0", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "crwdns3549649:0crwdne3549649:0", "app.components.admin.PostManager.pbItemCountTooltip": "crwdns209316:0crwdne209316:0", "app.components.admin.PostManager.petitionFormTitle": "crwdns3324815:0crwdne3324815:0", "app.components.admin.PostManager.postedIn": "crwdns353642:0{projectLink}crwdne353642:0", "app.components.admin.PostManager.projectFormTitle": "crwdns353644:0crwdne353644:0", "app.components.admin.PostManager.projectsTab": "crwdns209318:0crwdne209318:0", "app.components.admin.PostManager.projectsTabTooltipContent": "crwdns209320:0crwdne209320:0", "app.components.admin.PostManager.proposalFormTitle": "crwdns3324817:0crwdne3324817:0", "app.components.admin.PostManager.proposedBudgetTitle": "crwdns353646:0crwdne353646:0", "app.components.admin.PostManager.publication_date": "crwdns209322:0crwdne209322:0", "app.components.admin.PostManager.questionFormTitle": "crwdns353648:0crwdne353648:0", "app.components.admin.PostManager.reactions": "crwdns777201:0crwdne777201:0", "app.components.admin.PostManager.resetFiltersButton": "crwdns209326:0crwdne209326:0", "app.components.admin.PostManager.resetInputFiltersDescription": "crwdns209328:0crwdne209328:0", "app.components.admin.PostManager.saved": "crwdns209330:0crwdne209330:0", "app.components.admin.PostManager.screeningTooltip": "crwdns2997293:0crwdne2997293:0", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "crwdns2997295:0crwdne2997295:0", "app.components.admin.PostManager.selectAPhase": "crwdns4747469:0crwdne4747469:0", "app.components.admin.PostManager.selectAProject": "crwdns4747471:0crwdne4747471:0", "app.components.admin.PostManager.setAsDefaultMapView": "crwdns209332:0crwdne209332:0", "app.components.admin.PostManager.startFromPastInputs": "crwdns4747473:0crwdne4747473:0", "app.components.admin.PostManager.statusChangeGenericError": "crwdns209336:0crwdne209336:0", "app.components.admin.PostManager.statusChangeSave": "crwdns209338:0crwdne209338:0", "app.components.admin.PostManager.statusesTab": "crwdns209340:0crwdne209340:0", "app.components.admin.PostManager.statusesTabTooltipContent": "crwdns209342:0crwdne209342:0", "app.components.admin.PostManager.submitApiError": "crwdns353650:0crwdne353650:0", "app.components.admin.PostManager.timelineTab": "crwdns209344:0crwdne209344:0", "app.components.admin.PostManager.timelineTabTooltipText": "crwdns209346:0crwdne209346:0", "app.components.admin.PostManager.title": "crwdns209348:0crwdne209348:0", "app.components.admin.PostManager.topicsTab": "crwdns209350:0crwdne209350:0", "app.components.admin.PostManager.topicsTabTooltipText": "crwdns209352:0crwdne209352:0", "app.components.admin.PostManager.view": "crwdns1919532:0crwdne1919532:0", "app.components.admin.PostManager.votes": "crwdns209356:0crwdne209356:0", "app.components.admin.PostManager.votesByInputExportFileName": "crwdns2234456:0crwdne2234456:0", "app.components.admin.PostManager.votesByUserExportFileName": "crwdns2234458:0crwdne2234458:0", "app.components.admin.PostManager.webMapAlreadyExists": "crwdns1919726:0crwdne1919726:0", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "crwdns1919728:0crwdne1919728:0", "app.components.admin.PostManager.webMapTooltip": "crwdns1919730:0crwdne1919730:0", "app.components.admin.PostManager.xDaysLeft": "crwdns353652:0x={x}crwdne353652:0", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "crwdns4305448:0crwdne4305448:0", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "crwdns4305450:0crwdne4305450:0", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "crwdns4305452:0crwdne4305452:0", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "crwdns4305454:0crwdne4305454:0", "app.components.admin.ProjectEdit.survey.downloadResults2": "crwdns4305460:0crwdne4305460:0", "app.components.admin.ReportExportMenu.FileName.fromFilter": "crwdns209358:0crwdne209358:0", "app.components.admin.ReportExportMenu.FileName.groupFilter": "crwdns209360:0crwdne209360:0", "app.components.admin.ReportExportMenu.FileName.projectFilter": "crwdns209362:0crwdne209362:0", "app.components.admin.ReportExportMenu.FileName.topicFilter": "crwdns209364:0crwdne209364:0", "app.components.admin.ReportExportMenu.FileName.untilFilter": "crwdns209366:0crwdne209366:0", "app.components.admin.ReportExportMenu.downloadPng": "crwdns209368:0crwdne209368:0", "app.components.admin.ReportExportMenu.downloadSvg": "crwdns209370:0crwdne209370:0", "app.components.admin.ReportExportMenu.downloadXlsx": "crwdns209372:0crwdne209372:0", "app.components.admin.SlugInput.regexError": "crwdns209374:0crwdne209374:0", "app.components.admin.TerminologyConfig.saveButton": "crwdns209376:0crwdne209376:0", "app.components.admin.commonGroundInputManager.title": "crwdns4747475:0crwdne4747475:0", "app.components.admin.seatSetSuccess.admin": "crwdns400346:0crwdne400346:0", "app.components.admin.seatSetSuccess.allDone": "crwdns400348:0crwdne400348:0", "app.components.admin.seatSetSuccess.close": "crwdns400350:0crwdne400350:0", "app.components.admin.seatSetSuccess.manager": "crwdns452771:0crwdne452771:0", "app.components.admin.seatSetSuccess.orderCompleted": "crwdns400354:0crwdne400354:0", "app.components.admin.seatSetSuccess.reflectedMessage": "crwdns400356:0crwdne400356:0", "app.components.admin.seatSetSuccess.rightsGranted": "crwdns400358:0{seatType}crwdne400358:0", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "crwdns4305466:0crwdne4305466:0", "app.components.app.containers.AdminPage.ProjectEdit.beta": "crwdns4747477:0crwdne4747477:0", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "crwdns4747479:0crwdne4747479:0", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "crwdns649363:0crwdne649363:0", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "crwdns209378:0crwdne209378:0", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "crwdns2735453:0crwdne2735453:0", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "crwdns2735455:0crwdne2735455:0", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "crwdns209380:0{googleFormsTooltipLink}crwdne209380:0", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "crwdns209382:0crwdne209382:0", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "crwdns209384:0crwdne209384:0", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "crwdns209386:0crwdne209386:0", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "crwdns3324819:0crwdne3324819:0", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "crwdns209388:0crwdne209388:0", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "crwdns209390:0crwdne209390:0", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "crwdns649365:0{supportArticleLink}crwdne649365:0", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "crwdns649367:0crwdne649367:0", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "crwdns649369:0crwdne649369:0", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "crwdns1877114:0crwdne1877114:0", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "crwdns209392:0crwdne209392:0", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "crwdns777203:0crwdne777203:0", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "crwdns777205:0crwdne777205:0", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "crwdns2223360:0crwdne2223360:0", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "crwdns209394:0crwdne209394:0", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "crwdns209396:0crwdne209396:0", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "crwdns777207:0crwdne777207:0", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "crwdns777209:0crwdne777209:0", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "crwdns3251033:0crwdne3251033:0", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "crwdns3251035:0crwdne3251035:0", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "crwdns209398:0crwdne209398:0", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "crwdns777211:0crwdne777211:0", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "crwdns2677451:0crwdne2677451:0", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "crwdns777213:0{optionsPageLink}crwdne777213:0", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "crwdns2997297:0crwdne2997297:0", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "crwdns2016762:0crwdne2016762:0", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "crwdns3324821:0crwdne3324821:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "crwdns1442896:0crwdne1442896:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "crwdns1442900:0{participants}crwdne1442900:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "crwdns1442902:0{participants}crwdne1442902:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "crwdns1442904:0crwdne1442904:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "crwdns1442906:0{participants}crwdne1442906:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "crwdns4747481:0crwdne4747481:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "crwdns1442908:0crwdne1442908:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "crwdns1442910:0crwdne1442910:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "crwdns1442912:0crwdne1442912:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "crwdns1442914:0crwdne1442914:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "crwdns1442916:0crwdne1442916:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "crwdns1442918:0crwdne1442918:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "crwdns1442920:0crwdne1442920:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "crwdns1442924:0crwdne1442924:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "crwdns1442926:0crwdne1442926:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "crwdns1442928:0crwdne1442928:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "crwdns1442930:0crwdne1442930:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "crwdns1442932:0crwdne1442932:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "crwdns1442938:0crwdne1442938:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "crwdns2677457:0crwdne2677457:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "crwdns1442940:0{participants}crwdne1442940:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "crwdns4902967:0{participants}crwdne4902967:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "crwdns1442942:0crwdne1442942:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "crwdns1442944:0{participants}crwdne1442944:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "crwdns1442946:0{participants}crwdne1442946:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "crwdns1442948:0{participants}crwdne1442948:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "crwdns1442950:0crwdne1442950:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "crwdns2677565:0{participants}crwdne2677565:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "crwdns1442952:0crwdne1442952:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "crwdns1442954:0{participants}crwdne1442954:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "crwdns1442956:0crwdne1442956:0", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "crwdns1442958:0crwdne1442958:0", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "crwdns2997299:0crwdne2997299:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "crwdns1442960:0crwdne1442960:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "crwdns3585463:0crwdne3585463:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "crwdns3848603:0crwdne3848603:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "crwdns4368164:0crwdne4368164:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "crwdns3848607:0{name}crwdne3848607:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "crwdns1442962:0crwdne1442962:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "crwdns1442964:0crwdne1442964:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "crwdns4169113:0crwdne4169113:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "crwdns1442966:0crwdne1442966:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "crwdns1442968:0crwdne1442968:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "crwdns4969195:0crwdne4969195:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "crwdns3763169:0crwdne3763169:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "crwdns4368166:0inFolder={inFolder}crwdne4368166:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "crwdns1442970:0participantsCount={participantsCount}crwdnd1442970:0participantsCount={participantsCount}crwdne1442970:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "crwdns3263233:0crwdne3263233:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "crwdns3263235:0crwdne3263235:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "crwdns3263237:0crwdne3263237:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "crwdns3763171:0crwdne3763171:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "crwdns3263241:0crwdne3263241:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "crwdns4902969:0crwdne4902969:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "crwdns3263243:0crwdne3263243:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "crwdns3848611:0crwdne3848611:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "crwdns4368168:0crwdne4368168:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "crwdns4969197:0crwdne4969197:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "crwdns3848615:0crwdne3848615:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "crwdns1442974:0crwdne1442974:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "crwdns1442976:0crwdne1442976:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "crwdns3585465:0crwdne3585465:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "crwdns3585467:0crwdne3585467:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "crwdns3848617:0crwdne3848617:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "crwdns3848619:0crwdne3848619:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "crwdns3848621:0crwdne3848621:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "crwdns3848623:0crwdne3848623:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "crwdns3848625:0crwdne3848625:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "crwdns4368170:0inFolder={inFolder}crwdne4368170:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "crwdns3848629:0crwdne3848629:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "crwdns3585469:0crwdne3585469:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "crwdns3585471:0crwdne3585471:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "crwdns3585473:0crwdne3585473:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "crwdns3848631:0crwdne3848631:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "crwdns3585475:0crwdne3585475:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "crwdns3585477:0crwdne3585477:0", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "crwdns1442978:0crwdne1442978:0", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "crwdns209400:0crwdne209400:0", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "crwdns3324823:0crwdne3324823:0", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "crwdns209402:0crwdne209402:0", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "crwdns2735457:0crwdne2735457:0", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "crwdns2735459:0crwdne2735459:0", "app.components.app.containers.AdminPage.ProjectEdit.report": "crwdns1845164:0crwdne1845164:0", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "crwdns2997301:0crwdne2997301:0", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "crwdns2016764:0crwdne2016764:0", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "crwdns2016766:0crwdne2016766:0", "app.components.formBuilder.cancelMethodChange1": "crwdns1962702:0crwdne1962702:0", "app.components.formBuilder.changeMethodWarning1": "crwdns1962704:0crwdne1962704:0", "app.components.formBuilder.changingMethod1": "crwdns1962706:0crwdne1962706:0", "app.components.formBuilder.confirmMethodChange1": "crwdns1962708:0crwdne1962708:0", "app.components.formBuilder.copySurveyModal.cancel": "crwdns1866536:0crwdne1866536:0", "app.components.formBuilder.copySurveyModal.description": "crwdns1866538:0crwdne1866538:0", "app.components.formBuilder.copySurveyModal.duplicate": "crwdns1866540:0crwdne1866540:0", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "crwdns1866542:0crwdne1866542:0", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "crwdns1866544:0crwdne1866544:0", "app.components.formBuilder.copySurveyModal.noProject": "crwdns1866546:0crwdne1866546:0", "app.components.formBuilder.copySurveyModal.noProjectSelected": "crwdns1866548:0crwdne1866548:0", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "crwdns1866550:0crwdne1866550:0", "app.components.formBuilder.copySurveyModal.surveyPhase": "crwdns1866552:0crwdne1866552:0", "app.components.formBuilder.copySurveyModal.title": "crwdns1866554:0crwdne1866554:0", "app.components.formBuilder.editWarningModal.addOrReorder": "crwdns1159936:0crwdne1159936:0", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "crwdns1159938:0crwdne1159938:0", "app.components.formBuilder.editWarningModal.changeQuestionText2": "crwdns1159940:0crwdne1159940:0", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "crwdns1159942:0crwdne1159942:0", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "crwdns1159944:0crwdne1159944:0", "app.components.formBuilder.editWarningModal.deteleAQuestion": "crwdns1159946:0crwdne1159946:0", "app.components.formBuilder.editWarningModal.exportYouResponses2": "crwdns1159948:0crwdne1159948:0", "app.components.formBuilder.editWarningModal.loseDataWarning3": "crwdns1159950:0crwdne1159950:0", "app.components.formBuilder.editWarningModal.noCancel": "crwdns1159952:0crwdne1159952:0", "app.components.formBuilder.editWarningModal.title4": "crwdns1159954:0crwdne1159954:0", "app.components.formBuilder.editWarningModal.yesContinue": "crwdns1159956:0crwdne1159956:0", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "crwdns4280902:0crwdne4280902:0", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "crwdns4394134:0{accessRightsSettingsLink}crwdne4394134:0", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "crwdns1866496:0crwdne1866496:0", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "crwdns1866498:0crwdne1866498:0", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "crwdns1866500:0crwdne1866500:0", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "crwdns1866502:0crwdne1866502:0", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "crwdns1866504:0{accessRightsSettingsLink}crwdne1866504:0", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "crwdns1866506:0crwdne1866506:0", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "crwdns1866508:0{accessRightsSettingsLink}crwdne1866508:0", "app.components.onboarding.askFollowPreferences": "crwdns1081650:0crwdne1081650:0", "app.components.onboarding.followHelperText": "crwdns1081652:0crwdne1081652:0", "app.components.onboarding.followPreferences": "crwdns1081654:0crwdne1081654:0", "app.components.seatsWithinPlan.seatsExceededPlanText": "crwdns487697:0{noOfSeatsInPlan}crwdnd487697:0{noOfAdditionalSeats}crwdne487697:0", "app.components.seatsWithinPlan.seatsWithinPlanText": "crwdns487699:0crwdne487699:0", "app.containers.Admin.Campaigns.campaignFrom": "crwdns209404:0crwdne209404:0", "app.containers.Admin.Campaigns.campaignTo": "crwdns209406:0crwdne209406:0", "app.containers.Admin.Campaigns.customEmails": "crwdns567995:0crwdne567995:0", "app.containers.Admin.Campaigns.customEmailsDescription": "crwdns567997:0crwdne567997:0", "app.containers.Admin.Campaigns.noAccess": "crwdns209422:0crwdne209422:0", "app.containers.Admin.Campaigns.tabAutomatedEmails": "crwdns209424:0crwdne209424:0", "app.containers.Admin.Insights.tabReports": "crwdns1845186:0crwdne1845186:0", "app.containers.Admin.Invitations.a11y_removeInvite": "crwdns3848671:0crwdne3848671:0", "app.containers.Admin.Invitations.addToGroupLabel": "crwdns209754:0crwdne209754:0", "app.containers.Admin.Invitations.adminLabel1": "crwdns284636:0crwdne284636:0", "app.containers.Admin.Invitations.adminLabelTooltip": "crwdns209758:0crwdne209758:0", "app.containers.Admin.Invitations.configureInvitations": "crwdns209760:0crwdne209760:0", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "crwdns209764:0crwdne209764:0", "app.containers.Admin.Invitations.deleteInvite": "crwdns209766:0crwdne209766:0", "app.containers.Admin.Invitations.deleteInviteConfirmation": "crwdns3848673:0crwdne3848673:0", "app.containers.Admin.Invitations.deleteInviteTooltip": "crwdns209768:0crwdne209768:0", "app.containers.Admin.Invitations.downloadFillOutTemplate": "crwdns209770:0crwdne209770:0", "app.containers.Admin.Invitations.downloadTemplate": "crwdns209772:0crwdne209772:0", "app.containers.Admin.Invitations.email": "crwdns209774:0crwdne209774:0", "app.containers.Admin.Invitations.emailListLabel": "crwdns209776:0crwdne209776:0", "app.containers.Admin.Invitations.exportInvites": "crwdns209778:0crwdne209778:0", "app.containers.Admin.Invitations.fileRequirements": "crwdns209780:0crwdne209780:0", "app.containers.Admin.Invitations.filetypeError": "crwdns209782:0crwdne209782:0", "app.containers.Admin.Invitations.groupsPlaceholder": "crwdns209784:0crwdne209784:0", "app.containers.Admin.Invitations.helmetDescription": "crwdns209786:0crwdne209786:0", "app.containers.Admin.Invitations.helmetTitle": "crwdns209788:0crwdne209788:0", "app.containers.Admin.Invitations.importOptionsInfo": "crwdns209790:0{supportPageLink}crwdne209790:0", "app.containers.Admin.Invitations.importTab": "crwdns209792:0crwdne209792:0", "app.containers.Admin.Invitations.invitationExpirationWarning": "crwdns1042155:0crwdne1042155:0", "app.containers.Admin.Invitations.invitationOptions": "crwdns209794:0crwdne209794:0", "app.containers.Admin.Invitations.invitationSubtitle": "crwdns209796:0crwdne209796:0", "app.containers.Admin.Invitations.invitePeople": "crwdns209798:0crwdne209798:0", "app.containers.Admin.Invitations.inviteStatus": "crwdns209800:0crwdne209800:0", "app.containers.Admin.Invitations.inviteStatusAccepted": "crwdns209802:0crwdne209802:0", "app.containers.Admin.Invitations.inviteStatusPending": "crwdns209804:0crwdne209804:0", "app.containers.Admin.Invitations.inviteTextLabel": "crwdns209806:0crwdne209806:0", "app.containers.Admin.Invitations.invitedSince": "crwdns209808:0crwdne209808:0", "app.containers.Admin.Invitations.invitesSupportPageURL": "crwdns209810:0crwdne209810:0", "app.containers.Admin.Invitations.localeLabel": "crwdns209812:0crwdne209812:0", "app.containers.Admin.Invitations.moderatorLabel": "crwdns209814:0crwdne209814:0", "app.containers.Admin.Invitations.moderatorLabelTooltip": "crwdns209816:0{moderatorLabelTooltipLink}crwdne209816:0", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "crwdns209818:0crwdne209818:0", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "crwdns209820:0crwdne209820:0", "app.containers.Admin.Invitations.name": "crwdns209822:0crwdne209822:0", "app.containers.Admin.Invitations.processing": "crwdns209824:0crwdne209824:0", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "crwdns209826:0crwdne209826:0", "app.containers.Admin.Invitations.save": "crwdns209828:0crwdne209828:0", "app.containers.Admin.Invitations.saveErrorMessage": "crwdns209830:0crwdne209830:0", "app.containers.Admin.Invitations.saveSuccess": "crwdns209832:0crwdne209832:0", "app.containers.Admin.Invitations.saveSuccessMessage": "crwdns209834:0crwdne209834:0", "app.containers.Admin.Invitations.supportPage": "crwdns209836:0crwdne209836:0", "app.containers.Admin.Invitations.supportPageLinkText": "crwdns209838:0crwdne209838:0", "app.containers.Admin.Invitations.tabAllInvitations": "crwdns209840:0crwdne209840:0", "app.containers.Admin.Invitations.tabInviteUsers": "crwdns209842:0crwdne209842:0", "app.containers.Admin.Invitations.textTab": "crwdns209844:0crwdne209844:0", "app.containers.Admin.Invitations.unknownError": "crwdns209846:0crwdne209846:0", "app.containers.Admin.Invitations.uploadCompletedFile": "crwdns209848:0crwdne209848:0", "app.containers.Admin.Invitations.visitSupportPage": "crwdns209850:0{supportPageLink}crwdne209850:0", "app.containers.Admin.Moderation.all": "crwdns209852:0crwdne209852:0", "app.containers.Admin.Moderation.belongsTo": "crwdns209854:0crwdne209854:0", "app.containers.Admin.Moderation.collapse": "crwdns209856:0crwdne209856:0", "app.containers.Admin.Moderation.comment": "crwdns209858:0crwdne209858:0", "app.containers.Admin.Moderation.commentDeletionCancelButton": "crwdns4582109:0crwdne4582109:0", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "crwdns4582111:0crwdne4582111:0", "app.containers.Admin.Moderation.confirmCommentDeletion": "crwdns4582113:0crwdne4582113:0", "app.containers.Admin.Moderation.content": "crwdns209860:0crwdne209860:0", "app.containers.Admin.Moderation.date": "crwdns209862:0crwdne209862:0", "app.containers.Admin.Moderation.deleteComment": "crwdns4582115:0crwdne4582115:0", "app.containers.Admin.Moderation.goToComment": "crwdns209864:0crwdne209864:0", "app.containers.Admin.Moderation.goToPost": "crwdns209866:0crwdne209866:0", "app.containers.Admin.Moderation.goToProposal": "crwdns209868:0crwdne209868:0", "app.containers.Admin.Moderation.markFlagsError": "crwdns209872:0crwdne209872:0", "app.containers.Admin.Moderation.markNotSeen": "crwdns209874:0selectedItemsCount={selectedItemsCount}crwdne209874:0", "app.containers.Admin.Moderation.markSeen": "crwdns209876:0selectedItemsCount={selectedItemsCount}crwdne209876:0", "app.containers.Admin.Moderation.moderationsTooltip": "crwdns209878:0crwdne209878:0", "app.containers.Admin.Moderation.noUnviewedItems": "crwdns209880:0crwdne209880:0", "app.containers.Admin.Moderation.noViewedItems": "crwdns209882:0crwdne209882:0", "app.containers.Admin.Moderation.pageTitle1": "crwdns568001:0crwdne568001:0", "app.containers.Admin.Moderation.post": "crwdns209886:0crwdne209886:0", "app.containers.Admin.Moderation.profanityBlockerSetting": "crwdns209888:0crwdne209888:0", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "crwdns209890:0crwdne209890:0", "app.containers.Admin.Moderation.project": "crwdns209892:0crwdne209892:0", "app.containers.Admin.Moderation.read": "crwdns209894:0crwdne209894:0", "app.containers.Admin.Moderation.readMore": "crwdns209896:0crwdne209896:0", "app.containers.Admin.Moderation.removeFlagsError": "crwdns209898:0crwdne209898:0", "app.containers.Admin.Moderation.rowsPerPage": "crwdns209900:0crwdne209900:0", "app.containers.Admin.Moderation.settings": "crwdns209902:0crwdne209902:0", "app.containers.Admin.Moderation.settingsSavingError": "crwdns209904:0crwdne209904:0", "app.containers.Admin.Moderation.show": "crwdns209906:0crwdne209906:0", "app.containers.Admin.Moderation.status": "crwdns209908:0crwdne209908:0", "app.containers.Admin.Moderation.successfulUpdateSettings": "crwdns209910:0crwdne209910:0", "app.containers.Admin.Moderation.type": "crwdns209912:0crwdne209912:0", "app.containers.Admin.Moderation.unread": "crwdns209914:0crwdne209914:0", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "crwdns209916:0crwdne209916:0", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "crwdns209918:0crwdne209918:0", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "crwdns209920:0crwdne209920:0", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "crwdns209930:0crwdne209930:0", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "crwdns209932:0crwdne209932:0", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "crwdns209934:0crwdne209934:0", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "crwdns209936:0crwdne209936:0", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "crwdns209938:0crwdne209938:0", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "crwdns209940:0crwdne209940:0", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "crwdns209942:0crwdne209942:0", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "crwdns209944:0crwdne209944:0", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "crwdns209946:0crwdne209946:0", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "crwdns209948:0crwdne209948:0", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "crwdns209950:0crwdne209950:0", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "crwdns209952:0crwdne209952:0", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "crwdns209954:0crwdne209954:0", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "crwdns209956:0crwdne209956:0", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "crwdns209958:0crwdne209958:0", "app.containers.Admin.PagesAndMenu.addButton": "crwdns209960:0crwdne209960:0", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "crwdns209962:0crwdne209962:0", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "crwdns209964:0crwdne209964:0", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "crwdns1101198:0crwdne1101198:0", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "crwdns209968:0crwdne209968:0", "app.containers.Admin.PagesAndMenu.components.savePage": "crwdns209970:0crwdne209970:0", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "crwdns209972:0crwdne209972:0", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "crwdns209974:0crwdne209974:0", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "crwdns209976:0crwdne209976:0", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "crwdns209978:0crwdne209978:0", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "crwdns209980:0crwdne209980:0", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "crwdns209982:0crwdne209982:0", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "crwdns209984:0crwdne209984:0", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "crwdns209986:0{orgName}crwdne209986:0", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "crwdns209988:0crwdne209988:0", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "crwdns209990:0crwdne209990:0", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "crwdns209992:0crwdne209992:0", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "crwdns209994:0crwdne209994:0", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "crwdns209996:0crwdne209996:0", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "crwdns209998:0crwdne209998:0", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "crwdns210000:0crwdne210000:0", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "crwdns210002:0crwdne210002:0", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "crwdns210004:0crwdne210004:0", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "crwdns210006:0crwdne210006:0", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "crwdns210008:0crwdne210008:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "crwdns3561761:0crwdne3561761:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "crwdns210010:0crwdne210010:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "crwdns210012:0crwdne210012:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "crwdns210014:0crwdne210014:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "crwdns210016:0crwdne210016:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "crwdns3799139:0crwdne3799139:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "crwdns210020:0crwdne210020:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "crwdns210022:0crwdne210022:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "crwdns210024:0crwdne210024:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "crwdns210026:0crwdne210026:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "crwdns210028:0crwdne210028:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "crwdns210030:0crwdne210030:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "crwdns210032:0crwdne210032:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "crwdns210034:0crwdne210034:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "crwdns210036:0{orgName}crwdne210036:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "crwdns210038:0crwdne210038:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "crwdns210040:0crwdne210040:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "crwdns210042:0crwdne210042:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "crwdns210044:0crwdne210044:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "crwdns210046:0crwdne210046:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "crwdns210048:0crwdne210048:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "crwdns210050:0crwdne210050:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "crwdns210052:0crwdne210052:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "crwdns210054:0crwdne210054:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "crwdns210056:0crwdne210056:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "crwdns210058:0crwdne210058:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "crwdns210060:0crwdne210060:0", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "crwdns210062:0crwdne210062:0", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "crwdns210064:0crwdne210064:0", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "crwdns210066:0{orgName}crwdne210066:0", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "crwdns210068:0crwdne210068:0", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "crwdns210070:0crwdne210070:0", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "crwdns1294614:0crwdne1294614:0", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "crwdns210072:0{pageSettingsLink}crwdne210072:0", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "crwdns210074:0crwdne210074:0", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "crwdns210076:0{orgName}crwdne210076:0", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "crwdns210078:0crwdne210078:0", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "crwdns210080:0crwdne210080:0", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "crwdns210082:0{pageSettingsLink}crwdne210082:0", "app.containers.Admin.PagesAndMenu.defaultTag": "crwdns210084:0crwdne210084:0", "app.containers.Admin.PagesAndMenu.deleteButton": "crwdns210086:0crwdne210086:0", "app.containers.Admin.PagesAndMenu.editButton": "crwdns210088:0crwdne210088:0", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "crwdns210090:0crwdne210090:0", "app.containers.Admin.PagesAndMenu.heroBannerError": "crwdns210092:0crwdne210092:0", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "crwdns210094:0crwdne210094:0", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "crwdns210096:0crwdne210096:0", "app.containers.Admin.PagesAndMenu.homeTitle": "crwdns210098:0crwdne210098:0", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "crwdns210100:0crwdne210100:0", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "crwdns2654407:0crwdne2654407:0", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "crwdns210102:0{orgName}crwdne210102:0", "app.containers.Admin.PagesAndMenu.removeButton": "crwdns210104:0crwdne210104:0", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "crwdns210106:0crwdne210106:0", "app.containers.Admin.PagesAndMenu.title": "crwdns210108:0crwdne210108:0", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "crwdns210110:0crwdne210110:0", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "crwdns210112:0crwdne210112:0", "app.containers.Admin.PagesAndMenu.topInfoError": "crwdns210114:0crwdne210114:0", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "crwdns210116:0crwdne210116:0", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "crwdns210118:0{orgName}crwdne210118:0", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "crwdns210120:0crwdne210120:0", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "crwdns210122:0crwdne210122:0", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "crwdns210124:0crwdne210124:0", "app.containers.Admin.PagesAndMenu.viewButton": "crwdns210126:0crwdne210126:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "crwdns2515566:0crwdne2515566:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "crwdns2467818:0crwdne2467818:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "crwdns2467820:0crwdne2467820:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "crwdns2467822:0crwdne2467822:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "crwdns2467824:0crwdne2467824:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "crwdns2467826:0crwdne2467826:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "crwdns2467828:0crwdne2467828:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "crwdns2467830:0crwdne2467830:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "crwdns2467832:0crwdne2467832:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "crwdns2467834:0crwdne2467834:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "crwdns2467836:0crwdne2467836:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "crwdns2467838:0crwdne2467838:0", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "crwdns2467840:0crwdne2467840:0", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "crwdns2311904:0crwdne2311904:0", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "crwdns417910:0crwdne417910:0", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "crwdns417912:0crwdne417912:0", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "crwdns210128:0crwdne210128:0", "app.containers.Admin.Users.GroupsHeader.editGroup": "crwdns210130:0crwdne210130:0", "app.containers.Admin.Users.GroupsPanel.admins": "crwdns2278824:0crwdne2278824:0", "app.containers.Admin.Users.GroupsPanel.allUsers": "crwdns210132:0crwdne210132:0", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "crwdns210134:0crwdne210134:0", "app.containers.Admin.Users.GroupsPanel.managers": "crwdns2278826:0crwdne2278826:0", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "crwdns2278828:0crwdne2278828:0", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "crwdns210136:0crwdne210136:0", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "crwdns210138:0crwdne210138:0", "app.containers.Admin.Users.admin": "crwdns210140:0crwdne210140:0", "app.containers.Admin.Users.assign": "crwdns2278830:0crwdne2278830:0", "app.containers.Admin.Users.assignedItems": "crwdns2278832:0{name}crwdne2278832:0", "app.containers.Admin.Users.buyOneAditionalSeat": "crwdns400360:0crwdne400360:0", "app.containers.Admin.Users.changeUserRights": "crwdns487701:0crwdne487701:0", "app.containers.Admin.Users.confirm": "crwdns284642:0crwdne284642:0", "app.containers.Admin.Users.confirmAdminQuestion": "crwdns284644:0{name}crwdne284644:0", "app.containers.Admin.Users.confirmNormalUserQuestion": "crwdns284646:0{name}crwdne284646:0", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "crwdns452773:0{name}crwdne452773:0", "app.containers.Admin.Users.deleteUser": "crwdns210142:0crwdne210142:0", "app.containers.Admin.Users.email": "crwdns210144:0crwdne210144:0", "app.containers.Admin.Users.folder": "crwdns2278834:0crwdne2278834:0", "app.containers.Admin.Users.folderManager": "crwdns284648:0crwdne284648:0", "app.containers.Admin.Users.helmetDescription": "crwdns210146:0crwdne210146:0", "app.containers.Admin.Users.helmetTitle": "crwdns210148:0crwdne210148:0", "app.containers.Admin.Users.inviteUsers": "crwdns568003:0crwdne568003:0", "app.containers.Admin.Users.joined": "crwdns2278836:0crwdne2278836:0", "app.containers.Admin.Users.lastActive": "crwdns2278838:0crwdne2278838:0", "app.containers.Admin.Users.name": "crwdns210150:0crwdne210150:0", "app.containers.Admin.Users.noAssignedItems": "crwdns2278840:0crwdne2278840:0", "app.containers.Admin.Users.options": "crwdns210152:0crwdne210152:0", "app.containers.Admin.Users.permissionToBuy": "crwdns284652:0{name}crwdne284652:0", "app.containers.Admin.Users.platformAdmin": "crwdns284654:0crwdne284654:0", "app.containers.Admin.Users.projectManager": "crwdns284656:0crwdne284656:0", "app.containers.Admin.Users.reachedLimitMessage": "crwdns284658:0{name}crwdne284658:0", "app.containers.Admin.Users.registeredUser": "crwdns284660:0crwdne284660:0", "app.containers.Admin.Users.remove": "crwdns2278842:0crwdne2278842:0", "app.containers.Admin.Users.removeModeratorFrom": "crwdns2278844:0{folderTitle}crwdne2278844:0", "app.containers.Admin.Users.role": "crwdns2278846:0crwdne2278846:0", "app.containers.Admin.Users.seeProfile": "crwdns210154:0crwdne210154:0", "app.containers.Admin.Users.selectPublications": "crwdns2278848:0crwdne2278848:0", "app.containers.Admin.Users.selectPublicationsPlaceholder": "crwdns2278850:0crwdne2278850:0", "app.containers.Admin.Users.setAsAdmin": "crwdns284662:0crwdne284662:0", "app.containers.Admin.Users.setAsNormalUser": "crwdns284664:0crwdne284664:0", "app.containers.Admin.Users.setAsProjectModerator": "crwdns2278852:0crwdne2278852:0", "app.containers.Admin.Users.setUserAsProjectModerator": "crwdns2278854:0{name}crwdne2278854:0", "app.containers.Admin.Users.userBlockModal.allDone": "crwdns417914:0crwdne417914:0", "app.containers.Admin.Users.userBlockModal.blockAction": "crwdns417916:0crwdne417916:0", "app.containers.Admin.Users.userBlockModal.blockInfo1": "crwdns417918:0crwdne417918:0", "app.containers.Admin.Users.userBlockModal.blocked": "crwdns417920:0crwdne417920:0", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "crwdns417922:0{from}crwdnd417922:0{to}crwdne417922:0", "app.containers.Admin.Users.userBlockModal.cancel": "crwdns417924:0crwdne417924:0", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "crwdns417926:0{name}crwdne417926:0", "app.containers.Admin.Users.userBlockModal.confirmation1": "crwdns417928:0{name}crwdnd417928:0{date}crwdne417928:0", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "crwdns417930:0numberOfDays={numberOfDays}crwdnd417930:0numberOfDays={numberOfDays}crwdne417930:0", "app.containers.Admin.Users.userBlockModal.header": "crwdns417932:0crwdne417932:0", "app.containers.Admin.Users.userBlockModal.reasonLabel": "crwdns417934:0crwdne417934:0", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "crwdns417936:0crwdne417936:0", "app.containers.Admin.Users.userBlockModal.subtitle1": "crwdns417938:0{daysBlocked}crwdne417938:0", "app.containers.Admin.Users.userBlockModal.unblockAction": "crwdns417940:0crwdne417940:0", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "crwdns417942:0crwdne417942:0", "app.containers.Admin.Users.userDeletionConfirmation": "crwdns210158:0crwdne210158:0", "app.containers.Admin.Users.userDeletionFailed": "crwdns210160:0crwdne210160:0", "app.containers.Admin.Users.userDeletionProposalVotes": "crwdns1199812:0crwdne1199812:0", "app.containers.Admin.Users.userExportFileName": "crwdns210162:0crwdne210162:0", "app.containers.Admin.Users.userInsights": "crwdns667349:0crwdne667349:0", "app.containers.Admin.Users.youCantDeleteYourself": "crwdns210164:0crwdne210164:0", "app.containers.Admin.Users.youCantUnadminYourself": "crwdns210166:0crwdne210166:0", "app.containers.Admin.communityMonitor.communityMonitorLabel": "crwdns4305468:0crwdne4305468:0", "app.containers.Admin.communityMonitor.healthScore": "crwdns4305470:0crwdne4305470:0", "app.containers.Admin.communityMonitor.healthScoreDescription": "crwdns4368236:0crwdne4368236:0", "app.containers.Admin.communityMonitor.lastQuarter": "crwdns4305472:0crwdne4305472:0", "app.containers.Admin.communityMonitor.liveMonitor": "crwdns4305474:0crwdne4305474:0", "app.containers.Admin.communityMonitor.noResults": "crwdns4305476:0crwdne4305476:0", "app.containers.Admin.communityMonitor.noSurveyResponses": "crwdns4305478:0crwdne4305478:0", "app.containers.Admin.communityMonitor.participants": "crwdns4305480:0crwdne4305480:0", "app.containers.Admin.communityMonitor.quarterChartLabel": "crwdns4305482:0{quarter}crwdnd4305482:0{year}crwdne4305482:0", "app.containers.Admin.communityMonitor.quarterYearCondensed": "crwdns4305484:0{quarterNumber}crwdnd4305484:0{year}crwdne4305484:0", "app.containers.Admin.communityMonitor.reports": "crwdns4305486:0crwdne4305486:0", "app.containers.Admin.communityMonitor.settings": "crwdns4305488:0crwdne4305488:0", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "crwdns4305490:0crwdne4305490:0", "app.containers.Admin.communityMonitor.settings.accessRights2": "crwdns4305492:0crwdne4305492:0", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "crwdns4368238:0crwdne4368238:0", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "crwdns4305496:0crwdne4305496:0", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "crwdns4305498:0crwdne4305498:0", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "crwdns4305500:0crwdne4305500:0", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "crwdns4305502:0crwdne4305502:0", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "crwdns4305504:0crwdne4305504:0", "app.containers.Admin.communityMonitor.settings.management2": "crwdns4305506:0crwdne4305506:0", "app.containers.Admin.communityMonitor.settings.popup": "crwdns4305508:0crwdne4305508:0", "app.containers.Admin.communityMonitor.settings.popupDescription3": "crwdns4368240:0crwdne4368240:0", "app.containers.Admin.communityMonitor.settings.popupSettings": "crwdns4305512:0crwdne4305512:0", "app.containers.Admin.communityMonitor.settings.preview": "crwdns4305514:0crwdne4305514:0", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "crwdns4368242:0crwdne4368242:0", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "crwdns4368244:0crwdne4368244:0", "app.containers.Admin.communityMonitor.settings.save": "crwdns4305520:0crwdne4305520:0", "app.containers.Admin.communityMonitor.settings.saved": "crwdns4305522:0crwdne4305522:0", "app.containers.Admin.communityMonitor.settings.settings": "crwdns4305524:0crwdne4305524:0", "app.containers.Admin.communityMonitor.settings.survey2": "crwdns4305526:0crwdne4305526:0", "app.containers.Admin.communityMonitor.settings.surveySettings3": "crwdns4305528:0crwdne4305528:0", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "crwdns4305530:0crwdne4305530:0", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "crwdns4305532:0crwdne4305532:0", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "crwdns4305534:0crwdne4305534:0", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "crwdns4305536:0crwdne4305536:0", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "crwdns4368246:0crwdne4368246:0", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "crwdns4305540:0crwdne4305540:0", "app.containers.Admin.communityMonitor.totalSurveyResponses": "crwdns4305542:0crwdne4305542:0", "app.containers.Admin.communityMonitor.upsell.aiSummary": "crwdns4305544:0crwdne4305544:0", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "crwdns4305546:0crwdne4305546:0", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "crwdns4305548:0crwdne4305548:0", "app.containers.Admin.communityMonitor.upsell.healthScore": "crwdns4305550:0crwdne4305550:0", "app.containers.Admin.communityMonitor.upsell.learnMore": "crwdns4305552:0crwdne4305552:0", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "crwdns4305554:0crwdne4305554:0", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "crwdns4305556:0crwdne4305556:0", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "crwdns4305558:0crwdne4305558:0", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "crwdns4305560:0crwdne4305560:0", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "crwdns4305562:0crwdne4305562:0", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "crwdns4305564:0crwdne4305564:0", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "crwdns4305566:0crwdne4305566:0", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "crwdns4305568:0crwdne4305568:0", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "crwdns4305570:0crwdne4305570:0", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "crwdns4582057:0crwdne4582057:0", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "crwdns4582059:0crwdne4582059:0", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "crwdns4582061:0crwdne4582061:0", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "crwdns4582063:0crwdne4582063:0", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "crwdns4582065:0crwdne4582065:0", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "crwdns4582067:0crwdne4582067:0", "app.containers.Admin.earlyAccessLabel": "crwdns4582163:0crwdne4582163:0", "app.containers.Admin.earlyAccessLabelExplanation": "crwdns4582165:0crwdne4582165:0", "app.containers.Admin.emails.addCampaign": "crwdns2223362:0crwdne2223362:0", "app.containers.Admin.emails.addCampaignTitle": "crwdns210186:0crwdne210186:0", "app.containers.Admin.emails.allParticipantsInProject": "crwdns2223364:0crwdne2223364:0", "app.containers.Admin.emails.allUsers": "crwdns210190:0crwdne210190:0", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "crwdns568005:0crwdne568005:0", "app.containers.Admin.emails.automatedEmails": "crwdns568007:0crwdne568007:0", "app.containers.Admin.emails.automatedEmailsDigest": "crwdns649249:0crwdne649249:0", "app.containers.Admin.emails.automatedEmailsRecipients": "crwdns649251:0crwdne649251:0", "app.containers.Admin.emails.automatedEmailsTriggers": "crwdns649253:0crwdne649253:0", "app.containers.Admin.emails.changeRecipientsButton": "crwdns210196:0crwdne210196:0", "app.containers.Admin.emails.clickOnButtonForExamples": "crwdns685335:0crwdne685335:0", "app.containers.Admin.emails.confirmSendHeader": "crwdns210198:0crwdne210198:0", "app.containers.Admin.emails.deleteButtonLabel": "crwdns210200:0crwdne210200:0", "app.containers.Admin.emails.draft": "crwdns210204:0crwdne210204:0", "app.containers.Admin.emails.editButtonLabel": "crwdns210206:0crwdne210206:0", "app.containers.Admin.emails.editCampaignTitle": "crwdns210208:0crwdne210208:0", "app.containers.Admin.emails.editDisabledTooltip2": "crwdns4760339:0crwdne4760339:0", "app.containers.Admin.emails.editRegion_button_text_multiloc": "crwdns4760341:0crwdne4760341:0", "app.containers.Admin.emails.editRegion_intro_multiloc": "crwdns4760343:0crwdne4760343:0", "app.containers.Admin.emails.editRegion_subject_multiloc": "crwdns4760345:0crwdne4760345:0", "app.containers.Admin.emails.editRegion_title_multiloc": "crwdns4760347:0crwdne4760347:0", "app.containers.Admin.emails.emailCreated": "crwdns5049585:0crwdne5049585:0", "app.containers.Admin.emails.emailUpdated": "crwdns5049587:0crwdne5049587:0", "app.containers.Admin.emails.emptyCampaignsDescription": "crwdns2223366:0crwdne2223366:0", "app.containers.Admin.emails.emptyCampaignsHeader": "crwdns2223368:0crwdne2223368:0", "app.containers.Admin.emails.failed": "crwdns210210:0crwdne210210:0", "app.containers.Admin.emails.fieldBody": "crwdns210212:0crwdne210212:0", "app.containers.Admin.emails.fieldBodyError": "crwdns210214:0crwdne210214:0", "app.containers.Admin.emails.fieldGroupContent": "crwdns5049589:0crwdne5049589:0", "app.containers.Admin.emails.fieldReplyTo": "crwdns210216:0crwdne210216:0", "app.containers.Admin.emails.fieldReplyToEmailError": "crwdns210218:0crwdne210218:0", "app.containers.Admin.emails.fieldReplyToError": "crwdns210220:0crwdne210220:0", "app.containers.Admin.emails.fieldReplyToTooltip": "crwdns210222:0crwdne210222:0", "app.containers.Admin.emails.fieldSender": "crwdns210224:0crwdne210224:0", "app.containers.Admin.emails.fieldSenderError": "crwdns210226:0crwdne210226:0", "app.containers.Admin.emails.fieldSenderTooltip": "crwdns210228:0crwdne210228:0", "app.containers.Admin.emails.fieldSubject": "crwdns210230:0crwdne210230:0", "app.containers.Admin.emails.fieldSubjectError": "crwdns210232:0crwdne210232:0", "app.containers.Admin.emails.fieldSubjectTooltip": "crwdns210234:0crwdne210234:0", "app.containers.Admin.emails.fieldTo": "crwdns210236:0crwdne210236:0", "app.containers.Admin.emails.fieldToTooltip": "crwdns210238:0crwdne210238:0", "app.containers.Admin.emails.formSave": "crwdns210240:0crwdne210240:0", "app.containers.Admin.emails.formSaveAsDraft": "crwdns5049591:0crwdne5049591:0", "app.containers.Admin.emails.from": "crwdns649255:0crwdne649255:0", "app.containers.Admin.emails.groups": "crwdns210242:0crwdne210242:0", "app.containers.Admin.emails.helmetDescription": "crwdns210244:0crwdne210244:0", "app.containers.Admin.emails.nameVariablesInfo2": "crwdns1497990:0{firstName}crwdnd1497990:0{lastName}crwdnd1497990:0{firstName}crwdnd1497990:0{lastName}crwdne1497990:0", "app.containers.Admin.emails.previewSentConfirmation": "crwdns210256:0crwdne210256:0", "app.containers.Admin.emails.previewTitle": "crwdns210258:0crwdne210258:0", "app.containers.Admin.emails.regionMultilocError": "crwdns4760349:0crwdne4760349:0", "app.containers.Admin.emails.seeEmailHereText": "crwdns685337:0crwdne685337:0", "app.containers.Admin.emails.send": "crwdns210262:0crwdne210262:0", "app.containers.Admin.emails.sendNowButton": "crwdns210264:0crwdne210264:0", "app.containers.Admin.emails.sendTestEmailButton": "crwdns210266:0crwdne210266:0", "app.containers.Admin.emails.sendTestEmailTooltip2": "crwdns5049593:0crwdne5049593:0", "app.containers.Admin.emails.senderRecipients": "crwdns210270:0crwdne210270:0", "app.containers.Admin.emails.sending": "crwdns210272:0crwdne210272:0", "app.containers.Admin.emails.sent": "crwdns210274:0crwdne210274:0", "app.containers.Admin.emails.sentToUsers": "crwdns649259:0crwdne649259:0", "app.containers.Admin.emails.subject": "crwdns649261:0crwdne649261:0", "app.containers.Admin.emails.supportButtonLabel": "crwdns649263:0crwdne649263:0", "app.containers.Admin.emails.supportButtonLink2": "crwdns4734573:0crwdne4734573:0", "app.containers.Admin.emails.to": "crwdns649267:0crwdne649267:0", "app.containers.Admin.emails.toAllUsers": "crwdns210282:0crwdne210282:0", "app.containers.Admin.emails.viewExample": "crwdns649269:0crwdne649269:0", "app.containers.Admin.ideas.import": "crwdns210284:0crwdne210284:0", "app.containers.Admin.inspirationHub.AllProjects": "crwdns4243459:0crwdne4243459:0", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "crwdns4305572:0crwdne4305572:0", "app.containers.Admin.inspirationHub.DocumentAnnotation": "crwdns4218407:0crwdne4218407:0", "app.containers.Admin.inspirationHub.ExternalSurvey": "crwdns4582097:0crwdne4582097:0", "app.containers.Admin.inspirationHub.Filters.Country": "crwdns4218413:0crwdne4218413:0", "app.containers.Admin.inspirationHub.Filters.Method": "crwdns4218415:0crwdne4218415:0", "app.containers.Admin.inspirationHub.Filters.Search": "crwdns4218417:0crwdne4218417:0", "app.containers.Admin.inspirationHub.Filters.Topic": "crwdns4456947:0crwdne4456947:0", "app.containers.Admin.inspirationHub.Filters.population": "crwdns4243455:0crwdne4243455:0", "app.containers.Admin.inspirationHub.Highlighted": "crwdns4243461:0crwdne4243461:0", "app.containers.Admin.inspirationHub.Ideation": "crwdns4218425:0crwdne4218425:0", "app.containers.Admin.inspirationHub.Information": "crwdns4218427:0crwdne4218427:0", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "crwdns4268270:0crwdne4268270:0", "app.containers.Admin.inspirationHub.PinnedProjects.country": "crwdns4268272:0crwdne4268272:0", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "crwdns4268274:0crwdne4268274:0", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "crwdns4268276:0crwdne4268276:0", "app.containers.Admin.inspirationHub.Poll": "crwdns4218429:0crwdne4218429:0", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "crwdns4218551:0crwdne4218551:0", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "crwdns4218553:0crwdne4218553:0", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "crwdns4218555:0{number}crwdnd4218555:0{title}crwdne4218555:0", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "crwdns4268286:0crwdne4268286:0", "app.containers.Admin.inspirationHub.Proposals": "crwdns4218439:0crwdne4218439:0", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "crwdns4243913:0crwdne4243913:0", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "crwdns4243915:0crwdne4243915:0", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "crwdns4243917:0crwdne4243917:0", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "crwdns4243919:0crwdne4243919:0", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "crwdns4243921:0crwdne4243921:0", "app.containers.Admin.inspirationHub.Survey": "crwdns4218443:0crwdne4218443:0", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "crwdns4394136:0crwdne4394136:0", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "crwdns4394138:0crwdne4394138:0", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "crwdns4394140:0crwdne4394140:0", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "crwdns4394142:0crwdne4394142:0", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "crwdns4394144:0crwdne4394144:0", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "crwdns5050109:0crwdne5050109:0", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "crwdns4394146:0crwdne4394146:0", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "crwdns4394148:0crwdne4394148:0", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "crwdns4394150:0crwdne4394150:0", "app.containers.Admin.inspirationHub.Volunteering": "crwdns4218445:0crwdne4218445:0", "app.containers.Admin.inspirationHub.Voting": "crwdns4218447:0crwdne4218447:0", "app.containers.Admin.inspirationHub.commonGround": "crwdns4747483:0crwdne4747483:0", "app.containers.Admin.inspirationHub.filters": "crwdns4268278:0crwdne4268278:0", "app.containers.Admin.inspirationHub.resetFilters": "crwdns4243909:0crwdne4243909:0", "app.containers.Admin.inspirationHub.seemsLike": "crwdns4268280:0{filters}crwdne4268280:0", "app.containers.Admin.messaging.automated.editModalTitle": "crwdns4760351:0crwdne4760351:0", "app.containers.Admin.messaging.automated.variablesToolTip": "crwdns4760353:0crwdne4760353:0", "app.containers.Admin.messaging.helmetTitle": "crwdns210348:0crwdne210348:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "crwdns3573585:0crwdne3573585:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "crwdns3573577:0crwdne3573577:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "crwdns3477979:0crwdne3477979:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "crwdns3573587:0crwdne3573587:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "crwdns3477981:0crwdne3477981:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "crwdns3573579:0crwdne3573579:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "crwdns3573581:0crwdne3573581:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "crwdns3573583:0crwdne3573583:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "crwdns3621233:0crwdne3621233:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "crwdns3621235:0crwdne3621235:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "crwdns3621237:0crwdne3621237:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "crwdns3621239:0crwdne3621239:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "crwdns3621241:0crwdne3621241:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "crwdns3621243:0crwdne3621243:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "crwdns3621245:0crwdne3621245:0", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "crwdns3621247:0crwdne3621247:0", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "crwdns2654409:0crwdne2654409:0", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "crwdns2654411:0crwdne2654411:0", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "crwdns2654413:0crwdne2654413:0", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "crwdns2654415:0crwdne2654415:0", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "crwdns2654417:0crwdne2654417:0", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "crwdns2654419:0crwdne2654419:0", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "crwdns2654421:0crwdne2654421:0", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "crwdns2654423:0crwdne2654423:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "crwdns4305574:0crwdne4305574:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "crwdns4305576:0crwdne4305576:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "crwdns4305578:0crwdne4305578:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "crwdns4305580:0crwdne4305580:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "crwdns4305582:0crwdne4305582:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "crwdns4419144:0crwdne4419144:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "crwdns4305584:0crwdne4305584:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "crwdns3477983:0crwdne3477983:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "crwdns3477985:0crwdne3477985:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "crwdns3763163:0crwdne3763163:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "crwdns1336700:0crwdne1336700:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "crwdns1336702:0{orgName}crwdne1336702:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "crwdns3373809:0crwdne3373809:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "crwdns3373811:0crwdne3373811:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "crwdns3373813:0crwdne3373813:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "crwdns3373815:0crwdne3373815:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "crwdns3373817:0crwdne3373817:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "crwdns3373819:0crwdne3373819:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "crwdns3799235:0crwdne3799235:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "crwdns3373821:0crwdne3373821:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "crwdns3373823:0crwdne3373823:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "crwdns3573589:0{days}crwdne3573589:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "crwdns3573591:0{weeks}crwdne3573591:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "crwdns3573593:0{days}crwdne3573593:0", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "crwdns3573595:0{weeks}crwdne3573595:0", "app.containers.Admin.project.Campaigns.campaignFrom": "crwdns2223370:0crwdne2223370:0", "app.containers.Admin.project.Campaigns.campaignTo": "crwdns2223372:0crwdne2223372:0", "app.containers.Admin.project.Campaigns.customEmails": "crwdns2223374:0crwdne2223374:0", "app.containers.Admin.project.Campaigns.customEmailsDescription": "crwdns2223376:0crwdne2223376:0", "app.containers.Admin.project.Campaigns.noAccess": "crwdns2223378:0crwdne2223378:0", "app.containers.Admin.project.emails.addCampaign": "crwdns2223380:0crwdne2223380:0", "app.containers.Admin.project.emails.addCampaignTitle": "crwdns2223382:0crwdne2223382:0", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "crwdns3202265:0{participants}crwdne3202265:0", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "crwdns3202267:0crwdne3202267:0", "app.containers.Admin.project.emails.dateSent": "crwdns2223388:0crwdne2223388:0", "app.containers.Admin.project.emails.deleteButtonLabel": "crwdns2223390:0crwdne2223390:0", "app.containers.Admin.project.emails.draft": "crwdns2223392:0crwdne2223392:0", "app.containers.Admin.project.emails.editButtonLabel": "crwdns2223394:0crwdne2223394:0", "app.containers.Admin.project.emails.editCampaignTitle": "crwdns2223396:0crwdne2223396:0", "app.containers.Admin.project.emails.emptyCampaignsDescription": "crwdns2223398:0crwdne2223398:0", "app.containers.Admin.project.emails.emptyCampaignsHeader": "crwdns2223400:0crwdne2223400:0", "app.containers.Admin.project.emails.failed": "crwdns2223402:0crwdne2223402:0", "app.containers.Admin.project.emails.fieldBody": "crwdns2223404:0crwdne2223404:0", "app.containers.Admin.project.emails.fieldBodyError": "crwdns2223406:0crwdne2223406:0", "app.containers.Admin.project.emails.fieldReplyTo": "crwdns2223408:0crwdne2223408:0", "app.containers.Admin.project.emails.fieldReplyToEmailError": "crwdns2223410:0crwdne2223410:0", "app.containers.Admin.project.emails.fieldReplyToError": "crwdns2223412:0crwdne2223412:0", "app.containers.Admin.project.emails.fieldReplyToTooltip": "crwdns2223414:0crwdne2223414:0", "app.containers.Admin.project.emails.fieldSender": "crwdns2223416:0crwdne2223416:0", "app.containers.Admin.project.emails.fieldSenderError": "crwdns2223418:0crwdne2223418:0", "app.containers.Admin.project.emails.fieldSenderTooltip": "crwdns2223420:0crwdne2223420:0", "app.containers.Admin.project.emails.fieldSubject": "crwdns2223422:0crwdne2223422:0", "app.containers.Admin.project.emails.fieldSubjectError": "crwdns2223424:0crwdne2223424:0", "app.containers.Admin.project.emails.fieldSubjectTooltip": "crwdns2223426:0crwdne2223426:0", "app.containers.Admin.project.emails.fieldTo": "crwdns2223428:0crwdne2223428:0", "app.containers.Admin.project.emails.formSave": "crwdns2223430:0crwdne2223430:0", "app.containers.Admin.project.emails.from": "crwdns2223432:0crwdne2223432:0", "app.containers.Admin.project.emails.helmetDescription": "crwdns2223434:0crwdne2223434:0", "app.containers.Admin.project.emails.infoboxAdminText": "crwdns2223436:0{link}crwdne2223436:0", "app.containers.Admin.project.emails.infoboxLinkText": "crwdns2223438:0crwdne2223438:0", "app.containers.Admin.project.emails.infoboxModeratorText": "crwdns2223440:0crwdne2223440:0", "app.containers.Admin.project.emails.message": "crwdns2223442:0crwdne2223442:0", "app.containers.Admin.project.emails.nameVariablesInfo2": "crwdns2223444:0{firstName}crwdnd2223444:0{lastName}crwdnd2223444:0{firstName}crwdnd2223444:0{lastName}crwdne2223444:0", "app.containers.Admin.project.emails.participants": "crwdns2223446:0crwdne2223446:0", "app.containers.Admin.project.emails.previewSentConfirmation": "crwdns2223448:0crwdne2223448:0", "app.containers.Admin.project.emails.previewTitle": "crwdns2223450:0crwdne2223450:0", "app.containers.Admin.project.emails.projectParticipants": "crwdns2223452:0crwdne2223452:0", "app.containers.Admin.project.emails.recipients": "crwdns2223454:0crwdne2223454:0", "app.containers.Admin.project.emails.send": "crwdns2223456:0crwdne2223456:0", "app.containers.Admin.project.emails.sendTestEmailButton": "crwdns2223458:0crwdne2223458:0", "app.containers.Admin.project.emails.sendTestEmailTooltip": "crwdns2223460:0crwdne2223460:0", "app.containers.Admin.project.emails.senderRecipients": "crwdns2223462:0crwdne2223462:0", "app.containers.Admin.project.emails.sending": "crwdns2223464:0crwdne2223464:0", "app.containers.Admin.project.emails.sent": "crwdns2223466:0crwdne2223466:0", "app.containers.Admin.project.emails.sentToUsers": "crwdns2223468:0crwdne2223468:0", "app.containers.Admin.project.emails.status": "crwdns2223470:0crwdne2223470:0", "app.containers.Admin.project.emails.subject": "crwdns2223472:0crwdne2223472:0", "app.containers.Admin.project.emails.to": "crwdns2223474:0crwdne2223474:0", "app.containers.Admin.project.messaging.helmetTitle": "crwdns2223476:0crwdne2223476:0", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "crwdns210350:0{supportPageLink}crwdne210350:0", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "crwdns210352:0{supportPageLink}crwdne210352:0", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "crwdns210354:0crwdne210354:0", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "crwdns210356:0crwdne210356:0", "app.containers.Admin.projects.all.askPersonalData3": "crwdns4481961:0crwdne4481961:0", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "crwdns5050111:0crwdne5050111:0", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "crwdns5050113:0crwdne5050113:0", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "crwdns5050115:0crwdne5050115:0", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "crwdns5063949:0crwdne5063949:0", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "crwdns5050117:0crwdne5050117:0", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "crwdns5050119:0crwdne5050119:0", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "crwdns4708647:0crwdne4708647:0", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "crwdns4708649:0crwdne4708649:0", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "crwdns4582089:0crwdne4582089:0", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "crwdns4582091:0crwdne4582091:0", "app.containers.Admin.projects.all.components.archived": "crwdns210358:0crwdne210358:0", "app.containers.Admin.projects.all.components.draft": "crwdns210360:0crwdne210360:0", "app.containers.Admin.projects.all.components.manageButtonLabel": "crwdns210362:0crwdne210362:0", "app.containers.Admin.projects.all.copyProjectButton": "crwdns210364:0crwdne210364:0", "app.containers.Admin.projects.all.copyProjectError": "crwdns210366:0crwdne210366:0", "app.containers.Admin.projects.all.customiseEnd": "crwdns4582093:0crwdne4582093:0", "app.containers.Admin.projects.all.customiseStart": "crwdns4582095:0crwdne4582095:0", "app.containers.Admin.projects.all.deleteFolderButton1": "crwdns210368:0crwdne210368:0", "app.containers.Admin.projects.all.deleteFolderConfirm": "crwdns210370:0crwdne210370:0", "app.containers.Admin.projects.all.deleteFolderError": "crwdns210372:0crwdne210372:0", "app.containers.Admin.projects.all.deleteProjectButtonFull": "crwdns210374:0crwdne210374:0", "app.containers.Admin.projects.all.deleteProjectConfirmation": "crwdns210376:0crwdne210376:0", "app.containers.Admin.projects.all.deleteProjectError": "crwdns210378:0crwdne210378:0", "app.containers.Admin.projects.all.exportAsPDF1": "crwdns2158456:0crwdne2158456:0", "app.containers.Admin.projects.all.itIsAlsoPossible1": "crwdns2158458:0crwdne2158458:0", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "crwdns2158460:0crwdne2158460:0", "app.containers.Admin.projects.all.logicNotInPDF": "crwdns2169378:0crwdne2169378:0", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "crwdns5050121:0crwdne5050121:0", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "crwdns4969251:0crwdne4969251:0", "app.containers.Admin.projects.all.new.Folders.Table.folder": "crwdns4747429:0crwdne4747429:0", "app.containers.Admin.projects.all.new.Folders.Table.managers": "crwdns4747431:0crwdne4747431:0", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "crwdns4747433:0numberOfProjects={numberOfProjects}crwdne4747433:0", "app.containers.Admin.projects.all.new.Folders.Table.status": "crwdns4747435:0crwdne4747435:0", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "crwdns4982643:0crwdne4982643:0", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "crwdns4996299:0crwdne4996299:0", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "crwdns4996301:0crwdne4996301:0", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "crwdns4996303:0crwdne4996303:0", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "crwdns4969335:0crwdne4969335:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "crwdns4969259:0crwdne4969259:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "crwdns4969261:0crwdne4969261:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "crwdns4969263:0crwdne4969263:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "crwdns4969265:0crwdne4969265:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "crwdns4969267:0crwdne4969267:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "crwdns4969269:0crwdne4969269:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "crwdns4969271:0crwdne4969271:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "crwdns4969273:0crwdne4969273:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "crwdns4969275:0crwdne4969275:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "crwdns4969277:0crwdne4969277:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "crwdns4969279:0crwdne4969279:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "crwdns4786305:0crwdne4786305:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "crwdns4786307:0crwdne4786307:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "crwdns4786309:0crwdne4786309:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "crwdns4786311:0crwdne4786311:0", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "crwdns4786313:0crwdne4786313:0", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "crwdns5077261:0crwdne5077261:0", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "crwdns5050123:0crwdne5050123:0", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "crwdns4982645:0crwdne4982645:0", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "crwdns4982647:0crwdne4982647:0", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "crwdns4734577:0crwdne4734577:0", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "crwdns4734579:0crwdne4734579:0", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc": "crwdns5063951:0crwdne5063951:0", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc": "crwdns5063953:0crwdne5063953:0", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "crwdns4734583:0crwdne4734583:0", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "crwdns4734585:0crwdne4734585:0", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "crwdns4996305:0crwdne4996305:0", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "crwdns4996307:0crwdne4996307:0", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "crwdns4996309:0crwdne4996309:0", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "crwdns4996311:0crwdne4996311:0", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "crwdns5036307:0crwdne5036307:0", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "crwdns5036309:0crwdne5036309:0", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "crwdns5036311:0crwdne5036311:0", "app.containers.Admin.projects.all.new.Projects.Table.admins": "crwdns4734587:0crwdne4734587:0", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "crwdns4969253:0crwdne4969253:0", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "crwdns4996319:0crwdne4996319:0", "app.containers.Admin.projects.all.new.Projects.Table.archived": "crwdns4734589:0crwdne4734589:0", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "crwdns4734591:0crwdne4734591:0", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "crwdns4734593:0{days}crwdne4734593:0", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "crwdns4734595:0{days}crwdne4734595:0", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "crwdns4996321:0crwdne4996321:0", "app.containers.Admin.projects.all.new.Projects.Table.draft": "crwdns4734597:0crwdne4734597:0", "app.containers.Admin.projects.all.new.Projects.Table.end": "crwdns4996323:0crwdne4996323:0", "app.containers.Admin.projects.all.new.Projects.Table.ended": "crwdns4734599:0crwdne4734599:0", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "crwdns4734601:0crwdne4734601:0", "app.containers.Admin.projects.all.new.Projects.Table.groups": "crwdns4734603:0crwdne4734603:0", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "crwdns4996325:0crwdne4996325:0", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "crwdns4969255:0crwdne4969255:0", "app.containers.Admin.projects.all.new.Projects.Table.manager": "crwdns4996327:0crwdne4996327:0", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "crwdns4734605:0{months}crwdne4734605:0", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "crwdns4734607:0{months}crwdne4734607:0", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "crwdns4996329:0crwdne4996329:0", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "crwdns4996331:0crwdne4996331:0", "app.containers.Admin.projects.all.new.Projects.Table.phase": "crwdns4996333:0crwdne4996333:0", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "crwdns4734609:0crwdne4734609:0", "app.containers.Admin.projects.all.new.Projects.Table.project": "crwdns4734611:0crwdne4734611:0", "app.containers.Admin.projects.all.new.Projects.Table.public": "crwdns4734617:0crwdne4734617:0", "app.containers.Admin.projects.all.new.Projects.Table.published": "crwdns4734619:0crwdne4734619:0", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "crwdns4969257:0crwdne4969257:0", "app.containers.Admin.projects.all.new.Projects.Table.start": "crwdns4996335:0crwdne4996335:0", "app.containers.Admin.projects.all.new.Projects.Table.status": "crwdns4734621:0crwdne4734621:0", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "crwdns4996337:0crwdne4996337:0", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "crwdns5049791:0crwdne5049791:0", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "crwdns4734623:0crwdne4734623:0", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "crwdns4996339:0crwdne4996339:0", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "crwdns4996341:0{numberOfGroups}crwdne4996341:0", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "crwdns4996343:0{numberOfManagers}crwdne4996343:0", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "crwdns4734625:0{years}crwdne4734625:0", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "crwdns4734627:0{years}crwdne4734627:0", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "crwdns4889679:0{phaseName}crwdnd4889679:0{participationMethod}crwdne4889679:0", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "crwdns4889681:0{days}crwdne4889681:0", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "crwdns4889683:0{folderName}crwdne4889683:0", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "crwdns4889685:0crwdne4889685:0", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "crwdns4889687:0crwdne4889687:0", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "crwdns4889689:0crwdne4889689:0", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "crwdns4889691:0{number}crwdnd4889691:0{phaseName}crwdnd4889691:0{participationMethod}crwdne4889691:0", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "crwdns4889693:0crwdne4889693:0", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "crwdns4889695:0crwdne4889695:0", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "crwdns4889697:0{date}crwdne4889697:0", "app.containers.Admin.projects.all.new.arrangeProjects": "crwdns5050125:0crwdne5050125:0", "app.containers.Admin.projects.all.new.calendar": "crwdns5050127:0crwdne5050127:0", "app.containers.Admin.projects.all.new.folders": "crwdns4708641:0crwdne4708641:0", "app.containers.Admin.projects.all.new.projects": "crwdns4708643:0crwdne4708643:0", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "crwdns4889699:0crwdne4889699:0", "app.containers.Admin.projects.all.new.timeline.noEndDay": "crwdns4889701:0crwdne4889701:0", "app.containers.Admin.projects.all.new.timeline.project": "crwdns4889703:0crwdne4889703:0", "app.containers.Admin.projects.all.notes": "crwdns2158464:0crwdne2158464:0", "app.containers.Admin.projects.all.personalDataExplanation5": "crwdns4481963:0crwdne4481963:0", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "crwdns4244042:0crwdne4244042:0", "app.containers.Admin.projects.project.analysis.Comments.comments": "crwdns4206223:0crwdne4206223:0", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "crwdns4244044:0crwdne4244044:0", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "crwdns4244046:0crwdne4244046:0", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "crwdns4244048:0count={count}crwdne4244048:0", "app.containers.Admin.projects.project.analysis.aiSummary": "crwdns1963148:0crwdne1963148:0", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "crwdns1963150:0crwdne1963150:0", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "crwdns4969199:0crwdne4969199:0", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "crwdns4969201:0crwdne4969201:0", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "crwdns4969203:0crwdne4969203:0", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "crwdns4969205:0crwdne4969205:0", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "crwdns4969207:0crwdne4969207:0", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "crwdns4969209:0crwdne4969209:0", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "crwdns4969211:0crwdne4969211:0", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "crwdns4969213:0crwdne4969213:0", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "crwdns4969215:0crwdne4969215:0", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "crwdns4969217:0crwdne4969217:0", "app.containers.Admin.projects.project.ideas.analysisAction1": "crwdns4305914:0crwdne4305914:0", "app.containers.Admin.projects.project.ideas.analysisText2": "crwdns4305916:0crwdne4305916:0", "app.containers.Admin.projects.project.ideas.importInputs": "crwdns2158468:0crwdne2158468:0", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "crwdns1919518:0crwdne1919518:0", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "crwdns1845166:0crwdne1845166:0", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "crwdns1845168:0crwdne1845168:0", "app.containers.Admin.projects.project.information.ReportTab.createReport": "crwdns1845170:0crwdne1845170:0", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "crwdns1845188:0crwdne1845188:0", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "crwdns1919520:0crwdne1919520:0", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "crwdns1919522:0crwdne1919522:0", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "crwdns1845190:0crwdne1845190:0", "app.containers.Admin.projects.project.information.ReportTab.report": "crwdns1845172:0crwdne1845172:0", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "crwdns1845174:0crwdne1845174:0", "app.containers.Admin.projects.project.information.ReportTab.visible": "crwdns1919524:0crwdne1919524:0", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "crwdns1919526:0crwdne1919526:0", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "crwdns1919528:0crwdne1919528:0", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "crwdns1326178:0crwdne1326178:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "crwdns1160014:0crwdne1160014:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "crwdns1160016:0crwdne1160016:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "crwdns2158470:0crwdne2158470:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "crwdns1160018:0crwdne1160018:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "crwdns1160020:0crwdne1160020:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "crwdns1160022:0crwdne1160022:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "crwdns2158472:0crwdne2158472:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "crwdns1160028:0crwdne1160028:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "crwdns2158474:0crwdne2158474:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "crwdns1160030:0crwdne1160030:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "crwdns2158476:0{hereLink}crwdne2158476:0", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "crwdns2158478:0{hereLink}crwdne2158478:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "crwdns2158480:0crwdne2158480:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "crwdns1160036:0crwdne1160036:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "crwdns2158482:0crwdne2158482:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "crwdns1160040:0crwdne1160040:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "crwdns1160042:0crwdne1160042:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "crwdns2158484:0crwdne2158484:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "crwdns1160046:0crwdne1160046:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "crwdns1160048:0crwdne1160048:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "crwdns1160050:0crwdne1160050:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "crwdns2158486:0crwdne2158486:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "crwdns1160052:0crwdne1160052:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "crwdns1160054:0crwdne1160054:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "crwdns2245856:0crwdne2245856:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "crwdns1160056:0crwdne1160056:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "crwdns2158492:0crwdne2158492:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "crwdns1160060:0crwdne1160060:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "crwdns2158494:0crwdne2158494:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "crwdns2158496:0crwdne2158496:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "crwdns1160062:0crwdne1160062:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "crwdns2223478:0crwdne2223478:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "crwdns2158500:0crwdne2158500:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "crwdns2158502:0{numIdeas}crwdne2158502:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "crwdns2158504:0{numNotApproved}crwdne2158504:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "crwdns1160064:0crwdne1160064:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "crwdns1160066:0{importFile}crwdne1160066:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "crwdns4305586:0{importFile}crwdne4305586:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "crwdns1160068:0crwdne1160068:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "crwdns1160070:0crwdne1160070:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "crwdns1160072:0crwdne1160072:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "crwdns1160074:0crwdne1160074:0", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "crwdns1160076:0crwdne1160076:0", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "crwdns1160078:0crwdne1160078:0", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "crwdns1160080:0crwdne1160080:0", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "crwdns1160082:0crwdne1160082:0", "app.containers.Admin.projects.project.participation.comments": "crwdns2016768:0crwdne2016768:0", "app.containers.Admin.projects.project.participation.inputs": "crwdns2016770:0crwdne2016770:0", "app.containers.Admin.projects.project.participation.participantsTimeline": "crwdns2016772:0crwdne2016772:0", "app.containers.Admin.projects.project.participation.reactions": "crwdns2016774:0crwdne2016774:0", "app.containers.Admin.projects.project.participation.selectPeriod": "crwdns2016776:0crwdne2016776:0", "app.containers.Admin.projects.project.participation.usersByAge": "crwdns2016778:0crwdne2016778:0", "app.containers.Admin.projects.project.participation.usersByGender": "crwdns2016780:0crwdne2016780:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "crwdns2888755:0crwdne2888755:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "crwdns2888757:0crwdne2888757:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "crwdns3561759:0crwdne3561759:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "crwdns2888759:0{customFieldName}crwdne2888759:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "crwdns2888761:0crwdne2888761:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "crwdns4280906:0crwdne4280906:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "crwdns2888763:0crwdne2888763:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "crwdns2888765:0crwdne2888765:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "crwdns2888767:0crwdne2888767:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "crwdns2888769:0crwdne2888769:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "crwdns2888771:0crwdne2888771:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "crwdns2888773:0{verificationMethod}crwdne2888773:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "crwdns2888775:0crwdne2888775:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "crwdns2888777:0crwdne2888777:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "crwdns2888779:0crwdne2888779:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "crwdns2888781:0crwdne2888781:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "crwdns2888783:0crwdne2888783:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "crwdns2924699:0crwdne2924699:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "crwdns2948737:0{verificationMethod}crwdne2948737:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "crwdns2888787:0crwdne2888787:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "crwdns2888789:0crwdne2888789:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "crwdns2924701:0crwdne2924701:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "crwdns2924703:0{days}crwdne2924703:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "crwdns2924705:0crwdne2924705:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "crwdns2924707:0crwdne2924707:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "crwdns2924709:0crwdne2924709:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "crwdns2924711:0crwdne2924711:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "crwdns2888791:0crwdne2888791:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "crwdns2888793:0{verificationMethod}crwdne2888793:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "crwdns2888795:0crwdne2888795:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "crwdns2888797:0crwdne2888797:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "crwdns2888799:0crwdne2888799:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "crwdns2888801:0crwdne2888801:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "crwdns2888803:0crwdne2888803:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "crwdns2888805:0crwdne2888805:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "crwdns2888807:0crwdne2888807:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "crwdns4607209:0crwdne4607209:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "crwdns4607211:0crwdne4607211:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "crwdns4607213:0crwdne4607213:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "crwdns4607215:0crwdne4607215:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "crwdns2888809:0crwdne2888809:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "crwdns2888811:0crwdne2888811:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "crwdns2888813:0crwdne2888813:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "crwdns2888815:0crwdne2888815:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "crwdns2888817:0crwdne2888817:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "crwdns2888819:0crwdne2888819:0", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "crwdns2888821:0{verificationMethod}crwdne2888821:0", "app.containers.Admin.projects.project.survey.aiAnalysis2": "crwdns2876881:0crwdne2876881:0", "app.containers.Admin.projects.project.survey.allFiles": "crwdns1667590:0crwdne1667590:0", "app.containers.Admin.projects.project.survey.allResponses": "crwdns1529776:0crwdne1529776:0", "app.containers.Admin.projects.project.survey.analysis.accuracy": "crwdns1963154:0{accuracy}crwdnd1963154:0{percentage}crwdne1963154:0", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "crwdns2608328:0crwdne2608328:0", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "crwdns1963156:0crwdne1963156:0", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "crwdns1963158:0crwdne1963158:0", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "crwdns1963160:0crwdne1963160:0", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "crwdns1963162:0crwdne1963162:0", "app.containers.Admin.projects.project.survey.analysis.percentage": "crwdns1963164:0crwdne1963164:0", "app.containers.Admin.projects.project.survey.analysis.refresh": "crwdns1963166:0{count}crwdne1963166:0", "app.containers.Admin.projects.project.survey.analysis.regenerate": "crwdns2608330:0crwdne2608330:0", "app.containers.Admin.projects.project.survey.analysis.showInsights": "crwdns1963168:0crwdne1963168:0", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "crwdns1963170:0crwdne1963170:0", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "crwdns992737:0crwdne992737:0", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "crwdns992987:0{question}crwdne992987:0", "app.containers.Admin.projects.project.survey.cancel": "crwdns787397:0crwdne787397:0", "app.containers.Admin.projects.project.survey.consentModalButton": "crwdns904179:0crwdne904179:0", "app.containers.Admin.projects.project.survey.consentModalCancel": "crwdns904181:0crwdne904181:0", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "crwdns904183:0crwdne904183:0", "app.containers.Admin.projects.project.survey.consentModalText1": "crwdns904185:0crwdne904185:0", "app.containers.Admin.projects.project.survey.consentModalText2": "crwdns904187:0crwdne904187:0", "app.containers.Admin.projects.project.survey.consentModalText3": "crwdns904189:0crwdne904189:0", "app.containers.Admin.projects.project.survey.consentModalText4": "crwdns904191:0{link}crwdne904191:0", "app.containers.Admin.projects.project.survey.consentModalText4Link": "crwdns904193:0crwdne904193:0", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "crwdns904195:0crwdne904195:0", "app.containers.Admin.projects.project.survey.consentModalTitle": "crwdns904197:0crwdne904197:0", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "crwdns4305918:0crwdne4305918:0", "app.containers.Admin.projects.project.survey.deleteAnalysis": "crwdns787401:0crwdne787401:0", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "crwdns787403:0crwdne787403:0", "app.containers.Admin.projects.project.survey.explore": "crwdns1963172:0crwdne1963172:0", "app.containers.Admin.projects.project.survey.followUpResponses": "crwdns4305588:0crwdne4305588:0", "app.containers.Admin.projects.project.survey.formResults.averageRank": "crwdns4061571:0#{averageRank}crwdne4061571:0", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "crwdns2747329:0crwdne2747329:0", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "crwdns2747331:0crwdne2747331:0", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "crwdns4061573:0crwdne4061573:0", "app.containers.Admin.projects.project.survey.formResults.rank": "crwdns4061575:0#{rank}crwdne4061575:0", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "crwdns4156945:0{respondentCount}crwdnd4156945:0{respondentCount}crwdnd4156945:0{respondentCount}crwdne4156945:0", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "crwdns4061577:0crwdne4061577:0", "app.containers.Admin.projects.project.survey.formResults.xChoices": "crwdns4061579:0{numberChoices}crwdnd4061579:0{numberChoices}crwdnd4061579:0{numberChoices}crwdne4061579:0", "app.containers.Admin.projects.project.survey.heatMap": "crwdns1962918:0crwdne1962918:0", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "crwdns1962920:0crwdne1962920:0", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "crwdns1962922:0crwdne1962922:0", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "crwdns1962924:0{heatmapToggleEsriLinkText}crwdne1962924:0", "app.containers.Admin.projects.project.survey.heatmapView": "crwdns1962926:0crwdne1962926:0", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "crwdns4156689:0crwdne4156689:0", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "crwdns4156691:0{pageNumber}crwdnd4156691:0{numQuestionsSkipped}crwdne4156691:0", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "crwdns4156693:0{numQuestionsSkipped}crwdne4156693:0", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "crwdns4156695:0{pageNumber}crwdnd4156695:0{numQuestionsSkipped}crwdne4156695:0", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "crwdns4156697:0{numQuestionsSkipped}crwdne4156697:0", "app.containers.Admin.projects.project.survey.newAnalysis": "crwdns1110976:0crwdne1110976:0", "app.containers.Admin.projects.project.survey.nextInsight": "crwdns1110978:0crwdne1110978:0", "app.containers.Admin.projects.project.survey.openAnalysis": "crwdns1110980:0crwdne1110980:0", "app.containers.Admin.projects.project.survey.otherResponses": "crwdns1667592:0crwdne1667592:0", "app.containers.Admin.projects.project.survey.page": "crwdns4156699:0crwdne4156699:0", "app.containers.Admin.projects.project.survey.previousInsight": "crwdns1110982:0crwdne1110982:0", "app.containers.Admin.projects.project.survey.responses": "crwdns1962928:0crwdne1962928:0", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "crwdns4156701:0crwdne4156701:0", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "crwdns4156703:0crwdne4156703:0", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "crwdns4305590:0crwdne4305590:0", "app.containers.Admin.projects.project.survey.upsell.bullet1": "crwdns1963174:0crwdne1963174:0", "app.containers.Admin.projects.project.survey.upsell.bullet2": "crwdns1963176:0crwdne1963176:0", "app.containers.Admin.projects.project.survey.upsell.bullet3": "crwdns1963178:0crwdne1963178:0", "app.containers.Admin.projects.project.survey.upsell.bullet4": "crwdns1963180:0{link}crwdne1963180:0", "app.containers.Admin.projects.project.survey.upsell.button": "crwdns1963182:0crwdne1963182:0", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "crwdns1963184:0crwdne1963184:0", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "crwdns1963186:0crwdne1963186:0", "app.containers.Admin.projects.project.survey.upsell.title": "crwdns1963188:0crwdne1963188:0", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "crwdns1963190:0crwdne1963190:0", "app.containers.Admin.projects.project.survey.viewAnalysis": "crwdns787405:0crwdne787405:0", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "crwdns4305920:0crwdne4305920:0", "app.containers.Admin.projects.project.traffic.selectPeriod": "crwdns2016784:0crwdne2016784:0", "app.containers.Admin.projects.project.traffic.trafficSources": "crwdns2016786:0crwdne2016786:0", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "crwdns4657645:0crwdne4657645:0", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "crwdns2016788:0crwdne2016788:0", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "crwdns1845194:0crwdne1845194:0", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "crwdns1845196:0crwdne1845196:0", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "crwdns210382:0crwdne210382:0", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "crwdns210384:0crwdne210384:0", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "crwdns210386:0crwdne210386:0", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "crwdns210388:0crwdne210388:0", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "crwdns210390:0crwdne210390:0", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "crwdns210392:0crwdne210392:0", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "crwdns1919494:0crwdne1919494:0", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "crwdns2467794:0crwdne2467794:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "crwdns2311906:0{days}crwdne2311906:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "crwdns2311908:0crwdne2311908:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "crwdns2311910:0crwdne2311910:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "crwdns2311912:0crwdne2311912:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "crwdns2311914:0crwdne2311914:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "crwdns2467842:0crwdne2467842:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "crwdns2467844:0crwdne2467844:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "crwdns2467846:0crwdne2467846:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "crwdns2467848:0crwdne2467848:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "crwdns2467850:0crwdne2467850:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "crwdns2467852:0crwdne2467852:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "crwdns2467854:0crwdne2467854:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "crwdns2212576:0crwdne2212576:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "crwdns2984471:0crwdne2984471:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "crwdns2212578:0crwdne2212578:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "crwdns2212580:0crwdne2212580:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "crwdns2212582:0{numberOfUsers}crwdnd2212582:0{percentageOfUsers}crwdne2212582:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "crwdns2201674:0crwdne2201674:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "crwdns2467856:0crwdne2467856:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "crwdns3094681:0crwdne3094681:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "crwdns2467858:0crwdne2467858:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "crwdns2504046:0crwdne2504046:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "crwdns2467860:0crwdne2467860:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "crwdns3094683:0crwdne3094683:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "crwdns3094685:0crwdne3094685:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "crwdns3094687:0crwdne3094687:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "crwdns1963248:0crwdne1963248:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "crwdns4305592:0crwdne4305592:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "crwdns4305594:0crwdne4305594:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "crwdns4305596:0crwdne4305596:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "crwdns4305598:0crwdne4305598:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "crwdns4305600:0crwdne4305600:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "crwdns4305602:0crwdne4305602:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "crwdns4305604:0crwdne4305604:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "crwdns1603844:0crwdne1603844:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "crwdns1603846:0crwdne1603846:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "crwdns210394:0crwdne210394:0", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "crwdns1603848:0crwdne1603848:0", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "crwdns2467796:0crwdne2467796:0", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "crwdns1603850:0crwdne1603850:0", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "crwdns2467798:0{reportName}crwdne2467798:0", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "crwdns2467800:0crwdne2467800:0", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "crwdns210400:0crwdne210400:0", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "crwdns2467802:0crwdne2467802:0", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "crwdns210402:0crwdne210402:0", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "crwdns2467804:0{# days}crwdnd2467804:0{# day}crwdnd2467804:0{# days}crwdne2467804:0", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "crwdns210410:0crwdne210410:0", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "crwdns210412:0crwdne210412:0", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "crwdns4305606:0crwdne4305606:0", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "crwdns210418:0crwdne210418:0", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "crwdns2467866:0crwdne2467866:0", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "crwdns1845198:0crwdne1845198:0", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "crwdns210422:0crwdne210422:0", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "crwdns4368248:0crwdne4368248:0", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "crwdns1603854:0crwdne1603854:0", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "crwdns2467868:0crwdne2467868:0", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "crwdns210428:0crwdne210428:0", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "crwdns210430:0crwdne210430:0", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "crwdns4305608:0{quarterValue}crwdne4305608:0", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "crwdns210432:0crwdne210432:0", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "crwdns210434:0crwdne210434:0", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "crwdns4305610:0crwdne4305610:0", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "crwdns4305612:0crwdne4305612:0", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "crwdns210436:0crwdne210436:0", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "crwdns210438:0crwdne210438:0", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "crwdns210440:0crwdne210440:0", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "crwdns210442:0crwdne210442:0", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "crwdns210444:0crwdne210444:0", "app.containers.Admin.reporting.contactToAccess": "crwdns210446:0crwdne210446:0", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "crwdns2467806:0crwdne2467806:0", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "crwdns4305614:0crwdne4305614:0", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "crwdns4305616:0crwdne4305616:0", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "crwdns210448:0crwdne210448:0", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "crwdns210450:0crwdne210450:0", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "crwdns2467808:0crwdne2467808:0", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "crwdns2467810:0crwdne2467810:0", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "crwdns2467812:0crwdne2467812:0", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "crwdns2467814:0crwdne2467814:0", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "crwdns2467816:0crwdne2467816:0", "app.containers.Admin.reporting.deprecated": "crwdns1845200:0crwdne1845200:0", "app.containers.Admin.reporting.helmetDescription": "crwdns210454:0crwdne210454:0", "app.containers.Admin.reporting.helmetTitle": "crwdns210456:0crwdne210456:0", "app.containers.Admin.reporting.printPrepare": "crwdns210458:0crwdne210458:0", "app.containers.Admin.reporting.reportBuilder": "crwdns210460:0crwdne210460:0", "app.containers.Admin.reporting.reportHeader": "crwdns210462:0crwdne210462:0", "app.containers.Admin.reporting.warningBanner3": "crwdns1962954:0crwdne1962954:0", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "crwdns4747485:0crwdne4747485:0", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "crwdns2467870:0crwdne2467870:0", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "crwdns2467872:0crwdne2467872:0", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "crwdns2467874:0crwdne2467874:0", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "crwdns2467876:0crwdne2467876:0", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "crwdns2467878:0crwdne2467878:0", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "crwdns2467880:0crwdne2467880:0", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "crwdns2467882:0{days}crwdnd2467882:0{count}crwdne2467882:0", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "crwdns2677459:0crwdne2677459:0", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "crwdns2467884:0crwdne2467884:0", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "crwdns2467886:0crwdne2467886:0", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "crwdns2467888:0crwdne2467888:0", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "crwdns4632373:0crwdne4632373:0", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "crwdns4632375:0crwdne4632375:0", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "crwdns4632377:0crwdne4632377:0", "app.containers.Admin.surveyFormTab.downloads": "crwdns4607301:0crwdne4607301:0", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "crwdns4619849:0crwdne4619849:0", "app.containers.Admin.surveyFormTab.editSurveyForm": "crwdns4607291:0crwdne4607291:0", "app.containers.Admin.surveyFormTab.inputFormDescription": "crwdns4607293:0crwdne4607293:0", "app.containers.Admin.surveyFormTab.surveyForm": "crwdns4607295:0crwdne4607295:0", "app.containers.Admin.tools.apiTokens.createTokenButton": "crwdns749037:0crwdne749037:0", "app.containers.Admin.tools.apiTokens.createTokenCancel": "crwdns749039:0crwdne749039:0", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "crwdns1442742:0{secret}crwdne1442742:0", "app.containers.Admin.tools.apiTokens.createTokenDescription": "crwdns749041:0crwdne749041:0", "app.containers.Admin.tools.apiTokens.createTokenError": "crwdns749043:0crwdne749043:0", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "crwdns749045:0crwdne749045:0", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "crwdns1442744:0{secret}crwdne1442744:0", "app.containers.Admin.tools.apiTokens.createTokenName": "crwdns749047:0crwdne749047:0", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "crwdns749049:0crwdne749049:0", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "crwdns749051:0crwdne749051:0", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "crwdns749053:0crwdne749053:0", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "crwdns749055:0{secret}crwdne749055:0", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "crwdns749057:0crwdne749057:0", "app.containers.Admin.tools.apiTokens.createTokenTitle": "crwdns749061:0crwdne749061:0", "app.containers.Admin.tools.apiTokens.createdAt": "crwdns749063:0crwdne749063:0", "app.containers.Admin.tools.apiTokens.delete": "crwdns749065:0crwdne749065:0", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "crwdns749067:0crwdne749067:0", "app.containers.Admin.tools.apiTokens.description": "crwdns749069:0{link}crwdne749069:0", "app.containers.Admin.tools.apiTokens.lastUsedAt": "crwdns749071:0crwdne749071:0", "app.containers.Admin.tools.apiTokens.link": "crwdns749073:0crwdne749073:0", "app.containers.Admin.tools.apiTokens.linkUrl2": "crwdns1442746:0crwdne1442746:0", "app.containers.Admin.tools.apiTokens.name": "crwdns749077:0crwdne749077:0", "app.containers.Admin.tools.apiTokens.noTokens": "crwdns749079:0crwdne749079:0", "app.containers.Admin.tools.apiTokens.title": "crwdns749081:0crwdne749081:0", "app.containers.Admin.tools.esriDisabled": "crwdns1582650:0crwdne1582650:0", "app.containers.Admin.tools.esriIntegration2": "crwdns1582652:0crwdne1582652:0", "app.containers.Admin.tools.esriIntegrationButton": "crwdns1582654:0crwdne1582654:0", "app.containers.Admin.tools.esriIntegrationDescription3": "crwdns1919732:0crwdne1919732:0", "app.containers.Admin.tools.esriIntegrationImageAlt": "crwdns1582658:0crwdne1582658:0", "app.containers.Admin.tools.esriKeyInputDescription": "crwdns1582660:0crwdne1582660:0", "app.containers.Admin.tools.esriKeyInputLabel": "crwdns1582662:0crwdne1582662:0", "app.containers.Admin.tools.esriKeyInputPlaceholder": "crwdns1582664:0crwdne1582664:0", "app.containers.Admin.tools.esriMaps": "crwdns1582666:0crwdne1582666:0", "app.containers.Admin.tools.esriSaveButtonError": "crwdns1582668:0crwdne1582668:0", "app.containers.Admin.tools.esriSaveButtonSuccess": "crwdns1582670:0crwdne1582670:0", "app.containers.Admin.tools.esriSaveButtonText": "crwdns1582672:0crwdne1582672:0", "app.containers.Admin.tools.learnMore": "crwdns667355:0crwdne667355:0", "app.containers.Admin.tools.managePublicAPIKeys": "crwdns667357:0crwdne667357:0", "app.containers.Admin.tools.manageWidget": "crwdns667359:0crwdne667359:0", "app.containers.Admin.tools.manageWorkshops": "crwdns667361:0crwdne667361:0", "app.containers.Admin.tools.powerBIAPIImage": "crwdns1211236:0crwdne1211236:0", "app.containers.Admin.tools.powerBIDescription": "crwdns1211238:0crwdne1211238:0", "app.containers.Admin.tools.powerBIDisabled1": "crwdns3799143:0crwdne3799143:0", "app.containers.Admin.tools.powerBIDownloadTemplates": "crwdns1211242:0crwdne1211242:0", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "crwdns1211244:0crwdne1211244:0", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "crwdns1211246:0crwdne1211246:0", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "crwdns1211248:0crwdne1211248:0", "app.containers.Admin.tools.powerBITemplates.intro": "crwdns1211250:0{link}crwdne1211250:0", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "crwdns1211252:0crwdne1211252:0", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "crwdns1442748:0{baseUrl}crwdne1442748:0", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "crwdns1211256:0crwdne1211256:0", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "crwdns1211258:0crwdne1211258:0", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "crwdns1442750:0{link}crwdne1442750:0", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "crwdns1442752:0crwdne1442752:0", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "crwdns1442754:0crwdne1442754:0", "app.containers.Admin.tools.powerBITemplates.title": "crwdns1211260:0crwdne1211260:0", "app.containers.Admin.tools.powerBITitle": "crwdns1211262:0crwdne1211262:0", "app.containers.Admin.tools.publicAPIDescription": "crwdns667363:0crwdne667363:0", "app.containers.Admin.tools.publicAPIDisabled1": "crwdns3799145:0crwdne3799145:0", "app.containers.Admin.tools.publicAPIImage": "crwdns667365:0crwdne667365:0", "app.containers.Admin.tools.publicAPITitle": "crwdns667367:0crwdne667367:0", "app.containers.Admin.tools.toolsLabel": "crwdns667369:0crwdne667369:0", "app.containers.Admin.tools.widgetDescription": "crwdns667371:0crwdne667371:0", "app.containers.Admin.tools.widgetImage": "crwdns667373:0crwdne667373:0", "app.containers.Admin.tools.widgetTitle": "crwdns667375:0crwdne667375:0", "app.containers.Admin.tools.workshopsDescription": "crwdns667377:0crwdne667377:0", "app.containers.Admin.tools.workshopsImage": "crwdns667379:0crwdne667379:0", "app.containers.Admin.tools.workshopsSupportLink": "crwdns667381:0crwdne667381:0", "app.containers.Admin.tools.workshopsTitle": "crwdns667383:0crwdne667383:0", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "crwdns210468:0crwdne210468:0", "app.containers.AdminPage.DashboardPage._blank": "crwdns210470:0crwdne210470:0", "app.containers.AdminPage.DashboardPage.allGroups": "crwdns210476:0crwdne210476:0", "app.containers.AdminPage.DashboardPage.allProjects": "crwdns210478:0crwdne210478:0", "app.containers.AdminPage.DashboardPage.allTime": "crwdns210480:0crwdne210480:0", "app.containers.AdminPage.DashboardPage.comments": "crwdns210482:0crwdne210482:0", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "crwdns210484:0crwdne210484:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "crwdns210486:0crwdne210486:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "crwdns210488:0crwdne210488:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "crwdns210490:0{fieldName}crwdne210490:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "crwdns210492:0numberOfHiddenItems={numberOfHiddenItems}crwdnd210492:0tableViewLink={tableViewLink}crwdne210492:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "crwdns210494:0{requiredOrOptional}crwdne210494:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "crwdns210496:0{known}crwdnd210496:0{total}crwdnd210496:0{percentage}crwdne210496:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "crwdns210498:0{numberOfHiddenItems}crwdne210498:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "crwdns210500:0crwdne210500:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "crwdns210502:0crwdne210502:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "crwdns210504:0crwdne210504:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "crwdns210506:0{representativenessArticleLink}crwdne210506:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "crwdns210508:0crwdne210508:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "crwdns210510:0crwdne210510:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "crwdns210512:0crwdne210512:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "crwdns210514:0crwdne210514:0", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "crwdns210516:0crwdne210516:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "crwdns210518:0crwdne210518:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "crwdns210520:0{age}crwdne210520:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "crwdns210522:0crwdne210522:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "crwdns210524:0{upperBound}crwdne210524:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "crwdns210526:0{number}crwdne210526:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "crwdns210528:0crwdne210528:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "crwdns210530:0crwdne210530:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "crwdns210532:0crwdne210532:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "crwdns210534:0crwdne210534:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "crwdns210536:0crwdne210536:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "crwdns210538:0crwdne210538:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "crwdns210540:0crwdne210540:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "crwdns210542:0crwdne210542:0", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "crwdns210544:0crwdne210544:0", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "crwdns210546:0crwdne210546:0", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "crwdns210548:0crwdne210548:0", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "crwdns210550:0crwdne210550:0", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "crwdns210552:0{numberOfHiddenItems}crwdne210552:0", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "crwdns210554:0crwdne210554:0", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "crwdns210556:0crwdne210556:0", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "crwdns210558:0crwdne210558:0", "app.containers.AdminPage.DashboardPage.components.Field.complete": "crwdns210560:0crwdne210560:0", "app.containers.AdminPage.DashboardPage.components.Field.default": "crwdns210562:0crwdne210562:0", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "crwdns210564:0crwdne210564:0", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "crwdns210566:0crwdne210566:0", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "crwdns210568:0crwdne210568:0", "app.containers.AdminPage.DashboardPage.components.Field.options": "crwdns210570:0crwdne210570:0", "app.containers.AdminPage.DashboardPage.components.Field.save": "crwdns210572:0crwdne210572:0", "app.containers.AdminPage.DashboardPage.components.Field.saved": "crwdns210574:0crwdne210574:0", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "crwdns210576:0{setAgeGroupsLink}crwdne210576:0", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "crwdns210578:0crwdne210578:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "crwdns210580:0{days}crwdne210580:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "crwdns210582:0crwdne210582:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "crwdns210584:0crwdne210584:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "crwdns210586:0crwdne210586:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "crwdns210588:0crwdne210588:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "crwdns210590:0crwdne210590:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "crwdns210592:0crwdne210592:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "crwdns210594:0crwdne210594:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "crwdns210596:0crwdne210596:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "crwdns210598:0crwdne210598:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "crwdns210600:0crwdne210600:0", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "crwdns210602:0crwdne210602:0", "app.containers.AdminPage.DashboardPage.components.editBaseData": "crwdns210604:0crwdne210604:0", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "crwdns210606:0crwdne210606:0", "app.containers.AdminPage.DashboardPage.continuousType": "crwdns210608:0crwdne210608:0", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "crwdns210610:0crwdne210610:0", "app.containers.AdminPage.DashboardPage.customDateRange": "crwdns210612:0crwdne210612:0", "app.containers.AdminPage.DashboardPage.day": "crwdns210614:0crwdne210614:0", "app.containers.AdminPage.DashboardPage.false": "crwdns210616:0crwdne210616:0", "app.containers.AdminPage.DashboardPage.female": "crwdns210618:0crwdne210618:0", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "crwdns777223:0crwdne777223:0", "app.containers.AdminPage.DashboardPage.fromTo": "crwdns210622:0{from}crwdnd210622:0{to}crwdne210622:0", "app.containers.AdminPage.DashboardPage.helmetDescription": "crwdns210624:0crwdne210624:0", "app.containers.AdminPage.DashboardPage.helmetTitle": "crwdns210626:0crwdne210626:0", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "crwdns210628:0crwdne210628:0", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "crwdns210630:0crwdne210630:0", "app.containers.AdminPage.DashboardPage.inputs1": "crwdns1877116:0crwdne1877116:0", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "crwdns1877118:0crwdne1877118:0", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "crwdns210636:0crwdne210636:0", "app.containers.AdminPage.DashboardPage.male": "crwdns210640:0crwdne210640:0", "app.containers.AdminPage.DashboardPage.month": "crwdns210642:0crwdne210642:0", "app.containers.AdminPage.DashboardPage.noData": "crwdns210646:0crwdne210646:0", "app.containers.AdminPage.DashboardPage.noPhase": "crwdns210648:0crwdne210648:0", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "crwdns777227:0crwdne777227:0", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "crwdns777229:0crwdne777229:0", "app.containers.AdminPage.DashboardPage.numberOfLikes": "crwdns777231:0crwdne777231:0", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "crwdns777233:0crwdne777233:0", "app.containers.AdminPage.DashboardPage.overview.management": "crwdns210660:0crwdne210660:0", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "crwdns210662:0crwdne210662:0", "app.containers.AdminPage.DashboardPage.overview.showLess": "crwdns210664:0crwdne210664:0", "app.containers.AdminPage.DashboardPage.overview.showMore": "crwdns210666:0crwdne210666:0", "app.containers.AdminPage.DashboardPage.participants": "crwdns2070246:0crwdne2070246:0", "app.containers.AdminPage.DashboardPage.participationPerProject": "crwdns210668:0crwdne210668:0", "app.containers.AdminPage.DashboardPage.participationPerTopic": "crwdns210670:0crwdne210670:0", "app.containers.AdminPage.DashboardPage.perPeriod": "crwdns210672:0{period}crwdne210672:0", "app.containers.AdminPage.DashboardPage.previous30Days": "crwdns210674:0crwdne210674:0", "app.containers.AdminPage.DashboardPage.previous90Days": "crwdns210676:0crwdne210676:0", "app.containers.AdminPage.DashboardPage.previousWeek": "crwdns210678:0crwdne210678:0", "app.containers.AdminPage.DashboardPage.previousYear": "crwdns210680:0crwdne210680:0", "app.containers.AdminPage.DashboardPage.projectType": "crwdns210682:0{projectType}crwdne210682:0", "app.containers.AdminPage.DashboardPage.reactions": "crwdns777235:0crwdne777235:0", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "crwdns210686:0crwdne210686:0", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "crwdns210688:0crwdne210688:0", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "crwdns210690:0{representativenessArticleLink}crwdne210690:0", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "crwdns210692:0crwdne210692:0", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "crwdns210694:0crwdne210694:0", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "crwdns210696:0crwdne210696:0", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "crwdns210698:0crwdne210698:0", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "crwdns210700:0{userRegistrationLink}crwdne210700:0", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "crwdns210702:0crwdne210702:0", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "crwdns210704:0crwdne210704:0", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "crwdns210706:0crwdne210706:0", "app.containers.AdminPage.DashboardPage.resolutionday": "crwdns210708:0crwdne210708:0", "app.containers.AdminPage.DashboardPage.resolutionmonth": "crwdns210710:0crwdne210710:0", "app.containers.AdminPage.DashboardPage.resolutionweek": "crwdns210712:0crwdne210712:0", "app.containers.AdminPage.DashboardPage.selectProject": "crwdns210714:0crwdne210714:0", "app.containers.AdminPage.DashboardPage.selectedProject": "crwdns210716:0crwdne210716:0", "app.containers.AdminPage.DashboardPage.selectedTopic": "crwdns210718:0crwdne210718:0", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "crwdns210720:0crwdne210720:0", "app.containers.AdminPage.DashboardPage.tabOverview": "crwdns210722:0crwdne210722:0", "app.containers.AdminPage.DashboardPage.tabReports": "crwdns210724:0crwdne210724:0", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "crwdns210726:0crwdne210726:0", "app.containers.AdminPage.DashboardPage.tabUsers": "crwdns210728:0crwdne210728:0", "app.containers.AdminPage.DashboardPage.timelineType": "crwdns210730:0crwdne210730:0", "app.containers.AdminPage.DashboardPage.titleDashboard": "crwdns210732:0crwdne210732:0", "app.containers.AdminPage.DashboardPage.total": "crwdns210734:0crwdne210734:0", "app.containers.AdminPage.DashboardPage.totalForPeriod": "crwdns210736:0{period}crwdne210736:0", "app.containers.AdminPage.DashboardPage.true": "crwdns210738:0crwdne210738:0", "app.containers.AdminPage.DashboardPage.unspecified": "crwdns210740:0crwdne210740:0", "app.containers.AdminPage.DashboardPage.users": "crwdns210742:0crwdne210742:0", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "crwdns210744:0crwdne210744:0", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "crwdns210746:0crwdne210746:0", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "crwdns210748:0crwdne210748:0", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "crwdns210750:0crwdne210750:0", "app.containers.AdminPage.DashboardPage.week": "crwdns210754:0crwdne210754:0", "app.containers.AdminPage.FaviconPage.favicon": "crwdns210756:0crwdne210756:0", "app.containers.AdminPage.FaviconPage.faviconExplaination": "crwdns210758:0crwdne210758:0", "app.containers.AdminPage.FaviconPage.save": "crwdns210760:0crwdne210760:0", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "crwdns210762:0crwdne210762:0", "app.containers.AdminPage.FaviconPage.saveSuccess": "crwdns210764:0crwdne210764:0", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "crwdns210766:0crwdne210766:0", "app.containers.AdminPage.FolderPermissions.addFolderManager": "crwdns210768:0crwdne210768:0", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "crwdns210770:0crwdne210770:0", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "crwdns210772:0crwdne210772:0", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "crwdns210774:0{projectManagementInfoCenterLink}crwdne210774:0", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "crwdns210776:0crwdne210776:0", "app.containers.AdminPage.FolderPermissions.noMatch": "crwdns210778:0crwdne210778:0", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "crwdns210780:0crwdne210780:0", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "crwdns210782:0crwdne210782:0", "app.containers.AdminPage.FoldersEdit.addToFolder": "crwdns210784:0crwdne210784:0", "app.containers.AdminPage.FoldersEdit.archivedStatus": "crwdns210786:0crwdne210786:0", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "crwdns210788:0crwdne210788:0", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "crwdns210790:0crwdne210790:0", "app.containers.AdminPage.FoldersEdit.draftStatus": "crwdns210792:0crwdne210792:0", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "crwdns210794:0crwdne210794:0", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "crwdns210796:0crwdne210796:0", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "crwdns210798:0crwdne210798:0", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "crwdns210800:0crwdne210800:0", "app.containers.AdminPage.FoldersEdit.folderName": "crwdns210802:0crwdne210802:0", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "crwdns210804:0crwdne210804:0", "app.containers.AdminPage.FoldersEdit.multilocError": "crwdns210806:0crwdne210806:0", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "crwdns210808:0crwdne210808:0", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "crwdns210810:0crwdne210810:0", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "crwdns210812:0crwdne210812:0", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "crwdns210814:0crwdne210814:0", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "crwdns210816:0crwdne210816:0", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "crwdns210818:0crwdne210818:0", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "crwdns210820:0crwdne210820:0", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "crwdns210822:0crwdne210822:0", "app.containers.AdminPage.FoldersEdit.publishedStatus": "crwdns210824:0crwdne210824:0", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "crwdns210826:0crwdne210826:0", "app.containers.AdminPage.FoldersEdit.save": "crwdns210828:0crwdne210828:0", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "crwdns210830:0crwdne210830:0", "app.containers.AdminPage.FoldersEdit.saveSuccess": "crwdns210832:0crwdne210832:0", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "crwdns210834:0crwdne210834:0", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "crwdns210836:0crwdne210836:0", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "crwdns210838:0crwdne210838:0", "app.containers.AdminPage.FoldersEdit.statusLabel": "crwdns210840:0crwdne210840:0", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "crwdns210842:0crwdne210842:0", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "crwdns210844:0crwdne210844:0", "app.containers.AdminPage.FoldersEdit.textFieldsError": "crwdns1263168:0crwdne1263168:0", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "crwdns210846:0crwdne210846:0", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "crwdns210848:0crwdne210848:0", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "crwdns210850:0crwdne210850:0", "app.containers.AdminPage.FoldersEdit.url": "crwdns210852:0crwdne210852:0", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "crwdns210854:0crwdne210854:0", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "crwdns210856:0crwdne210856:0", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "crwdns210858:0crwdne210858:0", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "crwdns210860:0crwdne210860:0", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "crwdns4218449:0crwdne4218449:0", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "crwdns210862:0{navigationLink}crwdne210862:0", "app.containers.AdminPage.PagesEdition.policiesTitle": "crwdns210864:0crwdne210864:0", "app.containers.AdminPage.PagesEdition.privacy-policy": "crwdns210866:0crwdne210866:0", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "crwdns210868:0crwdne210868:0", "app.containers.AdminPage.Project.confirmation.description": "crwdns3501483:0crwdne3501483:0", "app.containers.AdminPage.Project.confirmation.no": "crwdns3501485:0crwdne3501485:0", "app.containers.AdminPage.Project.confirmation.title": "crwdns3501487:0crwdne3501487:0", "app.containers.AdminPage.Project.confirmation.yes": "crwdns3501489:0crwdne3501489:0", "app.containers.AdminPage.Project.data.descriptionText1": "crwdns4902971:0crwdne4902971:0", "app.containers.AdminPage.Project.data.title": "crwdns3501493:0crwdne3501493:0", "app.containers.AdminPage.Project.resetParticipationData": "crwdns3501497:0crwdne3501497:0", "app.containers.AdminPage.Project.settings.accessRights": "crwdns1442980:0crwdne1442980:0", "app.containers.AdminPage.Project.settings.back": "crwdns1442982:0crwdne1442982:0", "app.containers.AdminPage.Project.settings.data": "crwdns3501499:0crwdne3501499:0", "app.containers.AdminPage.Project.settings.description": "crwdns1442984:0crwdne1442984:0", "app.containers.AdminPage.Project.settings.events": "crwdns1442986:0crwdne1442986:0", "app.containers.AdminPage.Project.settings.general": "crwdns1442988:0crwdne1442988:0", "app.containers.AdminPage.Project.settings.projectTags": "crwdns1442990:0crwdne1442990:0", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "crwdns210870:0crwdne210870:0", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "crwdns210872:0crwdne210872:0", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "crwdns210874:0crwdne210874:0", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "crwdns210878:0crwdne210878:0", "app.containers.AdminPage.ProjectDashboard.published": "crwdns210880:0crwdne210880:0", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "crwdns210882:0crwdne210882:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "crwdns1252798:0crwdne1252798:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "crwdns1252800:0crwdne1252800:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "crwdns1252802:0crwdne1252802:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "crwdns1252804:0crwdne1252804:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "crwdns1252806:0crwdne1252806:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "crwdns1252808:0crwdne1252808:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "crwdns1252810:0crwdne1252810:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "crwdns1252812:0crwdne1252812:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "crwdns1252814:0crwdne1252814:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "crwdns1252816:0crwdne1252816:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "crwdns1252818:0crwdne1252818:0", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "crwdns1252820:0crwdne1252820:0", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "crwdns210908:0crwdne210908:0", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "crwdns210910:0crwdne210910:0", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "crwdns210912:0crwdne210912:0", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "crwdns210914:0crwdne210914:0", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "crwdns210916:0crwdne210916:0", "app.containers.AdminPage.ProjectDescription.errorMessage": "crwdns210918:0crwdne210918:0", "app.containers.AdminPage.ProjectDescription.preview": "crwdns210924:0crwdne210924:0", "app.containers.AdminPage.ProjectDescription.save": "crwdns210926:0crwdne210926:0", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "crwdns210928:0crwdne210928:0", "app.containers.AdminPage.ProjectDescription.saved": "crwdns210930:0crwdne210930:0", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "crwdns210932:0crwdne210932:0", "app.containers.AdminPage.ProjectDescription.titleDescription": "crwdns210934:0crwdne210934:0", "app.containers.AdminPage.ProjectDescription.whiteSpace": "crwdns210944:0crwdne210944:0", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "crwdns210946:0crwdne210946:0", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "crwdns210948:0crwdne210948:0", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "crwdns210950:0crwdne210950:0", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "crwdns210952:0crwdne210952:0", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "crwdns210954:0crwdne210954:0", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "crwdns210956:0crwdne210956:0", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "crwdns210958:0crwdne210958:0", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "crwdns210960:0crwdne210960:0", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "crwdns210962:0crwdne210962:0", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "crwdns210964:0crwdne210964:0", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "crwdns210966:0crwdne210966:0", "app.containers.AdminPage.ProjectEdit.MapTab.here": "crwdns210968:0crwdne210968:0", "app.containers.AdminPage.ProjectEdit.MapTab.import": "crwdns210970:0crwdne210970:0", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "crwdns210972:0crwdne210972:0", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "crwdns210974:0crwdne210974:0", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "crwdns210976:0crwdne210976:0", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "crwdns210978:0crwdne210978:0", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "crwdns210980:0{url}crwdne210980:0", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "crwdns210982:0crwdne210982:0", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "crwdns210984:0crwdne210984:0", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "crwdns210986:0crwdne210986:0", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "crwdns210988:0crwdne210988:0", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "crwdns210990:0crwdne210990:0", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "crwdns210992:0{supportArticle}crwdne210992:0", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "crwdns210994:0crwdne210994:0", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "crwdns210996:0crwdne210996:0", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "crwdns1962930:0{button}crwdne1962930:0", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "crwdns211000:0crwdne211000:0", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "crwdns211002:0crwdne211002:0", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "crwdns1442992:0crwdne1442992:0", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "crwdns211004:0crwdne211004:0", "app.containers.AdminPage.ProjectEdit.MapTab.save": "crwdns211006:0crwdne211006:0", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "crwdns1962932:0crwdne1962932:0", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "crwdns211008:0crwdne211008:0", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "crwdns1919734:0crwdne1919734:0", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "crwdns2948607:0crwdne2948607:0", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "crwdns211012:0crwdne211012:0", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "crwdns211014:0crwdne211014:0", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "crwdns604491:0crwdne604491:0", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "crwdns604493:0crwdne604493:0", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "crwdns604495:0crwdne604495:0", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "crwdns4280908:0crwdne4280908:0", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "crwdns4280910:0crwdne4280910:0", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "crwdns4280912:0crwdne4280912:0", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "crwdns3338583:0{link}crwdne3338583:0", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "crwdns3338585:0crwdne3338585:0", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "crwdns3338587:0crwdne3338587:0", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "crwdns3338589:0crwdne3338589:0", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "crwdns3338591:0crwdne3338591:0", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "crwdns3338593:0crwdne3338593:0", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "crwdns550097:0crwdne550097:0", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "crwdns211018:0crwdne211018:0", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "crwdns550099:0crwdne550099:0", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "crwdns211022:0crwdne211022:0", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "crwdns211024:0crwdne211024:0", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "crwdns211026:0crwdne211026:0", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "crwdns211028:0crwdne211028:0", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "crwdns550101:0crwdne550101:0", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "crwdns550103:0crwdne550103:0", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "crwdns550105:0crwdne550105:0", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "crwdns550107:0crwdne550107:0", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "crwdns211036:0crwdne211036:0", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "crwdns211038:0crwdne211038:0", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "crwdns211042:0crwdne211042:0", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "crwdns211044:0crwdne211044:0", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "crwdns211046:0crwdne211046:0", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "crwdns211048:0crwdne211048:0", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "crwdns211050:0crwdne211050:0", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "crwdns550109:0{questionTitle}crwdne550109:0", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "crwdns211054:0crwdne211054:0", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "crwdns211056:0crwdne211056:0", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "crwdns211058:0crwdne211058:0", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "crwdns211060:0crwdne211060:0", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "crwdns550111:0crwdne550111:0", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "crwdns211062:0crwdne211062:0", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "crwdns211064:0crwdne211064:0", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "crwdns211066:0crwdne211066:0", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "crwdns3645045:0crwdne3645045:0", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "crwdns211068:0crwdne211068:0", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "crwdns2735415:0crwdne2735415:0", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "crwdns211070:0crwdne211070:0", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "crwdns3338595:0crwdne3338595:0", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "crwdns3338597:0crwdne3338597:0", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "crwdns3338599:0crwdne3338599:0", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "crwdns211072:0crwdne211072:0", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "crwdns4607297:0crwdne4607297:0", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "crwdns211074:0crwdne211074:0", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "crwdns4607299:0crwdne4607299:0", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "crwdns211076:0crwdne211076:0", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "crwdns211078:0crwdne211078:0", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "crwdns211080:0crwdne211080:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "crwdns211082:0crwdne211082:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "crwdns211084:0crwdne211084:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "crwdns211086:0crwdne211086:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "crwdns211088:0crwdne211088:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "crwdns211090:0crwdne211090:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "crwdns211092:0crwdne211092:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "crwdns211094:0crwdne211094:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "crwdns211096:0crwdne211096:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "crwdns211098:0crwdne211098:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "crwdns211100:0crwdne211100:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "crwdns211102:0crwdne211102:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "crwdns211104:0crwdne211104:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "crwdns211106:0crwdne211106:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "crwdns211108:0crwdne211108:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "crwdns211110:0crwdne211110:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "crwdns211112:0crwdne211112:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "crwdns211114:0crwdne211114:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "crwdns211116:0crwdne211116:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "crwdns211118:0crwdne211118:0", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "crwdns211120:0x={x}crwdne211120:0", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "crwdns777237:0crwdne777237:0", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "crwdns777239:0crwdne777239:0", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "crwdns777241:0crwdne777241:0", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "crwdns777243:0crwdne777243:0", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "crwdns4916069:0crwdne4916069:0", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "crwdns777245:0crwdne777245:0", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "crwdns777247:0crwdne777247:0", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "crwdns777249:0crwdne777249:0", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "crwdns825977:0crwdne825977:0", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "crwdns777251:0crwdne777251:0", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "crwdns777253:0{voteTypeDescription}crwdnd777253:0{optionAnalysisArticleLink}crwdne777253:0", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "crwdns777255:0crwdne777255:0", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "crwdns777257:0crwdne777257:0", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "crwdns777259:0crwdne777259:0", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "crwdns777261:0crwdne777261:0", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "crwdns777263:0crwdne777263:0", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "crwdns777265:0crwdne777265:0", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "crwdns777267:0crwdne777267:0", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "crwdns777269:0crwdne777269:0", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "crwdns777271:0crwdne777271:0", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "crwdns4916071:0crwdne4916071:0", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "crwdns777273:0crwdne777273:0", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "crwdns777275:0crwdne777275:0", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "crwdns777277:0crwdne777277:0", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "crwdns4916073:0crwdne4916073:0", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "crwdns825979:0crwdne825979:0", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "crwdns777279:0crwdne777279:0", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "crwdns777281:0crwdne777281:0", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "crwdns777283:0crwdne777283:0", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "crwdns4916075:0crwdne4916075:0", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "crwdns777285:0crwdne777285:0", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "crwdns777287:0crwdne777287:0", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "crwdns777289:0crwdne777289:0", "app.containers.AdminPage.ProjectEdit.addNewInput": "crwdns211126:0crwdne211126:0", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "crwdns4431682:0crwdne4431682:0", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "crwdns211138:0crwdne211138:0", "app.containers.AdminPage.ProjectEdit.altText": "crwdns3561673:0crwdne3561673:0", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "crwdns211140:0crwdne211140:0", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "crwdns211142:0crwdne211142:0", "app.containers.AdminPage.ProjectEdit.approved": "crwdns3848633:0crwdne3848633:0", "app.containers.AdminPage.ProjectEdit.archived": "crwdns211144:0crwdne211144:0", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "crwdns3585457:0crwdne3585457:0", "app.containers.AdminPage.ProjectEdit.archivedStatus": "crwdns211146:0crwdne211146:0", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "crwdns211148:0crwdne211148:0", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "crwdns211150:0crwdne211150:0", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "crwdns211152:0crwdne211152:0", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "crwdns211154:0crwdne211154:0", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "crwdns211156:0{areasLabelTooltipLink}crwdne211156:0", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "crwdns211158:0crwdne211158:0", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "crwdns211160:0crwdne211160:0", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "crwdns211162:0crwdne211162:0", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "crwdns211164:0crwdne211164:0", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "crwdns211166:0crwdne211166:0", "app.containers.AdminPage.ProjectEdit.cardDisplay": "crwdns211168:0crwdne211168:0", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "crwdns3645019:0crwdne3645019:0", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "crwdns4305618:0crwdne4305618:0", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "crwdns4305620:0crwdne4305620:0", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "crwdns4305622:0crwdne4305622:0", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "crwdns4305624:0crwdne4305624:0", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "crwdns4305626:0crwdne4305626:0", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "crwdns4305628:0crwdne4305628:0", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "crwdns777291:0crwdne777291:0", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "crwdns777293:0crwdne777293:0", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "crwdns211178:0crwdne211178:0", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "crwdns211180:0crwdne211180:0", "app.containers.AdminPage.ProjectEdit.createInput": "crwdns4747487:0crwdne4747487:0", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "crwdns211182:0crwdne211182:0", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "crwdns211184:0crwdne211184:0", "app.containers.AdminPage.ProjectEdit.createPoll": "crwdns211186:0crwdne211186:0", "app.containers.AdminPage.ProjectEdit.createPollDescription": "crwdns211188:0crwdne211188:0", "app.containers.AdminPage.ProjectEdit.createProject": "crwdns2038162:0crwdne2038162:0", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "crwdns649373:0crwdne649373:0", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "crwdns211192:0crwdne211192:0", "app.containers.AdminPage.ProjectEdit.defaultSorting": "crwdns211194:0crwdne211194:0", "app.containers.AdminPage.ProjectEdit.departments": "crwdns211196:0crwdne211196:0", "app.containers.AdminPage.ProjectEdit.descriptionTab": "crwdns211198:0crwdne211198:0", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "crwdns3848717:0crwdne3848717:0", "app.containers.AdminPage.ProjectEdit.disabled": "crwdns211202:0crwdne211202:0", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "crwdns777297:0crwdne777297:0", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "crwdns777299:0crwdne777299:0", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "crwdns649375:0crwdne649375:0", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "crwdns649377:0crwdne649377:0", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "crwdns211204:0crwdne211204:0", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "crwdns211206:0crwdne211206:0", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "crwdns3585459:0crwdne3585459:0", "app.containers.AdminPage.ProjectEdit.draft": "crwdns211212:0crwdne211212:0", "app.containers.AdminPage.ProjectEdit.draftStatus": "crwdns211214:0crwdne211214:0", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "crwdns211216:0crwdne211216:0", "app.containers.AdminPage.ProjectEdit.enabled": "crwdns211218:0crwdne211218:0", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "crwdns777301:0crwdne777301:0", "app.containers.AdminPage.ProjectEdit.enalyzer": "crwdns211220:0crwdne211220:0", "app.containers.AdminPage.ProjectEdit.eventsTab": "crwdns211222:0crwdne211222:0", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "crwdns211224:0crwdne211224:0", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "crwdns211226:0crwdne211226:0", "app.containers.AdminPage.ProjectEdit.filesTab": "crwdns4760305:0crwdne4760305:0", "app.containers.AdminPage.ProjectEdit.findVolunteers": "crwdns211228:0crwdne211228:0", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "crwdns211230:0crwdne211230:0", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "crwdns4431684:0crwdne4431684:0", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "crwdns3561675:0crwdne3561675:0", "app.containers.AdminPage.ProjectEdit.folderSelectError": "crwdns211234:0crwdne211234:0", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "crwdns211236:0crwdne211236:0", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "crwdns211238:0crwdne211238:0", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "crwdns211240:0crwdne211240:0", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "crwdns211242:0crwdne211242:0", "app.containers.AdminPage.ProjectEdit.fromATemplate": "crwdns211244:0crwdne211244:0", "app.containers.AdminPage.ProjectEdit.generalTab": "crwdns211248:0crwdne211248:0", "app.containers.AdminPage.ProjectEdit.google_forms": "crwdns211252:0crwdne211252:0", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "crwdns3561677:0crwdne3561677:0", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "crwdns211254:0crwdne211254:0", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "crwdns211256:0crwdne211256:0", "app.containers.AdminPage.ProjectEdit.information.new1": "crwdns1962710:0crwdne1962710:0", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "crwdns1326194:0crwdne1326194:0", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "crwdns1326196:0crwdne1326196:0", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "crwdns211258:0crwdne211258:0", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "crwdns777303:0crwdne777303:0", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "crwdns211262:0crwdne211262:0", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "crwdns211264:0{ideaManagerLink}crwdne211264:0", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "crwdns211266:0crwdne211266:0", "app.containers.AdminPage.ProjectEdit.inputFormTab": "crwdns211268:0crwdne211268:0", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "crwdns211270:0crwdne211270:0", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "crwdns211272:0crwdne211272:0", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "crwdns211274:0crwdne211274:0", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "crwdns777305:0crwdne777305:0", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "crwdns211278:0crwdne211278:0", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "crwdns211280:0crwdne211280:0", "app.containers.AdminPage.ProjectEdit.inspirationHub": "crwdns4268282:0crwdne4268282:0", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "crwdns649379:0crwdne649379:0", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "crwdns777307:0crwdne777307:0", "app.containers.AdminPage.ProjectEdit.limited": "crwdns211282:0crwdne211282:0", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "crwdns211284:0crwdne211284:0", "app.containers.AdminPage.ProjectEdit.mapDisplay": "crwdns211286:0crwdne211286:0", "app.containers.AdminPage.ProjectEdit.mapTab": "crwdns211288:0crwdne211288:0", "app.containers.AdminPage.ProjectEdit.maxDislikes": "crwdns777309:0crwdne777309:0", "app.containers.AdminPage.ProjectEdit.maxLikes": "crwdns777311:0crwdne777311:0", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "crwdns777313:0crwdne777313:0", "app.containers.AdminPage.ProjectEdit.maximum": "crwdns211294:0crwdne211294:0", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "crwdns211296:0crwdne211296:0", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "crwdns211298:0crwdne211298:0", "app.containers.AdminPage.ProjectEdit.minimum": "crwdns211300:0crwdne211300:0", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "crwdns211302:0crwdne211302:0", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "crwdns211304:0crwdne211304:0", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "crwdns2400472:0crwdne2400472:0", "app.containers.AdminPage.ProjectEdit.moreDetails": "crwdns211310:0crwdne211310:0", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "crwdns211312:0crwdne211312:0", "app.containers.AdminPage.ProjectEdit.needInspiration": "crwdns4268284:0{inspirationHubLink}crwdne4268284:0", "app.containers.AdminPage.ProjectEdit.newContribution": "crwdns1442994:0crwdne1442994:0", "app.containers.AdminPage.ProjectEdit.newIdea": "crwdns1442996:0crwdne1442996:0", "app.containers.AdminPage.ProjectEdit.newInitiative": "crwdns3324825:0crwdne3324825:0", "app.containers.AdminPage.ProjectEdit.newIssue": "crwdns1442998:0crwdne1442998:0", "app.containers.AdminPage.ProjectEdit.newOption": "crwdns1443000:0crwdne1443000:0", "app.containers.AdminPage.ProjectEdit.newPetition": "crwdns3324827:0crwdne3324827:0", "app.containers.AdminPage.ProjectEdit.newProject": "crwdns211314:0crwdne211314:0", "app.containers.AdminPage.ProjectEdit.newProposal": "crwdns3324829:0crwdne3324829:0", "app.containers.AdminPage.ProjectEdit.newQuestion": "crwdns1443002:0crwdne1443002:0", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "crwdns211316:0crwdne211316:0", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "crwdns211318:0crwdne211318:0", "app.containers.AdminPage.ProjectEdit.noFolder": "crwdns211320:0crwdne211320:0", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "crwdns4431686:0crwdne4431686:0", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "crwdns211322:0crwdne211322:0", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "crwdns211324:0crwdne211324:0", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "crwdns211326:0crwdne211326:0", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "crwdns211328:0crwdne211328:0", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "crwdns211330:0crwdne211330:0", "app.containers.AdminPage.ProjectEdit.optionNo": "crwdns211332:0crwdne211332:0", "app.containers.AdminPage.ProjectEdit.optionYes": "crwdns211334:0crwdne211334:0", "app.containers.AdminPage.ProjectEdit.participationLevels": "crwdns211338:0crwdne211338:0", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "crwdns211340:0crwdne211340:0", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "crwdns211342:0crwdne211342:0", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "crwdns523115:0crwdne523115:0", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "crwdns523117:0crwdne523117:0", "app.containers.AdminPage.ProjectEdit.pendingReview": "crwdns3848635:0crwdne3848635:0", "app.containers.AdminPage.ProjectEdit.permissionsTab": "crwdns211344:0crwdne211344:0", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "crwdns1443004:0crwdne1443004:0", "app.containers.AdminPage.ProjectEdit.phaseEmails": "crwdns5050107:0crwdne5050107:0", "app.containers.AdminPage.ProjectEdit.pollTab": "crwdns211354:0crwdne211354:0", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "crwdns777315:0crwdne777315:0", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "crwdns211358:0crwdne211358:0", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "crwdns211360:0{supportPageLink}crwdne211360:0", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "crwdns4431688:0crwdne4431688:0", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "crwdns211364:0{supportPageLink}crwdne211364:0", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "crwdns3561679:0crwdne3561679:0", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "crwdns3561681:0crwdne3561681:0", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "crwdns523119:0crwdne523119:0", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "crwdns211366:0{moderationInfoCenterLink}crwdne211366:0", "app.containers.AdminPage.ProjectEdit.projectName": "crwdns211370:0crwdne211370:0", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "crwdns211372:0crwdne211372:0", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "crwdns211374:0crwdne211374:0", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "crwdns211376:0crwdne211376:0", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "crwdns523121:0crwdne523121:0", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "crwdns523123:0crwdne523123:0", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "crwdns3477975:0crwdne3477975:0", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "crwdns3585461:0crwdne3585461:0", "app.containers.AdminPage.ProjectEdit.publishedStatus": "crwdns211378:0crwdne211378:0", "app.containers.AdminPage.ProjectEdit.purposes": "crwdns211380:0crwdne211380:0", "app.containers.AdminPage.ProjectEdit.qualtrics": "crwdns211382:0crwdne211382:0", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "crwdns211384:0crwdne211384:0", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "crwdns3501501:0crwdne3501501:0", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "crwdns211386:0crwdne211386:0", "app.containers.AdminPage.ProjectEdit.saveProject": "crwdns211388:0crwdne211388:0", "app.containers.AdminPage.ProjectEdit.saveSuccess": "crwdns211390:0crwdne211390:0", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "crwdns211392:0crwdne211392:0", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "crwdns211394:0crwdne211394:0", "app.containers.AdminPage.ProjectEdit.selectGroups": "crwdns211396:0crwdne211396:0", "app.containers.AdminPage.ProjectEdit.setup": "crwdns1443006:0crwdne1443006:0", "app.containers.AdminPage.ProjectEdit.shareInformation": "crwdns211398:0crwdne211398:0", "app.containers.AdminPage.ProjectEdit.smart_survey": "crwdns211402:0crwdne211402:0", "app.containers.AdminPage.ProjectEdit.snap_survey": "crwdns211404:0crwdne211404:0", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "crwdns211408:0crwdne211408:0", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "crwdns211410:0crwdne211410:0", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "crwdns211412:0crwdne211412:0", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "crwdns211416:0crwdne211416:0", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "crwdns211418:0{percentage}crwdnd211418:0{# choices}crwdnd211418:0{# choice}crwdnd211418:0{# choices}crwdne211418:0", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "crwdns667339:0crwdne667339:0", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "crwdns4305630:0crwdne4305630:0", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "crwdns1159958:0crwdne1159958:0", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "crwdns1667594:0crwdne1667594:0", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "crwdns1919516:0crwdne1919516:0", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "crwdns2158512:0crwdne2158512:0", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "crwdns4305632:0crwdne4305632:0", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "crwdns4305634:0crwdne4305634:0", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "crwdns211442:0crwdne211442:0", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "crwdns4132883:0crwdne4132883:0", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "crwdns992743:0crwdne992743:0", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "crwdns211444:0crwdne211444:0", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "crwdns1667596:0crwdne1667596:0", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "crwdns4619851:0crwdne4619851:0", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "crwdns211446:0crwdne211446:0", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "crwdns211448:0crwdne211448:0", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "crwdns4305636:0crwdne4305636:0", "app.containers.AdminPage.ProjectEdit.survey.optional2": "crwdns211450:0crwdne211450:0", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "crwdns4156687:0{supportPageLink}crwdne4156687:0", "app.containers.AdminPage.ProjectEdit.survey.point": "crwdns1962934:0crwdne1962934:0", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "crwdns211456:0crwdne211456:0", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "crwdns667341:0crwdne667341:0", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "crwdns4061581:0crwdne4061581:0", "app.containers.AdminPage.ProjectEdit.survey.rating": "crwdns4156869:0crwdne4156869:0", "app.containers.AdminPage.ProjectEdit.survey.required2": "crwdns211460:0crwdne211460:0", "app.containers.AdminPage.ProjectEdit.survey.response": "crwdns4156871:0{responses}crwdnd4156871:0{response}crwdnd4156871:0{responses}crwdne4156871:0", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "crwdns4156873:0{# responses}crwdnd4156873:0{# response}crwdnd4156873:0{# responses}crwdne4156873:0", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "crwdns211462:0crwdne211462:0", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "crwdns4305638:0crwdne4305638:0", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "crwdns2747333:0crwdne2747333:0", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "crwdns211464:0crwdne211464:0", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "crwdns211466:0crwdne211466:0", "app.containers.AdminPage.ProjectEdit.survey.survey2": "crwdns211468:0crwdne211468:0", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "crwdns1443014:0crwdne1443014:0", "app.containers.AdminPage.ProjectEdit.survey.text2": "crwdns992745:0crwdne992745:0", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "crwdns211474:0{count}crwdne211474:0", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "crwdns211476:0crwdne211476:0", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "crwdns4305640:0crwdne4305640:0", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "crwdns211482:0crwdne211482:0", "app.containers.AdminPage.ProjectEdit.surveyService": "crwdns211484:0crwdne211484:0", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "crwdns211486:0{surveyServiceTooltipLink}crwdne211486:0", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "crwdns1709184:0crwdne1709184:0", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "crwdns211490:0crwdne211490:0", "app.containers.AdminPage.ProjectEdit.survey_monkey": "crwdns211492:0crwdne211492:0", "app.containers.AdminPage.ProjectEdit.survey_xact": "crwdns211494:0crwdne211494:0", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "crwdns211496:0crwdne211496:0", "app.containers.AdminPage.ProjectEdit.titleGeneral": "crwdns211500:0crwdne211500:0", "app.containers.AdminPage.ProjectEdit.titleLabel": "crwdns211502:0crwdne211502:0", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "crwdns211504:0crwdne211504:0", "app.containers.AdminPage.ProjectEdit.topicLabel": "crwdns211506:0crwdne211506:0", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "crwdns211508:0{topicsCopy}crwdne211508:0", "app.containers.AdminPage.ProjectEdit.totalBudget": "crwdns211510:0crwdne211510:0", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "crwdns211514:0crwdne211514:0", "app.containers.AdminPage.ProjectEdit.typeform": "crwdns211516:0crwdne211516:0", "app.containers.AdminPage.ProjectEdit.unassigned": "crwdns211518:0crwdne211518:0", "app.containers.AdminPage.ProjectEdit.unlimited": "crwdns211520:0crwdne211520:0", "app.containers.AdminPage.ProjectEdit.url": "crwdns211524:0crwdne211524:0", "app.containers.AdminPage.ProjectEdit.useTemplate": "crwdns211526:0crwdne211526:0", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "crwdns211528:0crwdne211528:0", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "crwdns211530:0crwdne211530:0", "app.containers.AdminPage.ProjectEdit.voteTermError": "crwdns777317:0crwdne777317:0", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "crwdns211532:0{# groups can view}crwdnd211532:0{# group can view}crwdnd211532:0{# groups can view}crwdne211532:0", "app.containers.AdminPage.ProjectEvents.addEventButton": "crwdns211534:0crwdne211534:0", "app.containers.AdminPage.ProjectEvents.additionalInformation": "crwdns953595:0crwdne953595:0", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "crwdns953597:0crwdne953597:0", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "crwdns953599:0crwdne953599:0", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "crwdns953601:0crwdne953601:0", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "crwdns953603:0crwdne953603:0", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "crwdns953605:0crwdne953605:0", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "crwdns1081510:0crwdne1081510:0", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "crwdns1081512:0crwdne1081512:0", "app.containers.AdminPage.ProjectEvents.customButtonLink": "crwdns1081514:0crwdne1081514:0", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "crwdns1081516:0crwdne1081516:0", "app.containers.AdminPage.ProjectEvents.customButtonText": "crwdns1081518:0crwdne1081518:0", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "crwdns4902973:0crwdne4902973:0", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "crwdns211536:0crwdne211536:0", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "crwdns211538:0crwdne211538:0", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "crwdns211540:0crwdne211540:0", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "crwdns211542:0crwdne211542:0", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "crwdns211544:0crwdne211544:0", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "crwdns211546:0crwdne211546:0", "app.containers.AdminPage.ProjectEvents.editEventTitle": "crwdns211548:0crwdne211548:0", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "crwdns4902975:0{userTabLink}crwdnd4902975:0{supportArticleLink}crwdne4902975:0", "app.containers.AdminPage.ProjectEvents.eventDates": "crwdns953607:0crwdne953607:0", "app.containers.AdminPage.ProjectEvents.eventImage": "crwdns1081524:0crwdne1081524:0", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "crwdns3561683:0crwdne3561683:0", "app.containers.AdminPage.ProjectEvents.eventLocation": "crwdns953609:0crwdne953609:0", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "crwdns4902977:0crwdne4902977:0", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "crwdns211550:0crwdne211550:0", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "crwdns211552:0crwdne211552:0", "app.containers.AdminPage.ProjectEvents.locationLabel": "crwdns211554:0crwdne211554:0", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "crwdns4902979:0crwdne4902979:0", "app.containers.AdminPage.ProjectEvents.newEventTitle": "crwdns211556:0crwdne211556:0", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "crwdns1032379:0crwdne1032379:0", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "crwdns1032381:0crwdne1032381:0", "app.containers.AdminPage.ProjectEvents.preview": "crwdns1081526:0crwdne1081526:0", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "crwdns953613:0crwdne953613:0", "app.containers.AdminPage.ProjectEvents.refineOnMap": "crwdns953615:0crwdne953615:0", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "crwdns1635632:0crwdne1635632:0", "app.containers.AdminPage.ProjectEvents.register": "crwdns4902981:0crwdne4902981:0", "app.containers.AdminPage.ProjectEvents.registerButton": "crwdns4902983:0crwdne4902983:0", "app.containers.AdminPage.ProjectEvents.registrant": "crwdns4902985:0crwdne4902985:0", "app.containers.AdminPage.ProjectEvents.registrants": "crwdns4902987:0crwdne4902987:0", "app.containers.AdminPage.ProjectEvents.registrationLimit": "crwdns4902989:0crwdne4902989:0", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "crwdns211558:0crwdne211558:0", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "crwdns211560:0crwdne211560:0", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "crwdns211562:0crwdne211562:0", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "crwdns211564:0crwdne211564:0", "app.containers.AdminPage.ProjectEvents.searchForLocation": "crwdns953617:0crwdne953617:0", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "crwdns211566:0crwdne211566:0", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "crwdns211568:0crwdne211568:0", "app.containers.AdminPage.ProjectEvents.titleEvents": "crwdns211570:0crwdne211570:0", "app.containers.AdminPage.ProjectEvents.titleLabel": "crwdns211572:0crwdne211572:0", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "crwdns4902991:0crwdne4902991:0", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "crwdns4902993:0crwdne4902993:0", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "crwdns4902995:0crwdne4902995:0", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "crwdns4902997:0crwdne4902997:0", "app.containers.AdminPage.ProjectEvents.usersTabLink": "crwdns1081532:0crwdne1081532:0", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "crwdns1081534:0crwdne1081534:0", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "crwdns4863651:0crwdne4863651:0", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "crwdns4863653:0crwdne4863653:0", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "crwdns4863655:0crwdne4863655:0", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "crwdns4863657:0crwdne4863657:0", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "crwdns4863659:0crwdne4863659:0", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "crwdns4863661:0crwdne4863661:0", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "crwdns4863663:0crwdne4863663:0", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "crwdns4863665:0crwdne4863665:0", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "crwdns4863667:0crwdne4863667:0", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "crwdns4863669:0crwdne4863669:0", "app.containers.AdminPage.ProjectFiles.addFiles": "crwdns4902665:0crwdne4902665:0", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "crwdns4982853:0crwdne4982853:0", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "crwdns4982855:0crwdne4982855:0", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "crwdns4982857:0crwdne4982857:0", "app.containers.AdminPage.ProjectFiles.askButton": "crwdns4916077:0crwdne4916077:0", "app.containers.AdminPage.ProjectFiles.categoryLabel": "crwdns4916079:0crwdne4916079:0", "app.containers.AdminPage.ProjectFiles.chooseFiles": "crwdns4825005:0crwdne4825005:0", "app.containers.AdminPage.ProjectFiles.close": "crwdns4902667:0crwdne4902667:0", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "crwdns4902669:0crwdne4902669:0", "app.containers.AdminPage.ProjectFiles.confirmDelete": "crwdns4863671:0crwdne4863671:0", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "crwdns4982859:0crwdne4982859:0", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "crwdns4982861:0crwdne4982861:0", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "crwdns4982863:0crwdne4982863:0", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "crwdns4982865:0crwdne4982865:0", "app.containers.AdminPage.ProjectFiles.deleteFile2": "crwdns4760315:0crwdne4760315:0", "app.containers.AdminPage.ProjectFiles.description": "crwdns4996313:0crwdne4996313:0", "app.containers.AdminPage.ProjectFiles.done": "crwdns4902671:0crwdne4902671:0", "app.containers.AdminPage.ProjectFiles.downloadFile": "crwdns4863673:0crwdne4863673:0", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "crwdns4982867:0crwdne4982867:0", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "crwdns4863675:0crwdne4863675:0", "app.containers.AdminPage.ProjectFiles.editFile": "crwdns4996315:0crwdne4996315:0", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "crwdns4916081:0crwdne4916081:0", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "crwdns4916083:0crwdne4916083:0", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "crwdns4916085:0crwdne4916085:0", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "crwdns4916087:0crwdne4916087:0", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "crwdns4982869:0crwdne4982869:0", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "crwdns4996317:0crwdne4996317:0", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "crwdns4902673:0crwdne4902673:0", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "crwdns4863683:0crwdne4863683:0", "app.containers.AdminPage.ProjectFiles.generatingPreview": "crwdns5049469:0crwdne5049469:0", "app.containers.AdminPage.ProjectFiles.info_sheet": "crwdns4982871:0crwdne4982871:0", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "crwdns4982873:0crwdne4982873:0", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "crwdns4982875:0crwdne4982875:0", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "crwdns4982877:0crwdne4982877:0", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "crwdns4982879:0crwdne4982879:0", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "crwdns4982881:0crwdne4982881:0", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "crwdns4982883:0crwdne4982883:0", "app.containers.AdminPage.ProjectFiles.interview": "crwdns4982885:0crwdne4982885:0", "app.containers.AdminPage.ProjectFiles.maxFilesError": "crwdns4863685:0{maxFiles}crwdne4863685:0", "app.containers.AdminPage.ProjectFiles.meeting": "crwdns4982887:0crwdne4982887:0", "app.containers.AdminPage.ProjectFiles.noFilesFound": "crwdns4902661:0crwdne4902661:0", "app.containers.AdminPage.ProjectFiles.other": "crwdns4982889:0crwdne4982889:0", "app.containers.AdminPage.ProjectFiles.policy": "crwdns4982891:0crwdne4982891:0", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "crwdns4982893:0crwdne4982893:0", "app.containers.AdminPage.ProjectFiles.report": "crwdns4982895:0crwdne4982895:0", "app.containers.AdminPage.ProjectFiles.retryUpload": "crwdns4982897:0crwdne4982897:0", "app.containers.AdminPage.ProjectFiles.save": "crwdns4916091:0crwdne4916091:0", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "crwdns4916093:0crwdne4916093:0", "app.containers.AdminPage.ProjectFiles.searchFiles": "crwdns4760309:0crwdne4760309:0", "app.containers.AdminPage.ProjectFiles.selectFileType": "crwdns4902675:0crwdne4902675:0", "app.containers.AdminPage.ProjectFiles.strategic_plan": "crwdns4982899:0crwdne4982899:0", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "crwdns4902677:0{maxFiles}crwdne4902677:0", "app.containers.AdminPage.ProjectFiles.unknown": "crwdns4863687:0crwdne4863687:0", "app.containers.AdminPage.ProjectFiles.upload": "crwdns4902679:0crwdne4902679:0", "app.containers.AdminPage.ProjectFiles.uploadSummary": "crwdns4982901:0numberOfFiles={numberOfFiles}crwdnd4982901:0numberOfErrors={numberOfErrors}crwdne4982901:0", "app.containers.AdminPage.ProjectFiles.viewFile": "crwdns4863689:0crwdne4863689:0", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "crwdns211574:0crwdne211574:0", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "crwdns211576:0crwdne211576:0", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "crwdns211578:0crwdne211578:0", "app.containers.AdminPage.ProjectIdeaForm.enabled": "crwdns211580:0crwdne211580:0", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "crwdns211582:0crwdne211582:0", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "crwdns211584:0crwdne211584:0", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "crwdns211586:0crwdne211586:0", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "crwdns211588:0crwdne211588:0", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "crwdns211590:0crwdne211590:0", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "crwdns211592:0crwdne211592:0", "app.containers.AdminPage.ProjectIdeaForm.required": "crwdns211594:0crwdne211594:0", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "crwdns211596:0crwdne211596:0", "app.containers.AdminPage.ProjectIdeaForm.save": "crwdns211598:0crwdne211598:0", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "crwdns211600:0crwdne211600:0", "app.containers.AdminPage.ProjectIdeaForm.saved": "crwdns211602:0crwdne211602:0", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "crwdns211604:0crwdne211604:0", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "crwdns777037:0crwdne777037:0", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "crwdns777039:0crwdne777039:0", "app.containers.AdminPage.ProjectTimeline.datesLabel": "crwdns211610:0crwdne211610:0", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "crwdns1930456:0crwdne1930456:0", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "crwdns1930458:0crwdne1930458:0", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "crwdns211616:0crwdne211616:0", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "crwdns211622:0crwdne211622:0", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "crwdns211626:0crwdne211626:0", "app.containers.AdminPage.ProjectTimeline.endDate": "crwdns1294618:0crwdne1294618:0", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "crwdns211628:0crwdne211628:0", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "crwdns211630:0crwdne211630:0", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "crwdns211634:0crwdne211634:0", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "crwdns1294622:0crwdne1294622:0", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "crwdns1294624:0crwdne1294624:0", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "crwdns1294626:0crwdne1294626:0", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "crwdns1294628:0crwdne1294628:0", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "crwdns1963130:0crwdne1963130:0", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "crwdns1443016:0crwdne1443016:0", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "crwdns211638:0crwdne211638:0", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "crwdns211642:0crwdne211642:0", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "crwdns211644:0crwdne211644:0", "app.containers.AdminPage.ProjectTimeline.startDate": "crwdns1294630:0crwdne1294630:0", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "crwdns211646:0crwdne211646:0", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "crwdns1930460:0crwdne1930460:0", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "crwdns1930462:0crwdne1930462:0", "app.containers.AdminPage.ProjectTimeline.titleLabel": "crwdns211650:0crwdne211650:0", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "crwdns777043:0crwdne777043:0", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "crwdns211654:0{fieldName}crwdne211654:0", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "crwdns211656:0crwdne211656:0", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "crwdns211658:0crwdne211658:0", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "crwdns211660:0{topicManagerLink}crwdne211660:0", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "crwdns211662:0crwdne211662:0", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "crwdns211664:0crwdne211664:0", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "crwdns211666:0crwdne211666:0", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "crwdns211668:0crwdne211668:0", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "crwdns211670:0crwdne211670:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "crwdns211672:0crwdne211672:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "crwdns211674:0crwdne211674:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "crwdns211676:0crwdne211676:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "crwdns211678:0crwdne211678:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "crwdns211680:0crwdne211680:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "crwdns211682:0crwdne211682:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "crwdns211684:0crwdne211684:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "crwdns211686:0crwdne211686:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "crwdns211688:0crwdne211688:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "crwdns211690:0crwdne211690:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "crwdns211696:0crwdne211696:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "crwdns211698:0crwdne211698:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "crwdns211700:0crwdne211700:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "crwdns211702:0crwdne211702:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "crwdns211704:0crwdne211704:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "crwdns211706:0{geographicAreasTabLink}crwdne211706:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "crwdns211708:0crwdne211708:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "crwdns211710:0crwdne211710:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "crwdns211712:0crwdne211712:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "crwdns211714:0crwdne211714:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "crwdns211716:0crwdne211716:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "crwdns211718:0crwdne211718:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "crwdns211720:0crwdne211720:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "crwdns211722:0crwdne211722:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "crwdns211724:0crwdne211724:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "crwdns211726:0crwdne211726:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "crwdns211728:0crwdne211728:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "crwdns211730:0crwdne211730:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "crwdns211732:0crwdne211732:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "crwdns211734:0crwdne211734:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "crwdns211736:0crwdne211736:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "crwdns211738:0crwdne211738:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "crwdns211740:0crwdne211740:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "crwdns211742:0crwdne211742:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "crwdns211744:0crwdne211744:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "crwdns211746:0crwdne211746:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "crwdns523125:0crwdne523125:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "crwdns523127:0crwdne523127:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "crwdns211748:0crwdne211748:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "crwdns211750:0crwdne211750:0", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "crwdns211752:0crwdne211752:0", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "crwdns211754:0crwdne211754:0", "app.containers.AdminPage.SettingsPage.addAreaButton": "crwdns211756:0crwdne211756:0", "app.containers.AdminPage.SettingsPage.addTopicButton": "crwdns211758:0crwdne211758:0", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "crwdns3338241:0crwdne3338241:0", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "crwdns3338243:0crwdne3338243:0", "app.containers.AdminPage.SettingsPage.approvalDescription": "crwdns3645127:0crwdne3645127:0", "app.containers.AdminPage.SettingsPage.approvalSave": "crwdns3645117:0crwdne3645117:0", "app.containers.AdminPage.SettingsPage.approvalTitle": "crwdns3645121:0crwdne3645121:0", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "crwdns211760:0crwdne211760:0", "app.containers.AdminPage.SettingsPage.areaTerm": "crwdns211762:0crwdne211762:0", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "crwdns211764:0crwdne211764:0", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "crwdns211766:0crwdne211766:0", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "crwdns211768:0crwdne211768:0", "app.containers.AdminPage.SettingsPage.areasTerm": "crwdns211770:0crwdne211770:0", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "crwdns211772:0crwdne211772:0", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "crwdns211774:0crwdne211774:0", "app.containers.AdminPage.SettingsPage.avatarsTitle": "crwdns211776:0crwdne211776:0", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "crwdns211778:0crwdne211778:0", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "crwdns211780:0crwdne211780:0", "app.containers.AdminPage.SettingsPage.bannerHeader": "crwdns211782:0crwdne211782:0", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "crwdns211784:0crwdne211784:0", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "crwdns211786:0crwdne211786:0", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "crwdns211788:0crwdne211788:0", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "crwdns211790:0crwdne211790:0", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "crwdns211792:0crwdne211792:0", "app.containers.AdminPage.SettingsPage.brandingDescription": "crwdns211794:0crwdne211794:0", "app.containers.AdminPage.SettingsPage.brandingTitle": "crwdns211796:0crwdne211796:0", "app.containers.AdminPage.SettingsPage.cancel": "crwdns211798:0crwdne211798:0", "app.containers.AdminPage.SettingsPage.chooseLayout": "crwdns211800:0crwdne211800:0", "app.containers.AdminPage.SettingsPage.color_primary": "crwdns211802:0crwdne211802:0", "app.containers.AdminPage.SettingsPage.color_secondary": "crwdns211804:0crwdne211804:0", "app.containers.AdminPage.SettingsPage.color_text": "crwdns211806:0crwdne211806:0", "app.containers.AdminPage.SettingsPage.colorsTitle": "crwdns211808:0crwdne211808:0", "app.containers.AdminPage.SettingsPage.confirmHeader": "crwdns211810:0crwdne211810:0", "app.containers.AdminPage.SettingsPage.contentModeration": "crwdns211812:0crwdne211812:0", "app.containers.AdminPage.SettingsPage.ctaHeader": "crwdns211814:0crwdne211814:0", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "crwdns211816:0{orgName}crwdne211816:0", "app.containers.AdminPage.SettingsPage.customized_button": "crwdns211818:0crwdne211818:0", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "crwdns211820:0crwdne211820:0", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "crwdns211822:0crwdne211822:0", "app.containers.AdminPage.SettingsPage.defaultTopic": "crwdns211824:0crwdne211824:0", "app.containers.AdminPage.SettingsPage.delete": "crwdns211826:0crwdne211826:0", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "crwdns211828:0crwdne211828:0", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "crwdns211830:0crwdne211830:0", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "crwdns211832:0{adminProjectsLink}crwdne211832:0", "app.containers.AdminPage.SettingsPage.desktop": "crwdns211834:0crwdne211834:0", "app.containers.AdminPage.SettingsPage.editFormTitle": "crwdns211836:0crwdne211836:0", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "crwdns211838:0crwdne211838:0", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "crwdns211840:0crwdne211840:0", "app.containers.AdminPage.SettingsPage.fieldDescription": "crwdns211842:0crwdne211842:0", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "crwdns211844:0crwdne211844:0", "app.containers.AdminPage.SettingsPage.fieldTitle": "crwdns211846:0crwdne211846:0", "app.containers.AdminPage.SettingsPage.fieldTitleError": "crwdns211848:0crwdne211848:0", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "crwdns211850:0crwdne211850:0", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "crwdns211852:0crwdne211852:0", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "crwdns211854:0crwdne211854:0", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "crwdns211856:0crwdne211856:0", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "crwdns211858:0crwdne211858:0", "app.containers.AdminPage.SettingsPage.fixedRatio": "crwdns211860:0crwdne211860:0", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "crwdns211862:0{link}crwdne211862:0", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "crwdns211864:0crwdne211864:0", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "crwdns211866:0crwdne211866:0", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "crwdns211868:0{link}crwdne211868:0", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "crwdns211870:0crwdne211870:0", "app.containers.AdminPage.SettingsPage.header": "crwdns211872:0crwdne211872:0", "app.containers.AdminPage.SettingsPage.headerDescription": "crwdns211874:0crwdne211874:0", "app.containers.AdminPage.SettingsPage.header_bg": "crwdns211876:0crwdne211876:0", "app.containers.AdminPage.SettingsPage.helmetDescription": "crwdns211878:0crwdne211878:0", "app.containers.AdminPage.SettingsPage.helmetTitle": "crwdns211880:0crwdne211880:0", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "crwdns211882:0crwdne211882:0", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "crwdns211884:0{orgName}crwdne211884:0", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "crwdns211886:0crwdne211886:0", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "crwdns211888:0crwdne211888:0", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "crwdns211890:0crwdne211890:0", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "crwdns211892:0crwdne211892:0", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "crwdns211894:0crwdne211894:0", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "crwdns1646226:0{linkToActivityPage}crwdne1646226:0", "app.containers.AdminPage.SettingsPage.languages": "crwdns211898:0crwdne211898:0", "app.containers.AdminPage.SettingsPage.languagesTooltip": "crwdns211900:0crwdne211900:0", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "crwdns211902:0crwdne211902:0", "app.containers.AdminPage.SettingsPage.logo": "crwdns211904:0crwdne211904:0", "app.containers.AdminPage.SettingsPage.noHeader": "crwdns211906:0crwdne211906:0", "app.containers.AdminPage.SettingsPage.no_button": "crwdns211910:0crwdne211910:0", "app.containers.AdminPage.SettingsPage.organizationName": "crwdns211912:0crwdne211912:0", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "crwdns211914:0crwdne211914:0", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "crwdns211916:0crwdne211916:0", "app.containers.AdminPage.SettingsPage.phone": "crwdns211920:0crwdne211920:0", "app.containers.AdminPage.SettingsPage.platformConfiguration": "crwdns211922:0crwdne211922:0", "app.containers.AdminPage.SettingsPage.population": "crwdns2256918:0crwdne2256918:0", "app.containers.AdminPage.SettingsPage.populationMinError": "crwdns2256920:0crwdne2256920:0", "app.containers.AdminPage.SettingsPage.populationTooltip": "crwdns2256922:0crwdne2256922:0", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "crwdns211924:0crwdne211924:0", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "crwdns211926:0crwdne211926:0", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "crwdns211928:0crwdne211928:0", "app.containers.AdminPage.SettingsPage.projectsSettings": "crwdns211930:0crwdne211930:0", "app.containers.AdminPage.SettingsPage.projects_header": "crwdns211932:0crwdne211932:0", "app.containers.AdminPage.SettingsPage.registrationFields": "crwdns211934:0crwdne211934:0", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "crwdns211936:0crwdne211936:0", "app.containers.AdminPage.SettingsPage.registrationTitle": "crwdns211938:0crwdne211938:0", "app.containers.AdminPage.SettingsPage.save": "crwdns211940:0crwdne211940:0", "app.containers.AdminPage.SettingsPage.saveArea": "crwdns211942:0crwdne211942:0", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "crwdns211944:0crwdne211944:0", "app.containers.AdminPage.SettingsPage.saveSuccess": "crwdns211946:0crwdne211946:0", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "crwdns211948:0crwdne211948:0", "app.containers.AdminPage.SettingsPage.selectApprovers": "crwdns3645123:0crwdne3645123:0", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "crwdns1081656:0crwdne1081656:0", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "crwdns1081658:0crwdne1081658:0", "app.containers.AdminPage.SettingsPage.settingsSavingError": "crwdns211950:0crwdne211950:0", "app.containers.AdminPage.SettingsPage.sign_up_button": "crwdns211952:0crwdne211952:0", "app.containers.AdminPage.SettingsPage.signed_in": "crwdns211954:0crwdne211954:0", "app.containers.AdminPage.SettingsPage.signed_out": "crwdns211956:0crwdne211956:0", "app.containers.AdminPage.SettingsPage.signupFormText": "crwdns211958:0crwdne211958:0", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "crwdns211960:0crwdne211960:0", "app.containers.AdminPage.SettingsPage.statuses": "crwdns4582167:0crwdne4582167:0", "app.containers.AdminPage.SettingsPage.step1": "crwdns211962:0crwdne211962:0", "app.containers.AdminPage.SettingsPage.step1Tooltip": "crwdns211964:0crwdne211964:0", "app.containers.AdminPage.SettingsPage.step2": "crwdns211966:0crwdne211966:0", "app.containers.AdminPage.SettingsPage.step2Tooltip": "crwdns211968:0crwdne211968:0", "app.containers.AdminPage.SettingsPage.subtitleAreas": "crwdns211970:0crwdne211970:0", "app.containers.AdminPage.SettingsPage.subtitleBasic": "crwdns211972:0crwdne211972:0", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "crwdns211974:0crwdne211974:0", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "crwdns211976:0crwdne211976:0", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "crwdns211978:0crwdne211978:0", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "crwdns211980:0crwdne211980:0", "app.containers.AdminPage.SettingsPage.tabAreas1": "crwdns568025:0crwdne568025:0", "app.containers.AdminPage.SettingsPage.tabBranding": "crwdns568027:0crwdne568027:0", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "crwdns667385:0crwdne667385:0", "app.containers.AdminPage.SettingsPage.tabPolicies": "crwdns211986:0crwdne211986:0", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "crwdns3848637:0crwdne3848637:0", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "crwdns3896993:0crwdne3896993:0", "app.containers.AdminPage.SettingsPage.tabRegistration": "crwdns211988:0crwdne211988:0", "app.containers.AdminPage.SettingsPage.tabSettings": "crwdns211990:0crwdne211990:0", "app.containers.AdminPage.SettingsPage.tabTopics2": "crwdns4582169:0crwdne4582169:0", "app.containers.AdminPage.SettingsPage.tabWidgets": "crwdns211994:0crwdne211994:0", "app.containers.AdminPage.SettingsPage.tablet": "crwdns211996:0crwdne211996:0", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "crwdns211998:0crwdne211998:0", "app.containers.AdminPage.SettingsPage.titleAreas": "crwdns212000:0crwdne212000:0", "app.containers.AdminPage.SettingsPage.titleBasic": "crwdns212002:0crwdne212002:0", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "crwdns212004:0crwdne212004:0", "app.containers.AdminPage.SettingsPage.titleTopicManager": "crwdns212006:0crwdne212006:0", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "crwdns212008:0{link}crwdne212008:0", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "crwdns212010:0crwdne212010:0", "app.containers.AdminPage.SettingsPage.twoRowLayout": "crwdns212012:0crwdne212012:0", "app.containers.AdminPage.SettingsPage.urlError": "crwdns212014:0crwdne212014:0", "app.containers.AdminPage.SettingsPage.urlPatternError": "crwdns212016:0crwdne212016:0", "app.containers.AdminPage.SettingsPage.urlTitle": "crwdns212018:0crwdne212018:0", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "crwdns212020:0crwdne212020:0", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "crwdns3338245:0crwdne3338245:0", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "crwdns3338247:0crwdne3338247:0", "app.containers.AdminPage.SideBar.administrator": "crwdns568029:0crwdne568029:0", "app.containers.AdminPage.SideBar.communityPlatform": "crwdns568031:0crwdne568031:0", "app.containers.AdminPage.SideBar.community_monitor": "crwdns4305642:0crwdne4305642:0", "app.containers.AdminPage.SideBar.customerPortal": "crwdns3311963:0crwdne3311963:0", "app.containers.AdminPage.SideBar.dashboard": "crwdns212024:0crwdne212024:0", "app.containers.AdminPage.SideBar.emails": "crwdns212026:0crwdne212026:0", "app.containers.AdminPage.SideBar.folderManager": "crwdns568033:0crwdne568033:0", "app.containers.AdminPage.SideBar.groups": "crwdns212028:0crwdne212028:0", "app.containers.AdminPage.SideBar.guide": "crwdns212030:0crwdne212030:0", "app.containers.AdminPage.SideBar.inputManager": "crwdns212034:0crwdne212034:0", "app.containers.AdminPage.SideBar.insights": "crwdns212036:0crwdne212036:0", "app.containers.AdminPage.SideBar.inspirationHub": "crwdns4218451:0crwdne4218451:0", "app.containers.AdminPage.SideBar.knowledgeBase": "crwdns568035:0crwdne568035:0", "app.containers.AdminPage.SideBar.language": "crwdns568037:0crwdne568037:0", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "crwdns3311965:0crwdne3311965:0", "app.containers.AdminPage.SideBar.linkToSupport2": "crwdns3311967:0crwdne3311967:0", "app.containers.AdminPage.SideBar.menu": "crwdns212044:0crwdne212044:0", "app.containers.AdminPage.SideBar.messaging": "crwdns212046:0crwdne212046:0", "app.containers.AdminPage.SideBar.moderation": "crwdns212048:0crwdne212048:0", "app.containers.AdminPage.SideBar.notifications": "crwdns568041:0crwdne568041:0", "app.containers.AdminPage.SideBar.processing": "crwdns212050:0crwdne212050:0", "app.containers.AdminPage.SideBar.projectManager": "crwdns568043:0crwdne568043:0", "app.containers.AdminPage.SideBar.projects": "crwdns212052:0crwdne212052:0", "app.containers.AdminPage.SideBar.settings": "crwdns212054:0crwdne212054:0", "app.containers.AdminPage.SideBar.signOut": "crwdns568045:0crwdne568045:0", "app.containers.AdminPage.SideBar.support": "crwdns568047:0crwdne568047:0", "app.containers.AdminPage.SideBar.toPlatform": "crwdns568049:0crwdne568049:0", "app.containers.AdminPage.SideBar.tools": "crwdns667389:0crwdne667389:0", "app.containers.AdminPage.SideBar.user.myProfile": "crwdns667391:0crwdne667391:0", "app.containers.AdminPage.SideBar.users": "crwdns212056:0crwdne212056:0", "app.containers.AdminPage.SideBar.workshops": "crwdns212058:0crwdne212058:0", "app.containers.AdminPage.Topics.addTopics": "crwdns212060:0crwdne212060:0", "app.containers.AdminPage.Topics.browseTopics": "crwdns212062:0crwdne212062:0", "app.containers.AdminPage.Topics.cancel": "crwdns212064:0crwdne212064:0", "app.containers.AdminPage.Topics.confirmHeader": "crwdns212066:0crwdne212066:0", "app.containers.AdminPage.Topics.delete": "crwdns212068:0crwdne212068:0", "app.containers.AdminPage.Topics.deleteTopicLabel": "crwdns212070:0crwdne212070:0", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "crwdns212072:0crwdne212072:0", "app.containers.AdminPage.Topics.inputForm": "crwdns212074:0crwdne212074:0", "app.containers.AdminPage.Topics.lastTopicWarning": "crwdns212076:0{ideaFormLink}crwdne212076:0", "app.containers.AdminPage.Topics.projectTopicsDescription": "crwdns212078:0crwdne212078:0", "app.containers.AdminPage.Topics.remove": "crwdns212080:0crwdne212080:0", "app.containers.AdminPage.Topics.title": "crwdns212082:0crwdne212082:0", "app.containers.AdminPage.Topics.topicManager": "crwdns212084:0crwdne212084:0", "app.containers.AdminPage.Topics.topicManagerInfo": "crwdns212086:0{topicManagerLink}crwdne212086:0", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "crwdns212088:0crwdne212088:0", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "crwdns212090:0crwdne212090:0", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "crwdns212092:0crwdne212092:0", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "crwdns212094:0crwdne212094:0", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "crwdns212096:0crwdne212096:0", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "crwdns4902663:0crwdne4902663:0", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "crwdns212100:0crwdne212100:0", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "crwdns212102:0crwdne212102:0", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "crwdns212104:0crwdne212104:0", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "crwdns212106:0crwdne212106:0", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "crwdns212108:0crwdne212108:0", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "crwdns212110:0crwdne212110:0", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "crwdns212112:0crwdne212112:0", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "crwdns212114:0crwdne212114:0", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "crwdns212116:0crwdne212116:0", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "crwdns212118:0{allUsersLink}crwdne212118:0", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "crwdns212120:0crwdne212120:0", "app.containers.AdminPage.Users.GroupsPanel.select": "crwdns212122:0crwdne212122:0", "app.containers.AdminPage.Users.UsersGroup.exportAll": "crwdns2278856:0crwdne2278856:0", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "crwdns2278858:0crwdne2278858:0", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "crwdns2278860:0crwdne2278860:0", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "crwdns212130:0crwdne212130:0", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "crwdns212132:0crwdne212132:0", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "crwdns212134:0crwdne212134:0", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "crwdns212136:0crwdne212136:0", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "crwdns212138:0crwdne212138:0", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "crwdns2278862:0crwdne2278862:0", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "crwdns212140:0crwdne212140:0", "app.containers.AdminPage.groups.permissions.add": "crwdns212144:0crwdne212144:0", "app.containers.AdminPage.groups.permissions.addAnswer": "crwdns523129:0crwdne523129:0", "app.containers.AdminPage.groups.permissions.addQuestion": "crwdns523131:0crwdne523131:0", "app.containers.AdminPage.groups.permissions.answerChoices": "crwdns523133:0crwdne523133:0", "app.containers.AdminPage.groups.permissions.answerFormat": "crwdns523135:0crwdne523135:0", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "crwdns523137:0crwdne523137:0", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "crwdns2829817:0crwdne2829817:0", "app.containers.AdminPage.groups.permissions.createANewQuestion": "crwdns523139:0crwdne523139:0", "app.containers.AdminPage.groups.permissions.createAQuestion": "crwdns523141:0crwdne523141:0", "app.containers.AdminPage.groups.permissions.defaultField": "crwdns523143:0crwdne523143:0", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "crwdns212146:0crwdne212146:0", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "crwdns212148:0crwdne212148:0", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "crwdns1782192:0crwdne1782192:0", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "crwdns523147:0crwdne523147:0", "app.containers.AdminPage.groups.permissions.fieldType_date": "crwdns523149:0crwdne523149:0", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "crwdns523151:0crwdne523151:0", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "crwdns523153:0crwdne523153:0", "app.containers.AdminPage.groups.permissions.fieldType_number": "crwdns523155:0crwdne523155:0", "app.containers.AdminPage.groups.permissions.fieldType_select": "crwdns523157:0crwdne523157:0", "app.containers.AdminPage.groups.permissions.fieldType_text": "crwdns523159:0crwdne523159:0", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "crwdns2888823:0crwdne2888823:0", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "crwdns212152:0crwdne212152:0", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "crwdns212154:0crwdne212154:0", "app.containers.AdminPage.groups.permissions.members": "crwdns212156:0count={count}crwdnd212156:0count={count}crwdne212156:0", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "crwdns523161:0crwdne523161:0", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "crwdns212158:0crwdne212158:0", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "crwdns212160:0crwdne212160:0", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "crwdns212162:0crwdne212162:0", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "crwdns2016748:0crwdne2016748:0", "app.containers.AdminPage.groups.permissions.option1": "crwdns523165:0crwdne523165:0", "app.containers.AdminPage.groups.permissions.pendingInvitation": "crwdns212164:0crwdne212164:0", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "crwdns649381:0crwdne649381:0", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "crwdns2677507:0crwdne2677507:0", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "crwdns523169:0crwdne523169:0", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "crwdns523171:0crwdne523171:0", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "crwdns523173:0crwdne523173:0", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "crwdns777319:0crwdne777319:0", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "crwdns523175:0crwdne523175:0", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "crwdns523177:0crwdne523177:0", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "crwdns523179:0crwdne523179:0", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "crwdns2677567:0crwdne2677567:0", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "crwdns523183:0crwdne523183:0", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "crwdns777321:0crwdne777321:0", "app.containers.AdminPage.groups.permissions.questionDescription": "crwdns523189:0crwdne523189:0", "app.containers.AdminPage.groups.permissions.questionTitle": "crwdns523191:0crwdne523191:0", "app.containers.AdminPage.groups.permissions.save": "crwdns212214:0crwdne212214:0", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "crwdns212216:0crwdne212216:0", "app.containers.AdminPage.groups.permissions.saveSuccess": "crwdns212218:0crwdne212218:0", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "crwdns212220:0crwdne212220:0", "app.containers.AdminPage.groups.permissions.select": "crwdns523195:0crwdne523195:0", "app.containers.AdminPage.groups.permissions.selectValueError": "crwdns523199:0crwdne523199:0", "app.containers.AdminPage.new.createAProject": "crwdns2038164:0crwdne2038164:0", "app.containers.AdminPage.new.fromScratch": "crwdns2038166:0crwdne2038166:0", "app.containers.AdminPage.phase.methodPicker.addOn1": "crwdns1962712:0crwdne1962712:0", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "crwdns1962714:0crwdne1962714:0", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "crwdns4747489:0crwdne4747489:0", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "crwdns4747491:0crwdne4747491:0", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "crwdns1962716:0crwdne1962716:0", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "crwdns1962718:0crwdne1962718:0", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "crwdns1962720:0crwdne1962720:0", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "crwdns1962722:0crwdne1962722:0", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "crwdns1962724:0crwdne1962724:0", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "crwdns1962726:0crwdne1962726:0", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "crwdns1962728:0crwdne1962728:0", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "crwdns1962730:0crwdne1962730:0", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "crwdns1962732:0crwdne1962732:0", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "crwdns1962734:0crwdne1962734:0", "app.containers.AdminPage.phase.methodPicker.logic1": "crwdns1962736:0crwdne1962736:0", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "crwdns1962738:0crwdne1962738:0", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "crwdns2677461:0crwdne2677461:0", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "crwdns2677463:0crwdne2677463:0", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "crwdns1962740:0crwdne1962740:0", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "crwdns1962742:0crwdne1962742:0", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "crwdns1962744:0crwdne1962744:0", "app.containers.AdminPage.phase.methodPicker.survey1": "crwdns1962746:0crwdne1962746:0", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "crwdns1962748:0crwdne1962748:0", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "crwdns1962750:0crwdne1962750:0", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "crwdns1962752:0crwdne1962752:0", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "crwdns1962754:0crwdne1962754:0", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "crwdns1962756:0crwdne1962756:0", "app.containers.AdminPage.phase.methodPicker.votingDescription": "crwdns1962758:0crwdne1962758:0", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "crwdns1962760:0crwdne1962760:0", "app.containers.AdminPage.projects.all.all": "crwdns2125518:0crwdne2125518:0", "app.containers.AdminPage.projects.all.createProjectFolder": "crwdns2038168:0crwdne2038168:0", "app.containers.AdminPage.projects.all.existingProjects": "crwdns212224:0crwdne212224:0", "app.containers.AdminPage.projects.all.homepageWarning1": "crwdns5063955:0crwdne5063955:0", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "crwdns2125522:0crwdne2125522:0", "app.containers.AdminPage.projects.all.noProjects": "crwdns2125524:0crwdne2125524:0", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "crwdns2038170:0crwdne2038170:0", "app.containers.AdminPage.projects.all.projectsAndFolders": "crwdns212228:0crwdne212228:0", "app.containers.AdminPage.projects.all.publishedTab": "crwdns3763221:0crwdne3763221:0", "app.containers.AdminPage.projects.all.searchProjects": "crwdns2125526:0crwdne2125526:0", "app.containers.AdminPage.projects.all.yourProjects": "crwdns2125528:0crwdne2125528:0", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "crwdns1199520:0crwdne1199520:0", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "crwdns1199522:0{accuracy}crwdnd1199522:0{percentage}crwdne1199522:0", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "crwdns1199526:0crwdne1199526:0", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "crwdns1963192:0crwdne1963192:0", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "crwdns1199528:0crwdne1199528:0", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "crwdns1963194:0crwdne1963194:0", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "crwdns1199530:0crwdne1199530:0", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "crwdns1199532:0crwdne1199532:0", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "crwdns1199534:0crwdne1199534:0", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "crwdns1199536:0crwdne1199536:0", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "crwdns1199538:0crwdne1199538:0", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "crwdns1199540:0crwdne1199540:0", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "crwdns1963196:0crwdne1963196:0", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "crwdns1199542:0crwdne1199542:0", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "crwdns1199544:0crwdne1199544:0", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "crwdns1199546:0crwdne1199546:0", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "crwdns1199548:0crwdne1199548:0", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "crwdns1199550:0crwdne1199550:0", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "crwdns1199552:0crwdne1199552:0", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "crwdns1963198:0crwdne1963198:0", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "crwdns1199558:0crwdne1199558:0", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "crwdns1199560:0crwdne1199560:0", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "crwdns1199562:0crwdne1199562:0", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "crwdns1963200:0crwdne1963200:0", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "crwdns1963202:0crwdne1963202:0", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "crwdns1199568:0crwdne1199568:0", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "crwdns1199570:0crwdne1199570:0", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "crwdns1199572:0crwdne1199572:0", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "crwdns1199574:0crwdne1199574:0", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "crwdns1199576:0crwdne1199576:0", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "crwdns1199578:0crwdne1199578:0", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "crwdns1199580:0crwdne1199580:0", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "crwdns1199582:0crwdne1199582:0", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "crwdns1199584:0crwdne1199584:0", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "crwdns1199586:0crwdne1199586:0", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "crwdns1199588:0crwdne1199588:0", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "crwdns1199590:0crwdne1199590:0", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "crwdns1963204:0crwdne1963204:0", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "crwdns1199592:0crwdne1199592:0", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "crwdns1199594:0crwdne1199594:0", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "crwdns1199596:0crwdne1199596:0", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "crwdns1199598:0crwdne1199598:0", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "crwdns1199600:0crwdne1199600:0", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "crwdns1199602:0crwdne1199602:0", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "crwdns1199604:0crwdne1199604:0", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "crwdns1199606:0crwdne1199606:0", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "crwdns1199608:0crwdne1199608:0", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "crwdns1199610:0crwdne1199610:0", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "crwdns1199612:0crwdne1199612:0", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "crwdns1199614:0crwdne1199614:0", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "crwdns1963206:0crwdne1963206:0", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "crwdns1199618:0crwdne1199618:0", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "crwdns1199620:0crwdne1199620:0", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "crwdns1199622:0crwdne1199622:0", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "crwdns1199624:0crwdne1199624:0", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "crwdns1199626:0crwdne1199626:0", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "crwdns1199628:0crwdne1199628:0", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "crwdns1199630:0crwdne1199630:0", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "crwdns1199632:0crwdne1199632:0", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "crwdns1199634:0crwdne1199634:0", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "crwdns1199636:0crwdne1199636:0", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "crwdns1199638:0crwdne1199638:0", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "crwdns1199640:0crwdne1199640:0", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "crwdns1199642:0crwdne1199642:0", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "crwdns1199644:0crwdne1199644:0", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "crwdns1199646:0crwdne1199646:0", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "crwdns1199648:0crwdne1199648:0", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "crwdns1199650:0crwdne1199650:0", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "crwdns1199652:0crwdne1199652:0", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "crwdns1963208:0crwdne1963208:0", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "crwdns1199654:0crwdne1199654:0", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "crwdns1199656:0crwdne1199656:0", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "crwdns1199658:0crwdne1199658:0", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "crwdns1199660:0crwdne1199660:0", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "crwdns1199662:0crwdne1199662:0", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "crwdns1199664:0crwdne1199664:0", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "crwdns1199666:0crwdne1199666:0", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "crwdns1199668:0crwdne1199668:0", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "crwdns1199670:0crwdne1199670:0", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "crwdns1199672:0crwdne1199672:0", "app.containers.AdminPage.projects.project.analysis.Tags.other": "crwdns1199674:0crwdne1199674:0", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "crwdns1199676:0crwdne1199676:0", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "crwdns1199678:0crwdne1199678:0", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "crwdns1963210:0crwdne1963210:0", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "crwdns1199680:0crwdne1199680:0", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "crwdns1199682:0crwdne1199682:0", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "crwdns1199684:0crwdne1199684:0", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "crwdns1199686:0crwdne1199686:0", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "crwdns1199688:0crwdne1199688:0", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "crwdns1963212:0crwdne1963212:0", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "crwdns1199690:0crwdne1199690:0", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "crwdns1199692:0crwdne1199692:0", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "crwdns1199694:0crwdne1199694:0", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "crwdns1199696:0crwdne1199696:0", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "crwdns1199698:0crwdne1199698:0", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "crwdns1199700:0crwdne1199700:0", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "crwdns1199702:0crwdne1199702:0", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "crwdns1199704:0crwdne1199704:0", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "crwdns1199706:0crwdne1199706:0", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "crwdns1199708:0crwdne1199708:0", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "crwdns1199710:0crwdne1199710:0", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "crwdns1199712:0crwdne1199712:0", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "crwdns1199714:0crwdne1199714:0", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "crwdns1199716:0crwdne1199716:0", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "crwdns1199718:0crwdne1199718:0", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "crwdns1199720:0crwdne1199720:0", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "crwdns1199722:0crwdne1199722:0", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "crwdns1199724:0crwdne1199724:0", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "crwdns1199726:0crwdne1199726:0", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "crwdns1199728:0crwdne1199728:0", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "crwdns1199730:0crwdne1199730:0", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "crwdns1199732:0crwdne1199732:0", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "crwdns1199734:0crwdne1199734:0", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "crwdns1199736:0crwdne1199736:0", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "crwdns1199738:0crwdne1199738:0", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "crwdns1199740:0crwdne1199740:0", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "crwdns1199742:0crwdne1199742:0", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "crwdns1199744:0crwdne1199744:0", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "crwdns1199746:0crwdne1199746:0", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "crwdns1199748:0crwdne1199748:0", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "crwdns1199750:0crwdne1199750:0", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "crwdns1199752:0crwdne1199752:0", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "crwdns1199754:0crwdne1199754:0", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "crwdns1199756:0crwdne1199756:0", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "crwdns1199758:0crwdne1199758:0", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "crwdns1199760:0crwdne1199760:0", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "crwdns1199762:0crwdne1199762:0", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "crwdns1199764:0crwdne1199764:0", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "crwdns1963214:0crwdne1963214:0", "app.containers.AdminPage.projects.project.analysis.anonymous": "crwdns1199766:0crwdne1199766:0", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "crwdns1199768:0crwdne1199768:0", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "crwdns1199770:0crwdne1199770:0", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "crwdns1199772:0crwdne1199772:0", "app.containers.AdminPage.projects.project.analysis.comments": "crwdns1199774:0crwdne1199774:0", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "crwdns1199778:0crwdne1199778:0", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "crwdns1963216:0crwdne1963216:0", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "crwdns1550946:0crwdne1550946:0", "app.containers.AdminPage.projects.project.analysis.end": "crwdns1199780:0crwdne1199780:0", "app.containers.AdminPage.projects.project.analysis.filter": "crwdns1199782:0crwdne1199782:0", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "crwdns1550948:0crwdne1550948:0", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "crwdns4368172:0crwdne4368172:0", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "crwdns4280830:0crwdne4280830:0", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "crwdns4280832:0{count}crwdne4280832:0", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "crwdns4280836:0crwdne4280836:0", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "crwdns4280838:0crwdne4280838:0", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "crwdns4280840:0crwdne4280840:0", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "crwdns4280842:0crwdne4280842:0", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "crwdns4280844:0crwdne4280844:0", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "crwdns4280846:0crwdne4280846:0", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "crwdns4280848:0crwdne4280848:0", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "crwdns4368176:0crwdne4368176:0", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "crwdns4280850:0crwdne4280850:0", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "crwdns4280852:0crwdne4280852:0", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "crwdns4280854:0crwdne4280854:0", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "crwdns4280856:0crwdne4280856:0", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "crwdns4280858:0crwdne4280858:0", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "crwdns4280860:0crwdne4280860:0", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "crwdns4280862:0crwdne4280862:0", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "crwdns4280864:0crwdne4280864:0", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "crwdns4280866:0crwdne4280866:0", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "crwdns4280868:0crwdne4280868:0", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "crwdns4280870:0crwdne4280870:0", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "crwdns4368178:0crwdne4368178:0", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "crwdns1199784:0crwdne1199784:0", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "crwdns2747335:0crwdne2747335:0", "app.containers.AdminPage.projects.project.analysis.limit": "crwdns1963218:0crwdne1963218:0", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "crwdns1963220:0crwdne1963220:0", "app.containers.AdminPage.projects.project.analysis.manageInput": "crwdns3669023:0crwdne3669023:0", "app.containers.AdminPage.projects.project.analysis.nextGraph": "crwdns4280872:0crwdne4280872:0", "app.containers.AdminPage.projects.project.analysis.noAnswer": "crwdns1199788:0crwdne1199788:0", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "crwdns2747337:0crwdne2747337:0", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "crwdns2770839:0crwdne2770839:0", "app.containers.AdminPage.projects.project.analysis.noInputs": "crwdns1199790:0crwdne1199790:0", "app.containers.AdminPage.projects.project.analysis.previousGraph": "crwdns4280874:0crwdne4280874:0", "app.containers.AdminPage.projects.project.analysis.reactions": "crwdns1199792:0crwdne1199792:0", "app.containers.AdminPage.projects.project.analysis.remove": "crwdns1963222:0crwdne1963222:0", "app.containers.AdminPage.projects.project.analysis.removeFilter": "crwdns1199794:0crwdne1199794:0", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "crwdns1199796:0crwdne1199796:0", "app.containers.AdminPage.projects.project.analysis.search": "crwdns1199798:0crwdne1199798:0", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "crwdns2747339:0crwdne2747339:0", "app.containers.AdminPage.projects.project.analysis.start": "crwdns1199800:0crwdne1199800:0", "app.containers.AdminPage.projects.project.analysis.supportArticle": "crwdns1199802:0crwdne1199802:0", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "crwdns1199804:0crwdne1199804:0", "app.containers.AdminPage.projects.project.analysis.unknown": "crwdns1199806:0crwdne1199806:0", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "crwdns1963224:0crwdne1963224:0", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "crwdns1963226:0crwdne1963226:0", "app.containers.AdminPage.projects.project.analysis.votes": "crwdns1199808:0crwdne1199808:0", "app.containers.AdminPage.widgets.copied": "crwdns212230:0crwdne212230:0", "app.containers.AdminPage.widgets.copyToClipboard": "crwdns212232:0crwdne212232:0", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "crwdns212234:0crwdne212234:0", "app.containers.AdminPage.widgets.fieldAccentColor": "crwdns212236:0crwdne212236:0", "app.containers.AdminPage.widgets.fieldBackgroundColor": "crwdns212238:0crwdne212238:0", "app.containers.AdminPage.widgets.fieldButtonText": "crwdns212240:0crwdne212240:0", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "crwdns212242:0crwdne212242:0", "app.containers.AdminPage.widgets.fieldFont": "crwdns212244:0crwdne212244:0", "app.containers.AdminPage.widgets.fieldFontDescription": "crwdns212246:0{googleFontsLink}crwdne212246:0", "app.containers.AdminPage.widgets.fieldFontSize": "crwdns212248:0crwdne212248:0", "app.containers.AdminPage.widgets.fieldHeaderSubText": "crwdns212250:0crwdne212250:0", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "crwdns212252:0crwdne212252:0", "app.containers.AdminPage.widgets.fieldHeaderText": "crwdns212254:0crwdne212254:0", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "crwdns212256:0crwdne212256:0", "app.containers.AdminPage.widgets.fieldHeight": "crwdns212258:0crwdne212258:0", "app.containers.AdminPage.widgets.fieldInputsLimit": "crwdns212260:0crwdne212260:0", "app.containers.AdminPage.widgets.fieldProjects": "crwdns212262:0crwdne212262:0", "app.containers.AdminPage.widgets.fieldRelativeLink": "crwdns212264:0crwdne212264:0", "app.containers.AdminPage.widgets.fieldShowFooter": "crwdns212266:0crwdne212266:0", "app.containers.AdminPage.widgets.fieldShowHeader": "crwdns212268:0crwdne212268:0", "app.containers.AdminPage.widgets.fieldShowLogo": "crwdns212270:0crwdne212270:0", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "crwdns212272:0crwdne212272:0", "app.containers.AdminPage.widgets.fieldSort": "crwdns212274:0crwdne212274:0", "app.containers.AdminPage.widgets.fieldTextColor": "crwdns212276:0crwdne212276:0", "app.containers.AdminPage.widgets.fieldTopics": "crwdns212278:0crwdne212278:0", "app.containers.AdminPage.widgets.fieldWidth": "crwdns212280:0crwdne212280:0", "app.containers.AdminPage.widgets.homepage": "crwdns212282:0crwdne212282:0", "app.containers.AdminPage.widgets.htmlCodeExplanation": "crwdns212284:0crwdne212284:0", "app.containers.AdminPage.widgets.htmlCodeTitle": "crwdns212286:0crwdne212286:0", "app.containers.AdminPage.widgets.previewTitle": "crwdns212288:0crwdne212288:0", "app.containers.AdminPage.widgets.settingsTitle": "crwdns212290:0crwdne212290:0", "app.containers.AdminPage.widgets.sortNewest": "crwdns212292:0crwdne212292:0", "app.containers.AdminPage.widgets.sortPopular": "crwdns212294:0crwdne212294:0", "app.containers.AdminPage.widgets.sortTrending": "crwdns212296:0crwdne212296:0", "app.containers.AdminPage.widgets.subtitleWidgets": "crwdns212298:0crwdne212298:0", "app.containers.AdminPage.widgets.title": "crwdns212300:0crwdne212300:0", "app.containers.AdminPage.widgets.titleDimensions": "crwdns212302:0crwdne212302:0", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "crwdns212304:0crwdne212304:0", "app.containers.AdminPage.widgets.titleInputSelection": "crwdns212306:0crwdne212306:0", "app.containers.AdminPage.widgets.titleStyle": "crwdns212308:0crwdne212308:0", "app.containers.AdminPage.widgets.titleWidgets": "crwdns212310:0crwdne212310:0", "app.containers.ContentBuilder.Save": "crwdns212314:0crwdne212314:0", "app.containers.ContentBuilder.homepage.PageTitle": "crwdns1252822:0crwdne1252822:0", "app.containers.ContentBuilder.homepage.SaveError": "crwdns1400760:0crwdne1400760:0", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "crwdns1284016:0crwdne1284016:0", "app.containers.ContentBuilder.homepage.bannerImage": "crwdns1305226:0crwdne1305226:0", "app.containers.ContentBuilder.homepage.bannerSubtext": "crwdns1263170:0crwdne1263170:0", "app.containers.ContentBuilder.homepage.bannerText": "crwdns1263172:0crwdne1263172:0", "app.containers.ContentBuilder.homepage.button": "crwdns1263174:0crwdne1263174:0", "app.containers.ContentBuilder.homepage.chooseLayout": "crwdns1284018:0crwdne1284018:0", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "crwdns4582117:0crwdne4582117:0", "app.containers.ContentBuilder.homepage.customized_button": "crwdns1263176:0crwdne1263176:0", "app.containers.ContentBuilder.homepage.customized_button_text_label": "crwdns1263178:0crwdne1263178:0", "app.containers.ContentBuilder.homepage.customized_button_url_label": "crwdns1263180:0crwdne1263180:0", "app.containers.ContentBuilder.homepage.events.eventsDescription": "crwdns1347278:0crwdne1347278:0", "app.containers.ContentBuilder.homepage.eventsDescription": "crwdns1347280:0crwdne1347280:0", "app.containers.ContentBuilder.homepage.fixedRatio": "crwdns1284020:0crwdne1284020:0", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "crwdns1284022:0{link}crwdne1284022:0", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "crwdns1284024:0crwdne1284024:0", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "crwdns1284026:0crwdne1284026:0", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "crwdns1284028:0{link}crwdne1284028:0", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "crwdns1284030:0crwdne1284030:0", "app.containers.ContentBuilder.homepage.imageOverlayColor": "crwdns1284032:0crwdne1284032:0", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "crwdns1284034:0crwdne1284034:0", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "crwdns1284036:0crwdne1284036:0", "app.containers.ContentBuilder.homepage.invalidUrl": "crwdns1263182:0crwdne1263182:0", "app.containers.ContentBuilder.homepage.no_button": "crwdns1263184:0crwdne1263184:0", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "crwdns1263186:0crwdne1263186:0", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "crwdns1284038:0crwdne1284038:0", "app.containers.ContentBuilder.homepage.projectsDescription": "crwdns1347282:0{link}crwdne1347282:0", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "crwdns1347284:0crwdne1347284:0", "app.containers.ContentBuilder.homepage.registeredUsersView": "crwdns1263188:0crwdne1263188:0", "app.containers.ContentBuilder.homepage.showAvatars": "crwdns1263190:0crwdne1263190:0", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "crwdns1263192:0crwdne1263192:0", "app.containers.ContentBuilder.homepage.sign_up_button": "crwdns1263194:0crwdne1263194:0", "app.containers.ContentBuilder.homepage.signedInDescription": "crwdns1347288:0crwdne1347288:0", "app.containers.ContentBuilder.homepage.signedOutDescription": "crwdns1347290:0crwdne1347290:0", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "crwdns2550670:0{link}crwdne2550670:0", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "crwdns1284042:0crwdne1284042:0", "app.containers.ContentBuilder.homepage.twoRowLayout": "crwdns1284044:0crwdne1284044:0", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "crwdns1252826:0crwdne1252826:0", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "crwdns1252828:0crwdne1252828:0", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "crwdns1252830:0crwdne1252830:0", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "crwdns1252832:0crwdne1252832:0", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "crwdns1252834:0crwdne1252834:0", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "crwdns1252836:0crwdne1252836:0", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "crwdns3933071:0crwdne3933071:0", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "crwdns1252840:0crwdne1252840:0", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "crwdns1252842:0crwdne1252842:0", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "crwdns1252844:0{visitLinkMessage}crwdne1252844:0", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "crwdns1252846:0crwdne1252846:0", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "crwdns1252848:0crwdne1252848:0", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "crwdns1252850:0crwdne1252850:0", "app.containers.admin.ContentBuilder.accordionMultiloc": "crwdns1252852:0crwdne1252852:0", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "crwdns1252854:0crwdne1252854:0", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "crwdns1252856:0crwdne1252856:0", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "crwdns1252858:0crwdne1252858:0", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "crwdns1252860:0crwdne1252860:0", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "crwdns1252862:0crwdne1252862:0", "app.containers.admin.ContentBuilder.buttonMultiloc": "crwdns1252864:0crwdne1252864:0", "app.containers.admin.ContentBuilder.delete": "crwdns212344:0crwdne212344:0", "app.containers.admin.ContentBuilder.error": "crwdns212358:0crwdne212358:0", "app.containers.admin.ContentBuilder.errorMessage": "crwdns212360:0{locale}crwdne212360:0", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "crwdns3338661:0crwdne3338661:0", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "crwdns4305644:0crwdne4305644:0", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "crwdns4305646:0crwdne4305646:0", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "crwdns4305648:0crwdne4305648:0", "app.containers.admin.ContentBuilder.homepage.default": "crwdns1252866:0crwdne1252866:0", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "crwdns1347292:0crwdne1347292:0", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "crwdns1252868:0crwdne1252868:0", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "crwdns3373825:0crwdne3373825:0", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "crwdns2936533:0crwdne2936533:0", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "crwdns2936535:0crwdne2936535:0", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "crwdns2936537:0crwdne2936537:0", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "crwdns2936539:0crwdne2936539:0", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "crwdns2936541:0crwdne2936541:0", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "crwdns2936543:0crwdne2936543:0", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "crwdns2936545:0crwdne2936545:0", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "crwdns2936549:0crwdne2936549:0", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "crwdns1252870:0crwdne1252870:0", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "crwdns1263196:0crwdne1263196:0", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "crwdns1252872:0crwdne1252872:0", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "crwdns1252874:0crwdne1252874:0", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "crwdns1252876:0crwdne1252876:0", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "crwdns1347296:0crwdne1347296:0", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "crwdns1252878:0crwdne1252878:0", "app.containers.admin.ContentBuilder.imageMultiloc": "crwdns1252882:0crwdne1252882:0", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "crwdns1252884:0crwdne1252884:0", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "crwdns1252886:0crwdne1252886:0", "app.containers.admin.ContentBuilder.participationBox": "crwdns3263247:0crwdne3263247:0", "app.containers.admin.ContentBuilder.textMultiloc": "crwdns1252888:0crwdne1252888:0", "app.containers.admin.ContentBuilder.threeColumnLayout": "crwdns212398:0crwdne212398:0", "app.containers.admin.ContentBuilder.twoColumnLayout": "crwdns212400:0crwdne212400:0", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "crwdns212402:0crwdne212402:0", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "crwdns212404:0crwdne212404:0", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "crwdns212406:0crwdne212406:0", "app.containers.admin.ContentBuilder.urlPlaceholder": "crwdns212410:0crwdne212410:0", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "crwdns1877120:0crwdne1877120:0", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "crwdns1603856:0crwdne1603856:0", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "crwdns1877122:0crwdne1877122:0", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "crwdns1877124:0crwdne1877124:0", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "crwdns212424:0crwdne212424:0", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "crwdns212426:0crwdne212426:0", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "crwdns1877126:0{numberOfIdeas}crwdne1877126:0", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "crwdns1761144:0crwdne1761144:0", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "crwdns1877128:0crwdne1877128:0", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "crwdns1761148:0crwdne1761148:0", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "crwdns1761150:0crwdne1761150:0", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "crwdns1761152:0crwdne1761152:0", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "crwdns1761154:0crwdne1761154:0", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "crwdns1761156:0crwdne1761156:0", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "crwdns1877130:0crwdne1877130:0", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "crwdns1761160:0crwdne1761160:0", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "crwdns2311916:0crwdne2311916:0", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "crwdns2311918:0crwdne2311918:0", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "crwdns212432:0crwdne212432:0", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "crwdns3166355:0crwdne3166355:0", "app.containers.admin.ReportBuilder.charts.analyticsChart": "crwdns212434:0crwdne212434:0", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "crwdns212436:0crwdne212436:0", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "crwdns212438:0crwdne212438:0", "app.containers.admin.ReportBuilder.charts.noData": "crwdns212440:0crwdne212440:0", "app.containers.admin.ReportBuilder.charts.trafficSources": "crwdns212442:0crwdne212442:0", "app.containers.admin.ReportBuilder.charts.users": "crwdns2212584:0crwdne2212584:0", "app.containers.admin.ReportBuilder.charts.usersByAge": "crwdns212444:0crwdne212444:0", "app.containers.admin.ReportBuilder.charts.usersByGender": "crwdns212446:0crwdne212446:0", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "crwdns212448:0crwdne212448:0", "app.containers.admin.ReportBuilder.managerLabel1": "crwdns2467890:0crwdne2467890:0", "app.containers.admin.ReportBuilder.periodLabel1": "crwdns2467892:0crwdne2467892:0", "app.containers.admin.ReportBuilder.projectLabel1": "crwdns2467894:0crwdne2467894:0", "app.containers.admin.ReportBuilder.quarterReport1": "crwdns4305650:0{year}crwdnd4305650:0{quarter}crwdne4305650:0", "app.containers.admin.ReportBuilder.start1": "crwdns2467896:0crwdne2467896:0", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "crwdns400362:0crwdne400362:0", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "crwdns400364:0crwdne400364:0", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "crwdns452775:0crwdne452775:0", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "crwdns452777:0crwdne452777:0", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "crwdns400370:0crwdne400370:0", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "crwdns212476:0crwdne212476:0", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "crwdns3896995:0crwdne3896995:0", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "crwdns212480:0crwdne212480:0", "app.containers.admin.ideaStatuses.all.editButtonLabel": "crwdns212482:0crwdne212482:0", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "crwdns212484:0crwdne212484:0", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "crwdns212486:0{manageTab}crwdne212486:0", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "crwdns212488:0crwdne212488:0", "app.containers.admin.ideaStatuses.all.manage": "crwdns212490:0crwdne212490:0", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "crwdns2550668:0crwdne2550668:0", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "crwdns667393:0crwdne667393:0", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "crwdns3896997:0crwdne3896997:0", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "crwdns667395:0crwdne667395:0", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "crwdns3896999:0crwdne3896999:0", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "crwdns212496:0crwdne212496:0", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "crwdns212498:0crwdne212498:0", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "crwdns2782559:0crwdne2782559:0", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "crwdns2782561:0crwdne2782561:0", "app.containers.admin.ideaStatuses.form.category": "crwdns212500:0crwdne212500:0", "app.containers.admin.ideaStatuses.form.categoryDescription": "crwdns212502:0crwdne212502:0", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "crwdns4015949:0crwdne4015949:0", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "crwdns212504:0crwdne212504:0", "app.containers.admin.ideaStatuses.form.fieldColor": "crwdns212506:0crwdne212506:0", "app.containers.admin.ideaStatuses.form.fieldDescription": "crwdns212508:0crwdne212508:0", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "crwdns212510:0crwdne212510:0", "app.containers.admin.ideaStatuses.form.fieldTitle": "crwdns212512:0crwdne212512:0", "app.containers.admin.ideaStatuses.form.fieldTitleError": "crwdns212514:0crwdne212514:0", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "crwdns212516:0crwdne212516:0", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "crwdns212518:0crwdne212518:0", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "crwdns2782567:0crwdne2782567:0", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "crwdns2782569:0crwdne2782569:0", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "crwdns212524:0crwdne212524:0", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "crwdns212526:0crwdne212526:0", "app.containers.admin.ideaStatuses.form.saveStatus": "crwdns212528:0crwdne212528:0", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "crwdns212530:0crwdne212530:0", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "crwdns212532:0crwdne212532:0", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "crwdns212534:0crwdne212534:0", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "crwdns212536:0crwdne212536:0", "app.containers.admin.ideas.all.inputManagerMetaDescription": "crwdns212538:0crwdne212538:0", "app.containers.admin.ideas.all.inputManagerMetaTitle": "crwdns212540:0{orgName}crwdne212540:0", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "crwdns212542:0crwdne212542:0", "app.containers.admin.ideas.all.inputManagerPageTitle": "crwdns776833:0crwdne776833:0", "app.containers.admin.ideas.all.tabOverview": "crwdns776835:0crwdne776835:0", "app.containers.admin.import.importInputs": "crwdns212556:0crwdne212556:0", "app.containers.admin.import.importNoLongerAvailable3": "crwdns2158516:0crwdne2158516:0", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "crwdns470105:0adminSeats={adminSeats}crwdnd470105:0managerSeats={managerSeats}crwdne470105:0", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "crwdns470107:0seats={seats}crwdne470107:0", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "crwdns470109:0seats={seats}crwdne470109:0", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "crwdns400384:0crwdne400384:0", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "crwdns444089:0crwdne444089:0", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "crwdns470111:0crwdne470111:0", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "crwdns1368384:0crwdne1368384:0", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "crwdns523211:0crwdne523211:0", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "crwdns523213:0crwdne523213:0", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "crwdns212566:0crwdne212566:0", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "crwdns523215:0crwdne523215:0", "app.containers.admin.project.permissions.permissionsSelectionLabel": "crwdns212568:0crwdne212568:0", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "crwdns523221:0crwdne523221:0", "app.containers.admin.project.permissions.viewingRightsTitle": "crwdns212572:0crwdne212572:0", "app.containers.phaseConfig.enableSimilarInputDetection": "crwdns4368136:0crwdne4368136:0", "app.containers.phaseConfig.similarInputDetectionTitle": "crwdns4368138:0crwdne4368138:0", "app.containers.phaseConfig.similarInputDetectionTooltip": "crwdns4368140:0crwdne4368140:0", "app.containers.phaseConfig.similarityThresholdBody": "crwdns4368142:0crwdne4368142:0", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "crwdns4431690:0crwdne4431690:0", "app.containers.phaseConfig.similarityThresholdTitle": "crwdns4368146:0crwdne4368146:0", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "crwdns4431692:0crwdne4431692:0", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "crwdns4406641:0crwdne4406641:0", "app.containers.survey.sentiment.noAnswers2": "crwdns4305652:0crwdne4305652:0", "app.containers.survey.sentiment.numberComments": "crwdns4305654:0count={count}crwdne4305654:0", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "crwdns3739151:0crwdne3739151:0", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "crwdns2070250:0crwdne2070250:0", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "crwdns212578:0crwdne212578:0", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "crwdns4519511:0crwdne4519511:0", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "crwdns212584:0crwdne212584:0", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "crwdns212586:0crwdne212586:0", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "crwdns212588:0crwdne212588:0", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "crwdns212590:0{quantity}crwdne212590:0", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "crwdns212592:0crwdne212592:0", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "crwdns212594:0crwdne212594:0", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "crwdns212596:0crwdne212596:0", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "crwdns212598:0crwdne212598:0", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "crwdns212600:0crwdne212600:0", "app.modules.commercial.analytics.admin.components.Events.completed": "crwdns212602:0crwdne212602:0", "app.modules.commercial.analytics.admin.components.Events.events": "crwdns212604:0crwdne212604:0", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "crwdns212606:0crwdne212606:0", "app.modules.commercial.analytics.admin.components.Events.upcoming": "crwdns212608:0crwdne212608:0", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "crwdns212610:0crwdne212610:0", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "crwdns212612:0crwdne212612:0", "app.modules.commercial.analytics.admin.components.Invitations.pending": "crwdns212614:0crwdne212614:0", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "crwdns212616:0crwdne212616:0", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "crwdns212618:0crwdne212618:0", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "crwdns212620:0crwdne212620:0", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "crwdns212622:0crwdne212622:0", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "crwdns212624:0crwdne212624:0", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "crwdns212626:0crwdne212626:0", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "crwdns212628:0crwdne212628:0", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "crwdns212630:0crwdne212630:0", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "crwdns212632:0crwdne212632:0", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "crwdns212634:0crwdne212634:0", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "crwdns212636:0crwdne212636:0", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "crwdns212638:0crwdne212638:0", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "crwdns212648:0crwdne212648:0", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "crwdns212650:0crwdne212650:0", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "crwdns4368256:0crwdne4368256:0", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "crwdns212654:0crwdne212654:0", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "crwdns212656:0crwdne212656:0", "app.modules.commercial.analytics.admin.components.Tab": "crwdns212658:0crwdne212658:0", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "crwdns212664:0crwdne212664:0", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "crwdns212666:0crwdne212666:0", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "crwdns212668:0crwdne212668:0", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "crwdns212672:0crwdne212672:0", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "crwdns212674:0crwdne212674:0", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "crwdns212676:0crwdne212676:0", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "crwdns212678:0crwdne212678:0", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "crwdns212680:0crwdne212680:0", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "crwdns212682:0crwdne212682:0", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "crwdns4582069:0crwdne4582069:0", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "crwdns212684:0crwdne212684:0", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "crwdns212686:0crwdne212686:0", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "crwdns212688:0crwdne212688:0", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "crwdns212690:0crwdne212690:0", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "crwdns212692:0crwdne212692:0", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "crwdns212694:0crwdne212694:0", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "crwdns212696:0{referrerListButton}crwdne212696:0", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "crwdns212698:0crwdne212698:0", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "crwdns212700:0crwdne212700:0", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "crwdns212702:0crwdne212702:0", "app.modules.commercial.analytics.admin.components.totalParticipants": "crwdns2070254:0crwdne2070254:0", "app.modules.commercial.analytics.admin.containers.visitors.noData": "crwdns1368386:0crwdne1368386:0", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "crwdns4657647:0crwdne4657647:0", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "crwdns212710:0crwdne212710:0", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "crwdns2070256:0crwdne2070256:0", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "crwdns212712:0crwdne212712:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "crwdns212724:0crwdne212724:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "crwdns212726:0crwdne212726:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "crwdns212728:0crwdne212728:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "crwdns212730:0crwdne212730:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "crwdns212732:0crwdne212732:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "crwdns212734:0crwdne212734:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "crwdns212736:0crwdne212736:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "crwdns212738:0crwdne212738:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "crwdns212740:0crwdne212740:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "crwdns4632379:0crwdne4632379:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "crwdns212742:0crwdne212742:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "crwdns4632381:0crwdne4632381:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "crwdns4632383:0crwdne4632383:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "crwdns212744:0crwdne212744:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "crwdns212746:0crwdne212746:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "crwdns4632385:0crwdne4632385:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "crwdns212748:0crwdne212748:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "crwdns212750:0crwdne212750:0", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "crwdns212752:0crwdne212752:0", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "crwdns212766:0crwdne212766:0", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "crwdns212768:0crwdne212768:0", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "crwdns212770:0crwdne212770:0", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "crwdns212772:0numberOfItems={numberOfItems}crwdne212772:0", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "crwdns212774:0crwdne212774:0", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "crwdns212776:0crwdne212776:0", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "crwdns212778:0crwdne212778:0", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "crwdns212784:0crwdne212784:0", "app.modules.navbar.admin.containers.addProject": "crwdns2654425:0crwdne2654425:0", "app.modules.navbar.admin.containers.createCustomPageButton": "crwdns212786:0crwdne212786:0", "app.modules.navbar.admin.containers.deletePageConfirmation": "crwdns212788:0crwdne212788:0", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "crwdns2654427:0crwdne2654427:0", "app.modules.navbar.admin.containers.pageHeader": "crwdns212790:0crwdne212790:0", "app.modules.navbar.admin.containers.pageSubtitle": "crwdns212792:0crwdne212792:0", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "crwdns1963228:0crwdne1963228:0", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "crwdns1963230:0crwdne1963230:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "crwdns3848675:0crwdne3848675:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "crwdns1963232:0crwdne1963232:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "crwdns1963234:0crwdne1963234:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "crwdns1963236:0crwdne1963236:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "crwdns1963238:0crwdne1963238:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "crwdns1963240:0crwdne1963240:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "crwdns1963242:0crwdne1963242:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "crwdns1963244:0crwdne1963244:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "crwdns1963246:0crwdne1963246:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "crwdns1877132:0crwdne1877132:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "crwdns1962956:0crwdne1962956:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "crwdns1761162:0crwdne1761162:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "crwdns1761166:0crwdne1761166:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "crwdns1984776:0crwdne1984776:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "crwdns1761168:0crwdne1761168:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "crwdns1667678:0crwdne1667678:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "crwdns1962958:0crwdne1962958:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "crwdns1761170:0crwdne1761170:0", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "crwdns1761172:0crwdne1761172:0", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "crwdns4280816:0crwdne4280816:0", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "crwdns4280818:0crwdne4280818:0", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "crwdns4280820:0crwdne4280820:0", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "crwdns4280822:0crwdne4280822:0", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "crwdns4280824:0crwdne4280824:0", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "crwdns4280826:0crwdne4280826:0", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "crwdns4280828:0crwdne4280828:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "crwdns4657649:0crwdne4657649:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "crwdns4268402:0crwdne4268402:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "crwdns4268406:0crwdne4268406:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "crwdns4657651:0crwdne4657651:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "crwdns4657653:0crwdne4657653:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "crwdns4482003:0crwdne4482003:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "crwdns3645029:0crwdne3645029:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "crwdns3645031:0crwdne3645031:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "crwdns3645033:0crwdne3645033:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "crwdns3645037:0crwdne3645037:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "crwdns3645039:0crwdne3645039:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "crwdns3763165:0crwdne3763165:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "crwdns3799237:0crwdne3799237:0", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "crwdns3645043:0{numberOfProjects}crwdne3645043:0", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "crwdns212796:0crwdne212796:0", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "crwdns212798:0crwdne212798:0", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "crwdns212800:0{supportPageLink}crwdne212800:0"}
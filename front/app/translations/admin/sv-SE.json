{"UI.FormComponents.required": "krävs", "app.Admin.ManagementFeed.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.after": "<PERSON><PERSON>", "app.Admin.ManagementFeed.before": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.changed": "Modifierad", "app.Admin.ManagementFeed.created": "Skapad", "app.Admin.ManagementFeed.date": "Datum", "app.Admin.ManagementFeed.deleted": "Borttagen", "app.Admin.ManagementFeed.folder": "Mapp", "app.Admin.ManagementFeed.idea": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.in": "i projekt {project}", "app.Admin.ManagementFeed.item": "Föremål", "app.Admin.ManagementFeed.key": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.managementFeedNudge": "Åtkomst till hanteringsflödet ingår inte i din nuvarande licens. Kontakta din GovSuccess Manager för att lära dig mer om det.", "app.Admin.ManagementFeed.noActivityFound": "Ingen aktivitet hittades", "app.Admin.ManagementFeed.phase": "Fas", "app.Admin.ManagementFeed.project": "Projekt", "app.Admin.ManagementFeed.projectReviewApproved": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.projectReviewRequested": "Projektgranskning begärd", "app.Admin.ManagementFeed.title": "<PERSON><PERSON>", "app.Admin.ManagementFeed.user": "Deltagare", "app.Admin.ManagementFeed.userPlaceholder": "Välj en användare", "app.Admin.ManagementFeed.value": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.viewDetails": "Visa detaljer", "app.Admin.ManagementFeed.warning": "Experimentell funktion: En minimal lista över utvalda åtgärder som utförts av administratörer eller projektledare under de senaste 30 dagarna. Alla åtgärder är inte inkluderade.", "app.Admin.Moderation.managementFeed": "<PERSON><PERSON>", "app.Admin.Moderation.participationFeed": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "<PERSON>r du säker?", "app.components.Admin.Campaigns.clicked": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deleteCampaignButton": "Ta bort kampanj", "app.components.Admin.Campaigns.deliveryStatus_accepted": "Accepterad", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Studsade", "app.components.Admin.Campaigns.deliveryStatus_clicked": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "<PERSON><PERSON> visar hur många mottagare som klickade på en länk i e-postmeddelandet. Observera att vissa säkerhetssystem kan följa länkar automatiskt för att skanna dem, vilket kan resultera i falska klick.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_failed": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_opened": "Öppnad", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "<PERSON>ta visar hur många mottagare som öppnade e-postmeddelandet. Observera att vissa säkerhetssystem (t.ex. Microsoft Defender) kan ladda innehåll i förväg för skanning, vilket kan leda till falska öppningar.", "app.components.Admin.Campaigns.deliveryStatus_sent": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.draft": "Utkast", "app.components.Admin.Campaigns.from": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.manageButtonLabel": "Hantera", "app.components.Admin.Campaigns.opened": "Öppnad", "app.components.Admin.Campaigns.project": "Projekt", "app.components.Admin.Campaigns.recipientsTitle": "Mottagare", "app.components.Admin.Campaigns.reply_to": "<PERSON><PERSON><PERSON>-<PERSON>", "app.components.Admin.Campaigns.sent": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.statsButton": "Statistik", "app.components.Admin.Campaigns.subject": "Ämne", "app.components.Admin.Campaigns.to": "<PERSON>", "app.components.Admin.ImageCropper.cropFinalSentence": "Se även: {link}.", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "<PERSON><PERSON>ll det viktigaste innehållet inom de streckade linjerna så att det alltid är synligt.", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "3:1 på mobil (endast området mellan de streckade linjerna visas)", "app.components.Admin.ImageCropper.cropSentenceOne": "Bilden beskärs automatiskt:", "app.components.Admin.ImageCropper.cropSentenceTwo": "{aspect} p<PERSON> skriv<PERSON> (visas i full bredd)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "rekommenderat förhållande", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Rubrik<PERSON> för registrerade användare", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Varning! Färgen du valde har inte tillräckligt hög kontrast. Det kan göra texten svår att läsa. Välj en mörkare färg för att optimera läsbarheten.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Lägg till evenemang i navigeringsfältet", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "<PERSON>är det är aktiverat kommer en länk till alla projektevenemang att läggas till i navigeringsfältet.", "app.components.AdminPage.SettingsPage.eventsSection": "<PERSON><PERSON><PERSON>", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Anpassningsbart avsnitt för startsidan", "app.components.AnonymousPostingToggle.userAnonymity": "Användarens anonymitet", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Använda<PERSON> kommer att kunna dölja sin identitet för andra användare, projektledare och administratörer. Dessa bidrag kan fortfarande modereras.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Låt användarna delta anonymt.", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Användare kan fortfarande välja att delta med sitt riktiga namn, men de kommer att ha möjlighet att skicka in bidrag anonymt om de väljer att göra det. Alla användare måste fortfarande uppfylla de krav som anges på fliken Åtkomsträttigheter för att deras bidrag ska gå igenom. Användarprofildata kommer inte att vara tillgängliga vid export av deltagardata.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "<PERSON><PERSON><PERSON> mer om användaranonymitet i vår {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "st<PERSON><PERSON><PERSON><PERSON>", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "När ytterligare platser läggs till kommer din fakturering att öka. Kontakta din GovSuccess Manager för att få veta mer om detta.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Tack för att du besvarade enkäten! Du är välkommen att besvara den igen nästa kvartal.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Ladda ner som pdf", "app.components.FormSync.downloadExcelTemplate": "Ladda ner en Excel-mall", "app.components.FormSync.downloadExcelTemplateTooltip2": "Excel-mallarna inneh<PERSON> inte frågor om rang<PERSON>, mat<PERSON><PERSON><PERSON><PERSON><PERSON>, fr<PERSON><PERSON> om filuppladdning och frågor om kartdata (Drop Pin, Draw Route, Draw Area, ESRI-filuppladdning) eftersom dessa inte stöds för bulkimport i nuläget.", "app.components.ProjectTemplatePreview.close": "Stäng", "app.components.ProjectTemplatePreview.createProject": "Skapa projekt", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "<PERSON>ka<PERSON> ett projekt baserat på mallen ''{templateTitle}''", "app.components.ProjectTemplatePreview.goBack": "Gå tillbaka", "app.components.ProjectTemplatePreview.goBackTo": "Gå tillbaka till {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "Go Vocal-expert", "app.components.ProjectTemplatePreview.infoboxLine1": "Vill du använda den här mallen för ditt deltagandeprojekt?", "app.components.ProjectTemplatePreview.infoboxLine2": "Kontakta den ansvariga personen i din stadsförvaltning eller kontakta en {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Projektmapp", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Det valda datumet är ogiltigt. Ange ett datum i följande format: ÅÅÅÅ-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "V<PERSON><PERSON><PERSON> ett startdatum för projektet", "app.components.ProjectTemplatePreview.projectStartDate": "Ditt projekts startdatum", "app.components.ProjectTemplatePreview.projectTitle": "<PERSON>tt projekts titel", "app.components.ProjectTemplatePreview.projectTitleError": "Skriv en projekttitel", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Skriv en projekttitel för alla språk", "app.components.ProjectTemplatePreview.projectsOverviewPage": "översiktssida för projekt", "app.components.ProjectTemplatePreview.responseError": "Ojdå! Något gick fel.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Se fler mallar", "app.components.ProjectTemplatePreview.successMessage": "Projektet skapades!", "app.components.ProjectTemplatePreview.typeProjectName": "Skriv projektets namn", "app.components.ProjectTemplatePreview.useTemplate": "<PERSON><PERSON><PERSON><PERSON> den här mallen", "app.components.SeatInfo.additionalSeats": "<PERSON><PERSON><PERSON><PERSON> sittplatser", "app.components.SeatInfo.additionalSeatsToolTip": "<PERSON><PERSON>r visas det antal extra platser som du har köpt utöver \"Inkluderade platser\".", "app.components.SeatInfo.adminSeats": "Admin platser", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} adminplatser ingår", "app.components.SeatInfo.adminSeatsTooltip1": "Administratörer ansvarar för plattformen och har chefsrättigheter för alla mappar och projekt. På {visitHelpCenter} kan du läsa mer om de olika rollerna.", "app.components.SeatInfo.currentAdminSeatsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> platser för administrat<PERSON>rer", "app.components.SeatInfo.currentManagerSeatsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> chefsplatser", "app.components.SeatInfo.includedAdminToolTip": "<PERSON><PERSON>r visas antalet tillgängliga platser för administratörer som ingår i årsavtalet.", "app.components.SeatInfo.includedManagerToolTip": "<PERSON><PERSON>r visas antalet lediga platser för projektledare som ingår i årsavtalet.", "app.components.SeatInfo.includedSeats": "Inkluderade säten", "app.components.SeatInfo.managerSeats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.SeatInfo.managerSeatsTooltip": "Mapp-/projektansvariga kan hantera ett obegränsat antal mappar/projekt. På {visitHelpCenter} kan du läsa mer om de olika rollerna.", "app.components.SeatInfo.managersIncludedText": "{managerSeats} Föreståndarsäten ingår.", "app.components.SeatInfo.remainingSeats": "Återstående platser", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Totalt antal platser", "app.components.SeatInfo.totalSeatsTooltip": "Här visas det sammanlagda antalet platser i din plan och ytterligare platser som du har köpt.", "app.components.SeatInfo.usedSeats": "Begagnade säten", "app.components.SeatInfo.view": "Visa", "app.components.SeatInfo.visitHelpCenter": "besöka vårt hjälpcenter", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Din plan har {adminSeatsIncluded}. <PERSON>är du har använt alla platser kommer ytterligare platser att läggas till under \"Ytterligare platser\".", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "<PERSON> plan har {<PERSON><PERSON><PERSON><PERSON><PERSON>nc<PERSON>}, som är kvalificerad för mappledare och projektledare. När du har använt alla platser kommer extra platser att läggas till under \"Ytterligare platser\".", "app.components.UserSearch.addModerators": "<PERSON><PERSON><PERSON> till", "app.components.UserSearch.searchUsers": "Skriv för att söka efter användare...", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternativt felmeddelande", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "Som standard visas följande felmeddelande för användarna:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "<PERSON><PERSON><PERSON> fel<PERSON>", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "Du kan skriva över detta meddelande för varje språk med hjälp av textrutan \"Alternativt felmeddelande\" nedan. Om du lämnar textrutan tom kommer standardmeddelandet att visas.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "Felmeddelande", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "Detta är vad deltagarna kommer att se när de inte uppfyller deltagarkraven.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "<PERSON>ra fel<PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "Ingen fråga vald. Vänligen välj en fråga först.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "<PERSON><PERSON> svar", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} svar", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "Enkätfråga", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} fram till nu", "app.components.admin.DatePhasePicker.Input.openEnded": "<PERSON><PERSON><PERSON> för alla", "app.components.admin.DatePhasePicker.Input.selectDate": "<PERSON><PERSON><PERSON><PERSON> datum", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "<PERSON><PERSON><PERSON> slut<PERSON>tum", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "<PERSON><PERSON><PERSON>", "app.components.admin.Graphs": "Ingen data tillgänglig med de aktuella filtren.", "app.components.admin.Graphs.noDataShort": "Ingen data tillgänglig.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Kommentarer över tid", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Inlägg över tid", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Reaktioner över tid", "app.components.admin.InputManager.onePost": "1 indata", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Justering av offline-val", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Justering av röster offline", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "Med det här alternativet kan du inkludera uppgifter om deltagande från and<PERSON>, t.ex. person- el<PERSON> pap<PERSON>:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Den kommer att skilja sig visuellt från digitala röster.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "Det kommer att påverka det slutliga röstresultatet.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "Det kommer inte att återspeglas i dashboards för deltagardata.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Offlineröster för ett alternativ kan bara anges en gång i ett projekt och delas mellan alla faser i ett projekt.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "Du måste först ange det totala antalet offline-deltagare.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Totalt antal offline-deltagare", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "<PERSON><PERSON><PERSON> att kunna beräkna rätt resultat måste vi veta det <b>totala antalet offline-deltagare för den här fasen.</b>", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Vänligen ange endast de som deltog offline.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "Modifierad av {name}", "app.components.admin.PostManager.PostPreview.assignee": "Tilldelad person", "app.components.admin.PostManager.PostPreview.cancelEdit": "<PERSON><PERSON><PERSON><PERSON><PERSON> redigering", "app.components.admin.PostManager.PostPreview.currentStatus": "Aktuell status", "app.components.admin.PostManager.PostPreview.delete": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Är du säker på att du vill ta bort dessa indata? Den här åtgärden kan inte ångras.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Är du säker på att du vill ta bort dessa indata? Indata kommer att tas bort från alla projektfaser och kan inte återställas.", "app.components.admin.PostManager.PostPreview.edit": "Rediger<PERSON>", "app.components.admin.PostManager.PostPreview.noOne": "O<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Antalet gånger detta har inkluderats i andra deltagares deltagandebudgetar", "app.components.admin.PostManager.PostPreview.picks": "Val: {picksNumber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Reaktionen räknas:", "app.components.admin.PostManager.PostPreview.save": "Spara", "app.components.admin.PostManager.PostPreview.submitError": "<PERSON><PERSON>", "app.components.admin.PostManager.addFeatureLayer": "Lägg till funktionslager", "app.components.admin.PostManager.addFeatureLayerInstruction": "Kopiera URL: en till funktionslagret som finns på ArcGIS Online och klistra in den i bidraget nedan:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Lägg till ett nytt funktionslager på kartan", "app.components.admin.PostManager.addWebMap": "Lägg till webbkarta", "app.components.admin.PostManager.addWebMapInstruction": "Kopiera portal-ID: t för din webbkarta från ArcGIS Online och klistra in det i bidraget nedan:", "app.components.admin.PostManager.allPhases": "<PERSON>a faser", "app.components.admin.PostManager.allProjects": "Alla projekt", "app.components.admin.PostManager.allStatuses": "Alla statusar", "app.components.admin.PostManager.allTopics": "Alla ämnen", "app.components.admin.PostManager.anyAssignment": "Vilken administratör som helst", "app.components.admin.PostManager.assignedTo": "Tilldelad till {assigneeName}", "app.components.admin.PostManager.assignedToMe": "Tilldelad till mig", "app.components.admin.PostManager.assignee": "Tilldelad person", "app.components.admin.PostManager.authenticationError": "Ett autentiseringsfel uppstod när du försökte hämta det här lagret. Kontrollera webbadressen och att din Esri API-nyckel har åtkomst till det här lagret.", "app.components.admin.PostManager.automatedStatusTooltipText": "Denna status uppdateras automatiskt när villkoren är uppfyllda", "app.components.admin.PostManager.bodyTitle": "Beskrivning", "app.components.admin.PostManager.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.co-sponsors": "Medförslagsställare", "app.components.admin.PostManager.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "<PERSON>ta innebär att du förlorar alla data som är kopplade till dessa inmatningar, t.ex. kommentarer, reaktioner och röster. Denna <PERSON> kan inte ångras.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Är du säker på att du vill ta bort dessa ingångar?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "Ta bort ämne", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "Du försöker ta bort denna idé från en fas där den har fått röster. Om du gör detta kommer dessa röster att gå förlorade. Är du säker på att du vill ta bort den här idén från den här fasen?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "De röster som är förknippade med denna idé kommer att gå förlorade", "app.components.admin.PostManager.components.goToInputManager": "Gå till inmatningshanteraren", "app.components.admin.PostManager.components.goToProposalManager": "Gå till förslagshanteraren", "app.components.admin.PostManager.contributionFormTitle": "<PERSON><PERSON><PERSON> bidrag", "app.components.admin.PostManager.cost": "Kostnad", "app.components.admin.PostManager.createInput": "<PERSON><PERSON><PERSON> inmatning", "app.components.admin.PostManager.createInputsDescription": "Skapa en ny uppsättning inmatningar från ett tidigare projekt", "app.components.admin.PostManager.currentLat": "Central latitud", "app.components.admin.PostManager.currentLng": "Central longitud", "app.components.admin.PostManager.currentZoomLevel": "Zoomnivå", "app.components.admin.PostManager.defaultEsriError": "Ett fel uppstod när du försökte hämta detta lager. Kontrollera din nätverksanslutning och att URL:en är korrekt.", "app.components.admin.PostManager.delete": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.deleteAllSelectedInputs": "Ta bort {count} in<PERSON><PERSON>gg", "app.components.admin.PostManager.deleteConfirmation": "<PERSON>r du säker på att du vill ta bort det här lagret?", "app.components.admin.PostManager.dislikes": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.edit": "Rediger<PERSON>", "app.components.admin.PostManager.editProjects": "Redigera projekt", "app.components.admin.PostManager.editStatuses": "Redigera statusar", "app.components.admin.PostManager.editTags": "<PERSON><PERSON><PERSON> taggar", "app.components.admin.PostManager.editedPostSave": "Spara", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Import av data från Esri ArcGIS Online är en tilläggsfunktion. Prata med din GS manager för att låsa upp den.", "app.components.admin.PostManager.esriSideError": "Ett fel uppstod i ArcGIS-applikationen. Vänta några minuter och försök igen senare.", "app.components.admin.PostManager.esriWebMap": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.exportAllInputs": "Exportera alla inlägg (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Exportera alla kommentarer (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Exportera kommentarer för det här projektet (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Exportera inlägg i det här projektet (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Exportera valda inl<PERSON>gg (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Exportera kommentarer för valda <PERSON> (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Exportera röster efter input (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Exportera röster per användare (.xslx)", "app.components.admin.PostManager.exports": "Exporter", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "Du får endast ladda upp kartdata som antingen GeoJSON-lager eller importera från ArcGIS Online. Ta bort alla nuvarande GeoJSON-lager om du vill lägga till ett funktionslager.", "app.components.admin.PostManager.featureLayerTooltop": "Du hittar URL-adressen för funktionslagret till höger på objektsidan i ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "<PERSON><PERSON><PERSON><PERSON> hur andra personer ska se ditt namn", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Förklara den här statusändringen", "app.components.admin.PostManager.fileUploadError": "En eller flera filer kunde inte laddas upp. Kontrollera filstorlek och format och försök igen.", "app.components.admin.PostManager.formTitle": "<PERSON><PERSON>a idé", "app.components.admin.PostManager.generalApiError2": "Ett fel uppstod när du försökte hämta det här objektet. Kontrollera att URL:en eller portal-ID:t är korrekt och att du har tillgång till objektet.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Du kan endast ladda upp kartdata som antingen GeoJSON-lager eller importera från ArcGIS Online. Ta bort all ArcGIS-data om du vill ladda upp ett GeoJSON-lager.", "app.components.admin.PostManager.goToDefaultMapView": "Gå till standardkartcentret", "app.components.admin.PostManager.hiddenFieldsLink": "dolda fält", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Tips! Om du använder Typeform lägger du till {hiddenFieldsLink} för att hålla reda på vilka som har svarat på din undersökning.", "app.components.admin.PostManager.import2": "Import", "app.components.admin.PostManager.importError": "Det gick inte att importera den valda filen eftersom den inte är en giltig GeoJSON-fil", "app.components.admin.PostManager.importEsriFeatureLayer": "Importera Esri-funktionslager", "app.components.admin.PostManager.importEsriWebMap": "Importera Esri <PERSON>", "app.components.admin.PostManager.importInputs": "Import av insatsvaror", "app.components.admin.PostManager.imported": "Importerad", "app.components.admin.PostManager.initiativeFormTitle": "Redigera initiativ", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputImportProgress": "{importedCount} ut ur {totalCount} {totalCount, plural, one {inmatning har} other {inmatningar har}} importerats. Importen pågår fortfarande, vänligen kom tillbaka senare.", "app.components.admin.PostManager.inputManagerHeader": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputs": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputsExportFileName": "idé", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Visa bara inlägg som behöver återkoppling", "app.components.admin.PostManager.issueFormTitle": "<PERSON><PERSON><PERSON> f<PERSON>", "app.components.admin.PostManager.latestFeedbackMode": "Använd den senaste befintliga officiella uppdateringen som en förklaring", "app.components.admin.PostManager.layerAdded": "Lager har lagts till framgångsrikt", "app.components.admin.PostManager.likes": "<PERSON><PERSON>", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Om du flyttar bort dessa indata från deras aktuella projekt förlorar du informationen om deras tilldelade faser. Vill du fortsätta?", "app.components.admin.PostManager.mapData": "Kartdata", "app.components.admin.PostManager.multipleInputs": "{ideaCount} in<PERSON><PERSON>gg", "app.components.admin.PostManager.newFeedbackMode": "Skriv en ny uppdatering för att förklara den här förändringen", "app.components.admin.PostManager.noFilteredResults": "Filtren du valde gav inga resultat", "app.components.admin.PostManager.noInputs": "Inga inmatningar ännu", "app.components.admin.PostManager.noInputsDescription": "<PERSON> lägger till din egen input eller börjar från ett tidigare deltagarprojekt.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 inmatningar} one {1 inmatning} other {# inmatningar}} kommer att importeras från det valda projektet och den valda fasen. Importen körs i bakgrunden och indata kommer att visas i indatahanteraren när den är klar.", "app.components.admin.PostManager.noOne": "O<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.noProject": "Inget projekt", "app.components.admin.PostManager.officialFeedbackModal.author": "Författaren", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "<PERSON><PERSON><PERSON><PERSON> hur ditt namn ska visas", "app.components.admin.PostManager.officialFeedbackModal.description": "Att ge officiell feedback bidrar till att hålla processen transparent och bygger upp förtroendet för plattformen.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Författare krävs", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Feedback krävs", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Officiell feedback", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Förklara orsaken till statusändringen", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Återkoppling av inlägg", "app.components.admin.PostManager.officialFeedbackModal.skip": "Hoppa över den här gången", "app.components.admin.PostManager.officialFeedbackModal.title": "<PERSON><PERSON><PERSON> ditt beslut", "app.components.admin.PostManager.officialUpdateAuthor": "<PERSON><PERSON><PERSON><PERSON> hur andra personer ska se ditt namn", "app.components.admin.PostManager.officialUpdateBody": "Förklara den här statusändringen", "app.components.admin.PostManager.offlinePicks": "Offline-val", "app.components.admin.PostManager.offlineVotes": "Offline röster", "app.components.admin.PostManager.onlineVotes": "Röster online", "app.components.admin.PostManager.optionFormTitle": "Redigera alternativ", "app.components.admin.PostManager.participants": "Deltagare", "app.components.admin.PostManager.participatoryBudgettingPicks": "Val", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Val online", "app.components.admin.PostManager.pbItemCountTooltip": "Antalet gånger detta har inkluderats i andra deltagares deltagandebudgetar", "app.components.admin.PostManager.petitionFormTitle": "<PERSON><PERSON><PERSON> f<PERSON>", "app.components.admin.PostManager.postedIn": "Inlagd i {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Redigera projekt", "app.components.admin.PostManager.projectsTab": "Projekt", "app.components.admin.PostManager.projectsTabTooltipContent": "Du kan dra och släppa inlägg för att flytta dem från ett projekt till ett annat. Obs! För tidslinjeprojekt måste du fortfarande lägga till inlägget i en specifik fas.", "app.components.admin.PostManager.proposalFormTitle": "Redigera förslag", "app.components.admin.PostManager.proposedBudgetTitle": "Förslag till budget", "app.components.admin.PostManager.publication_date": "Publicerades", "app.components.admin.PostManager.questionFormTitle": "<PERSON><PERSON><PERSON> f<PERSON>", "app.components.admin.PostManager.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.resetFiltersButton": "<PERSON><PERSON>tä<PERSON> filter", "app.components.admin.PostManager.resetInputFiltersDescription": "Återställ filtren för att se alla indata.", "app.components.admin.PostManager.saved": "Sparad", "app.components.admin.PostManager.screeningTooltip": "Screening ing<PERSON>r inte i din nuvarande plan. Prata med din Government Success Manager <PERSON><PERSON> administra<PERSON><PERSON><PERSON> för att låsa upp den.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Screening är avstängd för den här fasen. Gå till fasinställningar för att aktivera det", "app.components.admin.PostManager.selectAPhase": "Välj en fas", "app.components.admin.PostManager.selectAProject": "V<PERSON><PERSON><PERSON> ett projekt", "app.components.admin.PostManager.setAsDefaultMapView": "Spara den aktuella mittpunkten och zoomnivån som standard för kartan", "app.components.admin.PostManager.startFromPastInputs": "Utgå från tidigare ingångar", "app.components.admin.PostManager.statusChangeGenericError": "Det uppstod ett fel, försök igen senare eller kontakta supporten.", "app.components.admin.PostManager.statusChangeSave": "Byt status", "app.components.admin.PostManager.statusesTab": "Status", "app.components.admin.PostManager.statusesTabTooltipContent": "Ändra status för ett inlägg genom att dra och släppa. Den ursprungliga författaren och andra bidragsgivare kommer att få ett meddelande om den ändrade statusen.", "app.components.admin.PostManager.submitApiError": "Det fanns ett problem med att skicka in formuläret. Kontrollera om det finns några fel och försök igen.", "app.components.admin.PostManager.timelineTab": "Tidslinje", "app.components.admin.PostManager.timelineTabTooltipText": "Dra och släpp inlägg för att kopiera dem till olika projektfaser.", "app.components.admin.PostManager.title": "Titel", "app.components.admin.PostManager.topicsTab": "Ämnen", "app.components.admin.PostManager.topicsTabTooltipText": "<PERSON><PERSON><PERSON> till taggar till en indata genom att dra och släppa.", "app.components.admin.PostManager.view": "Visa", "app.components.admin.PostManager.votes": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votesByInputExportFileName": "r<PERSON><PERSON>_per_inmatning", "app.components.admin.PostManager.votesByUserExportFileName": "r<PERSON><PERSON>_från_användare", "app.components.admin.PostManager.webMapAlreadyExists": "Du kan bara lägga till en webbkarta åt gången. Ta bort den aktuella kartan för att importera en annan.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Du får endast ladda upp kartdata som antingen GeoJSON-lager eller importera från ArcGIS Online. Ta bort alla aktuella GeoJSON-lager om du vill ansluta en webbkarta.", "app.components.admin.PostManager.webMapTooltip": "Du hittar portal-ID:t för webbkartan på ArcGIS Online-objektsidan, på höger sida.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {<PERSON><PERSON> än en dag} one {En dag} other {# dagar}} kvar", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "Ja, radera undersökningsresultat", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "<PERSON>ta kan inte göras ogjort", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Radera undersökningsresultat", "app.components.admin.ProjectEdit.survey.downloadResults2": "Ladda ner undersökningsresultat", "app.components.admin.ReportExportMenu.FileName.fromFilter": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ReportExportMenu.FileName.groupFilter": "grupp", "app.components.admin.ReportExportMenu.FileName.projectFilter": "projekt", "app.components.admin.ReportExportMenu.FileName.topicFilter": "ämne", "app.components.admin.ReportExportMenu.FileName.untilFilter": "tills", "app.components.admin.ReportExportMenu.downloadPng": "Ladda ned som PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Ladda ned som SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Ladda ner Excel", "app.components.admin.SlugInput.regexError": "En slug får bara innehålla små bokstäver (a–z), si<PERSON><PERSON><PERSON> (0–9) och bindestreck (-). Det första och sista tecknet får inte vara ett bindestreck. Två bindestreck i rad (--) får inte användas.", "app.components.admin.TerminologyConfig.saveButton": "Spara", "app.components.admin.commonGroundInputManager.title": "Titel", "app.components.admin.seatSetSuccess.admin": "Admin", "app.components.admin.seatSetSuccess.allDone": "<PERSON>t är klart", "app.components.admin.seatSetSuccess.close": "Stäng", "app.components.admin.seatSetSuccess.manager": "Chef", "app.components.admin.seatSetSuccess.orderCompleted": "Beställning slutförd", "app.components.admin.seatSetSuccess.reflectedMessage": "Ändringarna i din plan kommer att återspeglas i nästa faktureringscykel.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} rät<PERSON>gheter har beviljats den eller de valda användarna.", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Är du säker på att du vill radera alla undersökningsresultat?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "Denna metod för deltagande är i betaversion. Vi lanserar den gradvis för att samla in feedback och förbättra upplevelsen.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Att samla in feedback på ett dokument är en anpassad funktion och ingår inte i din nuvarande licens. Kontakta din GovSuccess Manager om du vill veta mer om den.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Bidrag", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "<PERSON>tal dagar som krävs", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "<PERSON><PERSON> da<PERSON> för att nå lägsta antal röster", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Mer information om hur du bäddar in en länk för Google Formulär finns i {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "den här supportartikeln", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Initiativ", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Vad ska indata heta?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Kommentar", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Ange länken till ditt Konveio-dokument här. <PERSON><PERSON><PERSON> vå<PERSON> {supportArticleLink} för mer information om hur du konfigurerar Konveio.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "st<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "Det<PERSON> ingår inte i din nuvarande plan. Kontakta din Government Success Manager <PERSON><PERSON> administra<PERSON><PERSON><PERSON> för att låsa upp den.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "En maxbudget krävs", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Maximalt antal röster per alternativ måste vara mindre än eller lika med det totala antalet röster", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "<PERSON><PERSON> maximalt antal röster krävs", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "Meddelanden", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Minimibudgeten får inte vara större än maxbudgeten", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "En minimibudget krävs", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Det lägsta antalet röster får inte vara större än det högsta antalet", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "<PERSON>tt minsta antal röster krävs", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Saknat slutdatum", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Saknat start<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Alternativ", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Fliken Input Manager", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Konfigurera röstningsalternativen på fliken Input manager n<PERSON><PERSON> <PERSON> har skapat en fas.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Konfigurera röstningsalternativen i {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Alternativ för deltagande", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "Deltagare", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Framställning", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Administratörer och projektledare", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Dokument med kommentarer:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} kan delta i denna fas.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Kommentar:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Gemensam jord<PERSON>s", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Radera fas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "<PERSON><PERSON>, radera denna fas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "<PERSON>r du säker på att du vill ta bort denna fas?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "Alla data som rör denna fas kommer att raderas. Detta kan inte ångras.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Fas för annotering av dokument", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "<PERSON>a", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "Extern undersökningsfas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "I undersökningsfasen för plattformar", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Informationsfas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "Inget slutdatum", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Omröstningsfas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Förslagsfasen", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>React:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b><PERSON><PERSON><PERSON> fö<PERSON>g:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Registrerade användare", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b><PERSON><PERSON><PERSON> in:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>O<PERSON><PERSON><PERSON><PERSON>ning:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b>Genomför undersökning:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Användare med bekräftad e-post", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>Volontärarbete:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Volontärfasen", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>O<PERSON><PERSON><PERSON><PERSON>ning:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Omröstningsfas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Vem kan delta?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Bidrag kommer inte att vara synligt förrän en administratör granskat och godkänt det. Deltagare kan inte redigera ett bidrag efter att det har granskats.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "Endast administratörer", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "Alla som har länken kan interagera med projektutkastet", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "Godkänna", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Genom att godkänna kan projektledarna publicera projektet.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "<PERSON><PERSON><PERSON><PERSON> av {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Arkiverad", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Utkast", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "<PERSON><PERSON><PERSON> be<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "<PERSON>a", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Grupper", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Offline väljare", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Endast administratörer{inFolder, select, true { eller mapphanterare} other {}} kan publicera projektet", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 deltagare} other {{participantsCount} deltagare}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Deltagare i inbäddade metoder (t.ex. externa enkäter)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Följare av ett projekt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Obs: Om du aktiverar anonym eller öppen deltagarbehörighet kan användare delta flera gånger, vilket kan leda till missvisande eller ofullständiga användardata.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "<PERSON><PERSON><PERSON> <b>ing<PERSON>r inte:</b>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Bland deltagarna finns:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Anm<PERSON><PERSON> till evenemang", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "Användare som interagerar med Go Vocal-metoder", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "Väntar på godkännande", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Projektgranskarna har underrättats.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "Allmänheten", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Publicera", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Publicerad - Aktiv", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Publicerad - Avslutad", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Uppdatera länken för förhandsgranskning av projekt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Återskapa länken för förhandsgranskning av projekt. Detta kommer att ogiltigförklara den tidigare länken.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "<PERSON><PERSON><PERSON> l<PERSON> slutar funger<PERSON>, men du kan när som helst skapa en ny.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Är du säker på det? Detta kommer att inaktivera den aktuella länken", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "<PERSON><PERSON>, uppdatera länken", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Begär godkänna<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "Projektet måste godkännas av en administratör{inFolder, select, true { eller en av mapphanterarna} other {}} innan du kan publicera det. Klicka på knappen nedan för att begära godkännande.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Inställningar", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Dela", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "<PERSON><PERSON><PERSON> länk", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "<PERSON><PERSON><PERSON> k<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Delning av privata länkar ingår inte i din nuvarande plan. Prata med din Government Success Manager el<PERSON> administra<PERSON><PERSON><PERSON> för att låsa upp det.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Dela detta projekt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "<PERSON><PERSON> har till<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "Visa", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Projekt", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Förslag", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Lägsta antal röster för att beaktas", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Minsta antal röster krävs", "app.components.app.containers.AdminPage.ProjectEdit.report": "Rapport", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Kräv granskning av bidrag", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Tidslinje", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Trafik", "app.components.formBuilder.cancelMethodChange1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.changeMethodWarning1": "Byte av metod kan leda till att alla indata som genererats eller mottagits under användning av den tidigare metoden döljs.", "app.components.formBuilder.changingMethod1": "Ändring av metod", "app.components.formBuilder.confirmMethodChange1": "Ja, fortsätt", "app.components.formBuilder.copySurveyModal.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.description": "<PERSON><PERSON> kommer att kopiera alla frågor och logik utan svar.", "app.components.formBuilder.copySurveyModal.duplicate": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "Inga lämpliga faser hittades i detta projekt", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "Ingen fas vald. Vänligen välj en fas först.", "app.components.formBuilder.copySurveyModal.noProject": "Inget projekt", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Inget projekt valt. Vänligen välj ett projekt först.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "Du har redan sparat ändringar i denna undersökning. Om du duplicerar en annan enkät kommer ändringarna att gå förlorade.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Undersökningsfas", "app.components.formBuilder.copySurveyModal.title": "Välj en undersökning att duplicera", "app.components.formBuilder.editWarningModal.addOrReorder": "<PERSON>ä<PERSON> till eller ändra ordning på frågor", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "<PERSON>a svarsuppgifter kan vara felaktiga", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Redigera text", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Rättar du ett stavfel? Det påverkar inte dina svarsdata", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Du förlorar svarsdata som är kopplade till den frågan", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Ta bort en fråga", "app.components.formBuilder.editWarningModal.exportYouResponses2": "exportera dina svar.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Varning: Du kan förlora svarsdata för alltid. <PERSON><PERSON> du fortsätter,", "app.components.formBuilder.editWarningModal.noCancel": "<PERSON><PERSON>, av<PERSON><PERSON><PERSON>", "app.components.formBuilder.editWarningModal.title4": "Redigera live-undersö<PERSON>ning", "app.components.formBuilder.editWarningModal.yesContinue": "Ja, fortsätt", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "inställningar för åtkomsträttigheter för denna under<PERSON>", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "\"Demografiska fält i undersökningsformuläret\" är aktiverat. När undersökningsformuläret visas kommer alla konfigurerade demografiska frågor att läggas till på en ny sida omedelbart innan undersökningen avslutas. Dessa frågor kan ändras på {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "inställningar för å<PERSON>komsträttigheter för denna fas.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Undersökningens respondenter kommer inte att behöva registrera sig eller logga in för att skicka in svar, vilket kan leda till dubbla inlämningar", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "Genom att hoppa över registrerings-/inloggningssteget accepterar du att inte samla in demografisk information om respondenterna, vilket kan påverka dina dataanalysmöjligheter", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "Denna undersökning är inställd på att tillåta åtkomst för \"Vem som helst\" under flike<PERSON> Åtkomsträttigheter.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "Om du vill ändra detta kan du göra det på {accessRightsSettingsLink}", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Du ställer följande demografiska frågor till respondenterna i undersökningen genom registrerings-/inloggningssteget.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "<PERSON><PERSON><PERSON> att effektivisera insamlingen av demografisk information och säkerställa att den integreras i din användardatabas rekommenderar vi att du införlivar alla demografiska frågor direkt i registrerings-/inloggningsprocessen. För att göra detta, använd {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Be användare att följa områden eller ämnen", "app.components.onboarding.followHelperText": "Detta aktiverar ett steg i registreringsprocessen där användare kommer att kunna följa områden eller ämnen som du väljer nedan", "app.components.onboarding.followPreferences": "<PERSON><PERSON><PERSON><PERSON>", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} inom planen, {noOfAdditionalSeats} ytterligare", "app.components.seatsWithinPlan.seatsWithinPlanText": "Sittplatser inom ramen för planen", "app.containers.Admin.Campaigns.campaignFrom": "Frå<PERSON>:", "app.containers.Admin.Campaigns.campaignTo": "Till:", "app.containers.Admin.Campaigns.customEmails": "Anpassade e-postmeddelanden", "app.containers.Admin.Campaigns.customEmailsDescription": "Skicka ut anpassade e-postmeddelanden och kontrollera statistiken.", "app.containers.Admin.Campaigns.noAccess": "Det verkar som att du inte har åtkomst till e-postavsnittet", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Automatiserade e-postmeddelanden", "app.containers.Admin.Insights.tabReports": "Rapporter", "app.containers.Admin.Invitations.a11y_removeInvite": "Ta bort inbjudan", "app.containers.Admin.Invitations.addToGroupLabel": "Lägg till de här personerna i specifika manuella användargrupper", "app.containers.Admin.Invitations.adminLabel1": "Ge administratörsrättigheter till inbjudna", "app.containers.Admin.Invitations.adminLabelTooltip": "<PERSON><PERSON>r du väljer det här alternativet får personerna du bjuder in åtkomst till alla dina plattformsinställningar.", "app.containers.Admin.Invitations.configureInvitations": "3. <PERSON><PERSON><PERSON><PERSON><PERSON> in<PERSON>", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Det finns inga inbjudningar som matchar din sökning", "app.containers.Admin.Invitations.deleteInvite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Är du säker på att du vill ta bort den här inbjudan?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Om du avbryter en inbjudan kan du skicka en inbjudan till den här personen igen.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. <PERSON><PERSON><PERSON> ner och fyll i mallen", "app.containers.Admin.Invitations.downloadTemplate": "Ladda ner mall", "app.containers.Admin.Invitations.email": "E-post", "app.containers.Admin.Invitations.emailListLabel": "Ange manuellt e-postadresserna till personerna du vill bjuda in. Avgränsa varje adress med ett kommatecken.", "app.containers.Admin.Invitations.exportInvites": "Exportera alla inbjudningar", "app.containers.Admin.Invitations.fileRequirements": "Viktigt! För att inbjudningarna ska skickas korrekt kan ingen kolumn tas bort från importmallen. Lämna oanvända kolumner tomma.", "app.containers.Admin.Invitations.filetypeError": "Felaktig filtyp. Endast XLSX-filer stöds.", "app.containers.Admin.Invitations.groupsPlaceholder": "Ingen grupp har valts", "app.containers.Admin.Invitations.helmetDescription": "Bjud in användare till plattformen", "app.containers.Admin.Invitations.helmetTitle": "Inbjudningsdashboard för administratörer", "app.containers.Admin.Invitations.importOptionsInfo": "De här alternativen kommer endast att beaktas när de inte är definierade i Excel-filen.\n      Besök {supportPageLink} för mer information.", "app.containers.Admin.Invitations.importTab": "Importera e-postadresser", "app.containers.Admin.Invitations.invitationExpirationWarning": "<PERSON>änk på att inbjudningar förfaller efter 30 dagar. Efter denna period kan du fortfarande skicka dem igen.", "app.containers.Admin.Invitations.invitationOptions": "Inbjudningsalternativ", "app.containers.Admin.Invitations.invitationSubtitle": "Bjud när som helst in människor till plattformen. De får ett neutralt inbjudningsmejl med din logotyp, där de ombes registrera sig på plattformen.", "app.containers.Admin.Invitations.invitePeople": "Bjud in personer via e-post", "app.containers.Admin.Invitations.inviteStatus": "Status", "app.containers.Admin.Invitations.inviteStatusAccepted": "Accepterad", "app.containers.Admin.Invitations.inviteStatusPending": "Väntande", "app.containers.Admin.Invitations.inviteTextLabel": "Om du vill kan du skriva ett meddelande som kommer att läggas till i inbjudningsmejlet.", "app.containers.Admin.Invitations.invitedSince": "Inbjuden", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Välj språk för inbjudan", "app.containers.Admin.Invitations.moderatorLabel": "Ge de här personerna projektledningsrättigheter", "app.containers.Admin.Invitations.moderatorLabelTooltip": "<PERSON>är du väljer det här alternativet kommer de inbjudna att tilldelas projektledarrättigheter för de valda projekten. Mer information om projektledarrättigheter finns här {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "<PERSON>är", "app.containers.Admin.Invitations.name": "<PERSON><PERSON>", "app.containers.Admin.Invitations.processing": "Skickar ut inbjudningar. Vänta...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Inget projekt/inga projekt har valts", "app.containers.Admin.Invitations.save": "Skicka ut inbjudningar", "app.containers.Admin.Invitations.saveErrorMessage": "<PERSON>tt eller flera fel uppstod och inbjudningarna skickades inte ut. Korrigera felet/felen nedan och försök igen.", "app.containers.Admin.Invitations.saveSuccess": "Sparad!", "app.containers.Admin.Invitations.saveSuccessMessage": "Inbjudan har skickats ut.", "app.containers.Admin.Invitations.supportPage": "supportsida", "app.containers.Admin.Invitations.supportPageLinkText": "Besök <PERSON>", "app.containers.Admin.Invitations.tabAllInvitations": "<PERSON>a <PERSON>", "app.containers.Admin.Invitations.tabInviteUsers": "Bjud in användare", "app.containers.Admin.Invitations.textTab": "<PERSON>e e-postadresser manuellt", "app.containers.Admin.Invitations.unknownError": "Något gick fel. Försök igen senare.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. <PERSON><PERSON><PERSON> upp din färdiga mallfil", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} om du vill ha mer information om alla kolumner som stöds i importmallen.", "app.containers.Admin.Moderation.all": "Allt", "app.containers.Admin.Moderation.belongsTo": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.collapse": "se mindre", "app.containers.Admin.Moderation.comment": "Kommentar", "app.containers.Admin.Moderation.commentDeletionCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.confirmCommentDeletion": "Är du säker på att du vill ta bort den här kommentaren? Detta är permanent och kan inte ångras.", "app.containers.Admin.Moderation.content": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.date": "Datum", "app.containers.Admin.Moderation.deleteComment": "Ta bort kommentar", "app.containers.Admin.Moderation.goToComment": "Öppna den här kommentaren på en ny flik", "app.containers.Admin.Moderation.goToPost": "Ö<PERSON>na det här inlägget på en ny flik", "app.containers.Admin.Moderation.goToProposal": "Öppna det här förslaget på en ny flik", "app.containers.Admin.Moderation.markFlagsError": "Det gick inte att markera objekt. Försök igen.", "app.containers.Admin.Moderation.markNotSeen": "Markera {selectedItemsCount, plural, one {# objekt} other {# objekt}} som ej visade", "app.containers.Admin.Moderation.markSeen": "Markera {selectedItemsCount, plural, one {# objekt} other {# objekt}} som visade", "app.containers.Admin.Moderation.moderationsTooltip": "<PERSON>å den här sidan kan du snabbt kontrollera alla nya inlägg som har publicerats på din plattform, inklusive idéer och kommentarer. Du kan markera inlägg som \"sedda\" så att andra vet vad som fortfarande behöver bearbetas.", "app.containers.Admin.Moderation.noUnviewedItems": "Det finns inga ej visade objekt", "app.containers.Admin.Moderation.noViewedItems": "Det finns inga visade objekt", "app.containers.Admin.Moderation.pageTitle1": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.post": "Inlägg", "app.containers.Admin.Moderation.profanityBlockerSetting": "Svordomsblockerare", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Blockera inlägg som innehåller de vanligast rapporterade stötande orden.", "app.containers.Admin.Moderation.project": "Projekt", "app.containers.Admin.Moderation.read": "Visat", "app.containers.Admin.Moderation.readMore": "<PERSON><PERSON><PERSON>r", "app.containers.Admin.Moderation.removeFlagsError": "Det gick inte att ta bort varning(ar). Försök igen.", "app.containers.Admin.Moderation.rowsPerPage": "Rader per sida", "app.containers.Admin.Moderation.settings": "Inställningar", "app.containers.Admin.Moderation.settingsSavingError": "Det gick inte att spara. Försök ändra inställningen igen.", "app.containers.Admin.Moderation.show": "Visa", "app.containers.Admin.Moderation.status": "Status", "app.containers.Admin.Moderation.successfulUpdateSettings": "Inställningarna har uppdaterats framgångsrikt.", "app.containers.Admin.Moderation.type": "<PERSON><PERSON>", "app.containers.Admin.Moderation.unread": "<PERSON><PERSON> visat", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Den här sidan består av följande avsnitt. Du kan aktivera/inaktivera dem och redigera dem vid behov.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Avsnitt", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "Visa sida", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Visas inte på sidan", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Visas på sidan", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Bilagor", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Lägg till filer (max. 50 MB) som kommer att vara tillgängliga för nerladdning från sidan.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Nedre informationsavsnitt", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "L<PERSON>gg till ditt eget innehåll i det anpassningsbara avsnittet längst ner på sidan.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Rediger<PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Lista över evenemang", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Visa evenemang som är relaterade till projekten.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Anpassa sidans banderollbild och -text.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Projektlista", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Visa projekten baserat på dina sidinställningar. Du kan förhandsgranska projekten som visas.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Övre informationsavsnitt", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Lägg till ditt eget innehåll i det anpassningsbara avsnittet högst upp på sidan.", "app.containers.Admin.PagesAndMenu.addButton": "Lägg till i navigeringsfältet", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Namn i navigeringsfältet", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "<PERSON>r du säker på att du vill ta bort den här sidan? Du kan inte ångra den här åtgärden.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Ge en titel för alla språk", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Andra tillgängliga sidor", "app.containers.Admin.PagesAndMenu.components.savePage": "Spara sida", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "<PERSON><PERSON> har sparats", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Bilagor (max 50 MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Lyckades", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Det gick inte att spara bilagor", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Filer bör inte vara större än 50 MB. Tillagda filer visas längst ner på den här sidan", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "<PERSON>rade bilagor", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Bilagor | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Bilagor", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Salvați și activați atașamentele", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "<PERSON>ra bilagor", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "<PERSON><PERSON> inn<PERSON>ll för alla språk", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Lyckades", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Det gick inte att spara det nedre informationsavsnittet", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Det nedre informationsavsnittet har sparats", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Nedre informationsavsnitt", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Salvați și activați secțiunea de informații de jos", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Spara nedre informationsavsnitt", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Att skapa anpassade sidor ingår inte i din nuvarande licens. Kontakta din GovSuccess Manager för att lära dig mer om det.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "<PERSON><PERSON><PERSON><PERSON> minst ett ämne", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Lyckades", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "<PERSON><PERSON>(n)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Att visa projekt efter tagg eller område är inte en del av din nuvarande licens. Kontakta din GovSuccess Manager för att lära dig mer om det.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "<PERSON><PERSON>a anpassad sida", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Länkade projekt", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Välj vilka projekt och relaterade evenemang som ska visas på sidan.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "<PERSON><PERSON> har ska<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "<PERSON><PERSON> har sparats", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "<PERSON><PERSON><PERSON> sida har sparats", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Titel i navigeringsfältet", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Ska<PERSON> anpassad sida | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Skapa anpassad sida", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "Inga", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Sidinställningar", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Spara anpassad sida", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "<PERSON><PERSON><PERSON><PERSON> ett område", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "<PERSON>t o<PERSON>rå<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Valda ämnen", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "En slug får bara innehålla små bokstäver (a–z), si<PERSON><PERSON><PERSON> (0–9) och bindestreck (-). Det första och sista tecknet får inte vara ett bindestreck. Två bindestreck i rad (--) får inte användas.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Du måste ange en slug", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Titel", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Ange en titel på alla språk", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "<PERSON>e en titel", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Visa anpassad sida", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Redigera anpassad sida | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Rediger<PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "<PERSON><PERSON><PERSON> enfasprojekt visas ingen tidslinje på projektsidan om slutdatumet är tomt och beskrivningen inte är ifylld.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "Inga tillgängliga projekt baserat på din {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Det här projektet har inget filter för ämnen eller områden – därför kommer inga projekt att visas.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Projektlista | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "sidinställningar", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Projektlista", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Följande projekt kommer att visas på den här sidan baserat på dina {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "STANDARD", "app.containers.Admin.PagesAndMenu.deleteButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.editButton": "Rediger<PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Lyckades", "app.containers.Admin.PagesAndMenu.heroBannerError": "Det gick inte att spara hero-banderollen", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Hero-band<PERSON><PERSON> har sparats", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Spara hero-banderoll", "app.containers.Admin.PagesAndMenu.homeTitle": "Start", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "<PERSON><PERSON> inn<PERSON>ll för minst ett språk", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Du kan bara lägga till upp till 5 objekt i navigeringsfältet", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "<PERSON>or och meny | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Ta bort från navigeringsfältet", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Salvați și activați bannerul eroului", "app.containers.Admin.PagesAndMenu.title": "Sidor och meny", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Lyckades", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.topInfoError": "Det gick inte att spara det övre informationsavsnittet", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Det övre informationsavsnittet har sparats", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Övre informationsavsnitt | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Övre informationsavsnitt", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Salvați și activați secțiunea de informații de sus", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Spara övre informationsavsnitt", "app.containers.Admin.PagesAndMenu.viewButton": "Vy", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "<PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Gemenskap", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Indikat<PERSON>r för inkludering på högsta nivå", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "I följande avsnitt beskrivs indikatorer för inkludering som belyser våra framsteg mot att skapa en mer inkluderande och representativ plattform för deltagande.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "deltagare", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Indikatorer för <PERSON>gande på högsta nivå", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "I följande avsnitt beskrivs de viktigaste indikatorerna för deltagande för det valda tidsintervallet, vilket ger en översikt över engagemangstrender och resultatmått.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projekt", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "publicerade projekt", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Plattformsrapport", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "<PERSON><PERSON> proje<PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "I följande avsnitt ges en översikt över offentligt synliga projekt som överlappar med det valda tidsintervallet, de mest använda metoderna i dessa projekt och mätvärden för det totala deltagandet.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Tidslinje för registre<PERSON>", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Blockerade användare", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Hantera blockerade användare.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Ta bort grupp", "app.containers.Admin.Users.GroupsHeader.editGroup": "<PERSON><PERSON>a grupp", "app.containers.Admin.Users.GroupsPanel.admins": "Administratörer", "app.containers.Admin.Users.GroupsPanel.allUsers": "Registrerade användare", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Grupper", "app.containers.Admin.Users.GroupsPanel.managers": "Projektledare", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "<PERSON><PERSON><PERSON> objekt", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Få en översikt över alla personer och organisationer som registrerat sig på plattformen. Lägg till ett urval av användare i Manuella grupper eller Smarta grupper.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Inbjudan väntar", "app.containers.Admin.Users.admin": "Admin", "app.containers.Admin.Users.assign": "<PERSON><PERSON>", "app.containers.Admin.Users.assignedItems": "<PERSON><PERSON><PERSON> poster för {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "<PERSON><PERSON><PERSON> en extra plats", "app.containers.Admin.Users.changeUserRights": "<PERSON><PERSON>", "app.containers.Admin.Users.confirm": "Bekräfta", "app.containers.Admin.Users.confirmAdminQuestion": "Är du säker på att du vill ge {name} administratörsrättigheter för plattformen?", "app.containers.Admin.Users.confirmNormalUserQuestion": "<PERSON>r du säker på att du vill ställa in {name} som en normal användare?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "<PERSON>r du säker på att du vill ange {name} som en normal användare? Observera att de kommer att förlora chefsrättigheterna för alla projekt och mappar som de har tilldelats vid bekräftelsen.", "app.containers.Admin.Users.deleteUser": "Ta bort användare", "app.containers.Admin.Users.email": "E-post", "app.containers.Admin.Users.folder": "Mapp", "app.containers.Admin.Users.folderManager": "Mapphanterare", "app.containers.Admin.Users.helmetDescription": "Användarlista för administratörer", "app.containers.Admin.Users.helmetTitle": "Admin – användardashboard", "app.containers.Admin.Users.inviteUsers": "Bjud in användare", "app.containers.Admin.Users.joined": "Ans<PERSON>en", "app.containers.Admin.Users.lastActive": "Senast aktiv", "app.containers.Admin.Users.name": "<PERSON><PERSON>", "app.containers.Admin.Users.noAssignedItems": "<PERSON>ga tilldelade objekt", "app.containers.Admin.Users.options": "Alternativ", "app.containers.Admin.Users.permissionToBuy": "Om du vill ge {name} administratörsrättigheter måste du köpa ytterligare 1 plats.", "app.containers.Admin.Users.platformAdmin": "Plattformsadministratör", "app.containers.Admin.Users.projectManager": "Projektledare", "app.containers.Admin.Users.reachedLimitMessage": "Du har nått gränsen för antalet platser inom din plan, 1 extra plats för {name} kommer att läggas till.", "app.containers.Admin.Users.registeredUser": "Registrerad användare", "app.containers.Admin.Users.remove": "<PERSON> bort", "app.containers.Admin.Users.removeModeratorFrom": "Användaren är moderator för den mapp som detta projekt tillhör. Ta bort uppdraget från \"{folderTitle}\" istället.", "app.containers.Admin.Users.role": "Roll", "app.containers.Admin.Users.seeProfile": "Se profil", "app.containers.Admin.Users.selectPublications": "Välj projekt eller mappar", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Skriv för att söka", "app.containers.Admin.Users.setAsAdmin": "<PERSON><PERSON><PERSON> in som administratör", "app.containers.Admin.Users.setAsNormalUser": "<PERSON><PERSON><PERSON> in som normal användare", "app.containers.Admin.Users.setAsProjectModerator": "Gör till projektledare", "app.containers.Admin.Users.setUserAsProjectModerator": "<PERSON><PERSON> {name} rollen som projektledare", "app.containers.Admin.Users.userBlockModal.allDone": "<PERSON>t är klart", "app.containers.Admin.Users.userBlockModal.blockAction": "Blockera användare", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Den här användarens innehåll kommer inte att tas bort genom denna <PERSON>t<PERSON>. Glöm inte att moderera deras innehåll om det behövs.", "app.containers.Admin.Users.userBlockModal.blocked": "Blockerad", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Den här användaren har blockerats sedan {from}. Förbudet gäller till och med {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "<PERSON>r du säker på att du vill avblockera {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} är blockerad tills {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 dag} other {{numberOfDays} dagar}}", "app.containers.Admin.Users.userBlockModal.header": "Blockera användare", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Mo<PERSON>ring", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "<PERSON><PERSON> kommer att meddelas den blockerade användaren.", "app.containers.Admin.Users.userBlockModal.subtitle1": "Den utvalda användaren kommer inte att kunna logga in på plattformen för {daysBlocked}. Om du vill ändra detta kan du häva blockeringen från listan över blockerade användare.", "app.containers.Admin.Users.userBlockModal.unblockAction": "A<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON>, jag vill avblockera den här användaren", "app.containers.Admin.Users.userDeletionConfirmation": "Vill du ta bort den här användaren permanent?", "app.containers.Admin.Users.userDeletionFailed": "<PERSON>tt fel uppstod när den här användaren skulle tas bort, fö<PERSON><PERSON><PERSON> igen.", "app.containers.Admin.Users.userDeletionProposalVotes": "Detta kommer också att radera alla röster från denna användare på förslag som fortfarande är öppna för omröstning.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.userInsights": "Användarinsikter", "app.containers.Admin.Users.youCantDeleteYourself": "Du kan inte ta bort ditt eget konto via användaradminsidan", "app.containers.Admin.Users.youCantUnadminYourself": "Du kan inte lämna din roll som administratör nu", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Gemenskapens övervakare", "app.containers.Admin.communityMonitor.healthScore": "Hälsoindex", "app.containers.Admin.communityMonitor.healthScoreDescription": "Denna poäng är genomsnittet av alla frå<PERSON> på <PERSON>kalan som deltagarna har besvarat under den valda perioden.", "app.containers.Admin.communityMonitor.lastQuarter": "sista k<PERSON>t", "app.containers.Admin.communityMonitor.liveMonitor": "Live-monitor", "app.containers.Admin.communityMonitor.noResults": "Inga resultat för denna period.", "app.containers.Admin.communityMonitor.noSurveyResponses": "Inga enkätsvar", "app.containers.Admin.communityMonitor.participants": "Deltagare", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Rapporter", "app.containers.Admin.communityMonitor.settings": "Inställningar", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Community Monitor Survey tar nu emot bidrag.", "app.containers.Admin.communityMonitor.settings.accessRights2": "Rättigheter för <PERSON>", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "När en användare har registrerat sig för ett evenemang, skickat in en röst eller återvänt till en projektsida efter att ha skickat in en enkät.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Community Monitor Managers kan komma åt och hantera alla inställningar och data för Community Monitor.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Chefer kan redigera Community Monitor-undersökningen och behörigheter, se svarsdata och skapa rapporter.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "Standardvärdet för frekvensen är 100%.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Popup-frekvens (0 till 100)", "app.containers.Admin.communityMonitor.settings.management2": "Förvaltning", "app.containers.Admin.communityMonitor.settings.popup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.popupDescription3": "En popup visas regelbundet för användare som uppmuntrar dem att fylla i Community Monitor Survey. Du kan justera frekvensen som avgör hur många procent av användarna som slumpmässigt kommer att se popup-fönstret när villkoren nedan är uppfyllda.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Inställningar för popup-fönster", "app.containers.Admin.communityMonitor.settings.preview": "Förhandsgranskning", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "Användaren har inte redan fyllt i enkäten under de senaste 3 månaderna.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "Användaren har inte redan sett popup-fönstret under de senaste 3 månaderna.", "app.containers.Admin.communityMonitor.settings.save": "Spara", "app.containers.Admin.communityMonitor.settings.saved": "<PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.settings": "Inställningar", "app.containers.Admin.communityMonitor.settings.survey2": "Undersö<PERSON>ning", "app.containers.Admin.communityMonitor.settings.surveySettings3": "Allmänna inställningar", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Vid laddning av starts<PERSON>n eller en anpassad sida.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonymisera alla användardata", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "All input från användarna i undersökningen kommer att anonymiseras innan den registreras", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Användarna måste fortfarande uppfylla deltagarkraven under \"Åtkomsträttigheter\". Användarprofildata kommer inte att finnas tillgängliga i exporten av undersökningsdata.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "Under vilka förhållanden kan popup-fönstret visas för användare?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Vilka är cheferna?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Totalt antal enkätsvar", "app.containers.Admin.communityMonitor.upsell.aiSummary": "Sammanfattning av AI", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Aktivera Community Monitor", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Den här funktionen ingår inte i din nuvarande plan. Prata med din Government Success Manager el<PERSON> administra<PERSON><PERSON><PERSON> för att låsa upp den.", "app.containers.Admin.communityMonitor.upsell.healthScore": "<PERSON><PERSON><PERSON> för hä<PERSON>", "app.containers.Admin.communityMonitor.upsell.learnMore": "<PERSON><PERSON><PERSON> mer om detta", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Poäng över tid", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "Community Monitor hj<PERSON><PERSON><PERSON> dig att ligga steget före genom att kontinuerligt följa upp invånarnas förtroende, nöjdhet med tjänster och samhällsliv.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Få tydliga resultat, kraftfulla citat och en kvartalsrapport som du kan dela med kollegor eller förtroendevalda.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Lättlästa poäng som utvecklas över tid", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Viktiga citat från in<PERSON>, sammanfattade av AI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "<PERSON><PERSON><PERSON> som är skräddarsydda för din stad", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Invånare rekryteras slumpmässigt på plattformen", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Kvartalsvisa PDF-rapporter, redo att delas", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "Förstå hur din grupp mår innan problemen växer", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "R<PERSON>k<PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "<PERSON>kriv<PERSON><PERSON> eller annat", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Mobil", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "<PERSON><PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Enhetstyper", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Enhetstyp", "app.containers.Admin.earlyAccessLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.earlyAccessLabelExplanation": "Detta är en nyutvecklad funktion som finns tillgänglig i Early Access.", "app.containers.Admin.emails.addCampaign": "Skapa e-post", "app.containers.Admin.emails.addCampaignTitle": "Skapa ett nytt e-postmeddelande", "app.containers.Admin.emails.allParticipantsInProject": "Alla deltagare i projektet", "app.containers.Admin.emails.allUsers": "Registrerade användare", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Automatiserade e-postmeddelanden skickas automatiskt och utlöses av en användares åtgärder. Du kan stänga av vissa av dem för alla användare av din plattform. De andra automatiserade e-postmeddelandena kan inte stängas av eftersom de krävs för att din plattform ska fungera korrekt.", "app.containers.Admin.emails.automatedEmails": "Automatiserad e-post", "app.containers.Admin.emails.automatedEmailsDigest": "E-postmeddelandet skickas endast om det finns innehåll", "app.containers.Admin.emails.automatedEmailsRecipients": "Användare som kommer att få detta e-postmeddelande", "app.containers.Admin.emails.automatedEmailsTriggers": "Händelse som utlöser detta e-postmeddelande", "app.containers.Admin.emails.changeRecipientsButton": "<PERSON><PERSON> mott<PERSON>", "app.containers.Admin.emails.clickOnButtonForExamples": "Klicka på knappen nedan för att se exempel på detta e-postmeddelande på vår supportsida.", "app.containers.Admin.emails.confirmSendHeader": "Vill du skicka e-post till alla användare?", "app.containers.Admin.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.draft": "Utkast", "app.containers.Admin.emails.editButtonLabel": "Rediger<PERSON>", "app.containers.Admin.emails.editCampaignTitle": "<PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.Admin.emails.editDisabledTooltip2": "Kommer inom kort: Det här e-postmeddelandet kan inte redigeras för närvarande.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Knapptext", "app.containers.Admin.emails.editRegion_intro_multiloc": "Inledning", "app.containers.Admin.emails.editRegion_subject_multiloc": "Ämne", "app.containers.Admin.emails.editRegion_title_multiloc": "Titel", "app.containers.Admin.emails.emailCreated": "E-post har framgångsrikt skapats i utkast", "app.containers.Admin.emails.emailUpdated": "E-post<PERSON><PERSON>en har uppdaterats", "app.containers.Admin.emails.emptyCampaignsDescription": "<PERSON>å enkelt kontakt med dina deltagare genom att skicka e-post till dem. Välj vem du vill kontakta och spåra ditt engagemang.", "app.containers.Admin.emails.emptyCampaignsHeader": "<PERSON><PERSON>a ditt första e-postmeddelande", "app.containers.Admin.emails.failed": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldBody": "Meddelande", "app.containers.Admin.emails.fieldBodyError": "Ange ett e-postmeddelande", "app.containers.Admin.emails.fieldGroupContent": "Innehåll i e-post", "app.containers.Admin.emails.fieldReplyTo": "<PERSON><PERSON> ska skickas till", "app.containers.Admin.emails.fieldReplyToEmailError": "Ange en e-postadress i rätt format, t.ex. <EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "<PERSON><PERSON> en e-postadress", "app.containers.Admin.emails.fieldReplyToTooltip": "Du kan välja vart du vill skicka svar på ditt e-postmeddelande.", "app.containers.Admin.emails.fieldSender": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldSenderError": "Ange en avsändare av e-postmeddelandet", "app.containers.Admin.emails.fieldSenderTooltip": "Du kan bestämma vem mottagarna ska se som avsändare av e-postmeddelandet.", "app.containers.Admin.emails.fieldSubject": "Ämne", "app.containers.Admin.emails.fieldSubjectError": "Ange ett ämne för e-postmeddelandet", "app.containers.Admin.emails.fieldSubjectTooltip": "Det kommer att visas i e-postmeddelandets ämnesrad och i användarens översikt för inkorgen. Se till att den är tydlig och engagerande.", "app.containers.Admin.emails.fieldTo": "<PERSON>", "app.containers.Admin.emails.fieldToTooltip": "Du kan välja vilka användargrupper som ska ta emot ditt e-postmeddelande", "app.containers.Admin.emails.formSave": "Spara som utkast", "app.containers.Admin.emails.formSaveAsDraft": "Spara som utkast", "app.containers.Admin.emails.from": "Frå<PERSON>:", "app.containers.Admin.emails.groups": "Grupper", "app.containers.Admin.emails.helmetDescription": "Skicka ut manuella e-postmeddelanden till användargrupper och aktivera automatiserade kampanjer", "app.containers.Admin.emails.nameVariablesInfo2": "Du kan vända dig direkt till mottagarna med hjälp av variablerna {firstName} {lastName}. T.ex. \"Kära {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "En förhandsvisning av e-postmeddelandet har skickats till din e-postadress", "app.containers.Admin.emails.previewTitle": "Förhandsvisning", "app.containers.Admin.emails.regionMultilocError": "Vänligen ange ett värde för alla språk", "app.containers.Admin.emails.seeEmailHereText": "Så snart ett e-postmeddelande av denna typ skickas kommer du att kunna kontrollera det här.", "app.containers.Admin.emails.send": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sendNowButton": "Skicka nu", "app.containers.Admin.emails.sendTestEmailButton": "<PERSON><PERSON><PERSON> ett testmejl till mig", "app.containers.Admin.emails.sendTestEmailTooltip2": "<PERSON><PERSON>r du klickar på den här länken skickas ett testmejl till endast din e-postadress. På så sätt kan du kontrollera hur e-postmeddelandet ser ut i \"verkligheten\".", "app.containers.Admin.emails.senderRecipients": "Avsändare och mottagare", "app.containers.Admin.emails.sending": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sent": "Skickades", "app.containers.Admin.emails.sentToUsers": "Detta är e-postmeddelanden som skickas till användare", "app.containers.Admin.emails.subject": "Ämne:", "app.containers.Admin.emails.supportButtonLabel": "Se exempel på vår supportsida", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "Till:", "app.containers.Admin.emails.toAllUsers": "Vill du skicka det här e-postmeddelandet till alla registrerade användare?", "app.containers.Admin.emails.viewExample": "Visa", "app.containers.Admin.ideas.import": "Importera", "app.containers.Admin.inspirationHub.AllProjects": "Alla projekt", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Gemenskapens övervakare", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Dokumentanteckning", "app.containers.Admin.inspirationHub.ExternalSurvey": "Extern undersökning", "app.containers.Admin.inspirationHub.Filters.Country": "Land", "app.containers.Admin.inspirationHub.Filters.Method": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Search": "<PERSON>ö<PERSON>", "app.containers.Admin.inspirationHub.Filters.Topic": "Ämne", "app.containers.Admin.inspirationHub.Filters.population": "Befolkning", "app.containers.Admin.inspirationHub.Highlighted": "Framhävd", "app.containers.Admin.inspirationHub.Ideation": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Information": "Information om", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Vä<PERSON>j ett land för att se projekt som du har pinnat", "app.containers.Admin.inspirationHub.PinnedProjects.country": "Land", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "Inga uppmärkta projekt hittades för det här landet. Byt land för att se pinnade projekt för andra länder", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Byt land för att se fler projekt", "app.containers.Admin.inspirationHub.Poll": "Opinionsundersökning", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "<PERSON><PERSON><PERSON> för alla", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "<PERSON><PERSON><PERSON> mer...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Fas {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "<PERSON>m du inte vill att ditt projekt ska ingå i inspirationshubben kan du kontakta din GovSuccess-chef.", "app.containers.Admin.inspirationHub.Proposals": "Förslag till beslut", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Sortera efter", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Deltagare (lägst först)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Deltagare (högst först)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Startdatum (äldst först)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Startdatum (nyaste först)", "app.containers.Admin.inspirationHub.Survey": "Undersö<PERSON>ning", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Sammanställd lista över de bästa projekten runt om i världen.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Prata med och lär dig av andra u<PERSON>.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filtrera efter metod, stadsstorlek och land.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Aktivera Inspiration Hub", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Den här funktionen ingår inte i din nuvarande plan. Prata med din Government Success Manager el<PERSON> administra<PERSON><PERSON><PERSON> för att låsa upp den.", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "https://support.govocal.com/en/articles/11093736-using-the-inspiration-hub", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "<PERSON><PERSON><PERSON> mer om detta", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "Inspiration <PERSON><PERSON> kopplar dig till ett kurerat flöde av exceptionella deltagandeprojekt på Go Vocal-plattformar över hela världen. <PERSON><PERSON>r dig hur andra städer driver framgångsrika projekt och prata med andra <PERSON>.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Gå med i ett nätverk av banbrytande demokratiutövare", "app.containers.Admin.inspirationHub.Volunteering": "Volontärarbete", "app.containers.Admin.inspirationHub.Voting": "Omr<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.commonGround": "Gemensam jord", "app.containers.Admin.inspirationHub.filters": "Filter", "app.containers.Admin.inspirationHub.resetFilters": "<PERSON><PERSON>tä<PERSON> filter", "app.containers.Admin.inspirationHub.seemsLike": "Det verkar som om det inte finns några fler projekt. Försök att ändra {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "<PERSON><PERSON><PERSON> kampanj<PERSON>", "app.containers.Admin.messaging.automated.variablesToolTip": "Du kan använda följande variabler i ditt meddelande:", "app.containers.Admin.messaging.helmetTitle": "Meddelanden", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "<PERSON>na widget visar varje användares projekt <b>baserat på deras preferenser</b>. Detta inkluderar projekt som de följer, liksom projekt där de följer input och projekt som är relaterade till ämnen eller områden som de är intresserade av.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "Denna widget visas endast för användaren om det finns projekt där de kan delta. Om du ser det här meddelandet betyder det att du (administratören) inte kan delta i några projekt just nu. Detta meddelande kommer inte att synas på den riktiga hemsidan.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Öppet för deltagande", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "<PERSON>na widget visar projekt där användaren för närvarande kan <b>vidta en åtgärd för att delta.</b>", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Titel", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "<PERSON>na widget kommer endast att visas för användaren om det finns projekt som är relevanta för dem baserat på deras följinställningar. Om du ser det här meddelandet betyder det att du (administratören) inte följer något för tillfället. Detta meddelande kommer inte att visas på den riktiga hemsidan.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "Följda objekt", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Arkiverad", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Filtrera efter", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Avslutad och arkiverad", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "Inga uppgifter tillgängliga", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "Den här widgeten visar <b>projekt</b> som <b>är avslutade och/eller arkiverade</b>. \"Avslutade\" inkluderar även projekt som befinner sig i den sista fasen och där den sista fasen är en rapport.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Avslutade projekt", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "Du sa, vi gjorde...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "<PERSON><PERSON> ett namn för alla språk", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "Projektet får inte vara tomt", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Projekt", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Resulterande URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "Spara", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Lägg till projekt", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "I navigeringsfältet visas endast projekt som användare har tillgång till.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "<PERSON> här widgeten visas bara på startsidan när Community Monitor tar emot svar.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Gemenskapens övervakare", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Beskrivning", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Titel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Vikti<PERSON>:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Exempel på en fråga i en sentimentundersökning", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "Inget slutdatum", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "<PERSON>ck på escape för att hoppa över karusellen", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projekt och mappar (äldre)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Projektets titel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} arb<PERSON>r för n<PERSON> med", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Knapptext", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Delta nu!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Beskrivning", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "mapp", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Vänligen välj ett projekt eller en mapp", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Välj projekt eller mapp", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Visa avatarer", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Spotlight", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Titel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "Start på {days} dagar", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Start på {weeks} veckor", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} dagar sedan", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} veckor sedan", "app.containers.Admin.project.Campaigns.campaignFrom": "Frå<PERSON>:", "app.containers.Admin.project.Campaigns.campaignTo": "Till:", "app.containers.Admin.project.Campaigns.customEmails": "Anpassade e-postmeddelanden", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Skicka ut anpassade e-postmeddelanden och kontrollera statistik.", "app.containers.Admin.project.Campaigns.noAccess": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, men det verkar som om du inte har tillgång till e-postavdelningen", "app.containers.Admin.project.emails.addCampaign": "Skapa e-post", "app.containers.Admin.project.emails.addCampaignTitle": "<PERSON><PERSON>", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Alla {participants} och fö<PERSON><PERSON>e från projektet", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Detta inkluderar registrerade användare som utförde någon åtgärd i projektet. Oregistrerade eller anonymiserade användare ingår inte.", "app.containers.Admin.project.emails.dateSent": "Datum för avsändande", "app.containers.Admin.project.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.draft": "Utkast", "app.containers.Admin.project.emails.editButtonLabel": "Rediger<PERSON>", "app.containers.Admin.project.emails.editCampaignTitle": "<PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.Admin.project.emails.emptyCampaignsDescription": "<PERSON>å enkelt kontakt med dina deltagare genom att skicka e-post till dem. Välj vem du vill kontakta och spåra ditt engagemang.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "<PERSON><PERSON>a ditt första e-postmeddelande", "app.containers.Admin.project.emails.failed": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.fieldBody": "E-postmeddelande", "app.containers.Admin.project.emails.fieldBodyError": "Tillhandahålla ett e-postmeddelande för alla språk", "app.containers.Admin.project.emails.fieldReplyTo": "<PERSON><PERSON><PERSON> ska skickas till", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Ange en e-postadress i rätt format, t.ex. <EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "<PERSON><PERSON> en e-postadress", "app.containers.Admin.project.emails.fieldReplyToTooltip": "Välj vilken e-postadress som ska få direkta svar från användare på din e-post.", "app.containers.Admin.project.emails.fieldSender": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.fieldSenderError": "Ange en avsändare av e-postmeddelandet", "app.containers.Admin.project.emails.fieldSenderTooltip": "Välj vem användarna ska se som avsändare av e-postmeddelandet.", "app.containers.Admin.project.emails.fieldSubject": "Ämne för e-post", "app.containers.Admin.project.emails.fieldSubjectError": "Ange ett ämne för e-postmeddelandet för alla språk", "app.containers.Admin.project.emails.fieldSubjectTooltip": "Detta kommer att visas i ämnesraden i e-postmeddelandet och i användarens inkorgsöversikt. G<PERSON><PERSON> det tydligt och engagerande.", "app.containers.Admin.project.emails.fieldTo": "<PERSON>", "app.containers.Admin.project.emails.formSave": "Spara som utkast", "app.containers.Admin.project.emails.from": "Frå<PERSON>:", "app.containers.Admin.project.emails.helmetDescription": "Skicka ut manuella e-postmeddelanden till projektdeltagarna", "app.containers.Admin.project.emails.infoboxAdminText": "Från fliken Project Messaging kan du bara skicka e-post till alla projektdeltagare.  Om du vill skicka e-post till andra deltagare eller undergrupper av användare går du till fliken {link} .", "app.containers.Admin.project.emails.infoboxLinkText": "Plattform för medd<PERSON>", "app.containers.Admin.project.emails.infoboxModeratorText": "Från fliken Project Messaging kan du bara skicka e-post till alla projektdeltagare. Administratörer kan skicka e-post till andra deltagare eller undergrupper av användare via fliken Platform Messaging.", "app.containers.Admin.project.emails.message": "Meddelande", "app.containers.Admin.project.emails.nameVariablesInfo2": "Du kan vända dig direkt till mottagarna genom att använda variablerna {firstName} {lastName}. T.ex. \"Kära {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "deltagare", "app.containers.Admin.project.emails.previewSentConfirmation": "Ett e-postmeddelande med förhandsgranskning har skickats till din e-postadress", "app.containers.Admin.project.emails.previewTitle": "Förhandsgranskning", "app.containers.Admin.project.emails.projectParticipants": "Deltagare i projektet", "app.containers.Admin.project.emails.recipients": "Mottagare", "app.containers.Admin.project.emails.send": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sendTestEmailButton": "Skicka en förhandsgranskning", "app.containers.Admin.project.emails.sendTestEmailTooltip": "<PERSON><PERSON><PERSON> detta utkast till e-post till den e-postadress som du är inloggad med för att kontrollera hur det ser ut i \"verkligheten\".", "app.containers.Admin.project.emails.senderRecipients": "Avsändare och mottagare", "app.containers.Admin.project.emails.sending": "Sändning", "app.containers.Admin.project.emails.sent": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sentToUsers": "Detta är e-postmeddelanden som skickas till användare", "app.containers.Admin.project.emails.status": "Status", "app.containers.Admin.project.emails.subject": "Ämne:", "app.containers.Admin.project.emails.to": "Till:", "app.containers.Admin.project.messaging.helmetTitle": "Meddelanden", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "Den här bilden ingår i mappkortet – kortet som sammanfattar mappen och som till exempel visas på startsidan. Mer information om rekommenderade bildupplösningar finns i {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Den här bilden visas högst upp på mappsidan. Mer information om rekommenderade bildupplösningar finns i {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "besök vårt supportcenter", "app.containers.Admin.projects.all.askPersonalData3": "Lägg till fält för namn och e-post", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "Aktivera <PERSON>", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "Den här funktionen ingår inte i din nuvarande plan. Prata med din Government Success Manager el<PERSON> administra<PERSON><PERSON><PERSON> för att låsa upp den.", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "<PERSON><PERSON><PERSON> mer om detta", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "Få en visuell överblick över dina projekts tidslinjer i vår kalendervy. Identifiera snabbt vilka projekt och faser som snart startar eller avslutas och som kräver åtgärder.", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "Förstå vad som händer och när", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Alla frågor visas i PDF-filen. Följande stöds dock inte för närvarande för import via FormSync: Bilder, Taggar och Filuppladdning.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Alla frågor visas i PDF-filen. Följande stöds dock inte för närvarande för import via FormSync: Kartfrå<PERSON> (drop pin, draw route och draw area), rank<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, matrisf<PERSON><PERSON><PERSON> och frågor om filuppladdning.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "Slut på formuläret", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Början av formuläret", "app.containers.Admin.projects.all.components.archived": "Arkiverad", "app.containers.Admin.projects.all.components.draft": "Utkast", "app.containers.Admin.projects.all.components.manageButtonLabel": "Hantera", "app.containers.Admin.projects.all.copyProjectButton": "<PERSON><PERSON><PERSON> projekt", "app.containers.Admin.projects.all.copyProjectError": "<PERSON>tt fel uppstod när det här projektet skulle kopiera<PERSON>, förs<PERSON><PERSON> igen senare.", "app.containers.Admin.projects.all.customiseEnd": "Anpassa slutet av formuläret.", "app.containers.Admin.projects.all.customiseStart": "Anpassa början av formuläret.", "app.containers.Admin.projects.all.deleteFolderButton1": "<PERSON> bort mapp", "app.containers.Admin.projects.all.deleteFolderConfirm": "Är du säker på att du vill ta bort den här mappen? Alla projekt i mappen kommer också att tas bort. Du kan inte ångra den här åtgärden.", "app.containers.Admin.projects.all.deleteFolderError": "Det uppstod ett problem när den här mappen skulle tas bort. Försök igen.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Ta bort projekt", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Är du säker på att du vill ta bort det här projektet? Du kan inte ångra den här åtgärden.", "app.containers.Admin.projects.all.deleteProjectError": "<PERSON>tt fel uppstod när det här projektet skulle tas bort, fö<PERSON><PERSON><PERSON> igen senare.", "app.containers.Admin.projects.all.exportAsPDF1": "Ladda ner PDF-formulär", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Du kan kombinera online- och offline-svar. <PERSON><PERSON>r att ladda upp offline-svar går du till fliken \"Input manager\" i det här projektet och klickar på \"Import\".", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Du kan kombinera online- och offline-svar. För att ladda upp offline-svar går du till fliken \"Survey\" i det här projektet och klickar på \"Import\".", "app.containers.Admin.projects.all.logicNotInPDF": "Enkätlogiken kommer inte att återspeglas i den nedladdade PDF-filen. Respondenter som svarar på papper ser alla enkätfrågor.", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "Sök mappar", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "Alla mappar har laddats", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Mapp", "app.containers.Admin.projects.all.new.Folders.Table.managers": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# projekt} other {# projekt}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Projektets startdatum", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Upptäckbarhet", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "Allmänheten", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Mappar", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filtrera efter den aktuella fasens deltagandemetod", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Information om", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "<PERSON><PERSON> för <PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Dokumentanteckning", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Gemensam jord", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "Undersö<PERSON>ning", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "Opinionsundersökning", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Förslag till beslut", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Volontärarbete", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Omr<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "Insamling av data", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Information", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Deltagande stat", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "Avvaktar godkännande", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "Sök projekt", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "I alfabetisk ordning (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "I alfabetisk ordning (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "Chef", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "<PERSON>as som b<PERSON><PERSON><PERSON> eller slutar snart", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc": "Nyligen skapad (ny-gammal)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc": "Nyligen skapad (gammal-ny)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "<PERSON><PERSON><PERSON><PERSON> visad", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Administratörer", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Grupper", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Synlighet", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "Allmänheten", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "Lägg till filter", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "Inga fler filter att lägga till", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Administratörer", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Alla projekt har laddats", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "Vem som helst", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Arkiverad", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON> fas", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d v<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d för att starta", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Upptäckbarhet:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Utkast", "app.containers.Admin.projects.all.new.Projects.Table.end": "Slut", "app.containers.Admin.projects.all.new.Projects.Table.ended": "Avslutad", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "<PERSON>lutar idag", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Grupper", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "Ladda mer…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "Chef", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}mo vä<PERSON>er", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}mo att starta", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Nästa fas:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Fas", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.project": "Projekt", "app.containers.Admin.projects.all.new.Projects.Table.public": "Allmänheten", "app.containers.Admin.projects.all.new.Projects.Table.published": "Publicerad", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "<PERSON><PERSON>a ner för att läsa mer", "app.containers.Admin.projects.all.new.Projects.Table.start": "Start", "app.containers.Admin.projects.all.new.Projects.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Status:", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "Denna kolumn använder cachade deltagaruppgifter. <PERSON><PERSON><PERSON> att se de senaste siffrorna, kolla fliken \"Deltagare\" i projektet.", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Synlighet", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Synlighet:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} grupper", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} chefer", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}y vänster", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y för att starta", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Aktuell fas: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} dagar kvar", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Mapp: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "Ingen aktuell fas", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "Inget slutdatum", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "<PERSON>ga faser", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Fas {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Faserna:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Projekt", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Startdatum: {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "Ordna projekt", "app.containers.Admin.projects.all.new.calendar": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.folders": "Mappar", "app.containers.Admin.projects.all.new.projects": "Projekt", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "<PERSON><PERSON><PERSON><PERSON> med att ladda tidslinjen.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Projektet har inget slutdatum", "app.containers.Admin.projects.all.new.timeline.project": "Projekt", "app.containers.Admin.projects.all.notes": "Anteckningar", "app.containers.Admin.projects.all.personalDataExplanation5": "Det här alternativet lägger till fälten för förnamn, efternamn och e-post i den exporterade PDF-filen. När vi laddar upp pappersformuläret använder vi dessa data för att automatiskt generera ett konto för den som svarar på offlineundersökningen.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "Sammanfattning av AI", "app.containers.Admin.projects.project.analysis.Comments.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "Sammanfattning av kommentarer är tillgänglig när det finns 5 eller fler kommentarer.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Samman<PERSON><PERSON> kommentarer", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {<PERSON><PERSON><PERSON><PERSON>} =1 {1 ny kommentar} other {# nya kommentarer}}", "app.containers.Admin.projects.project.analysis.aiSummary": "Sammanfattning av AI", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "Detta är AI-genererat innehåll. Det kanske inte är 100% korrekt. Vänligen granska och korsreferera med de faktiska indata för noggrannhet. Observera att noggrannheten sannolikt kommer att förbättras om antalet valda indata minskas.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "E-postmeddelanden skickas endast till deltagare", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "<PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "Indexeras inte av sökmotorer", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Syns inte på startsidan eller i widgetar", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Endast tillgänglig via direkt URL", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "Allmänheten", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "<PERSON><PERSON><PERSON><PERSON> hur sökbart detta projekt är.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "Detta projekt är synligt för alla som har åtkomst och visas på startsidan och i widgetarna.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Detta projekt kommer att vara dolt för allmänheten och kommer endast att vara synligt för dem som har länken.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Vem kan hitta det här projektet?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Öppen AI-analys", "app.containers.Admin.projects.project.ideas.analysisText2": "Utforska AI-drivna sammanfattningar och se enskilda bidrag.", "app.containers.Admin.projects.project.ideas.importInputs": "Import", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "<PERSON><PERSON>r du har skapat en rapport kan du välja att dela den med allmänheten när fasen startar.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Skapa en mer komplex sida för informationsdelning", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Skapa en rapport till:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "Skapa en rapport", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Skapa en rapport för en tidigare fas eller börja om från bö<PERSON>.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "Denna rapport är inte offentlig. För att göra den offentlig, aktivera knappen \"Synlig\".", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "<PERSON>na fas har startat, men rapporten är ännu inte offentlig. <PERSON><PERSON>r att göra den offentlig, aktivera växeln \"Synlig\".", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Börja med en fasmall", "app.containers.Admin.projects.project.information.ReportTab.report": "Rapport", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Dela med dig av resultaten från en tidigare undersökning eller idéfas", "app.containers.Admin.projects.project.information.ReportTab.visible": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "Denna rapport kommer att vara offentlig så snart fasen startar. Om du inte vill att den ska vara offentlig avaktiverar du knappen \"Synlig\".", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "Denna rapport är för närvarande offentlig. Om du vill att den inte ska vara offentlig inaktiverar du knappen \"Synlig\".", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "<PERSON>r du säker på att du vill ta bort denna rapport? Denna åt<PERSON>rd kan inte ång<PERSON>.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Lägg till fas", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "Du måste godkänna detta innan du kan forts<PERSON>ta", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "Formul<PERSON><PERSON> kan laddas ner här.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "Det uppladdade formuläret skapades med avsnittet \"Personuppgifter\"", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Formulärspråk", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "<PERSON><PERSON> samtycker härmed till att denna fil behandlas med hjälp av Google Cloud Form Parser", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Importera Excel-fil", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Ladda upp en fil för att fortsätta", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "<PERSON>en kan laddas ner här.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Uppladdning", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Ladda upp en färdig <b>Excel-fil</b> (.xlsx). Den måste använda den mall som tillhandahålls för detta projekt. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Ladda upp en <b>PDF-fil med skannade formulär</b>. Det måste använda ett formulär som skrivits ut från denna fas. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Använd detta e-postmeddelande för den nya användaren", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Ange en giltig e-postadress för att skapa ett nytt konto", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Ett nytt konto kommer att skapas för författaren med dessa uppgifter. Denna inmatning kommer att läggas till i det.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "Förnamn", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "E<PERSON>nam<PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Ange en e-postadress och/eller ett förnamn och efternamn för att tilldela denna inmatning till en författare. Eller avmarkera rutan för sam<PERSON>.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Det finns redan ett konto kopplat till den här e-postadressen. Denna inmatning kommer att läggas till i det.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Anv<PERSON><PERSON><PERSON><PERSON> (skapa användarkonto)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "Godkänna", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Godkänna alla inmatningar", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Författare:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "E-post:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Fel uppstod under importen och vissa inmatningar har inte importerats. Korrigera felen och importera de saknade inmatningarna på nytt.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Ogiltiga formulärdata. Kontrollera om formuläret ovan är felaktigt.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Importera Excel-fil (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Import", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Importera skannade formulä<PERSON> (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Importera skannade formulär", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "Importerade inmatningar", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importera. Denna process kan ta n<PERSON>gra minuter.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "Denna input importerades anonymt.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} input har importerats och kräver godkännande.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} Inmatningarna kunde inte godkännas. Kontrollera varje inmatning för valideringsproblem och bekräfta individuellt.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Lokal:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Inget att recensera ännu. Klicka på \"{importFile}\" för att importera en PDF-fil med skannade inmatningsformulär eller en Excel-fil med inmatningar.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Inget att granska ännu. Klicka på \"{importFile}\" för att importera en Excel-fil som innehåller indata.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "<PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Det går inte att visa den importerade filen. Visning av importerade filer är endast tillgängligt för PDF-import.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Fas:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "Den valda fasen kan inte innehålla ingångar. Vänligen välj en annan.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Detta projekt innehåller inga faser som kan innehålla idéer.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "<PERSON><PERSON><PERSON><PERSON> till vilken fas du vill lägga till dessa ing<PERSON>ngar.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Importör av indata", "app.containers.Admin.projects.project.participation.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.inputs": "Bidrag", "app.containers.Admin.projects.project.participation.participantsTimeline": "Deltagarnas tidslinje", "app.containers.Admin.projects.project.participation.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.selectPeriod": "Välj period", "app.containers.Admin.projects.project.participation.usersByAge": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>", "app.containers.Admin.projects.project.participation.usersByGender": "Anv<PERSON><PERSON><PERSON> efter kön", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "Lägg till en fråga", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "Möjligheten att lägga till eller redigera användarfält på fasnivå ingår inte i din nuvarande licens. Kontakta din GovSuccess Manager för att lära dig mer om det.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} alternativ", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "Status för fält", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "Dessa frågor kommer att läggas till som sista sida i enkätformuläret, eftersom \"Visa fält i enkäten?\" har valts i fasinställningarna.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "Inga extra fr<PERSON><PERSON> kommer att ställas.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Valfritt - alltid aktiverat eftersom det refereras till av gruppen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "Ta bort fält", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Obligatoriskt - alltid aktiverat eftersom det refereras till av gruppen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Autentisera dig med {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Fyll i de extra frågorna nedan", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Bekräfta din e-post", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Data returneras från verifieringsmetod:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "<PERSON><PERSON>, e<PERSON><PERSON><PERSON>, e-postadress och l<PERSON>senord", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "<PERSON><PERSON> <PERSON> e-postadress", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "Hur ofta ska användare verifieras?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Identitetsverifiering med {verificationMethod} (baserat på användargrupp)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "Inga åtgärder krävs för att delta", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "An<PERSON><PERSON><PERSON> smarta grupper för att begränsa deltagandet baserat på de verifierade uppgifter som anges ovan", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Användare måste ha verifierats under de senaste 30 minuterna.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Användare måste ha verifierats under de senaste {days} dagarna.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "Under de senaste 30 dagarna", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "Under de senaste 30 minuterna", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "Under de senaste 7 dagarna", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "En gång är nog", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Verifierade fält:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} verifiering", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Skapa konto", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Deltagarna måste skapa ett fullständigt konto med sitt namn, bekräftad e-postadress och lösenord.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Deltagarna måste skapa ett fullständigt konto med namn, e-postadress och lösenord.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Autentisering", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "Rediger<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Bekräftelse via e-post", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Deltagarna måste bekräfta sin e-post med en engångskod.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Avancerad upptäckt av skräppost", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "Den här funktionen hjälper till att förhindra dubbla enkäter från inloggade användare genom att analysera IP-adresser och enhetsdata. Även om det inte är lika exakt som att kräva inloggning kan det bidra till att minska antalet duplicerade svar.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Obs: I delade n<PERSON> (t.ex. kontor eller offentligt Wi-Fi) finns det en liten risk att olika användare flaggas som dubbletter.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Aktivera avancerad upptäckt av skräppost", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Extra frågor som ställdes till deltagarna", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "Ingen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Vem som helst kan delta utan att registrera sig eller logga in.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Återställ extra fr<PERSON><PERSON> och grupper", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Begränsa deltagandet till användargrupp(er)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO-verifiering", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Deltagarna måste verifiera sin identitet med {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Öppen AI-analys", "app.containers.Admin.projects.project.survey.allFiles": "Alla filer", "app.containers.Admin.projects.project.survey.allResponses": "<PERSON>a svar", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Noggrannhet: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Det uppstod ett fel vid genereringen av AI-sammandraget. Vänligen försök att återskapa den nedan.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Öppen AI-analys", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "<PERSON><PERSON><PERSON><PERSON> sammanfattningar för denna fråga", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "v<PERSON>a <PERSON>", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Öppna analysåtgärder", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} nya svar", "app.containers.Admin.projects.project.survey.analysis.regenerate": "Regenerera", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Visa AI-insikter", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "Du kan sammanfatta högst 30 inmatningar åt gången med din nuvarande plan. Prata med din GovSuccess Manager eller administratör om du vill veta mer.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "<PERSON><PERSON><PERSON><PERSON> fr<PERSON><PERSON> för analys", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "Vill du inkludera andra relaterade fr<PERSON> i din analys av {question}?", "app.containers.Admin.projects.project.survey.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalButton": "Fortsätta", "app.containers.Admin.projects.project.survey.consentModalCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "<PERSON><PERSON> samtycker till att använda OpenAI som personuppgiftsbiträde för detta projekt", "app.containers.Admin.projects.project.survey.consentModalText1": "Genom att fortsätta godkänner du att OpenAI används som personuppgiftsbiträde för detta projekt.", "app.containers.Admin.projects.project.survey.consentModalText2": "OpenAI API:erna driver de automatiserade textsammanfattningarna och delar av den automatiserade taggningen.", "app.containers.Admin.projects.project.survey.consentModalText3": "<PERSON>i skickar bara det som användarna skrivit i sina enkäter, idéer och kommentarer till OpenAI API:er, aldrig någon information från deras profil.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI kommer inte att använda dessa uppgifter för vidareutbildning av sina modeller. Mer information om hur OpenAI hanterar dataintegritet finns på {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "<PERSON>är", "app.containers.Admin.projects.project.survey.consentModalTitle": "<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "Du kan inte gå in i analysen innan du har redigerat formuläret", "app.containers.Admin.projects.project.survey.deleteAnalysis": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Är du säker på att du vill radera denna analys? Denna åtg<PERSON>rd kan inte ång<PERSON>.", "app.containers.Admin.projects.project.survey.explore": "Utforska", "app.containers.Admin.projects.project.survey.followUpResponses": "Uppföljning av svar", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> genomsnitt", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Exportera som GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Exportera svaren på den här frågan som en GeoJSON-fil. <PERSON><PERSON><PERSON> varje GeoJSON-funktion kommer alla relaterade respondenters enkätsvar att listas i funktionens \"properties\"-objekt.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} respondenter} one {{respondentCount} respondent} other {{respondentCount} respondenter}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "Visa detaljer", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} val} one {{numberChoices} ~ val} other {{numberChoices} val}}", "app.containers.Admin.projects.project.survey.heatMap": "Värmekarta", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "<PERSON><PERSON><PERSON> mer om värmekartor som genererats med Esri Smart Mapping.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "Värmekartan har genererats med hjälp av Esri Smart Mapping. Värmekartor är användbara när det finns en stor mängd datapunkter. För färre punkter kan det vara bättre att bara titta på platspunkterna direkt. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "<PERSON><PERSON>rme kartvy", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Dold av logik", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "När en användare väljer detta svar hoppar logiken över alla sidor fram till sidan {pageNumber} ({numQuestionsSkipped} fr<PERSON>gor som hoppats över). Klicka för att dölja eller visa de överhoppade sidorna och frågorna.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "När en användare väljer detta svar hoppar logiken till slutet av undersökningen ({numQuestionsSkipped} frågor som hoppats över). Klicka för att dölja eller visa de sidor och frågor som hoppats över.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "Logiken på denna sida hoppar över alla sidor fram till sidan {pageNumber} ({numQuestionsSkipped} frågor hoppade över). <PERSON>licka för att dölja eller visa de överhoppade sidorna och frågorna.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Logiken på denna sida hoppar till slutet av undersökningen ({numQuestionsSkipped} frågor hoppade över). Klicka för att dölja eller visa de sidor och frågor som hoppats över.", "app.containers.Admin.projects.project.survey.newAnalysis": "Ny analys", "app.containers.Admin.projects.project.survey.nextInsight": "Nästa insikt", "app.containers.Admin.projects.project.survey.openAnalysis": "Öppen AI-analys", "app.containers.Admin.projects.project.survey.otherResponses": "<PERSON><PERSON> svar", "app.containers.Admin.projects.project.survey.page": "<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.previousInsight": "Tidigare insikt", "app.containers.Admin.projects.project.survey.responses": "<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "Antalet svar för den här sidan är lägre än det totala antalet enkätsvar eftersom vissa respondenter inte har sett den här sidan på grund av logiken i enkäten.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "Antalet svar på den här frågan är lägre än det totala antalet svar på undersökningen eftersom vissa respondenter inte har sett den här frågan på grund av logik i undersökningen.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Sentiments<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Sammanfatta alla dina svar direkt.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Prata med dina data på naturligt språk.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "<PERSON><PERSON><PERSON><PERSON> referenser till enskilda svar från AI-genererade sammanfattningar.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Se vår {link} för en fullständig översikt.", "app.containers.Admin.projects.project.survey.upsell.button": "<PERSON><PERSON>s upp AI-analys", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "st<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Analysera data snabbare med AI", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "Den här funktionen ingår inte i din nuvarande plan. Prata med din Government Success Manager el<PERSON> administra<PERSON><PERSON><PERSON> för att låsa upp den.", "app.containers.Admin.projects.project.survey.viewAnalysis": "Visa", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Utforska AI-drivna sammanfattningar och se enskilda bidrag.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Välj period", "app.containers.Admin.projects.project.traffic.trafficSources": "Källor till trafik", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Vi har ändrat vårt sätt att samla in och visa besöksdata. Som ett resultat är besöksdata mer exakt och fler typer av data finns tillgängliga, samtidigt som de fortfarande är GDPR-kompatibla. Vi bör<PERSON>de samla in dessa nya data först i november 2024, så innan dess finns inga data tillgängliga.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Besökarnas tidslinje", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Fasrapport", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "<PERSON><PERSON><PERSON> till lite text om fasen", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Det här är lite text. Du kan redigera och formatera den genom att använda redigeraren i panelen till höger.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Deltagare", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Projektresultat", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Sammanfattning av rapport", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Lägg till projektets mål, metoder som använts för deltagande och resultatet", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Denna rapport innehåller osparade ändringar. Spara innan du skriver ut.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Titeln är redan upptagen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "Jämfört med tidigare {days} dagar", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Dölj statistik", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Deltagandegrad", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Visa jämförelse med föregående period", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "Du måste först välja ett datumintervall.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Bidrag", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Deltagande", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Visa kommentarer", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "Visa ingångar", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Visa röster", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "<PERSON><PERSON> för registrering", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Registreringsfält", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Okä<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Användare: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "Anpass<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Aktiv", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Arkiverad", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "Öppen frågeställning", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "Planerad", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projekt", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Status för publicering", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Publicerad", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "Data för denna widget saknas. Konfigurera om eller ta bort den för att kunna spara rapporten.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Community Monitor Hälsoindex", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "Inga lämpliga faser hittades i detta projekt", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "Ingen fas vald. Vänligen välj en fas först.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "Inget projekt", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Inget projekt valt. Vänligen välj ett projekt först.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "Du kan inte kopiera den här rapporten eftersom den innehåller uppgifter som du inte har tillgång till.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "Du kan inte redigera den här rapporten eftersom den innehåller data som du inte har tillgång till.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "<PERSON>r du säker att du vill ta bort \"{reportName}\"? Denna åtg<PERSON>rd kan inte ångras.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "<PERSON>r du säker på att du vill ta bort den här rapporten? Denna åtgärd kan inte ångras.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Du<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Rediger<PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "<PERSON><PERSON>d {days, plural, no {# dagar} one {# dag} other {# dagar}} sedan", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Ett fel inträffade när du försökte skapa den här rapporten. Försök igen senare.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "<PERSON><PERSON><PERSON><PERSON> med en tom sida", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Börja med en mall för Community Monitor", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Rapporttitel", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "Skapa en rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Anpassa din rapport och dela den med interna intressenter eller deltagarna som en PDF-fil.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Skapa en rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Skapa din första rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "Inget projekt utvalt", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Börja med en plattformsmall", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Skriv ut till PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Börja med en projektmall", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "<PERSON><PERSON><PERSON> {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Rapportmall", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Det finns redan en rapport med denna titel. Välj en annan titel.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Dela som PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Om du vill dela rapporten med alla kan du skriva ut den som en PDF-fil.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Dela som webblänk", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "Den här webblänken är endast tillgänglig för administratörer.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Dela", "app.containers.Admin.reporting.contactToAccess": "Möjligheten att skapa en anpassad rapport är en del av premiumlicensen. Kontakta din GovSuccess-ansvarige för att få veta mer om det här.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "<PERSON>a rapporter", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Rapporter från gemenskapens övervakare", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Dessa rapporter är relaterade till Community Monitor. Rapporterna genereras automatiskt varje kvartal.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Skapa en rapport", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Anpassa din rapport och dela den med interna intressenter eller en community med en webblänk.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "<PERSON>a rapporter kommer att visas här.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "<PERSON><PERSON><PERSON> rapporter", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Lägesrapporter", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Detta är rapporter som skapats av din Government Success Manager", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "<PERSON><PERSON> rapporter", "app.containers.Admin.reporting.deprecated": "DEPRECERAD", "app.containers.Admin.reporting.helmetDescription": "Rapporteringssida för administra<PERSON>rer", "app.containers.Admin.reporting.helmetTitle": "Rapportering", "app.containers.Admin.reporting.printPrepare": "Förbereder utskrift...", "app.containers.Admin.reporting.reportBuilder": "Rapportbyggare", "app.containers.Admin.reporting.reportHeader": "Rapportrubrik", "app.containers.Admin.reporting.warningBanner3": "Grafer och siffror i denna rapport uppdateras endast automatiskt på denna sida. Spara rapporten för att uppdatera dem på andra sidor.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Gemensam jord", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Information om", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "<PERSON><PERSON><PERSON><PERSON> metoder", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "Undersö<PERSON>ning", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "Opinionsundersökning", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "Tidigare {days} dagar: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Förslag till beslut", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "Extern undersökning", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Volontärarbete", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Omr<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Diagram", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "<PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.surveyFormTab.downloads": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "Duplicera en annan undersökning", "app.containers.Admin.surveyFormTab.editSurveyForm": "Redigera enkätformulär", "app.containers.Admin.surveyFormTab.inputFormDescription": "Ange vilken information som ska lämnas, lägg till korta beskrivningar eller instruktioner för att vägleda deltagarnas svar och ange om varje fält är valfritt eller obligatoriskt.", "app.containers.Admin.surveyFormTab.surveyForm": "Enkätformulär", "app.containers.Admin.tools.apiTokens.createTokenButton": "Skapa ny token", "app.containers.Admin.tools.apiTokens.createTokenCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Din token har skapats. <PERSON><PERSON>ra {secret} nedan och spara den på ett säkert sätt.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Skapa en ny token att använda med vårt offentliga API.", "app.containers.Admin.tools.apiTokens.createTokenError": "<PERSON>e ett namn för din <PERSON>", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Skapa token", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b><PERSON><PERSON><PERSON><PERSON>!</b> Du kan bara kopiera denna {secret} en gång. Om du stänger detta fönster kommer du inte att kunna se det igen.", "app.containers.Admin.tools.apiTokens.createTokenName": "<PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Ge din token ett namn", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Din token har skapats", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Nä<PERSON>", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "<PERSON><PERSON><PERSON> {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Kopierad!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Skapa en ny token", "app.containers.Admin.tools.apiTokens.createdAt": "Skapad", "app.containers.Admin.tools.apiTokens.delete": "Ta bort token", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Är du säker på att du vill ta bort denna token?", "app.containers.Admin.tools.apiTokens.description": "Hantera dina API-tokens för vårt publika API. För mer information, se vår {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "<PERSON><PERSON> an<PERSON>", "app.containers.Admin.tools.apiTokens.link": "API-dokumentation", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "<PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.noTokens": "Du har inga tokens ännu.", "app.containers.Admin.tools.apiTokens.title": "Offentliga API-tokens", "app.containers.Admin.tools.esriDisabled": "Integrationen med Esri är en tilläggsfunktion. Kontakta din GovSuccess Manager om du vill ha mer information om detta.", "app.containers.Admin.tools.esriIntegration2": "Integration med Esri", "app.containers.Admin.tools.esriIntegrationButton": "Aktivera Esri", "app.containers.Admin.tools.esriIntegrationDescription3": "Anslut ditt Esri-konto och importera data från ArcGIS Online direkt till dina mappningsprojekt.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri-logotyp", "app.containers.Admin.tools.esriKeyInputDescription": "Lägg till din Esri API-nyckel för att kunna importera dina kartlager från ArcGIS Online i kartflikarna i projekt.", "app.containers.Admin.tools.esriKeyInputLabel": "API-nyckel för <PERSON>", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Klistra in API-nyckeln här", "app.containers.Admin.tools.esriMaps": "Esri-kartor", "app.containers.Admin.tools.esriSaveButtonError": "<PERSON> uppstod ett fel när du sparade din nyckel, förs<PERSON><PERSON> igen.", "app.containers.Admin.tools.esriSaveButtonSuccess": "API-nyckel sparad", "app.containers.Admin.tools.esriSaveButtonText": "Spara-tangent", "app.containers.Admin.tools.learnMore": "<PERSON><PERSON><PERSON>r", "app.containers.Admin.tools.managePublicAPIKeys": "Hantera API-nycklar", "app.containers.Admin.tools.manageWidget": "<PERSON>tera widget", "app.containers.Admin.tools.manageWorkshops": "Skapa workshops", "app.containers.Admin.tools.powerBIAPIImage": "Power BI-bild", "app.containers.Admin.tools.powerBIDescription": "Använd våra plug & play Power BI-mallar för att få tillgång till Go Vocal-data i din Microsoft Power BI-arbetsyta.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI är inte en del av din licens. Kontakta din GovSuccess Manager om du vill ha mer information om detta.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Ladda ner mallar", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "Om du tänker använda dina Go Vocal-data i ett Power BI-dataflöde kan du med hjälp av den här mallen skapa ett nytt dataflöde som ansluter till dina Go Vocal-data. När du har laddat ner denna mall måste du först hitta och ersätta följande strängar ##CLIENT_ID## och ##CLIENT_SECRET## i mallen med dina offentliga API-autentiseringsuppgifter innan du laddar upp till PowerBI.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Ladda ner mall för dataflöde", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Dataflödesmall", "app.containers.Admin.tools.powerBITemplates.intro": "Observera: <PERSON><PERSON><PERSON> att kunna använda någon av dessa Power BI-mallar måste du först {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "skapa en uppsättning autentiseringsuppgifter för vårt offentliga API", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "Den här mallen skapar en Power BI-rapport baserad på dina Go Vocal-data. Den konfigurerar alla dataanslutningar till din Go Vocal-plattform, skapar datamodellen och några standardinstrumentpaneler. När du öppnar mallen i Power BI kommer du att uppmanas att ange dina offentliga API-autentiseringsuppgifter. Du måste också ange Base Url för din plattform, som är: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Ladda ner rapportering<PERSON>ll", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Mall för rapport", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Mer information om hur du använder dina Go Vocal-data i Power BI finns i vår {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "st<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Power BI-mallar", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "Hantera autentiseringsuppgifterna för att skapa anpassade integrationer på vårt offentliga API.", "app.containers.Admin.tools.publicAPIDisabled1": "Det publika API:et är inte en del av din nuvarande licens. Kontakta din GovSuccess Manager om du vill ha mer information om detta.", "app.containers.Admin.tools.publicAPIImage": "Bild fö<PERSON>", "app.containers.Admin.tools.publicAPITitle": "Offentlig API-åtkomst", "app.containers.Admin.tools.toolsLabel": "Verktyg", "app.containers.Admin.tools.widgetDescription": "Du kan skapa en widget, anpassa den och lägga till den på din egen webbplats för att locka människor till den här plattformen.", "app.containers.Admin.tools.widgetImage": "<PERSON><PERSON><PERSON> för widget", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "<PERSON><PERSON>ll videomöten i realtid och underlätta samtidiga gruppdiskussioner och debatter. <PERSON><PERSON> in synpunkter, rö<PERSON> och nå konsensus, precis som du skulle ha gjort offline.", "app.containers.Admin.tools.workshopsImage": "Workshops bild", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Workshops för överläggningar online", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "totalt antal användare på plattformen", "app.containers.AdminPage.DashboardPage._blank": "okänd", "app.containers.AdminPage.DashboardPage.allGroups": "Alla grupper", "app.containers.AdminPage.DashboardPage.allProjects": "Alla projekt", "app.containers.AdminPage.DashboardPage.allTime": "<PERSON>a tider", "app.containers.AdminPage.DashboardPage.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "En basdatauppsättning krävs för att mäta representationen av plattformsdeltagare.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "<PERSON><PERSON> snart", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Vi arbetar för nä<PERSON>e på dashboarden {fieldName} – den kommer att vara tillgänglig snart", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# objekt är} other {# objekt är}} dolt/dolda i det här diagrammet. Ändra till {tableViewLink} för att visa alla data.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} för användarregistrering", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} av {total} användare inkluderas ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Visa {numberOfHiddenItems} fler", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Ange en basdatauppsättning.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Poäng för representativitet:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Den här poängen visar hur exakt data om plattformsdeltagare återspeglar den totala befolkningen. Läs mer om {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Ski<PERSON>a in basdata", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "tabellvy", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Total befolkning", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Deltagare", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Lägg till en åldersgrupp", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} och högre", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Åldersgrupp", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Åldersgrupp(er) som är {upperBound} och högre ingår inte.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Åldersgrupp {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Åldersgrupper", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "och högre", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Anv<PERSON>nd exempelgruppering", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "<PERSON><PERSON> alla", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Ange åldersgrupper som ska anpassas till din basdatauppsättning.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Intervall", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Spara", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "<PERSON><PERSON><PERSON>ldersgrupper", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Det här objektet kommer inte att beräknas.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "Se mindre", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Se {numberOfHiddenItems} fler...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Basmånad (valfritt)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Åldersgrupper (födelseår)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "<PERSON><PERSON> snart", "app.containers.AdminPage.DashboardPage.components.Field.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.default": "Standard", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "F<PERSON>l i alla aktiverade alternativ, eller inaktivera alternativen som du inte vill ha med i diagrammet. Du måste fylla i minst ett alternativ.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "<PERSON>ull<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Totalantal invånare", "app.containers.AdminPage.DashboardPage.components.Field.options": "Alternativ", "app.containers.AdminPage.DashboardPage.components.Field.save": "Spara", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Sparad", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "{setAgeGroupsLink} först för att bö<PERSON><PERSON> mata in basdata.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "ange åldersgrupper", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Genomsnittlig svarstid: {days} dagar", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Genomsnittligt antal dagar för att svara", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "lämnad feedback", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Status för indata", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Indata efter status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Antal indata", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Officiell uppdatering", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Procentandel indata", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Status har ä<PERSON>ts", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Totalt", "app.containers.AdminPage.DashboardPage.components.editBaseData": "<PERSON><PERSON>a basdata", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "så här beräknar vi poängen för representativitet", "app.containers.AdminPage.DashboardPage.continuousType": "<PERSON><PERSON> t<PERSON>", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Kumulativ <PERSON>", "app.containers.AdminPage.DashboardPage.customDateRange": "Anpassad", "app.containers.AdminPage.DashboardPage.day": "dag", "app.containers.AdminPage.DashboardPage.false": "falskt", "app.containers.AdminPage.DashboardPage.female": "kvinna", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Topp 5 inmatningar efter reaktioner", "app.containers.AdminPage.DashboardPage.fromTo": "fr<PERSON>n {from} till {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Dashboard för aktiviteter på plattformen", "app.containers.AdminPage.DashboardPage.helmetTitle": "Sida för administratörsdashboard", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "V<PERSON><PERSON>j resurs att visa efter projekt", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Välj en resurs att visa efter ämne", "app.containers.AdminPage.DashboardPage.inputs1": "Bidrag", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Ingångar efter status", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Vä<PERSON>j användargrupp", "app.containers.AdminPage.DashboardPage.male": "man", "app.containers.AdminPage.DashboardPage.month": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.noData": "Det finns inga data att visa.", "app.containers.AdminPage.DashboardPage.noPhase": "Ingen fas har skapats för det här projektet", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Antalet deltagare som postade inputs, reagerade eller kommenterade.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.numberOfLikes": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Totalt antal reaktioner", "app.containers.AdminPage.DashboardPage.overview.management": "Hantering", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projekt och deltagande", "app.containers.AdminPage.DashboardPage.overview.showLess": "Visa mindre", "app.containers.AdminPage.DashboardPage.overview.showMore": "Visa mer", "app.containers.AdminPage.DashboardPage.participants": "Deltagare", "app.containers.AdminPage.DashboardPage.participationPerProject": "Deltagande per projekt", "app.containers.AdminPage.DashboardPage.participationPerTopic": "<PERSON><PERSON><PERSON> per ämne", "app.containers.AdminPage.DashboardPage.perPeriod": "Per {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Föregående 30 dagar", "app.containers.AdminPage.DashboardPage.previous90Days": "Föregående 90 dagar", "app.containers.AdminPage.DashboardPage.previousWeek": "<PERSON><PERSON><PERSON> ve<PERSON>", "app.containers.AdminPage.DashboardPage.previousYear": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.projectType": "Projekttyp: {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Den här basdatauppsättningen krävs för att beräkna plattformsanvändarnas representativitet i förhållande till den totala befolkningen.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Ange en basdatauppsättning.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Se hur representativa dina plattformsdeltagare är jämfört med den totala befolkningen – baserat på data som samlas in under användarregistreringen. Läs mer om {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Se hur representativa dina plattformsdeltagare är jämfört med den totala befolkningen – baserat på data som samlas in under användarregistreringen.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Communityrepresentation", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Tillbaka till dashboard", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Inget av de aktiverade registreringsfälten stöds för närvarande.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Här kan du visa/dö<PERSON>ja objekt på dashboard och ange basdata. Endast de aktiverade fälten för {userRegistrationLink} visas här.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "<PERSON><PERSON>a basdata", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "användarregistrering", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Ski<PERSON>a in basdata", "app.containers.AdminPage.DashboardPage.resolutionday": "i dagar", "app.containers.AdminPage.DashboardPage.resolutionmonth": "i måna<PERSON>", "app.containers.AdminPage.DashboardPage.resolutionweek": "i veckor", "app.containers.AdminPage.DashboardPage.selectProject": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.containers.AdminPage.DashboardPage.selectedProject": "aktuellt projektfilter", "app.containers.AdminPage.DashboardPage.selectedTopic": "aktuellt ämnesfilter", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Upptäck vad som händer på din plattform.", "app.containers.AdminPage.DashboardPage.tabOverview": "Översikt", "app.containers.AdminPage.DashboardPage.tabReports": "Projekt", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Representation", "app.containers.AdminPage.DashboardPage.tabUsers": "Deltagarna", "app.containers.AdminPage.DashboardPage.timelineType": "Tidslinje", "app.containers.AdminPage.DashboardPage.titleDashboard": "Dashboard", "app.containers.AdminPage.DashboardPage.total": "Totalt", "app.containers.AdminPage.DashboardPage.totalForPeriod": "<PERSON><PERSON> {period}", "app.containers.AdminPage.DashboardPage.true": "sant", "app.containers.AdminPage.DashboardPage.unspecified": "ospecificerad", "app.containers.AdminPage.DashboardPage.users": "Deltagarna", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Användare efter geografiskt område", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Anv<PERSON><PERSON><PERSON> efter kön", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Registreringar", "app.containers.AdminPage.DashboardPage.week": "vecka", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Tips för att välja en faviconbild: välj en enkel bild, eftersom den visade bildstorleken är mycket liten. Bilden ska sparas i filformatet PNG och vara kvadratisk med transparent bakgrund (eller vit bakgrund om det krävs). Din favicon bör endast konfigureras en gång eftersom ändringar kräver viss teknisk support.", "app.containers.AdminPage.FaviconPage.save": "Spara", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "<PERSON><PERSON><PERSON> gick fel, f<PERSON><PERSON><PERSON><PERSON> igen senare.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Sparad!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "<PERSON><PERSON> har sparats.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "<PERSON><PERSON><PERSON> till", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Mapphanterare", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Mapphanterare kan redigera mappbeskrivningen, skapa nya projekt i mappen och ha projektledningsrättigheter över alla projekt i mappen. De kan inte ta bort projekt och de har inte åtkomst till projekt som inte finns i deras mapp. Du kan {projectManagementInfoCenterLink} för att hitta mer information om projektledningsrättigheter.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Ingen matchning hittades", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "besök vårt hjälpcenter", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "<PERSON><PERSON>k använda<PERSON>", "app.containers.AdminPage.FoldersEdit.addToFolder": "Lägg till i mapp", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Arkiverad", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Ta bort den här mappen", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Beskrivning", "app.containers.AdminPage.FoldersEdit.draftStatus": "Utkast", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "<PERSON><PERSON><PERSON> till filer i den här mappen", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Filer bör inte vara större än 50 MB. Tillagda filer kommer att visas på mappsidan.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Beskrivningar", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "Det finns inga projekt i den här mappen. Gå tillbaka till huvudfliken Projekt för att skapa och lägga till projekt.", "app.containers.AdminPage.FoldersEdit.folderName": "Mappnamn", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Rubrikbild", "app.containers.AdminPage.FoldersEdit.multilocError": "Alla textfält måste fyllas i för varje språk.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Det finns inga projekt som du kan lägga till i den här mappen.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Mappkortsbild", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Mapprojekt", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Inställningar", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projekt som har lagts till i den här mappen", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Projekt som du kan lägga till i den här mappen", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Välj om den här mappen är \"utkast\", \"publicerat\" eller \"arkiverat\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Publicerad", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Ta bort från mappen", "app.containers.AdminPage.FoldersEdit.save": "Spara", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "<PERSON><PERSON><PERSON> gick fel, f<PERSON><PERSON><PERSON><PERSON> igen senare.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Sparad!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "<PERSON><PERSON> har sparats.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "<PERSON><PERSON> be<PERSON>ri<PERSON>", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "visas på startsidan", "app.containers.AdminPage.FoldersEdit.statusLabel": "Publiceringsstatus", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "<PERSON>örk<PERSON><PERSON> varför projekten hör i<PERSON>, definiera en visuell identitet och dela information.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "<PERSON>örk<PERSON><PERSON> varför projekten hör i<PERSON>, definiera en visuell identitet och dela information.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Alla textfält måste fyllas i.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Titel", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Skapa en ny mapp", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Inställningar", "app.containers.AdminPage.FoldersEdit.url": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Se mapp", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Anpassa hero-bandero<PERSON>s bild och text.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Spara hero-banderoll", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Inspiration Hub är en plats där du kan hitta inspiration till dina projekt genom att bläddra bland projekt på andra plattformar.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Redigera villkoren och sekretesspolicyn för din plattform. <PERSON><PERSON> sidor, inklusive sidorna Om och Vanliga frågor och svar, kan redigeras på fliken {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Plattformspolicy", "app.containers.AdminPage.PagesEdition.privacy-policy": "Sekretesspolicy", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Villkor", "app.containers.AdminPage.Project.confirmation.description": "<PERSON><PERSON> kan inte ång<PERSON>.", "app.containers.AdminPage.Project.confirmation.no": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.confirmation.title": "Är du säker på att du vill återställa alla deltagardata?", "app.containers.AdminPage.Project.confirmation.yes": "Återställ alla deltagardata", "app.containers.AdminPage.Project.data.descriptionText1": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, svar <PERSON>å <PERSON>ö<PERSON>, volontärer och evenemangsanmälare. När det gäller omröstningsfaser kommer denna åtgärd att rensa rösterna men inte alternativen.", "app.containers.AdminPage.Project.data.title": "Rensa alla deltagardata från detta projekt", "app.containers.AdminPage.Project.resetParticipationData": "Återställ alla deltagardata", "app.containers.AdminPage.Project.settings.accessRights": "Behörigheter", "app.containers.AdminPage.Project.settings.back": "Tillbaka", "app.containers.AdminPage.Project.settings.data": "Uppgifter", "app.containers.AdminPage.Project.settings.description": "Beskrivning", "app.containers.AdminPage.Project.settings.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.general": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.projectTags": "Projekt-taggar", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Lista över projekt på plattformen", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Dashboard för projekt", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Skapa nya projekt eller hantera befintliga projekt.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projekt", "app.containers.AdminPage.ProjectDashboard.published": "Publicerad", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Stäng inställningspanel", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Centrum", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "Full bredd", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "<PERSON><PERSON> av knappar", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Text fö<PERSON> knapp", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Ange text för knappen", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "<PERSON>p av knapp", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Sekundär", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "URL för knapp", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Ange en URL för knappen", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Kolumnlayout", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Beskrivning", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Beskrivning för <PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Visas på projektkortet på startsidan.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Visas på projektsidan. Beskriv tydligt vad projektet handlar om, vad du förväntar dig av dina användare och vad de kan förvänta sig av dig.", "app.containers.AdminPage.ProjectDescription.errorMessage": "<PERSON><PERSON><PERSON> gick fel, <PERSON><PERSON><PERSON><PERSON><PERSON> igen senare", "app.containers.AdminPage.ProjectDescription.preview": "Förhandsvisning", "app.containers.AdminPage.ProjectDescription.save": "Spara", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "<PERSON><PERSON> har sparats.", "app.containers.AdminPage.ProjectDescription.saved": "Sparad!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Bestäm vilket budskap du vill ge till din målgrupp. Redigera ditt projekt och komplettera det med bilder, videor, filbilagor,... Den här informationen hjälper besökare förstå vad ditt projekt handlar om.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Projektbeskrivning", "app.containers.AdminPage.ProjectDescription.whiteSpace": "Vitt utrymme", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "<PERSON><PERSON><PERSON><PERSON> ram", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "<PERSON><PERSON><PERSON><PERSON> hö<PERSON>d", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "St<PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Medel", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "<PERSON>ten", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Standardlatitud för kartans mittpunkt. Ett värde mellan -90 och 90 godkänns.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Standardlongituden för kartans mittpunkt. Ett värde mellan -90 och 90 godkänns.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "<PERSON><PERSON><PERSON> kartlager", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Redigera lager", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "<PERSON><PERSON><PERSON> gick fel, <PERSON><PERSON><PERSON><PERSON><PERSON> igen senare", "app.containers.AdminPage.ProjectEdit.MapTab.here": "<PERSON>är", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Importera GeoJSON-fil", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Standardlatitud", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "La<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Alla funktioner i lagret kommer att formateras med den här färgen. Den här färgen skriver även över eventuell befintlig formatering i din GeoJSON-fil.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Om du vill väljer du en ikon som visas i markörerna. Klicka på {url} för att se listan över ikoner som du kan välja.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Lagernamn", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Det här lagernamnet visas på kartförklaringen", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Verktygstips för lager", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Den här texten visas som verktygstips när du håller muspekaren över lagerfunktionerna på kartan", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "<PERSON>i stödjer för närvarande GeoJSON-filer. <PERSON><PERSON><PERSON> {supportArticle} om du vill få tips om hur du konverterar och formaterar kartlager.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Standardlongitud", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Kartans standardmittpunkt och -zoomnivå", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Standardvärden för kartans mittpunkt och zoomnivå. Justera värdena nedan manuellt eller klicka på knappen {button} i kartans nedre vänstra hörn för att spara kartans aktuella mittpunkt och zoomnivå som standardvärden.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "<PERSON><PERSON><PERSON> kart<PERSON>, inklusive uppladdning och formatering av kartlager och inställning av kartans mittpunkt och zoomnivå.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Kartkonfiguration", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "Kartkonfigurationen delas för nä<PERSON><PERSON> mellan faser, du kan inte skapa olika kartkonfigurationer per fas.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Ta bort lager", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Spara", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Spara zoom", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "supportartikel", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Icke namngivet lager", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Kartans zoomnivå", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Kartans standardzoomnivå. Ett värde mellan 1 och 17 godk<PERSON>nns, där 1 är helt utzoomat (hela världen visas) och 17 är helt inzoomat (k<PERSON><PERSON> och byggnader visas)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Anonymisera alla användardata", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Alla uppgifter från användarna i undersökningen kommer att anonymiseras innan de registreras.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Användarna måste fortfarande uppfylla kraven för deltagande under fliken \"Access Rights\" (åtkomsträttigheter). Användarprofiluppgifter kommer inte att finnas tillgängliga i exporten av undersökningsdata.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "Om du aktiverar det här alternativet visas användarregistreringsfälten på den sista sidan i undersökningen i stället för som en del av registreringsprocessen.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Demografiska fält i enkätformulär", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Visa demografiska fält i enkäten?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "<PERSON><PERSON><PERSON> mer om hur bildelning fungerar i den här artikeln.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Automatisk delning av resultat", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Röstningsresultaten delas på plattformen och via e-post till deltagarna när fasen avslutas. Detta säkerställer transparens som standard.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Resultatdelning", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Lägg till ett svarsalternativ", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Lägg till en omröstningsfråga", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "<PERSON><PERSON><PERSON> s<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "<PERSON><PERSON><PERSON> f<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Exportera omröstningsresultaten", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Det maximala antalet val är större än antalet alternativ", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "Inga alternativ", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "Alla fr<PERSON>gor måste ha svarsval", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Endast ett alternativ", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "De som svarar på omröstningen har bara ett val", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Hantera svarsalternativ för: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "<PERSON>är kan du skapa omr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kon<PERSON><PERSON><PERSON> svar<PERSON>val för deltagare att välja mellan för varje fråga, bestämma om du vill att deltagare bara ska kunna välja ett svarsval (ett enda val) eller flera svarsval (flerval) samt exportera omröstningsresultaten. Du kan skapa flera omröstningsfrågor inom en omröstning.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Spara", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "Spara", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "Spara", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Ett enda val", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Omröstningar – inställningar och resultat", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Fel max", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "<PERSON><PERSON> <PERSON><PERSON>, lägg till ämnen eller kopiera inlägg till nästa projektfas.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "<PERSON><PERSON> förslag, ge feedback och tilldela ämnen.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Indatahanteraren", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "Resultatdelning är avstängd.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Omröstningsresultaten kommer inte att delas i slutet av fasen om du inte ändrar det i fasinställningen.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Dessa resultat kommer att delas automatiskt när fasen avslutas. Ändra slutdatumet för den här fasen för att ändra när resultaten ska delas.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Exportera undersökningsresultaten (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Resultat", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Här kan du ladda ner resultaten från Typeform-undersökningen/-undersökningarna inom det här projektet som en Excel-fil.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "Enkätformulär", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Undersökningsresultat", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Undersö<PERSON>ning", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Se undersökningssvaren", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Lägg till sakfråga", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "<PERSON>r du säker?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Beskrivning", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "<PERSON>v<PERSON>nd det här för att förklara vad som krävs av personer som anmäler sig frivilligt och vad de kan förvänta sig.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Det gick inte att spara eftersom formuläret innehåller fel.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Bild", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Titel", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Rediger<PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "En sakfråga är en åtgärd eller aktivitet som deltagare kan anmäla sig som frivilliga till.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Lägg till en beskrivning", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "<PERSON><PERSON><PERSON> till en titel", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Exportera personer som anmäler sig frivilligt", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "En sakfråga är en åtgärd eller aktivitet som deltagare kan anmäla sig som frivilliga till.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "Ny sakfråga", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Spara", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Här kan du konfigurera sakfrågorna som användare kan anmäla sig frivilligt till och ladda ner information om personer som anmäler sig frivilligt.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "<PERSON>m<PERSON><PERSON> sig frivilligt", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {inga anmälde sig frivilligt} one {# anmälde sig frivilligt} other {# anmälde sig frivilligt}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "Budgetanslag", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Tilldela en budget till alternativen och be deltagarna att välja de alternativ som de föredrar och som ryms inom den totala budgeten.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Tilldelning av budgetmedel", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Att tillåta användare att kommentera kan snedvrida omröstningsprocessen.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "Kredit", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Standardvy av alternativ", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Åtgärder för <PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Välj vilka ytterligare åtgärder användarna kan vidta.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Fast belopp", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "Om den lämnas tom är standardinställningen \"rösta\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "<PERSON><PERSON><PERSON> mer om när du ska använda <b> {voteTypeDescription} </b> i vår {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Maximalt antal röster per alternativ", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Maxim<PERSON><PERSON> antal <PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Du kan begränsa antalet röster som en användare kan avge totalt (med högst en röst per alternativ).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "flera röster per alternativ", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Användarna får en mängd tokens att fördela mellan alternativen", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Flera röster per alternativ", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "<PERSON><PERSON> rö<PERSON> per användare", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Översikt över analys av alternativ", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Alternativ att rösta om", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "en röst per alternativ", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Användare kan välja att godkänna något av alternativen", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "En röst per alternativ", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Token", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Obegränsad", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Vad ska en omrö<PERSON>ning kallas?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "T.ex. polletter, p<PERSON><PERSON><PERSON>, koldioxidkrediter...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "T.ex. symbol, poäng, koldioxidkredit...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "Omr<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Varje omröstningsmetod har olika förkonfigurationer", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "Omröstningsmetod", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "Röstningsmetoden bestämmer reglerna för hur användarna röstar", "app.containers.AdminPage.ProjectEdit.addNewInput": "Lägg till indata", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "Du kan lägga till ditt projekt i en mapp nu, eller göra det senare i projektinställningarna", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Projektämnen", "app.containers.AdminPage.ProjectEdit.altText": "Alt-text", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Anonym omröstning", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "N<PERSON>r funktionen har aktiverats går det inte att se vem som röstade på vad. Användare behöver fortfarande ha ett konto och kan bara rösta en gång.", "app.containers.AdminPage.ProjectEdit.approved": "Godkänd", "app.containers.AdminPage.ProjectEdit.archived": "Arkiverad", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Arkiverade projekt är fortfarande synliga, men tillåter inte ytterligare deltagande", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Arkiverad", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Du kan inte ta bort det här området eftersom det används för att visa projekt på följande ytterligare anpassade sidor. Du måste ta bort länken till området från sidan eller ta bort sidan innan du kan ta bort området.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Alla om<PERSON>", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Projektet kommer att visas i alla områdesfilter.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Områdesfilter", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projekt kan filtreras på startsidan med områden. Områden kan ställas in {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "<PERSON>är", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Inget specifikt område", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Projektet kommer inte att visas inte vid filtrering efter område.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Urval", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Projektet kommer att visas i valda områdesfilter.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "Mest diskuterade", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Lägg till innehåll i undersökningen", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Bidragen till denna undersökning har bör<PERSON>t komma in. Ändringar i undersökningen kan leda till dataförlust och ofullständiga data i de exporterade filerna.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Enkäten har sparats framgångsrikt", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "Undersö<PERSON>ning", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "Visa översikt", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Välj en röstningsmetod och låt användarna prioritera mellan några olika alternativ.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eller prioritering", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Skapa ett projekt från en mall", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Creați un sondaj extern", "app.containers.AdminPage.ProjectEdit.createInput": "Lägg till ny inmatning", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Skapa en undersökning på plattformen", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Skapa en undersökning utan att lämna vår plattform.", "app.containers.AdminPage.ProjectEdit.createPoll": "Skapa en omröstning", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Skapa ett flervalsformulär.", "app.containers.AdminPage.ProjectEdit.createProject": "Nytt projekt", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "<PERSON><PERSON>dda in en Typeform-, Google Formulär-, Enalyzer-, Qualtrics- eller SurveyXact-undersökning.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Du kan ange standardordningen för inlägg som ska visas på huvudprojektsidan.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Sortering", "app.containers.AdminPage.ProjectEdit.departments": "Avdelningar", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Beskrivning", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "<PERSON><PERSON> kommer att aktivera eller inaktivera ogillande, men gillande kommer fortfarande att vara aktiverat. Vi rekommenderar att du låter detta vara inaktiverat om du inte genomför en alternativanalys.", "app.containers.AdminPage.ProjectEdit.disabled": "Inaktiverad", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Antal ogillanden per deltagare", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Aktivera ogillande", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Insamling av feedback på ett dokument", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "<PERSON><PERSON><PERSON><PERSON> in en interaktiv PDF och samla in kommentarer och feedback med Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Inaktiverad", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Aktiverad", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Utkast till projekt är dolda för alla personer utom administratörer och tilldelade projektledare.", "app.containers.AdminPage.ProjectEdit.draft": "Utkast", "app.containers.AdminPage.ProjectEdit.draftStatus": "Utkast", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Hantera", "app.containers.AdminPage.ProjectEdit.enabled": "Aktiverad", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Välj vilka deltagande åtgärder användarna kan vidta.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Event", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Bilagor (max 50 MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Filer bör inte vara större än 50 MB. Tillagda filer visas på projektets informationssida.", "app.containers.AdminPage.ProjectEdit.filesTab": "Filer", "app.containers.AdminPage.ProjectEdit.findVolunteers": "<PERSON>ta personer som anmäler sig frivilligt", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Be deltagare att anmäla sig frivilligt för aktiviteter och sakfrågor.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "<PERSON><PERSON> mapphanterare kan du välja en mapp när du skapar projektet, men endast en administratör kan ändra den efteråt", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Mappkort bild alternativ text", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Välj en mapp att lägga till detta projekt i.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Anpassat innehåll", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Det har börjat komma inlämningar till det här formuläret. Ändringar av formuläret kan leda till förlust av data och ofullständiga uppgifter i de exporterade filerna.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "<PERSON><PERSON><PERSON><PERSON> har sparats", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Undersökningens slut", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Från en mall", "app.containers.AdminPage.ProjectEdit.generalTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Formulär", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Alt-text för <PERSON>", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Rubrikbild", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NY", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Ge information till anv<PERSON><PERSON><PERSON>, eller använd rapportbyggaren för att dela resultat från tidigare faser.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Dela information eller resultat", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Samla in indata och återkoppling", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "<PERSON><PERSON><PERSON> eller samla in input, reaktioner och/eller kommentarer. Välj mellan olika typer av input: idéins<PERSON>ling, analys av alternativ, fr<PERSON><PERSON> och svar, identifiering av problem och mycket mer.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Vem ans<PERSON>ar för att bearbeta inläggen?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Alla nya indata i det här projektet kommer att tilldelas den här personen. Den tilldelade personen kan ändras i {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Kommenterar på inlägg", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Indataformulär", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "indatahanteraren", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Indatahanteraren", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Reagera på input", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Standardvy", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Välj standardvy för att visa indata – kort i en rutnätsvy eller nålar på en karta. Deltagare kan växla manuellt mellan de två vyerna.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Inspirationsnav", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Bädda in Konveio URL", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Antal gilla per deltagare", "app.containers.AdminPage.ProjectEdit.limited": "Begränsade", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "<PERSON><PERSON><PERSON> in fler mallar", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Karta", "app.containers.AdminPage.ProjectEdit.mapTab": "Karta", "app.containers.AdminPage.ProjectEdit.maxDislikes": "Maximalt antal ogillanden", "app.containers.AdminPage.ProjectEdit.maxLikes": "Maximalt antal likes", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Maximalt antal röster per alternativ måste vara mindre än eller lika med det totala antalet röster", "app.containers.AdminPage.ProjectEdit.maximum": "Max", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "<PERSON><PERSON><PERSON> kan inte överskrida den här budgeten när de skickar in sin korg.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Min", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Kräv att deltagare uppfyller en minimibudget för att skicka sin korg (ange 0 om du inte vill sätta ett minimum).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "besök vårt hjälpcenter", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Vilka är projektledarna?", "app.containers.AdminPage.ProjectEdit.moreDetails": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "Behöver du inspiration? Utforska liknande projekt från andra städer på {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "Lägg till ett bidrag", "app.containers.AdminPage.ProjectEdit.newIdea": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newInitiative": "Lägg till ett initiativ", "app.containers.AdminPage.ProjectEdit.newIssue": "Lägg till en fråga", "app.containers.AdminPage.ProjectEdit.newOption": "Lägg till ett alternativ", "app.containers.AdminPage.ProjectEdit.newPetition": "Lägg till en namninsamling", "app.containers.AdminPage.ProjectEdit.newProject": "Nytt projekt", "app.containers.AdminPage.ProjectEdit.newProposal": "Lägg till ett förslag", "app.containers.AdminPage.ProjectEdit.newQuestion": "Lägg till en fråga", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "<PERSON>", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Inte ett giltigt belopp", "app.containers.AdminPage.ProjectEdit.noFolder": "Ingen mapp", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "- Ingen mapp -", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Inga mallar hittades", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Ange en projekttitel", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Inte ett giltigt nummer", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Visas endast för administrat<PERSON>rer", "app.containers.AdminPage.ProjectEdit.optionNo": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.optionYes": "Ja (välj mapp)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Deltagandeniv<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Vad vill du göra?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Välj hur användare kan delta.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "Du kan ange vem som kan utföra varje åtgärd och ställa ytterligare frågor till deltagarna för att samla in mer information.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Deltagarnas krav och frågor", "app.containers.AdminPage.ProjectEdit.pendingReview": "Avvaktar godkännande", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Åtkomsträttigheter", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "Behörigheter", "app.containers.AdminPage.ProjectEdit.phaseEmails": "Meddelanden", "app.containers.AdminPage.ProjectEdit.pollTab": "Omr<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "<PERSON><PERSON><PERSON> reaktioner", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Projektkortsbild", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Den här bilden ingår i projektkortet – kortet som sammanfattar projektet och som till exempel visas på startsidan.\n\n    Mer information om rekommenderade bildupplösningar finns i {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Mapp", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Den här bilden visas högst upp på projektsidan.\n\n    Mer information om rekommenderade bildupplösningar finns i {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Projektkort bild alternativ text", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Ge en kort beskrivning av bilden för synskadade användare. Detta hj<PERSON>lper skärmläsare att förmedla vad bilden handlar om.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Projektledning", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Projektledare kan redigera projekt, hantera inlägg och skicka e-post till deltagare. Du kan {moderationInfoCenterLink} för att hitta mer information om rättigheterna som tilldelas projektledare.", "app.containers.AdminPage.ProjectEdit.projectName": "Projektnamn", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Projekttyp", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Projekt med en tidslinje har en tydlig bör<PERSON> ett tydligt och slut och kan ha olika faser. Projekt utan tidslinje är kontinuerliga.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Projekttypen kan inte ändra<PERSON> senare.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "Du kan ställa in projektet så att det är osynligt för vissa användare.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Projektets synlighet", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Letar du efter projektstatus? Nu kan du ändra den när som helst direkt från sidhuvudet på projektsidan.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Publicerade projekt är synliga för alla eller för en del av en grupp om den har valts.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Publicerad", "app.containers.AdminPage.ProjectEdit.purposes": "Syften", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Slumpmässig", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Återställ deltagardata", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Ett fel inträffade när du sparade dina uppgifter. Vänligen försök igen.", "app.containers.AdminPage.ProjectEdit.saveProject": "Spara", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Sparad!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "<PERSON>tt form<PERSON>r har sparats!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Sök i mallarna", "app.containers.AdminPage.ProjectEdit.selectGroups": "<PERSON><PERSON><PERSON><PERSON> grupp(er)", "app.containers.AdminPage.ProjectEdit.setup": "Inställning", "app.containers.AdminPage.ProjectEdit.shareInformation": "Information", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Konfigurera och anpassa ditt projekt.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "besök vårt supportcenter", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Lägg till innehåll i undersökningen", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage} % ({choiceCount, plural, no {# val} one {# val} other {# val}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON><PERSON>, jag vill lämna", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "Rediger<PERSON>", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Bidragen till denna undersökning har bör<PERSON>t komma in. Ändringar i undersökningen kan leda till dataförluster och ofullständiga data i de exporterade filerna.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "Uppladdning av filer", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "Gå tillbaka", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Import", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "AI-sammanfattningar för uppföljningsfrågor med korta svar, långa svar och sentimentskala kan nås från AI-fliken i vänster sidofält.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "<PERSON><PERSON><PERSON><PERSON> skala", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "L<PERSON>ngt svar", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Flerval – välj flera", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Bildval - välj många", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "Ny inlä<PERSON>ning", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Inga undersökningssvar ännu", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "<PERSON><PERSON><PERSON> för svar", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "<PERSON><PERSON><PERSON> för svar", "app.containers.AdminPage.ProjectEdit.survey.optional2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "Om ingen logik läggs till kommer undersökningen att följa sitt normala flöde. Om både sidan och dess frågor har logik kommer frågelogiken att ha företräde. Se till att detta stämmer överens med ditt avsedda undersökningsflöde. För mer information, besök {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "Plats", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Är du säker på att du vill lämna?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "<PERSON><PERSON> nuvarande ändringar kommer inte att sparas.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Ranking", "app.containers.AdminPage.ProjectEdit.survey.rating": "Betyg", "app.containers.AdminPage.ProjectEdit.survey.required2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {svar} one {svar} other {svar}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# svar} one {# svar} other {# svar}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Flerval – välj ett", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Sentiment linj<PERSON>r skala", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Uppladdning av Esri shapefile", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Undersökningen har sparats", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Undersö<PERSON>ning", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "<PERSON><PERSON> på enkäten", "app.containers.AdminPage.ProjectEdit.survey.text2": "<PERSON><PERSON> svar", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Totalt {count} svar", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "Visa undersökning", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "vy", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "<PERSON><PERSON><PERSON><PERSON> in webbadress", "app.containers.AdminPage.ProjectEdit.surveyService": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Du kan hitta mer information om hur du bäddar in en undersökning {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "<PERSON>är", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Du kan inte ta bort det här ämnet eftersom det används för att visa projekt på följande ytterligare anpassade sidor. \nDu måste ta bort länken till ämnet från sidan eller ta bort sidan innan du kan ta bort ämnet.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Allmänna inställningar för projektet", "app.containers.AdminPage.ProjectEdit.titleLabel": "Titel", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Välj en titel som är kort, <PERSON><PERSON><PERSON> och tydlig. Den kommer att visas i översikten i rullgardinsmenyn och på projektkorten på startsidan.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Ämnen", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Välj {topicsCopy} för det här projektet. Användare kan använda dem för att filtrera projekt efter.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Totalbudget", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Trender", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "O<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.unlimited": "Obegränsade", "app.containers.AdminPage.ProjectEdit.url": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.useTemplate": "Använd mall", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "Visa projekt", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "<PERSON>m<PERSON><PERSON> sig frivilligt", "app.containers.AdminPage.ProjectEdit.voteTermError": "Röstningstermer måste anges för alla språk", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# grupper kan se} one {# grupp kan se} other {# grupper kan se}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "<PERSON>ä<PERSON> till ett evenemang", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Ytterligare information", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Adress 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Gatuadress till platsen för evenemanget", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Adress 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "T.ex. lägenhet, svit, byggnad", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Ytterligare adressinformation som kan hjälpa till att identifiera platsen, t.ex. byggnadens namn, våningsnummer etc.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "Se supportartikeln", "app.containers.AdminPage.ProjectEvents.customButtonLink": "Extern länk", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "<PERSON><PERSON>gg till en länk till en extern URL (t.ex. evenemangstjänst eller biljettwebbplats). <PERSON><PERSON> du ställer in detta kommer standardbeteendet för närvaroknappen att åsidosättas.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Anpassad knapptext", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "<PERSON>e att knapptexten ska ha ett annat värde än \"Register\" när en extern URL har angetts.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Start", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "Slut", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Är du säker på att du vill ta bort det här evenemanget? Det går inte att ångra den här åtgärden!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Evenemangsbeskrivning", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Rediger<PERSON>", "app.containers.AdminPage.ProjectEvents.editEventTitle": "<PERSON><PERSON><PERSON> even<PERSON>", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "<PERSON><PERSON><PERSON> att skicka e-post till registranter direkt från plattformen måste administratörer skapa en användargrupp på fliken {userTabLink} . {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "<PERSON><PERSON> för <PERSON>", "app.containers.AdminPage.ProjectEvents.eventImage": "Bild av evenemang", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Händelsebild alternativ text", "app.containers.AdminPage.ProjectEvents.eventLocation": "<PERSON><PERSON><PERSON> för <PERSON>", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Exportregistranter", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Bilagor (max 50 MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Bilagor visas under evenemangsbeskrivningen.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Plats", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Högsta antal registrerade", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Skapa ett nytt evenemang", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Länk till evenemang online", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "Om ditt evenemang är online, lägg till en länk till det här.", "app.containers.AdminPage.ProjectEvents.preview": "Förhandsvisning", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Förfina kartans läge", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Förfina plats på karta", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "Du kan förfina var din evenemangsplatsmarkör visas genom att klicka på kartan nedan.", "app.containers.AdminPage.ProjectEvents.register": "Registrera", "app.containers.AdminPage.ProjectEvents.registerButton": "<PERSON><PERSON><PERSON> för registrering", "app.containers.AdminPage.ProjectEvents.registrant": "registrerad", "app.containers.AdminPage.ProjectEvents.registrants": "registrerade", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Registreringsgräns", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Spara", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "<PERSON>i kunde inte spara dina ä<PERSON>, f<PERSON>rs<PERSON><PERSON> igen.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Sparad!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "<PERSON><PERSON> har sparats.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "<PERSON><PERSON><PERSON> efter en plats", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Länka kommande evenemang till det här projektet och visa dem på projektets evenemangssida.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Titel och datum", "app.containers.AdminPage.ProjectEvents.titleEvents": "Projektevenemang", "app.containers.AdminPage.ProjectEvents.titleLabel": "Evenemangsnamn", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Länka knappen till en extern URL", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "Som standard visas knappen för registrering av evenemang i plattformen så att användarna kan registrera sig för ett evenemang. Du kan ändra detta så att det istället länkar till en extern URL.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Begränsa antalet anmälningar till evenemanget", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "<PERSON>e ett maximalt antal anmälningar till evenemanget. Om gränsen nås accepteras inga ytterligare registreringar.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/användare", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Deltagarna", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "<PERSON><PERSON><PERSON> till filer i ditt projekt", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Bifoga filer fr<PERSON>n den här listan till ditt projekt, dina faser och händels<PERSON>.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "<PERSON><PERSON><PERSON> till filer som sammanhang för Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "<PERSON><PERSON><PERSON> till filer i ditt Sensemaking-projekt för att ge sammanhang och insikter.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Kommer inom kort", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Synkronisera enkäter, ladda upp intervjuer och låt AI koppla samman dina data.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Ladda upp valfri fil", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Använd AI för att analysera filer", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Be<PERSON><PERSON> utskrifter etc.", "app.containers.AdminPage.ProjectFiles.addFiles": "Lägg till filer", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "<PERSON>-dri<PERSON>na insikter", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "Analysera uppladdade filer för att få fram viktiga ämnen.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Till<PERSON>t avancerad analys av dessa filer med hjälp av AI-bearbetning.", "app.containers.AdminPage.ProjectFiles.askButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.categoryLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.chooseFiles": "<PERSON><PERSON><PERSON><PERSON> filer", "app.containers.AdminPage.ProjectFiles.close": "Nä<PERSON>", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Bekräfta och ladda upp", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Är du säker på att du vill radera den här filen?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "Det gick inte att ladda markdown-filen.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "Det gick inte att ladda CSV-förhandsgranskning.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "Maximalt 50 rader visas i CSV-förhandsgranskningar.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "CSV-filen är för stor för att förhandsgranska.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "Ta bort fil", "app.containers.AdminPage.ProjectFiles.description": "Beskrivning", "app.containers.AdminPage.ProjectFiles.done": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.downloadFile": "Ladda ner fil", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Ladda ner hela filen", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "<PERSON>a och släpp alla filer här eller", "app.containers.AdminPage.ProjectFiles.editFile": "Redigera fil", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Beskrivning", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "Filnamnet får inte innehålla en punkt.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "Filens namn", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "Filnamnet är obligatoriskt.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Ladda ner fil", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Förhandsgranskning", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "Den här filen kommer inte att laddas upp, eftersom den överskrider maxgränsen på 50 MB.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "Alla filer har laddats upp framgångsrikt", "app.containers.AdminPage.ProjectFiles.generatingPreview": "Genererar för<PERSON>visning...", "app.containers.AdminPage.ProjectFiles.info_sheet": "Information om", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "T.ex. WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, inspelningar från stadshuset", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "T.ex. PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "Rapporter, informationsdokument", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "T.ex. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "Bilder", "app.containers.AdminPage.ProjectFiles.interview": "Intervju", "app.containers.AdminPage.ProjectFiles.maxFilesError": "Du kan bara ladda upp högst {maxFiles} filer <PERSON><PERSON> gången.", "app.containers.AdminPage.ProjectFiles.meeting": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.noFilesFound": "Inga filer hittades.", "app.containers.AdminPage.ProjectFiles.other": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.policy": "Policy", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Förhandsgranskning stöds ännu inte för den här filtypen.", "app.containers.AdminPage.ProjectFiles.report": "Rapport", "app.containers.AdminPage.ProjectFiles.retryUpload": "Försök ladda upp på nytt", "app.containers.AdminPage.ProjectFiles.save": "Spara", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "Filen uppdaterades framgångsrikt.", "app.containers.AdminPage.ProjectFiles.searchFiles": "<PERSON><PERSON><PERSON> filer", "app.containers.AdminPage.ProjectFiles.selectFileType": "Filtyp", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Strategisk plan", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "Du kan bara ladda upp högst {maxFiles} filer <PERSON><PERSON> gången.", "app.containers.AdminPage.ProjectFiles.unknown": "Okä<PERSON>", "app.containers.AdminPage.ProjectFiles.upload": "Ladda upp", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# fil} other {# filer}} uppladdad framgångsrikt, {numberOfErrors, plural, one {# fel} other {# fel}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "Visa fil", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Minimera alla fält", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Fältbeskrivning", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Redigera inmatningsformulär", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Aktiverad", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Inkludera det här fältet.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "<PERSON><PERSON><PERSON> gick fel, <PERSON><PERSON><PERSON><PERSON><PERSON> igen senare", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Expandera alla fält", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Indataformulär", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Ange vilken information som ska tillhandahållas, lägg till korta beskrivningar eller instruktioner för att vägleda deltagares svar och ange om respektive fält är valfritt eller obligatoriskt.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Ange vilken information som ska tillhandahållas, lägg till korta beskrivningar eller instruktioner för att vägleda deltagares svar och ange om respektive fält är valfritt eller obligatoriskt", "app.containers.AdminPage.ProjectIdeaForm.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Kräv att det här fältet fylls i.", "app.containers.AdminPage.ProjectIdeaForm.save": "Spara", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "<PERSON><PERSON> har sparats.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Sparad!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "Visa formulär", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Automatiserade e-postmeddelanden", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Du kan konfigurera e-postmeddelanden som skickas när en ny fas inleds", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Datum", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "<PERSON><PERSON>a på enkäten", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Undersö<PERSON>ning", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "<PERSON>r du säker på att du vill ta bort den här fasen?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Fasbeskrivning", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "Redigera fas", "app.containers.AdminPage.ProjectTimeline.endDate": "Slut<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Slut<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Bilagor (max 50 MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Skapa en ny fas", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "Denna fas har inget fördefinierat slutdatum.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Vissa metoders resultatdelning (t.ex. omröstningsresultat) kommer inte att aktiveras förrän ett slutdatum har valts.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "Så snart du lägger till en fas efter denna, kommer den att lägga till ett slutdatum för denna fas.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "Att inte välja ett slutdatum för detta innebär att:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Förhandsvisning", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Det uppstod ett fel när formuläret skickades, försök igen.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Sparad!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "<PERSON><PERSON> har sparats.", "app.containers.AdminPage.ProjectTimeline.startDate": "Startdatum", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Startdatum", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "Undersökningens titel", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Fasnamn", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Ladda upp bilagor", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartition", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologi (startsidefilter)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Vad ska ämnen i förstasidesfiltret kallas för? T.ex. ämnen, kategorier, avdelningar, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Ämnen kan konfigureras {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "<PERSON>är", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "<PERSON><PERSON> för ett ämne (singular)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "ämne", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "<PERSON><PERSON> för fl<PERSON> (plural)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "ämnen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Lägg till fält", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Lägg till nytt registreringsfält", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Lägg till alternativ", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Svarsformat", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Ange ett svarsformat", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "<PERSON><PERSON> ett svarsalternativ för alla språk", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> har sparats", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Dra och släpp fälten för att bestämma i vilken ordning de visas i registreringsformuläret.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Standardfält", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Valfri text som visas under fältnamnet på registreringsformuläret.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "<PERSON><PERSON><PERSON>val för bostadsort kan anges i {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Rediger<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Beskrivning", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Fältnamn", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Ange ett fältnamn för alla språk", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Fältinställningar", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "<PERSON><PERSON><PERSON><PERSON><PERSON> (kryssruta)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Datum", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "L<PERSON>ngt svar", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Flerval (välj flera)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Numeriskt värde", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Flerval (välj en)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "<PERSON><PERSON> svar", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Flik med geografiska områden", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "<PERSON><PERSON> fält", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Ska det vara obligatoriskt att svara på det här fältet?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Anpassade fält", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Lägg till svarsalternativ", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Är du säker på att du vill ta bort det här svarsalternativet för registreringsfrågan? Alla poster som specifika användare har besvarat med det här alternativet kommer att raderas permanent. Den här åtgärden kan inte ångras.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Är du säker på att du vill radera denna registreringsfråga? Alla svar som användare har gett på denna fråga kommer att raderas permanent och den kommer inte längre att ställas i projekt eller förslag. Den här åtgärden kan inte ångras.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Spara fält", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "<PERSON><PERSON><PERSON>et har sparats", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "Två kolumner", "app.containers.AdminPage.SettingsPage.addAreaButton": "Lägg till ett geografiskt område", "app.containers.AdminPage.SettingsPage.addTopicButton": "Lägg till ämne", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Djur - t.ex. <PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Användare - t.ex. Användare 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "Välj vilka administratörer som ska få meddelanden om att godkänna projekt. Mapphanterare är som standard godkännare för alla projekt inom sina mappar.", "app.containers.AdminPage.SettingsPage.approvalSave": "Spara", "app.containers.AdminPage.SettingsPage.approvalTitle": "Inställningar för projektgodkännande", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Är du säker på att du vill ta bort det här området?", "app.containers.AdminPage.SettingsPage.areaTerm": "<PERSON><PERSON> för ett område (singular)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "område", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Rediger<PERSON>", "app.containers.AdminPage.SettingsPage.areasTerm": "<PERSON><PERSON> för flera o<PERSON> (plural)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "områden", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "<PERSON><PERSON><PERSON><PERSON> minst ett språk.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Ava<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Visa avatarer", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Visa deltagares profilbilder och antal för icke-registrerade be<PERSON><PERSON>e", "app.containers.AdminPage.SettingsPage.bannerHeader": "Rubriktext", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "<PERSON><PERSON><PERSON><PERSON> för <PERSON>-registrerade be<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Underrubriktext för i<PERSON>-registrerade besökare", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Underrubriktext", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Bannertext", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Visa förhandsvisning för", "app.containers.AdminPage.SettingsPage.brandingDescription": "Lägg till din logotyp och konfigurera plattformsfärgerna.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Plattformens varumärke", "app.containers.AdminPage.SettingsPage.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.chooseLayout": "Layout", "app.containers.AdminPage.SettingsPage.color_primary": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.color_secondary": "Sekundär färg", "app.containers.AdminPage.SettingsPage.color_text": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.colorsTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.confirmHeader": "Är du säker på att du vill ta bort det här ämnet?", "app.containers.AdminPage.SettingsPage.contentModeration": "Innehållsmoderering", "app.containers.AdminPage.SettingsPage.ctaHeader": "Knappar", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Anpassad sidrubrik | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Anpassad", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Knapptext", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Knapplänk", "app.containers.AdminPage.SettingsPage.defaultTopic": "Standardämne", "app.containers.AdminPage.SettingsPage.delete": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Detta kommer att ta bort ämnet från alla befintliga inlägg. Den här ändringen kommer att gälla för alla projekt.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "<PERSON><PERSON>gg till och ta bort ämnen som du vill använda på din plattform för att kategorisera inlägg. Du kan lägga till ämnena i specifika projekt i {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Skrivbord", "app.containers.AdminPage.SettingsPage.editFormTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Rediger<PERSON>", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "<PERSON><PERSON>a <PERSON>", "app.containers.AdminPage.SettingsPage.fieldDescription": "Områdesbeskrivning", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Den här beskrivningen är endast för internt samarbete och visas inte för användare.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Områdesnamn", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Ang<PERSON> ett områdesnamn för alla språk", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Namnet du väljer för respektive område kan användas som ett alternativ i ett registreringsfält och för att filtrera projekt på startsidan.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Spara <PERSON>", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Ämnesnamn", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Ange ett ämnesnamn för alla språk", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Namnet du väljer för respektive ämne kommer att vara synligt för plattformsanvändarna", "app.containers.AdminPage.SettingsPage.fixedRatio": "Banderoll med fasta förhållanden", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Den här typen av banderoll fungerar bäst med bilder som inte bör besk<PERSON>, t.ex. bilder med text, en logotyp eller specifika element som är viktiga för dina medborgare. Den här banderollen ersätts med en enfärgade ruta i den primära färgen när användarna är inloggade. Du kan ställa in den här färgen i de allmänna inställningarna. Mer information om rekommenderad bildanvändning finns i vår {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "kunskapsbas", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Banderoll med full bredd", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Den här banderollen sträcker sig över hela bredden för en fantastisk visuell effekt. Bilden försöker täcka så mycket utrymme som möjligt, vilket gör att den inte alltid är synlig hela tiden. Du kan kombinera den här banderollen med en överlagring i valfri färg. Mer information om rekommenderad bildanvändning finns i vår {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "kunskapsbas", "app.containers.AdminPage.SettingsPage.header": "Startsidebanner", "app.containers.AdminPage.SettingsPage.headerDescription": "Anpassa startsidans bannerbild och -text.", "app.containers.AdminPage.SettingsPage.header_bg": "Bannerbild", "app.containers.AdminPage.SettingsPage.helmetDescription": "Sida för administratörsinställningar", "app.containers.AdminPage.SettingsPage.helmetTitle": "Sida för administratörsinställningar", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "<PERSON><PERSON><PERSON> till ditt eget innehåll i den anpassningsbara delen längst ner på startsidan.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Startsidans rubrik | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "<PERSON><PERSON><PERSON> rör <PERSON>ldö<PERSON>lag<PERSON>", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Opacitet för bildöverlagring", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Identifiera olämpligt inneh<PERSON>ll", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Upptäck automatiskt olämpligt innehåll som publiceras på plattformen.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "När denna funktion är aktiverad kommer input, förslag och kommentarer från deltagarna att granskas automatiskt. Inlägg som flaggas för att innehålla olämpligt innehåll blockeras inte, utan markeras för granskning på sidan {linkToActivityPage} .", "app.containers.AdminPage.SettingsPage.languages": "Språk", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Du kan välja flera språk som du vill erbjuda din plattform till användarna på. Du måste skapa innehåll för varje valt språk.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Aktivitet", "app.containers.AdminPage.SettingsPage.logo": "Logotyp", "app.containers.AdminPage.SettingsPage.noHeader": "Ladda upp en rubrikbild", "app.containers.AdminPage.SettingsPage.no_button": "<PERSON><PERSON> knapp", "app.containers.AdminPage.SettingsPage.organizationName": "Namn på stad eller organisation", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Ange ett organisationsnamn eller en stad för alla språk.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Aktivera överlagring", "app.containers.AdminPage.SettingsPage.phone": "Telefon", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Plattformskonfiguration", "app.containers.AdminPage.SettingsPage.population": "Befolkning", "app.containers.AdminPage.SettingsPage.populationMinError": "Population måste vara ett positivt tal.", "app.containers.AdminPage.SettingsPage.populationTooltip": "Det totala antalet invånare på ditt territorium. Detta används för att beräkna deltagandegraden. Lämna tomt om det inte är tillämpligt.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Svordomsblockerare", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Blockera indata, förslag och kommentarer som innehåller de vanligast rapporterade stötande orden", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Den här texten visas på startsidan ovanför projekten.", "app.containers.AdminPage.SettingsPage.projectsSettings": "projektinställningar", "app.containers.AdminPage.SettingsPage.projects_header": "Projektrubrik", "app.containers.AdminPage.SettingsPage.registrationFields": "Registreringsfält", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Ge en kort beskrivning högst upp i ditt registreringsformulär.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registrering", "app.containers.AdminPage.SettingsPage.save": "Spara", "app.containers.AdminPage.SettingsPage.saveArea": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "<PERSON><PERSON><PERSON> gick fel, f<PERSON><PERSON><PERSON><PERSON> igen senare.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Sparad!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "<PERSON><PERSON> har sparats.", "app.containers.AdminPage.SettingsPage.selectApprovers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "V<PERSON><PERSON>j de områden som ska visas för användare att följa efter registrering", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Välj de ämnen som ska visas för användare att följa efter registrering", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Det gick inte att spara. Försök ändra inställningen igen.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Registrera dig\"", "app.containers.AdminPage.SettingsPage.signed_in": "<PERSON><PERSON><PERSON> för registrerade be<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.signed_out": "<PERSON><PERSON><PERSON> för <PERSON>-registre<PERSON> be<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.signupFormText": "Registreringshjälptext", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Lägg till en kort beskrivning överst i registreringsformuläret.", "app.containers.AdminPage.SettingsPage.statuses": "Status", "app.containers.AdminPage.SettingsPage.step1": "Steg för e-postadress och lösenord", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Det här visas överst på registreringsformulärets första sida (namn, e-postadress, lösenord).", "app.containers.AdminPage.SettingsPage.step2": "Registreringsfrågor – steg", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Det här visas överst på registreringsformulärets andra sida (fler registreringsfält).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Definiera de geografiska områden som du vill använda för din plattform, till exempel områden, stadsdelar eller distrikt. Du kan associera de här geografiska områdena med projekt (filtrerbara på målsidan) eller be deltagare att välja sitt bostadsområde som ett registreringsfält för att skapa smarta grupper och definiera åtkomsträttigheter.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "V<PERSON><PERSON>j hur andra ska se ditt organisationsnamn, välj språken på din plattform och länka till din webbplats.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Den angivna undertexten överskrider den högsta tillåtna teckengränsen (90 tecken)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Ange vilken information som personer ombeds lämna när de registrerar sig.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminologi", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Inställningarna har uppdaterats framgångsrikt.", "app.containers.AdminPage.SettingsPage.tabAreas1": "Områden", "app.containers.AdminPage.SettingsPage.tabBranding": "Varumärkesprofilering", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Status för bidrag", "app.containers.AdminPage.SettingsPage.tabPolicies": "Policy", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Godkännande av projekt", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Status för förslag", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registrering", "app.containers.AdminPage.SettingsPage.tabSettings": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabTopics2": "Etiketter", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Surfplatta", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Definiera vilken geografisk enhet du vill använda för dina projekt (t.ex. områden, distrikt och stadsdelar)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geografiska områden", "app.containers.AdminPage.SettingsPage.titleBasic": "Allmänna inställningar", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Den angivna titeln överskrider den högsta tillåtna teckengränsen (35 tecken)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Ämneshanteraren", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Den här banderollen är särskilt användbar för bilder som inte fungerar bra tillsammans med text från titeln, undertexten eller knappen. De här objekten kommer att flyttas till en placering under banderollen. Mer information om rekommenderad bildanvändning finns i vår {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "kunskapsbas", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON><PERSON><PERSON> rader", "app.containers.AdminPage.SettingsPage.urlError": "Webbadressen är inte giltig", "app.containers.AdminPage.SettingsPage.urlPatternError": "Ange en giltig URL.", "app.containers.AdminPage.SettingsPage.urlTitle": "Webbp<PERSON>s", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Du kan lägga till en länk till din egen webbplats. Den här länken kommer att användas längst ner på startsidan.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "V<PERSON><PERSON>j hur användare som saknar namn i sin profil ska visas i plattformen. Detta kommer att ske när du ställer in åtkomsträttigheterna för en fas till \"E-postbekräftelse\". I samtliga fall kommer användarna att kunna uppdatera det profilnamn som vi autogenererat åt dem när de deltar.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Visning av användarnamn (endast för användare med bekräftad e-post)", "app.containers.AdminPage.SideBar.administrator": "Administratör", "app.containers.AdminPage.SideBar.communityPlatform": "Gemenskapsplattform", "app.containers.AdminPage.SideBar.community_monitor": "Gemenskapens övervakare", "app.containers.AdminPage.SideBar.customerPortal": "Kundportal", "app.containers.AdminPage.SideBar.dashboard": "Dashboard", "app.containers.AdminPage.SideBar.emails": "E-postmeddelanden", "app.containers.AdminPage.SideBar.folderManager": "Mapphanterare", "app.containers.AdminPage.SideBar.groups": "Grupper", "app.containers.AdminPage.SideBar.guide": "Guide", "app.containers.AdminPage.SideBar.inputManager": "Indatahanteraren", "app.containers.AdminPage.SideBar.insights": "Rapportering", "app.containers.AdminPage.SideBar.inspirationHub": "Inspirationsnav", "app.containers.AdminPage.SideBar.knowledgeBase": "Kunskapsbas", "app.containers.AdminPage.SideBar.language": "Språk", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Sidor och meny", "app.containers.AdminPage.SideBar.messaging": "Meddelanden", "app.containers.AdminPage.SideBar.moderation": "Aktivitet", "app.containers.AdminPage.SideBar.notifications": "Notiser", "app.containers.AdminPage.SideBar.processing": "Bearbetning", "app.containers.AdminPage.SideBar.projectManager": "Projektledare", "app.containers.AdminPage.SideBar.projects": "Projekt", "app.containers.AdminPage.SideBar.settings": "Inställningar", "app.containers.AdminPage.SideBar.signOut": "Logga ut", "app.containers.AdminPage.SideBar.support": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.toPlatform": "Till plattformen", "app.containers.AdminPage.SideBar.tools": "Verktyg", "app.containers.AdminPage.SideBar.user.myProfile": "<PERSON> profil", "app.containers.AdminPage.SideBar.users": "Deltagarna", "app.containers.AdminPage.SideBar.workshops": "Workshops", "app.containers.AdminPage.Topics.addTopics": "<PERSON><PERSON><PERSON> till", "app.containers.AdminPage.Topics.browseTopics": "Bläddra bland ämnen", "app.containers.AdminPage.Topics.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.confirmHeader": "Är du säker på att du vill ta bort det här projektämnet?", "app.containers.AdminPage.Topics.delete": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.deleteTopicLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Det här ämnet kommer inte längre att kunna läggas till i nya inlägg i det här projektet.", "app.containers.AdminPage.Topics.inputForm": "Indataformulär", "app.containers.AdminPage.Topics.lastTopicWarning": "Minst ett ämne krävs. Om du inte vill använda ämnen kan de inaktiveras på fliken {ideaFormLink}.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Du kan lägga till och ta bort ämnen som kan till<PERSON>s inlägg i det här projektet.", "app.containers.AdminPage.Topics.remove": "<PERSON> bort", "app.containers.AdminPage.Topics.title": "Projektämnen", "app.containers.AdminPage.Topics.topicManager": "Ämneshanteraren", "app.containers.AdminPage.Topics.topicManagerInfo": "Om du vill lägga till ytterligare projektämnen kan du göra det i {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Lägg till en ny grupp", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Gruppnamn", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Ange ett gruppnamn", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Skapa en manuell grupp", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Vilken typ av grupp behöver du?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Spara grupp", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Skapa en manuell grupp", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Skapa en smart grupp", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "<PERSON><PERSON><PERSON> mer om grupper", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Du kan välja användare från översikten och lägga till dem i den här gruppen.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Du kan definiera att villkor och användare som uppfyller villkoren automatiskt läggs till i den här gruppen.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "<PERSON><PERSON> grupp", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Smart grupp", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Det finns ingen i den här gruppen ännu", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Gå till {allUsersLink} för att manuellt lägga till några användare.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Ingen användare matchar din sökning", "app.containers.AdminPage.Users.GroupsPanel.select": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Exportera alla", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Exportera användare i grupp", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Export vald", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Är du säker på att du vill ta bort den här gruppen?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Ett fel uppstod när användare lades till i grupperna, försök igen.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Ta bort från grupp", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Vill du ta bort valda användare från den här gruppen?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "<PERSON>tt fel uppstod när användare skulle tas bort från gruppen, fö<PERSON><PERSON><PERSON> igen.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Lägg till användare i gruppen", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "<PERSON><PERSON><PERSON> till", "app.containers.AdminPage.groups.permissions.add": "<PERSON><PERSON><PERSON> till", "app.containers.AdminPage.groups.permissions.addAnswer": "<PERSON><PERSON><PERSON> till ett svar", "app.containers.AdminPage.groups.permissions.addQuestion": "Lägg till demografiska frågor", "app.containers.AdminPage.groups.permissions.answerChoices": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.answerFormat": "Svarsformat", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "<PERSON>st ett val måste anges.", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "Den här användaren är moderator för mappen som innehåller det här projektet. Om du vill ta bort moderatorrättigheterna för det här projektet kan du antingen återkalla mapprättigheterna eller flytta projektet till en annan mapp.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Skapa en ny fråga", "app.containers.AdminPage.groups.permissions.createAQuestion": "Skapa en fråga", "app.containers.AdminPage.groups.permissions.defaultField": "Standardfält", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Ange en titel för alla alternativ", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "<PERSON><PERSON><PERSON><PERSON><PERSON> (kryssruta)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Datum", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "L<PERSON>ngt svar", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Flera val (vä<PERSON>j flera)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Numeriskt värde", "app.containers.AdminPage.groups.permissions.fieldType_select": "Flera val (välj en)", "app.containers.AdminPage.groups.permissions.fieldType_text": "<PERSON><PERSON> svar", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Att ändra granulerade behörigheter ingår inte i din licens. Kontakta din GovSuccess Manager för att få veta mer om det.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Är du säker på att du vill ta bort den här gruppen från projektet?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Vä<PERSON>j en eller flera grupper", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {Inga medlemmar} one {1 medlem} other {{count} medlemmar}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Fyll i rubriken på alla språk", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "<PERSON>r du säker?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Projektledare hittades inte", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Ingenting visas eftersom det inte finns några åtgärder som användaren kan vidta i det här projektet.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Endast administratörer kan skapa en ny fråga.", "app.containers.AdminPage.groups.permissions.option1": "Alternativ 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Väntande inbjudan", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Vem kan kommentera dokumentet?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Vem kan anmäla sig för att delta i ett evenemang?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Vem kan kommentera input?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Vem kan kommentera förslagen?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Vem kan lägga upp ett förslag?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Vem kan reagera på inmatningar?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Vem kan lämna in bidrag?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Vem kan göra omröstningen?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Vem kan delta i undersökningen?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Vem kan anmäla sig som frivillig?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Vem kan rösta om förslagen?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Vem får rö<PERSON>?", "app.containers.AdminPage.groups.permissions.questionDescription": "Beskrivning av frågan", "app.containers.AdminPage.groups.permissions.questionTitle": "<PERSON><PERSON><PERSON><PERSON> titel", "app.containers.AdminPage.groups.permissions.save": "Spara", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "<PERSON><PERSON><PERSON> gick fel, f<PERSON><PERSON><PERSON><PERSON> igen senare.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Sparad!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "<PERSON><PERSON> har sparats.", "app.containers.AdminPage.groups.permissions.select": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.selectValueError": "Välj en typ av svar", "app.containers.AdminPage.new.createAProject": "Skapa ett projekt", "app.containers.AdminPage.new.fromScratch": "<PERSON><PERSON><PERSON> scratch", "app.containers.AdminPage.phase.methodPicker.addOn1": "<PERSON><PERSON><PERSON> till", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "<PERSON>-dri<PERSON>na insikter", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Hjälp deltagarna att visa upp enighet och oenighet, en idé i taget.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "<PERSON>ta en gemensam grund", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "<PERSON><PERSON><PERSON><PERSON> in en interaktiv PDF och samla in kommentarer och feedback med Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Insamling av feedback på ett dokument", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Bädda in en undersökning från tredje part", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "Extern undersökning", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "<PERSON><PERSON><PERSON> deltagare dela och diskutera idéer genom karta eller idékort.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Idégenering - sam<PERSON> in synpunkter, idéer och förslag", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Information", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Saknar AI-drivna insikter på plattformen", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Saknar plattformsrapportering och datavisualisering och bearbetning", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Länk till rapportbyggare i plattformen", "app.containers.AdminPage.phase.methodPicker.logic1": "Logik", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Brett utbud av frågetyper", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Ge deltagare möjlighet att ladda upp idéer med en tids- och röstbegränsning genom karta eller idékort.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "E-petion - samla in förslag som invånare kan rösta på", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "Snabb omröstning", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Skapa en kort enkät med flera svarsalternativ.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, visualisera resultat och skapa rapporter.", "app.containers.AdminPage.phase.methodPicker.survey1": "Undersö<PERSON>ning", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Gör en undersökning med hjälp av ett brett utbud av frågetyper.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Undersökningsalternativ", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Enkät", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Be deltagare ställa upp som volontärer eller deltagare på aktiviteter, uppdrag eller evenemang.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Rekrytering av deltagare eller volontärer", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Välj en omröstningsmetod och låt deltagare prioritera mellan olika alternativ genom karta eller idékort.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eller prioritering", "app.containers.AdminPage.projects.all.all": "<PERSON>a", "app.containers.AdminPage.projects.all.createProjectFolder": "Ny folder", "app.containers.AdminPage.projects.all.existingProjects": "Befintliga projekt", "app.containers.AdminPage.projects.all.homepageWarning1": "Anv<PERSON>nd den här sidan för att ställa in ordningen på projekten i rullgardinsmenyn \"Alla projekt\" i navigeringsfältet. Om du använder widgetarna \"Publicerade projekt och mappar\" eller \"Projekt och mappar (äldre)\" på din startsida kommer ordningen på projekten i dessa widgetar också att bestämmas av den ordning du anger här.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "Projekt där du är projektledare visas här.", "app.containers.AdminPage.projects.all.noProjects": "Inga projekt hittades.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Endast administratörer kan skapa projektmappar.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projekt och mappar", "app.containers.AdminPage.projects.all.publishedTab": "Publicerad", "app.containers.AdminPage.projects.all.searchProjects": "Sök projekt", "app.containers.AdminPage.projects.all.yourProjects": "<PERSON><PERSON> proje<PERSON>", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "AI-analys", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Noggrannhet: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "Istället för att sammanfatta kan du ställa relevanta frågor till dina data. Den här funktionen ingår inte i din nuvarande plan. Prata med din Government Success Manager el<PERSON> administrat<PERSON>r för att låsa upp den.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Ställ en fråga", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "<PERSON>na insikt omfattar följande frågor:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "<PERSON><PERSON><PERSON> fr<PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "<PERSON>r du säker på att du vill ta bort den här frågan?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Är du säker på att du vill radera dessa sammanfattningar?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "<PERSON><PERSON> kommer att visas här, men du har för närvarande inga ännu.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "<PERSON><PERSON>a på knappen Auto-summarize o<PERSON> för att komma igång.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "v<PERSON>a <PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Att ställa frågor om färre indata leder till en högre noggrannhet. Minska det aktuella urvalet av indata genom att använda taggar, sök- eller demografiska filter.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Fråga till alla input", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Bedöm kvaliteten på denna insikt", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "<PERSON><PERSON>tä<PERSON> filter", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Sammanfatta", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Sammanfattning för", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Sammanfattning för alla inmatningar", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "Tack för din <PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "AI kan inte bearbeta så många indata på en gång. Dela upp dem i mindre grupper.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "Du kan sammanfatta högst 30 inmatningar åt gången med din nuvarande plan. Prata med din GovSuccess Manager eller administratör om du vill veta mer.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "Jag förstår", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Vår plattform gör det möjligt för dig att utforska de viktigaste temana, sammanfatta data och undersöka olika perspektiv. Om du letar efter specifika svar eller insikter kan du överväga att använda funktionen \"Ställ en fråga\" för att dyka djupare bortom sammanfattningen.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "Även om det är sällsynt kan AI ibland generera information som inte uttryckligen fanns i det ursprungliga datasetet.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Hallucinationer:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "AI:n kan betona vissa teman eller idéer mer än andra, vilket kan snedvrida den övergripande tolkningen.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Överdrivning:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Vårt system är optimerat för att hantera 20-200 väldefinierade indata för de mest exakta resultaten. När datavolymen ökar utöver detta intervall kan sammanfattningen bli mer övergripande och generaliserad. Detta innebär inte att AI blir \"mindre exakt\", utan snarare att den kommer att fokusera på bredare trender och mönster. För mer nyanserade insikter rekommenderar vi att du använder (auto)-taggningsfunktionen för att segmentera större datamängder i mindre, mer hanterbara delmängder.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Datavolym och noggrannhet:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "Vi rekommenderar att AI-genererade sammanfattningar används som en utgångspunkt för att förstå stora datamängder, men inte som det slutgiltiga ordet.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "Hur man arbetar med AI", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Lägg till valda indata till tagg", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "Lägg till tagg", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "Den här funktionen ingår inte i din nuvarande plan. Prata med din Government Success Manager el<PERSON> administra<PERSON><PERSON><PERSON> för att låsa upp den.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "<PERSON><PERSON> tag<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "<PERSON><PERSON>, jag gör det", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Vill du automatiskt tilldela ingångar till din tagg?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "<PERSON> finns <b>o<PERSON>a metoder</b> att automatiskt tilldela inmatningar till taggar.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Använd <b>knappen för automatisk märkning</b> för att starta den metod du föredrar.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Klicka på en tagg för att tilldela den till den markerade bidraget.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "<PERSON><PERSON>, automatisk märkning", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Automatisk märkning", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Autotaggar skapas automatiskt av datorn. Du kan när som helst ändra eller ta bort dem.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Automatisk märkning", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Ingångar som redan är associerade med dessa taggar kommer inte att klassificeras igen.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "Klassificeringen baseras enbart på namnet på taggen. Välj relevanta nyckelord för bästa resultat.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Etiketter: <PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "<PERSON> skapar taggarna och tilldelar manuellt några ingångar som ett exempel, datorn tilldelar resten", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Etiketter: <PERSON><PERSON> exempel", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "Liknande \"Tags: by label\" men med ökad noggrannhet eftersom du tränar systemet med bra exempel.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "<PERSON>, da<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Etiketter: <PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "<PERSON>ta fungerar bra när du har en fördefinierad uppsättning taggar eller när ditt projekt har en begränsad omfattning när det gäller taggar.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Detektera inmatningar med en betydande andel ogillade/ogillade", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Kontroversiell", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Ta bort tagg", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "Är du säker på att du vill ta bort den här taggen?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Visa inte detta igen", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "<PERSON><PERSON>a tagg", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "Lägg till namn", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Välj högst 9 taggar som du vill att ingångarna ska fördelas mellan.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "Klassificeringen baseras på de inmatningar som för närvarande tilldelas taggarna. Datorn kommer att försöka följa ditt exempel.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Etiketter: <PERSON><PERSON> exempel", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "Du har inga egna taggar ännu.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "Datorn identifierar automatiskt taggar och tilldelar dem till dina inmatningar.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Taggar: <PERSON><PERSON> automatiser<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "<PERSON><PERSON>ar bra när dina projekt omfattar ett brett spektrum av taggar. Bra ställe att börja på.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "Hur vill du tagga?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Ingångar utan taggar", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Avkänna språket för varje inmatning", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "Språk", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "Start", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "Inga aktiva filter", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "<PERSON><PERSON><PERSON>nd taggar för att dela upp och filtrera bidraget, för att göra mer exakta eller riktade sammanfattningar.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Taggar: Plattformens taggar", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Tilldela de befintliga plattformstaggarna som författaren valde när han publicerade", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Rekommenderade", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "Byt namn på tagg", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "Spara", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "Byt namn på tagg", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "<PERSON><PERSON><PERSON><PERSON> alla", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Tilldela en positiv eller negativ känsla till varje inmatning, hä<PERSON>dd från texten", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Detektering av taggar", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Använd aktuella filter", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "Vilka ing<PERSON>ngar vill du tagga?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Autotaggning av uppgift", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Kontroversiell", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "Anpassad", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Avslutad vid", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "Som exempel", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "Språk", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "NLP-tagg", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "Inga nya AI-uppgifter har utf<PERSON>rts", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Plattformstagg", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "I kö", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Startade vid", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Lyckades", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Sammanfattningsuppgift", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Utl<PERSON>st vid", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "<PERSON>a", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "Deltagare", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "Födelseår", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "Domicil", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Uppdrag", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "Filter", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "<PERSON><PERSON> kom<PERSON>r", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "<PERSON>tal reaktioner", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "<PERSON>", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Lägg till analys", "app.containers.AdminPage.projects.project.analysis.anonymous": "Anonym inmatning", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Författare efter ålder", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Författare efter hem<PERSON>", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Bakgrundsjobb", "app.containers.AdminPage.projects.project.analysis.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "Hemortsdiagrammet är för stort för att visas", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "<PERSON><PERSON><PERSON><PERSON> tomma svar", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.end": "Slut", "app.containers.AdminPage.projects.project.analysis.filter": "Visa endast inmatningar med detta värde", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "<PERSON><PERSON><PERSON><PERSON> svar utan svar", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Auto-insikter", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Kolumnvärden", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "<PERSON> finns {count} exempel på denna kombination.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "<PERSON><PERSON> illa om", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Utforska", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "Falsk", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "Bidrag", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Nästa värmekarta", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Nästa insikt", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Inte en statistiskt signifikant insikt.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Auto Insights är inte tillgängligt för projekt med färre än 30 deltagare.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "Deltagare", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Tidigare värmekarta", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Föregående insikt", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "Radvärden", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Statistiskt signifikant insikt.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Sammanfatta", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "<PERSON><PERSON> för analys", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "<PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "Enheter", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "Visa alla insikter", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Visa auto-insikter", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Ingångar utan taggar", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "En ogiltig shapefil har laddats upp och kan inte visas.", "app.containers.AdminPage.projects.project.analysis.limit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "Huvudfråga", "app.containers.AdminPage.projects.project.analysis.manageInput": "Hantera inmatning", "app.containers.AdminPage.projects.project.analysis.nextGraph": "<PERSON><PERSON><PERSON> graf", "app.containers.AdminPage.projects.project.analysis.noAnswer": "<PERSON><PERSON> svar", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "Inget svar lämnades.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "Ingen shapefil uppladdad.", "app.containers.AdminPage.projects.project.analysis.noInputs": "Inga ingångar motsvarar dina nuvarande filter", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Föregående graf", "app.containers.AdminPage.projects.project.analysis.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.remove": "<PERSON> bort", "app.containers.AdminPage.projects.project.analysis.removeFilter": "Ta bort filter", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "Ta bort filter", "app.containers.AdminPage.projects.project.analysis.search": "<PERSON>ö<PERSON>", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Shapefiler visas i GeoJSON-format här. Därför kan det hända att styling i originalfilen inte visas korrekt.", "app.containers.AdminPage.projects.project.analysis.start": "Start", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Art<PERSON>l om stöd", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "Okä<PERSON>", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "Se alla frågor", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "Visa utvalda frågor", "app.containers.AdminPage.projects.project.analysis.votes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.copied": "Kopierade till urklipp", "app.containers.AdminPage.widgets.copyToClipboard": "Ko<PERSON><PERSON> den här koden", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Kopiera HTML-koden", "app.containers.AdminPage.widgets.fieldAccentColor": "Accentfärg", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Bakgrundsfärg för widget", "app.containers.AdminPage.widgets.fieldButtonText": "Knapptext", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Gå med nu", "app.containers.AdminPage.widgets.fieldFont": "Teckensnitt", "app.containers.AdminPage.widgets.fieldFontDescription": "Det här måste vara ett befintligt teckensnittsnamn från {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "Teckenstorlek (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Rubrikundertext", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Du kan säga något", "app.containers.AdminPage.widgets.fieldHeaderText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Vår plattform för deltagande", "app.containers.AdminPage.widgets.fieldHeight": "Höjd (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldProjects": "Projekt", "app.containers.AdminPage.widgets.fieldRelativeLink": "Länkar till", "app.containers.AdminPage.widgets.fieldShowFooter": "<PERSON> knapp", "app.containers.AdminPage.widgets.fieldShowHeader": "Visa rubrik", "app.containers.AdminPage.widgets.fieldShowLogo": "Visa logotyp", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Bakgrundsfärg för webbplats", "app.containers.AdminPage.widgets.fieldSort": "Sorteras efter", "app.containers.AdminPage.widgets.fieldTextColor": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldTopics": "Ämnen", "app.containers.AdminPage.widgets.fieldWidth": "Bredd", "app.containers.AdminPage.widgets.homepage": "Startsida", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Du kan kopiera den här HTML-koden och klistra in den på den del av din webbplats där du vill lägga till din widget.", "app.containers.AdminPage.widgets.htmlCodeTitle": "HTML-kod för widget", "app.containers.AdminPage.widgets.previewTitle": "Förhandsvisning", "app.containers.AdminPage.widgets.settingsTitle": "Inställningar", "app.containers.AdminPage.widgets.sortNewest": "Nyast", "app.containers.AdminPage.widgets.sortPopular": "Populär", "app.containers.AdminPage.widgets.sortTrending": "Trender", "app.containers.AdminPage.widgets.subtitleWidgets": "Du kan skapa en widget, anpassa den och lägga till den på din egen webbplats för att locka människor till den här plattformen.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "<PERSON><PERSON><PERSON> och sidfot", "app.containers.AdminPage.widgets.titleInputSelection": "Val av indata", "app.containers.AdminPage.widgets.titleStyle": "Format", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Spara", "app.containers.ContentBuilder.homepage.PageTitle": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.SaveError": "<PERSON><PERSON><PERSON> gick fel när hemsidan sparades.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "Två kolumner", "app.containers.ContentBuilder.homepage.bannerImage": "Bannerbild", "app.containers.ContentBuilder.homepage.bannerSubtext": "Banner undertext", "app.containers.ContentBuilder.homepage.bannerText": "Text för banner", "app.containers.ContentBuilder.homepage.button": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.chooseLayout": "Layout", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Anpassning av andra inställningar än bilden och texten på startbannern ingår inte i din nuvarande licens. Kontakta din GovSuccess Manager för att få veta mer om det.", "app.containers.ContentBuilder.homepage.customized_button": "Anpassad", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Text fö<PERSON> knapp", "app.containers.ContentBuilder.homepage.customized_button_url_label": "Länk till knapp", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Visar de närmaste 3 kommande händelserna på din plattform.", "app.containers.ContentBuilder.homepage.eventsDescription": "Visar de närmaste 3 kommande händelserna på din plattform.", "app.containers.ContentBuilder.homepage.fixedRatio": "Banner med fast förhållande", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Denna typ av banner fungerar bäst med bilder som inte bör be<PERSON>, t.ex. bilder med text, en logotyp eller specifika element som är avgörande för dina medborgare. Denna banner ersätts med en solid ruta i primärfärgen när användarna är inloggade. Du kan ställa in denna färg i de allmänna inställningarna. Mer information om rekommenderad bildanvändning finns på vår {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "kunskapsbas", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "Banner i full bredd", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Denna banner sträcker sig över hela bredden för en fantastisk visuell effekt. Bilden kommer att försöka täcka så mycket utrymme som möjligt, vilket gör att den inte alltid är synlig hela tiden. Du kan kombinera denna banner med en overlay i valfri färg. Mer information om rekommenderad bildanvändning finns på vår {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "kunskapsbas", "app.containers.ContentBuilder.homepage.imageOverlayColor": "Färg på bildöverlägg", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Opacitet för bildö<PERSON>lägg", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "Ogiltig URL", "app.containers.ContentBuilder.homepage.no_button": "<PERSON><PERSON> knapp", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Icke-registrerade användare", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Aktivera överlagring", "app.containers.ContentBuilder.homepage.projectsDescription": "<PERSON><PERSON><PERSON> att konfigurera i vilken ordning dina projekt ska visas, ändra ordning på dem på {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.registeredUsersView": "Registrerade användare", "app.containers.ContentBuilder.homepage.showAvatars": "Visa avatarer", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Visa profilbilder på deltagare och antal deltagare för icke-registrerade besökare", "app.containers.ContentBuilder.homepage.sign_up_button": "Registrera dig", "app.containers.ContentBuilder.homepage.signedInDescription": "Det<PERSON> är hur registrerade användare ser bannern.", "app.containers.ContentBuilder.homepage.signedOutDescription": "Det är så besökare som inte är registrerade på plattformen ser bannern.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "Denna banner är särskilt användbar med bilder som inte fungerar bra med text från titeln, undertexten eller knappen. Dessa objekt kommer att skjutas in under bannern. Mer information om rekommenderad bildanvändning finns på vår {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "kunskapsbas", "app.containers.ContentBuilder.homepage.twoRowLayout": "<PERSON><PERSON><PERSON> rader", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "Inbäddningens höjd (pixlar)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "<PERSON><PERSON><PERSON>d som du vill att det inbäddade innehållet ska visas på sidan (i pixlar).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "<PERSON>rt beskrivning av det innehåll du bäddar in", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "Det är användbart att tillhandahålla denna information för användare som förlitar sig på en skärmläsare eller annan hjälpteknik.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Adress till webb<PERSON><PERSON>sen", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "Fullständig URL för den webbplats du vill bädda in.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Visa innehåll från en extern webbplats på din sida i en HTML iFrame. Observera att inte alla sidor kan bäddas in. Om du har problem med att bädda in en sida ska du kontrollera med sidans ägare om den är konfigurerad för att tillåta inbäddning.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Besök vår supportsida", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "<PERSON><PERSON><PERSON><PERSON> kunde detta innehåll inte bäddas in. {visitLinkMessage} för att få veta mer.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "<PERSON><PERSON> en giltig webbadress, t.ex. https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Inbäddning", "app.containers.admin.ContentBuilder.accordionMultiloc": "Dragspel", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Öppet som standard", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Text", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Detta är expanderbart dragspelsinnehåll. Du kan redigera och formatera det med hjälp av redigeraren i panelen till höger.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Titel", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Dragspelets titel", "app.containers.admin.ContentBuilder.buttonMultiloc": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.delete": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.error": "fel", "app.containers.admin.ContentBuilder.errorMessage": "Det finns ett fel i innehållet på {locale} – åtgärda problemet för att kunna spara dina ändringar", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Dölj <PERSON>garavatarer", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "Detta är en kvartalsvis, löpande undersökning som visar vad du tycker om styrning och offentliga tjänster.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "<PERSON><PERSON>a på enkäten", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Hjälp oss att ge dig bättre service", "app.containers.admin.ContentBuilder.homepage.default": "standard", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Uppmaning till handling", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Beskrivning", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "URL för primär knapp", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "URL för sekundär knapp", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Text för sekund<PERSON><PERSON> knapp", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Titel", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "<PERSON> för hemsida", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "<PERSON> för hemsida", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Bild- och textkort", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 kolumn", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projekt", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Aktivera förslag i avsnittet \"Förslag\" i adminpanelen för att låsa upp dem på startsidan", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Förslag", "app.containers.admin.ContentBuilder.imageMultiloc": "Bild", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "<PERSON>rt beskrivning av bilden", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "Att lägga till \"alt-text\" för bilder är viktigt för att göra din plattform tillgänglig för användare som använder skärmläsare.", "app.containers.admin.ContentBuilder.participationBox": "Box för deltagande", "app.containers.admin.ContentBuilder.textMultiloc": "Text", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 kolumn", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 kolumn", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 kolumner med 30 % respektive 60 % bredd", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 kolumner med 60 % respektive 30 % bredd", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 lika breda kolumner", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://exempel.se", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "Bidrag med flest reaktioner", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "Det finns inga tillgängliga inmatningar för detta projekt eller denna fas.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "<PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Visa mer", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Titel", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Totala insatser: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>ng text", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "Det finns inga tillgängliga inmatningar för detta projekt eller denna fas.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Vä<PERSON>j fas", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Deltagare", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Titel", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Registreringsavgift", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Registreringar", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Deltagarnas tidslinje", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Observera att antalet deltagare kanske inte är helt korrekt eftersom vissa uppgifter samlas in i en extern undersökning som vi inte följer upp.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "DIAGRAM", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Datumintervall", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Titel", "app.containers.admin.ReportBuilder.charts.noData": "Det finns inga data tillgängliga för de filter du har valt.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Trafikkällor", "app.containers.admin.ReportBuilder.charts.users": "Deltagarna", "app.containers.admin.ReportBuilder.charts.usersByAge": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>", "app.containers.admin.ReportBuilder.charts.usersByGender": "Anv<PERSON><PERSON><PERSON> efter kön", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Tidsplan för <PERSON>", "app.containers.admin.ReportBuilder.managerLabel1": "Projektledare", "app.containers.admin.ReportBuilder.periodLabel1": "Period", "app.containers.admin.ReportBuilder.projectLabel1": "Projekt", "app.containers.admin.ReportBuilder.quarterReport1": "Rapport från Community Monitor: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Start", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Köp 1 extra plats", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Bekräfta", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Är du säker på att du vill ge en person chefsrät<PERSON>gh<PERSON>?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Ge chefen rättigheter", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Du har nått gränsen för antalet inkluderade platser i din plan, {noOfSeats} ytterligare {noOfSeats, plural, one {plats} other {platser}} kommer att läggas till.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Lägg till status", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Standardstatusar kan inte tas bort.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Rediger<PERSON>", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Redigera status", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Statusar som för närvarande är tilldelade indata från deltagare kan inte tas bort. Du kan ta bort/ändra status från befintliga indata på fliken {manageTab}.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Den här statusen kan inte tas bort eller flyttas.", "app.containers.admin.ideaStatuses.all.manage": "Rediger<PERSON>", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Konfigurering av anpassade inmatningsstatusar ingår inte i din nuvarande plan. Prata med din Government Success Manager <PERSON><PERSON> administra<PERSON><PERSON><PERSON> för att låsa upp det.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Hantera den status som kan tilldelas deltagarnas bidrag inom ett projekt. Statusen är offentligt synlig och hjälper till att hålla deltagarna informerade.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Hantera den status som kan tilldelas förslag inom ett projekt. Statusen är synlig för allmänheten och hjälper till att hålla deltagarna informerade.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Redigera status för bidrag", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "<PERSON><PERSON><PERSON> fö<PERSON>lag<PERSON>tatus", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Vald för implementering eller nästa steg", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Godkänd", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Officiell feedback har lämnats", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.category": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.categoryDescription": "<PERSON><PERSON><PERSON><PERSON> kategorin som bäst representerar din status. Det här urvalet hjälper vårt analysverktyg att bearbeta och analysera inlägg mer exakt.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Inte passar in på något av de andra alternativen", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldColor": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescription": "Statusbeskrivning", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Ange en statusbeskrivning för alla språk", "app.containers.admin.ideaStatuses.form.fieldTitle": "Statusnamn", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Ange ett statusnamn för alla språk", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Genom<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Genom<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "Förslaget är inte stödberättigande", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "<PERSON><PERSON> valbar", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Oberättigad eller inte vald för att gå vidare", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "<PERSON><PERSON> valt", "app.containers.admin.ideaStatuses.form.saveStatus": "Spara status", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Övervägs för implementering eller nästa steg", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Under övervägande", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Har setts men ännu inte bearbetats", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Har setts", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Hantera indata och deras statusar.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Indatahanteraren | Plattform för deltagande för {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "<PERSON><PERSON> <PERSON><PERSON>, lägg till ämnen och flytta indata från ett projekt till ett annat", "app.containers.admin.ideas.all.inputManagerPageTitle": "Indatahanteraren", "app.containers.admin.ideas.all.tabOverview": "Översikt", "app.containers.admin.import.importInputs": "Importera indata", "app.containers.admin.import.importNoLongerAvailable3": "Denna funktion är inte längre tillgänglig här. F<PERSON>r att importera indata till en idéfas, gå till fasen och välj \"Importera\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 extra administratörsplats} other {# ytterligare administratörsplatser}} och {managerSeats, plural, one {1 ytterligare chefsplats} other {# ytterligare chefsplatser}} kommer att läggas till över gränsen.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 ytterligare administratörsplats kommer att läggas till över gränsen} other {# ytterligare administratörsplatser kommer att läggas till över gränsen}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {Ytterligare 1 plats för projektledare kommer att läggas till utöver gränsen} other {# ytterligare platser för projektledare kommer att läggas till över gränsen}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Bekräfta och skicka ut inbjudningar", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Bekräfta inverkan på användningen av säten", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Du har nått gränsen för antalet tillgängliga platser i din plan.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Administratörer och projektledare för detta projekt", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Endast administratörer och medarbetare", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Endast plattformsadministratörer, mapphanterare och projektledare kan vidta åtgärden.", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Vem som helst", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "<PERSON><PERSON> som he<PERSON>t, <PERSON><PERSON> oregistrerade användare, kan delta.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Urval", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Användare i specifika användargrupper kan delta. Du kan hantera användargrupper på fliken \"Användare\".", "app.containers.admin.project.permissions.viewingRightsTitle": "Vilka kan se det här projektet?", "app.containers.phaseConfig.enableSimilarInputDetection": "Aktivera detektering av liknande inmatning", "app.containers.phaseConfig.similarInputDetectionTitle": "Detektering av liknande indata", "app.containers.phaseConfig.similarInputDetectionTooltip": "Visa deltagare liknande inmatning medan de skriver för att undvika dubbletter.", "app.containers.phaseConfig.similarityThresholdBody": "Tröskelvärde för lik<PERSON>t (kropp)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "Detta styr hur lika två beskrivningar måste vara för att flaggas som liknande. Använd ett värde mellan 0 (strikt) och 1 (eftergiven). Lägre värden ger färre men mer exakta matchningar.", "app.containers.phaseConfig.similarityThresholdTitle": "Tröskelvärde för lik<PERSON>t (titel)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "Detta styr hur lika två titlar måste vara för att flaggas som liknande. Använd ett värde mellan 0 (strikt) och 1 (eftergiven). Lägre värden ger färre men mer exakta matchningar.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "Den här funktionen är tillgänglig som en del av ett erbjudande om tidig åtkomst till och med den 30 juni 2025. Om du vill fortsätta använda den efter det datumet kan du kontakta din Government Success Manager el<PERSON> administrat<PERSON>r för att diskutera aktiveringsalternativ.", "app.containers.survey.sentiment.noAnswers2": "<PERSON>ga svar för n<PERSON>.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 kommentarer} one {1 kommentar} other {# kommentarer}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Deltagare är användare eller besökare som har deltagit i ett projekt, publicerat eller interagerat med ett förslag eller deltagit i evenemang.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "Deltagare", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Deltagandefrekvens", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "<PERSON><PERSON> av be<PERSON><PERSON><PERSON>na som blir deltagare.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Totalt antal deltagare", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Automatiserade kampanjer", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automatiserade e-postmeddelanden", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "<PERSON><PERSON><PERSON> {quantity} kamp<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Anpass<PERSON> kampanjer", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Anpassade e-postmeddelanden", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "E-postmeddelanden", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Totalt antal e-postmeddelanden som skickats", "app.modules.commercial.analytics.admin.components.Events.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.events": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Totalt antal evenemang som lagts till", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Kommande", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Accepterad", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Inbjudn<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Väntande", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Totalt antal inbjudningar som skickats", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Gå till Indatahanteraren", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Indata", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Aktiv", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Projekt som inte är arkiverade och som visas i tabellen Aktiva på startsidan.", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Arkiverad", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Utkast till projekt", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Alla arkiverade projekt och aktiva tidslinjeprojekt som har avslutats räknas här.", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projekt", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Totalt antal projekt", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Antalet projekt som är synliga på plattformen.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "<PERSON><PERSON> regis<PERSON>", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Registreringsfrekvens", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Procentandel av besökarna som blir registrerade användare.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registreringar", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Totalt antal registreringar", "app.modules.commercial.analytics.admin.components.Tab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "De senaste 30 dagarna:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "De senaste 7 dagarna:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Sidvisningar per besök", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Besökets varaktighet", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Besökare\" är antalet unika besökare. Om en person besöker plattformen flera gånger räknas hen bara en gång.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Besök", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Besök\" är antalet sessioner. Om en person har besökt plattformen flera gånger räknas varje besök.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "I går:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "R<PERSON>k<PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Språk", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Numărul de vizitatori", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Procentul de vizitatori", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "faceți clic aici", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "<PERSON><PERSON><PERSON> att se hela listan över hänvisare, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Trafikkällor", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Besök", "app.modules.commercial.analytics.admin.components.totalParticipants": "Totalt antal deltagare", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Det finns ännu inga besöksdata.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Vi har ändrat vårt sätt att samla in och visa besöksdata. Som ett resultat är besöksdata mer exakta och fler typer av data är tillgängliga, samtidigt som de fortfarande är GDPR-kompatibla. Medan de data som används för besökarnas tidslinje går längre tillbaka, börja<PERSON> vi samla in data för \"Besökslängd\", \"Sidvisningar per besök\" och de andra graferna först i november 2024, så innan dess finns inga data tillgängliga. Om du väljer data före november 2024 bör du därför vara medveten om att vissa grafer kan vara tomma eller se konstiga ut.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "E-postleveranser över tid", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "<PERSON><PERSON><PERSON> över tid", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Registreringar över tid", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Datum", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistik", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Övergripande statistik", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Besö<PERSON> och besökare över tid", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Totalt över period", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Språk", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Direktlänk", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Procent<PERSON><PERSON> andel av be<PERSON><PERSON>k", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "Hänvisare", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Refererande webbplatser", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "S<PERSON>k<PERSON>rer", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Sociala nätverk", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO-omdirigeringar", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Trafikkälla", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Webbplatser", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Du kan ta bort den här innehållsflaggan genom att välja det här objektet och klicka på knappen Ta bort högst upp. Då visas det sedan på flikarna Visat eller Ej visat", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Olämpligt innehåll har identifierats automatiskt.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Inga inlägg har rapporterats för granskning av communityn eller flaggats för olämpligt innehåll av vårt naturliga system för behandling av naturligt språk", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Ta bort {numberOfItems, plural, one {innehållsvarning} other {# innehållsvarningar}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Rapporterad som olämplig av en plattformsdeltagare.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Innehållsvarningar", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Rapportbyggare", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Sidor som visas i navigeringsfältet", "app.modules.navbar.admin.containers.addProject": "Lägg till projekt i navigeringsfältet", "app.modules.navbar.admin.containers.createCustomPageButton": "Skapa anpassad sida", "app.modules.navbar.admin.containers.deletePageConfirmation": "Är du säker på att du vill ta bort den här sidan? Du kan inte ångra den här åtgärden. Du kan också ta bort sidan från navigeringsfältet om du inte är redo att ta bort den ännu.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Du kan bara lägga till upp till 5 objekt i navigeringsfältet", "app.modules.navbar.admin.containers.pageHeader": "Sidor och meny", "app.modules.navbar.admin.containers.pageSubtitle": "Ditt navigeringsfält kan visa upp till fem sidor utöver start- och projektsidorna. Du kan byta namn på menyobjekt, <PERSON>ndra ordning på och lägga till nya sidor med ditt eget innehåll.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widgets", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Använd ☰-ikonen nedan för att dra AI-innehåll till din rapport.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "Det finns inga tillgängliga AI-insikter. Du kan skapa dem i ditt projekt.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Gå till projekt", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Vä<PERSON>j fas", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "<PERSON><PERSON>s upp AI-analys", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Dra in AI-genererade insikter i din rapport", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Rapportera snabbare med AI", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Rapportering med AI ingår inte i din nuvarande plan. Prata med din Government Success Manager f<PERSON><PERSON> att lå<PERSON> upp den här funktionen.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "Det<PERSON> ingår inte i din nuvarande plan. Kontakta din Government Success Manager <PERSON><PERSON> administra<PERSON><PERSON><PERSON> för att låsa upp den.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Gruppera efter registreringsfält", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Grupp per undersökningsfråga", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Gruppläge", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Gruppera enkätsvar efter registreringsfält (kön, plats, ålder etc.) eller andra enk<PERSON>.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "Ingen", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Registreringsfält", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Undersökningsfas", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "Enkätfråga", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Är du säker på att du vill ta bort det här?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "Rediger<PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "<PERSON><PERSON><PERSON> in din kommentar", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "Spara", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "Skriv din kommentar här", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Klicka på knapparna nedan för att följa eller avfölja. Antalet projekt visas inom parentes.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "I ditt omr<PERSON>de", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "<PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "<PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "Det finns för närvarande inga aktiva projekt med tanke på dina följarinställningar.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "<PERSON>na widget visar projekt som är kopplade till de \"områden\" som användaren följer. Observera att din plattform kan använda ett annat namn för \"områden\" - se fliken \"Områden\" i plattformsinställningarna. Om användaren inte följer några områden ännu kommer widgeten att visa de tillgängliga områden som kan följas. I detta fall kommer widgeten att visa maximalt 100 områden.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "Inga publicerade projekt eller mappar tillgängliga", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Publicerade projekt och mappar", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "<PERSON>na widget kommer att visa de projekt och mappar som för närvarande är publicerade, med hänsyn till den ordning som anges på projektsidan. Detta beteende är detsamma som fliken \"aktiv\" i widgeten \"äldre\" projekt.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "Inga projekt eller mappar valda", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Välj projekt eller mappar", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Valda projekt och mappar", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "Med den här widgeten kan du välja och bestämma i vilken ordning du vill att projekt eller mappar ska visas för användarna.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projekt", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "besök vårt supportcenter", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Mer information om rekommenderade bildupplösningar finns i {supportPageLink}."}
{"UI.FormComponents.required": "richiesto", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Testo dell'intestazione per gli utenti registrati", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Attenzione: il colore che hai selezionato non ha un contrasto abbastanza alto. <PERSON>o può risultare in un testo difficile da leggere. Scegli un colore più scuro per ottimizzare la leggibilità.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Aggiungi Eventi alla barra di navigazione", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "Quando è abilitato, un link a tutti gli eventi del progetto sarà aggiunto alla barra di navigazione.", "app.components.AdminPage.SettingsPage.eventsSection": "Eventi", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Homepage sezione personalizzabile", "app.components.AnonymousPostingToggle.userAnonymity": "Anonimato dell'utente", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Consentire agli utenti di partecipare in modo anonimo", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Gli utenti possono ancora scegliere di partecipare con il loro vero nome, ma avranno la possibilità di inviare contributi in forma anonima se lo desiderano. Tutti gli utenti dovranno comunque rispettare i requisiti stabiliti nella scheda Diritti di accesso affinché i loro contributi vengano accettati. I dati del profilo utente non saranno disponibili nell'esportazione dei dati di partecipazione.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Per saperne di più sull'anonimato degli utenti, consulta il nostro sito {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "articolo di supporto", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Once additional seats are added, your billing will be increased. Reach out to your GovSuccess Manager to learn more about it.", "app.components.ProjectTemplatePreview.close": "Close", "app.components.ProjectTemplatePreview.createProject": "<PERSON><PERSON><PERSON> un progetto", "app.components.ProjectTemplatePreview.goBack": "Go back", "app.components.ProjectTemplatePreview.goBackTo": "Torna al {goBackLink}.", "app.components.ProjectTemplatePreview.infoboxLine1": "Vuoi usare questo modello per il tuo progetto di partecipazione?", "app.components.ProjectTemplatePreview.infoboxLine2": "Rivolgiti alla persona responsabile nell'amministrazione della tua città, o contatta un {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Project folder", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "La data selezionata non è valida. Si prega di fornire una data nel seguente formato: YYYY-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Seleziona una data di inizio del progetto", "app.components.ProjectTemplatePreview.projectStartDate": "La data di inizio del suo progetto", "app.components.ProjectTemplatePreview.projectTitle": "Il titolo del tuo progetto", "app.components.ProjectTemplatePreview.projectTitleError": "Per favore, scrivi il titolo di un progetto", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Si prega di digitare un titolo di progetto per tutte le lingue", "app.components.ProjectTemplatePreview.projectsOverviewPage": "pagina panoramica dei progetti", "app.components.ProjectTemplatePreview.responseError": "Ops, qualcosa è andato storto.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.successMessage": "Il progetto è stato creato con successo!", "app.components.ProjectTemplatePreview.typeProjectName": "Scrivi il nome del progetto", "app.components.ProjectTemplatePreview.useTemplate": "Usa questo modello", "app.components.SeatInfo.additionalSeats": "Additional seats", "app.components.SeatInfo.additionalSeatsToolTip": "This shows the number of additional seats you have purchased on top of 'Included seats'.", "app.components.SeatInfo.adminSeats": "Admin seats", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} admin seats included", "app.components.SeatInfo.adminSeatsTooltip1": "Administrators are in charge of the platform and they have manager rights for all folders and projects. You can {visitHelpCenter} to learn more about the different roles.", "app.components.SeatInfo.currentAdminSeatsTitle": "Current admin seats", "app.components.SeatInfo.currentManagerSeatsTitle": "Current manager seats", "app.components.SeatInfo.includedAdminToolTip": "This shows the number of available seats for admins included in the yearly contract.", "app.components.SeatInfo.includedManagerToolTip": "This shows the number of available seats for managers included in the yearly contract.", "app.components.SeatInfo.includedSeats": "Included seats", "app.components.SeatInfo.managerSeats": "Manager seats", "app.components.SeatInfo.managerSeatsTooltip": "Folder/project managers can manage an unlimited number of folders/projects. You can {visitHelpCenter} to learn more about the different roles.", "app.components.SeatInfo.managersIncludedText": "{managerSeats} manager seats included", "app.components.SeatInfo.remainingSeats": "Remaining seats", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Total seats", "app.components.SeatInfo.totalSeatsTooltip": "This shows the summed number of seats within your plan and additional seats you have purchased.", "app.components.SeatInfo.usedSeats": "Used seats", "app.components.SeatInfo.view": "View", "app.components.SeatInfo.visitHelpCenter": "visit our help center", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Your plan has {adminSeatsIncluded}. Once you've used all the seats, extra seats will be added under 'Additional seats'.", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Your plan has {manager<PERSON><PERSON><PERSON><PERSON>ncluded}, eligible for folder managers and project managers. Once you've used all the seats, extra seats will be added under 'Additional seats'.", "app.components.UserSearch.addModerators": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.UserSearch.searchUsers": "Digita per cercare gli utenti...", "app.components.admin.Graphs": "No data available with the current filters.", "app.components.admin.Graphs.noDataShort": "<PERSON><PERSON><PERSON> dato disponibile.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Comments over time", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Posts over time", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Reazioni nel tempo", "app.components.admin.InputManager.onePost": "1 ingresso", "app.components.admin.PostManager.PostPreview.assignee": "Assegnatar<PERSON>", "app.components.admin.PostManager.PostPreview.cancelEdit": "Annullare la modifica", "app.components.admin.PostManager.PostPreview.currentStatus": "Current status", "app.components.admin.PostManager.PostPreview.delete": "Delete", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Sei sicuro di voler cancellare questo input? Questa azione non può essere annullata.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Sei sicuro di voler cancellare questo input? L'input sarà cancellato da tutte le fasi del progetto e non potrà essere recuperato.", "app.components.admin.PostManager.PostPreview.edit": "Edit", "app.components.admin.PostManager.PostPreview.noOne": "Non assegnato", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Il numero di volte che è stato incluso nei bilanci partecipativi di altri partecipanti", "app.components.admin.PostManager.PostPreview.picks": "<PERSON><PERSON><PERSON>: {picks<PERSON><PERSON><PERSON>}", "app.components.admin.PostManager.PostPreview.reactionCounts": "La reazione conta:", "app.components.admin.PostManager.PostPreview.save": "Save", "app.components.admin.PostManager.PostPreview.submitError": "Errore", "app.components.admin.PostManager.allPhases": "<PERSON><PERSON> le fasi", "app.components.admin.PostManager.allProjects": "All projects", "app.components.admin.PostManager.allStatuses": "Tutti gli stati", "app.components.admin.PostManager.allTopics": "<PERSON>tti i tag", "app.components.admin.PostManager.anyAssignment": "Qualsiasi amministratore", "app.components.admin.PostManager.assignedTo": "Assegnato a {assigneeName}", "app.components.admin.PostManager.assignedToMe": "Assegnato a me", "app.components.admin.PostManager.assignee": "Assignee", "app.components.admin.PostManager.bodyTitle": "Descrizione", "app.components.admin.PostManager.comments": "Comments", "app.components.admin.PostManager.components.goToInputManager": "Vai al gestore degli input", "app.components.admin.PostManager.components.goToProposalManager": "Vai al gestore delle proposte", "app.components.admin.PostManager.contributionFormTitle": "Modifica del contributo", "app.components.admin.PostManager.cost": "Costo", "app.components.admin.PostManager.currentLat": "Latitudine del centro", "app.components.admin.PostManager.currentLng": "Longitudine del centro", "app.components.admin.PostManager.currentZoomLevel": "Livello di zoom", "app.components.admin.PostManager.delete": "Delete", "app.components.admin.PostManager.deleteAllSelectedInputs": "<PERSON><PERSON>a {count} post", "app.components.admin.PostManager.deleteConfirmation": "Sei sicuro di voler cancellare questo livello?", "app.components.admin.PostManager.dislikes": "Antipatie", "app.components.admin.PostManager.edit": "Edit", "app.components.admin.PostManager.editProjects": "Modifica i progetti", "app.components.admin.PostManager.editStatuses": "Modifica gli stati", "app.components.admin.PostManager.editTags": "Modifica i tag", "app.components.admin.PostManager.editedPostSave": "Risparmiare", "app.components.admin.PostManager.exportAllInputs": "Esportare tutti i messaggi (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Esportare tutti i commenti (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Esportazione di commenti per questo progetto (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Esportazione di messaggi in questo progetto (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Esportazione di messaggi selezionati (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Esportazione di commenti per i post selezionati (.xslx)", "app.components.admin.PostManager.exports": "Esportazioni", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "<PERSON><PERSON><PERSON> come le persone vedranno il tuo nome", "app.components.admin.PostManager.feedbackBodyPlaceholder": "S<PERSON>gare questo cambiamento di stato", "app.components.admin.PostManager.fileUploadError": "Il caricamento di uno o più file non è riuscito. Controllare le dimensioni e il formato del file e riprovare.", "app.components.admin.PostManager.formTitle": "Modifica idea", "app.components.admin.PostManager.goToDefaultMapView": "Vai al centro mappe predefinito", "app.components.admin.PostManager.hiddenFieldsLink": "campi nascosti", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/en/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Suggerimento: Se stai usando Typeform, aggiungi {hiddenFieldsLink} per tenere traccia di chi ha risposto alla tua indagine.", "app.components.admin.PostManager.importError": "Il file selezionato non può essere importato perché non è un file GeoJSON valido", "app.components.admin.PostManager.inputCommentsExportFileName": "commenti_input", "app.components.admin.PostManager.inputManagerHeader": "Ingresso", "app.components.admin.PostManager.inputs": "Input", "app.components.admin.PostManager.inputsExportFileName": "ingresso", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "<PERSON>ra solo i post che hanno bisogno di feedback", "app.components.admin.PostManager.issueFormTitle": "Modifica del problema", "app.components.admin.PostManager.latestFeedbackMode": "Usa l'ultimo aggiornamento ufficiale esistente come spiegazione", "app.components.admin.PostManager.likes": "Preferiti", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Moving this input away from its current project will lose the information about its assigned phases. Do you want to proceed?", "app.components.admin.PostManager.multipleInputs": "{ideaCount} post", "app.components.admin.PostManager.newFeedbackMode": "Scrivi un nuovo aggiornamento per spiegare questo cambiamento", "app.components.admin.PostManager.noFilteredResults": "The filters you selected did not return any results", "app.components.admin.PostManager.noOne": "Unassigned", "app.components.admin.PostManager.officialUpdateAuthor": "Choose how people will see your name", "app.components.admin.PostManager.officialUpdateBody": "Explain this status change", "app.components.admin.PostManager.optionFormTitle": "Opzione di modifica", "app.components.admin.PostManager.participants": "Partecipanti", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.pbItemCountTooltip": "The number of times this has been included in other participants' participatory budgets", "app.components.admin.PostManager.postedIn": "Pubblicato in {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Modifica del progetto", "app.components.admin.PostManager.projectsTab": "Projects", "app.components.admin.PostManager.projectsTabTooltipContent": "Puoi trascinare i post per spostarli da un progetto all'altro. Nota che per i progetti della timeline, dovrai comunque aggiungere il post ad una fase specifica.", "app.components.admin.PostManager.proposedBudgetTitle": "Bilancio proposto", "app.components.admin.PostManager.publication_date": "Pubblicato su", "app.components.admin.PostManager.questionFormTitle": "Modifica domanda", "app.components.admin.PostManager.reactions": "Reazioni", "app.components.admin.PostManager.resetFiltersButton": "Reset filters", "app.components.admin.PostManager.resetInputFiltersDescription": "Resetta i filtri per vedere tutti gli input.", "app.components.admin.PostManager.saved": "Sal<PERSON><PERSON>", "app.components.admin.PostManager.setAsDefaultMapView": "Salva il punto centrale corrente e il livello di zoom come impostazioni predefinite della mappa", "app.components.admin.PostManager.statusChangeGenericError": "Si è verificato un errore, riprova più tardi o contatta il supporto.", "app.components.admin.PostManager.statusChangeSave": "Cambia stato", "app.components.admin.PostManager.statusesTab": "Status", "app.components.admin.PostManager.statusesTabTooltipContent": "Cambia lo stato di un post usando il drag and drop. L'autore originale e gli altri collaboratori riceveranno una notifica del cambiamento di stato.", "app.components.admin.PostManager.submitApiError": "Si è verificato un problema nell'invio del modulo. Verificare la presenza di eventuali errori e riprovare.", "app.components.admin.PostManager.timelineTab": "Timeline", "app.components.admin.PostManager.timelineTabTooltipText": "Trascina e rilascia i post per copiarli in diverse fasi del progetto.", "app.components.admin.PostManager.title": "Title", "app.components.admin.PostManager.topicsTab": "Tags", "app.components.admin.PostManager.topicsTabTooltipText": "Aggiungere tag a un input usando il drag and drop.", "app.components.admin.PostManager.votes": "Voti", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {<PERSON><PERSON> di un giorno} one {Un giorno} other {# giorni}} sinistra", "app.components.admin.ReportExportMenu.FileName.fromFilter": "da", "app.components.admin.ReportExportMenu.FileName.groupFilter": "gruppo", "app.components.admin.ReportExportMenu.FileName.projectFilter": "progetto", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tag", "app.components.admin.ReportExportMenu.FileName.untilFilter": "fino a", "app.components.admin.ReportExportMenu.downloadPng": "Download as PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Download as SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Scaricare Excel", "app.components.admin.SlugInput.regexError": "Lo slug può contenere solo lettere regolari minuscole (a-z), numeri (0-9) e trattini (-). Il primo e l'ultimo carattere non possono essere trattini. I trattini consecutivi (--) sono proibiti.", "app.components.admin.TerminologyConfig.saveButton": "Save", "app.components.admin.seatSetSuccess.admin": "Admin", "app.components.admin.seatSetSuccess.allDone": "<PERSON><PERSON> fatto", "app.components.admin.seatSetSuccess.close": "<PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.manager": "Direttore", "app.components.admin.seatSetSuccess.orderCompleted": "Ordine completato", "app.components.admin.seatSetSuccess.reflectedMessage": "Le modifiche apportate al piano si rifletteranno sul ciclo di fatturazione successivo.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} sono stati concessi i diritti all'utente o agli utenti selezionati.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "La raccolta di feedback su un documento è una funzione personalizzata e non è inclusa nella tua licenza attuale. Contatta il tuo GovSuccess Manager per saperne di più.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Contributo", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Maggiori informazioni su come incorporare un link per Google Forms possono essere trovate in {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/en/articles/5050525-how-to-embed-your-google-forms-survey-in-a-project-phase", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "questo articolo di supporto", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Idea", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Come dovrebbe essere chiamato un ingresso?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Emissione", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Inserisci qui il link al tuo documento Konveio. Leggi il nostro {supportArticleLink} per maggiori informazioni sulla configurazione di Konveio.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "articolo di supporto", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "E' richiesto un budget massimo", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Il numero massimo di voti per opzione deve essere inferiore o uguale al numero totale di voti.", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "È richiesto un numero massimo di voti", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Il budget minimo non può essere più grande del budget massimo", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "È richiesto un budget minimo", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Il numero minimo di voti non può essere superiore al numero massimo.", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "È richiesto un numero minimo di voti", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Opzione", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Scheda Gestione ingressi", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Configura le opzioni di voto nel sito {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Progetto", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON>", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} within plan, {noOfAdditionalSeats} additional", "app.components.seatsWithinPlan.seatsWithinPlanText": "Seats within plan", "app.containers.Admin.Campaigns.campaignFrom": "Da:", "app.containers.Admin.Campaigns.campaignTo": "A:", "app.containers.Admin.Campaigns.customEmails": "Email <PERSON>", "app.containers.Admin.Campaigns.customEmailsDescription": "Invia email personalizzate e controlla le statistiche.", "app.containers.Admin.Campaigns.noAccess": "<PERSON>i dispiace, ma sembra che tu non abbia accesso alla sezione delle email", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Automated emails", "app.containers.Admin.Invitations.addToGroupLabel": "Aggiungere queste persone a specifici gruppi di utenti manuali", "app.containers.Admin.Invitations.adminLabel1": "Assegnare agli invitati i diritti di amministratore", "app.containers.Admin.Invitations.adminLabelTooltip": "Quando selezioni questa opzione, le persone che stai invitando avranno accesso a tutte le impostazioni della tua piattaforma.", "app.containers.Admin.Invitations.configureInvitations": "3. <PERSON><PERSON><PERSON><PERSON><PERSON> gli inviti", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Non ci sono inviti che corrispondono alla tua ricerca", "app.containers.Admin.Invitations.deleteInvite": "Delete", "app.containers.Admin.Invitations.deleteInviteTooltip": "Annullare un invito ti permetterà di inviare nuovamente un invito a questa persona.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Scarica e compila il modello", "app.containers.Admin.Invitations.downloadTemplate": "Scarica il modello", "app.containers.Admin.Invitations.email": "Email", "app.containers.Admin.Invitations.emailListLabel": "Inserisci manualmente gli indirizzi e-mail delle persone che vuoi invitare. Separa ogni indirizzo con una virgola.", "app.containers.Admin.Invitations.exportInvites": "Esportare tutti gli inviti", "app.containers.Admin.Invitations.fileRequirements": "Importante: per inviare correttamente gli inviti, nessuna colonna può essere rimossa dal modello di importazione. Lascia vuote le colonne non utilizzate.", "app.containers.Admin.Invitations.filetypeError": "Tipo di file errato. Sono supportati solo i file XLSX.", "app.containers.Admin.Invitations.groupsPlaceholder": "Nessun gruppo selezionato", "app.containers.Admin.Invitations.helmetDescription": "Invitare gli utenti alla piattaforma", "app.containers.Admin.Invitations.helmetTitle": "Admin invitation dashboard", "app.containers.Admin.Invitations.importOptionsInfo": "These options will only be taken into account when they are not defined in the Excel file.\n      Please visit the {supportPageLink} for more information.", "app.containers.Admin.Invitations.importTab": "Import email addresses", "app.containers.Admin.Invitations.invitationOptions": "Invitation options", "app.containers.Admin.Invitations.invitationSubtitle": "Invite people to the platform at any point in time. They get a neutral invitation email with your logo, in which they are asked to register on the platform.", "app.containers.Admin.Invitations.invitePeople": "Invite people via email", "app.containers.Admin.Invitations.inviteStatus": "Status", "app.containers.Admin.Invitations.inviteStatusAccepted": "Accepted", "app.containers.Admin.Invitations.inviteStatusPending": "Pending", "app.containers.Admin.Invitations.inviteTextLabel": "Opzionalmente, digita un messaggio che sarà aggiunto alla mail di invito.", "app.containers.Admin.Invitations.invitedSince": "Invitato", "app.containers.Admin.Invitations.invitesSupportPageURL": "http://support.govocal.com/en/articles/1771605-invite-people-to-the-platform", "app.containers.Admin.Invitations.localeLabel": "Seleziona la lingua dell'invito", "app.containers.Admin.Invitations.moderatorLabel": "Dare a queste persone i diritti di gestione del progetto", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Quando selezioni questa opzione, agli invitati saranno assegnati i diritti di project manager per i progetti selezionati. Maggiori informazioni sui diritti di project manager {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "here", "app.containers.Admin.Invitations.name": "Name", "app.containers.Admin.Invitations.processing": "Invio degli inviti. Si prega di attendere...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "<PERSON><PERSON><PERSON> progetto selezio<PERSON>o", "app.containers.Admin.Invitations.save": "In<PERSON>re inviti", "app.containers.Admin.Invitations.saveErrorMessage": "Si sono verificati uno o più errori e gli inviti non sono stati inviati. Si prega di correggere l'errore o gli errori elencati di seguito e riprovare.", "app.containers.Admin.Invitations.saveSuccess": "Successo!", "app.containers.Admin.Invitations.saveSuccessMessage": "Invito inviato con successo.", "app.containers.Admin.Invitations.supportPage": "pagina di supporto", "app.containers.Admin.Invitations.supportPageLinkText": "Visita la pagina di supporto", "app.containers.Admin.Invitations.tabAllInvitations": "<PERSON><PERSON> gli inviti", "app.containers.Admin.Invitations.tabInviteUsers": "In<PERSON>ta gli utenti", "app.containers.Admin.Invitations.textTab": "Inserire manualmente gli indirizzi e-mail", "app.containers.Admin.Invitations.unknownError": "Something went wrong. Please try again later.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. <PERSON><PERSON> il tuo file modello completato", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} se vuoi maggiori informazioni su tutte le colonne supportate nel modello di importazione.", "app.containers.Admin.Moderation.all": "All", "app.containers.Admin.Moderation.belongsTo": "Appartiene a", "app.containers.Admin.Moderation.collapse": "vedi meno", "app.containers.Admin.Moderation.comment": "Comment", "app.containers.Admin.Moderation.content": "Content", "app.containers.Admin.Moderation.date": "Data", "app.containers.Admin.Moderation.goToComment": "Apri questo commento in una nuova scheda", "app.containers.Admin.Moderation.goToPost": "<PERSON>i questo post in una nuova scheda", "app.containers.Admin.Moderation.goToProposal": "Apri questa proposta in una nuova scheda", "app.containers.Admin.Moderation.markFlagsError": "Impossibile contrassegnare l'articolo(i). Prova di nuovo.", "app.containers.Admin.Moderation.markNotSeen": "Contrassegna {selectedItemsCount, plural, one {# item} other {# itemsi}} come non visto", "app.containers.Admin.Moderation.markSeen": "Contrassegna {selectedItemsCount, plural, one {# item} other {# items}} come visto", "app.containers.Admin.Moderation.moderationsTooltip": "Questa pagina ti permette di controllare rapidamente tutti i nuovi input che sono stati aggiunti alla tua piattaforma, compresi i post e i commenti. Puoi contrassegnare i post come \"visti\" in modo che gli altri sappiano quali input devono ancora essere elaborati.", "app.containers.Admin.Moderation.noUnviewedItems": "Non ci sono oggetti non visti", "app.containers.Admin.Moderation.noViewedItems": "Non ci sono oggetti visti", "app.containers.Admin.Moderation.pageTitle1": "Alimentazione", "app.containers.Admin.Moderation.post": "Invia", "app.containers.Admin.Moderation.profanityBlockerSetting": "Blocco delle bestemmie", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Blocca i post che contengono le parole offensive più comunemente segnalate.", "app.containers.Admin.Moderation.project": "Project", "app.containers.Admin.Moderation.read": "Visto", "app.containers.Admin.Moderation.readMore": "Read more", "app.containers.Admin.Moderation.removeFlagsError": "Impossibile rimuovere gli avvisi. Riprova.", "app.containers.Admin.Moderation.rowsPerPage": "Righe per pagina", "app.containers.Admin.Moderation.settings": "Impostazioni", "app.containers.Admin.Moderation.settingsSavingError": "Non è stato possibile salvare. Prova a cambiare di nuovo l'impostazione.", "app.containers.Admin.Moderation.show": "Mostra", "app.containers.Admin.Moderation.status": "Status", "app.containers.Admin.Moderation.successfulUpdateSettings": "Impostazioni aggiornate con successo.", "app.containers.Admin.Moderation.type": "Tipo", "app.containers.Admin.Moderation.unread": "Non visto", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "This page consists of the following sections. You can turn them on/off and edit them as required.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Sections", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "View page", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Non mostrato a pagina", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Mostrato a pagina", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Attachments", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Add files (max. 50 MB) that will be available to download from the page.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Bottom info section", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Add your own content to the customizable section at the bottom of the page.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Edit", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Events list", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Mostra gli eventi relativi ai progetti.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Hero banner", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Customise the page banner image and text.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Mostra i progetti in base alle impostazioni della pagina. È possibile visualizzare in anteprima i progetti che verranno mostrati.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Top info section", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Add your own content to the customizable section at the top of the page.", "app.containers.Admin.PagesAndMenu.addButton": "Aggiungi alla barra di navigazione", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Nome nella barra di navigazione", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Sei sicuro di volerlo cancellare? Questa azione non può essere annullata.", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Altre pagine disponibili", "app.containers.Admin.PagesAndMenu.components.savePage": "Save page", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "<PERSON> successfully saved", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Attachments (max 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Couldn't save attachments", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the bottom of this page", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Attachments saved", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Attachments | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Attachments", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Salva e abilita gli allegati", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Save attachments", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Fornisci contenuti per tutte le lingue", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Couldn't save bottom info section", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Bottom info section saved", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Bottom info section", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Salva e abilita la sezione delle informazioni in basso", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Save bottom info section", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Please select at least one tag", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "Per area", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "Per tag", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Edit custom page", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "<PERSON><PERSON><PERSON> collegati", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Selezionare i progetti e gli eventi correlati che si possono visualizzare nella pagina.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "<PERSON> successfully created", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "<PERSON> successfully saved", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Custom page saved", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Titolo nella barra di navigazione", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Create custom page | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Create custom page", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Page settings", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Save custom page", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Selezionare un'area", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Area selezionata", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Tag selezionati", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "The slug can only contain regular, lowercase letters (a-z), numbers (0-9) and hyphens (-). The first and last characters cannot be hyphens. Consecutive hyphens (--) are forbidden.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "You must enter a slug", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Title", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Enter a title in every language", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Enter a title", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Visualizza pagina del cliente", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Edit custom page | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Page content", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Edit", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "<PERSON><PERSON><PERSON> progetto disponibile basato sul tuo {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Questo progetto non ha alcun filtro di tag o area, quindi non verranno visualizzati progetti.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Elenco progetti | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "impostazioni pagina", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Projects list", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "I seguenti progetti verranno visualizzati in questa pagina in base al tuo {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "PREDEFINITO", "app.containers.Admin.PagesAndMenu.deleteButton": "Delete", "app.containers.Admin.PagesAndMenu.editButton": "Edit", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Success", "app.containers.Admin.PagesAndMenu.heroBannerError": "Couldn't save hero banner", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Hero banner saved", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Save hero banner", "app.containers.Admin.PagesAndMenu.homeTitle": "Home", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Provide content for at least one language", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Pages & menu | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "R<PERSON>uovi dalla barra di navigazione", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Salva e abilita il banner dell'eroe", "app.containers.Admin.PagesAndMenu.title": "Pages & menu", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Success", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.topInfoError": "Couldn't save top info section", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Top info section saved", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Top info section | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Top info section", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Salva e abilita la sezione delle informazioni principali", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Save top info section", "app.containers.Admin.PagesAndMenu.viewButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "<PERSON><PERSON><PERSON> b<PERSON>i", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Gestire gli utenti bloccati.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Cancellare il gruppo", "app.containers.Admin.Users.GroupsHeader.editGroup": "Modifica gruppo", "app.containers.Admin.Users.GroupsPanel.allUsers": "Utenti registrati", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Gruppi", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Ottieni una panoramica di tutte le persone e organizzazioni che si sono registrate sulla piattaforma. Aggiungi una selezione di utenti ai gruppi manuali o ai gruppi Smart.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Invitation pending", "app.containers.Admin.Users.admin": "Admin", "app.containers.Admin.Users.buyOneAditionalSeat": "Acquista un posto aggiuntivo", "app.containers.Admin.Users.changeUserRights": "Change user rights", "app.containers.Admin.Users.confirm": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.confirmAdminQuestion": "Siete sicuri di voler dare a {name} i diritti di amministratore della piattaforma?", "app.containers.Admin.Users.confirmNormalUserQuestion": "Siete sicuri di voler impostare {name} come utente normale?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Siete sicuri di voler impostare {name} come utente normale? Si noti che alla conferma perderà i diritti di manager su tutti i progetti e le cartelle a cui è stato assegnato.", "app.containers.Admin.Users.deleteUser": "Cancellare l'utente", "app.containers.Admin.Users.email": "Email", "app.containers.Admin.Users.folderManager": "Gestione cartelle", "app.containers.Admin.Users.helmetDescription": "<PERSON><PERSON><PERSON> u<PERSON>ti in admin", "app.containers.Admin.Users.helmetTitle": "Admin - c<PERSON><PERSON>o utenti", "app.containers.Admin.Users.inviteUsers": "In<PERSON>ta gli utenti", "app.containers.Admin.Users.name": "Name", "app.containers.Admin.Users.options": "Options", "app.containers.Admin.Users.permissionToBuy": "Per dare a {name} i diritti di amministrazione, è necessario acquistare 1 posto aggiuntivo.", "app.containers.Admin.Users.platformAdmin": "Amministratore della piattaforma", "app.containers.Admin.Users.projectManager": "Responsabile di progetto", "app.containers.Admin.Users.reachedLimitMessage": "Avete raggiunto il limite di posti nel vostro piano, verrà aggiunto 1 posto supplementare per {name} .", "app.containers.Admin.Users.registeredUser": "Utente registrato", "app.containers.Admin.Users.seeProfile": "Vedere il profilo", "app.containers.Admin.Users.setAsAdmin": "Imposta come amministratore", "app.containers.Admin.Users.setAsNormalUser": "Impostazione come utente normale", "app.containers.Admin.Users.userBlockModal.allDone": "<PERSON><PERSON> fatto", "app.containers.Admin.Users.userBlockModal.blockAction": "Bloccare l'utente", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Il contenuto di questo utente non verrà rimosso con questa azione. Non dimenticare di moderare i suoi contenuti, se necessario.", "app.containers.Admin.Users.userBlockModal.blocked": "Bloccato", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Questo utente è stato bloccato da {from}. Il blocco dura fino a {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "Annullamento", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Sei sicuro di voler sbloccare {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} è bloccato fino a {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 giorno} other {{numberOfDays} giorni}}", "app.containers.Admin.Users.userBlockModal.header": "Bloccare l'utente", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Motivo", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Questo verrà comunicato all'utente bloccato.", "app.containers.Admin.Users.userBlockModal.subtitle1": "L'utente selezionato non potrà accedere alla piattaforma per {daysBlocked}. Se si desidera ripristinare la situazione, è possibile sbloccarlo dall'elenco degli utenti bloccati.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Sbloccare", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON>, voglio sbloccare questo utente", "app.containers.Admin.Users.userDeletionConfirmation": "Rimuovere definitivamente questo utente?", "app.containers.Admin.Users.userDeletionFailed": "Si è verificato un errore durante la cancellazione di questo utente, per favore riprova.", "app.containers.Admin.Users.userExportFileName": "esportazione_utente", "app.containers.Admin.Users.userInsights": "Approfondimenti per gli utenti", "app.containers.Admin.Users.youCantDeleteYourself": "Non puoi cancellare il tuo account tramite la pagina di amministrazione dell'utente", "app.containers.Admin.Users.youCantUnadminYourself": "Non puoi rinunciare ora al tuo ruolo di amministratore", "app.containers.Admin.emails.addCampaignTitle": "Create a new email", "app.containers.Admin.emails.allUsers": "Registered users", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Le e-mail automatiche vengono inviate automaticamente e sono attivate dalle azioni di un utente. Puoi disattivarne alcune per tutti gli utenti della tua piattaforma. Le altre email automatiche non possono essere disattivate perché sono necessarie per il corretto funzionamento della tua piattaforma.", "app.containers.Admin.emails.automatedEmails": "Email automatiche", "app.containers.Admin.emails.automatedEmailsDigest": "L'email verrà inviata solo se c'è del contenuto", "app.containers.Admin.emails.automatedEmailsRecipients": "Utenti che riceveranno questa email", "app.containers.Admin.emails.automatedEmailsTriggers": "Evento che fa scattare questa email", "app.containers.Admin.emails.changeRecipientsButton": "Change recipients", "app.containers.Admin.emails.clickOnButtonForExamples": "Clicca sul pulsante qui sotto per vedere gli esempi di questa email sulla nostra pagina di supporto.", "app.containers.Admin.emails.confirmSendHeader": "Email to all users?", "app.containers.Admin.emails.deleteButtonLabel": "Delete", "app.containers.Admin.emails.draft": "Draft", "app.containers.Admin.emails.editButtonLabel": "Edit", "app.containers.Admin.emails.editCampaignTitle": "Edit campaign", "app.containers.Admin.emails.failed": "Failed", "app.containers.Admin.emails.fieldBody": "Message", "app.containers.Admin.emails.fieldBodyError": "Fornire un messaggio e-mail per tutte le lingue", "app.containers.Admin.emails.fieldReplyTo": "Replies should go to", "app.containers.Admin.emails.fieldReplyToEmailError": "Fornire un indirizzo e-mail nel formato corretto, <NAME_EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Fornire un indirizzo e-mail", "app.containers.Admin.emails.fieldReplyToTooltip": "You can choose where to send replies to your email.", "app.containers.Admin.emails.fieldSender": "From", "app.containers.Admin.emails.fieldSenderError": "Fornire un mittente dell'e-mail", "app.containers.Admin.emails.fieldSenderTooltip": "You can decide who the recipients will see as the sender of the email.", "app.containers.Admin.emails.fieldSubject": "Email Subject", "app.containers.Admin.emails.fieldSubjectError": "Fornire un oggetto e-mail per tutte le lingue", "app.containers.Admin.emails.fieldSubjectTooltip": "This will be shown in the subject line of the email and in the user’s inbox overview. Make it clear and engaging.", "app.containers.Admin.emails.fieldTo": "To", "app.containers.Admin.emails.fieldToTooltip": "You can select the user groups that will receive your email", "app.containers.Admin.emails.formSave": "<PERSON>va come bozza", "app.containers.Admin.emails.from": "Da:", "app.containers.Admin.emails.groups": "Groups", "app.containers.Admin.emails.helmetDescription": "Send out manual emails to user groups and activate automated campaigns", "app.containers.Admin.emails.previewSentConfirmation": "A preview email has been sent to your email address", "app.containers.Admin.emails.previewTitle": "Preview", "app.containers.Admin.emails.seeEmailHereText": "Non appena verrà inviata un'e-mail di questo tipo, potrai controllarla qui.", "app.containers.Admin.emails.send": "Send", "app.containers.Admin.emails.sendNowButton": "Send now", "app.containers.Admin.emails.sendTestEmailButton": "Send me a test email", "app.containers.Admin.emails.senderRecipients": "Sender and recipients", "app.containers.Admin.emails.sending": "Sending", "app.containers.Admin.emails.sent": "<PERSON><PERSON>", "app.containers.Admin.emails.sentToUsers": "Si tratta di email inviate agli utenti", "app.containers.Admin.emails.subject": "Oggetto:", "app.containers.Admin.emails.supportButtonLabel": "Guarda gli esempi nella nostra pagina di supporto", "app.containers.Admin.emails.to": "A:", "app.containers.Admin.emails.toAllUsers": "Do you want to send this email to all registered users?", "app.containers.Admin.emails.viewExample": "Guarda", "app.containers.Admin.ideas.import": "Import", "app.containers.Admin.messaging.helmetTitle": "Messaging", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "This image is part of the folder card; the card that summarizes the folder and is shown on the homepage for example. For more information on recommended image resolutions, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "This image is shown at the top of the folder page. For more information on recommended image resolutions, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "visit our support center", "app.containers.Admin.projects.all.components.archived": "Archived", "app.containers.Admin.projects.all.components.draft": "Draft", "app.containers.Admin.projects.all.components.manageButtonLabel": "Edit", "app.containers.Admin.projects.all.copyProjectButton": "Copy project", "app.containers.Admin.projects.all.copyProjectError": "There was an error copying this project, please try again later.", "app.containers.Admin.projects.all.deleteFolderButton1": "Delete folder", "app.containers.Admin.projects.all.deleteFolderConfirm": "Are you sure you want to delete this folder? All of the projects within the folder will also be deleted. This action cannot be undone.", "app.containers.Admin.projects.all.deleteFolderError": "There was an issue removing this folder. Please try again.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Delete project", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Are you sure you want to delete this project? This cannot be undone.", "app.containers.Admin.projects.all.deleteProjectError": "There was an error deleting this project, please try again later.", "app.containers.Admin.projects.project.survey.cancel": "Cancel", "app.containers.Admin.projects.project.survey.consentModalButton": "Continue", "app.containers.Admin.projects.project.survey.consentModalCancel": "Cancel", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "I agree to using OpenAI as a data processor for this project", "app.containers.Admin.projects.project.survey.consentModalText1": "By continuing you agree to the using OpenAI as a data processor for this project.", "app.containers.Admin.projects.project.survey.consentModalText2": "The OpenAI APIs power the automated text summaries and parts of the automated tagging experience.", "app.containers.Admin.projects.project.survey.consentModalText3": "We only send what users wrote in their surveys, ideas and comments to the OpenAI APIs, never any information from their profile.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI will not use this data for further training of its models. More information on how OpenAI handles data privacy can be found {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "here", "app.containers.Admin.projects.project.survey.consentModalTitle": "Before you continue", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Delete", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Are you sure you want to delete this analysis? This action cannot be undone.", "app.containers.Admin.projects.project.survey.viewAnalysis": "View", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "This is some text. You can edit and format it by using the editor in the panel on the right.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Participants", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Project results", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Report summary", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Add the goal of the project, participation methods used, and the outcome", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Visitors", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Il titolo è già stato preso", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "No project", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "Non puoi duplicare questo report perché contiene dati a cui non hai accesso.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "Non puoi modificare questo report perché contiene dati a cui non hai accesso.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Sei sicuro di voler cancellare \"{reportName}\"? Questa azione non può essere annullata.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Sei sicuro di voler cancellare questo rapporto? Questa azione non può essere annullata.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Delete", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Dup<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Edit", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "Modificato {days, plural, no {# giorni} one {# giorno} other {# giorni}} fa", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "An error occurred when trying to create this report. Please try again later.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Start with a blank page", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Titolo <PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Creare un report", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Print to PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Start with a project template", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Report template", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "A report with this title already exists. Please pick a different title.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Share as PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "To share with everyone, print the report as a PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Share as web link", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "This web link is only accessible to admin users.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Share", "app.containers.Admin.reporting.contactToAccess": "Creating a custom report is part of the premium license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Tutti i rapporti", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Create a report", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Customise your report and share it with internal stakeholders or community with a web link.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "I tuoi report appariranno qui.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Rapporti di ricerca", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Rapporti sullo stato di avanzamento", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Questi sono i rapporti creati dal tuo Government Success Manager", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "I tuoi rapporti", "app.containers.Admin.reporting.helmetDescription": "Pagina di segnalazione degli amministratori", "app.containers.Admin.reporting.helmetTitle": "Reporting", "app.containers.Admin.reporting.printPrepare": "Preparing to print...", "app.containers.Admin.reporting.reportBuilder": "Generatore report", "app.containers.Admin.reporting.reportHeader": "Intestazione report", "app.containers.Admin.tools.apiTokens.createTokenButton": "Crea un nuovo token", "app.containers.Admin.tools.apiTokens.createTokenCancel": "Annullamento", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Crea un nuovo token da utilizzare con la nostra API pubblica.", "app.containers.Admin.tools.apiTokens.createTokenError": "Fornisci un nome per il tuo token", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Crea token", "app.containers.Admin.tools.apiTokens.createTokenName": "Nome", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Dai un nome al tuo token", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Il tuo token è stato creato", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "<PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "Copia {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Copiato!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Crea un nuovo token", "app.containers.Admin.tools.apiTokens.createdAt": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.delete": "<PERSON><PERSON> il token", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Sei sicuro di voler eliminare questo token?", "app.containers.Admin.tools.apiTokens.description": "Gestisci i tuoi token API per la nostra API pubblica. Per maggiori informazioni, consulta il nostro sito {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.link": "Documentazione API", "app.containers.Admin.tools.apiTokens.name": "Nome", "app.containers.Admin.tools.apiTokens.noTokens": "Non hai ancora nessun gettone.", "app.containers.Admin.tools.apiTokens.title": "Token API pubblici", "app.containers.Admin.tools.learnMore": "Per saperne di più", "app.containers.Admin.tools.managePublicAPIKeys": "Gestire le chiavi API", "app.containers.Admin.tools.manageWidget": "Gestisci il widget", "app.containers.Admin.tools.manageWorkshops": "Gestisci i workshop", "app.containers.Admin.tools.publicAPIDescription": "Gestisci le credenziali per creare integrazioni personalizzate sulla nostra API pubblica.", "app.containers.Admin.tools.publicAPIImage": "Immagine API pubblica", "app.containers.Admin.tools.publicAPITitle": "Accesso API pubblico", "app.containers.Admin.tools.toolsLabel": "Strumenti", "app.containers.Admin.tools.widgetDescription": "Puoi creare un widget, personaliz<PERSON><PERSON> e aggiungerlo al tuo sito web per attirare le persone su questa piattaforma.", "app.containers.Admin.tools.widgetImage": "Immagine del widget", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "Organizza riunioni video in diretta, facilita discussioni e dibattiti di gruppo simultanei. Raccogli contributi, vota e raggiungi il consenso, proprio come faresti offline.", "app.containers.Admin.tools.workshopsImage": "Immagine dei laboratori", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Workshop di deliberazione online", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "total users on the platform", "app.containers.AdminPage.DashboardPage._blank": "unknown", "app.containers.AdminPage.DashboardPage.allGroups": "All Groups", "app.containers.AdminPage.DashboardPage.allProjects": "All Projects", "app.containers.AdminPage.DashboardPage.allTime": "All Time", "app.containers.AdminPage.DashboardPage.comments": "Comments", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Comments", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "A base dataset is required to measure the representativeness of platform users.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Coming soon", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "We're currently working on the {fieldName} dashboard, it will be available soon", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# item is} other {# items are}} hidden in this graph. Change to {tableViewLink} to view all data.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} for user registration", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} out of {total} users included ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Show {numberOfHiddenItems} more", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Optional", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Please provide a base dataset.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Representativeness score:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "This score reflects how accurately platform user data reflects the total population. Learn more about {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Required", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Submit base data", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "table view", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Total population", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Users", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Aggiungi una fascia di età", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} e oltre", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Fascia di età", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "le fasce di età di {upperBound} e oltre non sono incluse.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Age group {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Fasce di età", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "e oltre", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Applica il raggruppamento di esempio", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "<PERSON><PERSON><PERSON> tutto", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "From", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Imposta le fasce di età per allinearle al tuo set di dati di base.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Intervallo", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Save", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "To", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Modifica fasce di età", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "This item will not be calculated.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "See less", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "See {numberOfHiddenItems} more...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Base month (optional)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Fasce di età (anno di nascita)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Coming soon", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Complete", "app.containers.AdminPage.DashboardPage.components.Field.default": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Please fill out all enabled options, or disable the options you want to omit from the graph. At least one option must be filled out.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Incomplete", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Number of total residents", "app.containers.AdminPage.DashboardPage.components.Field.options": "Options", "app.containers.AdminPage.DashboardPage.components.Field.save": "Save", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Saved", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "{setAgeGroupsLink} prima di iniziare a inserire i dati di base.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "imposta fasce di età", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Tempo medio di risposta: {days} gior<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Numero medio di giorni per rispondere", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "Feedback dato", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Stato input", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Input per stato", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Numero di input", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Aggiornamento ufficiale", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Percentuale di input", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Tempo di risposta", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Stato camb<PERSON>to", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Total", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Edit base data", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "how we calculate representativeness scores", "app.containers.AdminPage.DashboardPage.continuousType": "Without a timeline", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Cumulative total", "app.containers.AdminPage.DashboardPage.customDateRange": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.day": "day", "app.containers.AdminPage.DashboardPage.false": "false", "app.containers.AdminPage.DashboardPage.female": "female", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "La top 5 degli input per reazioni", "app.containers.AdminPage.DashboardPage.fromTo": "from {from} to {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Dashboard for activities on the platform", "app.containers.AdminPage.DashboardPage.helmetTitle": "Admin dashboard page", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Pick resource to show by project", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Pick a resource to show by tag", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Select user group", "app.containers.AdminPage.DashboardPage.male": "male", "app.containers.AdminPage.DashboardPage.month": "month", "app.containers.AdminPage.DashboardPage.noData": "There is no data to be shown.", "app.containers.AdminPage.DashboardPage.noPhase": "No phase created for this project", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Il numero di partecipanti che hanno inviato input, reagito o commentato.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "Antipatie", "app.containers.AdminPage.DashboardPage.numberOfLikes": "Preferiti", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Reazioni totali", "app.containers.AdminPage.DashboardPage.overview.management": "Gestione", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Progetti & Partecipazione", "app.containers.AdminPage.DashboardPage.overview.showLess": "Show less", "app.containers.AdminPage.DashboardPage.overview.showMore": "Show more", "app.containers.AdminPage.DashboardPage.participationPerProject": "Participation per project", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Participation per tag", "app.containers.AdminPage.DashboardPage.perPeriod": "Per {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Previous 30 days", "app.containers.AdminPage.DashboardPage.previous90Days": "Previous 90 days", "app.containers.AdminPage.DashboardPage.previousWeek": "Previous week", "app.containers.AdminPage.DashboardPage.previousYear": "Previous year", "app.containers.AdminPage.DashboardPage.projectType": "Project type : {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "Reazioni", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "This base dataset is required to calculate the representativeness of platform users compared to the total population.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Please provide a base dataset.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "See how representative your platform users are compared to the total population - based on data collected during user registration. Learn more about {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "See how representative your platform users are compared to the total population - based on data collected during user registration.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Community representativeness", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Back to dashboard", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "None of the enabled registration fields are supported at the moment.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Here you can show/hide items on the dashboard and enter the base data. Only the enabled fields for {userRegistrationLink} will appear here.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Edit base data", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "user registration", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Submit base data", "app.containers.AdminPage.DashboardPage.resolutionday": "in Days", "app.containers.AdminPage.DashboardPage.resolutionmonth": "in Months", "app.containers.AdminPage.DashboardPage.resolutionweek": "in Weeks", "app.containers.AdminPage.DashboardPage.selectProject": "Select project", "app.containers.AdminPage.DashboardPage.selectedProject": "current project filter", "app.containers.AdminPage.DashboardPage.selectedTopic": "current tag filter", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Discover what's happening on your platform.", "app.containers.AdminPage.DashboardPage.tabOverview": "Panoramica", "app.containers.AdminPage.DashboardPage.tabReports": "Reports", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Representativeness", "app.containers.AdminPage.DashboardPage.tabUsers": "Users", "app.containers.AdminPage.DashboardPage.timelineType": "Timeline", "app.containers.AdminPage.DashboardPage.titleDashboard": "Dashboard", "app.containers.AdminPage.DashboardPage.total": "Total", "app.containers.AdminPage.DashboardPage.totalForPeriod": "This {period}", "app.containers.AdminPage.DashboardPage.true": "true", "app.containers.AdminPage.DashboardPage.unspecified": "unspecified", "app.containers.AdminPage.DashboardPage.users": "Users", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Users by age", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Users by geographic area", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Users by gender", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Registrations", "app.containers.AdminPage.DashboardPage.week": "week", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Tips for choosing a favicon image: select a simple image, as the shown image size is very small. The image should be saved as a PNG, and should be square with a transparent background (or a white background if necessary). Your favicon should only be set once as changes will require some technical support.", "app.containers.AdminPage.FaviconPage.save": "Save", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Success!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Add", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Delete", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Folder managers", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Folder managers can edit the folder description, create new projects within the folder, and have project management rights over all projects within the folder. They cannot delete projects and they do not have access to projects that are not within their folder. You can {projectManagementInfoCenterLink} to find more information on project management rights.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "No match found", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "visit our Help Center", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Search users", "app.containers.AdminPage.FoldersEdit.addToFolder": "Add to folder", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Archived", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Delete this folder", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Description", "app.containers.AdminPage.FoldersEdit.draftStatus": "Draft", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Add files to this folder", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the folder page.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Descrizioni", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "There are no projects in this folder. Go back to the main Projects tab to create and add projects.", "app.containers.AdminPage.FoldersEdit.folderName": "Nome cartella", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Header image", "app.containers.AdminPage.FoldersEdit.multilocError": "All text fields must be filled in for every language.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "There are no projects that you can add to this folder.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Folder card image", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Permissions", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Folder projects", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Settings", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projects added to this folder", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Projects you can add to this folder", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Choose whether this folder is \"draft\", \"published\" or \"archived\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Published", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Remove from folder", "app.containers.AdminPage.FoldersEdit.save": "Save", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Success!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Short description", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "shown on the homepage", "app.containers.AdminPage.FoldersEdit.statusLabel": "Publication status", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Explain why the projects belong together, define a visual identity and share information.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Explain why the projects belong together, define a visual identity and share information.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Title", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Create a new folder", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Settings", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "View Folder", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Customise the hero banner image and text.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Hero banner", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Save hero banner", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Modifica i termini e le condizioni della tua piattaforma e l'informativa sulla privacy. Altre pagine, comprese le pagine About e FAQ, possono essere modificate nella scheda {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Politiche della piattaforma", "app.containers.AdminPage.PagesEdition.privacy-policy": "Privacy Policy", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Terms and Conditions", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "List of projects on the platform", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Projects dashboard", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Create new projects or manage existing projects.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projects", "app.containers.AdminPage.ProjectDashboard.published": "Published", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Close settings panel", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Column layout", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Description", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Homepage description", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Shown on the project card on the home page.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Shown on the project page. Clearly describe what the project is about, what you expect from your users and what they can expect from you.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectDescription.preview": "Preview", "app.containers.AdminPage.ProjectDescription.save": "Save", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectDescription.saved": "Saved!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Decide on which message you want to give to your audience. Edit your project and enrich it with images, videos, file attachments,… This information helps visitors to understand what your project is about.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Project description", "app.containers.AdminPage.ProjectDescription.whiteSpace": "White space", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Include border", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "Vertical height", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "Large", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Medium", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Small", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Cancel editing", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "The default latitude of the map center point. Accepts a value between -90 and 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "The default longitude of the map center point. Accepts a value between -90 and 90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Edit map layer", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Edit layer", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectEdit.MapTab.here": "here", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Import GeoJSON file", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Default latitude", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Layer color", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "All features in the layer will be styled with this color. This color will also overwrite any existing styling in your GeoJSON file.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Marker icon", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Optionally select an icon that is displayed in the markers. Click {url} to see the list of icons you can select.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Layer name", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "This layer name is shown on the map legend", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Layer tooltip", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "This text is displayed as a tooltip when hovering over the layer features on the map", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Map layers", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Default longitude", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Map default center and zoom", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Customize the map view, including uploading and styling map layers and setting the map center and zoom level.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Map configuration", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Remove layer", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Save", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "support article", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Map zoom level", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "The default zoom level of the map. Accepts a value between 1 and 17, where 1 is fully zoomed out (the entire world is visible) and 17 is fully zoomed in (blocks and buildings are visible)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Anonimizzare tutti i dati degli utenti", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Tutti i dati forniti dagli utenti del sondaggio saranno resi anonimi prima di essere registrati.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Gli utenti dovranno comunque rispettare i requisiti di partecipazione previsti dalla scheda \"Diritti di accesso\". I dati del profilo utente non saranno disponibili nell'esportazione dei dati dell'indagine.", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Aggiungi un'opzione di risposta", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Add a poll question", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "Annullamento", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Cancel", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Cancel", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Delete", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Delete", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Modifica l'opzione di risposta", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Salva le opzioni di risposta", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Modifica le opzioni di risposta", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "Modifica la domanda", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Export the poll results", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "The maximum number of choices is greater than the number of options", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Multiple choice", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "No options", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "All questions must have answer choices", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Only one option", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Poll respondents have only one choice", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Gestisci le opzioni di risposta per: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Here you can create poll questions, set the answer choices for participants to choose from for each question, decide whether you want participants to only be able to select one answer choice (single choice) or multiple answer choices (multiple choice), and export the poll results. You can create multiple poll questions within one poll.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Save", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "Save", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "Risparmia", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Single choice", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Polls settings and results", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Wrong maximum", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "Give feedback, add tags or copy posts to the next project phase.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Input manager", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Export the survey results (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Here, you can download the results of the Typeform survey(s) within this project as an Excel file.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Survey Results", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Survey", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Consult the survey answers", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Add cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Are you sure?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Use this to explain what is required from volunteers and what they can expect.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Couldn't save because the form contains errors.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Image", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Title", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Delete", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "A cause is an action or activity that participants can volunteer for.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Edit cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Add a description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Add a title", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Export volunteers", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "A cause is an action or activity that participants can volunteer for.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "New cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Save", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Here, you can set up the causes users can volunteer for and download the volunteers.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Volunteering", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {no participants} one {# participant} other {# participants}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "allocazione del budget", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Assegna un budget alle opzioni e chiedi ai partecipanti di selezionare le opzioni che preferiscono e che rientrano nel budget totale.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Assegnazione del budget", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Permettere agli utenti di commentare può influenzare il processo di votazione.", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Visualizzazione predefinita delle opzioni", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Azioni per gli utenti", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Seleziona le azioni aggiuntive che gli utenti possono compiere.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Importo fisso", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "Se viene lasciato vuoto, il valore predefinito sarà \"voto\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "Sc<PERSON><PERSON> di più su quando usare <b> {voteTypeDescription} </b> nel nostro sito {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Voti massimi per opzione", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Numero massimo di voti", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Puoi limitare il numero di voti che un utente può esprimere in totale (con un massimo di un voto per opzione).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "voti multipli per opzione", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Gli utenti ricevono una quantità di gettoni da distribuire tra le varie opzioni", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Voti multipli per opzione", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Numero di voti per utente", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Panoramica dell'analisi delle opzioni", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Opzioni da votare", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "un solo voto per opzione", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Gli utenti possono scegliere di approvare qualsiasi opzione", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Un voto per ogni opzione", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Illimitato", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Come dovrebbe chiamarsi una votazione?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "Ad esempio, gettoni, punti, crediti di carbonio...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "Ad esempio, gettone, punto, credito di carbonio...", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Ogni metodo di voto ha diverse preconfigurazioni", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "<PERSON><PERSON><PERSON> di voto", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "Il metodo di voto determina le regole di votazione degli utenti.", "app.containers.AdminPage.ProjectEdit.addNewInput": "Add an input", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Tag di ingresso consentiti", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Anonymous polling", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "When enabled it's impossible to see who voted on what. Users still need an account and can only vote once.", "app.containers.AdminPage.ProjectEdit.archived": "Archived", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Archived", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Non è possibile eliminare questa area perché viene utilizzata per visualizzare i progetti nelle seguenti pagine più personalizzate. È necessario scollegare l'area dalla pagina o eliminare la pagina prima di eliminare l'area.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "All Areas", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "The project will show on every area filter.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Area filter", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projects can be filtered on the homepage using areas. Areas can be set {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "No specific area", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "The project will not show when filtering by area.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Selection", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "The project will show on selected area filter(s).", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Cards", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Scegli un metodo di votazione e fai in modo che gli utenti scelgano una priorità tra alcune opzioni diverse.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Eseguire un esercizio di votazione o di definizione delle priorità", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Create a project from a template", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "<PERSON>rea un sondaggio esterno", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Create an in-platform survey", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Set up a survey without leaving our platform.", "app.containers.AdminPage.ProjectEdit.createPoll": "Create a poll", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Set up a multiple-choice questionnaire.", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Incorporare un'indagine Typeform, Google Form, Enalyzer, SurveyXact, Qualtrics, SmartSurvey, Snap Survey o Microsoft Forms.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "You can set the default order for posts to be displayed on the main project page.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Sorting", "app.containers.AdminPage.ProjectEdit.departments": "Departments", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Description", "app.containers.AdminPage.ProjectEdit.disabled": "Disabled", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Numero di non mi piace per partecipante", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Abilita l'antipatia", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Raccogli il feedback su un documento", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Incorpora un PDF interattivo e raccogli commenti e feedback con Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Disabled", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Enabled", "app.containers.AdminPage.ProjectEdit.draft": "Draft", "app.containers.AdminPage.ProjectEdit.draftStatus": "Draft", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEdit.enabled": "Enabled", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Seleziona le azioni partecipative che gli utenti possono intraprendere.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Events", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Attachments (max 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the project information page.", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Find volunteers", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Ask participants to volunteer for activities and causes.", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Select a folder to add this project to.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Custom content", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Submissions to this form have started to come in. Changes to the form may result in data loss and incomplete data in the exported files.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Form successfully saved", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Survey end", "app.containers.AdminPage.ProjectEdit.fromATemplate": "From a template", "app.containers.AdminPage.ProjectEdit.generalTab": "General", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Header image", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Collect input and feedback", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Crea o raccogli input, reazioni e/o commenti. Scegli tra diversi tipi di input: raccolta di idee, analisi di opzioni, domande e risposte, identificazione di problemi e altro ancora.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Who is responsible for processing the posts?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "All new input in this project will be assigned to this person. The assignee can be changed in the {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Commenting on posts", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Input form", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "input manager", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Input manager", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Submitting posts", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Re<PERSON>re agli input", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Default view", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Choose the default view to show input: cards in a grid view or pins on a map. Participants can manually switch between the two views.", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Incorpora l'URL di Konveio", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Numero di like per partecipante", "app.containers.AdminPage.ProjectEdit.limited": "Limited", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Load more templates", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Map", "app.containers.AdminPage.ProjectEdit.mapTab": "Map", "app.containers.AdminPage.ProjectEdit.maxDislikes": "Il massimo delle antipatie", "app.containers.AdminPage.ProjectEdit.maxLikes": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Il numero massimo di voti per opzione deve essere inferiore o uguale al numero totale di voti.", "app.containers.AdminPage.ProjectEdit.maximum": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "I partecipanti non possono superare questo budget quando presentano il loro carrello.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimo", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Richiedi ai partecipanti di soddisfare un budget minimo per presentare il loro cestino (inserisci '0' se non vuoi impostare un minimo).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "visit our Help Center", "app.containers.AdminPage.ProjectEdit.moreDetails": "More details", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.containers.AdminPage.ProjectEdit.newProject": "New Project", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Most recent", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Not a valid amount", "app.containers.AdminPage.ProjectEdit.noFolder": "No folder", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "No templates found", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Please enter a project title", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Not a valid number", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Oldest", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Only visible to admin", "app.containers.AdminPage.ProjectEdit.optionNo": "No", "app.containers.AdminPage.ProjectEdit.optionYes": "Yes (select folder)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Participation levels", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "What do you want to do?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Choose how users can participate.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "È possibile specificare chi può eseguire ogni azione e porre ulteriori domande ai partecipanti per raccogliere ulteriori informazioni.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Requisiti e domande dei partecipanti", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Access rights", "app.containers.AdminPage.ProjectEdit.pollTab": "Poll", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "La maggior parte delle reazioni", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Project card image", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "This image is part of the project card; the card that summarizes the project and is shown on the homepage for example.\n\n    For more information on recommended image resolutions, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "This image is shown at the top of the project page.\n\n    For more information on recommended image resolutions, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Gestione del progetto", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Project managers can edit projects, manage posts and email participants. You can {moderationInfoCenterLink} to find more information about the rights assigned to project managers.", "app.containers.AdminPage.ProjectEdit.projectName": "Project name", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Project type", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Projects with a timeline have a clear beginning and end and can have different phases. Projects without a timeline are continuous.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "The project type can not be changed later.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "È possibile impostare il progetto in modo che sia invisibile a determinati utenti.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Visibilità del progetto", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Published", "app.containers.AdminPage.ProjectEdit.purposes": "Purposes", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Random", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Si è verificato un errore durante il salvataggio dei dati. Si prega di riprovare.", "app.containers.AdminPage.ProjectEdit.saveProject": "Save", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Success!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Your form has been saved!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Search the templates", "app.containers.AdminPage.ProjectEdit.selectGroups": "Select group(s)", "app.containers.AdminPage.ProjectEdit.shareInformation": "Share information", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Snap Survey", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Set up and personalize your project.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "visit our support center", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Add survey content", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Cancel", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# choices} one {# choice} other {# choices}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON><PERSON>, voglio and<PERSON>", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Linear scale", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Multiple choice - choose many", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "No survey responses yet", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Open for responses", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Optional", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Are you sure you want to leave?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Le tue modifiche attuali non verranno salvate.", "app.containers.AdminPage.ProjectEdit.survey.required2": "Required", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Multiple choice - choose one", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Survey successfully saved", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Survey", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Total {count} responses", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "View survey", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Embed URL", "app.containers.AdminPage.ProjectEdit.surveyService": "Service", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "You can find more information on how to embed a survey {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Non è possibile eliminare questo tag perché viene utilizzato per visualizzare i progetti nelle seguenti pagine più personalizzate. \nÈ necessario scollegare il tag dalla pagina o eliminare la pagina prima di eliminare il tag.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "General settings for the project", "app.containers.AdminPage.ProjectEdit.titleLabel": "Title", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Choose a title that is short, engaging and clear. It will be shown in the dropdown overview and on the project cards on the home page.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Tags", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Seleziona {topicsCopy} per questo progetto. Gli utenti possono usarli per filtrare i progetti.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Bilancio totale", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Trending", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Unassigned", "app.containers.AdminPage.ProjectEdit.unlimited": "Unlimited", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Use template", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "View project", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Volunteering", "app.containers.AdminPage.ProjectEdit.voteTermError": "I termini di voto devono essere specificati per tutti i locali", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# groups can view} one {# group can view} other {# groups can view}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Add an event", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Start", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "End", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Delete", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Are you sure you want to delete this event? There is no way to undo this!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Event description", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Edit Event", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Attachments (max 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Attachments are shown below the event description.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Location", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Create a new event", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Save", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "We could not save your changes, please try again.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Success!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Link upcoming events to this projects and show them on the project's event page.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Title and dates", "app.containers.AdminPage.ProjectEvents.titleEvents": "Project events", "app.containers.AdminPage.ProjectEvents.titleLabel": "Event name", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Collapse all fields", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Field description", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Edit input form", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Enabled", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Include this field.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Expand all fields", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Input form", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required", "app.containers.AdminPage.ProjectIdeaForm.required": "Required", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Require this field to be filled in.", "app.containers.AdminPage.ProjectIdeaForm.save": "Save", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Saved!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "View form", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Email automatiche", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "<PERSON><PERSON>i configurare le email attivate a livello di fase", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Dates", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Are you sure you want to delete this phase?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Phase description", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "End Date", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Attachments (max 50MB)", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "There was an error submitting the form, please try again.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Saved!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Start date", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Phase name", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Carica gli allegati", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartition", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologia (filtro prima pagina)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Come dovrebbero essere chiamati i tag nel filtro della prima pagina? per esempio tag, categorie, dipartimenti, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "I tag possono essere configurati {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "here", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Term for one tag (singular)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tag", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Termine per tag multipli (plurale)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "tags", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Add field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Add a new registration field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Add option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Answer format", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Provide an answer format", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Provide an answer option for all languages", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Save answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Answer option successfully saved", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Answer choices", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Trascina i campi per determinare l'ordine in cui appaiono nel modulo d'iscrizione.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "De<PERSON>ult field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Optional text shown under the field name on the signup form.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Answer choices for place of residence can be set in the {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Edit answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Description", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Field name", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Provide a field name for all languages", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Field settings", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Yes-no (checkbox)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Date", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Long answer", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Multiple choice (select multiple)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Numeric value", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Multiple choice (select one)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Short answer", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Geographic areas tab", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Hidden field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Make answering this field required?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Custom fields", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Add answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Cancel", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Delete", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Sei sicuro di voler eliminare questa opzione di risposta alla domanda di registrazione? Tutti i record a cui gli utenti specifici hanno risposto con questa opzione saranno eliminati in modo permanente. Questa azione non può essere annullata.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Sei sicuro di voler cancellare questa domanda di registrazione? Tutte le risposte che gli utenti hanno dato a questa domanda saranno cancellate in modo permanente e la domanda non potrà più essere posta nei progetti o nelle proposte. Questa azione non può essere annullata.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Required", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Save field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "<PERSON> successfully saved", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "Due colonne", "app.containers.AdminPage.SettingsPage.addAreaButton": "Add a geographic area", "app.containers.AdminPage.SettingsPage.addTopicButton": "Add tag", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Are you sure you want to delete this area?", "app.containers.AdminPage.SettingsPage.areaTerm": "Term for one area (singular)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "area", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.areasTerm": "Term for multiple areas (plural)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "areas", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Select at least one language.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Visualiz<PERSON>e gli avatar", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Mostra le foto del profilo dei partecipanti e il loro numero ai visitatori non registrati", "app.containers.AdminPage.SettingsPage.bannerHeader": "Header text", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Header text for non-registered visitors", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Sub-header text for non-registered visitors", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Sub-header text", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Testo del banner", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Mostra in anteprima per", "app.containers.AdminPage.SettingsPage.brandingDescription": "Aggiungi il tuo logo e imposta i colori della piattaforma.", "app.containers.AdminPage.SettingsPage.brandingTitle": "<PERSON><PERSON> p<PERSON>ttaforma", "app.containers.AdminPage.SettingsPage.cancel": "Cancel", "app.containers.AdminPage.SettingsPage.chooseLayout": "Layout", "app.containers.AdminPage.SettingsPage.color_primary": "Primary color", "app.containers.AdminPage.SettingsPage.color_secondary": "Secondary color", "app.containers.AdminPage.SettingsPage.color_text": "Text color", "app.containers.AdminPage.SettingsPage.colorsTitle": "Colori", "app.containers.AdminPage.SettingsPage.confirmHeader": "Are you sure you want to delete this tag?", "app.containers.AdminPage.SettingsPage.contentModeration": "Moderazione dei contenuti", "app.containers.AdminPage.SettingsPage.ctaHeader": "Buttons", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Custom page header | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Custom", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Button text", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Button link", "app.containers.AdminPage.SettingsPage.defaultTopic": "Default tag", "app.containers.AdminPage.SettingsPage.delete": "Delete", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "This will delete the tag from all existing posts. This change will apply to all projects.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Add and delete tags that you would like to use on your platform to categorize posts. You can add the tags to specific projects in the {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Desktop", "app.containers.AdminPage.SettingsPage.editFormTitle": "Edit area", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Edit tag", "app.containers.AdminPage.SettingsPage.fieldDescription": "Area description", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "This description is only for internal collaboration and is not shown to users.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Area name", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Provide an area name for all languages", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "The name you choose for each area can be used as a registration field option and to filter projects on the homepage.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Save tag", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Tag name", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Provide a tag name for all languages", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "The name you choose for each tag will be visible to the platform users", "app.containers.AdminPage.SettingsPage.fixedRatio": "Banner a rapporto fisso", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Questo tipo di banner funziona meglio con immagini che non dovrebbero essere ritagliate, come ad esempio immagini con testo, logo o elementi specifici che sono cruciali per i tuoi cittadini. Questo banner viene sostituito con una casella continua nel colore principale quando gli utenti hanno eseguito l'accesso. È possibile impostare questo colore nelle impostazioni generali. Maggiori informazioni sull'uso consigliato delle immagini sono disponibili sul nostro {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "base di conoscenze", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Banner a tutta larghezza", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Questo banner si estende su tutta la larghezza per un grande effetto visivo. L'immagine cercherà di coprire più spazio possibile in modo che non sia sempre visibile in ogni momento. Puoi combinare questo banner con una sovrapposizione di qualsiasi colore. Maggiori informazioni sull'uso consigliato delle immagini sono disponibili sul nostro {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.header": "Homepage banner", "app.containers.AdminPage.SettingsPage.headerDescription": "Personalizza l'immagine e il testo del banner della homepage.", "app.containers.AdminPage.SettingsPage.header_bg": "Banner image", "app.containers.AdminPage.SettingsPage.helmetDescription": "Admin settings page", "app.containers.AdminPage.SettingsPage.helmetTitle": "Admin settings page", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Aggiungi i tuoi contenuti alla sezione personalizzabile in fondo alla homepage.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Homepage header | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Image overlay color", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Image overlay opacity", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "<PERSON><PERSON><PERSON> i contenuti inappropriati", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Rileva automaticamente i contenuti inappropriati pubblicati sulla piattaforma.", "app.containers.AdminPage.SettingsPage.languages": "Languages", "app.containers.AdminPage.SettingsPage.languagesTooltip": "You can select multiple languages in which you want to offer your platform to users. You will need to create content for every selected language.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Attività", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Please upload a header image", "app.containers.AdminPage.SettingsPage.no_button": "No button", "app.containers.AdminPage.SettingsPage.organizationName": "Name of city or organization", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Provide an organization name or city for all languages.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Enable overlay", "app.containers.AdminPage.SettingsPage.phone": "Telefono", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Configurazione della piattaforma", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Blocco delle bestemmie", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Bloccare input, proposte e commenti che contengono le parole offensive più comunemente riportate", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Questo testo è mostrato sulla homepage sopra i progetti.", "app.containers.AdminPage.SettingsPage.projectsSettings": "project settings", "app.containers.AdminPage.SettingsPage.projects_header": "Projects header", "app.containers.AdminPage.SettingsPage.registrationFields": "Registration fields", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Fornisci una breve descrizione all'inizio del tuo modulo di registrazione.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registration", "app.containers.AdminPage.SettingsPage.save": "Save", "app.containers.AdminPage.SettingsPage.saveArea": "Save area", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Success!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Non è stato possibile salvare. Prova a cambiare di nuovo l'impostazione.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Sign up\"", "app.containers.AdminPage.SettingsPage.signed_in": "Button for registered visitors", "app.containers.AdminPage.SettingsPage.signed_out": "Button for non-registered visitors", "app.containers.AdminPage.SettingsPage.signupFormText": "Registration helper text", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Add a short description at the top of the sign-up form.", "app.containers.AdminPage.SettingsPage.step1": "Email and password step", "app.containers.AdminPage.SettingsPage.step1Tooltip": "This is shown on the top of the first page of the sign-up form (name, email, password).", "app.containers.AdminPage.SettingsPage.step2": "Registration questions step", "app.containers.AdminPage.SettingsPage.step2Tooltip": "This is shown on the top of the second page of the sign-up form (additional registration fields).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Define the geographic areas that you would like to use for your platform, such as neighborhoods, boroughs or districts. You can associate these geographic areas with projects (filterable on the landing page) or ask participants to select their area of residence as a registration field to create Smart Groups and define access rights.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Choose how people will see your organization name, select the languages of your platform and link to your website.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "The provided subtitle exceeds the maximum allowed character limit (90 chars)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminology", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Impostazioni aggiornate con successo.", "app.containers.AdminPage.SettingsPage.tabAreas1": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabBranding": "Branding", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Stati di ingresso", "app.containers.AdminPage.SettingsPage.tabPolicies": "Politiche", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registration", "app.containers.AdminPage.SettingsPage.tabSettings": "General", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Define what geographic unit you would like to use for your projects (e.g., neighborhoods, districts, boroughs, etc.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geographic areas", "app.containers.AdminPage.SettingsPage.titleBasic": "General settings", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "The provided title exceeds the maximum allowed character limit (35 chars)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Tag manager", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Questo banner è particolarmente utile con le immagini che non funzionano bene con il testo del titolo, del sottotitolo o del pulsante. Questi elementi verranno spinti sotto il banner. Maggiori informazioni sull'uso consigliato delle immagini sono disponibili sul nostro {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.twoRowLayout": "Due righe", "app.containers.AdminPage.SettingsPage.urlError": "The URL is not valid", "app.containers.AdminPage.SettingsPage.urlPatternError": "Enter a valid URL.", "app.containers.AdminPage.SettingsPage.urlTitle": "Website", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "You can add a link to your own website. This link will be used on the bottom of the homepage.", "app.containers.AdminPage.SideBar.administrator": "Amministratore", "app.containers.AdminPage.SideBar.communityPlatform": "Piattaforma comunitaria", "app.containers.AdminPage.SideBar.dashboard": "Dashboard", "app.containers.AdminPage.SideBar.emails": "Emails", "app.containers.AdminPage.SideBar.folderManager": "Gestione cartelle", "app.containers.AdminPage.SideBar.groups": "Groups", "app.containers.AdminPage.SideBar.guide": "Guide", "app.containers.AdminPage.SideBar.inputManager": "Input manager", "app.containers.AdminPage.SideBar.insights": "Reporting", "app.containers.AdminPage.SideBar.knowledgeBase": "Base di conoscenze", "app.containers.AdminPage.SideBar.language": "<PERSON><PERSON>", "app.containers.AdminPage.SideBar.menu": "Pages & menu", "app.containers.AdminPage.SideBar.messaging": "Messaging", "app.containers.AdminPage.SideBar.moderation": "Activity", "app.containers.AdminPage.SideBar.notifications": "Notifiche", "app.containers.AdminPage.SideBar.processing": "Processing", "app.containers.AdminPage.SideBar.projectManager": "Responsabile del progetto", "app.containers.AdminPage.SideBar.projects": "Projects", "app.containers.AdminPage.SideBar.settings": "Settings", "app.containers.AdminPage.SideBar.signOut": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.support": "Supporto", "app.containers.AdminPage.SideBar.toPlatform": "Alla piattaforma", "app.containers.AdminPage.SideBar.tools": "Strumenti", "app.containers.AdminPage.SideBar.user.myProfile": "Il mio profilo", "app.containers.AdminPage.SideBar.users": "Users", "app.containers.AdminPage.SideBar.workshops": "Workshops", "app.containers.AdminPage.Topics.addTopics": "Add", "app.containers.AdminPage.Topics.browseTopics": "Browse tags", "app.containers.AdminPage.Topics.cancel": "Cancel", "app.containers.AdminPage.Topics.confirmHeader": "Are you sure you want to delete this project tag?", "app.containers.AdminPage.Topics.delete": "Delete", "app.containers.AdminPage.Topics.deleteTopicLabel": "Delete", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "This tag will no longer be able to be added to new posts in this project.", "app.containers.AdminPage.Topics.inputForm": "Input form", "app.containers.AdminPage.Topics.lastTopicWarning": "At least one tag is required. If you do not want to use tags, they can be disabled in the {ideaFormLink} tab.", "app.containers.AdminPage.Topics.projectTopicsDescription": "You can add and delete the tags that can be assigned to posts in this project.", "app.containers.AdminPage.Topics.remove": "Remove", "app.containers.AdminPage.Topics.title": "Project tags", "app.containers.AdminPage.Topics.topicManager": "Tag manager", "app.containers.AdminPage.Topics.topicManagerInfo": "If you would like to add additional project tags, you can do so in the {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Add a new group", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Group name", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Fornire un nome di gruppo", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Create a manual group", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "What type of group do you need?", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Salva gruppo", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Create a manual group", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Create a smart group", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Learn more about groups", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "You can select users from the overview and add them to this group.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "You can define conditions and users who meet the conditions are automatically added to this group.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Manual group", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Smart group", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "There is no one in this group yet", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Go to {allUsersLink} to manually add some users.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "No user(s) match your search", "app.containers.AdminPage.Users.GroupsPanel.select": "Select", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Are you sure you want to delete this group?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "An error occurred while adding users to the groups, please try again.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Remove from group", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Delete selected users from this group?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "An error occurred while deleting users from the group, please try again.", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Add", "app.containers.AdminPage.groups.permissions.add": "Add", "app.containers.AdminPage.groups.permissions.addQuestion": "Aggiungere domande demografiche", "app.containers.AdminPage.groups.permissions.createANewQuestion": "<PERSON><PERSON>re una nuova domanda", "app.containers.AdminPage.groups.permissions.createAQuestion": "<PERSON><PERSON><PERSON> una domanda", "app.containers.AdminPage.groups.permissions.defaultField": "Campo predefinito", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Delete", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Delete", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "Sì-no (casella di controllo)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Data", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "<PERSON><PERSON><PERSON><PERSON> lunga", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "<PERSON>elta multipla (selezionare più)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Valore numerico", "app.containers.AdminPage.groups.permissions.fieldType_select": "Scelta multipla (selezionare uno)", "app.containers.AdminPage.groups.permissions.fieldType_text": "Risposta breve", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Are you sure you want to remove this group from the project?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Select one or more groups", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {No members} one {1 member} other {{count} members}}", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Are you sure?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Project managers not found", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Nothing is shown, because there are no actions the user can take in this project.", "app.containers.AdminPage.groups.permissions.option1": "Opzione 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Pending invitation", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Chi può annotare il documento?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Chi può commentare gli input?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Chi può commentare le proposte?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Chi può inviare una proposta?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Chi può reagire agli input?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Chi può inviare i contributi?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Chi può partecipare al sondaggio?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Chi può partecipare al sondaggio?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Chi può votare le proposte?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Chi può votare?", "app.containers.AdminPage.groups.permissions.save": "Save", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Success!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.groups.permissions.select": "Selezionare", "app.containers.AdminPage.projects.all.existingProjects": "Existing projects", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projects and folders", "app.containers.AdminPage.widgets.copied": "Copied to clipboard", "app.containers.AdminPage.widgets.copyToClipboard": "Copy this code", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Copy the HTML code", "app.containers.AdminPage.widgets.fieldAccentColor": "Accent color", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Widget background color", "app.containers.AdminPage.widgets.fieldButtonText": "Button text", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Join now", "app.containers.AdminPage.widgets.fieldFont": "Font", "app.containers.AdminPage.widgets.fieldFontDescription": "This must be an existing font name from {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "Font size (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Header subtitle", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "You can have a say", "app.containers.AdminPage.widgets.fieldHeaderText": "Header title", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Our participation platform", "app.containers.AdminPage.widgets.fieldHeight": "Height (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Number of posts", "app.containers.AdminPage.widgets.fieldProjects": "Projects", "app.containers.AdminPage.widgets.fieldRelativeLink": "Links to", "app.containers.AdminPage.widgets.fieldShowFooter": "Show button", "app.containers.AdminPage.widgets.fieldShowHeader": "Show header", "app.containers.AdminPage.widgets.fieldShowLogo": "Show logo", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Site background color", "app.containers.AdminPage.widgets.fieldSort": "Sorted by", "app.containers.AdminPage.widgets.fieldTextColor": "Text color", "app.containers.AdminPage.widgets.fieldTopics": "Tags", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Homepage", "app.containers.AdminPage.widgets.htmlCodeExplanation": "You can copy this HTML code and paste it on that part of your website where you want to add your widget.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Widget HTML code", "app.containers.AdminPage.widgets.previewTitle": "Preview", "app.containers.AdminPage.widgets.settingsTitle": "Settings", "app.containers.AdminPage.widgets.sortNewest": "Newest", "app.containers.AdminPage.widgets.sortPopular": "Popular", "app.containers.AdminPage.widgets.sortTrending": "Trending", "app.containers.AdminPage.widgets.subtitleWidgets": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Dimensions", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Header & Footer", "app.containers.AdminPage.widgets.titleInputSelection": "Input selection", "app.containers.AdminPage.widgets.titleStyle": "Style", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Save", "app.containers.admin.ContentBuilder.delete": "Delete", "app.containers.admin.ContentBuilder.error": "error", "app.containers.admin.ContentBuilder.errorMessage": "There is an error on {locale} content, please fix the issue to be able to save your changes", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 column", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 column", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 columns with 30% and 60% width respectively", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 columns with 60% and 30% width respectively", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 even columns", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Show more", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Title", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Participants timeline", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Chart", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Date range", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Title", "app.containers.admin.ReportBuilder.charts.noData": "There is no data available for the filters you have selected.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Traffic sources", "app.containers.admin.ReportBuilder.charts.usersByAge": "Users by age", "app.containers.admin.ReportBuilder.charts.usersByGender": "Users by gender", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Visitor timeline", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Acquista 1 posto aggiuntivo", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "<PERSON><PERSON><PERSON>", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Sei sicuro di voler dare a una persona i diritti di manager?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Attribuire i diritti di manager", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "È stato raggiunto il limite di posti inclusi nel piano, {noOfSeats} aggiuntivi {noOfSeats, plural, one {posto} other {posti}} saranno aggiunti.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Add status", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Delete", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Edit", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Edit status", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Statuses currently assigned to participant input cannot be deleted. You can remove/change the status from existing input in the {manageTab} tab.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "This status cannot be deleted or moved.", "app.containers.admin.ideaStatuses.all.manage": "Edit", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Gestisci lo stato che può essere assegnato agli input dei partecipanti all'interno di un progetto. Lo stato è visibile pubblicamente e aiuta a tenere informati i partecipanti.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Modifica gli stati di ingresso", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Selected for implementation or next steps", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Approved", "app.containers.admin.ideaStatuses.form.category": "Category", "app.containers.admin.ideaStatuses.form.categoryDescription": "Please select the category that best represents your status. This selection will help our analytics tool to more accurately process and analyze posts.", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "Other", "app.containers.admin.ideaStatuses.form.fieldColor": "Color", "app.containers.admin.ideaStatuses.form.fieldDescription": "Status Description", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Provide a status description for all lanugages", "app.containers.admin.ideaStatuses.form.fieldTitle": "Status Name", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Provide a status name for all lanugages", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Successfully implemented", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Implemented", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Ineligible or not selected to move forward", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Not Selected", "app.containers.admin.ideaStatuses.form.saveStatus": "Save status", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Considered for implementation or next steps", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Under Consideration", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Viewed but not yet processed", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Viewed", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Manage input and their statuses.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Input manager | Participation platform of {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Give feedback, add tags and move input from one project to another", "app.containers.admin.ideas.all.inputManagerPageTitle": "Gestore degli ingressi", "app.containers.admin.ideas.all.tabOverview": "Panoramica", "app.containers.admin.import.importInputs": "Import inputs", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 posto di amministratore aggiuntivo} other {# Posti di amministratore aggiuntivi}} e {managerSeats, plural, one {1 posto manager aggiuntivo} other {# posti aggiuntivi per manager}} saranno aggiunti oltre il limite.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 posto di amministratore aggiuntivo sarà aggiunto oltre il limite} other {# Ulteriori posti di amministratore saranno aggiunti oltre il limite}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 posto da manager aggiuntivo sarà aggiunto oltre il limite} other {# Ulteriori posti da manager saranno aggiunti oltre il limite}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Confermare e inviare gli inviti", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Confirm impact on seat usage", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "È stato raggiunto il limite di posti disponibili all'interno del piano.", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Solo amministratori e collaboratori", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Solo gli amministratori della piattaforma, i gestori di cartelle e i project manager possono intraprendere l'azione.", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Anyone", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "<PERSON><PERSON><PERSON>, compresi gli utenti non registrati, può partecipare.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Selection", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Possono partecipare gli utenti di uno specifico gruppo di utenti. È possibile gestire i gruppi di utenti nella scheda \"Utenti\".", "app.containers.admin.project.permissions.viewingRightsTitle": "Who can see this project?", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Tasso di partecipazione", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Total participants", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Automated campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automated emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "From {quantity} campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Custom campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Custom emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "Emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Total emails sent", "app.modules.commercial.analytics.admin.components.Events.completed": "Completato", "app.modules.commercial.analytics.admin.components.Events.events": "Events", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Totale eventi aggiunti", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Prossimo", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Accepted", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Invitations", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Pending", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Totale inviti inviati", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Vai a Gestione input", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Ingressi", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Active", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Progetti non archiviati e visibili nella tabella \"Attivi\" della home page", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Archived", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Draft projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Finished", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Tutti i progetti archiviati e i progetti cronologici attivi che sono stati completati vengono conteggiati qui", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Totale progetti", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Il numero di progetti visibili sulla piattaforma", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "New registrations", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Tasso di registrazione", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registrations", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Total registrations", "app.modules.commercial.analytics.admin.components.Tab": "Visitatori", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Ultimi 30 giorni:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Ultimi 7 giorni:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Visite pagina per visita", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "<PERSON><PERSON> della visita", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Visitatori\" è il numero di visitatori unici. Se una persona visita la piattaforma più volte, viene conteggiata una sola volta.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Visite", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Visite\" è il numero di sessioni. Se una persona ha visitato la piattaforma più volte, ogni visita viene conteggiata.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Ieri:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Language", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Numero di visitatori", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Percentuale di visitatori", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "fare clic qui", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Referenti", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "Per visualizzare l'elenco completo di referenti, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Sorgenti di traffico", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Visits", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Email deliveries over time", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Registrations over time", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Date", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistica", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Statistiche generali", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Visite e visitatori nel tempo", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Totale nel periodo", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Conta", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Language", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Campagne", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Inserimento diretta", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Percentuale di visite", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Motori di ricerca", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Social network", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Sorgente di traffico", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Numero di visite", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Siti Web", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Puoi rimuovere questa segnalazione di contenuto selezionando questo elemento e cliccando sul pulsante di rimozione in alto. Riapparirà quindi nelle schede Visto o Non visto", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Contenuto inappropriato rilevato automaticamente.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Non ci sono post segnalati per la revisione da parte della comunità o segnalati per contenuti inappropriati dal nostro sistema di elaborazione del linguaggio naturale", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Rimuovere {numberOfItems, plural, one {avviso di contenuto} other {# avvertenze sul contenuto}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "<PERSON><PERSON><PERSON><PERSON> come inappropriato da un utente della piattaforma.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Avvertenze sul contenuto", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Report builder", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Pagine mostrate sulla tua barra di navigazione", "app.modules.navbar.admin.containers.createCustomPageButton": "Create custom page", "app.modules.navbar.admin.containers.deletePageConfirmation": "Sei sicuro di voler cancellare questa pagina? Questo non può essere annullato. Puoi anche rimuovere la pagina dalla barra di navigazione se non sei ancora pronto a cancellarla.", "app.modules.navbar.admin.containers.pageHeader": "Pages & menu", "app.modules.navbar.admin.containers.pageSubtitle": "La tua barra di navigazione può visualizzare fino a quattro pagine oltre alle pagine Home e progetti. Puoi rinominare le voci di menu, riordinare e aggiungere nuove pagine con i tuoi contenuti.", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "visit our support center", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "For more information on recommended image resolutions, {supportPageLink}."}
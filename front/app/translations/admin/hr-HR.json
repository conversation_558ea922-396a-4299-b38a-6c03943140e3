{"UI.FormComponents.required": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.action": "<PERSON>k<PERSON><PERSON>ski", "app.Admin.ManagementFeed.after": "Na<PERSON>", "app.Admin.ManagementFeed.before": "P<PERSON>je", "app.Admin.ManagementFeed.changed": "Izmijenjeno", "app.Admin.ManagementFeed.created": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.date": "Datum", "app.Admin.ManagementFeed.deleted": "Izbrisano", "app.Admin.ManagementFeed.folder": "Mapa", "app.Admin.ManagementFeed.idea": "I<PERSON>ja", "app.Admin.ManagementFeed.in": "u projektu {project}", "app.Admin.ManagementFeed.item": "Artikal", "app.Admin.ManagementFeed.key": "K<PERSON><PERSON>č", "app.Admin.ManagementFeed.managementFeedNudge": "Pristup feedu za upravljanje nije uključen u vašu trenutnu licencu. Obratite se svom GovSuccess Manageru da saznate više o tome.", "app.Admin.ManagementFeed.noActivityFound": "<PERSON><PERSON>", "app.Admin.ManagementFeed.phase": "Faza", "app.Admin.ManagementFeed.project": "Projekt", "app.Admin.ManagementFeed.projectReviewApproved": "Projekt odobren", "app.Admin.ManagementFeed.projectReviewRequested": "Zatražen je pregled projekta", "app.Admin.ManagementFeed.title": "Feed <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.user": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.userPlaceholder": "Odaberite korisnika", "app.Admin.ManagementFeed.value": "Vrijednost", "app.Admin.ManagementFeed.viewDetails": "Pregledavati pojedinosti", "app.Admin.ManagementFeed.warning": "Eksperimentalna značajka: minimalni popis odabranih radnji koje su izvršili administratori ili upravitelji u posljednjih 30 dana. Nisu uključene sve radnje.", "app.Admin.Moderation.managementFeed": "Feed <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.Moderation.participationFeed": "Feed su<PERSON><PERSON>lov<PERSON>", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "Jesi li siguran?", "app.components.Admin.Campaigns.clicked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deleteCampaignButton": "Izbriši kampanju", "app.components.Admin.Campaigns.deliveryStatus_accepted": "P<PERSON>h<PERSON>ć<PERSON>", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Odskočio", "app.components.Admin.Campaigns.deliveryStatus_clicked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "Ovo pokazuje koliko je primatelja kliknulo na poveznicu u e-poruci. Imajte na umu da neki sigurnosni sustavi mogu automatski pratiti poveznice kako bi ih skenirali, što može rezultirati lažnim klikovima.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "Isporučeno", "app.components.Admin.Campaigns.deliveryStatus_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_opened": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "Ovo pokazuje koliko je primatelja otvorilo e-poruku. Imajte na umu da neki sigurnos<PERSON> susta<PERSON> (poput Microsoft Defendera) mogu unaprijed učitati sadržaj za skeniranje, što može rezultirati lažnim otvaranjem.", "app.components.Admin.Campaigns.deliveryStatus_sent": "Poslano", "app.components.Admin.Campaigns.draft": "Nacrt", "app.components.Admin.Campaigns.from": "Iz", "app.components.Admin.Campaigns.manageButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.opened": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.project": "Projekt", "app.components.Admin.Campaigns.recipientsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.reply_to": "Odgovori", "app.components.Admin.Campaigns.sent": "Poslano", "app.components.Admin.Campaigns.statsButton": "Statistika", "app.components.Admin.Campaigns.subject": "Predmet", "app.components.Admin.Campaigns.to": "Do", "app.components.Admin.ImageCropper.cropFinalSentence": "<PERSON><PERSON><PERSON>: {link}.", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "Ključni sadržaj neka bude unutar isprekidanih linija kako bi uvijek bio vidljiv.", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "3:1 na mobitelu (prikazuje se samo područje između isprekidanih linija)", "app.components.Admin.ImageCropper.cropSentenceOne": "Slika se automatski obrezuje:", "app.components.Admin.ImageCropper.cropSentenceTwo": "{aspect} na računalu (prikazano u punoj širini)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "preporučeni omjer", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Tekst zaglavlja za registrirane korisnike", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Upozorenje: boja koju ste odabrali nema dovoljno visok kontrast. To može rezultirati tekstom koji je teško čitati. Odaberite tamniju boju kako biste optimizirali čitljivost.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Dodajte Događaje na navigacijsku traku", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "<PERSON><PERSON> je <PERSON><PERSON><PERSON>, veza na sve događaje projekta bit će dodana na navigacijsku traku.", "app.components.AdminPage.SettingsPage.eventsSection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Prilagodljiv dio početne stranice", "app.components.AnonymousPostingToggle.userAnonymity": "Anonimnost korisnika", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Korisnici će moći sakriti svoj identitet od drugih korisnika, voditelja projekta i administratora. Ovi se doprinosi još uvijek mogu moderirati.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Omogućite korisnicima anonimno sudjelovanje", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Korisnici i dalje mogu odlučiti sudjelovati pod svojim pravim imenom, ali će imati opciju anonimnog slanja doprinosa ako to odluče. Svi će korisnici i dalje morati ispunjavati zahtjeve postavljene na kartici Prava pristupa kako bi njihovi doprinosi prošli. Podaci o korisničkom profilu neće biti dostupni u izvozu podataka o sudjelovanju.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Saznajte više o anonimnosti korisnika u našem {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "članak podrške", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Nakon što se dodaju dodatna mjesta, va<PERSON> naplata će se povećati. Obratite se svom GovSuccess Manageru da saznate više o tome.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Hvala što ste ispunili anketu! Slobodno ga ponovite sljedećeg kvartala.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Preuzmi kao pdf", "app.components.FormSync.downloadExcelTemplate": "Preuzmite Excel predložak", "app.components.FormSync.downloadExcelTemplateTooltip2": "Excel predlošci neće uključivati pitanja o rangiranju, matri<PERSON><PERSON> pitanja, pitanja o prijenosu datoteka i bilo koja pitanja o unosu mapiranja (Ispuštanje pribadače, Crtanje rute, Crtanje <PERSON>ruč<PERSON>, Prijenos ESRI datoteke) jer trenutno nije podržan skupni uvoz.", "app.components.ProjectTemplatePreview.close": "Zatvori", "app.components.ProjectTemplatePreview.createProject": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Izradite projekt na temelju predloška ''{templateTitle}''", "app.components.ProjectTemplatePreview.goBack": "<PERSON><PERSON> natrag", "app.components.ProjectTemplatePreview.goBackTo": "Povratak na {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "Stručnjak za Go Vocal", "app.components.ProjectTemplatePreview.infoboxLine1": "Želite li koristiti ovaj predložak za svoj projekt?", "app.components.ProjectTemplatePreview.infoboxLine2": "Obratite se odgovornoj osobi u svojoj gradskoj upravi ili kontaktirajte {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Mapa projekta", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Odabrani datum je nevaljan. Odaberite datum u sljedećem formatu GGGG-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Odredite datum početka projekta", "app.components.ProjectTemplatePreview.projectStartDate": "Datum početka vašeg projekta", "app.components.ProjectTemplatePreview.projectTitle": "Naziv vašeg projekta", "app.components.ProjectTemplatePreview.projectTitleError": "Unesite naziv projekta", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Molimo vas da unesete naziv projekta na svim jezicima", "app.components.ProjectTemplatePreview.projectsOverviewPage": "stranica pregleda projekata", "app.components.ProjectTemplatePreview.responseError": "<PERSON>s, ne<PERSON>to je pošlo po krivu.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Pogledajte jo<PERSON>", "app.components.ProjectTemplatePreview.successMessage": "Projekt je uspješ<PERSON> kreiran!", "app.components.ProjectTemplatePreview.typeProjectName": "Unesite naziv projekta", "app.components.ProjectTemplatePreview.useTemplate": "Iskoristite ovaj predložak", "app.components.SeatInfo.additionalSeats": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>a", "app.components.SeatInfo.additionalSeatsToolTip": "Ovo pokazuje broj dodatnih mjesta koja ste kupili povrh 'Uključenih mjesta'.", "app.components.SeatInfo.adminSeats": "Administratorska mjesta", "app.components.SeatInfo.adminSeatsIncludedText": "Ukl<PERSON><PERSON><PERSON> je {adminSeats} <PERSON><PERSON><PERSON> m<PERSON>", "app.components.SeatInfo.adminSeatsTooltip1": "Administratori su zaduženi za platformu i imaju menadžerska prava za sve mape i projekte. Možete {visitHelpCenter} saznati više o različitim ulogama.", "app.components.SeatInfo.currentAdminSeatsTitle": "Trenutna administrativna mjesta", "app.components.SeatInfo.currentManagerSeatsTitle": "Trenutna menadžerska mjesta", "app.components.SeatInfo.includedAdminToolTip": "Ovo pokazuje broj dostupnih mjesta za administratore uključenih u godišnji ugovor.", "app.components.SeatInfo.includedManagerToolTip": "Ovo pokazuje broj raspoloživih mjesta za menadžere koji su uključeni u godišnji ugovor.", "app.components.SeatInfo.includedSeats": "Uključena sjedala", "app.components.SeatInfo.managerSeats": "Menadžerska mjesta", "app.components.SeatInfo.managerSeatsTooltip": "Upravitelji mapa/projekata mogu upravljati neograničenim brojem mapa/projekata. Možete {visitHelpCenter} saznati više o različitim ulogama.", "app.components.SeatInfo.managersIncludedText": "Ukl<PERSON><PERSON><PERSON> je {manager<PERSON>eat<PERSON>} menadžerskih mjesta", "app.components.SeatInfo.remainingSeats": "Preostala mjesta", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Ukupno mjesta", "app.components.SeatInfo.totalSeatsTooltip": "Ovo pokazuje zbroj mjesta unutar vašeg plana i dodatnih mjesta koja ste kupili.", "app.components.SeatInfo.usedSeats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.SeatInfo.view": "Pogled", "app.components.SeatInfo.visitHelpCenter": "posjetite naš centar za pomoć", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "<PERSON>a<PERSON> plan ima {adminSeatsIncluded}. <PERSON><PERSON> što iskoristite sva sjedala, dodatna sjedala bit će dodana pod 'Dodatna sjedala'.", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "<PERSON>aš plan ima {manager<PERSON><PERSON><PERSON><PERSON>ncluded}, prihvatljiv za upravitelje mapa i voditelje projekata. <PERSON><PERSON> što iskoristite sva sjedala, dodatna sjedala bit će dodana pod 'Dodatna sjedala'.", "app.components.UserSearch.addModerators": "<PERSON><PERSON><PERSON>", "app.components.UserSearch.searchUsers": "Unesite za pretraživanje korisnika...", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternativna poruka o pogrešci", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "Korisnicima će prema zadanim postavkama biti prikazana sljedeća poruka o pogrešci:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Prilagodite poruku o pogrešci", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "Ovu poruku možete prebrisati za svaki jezik koristeći tekstni okvir \"Alternativna poruka o pogrešci\" ispod. Ako tekstni okvir ostavite praznim, prikazat će se zadana poruka.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "Poruka o pogrešci", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "To je ono što će sudionici vidjeti kada ne zadovolje uvjete za sudjelovanje.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Spremi poruku o pogrešci", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "<PERSON>je odabrano nijedno pitanje. Prvo odaberite pitanje.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "<PERSON>ema odgovora", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} odgovora", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "<PERSON><PERSON><PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} do sada", "app.components.admin.DatePhasePicker.Input.openEnded": "Otvoreni kraj", "app.components.admin.DatePhasePicker.Input.selectDate": "Odaberite datum", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Oč<PERSON>i datum završetka", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Očistite datum poč<PERSON>ka", "app.components.admin.Graphs": "<PERSON><PERSON> dostu<PERSON>h podataka za trenutne filtre.", "app.components.admin.Graphs.noDataShort": "<PERSON><PERSON> podata<PERSON>.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Komentari tijekom vremena", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Objave tijekom vremena", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Reakcije tijekom vremena", "app.components.admin.InputManager.onePost": "1 unos", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Prilagodba izvanmrežnih izbora", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Offline podešavanje glasova", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "Ova vam opcija omogućuje uključivanje podataka o sudjelovanju iz drugih izvora, kao što su osobni ili papirnati glasovi:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Vizualno će se razlikovati od digitalnih glasova.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "To će utjecati na konačne rezultate glasovanja.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "Neće se odraziti na nadzornim pločama podataka o sudjelovanju.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Izvanmrežni glasovi za opciju mogu se postaviti samo jednom u projektu i dijele se između svih faza projekta.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "Prvo morate unijeti ukupan broj izvanmrežnih sudionika.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Ukupno izvanmrežnih sudionika", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "<PERSON><PERSON> bi<PERSON> izrač<PERSON>li to<PERSON> rezultate, moramo znati <b>ukupan broj izvanmrežnih sudionika za ovu fazu</b>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Navedite samo one koji su sudjelovali izvan mreže.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> {name}", "app.components.admin.PostManager.PostPreview.assignee": "Opunomoćenik", "app.components.admin.PostManager.PostPreview.cancelEdit": "Otkaži uređivanje", "app.components.admin.PostManager.PostPreview.currentStatus": "Trenutačni status", "app.components.admin.PostManager.PostPreview.delete": "Izbriši", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Jeste li sigurni da želite izbrisati ovaj unos? Ovu radnju ne možete poništiti.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Jeste li sigurni da želite izbrisati ovaj unos? Unos će biti izbrisan iz svih faza projekta i ne može se povratiti.", "app.components.admin.PostManager.PostPreview.edit": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.noOne": "Nedodijeljeno", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Broj odabira ovog prijedloga u sklopu participativnih proračuna drugih sudionika", "app.components.admin.PostManager.PostPreview.picks": "<PERSON><PERSON><PERSON><PERSON>: {picksNumber} ", "app.components.admin.PostManager.PostPreview.reactionCounts": "Reakcija se broji:", "app.components.admin.PostManager.PostPreview.save": "Sp<PERSON>i", "app.components.admin.PostManager.PostPreview.submitError": "Pogreška", "app.components.admin.PostManager.addFeatureLayer": "Dodajte sloj značajki", "app.components.admin.PostManager.addFeatureLayerInstruction": "Kopirajte URL sloja značajki koji se nalazi na ArcGIS Online i zalijepite ga u unos ispod:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Dodajte novi sloj značajki na kartu", "app.components.admin.PostManager.addWebMap": "Dodajte web kartu", "app.components.admin.PostManager.addWebMapInstruction": "Kopirajte ID portala vaše web karte s ArcGIS Online i zalijepite ga u unos ispod:", "app.components.admin.PostManager.allPhases": "Sve faze", "app.components.admin.PostManager.allProjects": "Svi projekti", "app.components.admin.PostManager.allStatuses": "Svi statusi", "app.components.admin.PostManager.allTopics": "<PERSON><PERSON> <PERSON>nake", "app.components.admin.PostManager.anyAssignment": "Bilo koji administrator", "app.components.admin.PostManager.assignedTo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> {assigneeName}", "app.components.admin.PostManager.assignedToMe": "Dodijeljeni meni", "app.components.admin.PostManager.assignee": "Opunomoćenik", "app.components.admin.PostManager.authenticationError": "Došlo je do pogreške pri autentifikaciji prilikom pokušaja dohvaćanja ovog sloja. Provjerite URL i ima li vaš Esri API ključ pristup ovom sloju.", "app.components.admin.PostManager.automatedStatusTooltipText": "Ovaj se status automatski ažurira kada se ispune uvjeti", "app.components.admin.PostManager.bodyTitle": "Opis", "app.components.admin.PostManager.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.co-sponsors": "Supokrovitelji", "app.components.admin.PostManager.comments": "Komentari", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "To znači da ćete izgubiti sve podatke povezane s tim unosima, poput komentara, reakcija i glasova. Ova se radnja ne može poništiti.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Jeste li sigurni da želite izbrisati ove unose?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "Ukloni temu", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "Pokušavate ukloniti ovu ideju iz faze u kojoj je dobila glasove. Ako to učinite, ovi će glasovi biti izgubljeni. Jeste li sigurni da želite ukloniti ovu ideju iz ove faze?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "Glasovi povezani s ovom idejom bit će izgubljeni", "app.components.admin.PostManager.components.goToInputManager": "Idite na upravitelj unosa", "app.components.admin.PostManager.components.goToProposalManager": "Idite do upravitelja ponuda", "app.components.admin.PostManager.contributionFormTitle": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cost": "t<PERSON>ša<PERSON>", "app.components.admin.PostManager.createInput": "<PERSON>zradi unos", "app.components.admin.PostManager.createInputsDescription": "Izradite novi skup ulaznih podataka iz prošlog projekta", "app.components.admin.PostManager.currentLat": "Središnja geografska širina", "app.components.admin.PostManager.currentLng": "Središnja geografska duljina", "app.components.admin.PostManager.currentZoomLevel": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.defaultEsriError": "Došlo je do pogreške prilikom pokušaja dohvaćanja ovog sloja. Provjerite svoju mrežnu vezu i je li URL točan.", "app.components.admin.PostManager.delete": "Izbriši", "app.components.admin.PostManager.deleteAllSelectedInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON> {count} objava", "app.components.admin.PostManager.deleteConfirmation": "Jeste li sigurni da želite izbrisati ovaj sloj?", "app.components.admin.PostManager.dislikes": "Nesviđanja", "app.components.admin.PostManager.edit": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.editProjects": "Uredite projekte", "app.components.admin.PostManager.editStatuses": "Uredite statuse", "app.components.admin.PostManager.editTags": "Uredite oznake", "app.components.admin.PostManager.editedPostSave": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Uvoz podataka iz Esri ArcGIS Online značajka je dodatka. Razgovarajte sa svojim GS upraviteljem da ga otključate.", "app.components.admin.PostManager.esriSideError": "Došlo je do greške u aplikaciji ArcGIS. Pričekajte nekoliko minuta i pokušajte ponovno kasnije.", "app.components.admin.PostManager.esriWebMap": "Esri web karta", "app.components.admin.PostManager.exportAllInputs": "Izvezi sve objave (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Izvezi sve komentare (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Izvezi komentare iz ovog projekta (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Izvezi objave iz ovog projekta (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Izvezi odabrane objave (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Izvezi komentare iz odabranih objava (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "<PERSON><PERSON><PERSON><PERSON> glasova prema unosu (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "<PERSON>zvoz glasova po korisniku (.xslx)", "app.components.admin.PostManager.exports": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "Kartografske podatke možete učitati samo kao GeoJSON slojeve ili kao uvoz iz ArcGIS Online. Uklonite sve trenutne GeoJSON slojeve ako želite dodati Feature Layer.", "app.components.admin.PostManager.featureLayerTooltop": "Možete pronaći URL sloja značajki na desnoj strani stranice stavke na ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Odaberite kako drugi vide vaše ime", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Objasnite promjenu ovog statusa", "app.components.admin.PostManager.fileUploadError": "Prijenos jedne ili više datoteka nije uspio. Provjerite veličinu i format datoteke i pokušajte ponovno.", "app.components.admin.PostManager.formTitle": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.generalApiError2": "Došlo je do pogreške prilikom pokušaja dohvaćanja ove stavke. Provjerite je li URL ili ID portala točan i imate li pristup ovoj stavci.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Kartografske podatke možete učitati samo kao GeoJSON slojeve ili kao uvoz iz ArcGIS Online. Uklonite sve ArcGIS podatke ako želite prenijeti GeoJSON sloj.", "app.components.admin.PostManager.goToDefaultMapView": "Idite na zadano središte mape", "app.components.admin.PostManager.hiddenFieldsLink": "skrivena polja", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Savjet: a<PERSON>, doda<PERSON><PERSON> {hiddenFieldsLink} kako biste mogli pratiti tko je odgovorio na upitnik.", "app.components.admin.PostManager.import2": "Uvoz", "app.components.admin.PostManager.importError": "Odabrana datoteka nije mogla biti uvezena jer nije valjana GeoJSON datoteka", "app.components.admin.PostManager.importEsriFeatureLayer": "Uvezi Eri Feature Layer", "app.components.admin.PostManager.importEsriWebMap": "Uvezi Eri web kartu", "app.components.admin.PostManager.importInputs": "<PERSON><PERSON>z ulaza", "app.components.admin.PostManager.imported": "Uvozni", "app.components.admin.PostManager.initiativeFormTitle": "Uredi inicijativu", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputImportProgress": "{importedCount} od {totalCount} {totalCount, plural, one {unosa je} other {unosa je}} uneseno. Uvoz je još uvijek u tijeku, provjerite kasnije.", "app.components.admin.PostManager.inputManagerHeader": "Unos", "app.components.admin.PostManager.inputs": "Unos", "app.components.admin.PostManager.inputsExportFileName": "unos", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Prikaži samo objave za koje su potrebne povratne informacije", "app.components.admin.PostManager.issueFormTitle": "Uredi problem", "app.components.admin.PostManager.latestFeedbackMode": "Iskoristite posljednju službenu obavijest kao objašnjenje", "app.components.admin.PostManager.layerAdded": "<PERSON><PERSON><PERSON> je uspješ<PERSON> dodan", "app.components.admin.PostManager.likes": "S<PERSON>đa mi se", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Premještanjem ovog unosa iz trenutnog projekta izgubit ćete informacije o dodijeljenim fazama. Želite li nastaviti?", "app.components.admin.PostManager.mapData": "Podaci karte", "app.components.admin.PostManager.multipleInputs": "{ideaCount} objava", "app.components.admin.PostManager.newFeedbackMode": "Napišite obavijest kako biste objas<PERSON>li ovu promjenu", "app.components.admin.PostManager.noFilteredResults": "Filtri koje ste odabrali vratili rezultate", "app.components.admin.PostManager.noInputs": "<PERSON><PERSON>", "app.components.admin.PostManager.noInputsDescription": "Dodajete vlastiti doprinos ili počinjete s projektom prethodnog sudjelovanja.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 ulaza} one {1 ulaz} other {# ulaza}} bit će uvezeno iz odabranog projekta i faze. Uvoz će se izvoditi u pozadini, a unosi će se pojaviti u upravitelju ulaza nakon što bude dovršen.", "app.components.admin.PostManager.noOne": "Nedodijeljeno", "app.components.admin.PostManager.noProject": "<PERSON><PERSON> projekta", "app.components.admin.PostManager.officialFeedbackModal.author": "Autor", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "Odaberite kako će se vaše ime pojaviti", "app.components.admin.PostManager.officialFeedbackModal.description": "Pružanje službenih povratnih informacija pomaže u održavanju transparentnosti procesa i gradi povjerenje u platformu.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Autor je <PERSON><PERSON><PERSON>", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Potrebna je povratna informacija", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Službene povratne informacije", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Objasnite razlog promjene statusa", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Objavite povratne informacije", "app.components.admin.PostManager.officialFeedbackModal.skip": "<PERSON>sko<PERSON><PERSON> ovaj put", "app.components.admin.PostManager.officialFeedbackModal.title": "Objasnite svoju odluku", "app.components.admin.PostManager.officialUpdateAuthor": "Odaberite kako drugi vide vaše ime", "app.components.admin.PostManager.officialUpdateBody": "Objasnite promjenu ovog statusa", "app.components.admin.PostManager.offlinePicks": "Offline izbori", "app.components.admin.PostManager.offlineVotes": "Izvanmrežni glasovi", "app.components.admin.PostManager.onlineVotes": "Online glasovi", "app.components.admin.PostManager.optionFormTitle": "Opcija uređivanja", "app.components.admin.PostManager.participants": "Sudionici", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Online odabiri", "app.components.admin.PostManager.pbItemCountTooltip": "Broj odabira ovog prijedloga u sklopu participativnih proračuna drugih sudionika", "app.components.admin.PostManager.petitionFormTitle": "<PERSON><PERSON><PERSON> petici<PERSON>", "app.components.admin.PostManager.postedIn": "Objavljeno u {projectLink}", "app.components.admin.PostManager.projectFormTitle": "<PERSON><PERSON><PERSON> projekt", "app.components.admin.PostManager.projectsTab": "Projekti", "app.components.admin.PostManager.projectsTabTooltipContent": "Možete povući i ispustiti objave kako biste ih premjestili iz jednog projekta u drugi. Vodite računa o tome da ćete kod projekata sa vremenskim trakama ipak morati dodati objavu u određenu fazu.", "app.components.admin.PostManager.proposalFormTitle": "Uredi prijedlog", "app.components.admin.PostManager.proposedBudgetTitle": "Predloženi proračun", "app.components.admin.PostManager.publication_date": "Objavljeno dana", "app.components.admin.PostManager.questionFormTitle": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.reactions": "Reak<PERSON><PERSON>", "app.components.admin.PostManager.resetFiltersButton": "Poništi filtre", "app.components.admin.PostManager.resetInputFiltersDescription": "Poništite filtre kako biste vidjeli sve unose.", "app.components.admin.PostManager.saved": "Spremljeno", "app.components.admin.PostManager.screeningTooltip": "Probir nije uključen u vaš trenutni plan. Razgovarajte sa svojim upraviteljem uspjeha vlade ili administratorom da ga otključate.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Provjera je isključena za ovu fazu. Idite na postavljanje faza da biste ga omogućili", "app.components.admin.PostManager.selectAPhase": "Odaberite fazu", "app.components.admin.PostManager.selectAProject": "Odaberite projekt", "app.components.admin.PostManager.setAsDefaultMapView": "Spremite trenutnu središnju točku i razinu zumiranja kao zadane za mapu", "app.components.admin.PostManager.startFromPastInputs": "Započni s prošlim unosima", "app.components.admin.PostManager.statusChangeGenericError": "Došlo je do pogreške. Molimo vas pokušajte kasnije ili kontaktirajte podršku.", "app.components.admin.PostManager.statusChangeSave": "Promijenite status", "app.components.admin.PostManager.statusesTab": "<PERSON><PERSON>", "app.components.admin.PostManager.statusesTabTooltipContent": "Povucite i ispustite objavu kako biste promijenili njezin status. Autor i sudionici u raspravi će biti obaviješteni o ovoj promjeni.", "app.components.admin.PostManager.submitApiError": "<PERSON><PERSON><PERSON> je do problema pri slanju obrasca. Provjerite ima li grešaka i pokušajte ponovno.", "app.components.admin.PostManager.timelineTab": "Vremenska traka", "app.components.admin.PostManager.timelineTabTooltipText": "Povucite i ispustite objave u razne faze projekta.", "app.components.admin.PostManager.title": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.topicsTab": "Oznake", "app.components.admin.PostManager.topicsTabTooltipText": "Dodajte oznake unosu koristeći povlačenje i ispuštanje.", "app.components.admin.PostManager.view": "Pogled", "app.components.admin.PostManager.votes": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votesByInputExportFileName": "glasovi_po_unosu", "app.components.admin.PostManager.votesByUserExportFileName": "glasovi_po_korisniku", "app.components.admin.PostManager.webMapAlreadyExists": "Možete dodati samo jednu web kartu odjednom. Uklonite trenutni da biste uvezli drugi.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Kartografske podatke možete učitati samo kao GeoJSON slojeve ili kao uvoz iz ArcGIS Online. Uklonite sve trenutne GeoJSON slojeve ako želite povezati web kartu.", "app.components.admin.PostManager.webMapTooltip": "Možete pronaći ID portala Web karte na vašoj stranici ArcGIS Online stavke, s desne strane.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {Manje od jednog dana} one {Jednog dana} other {# dana}} lijevo", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "Da, izbriši rezultate ankete", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "Ovo se ne može poništiti", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Brisanje rezultata ankete", "app.components.admin.ProjectEdit.survey.downloadResults2": "Preuzmite rezultate ankete", "app.components.admin.ReportExportMenu.FileName.fromFilter": "od", "app.components.admin.ReportExportMenu.FileName.groupFilter": "grupa", "app.components.admin.ReportExportMenu.FileName.projectFilter": "projekt", "app.components.admin.ReportExportMenu.FileName.topicFilter": "oznaka", "app.components.admin.ReportExportMenu.FileName.untilFilter": "do", "app.components.admin.ReportExportMenu.downloadPng": "Preuzmi format PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Preuzmi format SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Preuzmi Excel", "app.components.admin.SlugInput.regexError": "Opis može sadržavati samo obična mala slova (a-z), bro<PERSON><PERSON> (0-9) i crtice (-). Crtica ne može biti na mjestu prvog i posljednjeg znaka. Dvije uzastopne crtice (--) su zabranjene.", "app.components.admin.TerminologyConfig.saveButton": "Sp<PERSON>i", "app.components.admin.commonGroundInputManager.title": "Titula", "app.components.admin.seatSetSuccess.admin": "Administrator", "app.components.admin.seatSetSuccess.allDone": "Gotovo", "app.components.admin.seatSetSuccess.close": "Zatvoriti", "app.components.admin.seatSetSuccess.manager": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.orderCompleted": "Narudžba izvršena", "app.components.admin.seatSetSuccess.reflectedMessage": "Promjene na vašem planu odrazit će se na vaš sljedeći ciklus naplate.", "app.components.admin.seatSetSuccess.rightsGranted": "Odabranim korisnicima dodijeljeno je {seatType} prava.", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Jeste li sigurni da želite izbrisati sve rezultate ankete?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "Ova metoda sudjelovanja je u beta verziji. Postupno je uvodimo kako bismo prikupili povratne informacije i poboljšali iskustvo.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Prikupljanje povratnih informacija o dokumentu prilagođena je značajka i nije uključena u vašu trenutnu licencu. Obratite se svom GovSuccess Manageru da saznate više o tome.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Potreban je broj dana", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "<PERSON><PERSON><PERSON> dana za postizanje minimalnog broja glasova", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Više informacija o tome kako ugraditi vezu za Google Forms može se pronaći na {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project#h_163e33df30", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "ovaj članak podrške", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "I<PERSON>ja", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Inicijativa", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Kako bi ovaj unos trebao zvati?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Ovdje navedite vezu na svoj Konveio dokument. Pročitajte naš {supportArticleLink} za više informacija o postavljanju Konveija.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "članak podrške", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "Ovo nije uključeno u vaš trenutni plan. Obratite se svom upravitelju uspjeha vlade ili administratoru da ga otključa.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Maksimalan iznos proračuna je obavezan", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "<PERSON><PERSON><PERSON><PERSON> broj glasova po opciji mora biti manji ili jednak ukupnom broju glasova", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Potreban je maksimalan broj g<PERSON>ova", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "<PERSON><PERSON><PERSON> poruka", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Minimalni iznos proračuna ne može biti veći od maksimalnog", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Minimum iznos proračuna je obavezan", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Minimalan broj glasova ne može biti veći od maksimalnog broja", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Potreban je <PERSON>an broj g<PERSON>ova", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Ned<PERSON><PERSON><PERSON> datum završetka", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Ned<PERSON><PERSON><PERSON> datum poč<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Opcija", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Kartica Input Manager", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Konfigurirajte opcije glasovanja na kartici Input manager nakon st<PERSON><PERSON> faze.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Konfigurirajte opcije glasovanja u {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Mogućnosti sudjelovanja", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "Sudionici", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Peticija", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Administratori i upravitelji", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b><PERSON><PERSON><PERSON> s komentarima:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} može sudjelovati u ovoj fazi.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>komentara:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Zajednička faza uzemljenja", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Izbriši fazu", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "Da, iz<PERSON>ši ovu fazu", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "Jeste li sigurni da želite izbrisati ovu fazu?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "<PERSON>vi podaci koji se odnose na ovu fazu bit će izbrisani. To se ne može poništiti.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Faza anotacije dokumenta", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Svat<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "Faza vanjskog istraživanja", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Faza ideje", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "U fazi istraživanja platforme", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Informacijska faza", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "Mješovita prava", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "<PERSON><PERSON> da<PERSON> završ<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Faza ankete", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Faza prijedloga", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Prijavljeni za događaj:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Registrirani k<PERSON>i", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b><PERSON><PERSON><PERSON><PERSON> unose:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b><PERSON><PERSON><PERSON><PERSON><PERSON> ankete:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Korisnici s potvrđenom e-poštom", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>Volontiranje:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Faza volontiranja", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>g<PERSON>ova:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Faza glasanja", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Tko može sudjelovati?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Unosi neće biti vidljivi dok ih administrator ne pregleda i odobri. Autori ne mogu uređivati unose nakon što su pregledani ili na njih reagirali.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "Samo za administratore", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "Svatko s vezom može komunicirati s nacrtom projekta", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Odobravanje omogućuje voditeljima projekta da objave projekt.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "<PERSON><PERSON><PERSON><PERSON> {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Nacrt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Uredi opis", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "Svat<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "grupe", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "Skriven", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Offline birači", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Samo administratori{inFolder, select, true { ili upravitelji mapa} other {}} mogu objaviti projekt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 sudionik} other {{participantsCount} sudionici}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Sudionici u ugrađenim metodama (npr. vanjske ankete)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Sljedbenici projekta", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Napomena: Omogućavanje dozvola za anonimno ili otvoreno sudjelovanje može omogućiti korisnicima višestruko sudjelovanje, što dovodi do pogrešnih ili nepotpunih korisničkih podataka.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "Sudionici <b>ne uklju<PERSON></b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Sudionici uključuju:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Registrirani za događaj", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "Korisnici u interakciji s Go Vocal metodama", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "Čeka se odobrenje", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Recenzenti projekta su obaviješteni.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "Javnost", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Objavljeno - Aktivno", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Objavljeno - Završeno", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Osvježi vezu za pregled projekta", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Ponovno generiraj vezu za pregled projekta. Ovo će poništiti prethodnu vezu.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Stare veze će prestati raditi, ali možete generirati novu u bilo kojem trenutku.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Jeste li sigurni? Ovo će onemogućiti trenutnu vezu", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "Da, osvježi vezu", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Zatražite odobrenje", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "Projekt mora odobriti administrator{inFolder, select, true { ili jedan od Upravitelja mapa} other {}} prije nego što ga možete objaviti. Pritisnite donji gumb da biste zatražili odobrenje.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "postavke", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Udio", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "Veza je kopirana", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Dijeljenje privatnih veza nije uključeno u vaš trenutni plan. Razgovarajte sa svojim upraviteljem uspjeha vlade ili administratorom da ga otključate.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Podijelite ovaj projekt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "<PERSON><PERSON> ima pristup", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "Pogled", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Projekt", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Prijedlog", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Minimalan broj glasova koji se uzima u obzir", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Potreban je <PERSON>an broj g<PERSON>ova", "app.components.app.containers.AdminPage.ProjectEdit.report": "izvješće", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Zah<PERSON>jevat<PERSON> provjeru ul<PERSON>a", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Vremenska Crta", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Promet", "app.components.formBuilder.cancelMethodChange1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.changeMethodWarning1": "Promjena metoda može dovesti do skrivanja svih ulaznih podataka generiranih ili primljenih tijekom korištenja prethodne metode.", "app.components.formBuilder.changingMethod1": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.confirmMethodChange1": "Da, nastavi", "app.components.formBuilder.copySurveyModal.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.description": "Ovo će kopirati sva pitanja i logiku bez odgovora.", "app.components.formBuilder.copySurveyModal.duplicate": "Duplikat", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "U ovom projektu nisu pronađene odgovarajuće faze", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "Nije odabrana faza. Prvo odaberite fazu.", "app.components.formBuilder.copySurveyModal.noProject": "<PERSON><PERSON> projekta", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Nije odabran nijedan projekt. Prvo odaberite projekt.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "Već ste spremili promjene u ovu anketu. Ako duplicirate drugu anketu, promjene će biti izgubljene.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Faza ankete", "app.components.formBuilder.copySurveyModal.title": "Odaberite anketu za dupliciranje", "app.components.formBuilder.editWarningModal.addOrReorder": "Dodajte ili promijenite redoslijed pitanja", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Vaši podaci o odgovoru mogu biti netočni", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Uredi tekst", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Ispravljate grešku pri upisu? To neće utjecati na vaše podatke o odgovoru", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Izgubit ćete podatke odgovora povezane s tim pitanjem", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Izbriši <PERSON>", "app.components.formBuilder.editWarningModal.exportYouResponses2": "izvezite svoje odgovore.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Upozorenje: mogli biste zauvijek izgubiti podatke o odgovoru. Prije nastavka,", "app.components.formBuilder.editWarningModal.noCancel": "Ne, otkaži", "app.components.formBuilder.editWarningModal.title4": "Uredite anketu uživo", "app.components.formBuilder.editWarningModal.yesContinue": "Da, nastavi", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "postavke prava pristupa za ovu anketu", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "Omogućena je 'Demografska polja u anketnom obrascu'. Kada se prikaže obrazac za anketu, sva konfigurirana demografska pitanja bit će dodana na novu stranicu neposredno prije završetka ankete. Ova pitanja se mogu promijeniti u {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "postavke prava pristupa za ovu fazu.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Ispitanici u anketi neće se morati prijaviti ili prijaviti za slanje odgovora na anketu, što može rezultirati dvostrukim slanjem", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "Preskakanjem koraka prijave/prijave prihvaćate da nećete prikupljati demografske podatke o ispitanicima, što može utjecati na vaše mogućnosti analize podataka", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "<PERSON>va je anketa postavljena tako da dopušta pristup \"Svakome\" pod karticom Prava pristupa.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "<PERSON><PERSON> to želite promijeniti, mo<PERSON><PERSON> to učiniti u {accessRightsSettingsLink}", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Postavljate sljedeća demografska pitanja ispitanicima kroz korak prijave/prijave.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "<PERSON>ko biste pojednostavili prikupljanje demografskih podataka i osigurali njihovu integraciju u vašu korisničku bazu podataka, savjetujemo da sva demografska pitanja uključite izravno u postupak prijave/prijave. Da biste to uč<PERSON>li, upotrijebite {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Zamolite korisnike da prate područja ili teme", "app.components.onboarding.followHelperText": "Ovo aktivira korak u procesu registracije gdje će korisnici moći pratiti područja ili teme koje odaberete u nastavku", "app.components.onboarding.followPreferences": "Slijedite postavke", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} u planu, {noOfAdditionalSeats} dop", "app.components.seatsWithinPlan.seatsWithinPlanText": "Sjedala u planu", "app.containers.Admin.Campaigns.campaignFrom": "Od:", "app.containers.Admin.Campaigns.campaignTo": "Za:", "app.containers.Admin.Campaigns.customEmails": "Prilagođene e-pošte", "app.containers.Admin.Campaigns.customEmailsDescription": "Pošaljite prilagođene e-poruke i provjerite statistiku.", "app.containers.Admin.Campaigns.noAccess": "<PERSON><PERSON> na<PERSON> je, ali <PERSON> se da vam pristup odjeljku e-pošte nije omogućen", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Automatske e-poruke", "app.containers.Admin.Insights.tabReports": "Izvještaji", "app.containers.Admin.Invitations.a11y_removeInvite": "Ukloni pozivnicu", "app.containers.Admin.Invitations.addToGroupLabel": "Dodajte ove osobe u određene ručne grupe korisnika", "app.containers.Admin.Invitations.adminLabel1": "Pozvanima daj<PERSON> administratorska prava", "app.containers.Admin.Invitations.adminLabelTooltip": "<PERSON>da odaberete ovu opciju, osobe koje ste pozvali će imati pristup svim postavkama platforme.", "app.containers.Admin.Invitations.configureInvitations": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pozivnice", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Nisu pronađene pozivnice koje odgovaraju kriterijima vaše pretrage", "app.containers.Admin.Invitations.deleteInvite": "Izbriši", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Jeste li sigurni da želite izbrisati ovu pozivnicu?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Prekid procesa pozivanja omogućit će vam da ovoj osobi ponovo pošaljete pozivnicu.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Preuzmite i popunite predložak", "app.containers.Admin.Invitations.downloadTemplate": "<PERSON>uz<PERSON> predložak", "app.containers.Admin.Invitations.email": "E-pošta", "app.containers.Admin.Invitations.emailListLabel": "Unesite adrese e-pošte osoba koje želite pozvati, razdvajajući ih zarezom.", "app.containers.Admin.Invitations.exportInvites": "Izvezite sve pozivnice", "app.containers.Admin.Invitations.fileRequirements": "Važno: Nemojte brisati stupce iz uvezenog predložaka. Ukoliko ih ne koristite, ostavite ih prazne.", "app.containers.Admin.Invitations.filetypeError": "Neispravan tip datoteke. Podržani su samo XLSX dokumenti.", "app.containers.Admin.Invitations.groupsPlaceholder": "<PERSON>je odabrana grupa", "app.containers.Admin.Invitations.helmetDescription": "Pozovite korisnike na platformu", "app.containers.Admin.Invitations.helmetTitle": "Nadzorna ploča za pozivnice administratora", "app.containers.Admin.Invitations.importOptionsInfo": "Te opcije bit će uzete u obzir ako nisu definirane u Excel datoteci.\n      Posjetite {supportPageLink} za više informacija.", "app.containers.Admin.Invitations.importTab": "Uvezite adrese e-pošte", "app.containers.Admin.Invitations.invitationExpirationWarning": "Imajte na umu da pozivnice istječu nakon 30 dana. Nakon tog razdoblja još uvijek ih možete ponovno poslati.", "app.containers.Admin.Invitations.invitationOptions": "Opcije pozivnica", "app.containers.Admin.Invitations.invitationSubtitle": "U bilo kojem trenutku pozovite ljude na platformu. Primit ćete neutralnu pozivnicu e-poštom s vašim logotipom u kojoj se od vas traži da se registrirate na platformi.", "app.containers.Admin.Invitations.invitePeople": "Pozovite osobe putem e-pošte", "app.containers.Admin.Invitations.inviteStatus": "<PERSON><PERSON>", "app.containers.Admin.Invitations.inviteStatusAccepted": "P<PERSON>h<PERSON>ć<PERSON>", "app.containers.Admin.Invitations.inviteStatusPending": "Na čekanju", "app.containers.Admin.Invitations.inviteTextLabel": "Opcijski možete dodati poruku koja će biti dodana uz pozivnicu.", "app.containers.Admin.Invitations.invitedSince": "Pozvan", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Odaberite jezik pozivnice", "app.containers.Admin.Invitations.moderatorLabel": "Dajte ovim osobama prava upravljanja projektom", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Ukoliko odaberete ovu opciju, pozvane osobe će postati voditelji odabranih projekata. Možete pronaći više informacija o pravima voditelja projekta {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "ovdje", "app.containers.Admin.Invitations.name": "Ime", "app.containers.Admin.Invitations.processing": "Pozivnice se šalju. Pričekajte...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "<PERSON><PERSON> o<PERSON> projekata", "app.containers.Admin.Invitations.save": "Poslane pozivnice", "app.containers.Admin.Invitations.saveErrorMessage": "Doš<PERSON> je do jedne ili više pogrešaka i pozivnice nisu poslane. Molimo vas da ispravite pogreške koje se nalaze u nastavku i pokušate ponovo.", "app.containers.Admin.Invitations.saveSuccess": "Us<PERSON><PERSON>h!", "app.containers.Admin.Invitations.saveSuccessMessage": "Pozivnice su uspješno poslane.", "app.containers.Admin.Invitations.supportPage": "stranica podrške", "app.containers.Admin.Invitations.supportPageLinkText": "posjetite stranicu podrške", "app.containers.Admin.Invitations.tabAllInvitations": "Sve pozivnice", "app.containers.Admin.Invitations.tabInviteUsers": "Pozovite korisnike", "app.containers.Admin.Invitations.textTab": "Ručno unesite adrese e-pošte", "app.containers.Admin.Invitations.unknownError": "Došlo je do pogreške. Pokušajte kasnije.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Prenesite svoju dovršenu datoteku predloška", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} ako želite više informacija o podržanim stupcima u uvezenom predlošku.", "app.containers.Admin.Moderation.all": "Sve", "app.containers.Admin.Moderation.belongsTo": "Pripada", "app.containers.Admin.Moderation.collapse": "p<PERSON><PERSON><PERSON> manje", "app.containers.Admin.Moderation.comment": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "Izbrisati", "app.containers.Admin.Moderation.confirmCommentDeletion": "Jeste li sigurni da želite izbrisati ovaj komentar? Ovo je trajno i ne može se poništiti.", "app.containers.Admin.Moderation.content": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.date": "Datum", "app.containers.Admin.Moderation.deleteComment": "Izbriši komentar", "app.containers.Admin.Moderation.goToComment": "Otvorite komentar u novoj kartici", "app.containers.Admin.Moderation.goToPost": "Otvorite objavu u novoj kartici", "app.containers.Admin.Moderation.goToProposal": "Otvorite prijedlog u novoj kartici", "app.containers.Admin.Moderation.markFlagsError": "Označavanje stavki nije uspjelo. Pokušajte ponovo.", "app.containers.Admin.Moderation.markNotSeen": "<PERSON><PERSON><PERSON><PERSON> {selectedItemsCount, plural, one {# stavka} other {# stavke}} kao ne<PERSON>", "app.containers.Admin.Moderation.markSeen": "<PERSON><PERSON><PERSON><PERSON> {selectedItemsCount, plural, one {# stavka} other {# stavke}} kao pro<PERSON>", "app.containers.Admin.Moderation.moderationsTooltip": "Ova stranica vam omogućuje da brzo provjerite sve nove objave na platformi, uključujući ideje i komentare. Unose možete označiti kao „pregledane“ kako bi drugi mogli pratiti koji od njih još uvijek čekaju na obradu.", "app.containers.Admin.Moderation.noUnviewedItems": "Trenutačno nema nepregledanih stavki", "app.containers.Admin.Moderation.noViewedItems": "Trenutačno nema pregledanih stavki", "app.containers.Admin.Moderation.pageTitle1": "hraniti se", "app.containers.Admin.Moderation.post": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.profanityBlockerSetting": "Blokiranje nepristojnih riječi", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Blokirajte objave koje sadrže najčešće prijavljene uvredljive riječi.", "app.containers.Admin.Moderation.project": "Projekt", "app.containers.Admin.Moderation.read": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.readMore": "Pročitajte više", "app.containers.Admin.Moderation.removeFlagsError": "Uklanjanje upozorenja nije uspjelo. Pokušajte ponovo.", "app.containers.Admin.Moderation.rowsPerPage": "Redova po strani", "app.containers.Admin.Moderation.settings": "Postavke", "app.containers.Admin.Moderation.settingsSavingError": "Spremanje nije uspjelo. Pokušajte izmijeniti postavke.", "app.containers.Admin.Moderation.show": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.status": "<PERSON><PERSON>", "app.containers.Admin.Moderation.successfulUpdateSettings": "Postavke su uspješno ažurirane.", "app.containers.Admin.Moderation.type": "Tip", "app.containers.Admin.Moderation.unread": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Stranica se sastoji od sljedećih odjeljaka. Možete ih uključiti/isključiti i urediti prema potrebi.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "Prikaz stranice", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Nije prikazano na stranici", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Prikazano na stranici", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Do<PERSON><PERSON><PERSON> da<PERSON> (maks. 50 MB) koje će biti dostupne za preuzimanje sa stranice.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Donji o<PERSON>jak s informacijama", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Dodajte vlastiti sadržaj prilagodljivom odjeljku u dnu stranice.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Prikaži događaje povezane s projektom.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Istaknuti natpis", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Prilagodite sliku i tekst istaknutog natpisa stranice.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "<PERSON><PERSON> projekata", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Prikažite projekte na temelju postavki stranice. Možete pretpregledati projekte koji će biti prikazani.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Odjeljak s informacijama na vrhu", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Dodajte vlastiti sadržaj prilagodljivom odjeljku na vrhu stranice.", "app.containers.Admin.PagesAndMenu.addButton": "Dodaj na navigacijsku traku", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Naziv u navigacijskoj traci", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Jeste li sigurni da želite izbrisati ovu stranicu? Ovu radnju ne možete poništiti.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Navedite naslov za sve jezike", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Ostale dostupne stranice", "app.containers.Admin.PagesAndMenu.components.savePage": "Spremi stranicu", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "Stranica uspješno spremljena", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (maks. 50 MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "<PERSON><PERSON> mogu<PERSON>e spremiti privitke", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Datoteke ne smiju biti veće od 50 MB. Dodane datoteke će se prikazivati na dnu stranice", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "P<PERSON>vici spreml<PERSON>ni", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Privici | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Spremi i omogući privitke", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Spremi privitke", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Navedite sadržaj za sve jezike", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "<PERSON><PERSON> bilo moguće spremiti odjeljak s informacijama na dnu", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Spremljen odjeljak s informacijama na dnu", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Donji o<PERSON>jak s informacijama", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Spremi i omogući donji odjeljak s informacijama", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Spremi odjeljak s informacijama na dnu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Izrada prilagođenih stranica nije uključena u vašu trenutnu licencu. Obratite se svom GovSuccess Manageru da saznate više o tome.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Dodajte najmanje jednu <PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "Prema području", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "Prema oznaci(oznakama)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Prikaz projekata prema oznaci ili području nije dio vaše trenutne licence. Obratite se svom GovSuccess Manageru da saznate više o tome.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Uredi prilagođenu stra<PERSON>u", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Povezani projekti", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Odaberite koji će se projekti i povezani događaji prikazati na stranici.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Stranica uspješno kreirana", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "Stranica uspješno spremljena", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Prilagođena stranica spremljena", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Naslov u navigacijskoj traci", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "<PERSON><PERSON><PERSON><PERSON> prilagođ<PERSON> stranicu | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Kreiraj prilagođenu stranicu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Postavke stranice", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Spremi prilagođenu stranicu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Odaberite područje", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Odabrano područje", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "O<PERSON><PERSON>ne oznake", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "Opis može sadržavati samo obična mala slova (a-z), bro<PERSON><PERSON> (0-9) i crtice (-). Crtica ne može biti na mjestu prvog i posljednjeg znaka. Dvije uzastopne crtice (--) su zabranjene.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Morate unijeti „puž“", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Unesite naslov na svakom jeziku", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Unesite naslov", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Prikaz prilagođene stranice", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON>b", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "<PERSON>redi prilago<PERSON> stra<PERSON> | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "<PERSON><PERSON><PERSON><PERSON> stranic<PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "Za jednofazne projekte, ako je datum završetka prazan i opis nije ispunjen, vremenska traka neće biti prikazana na stranici projekta.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "<PERSON><PERSON> dostu<PERSON>nih projekata na temelju {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Projekt nema filtra oznake ili područja, pa se neće prikazati projekti.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Popis projekata | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "postavke stranice", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "<PERSON><PERSON> projekata", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Sljedeći projekti prikazat će se na stranici na temelju {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "ZADANO", "app.containers.Admin.PagesAndMenu.deleteButton": "Izbriši", "app.containers.Admin.PagesAndMenu.editButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerError": "<PERSON><PERSON> mogu<PERSON>e spremiti istaknuti natpis", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Istaknuti natpis spremljen", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Spremi istaknuti natpis", "app.containers.Admin.PagesAndMenu.homeTitle": "Početna", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Unesite sadržaj za barem jedan jezik", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Na navigacijsku traku možete dodati najviše 5 stavki", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Stranice i izbornik | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Ukloni iz navigacijske trake", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Spremi i omogući istaknuti natpis", "app.containers.Admin.PagesAndMenu.title": "Stranice i izbornik", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.topInfoError": "<PERSON><PERSON> mogu<PERSON>e spremiti odjeljak s informacijama na vrhu", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Spremljen odjeljak s informacijama na vrhu", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> s informacijama na vrhu | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Odjeljak s informacijama na vrhu", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Spremi i omogući gornji odjeljak s informacijama", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Spremi odjeljak s informacijama na vrhu", "app.containers.Admin.PagesAndMenu.viewButton": "Prikaz", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "<PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Zajednica", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Izvršni sažetak", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Indikatori uključenosti najviše razine", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "Sljedeći odjeljak opisuje pokazatelje uključenosti, ističući vaš naš napredak prema poticanju inkluzivnije i reprezentativnije platforme za sudjelovanje.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "sudionik<PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Indikatori sudjelovanja na najvišoj razini", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "Sljedeći odjeljak opisuje ključne pokazatelje sudjelovanja za odabrani vremenski raspon, p<PERSON>ž<PERSON><PERSON><PERSON><PERSON> pregled trendova angažmana i metriku učinka.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projekti", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "objavljeni projekti", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Izvješće o platformi", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "Vaši projekti", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "Sljedeći odjeljak daje pregled javno vidljivih projekata koji se preklapaju s odabranim vremenskim rasponom, najčešće korištene metode u tim projektima i metriku koja se odnosi na ukupan iznos sudjelovanja.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Vremenska linija registracija", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Blokirani korisnici", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Upravljanje blokiranim korisnicima.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Izbriši grupu", "app.containers.Admin.Users.GroupsHeader.editGroup": "Uredi grupu", "app.containers.Admin.Users.GroupsPanel.admins": "<PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.allUsers": "Registrirani k<PERSON>i", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Grupe", "app.containers.Admin.Users.GroupsPanel.managers": "Voditelji projekata", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Dodijeljene stavke", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Dobijte pregled svih ljudi i organizacija koje su se registrirale na platformi. Dodajte odabrane korisnike u ručne ili pametne grupe.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Pozivnica na čekanju", "app.containers.Admin.Users.admin": "Administrator", "app.containers.Admin.Users.assign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.assignedItems": "Dodijeljene stavke za {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "Kupite jedno dodatno mjesto", "app.containers.Admin.Users.changeUserRights": "Promjena korisničkih prava", "app.containers.Admin.Users.confirm": "Potvrdi", "app.containers.Admin.Users.confirmAdminQuestion": "Jeste li sigurni da korisniku {name} ž<PERSON><PERSON> dati <PERSON>ka prava platforme?", "app.containers.Admin.Users.confirmNormalUserQuestion": "Jeste li sigurni da ž<PERSON>te korisnika {name} postaviti kao normalnog korisnika?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Jeste li sigurni da <PERSON><PERSON><PERSON> post<PERSON> {name} kao običan korisnik? Imajte na umu da će nakon potvrde izgubiti upraviteljska prava za sve projekte i mape kojima su dodijeljeni.", "app.containers.Admin.Users.deleteUser": "Izbriši korisnika", "app.containers.Admin.Users.email": "E-pošta", "app.containers.Admin.Users.folder": "Mapa", "app.containers.Admin.Users.folderManager": "Upravitelj mape", "app.containers.Admin.Users.helmetDescription": "<PERSON><PERSON> korisnika koji su <PERSON>i", "app.containers.Admin.Users.helmetTitle": "Admin – Nadzorna ploča korisnika", "app.containers.Admin.Users.inviteUsers": "Pozovite korisnike", "app.containers.Admin.Users.joined": "S<PERSON>je<PERSON>", "app.containers.Admin.Users.lastActive": "Zadnje aktivno", "app.containers.Admin.Users.name": "Ime", "app.containers.Admin.Users.noAssignedItems": "<PERSON><PERSON> sta<PERSON>ki", "app.containers.Admin.Users.options": "Opcije", "app.containers.Admin.Users.permissionToBuy": "Da biste korisniku {name} dali <PERSON><PERSON> prava, morate kupiti 1 dodatno mjesto.", "app.containers.Admin.Users.platformAdmin": "Administrator platforme", "app.containers.Admin.Users.projectManager": "Upravitelj projekta", "app.containers.Admin.Users.reachedLimitMessage": "Dosegli ste ograničenje broja mjesta u svom planu, bit će dodano 1 dodatno mjesto za korisnika {name}.", "app.containers.Admin.Users.registeredUser": "Registrirani korisnik", "app.containers.Admin.Users.remove": "Ukloniti", "app.containers.Admin.Users.removeModeratorFrom": "Korisnik moderira mapu kojoj ovaj projekt pripada. Umjesto toga uklonite dodjelu iz \"{folderTitle}\".", "app.containers.Admin.Users.role": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.seeProfile": "Pogledajte profil", "app.containers.Admin.Users.selectPublications": "Odaberite projekte ili mape", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Upišite za pretraživanje", "app.containers.Admin.Users.setAsAdmin": "<PERSON><PERSON> kao <PERSON>a", "app.containers.Admin.Users.setAsNormalUser": "<PERSON><PERSON> kao normalnog korisnika", "app.containers.Admin.Users.setAsProjectModerator": "Postavi kao voditelja projekta", "app.containers.Admin.Users.setUserAsProjectModerator": "<PERSON><PERSON><PERSON><PERSON> {name} kao voditelja projekta", "app.containers.Admin.Users.userBlockModal.allDone": "Gotovo", "app.containers.Admin.Users.userBlockModal.blockAction": "Blokirati k<PERSON>", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Sadržaj ovog korisnika neće biti uklonjen ovom radnjom. Ne zaboravite moderirati njihov sadržaj ako je potrebno.", "app.containers.Admin.Users.userBlockModal.blocked": "Blokiran", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "<PERSON><PERSON><PERSON> korisnik je blokiran od {from}. <PERSON><PERSON><PERSON> traje do {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Jeste li sigurni da ž<PERSON>te deblokirati {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} je blo<PERSON><PERSON> do {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 dan} other {{numberOfDays} dana}}", "app.containers.Admin.Users.userBlockModal.header": "Blokirati k<PERSON>", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Razlog", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "<PERSON><PERSON> će biti priopćeno blokiranom korisniku.", "app.containers.Admin.Users.userBlockModal.subtitle1": "Odabrani korisnik neće se moći prijaviti na platformu {daysBlocked}. A<PERSON> to <PERSON><PERSON><PERSON>, mož<PERSON> ih deblokirati s popisa blokiranih korisnika.", "app.containers.Admin.Users.userBlockModal.unblockAction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON>, <PERSON><PERSON><PERSON> de<PERSON> ovog korisnika", "app.containers.Admin.Users.userDeletionConfirmation": "Trajno ukloniti ovog korisnika?", "app.containers.Admin.Users.userDeletionFailed": "Do<PERSON><PERSON> je do pogreške prilikom brisanja korisnika, pokušajte kasnije.", "app.containers.Admin.Users.userDeletionProposalVotes": "O<PERSON> će također izbrisati sve glasove ovog korisnika o prijedlozima koji su još otvoreni za glasovanje.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.userInsights": "<PERSON><PERSON><PERSON> k<PERSON>", "app.containers.Admin.Users.youCantDeleteYourself": "Ne možete obrisati vlastiti račun putem administratorske stranice", "app.containers.Admin.Users.youCantUnadminYourself": "U ovom trenutku ne možete napustiti ulogu administratora", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Monitor zajednice", "app.containers.Admin.communityMonitor.healthScore": "Ocjena zdravlja", "app.containers.Admin.communityMonitor.healthScoreDescription": "Ovaj rezultat je prosjek svih pitanja na skali sentimenta na koja su sudionici odgovorili za odabrano razdoblje.", "app.containers.Admin.communityMonitor.lastQuarter": "zadnja četvrtina", "app.containers.Admin.communityMonitor.liveMonitor": "Monitor uživo", "app.containers.Admin.communityMonitor.noResults": "Nema rezultata za ovo razdoblje.", "app.containers.Admin.communityMonitor.noSurveyResponses": "Nema odgovora na anketu", "app.containers.Admin.communityMonitor.participants": "Sudionici", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "izvješća", "app.containers.Admin.communityMonitor.settings": "postavke", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Community Monitor Survey prihvaća prijave.", "app.containers.Admin.communityMonitor.settings.accessRights2": "<PERSON><PERSON><PERSON> pristupa", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "Nakon što korisnik registrira prisustvovanje događaju, po<PERSON><PERSON>je glas ili se vrati na stranicu projekta nakon slanja ankete.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Upravitelji Community Monitora mogu pristupiti i upravljati svim postavkama i podacima Community Monitora.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Upravitelji monitora zajednice", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Upravitelji mogu uređivati anketu i dopuštenja za Community Monitor, vidjeti podatke o odgovorima i stvarati izvješća.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "Zadana vrijednost učestalosti je 100%.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Učestalost skočnih prozora (0 do 100)", "app.containers.Admin.communityMonitor.settings.management2": "Upravljanje", "app.containers.Admin.communityMonitor.settings.popup": "Skočni prozor", "app.containers.Admin.communityMonitor.settings.popupDescription3": "Korisnicima se povremeno prikazuje skočni prozor koji ih potiče da ispune Anketu za praćenje zajednice. Možete prilagoditi učestalost koja određuje postotak korisnika koji će nasumično vidjeti skočni prozor kada se ispune dolje navedeni uvjeti.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Postavke skočnih prozora", "app.containers.Admin.communityMonitor.settings.preview": "Pregled", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "Korisnik još nije ispunio anketu u prethodna 3 mjeseca.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "Korisnik već nije vidio skočni prozor u prethodna 3 mjeseca.", "app.containers.Admin.communityMonitor.settings.save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.saved": "Spremljeno", "app.containers.Admin.communityMonitor.settings.settings": "postavke", "app.containers.Admin.communityMonitor.settings.survey2": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.surveySettings3": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Nakon učitavanja početne ili prilagođene stranice.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonimizirajte sve korisničke podatke", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "Svi unosi korisnika u anketu bit će anonimizirani prije snimanja", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Korisnici će i dalje morati ispunjavati zahtjeve za sudjelovanje pod 'Pravima pristupa'. Podaci o korisničkom profilu neće biti dostupni u izvozu podataka ankete.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "Pod kojim se uvjetima skočni prozor može pojaviti korisnicima?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Tko su menadžeri?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Ukupni odgovori na anketu", "app.containers.Admin.communityMonitor.upsell.aiSummary": "AI sažetak", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Omogući Monitor zajednice", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Ova značajka nije uključena u vaš trenutni plan. Razgovarajte sa svojim upraviteljem uspjeha vlade ili administratorom da ga otključate.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Zdravstveni rezultat", "app.containers.Admin.communityMonitor.upsell.learnMore": "Saznajte više", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Rezultat tijekom vremena", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "Community Monitor vam pomaže da ostanete ispred prateći povjerenje stanovnika, zadovoljstvo uslugama i životom u zajednici - neprekidno.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Dobijte jasne rezultate, moćne citate i tromjesečno izvješće koje možete podijeliti s kolegama ili izabranim dužnosnicima.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Lako čitljivi rezultati koji se mijenjaju tijekom vremena", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Ključni citati stanovnika, sažeto AI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Pitanja prilagođena kontekstu vašeg grada", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Stanovnici nasumično regrutirani na platformi", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Tromjesečna izvješća u PDF formatu, spremna za dijeljenje", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "Shvatite kako se vaša zajednica osjeća prije nego problemi porastu", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Stolno računalo ili drugo", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "<PERSON><PERSON><PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tableta", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "<PERSON><PERSON><PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "<PERSON><PERSON><PERSON>", "app.containers.Admin.earlyAccessLabel": "<PERSON> pristup", "app.containers.Admin.earlyAccessLabelExplanation": "Ovo je novoizdana značajka dostupna u ranom pristupu.", "app.containers.Admin.emails.addCampaign": "Stvorite e-poštu", "app.containers.Admin.emails.addCampaignTitle": "Kreirajte novu e-poruku", "app.containers.Admin.emails.allParticipantsInProject": "Svi sudionici projekta", "app.containers.Admin.emails.allUsers": "Registrirani k<PERSON>i", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Automatske e-poruke automatski se šalju i pokreću radnje korisnika. Neke od njih možete isključiti za sve korisnike svoje platforme. Druge automatizirane e-poruke nije moguće isključiti jer su potrebne za pravilan rad vaše platforme.", "app.containers.Admin.emails.automatedEmails": "Automatizirana e-pošta", "app.containers.Admin.emails.automatedEmailsDigest": "E-poruka će biti poslana samo ako ima sad<PERSON>žaja", "app.containers.Admin.emails.automatedEmailsRecipients": "Korisnici koji će primiti ovu e-poruku", "app.containers.Admin.emails.automatedEmailsTriggers": "Dog<PERSON><PERSON><PERSON> koji pokre<PERSON>e ovu e-poruku", "app.containers.Admin.emails.changeRecipientsButton": "Promijeni primatelje", "app.containers.Admin.emails.clickOnButtonForExamples": "Kliknite gumb u nastavku da provjerite primjere ove e-pošte na našoj stranici za podršku.", "app.containers.Admin.emails.confirmSendHeader": "<PERSON>slati e-poruku svim korisnicima?", "app.containers.Admin.emails.deleteButtonLabel": "Izbriši", "app.containers.Admin.emails.draft": "Nacrt", "app.containers.Admin.emails.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editCampaignTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editDisabledTooltip2": "Uskoro: <PERSON>va e-pošta trenutno se ne može uređivati.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Tekst gumba", "app.containers.Admin.emails.editRegion_intro_multiloc": "Uvod", "app.containers.Admin.emails.editRegion_subject_multiloc": "Predmet", "app.containers.Admin.emails.editRegion_title_multiloc": "Titula", "app.containers.Admin.emails.emailCreated": "E-pošta je uspješno kreirana u nacrtu", "app.containers.Admin.emails.emailUpdated": "E-pošta usp<PERSON> a<PERSON>", "app.containers.Admin.emails.emptyCampaignsDescription": "Lako se povežite sa svojim sudionicima slanjem e-pošte. Odaberite koga ćete kontaktirati i pratite svoj angažman.", "app.containers.Admin.emails.emptyCampaignsHeader": "Pošaljite svoj prvi email", "app.containers.Admin.emails.failed": "<PERSON><PERSON>", "app.containers.Admin.emails.fieldBody": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldBodyError": "Unesite poruku e-pošte", "app.containers.Admin.emails.fieldGroupContent": "Sadržaj e-pošte", "app.containers.Admin.emails.fieldReplyTo": "Odgovori bi trebali ići", "app.containers.Admin.emails.fieldReplyToEmailError": "Unesite adresu e-pošte u ispravnom formatu, <NAME_EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Unesite adresu e-pošte", "app.containers.Admin.emails.fieldReplyToTooltip": "Možete izabrati kamo želite da stižu odgovori na vašu e-poruku.", "app.containers.Admin.emails.fieldSender": "Od", "app.containers.Admin.emails.fieldSenderError": "Unesite pošiljatelja e-pošte", "app.containers.Admin.emails.fieldSenderTooltip": "Možete odrediti koga će primatelji vidjeti kao pošiljatelja.", "app.containers.Admin.emails.fieldSubject": "Predmet poruke e-pošte", "app.containers.Admin.emails.fieldSubjectError": "Unesite predmet e-poruke", "app.containers.Admin.emails.fieldSubjectTooltip": "<PERSON>vo će biti prikazano kao predmet e-poruke. Nastojte da bude jasan i da privlači pažnju.", "app.containers.Admin.emails.fieldTo": "Prima", "app.containers.Admin.emails.fieldToTooltip": "Možete odabrati grupe korisnika koje će primiti vašu e-poruku", "app.containers.Admin.emails.formSave": "Sačuvajte kao nacrt", "app.containers.Admin.emails.formSaveAsDraft": "<PERSON><PERSON><PERSON><PERSON> kao skicu", "app.containers.Admin.emails.from": "Iz:", "app.containers.Admin.emails.groups": "Grupe", "app.containers.Admin.emails.helmetDescription": "Ručno pošaljite e-poruke grupama korisnika i aktivirajte automatske kampanje", "app.containers.Admin.emails.nameVariablesInfo2": "Možete se obratiti izravno građanima koristeći varijable {firstName} {lastName}. Npr. \"Dragi {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "Probna e-poruka poslana je na vašu adresu e-pošte", "app.containers.Admin.emails.previewTitle": "Pretpregled", "app.containers.Admin.emails.regionMultilocError": "Molimo navedite vrijednost za sve jezike", "app.containers.Admin.emails.seeEmailHereText": "<PERSON><PERSON> se pošalje e-pošta ove vrste, moći ćete je provjeriti ovdje.", "app.containers.Admin.emails.send": "Pošalji", "app.containers.Admin.emails.sendNowButton": "Poša<PERSON><PERSON> sad", "app.containers.Admin.emails.sendTestEmailButton": "Pošalji mi probnu e-poruku", "app.containers.Admin.emails.sendTestEmailTooltip2": "Kada kliknete na ovu poveznicu, probna e-pošta bit će poslana samo na vašu adresu e-pošte. To vam omogućuje da provjerite kako e-pošta izgleda u 'stvarnom životu'.", "app.containers.Admin.emails.senderRecipients": "Pošiljatelj i primatelji", "app.containers.Admin.emails.sending": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sent": "Poslano", "app.containers.Admin.emails.sentToUsers": "Ovo su e-poruke poslane korisnicima", "app.containers.Admin.emails.subject": "Predmet:", "app.containers.Admin.emails.supportButtonLabel": "Pogledajte primjere na našoj stranici za podršku", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "Do:", "app.containers.Admin.emails.toAllUsers": "Ž<PERSON>te li poslati ovu e-poruku svim registriranim korisnicima?", "app.containers.Admin.emails.viewExample": "Pogled", "app.containers.Admin.ideas.import": "Uvoz", "app.containers.Admin.inspirationHub.AllProjects": "Svi projekti", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Monitor zajednice", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Bilješka dokumenta", "app.containers.Admin.inspirationHub.ExternalSurvey": "<PERSON><PERSON><PERSON> pregled", "app.containers.Admin.inspirationHub.Filters.Country": "Zemlja", "app.containers.Admin.inspirationHub.Filters.Method": "metoda", "app.containers.Admin.inspirationHub.Filters.Search": "Pretraživanje", "app.containers.Admin.inspirationHub.Filters.Topic": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.population": "Stanovništvo", "app.containers.Admin.inspirationHub.Highlighted": "Istaknuto", "app.containers.Admin.inspirationHub.Ideation": "Ideacija", "app.containers.Admin.inspirationHub.Information": "Informacija", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Odaberite zemlju da biste vidjeli prikvačene projekte", "app.containers.Admin.inspirationHub.PinnedProjects.country": "Zemlja", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "Za ovu zemlju nisu pronađeni prikvačeni projekti. Promijenite zemlju da biste vidjeli prikvačene projekte za druge zemlje", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Promijenite zemlju da vidite više prikvačenih projekata", "app.containers.Admin.inspirationHub.Poll": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "Otvoreni kraj", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "Pročitaj više...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Faza {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "Ako ne želite da vaš projekt bude uključen u središte inspiracije, obratite se svom GovSuccess upravitelju.", "app.containers.Admin.inspirationHub.Proposals": "Prijedlozi", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Poredaj po", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Sudionici (prvo najniži)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Sudionici (prvo najviši)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "<PERSON><PERSON> (prvo najstariji)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "<PERSON><PERSON> (prvo najnoviji)", "app.containers.Admin.inspirationHub.Survey": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Odabrani popis najboljih projekata diljem svijeta.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Razgovarajte s kolegama praktičarima i učite od njih.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filtrirajte prema metodi, veli<PERSON>ini grada i državi.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Omogući Inspiration Hub", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Ova značajka nije uključena u vaš trenutni plan. Razgovarajte sa svojim upraviteljem uspjeha vlade ili administratorom da ga otključate.", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "https://support.govocal.com/en/articles/11093736-using-the-inspiration-hub", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "Saznajte više", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "Inspiration Hub povezuje vas s odabranim feedom izuzetnih projekata sudjelovanja na Go Vocal platformama diljem svijeta. Saznajte kako drugi gradovi vode uspješne projekte i razgovarajte s drugim stručnjacima.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Pridružite se mreži pionira praktičara demokracije", "app.containers.Admin.inspirationHub.Volunteering": "Volontiranje", "app.containers.Admin.inspirationHub.Voting": "Glasovanje", "app.containers.Admin.inspirationHub.commonGround": "Zajednički temelj", "app.containers.Admin.inspirationHub.filters": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.resetFilters": "<PERSON><PERSON><PERSON><PERSON>e", "app.containers.Admin.inspirationHub.seemsLike": "<PERSON><PERSON> se da više nema projekata. Pokušajte promijeniti {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "Uredi polja kampanje", "app.containers.Admin.messaging.automated.variablesToolTip": "U svojoj poruci možete koristiti sljedeće varijable:", "app.containers.Admin.messaging.helmetTitle": "<PERSON><PERSON><PERSON> poruka", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "<PERSON><PERSON><PERSON> widget prikazuje projekte svakog korisnika <b>na temelju njihovih preferencija praćenja</b>. To uključuje projekte koje prate, kao i projekte u kojima prate unose te projekte koji se odnose na teme ili područja koja ih zanimaju.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "<PERSON><PERSON><PERSON> widget bit će prikazan korisniku samo ako postoje projekti u kojima mogu sudjelovati. Ako vidite ovu poruku, to znači da vi (admin) trenutno ne možete sudjelovati ni u jednom projektu. Ova poruka neće biti vidljiva na stvarnoj početnoj stranici.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Otvoren za sudjelovanje", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "<PERSON><PERSON><PERSON> widget će prikazati projekte u kojima korisnik trenutno može <b>poduzeti akciju da sudjeluje</b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Titula", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "Za vas", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "<PERSON><PERSON><PERSON> widget bit će prikazan korisniku samo ako postoje projekti relevantni za njih na temelju njihovih preferencija praćenja. <PERSON><PERSON> vidite ovu poruku, to znači da vi (admin) trenutno ništa ne pratite. Ova poruka neće biti vidljiva na stvarnoj početnoj stranici.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "Praćene stavke", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "<PERSON><PERSON><PERSON><PERSON> prema", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Gotovo", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Završeno i arhivirano", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "<PERSON><PERSON> podata<PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "<PERSON><PERSON><PERSON> widget prikazuje <b>projekte koji su završeni i/ili arhivirani.</b>. U \"Gotovo\" spadaju i projekti koji su u zadnjoj fazi, a gdje je zadnja faza izvještaj.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Gotovi projekti", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "<PERSON><PERSON><PERSON>, jes<PERSON>...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "Navedite naziv za sve jezike", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "Projekt ne može biti prazan", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Ime", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Projekt", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Rezultirajući URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Dodajte projekt", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "Navigacijska traka prikazuje samo projekte kojima korisnici imaju pristup.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "<PERSON><PERSON><PERSON> widget bit će vidljiv samo na početnoj stranici kada Monitor zajednice prihvaća odgovore.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Monitor zajednice", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Opis", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "<PERSON><PERSON>b", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Titula", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Važno:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Primjer pitanja iz ankete o raspoloženju", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "<PERSON><PERSON> da<PERSON> završ<PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "Pritisnite Escape za preskakanje vrtuljka", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projekti i mape (nasljeđe)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Naziv projekta", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} trenutno se radi", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Tekst gumba", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Sudjelujte sada!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Opis", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "mapa", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Odaberite projekt ili mapu", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Odaberite projekt ili mapu", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Prikaži avatare", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Reflektor", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Titula", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "<PERSON><PERSON><PERSON><PERSON> za {days} dana", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "<PERSON><PERSON><PERSON><PERSON> za {weeks} tjedana", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "prije {days} dana", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "prije {weeks} tjedana", "app.containers.Admin.project.Campaigns.campaignFrom": "Iz:", "app.containers.Admin.project.Campaigns.campaignTo": "Do:", "app.containers.Admin.project.Campaigns.customEmails": "Prilagođene e-pošte", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Pošaljite prilagođene e-poruke i provjerite statistiku.", "app.containers.Admin.project.Campaigns.noAccess": "<PERSON><PERSON> na<PERSON> je, ali <PERSON> se da nemate pristup od<PERSON>l<PERSON>u s e-poštom", "app.containers.Admin.project.emails.addCampaign": "Stvorite e-poštu", "app.containers.Admin.project.emails.addCampaignTitle": "Nova kampanja", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Svi {participants} i pratitelji sa projekta", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Ovo uključuje registrirane korisnike koji su izvršili bilo koju radnju u projektu. Neregistrirani ili anonimizirani korisnici nisu uključeni.", "app.containers.Admin.project.emails.dateSent": "<PERSON><PERSON> slanja", "app.containers.Admin.project.emails.deleteButtonLabel": "Izbrisati", "app.containers.Admin.project.emails.draft": "Nacrt", "app.containers.Admin.project.emails.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.editCampaignTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Lako se povežite sa svojim sudionicima slanjem e-pošte. Odaberite koga ćete kontaktirati i pratite svoj angažman.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Pošaljite svoj prvi email", "app.containers.Admin.project.emails.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.fieldBody": "Poruka e-pošte", "app.containers.Admin.project.emails.fieldBodyError": "Unesite poruku e-pošte za sve jezike", "app.containers.Admin.project.emails.fieldReplyTo": "Odgovori bi trebali ići na", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Navedite adresu e-pošte u ispravnom formatu, <NAME_EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "Navedite adresu e-p<PERSON>šte", "app.containers.Admin.project.emails.fieldReplyToTooltip": "Odaberite koja adresa e-pošte treba primati izravne odgovore od korisnika na vašu e-poštu.", "app.containers.Admin.project.emails.fieldSender": "Iz", "app.containers.Admin.project.emails.fieldSenderError": "Navedite pošiljatelja e-pošte", "app.containers.Admin.project.emails.fieldSenderTooltip": "Odaberite koga će korisnici vidjeti kao pošiljatelja e-pošte.", "app.containers.Admin.project.emails.fieldSubject": "Predmet e-pošte", "app.containers.Admin.project.emails.fieldSubjectError": "Navedite predmet e-pošte za sve jezike", "app.containers.Admin.project.emails.fieldSubjectTooltip": "To će biti prikazano u retku predmeta e-pošte iu pregledu korisnikove pristigle pošte. Neka bude jasno i privlačno.", "app.containers.Admin.project.emails.fieldTo": "Do", "app.containers.Admin.project.emails.formSave": "<PERSON><PERSON><PERSON><PERSON> kao skicu", "app.containers.Admin.project.emails.from": "Iz:", "app.containers.Admin.project.emails.helmetDescription": "Pošaljite ručno e-poštu sudionicima projekta", "app.containers.Admin.project.emails.infoboxAdminText": "Na kartici Project Messaging možete poslati e-poštu samo svim sudionicima projekta. Za slanje e-pošte drugim sudionicima ili podskupovima korisnika idite na karticu {link} .", "app.containers.Admin.project.emails.infoboxLinkText": "Razmjena poruka na platformi", "app.containers.Admin.project.emails.infoboxModeratorText": "Na kartici Project Messaging možete poslati e-poštu samo svim sudionicima projekta. Administratori mogu slati e-poštu drugim sudionicima ili podskupovima korisnika putem kartice Platform Messaging.", "app.containers.Admin.project.emails.message": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.nameVariablesInfo2": "Možete se obratiti izravno građanima koristeći varijable {firstName} {lastName}. Npr. \"Dragi {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "sudionik<PERSON>", "app.containers.Admin.project.emails.previewSentConfirmation": "E-pošta za pregled poslana je na vašu adresu e-pošte", "app.containers.Admin.project.emails.previewTitle": "Pregled", "app.containers.Admin.project.emails.projectParticipants": "Sudionici projekta", "app.containers.Admin.project.emails.recipients": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.send": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sendTestEmailButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> pregled", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Pošaljite ovu skicu e-pošte na e-mail adresu kojom ste prijavljeni da provjerite kako izgleda u 'stvarnom životu'.", "app.containers.Admin.project.emails.senderRecipients": "Pošiljatelj i primatelji", "app.containers.Admin.project.emails.sending": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sent": "Poslano", "app.containers.Admin.project.emails.sentToUsers": "Ovo su e-poruke poslane korisnicima", "app.containers.Admin.project.emails.status": "Status", "app.containers.Admin.project.emails.subject": "Predmet:", "app.containers.Admin.project.emails.to": "Do:", "app.containers.Admin.project.messaging.helmetTitle": "<PERSON><PERSON><PERSON> poruka", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "Ova je slika dio kartice mape; kartice koja pruža sažetak mape i prikazuje se na početnoj stranici, na primjer. Za više informacija o preporučenim razlučivostima slika, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Ova se slika prikazuje je na vrhu stranice mape. Za više informacija o preporučenim razlučivostima slika, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "posjetite naš centar za podršku", "app.containers.Admin.projects.all.askPersonalData3": "Dodajte polja za ime i e-poštu", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "Omogući prikaz kalendara", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "Ova značajka nije uključena u vaš trenutni plan. Obratite se svom voditelju za uspjeh u vladi ili administratoru da biste je otključali.", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "Saznajte više", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "Dobijte vizualni pregled vremenskih okvira vaših projekata u našem kalendarskom prikazu. Brzo prepoznajte koji projekti i faze uskoro počinju ili završavaju i zahtijevaju djelovanje.", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "Razumjeti što se događa i kada", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Sva pitanja su prikazana u PDF-u<PERSON>, sljedeće trenutno nije podržano za uvoz putem FormSynca: slike, oznake i prijenos datoteka.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Sva pitanja su prikazana u PDF-<PERSON><PERSON>, s<PERSON>jedeća pitanja trenutno nisu podržana za uvoz putem FormSynca: pitanja o mapiranju (ispuštanje pribadače, crtanje rute i crtanje područja), pitanja o rangiranju, matrična pitanja i pitanja o prijenosu datoteka.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "<PERSON><PERSON>", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Početak obrasca", "app.containers.Admin.projects.all.components.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.components.draft": "Nacrt", "app.containers.Admin.projects.all.components.manageButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.copyProjectButton": "<PERSON><PERSON><PERSON> proje<PERSON>", "app.containers.Admin.projects.all.copyProjectError": "Došlo je do pogreške prilikom kopiranja ovog projekta, pokušajte ponovno kasnije.", "app.containers.Admin.projects.all.customiseEnd": "Prilagodite kraj o<PERSON>.", "app.containers.Admin.projects.all.customiseStart": "Prilagodite početak obrasca.", "app.containers.Admin.projects.all.deleteFolderButton1": "Izbriši mapu", "app.containers.Admin.projects.all.deleteFolderConfirm": "Jeste li sigurni da želite obrisati ovu mapu? Svi projekti u okviru njega će također biti obrisani. Ova radnja se ne može poništiti.", "app.containers.Admin.projects.all.deleteFolderError": "Do<PERSON><PERSON> je do problema prilikom uklanjanja ove mape. Molimo vas pokušajte kasnije.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Izbriši projekt", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Jeste li sigurni da želite izbrisati ovaj projekt? Ovu radnju ne možete poništiti.", "app.containers.Admin.projects.all.deleteProjectError": "<PERSON><PERSON><PERSON> je do pogreške prilikom brisanja projekta, molimo vas da pokušate kasnije.", "app.containers.Admin.projects.all.exportAsPDF1": "Preuzmite PDF obrazac", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Možete kombinirati online i offline odgovore. Za prijenos izvanmrežnih odgovora idite na karticu \"Upravitelj unosa\" ovog projekta i kliknite \"Uvezi\".", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Možete kombinirati online i offline odgovore. Za prijenos izvanmrežnih odgovora idite na karticu \"Anketa\" ovog projekta i kliknite \"Uvezi\".", "app.containers.Admin.projects.all.logicNotInPDF": "Logika ankete neće se odraziti na preuzeti PDF. Papirnati ispitanici će vidjeti sva anketna pitanja.", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "Pretraži mape", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "Sve mape su učitane", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Mapa", "app.containers.Admin.projects.all.new.Folders.Table.managers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# projekt} other {# projekti}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Datum početka projekta", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Vidljivost", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "Skriven", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "Javnost", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Mape", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filt<PERSON>raj prema metodi sudjelovanja u trenutnoj fazi", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "Idejacija", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Informacija", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Metoda sudjelovanja", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Bilješke dokumenta", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Zajednički temelj", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Prijedlozi", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Volontiranje", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Glasanje", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "Prikupljanje podataka", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informiranje", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Stanje sudjelovan<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Prošlost", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "Čeka odobrenje", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "Pretraživanje projekata", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Abecedno (az)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Abecedno (za)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Faza koja uskoro počinje ili završava", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc": "<PERSON><PERSON><PERSON> (novo-staro)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc": "<PERSON><PERSON><PERSON> (staro-novo)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Grupe", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Vidljivost", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "Javnost", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "Jasa<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "Nema više filtera za dodavanje", "app.containers.Admin.projects.all.new.Projects.Table.admins": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Svi projekti su učitani", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "Bilo tko", "app.containers.Admin.projects.all.new.Projects.Table.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "<PERSON><PERSON><PERSON><PERSON> faza", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "<PERSON><PERSON><PERSON> je {days}d", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d za po<PERSON><PERSON>k", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Vidljivost:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Nacrt", "app.containers.Admin.projects.all.new.Projects.Table.end": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.ended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "<PERSON><PERSON><PERSON><PERSON><PERSON> danas", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Grupe", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "Skriven", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "Učitavanje više…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}mj. pre<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}mj. za po<PERSON><PERSON>k", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Sljedeća faza:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Faza", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.project": "Projekt", "app.containers.Admin.projects.all.new.Projects.Table.public": "Javnost", "app.containers.Admin.projects.all.new.Projects.Table.published": "Objavljeno", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Pomaknite se prema dolje za učitavanje više", "app.containers.Admin.projects.all.new.Projects.Table.start": "Start", "app.containers.Admin.projects.all.new.Projects.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Status:", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "Ovaj stupac koristi predmemorirane podatke o sudionicima. Da biste vidjeli najnovije brojeve, provjerite karticu \"Sudionici\" u projektu.", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Vidljivost", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Vidljivost:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} grupe", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} menad<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "<PERSON><PERSON><PERSON> je {years}y", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y za početak", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Trenutna faza: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "<PERSON><PERSON><PERSON> je {days} dana", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Mapa: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "Nema trenutne faze", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "<PERSON><PERSON> da<PERSON> završ<PERSON>", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "<PERSON><PERSON> faza", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Faza {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Faze:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Projekt", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Datum po<PERSON>ka: {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "Organiziraj projekte", "app.containers.Admin.projects.all.new.calendar": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.folders": "Mape", "app.containers.Admin.projects.all.new.projects": "Projekti", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "Učitavanje vremenske crte nije uspjelo.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Projekt nema datum završetka", "app.containers.Admin.projects.all.new.timeline.project": "Projekt", "app.containers.Admin.projects.all.notes": "Bilješke", "app.containers.Admin.projects.all.personalDataExplanation5": "Ova će opcija dodati polja za ime, prezime i e-poštu u izvezeni PDF. Nakon prijenosa papirnatog obrasca, te ćemo podatke koristiti za automatsko generiranje računa za ispitanika izvan mreže u anketi.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "AI Sažetak", "app.containers.Admin.projects.project.analysis.Comments.comments": "Komentari", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "Sažetak komentara dostupan je kada ima 5 ili više komentara.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Sažmite komentare", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} =1 {1 novi komentar} other {# nova komentara}}", "app.containers.Admin.projects.project.analysis.aiSummary": "AI sažetak", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "Ovo je sadržaj generiran umjetnom inteligencijom. Možda nije 100% točno. Pregledajte i usporedite sa stvarnim unosima radi točnosti. Imajte na umu da će se točnost vjerojatno poboljšati ako se smanji broj odabranih ulaza.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Obavijesti e-poštom šalju se samo sudionicima", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "Skriven", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "<PERSON><PERSON> indek<PERSON> od strane tražilica", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Nije vidljivo na početnoj stranici ili u widgetima", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Dostupno samo putem izravnog URL-a", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "Javnost", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "Odaberite koliko je ovaj projekt vidljiv.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "Ovaj projekt je vidljiv svima koji imaju pristup i prikazivat će se na početnoj stranici i u widgetima.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Ovaj projekt bit će skriven od šire javnosti i bit će vidljiv samo onima koji imaju poveznicu.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Tko može pronaći ovaj projekt?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Otvorena AI analiza", "app.containers.Admin.projects.project.ideas.analysisText2": "Istražite sažetke koje pokreće AI i pregledajte pojedinačne podneske.", "app.containers.Admin.projects.project.ideas.importInputs": "Uvoz", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "Nakon što izradite izv<PERSON>, možete ga podijeliti s javnoš<PERSON>u nakon što faza započne.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Napravite složeniju stranicu za razmjenu informacija", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Napravite izvješće za:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "Napravi izvješće", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Napravite izvješće za prošlu fazu ili počnite od nule.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "<PERSON><PERSON> izv<PERSON>š<PERSON>e nije javno. Da biste ga učinili javnim, omogućite prekidač \"Vidljivo\".", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "<PERSON><PERSON> faza je zap<PERSON>, ali iz<PERSON><PERSON><PERSON> još nije javno. Da biste ga učinili javnim, omogućite prekidač \"Vidljivo\".", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Počnite s predloškom faze", "app.containers.Admin.projects.project.information.ReportTab.report": "izvješće", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Podijelite rezultate prošlog istraživanja ili faze razmišljanja", "app.containers.Admin.projects.project.information.ReportTab.visible": "Vidljivo", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "<PERSON>vo će izvješće biti javno čim faza započne. Da ne bude javno, onemogućite prekidač \"Vidljivo\".", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "<PERSON><PERSON> je trenutno javno. <PERSON> ne bude javno, onemoguć<PERSON> prekidač \"Vidljivo\".", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Jeste li sigurni da želite izbrisati ovo izvješće? Ova se radnja ne može poništiti.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Dodaj u fazu", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "Morate pristati na ovo prije nego što možete nastaviti", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "Obrazac možete preuzeti ovdje.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "Učitani obrazac je kreiran sa odjeljkom \"Osobni podaci\".", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "Ovime pristajem na obradu ove datoteke pomoću Google Cloud Form Parsera", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Uvezi Excel datoteku", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Učitajte datoteku za nastavak", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "Predložak možete preuzeti ovdje.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Učitaj", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Prenesite dovrš<PERSON>u <b>Excel datoteku</b> (.xlsx). <PERSON>ra koristiti predložak predviđen za ovaj projekt. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Prenesite <b>PDF datote<PERSON> skeniranih obrazaca</b>. <PERSON><PERSON> koristiti obrazac ispisan iz ove faze. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Koristite ovu e-poštu za novog korisnika", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Unesite važeću e-poštu za stvaranje novog računa", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Za autora će se stvoriti novi račun s ovim detaljima. Ovaj unos će mu biti dodan.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "Ime", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Prezime", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Unesite adresu e-pošte i/ili ime i prezime kako biste ovaj unos dodijelili autoru. Ili poništite okvir za pristanak.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Već postoji račun povezan s ovom e-poštom. Ovaj unos će mu biti dodan.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Pristanak korisnika (kreiranje korisničkog računa)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Odobrite sve unose", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Autor:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "Email:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Došlo je do pogrešaka tijekom uvoza i neki unosi nisu uvezeni. Ispravite pogreške i ponovno uvezite unose koji nedostaju.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Nevažeći podaci obrasca. Provjerite gornji obrazac za pogreške.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Uvezi Excel datoteku (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Uvoz", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "<PERSON><PERSON>z sken<PERSON>h o<PERSON> (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "U<PERSON>z skeniranih obrazaca", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Uvoz. Ovaj proces može potrajati nekoliko minuta.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "<PERSON><PERSON><PERSON> unos uvezen je anonimno.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} unosi su uvezeni i zahtijevaju odobrenje.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} unosi nisu mogli biti odobreni. Provjerite svaki unos ima li problema s provjerom valjanosti i potvrdite pojedinačno.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Lokalitet:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "<PERSON><PERSON> ništa za recenziju. Kliknite \"{importFile}\" za uvoz PDF datoteke koja sadrži skenirane obrasce za unos ili Excel datoteke koja sadrži unose.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "<PERSON><PERSON> ništa za recenziju. Kliknite \"{importFile}\" za uvoz Excel datoteke koja sadrži unose.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "Stranica", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "<PERSON><PERSON> mogu<PERSON>e prikazati uvezenu datoteku. Pre<PERSON> uvez<PERSON>h datoteka dostupan je samo za uvoze PDF-a.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Faza:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "Odabrana faza ne može sadržavati ulaze. Odaberite drugu.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Ovaj projekt ne sadrži nijednu fazu koja može sadržavati ideje.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "Odaberite u koju fazu želite dodati ove ulaze.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Uvoznik unosa", "app.containers.Admin.projects.project.participation.comments": "Komentari", "app.containers.Admin.projects.project.participation.inputs": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.participantsTimeline": "Vremenska linija sudionika", "app.containers.Admin.projects.project.participation.reactions": "Reak<PERSON><PERSON>", "app.containers.Admin.projects.project.participation.selectPeriod": "Odaberite razdoblje", "app.containers.Admin.projects.project.participation.usersByAge": "<PERSON><PERSON><PERSON><PERSON> prema dobi", "app.containers.Admin.projects.project.participation.usersByGender": "<PERSON><PERSON><PERSON><PERSON> prema spolu", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "Potreban", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "Mogućnost dodavanja ili uređivanja korisničkih polja na razini faze nije uključena u vašu trenutnu licencu. Obratite se svom GovSuccess Manageru da saznate više o tome.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} opcije", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "Status polja", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "<PERSON>va će pitanja biti dodana kao zadnja stranica anketnog obrasca, jer '<PERSON><PERSON>ži polja u anketi?' je odabran u postavkama faze.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "Neće se postavljati dodatna pitanja.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Neobavezno", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Neobavezno - uvijek omogućeno jer je navedeno u grupi", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "Ukloni polje", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Obavezno - uvijek omogućeno jer je navedeno u grupi", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Provjeri autentičnost s {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Ispunite dodatna pitanja u nastavku", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Potvrdite svoju e-poštu", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Podaci vraćeni od metode provjere:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Unesite ime, prezime, email i lozinku", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "Unesite svoju e-poštu", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "Koliko bi se nedavno trebali verificirati korisnici?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Provjera identiteta s {verificationMethod} (na temelju korisničke grupe)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "Za sudjelovanje nisu potrebne nikakve radnje", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Koristite pametne grupe za ograničavanje sudjelovanja na temelju gore navedenih provjerenih podataka", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Korisnici moraju biti verificirani u zadnjih 30 minuta.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Korisnici moraju biti verificirani u zadnjih {days} dana.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "U zadnjih 30 dana", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "U zadnjih 30 minuta", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "U zadnjih 7 dana", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "Jednom je dovoljno", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Potvrđena polja:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} potvrda", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Izrada računa", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Sudionici trebaju stvoriti puni račun sa svojim imenom, potvrđenom e-poštom i lozinkom.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Sudionici moraju stvoriti puni račun sa svojim imenom, e-poštom i lozinkom.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Autentifikacija", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Potvrda e-pošte", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Sudionici moraju potvrditi svoju e-poštu jednokratnim kodom.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Napredno otkrivanje spama", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "Ova značajka pomaže spriječiti dvostruka podnošenja ankete od odjavljenih korisnika analizom IP adresa i podataka uređaja. Iako nije tako precizan kao zahtijevanje prijave, može pomoći u smanjenju broja dvostrukih odgovora.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Napomena: <PERSON> zajedničkim mrež<PERSON> (kao što su uredi ili javni Wi-Fi), postoji mala vjerojatnost da različiti korisnici mogu biti označeni kao duplikati.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Omogući napredno otkrivanje neželjene pošte", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Dodatna pitanja postavljena sudionicima", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Svatko može sudjelovati bez registracije ili prijave.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Ponovno postavite dodatna pitanja i grupe", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Ograniči sudjelovanje na grupe korisnika", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO verifikacija", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Sudionici trebaju potvrditi svoju identifikaciju s {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Otvorena AI analiza", "app.containers.Admin.projects.project.survey.allFiles": "<PERSON><PERSON> dato<PERSON>ke", "app.containers.Admin.projects.project.survey.allResponses": "Svi odgovori", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Točnost: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Došlo je do pogreške prilikom generiranja AI sažetka. Pokušajte ga regenerirati u nastavku.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Otvorena AI analiza", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "Sakrij sažetke za ovo pitanje", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "o<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Otvorene radnje analize", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} novih odgovora", "app.containers.Admin.projects.project.survey.analysis.regenerate": "Regenerirati", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Prikaži AI uvide", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "Možete rezimirati najviše 30 unosa odjednom na vašem trenutnom planu. Razgovarajte sa svojim GovSuccess Managerom ili administratorom da otključate više.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Odaberite povezana pitanja za analizu", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "Želite li uključiti neka druga povezana pitanja u svoju analizu {question}?", "app.containers.Admin.projects.project.survey.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "Slažem se s korištenjem OpenAI-ja kao procesora podataka za ovaj projekt", "app.containers.Admin.projects.project.survey.consentModalText1": "Nastavkom pristajete na korištenje OpenAI-ja kao obrađivača podataka za ovaj projekt.", "app.containers.Admin.projects.project.survey.consentModalText2": "OpenAI API-ji pokreću automatizirane tekstualne sažetke i dijelove iskustva automatiziranog označavanja.", "app.containers.Admin.projects.project.survey.consentModalText3": "OpenAI API-<PERSON><PERSON> samo ono što su korisnici napisali u svojim anketama, ideje i komentare, nikada informacije s njihovih profila.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI neće koristiti ove podatke za daljnju obuku svojih modela. Više informacija o tome kako OpenAI postupa s privatnošću podataka možete pronaći {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "ovdje", "app.containers.Admin.projects.project.survey.consentModalTitle": "Prije nego nastavite", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "Ne možete unijeti analizu prije nego što uredite obrazac", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Izbrisati", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Jeste li sigurni da želite izbrisati ovu analizu? Ova se radnja ne može poništiti.", "app.containers.Admin.projects.project.survey.explore": "Istražiti", "app.containers.Admin.projects.project.survey.followUpResponses": "Pratite odgovore", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> prosjek", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Izvezi kao GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Izvezite odgovore na ovo pitanje kao GeoJSON datoteku. Za svaku značajku GeoJSON, svi povezani anketni odgovori ispitanika bit će navedeni u objektu 'svojstva' te značajke.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} ispitanici} one {{respondentCount} ispitanik} other {{respondentCount} ispitanici}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "Pogledajte de<PERSON>je", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} izbori} one {{numberChoices} izbor} other {{numberChoices} izbori}}", "app.containers.Admin.projects.project.survey.heatMap": "Toplinska karta", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Saznajte više o toplinskim kartama generiranim pomoću Esri Smart Mappinga.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "Toplinska karta se generira pomoću Esri Smart Mappinga. Toplinske karte korisne su kada postoji velika količina podatkovnih točaka. Za manji broj točaka možda je bolje izravno pogledati samo točke lokacije. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Prikaz toplinske karte", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Skriveno logikom", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "Kada korisnik odabere ovaj odgovor, logika preskače sve stranice do stranice {pageNumber} ({numQuestionsSkipped} pitanja preskočena). Kliknite za skrivanje ili prikaz preskočenih stranica i pitanja.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "Kada korisnik odabere ovaj odgovor, logika preskače na kraj ankete ({numQuestionsSkipped} pitanja preskočeno). Kliknite za skrivanje ili prikaz preskočenih stranica i pitanja.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "Logika na ovoj stranici preskače sve stranice do stranice {pageNumber} ({numQuestionsSkipped} pitanja preskočena). Kliknite za skrivanje ili prikaz preskočenih stranica i pitanja.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Logika na ovoj stranici preskače na kraj ankete ({numQuestionsSkipped} pitanja preskočena). Kliknite za skrivanje ili prikaz preskočenih stranica i pitanja.", "app.containers.Admin.projects.project.survey.newAnalysis": "Nova analiza", "app.containers.Admin.projects.project.survey.nextInsight": "Sljedeć<PERSON> uvid", "app.containers.Admin.projects.project.survey.openAnalysis": "Otvorena AI analiza", "app.containers.Admin.projects.project.survey.otherResponses": "Ostali odgovori", "app.containers.Admin.projects.project.survey.page": "Stranica", "app.containers.Admin.projects.project.survey.previousInsight": "<PERSON><PERSON><PERSON><PERSON> uvid", "app.containers.Admin.projects.project.survey.responses": "Odgovori", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "Broj odgovora za ovu stranicu manji je od ukupnog broja odgovora na anketu jer neki ispitanici neće vidjeti ovu stranicu zbog logike u anketi.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "Broj odgovora na ovo pitanje manji je od ukupnog broja odgovora u anketi jer neki ispitanici neće vidjeti ovo pitanje zbog logike u anketi.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "S<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Odmah sažmite sve svoje odgovore.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Razgovarajte sa svojim podacima prirodnim jezikom.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Dobijte reference na pojedinačne odgovore iz sažetaka koje je generirala umjetna inteligencija.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Provjerite naš {link} za potpuni pregled.", "app.containers.Admin.projects.project.survey.upsell.button": "Otključaj AI analizu", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "članak podrške", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Brže analizirajte podatke uz AI", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "Ova značajka nije uključena u vaš trenutni plan. Razgovarajte sa svojim upraviteljem uspjeha vlade ili administratorom da ga otključate.", "app.containers.Admin.projects.project.survey.viewAnalysis": "Pogled", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Istražite sažetke koje pokreće AI i pregledajte pojedinačne podneske.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Odaberite razdoblje", "app.containers.Admin.projects.project.traffic.trafficSources": "Izvori prometa", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Promijenili smo način prikupljanja i prikaza podataka o posjetiteljima. Kao rezultat toga, podaci o posjetiteljima su točniji i dostupno je više vrsta podataka, a istovremeno su i dalje u skladu s GDPR-om. Ove nove podatke počeli smo prikupljati tek u studenom 2024., tako da prije toga nisu bili dostupni nikakvi podaci.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Vremenska traka posjetitelja", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Izvješće o fazi", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Dodajte tekst o fazi", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Ovo je neki tekst. Možete ga uređivati i formatirati pomoću uređivača u ploči s desne strane.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Sudionici", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Rezultati projekta", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Sažetak izvješća", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Dodajte cilj projekta, upotrijebljene načine sudjelovanja i ishod", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Posjetitelji", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Ovo izv<PERSON>šće sadrži nespremljene promjene. Molimo spremite prije ispisa.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Na<PERSON><PERSON> je već zauzet", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "U usporedbi s prethodnih {days} dana", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "<PERSON><PERSON><PERSON><PERSON> statistik<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Stopa sudjelovanja", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Prikaži usporedbu s <PERSON><PERSON><PERSON> razdobljem", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "Najprije morate odabrati razdoblje.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "Komentari", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Sudjelovanje", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Prikaži komentare", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Prikaž<PERSON> g<PERSON>ove", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Demografija", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Raspon datuma registracije", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Polje za registraciju", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Nepoznato", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Korisnici: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "Istegnite se", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Aktivan", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "Gotovo", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "Otvorenog kraja", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projekti", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Status objave", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Objavljeno", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "Podaci za ovaj widget nedostaju. Ponovno ga konfigurirajte ili izbrišite da biste mogli spremiti izvješće.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Community Monitor Health Score", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "Četvrt", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "P1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "U ovom projektu nisu pronađene odgovarajuće faze", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "Nije odabrana faza. Prvo odaberite fazu.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "<PERSON><PERSON> projekta", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Nije odabran nijedan projekt. Prvo odaberite projekt.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "Ne možete duplicirati ovo izvješće jer sadrži podatke kojima nemate pristup.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "Ne možete uređivati ovo izvješće jer sadrži podatke kojima nemate pristup.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Jeste li sigurni da želite izbrisati \"{reportName}\"? Ova se radnja ne može poništiti.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Jeste li sigurni da želite izbrisati ovo izvješće? Ova se radnja ne može poništiti.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Izbriši", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Duplikat", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {days, plural, no {prije # dana} one {# dana} other {# dana}}", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Došlo je do pogreške prilikom pokušaja izrade ovog izvješća. Pokušajte ponovno kasnije.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Započni s praznom stranicom", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Započnite s predloškom Community Monitor", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Naslov izvješća", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "Napravi izvješće", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Prilagodite svoje izvješće i podijelite ga s internim dionicima ili zajednicom kao PDF datoteku.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Izradite izvješće", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Napravite svoje prvo izvješće", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "<PERSON><PERSON> odabran nijedan projekt", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Započnite s predloškom platforme", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Ispiši u PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Započni s predloškom projekta", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Četvrtina {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Predložak izvješća", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Izvješće s ovim naslovom već postoji. Odaberite drugi naslov.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Odaberite četvrtinu", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Odaberite godinu", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "<PERSON><PERSON><PERSON><PERSON> kao <PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Za dijeljenje sa svima ispišite izvješće kao PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Podijeli kao web poveznicu", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "Ovoj web poveznici mogu pristupiti samo administratori.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.contactToAccess": "Izrada prilagođenog izvješća dio je premium licence. Obratite se svom GovSuccess upravitelju kako biste saznali više o tome.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Sva izvješća", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Izvješća monitora zajednice", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Ovo su izvješća povezana s Monitorom zajednice. Izvješća se automatski generiraju svakog kvartala.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Izradite izvješće", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Prilagodite svoj izvješće i podijelite ga s internim dionicima ili zajednicom putem web poveznice.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Ovdje će se pojaviti vaša izvješća.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Izvješća pretraživanja", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Izvješća o napretku", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Ovo su izvješća koja je izradio vaš <PERSON> upravitelj uspjeha", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.deprecated": "ZASTARJELO", "app.containers.Admin.reporting.helmetDescription": "Stranica s administratorskim izvještavanjem", "app.containers.Admin.reporting.helmetTitle": "Izvještavanje", "app.containers.Admin.reporting.printPrepare": "Priprema za tisak...", "app.containers.Admin.reporting.reportBuilder": "Alat za izgradnju izvješća", "app.containers.Admin.reporting.reportHeader": "Zaglavlje izvješća", "app.containers.Admin.reporting.warningBanner3": "Grafikoni i brojevi u ovom izvješću automatski se ažuriraju samo na ovoj stranici. Spremite izvješće kako biste ga ažurirali na drugim stranicama.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Zajednički temelj", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Ideacija", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Informacija", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Korištene metode", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "Pregled", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "Prethodnih {days} dana: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Prijedlozi", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "<PERSON><PERSON><PERSON> pregled", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Volontiranje", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Glasovanje", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Grafikon", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "Stol", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "Pogled", "app.containers.Admin.surveyFormTab.downloads": "Preuziman<PERSON>", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "<PERSON><PERSON><PERSON><PERSON><PERSON> jo<PERSON> j<PERSON>", "app.containers.Admin.surveyFormTab.editSurveyForm": "Uredi obrazac za anketu", "app.containers.Admin.surveyFormTab.inputFormDescription": "Navedite koje informacije treba navesti, dodajte kratke opise ili upute za usmjeravanje odgovora sudionika i navedite je li svako polje neobavezno ili obavezno.", "app.containers.Admin.surveyFormTab.surveyForm": "Obrazac za anketu", "app.containers.Admin.tools.apiTokens.createTokenButton": "Stvorite novi token", "app.containers.Admin.tools.apiTokens.createTokenCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "<PERSON>a<PERSON> token je stvoren. <PERSON><PERSON>raj<PERSON> {secret} ispod i pohranite je na sigurno.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Izradite novi token za korištenje s našim javnim API-jem.", "app.containers.Admin.tools.apiTokens.createTokenError": "Navedite naziv za svoj token", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "St<PERSON>i token", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>V<PERSON>ž<PERSON>!</b> Ovo možete kopirati samo {secret} jednom. Ako zatvorite ovaj prozor, više ga nećete moći vidjeti.", "app.containers.Admin.tools.apiTokens.createTokenName": "Ime", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Dajte naziv svom tokenu", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "<PERSON><PERSON><PERSON> <PERSON> je stvoren", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Zatvoriti", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "<PERSON><PERSON><PERSON> {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "<PERSON><PERSON><PERSON>!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Stvorite novi token", "app.containers.Admin.tools.apiTokens.createdAt": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.delete": "Izbriši token", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Jeste li sigurni da želite izbrisati ovaj token?", "app.containers.Admin.tools.apiTokens.description": "Upravljajte svojim API tokenima za naš javni API. Za više informacija pogledajte naš {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Zadnje k<PERSON>š<PERSON>o", "app.containers.Admin.tools.apiTokens.link": "API dokumentacija", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Ime", "app.containers.Admin.tools.apiTokens.noTokens": "<PERSON><PERSON> nemate nijedan token.", "app.containers.Admin.tools.apiTokens.title": "Javni API tokeni", "app.containers.Admin.tools.esriDisabled": "Esri integracija je dodatna značajka. Kontaktirajte svog GovSuccess Managera ako želite više informacija o ovome.", "app.containers.Admin.tools.esriIntegration2": "Esri integracija", "app.containers.Admin.tools.esriIntegrationButton": "Omogućite Esri", "app.containers.Admin.tools.esriIntegrationDescription3": "Povežite svoj Esri račun i uvezite podatke iz ArcGIS Online izravno u svoje projekte mapiranja.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri logo", "app.containers.Admin.tools.esriKeyInputDescription": "Dodajte svoj Esri API ključ kako biste omogućili uvoz slojeva karte iz ArcGIS Online u kartice karte u projektima.", "app.containers.Admin.tools.esriKeyInputLabel": "Esri API ključ", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Ovdje zalijepite API ključ", "app.containers.Admin.tools.esriMaps": "<PERSON><PERSON><PERSON> karte", "app.containers.Admin.tools.esriSaveButtonError": "Došlo je do pogreške prilikom spremanja vašeg ključa, pokušajte ponovno.", "app.containers.Admin.tools.esriSaveButtonSuccess": "API ključ spremljen", "app.containers.Admin.tools.esriSaveButtonText": "Spremi ključ", "app.containers.Admin.tools.learnMore": "Saznajte više", "app.containers.Admin.tools.managePublicAPIKeys": "Upravljanje API ključevima", "app.containers.Admin.tools.manageWidget": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>", "app.containers.Admin.tools.manageWorkshops": "Vodite radionice", "app.containers.Admin.tools.powerBIAPIImage": "Power BI slika", "app.containers.Admin.tools.powerBIDescription": "Koristite naše plug & play Power BI predloške za pristup podacima Go Vocala u vašem Microsoft Power BI Workspaceu.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI nije dio vaše licence. Kontaktirajte svog GovSuccess Managera ako želite više informacija o ovome.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Preuzmite predloške", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "Ako namjeravate koristiti svoje Go Vocal podatke unutar Power BI protoka podataka, ovaj predložak će vam omogućiti da postavite novi protok podataka koji se povezuje s vašim Go Vocal podacima. Nakon što preuzmete ovaj predložak, prvo morate pronaći i zamijeniti sljedeće nizove ##CLIENT_ID## i ##CLIENT_SECRET## u predlošku svojim javnim API vjerodajnicama prije učitavanja u PowerBI.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Preuzmite predložak protoka podataka", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Predložak tijeka podataka", "app.containers.Admin.tools.powerBITemplates.intro": "Napomena: Da biste koristili bilo koji od ovih Power BI predložaka, prvo morate {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "izradite skup vjerodajnica za naš javni API", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "Ovaj će predložak izraditi Power BI izvješće na temelju vaših Go Vocal podataka. Postavit će sve podatkovne veze s vašom Go Vocal platformom, izraditi podatkovni model i neke zadane nadzorne ploče. Kada otvorite predložak u Power BI-u, od vas će se tražiti da unesete svoje javne API vjerodajnice. Također ćete morati unijeti osnovni URL za svoju platformu, a to je: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Preuzmite obrazac izvješća", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Predložak izvješća", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Dodatne pojedinosti o korištenju vaših Go Vocal podataka u Power BI-ju možete pronaći u našem {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "članak podrške", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Power BI predlošci", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "Upravljajte vjerodajnicama za stvaranje prilagođenih integracija na našem javnom API-ju.", "app.containers.Admin.tools.publicAPIDisabled1": "Javni API nije dio vaše trenutne licence. Kontaktirajte svog GovSuccess Managera ako želite više informacija o ovome.", "app.containers.Admin.tools.publicAPIImage": "Slika javnog API-ja", "app.containers.Admin.tools.publicAPITitle": "Javni API pristup", "app.containers.Admin.tools.toolsLabel": "<PERSON><PERSON>", "app.containers.Admin.tools.widgetDescription": "Možete izraditi widget, prilagoditi ga i dodati na vlastitu web stranicu kako biste privukli ljude na ovu platformu.", "app.containers.Admin.tools.widgetImage": "Slika widgeta", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "Održavajte videosastanke uživo, omogućite simultane grupne rasprave i debate. Prikupite unose, glasajte i postignite konsenzus, ba<PERSON> kao <PERSON> biste to učinili izvan mreže.", "app.containers.Admin.tools.workshopsImage": "Slika radionice", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Online radionice deliberacije", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "ukupno korisnika na platformi", "app.containers.AdminPage.DashboardPage._blank": "nepoznato", "app.containers.AdminPage.DashboardPage.allGroups": "Sve grupe", "app.containers.AdminPage.DashboardPage.allProjects": "Svi projekti", "app.containers.AdminPage.DashboardPage.allTime": "Svo vrijeme", "app.containers.AdminPage.DashboardPage.comments": "Komentari", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Komentari", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Za mjerenje reprezentativnosti korisnika zajednice potreban je skup podataka baze.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Trenutno radimo na nadzornoj ploči {fieldName}. Uskoro će biti dostupna", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# stavka je} other {# stavke su}} sakrivene na ovom grafikonu. Promijenite kako biste {tableViewLink} prikazali sve podatke.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} za registraciju korisnika", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} od <PERSON><PERSON> {total} korisnika ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "<PERSON><PERSON><PERSON><PERSON> jo<PERSON> {numberOfHiddenItems}", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "<PERSON><PERSON>j<PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Pružite skup podataka baze.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Rezultat reprezentativnosti:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Rezultat odražava koliko točno podaci korisnika platforme odražavaju ukupno stanovništvo. Saznajte više o {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Potrebno", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Pošaljite podatke baze", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "prikaz tablice", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Ukupno stanovništvo", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "<PERSON><PERSON><PERSON> do<PERSON> s<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} i stariji", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "<PERSON><PERSON><PERSON> s<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "<PERSON>je uključena dobna skupina(e) od {upperBound} i stariji.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "<PERSON><PERSON><PERSON> sku<PERSON> {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "i stariji", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Primijeni primjer grupiranja", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Izbriši sve", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "Od", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Postavite dobne skupine za poravnanje sa skupom podataka baze podataka.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Ra<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Sp<PERSON>i", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "Prima", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "<PERSON><PERSON><PERSON> do<PERSON> sku<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Ova stavka se neće izračunavati.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "Prika<PERSON><PERSON> manje", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Prikaž<PERSON> još {numberOfHiddenItems}...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Osnovni mjesec (opcija)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "<PERSON><PERSON><PERSON> (godina rođ<PERSON>)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.default": "Zadano", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Popunite sve omogućene opcije ili onemogućite opcije koje želite izostaviti iz grafikona. Morate popuniti barem jednu opciju.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Nepotpuno", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "<PERSON><PERSON><PERSON> broj stanara", "app.containers.AdminPage.DashboardPage.components.Field.options": "Opcije", "app.containers.AdminPage.DashboardPage.components.Field.save": "Sp<PERSON>i", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Spremljeno", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Molimo da prvo {setAgeGroupsLink} kako biste počeli unositi podatke u bazu.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "postavite dobne skupine", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Pros. vri<PERSON>me od<PERSON>: {days} dana", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Prosječan broj dana za odgovor", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "<PERSON> povratne informacije", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Status unosa", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Unosi po statusu", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "službeno <PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Postotak unosa", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "V<PERSON>jeme od<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Status izmijenjen", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Ukupno", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Uredi podatke baze", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "Kako izrač<PERSON>vamo ocjene reprezentativnosti", "app.containers.AdminPage.DashboardPage.continuousType": "Bez vremenske trake", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Ukupno kumulativno", "app.containers.AdminPage.DashboardPage.customDateRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.day": "dan", "app.containers.AdminPage.DashboardPage.false": "netočno", "app.containers.AdminPage.DashboardPage.female": "ženski", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Top 5 unosa prema reakcijama", "app.containers.AdminPage.DashboardPage.fromTo": "od {from} do {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Nadzorna ploča za aktivnosti na platformi", "app.containers.AdminPage.DashboardPage.helmetTitle": "Stranica nadzorne ploče administratora", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Odaberite resurs za prikaz po projektu", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Izaberite resurs za prikaz po oznaci", "app.containers.AdminPage.DashboardPage.inputs1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Unosi po statusu", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Odaberite grupu korisnika", "app.containers.AdminPage.DashboardPage.male": "mu<PERSON>ki", "app.containers.AdminPage.DashboardPage.month": "mjesec", "app.containers.AdminPage.DashboardPage.noData": "Nema podataka za prikaz.", "app.containers.AdminPage.DashboardPage.noPhase": "Za ovaj projekt nisu kreirane faze", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "<PERSON><PERSON><PERSON> su<PERSON>a koji su obja<PERSON> unose, re<PERSON><PERSON>i ili komentirali.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "Nesviđanja", "app.containers.AdminPage.DashboardPage.numberOfLikes": "S<PERSON>đa mi se", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "<PERSON>ne reak<PERSON>", "app.containers.AdminPage.DashboardPage.overview.management": "Upravljanje", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projekti i sudjelovanje", "app.containers.AdminPage.DashboardPage.overview.showLess": "Prika<PERSON><PERSON> manje", "app.containers.AdminPage.DashboardPage.overview.showMore": "Prikaži više", "app.containers.AdminPage.DashboardPage.participants": "Sudionici", "app.containers.AdminPage.DashboardPage.participationPerProject": "Sudjelovanje po projektu", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Sudjelovanje po oznaci", "app.containers.AdminPage.DashboardPage.perPeriod": "Po {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Prethodnih 30 dana", "app.containers.AdminPage.DashboardPage.previous90Days": "Prethodnih 90 dana", "app.containers.AdminPage.DashboardPage.previousWeek": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>dan", "app.containers.AdminPage.DashboardPage.previousYear": "<PERSON><PERSON><PERSON><PERSON> godine", "app.containers.AdminPage.DashboardPage.projectType": "Tip projekta: {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "Reak<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Ovaj skup podataka baze podataka potreban je za izračunavanje reprezentativnosti korisnika platforme u usporedbi s ukupnom populacijom.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Pružite skup podataka baze.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Pogledajte koliko su reprezentativni korisnici platforme u odnosu na ukupno stanovništvo na temelju podataka prikupljenih tijekom registracije korisnika. Saznajte više o {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Pogledajte koliko su reprezentativni korisnici platforme u odnosu na ukupno stanovništvo na temelju podataka prikupljenih tijekom registracije korisnika.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Reprezentativnost zajednice", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Natrag na nadzornu ploču", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "U ovom trenutku nije podržano bilo koje od omogućenih polja za registraciju.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Ovdje možete prikazati/sakriti stavke nadzorne ploče i unijeti podatke u bazu podataka. Ovdje će se pojaviti samo omogućena polja za {userRegistrationLink}.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Uredi podatke baze", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "registracija korisnika", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Pošaljite podatke baze", "app.containers.AdminPage.DashboardPage.resolutionday": "u danima", "app.containers.AdminPage.DashboardPage.resolutionmonth": "u Mjesecima", "app.containers.AdminPage.DashboardPage.resolutionweek": "u Tjednima", "app.containers.AdminPage.DashboardPage.selectProject": "Odaberite projekt", "app.containers.AdminPage.DashboardPage.selectedProject": "filtar trenutnog projekta", "app.containers.AdminPage.DashboardPage.selectedTopic": "trenutni filtar oznake", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Saznajte što se događa na vašoj platformi.", "app.containers.AdminPage.DashboardPage.tabOverview": "Pregled", "app.containers.AdminPage.DashboardPage.tabReports": "Izvješća", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Reprezentativnost", "app.containers.AdminPage.DashboardPage.tabUsers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.timelineType": "Vremenska traka", "app.containers.AdminPage.DashboardPage.titleDashboard": "Nadzorna ploča", "app.containers.AdminPage.DashboardPage.total": "Ukupno", "app.containers.AdminPage.DashboardPage.totalForPeriod": "Ovo {period}", "app.containers.AdminPage.DashboardPage.true": "točno", "app.containers.AdminPage.DashboardPage.unspecified": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.users": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Korisnici po starosti", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Korisnici prema geografskom području", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "<PERSON><PERSON><PERSON><PERSON> prema spolu", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Registracije", "app.containers.AdminPage.DashboardPage.week": "t<PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Savjeti za odabir favicon slike: odaberite jednostavnu sliku s obzirom da će veličina njenog prikaza biti vrlo mala. Sliku bi trebalo spremiti u PNG formatu, u obliku kvadrata s prozirnom pozadinom (ili bijelom). Vaš favicon bi trebalo postaviti samo jednom jer će za izmjenu biti potrebna određena tehnička podrška.", "app.containers.AdminPage.FaviconPage.save": "Sp<PERSON>i", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Došlo je do pogreške. Pokušajte kasnije.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Us<PERSON><PERSON>h!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Vaše promjene su spremljene.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Izbriši", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Voditelji mapa", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Voditelji mapa mogu uređivati opis mape, kreirati nove projekte u okviru mape i imati upravljačka prava nad svim projektima koji se nalaze u mapi. Oni ne mogu brisati projekte i nemaju pristup projektima koji se nalaze izvan njihove mape. Možete otvoriti {projectManagementInfoCenterLink} kako biste pronašli više informacija o pravima voditelja projekata.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Nisu pronađena <PERSON>", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "posjetite naš Centar za podršku", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Pretražite korisnike", "app.containers.AdminPage.FoldersEdit.addToFolder": "Dodaj u mapu", "app.containers.AdminPage.FoldersEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Izbrišite mapu", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Opis", "app.containers.AdminPage.FoldersEdit.draftStatus": "Nacrt", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Dodajte datoteke u mapu", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Datoteke ne smiju biti veće od 50 MB. Dodane datoteke će se prikazivati na stranici mape.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Opisi", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "U ovoj mapi nema projekata. Vratite se na karticu Projekti kako biste kreirali i dodali projekte.", "app.containers.AdminPage.FoldersEdit.folderName": "Naziv mape", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Naslovna slika", "app.containers.AdminPage.FoldersEdit.multilocError": "Sva tekstna polja moraju biti popunjena na svakom jeziku.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Nema projekata koje možete dodati u ovu mapu.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Slika kartice mape", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Dopuštenja", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Projekti mape", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Postavke", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projekti dodani u ovu mapu", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Projekti koje možete dodati u ovu mapu", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Odaberite je li ova mapa „nacrt“, „objavljena“ ili „arhivirana“.", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Objavljeno", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Ukloni iz mape", "app.containers.AdminPage.FoldersEdit.save": "Sp<PERSON>i", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Došlo je do pogreške. Pokušajte kasnije.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Us<PERSON><PERSON>h!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Vaše promjene su spremljene.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Kratak opis", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "prikazuje se na početnoj stranici", "app.containers.AdminPage.FoldersEdit.statusLabel": "Status publikacije", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Objasnite kako su projekti međusobno povezani, definirajte vizualni identitet i podijelite informacije.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Objasnite kako su projekti međusobno povezani, definirajte vizualni identitet i podijelite informacije.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Sva tekstualna polja moraju biti popunjena.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Kreirajte novu mapu", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Postavke", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Prikaži mapu", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Prilagodite sliku i tekst istaknutog natpisa.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Istaknuti natpis", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Spremi istaknuti natpis", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Inspiration Hub je mjesto gdje možete pronaći inspiraciju za svoje projekte pregledavajući projekte na drugim platformama.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Uredite uvjete i odredbe i pravila o privatnosti na vašoj platformi. Druge stranice, uključujući O web-mjestu i Česta pitanja mogu se uređivati na kartici {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Pravila platforme", "app.containers.AdminPage.PagesEdition.privacy-policy": "Pravila o privatnosti", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "<PERSON><PERSON>i korišćenja", "app.containers.AdminPage.Project.confirmation.description": "Ova se radnja ne može poništiti.", "app.containers.AdminPage.Project.confirmation.no": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.confirmation.title": "Jeste li sigurni da želite poništiti sve podatke o sudjelovanju?", "app.containers.AdminPage.Project.confirmation.yes": "Poništi sve podatke o sudjelovanju", "app.containers.AdminPage.Project.data.descriptionText1": "O<PERSON><PERSON>i ideje, kome<PERSON><PERSON>, glas<PERSON>, re<PERSON><PERSON><PERSON>, odgovore na ankete, odgovore na ankete, volontere i registrirane sudionike događaja. U slučaju faza glasanja, ova će radnja obrisati glasove, ali ne i opcije.", "app.containers.AdminPage.Project.data.title": "Izbrišite sve podatke o sudjelovanju iz ovog projekta", "app.containers.AdminPage.Project.resetParticipationData": "Poništi sve podatke o sudjelovanju", "app.containers.AdminPage.Project.settings.accessRights": "<PERSON><PERSON><PERSON> pristupa", "app.containers.AdminPage.Project.settings.back": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.data": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.description": "Opis", "app.containers.AdminPage.Project.settings.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.general": "Općenito", "app.containers.AdminPage.Project.settings.projectTags": "Oznake projekta", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Popis projekata na ovoj platformi", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Nadzorna ploča s projektima", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Kreirajte novi projekt ili upravljajte postojećima.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projekti", "app.containers.AdminPage.ProjectDashboard.published": "Objavljeno", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Zatvorite ploču postavki", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Centar", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "Puna širina", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "Lijevo", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "Pravo", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Tekst gumba", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Unesite tekst za gumb", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "<PERSON>rsta gumba", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Se<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "URL gumba", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Unesite URL za gumb", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Raspored stupaca", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Opis", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Opis početne stranice", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Ovaj se opis prikazuje na pregledu projekata u okviru početne stranice.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Ovaj se opis prikazuje na stranici projekta. Jasno opišite o čemu je projekt, što očekujete od korisnika i što oni mogu očekivati od vas.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Došlo je do pogreške. Pokušajte kasnije", "app.containers.AdminPage.ProjectDescription.preview": "Pretpregled", "app.containers.AdminPage.ProjectDescription.save": "Sp<PERSON>i", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Vaše promjene su spremljene.", "app.containers.AdminPage.ProjectDescription.saved": "Spremljeno!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Odlučite koju poruku želite prenijeti svojoj publici. Uredite projekt i obogatite ga slikama, videozapisima, datotekama privitaka,... Ove informacije posjetiteljima pomažu u shvaćanju o čemu je projekt.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Opis projekta", "app.containers.AdminPage.ProjectDescription.whiteSpace": "Razmak", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Uključi obrub", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "Okomita visina", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Srednje", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Malo", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Otkaži uređivanje", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Zadana geografska širina središnje točke mape. Prihvaća vrijednost između -90 i 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Zadana geografska dužina središnje točke mape. Prihvaća vrijednost između -90 i 90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Uredi sloj mape", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "<PERSON><PERSON><PERSON> sloj", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Došlo je do pogreške. Pokušajte kasnije", "app.containers.AdminPage.ProjectEdit.MapTab.here": "ovdje", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Uvezi GeoJSON datoteku", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Zadana geografska širina", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Boja sloja", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Sve funkcije u sloju će biti stilizirane ovom bojom. Ova boja će također prebrisati sve postojeće stilove u vašoj GeoJSON datoteci.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "<PERSON><PERSON><PERSON>ke", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Kao opciju odaberite ikonu koja se prikazuje na oznakama. Kliknite na {url} kako biste vidjeli popis ikona koje možete izabrati.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Naziv sloja", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Naziv ovoga sloja prikazan je na legendi mape", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Opis sloja", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Ovaj tekst prikazuje se kao savjet pri prelasku kursorom preko značajke sloja na mapi", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Slojevi mape", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "Trenutno podržavamo GeoJSON datoteke. Pročitajte {supportArticle} za savjete o tome kako da konvertirate i stilizirate slojeve mape.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Zadana geografska dužina", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Zadano središte mape i uvećanje", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Zadana središnja točka i razina zumiranja karte. Ručno prilagodite vrijednosti u nastavku ili kliknite na gumb {button} u donjem lijevom kutu karte da biste spremili trenutnu središnju točku i razinu zumiranja karte kao zadane vrijednosti.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Prilagodite prikaz mape, uključujući prijenosa i oblikovanja slojeva mape i postavljanje središta mape i razine zumiranja.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Konfiguracija mape", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "Konfiguracija karte trenutno se dijeli kroz faze, ne možete kreirati različite konfiguracije karte po fazi.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Ukloni sloj", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Sp<PERSON>i", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "članak podrške", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Neimenovani sloj", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Razina uvećanja mape", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Zadana razina uvećanja karte. Prihvaća vrijednost između 1 i 17, pri čemu je 1 potpuno umanjena (vidljiv je cijeli svijet), a 17 je potpuno uvećana (vidljivi su blokovi i zgrade)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Anonimizirajte sve korisničke podatke", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Svi unosi korisnika u anketu bit će anonimizirani prije snimanja", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Korisnici će i dalje morati ispunjavati zahtjeve za sudjelovanje pod karticom 'Prava pristupa'. Podaci o korisničkom profilu neće biti dostupni u izvozu podataka ankete.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "<PERSON><PERSON> ovu opciju, polja za registraciju korisnika bit će prikazana kao zadnja stranica u anketi umjesto kao dio procesa prijave.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Demografska polja u anketnom obrascu", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Prikaži demografska polja u anketi?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "Pročitajte više o tome kako funkcionira automatsko dijeljenje u ovom članku.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Automatsko dijeljenje rezultata", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Rezultati glasovanja dijele se na platformi i putem e-pošte sudionicima kada faza završi. To osigurava transparentnost prema zadanim postavkama.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Dijeljenje rezultata", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Dodajte opciju odgovora", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Odustani", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Odustani", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Izbriši", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Izbriši", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Uredi opciju odgovora", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Spremi opcije odgovora", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Uredi opcije odgovora", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Izvezi rezultate ankete", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Maks<PERSON>lan broj izbora je veći od broja opcija", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Višestruki izbor", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "Bez opcija", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "Sva pitanja moraju imati dostupan izbor odgovora", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Samo jedna opcija", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Sudionici ankete imaju samo jednu opciju odgovora", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Upravljajte opcijama odgovora za: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Ovdje možete kreirati anketna pitanja, postavite izbore odgovora za sudionike, odlučite želite li da sudionici mogu izabrati samo jedan odgovor (jedan izbor) ili više odgovora (višestruki izbor) te izvesti rezultate ankete. U jednoj anketi možete postaviti više pitanja.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Sp<PERSON>i", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "Sp<PERSON>i", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Podešavanja i rezultati anketa", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "<PERSON>g<PERSON><PERSON><PERSON> maksim<PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Uvoz", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "<PERSON><PERSON><PERSON> povratne informacije, dodijelite teme ili kopirajte objave za sljedeću fazu projekta.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "Upravljajte prijedlozima, dajte povratne informacije i dodijelite teme.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "Dijeljenje rezultata je isključeno.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Rezultati glasovanja neće se dijeliti na kraju faze osim ako ih ne izmijenite u postavkama faze.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Ovi će se rezultati automatski dijeliti nakon završetka faze. Izmijenite datum završetka ove faze kako biste promijenili vrijeme dijeljenja rezultata.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Izvezite rezultate upitnika (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Ovdje možete preuzeti rezultate Typeform upitnika u sklopu projekta kao Excel dokument.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "Obrazac za anketu", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Rezultati upitnika", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Upitnik", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Pogledajte odgovore na upitnik", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "<PERSON><PERSON><PERSON> raz<PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Jeste li sigurni?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Opis", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Koristite ovo da volonterima pojasnite što se od njih očekuje, kao i što mogu očekivati.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Obrazac sadrži pogreške i nije mogao biti sačuvan.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Slika", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Izbriši", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Povod predstavlja radnju ili aktivnost u kojoj građani mogu sudjelovati kao volonteri.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "<PERSON><PERSON><PERSON> povod", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "<PERSON><PERSON><PERSON> opis", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Izvezi volontere", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Povod predstavlja radnju ili aktivnost u kojoj građani mogu sudjelovati kao volonteri.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "Novi razlog", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Sp<PERSON>i", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Ovdje možete postaviti povode na kojima ljudi mogu volontirati i preuzeti opis volontera.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Volontiranje", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {nema volontera} one {# volonter} other {# volontera}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "raspodjela proračuna", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Dodijelite proračun opcijama i zamolite sudionike da odaberu željene opcije koje se uklapaju u ukupni proračun.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Raspodjela proračuna", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Dopuštanje korisnicima da komentiraju može utjecati na proces glasovanja.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "Kredit", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Zadani prikaz opcija", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Akcije za korisnike", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Odaberite koje dodatne radnje korisnici mogu poduzeti.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Fiksni iznos", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "Ako se ostavi prazno, ovo će biti zadano \"glasovanje\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "Saznajte više o tome kada koristiti <b> {voteTypeDescription} </b> u našem {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "<PERSON><PERSON><PERSON><PERSON> broj glasova po opciji", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "<PERSON><PERSON><PERSON><PERSON> broj <PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Možete ograničiti ukupan broj glasova koje korisnik može dati (s maksimalno jednim glasom po opciji).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "vi<PERSON>e glasova po opciji", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Korisnici dobivaju količinu tokena za raspodjelu između opcija", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "<PERSON><PERSON><PERSON><PERSON> glasova po opciji", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "<PERSON><PERSON><PERSON> po korisniku", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Pregled analize opcija", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Opcije za glasanje", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "Točka", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "jedan glas po opciji", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Korisnici mogu odlučiti odobriti bilo koju od opcija", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Jedan glas po opciji", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Znak", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Neogra<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Kako bi se glasovanje trebalo zvati?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "Npr. <PERSON><PERSON>, bod<PERSON><PERSON>, karbonski krediti...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "Npr. token, bod, karbon kredit...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "Glasanje", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Svaka metoda glasovanja ima različite predkonfiguracije", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "Metoda glasovanja određuje pravila kako korisnici glasaju", "app.containers.AdminPage.ProjectEdit.addNewInput": "<PERSON><PERSON><PERSON><PERSON> un<PERSON>", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "Svoj projekt možete dodati u mapu sada ili to učiniti kasnije u postavkama projekta", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Oznake projekta", "app.containers.AdminPage.ProjectEdit.altText": "Alternativni tekst", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Anonimno glasovanje", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "<PERSON>da je ovo <PERSON>, nemo<PERSON>će je vidjeti tko je i za što glasao. Korisnici i dalje moraju imati račun i mogu glasati samo jednom.", "app.containers.AdminPage.ProjectEdit.approved": "Odobreno", "app.containers.AdminPage.ProjectEdit.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Arhivirani projekti i dalje su vidljivi, ali ne dopuštaju daljnje sudjelovanje", "app.containers.AdminPage.ProjectEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Područje nije moguće izbrisati jer se koristi za prikaz projekata na jednoj ili više prilagođenih stranica. Prije brisanja područja trebate odspojiti područje sa stranice ili izbrisati stranicu.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Sva područja", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Projekt će se prikazati uz svaki filtar područja.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "<PERSON><PERSON><PERSON> područ<PERSON>", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projekte možete filtrirati na početnoj stranici koristeći područja. Područja možete postaviti {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "ovdje", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "<PERSON><PERSON> o<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Projekt se neće prikazati kad filtrirate područje.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Projekt će se prikazati uz odabrani filtar(e) područja.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Kartice", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Dodajte sadr<PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Počeli su pristizati zahtjevi za ovu anketu. Promjene u anketi mogu dovesti do gubitka podataka i nepotpunih podataka u izvezenim datotekama.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Anketa je uspješno spremljena", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Odaberite metodu glasovanja i neka korisnici odaberu prioritet između nekoliko različitih opcija.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Provedite glasovanje ili vježbu određivanja prioriteta", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Kreirajte projekt na osnovu predloška", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Ugradite vanjski upitnik", "app.containers.AdminPage.ProjectEdit.createInput": "Do<PERSON>j novi unos", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Izradite upitnik unutar platforme", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Postavite upitnik bez napuštanja platforme.", "app.containers.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Podesite upitnik s više odgovora.", "app.containers.AdminPage.ProjectEdit.createProject": "Novi projekt", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Ugradite Typeform, Google Form, Enalyzer, SurveyXact, Qualtrics, SmartSurvey, Snap Survey ili Microsoft Forms anketu.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Možete postaviti zadani redoslijed objava koji se prikazuju na glavnoj stranici projekta.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Razvrstavanje", "app.containers.AdminPage.ProjectEdit.departments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Opis", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "Ovo će omogućiti ili onemogućiti nesviđanje, ali će ocjenjivanje i dalje biti omogućeno. Preporučujemo da ovo ostavite onemogućenim osim ako ne provodite analizu opcija.", "app.containers.AdminPage.ProjectEdit.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "B<PERSON>j nesviđanja po sudioniku", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Omogući nesviđanje", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Prikupite povratne informacije o dokumentu", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Ugradite interaktivni PDF i skupljajte komentare i povratne informacije s Konveiom.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Nacrti projekata skriveni su za sve osobe osim za administratore i dodijeljene voditelje projekata.", "app.containers.AdminPage.ProjectEdit.draft": "Nacrt", "app.containers.AdminPage.ProjectEdit.draftStatus": "Nacrt", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Odaberite koje radnje sudjelovanja korisnici mogu poduzeti.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (maks. 50 MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Datoteke ne smiju biti veće od 50 MB. Privici će biti prikazani na stranici sa informacijama o projektu.", "app.containers.AdminPage.ProjectEdit.filesTab": "Datoteke", "app.containers.AdminPage.ProjectEdit.findVolunteers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Zatražite od sudionika da volontiraju za aktivnosti i povode.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "<PERSON><PERSON> upravi<PERSON>j mapa, možete odabrati mapu prilikom stvaranja projekta, ali samo administrator je može naknadno promijeniti.", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Alternativni tekst slike kartice mape", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Odaberite mapu u koji ćete dodati ovaj projekt:", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Prilagođeni sadržaj", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Počeli su pristizati zahtjevi za ovaj obrazac. Promjene obrasca mogu dovesti do gubitka podataka i nepotpunih podataka u izvezenim datotekama.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Obrazac je uspješno spremljen", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Završetak upitnika", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Na osnovu predloška", "app.containers.AdminPage.ProjectEdit.generalTab": "Općenito", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Zamjenski tekst slike zaglavlja", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Naslovna slika", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NOVI", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Pružite informacije korisnicima ili koristite alat za izradu izvješća za dijeljenje rezultata prošlih faza.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Dijelite informacije ili rezultate", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Prikupljajte unose i povratne informacije", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Kreirajte ili skupljajte unose, reakcije i/ili komentare. Birajte između različitih vrsta unosa: prikupljan<PERSON> ideja, analiza opcija, pitanje i odgovor, identifikacija problema i više.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Tko je odgovoran za obradu objava?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Svi novi unosi u ovom projektu bit će dodijeljeni ovoj osobi. Ona se može promijeniti u {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Komentiranje objava", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Obrazac za unos", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "vod<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "<PERSON><PERSON><PERSON> ob<PERSON>", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Reagiranje na unose", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Zadani prikaz", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Odaberite zadani prikaz korisničkih unosa: kartice ili tačke na mapi. Sudionici mogu samostalno odlučivati o izboru ova dva prikaza.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Središte inspiracije", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Ugradite Konveio URL", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "<PERSON><PERSON><PERSON> po sudioniku", "app.containers.AdminPage.ProjectEdit.limited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Učitaj<PERSON> jo<PERSON>", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Karta", "app.containers.AdminPage.ProjectEdit.mapTab": "Karta", "app.containers.AdminPage.ProjectEdit.maxDislikes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxLikes": "<PERSON><PERSON><PERSON><PERSON>o<PERSON>", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "<PERSON><PERSON><PERSON><PERSON> broj glasova po opciji mora biti manji ili jednak ukupnom broju glasova", "app.containers.AdminPage.ProjectEdit.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Sudionici ne mogu premašiti ovaj proračun prilikom prijave košarice.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimum", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Zatražite od sudionika da ispune minimalni proračun prilikom slanja košarice (unesite „0“ ako ne želite da postavite minimum).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "posjetite naš Centar za podršku", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Tko su voditelji projekta?", "app.containers.AdminPage.ProjectEdit.moreDetails": "Više pojedinosti", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "Trebate inspiraciju? Istražite slične projekte iz drugih gradova u {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newIdea": "Nova ideja", "app.containers.AdminPage.ProjectEdit.newInitiative": "Dodajte inicijativu", "app.containers.AdminPage.ProjectEdit.newIssue": "<PERSON><PERSON><PERSON><PERSON> problem", "app.containers.AdminPage.ProjectEdit.newOption": "Dodajte opciju", "app.containers.AdminPage.ProjectEdit.newPetition": "Dodajte peticiju", "app.containers.AdminPage.ProjectEdit.newProject": "Novi projekt", "app.containers.AdminPage.ProjectEdit.newProposal": "Dodajte prijedlog", "app.containers.AdminPage.ProjectEdit.newQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Najnovije", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "<PERSON>je valjani iznos", "app.containers.AdminPage.ProjectEdit.noFolder": "<PERSON><PERSON> mape", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "— <PERSON><PERSON> mape —", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Nisu pronađeni <PERSON>", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Unesite naziv projekta", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "<PERSON><PERSON> val<PERSON>i broj", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Najstarije", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Samo administratori mogu vidjeti", "app.containers.AdminPage.ProjectEdit.optionNo": "Ne", "app.containers.AdminPage.ProjectEdit.optionYes": "Da (odaberite mapu)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Razine sudjelovanja", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Što želite učiniti?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Odaberite kako korisnici sudjeluju.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "Možete odrediti tko može poduzeti svaku radnju i postaviti dodatna pitanja sudionicima kako biste prikupili više informacija.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Zahtjevi i pitanja za sudionike", "app.containers.AdminPage.ProjectEdit.pendingReview": "Čeka odobrenje", "app.containers.AdminPage.ProjectEdit.permissionsTab": "<PERSON><PERSON><PERSON> pristupa", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "<PERSON><PERSON><PERSON> pristupa", "app.containers.AdminPage.ProjectEdit.phaseEmails": "Obavijesti", "app.containers.AdminPage.ProjectEdit.pollTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "Većina reakci<PERSON>", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Slika kartice projekta", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Ova je slika dio kartice projekta; kartice koja pruža sažetak projekta i prikazuje se na početnoj stranici, na primjer.\n\n    Za više informacija o preporučenim razlučivostima slika, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Mapa", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Ova se slika prikazuje je na vrhu stranice projekta.\n\n    Za više informacija o preporučenim razlučivostima slika, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Alternativni tekst slike kartice projekta", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Navedite kratak opis slike za korisnike oštećena vida. To pomaže čitačima zaslona da prenesu o čemu se radi na slici.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Upravljanje projektima", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Voditelji projekata mogu uređivati projekte, upravljati objavama i slati obavijesti e-poštom korisnicima. Možete {moderationInfoCenterLink} kako biste pronašli više informacija o pravima voditelja projekata.", "app.containers.AdminPage.ProjectEdit.projectName": "Naziv projekta", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Tip projekta", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Odaberite hoće li ovaj projekt imati jasan početak i kraj. Ovakvi projekti mogu obuhvaćati različite faze. Projekti bez vremenskih traka nemaju ograničenje u pogledu trajanja.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Tip projekta se ne može naknadno mi<PERSON>.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "Projekt možete postaviti tako da bude nevidljiv određenim korisnicima.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Vidljivost projekta", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Tražite status projekta? Sada ga možete promijeniti u bilo kojem trenutku izravno iz zaglavlja stranice projekta.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Objavljeni projekti vidljivi su svima ili podskupu grupe ako je odabrano.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Objavljeno", "app.containers.AdminPage.ProjectEdit.purposes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Slučajni odabir", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Poništi podatke o sudjelovanju", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Do<PERSON><PERSON> je do pogreške prilikom spremanja unosa. Molimo pokušajte ponovo.", "app.containers.AdminPage.ProjectEdit.saveProject": "Sp<PERSON>i", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Us<PERSON><PERSON>h!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "<PERSON><PERSON><PERSON> o<PERSON> je spremljen!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Pretraži predloš<PERSON>", "app.containers.AdminPage.ProjectEdit.selectGroups": "Odaberi grupu(e)", "app.containers.AdminPage.ProjectEdit.setup": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.shareInformation": "Podijelite informacije", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "<PERSON><PERSON><PERSON> up<PERSON>", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Postavite i prilagodite svoj projekt.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "posjetite naš centar za podršku", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Odustani", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# izbora} one {# izbor} other {# izbora}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON>, <PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Počeli su pristizati zahtjevi za ovu anketu. Promjene u anketi mogu dovesti do gubitka podataka i nepotpunih podataka u izvezenim datotekama.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "<PERSON><PERSON> natrag", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Uvoz", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Uvoz", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "AI sažecima za kratke odgovore, duge odgovore i dodatna pitanja na ljestvici osjećaja može se pristupiti s kartice AI na lijevoj bočnoj traci.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "Matrica", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "Dug odgovor", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Višestruki izbor – odaberite više", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Izbor slike - odaberite više", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "Novi podnesak", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Još nema odgovora na upitnik", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Otvori za odgovore", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Otvoreno za odgovore", "app.containers.AdminPage.ProjectEdit.survey.optional2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "Ako se ne doda nikakva logika, anketa će slijediti normalan tijek. Ako i stranica i njena pitanja imaju logiku, logika pitanja će imati prednost. Provjerite je li ovo usklađeno s planiranim tokom ankete. Za više informacija posjetite {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "<PERSON><PERSON><PERSON> želite napustiti?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Vaše trenutne promjene neće biti spremljene.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.rating": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.required2": "Neoph<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {odgovori} one {odgovor} other {odgovori}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# odgovor} one {# odgovor} other {# odgovor}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Višestruki izbor – odaberite jedan", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Linearna skala o<PERSON>", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Prijenos Esri shapefile", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Upitnik je uspješno spremljen", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Upitnik", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Odgovori na anketu", "app.containers.AdminPage.ProjectEdit.survey.text2": "Kratak odgovor", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Ukupno {count} odgovora", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "Prikaži upitnik", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "Pogled", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Umetni URL", "app.containers.AdminPage.ProjectEdit.surveyService": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Možete pronaći više informacija o integraciji upitnika {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "ovdje", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Oznaku nije moguće izbrisati jer se koristi za prikaz projekata na jednoj ili više prilagođenih stranica.\nPrije brisanja oznake trebate odspojiti oznaku sa stranice ili izbrisati stranicu.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Opće postavke projekta", "app.containers.AdminPage.ProjectEdit.titleLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Odaberite naziv koji je kratak, afirmativan i jasan. On će biti prikazan u padajućem izborniku i projektnoj kartici na početnoj stranici.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Oznake", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Odaberite {topicsCopy} za ovaj projekt. Korisnici ovo mogu koristiti za filtriranje projekata.", "app.containers.AdminPage.ProjectEdit.totalBudget": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "<PERSON> <PERSON>u", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Nedodijeljeno", "app.containers.AdminPage.ProjectEdit.unlimited": "Neo<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Iskoristite predložak", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "Prikaz projekta", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Volontiranje", "app.containers.AdminPage.ProjectEdit.voteTermError": "Uvjeti glasanja moraju biti navedeni za sve jezike", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# može vidjeti grupa} one {# može vidjeti grupu} other {# može vidjeti grupa}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Dodatne informacije", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "adresa 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "<PERSON><PERSON><PERSON> m<PERSON> dog<PERSON>", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Adresa 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "Npr. stan, <PERSON>man, z<PERSON>a", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Dodatne informacije o adresi koje bi mogle pomoći u identifikaciji lokacije, poput naziva zgrade, broja kata itd.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "Pogledajte članak podrške", "app.containers.AdminPage.ProjectEvents.customButtonLink": "Vanjska poveznica", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Dodajte poveznicu na vanjski URL (npr. usluga događaja ili web mjesto za prodaju karata). Ovo postavljanje će nadjačati zadano ponašanje gumba za prisustvo.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Prilagođeni tekst gumba", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Postavite tekst gumba na vrijednost koja nije \"Registriraj se\" kada je postavljen vanjski URL.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Početak", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Izbriši", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Jeste li sigurni da želite obrisati ovaj događaj? Ovo nije moguće poništiti!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Opis dog<PERSON>", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.editEventTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "Za slanje e-pošte registriranima izravno s platforme, administratori moraju stvoriti korisničku grupu na kartici {userTabLink} . {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.eventImage": "Slika događaja", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Alternativni tekst slike događaja", "app.containers.AdminPage.ProjectEvents.eventLocation": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Izvoznici", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (maks. 50 MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Privici se prikazuju ispod opisa događaja.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Lokacija", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "<PERSON><PERSON><PERSON><PERSON> broj registriranih osoba", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Izradi novi događaj", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Link za online događaj", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "Ako je vaš događaj online, dodajte poveznicu na njega ovdje.", "app.containers.AdminPage.ProjectEvents.preview": "Pregled", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Precizirajte lokaciju karte", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Precizirajte lokaciju na karti", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "Možete precizirati gdje se prikazuje oznaka lokacije vašeg događaja klikom na kartu u nastavku.", "app.containers.AdminPage.ProjectEvents.register": "Registar", "app.containers.AdminPage.ProjectEvents.registerButton": "Gumb za registraciju", "app.containers.AdminPage.ProjectEvents.registrant": "registrirani", "app.containers.AdminPage.ProjectEvents.registrants": "registrirani", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Ograničenje registracije", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Sp<PERSON>i", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "Nismo mogli spremiti izmjene, pokušajte kasnije.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Us<PERSON><PERSON>h!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Vaše promjene su spremljene.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "Potražite lokaciju", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Povežite predstojeće događaje s ovim projektom kako biste ih prikazali na stranici događaja projekta.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Naziv i datumi", "app.containers.AdminPage.ProjectEvents.titleEvents": "Događaji projekta", "app.containers.AdminPage.ProjectEvents.titleLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Poveži gumb s vanjskim URL-om", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "Prema zadanim postavkama, gumb za registraciju događaja na platformi bit će prikazan kako bi korisnicima omogućio registraciju za događaj. To možete promijeniti tako da umjesto toga vodi do vanjske URL adrese.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Ograničite broj prijavljenih na događaj", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Postavite maksimalan broj prijavljenih na događaj. Ako se dostigne ograničenje, daljnje prijave neće biti prihvaćene.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/korisnici", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "Dodajte datoteke u svoj projekt", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Priložite datoteke s ovog popisa svom projektu, fazama i događajima.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Dodajte datoteke kao kontekst u Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Dodajte datoteke svom projektu Sensemaking kako biste pružili kontekst i uvide.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Uskoro", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Sinkronizirajte ankete, prenesite intervjue i dopustite umjetnoj inteligenciji da poveže vaše podatke.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Prenesite bilo koju dato<PERSON>ku", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Koristite umjetnu inteligenciju za analizu datoteka", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Transkripti procesa itd.", "app.containers.AdminPage.ProjectFiles.addFiles": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "Uvidi utemeljeni na umjetnoj inteligenciji", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "Analizirajte prenesene datoteke kako biste istaknuli ključne teme.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Omogućite naprednu analitiku ovih datoteka pomoću umjetne inteligencije.", "app.containers.AdminPage.ProjectFiles.askButton": "Pitaj<PERSON>", "app.containers.AdminPage.ProjectFiles.categoryLabel": "Kategorija", "app.containers.AdminPage.ProjectFiles.chooseFiles": "Odaberite datoteke", "app.containers.AdminPage.ProjectFiles.close": "Zatvoriti", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Potvrdi i prenesi", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Jeste li sigurni da želite izbrisati ovu datoteku?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "<PERSON><PERSON> mogu<PERSON>e učitati datoteku za označavanje.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "<PERSON>je moguće učitati CSV pregled.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "U pregledima CSV-a prikazuje se maksimalno 50 redaka.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "CSV datoteka je prevelika za pregled.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "Izbriši datoteku", "app.containers.AdminPage.ProjectFiles.description": "Opis", "app.containers.AdminPage.ProjectFiles.done": "Gotovo", "app.containers.AdminPage.ProjectFiles.downloadFile": "<PERSON><PERSON><PERSON> datoteku", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Preuzmi cijelu datoteku", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Povucite i ispustite bilo koje datoteke ovdje ili", "app.containers.AdminPage.ProjectFiles.editFile": "<PERSON><PERSON><PERSON> da<PERSON>", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Opis", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "Naziv datoteke ne smije sadržavati točku.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "<PERSON>v datoteke", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "<PERSON>v datoteke je obavez<PERSON>.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "<PERSON><PERSON><PERSON> datoteku", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Pregled", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "<PERSON><PERSON> datoteka neće biti prenesena jer prelazi maksimalno ograničenje od 50 MB.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "Sve datoteke su uspješno prenesene", "app.containers.AdminPage.ProjectFiles.generatingPreview": "<PERSON><PERSON><PERSON><PERSON>...", "app.containers.AdminPage.ProjectFiles.info_sheet": "Informacija", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "Npr. <PERSON>V, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Audio intervjui, snimke gradske vijećnice", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "Npr. PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, informativni dokumenti", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "Npr. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "Slike", "app.containers.AdminPage.ProjectFiles.interview": "Intervju", "app.containers.AdminPage.ProjectFiles.maxFilesError": "Možete prenijeti najviše {maxFiles} datoteka odjednom.", "app.containers.AdminPage.ProjectFiles.meeting": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.noFilesFound": "<PERSON><PERSON> pronađene dato<PERSON>.", "app.containers.AdminPage.ProjectFiles.other": "Ostalo", "app.containers.AdminPage.ProjectFiles.policy": "Politika", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Pregled još nije podržan za ovu vrstu datoteke.", "app.containers.AdminPage.ProjectFiles.report": "Izvješće", "app.containers.AdminPage.ProjectFiles.retryUpload": "Ponovi prijenos", "app.containers.AdminPage.ProjectFiles.save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "Datoteka je uspješ<PERSON>.", "app.containers.AdminPage.ProjectFiles.searchFiles": "Pretraži datoteke", "app.containers.AdminPage.ProjectFiles.selectFileType": "<PERSON><PERSON><PERSON> da<PERSON>", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Strateški plan", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "Možete prenijeti najviše {maxFiles} datoteka odjednom.", "app.containers.AdminPage.ProjectFiles.unknown": "Nepoznato", "app.containers.AdminPage.ProjectFiles.upload": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# datoteka} other {# datoteke}} us<PERSON><PERSON><PERSON><PERSON>, {numberOfErrors, plural, one {# greška} other {# greške}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "Prikaži da<PERSON>ku", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Skupite sva polja", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Opis polja", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Uredi obrazac za unos", "app.containers.AdminPage.ProjectIdeaForm.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Uključi ovo polje.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Došlo je do pogreške. Pokušajte kasnije", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Proširi sva polja", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Obrazac za unos", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Navedite koje informacije treba navesti, dodajte kratke opise ili upute za usmjeravanje odgovora sudionika i navedite je li svako polje neobavezno ili obavezno.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Odredite koje informacije su potrebne, dodajte kratke opise ili upute kako biste usmjerili odgovore sudionika i precizirali koje polje je obavezno, a koje izborno", "app.containers.AdminPage.ProjectIdeaForm.required": "Potrebno", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Zatražite da ovo polje bude popunjeno.", "app.containers.AdminPage.ProjectIdeaForm.save": "Sp<PERSON>i", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Vaše promjene su spremljene.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Spremljeno!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Automatizirana e-pošta", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Možete konfigurirati e-poruke koje se aktiviraju na razini faze", "app.containers.AdminPage.ProjectTimeline.datesLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Pregled", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Jeste li sigurni da želite izbrisati ovu fazu?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Opis faze", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "<PERSON><PERSON><PERSON> fazu", "app.containers.AdminPage.ProjectTimeline.endDate": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (maks. 50 MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Izradi novu fazu", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "<PERSON>va faza nema unaprijed definiran datum završetka.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Dijeljenje rezultata nekih metoda (kao što su rezultati glasovanja) neće se pokrenuti dok se ne odabere datum završetka.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "<PERSON><PERSON> dodate fazu nakon ove, to će dodati datum završetka ovoj fazi.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "Neodabir datuma završetka za ovo podrazumijeva sljedeće:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Pregled", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "S<PERSON><PERSON>i promje<PERSON>", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "<PERSON><PERSON><PERSON> je do pogreške prilikom slanja obrasca, pokušajte ponovo.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Spremljeno!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Vaše promjene su spremljene.", "app.containers.AdminPage.ProjectTimeline.startDate": "Početni datum", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "<PERSON>tum <PERSON>", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "Na<PERSON>lov ankete", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Naziv faze", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Upload privitaka", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartition", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologija (filtar naslovne stranice)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Kako bi kategorije u filtru naslovne stranice trebalo glasiti? Na primjer, oz<PERSON><PERSON>, kategor<PERSON><PERSON>, od<PERSON><PERSON>...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Oznake koje se mogu konfigurirati {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "ovdje", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Pojam za jednu <PERSON> (jednina)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "oznaka", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Pojam za višestruke oznake (množina)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "oznake", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Dodaj polje", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Dodajte novo polje za registraciju", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Format odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Unesite format odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Opcija odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Dajte opciju odgovora za sve jezike", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Spremi opciju odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Opcija odgovora uspješno spremljena", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Izbori odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Povucite i ispustite polja kako biste odredili redoslijed kojim se pojavljuju na obrascu za registraciju.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Zadano polje", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Izbriši", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Opcijski tekst koji se prikazuje ispod naziva polja na obrascu za registraciju.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Opcije izbora mjesta prebivališta mogu se postaviti u {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Uredi opciju odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Opis", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Naziv polja", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Unesite naziv polja za sve jezike", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Postavke polja", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Da<PERSON>ne (potvrdni okvir)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Datum", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Dulji odgovor", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Višestruki izbor (odaberite više)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Numerička vrijednost", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Višestruki iz<PERSON> (odaberite jedan)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Kraći odgovor", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Kartica s geografskim pod<PERSON>čjima", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Skriveno polje", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Želite li da odgovor na ovo polje bude obavezan?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Prilagođena polja", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Dodaj opciju odgovora", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Odustani", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Izbriši", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Jeste li sigurni da želite izbrisati ovu opciju odgovora na pitanje o registraciji? Svi zapisi na koje su određeni korisnici odgovorili ovom opcijom bit će trajno izbrisani. Ova se radnja ne može poništiti.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Jeste li sigurni da želite izbrisati ovo registracijsko pitanje? Svi odgovori koje su korisnici dali na ovo pitanje bit će trajno izbrisani i ono se više neće postavljati u projektima ili prijedlozima. Ova se radnja ne može poništiti.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Potrebno", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Spremi polje", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Polje uspješno spremljeno", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "<PERSON><PERSON> stu<PERSON>ca", "app.containers.AdminPage.SettingsPage.addAreaButton": "Dodaj geografsko područje", "app.containers.AdminPage.SettingsPage.addTopicButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Životinja - npr. <PERSON><PERSON><PERSON> slon", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Korisnik - npr. <PERSON><PERSON><PERSON> 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "Odaberite koji će administratori primati obavijesti za odobravanje projekata. Upravitelji mapa su prema zadanim postavkama odobravatelji za sve projekte unutar svojih mapa.", "app.containers.AdminPage.SettingsPage.approvalSave": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.approvalTitle": "Postavke odobrenja projekta", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Jeste li sigurni da želite izbrisati ovo područje?", "app.containers.AdminPage.SettingsPage.areaTerm": "Pojam za jedno područje (jednina)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "područje", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Izbriši", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areasTerm": "Pojam za više područja (množina)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "područja", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Odaberite barem jedan jezik.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Prikaži avatare", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Prikaži profilne slike sudionika i koliko ih ima neregistriranim posjetiteljima", "app.containers.AdminPage.SettingsPage.bannerHeader": "Tekst zaglavlja", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Tekst zaglavlja za neregistrirane posjetitelje", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Tekst podzaglavlja za neregistrirane posjetitelje", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Tekst podzaglavlja", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Tekst natpisa", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "<PERSON><PERSON><PERSON><PERSON> pregled za", "app.containers.AdminPage.SettingsPage.brandingDescription": "Dodajte svoj logotip i postavite boje platforme.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Brendiranje platforme", "app.containers.AdminPage.SettingsPage.cancel": "Odustani", "app.containers.AdminPage.SettingsPage.chooseLayout": "Ra<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.color_primary": "<PERSON><PERSON><PERSON> boja", "app.containers.AdminPage.SettingsPage.color_secondary": "<PERSON><PERSON><PERSON><PERSON> boja", "app.containers.AdminPage.SettingsPage.color_text": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.colorsTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.confirmHeader": "Jeste li sigurni da želite izbrisati ovu oznaku?", "app.containers.AdminPage.SettingsPage.contentModeration": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.ctaHeader": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Prilagođeno zaglavlje stranice | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Tekst gumba", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "<PERSON>eza gumba", "app.containers.AdminPage.SettingsPage.defaultTopic": "<PERSON><PERSON><PERSON> tema", "app.containers.AdminPage.SettingsPage.delete": "Izbriši", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Izbriši", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Ovo će obrisati temu zajedno sa svim postojećim unosima. Ova izmjena će se primijeniti na sve projekte.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Dodajte i izbrišite teme koje želite da koristite na svojoj platformi za kategorizaciju objava. Teme možete dodati određenim projektima u {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editFormTitle": "Uredi područje", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.fieldDescription": "Opis područ<PERSON>", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Ovaj opis je namijenjen internoj suradnji i ne prikazuje se korisnicima.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Naziv područja", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Unesite naziv područja za sve jezike", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Naziv koji odaberete za svako područje može se koristiti kao opcija polja za registraciju i za filtriranje projekata na početnoj stranici.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Spremi oznaku", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "<PERSON>v oznake", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Unesite naziv oznake za sve jezike", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Naziv svake teme će biti vidljiv korisnicima platforme", "app.containers.AdminPage.SettingsPage.fixedRatio": "<PERSON><PERSON> omjera", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Ova vrsta natpisa najbolje funkcionira sa slikama koje se ne bi trebale obrezivati, poput slika s tekstom, logotipa ili određenih elemenata koji su ključni za vaše građane. Ovaj natpis zamjenjuje se punim okvirom u primarnoj boji kada su korisnici prijavljeni. Boju možete postaviti u općim postavkama. Više informacija o preporučenoj upotrebi slika možete pronaći na {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "baza znanja", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "<PERSON><PERSON> pune <PERSON>irine", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Ovaj natpis proteže se preko cijele širine za odličan vizualni učinak. Slika će nastojati pokriti što više prostora, zbog čega neće biti vidljiva u svakom trenutku. Ovaj natpis možete kombinirati s preklapanjem bilo koje boje. Više informacija o preporučenoj uporabi slika možete pronaći na {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "baza znanja", "app.containers.AdminPage.SettingsPage.header": "Natpis na početnoj stranici", "app.containers.AdminPage.SettingsPage.headerDescription": "Prilagodite sliku i tekst natpisa na početnoj stranici.", "app.containers.AdminPage.SettingsPage.header_bg": "Slika natpisa", "app.containers.AdminPage.SettingsPage.helmetDescription": "Stranica s administratorskim postavkama", "app.containers.AdminPage.SettingsPage.helmetTitle": "Stranica s administratorskim postavkama", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Dodajte vlastiti sadržaj prilagodljivom odjeljku u dnu početne stranice.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Zaglavlje početne stranice | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Boja sloja preko slike", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Prozirnost sloja preko slike", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Otkrijte neprimjeren sadržaj", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Automatsko otkrivanje neprimjerenog sadržaja objavljenog na platformi.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "Dok je ova značajka omogućena, unos, prijedlozi i komentari koje su objavili sudionici bit će automatski pregledani. Objave označene kao potencijalno neprimjerene neće biti blokirane, ali će biti istaknute za pregled na stranici {linkToActivityPage} .", "app.containers.AdminPage.SettingsPage.languages": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Možete odabrati različite jezike koji će biti dostupni na vašoj platformi. Bit će potrebno kreirati sadržaj za svaki izabrani jezik.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Aktivnost", "app.containers.AdminPage.SettingsPage.logo": "Logotip", "app.containers.AdminPage.SettingsPage.noHeader": "Prenesite sliku zaglavlja", "app.containers.AdminPage.SettingsPage.no_button": "<PERSON><PERSON> gumba", "app.containers.AdminPage.SettingsPage.organizationName": "Naziv grada ili organizacije", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Navedite naziv organizacije ili grad za sve jezike.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Omogući preklapanje", "app.containers.AdminPage.SettingsPage.phone": "Telefon", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Konfiguracija platforme", "app.containers.AdminPage.SettingsPage.population": "Populacija", "app.containers.AdminPage.SettingsPage.populationMinError": "Populacija mora biti pozitivan broj.", "app.containers.AdminPage.SettingsPage.populationTooltip": "Ukupan broj stanovnika na vašem teritoriju. Ovo se koristi za izračun stope sudjelovanja. Ostavite prazno ako nije primjenjivo.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Blokiranje nepristojnih riječi", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Blokirajte unos, prijedloge i komentare koji sadrže najčešće prijavljivane uvredljive riječi", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Ovaj tekst prikazan je na početnoj stranici iznad projekata.", "app.containers.AdminPage.SettingsPage.projectsSettings": "postavke projekta", "app.containers.AdminPage.SettingsPage.projects_header": "Zaglavlje projekata", "app.containers.AdminPage.SettingsPage.registrationFields": "Polja za registraciju", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Navedite kratak opis na vrhu vašeg obrasca za registraciju.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registracija", "app.containers.AdminPage.SettingsPage.save": "Sp<PERSON>i", "app.containers.AdminPage.SettingsPage.saveArea": "Spremi područje", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Došlo je do pogreške. Pokušajte kasnije.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Us<PERSON><PERSON>h!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Vaše promjene su spremljene.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Odaberite odobravatelje", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "Odaberite područja koja će biti prikazana korisnicima nakon registracije", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Odaberite teme koje će biti prikazane korisnicima za praćenje nakon registracije", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Spremanje nije uspjelo. Pokušajte izmijeniti postavke.", "app.containers.AdminPage.SettingsPage.sign_up_button": "„Registracija“", "app.containers.AdminPage.SettingsPage.signed_in": "Gumb za registrirane posjetitelje", "app.containers.AdminPage.SettingsPage.signed_out": "Gumb za neregistrirane posjetitelje", "app.containers.AdminPage.SettingsPage.signupFormText": "Pomoćni tekst za registraciju", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Navedite kratak opis na vrhu vašeg obrasca za registraciju.", "app.containers.AdminPage.SettingsPage.statuses": "Statusi", "app.containers.AdminPage.SettingsPage.step1": "Korak za e-poštu i lozinku", "app.containers.AdminPage.SettingsPage.step1Tooltip": "<PERSON>vo je prikazano na vrhu prve stranice obrasca za registraciju (ime, e-pošta, zaporka).", "app.containers.AdminPage.SettingsPage.step2": "Korak za pitanja u vezi registracije", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Ovo je prikazano na vrhu druge stranice obrasca za registraciju (dodatna polja za registraciju).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Definirajte geografska područja koja biste željeli da koristite u okviru platforme kao što su četvrti, m<PERSON>sne zajednice, op<PERSON><PERSON> ili okruzi. Ova geografska područja možete povezati s projektima (kako bi se mogli filtrirati na početnoj stranici) ili zatražiti od sudionika da izaberu svoje prebivalište kao polje za registraciju u svrhe kreiranja pametnih grupa i definiranja prava pristupa.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Odaberite kako će korisnici vidjeti naziv vaše organizacije, odaberite jezike vaše platforme i postavite vezu do svojeg web-mjesta.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Željeni podnaslov prelazi dozvoljeno maksimalno ograničenje (90 znakova)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminologija", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Postavke su uspješno ažurirane.", "app.containers.AdminPage.SettingsPage.tabAreas1": "Podru<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabBranding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "<PERSON>i unosa", "app.containers.AdminPage.SettingsPage.tabPolicies": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Odobrenje projekta", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Statusi prijedloga", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registracija", "app.containers.AdminPage.SettingsPage.tabSettings": "Općenito", "app.containers.AdminPage.SettingsPage.tabTopics2": "Oznake", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Definirajte koju geografsku jedinicu želite koristiti za svoje projekte (npr. naselja, okruzi, općine itd.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geografsko područje", "app.containers.AdminPage.SettingsPage.titleBasic": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Željeni naslov prelazi dozvoljeno maksimalno ograničenje (35 znakova)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Ovaj natpis posebno je koristan sa slikama koje ne funkcioniraju dobro s tekstom naslova, pod<PERSON>lova ili gumba. Te stavke bit će gurnute ispod natpisa. Više informacija o preporučenoj upotrebi slika možete pronaći na {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "baza znanja", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON><PERSON> reda", "app.containers.AdminPage.SettingsPage.urlError": "URL nije valjan", "app.containers.AdminPage.SettingsPage.urlPatternError": "Unesite važeći URL.", "app.containers.AdminPage.SettingsPage.urlTitle": "Web-mjesto", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Možete dodati vez na svoje web-mjesto. Ta veza koristit će se na dnu početne stranice.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "Odaberite kako će se korisnici bez imena u profilu pojaviti na platformi. To će se dogoditi kada prava pristupa za fazu postavite na 'Potvrda putem e-pošte'. U svim sluč<PERSON>evima, nakon sudjelovanja, korisnici će moći ažurirati naziv profila koji smo za njih automatski generirali.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Prikaz korisničkog imena (samo za korisnike s potvrđenom e-poštom)", "app.containers.AdminPage.SideBar.administrator": "Administrator", "app.containers.AdminPage.SideBar.communityPlatform": "Platforma zajednice", "app.containers.AdminPage.SideBar.community_monitor": "Monitor zajednice", "app.containers.AdminPage.SideBar.customerPortal": "Portal za korisnike", "app.containers.AdminPage.SideBar.dashboard": "Nadzorna ploča", "app.containers.AdminPage.SideBar.emails": "E-poruke", "app.containers.AdminPage.SideBar.folderManager": "Upravitelj mapa", "app.containers.AdminPage.SideBar.groups": "Grupe", "app.containers.AdminPage.SideBar.guide": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inputManager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.insights": "Izvještavanje", "app.containers.AdminPage.SideBar.inspirationHub": "Središte inspiracije", "app.containers.AdminPage.SideBar.knowledgeBase": "Baza znanja", "app.containers.AdminPage.SideBar.language": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Stranice i izbornik", "app.containers.AdminPage.SideBar.messaging": "<PERSON><PERSON><PERSON> poruka", "app.containers.AdminPage.SideBar.moderation": "Aktivnost", "app.containers.AdminPage.SideBar.notifications": "Obavijesti", "app.containers.AdminPage.SideBar.processing": "O<PERSON><PERSON><PERSON><PERSON> se", "app.containers.AdminPage.SideBar.projectManager": "Voditelj projekta", "app.containers.AdminPage.SideBar.projects": "Projekti", "app.containers.AdminPage.SideBar.settings": "Postavke", "app.containers.AdminPage.SideBar.signOut": "<PERSON><PERSON><PERSON><PERSON> se", "app.containers.AdminPage.SideBar.support": "podrška", "app.containers.AdminPage.SideBar.toPlatform": "Na platformu", "app.containers.AdminPage.SideBar.tools": "<PERSON><PERSON>", "app.containers.AdminPage.SideBar.user.myProfile": "<PERSON><PERSON> profil", "app.containers.AdminPage.SideBar.users": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.workshops": "Radionice", "app.containers.AdminPage.Topics.addTopics": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.browseTopics": "Pretraži oznake", "app.containers.AdminPage.Topics.cancel": "Odustani", "app.containers.AdminPage.Topics.confirmHeader": "Jeste li sigurni da želite izbrisati ovu oznaku projekta?", "app.containers.AdminPage.Topics.delete": "Izbriši", "app.containers.AdminPage.Topics.deleteTopicLabel": "Izbriši", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Ova tema više neće moći biti dodjeljivana objavama u sklopu ovog projekta.", "app.containers.AdminPage.Topics.inputForm": "Obrazac za unos", "app.containers.AdminPage.Topics.lastTopicWarning": "Potrebna je barem jedna oznaka. Ako ne želite koristiti ozna<PERSON>, možete ih onemogućiti putem {ideaFormLink} kartice.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Možete dodati ili obrisati oznake koje se dodjeljuju objavama u okviru ovog projekta.", "app.containers.AdminPage.Topics.remove": "Ukloni", "app.containers.AdminPage.Topics.title": "Oznake projekta", "app.containers.AdminPage.Topics.topicManager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.topicManagerInfo": "<PERSON><PERSON> do<PERSON>ti još proje<PERSON>, to možete učiniti ovdje {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Dodaj novu grupu", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Naziv grupe", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Navedite naziv grupe", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Kreirajte ručnu grupu", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Kakav tip grupe trebate?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Spremi grupu", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Kreirajte ručnu grupu", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Kreirajte pametnu grupu", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Saznajte više o grupama", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Možete odabrati korisnike iz pregleda i dodati ih u ovu grupu.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Možete odrediti uvjete čije će ispunjenje automatski dodati korisnike u ovu grupu.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Ručna grupa", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Pametna grupa", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Još uvijek nema nikoga u grupi", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Pogledajte {allUsersLink} kako biste ručno dodali korisnike.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "<PERSON>ema korisnika koji se podudaraju s vašom pretragom", "app.containers.AdminPage.Users.GroupsPanel.select": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Izvezi sve", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Izvoz korisnika u grupi", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Izvoz je odabran", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Jeste li sigurni da želite izbrisati ovu grupu?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Došlo je do pogreške prilikom dodavanja korisnika u grupe, pokušajte kasnije.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Ukloni iz grupe", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Izbrisati odabrane korisnike iz ove grupe?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Došlo je do pogreške prilikom brisanja korisnika iz grupe, pokušajte kasnije.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Dodajte korisnike u grupu", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.add": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.addAnswer": "Dodajte odgovor", "app.containers.AdminPage.groups.permissions.addQuestion": "Dodajte demografska pitanja", "app.containers.AdminPage.groups.permissions.answerChoices": "Izbori odgovora", "app.containers.AdminPage.groups.permissions.answerFormat": "Format odgovora", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "<PERSON><PERSON> se dati barem jedan i<PERSON>bor", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "Ovaj korisnik moderira mapu koja sadrži ovaj projekt. Da biste uklonili njihova prava moderatora za ovaj projekt, možete opozvati njihova prava mape ili premjestiti projekt u drugu mapu.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Napravi novo pitanje", "app.containers.AdminPage.groups.permissions.createAQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.defaultField": "Zadano polje", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Izbriši", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Izbriši", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Navedite naslov za sve opcije", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "Da<PERSON>ne (potvrdni okvir)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Datum", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Dug odgovor", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Višestruki izbor (odaberi više)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Numerička vrijednost", "app.containers.AdminPage.groups.permissions.fieldType_select": "Višestruki iz<PERSON> (odaberite jedan)", "app.containers.AdminPage.groups.permissions.fieldType_text": "Kratak odgovor", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Promjena detaljnih dopuštenja nije dio vaše licence. Obratite se svom GovSuccess Manageru da saznate više o tome.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Jeste li sigurni da želite ukloniti ovu grupu iz projekta?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Odaberite jednu ili više grupa", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {<PERSON><PERSON>} one {1 član} other {{count} <PERSON><PERSON><PERSON>}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Molimo ispunite naslov na svim jezicima", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Jeste li sigurni?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Nisu pronađeni voditelji projekata", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Ništa nije prikazano jer nema radnji koje korisnici mogu obaviti u ovom projektu.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Samo administratori mogu kreirati novo pitanje.", "app.containers.AdminPage.groups.permissions.option1": "opcija 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Pozivnica na čekanju", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Tko može označiti dokument?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Tko se može prijaviti za sudjelovanje na događaju?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "<PERSON><PERSON> može komentirati unose?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Tko može komentirati prijedloge?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Tko može objaviti prijedlog?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "<PERSON><PERSON> može reagirati na unose?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "<PERSON><PERSON> može podnijeti unose?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Tko može sudjelovati u anketi?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "<PERSON><PERSON> može ispuniti anketu?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Tko može volontirati?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Tko može glasovati o prijedlozima?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "<PERSON><PERSON> može glas<PERSON>?", "app.containers.AdminPage.groups.permissions.questionDescription": "<PERSON><PERSON> pitan<PERSON>", "app.containers.AdminPage.groups.permissions.questionTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.save": "Sp<PERSON>i", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Došlo je do pogreške. Pokušajte kasnije.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Us<PERSON><PERSON>h!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Vaše promjene su spremljene.", "app.containers.AdminPage.groups.permissions.select": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.selectValueError": "Odaberite vrstu odgovora", "app.containers.AdminPage.new.createAProject": "Napravite projekt", "app.containers.AdminPage.new.fromScratch": "Od nule", "app.containers.AdminPage.phase.methodPicker.addOn1": "Dodatak", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "Uvidi koje pokreće AI", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Pomozite sudionicima da iznesu slaganje i neslaganje, jednu ideju po jednu.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Pronađite zajednički jezik", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Ugradite interaktivni PDF i skupljajte komentare i povratne informacije s Konveiom.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Prikupite povratne informacije o dokumentu", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Ugradite anketu treće strane", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "<PERSON><PERSON><PERSON> pregled", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Iskoristite kolektivnu inteligenciju svojih korisnika. Pozovite ih da podnesu, raspravite ideje i/ili dajte povratne informacije na javnom forumu.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Prikupite informacije i povratne informacije u javnosti", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Dijeljenje informacija", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Nedostaju uvidi koje pokreće AI unutar platforme", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Nedostaje izvješćivanje unutar platforme te vizualizacija i obrada podataka", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Povežite se s alatom za izradu izvješća unutar platforme", "app.containers.AdminPage.phase.methodPicker.logic1": "Logika", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Širok raspon vrsta pitanja", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Dopustite sudionicima da učitaju ideje uz ograničenje vremena i glasova.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, peticije ili inicijative", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Postavite kratki upitnik s višestrukim odgovorima.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "Pružite informacije korisnicima, vizualizirajte rezultate iz drugih faza i izradite izvješća bogata podacima.", "app.containers.AdminPage.phase.methodPicker.survey1": "Pregled", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Shvatite potrebe i razmišljanja svojih korisnika putem širokog raspona tipova privatnih pitanja.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Mogućnosti ankete", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Zamolite korisnike da volontiraju za aktivnosti i ciljeve ili pronađite sudionike za panel.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Angažirajte sudionike ili volontere", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Odaberite metodu glasovanja i neka korisnici odaberu prioritet između nekoliko različitih opcija.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Provedite glasovanje ili vježbu određivanja prioriteta", "app.containers.AdminPage.projects.all.all": "svi", "app.containers.AdminPage.projects.all.createProjectFolder": "Nova mapa", "app.containers.AdminPage.projects.all.existingProjects": "Postojeći projekti", "app.containers.AdminPage.projects.all.homepageWarning1": "Pomoću ove stranice postavite redoslijed projekata u padajućem izborniku \"Svi projekti\" u navigacijskoj traci. Ako na svojoj početnoj stranici koristite widgete \"Objavljeni projekti i mape\" ili \"Projekti i mape (naslijeđeno)\", redoslijed projekata u tim widgetima također će biti određen redoslijedom koji ovdje postavite.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "Ovdje će se pojaviti projekti u kojima ste voditelj projekta.", "app.containers.AdminPage.projects.all.noProjects": "<PERSON><PERSON> prona<PERSON> proje<PERSON>.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Samo administratori mogu kreirati mape projekta.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projekti i mape", "app.containers.AdminPage.projects.all.publishedTab": "Objavljeno", "app.containers.AdminPage.projects.all.searchProjects": "Pretražite projekte", "app.containers.AdminPage.projects.all.yourProjects": "Vaši projekti", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "AI analiza", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Točnost: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "Pitaj<PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "Umjesto <PERSON>, možete postavljati relevantna pitanja svojim podacima. Ova značajka nije uključena u vaš trenutni plan. Razgovarajte sa svojim upraviteljem uspjeha vlade ili administratorom da ga otključate.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Pitati <PERSON>anje", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "Ovaj uvid uključuje sljedeća pitanja:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "Izbriši <PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "Jeste li sigurni da želite izbrisati ovo pitanje?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Izbriši sažetak", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Jeste li sigurni da želite izbrisati ove sažetke?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Vaši tekstualni sažeci bit će prikazani ovdje, ali trenutno još nemate nijedan.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "Za početak kliknite gornji gumb Automatsko sažimanje.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "o<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Postavljanje pitanja o manje unosa dovodi do veće točnosti. Smanjite trenutni odabir unosa pomoću oznaka, pretraživanja ili demografskih filtara.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "Pit<PERSON>ja za", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Pitanje za sve ulaze", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Ocijenite kvalitetu ovog uvida", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "<PERSON><PERSON>i filtre", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Rezimirati", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Sažetak za", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Sažetak za sve ulaze", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "Hvala vam na povratnim informacijama", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "AI ne može obraditi toliko unosa odjednom. Podijelite ih u manje skupine.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "Možete rezimirati najviše 30 unosa odjednom na vašem trenutnom planu. Razgovarajte sa svojim GovSuccess Managerom ili administratorom da otključate više.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "razumijem", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Naša platforma omogućuje vam istraživanje temeljnih tema, sažetak podataka i ispitivanje različitih perspektiva. Ako tražite konkretne odgovore ili uvide, razmislite o korištenju značajke \"Postavite pitanje\" kako biste dublje zaronili izvan sažetka.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "<PERSON><PERSON><PERSON>, AI može povremeno generirati informacije koje nisu bile eksplicitno prisutne u izvornom skupu podataka.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Halucinacije:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "AI može istaknuti određene teme ili ideje više od drugih, potencijalno iskrivljujući cjelokupno tumačenje.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Pretjerivanje:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Naš sustav je optimiziran za rukovanje 20-200 dobro definiranih unosa za najtočnije rezultate. Kako se količina podataka povećava izvan ovog raspona, sažetak može postati više razine i generaliziraniji. To ne znači da umjetna inteligencija postaje \"manje točna\", već da će se usredotočiti na šire trendove i obrasce. Za nijansiranije uvide preporučujemo korištenje značajke (automatskog) označavanja za segmentiranje većih skupova podataka u manje podskupove kojima je lakše upravljati.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Količina podataka i točnost:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "Preporučujemo korištenje sažetaka koje je generirala umjetna inteligencija kao početnu točku za razumijevanje velikih skupova podataka, ali ne kao završnu riječ.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "<PERSON><PERSON> radi<PERSON> s <PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Dodaj odabrane ulaze u oznaku", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "Ova značajka nije uključena u vaš trenutni plan. Razgovarajte sa svojim upraviteljem uspjeha vlade ili administratorom da ga otključate.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "<PERSON><PERSON> <PERSON>nake", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "<PERSON><PERSON>, ja ću to u<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Želite li automatski dodijeliti unose svojoj oznaci?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "Post<PERSON>ji <b>r<PERSON><PERSON><PERSON><PERSON><PERSON> metoda</b> za automatsko dodjeljivanje ulaza oznakama.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Koristite <b>gumb za automatsko označavanje</b> za pokretanje željene metode.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Kliknite na oznaku da je dodijelite trenutno odabranom unosu.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "Da, automatsko označavanje", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Automatsko označavanje", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Računalo automatski izvodi automatske oznake. Možete ih promijeniti ili ukloniti u svakom trenutku.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Automatsko označavanje", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Unosi koji su već povezani s ovim oznakama neće se ponovno klasificirati.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "Klasifikacija se temelji isključivo na nazivu oznake. Odaberite relevantne ključne riječi za najbolje rezultate.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Oznake: <PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "Vi kreirate oznake i ručno dodijelite nekoliko ulaza kao primjer, računalo dodjeljuje ostatak", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Oznake: Primjerom", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "<PERSON><PERSON><PERSON><PERSON> kao \"Oznake: prema oznaci\", ali s povećanom preciznošću jer uvježbavate sustav dobrim primjerima.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "Vi kreirate oznake, rač<PERSON>lo dodjeljuje ulaze", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Oznake: <PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "Ovo dobro funkcionira kada imate unaprijed definiran skup oznaka ili kada vaš projekt ima ograničen opseg u smislu oznaka.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Otkrijte unose sa značajnim omjerom nesviđanja/sviđanja", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Kontroverzno", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Izbriši oznaku", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "Jeste li sigurni da želite izbrisati ovu oznaku?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Ne prikazuj ovo više", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "Dodajte ime", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Odaberite maksimalno 9 oznaka između kojih želite da se raspodijele unosi.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "Klasifikacija se temelji na ulazima koji su trenutno dodijeljeni oznakama. Računalo će pokušati slijediti vaš primjer.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Oznake: Primjerom", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "<PERSON><PERSON> nemate nijednu prilagođenu oz<PERSON>.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "Računalo automatski otkriva oznake i dodjeljuje ih vašim unosima.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Oznake: Potpuno automatizirano", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "Dobro funkcionira kada vaši projekti pokrivaju širok raspon oznaka. Dobro mjesto za početak.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "<PERSON><PERSON> označiti?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Unosi bez oznaka", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Otkrijte jezik svakog unosa", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "Pokreni", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "<PERSON><PERSON> akt<PERSON>ltara", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Upotrijebite oznake za podjelu i filtriranje unosa kako biste napravili točnije ili ciljanije sažetke.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "ostalo", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Oznake: Oznake platforme", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Dodijelite postojeće oznake platforme koje je autor odabrao prilikom objave", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Preporučeno", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Ime", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "Odaberi sve", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Dodijelite pozitivan ili negativan osjećaj svakom unosu, izvedenom iz teksta", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Otkrivanje oz<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Koristite trenutne filtre", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "Ko<PERSON> unose želite označiti?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Zadatak automatskog označavanja", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Kontroverzno", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Završeno u", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "Primjerom", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "U nastajanju", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "<PERSON> et<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "NLP oznaka", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "Nema nedavnih izvršenih zadataka umjetne inteligencije", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Oznaka platforme", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "U redu čekanja", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Započelo u", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Zadatak sažimanja", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Pokrenuto u", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "Iznad", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "svi", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "Autor", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "Ispod", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "Prebivalište", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "Iz", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "Spol", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "Do", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Dodaj u analizu", "app.containers.AdminPage.projects.project.analysis.anonymous": "Anonimni unos", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Autori po godinama", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Autori po prebivalištu", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Pozadinski poslovi", "app.containers.AdminPage.projects.project.analysis.comments": "Komentari", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "Grafikon prebivališta je prevelik za prikaz", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "Sakrij prazne odgovore", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Odgovori", "app.containers.AdminPage.projects.project.analysis.end": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.filter": "Prikaži samo unose s ovom vrijednošću", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Sakrij odgovore bez odgovora", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Automatski uvidi", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Vrijednosti stupaca", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "<PERSON><PERSON><PERSON> {count} primjeraka ove kombinacije.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "Nesviđanja", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Istražiti", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "lažno", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "sviđ<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Sljedeća toplinska karta", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Sljedeć<PERSON> uvid", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Nije statistički značajan uvid.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Automatski uvidi nisu dostupni za projekte s manje od 30 sudionika.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "Sudionici", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "<PERSON>thodna toplinska karta", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "<PERSON><PERSON><PERSON><PERSON> uvid", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "Vrijednosti reda", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Statistički značajan uvid.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Rezimirati", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Oznake za analizu", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "jed<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "Pogledajte sve uvide", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Pregledajte automatske uvide", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Unosi bez oznaka", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "Učitana je nevažeća datoteka oblika i ne može se prikazati.", "app.containers.AdminPage.projects.project.analysis.limit": "Ograničiti", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "Glavno pitanje", "app.containers.AdminPage.projects.project.analysis.manageInput": "Upravljanje unosom", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Sljedeći grafikon", "app.containers.AdminPage.projects.project.analysis.noAnswer": "<PERSON>ema odgovora", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "Nema odgovora.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "Nije učitana datoteka oblika.", "app.containers.AdminPage.projects.project.analysis.noInputs": "Nijedan unos ne odgovara vašim trenutnim filtrima", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Prethodni grafikon", "app.containers.AdminPage.projects.project.analysis.reactions": "Reak<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.remove": "Ukloniti", "app.containers.AdminPage.projects.project.analysis.removeFilter": "Uklonite filter", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "Uklonite filter", "app.containers.AdminPage.projects.project.analysis.search": "tra<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Datoteke oblika ovdje se prikazuju u formatu GeoJSON. Ka<PERSON> taka<PERSON>, stil u izvornoj datoteci možda se neće ispravno prikazati.", "app.containers.AdminPage.projects.project.analysis.start": "Početak", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Članak podrške", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "Nepoznato", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "<PERSON><PERSON><PERSON> sva <PERSON>ja", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "Pogledajte odabrana <PERSON>", "app.containers.AdminPage.projects.project.analysis.votes": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.copied": "Kopirano u međuspremnik", "app.containers.AdminPage.widgets.copyToClipboard": "<PERSON><PERSON><PERSON>j kod", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "<PERSON><PERSON>raj HTML kod", "app.containers.AdminPage.widgets.fieldAccentColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> boja", "app.containers.AdminPage.widgets.fieldBackgroundColor": "<PERSON><PERSON> pozadine widgeta", "app.containers.AdminPage.widgets.fieldButtonText": "Tekst gumba", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Pridružite se sada", "app.containers.AdminPage.widgets.fieldFont": "Font", "app.containers.AdminPage.widgets.fieldFontDescription": "Ovo mora biti postojeći naziv fonta iz {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "<PERSON><PERSON><PERSON><PERSON> (piksela)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Podnaslov zaglavlja", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Možete reći što mislite", "app.containers.AdminPage.widgets.fieldHeaderText": "Naslov zaglavlja", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "<PERSON>ša platforma za sudjelovanje", "app.containers.AdminPage.widgets.fieldHeight": "<PERSON><PERSON><PERSON> (piksela)", "app.containers.AdminPage.widgets.fieldInputsLimit": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldProjects": "Projekti", "app.containers.AdminPage.widgets.fieldRelativeLink": "Veze do", "app.containers.AdminPage.widgets.fieldShowFooter": "<PERSON><PERSON><PERSON><PERSON> gumb", "app.containers.AdminPage.widgets.fieldShowHeader": "Prikaži zaglavlje", "app.containers.AdminPage.widgets.fieldShowLogo": "Prikaži logotip", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Boja pozadine web-mjesta", "app.containers.AdminPage.widgets.fieldSort": "Sortirano po", "app.containers.AdminPage.widgets.fieldTextColor": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldTopics": "Oznake", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Početna stranica", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Možete kopirati ovaj HTML kod i zalijepiti ga na dio web-mjesta na koji želite dodati widget.", "app.containers.AdminPage.widgets.htmlCodeTitle": "HTML kod widgeta", "app.containers.AdminPage.widgets.previewTitle": "Pretpregled", "app.containers.AdminPage.widgets.settingsTitle": "Postavke", "app.containers.AdminPage.widgets.sortNewest": "Najnovije", "app.containers.AdminPage.widgets.sortPopular": "Popular<PERSON>", "app.containers.AdminPage.widgets.sortTrending": "<PERSON> <PERSON>u", "app.containers.AdminPage.widgets.subtitleWidgets": "Možete izraditi widget, prilagoditi ga i dodati na vlastitu web stranicu kako biste privukli ljude na ovu platformu.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Dimenzije", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Zaglavlje i podnožje", "app.containers.AdminPage.widgets.titleInputSelection": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleStyle": "Stil", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Sp<PERSON>i", "app.containers.ContentBuilder.homepage.PageTitle": "Početna stranica", "app.containers.ContentBuilder.homepage.SaveError": "Nešto nije u redu prilikom spremanja početne stranice.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "<PERSON><PERSON> stu<PERSON>ca", "app.containers.ContentBuilder.homepage.bannerImage": "Slika natpisa", "app.containers.ContentBuilder.homepage.bannerSubtext": "Podtekst bannera", "app.containers.ContentBuilder.homepage.bannerText": "Tekst natpisa", "app.containers.ContentBuilder.homepage.button": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.chooseLayout": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Prilagođavanje postavki osim slike i teksta na početnoj stranici nije uključeno u vašu trenutnu licencu. Za više informacija obratite se svom GovSuccess menadžeru.", "app.containers.ContentBuilder.homepage.customized_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Tekst gumba", "app.containers.ContentBuilder.homepage.customized_button_url_label": "<PERSON>eza gumba", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Prikazuje sljedeća 3 nadolazeća događaja na vašoj platformi.", "app.containers.ContentBuilder.homepage.eventsDescription": "Prikazuje sljedeća 3 nadolazeća događaja na vašoj platformi.", "app.containers.ContentBuilder.homepage.fixedRatio": "Banner s fiksnim omjerom", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Ova vrsta bannera najbolje funkcionira sa slikama koje se ne bi trebale izrezati, poput slika s tekstom, logotipom ili određenim elementima koji su ključni za vaše građane. Ovaj natpis zamjenjuje se punim okvirom u primarnoj boji kada su korisnici prijavljeni. Ovu boju možete postaviti u općim postavkama. Više informacija o preporučenoj upotrebi slika možete pronaći na našoj {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "baza znanja", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "Banner pune širine", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Ovaj banner se proteže preko cijele širine za odličan vizualni učinak. Slika će pokušati pokriti što više prostora, zbog čega neće biti vidljiva u svakom trenutku. Ovaj banner možete kombinirati s preklapanjem bilo koje boje. Više informacija o preporučenoj upotrebi slika možete pronaći na našoj {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "baza znanja", "app.containers.ContentBuilder.homepage.imageOverlayColor": "Boja preklapanja slike", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Neprozirnost sloja slike", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "Neispravan URL", "app.containers.ContentBuilder.homepage.no_button": "Bez gumba", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Neregistrirani k<PERSON>", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Omogući preklapanje", "app.containers.ContentBuilder.homepage.projectsDescription": "Da biste konfigurirali redoslijed prikaza svojih projekata, promijenite njihov redoslijed na {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "Stranica Projekti", "app.containers.ContentBuilder.homepage.registeredUsersView": "Registrirani k<PERSON>i", "app.containers.ContentBuilder.homepage.showAvatars": "Prikaz avatara", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Pokažite profilne slike sudionika i njihov broj neregistriranim posjetiteljima", "app.containers.ContentBuilder.homepage.sign_up_button": "Prijavite se", "app.containers.ContentBuilder.homepage.signedInDescription": "Ovako banner vide registrirani korisnici.", "app.containers.ContentBuilder.homepage.signedOutDescription": "Ovako posjetitelji koji nisu registrirani na platformi vide banner.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "Ovaj natpis je posebno koristan sa slikama koje ne funkcioniraju dobro s tekstom iz naslova, podnaslova ili gumba. Ove stavke bit će gurnute ispod natpisa. Više informacija o preporučenoj upotrebi slika možete pronaći na našem {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "baza znanja", "app.containers.ContentBuilder.homepage.twoRowLayout": "<PERSON><PERSON> reda", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "<PERSON><PERSON>na <PERSON> (pikseli)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "Visina na kojoj želite da se vaš ugrađeni sadržaj pojavi na stranici (u pikselima).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Kratak opis sadržaja koji ugrađujete", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "Korisno je pružiti ove informacije korisnicima koji se oslanjaju na čitač zaslona ili drugu pomoćnu tehnologiju.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Adresa web stranice", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "Puni URL web stranice koju želite ugraditi.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Prikažite sadržaj s vanjske web stranice na svojoj stranici u HTML iFrameu. Imajte na umu da se ne može ugraditi svaka stranica. Ako imate problema s ugradnjom stranice, provjerite s vlasnikom stranice je li konfigurirana za dopuštanje ugradnje.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Posjetite našu stranicu za podršku", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "<PERSON><PERSON><PERSON><PERSON>, ovaj sadr<PERSON>aj nije moguće ugraditi. {visitLinkMessage} da biste saznali više.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Unesite valjanu web adresu, na primjer https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Ugraditi", "app.containers.admin.ContentBuilder.accordionMultiloc": "Ha<PERSON><PERSON>", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Ot<PERSON>eno prema zadanim postavkama", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Tekst", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Ovo je proširivi harmonikaški sadržaj. Možete ga uređivati i oblikovati pomoću uređivača na ploči s desne strane.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Titula", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Naslov harmonike", "app.containers.admin.ContentBuilder.buttonMultiloc": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.delete": "Izbriši", "app.containers.admin.ContentBuilder.error": "p<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.errorMessage": "Pojavila se pogreška na {locale} sad<PERSON>žaj<PERSON>, ispravite problem kako biste mogli spremiti promjene", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Sakrij avatare sudjelovanja", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "Ovo je tromjesečno istraživanje koje je u tijeku i prati vaše mišljenje o upravljanju i javnim uslugama.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Ispunite anketu", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Pomozite nam da vas bolje služimo", "app.containers.admin.ContentBuilder.homepage.default": "zadano", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Poziv na akciju", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Opis", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "URL primarnog gumba", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Tekst primarnog gumba", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "URL sekundarnog gumba", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Tekst sekundarnog gumba", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Titula", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Banner početne stranice", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Banner početne stranice", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Kartice sa slikama i tekstom", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 stupac", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projekti", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Omogućite prijedloge u odjeljku \"Prijedlozi\" na administratorskoj ploči kako biste ih otključali na početnoj stranici", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Prijedlozi", "app.containers.admin.ContentBuilder.imageMultiloc": "Slika", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Kratki opis slike", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "Dodavanje \"alternativnog teksta\" za slike važno je kako bi vaša platforma bila dostupna korisnicima koji koriste čitače zaslona.", "app.containers.admin.ContentBuilder.participationBox": "Kutija za sudjelovanje", "app.containers.admin.ContentBuilder.textMultiloc": "Tekst", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 stupca", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 stupca", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 stupca s 30 % odnosno 60 % širine", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 stupca s 60 % odnosno 30 % širine", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 jednaka stupca", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "<PERSON><PERSON><PERSON><PERSON>a", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Faza ideje", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "<PERSON><PERSON> dos<PERSON>h inputa za ovaj projekt ili fazu.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Prikaži više", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Ukupno unosa: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Sažmi dugačak tekst", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "<PERSON><PERSON> dos<PERSON>h inputa za ovaj projekt ili fazu.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Odaberite fazu", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Autor", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "Reak<PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Titula", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Stopa registracije", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Prijave", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Vremenska traka sudionika", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Imajte na umu da brojevi sudjelovanja možda neće biti potpuno točni jer su neki podaci zabilježeni u vanjskoj anketi koju ne pratimo.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Grafikon", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "<PERSON><PERSON><PERSON> da<PERSON>a", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.noData": "<PERSON><PERSON> dos<PERSON> podataka za filtre koje ste odabrali.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Izvori prometa", "app.containers.admin.ReportBuilder.charts.users": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.usersByAge": "Korisnici po starosti", "app.containers.admin.ReportBuilder.charts.usersByGender": "<PERSON><PERSON><PERSON><PERSON> prema spolu", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Vremenska traka posjetitelja", "app.containers.admin.ReportBuilder.managerLabel1": "Voditelj projekta", "app.containers.admin.ReportBuilder.periodLabel1": "Razdoblje", "app.containers.admin.ReportBuilder.projectLabel1": "Projekt", "app.containers.admin.ReportBuilder.quarterReport1": "Izvješće Monitor zajednice: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Početak", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Kupite 1 dodatno sjedalo", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Potvrdi", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Jeste li sigurni da jednoj osobi želite dati upraviteljska prava?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Dajte upraviteljska prava", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Dosegli ste ograničenje uključenih sjedala u svoj plan, {noOfSeats} dodatna {noOfSeats, plural, one {sjedi<PERSON><PERSON>} other {sjedala}} bit će dodan.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Dodaj status", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Zadani statusi se ne mogu brisati.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Izbriši", "app.containers.admin.ideaStatuses.all.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Uredi status", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Statusi koji su trenutno dodijeljeni unosima ne mogu se izbrisati. Možete ukloniti/promijeniti status sa postojećih unosa na kartici {manageTab}.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Ovaj status nije moguće izbrisati ili premjestiti.", "app.containers.admin.ideaStatuses.all.manage": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Konfiguriranje prilagođenih statusa unosa nije uključeno u vaš trenutni plan. Razgovarajte sa svojim upraviteljem uspjeha vlade ili administratorom da ga otključate.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Upravljajte statusom koji se može dodijeliti unosu sudionika unutar projekta. Status je javno vidljiv i pomaže u informiranju sudionika.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Upravljajte statusom koji se može dodijeliti prijedlozima unutar projekta. Status je javno vidljiv i pomaže u informiranju sudionika.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Uredite statuse unosa", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Uredite statuse prijedloga", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Odabrano za implementaciju ili sljedeće korake", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Odobreno", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Pružene službene povratne informacije", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Odgo<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.category": "Kategorija", "app.containers.admin.ideaStatuses.form.categoryDescription": "Odaberite kategoriju koja najbolje predstavlja vaš status. Ovaj izbor pomoći će našem analitičkom alatu u preciznijem obrađivanju i analizi objava.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Ne podudara se ni s jednom drugom opcijom", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "Ostalo", "app.containers.admin.ideaStatuses.form.fieldColor": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescription": "Opis statusa", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Unesite opis statusa za sve jezike", "app.containers.admin.ideaStatuses.form.fieldTitle": "Naziv statusa", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Unesite naziv statusa za sve jezike", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Uspješno implementirano", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Implementirano", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "Prijedlog ne ispunjava uvjete", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "Ne ispunjava uvjete", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Neprihvatljivo ili nije odabrano za daljnje postupanje", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.saveStatus": "Spremi status", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Razmatra se za realizaciju u sljedećim koracima", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Razmat<PERSON> se", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "<PERSON><PERSON><PERSON>, ali još nije o<PERSON>", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Prikazano", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Upravljajte unosima i njihovim statusima.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Voditelj unosa | Platforma za sudjelovanje organizacije {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Pružite povratne informacije, dodajte teme i premještajte unose iz jednog projekta u drugi", "app.containers.admin.ideas.all.inputManagerPageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideas.all.tabOverview": "Pregled", "app.containers.admin.import.importInputs": "U<PERSON>z unosa", "app.containers.admin.import.importNoLongerAvailable3": "Ova značajka ovdje više nije dostupna. Za uvoz inputa u fazu ideje, idite na fazu i odaberite \"Uvezi\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 dodatno administrativno mjesto} other {# dodatnih administratorskih mjesta}} i {managerSeats, plural, one {1 dodatno menadžersko mjesto} other {# dodatnih menadžerskih mjesta}} dodat će se preko ograničenja.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {Dodat će se 1 dodatno administratorsko mjesto preko ograničenja} other {Dodat će se # dodatnih administratorskih mjesta preko ograničenja}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {Dodat će se 1 dodatno menadžersko mjesto preko ograničenja} other {Dodat će se # dodatnih menadžerskih mjesta preko ograničenja}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Potvrdite i pošaljite pozivnice", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Potvrdite utjecaj na korištenje sjedala", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Dosegli ste ograničenje dostupnih mjesta unutar vašeg plana.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Administratori i voditelji ovog projekta", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Samo administratori i suradnici", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Samo administratori platforme, upravitelji mapa i voditelji projekata mogu poduzeti radnju", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Bilo tko", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "Svatko uključujući i neregistrirane korisnike može sudjelovati.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Korisnici u određenim korisničkim skupinama mogu sudjelovati. Grupama korisnika možete upravljati u kartici „Korisnici“.", "app.containers.admin.project.permissions.viewingRightsTitle": "Tko može vidjeti ovaj projekt?", "app.containers.phaseConfig.enableSimilarInputDetection": "Omogući otkrivanje sličnog unosa", "app.containers.phaseConfig.similarInputDetectionTitle": "Detekcija sličnog unosa", "app.containers.phaseConfig.similarInputDetectionTooltip": "Pokažite sudionicima sličan unos dok tipkaju kako biste izbjegli duplikate.", "app.containers.phaseConfig.similarityThresholdBody": "<PERSON><PERSON> (tijelo)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "Ovo kontrolira koliko dva opisa moraju biti slična da bi bila označena kao slična. Koristite vrijednost između 0 (strogo) i 1 (blago). Niže vrijednosti vraćaju manje, ali točnije podudaranje.", "app.containers.phaseConfig.similarityThresholdTitle": "<PERSON><PERSON> (naslov)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "Ovo kontrolira koliko dva naslova moraju biti slična da bi bila označena kao slična. Koristite vrijednost između 0 (strogo) i 1 (blago). Niže vrijednosti vraćaju manje, ali točnije podudaranje.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "<PERSON>va je značajka dostupna kao dio ponude ranog pristupa do 30. lipnja 2025. <PERSON><PERSON> je želite nastaviti koristiti nakon tog datuma, obra<PERSON> se svom upravitelju za uspjeh vlade ili administratoru kako biste razgovarali o opcijama aktivacije.", "app.containers.survey.sentiment.noAnswers2": "Trenutačno nema odgovora.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 komentara} one {1 komentar} other {# komentara}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Sudionici su korisnici ili posjetitelji koji su sudjelovali u projektu, obja<PERSON> ili stupili u interakciju s prijedlogom ili su prisustvovali događajima.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "Sudionici", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Stopa sudjelovanja", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Postotak posjetitelja koji postaju sudionici.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "<PERSON><PERSON><PERSON> broj su<PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Automatske kampanje", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automatske e-poruke", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "Od {quantity} kampanja", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Kampanje", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Prilagođene kampanje", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Prilagođene e-poruke", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "E-poruke", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Ukupno poslano e-poruka", "app.modules.commercial.analytics.admin.components.Events.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Uku<PERSON><PERSON> dodano dog<PERSON>", "app.modules.commercial.analytics.admin.components.Events.upcoming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "P<PERSON>h<PERSON>ć<PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Pozivnice", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Na čekanju", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Ukupno poslano pozivnica", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Otvorite voditelja unosa", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Aktivno", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Projekti koji nisu arhivirani i koji su vidljivi u tablici „Aktivni“ na početnoj stranici", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Nacrti projekata", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Ovdje se broje svi arhivirani projekti i aktivni projekti s vremenske linije koji su završeni", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projekti", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Ukupno projekata", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "B<PERSON>j projekata koji su vidljivi na platformi", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Nove registracije", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Postotak registracije", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Postotak posjetitelja koji postaju registrirani korisnici.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registracije", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Ukupno registracija", "app.modules.commercial.analytics.admin.components.Tab": "Posjetitelji", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Prethodnih 30 dana:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Prethodnih 7 dana:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Prikaza stranica po posjetu", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "<PERSON><PERSON><PERSON><PERSON> pos<PERSON>a", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Posjetitelji", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "„Posjetitelji“ su broj jedinstvenih posjetitelja. Ako osoba posjeti platformu više puta, broji se samo jednom.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Posjeti", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "„Posjeti“ su broj sesija. Ako osoba posjeti platformu više puta, broji se svaki posjet.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Ju<PERSON>er:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "<PERSON><PERSON><PERSON> p<PERSON>jet<PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Postotak posjetitelja", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "Preporučitelj", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "Kliknite ovdje", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Referenti", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "Popis preporučitelja pogledajte ovdje {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Posjetitelji", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Izvori prometa", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Posjeti", "app.modules.commercial.analytics.admin.components.totalParticipants": "Ukupno sudionika", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Još nema podataka o posjetiteljima.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Promijenili smo način prikupljanja i prikaza podataka o posjetiteljima. Kao rezultat toga, podaci o posjetiteljima su točniji i dostupno je više vrsta podataka, a istovremeno su i dalje u skladu s GDPR-om. Iako podaci korišteni za vremensku crtu posjetitelja sežu dulje unatrag, podatke za \"Trajanje posjeta\", \"Preglede stranica po posjetu\" i ostale grafikone počeli smo prikupljati tek u studenom 2024., tako da prije toga podaci nisu dostupni. Stoga, ako odaberete podatke prije studenog 2024., imajte na umu da neki grafikoni mogu biti prazni ili izgledati čudno.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Dostavljeno e-poruka kroz vrijeme", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Sudionici tijekom vremena", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Registracija kroz vrijeme", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Datum", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistika", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "<PERSON><PERSON>ukupna statistika", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Posjeta i posjetitelja kroz razdoblje", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Ukupno kroz razdoblje", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Kampanje", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "<PERSON><PERSON><PERSON> p<PERSON>jet<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Postotak posjeta", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "Preporučitelj", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Web-stranice s preporukama", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Tražilice", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Društvene m<PERSON>že", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO preusmjeravanja", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "<PERSON><PERSON><PERSON> prometa", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Web-mjesta", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Ovu zastavicu sadržaja možete ukloniti odabirom ove stavke i klikom na gumb za uklanjanje pri vrhu. Ona će se ponovo pojaviti na karticama pregledanog i nepregledanog sadržaja", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Automatski je otkriven neprimjereni sadržaj.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Nema objava prijavljenih za pregled od strane zajednice niti označenih neprimjerenim sadržajem od strane našeg sustava za obradu prirodnog jezika", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Ukloni {numberOfItems, plural, one {upozorenje na sadržaj} other {# upozorenja na sadržaj}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Prijavljeno kao neprimjereno od strane korisnika platforme.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Upozorenja o sadržaju", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Alat za izgradnju izvješća", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Stranice prikazane u navigacijskoj traci", "app.modules.navbar.admin.containers.addProject": "Dodajte projekt na navigacijsku traku", "app.modules.navbar.admin.containers.createCustomPageButton": "Kreiraj prilagođenu stranicu", "app.modules.navbar.admin.containers.deletePageConfirmation": "Jeste li sigurni da želite izbrisati ovu stranicu? Ova radnja ne može se poništiti. Također možete ukloniti stranicu iz navigacijske trake ako je još uvijek niste spremni izbrisati.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Na navigacijsku traku možete dodati najviše 5 stavki", "app.modules.navbar.admin.containers.pageHeader": "Stranice i izbornik", "app.modules.navbar.admin.containers.pageSubtitle": "Na vašoj navigacijskoj traci može biti prikazano do pet stranica, pored Početne i projektnih stranica. Možete preimenovati stavke izbornika, preurediti i dodati nove stranice s vlastitim sadržajem.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Upotrijebite ikonu ☰ u nastavku da povučete AI sadržaj u svoje izvješće.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "Nema dostupnih AI uvida. Možete ih izraditi u svom projektu.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Idi na projekt", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Odaberite fazu", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Otključaj AI analizu", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "U svoje izvješće unesite uvide generirane umjetnom inteligencijom", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Prijavite brže uz AI", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Izvještavanje s umjetnom inteligencijom nije uključeno u vaš trenutni plan. Razgovarajte sa svojim Vladinim upraviteljem uspjeha da otključate ovu značajku.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "Ovo nije uključeno u vaš trenutni plan. Obratite se svom upravitelju uspjeha vlade ili administratoru da ga otključa.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Grupirajte po registracijskom polju", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Grupirajte po anketnom pitanju", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Grupni način rada", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Grupirajte odgovore u anketi prema poljima za registraciju (spol, lokacija, dob itd.) ili drugim pitanjima u anketi.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Polje za registraciju", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Faza ankete", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Jeste li sigurni da želite ovo izbrisati?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "Izbrisati", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Objavite svoj komentar", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "Ov<PERSON>je nap<PERSON>šite svoj komentar", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Kliknite na donje gumbe za praćenje ili prestanak praćenja. Broj projekata prikazan je u zagradama.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "U vašem području", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "Gotovo", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "Trenutno nema aktivnih projekata s obzirom na vaše postavke praćenja.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "O<PERSON>j widget prikazuje projekte povezane s \"podru<PERSON><PERSON>\" koja korisnik prati. Imajte na umu da vaša platforma može koristiti drugačiji naziv za \"područja\" - pogledajte karticu \"Područja\" u postavkama platforme. Ako korisnik još ne prati nijedno područje, widget će prikazati dostupna područja za praćenje. U tom slučaju, widget će prikazati maksimalno 100 područja.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "<PERSON><PERSON> objavljenih projekata ili mapa", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Objavljeni projekti i mape", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "<PERSON><PERSON><PERSON> widget će prikazati projekte i mape koji su trenutno objavljeni, poštujući redoslijed definiran na stranici projekata. Ovo ponašanje je isto kao i \"aktivna\" kartica widgeta \"naslijeđenih\" projekata.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "<PERSON><PERSON> odabranih projekata ni mapa", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Odaberite projekte ili mape", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Odabrani projekti i mape", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "Pomoću ovog widgeta možete odabrati i odrediti redoslijed kojim želite da se projekti ili mape prikazuju korisnicima.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projekti", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "posjetite naš centar za podršku", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Za više informacija o preporučenim razlučivostima slika, {supportPageLink}."}
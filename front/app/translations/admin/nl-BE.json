{"UI.FormComponents.required": "vereist", "app.Admin.ManagementFeed.action": "<PERSON><PERSON>", "app.Admin.ManagementFeed.after": "Na", "app.Admin.ManagementFeed.before": "Voor", "app.Admin.ManagementFeed.changed": "Gewijzigd", "app.Admin.ManagementFeed.created": "Aangemaakt", "app.Admin.ManagementFeed.date": "Datum", "app.Admin.ManagementFeed.deleted": "Verwijderd", "app.Admin.ManagementFeed.folder": "Map", "app.Admin.ManagementFeed.idea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.in": "in project {project}", "app.Admin.ManagementFeed.item": "<PERSON><PERSON>", "app.Admin.ManagementFeed.key": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.managementFeedNudge": "Toegang tot de managementfeed is niet inbegrepen in je huidige licentie. Neem contact op met je GovSuccess Manager voor meer informatie.", "app.Admin.ManagementFeed.noActivityFound": "Geen activiteit gevonden", "app.Admin.ManagementFeed.phase": "Fase", "app.Admin.ManagementFeed.project": "Project", "app.Admin.ManagementFeed.projectReviewApproved": "Project goedgekeurd", "app.Admin.ManagementFeed.projectReviewRequested": "Projectbeoordeling aangevraagd", "app.Admin.ManagementFeed.title": "Beheer feed", "app.Admin.ManagementFeed.user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.userPlaceholder": "Selecteer een g<PERSON><PERSON><PERSON>r", "app.Admin.ManagementFeed.value": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.viewDetails": "Bekijk details", "app.Admin.ManagementFeed.warning": "Experimentele functionaliteit: <PERSON><PERSON> minimale lijst van geselecteerde acties die in de afgelopen 30 dagen zijn uitgevoerd door platform- of projectbeheerders. Niet alle acties zijn opgenomen.", "app.Admin.Moderation.managementFeed": "Beheer feed", "app.Admin.Moderation.participationFeed": "Participatie feed", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "<PERSON><PERSON> weten?", "app.components.Admin.Campaigns.clicked": "Geklikt", "app.components.Admin.Campaigns.deleteCampaignButton": "Campagne verwijderen", "app.components.Admin.Campaigns.deliveryStatus_accepted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Bounced", "app.components.Admin.Campaigns.deliveryStatus_clicked": "Geklikt", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "Dit laat zien hoeveel ontvangers op een link in de e-mail hebben geklikt. Houd er rekening mee dat sommige beveiligingssystemen links automatisch volgen om ze te scannen, wat kan resulteren in valse klikken.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_failed": "Mislukt", "app.components.Admin.Campaigns.deliveryStatus_opened": "Geopend", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "Dit laat zien hoeveel ontvangers de e-mail hebben geopend. Houd er rekening mee dat sommige beveiligingssystemen (zoals Microsoft Defender) inhoud vooraf kunnen laden om te scannen, wat kan resulteren in valse openingen.", "app.components.Admin.Campaigns.deliveryStatus_sent": "Verzonden", "app.components.Admin.Campaigns.draft": "Concept", "app.components.Admin.Campaigns.from": "<PERSON>", "app.components.Admin.Campaigns.manageButtonLabel": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.opened": "Geopend", "app.components.Admin.Campaigns.project": "Project", "app.components.Admin.Campaigns.recipientsTitle": "Ontvangers", "app.components.Admin.Campaigns.reply_to": "Antwoorden op", "app.components.Admin.Campaigns.sent": "Verzonden", "app.components.Admin.Campaigns.statsButton": "Statistieken", "app.components.Admin.Campaigns.subject": "Onderwerp", "app.components.Admin.Campaigns.to": "<PERSON><PERSON>", "app.components.Admin.ImageCropper.cropFinalSentence": "<PERSON><PERSON> ook: {link}.", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "<PERSON><PERSON> belang<PERSON>jke inhoud binnen de stippellijnen zodat deze altijd zich<PERSON>ar is.", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "3:1 op mobiel (alleen het gebied tussen de stippellijnen wordt getoond)", "app.components.Admin.ImageCropper.cropSentenceOne": "De afbeelding wordt automatisch bijgesneden:", "app.components.Admin.ImageCropper.cropSentenceTwo": "{aspect} op computer (volledige breedte getoond)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "aanbevolen verhouding", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Bannertitel voor geregistreerde gebruikers", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Opgelet: Deze kleur heeft een te laag contrast. Hierdoor wordt sommige tekst moeilijk leesbaar. Kies een donkerdere kleur om de leesbaarheid te verbeteren.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Activiteiten toevoegen aan navigatiebalk", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "Voeg een link toe naar alle projectactiviteiten in de navigatiebalk", "app.components.AdminPage.SettingsPage.eventsSection": "Activiteiten", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "<PERSON><PERSON>j veld op startpagina", "app.components.AnonymousPostingToggle.userAnonymity": "Anonimite<PERSON> van de gebruiker", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Gebruikers zullen hun identiteit kunnen verbergen voor andere gebruikers en beheerders. Deze bijdragen kunnen nog steeds gemodereerd worden.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Sta gebruikers toe anoniem deel te nemen", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Gebruikers kunnen er nog steeds voor kiezen om met hun echte naam deel te nemen, maar ze zullen de mogelijkheid hebben om bijdragen anoniem in te dienen als ze dat willen. Alle gebruikers zullen nog steeds moeten voldoen aan de eisen op het tabblad Toegangsrechten om hun bijdragen in te dienen. Gebruikersprofielgegevens zullen niet beschikbaar zijn in de export van deelnemingsgegevens.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "<PERSON><PERSON> meer over gebruikersanonimiteit in onze {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "support artikel", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/nl/articles/7946486-anonieme-de<PERSON>name-mogelijk-maken", "app.components.BillingWarning.billingWarning": "Zodra er extra accounts worden toegevoegd, wordt jullie factuur verhoogd. Neem contact op met je Government Success Manager voor meer informatie.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Bedankt voor het invullen van de vragenlijst! Je bent van harte welkom om hem volgend kwartaal weer in te vullen.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Download als pdf", "app.components.FormSync.downloadExcelTemplate": "Download een <PERSON>-s<PERSON><PERSON><PERSON>on", "app.components.FormSync.downloadExcelTemplateTooltip2": "Excel-sjablonen bevatten geen rangschikkingsvragen, matrixvragen, bestand uploadvragen en kaart-gerelateerde vragen (pin prikken, route tekenen, gebied tekenen, ESRI-bestandsupload) omdat deze op dit moment nog niet worden ondersteund voor bulkimport.", "app.components.ProjectTemplatePreview.close": "Sluiten", "app.components.ProjectTemplatePreview.createProject": "Maak een project aan", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Maak een project aan op basis van het sjabloon ''{templateTitle}''.", "app.components.ProjectTemplatePreview.goBack": "Ga terug", "app.components.ProjectTemplatePreview.goBackTo": "Ga terug naar de {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "CitizenLab-expert", "app.components.ProjectTemplatePreview.infoboxLine1": "Wil je dit sjabloon gebruiken voor jouw participatieproject?", "app.components.ProjectTemplatePreview.infoboxLine2": "Neem contact op met de verantwoordelijke binnen je organisatie, of contacteer een {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Projectmap", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "De ingevoerde datum is ongeldig. Gelieve het volgende formaat te gebruiken: JJJJ-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Selecteer een startdatum voor het project", "app.components.ProjectTemplatePreview.projectStartDate": "De startdatum van je project", "app.components.ProjectTemplatePreview.projectTitle": "De titel van je project", "app.components.ProjectTemplatePreview.projectTitleError": "<PERSON><PERSON> een titel voor je project", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "<PERSON><PERSON> een projecttitel voor alle talen", "app.components.ProjectTemplatePreview.projectsOverviewPage": "pagina met het <PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.responseError": "Er ging iets mis. <PERSON><PERSON><PERSON> later opnieuw of neem contact <NAME_EMAIL>.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "<PERSON><PERSON> s<PERSON> be<PERSON>jken", "app.components.ProjectTemplatePreview.successMessage": "Het project werd met succes aangema<PERSON>t!", "app.components.ProjectTemplatePreview.typeProjectName": "Typ de naam van het project in", "app.components.ProjectTemplatePreview.useTemplate": "<PERSON><PERSON> sja<PERSON><PERSON><PERSON> geb<PERSON>iken", "app.components.SeatInfo.additionalSeats": "Extra accounts", "app.components.SeatInfo.additionalSeatsToolTip": "Dit toont het aantal extra accounts dat je hebt gekocht bovenop de \"Inbegrepen accounts\".", "app.components.SeatInfo.adminSeats": "Platformbeheerders", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} platformbeheerders inbegrepen", "app.components.SeatInfo.adminSeatsTooltip1": "Platformbeheerders zijn verantwoordelijk voor het platform en hebben beheerdersrechten voor alle mappen en projecten. Je kunt {visitHelpCenter} raadplegen voor meer informatie over de verschillende rollen.", "app.components.SeatInfo.currentAdminSeatsTitle": "Huidige platformbeheerders", "app.components.SeatInfo.currentManagerSeatsTitle": "Huidige map- en projectbeheerders", "app.components.SeatInfo.includedAdminToolTip": "Dit toont het aantal beschikbare accounts voor map- en projectbeheerders binnen het jaarcontract.", "app.components.SeatInfo.includedManagerToolTip": "Dit toont het aantal beschikbare accounts voor map- en projectbeheerders binnen het jaarcontract.", "app.components.SeatInfo.includedSeats": "Accounts in gebruik", "app.components.SeatInfo.managerSeats": "Map- en projectbeheerders", "app.components.SeatInfo.managerSeatsTooltip": "Map- en projectbeheerders kunnen een onbeperkt aantal mappen/projecten beheren, {visitHelpCenter} voor meer informatie over de verschillende rollen.", "app.components.SeatInfo.managersIncludedText": "{managerSeats} accounts voor map- en projectbeheerders inbegrepen", "app.components.SeatInfo.remainingSeats": "Beschikbare accounts", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/nl/articles/2672884-wat-zijn-de-verschillende-rollen-op-het-platform", "app.components.SeatInfo.totalSeats": "Accounts binnen het abonnement", "app.components.SeatInfo.totalSeatsTooltip": "Dit toont het opgetelde aantal accounts binnen jullie abonnement en extra accounts die jullie hebben gekocht.", "app.components.SeatInfo.usedSeats": "Accounts in gebruik", "app.components.SeatInfo.view": "Bekijk", "app.components.SeatInfo.visitHelpCenter": "bekijk onze supportpagina", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "In je abonnement zijn {adminSeatsIncluded} voor platformbeheerders inbegrepen. Zodra je meer beheerdersaccounts toevoegt, zie je deze bij ‘Extra accounts’.", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Je abonnement heeft {managerSeatsIncluded}, die in aanmerking komen voor map- en projectbeheerders. Zodra je alle accounts hebt gebruikt, worden extra accounts toegevoegd onder 'Extra accounts'.", "app.components.UserSearch.addModerators": "Toevoegen", "app.components.UserSearch.searchUsers": "Zoek gebruikers", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternatieve foutmelding", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "Standaard wordt de volgende foutmelding aan gebruikers getoond:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Foutmelding aanpassen", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "Je kunt deze foutmelding voor elke taal <PERSON><PERSON><PERSON><PERSON><PERSON> met be<PERSON><PERSON> van het onderstaande tekstvak \"Alternatieve foutmelding\". Als je het tekstvak leeg laat, wordt de standaard foutmelding getoond.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "Foutmelding", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "Dit is wat deelnemers te zien krijgen als ze niet voldoen aan de deelnamevereisten.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Foutmelding opslaan", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "<PERSON><PERSON> vraag geselecteerd. Selecteer e<PERSON>t een vraag.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "<PERSON><PERSON> ant<PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} reacties", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} tot nu toe", "app.components.admin.DatePhasePicker.Input.openEnded": "Open einde", "app.components.admin.DatePhasePicker.Input.selectDate": "Selecteer datum", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Einddatum wissen", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Beginda<PERSON> wissen", "app.components.admin.Graphs": "<PERSON><PERSON> g<PERSON><PERSON><PERSON> met de huidige filters.", "app.components.admin.Graphs.noDataShort": "<PERSON><PERSON> g<PERSON><PERSON>.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Opmerkingen in de tijd", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Berichten in de tijd", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Reacties in de loop van de tijd", "app.components.admin.InputManager.onePost": "1 bijdrage", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Offline keuzes aanpassen", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Offline stemmen a<PERSON><PERSON>en", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "Met deze optie kun je de<PERSON><PERSON><PERSON><PERSON><PERSON> van andere bronnen opnemen, zoals in-person of papieren stemmingen:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Dit zal zich visueel onderscheiden van digitale stemmen.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "<PERSON><PERSON> is van invloed op de uiteindelijke stemresultaten.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "Dit wordt niet weergegeven in de dashboards met participatiegegevens.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Offline stemmen voor een optie kan maar één keer worden ingesteld in een project en wordt gedeeld tussen alle fasen van een project.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "Je moet eerst het totaal aantal offline deelnemers invoeren.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Totaal offline deelnemers", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "Om de juiste resultaten te kunnen berekenen, moeten we het <b>totale aantal offline deelnemers voor deze fase</b> weten.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Ver<PERSON>d alleen degenen die offline hebben meegedaan.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> door {name}", "app.components.admin.PostManager.PostPreview.assignee": "Toegewezen aan", "app.components.admin.PostManager.PostPreview.cancelEdit": "<PERSON><PERSON><PERSON> de wijzigingen", "app.components.admin.PostManager.PostPreview.currentStatus": "Huidige status", "app.components.admin.PostManager.PostPreview.delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Weet je zeker dat je deze bijdrage wilt verwijderen? Dit kan niet ongedaan worden gemaakt.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Weet je zeker dat je deze bijdrage wilt verwijderen? De bijdrage wordt uit alle projectfasen verwijderd en kan niet worden hersteld.", "app.components.admin.PostManager.PostPreview.edit": "Bewerk", "app.components.admin.PostManager.PostPreview.noOne": "<PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Het aantal keren dat dit in de burgerbudgetten van andere deelnemers is opgenomen", "app.components.admin.PostManager.PostPreview.picks": "Keuzes: {picksNumber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Aantal reacties:", "app.components.admin.PostManager.PostPreview.save": "Opsla<PERSON>", "app.components.admin.PostManager.PostPreview.submitError": "Fout", "app.components.admin.PostManager.addFeatureLayer": "Functielaag toe<PERSON>n", "app.components.admin.PostManager.addFeatureLayerInstruction": "<PERSON><PERSON>er de URL van de functielaag die gehost wordt op ArcGIS Online en plak deze in het onderstaande veld:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Voeg een nieuwe functielaag toe aan de kaart", "app.components.admin.PostManager.addWebMap": "<PERSON><PERSON><PERSON>n", "app.components.admin.PostManager.addWebMapInstruction": "Kopieer het portal ID van je webkaart uit ArcGIS Online en plak deze in het onderstaande veld:", "app.components.admin.PostManager.allPhases": "Alle fases", "app.components.admin.PostManager.allProjects": "Alle projecten", "app.components.admin.PostManager.allStatuses": "Alle statussen", "app.components.admin.PostManager.allTopics": "Alle tags", "app.components.admin.PostManager.anyAssignment": "<PERSON>e toe<PERSON>", "app.components.admin.PostManager.assignedTo": "{assigneeName}", "app.components.admin.PostManager.assignedToMe": "Toegewezen aan mij", "app.components.admin.PostManager.assignee": "Toegewezen aan", "app.components.admin.PostManager.authenticationError": "Er is een authenticatiefout opgetreden tijdens het ophalen van deze laag. Controleer de URL en/of je Esri API-sleutel toegang heeft tot deze laag.", "app.components.admin.PostManager.automatedStatusTooltipText": "Deze status wordt automatisch bijgewerkt als aan de voorwaarden wordt voldaan", "app.components.admin.PostManager.bodyTitle": "Beschrijving", "app.components.admin.PostManager.cancel": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel2": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.co-sponsors": "Ondersteuners", "app.components.admin.PostManager.comments": "Reacties", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "Dit betekent dat je alle gegevens verliest die bij deze bijdragen horen, zoa<PERSON> op<PERSON>, reacties en stemmen. Deze actie kan niet ongedaan worden gemaakt.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Weet je zeker dat je deze bijdragen wilt verwijderen?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "Tag verwijderen", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "Je probeert dit idee te verwijderen uit een fase waarin het stemmen heeft gekregen. Als je dit doet, gaan deze stemmen verloren. Weet je zeker dat je dit idee uit deze fase wilt verwijderen?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "De stemmen die bij dit idee horen gaan verloren", "app.components.admin.PostManager.components.goToInputManager": "<PERSON>a naar beheer bi<PERSON>", "app.components.admin.PostManager.components.goToProposalManager": "<PERSON>a naar voorstellenbeheer", "app.components.admin.PostManager.contributionFormTitle": "Bijdrage bewerken", "app.components.admin.PostManager.cost": "<PERSON><PERSON>", "app.components.admin.PostManager.createInput": "Bijdrage creëren", "app.components.admin.PostManager.createInputsDescription": "Maak een nieuwe collectie van bijdragen van een eerder project", "app.components.admin.PostManager.currentLat": "Huidige breedtegraad", "app.components.admin.PostManager.currentLng": "<PERSON><PERSON><PERSON> le<PERSON>", "app.components.admin.PostManager.currentZoomLevel": "Huidig zoomniveau", "app.components.admin.PostManager.defaultEsriError": "Er is een fout opgetreden tijdens het ophalen van deze laag. Controleer je netwerkverbinding en of de URL correct is.", "app.components.admin.PostManager.delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.deleteAllSelectedInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON> {count} bijdragen", "app.components.admin.PostManager.deleteConfirmation": "Weet je zeker dat je deze laag wilt verwijderen?", "app.components.admin.PostManager.dislikes": "Dislikes", "app.components.admin.PostManager.edit": "Bewerk", "app.components.admin.PostManager.editProjects": "Bewerk projecten", "app.components.admin.PostManager.editStatuses": "Bewerk statussen", "app.components.admin.PostManager.editTags": "Bewerk tags", "app.components.admin.PostManager.editedPostSave": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Het importeren van gegevens uit Esri ArcGIS Online is een add-on functionaliteit. Neem contact op met je Government Success Manager om deze functionaliteit te ontgrendelen.", "app.components.admin.PostManager.esriSideError": "Er is een fout opgetreden in de ArcGIS-toepassing. Wacht een paar minuten en probeer het later nog eens.", "app.components.admin.PostManager.esriWebMap": "Esri-<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.exportAllInputs": "Alle bijdragen exporteren (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Alle reacties exporteren (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Alle reacties van dit project exporteren (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Bijdragen in dit project exporteren (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Geselecteerde bijdragen selecteren (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Opmerkingen voor geselecteerde bijdragen exporteren (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Exporteer stemmen per bijdrage (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Exporteer stemmen per gebruiker (.xslx)", "app.components.admin.PostManager.exports": "Exporteren", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "Je mag alleen kaartgegevens uploaden als GeoJSON laag of als je importeert vanuit ArcGIS Online. Verwijder alle huidige GeoJSON lagen als je een functielaag wilt toevoegen.", "app.components.admin.PostManager.featureLayerTooltop": "<PERSON> vindt de URL van de functielaag aan de rechterkant van de itempagina op ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Kies welke naam getoond wordt", "app.components.admin.PostManager.feedbackBodyPlaceholder": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "app.components.admin.PostManager.fileUploadError": "<PERSON><PERSON> of meer bestanden zijn niet geüpload. Controleer de bestandsgrootte en het formaat en probeer het opnieuw.", "app.components.admin.PostManager.formTitle": "Idee bewerken", "app.components.admin.PostManager.generalApiError2": "Er is een fout opgetreden tijdens het ophalen van dit item. Controleer of de URL of Portal ID correct is en of je toegang hebt tot dit item.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Je mag alleen kaartgegevens uploaden als GeoJSON laag of als je importeert vanuit ArcGIS Online. Verwijder alle ArcGIS gegevens als je een GeoJSON laag wilt uploaden.", "app.components.admin.PostManager.goToDefaultMapView": "Ga naar het standaard middelpunt en zoom-niveau", "app.components.admin.PostManager.hiddenFieldsLink": "verborgen veld", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Tip: voeg {hiddenFieldsLink} toe bij het instellen van je TypeForm-enquête om bij te houden wie er deelnam.", "app.components.admin.PostManager.import2": "Importeren", "app.components.admin.PostManager.importError": "Het geselecteerde bestand kon niet worden geïmporteerd omdat het geen geldig GeoJSON-bestand is", "app.components.admin.PostManager.importEsriFeatureLayer": "Importeer <PERSON>", "app.components.admin.PostManager.importEsriWebMap": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.importInputs": "Importeer bijdragen", "app.components.admin.PostManager.imported": "Geïmporteerd", "app.components.admin.PostManager.initiativeFormTitle": "Bewerk dit initiatief", "app.components.admin.PostManager.inputCommentsExportFileName": "reacties_op_input", "app.components.admin.PostManager.inputImportProgress": "{importedCount} uit {totalCount} {totalCount, plural, one {bijdrage is} other {bijdragen zijn}} geïmporteerd. Het importeren is nog bezig, kom later nog eens terug.", "app.components.admin.PostManager.inputManagerHeader": "Bijdragen", "app.components.admin.PostManager.inputs": "Bijdragen", "app.components.admin.PostManager.inputsExportFileName": "bijdragen", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Toon alleen bijdragen die feedback nodig hebben", "app.components.admin.PostManager.issueFormTitle": "Stelling bewerken", "app.components.admin.PostManager.latestFeedbackMode": "Gebruik de laatste officiële update", "app.components.admin.PostManager.layerAdded": "Laag succesvol toegevoegd", "app.components.admin.PostManager.likes": "<PERSON>s", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Als u deze inputs verwijdert van het huidige project, gaat de informatie over de toegewezen fasen verloren. Wilt u doorgaan?", "app.components.admin.PostManager.mapData": "Ka<PERSON>gege<PERSON><PERSON>", "app.components.admin.PostManager.multipleInputs": "{ideaCount} bijdragen", "app.components.admin.PostManager.newFeedbackMode": "<PERSON><PERSON>i<PERSON><PERSON> een nieuwe update om deze wijziging te verklaren", "app.components.admin.PostManager.noFilteredResults": "De filters die je selecteerde hebben geen resultaten opgeleverd", "app.components.admin.PostManager.noInputs": "Nog geen bij<PERSON>gen", "app.components.admin.PostManager.noInputsDescription": "Je kunt je eigen bijdragen toevoegen, of je kunt beginnen op basis van een participatieproject uit het verleden.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 bijdrage } one {1 bijdrage} other {# bijdragen}} worden geïmporteerd uit het geselecteerde project en de geselecteerde fase. Het importeren gebeurt op de achtergrond en de bijdragen verschijnen in de invoermanager zodra het is voltooid.", "app.components.admin.PostManager.noOne": "<PERSON><PERSON>", "app.components.admin.PostManager.noProject": "Geen project", "app.components.admin.PostManager.officialFeedbackModal.author": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "<PERSON>es hoe je naam wordt weergegeven", "app.components.admin.PostManager.officialFeedbackModal.description": "Het geven van officiële feedback helpt het proces transparant te houden en versterkt vertrouwen in het platform.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Auteur is vereist", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Feedback is vereist", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Officiële feedback", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "<PERSON><PERSON> een reden voor de statuswijziging", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "<PERSON>ed<PERSON> p<PERSON><PERSON>en", "app.components.admin.PostManager.officialFeedbackModal.skip": "<PERSON><PERSON> deze keer over", "app.components.admin.PostManager.officialFeedbackModal.title": "Leg je beslissing uit", "app.components.admin.PostManager.officialUpdateAuthor": "Kies welke naam getoond wordt", "app.components.admin.PostManager.officialUpdateBody": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "app.components.admin.PostManager.offlinePicks": "Offline keuzes", "app.components.admin.PostManager.offlineVotes": "Offline stemmen", "app.components.admin.PostManager.onlineVotes": "Online stemmen", "app.components.admin.PostManager.optionFormTitle": "<PERSON><PERSON> bewerken", "app.components.admin.PostManager.participants": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Online keuzes", "app.components.admin.PostManager.pbItemCountTooltip": "Het aantal keren dat dit in de burgerbudgetten van andere deelnemers is opgenomen", "app.components.admin.PostManager.petitionFormTitle": "Bewerk deze petitie", "app.components.admin.PostManager.postedIn": "Geplaatst in {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Project bewerken", "app.components.admin.PostManager.projectsTab": "Projecten", "app.components.admin.PostManager.projectsTabTooltipContent": "Je kan bijdragen verslepen om ze van het ene naar het andere project te verplaatsen. Merk op dat je voor projecten met een tijdslijn nog steeds de bijdrage moet toevoegen aan je gewenste tijdlijnfase.", "app.components.admin.PostManager.proposalFormTitle": "Bewerk dit voorstel", "app.components.admin.PostManager.proposedBudgetTitle": "Voorgesteld budget", "app.components.admin.PostManager.publication_date": "Geplaatst op", "app.components.admin.PostManager.questionFormTitle": "Vraag bewerken", "app.components.admin.PostManager.reactions": "Reacties", "app.components.admin.PostManager.resetFiltersButton": "Reset de filters", "app.components.admin.PostManager.resetInputFiltersDescription": "Reset de filters om alle bijdragen te zien.", "app.components.admin.PostManager.saved": "Opgeslagen", "app.components.admin.PostManager.screeningTooltip": "Voorselectie is niet inbegrepen in je huidige plan. <PERSON><PERSON><PERSON> met je Government Success Manager of beheerder om het vrij te geven.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Voorselectie is uitgeschakeld voor deze fase. Ga naar fase-instelling om het in te schakelen", "app.components.admin.PostManager.selectAPhase": "Selecteer een fase", "app.components.admin.PostManager.selectAProject": "Selecteer een project", "app.components.admin.PostManager.setAsDefaultMapView": "Het huidige middelpunt en zoom-niveau van de kaart opslaan als standaard", "app.components.admin.PostManager.startFromPastInputs": "<PERSON><PERSON><PERSON><PERSON> van bijdragen uit het verleden", "app.components.admin.PostManager.statusChangeGenericError": "Er is een fout opgetreden, probeer het later nog eens of neem contact <NAME_EMAIL>.", "app.components.admin.PostManager.statusChangeSave": "Wijzig de status", "app.components.admin.PostManager.statusesTab": "<PERSON><PERSON>", "app.components.admin.PostManager.statusesTabTooltipContent": "Wijzig de status van een bijdrage door het te verslepen. De oorspronkelijke auteur en andere deelnemers krijgen een melding van de gewijzigde status.", "app.components.admin.PostManager.submitApiError": "Er is een probleem opgetreden bij het verzenden van het formulier. Controleer op eventuele fouten en probeer het opnieuw.", "app.components.admin.PostManager.timelineTab": "Fases", "app.components.admin.PostManager.timelineTabTooltipText": "Versleep de bijdragen om deze te kopiëren naar andere projectfasen.", "app.components.admin.PostManager.title": "Titel", "app.components.admin.PostManager.topicsTab": "Tags", "app.components.admin.PostManager.topicsTabTooltipText": "<PERSON><PERSON><PERSON> tags toe aan een bijdrage door ze naar daar te verslepen.", "app.components.admin.PostManager.view": "Bekijk", "app.components.admin.PostManager.votes": "Stemmen", "app.components.admin.PostManager.votesByInputExportFileName": "stemmen_per_bijdrage", "app.components.admin.PostManager.votesByUserExportFileName": "stemmen_per_geb<PERSON>iker", "app.components.admin.PostManager.webMapAlreadyExists": "Je kunt maar <PERSON><PERSON> web<PERSON> per keer toevoegen. Verwijder de huidige om een andere te importeren.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Je mag alleen kaartgegevens uploaden als GeoJSON laag of als je importeert vanuit ArcGIS Online. Verwijder alle huidige GeoJSON lagen als je een webkaart wilt koppelen.", "app.components.admin.PostManager.webMapTooltip": "Je kunt de portal-<PERSON> van de webkaart vinden op de itempagina van ArcGIS Online, aan de rechterkant.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {<PERSON><PERSON> dan een dag} one {<PERSON>en dag} other {# dagen}} over", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "<PERSON><PERSON><PERSON>", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "<PERSON><PERSON>, ver<PERSON><PERSON><PERSON> de resultaten van de vragenlijst", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "Dit kan niet ongedaan worden gemaakt", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Resultaten van de vragenlijst verwijderen", "app.components.admin.ProjectEdit.survey.downloadResults2": "Download de resultaten van de vragenlijst", "app.components.admin.ReportExportMenu.FileName.fromFilter": "van", "app.components.admin.ReportExportMenu.FileName.groupFilter": "groep", "app.components.admin.ReportExportMenu.FileName.projectFilter": "project", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tag", "app.components.admin.ReportExportMenu.FileName.untilFilter": "tot", "app.components.admin.ReportExportMenu.downloadPng": "Downloaden als PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Downloaden als SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Download Excel", "app.components.admin.SlugInput.regexError": "De slug kan alleen gewone, kleine letters (a-z), cij<PERSON> (0-9) en koppeltekens (-) bevatten. De eerste en de laatste letter kunnen geen koppelteken zijn. Opeenvolgende koppeltekens (--) kunnen niet.", "app.components.admin.TerminologyConfig.saveButton": "Opsla<PERSON>", "app.components.admin.commonGroundInputManager.title": "Titel", "app.components.admin.seatSetSuccess.admin": "Platformbeheerder", "app.components.admin.seatSetSuccess.allDone": "<PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.close": "Sluit", "app.components.admin.seatSetSuccess.manager": "Map- en projectbeheerder", "app.components.admin.seatSetSuccess.orderCompleted": "Bestelling voltooid", "app.components.admin.seatSetSuccess.reflectedMessage": "De wijzigingen aan je licentie worden bij je volgende facturering verwerkt.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} rechten zijn toegekend aan de geselecteerde gebruiker(s).", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Weet u zeker dat u alle resultaten van de vragenlijst wilt verwijderen?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "Deze deelnamemethode is in bèta. We rollen het geleidelijk uit om feedback te verzamelen en de ervaring te verbeteren.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Het verzamelen van feedback op een document is een aangepaste functionaliteit, en is niet inbegrepen in je huidige abonnement. Neem contact op met je Government Success Manager voor meer informatie.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Onderwerp", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Aantal dagen is vereist", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "Aantal dagen om minimum aantal stemmen te bereiken", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "<PERSON><PERSON> informatie over het embedden van Google Forms vind je in {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project#h_163e33df30", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "dit support-artikel", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Idee", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Initiatief", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Hoe moet een bijdrage worden genoemd?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "{<PERSON><PERSON><PERSON>, select, gent {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "<PERSON><PERSON> hier de link naar je Konveio-document. Lees ons {supportArticleLink} voor meer informatie over het instellen van Konveio.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "support-artike<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "<PERSON><PERSON> is niet inbegrepen in je huidige plan. Neem contact op met je Government Success Manager of admin om het vrij te geven.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Er is een <PERSON>bu<PERSON>t nodig", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Maximum aantal stemmen per optie moet kleiner zijn dan of gelijk aan totaal aantal stemmen", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Er is een maximum aantal stemmen vereist", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "E-mails", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Het minimumbudget kan niet groter zijn dan het maximumbudget", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Er is een <PERSON>bu<PERSON>t nodig", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Het minimum aantal stemmen kan niet groter zijn dan het maximum aantal", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Een minimum aantal stemmen is vereist", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Ontbrekende einddatum", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Ontbrekende begindatum", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Configureer de stemopties via het tabb<PERSON> '<PERSON><PERSON><PERSON> bi<PERSON>' nadat je een fase hebt gemaakt.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Configureer de stemopties in het {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Opties voor deelname", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Platform- en projectbeheerders", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Verzamel feedback op een document:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} kunnen de<PERSON> in deze fase.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Reactie:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Raakvlakkenmethode-fase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Verwi<PERSON><PERSON> fase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "<PERSON><PERSON>, ver<PERSON><PERSON><PERSON> deze fase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "Weet je zeker dat je deze fase wilt verwijderen?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "Alle aan deze fase gerelateerde data wordt verwijderd. Dit kan niet ongedaan worden gemaakt.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Fase voor feedback op een document", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "Fase voor externe enquête", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Fase voor ideeënvorming", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "Fase voor enquête op het platform", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Informatiefase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "Gemengde rechten", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Fase voor peiling", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Fase voor voorstellen", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>Liken/unliken:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Geregistreerd voor activiteit:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Geregistreerde gebruikers", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b><PERSON><PERSON>jdragen indienen:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>Peiling:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b>Vragenlijst:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Geb<PERSON><PERSON><PERSON> met bevestigd e-mailadres", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>V<PERSON><PERSON><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Fase voor deelnemers zoeken", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Stemmen:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Fase voor stemmen", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Wie kan deelnemen?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Bijdragen zijn pas zichtbaar als een beheerder ze heeft beoordeeld en goedgekeurd. Auteurs kunnen bijdragen niet meer bewerken nadat ze zijn gescreend of nadat erop is gereageerd.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "Alleen platformbeheerders", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "<PERSON><PERSON><PERSON> met de link kan reageren op het conceptproject", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Goedkeuren maakt het mogelijk voor projectbeheerders om het project te publiceren.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "Goedgekeurd door {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Gearchiveerd", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Concept", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Beschrijving bewerken", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "Verborgen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Offline stemmers", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Alleen admins{inFolder, select, true { of de mapbeheerders} other {}} kunnen het project publiceren.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 deelnemer} other {{participantsCount} deelnemers}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Deelnemers aan ingesloten methoden (bijv. externe onderzoeken)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Vol<PERSON> van een project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Opmerking: <PERSON><PERSON> je anonieme of open deelnamemogelijkheden inschakelt, kunnen gebruikers meerdere keren de<PERSON>, wat kan leiden tot misleidende of onvolledige gebruikersgegevens.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "<PERSON><PERSON><PERSON><PERSON> <b>zijn niet</b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "<PERSON><PERSON><PERSON><PERSON> zijn onder and<PERSON>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Geregistreerden activiteit", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "<PERSON><PERSON><PERSON><PERSON><PERSON> in <PERSON>ie met <PERSON>-<PERSON>en", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "Wachtend op goedkeuring", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Projectbeoordelaars zijn op de hoogte gesteld.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Publiceren", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Gepubliceerd - Actief", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Gepubliceerd - Afgerond", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Vernieuw voorbeeldlink project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Maak de voorbeeldlink van het project opnieuw aan. Hierdoor wordt de vorige link ongeldig.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Oude links zullen niet meer werken, maar je kunt op elk moment een nieuwe a<PERSON>ma<PERSON>.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Weet je het zeker? Dit schakelt de huidige link uit", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "<PERSON><PERSON>, link vern<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Goedkeuring aanvragen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "Het project moet worden goedgekeurd door een beheerder{inFolder, select, true { of een <PERSON>hee<PERSON>} other {}} voordat je het kunt publiceren. Klik op de knop hieronder om goedkeuring aan te vragen.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Instellingen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "<PERSON><PERSON><PERSON> de link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "<PERSON> gek<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Het del<PERSON> van privélinks is niet inbegrepen in je huidige plan. <PERSON><PERSON><PERSON> met je Government Success Manager of admin om dit vrij te geven.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Deel dit project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "Wie heeft toegang", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "Bekijk", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Project", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Voorstel", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "Vraag", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Minimum aantal stemmen om in aanmerking te komen", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Minimum aantal stemmen is vereist", "app.components.app.containers.AdminPage.ProjectEdit.report": "Rapport", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Vereis voorselectie van bijdragen", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.cancelMethodChange1": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.changeMethodWarning1": "Het veranderen van methode kan leiden tot het verbergen van input die is gegener<PERSON>d of ontvangen tijdens het gebruik van de vorige methode.", "app.components.formBuilder.changingMethod1": "Methode wijzigen", "app.components.formBuilder.confirmMethodChange1": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.cancel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.description": "Dit kopieert alle vragen en logica zonder de antwoorden.", "app.components.formBuilder.copySurveyModal.duplicate": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "Geen geschikte fases gevonden in dit project", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "<PERSON><PERSON> fase geselecteerd. Selecteer e<PERSON>t een fase.", "app.components.formBuilder.copySurveyModal.noProject": "Geen project", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Geen project geselecteerd. Selecteer eerst een project.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "Je hebt al wijzigingen in deze vragenlijst opgeslagen. Als je een andere vragenlijst dupliceert, gaan de wijzigingen verloren.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Vragenlijst fase", "app.components.formBuilder.copySurveyModal.title": "Selecteer een vragenlijst om te dupliceren", "app.components.formBuilder.editWarningModal.addOrReorder": "Vragen toevoegen of opnieuw ordenen", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Je responsgegevens kunnen onnauwkeurig zijn", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Bewerk tekst", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Een typefout herstellen? Het heeft geen invloed op je responsgegevens", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Je verliest responsgegevens die aan die vraag zijn gekoppeld", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Verwijder een vraag", "app.components.formBuilder.editWarningModal.exportYouResponses2": "exporteer je reacties.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Waarschuwing: Je kunt de responsgegevens voor altijd kwijtraken. Voordat je verder gaat,", "app.components.formBuilder.editWarningModal.noCancel": "<PERSON><PERSON>, annuleren", "app.components.formBuilder.editWarningModal.title4": "Bewerk live vragenlijst", "app.components.formBuilder.editWarningModal.yesContinue": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "instellingen voor toegangsrechten voor deze vragenlijst", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "'Demografische velden in vragenlijstformulier' is ingeschakeld. Als het vragenlijstformulier wordt weergegeven, worden alle geconfigureerde demografische vragen toegevoegd op een nieuwe pagina direct voor het einde van de vragenlijst. Deze vragen kunnen worden gewijzigd in de {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "instellingen voor toegangsrechten voor deze fase.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "<PERSON><PERSON><PERSON><PERSON> van enquêtes hoeven zich niet te registreren of in te loggen om antwoorden in te dienen, wat kan leiden tot dubbele inzendingen", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "Door de stap registreren/inloggen over te slaan, accepteer je dat er geen demografische informatie over respondenten wordt verz<PERSON>, wat gevolgen kan hebben voor je gegevensanalyse", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "De<PERSON> enquête is op het tabblad Toegangsrechten zo ingesteld dat \"<PERSON><PERSON><PERSON>\" toe<PERSON>g heeft.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "Als je dit wilt veranderen, kun je dat doen in de {accessRightsSettingsLink}", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Je stelt de volgende demografische vragen aan enquêtedeelnemers via de registratie/inlog stap.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "Om het verzamelen van demografische informatie te stroomlijnen en ervoor te zorgen dat deze in je gebruikersdatabase wordt opgenomen, adviseren we om demografische vragen direct in het registratie-/loginproces op te nemen. Gebruik hiervoor de {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Vraag gebruikers om gebieden of thema's te volgen", "app.components.onboarding.followHelperText": "Dit activeert een stap in het registratieproces waar gebruikers gebieden of thema's kunnen volgen die je hieronder selecteert", "app.components.onboarding.followPreferences": "Voorkeuren volgen", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} binnen het abonnement, {noOfAdditionalSeats} extra", "app.components.seatsWithinPlan.seatsWithinPlanText": "Accounts binnen het abonnement", "app.containers.Admin.Campaigns.campaignFrom": "<PERSON>:", "app.containers.Admin.Campaigns.campaignTo": "Aan:", "app.containers.Admin.Campaigns.customEmails": "Aangepaste e-mails", "app.containers.Admin.Campaigns.customEmailsDescription": "<PERSON><PERSON>ur aangepaste e-mails en controleer de statistieken.", "app.containers.Admin.Campaigns.noAccess": "Het spijt ons, maar het lijkt alsof je geen toegang tot de e-mails sectie hebt", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Geautomatiseerde e-mails", "app.containers.Admin.Insights.tabReports": "Rapporten", "app.containers.Admin.Invitations.a11y_removeInvite": "Uitnodiging verwijderen", "app.containers.Admin.Invitations.addToGroupLabel": "Voeg deze mensen toe aan specifieke manuele gebruikersgroepen", "app.containers.Admin.Invitations.adminLabel1": "<PERSON><PERSON> genodi<PERSON>den behee<PERSON>", "app.containers.Admin.Invitations.adminLabelTooltip": "Indien geactiveerd krijgen mensen die je uitnodiging accepteren ook toegang tot alle admin instellingen van het platform.", "app.containers.Admin.Invitations.configureInvitations": "3. Stel de uitnodigingen op", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Er komen geen gebruikers overeen met je zoekopdracht", "app.containers.Admin.Invitations.deleteInvite": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Weet je zeker dat je deze uitnodiging wilt verwijderen?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Het annuleren van een uitnodiging laat toe deze persoon een nieuwe uitnodiging te sturen.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Download en gebruik het sjabloon", "app.containers.Admin.Invitations.downloadTemplate": "Sjabloon downloaden", "app.containers.Admin.Invitations.email": "E-mail", "app.containers.Admin.Invitations.emailListLabel": "<PERSON><PERSON> de <PERSON> in van de mensen die je wil uitnodigen. <PERSON><PERSON><PERSON> met een komma.", "app.containers.Admin.Invitations.exportInvites": "Expo<PERSON><PERSON> <PERSON>i<PERSON> met uit<PERSON>di<PERSON><PERSON>", "app.containers.Admin.Invitations.fileRequirements": "Belangrijk: <PERSON><PERSON> de uitnodigingen correct te kunnen versturen, kan geen enkele kolom uit het sjabloon worden verwijderd - laat ongebruikte kolommen leeg. Zorg er ook voor dat er geen dubbele e-mailadressen in het bestand zitten, omdat dit fouten veroorzaakt.", "app.containers.Admin.Invitations.filetypeError": "Onjuist bestandstype. Alleen xlsx.-bestanden worden ondersteund.", "app.containers.Admin.Invitations.groupsPlaceholder": "<PERSON><PERSON> gro<PERSON> g<PERSON>d", "app.containers.Admin.Invitations.helmetDescription": "Gebruikers uitnodigen voor het platform", "app.containers.Admin.Invitations.helmetTitle": "Dashboard uitnodigingen", "app.containers.Admin.Invitations.importOptionsInfo": "Deze opties zullen alleen in rekening worden gebracht wanneer ze niet zijn gedefinieerd in het Excel-bestand.\nBezoek de {supportPageLink} voor meer informatie.", "app.containers.Admin.Invitations.importTab": "Importeer e-mailadressen", "app.containers.Admin.Invitations.invitationExpirationWarning": "Houd er rekening mee dat uitnodigingen na 30 dagen verlopen. Na deze periode kun je ze nog steeds opnieuw versturen.", "app.containers.Admin.Invitations.invitationOptions": "Opties voor uitnodigingen", "app.containers.Admin.Invitations.invitationSubtitle": "Nodig mensen uit die nog niet geregistreerd zijn op het platform. Importeer je e-mailadressen door ze in het import-sjabloon te plaatsen of voer de e-mailadressen handmatig in. Voeg indien gewenst een persoonlijk bericht toe, geef ze extra rechten of voeg ze toe aan een manuele groep.", "app.containers.Admin.Invitations.invitePeople": "Uitnodigingen", "app.containers.Admin.Invitations.inviteStatus": "Status", "app.containers.Admin.Invitations.inviteStatusAccepted": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteStatusPending": "In behandeling", "app.containers.Admin.Invitations.inviteTextLabel": "Voeg een persoonlijk bericht toe aan de uitnodiging", "app.containers.Admin.Invitations.invitedSince": "Uitgenodigd op", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Selecteer <PERSON> <PERSON><PERSON> van de uitnodi<PERSON>", "app.containers.Admin.Invitations.moderatorLabel": "<PERSON><PERSON> deze mensen projectbeheerder-rechten", "app.containers.Admin.Invitations.moderatorLabelTooltip": "<PERSON>n gea<PERSON>, krij<PERSON> mensen die je uitnodiging accepteren ook projectbeheerderrechten voor een of meerdere projecten. Meer info over de rol van projectbeheerder vind je {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "hier", "app.containers.Admin.Invitations.name": "<PERSON><PERSON>", "app.containers.Admin.Invitations.processing": "De uitnodigingen worden verzonden. Even geduld...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Geen project(en) geselecteerd", "app.containers.Admin.Invitations.save": "Verstuur je uitnodigingen", "app.containers.Admin.Invitations.saveErrorMessage": "Er trad(en) één (of meer) fout(en) op.\nEr werden dus geen uitnodigingen verstuurd.\nCorrigeer de hieronder vermelde fout(en) en probeer het opnieuw.", "app.containers.Admin.Invitations.saveSuccess": "Opgeslagen!", "app.containers.Admin.Invitations.saveSuccessMessage": "Uitnodiging succesvol verstuurd.", "app.containers.Admin.Invitations.supportPage": "support-pagina", "app.containers.Admin.Invitations.supportPageLinkText": "<PERSON><PERSON> het help<PERSON>ikel", "app.containers.Admin.Invitations.tabAllInvitations": "Alle uitnodigingen", "app.containers.Admin.Invitations.tabInviteUsers": "<PERSON>sen uitnodigen", "app.containers.Admin.Invitations.textTab": "<PERSON><PERSON><PERSON> de e-mail<PERSON><PERSON><PERSON> in", "app.containers.Admin.Invitations.unknownError": "Er ging iets mis. Probeer het opnieuw.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Upload je ingevulde bestand", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} als je meer informatie wilt over alle mogelijke kolommen in het sjabloon.", "app.containers.Admin.Moderation.all": "Alle", "app.containers.Admin.Moderation.belongsTo": "Behoort tot", "app.containers.Admin.Moderation.collapse": "Samenvouwen", "app.containers.Admin.Moderation.comment": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.confirmCommentDeletion": "Weet je zeker dat je deze reactie wilt verwijderen? Dit is permanent en kan niet ongedaan worden gemaakt.", "app.containers.Admin.Moderation.content": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.date": "Datum", "app.containers.Admin.Moderation.deleteComment": "Reactie verwijderen", "app.containers.Admin.Moderation.goToComment": "Open deze reactie in een nieuw tabblad", "app.containers.Admin.Moderation.goToPost": "Open deze bijdrage in een nieuw tabblad", "app.containers.Admin.Moderation.goToProposal": "Open dit voorstel in een nieuw tabblad", "app.containers.Admin.Moderation.markFlagsError": "Kon item(s) niet markeren. Probeer het opnieuw.", "app.containers.Admin.Moderation.markNotSeen": "Markeer {selectedItemsCount, plural, one {# item} other {# items}} als niet gezien", "app.containers.Admin.Moderation.markSeen": "Markeer {selectedItemsCount, plural, one {# item} other {# items}} als gezien", "app.containers.Admin.Moderation.moderationsTooltip": "Op deze pagina kun je snel alle nieuwe bijdragen bekijken die aan je platform is toegevoegd, inclusief reacties en voorstellen. Je kunt ze markeren als 'gezien' zodat andere beheerders weten welke bijdragen nog moeten worden verwerkt.", "app.containers.Admin.Moderation.noUnviewedItems": "Er zijn geen ongeziene items", "app.containers.Admin.Moderation.noViewedItems": "Er zijn geen geziene items", "app.containers.Admin.Moderation.pageTitle1": "Activiteit", "app.containers.Admin.Moderation.post": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.profanityBlockerSetting": "Filter grof <PERSON>", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Blokkeer input, voorstellen en reacties die het meest voorkomend grof taalgebruik bevatten.", "app.containers.Admin.Moderation.project": "Project", "app.containers.Admin.Moderation.read": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.readMore": "<PERSON><PERSON> meer", "app.containers.Admin.Moderation.removeFlagsError": "<PERSON>n wa<PERSON>chuwing(en) niet verwijderen. Probeer het opnieuw.", "app.containers.Admin.Moderation.rowsPerPage": "Rijen per pagina", "app.containers.Admin.Moderation.settings": "Instellingen", "app.containers.Admin.Moderation.settingsSavingError": "<PERSON>n niet opsla<PERSON>. Probeer de instelling opnieuw te veranderen.", "app.containers.Admin.Moderation.show": "<PERSON>n", "app.containers.Admin.Moderation.status": "Status", "app.containers.Admin.Moderation.successfulUpdateSettings": "Instellingen succesvol bijgewerkt.", "app.containers.Admin.Moderation.type": "Type", "app.containers.Admin.Moderation.unread": "<PERSON><PERSON> g<PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Deze pagina bestaat uit de volgende onderdelen. Je kunt ze in-/uitschakelen en naar wens bewerken.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Secties", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "Toon pagina", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "<PERSON><PERSON> getoond op de pagina", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Getoond op de pagina", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Bijlagen", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Voeg bestanden toe (max. 50 MB) die vanaf de pagina kunnen worden gedownload.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Onderste infosectie", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Voeg je eigen inhoud toe aan het aanpasbare gedeelte onder aan de pagina.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Bewerk", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Activiteitenlijst", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Toon activiteiten die verband houden met de projecten.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Hero-banner", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Pas de afbeelding en tekst van de paginabanner aan.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Projectlijst", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Toon de projecten op basis van je pagina-instellingen. Je kunt de projecten bekijken die zullen worden getoond.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Bovenste infosectie", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Voeg je eigen inhoud toe aan het aanpasbare gedeelte boven aan de pagina.", "app.containers.Admin.PagesAndMenu.addButton": "Voeg toe aan navigatiebalk", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Naam in navigatiebalk", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Weet je zeker dat je deze pagina wilt verwijderen? Dit kan niet ongedaan gemaakt worden.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "<PERSON><PERSON> een titel voor alle talen", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "<PERSON><PERSON> beschikbare pagina's", "app.containers.Admin.PagesAndMenu.components.savePage": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "Pagina succesvol opgeslagen", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Opgeslagen", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "<PERSON><PERSON>jlagen konden niet op worden geslagen.", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Bestanden mogen niet groter zijn dan 50Mb. Toegevoegde bestanden worden onderaan deze pagina getoond", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Bijlagen opgeslagen", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Bijlagen | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Bijlagen", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Opslaan en bijlagen inschakelen", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Bijlagen opslaan", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "<PERSON><PERSON>r voor alle talen inhoud in", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Opgeslagen", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Onderste infosectie kon niet opgeslagen worden", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Onderste infosectie is opgeslagen", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Onderste infosectie", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Opslaan en onderste infosectie inschakelen", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Onderste infosectie opslaan", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Het maken van aangepaste pagina's is niet inbegrepen in je huidige licentie. Neem contact op met je GovSuccess Manager voor meer informatie.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Selecteer ten minste één tag", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Opgeslagen", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "Op gebied", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "Op tag(s)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Het weerge<PERSON> van projecten per tag of gebied maakt geen deel uit van je huidige licentie. Neem contact op met je GovSuccess Manager voor meer informatie.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Aanpasbare pagina bewerken", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Gekoppelde projecten", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Selecteer welke projecten en gerelateerde gebeurtenissen op de pagina kunnen worden getoond.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Pagina succesvol aangemaakt", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "Pagina succesvol opgeslagen", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Aanpasbare pagina opgeslagen", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Titel in navigatiebalk", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "<PERSON><PERSON><PERSON><PERSON>e pagina aanmaken | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> pagina aanmaken", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Paginainstellingen", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> pagina opslaan", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Selecteer een gebied", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "G<PERSON><PERSON><PERSON>d gebied", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Geselecteerde tags", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "De slug kan alleen gewone, kleine letters (a-z), cij<PERSON> (0-9) en koppeltekens (-) bevatten. De eerste en de laatste letter kunnen geen koppelteken zijn. Opeenvolgende koppeltekens (--) kunnen niet.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Er dient een slug ingevoerd te worden", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Titel", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "<PERSON><PERSON><PERSON> voor elke taal een titel in", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "<PERSON><PERSON>r een titel in", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "<PERSON>n g<PERSON><PERSON><PERSON><PERSON>de pagina", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "Knop", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "<PERSON><PERSON><PERSON><PERSON>e pagina aanpassen | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Pagina-inhoud", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Bewerk", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "Voor projecten met slechts één fase; als er geen einddatum is voorzien is en de beschrijving van de fase niet is ingevuld, zal er geen tijdlijn worden getoond op de projectpagina.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "<PERSON><PERSON> beschikbare projecten op basis van je {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Dit project heeft geen tag- of gebiedsfilter, dus worden er geen projecten getoond.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Projectenlijst | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "pagina-instellingen", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Projectlijst", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "De volgende projecten worden op deze pagina getoond op basis van je {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "STANDAARD", "app.containers.Admin.PagesAndMenu.deleteButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.editButton": "Bewerk", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Opgeslagen", "app.containers.Admin.PagesAndMenu.heroBannerError": "Hero-banner kon niet opgeslagen worden", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Hero-banner opgeslagen", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Hero-banner opslaan", "app.containers.Admin.PagesAndMenu.homeTitle": "Home", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "<PERSON><PERSON> inhoud op voor ten minste één taal", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Je kunt maximaal 5 items toevoegen aan de navigatiebalk", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "<PERSON><PERSON><PERSON>'s en menu | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Verwijderen van navigatiebalk", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Opslaan en hero-banner inschakelen", "app.containers.Admin.PagesAndMenu.title": "Pa<PERSON><PERSON>'s en menu", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Opgeslagen", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.topInfoError": "Bovenste infosectie kon niet opgeslagen worden", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Bovenste infosectie opgeslagen", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Bovenste infosectie | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Bovenste infosectie", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Opslaan en bovenste infosectie opslaan", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Bovenste infosectie opslaan", "app.containers.Admin.PagesAndMenu.viewButton": "<PERSON>n", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "Leeftijd", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Gemeenschap", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Hoogste niveau inclusie-indicatoren", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "In het volgende gedeelte worden inclusie-indicatoren beschreven, die onze vooruitgang in het bevorderen van een meer inclusief en representatief participatieplatform benadrukken.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Hoogste niveau participatie-indicatoren", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "In het volgende gedeelte worden de belangrijkste participatie-indicatoren voor de geselecteerde periode beschreven, met een overzicht van betrokkenheidstrends en prestatiecijfers.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projecten", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "gepubliceerde projecten", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Platform rapport", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "<PERSON><PERSON><PERSON> <PERSON>en", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "De volgende sectie geeft een overzicht van publiekelijk zichtbare projecten die overlappen met de geselecteerde tijdsspan<PERSON>, de meest gebruikte methoden in deze projecten en statistieken over de totale deelnamecijfers.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Tijdlijn registraties", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Geblokkeerde gebruikers", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>e gebruikers.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Verwi<PERSON><PERSON> g<PERSON>", "app.containers.Admin.Users.GroupsHeader.editGroup": "Bewerk groep", "app.containers.Admin.Users.GroupsPanel.admins": "Platformbeheerders", "app.containers.Admin.Users.GroupsPanel.allUsers": "Geregistreerde gebruikers", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.managers": "Projectbeheerders", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Toegewezen items", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "<PERSON>heer alle gebruikers op je platform. <PERSON><PERSON> man<PERSON>e of slimme groepen aan om hen te segmenteren.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Uitnodiging in behandeling", "app.containers.Admin.Users.admin": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.assign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.assignedItems": "Toegewezen items voor {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "<PERSON><PERSON> een extra account", "app.containers.Admin.Users.changeUserRights": "Gebruikersrechten wijzigen", "app.containers.Admin.Users.confirm": "Bevestig", "app.containers.Admin.Users.confirmAdminQuestion": "Weet je zeker dat je {name} platform admin rechten wilt geven?", "app.containers.Admin.Users.confirmNormalUserQuestion": "Weet je zeker dat je {name} als normale gebruiker wilt instellen?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Weet je zeker dat je {name} als normale gebruiker wilt instellen? Houd er rekening mee dat deze gebruiker bij bevestiging de beheerdersrechten verliest voor alle projecten en mappen waaraan deze is toegewezen.", "app.containers.Admin.Users.deleteUser": "Verwijder deze gebruiker", "app.containers.Admin.Users.email": "E-mail", "app.containers.Admin.Users.folder": "Map", "app.containers.Admin.Users.folderManager": "Mapbeheerder", "app.containers.Admin.Users.helmetDescription": "Gebruikerslijst in admin backoffice", "app.containers.Admin.Users.helmetTitle": "Admin - gebruikers dashboard", "app.containers.Admin.Users.inviteUsers": "Gebruikers uitnodigen", "app.containers.Admin.Users.joined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.lastActive": "Laatst actief", "app.containers.Admin.Users.name": "<PERSON><PERSON>", "app.containers.Admin.Users.noAssignedItems": "Geen toegewezen items", "app.containers.Admin.Users.options": "Opties", "app.containers.Admin.Users.permissionToBuy": "Om {name} admin-rechten te geven, moet u 1 extra seat kopen.", "app.containers.Admin.Users.platformAdmin": "Platformbeheerder", "app.containers.Admin.Users.projectManager": "Projectbeheerder", "app.containers.Admin.Users.reachedLimitMessage": "U heeft de limiet van het aantal plaatsen binnen uw plan bereikt, 1 extra plaats voor {name} zal worden toegevoegd.", "app.containers.Admin.Users.registeredUser": "Geregistreerde gebruiker", "app.containers.Admin.Users.remove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.removeModeratorFrom": "De gebruiker beheert de map waartoe dit project behoort. Verwijder in plaats daarvan de rechten van \"{folderTitle}\".", "app.containers.Admin.Users.role": "Rol", "app.containers.Admin.Users.seeProfile": "Bekijk het profiel van deze gebruiker", "app.containers.Admin.Users.selectPublications": "Selecteer projecten of mappen", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Typ om te zoeken", "app.containers.Admin.Users.setAsAdmin": "Instellen als admin", "app.containers.Admin.Users.setAsNormalUser": "Instellen als normale gebruiker", "app.containers.Admin.Users.setAsProjectModerator": "Instellen als projectbeheerder", "app.containers.Admin.Users.setUserAsProjectModerator": "Wijs {name} aan als <PERSON><PERSON><PERSON><PERSON>r", "app.containers.Admin.Users.userBlockModal.allDone": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.blockAction": "Blokkeer gebruiker", "app.containers.Admin.Users.userBlockModal.blockInfo1": "<PERSON> inhou<PERSON> van deze gebruiker zal niet verwijderd worden door deze actie. Vergeet niet hun inhoud te modereren indien nodig.", "app.containers.Admin.Users.userBlockModal.blocked": "Geblokkeerd", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Deze gebruiker is geb<PERSON>k<PERSON><PERSON> sinds {from}. Het verbod duurt tot {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Weet je zeker dat je {name} wilt de<PERSON><PERSON><PERSON><PERSON>?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} is g<PERSON><PERSON><PERSON><PERSON><PERSON> tot {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 dag} other {{numberOfDays} dagen}}", "app.containers.Admin.Users.userBlockModal.header": "Blokkeer gebruiker", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Reden", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Dit zal worden meegedeeld aan de geblokkeerde gebruiker.", "app.containers.Admin.Users.userBlockModal.subtitle1": "De geselecteerde gebruiker zal niet kunnen inloggen op het platform voor {daysBlocked}. Als je dit wilt te<PERSON>, kun je deze gebruiker deblokkeren uit de lijst van geblokkeerde gebruikers.", "app.containers.Admin.Users.userBlockModal.unblockAction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON>, ik wil deze geb<PERSON>iker de<PERSON>", "app.containers.Admin.Users.userDeletionConfirmation": "Deze gebruiker permanent verwijderen?", "app.containers.Admin.Users.userDeletionFailed": "Er is een fout opgetreden tijdens het verwijderen van deze geb<PERSON>iker, gelieve opnieuw te proberen.", "app.containers.Admin.Users.userDeletionProposalVotes": "Dit verwijdert ook alle stemmen van deze gebruiker op voorstellen waar nog over gestemd kan worden.", "app.containers.Admin.Users.userExportFileName": "gebruikers_export", "app.containers.Admin.Users.userInsights": "Inzichten van gebruikers", "app.containers.Admin.Users.youCantDeleteYourself": "Je kunt je eigen account via de adminpa<PERSON><PERSON> van de gebruiker niet verwijderen", "app.containers.Admin.Users.youCantUnadminYourself": "Je kunt nu je rol van beheerder niet opgeven", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Tevredenheidsmonitor", "app.containers.Admin.communityMonitor.healthScore": "Gezondheidsscore", "app.containers.Admin.communityMonitor.healthScoreDescription": "Deze score is het gemid<PERSON>de van alle sentiment-schaalvragen die door deelnemers zijn beantwoord voor de geselecteerde periode.", "app.containers.Admin.communityMonitor.lastQuarter": "laatste kwartaal", "app.containers.Admin.communityMonitor.liveMonitor": "Live monitor", "app.containers.Admin.communityMonitor.noResults": "<PERSON><PERSON> resultaten voor deze periode.", "app.containers.Admin.communityMonitor.noSurveyResponses": "<PERSON><PERSON> reacties", "app.containers.Admin.communityMonitor.participants": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Rapporten", "app.containers.Admin.communityMonitor.settings": "Instellingen", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "De vragenlijst van de tevredenheidsmonitor accepteert inzendingen.", "app.containers.Admin.communityMonitor.settings.accessRights2": "Toegangsrechten", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "Nadat een gebruiker een evenement heeft bijgewoond, een stem heeft uitgebracht of is teruggekeerd naar een projectpagina na het insturen van een vragenlijst.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Tevredenheidsmonitor-managers hebben toegang tot alle tevredenheidsmonitor instellingen en gegevens en kunnen deze beheren.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Tevredenheidsmonitor-managers", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Managers kunnen de tevredenheidsmonitor vragenlijst en toegangsrechten bewerken, responsgegevens bekijken en rapporten maken.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "De standaardfrequentiewaarde is 100%.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Pop-up frequentie (0 tot 100)", "app.containers.Admin.communityMonitor.settings.management2": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.popup": "Pop-up", "app.containers.Admin.communityMonitor.settings.popupDescription3": "Er wordt periodiek een pop-upvenster weergegeven waarin bewoners worden aangemoedigd om de tevredenheidsmonitor-vragenlijst in te vullen. Je kunt de frequentie instellen die bepaalt welk percentage van de bewoners de pop-up willekeurig te zien krijgt als aan de onderstaande voorwaarden wordt voldaan.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Pop-up instellingen", "app.containers.Admin.communityMonitor.settings.preview": "Test e-mail", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "De gebruiker heeft de enquête in de afgelopen 3 maanden nog niet ingevuld.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "Gebruiker heeft de popup in de afgelopen 3 maanden nog niet gezien.", "app.containers.Admin.communityMonitor.settings.save": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.saved": "Opgeslagen", "app.containers.Admin.communityMonitor.settings.settings": "Instellingen", "app.containers.Admin.communityMonitor.settings.survey2": "Vragenlijst", "app.containers.Admin.communityMonitor.settings.surveySettings3": "Algemene instellingen", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "<PERSON><PERSON><PERSON> het laden van de startpagina of een aangepaste pagina.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Alle gebruikersgegevens anonimiseren", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "Alle vragenlijst-bijdra<PERSON> van de gebruikers worden geanonimiseerd voordat ze worden opgenomen", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Gebruikers moeten nog steeds voldoen aan de deelnamevereisten onder de 'Toegangsrechten'. <PERSON><PERSON><PERSON><PERSON> van gebruikersprofielen zullen niet besch<PERSON> zijn in de gegevensexport van de enquête.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "Onder welke omstandigheden kan de popup verschijnen voor gebruikers?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Wie zijn de managers?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Totaal aantal reacties", "app.containers.Admin.communityMonitor.upsell.aiSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Tevredenheidsmonitor inschakelen", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Deze functionaliteit is niet inbegrepen in je huidige plan. <PERSON><PERSON><PERSON> met je Government Success Manager of beheerder om het in te schakelen.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Gezondheidsscore", "app.containers.Admin.communityMonitor.upsell.learnMore": "Meer informatie", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Score door de tijd", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "De tevredenheidsmonitor houdt je op de hoogte van het vert<PERSON><PERSON> van be<PERSON>, de tevre<PERSON><PERSON><PERSON> met de diensten en het leven in de gemeenschap.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "<PERSON><PERSON><PERSON><PERSON> scores, krachtige citaten en een kwartaalrapport dat je kunt delen met collega's of verkozen ambtenaren.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Gemakkelijk te lezen scores door de tijd", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Belangrijkste citaten van bewon<PERSON>, samengevat door AI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Vragen afgestemd op de context van jouw stad", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Bewoners willekeurig geworven op het platform", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Driemaandelijkse PDF-rapporten, klaar om te delen", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "Begrij<PERSON> hoe je gemeenschap zich voelt voordat er problemen ontstaan", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Aantal", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Desktop of overig", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Mobiel", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tablet", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Apparaattypen", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Type apparaat", "app.containers.Admin.earlyAccessLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.earlyAccessLabelExplanation": "<PERSON><PERSON> is een nieuwe functie die beschikbaar is in Vroege Toegang.", "app.containers.Admin.emails.addCampaign": "E-mail maken", "app.containers.Admin.emails.addCampaignTitle": "Maak een nieuwe e-mail aan", "app.containers.Admin.emails.allParticipantsInProject": "Alle deelnemers van het project", "app.containers.Admin.emails.allUsers": "Geregistreerde gebruikers", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Geautomatiseerde e-mails worden automatisch verzonden en worden getriggerd door acties van een gebruiker. So<PERSON>ige daarvan kun je uitschakelen voor alle gebruikers van je platform. De andere geautomatiseerde e-mails kun je niet uitzetten omdat ze nodig zijn voor de goede werking van je platform.", "app.containers.Admin.emails.automatedEmails": "Geautomatiseerde e-mails", "app.containers.Admin.emails.automatedEmailsDigest": "De e-mail wordt alleen verzonden als er inhoud is", "app.containers.Admin.emails.automatedEmailsRecipients": "Gebruikers die deze e-mail zullen ontvangen", "app.containers.Admin.emails.automatedEmailsTriggers": "Gebeurtenis die deze e-mail triggert", "app.containers.Admin.emails.changeRecipientsButton": "W<PERSON>jzig de ontvangers", "app.containers.Admin.emails.clickOnButtonForExamples": "Klik op de knop hieronder om voorbeelden van deze e-mail te bekijken op onze supportpagina.", "app.containers.Admin.emails.confirmSendHeader": "E-mail alle gebruikers?", "app.containers.Admin.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.draft": "Concept", "app.containers.Admin.emails.editButtonLabel": "Bewerk", "app.containers.Admin.emails.editCampaignTitle": "Bewerk de campagne", "app.containers.Admin.emails.editDisabledTooltip2": "Binnenkort beschikbaar: Deze e-mail kan momenteel niet worden bewerkt.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Knop tekst", "app.containers.Admin.emails.editRegion_intro_multiloc": "Inleiding", "app.containers.Admin.emails.editRegion_subject_multiloc": "Onderwerp", "app.containers.Admin.emails.editRegion_title_multiloc": "Titel", "app.containers.Admin.emails.emailCreated": "E-mail succesvol aangemaakt in concept", "app.containers.Admin.emails.emailUpdated": "E-mail succesvol bijgewerkt", "app.containers.Admin.emails.emptyCampaignsDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON> in contact met je deelnemers door ze e-mails te sturen. <PERSON><PERSON> met wie je contact opneemt en houd de betrokkenheid bij.", "app.containers.Admin.emails.emptyCampaignsHeader": "<PERSON><PERSON>ur je eerste e-mail", "app.containers.Admin.emails.failed": "Mislukt", "app.containers.Admin.emails.fieldBody": "Bericht", "app.containers.Admin.emails.fieldBodyError": "<PERSON><PERSON>r een <PERSON> in", "app.containers.Admin.emails.fieldGroupContent": "Inhoud e-mail", "app.containers.Admin.emails.fieldReplyTo": "Antwoorden gaan naar", "app.containers.Admin.emails.fieldReplyToEmailError": "<PERSON><PERSON><PERSON> een email<PERSON><PERSON> met de juiste vorm in, bijvoorbeeld <EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "<PERSON><PERSON><PERSON> een email<PERSON>res in", "app.containers.Admin.emails.fieldReplyToTooltip": "<PERSON><PERSON><PERSON> waar antwoorden op je e-mail mogen terechtko<PERSON>.", "app.containers.Admin.emails.fieldSender": "<PERSON>", "app.containers.Admin.emails.fieldSenderError": "<PERSON><PERSON>r een verzender in voor de email", "app.containers.Admin.emails.fieldSenderTooltip": "Bepaal wie gebruikers als afzender van de e-mail zien.", "app.containers.Admin.emails.fieldSubject": "Onderwerp", "app.containers.Admin.emails.fieldSubjectError": "<PERSON><PERSON>r een onderwerp in", "app.containers.Admin.emails.fieldSubjectTooltip": "Dit wordt weergegeven in de onderwerpregel van de e-mail en in de inbox van de gebruiker. Maak het duidelijk en boeiend.", "app.containers.Admin.emails.fieldTo": "<PERSON><PERSON>", "app.containers.Admin.emails.fieldToTooltip": "Be<PERSON><PERSON> de gebruikersgroep(en) die je e-mail zal/zullen ontvangen.", "app.containers.Admin.emails.formSave": "Be<PERSON>ar als concept", "app.containers.Admin.emails.formSaveAsDraft": "Be<PERSON>ar als concept", "app.containers.Admin.emails.from": "<PERSON>:", "app.containers.Admin.emails.groups": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.helmetDescription": "Send out manual emails to certain citizen group and active automated campaigns", "app.containers.Admin.emails.nameVariablesInfo2": "Je kunt inwoners rechtstreeks aanspreken met de variabelen {firstName} {lastName}. Bijv. \"Beste {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "<PERSON>en voorbeeld e-mail is verzonden naar jouw e-mailadres", "app.containers.Admin.emails.previewTitle": "Voorvertoning", "app.containers.Admin.emails.regionMultilocError": "<PERSON><PERSON> een waarde op voor alle talen", "app.containers.Admin.emails.seeEmailHereText": "<PERSON><PERSON>ra dit type e-mail wordt verstuurd, kun je hem hier bekijken.", "app.containers.Admin.emails.send": "Verzenden", "app.containers.Admin.emails.sendNowButton": "Stuur nu", "app.containers.Admin.emails.sendTestEmailButton": "Stuur me een test e-mail", "app.containers.Admin.emails.sendTestEmailTooltip2": "Als je op deze link klikt, wordt er een testmail naar alleen jouw e-mailadres gestuurd. Zo kun je controleren hoe de e-mail er in het 'echt' uitziet.", "app.containers.Admin.emails.senderRecipients": "Afzender en ontvangers", "app.containers.Admin.emails.sending": "Verzenden", "app.containers.Admin.emails.sent": "Verzonden", "app.containers.Admin.emails.sentToUsers": "Dit zijn e-mails die naar gebruikers worden gestuurd", "app.containers.Admin.emails.subject": "Onderwerp:", "app.containers.Admin.emails.supportButtonLabel": "<PERSON><PERSON> voorbeelden op onze support pagina", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "Aan:", "app.containers.Admin.emails.toAllUsers": "Wil je deze e-mail naar alle gebruikers sturen?", "app.containers.Admin.emails.viewExample": "Bekijk", "app.containers.Admin.ideas.import": "Importeren", "app.containers.Admin.inspirationHub.AllProjects": "Alle projecten", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Tevredenheidsmonitor", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Feedback op een document", "app.containers.Admin.inspirationHub.ExternalSurvey": "Externe vragenlijst", "app.containers.Admin.inspirationHub.Filters.Country": "Land", "app.containers.Admin.inspirationHub.Filters.Method": "<PERSON>e", "app.containers.Admin.inspirationHub.Filters.Search": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Topic": "<PERSON>a", "app.containers.Admin.inspirationHub.Filters.population": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Highlighted": "Uitgelicht", "app.containers.Admin.inspirationHub.Ideation": "Ideeën<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Information": "Informatie delen", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Kies een land om gepinde projecten te bekijken", "app.containers.Admin.inspirationHub.PinnedProjects.country": "Land", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "Geen gepinde projecten gevonden voor dit land. Wijzig het land om gepinde projecten voor andere landen te zien", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "<PERSON>nder het land om meer gepinde projecten te zien", "app.containers.Admin.inspirationHub.Poll": "Peiling", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "Open einde", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "<PERSON><PERSON> meer...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Fase {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "Als je niet wilt dat jouw project wordt opgenomen in het inspiratiecentrum, neem dan contact op met je GovSuccess manager.", "app.containers.Admin.inspirationHub.Proposals": "Voorstellen", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Sorteren op", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "<PERSON><PERSON><PERSON><PERSON> (laagste e<PERSON>t)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "<PERSON><PERSON><PERSON><PERSON> (hoogste e<PERSON>t)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Begindatum (oudste e<PERSON>t)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Startdatum (nieuwste eerst)", "app.containers.Admin.inspirationHub.Survey": "Vragenlijst", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Samengestelde lijst van de beste projecten wereldwijd.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "<PERSON><PERSON><PERSON> met en le<PERSON> van collega's uit de praktijk.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filter op methode, stadsgrootte & land.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Inspiratie Hub inschakelen", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Deze functionaliteit is niet inbegrepen in je huidige plan. <PERSON><PERSON><PERSON> met je Government Success Manager of beheerder om het in te schakelen.", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "https://support.govocal.com/en/articles/11093736-using-the-inspiration-hub", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "Meer informatie", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "Het inspiratiecentrum verbindt je met een samengestelde feed van uitzonderlijke participatieprojecten op Go Vocal platforms over de hele wereld. <PERSON><PERSON> hoe andere steden succesvolle projecten uitvoeren en praat met andere mensen uit de praktijk.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Sluit je aan bij een netwerk van pioniers op het gebied van democratie", "app.containers.Admin.inspirationHub.Volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Voting": "Stemming", "app.containers.Admin.inspirationHub.commonGround": "Raakvlakkenmethode", "app.containers.Admin.inspirationHub.filters": "filters", "app.containers.Admin.inspirationHub.resetFilters": "Reset de filters", "app.containers.Admin.inspirationHub.seemsLike": "Het lijkt erop dat er geen projecten meer zijn. Probeer de {filters}te veranderen.", "app.containers.Admin.messaging.automated.editModalTitle": "Campagnevelden bewerken", "app.containers.Admin.messaging.automated.variablesToolTip": "Je kunt de volgende variabelen gebruiken in je bericht:", "app.containers.Admin.messaging.helmetTitle": "Berichten", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "Deze widget toont elke gebruiker projecten <b>op basis van hun volg-voorkeuren</b>. Dit omvat projecten die ze volgen, maar ook projecten waarvan ze input volgen, en projecten die gerelateerd zijn aan onderwerpen of gebieden waarin ze geïnteresseerd zijn.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "Deze widget wordt alleen aan de gebruiker getoond als er projecten zijn waaraan hij of zij kan deelnemen. Als je dit bericht ziet, betekent dit dat jij (de beheerder) op dit moment niet kunt deelnemen aan projecten. Dit bericht is niet zichtbaar op de echte startpagina.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Open voor deelname", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "Deze widget laat projecten zien waar de gebruiker op dit moment <b>een actie kan ondernemen om deel te nemen</b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "<PERSON><PERSON> jou", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "Deze widget wordt alleen aan de gebruiker getoond als er projecten zijn die relevant voor hem zijn op basis van zijn of haar volg-voorkeuren. Als je dit bericht ziet, betekent het dat jij (de beheerder) op dit moment niets volgt. Dit bericht is niet zichtbaar op de echte homepage.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "Gevolgde items", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Gearchiveerd", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Filter op", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Afgelopen", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Afgelopen en gearchiveerd", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "<PERSON><PERSON> g<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "Deze widget toont <b>projecten die zijn afgelopen en/of gearchiveerd.</b> \"Afgelopen\" omvat ook projecten die zich in de laatste fase bevinden en waarbij de laatste fase een rapport is.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Afgelopen projecten", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "<PERSON><PERSON>, wij deden...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "<PERSON><PERSON> een naam voor elke taal", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "Het project kan niet leeg zijn", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Project", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Resulterende URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "Opsla<PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Project toevoegen", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "De navigatiebalk toont alleen projecten waar gebruikers toegang tot hebben.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "Deze widget is alleen zichtbaar op de startpage als de tevredenheidsmonitor reacties accepteert.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Tevredenheidsmonitor", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Beschrijving", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "Knop", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Titel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Belangrijk:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "<PERSON><PERSON><PERSON><PERSON> van een sentiment vragenlijst-vraag", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "<PERSON><PERSON> op escape om carrousel over te slaan", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projecten en mappen (legacy)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Projectnaam", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} werkt momenteel aan", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "<PERSON><PERSON><PERSON> knop", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Doe nu mee!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Beschrijving", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "map", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Selecteer een project of map", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Selecteer project of map", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Avatars tonen", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "In de schijnwerpers", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "Begint over {days} dagen", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Begint over {weeks} weken", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} dagen geleden", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} weken geleden", "app.containers.Admin.project.Campaigns.campaignFrom": "<PERSON>:", "app.containers.Admin.project.Campaigns.campaignTo": "Aan:", "app.containers.Admin.project.Campaigns.customEmails": "Aangepaste e-mails", "app.containers.Admin.project.Campaigns.customEmailsDescription": "<PERSON><PERSON>ur aangepaste e-mails en controleer de statistieken.", "app.containers.Admin.project.Campaigns.noAccess": "Het spijt ons, maar het lijkt alsof je geen toegang hebt tot de e-mails sectie", "app.containers.Admin.project.emails.addCampaign": "E-mail maken", "app.containers.Admin.project.emails.addCampaignTitle": "Maak een nieuwe e-mail aan", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Alle {participants} en volgers van het project", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Hieronder vallen geregistreerde gebruikers die een actie in het project hebben uitgevoerd. Niet-geregistreerde of anonieme gebruikers worden niet meegenomen.", "app.containers.Admin.project.emails.dateSent": "Verzenddatum", "app.containers.Admin.project.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.draft": "Concept", "app.containers.Admin.project.emails.editButtonLabel": "Bewerk", "app.containers.Admin.project.emails.editCampaignTitle": "Bewerk de campagne", "app.containers.Admin.project.emails.emptyCampaignsDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON> in contact met je deelnemers door ze e-mails te sturen. <PERSON><PERSON> met wie je contact opneemt en houd de betrokkenheid bij.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "<PERSON><PERSON>ur je eerste e-mail", "app.containers.Admin.project.emails.failed": "Mislukt", "app.containers.Admin.project.emails.fieldBody": "Bericht", "app.containers.Admin.project.emails.fieldBodyError": "Voer een bericht in voor alle talen", "app.containers.Admin.project.emails.fieldReplyTo": "Antwoorden gaan naar", "app.containers.Admin.project.emails.fieldReplyToEmailError": "<PERSON>f een e-mailadres in het juiste formaat, bijvoorbeeld <EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "<PERSON>f een e-mailadres", "app.containers.Admin.project.emails.fieldReplyToTooltip": "Bepaal op welk e-mailadres antwoorden van gebruikers op je e-mail moeten binnenkomen.", "app.containers.Admin.project.emails.fieldSender": "<PERSON>", "app.containers.Admin.project.emails.fieldSenderError": "<PERSON><PERSON>r een verzender in voor de e-mail", "app.containers.Admin.project.emails.fieldSenderTooltip": "Bepaal wie gebruikers als afzender van de e-mail zien.", "app.containers.Admin.project.emails.fieldSubject": "Onderwerp", "app.containers.Admin.project.emails.fieldSubjectError": "Voer een onderwerp in voor alle talen", "app.containers.Admin.project.emails.fieldSubjectTooltip": "Dit wordt weergegeven in de onderwerpregel van de e-mail en in de inbox van de gebruiker. Maak het duidelijk en boeiend.", "app.containers.Admin.project.emails.fieldTo": "<PERSON><PERSON>", "app.containers.Admin.project.emails.formSave": "Be<PERSON>ar als concept", "app.containers.Admin.project.emails.from": "<PERSON>:", "app.containers.Admin.project.emails.helmetDescription": "Verstuur handmatige e-mails naar projectdeelnemers", "app.containers.Admin.project.emails.infoboxAdminText": "Vanaf het tabblad Project e-mails kun je alleen e-mailen naar alle projectdeelnemers. Om andere deelnemers of subsets van gebruikers te e-mailen ga je naar het tabblad {link}.", "app.containers.Admin.project.emails.infoboxLinkText": "Platform e-mails", "app.containers.Admin.project.emails.infoboxModeratorText": "Vanaf het tabblad Project e-mails kun je alleen e-mails sturen naar alle projectdeelnemers. Platformbeheerders kunnen e-mails sturen naar andere deelnemers of subsets van gebruikers via het tabblad Platform e-mails.", "app.containers.Admin.project.emails.message": "Bericht", "app.containers.Admin.project.emails.nameVariablesInfo2": "Je kunt inwoners rechtstreeks aanspreken met de variabelen {firstName} {lastName}. Bijv. \"Beste {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.previewSentConfirmation": "<PERSON>en voorbeeld e-mail is verzonden naar jouw e-mailadres", "app.containers.Admin.project.emails.previewTitle": "Test e-mail", "app.containers.Admin.project.emails.projectParticipants": "Project deelnemers", "app.containers.Admin.project.emails.recipients": "Ontvangers", "app.containers.Admin.project.emails.send": "Verzenden", "app.containers.Admin.project.emails.sendTestEmailButton": "Stuur een test e-mail", "app.containers.Admin.project.emails.sendTestEmailTooltip": "<PERSON>d deze test e-mail naar het e-mailadres waarmee je bent ingelogd om na te gaan hoe je e-mail er 'in het echt' zal uitzien.", "app.containers.Admin.project.emails.senderRecipients": "Afzender en ontvangers", "app.containers.Admin.project.emails.sending": "Verzenden", "app.containers.Admin.project.emails.sent": "Verzonden", "app.containers.Admin.project.emails.sentToUsers": "Dit zijn e-mails die naar gebruikers zijn gestuurd", "app.containers.Admin.project.emails.status": "Status", "app.containers.Admin.project.emails.subject": "Onderwerp:", "app.containers.Admin.project.emails.to": "Aan:", "app.containers.Admin.project.messaging.helmetTitle": "E-mails", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "Deze afbeelding maakt deel uit van de mapkaart; de kaart die de map samenvat en bijvoorbeeld op de homepage wordt getoond. Voor meer informatie over aanbevolen beeldresoluties, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Deze afbeelding staat bovenaan de mappagina. Voor meer informatie over aanbevolen beeldresoluties, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "bekijk onze supportpagina", "app.containers.Admin.projects.all.askPersonalData3": "Velden voor naam en e-mail toevoegen", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "Kalenderweergave inschakelen", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "Deze functionaliteit is niet inbegrepen in je huidige plan. <PERSON><PERSON><PERSON> met je Government Success Manager of beheerder om het in te schakelen.", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "Meer informatie", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "Krijg een visueel overzicht van de tijdlijnen van je projecten in onze kalenderweergave. Zie snel welke projecten en fasen binnenkort beginnen of eindigen en actie vereisen.", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "Beg<PERSON>j<PERSON> wat er gebeurt en wanneer", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Alle vragen worden getoond op de PDF. De volgende vragen worden momenteel echter niet ondersteund voor import via FormSync: Afbeeldingen, Tags en Bestanden uploaden.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Alle vragen worden getoond op de PDF. De volgende vragen worden momenteel echter niet ondersteund voor import via FormSync: kaart-gerelateerde vragen (pin prikken, route tekenen en gebied tekenen), rangschikkingsvragen, matrixvragen en bestand upload vragen.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "<PERSON><PERSON> van het formulier", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "<PERSON><PERSON> van het formulier", "app.containers.Admin.projects.all.components.archived": "Gearchiveerd", "app.containers.Admin.projects.all.components.draft": "Concept", "app.containers.Admin.projects.all.components.manageButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.copyProjectButton": "Kopieer project", "app.containers.Admin.projects.all.copyProjectError": "Er is een fout opgetreden bij het kopiëren van dit project, probeer het later nog eens.", "app.containers.Admin.projects.all.customiseEnd": "Pas het einde van het formulier aan.", "app.containers.Admin.projects.all.customiseStart": "Pas het begin van het formulier aan.", "app.containers.Admin.projects.all.deleteFolderButton1": "Map verwijderen", "app.containers.Admin.projects.all.deleteFolderConfirm": "Weet je zeker dat je deze map wil verwijderen? Alle projecten in deze map worden ook verwijderd. Deze actie kan niet ongedaan gemaakt worden.", "app.containers.Admin.projects.all.deleteFolderError": "Er ging iets fout bij het verwijderen van deze map. Probeer het opnieuw.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Verwijder project", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Weet u zeker dat u dit project wilt verwijderen? Dit kan niet ongedaan worden gemaakt.", "app.containers.Admin.projects.all.deleteProjectError": "Er was een fout bij het verwijderen van dit project, probeer het later opnieuw.", "app.containers.Admin.projects.all.exportAsPDF1": "Download PDF formulier", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Je kunt online en offline reacties combineren. Om offline reacties te uploaden, ga je naar het tabblad '<PERSON>heer bijdragen' van dit project en klik je op 'Importeren'.", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Je kunt online en offline reacties combineren. Als je offline reacties wilt uploaden, ga je naar het tabblad 'Vragenlijst' van dit project en klik je op 'Importeren'.", "app.containers.Admin.projects.all.logicNotInPDF": "Enquêtelogica wordt niet weergegeven in de gedownloade PDF. Papieren respondenten zien alle enquêtevragen.", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "<PERSON><PERSON> z<PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "Alle mappen zijn geladen", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Map", "app.containers.Admin.projects.all.new.Folders.Table.managers": "Beheerders", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# project} other {# projecten}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Begindatum project", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Ontdekbaarheid", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "Verborgen", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Mappen", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filter op de participatiemethode van de huidige fase", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "Ideeën<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Informatie delen", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Participatiemethode", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Feedback op een document", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Raakvlakkenmethode", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "Vragenlijst", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "Peiling", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Voorstellen", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Stemming", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informeren", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "<PERSON><PERSON> gestart", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "<PERSON><PERSON><PERSON> staat", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Afgelopen", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "In afwachting van goedkeuring", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "Zoek <PERSON>en", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Alfabetisch (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Alfabetisch (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Fase begint of eindigt binnenkort", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc": "Recent gemaakt (nieuw-oud)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc": "Recent gemaakt (oud-nieuw)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "Onlangs bekeken", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Platformbeheerders", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "<PERSON><PERSON><PERSON>ba<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "<PERSON><PERSON>n", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "Leegmaken", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "<PERSON><PERSON> filters over om toe te voegen", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Platformbeheerders", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Alle projecten zijn geladen", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Gearchiveerd", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "Huidige fase", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d tot einde", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d tot start", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Ontdekbaarheid:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Concept", "app.containers.Admin.projects.all.new.Projects.Table.end": "Einde", "app.containers.Admin.projects.all.new.Projects.Table.ended": "Beëindigd", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.groups": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "Verborgen", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "<PERSON><PERSON> laden…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}m tot einde", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}m tot start", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Volgende fase:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Fase", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "Start binnenkort", "app.containers.Admin.projects.all.new.Projects.Table.project": "Projecten", "app.containers.Admin.projects.all.new.Projects.Table.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.published": "Gepubliceerd", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "<PERSON><PERSON> naar beneden om meer te laden", "app.containers.Admin.projects.all.new.Projects.Table.start": "Start", "app.containers.Admin.projects.all.new.Projects.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Status:", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "Deze kolom gebruikt deelnemersgegevens uit de cache. Kijk op het tabblad \"Deelnemers\" van het project om de meest recente cijfers te zien.", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "<PERSON><PERSON><PERSON>ba<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Zichtbaarheid:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} gro<PERSON>en", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} managers", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}j tot einde", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}j tot start", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Huidige fase: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} dagen over", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Map: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "<PERSON><PERSON> huidige fase", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "<PERSON><PERSON> fasen", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Fase {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Fases:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Projecten", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Startdatum: {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "Projecten rangschikken", "app.containers.Admin.projects.all.new.calendar": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.folders": "Mappen", "app.containers.Admin.projects.all.new.projects": "Projecten", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is niet geladen.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Project heeft geen einddatum", "app.containers.Admin.projects.all.new.timeline.project": "Projecten", "app.containers.Admin.projects.all.notes": "Opmerkingen", "app.containers.Admin.projects.all.personalDataExplanation5": "Deze optie voegt de velden voor<PERSON>, achternaam en e-mail toe aan de geëxporteerde PDF. Bij het uploaden van het papieren formulier zullen we deze gegevens gebruiken om automatisch een account te genereren voor de offline respondent.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "AI Samenvatting", "app.containers.Admin.projects.project.analysis.Comments.comments": "Reacties", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "<PERSON> samenvatting van reacties is be<PERSON><PERSON><PERSON>ar als er 5 of meer reacties zijn.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "React<PERSON> samen<PERSON>ten", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {<PERSON><PERSON><PERSON><PERSON>} =1 {1 nieuwe reactie} other {# nieuwe reacties}}", "app.containers.Admin.projects.project.analysis.aiSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "<PERSON><PERSON> is AI-gegenereerde inhoud. Het is mogeli<PERSON> niet 100% nauwkeurig. Controleer de nauwkeurigheid door te vergelijken met de werkelijke bijdragen. Houd er rekening mee dat de nauwkeurigheid waarschijnlijk verbetert als het aantal geselecteerde bijdragen wordt verminderd.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "E-mailmeldingen worden alleen naar deelnemers gestuurd", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "Verborgen", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "<PERSON>et geï<PERSON>xeerd door zoekmachines", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "<PERSON><PERSON> op de startpagina of in de widgets", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "<PERSON><PERSON> via directe URL", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "Selecteer hoe vindbaar dit project is.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "Dit project is zichtbaar voor iedereen die toegang heeft, en verschijnt op de startpagina en in de widgets.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Dit project blijft verborgen voor het grote publiek en is alleen zichtbaar voor degenen die de link hebben.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Wie kan dit project vinden?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Open AI-analyse", "app.containers.Admin.projects.project.ideas.analysisText2": "Ontdek samenvattingen op basis van AI en bekijk individuele inzendingen.", "app.containers.Admin.projects.project.ideas.importInputs": "Importeren", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "Nadat je een rapport hebt gema<PERSON>t, kun je ervoor kiezen om het met het publiek te delen zodra de fase begint.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Een complexere pagina te maken om informatie te delen", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Maak een rapport om:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "Maak een rapport", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Maak een rapport voor een eerdere fase of begin helemaal opnieuw.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "Dit rapport is niet openbaar. Om het openbaar te maken, schakel je de \"<PERSON>ichtbaar\" knop in.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "Deze fase is gestart, maar het rapport is nog niet openbaar. Om het openbaar te maken, schakel je de knop \"Zichtbaar\" in.", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "<PERSON><PERSON> met een f<PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.report": "Rapport", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "De resultaten van een eerdere vragenlijst of fase waarin ideeën zijn verzameld te delen", "app.containers.Admin.projects.project.information.ReportTab.visible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "Dit rapport wordt openbaar zodra de fase begint. Om het niet openbaar te maken, schakel je de \"<PERSON>ichtbaar\" knop uit.", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "Dit rapport is momenteel openbaar. Om het niet openbaar te maken, schakel je de \"Zichtbaar\" knop uit.", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Weet je zeker dat je dit rapport wilt verwijderen? Dit kan niet ongedaan worden gemaakt.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Toevoegen aan fase", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "Je moet hiermee instemmen voordat je verder kunt gaan", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "Je kunt het formulier hier downloaden.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "Het ge<PERSON><PERSON><PERSON>e formulier is gemaakt met het gede<PERSON>te \"Persoonlijke gegevens", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "<PERSON><PERSON> van het formulier", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "<PERSON><PERSON><PERSON><PERSON> geef ik toes<PERSON> om dit bestand te verwerken met de Google Cloud Form Parser", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Importeer Excel-bestand", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Upload een bestand om verder te gaan", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "Het sjab<PERSON>on kan hier worden gedownload.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Uploaden", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Upload een ingevuld <b>Excel-bestand</b> (.xlsx). Het moet het sjabloon gebruiken dat voor dit project is geleverd. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Upload een <b>PDF-bestand van gescande formulieren</b>. Het moet een formulier gebruiken dat in deze fase is afgedrukt. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Gebruik deze e-mail voor de nieuwe gebruiker", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Voer een geldig e-mailadres in om een nieuw account aan te maken", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Er wordt een nieuw account aangemaakt voor de auteur met deze gegeven<PERSON>. Deze bijdrage wordt daaraan toegevoegd.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Achternaam", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Voer een e-mailadres en/of een voor- en achternaam in om deze bijdrage toe te wijzen aan een auteur. Of vink het toestemmingsvakje uit.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Er is al een account gekoppeld aan dit e-mailadres. Deze bijdrage zal daaraan worden toegevoegd.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Toestemming gebruiker (gebruikersaccount aan<PERSON>)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Alle bijdragen goedkeuren", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Auteur:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "E-mail:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Er zijn fouten opgetreden tijdens het importeren en sommige bijdragen zijn niet geïmporteerd. Corrigeer de fouten en importeer de ontbrekende bijdragen opnieuw.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Ongeldige formuliergegevens. Controleer het formulier hierboven op fouten.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Importeer Excel-bestand (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Importeren", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Importeer gescande form<PERSON> (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Importeer gescande form<PERSON>n", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "Geïmporteerde bijdragen", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importeren. Dit proces kan een paar minuten duren.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "Deze input is anoniem geïmpo<PERSON>erd.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} bijdragen zijn geïmporteerd en moeten worden goedgekeurd.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} bijdragen konden niet worden goedgekeurd. Controleer elke bijdrage op validatieproblemen en bevestig deze afzonderlijk.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Taal:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Nog niets om te beoordelen. Klik op \"{importFile}\" om een PDF-bestand met gescande invulformulieren of een Excel-bestand met bijdragen te importeren.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Nog niets om te beoordelen. Klik op \"{importFile}\" om een Excel-bestand met bijdragen te importeren.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "Bijdragen", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Kan het geïmporteerde bestand niet weergeven. Het bekijken van geïmporteerde bestanden is alleen beschik<PERSON>ar voor PDF-bestanden.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Fase:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "De geselecteerde fase kan geen bijdragen bevatten. Selecteer een andere.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Dit project bevat geen fases die ideeën kunnen bevatten.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "Selecteer aan welke fase je deze bijdragen wilt toevoegen.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Input Importeur", "app.containers.Admin.projects.project.participation.comments": "Reacties", "app.containers.Admin.projects.project.participation.inputs": "Bijdragen", "app.containers.Admin.projects.project.participation.participantsTimeline": "<PERSON><PERSON><PERSON><PERSON> tijdlijn", "app.containers.Admin.projects.project.participation.reactions": "Likes/dislikes", "app.containers.Admin.projects.project.participation.selectPeriod": "Selecteer periode", "app.containers.Admin.projects.project.participation.usersByAge": "Gebruikers per leeftijd", "app.containers.Admin.projects.project.participation.usersByGender": "Gebruikers per geslacht", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "Vere<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "Voeg een vraag toe", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "De mogelijkheid om gebruikersvelden toe te voegen of te bewerken op faseniveau is niet inbegrepen in je huidige licentie. Neem contact op met je GovSuccess Manager voor meer informatie.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} opties", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "Deze vragen worden toegevoegd als laatste pagina van het vragenlijstformulier, omdat 'Toon velden in enquête?' is gese<PERSON>eerd in de fase-instellingen.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "Er worden geen extra vragen gesteld.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Optioneel", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Optioneel - altijd ingeschakeld omdat hiernaar wordt verwezen door groep", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "Veld verwijderen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Vereist - altijd ingeschakeld omdat hiernaar wordt verwezen door groep", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Authenticeren met {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Vul de extra vragen hieronder in", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Bevestig je e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Gegevens die terugkomen van de verificatiemethode:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "<PERSON><PERSON><PERSON> v<PERSON>, acht<PERSON><PERSON><PERSON>, e-mail en wachtwoord in", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "<PERSON><PERSON>r je e-mail in", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "Hoe recent moeten gebruikers zijn geverif<PERSON>erd?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Identiteitsverificatie met {verificationMethod} (gebaseerd op gebruikersgroep)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "Er zijn geen acties nodig om deel te nemen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Gebruik slimme groepen om deelname te beperken op basis van bovenstaande geverifieerde gegevens", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Gebruikers moeten in de afgelopen 30 minuten zijn g<PERSON>.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Gebruikers moeten in de afgelopen {days} dagen zijn g<PERSON>.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "In de afgelopen 30 dagen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "In de afgelopen 30 minuten", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "In de afgelopen 7 dagen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "<PERSON><PERSON> keer is genoeg", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Geverifieerde velden:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} verificatie", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Account aan<PERSON>ken", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Deelnemers moeten een volledige account aanmaken met hun naam, bevestigde e-mailadres en wachtwoord.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Deelnemers moeten een volledige account aanmaken met hun naam, e-mailadres en wachtwoord.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Authenticatie", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "Bewerk", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Bevestiging per e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Deelnemers moeten hun e-mail bevestigen met een eenmalige code.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Geavanceerde spamdetectie", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "Deze functie helpt dubbele vragenlijstreacties van uitgelogde gebruikers te voorkomen door IP-adressen en apparaatgegevens te analyseren. <PERSON><PERSON><PERSON> dit niet zo nauwkeurig is als het vereisen van aanmelding, kan het helpen om het aantal dubbele reacties te verminderen.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Opmerking: <PERSON> g<PERSON>e netwerken (zoals kantoren of openbare Wi-Fi) is er een kleine kans dat verschillende gebruikers als duplicaten worden gemarkeerd.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Geavanceerde spamdetectie inschakelen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Extra vragen gesteld aan de<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "<PERSON><PERSON><PERSON> kan meedoen zonder zich aan te melden of in te loggen.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Reset extra vragen en groepen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Deelname beperken tot gebruikersgroep(en)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO-verificatie", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Deelnemers moeten hun identiteit verifiëren met {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Open AI-analyse", "app.containers.Admin.projects.project.survey.allFiles": "Alle bestanden", "app.containers.Admin.projects.project.survey.allResponses": "Alle reacties", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Nauwkeurigheid: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Er is een fout opgetreden bij het genereren van de AI-samenvatting. Probeer hem hieronder opnieuw te genereren.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Open AI-analyse", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "Verberg samenvattingen voor deze vraag", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "geselecteerde bi<PERSON>gen", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Open analyse acties", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} nieuwe reacties", "app.containers.Admin.projects.project.survey.analysis.regenerate": "Regenereer", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Toon AI-inzichten", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "Binnen je huidige abonnement kun je maximaal 30 bijdragen tegelijk samenvatten. Neem contact op met je Government Success Manager of platformbeheerder om meer te ontgrendelen.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Selecteer gere<PERSON><PERSON><PERSON> vragen voor analyse", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "Wil je nog andere gerelateerde vragen opnemen in je analyse van {question}?", "app.containers.Admin.projects.project.survey.cancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalButton": "Ga verder", "app.containers.Admin.projects.project.survey.consentModalCancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "<PERSON><PERSON> ga <PERSON><PERSON><PERSON><PERSON> met het geb<PERSON>ik van OpenAI als gegevensverwerker voor dit project", "app.containers.Admin.projects.project.survey.consentModalText1": "Door verder te gaan ga je a<PERSON><PERSON><PERSON> met het g<PERSON><PERSON><PERSON> van OpenAI als gegevensverwerker voor dit project.", "app.containers.Admin.projects.project.survey.consentModalText2": "De OpenAI API's voeden de geautomatiseerde samenvattingen van de tekst en delen van de geautomatiseerde tagging-ervaring.", "app.containers.Admin.projects.project.survey.consentModalText3": "We sturen alleen wat gebruikers schreven in hun vragenlijsten, ideeën en opmerkingen naar de OpenAI API's, nooit enige informatie van hun profiel.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI zal deze gegevens niet gebruiken voor verdere training van zijn modellen. Meer informatie over hoe OpenAI omgaat met privacygevoelige gegevens is te vinden op {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "hier", "app.containers.Admin.projects.project.survey.consentModalTitle": "<PERSON><PERSON><PERSON>t je verder gaat", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "Je kunt de analyse niet zien totdat je het formulier hebt bewerkt", "app.containers.Admin.projects.project.survey.deleteAnalysis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Weet je zeker dat je deze analyse wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.", "app.containers.Admin.projects.project.survey.explore": "Verken", "app.containers.Admin.projects.project.survey.followUpResponses": "Vervolgacties", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> gemiddelde", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Exporteren als GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Exporteer de reacties op deze vraag als een GeoJSON-bestand. Voor elke GeoJSON Feature worden alle vragenlijst-reacties van de betreffende respondent weergegeven in het object 'properties' van die Feature.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "Details verbergen", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} respondenten} one {{respondentCount} respondent} other {{respondentCount} respondenten}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "Bekijk details", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} keuzes} one {{numberChoices} keuze} other {{numberChoices} keuzes}}", "app.containers.Admin.projects.project.survey.heatMap": "Heat map", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Me<PERSON> informatie over heat maps die zijn gegener<PERSON>d met <PERSON><PERSON><PERSON> Mapping.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "De heat map is gege<PERSON><PERSON>d met Esri Smart Mapping. Heat maps zijn handig als er een grote hoeveelheid datapunten is. Voor minder punten kan het beter zijn om de locatiepunten individueel te bekijken. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Heat map weergave", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Verborgen door logica", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "Als een gebruiker dit antwoord selecteert, slaat de logica alle pagina's over tot pagina {pageNumber} ({numQuestionsSkipped} overgeslagen vragen). Klik om de overgeslagen pagina's en vragen te verbergen of te tonen.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "Als een gebruiker dit antwoord selecteert, gaat de logica naar het einde van de enquête ({numQuestionsSkipped} overgeslagen vragen). Klik op om de overgeslagen pagina's en vragen te verbergen of weer te geven.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "Logica op deze pagina slaat alle pagina's over tot pagina {pageNumber} ({numQuestionsSkipped} vragen overgeslagen). Klik om de overgeslagen pagina's en vragen te verbergen of te tonen.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Logica op deze pagina slaat over naar het einde van de vragenlijst ({numQuestionsSkipped} overgeslagen vragen). Klik om de overgeslagen pagina's en vragen te verbergen of weer te geven.", "app.containers.Admin.projects.project.survey.newAnalysis": "Nieuwe analyse", "app.containers.Admin.projects.project.survey.nextInsight": "Volgende inzicht", "app.containers.Admin.projects.project.survey.openAnalysis": "Open AI-analyse", "app.containers.Admin.projects.project.survey.otherResponses": "Andere reacties", "app.containers.Admin.projects.project.survey.page": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.previousInsight": "Vorig inzicht", "app.containers.Admin.projects.project.survey.responses": "Reacties", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "Het aantal reacties voor deze pagina is lager dan het totale aantal reacties op de vragenlijst, omdat sommige respondenten deze pagina niet hebben gezien vanwege logica in de vragenlijst.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "Het aantal reacties op deze vraag is lager dan het totale aantal reacties op de vragenlijst omdat sommige respondenten deze vraag niet gezien zullen hebben vanwege logica in de vragenlijst.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Sentiments<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Vat direct al je antwoorden samen.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "<PERSON><PERSON><PERSON> met je data in natuurlijke taal.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Krijg verwijzingen naar individuele reacties uit AI gegenereerde samenvattingen.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Kijk op {link} voor een volledig overzicht.", "app.containers.Admin.projects.project.survey.upsell.button": "Ontgrendel AI-analyse", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "supportartikel", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/nl/articles/8316692-ai-analyse", "app.containers.Admin.projects.project.survey.upsell.title": "Analyseer data sneller met AI", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "Deze functionaliteit is niet in je huidige abonnement inbegrepen. Neem contact op met je Government Success Manager of platformbeheerder om deze functionaliteit te ontgrendelen.", "app.containers.Admin.projects.project.survey.viewAnalysis": "Bekijk", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Ontdek samenvattingen op basis van AI en bekijk individuele inzendingen.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Selecteer periode", "app.containers.Admin.projects.project.traffic.trafficSources": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "We hebben de manier waarop we bezoekersgegevens verzamelen en weergeven veranderd. Hierdoor zijn de bezoekersgegevens nauwkeuriger en zijn er meer soorten gegevens beschik<PERSON>, terwijl we nog steeds voldoen aan de GDPR. We zijn pas in november 2024 begon<PERSON> met het verzamel<PERSON> van deze nieuwe gegevens, dus daarvoor zijn geen gegevens beschik<PERSON>.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Bezoekers tijdlijn", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Faserapport", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Voeg wat tekst over de fase toe", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "<PERSON><PERSON> is tekst. Je kunt het aanpassen en formatteren met be<PERSON><PERSON> van de editor in het paneel aan de rechterzijde.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Resultaten van het project", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "<PERSON><PERSON><PERSON><PERSON> van het verslag", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Voeg het doel van het project, de gebruikte participatiemethoden en het resultaat toe", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Bezoekers", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Dit rapport bevat niet-opgeslagen wijzigingen. Sla het op voordat je het afdrukt.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Deze titel is al in gebruik", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "<PERSON><PERSON><PERSON><PERSON><PERSON> met vorige {days} dagen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Verberg statistieken", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "<PERSON><PERSON> met vorige periode", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "Je moet eerst een datumbereik selecteren.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "Reacties", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Bijdragen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Opmerkingen tonen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "<PERSON><PERSON> bi<PERSON>gen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Toon stemmen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "Stemmen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Bereik registratiedatum", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Registratieveld", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Onbekend", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Gebruikers: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "Uitrekken", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Actief", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Gearchiveerd", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "Afgelopen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "Open einde", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projecten", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Publicatiestatus", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Gepubliceerd", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "De gegevens voor deze widget ontbreken. Configureer het opnieuw of verwijder het om het rapport op te kunnen slaan.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Gezondheidscore tevredenheidsmonitor", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "Kwart<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "Jaar", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "Geen geschikte fasen gevonden in dit project", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "<PERSON><PERSON> fase geselecteerd. Selecteer e<PERSON>t een fase.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "Geen project", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Geen project geselecteerd. Selecteer eerst een project.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "Je kunt dit rapport niet dupliceren omdat het gegevens bevat waartoe je geen toegang hebt.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "Je kunt dit rapport niet bewerken omdat het gegevens bevat waartoe je geen toegang hebt.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Weet je zeker dat je \"{reportName}\" wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Weet je zeker dat je dit rapport wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Bewerk", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "G<PERSON><PERSON><PERSON>zigd {days, plural, no {# dagen} one {# dag} other {# dagen}} geleden", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Er is een fout opgetreden bij het maken van dit rapport. <PERSON><PERSON>r het later nog eens.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "<PERSON><PERSON> met een lege pagina", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "<PERSON><PERSON> met een tevredenheids<PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "<PERSON><PERSON><PERSON> van het rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "Maak een rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Pas je rapport aan en deel het als PDF-bestand met interne be<PERSON><PERSON> of de community.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Maak een rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Maak je eerste rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "Geen project geselecteerd", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "<PERSON><PERSON> met een <PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Afdrukken naar PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "<PERSON><PERSON> met een <PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "K<PERSON>aal {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Sjabloon voor verslag", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "<PERSON>r best<PERSON><PERSON> al een rapport met deze titel. Kies een andere titel.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Selecteer kwart<PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Selecteer jaar", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "<PERSON>en als PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Print het rapport als PDF om het met i<PERSON><PERSON> te delen.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Delen als weblink", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "Deze weblink is alleen toe<PERSON> voor beheerders.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "<PERSON><PERSON>", "app.containers.Admin.reporting.contactToAccess": "Het maken van een aangepast rapport maakt deel uit van de premium licentie. Neem contact op met uw GovSuccess Manager voor meer informatie.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Alle rapporten", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Rapport van de tevredenheidsmonitor", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Deze rapporten horen bij de tevredenheidsmonitor. Rapporten worden elk kwartaal automatisch gegenereerd.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Maak een rapport", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Pas je rapport aan en deel het met inter<PERSON> <PERSON><PERSON> of de gemeenschap met een weblink.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "<PERSON><PERSON><PERSON> rapporten verschijnen hier.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Rapporten zoeken", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Voortgangsrapporten", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Dit zijn rapporten die zijn gemaakt door je Government Success Manager", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "<PERSON><PERSON><PERSON> rapport<PERSON>", "app.containers.Admin.reporting.deprecated": "VEROUDERD", "app.containers.Admin.reporting.helmetDescription": "Beheerderspagina voor rapporten", "app.containers.Admin.reporting.helmetTitle": "Rapportering", "app.containers.Admin.reporting.printPrepare": "Klaarmaken om af te drukken...", "app.containers.Admin.reporting.reportBuilder": "Rapportbouwer", "app.containers.Admin.reporting.reportHeader": "Koptekst rapport", "app.containers.Admin.reporting.warningBanner3": "Grafieken en getallen in dit rapport worden alleen automatisch bijgewerkt op deze pagina. Sla het rapport op om ze op andere pagina's bij te werken.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Raakvlakkenmethode", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Ideeën verz<PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Informatie delen", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Gebruikte methoden", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "Peiling", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "Vorige {days} dagen: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Voorstellen", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "<PERSON>terne enquête", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Stemming", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "<PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "<PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "Bekijk", "app.containers.Admin.surveyFormTab.downloads": "Downloads", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "<PERSON><PERSON> andere vragenlijst dupliceren", "app.containers.Admin.surveyFormTab.editSurveyForm": "Vragenlijstformulier bewerken", "app.containers.Admin.surveyFormTab.inputFormDescription": "Specificeer welke informatie moet worden verstrekt, voeg korte beschrijvingen of instructies toe om de antwoorden van deelnemers te sturen en specificeer of een veld optioneel of vereist is.", "app.containers.Admin.surveyFormTab.surveyForm": "Vragenlijstformulier", "app.containers.Admin.tools.apiTokens.createTokenButton": "Nieuw token aanmaken", "app.containers.Admin.tools.apiTokens.createTokenCancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Je token is aangema<PERSON>t. <PERSON><PERSON>er het {secret} hieronder en bewaar het veilig.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Maak een nieuw token om te gebruiken bij onze openbare API.", "app.containers.Admin.tools.apiTokens.createTokenError": "<PERSON><PERSON> een naam op voor je token", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Maak een token aan", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b><PERSON><PERSON><PERSON><PERSON>!</b> Je kunt dit {secret} maar <PERSON><PERSON> keer kopiëren. Als je dit venster sluit, kun je het niet meer zien.", "app.containers.Admin.tools.apiTokens.createTokenName": "<PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "<PERSON><PERSON> je token een naam", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Je token is aangemaakt", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Sluiten", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "<PERSON><PERSON><PERSON> {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Gekopieerd!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Maak een nieuw token", "app.containers.Admin.tools.apiTokens.createdAt": "Gemaakt", "app.containers.Admin.tools.apiTokens.delete": "<PERSON><PERSON><PERSON><PERSON><PERSON> token", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Weet je zeker dat je deze token wilt verwijderen?", "app.containers.Admin.tools.apiTokens.description": "Beheer je API tokens voor onze openbare API. Zie voor meer informatie onze {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Laatst gebruikt", "app.containers.Admin.tools.apiTokens.link": "API-documentatie", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "<PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.noTokens": "Je hebt nog geen tokens.", "app.containers.Admin.tools.apiTokens.title": "Openbare API tokens", "app.containers.Admin.tools.esriDisabled": "<PERSON> E<PERSON>ri-integratie is een add-on functie. Neem contact op met je Government Success Manager als je hier meer informatie over wilt.", "app.containers.Admin.tools.esriIntegration2": "Esri-integratie", "app.containers.Admin.tools.esriIntegrationButton": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.esriIntegrationDescription3": "<PERSON><PERSON> verb<PERSON> met je Esri-account en importeer g<PERSON><PERSON><PERSON> van ArcGIS Online rechtstreeks in je kaartprojecten.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri-logo", "app.containers.Admin.tools.esriKeyInputDescription": "Voeg je Esri API-sleutel toe om het importeren van je kaartlagen uit ArcGIS Online in de kaarttabbladen in projecten mogelijk te maken.", "app.containers.Admin.tools.esriKeyInputLabel": "Esri API-sleutel", "app.containers.Admin.tools.esriKeyInputPlaceholder": "<PERSON><PERSON> hier de <PERSON>-sleutel", "app.containers.Admin.tools.esriMaps": "Esri-<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.esriSaveButtonError": "Er is een fout opgetreden bij het opsla<PERSON> van je sleutel, probeer het opnieuw.", "app.containers.Admin.tools.esriSaveButtonSuccess": "API-sleutel opgeslagen", "app.containers.Admin.tools.esriSaveButtonText": "Sleutel opslaan", "app.containers.Admin.tools.learnMore": "Meer informatie", "app.containers.Admin.tools.managePublicAPIKeys": "Beheer API-sleutels", "app.containers.Admin.tools.manageWidget": "<PERSON><PERSON><PERSON> widget", "app.containers.Admin.tools.manageWorkshops": "Beheer workshops", "app.containers.Admin.tools.powerBIAPIImage": "Power BI-afbeelding", "app.containers.Admin.tools.powerBIDescription": "Gebruik onze plug & play Power BI-sjablonen om toegang te krijgen tot Go Vocal-gegevens in uw Microsoft Power BI-werkruimte.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI maakt geen deel uit van je licentie. Neem contact op met je GovSuccess Manager als je hier meer informatie over wilt.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Sjabloon downloaden", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "Als u van plan bent uw Go Vocal data te gebruiken binnen een Power BI data flow, kunt u met dit sjabloon een nieuwe data flow opzetten die aansluit op uw Go Vocal data. Nadat u deze sjabloon heeft gedownload, moet u eerst de volgende tekenreeksen ##CLIENT_ID## en ##CLIENT_SECRET## in de sjabloon zoeken en vervangen door uw openbare API-gegevens voordat u deze uploadt naar PowerBI.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Download data flow sjabloon", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Dataflow sjabloon", "app.containers.Admin.tools.powerBITemplates.intro": "Opmerking: als u een van deze Power BI-sjab<PERSON>n wilt geb<PERSON>iken, moet u eerst {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "maak een set referenties voor onze openbare API", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "Dit sjabloon maakt een Power BI-rapport op basis van je Go Vocal data. Het zal alle dataverbindingen met je Go Vocal platform opzetten, het datamodel en een aantal standaard dashboards maken. Als je het sjabloon opent in Power BI, wordt je gevraagd om je openbare API-referenties in te voeren. Je moet ook de Base Url voor je platform invoeren, die is: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Rapportagesjabloon downloaden", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Rapportagesjabloon", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "<PERSON><PERSON> informatie over het g<PERSON><PERSON><PERSON> van Go <PERSON> data in Power BI kun je vinden op {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "supportartikel", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/nl/articles/8512834-citizenlab-gegevens-gebruiken-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Power BI-sjablonen", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "<PERSON><PERSON><PERSON> de credentials om aangepaste integraties te maken op onze openbare API.", "app.containers.Admin.tools.publicAPIDisabled1": "De publieke API maakt geen deel uit van je huidige licentie. Neem contact op met je GovSuccess Manager als je hier meer informatie over wilt.", "app.containers.Admin.tools.publicAPIImage": "Openbare API-afbeelding", "app.containers.Admin.tools.publicAPITitle": "Openbare API-toegang", "app.containers.Admin.tools.toolsLabel": "Tools", "app.containers.Admin.tools.widgetDescription": "Je kunt een widget maken, deze aanpassen en aan je eigen website toevoegen om mensen naar dit platform te brengen.", "app.containers.Admin.tools.widgetImage": "Widget afbeelding", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "Houd live videovergaderingen, faciliteer gelijktijdige groepsdiscussies en debatten. Verzamel input, stem en bereik consensus, net zoals je dat offline zou doen.", "app.containers.Admin.tools.workshopsImage": "Workshops afbeelding", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/nl/articles/4155778-een-online-workshop-opzetten", "app.containers.Admin.tools.workshopsTitle": "Online overleg workshops", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "totaal aantal op het platform", "app.containers.AdminPage.DashboardPage._blank": "onbekend", "app.containers.AdminPage.DashboardPage.allGroups": "Alle groepen", "app.containers.AdminPage.DashboardPage.allProjects": "Alle projecten", "app.containers.AdminPage.DashboardPage.allTime": "Altijd", "app.containers.AdminPage.DashboardPage.comments": "Reacties", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Reacties", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Er is een basisdataset nodig om de representatie van platformgebruikers te meten.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Binnenkort", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "We werken momenteel aan het {fieldName}-dashboard, deze zal binnenkort beschik<PERSON>ar zijn", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{number<PERSON>f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> {# item is} andere {# items zijn}} verborgen in deze grafiek. Ga naar {tableViewLink} om alle gegevens te bekijken.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} voor gebruikerregistratie", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} van {total} gebruikers inbegrepen ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Toon {numberOfHiddenItems} meer", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Optioneel", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Geef een basisdataset op.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Representativiteitsscore:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Deze score geeft weer hoe nauwkeurig de gebruikersgegevens van het platform de totale populatie weerspiegelen. Meer informatie over {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Vere<PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Geef basisdataset op", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "tabelweergave", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Totale populatie", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Gebruikers", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} en ouder", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Leeftijdsgroep", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Leeftijdsgroep(en) van {upperBound} en ouder zijn niet inbegrepen.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Leeftijdgroep {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Leeftijdsgroepen", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "en ouder", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Voorbeeldgroepering toepassen", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Alles wissen", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Stel leeftijdsgroepen in zodat deze overeenkomen met je basisdataset.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Be<PERSON>ik", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Opsla<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "Tot en met", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Leeftijdsgroepen wijzigen", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Dit artikel zal niet worden berekend.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "<PERSON><PERSON> minder", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "<PERSON>ie {numberOfHiddenItems} meer...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Basismaand (optioneel)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Leeftijdsgroepen (geboortejaar)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Binnenkort", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Compleet", "app.containers.AdminPage.DashboardPage.components.Field.default": "Standaard", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Vul alle ingeschakelde opties in, of schakel de opties die je weg wilt laten uit de grafiek uit. Ten minste één optie moet ingeschakeld zijn.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Incompleet", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Aantal totale inwoners", "app.containers.AdminPage.DashboardPage.components.Field.options": "Opties", "app.containers.AdminPage.DashboardPage.components.Field.save": "Opsla<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Opgeslagen", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "<PERSON><PERSON><PERSON> {setAgeGroupsLink} om te beginnen met het invoeren van basisdata.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "leeftijdsgroepen instellen", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Gemiddelde reactietijd: {days} dagen", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Gemiddelde aantal dagen tot antwoord", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "feedback gegeven", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Invoeren op status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Aantal invoeren", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Officiële update", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Percentage invoeren", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Reactietijd", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Status aangepast", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Totaal", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Wijzig basisdataset", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "hoe we representativiteitsscores berekenen", "app.containers.AdminPage.DashboardPage.continuousType": "<PERSON><PERSON><PERSON> ti<PERSON>", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.customDateRange": "Eigen", "app.containers.AdminPage.DashboardPage.day": "dag", "app.containers.AdminPage.DashboardPage.false": "fout", "app.containers.AdminPage.DashboardPage.female": "vrouw", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Top 5 bijdragen op basis van reacties", "app.containers.AdminPage.DashboardPage.fromTo": "van {from} tot {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Dashboard voor activiteiten op het platform", "app.containers.AdminPage.DashboardPage.helmetTitle": "Admin - Dashboard", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Kies type data om per project te tonen", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Kies type data om per tag te tonen", "app.containers.AdminPage.DashboardPage.inputs1": "Bijdragen", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Bijdragen per status", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Selecteer geb<PERSON><PERSON><PERSON><PERSON>ep", "app.containers.AdminPage.DashboardPage.male": "man", "app.containers.AdminPage.DashboardPage.month": "maand", "app.containers.AdminPage.DashboardPage.noData": "<PERSON>r zijn geen gegevens om te tonen", "app.containers.AdminPage.DashboardPage.noPhase": "Geen fase aangemaakt voor dit project", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Het aantal deelnemers dat bijdragen heeft gepost, heeft geliked of een reactie heeft gegeven.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "Dislikes", "app.containers.AdminPage.DashboardPage.numberOfLikes": "<PERSON>s", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Totaal aantal likes/dislikes", "app.containers.AdminPage.DashboardPage.overview.management": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projecten en participatie", "app.containers.AdminPage.DashboardPage.overview.showLess": "Toon minder", "app.containers.AdminPage.DashboardPage.overview.showMore": "Toon meer", "app.containers.AdminPage.DashboardPage.participants": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.participationPerProject": "Participatie per project", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Participatie per tag", "app.containers.AdminPage.DashboardPage.perPeriod": "Per {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Laatste 30 dagen", "app.containers.AdminPage.DashboardPage.previous90Days": "Laatste 90 dagen", "app.containers.AdminPage.DashboardPage.previousWeek": "Vorige week", "app.containers.AdminPage.DashboardPage.previousYear": "<PERSON><PERSON>g jaar", "app.containers.AdminPage.DashboardPage.projectType": "Projecttype : {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "Likes/dislikes", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Deze basisdataset is nodig om de representativiteit van platformgebruikers te berekenen ten opzichte van de totale populatie.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Geef een basisdataset op.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Bekijk hoe representatief jouw platformgebruikers zijn in vergelijking met de totale populatie - op basis van gegevens die zijn verzameld tijdens de gebruikersregistratie. Meer informatie over {representativiteitArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Bekijk hoe representatief jouw platformgebruikers zijn in vergelijking met de totale populatie - op basis van gegevens die zijn verzameld tijdens de gebruikersregistratie.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Meet de representativiteit van je platform", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Terug naar dashboard", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "<PERSON><PERSON> van de ingeschakelde registratievelden wordt momenteel ondersteund.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Hier kun je items weergeven/verbergen op het dashboard en de basisdata invoeren. Enkel de ingeschakelde velden voor {userRegistrationLink} zullen hier verschijnen.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Wijzig basisdataset", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "gebruikersregistratie", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Geef basisdataset op", "app.containers.AdminPage.DashboardPage.resolutionday": "per dag", "app.containers.AdminPage.DashboardPage.resolutionmonth": "per maand", "app.containers.AdminPage.DashboardPage.resolutionweek": "per week", "app.containers.AdminPage.DashboardPage.selectProject": "Selecteer project", "app.containers.AdminPage.DashboardPage.selectedProject": "huidige projectfilter", "app.containers.AdminPage.DashboardPage.selectedTopic": "huidige tag-filter", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Ontdek wat er gebeurt op je platform.", "app.containers.AdminPage.DashboardPage.tabOverview": "Overzicht", "app.containers.AdminPage.DashboardPage.tabReports": "Projecten", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Representativiteit", "app.containers.AdminPage.DashboardPage.tabUsers": "Gebruikers", "app.containers.AdminPage.DashboardPage.timelineType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.titleDashboard": "Dashboard", "app.containers.AdminPage.DashboardPage.total": "Totaal", "app.containers.AdminPage.DashboardPage.totalForPeriod": "Deze {period}", "app.containers.AdminPage.DashboardPage.true": "echt", "app.containers.AdminPage.DashboardPage.unspecified": "onbe<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.users": "Gebruikers", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Gebruikers per leeftijd", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Gebruikers per geografisch gebied", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Gebruikers per geslacht", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Registraties", "app.containers.AdminPage.DashboardPage.week": "week", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "It has to be a simple enough image to be seen in very little. It should be a square PNG. It can use transparency. If it doesn't, prefer a white background. This should be set once and changed as little as possible.", "app.containers.AdminPage.FaviconPage.save": "Opsla<PERSON>", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Er ging iets mis. Probeer het opnieuw.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Opgeslagen!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Je wijzigingen zijn opgeslagen.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Voeg toe", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Mapbeheerders", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Mapbeheerders kunnen de mapbeschrijving bewerken, kunnen binnen de map nieuwe projecten aanmaken en kunnen alle projecten binnen een map beheren. Ze kunnen geen projecten verwijderen en hebben geen toegang tot projecten die niet in hun map staan. Je kunt {projectManagementInfoCenterLink} meer informatie vinden over projectbeheer-rechten.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Geen match gevonden", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "Bezoek ons Helpcentrum", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Zoek gebruikers", "app.containers.AdminPage.FoldersEdit.addToFolder": "Toevoegen aan de map", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Gearchiveerd", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Verwijder deze map", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Beschrijving", "app.containers.AdminPage.FoldersEdit.draftStatus": "Concept", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Voeg bestanden toe aan deze map", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Bestanden mogen niet groter zijn dan 50Mb. Toegevoegde bestanden worden getoond op de pagina van de map.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Beschrijvingen", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "<PERSON>r staan geen projecten in deze map. Ga terug naar het tabblad Projecten om projecten aan te maken en toe te voegen.", "app.containers.AdminPage.FoldersEdit.folderName": "Mapnaam", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Bannerafbeelding", "app.containers.AdminPage.FoldersEdit.multilocError": "Alle tekstvelden moeten voor elke taal worden ingevuld.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Er zijn geen projecten die je aan deze map kan toevoegen.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Mapafbeelding", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Toegangsrechten", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Projecten in de map", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Instellingen", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projecten toegevoegd aan deze map", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "<PERSON>en die je kan toevoegen aan deze map", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Kies of deze map \"concept\", \"gepubliceerd\" of \"gearchiveerd\" is.", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Gepubliceerd", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Verwijderen uit de map", "app.containers.AdminPage.FoldersEdit.save": "Opsla<PERSON>", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Er ging iets mis. Probeer het opnieuw.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Succes!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Je wijzigingen zijn opgeslagen.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "<PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "Getoond op de homepagina", "app.containers.AdminPage.FoldersEdit.statusLabel": "Status van de map", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON> wa<PERSON>m deze projecten same<PERSON>, bepaal een visuele identiteit en deel informatie.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON> wa<PERSON>m deze projecten same<PERSON>, bepaal een visuele identiteit en deel informatie.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Alle tekstvelden moeten worden ingevuld.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Titel", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Maak een nieuwe map aan", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Instellingen", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Bekijk de map", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "<PERSON>beelding en -tekst bewerken.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Hero-banner", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Hero-banner opslaan", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Het inspiratiecentrum is een plek waar je inspiratie kunt opdoen voor je projecten door projecten op andere platforms te bekijken.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Wi<PERSON>zig de algemene voorwaarden van je platform. <PERSON><PERSON> pagina's, wa<PERSON><PERSON> de <PERSON> en FAQ pagina's, kunnen gewijzigd worden in de {navigationLink} tab.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Beleidspagina's", "app.containers.AdminPage.PagesEdition.privacy-policy": "Privacybeleid", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Gebruiksvoorwaarden", "app.containers.AdminPage.Project.confirmation.description": "Deze actie kan niet ongedaan worden gemaakt.", "app.containers.AdminPage.Project.confirmation.no": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.confirmation.title": "Weet je zeker dat je alle deelnamegegevens opnieuw wilt instellen?", "app.containers.AdminPage.Project.confirmation.yes": "Reset alle deelnamegegevens", "app.containers.AdminPage.Project.data.descriptionText1": "<PERSON><PERSON><PERSON><PERSON><PERSON> idee<PERSON>, opmerking<PERSON>, stemmen, reacties, reacties op vragenlijsten, polls, vrijwilligers en geregistreerden voor activiteiten. In het geval van stemfasen wist deze actie de stemmen, maar niet de opties.", "app.containers.AdminPage.Project.data.title": "Wis alle deelnamegegevens van dit project", "app.containers.AdminPage.Project.resetParticipationData": "Reset alle deelnamegegevens", "app.containers.AdminPage.Project.settings.accessRights": "Toegangsrechten", "app.containers.AdminPage.Project.settings.back": "Vorige", "app.containers.AdminPage.Project.settings.data": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.description": "Beschrijving", "app.containers.AdminPage.Project.settings.events": "Activiteiten", "app.containers.AdminPage.Project.settings.general": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.projectTags": "Project tags", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Lijst van projecten op het platform", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Projecten dashboard", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "<PERSON>ak nieuwe projecten aan of beheer bestaande projecten.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projecten", "app.containers.AdminPage.ProjectDashboard.published": "Gepubliceerd", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Instellingspaneel sluiten", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Midden", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "Volledige breedte", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "Links", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "Knopuitlijning", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Knoptekst", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "<PERSON><PERSON><PERSON>op<PERSON> in", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "Primair", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Knoptype", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Secundair", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "Knop-URL", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "<PERSON><PERSON><PERSON> een knop-U<PERSON> in", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Kolom layout", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Volledige beschrijving", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Beschrijving op homepagina", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Weergegeven op de projectkaart op de homepagina.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Weergegeven op de projectpagina. Beschrijf du<PERSON>lijk waarover je project gaat, wat je verwacht van je gebruikers en wat zij mogen verwachten van jou.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Er ging iets mis. Probeer het opnieuw.", "app.containers.AdminPage.ProjectDescription.preview": "Voorvertoning", "app.containers.AdminPage.ProjectDescription.save": "Opsla<PERSON>", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Je wijzigingen werden opgeslagen.", "app.containers.AdminPage.ProjectDescription.saved": "Opgeslagen!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> een heldere boodschap over het project. <PERSON><PERSON>g a<PERSON>bee<PERSON>en, video's en bijlagen toe aan het project.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Projectbeschrijving", "app.containers.AdminPage.ProjectDescription.whiteSpace": "Witruimte", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Rand opnemen", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "Verticale hoogte", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "Groot", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "<PERSON> breed<PERSON><PERSON> van het middel<PERSON> van de <PERSON>. Accepteert een waarde tussen -90 en 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "<PERSON>ng<PERSON> van het middel<PERSON> van de <PERSON>. Accepteert een waarde tussen -180 en 180.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Bewerk laag", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Bewerk laag", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Er ging iets mis. Probeer het opnieuw.", "app.containers.AdminPage.ProjectEdit.MapTab.here": "hier", "app.containers.AdminPage.ProjectEdit.MapTab.import": "GeoJSON-bestand importeren", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Standaard breedtegraad", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Deze kleur wordt toegepast op alle onderdelen van de kaart<PERSON>ag. De g<PERSON> van de markers, de lijndikte en de transparantie zijn standaard ingesteld.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Markeringsicoon", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Optioneel: selecteer een icoon dat in de markers wordt weergegeven. Klik op {url} om de lijst van iconen te zien die je kan selecteren.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Deze naam wordt getoond in de legend<PERSON> van <PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Laag tooltip", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Deze tekst wordt weergegeven als een tooltip wanneer je met de muis over de onderdelen in de laag op de kaart gaat", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Lagen", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "We ondersteunen momenteel GeoJSON-bestanden. <PERSON>s het {supportArticle} voor tips over het converteren en stijlen van kaartlagen.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Standaard lengtegraad", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Middelpunt en zoom-nive<PERSON> van <PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Het standaard middelpunt en zoom-niveau van de kaart. Pas de waarden hieronder handmatig aan, of klik op de {button} knop in de linkerbovenhoek van de kaart om het huidige middelpunt en zoom-niveau van de kaart op te slaan als de standaardwaarden.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "<PERSON><PERSON> <PERSON> ka<PERSON>weergave aan door kaartlagen te uploaden en te stylen en door het middelpunt en zoom-niveau in te stellen.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "<PERSON><PERSON> configuratie", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "De kaartconfiguratie is momenteel hetzelfde voora alle fases, je kunt niet verschillende kaartconfiguraties per fase maken.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Laag verwijderen", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Opsla<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "<PERSON>m op<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "support-artike<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/nl/articles/7022129-input-en-feedback-verzamelen-lijst-en-kaartweergave", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Na<PERSON><PERSON><PERSON> laag", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Standaard zoom-niveau", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Stel in hoe de kaart standaard is ingezoomd. Kies een waarde tussen 0 en 20, waarbij 0 volledig uitgezoomd is (de hele wereld is zichtbaar) en 20 volledig ingezoomd is (blokken en gebouwen zijn zichtbaar)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Alle gebruikersgegevens anonimiseren", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Alle vragenlijst-input van de gebruikers wordt geanonimiseerd voordat hij wordt opgenomen", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Gebruikers zullen nog steeds moeten voldoen aan de deelnamevereisten onder het tabblad 'Toegangsrechten'. Gebruikersprofielgegevens zullen niet beschik<PERSON> zijn in de export van enquêtegegevens.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "Als je deze optie inschakelt, worden de registratievelden voor gebruikers als laatste pagina in de vragenlijst weergegeven in plaats van als onderdeel van het aanmeldingsproces.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Demografische velden in vragenlijstformulier", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Demografische velden in vragenlijst weergeven?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "<PERSON><PERSON> meer over hoe automatisch delen werkt in dit artikel.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Resultaten automatisch delen", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "De stemresultaten worden gedeeld op het platform en via e-mail aan de deelnemers wanneer de fase eindigt. Dit zorgt standaard voor transparantie.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Resultaat delen", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Voeg een antwoordmogelijkheid toe", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Voeg een peiling<PERSON><PERSON> toe", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Bewerk antwoordmogelijkheid", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Antwoordmogelijkheden opslaan", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Bewerk antwoordmogelijkheid", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "Bewerk vraag", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Exporteer de peilingresult<PERSON>n", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Het maximum aantal keuzes is groter dan het aantal opties", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Meerdere keuzes", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "Geen opties", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "Nog niet alle vragen hebben opties, waard<PERSON> de peiling niet kan beantwoord worden", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "<PERSON><PERSON><PERSON><PERSON> hebben maar <PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "<PERSON><PERSON><PERSON>woordmogelijkheden voor: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "peiling_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Hier kun je peilingvragen maken, de antwoordmogelijkheden instellen waaruit deelnemers kunnen kiezen voor elke vraag, be<PERSON><PERSON><PERSON> of deelnemers slechts één antwoordmogelijkheid (enkelvoudige keuze) of meerdere antwoordmogelijkheden (meerkeuze) kunnen kiezen, en de peilingresultaten exporteren. Je kunt meerdere peilingvragen binnen één peiling maken.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Opsla<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "Opsla<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "Opsla<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Instellingen en resultaten van de peilingen", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Verkeerd maximum", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Importeren", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "<PERSON><PERSON><PERSON> hier de bijdrages van alle projecten. Geef feedback, voeg tags toe, verander de status of verplaats bijdragen van het ene project naar het andere.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "<PERSON><PERSON><PERSON>, geef feedback en wijs onderwerpen toe.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "He<PERSON> del<PERSON> van resultaten is uitgeschakeld.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "De stemresultaten worden niet gedeeld aan het einde van de fase, tenzij je dit aanpast in de fase-instelling.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Deze resultaten worden automatisch gedeeld zodra de fase eindigt. Wijzig de einddatum van deze fase om aan te passen wanneer de resultaten worden gedeeld.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Exporteer de enquêteresultaten (.xslx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Resultaten", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Hier kan je de resultaten van de Typeform-enquête(s) binnen dit project downloaden als een Excel-bestand.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "Vragenlijstformulier", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Enquêteresultaten", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Raadpleeg de antwoorden op de enquête", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Voeg een actie toe", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "<PERSON><PERSON> weten?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Beschrijving", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Gebruik dit om uit te leggen wat er van de vrijwilligers wordt verlangd en wat zij kunnen verwachten.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Kon niet opslaan omdat het formulier fouten bevat.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Afbeelding", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Titel", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Bewerk", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Een actie is een activiteit waarvoor mensen zich als vrijwilliger kunnen opgeven.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Bewerk de actie", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "<PERSON><PERSON> be<PERSON>n", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "<PERSON><PERSON> titel toe<PERSON>n", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Een actie is een activiteit waarvoor mensen zich als vrijwilliger kunnen opgeven.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "Nieuwe actie", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Opsla<PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Hier kan je de vrijwilligersacties instellen en de lijst met vrij<PERSON><PERSON><PERSON>.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Vrijwilligersacties", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {nog geen vrijwilligers} one {# vrijwilliger} other {# vrijwilligers}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "toewijzing budget", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Ken een budget toe aan opties en vraag de deelnemers om hun voorkeursopties te kiezen die binnen het totale budget passen.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Toewijzing budget", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Gebruikers toestaan om commentaar te geven kan het stemproces beïnvloeden.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "Credit", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Standaard weergave van opties", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Acties voor gebruikers", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Selecteer welke extra acties gebruikers kunnen ondernemen.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Vastgesteld aantal", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "Als dit leeg wordt gelaten, zal dit standaard op \"stem\" staan.", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "<PERSON><PERSON> meer over wanneer je het beste <b> {voteTypeDescription} </b> kunt gebruiken in ons {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Maximaal aantal stemmen per optie", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Maximaal aantal stemmen", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Je kunt het aantal stemmen dat een gebruiker in totaal kan uitbrengen beperken (met een maximum van één stem per optie).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "meerdere stemmen per optie", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Gebruikers krijgen een hoeveelheid tokens om te verdelen over opties", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Meerdere stemmen per optie", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Aantal stemmen per gebruiker", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "supportartikel", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Opties om op te stemmen", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "Punt", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "één stem per optie", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Gebruikers kunnen ervoor kiezen om een van de opties goed te keuren", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Eén stem per optie", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Token", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Onbeperkt", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Hoe moet een stem heten?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "Bijv. tokens, punten, CO2-kredieten...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "Bijv. token, punt, CO2-krediet...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Elke stemmethode heeft verschillende pre-configuraties", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "Stemmethode", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "De stemmethode bepaalt de regels voor hoe gebruikers stemmen", "app.containers.AdminPage.ProjectEdit.addNewInput": "Voeg een invoer toe", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "Je kunt je project nu aan een map toevoegen of dit later doen in de projectinstellingen", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Toegestane invoer-tags", "app.containers.AdminPage.ProjectEdit.altText": "Alt-tekst", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "<PERSON><PERSON><PERSON><PERSON> peiling", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "Bij een anonieme peiling is het onmogelijk om te zien wie waarvoor koos. Deelnemers moeten zich wel nog steeds registreren en kunnen slechts eenmaal hun stem uitbrengen.", "app.containers.AdminPage.ProjectEdit.approved": "Goedgekeurd", "app.containers.AdminPage.ProjectEdit.archived": "Gearchiveerd", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Gearchiveerde projecten zijn nog steeds zich<PERSON><PERSON>, maar laten geen verdere deelname toe", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Gearchiveerd", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Dit gebied kan niet worden verwijderd omdat het wordt gebruikt om projecten te tonen op de volgende meer aangepaste pagina('s). Je moet het gebied los<PERSON>ppelen van de pagina, of de pagina verwijderen voordat je het gebied kunt verwijderen.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Alle gebieden", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Het project wordt getoond bij elke gebiedsfilter.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "G<PERSON><PERSON>sfilter", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projecten kunnen op de homepage worden gefilterd met behulp van gebieden. Gebieden kunnen worden ingesteld {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "hier", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "<PERSON><PERSON> specifiek gebied", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Het project wordt niet getoond als er op gebied wordt gefilterd.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Selecteer een gebied", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Het project wordt getoond bij geselecteerde gebiedsfilters.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "In een lijst", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "Meest besproken", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Vragenlijst-inhoud toe<PERSON>n", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "De inzendingen voor deze vragenlijst beginnen binnen te komen. Wijzigingen aan de vragenlijst kunnen leiden tot gegevensverlies en onvolledige gegevens in de geëxporteerde bestanden.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Vragenlijst succesvol opgeslagen", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "Vragenlijst", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "Bekijk vragenlijst", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Kies een stemmethode en laat gebruikers prioriteiten stellen tussen een paar verschillende opties.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Stemmen of prioriteiten stellen", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Maak een project aan met een sja<PERSON><PERSON>on", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Een externe enquête maken", "app.containers.AdminPage.ProjectEdit.createInput": "Nieuwe bijdrage toevoegen", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Maak een enquête op het platform", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Stel een enquête op zonder ons platform te verlaten.", "app.containers.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON> poll aan<PERSON>ken", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Stel een meerkeuze-vragenlijst op.", "app.containers.AdminPage.ProjectEdit.createProject": "Nieuw project", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Integreer een Typeform-, Google Forms- of Enalyzer-enquête.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Je kunt de standaardvolgorde instellen voor berichten die op de hoofdpagina van het project dienen te worden weergegeven.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "So<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.departments": "Departementen", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Beschrijving", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "Hiermee wordt disliking in- of uitgeschakeld, liken blijft nog steeds aanstaan. We raden aan om disliking uitgeschakeld te laten, tenzij je een optieanalyse uitvoert.", "app.containers.AdminPage.ProjectEdit.disabled": "Uitgeschakeld", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Aantal dislikes per de<PERSON><PERSON>mer", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "<PERSON><PERSON><PERSON><PERSON> ins<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Verzamel feedback op een document", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Voeg een interactieve PDF in en verzamel reacties en feedback met Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Uitgeschakeld", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Ingeschakeld", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Conceptprojecten zijn verborgen voor alle mensen behalve beheerders en toegewezen projectmanagers.", "app.containers.AdminPage.ProjectEdit.draft": "Concept", "app.containers.AdminPage.ProjectEdit.draftStatus": "Concept", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabled": "Ingeschakeld", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Selecteer welke participatieve acties gebruikers kunnen ondernemen.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Activiteiten", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Bijlagen worden getoond op de informatiepagina van het project.", "app.containers.AdminPage.ProjectEdit.filesTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.findVolunteers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Vra<PERSON> de deelnemers om zich als vrijwilliger op te geven voor een reeks acties.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "Als mapbeheerder kun je een map kiezen bij het maken van het project, maar alleen een beheerder kan deze achteraf wijzigen.", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Mapkaart afbeelding alternatieve tekst", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Selecteer de map waar je dit project aan wilt toevoegen.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Aangepaste inhoud", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "De inzendingen voor dit formulier beginnen binnen te komen. Wijzigingen in het formulier kunnen leiden tot gegevensverlies en onvolledige gegevens in de geëxporteerde bestanden.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Formulier succesvol opgeslagen", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "<PERSON><PERSON> en<PERSON>u<PERSON>", "app.containers.AdminPage.ProjectEdit.fromATemplate": "<PERSON> van een sja<PERSON><PERSON>on", "app.containers.AdminPage.ProjectEdit.generalTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Formulieren", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Header afbeelding alternatieve text", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Bannerafbeelding", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NIEUW", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Informatie aan gebruikers geven of gebruik de rapport vormgever om resultaten van eerdere fases te delen.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Informatie of resultaten delen", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Input en/of feedback verzamelen", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Creëer en/of verzamel bijdragen, likes en/of reacties. Kies tussen verschillende soorten bijdragen: ideeën, opties, projecten, bijdragen, stellingen of reacties.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Wie is verantwoordelijk voor de verwerking van de bijdragen?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Alle nieuwe bijdragen in dit project worden aan deze persoon toegewezen. De toegewezen persoon kan worden gewijzigd in de {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Reageren op bijdragen", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Invulformulier", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "bijdragebeheerder", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Bijdragen toevoegen", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Bijdragen 'liken' en 'unliken'", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Standaardweergave", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "De standaardweergave van de bijdragen van de deelnemer kiezen: kaarten in een rasterweergave of pins op een kaart. Deelnemers kunnen handmatig schakelen tussen de twee weergaven.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Inspiratiecentrum", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Konveio URL", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Aantal likes per de<PERSON><PERSON>mer", "app.containers.AdminPage.ProjectEdit.limited": "Beperkt", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "<PERSON><PERSON> s<PERSON>n tonen", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Op een kaart", "app.containers.AdminPage.ProjectEdit.mapTab": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxDislikes": "Maximaal aantal dislikes", "app.containers.AdminPage.ProjectEdit.maxLikes": "Maximaal aantal likes", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Maximum aantal stemmen per optie moet kleiner zijn dan of gelijk aan totaal aantal stemmen", "app.containers.AdminPage.ProjectEdit.maximum": "Maximum", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "De deelnemers mogen dit budget niet overschrijden bij de indiening van hun mandje.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft formulieren", "app.containers.AdminPage.ProjectEdit.minimum": "Minimum", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Eis dat deelnemers aan een minimumbudget voldoen om hun mandje in te dienen (vul '0' in als je geen minimum wenst in te stellen).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "in ons Helpcentrum", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Wie zijn de projectbeheerders?", "app.containers.AdminPage.ProjectEdit.moreDetails": "Meer details", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "Inspiratie nodig? Bekijk soortgelijke projecten van andere steden in het {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "Voeg een bijdrage toe", "app.containers.AdminPage.ProjectEdit.newIdea": "<PERSON><PERSON><PERSON> idee", "app.containers.AdminPage.ProjectEdit.newInitiative": "<PERSON>en initiatief toe<PERSON>n", "app.containers.AdminPage.ProjectEdit.newIssue": "Voeg een reactie toe", "app.containers.AdminPage.ProjectEdit.newOption": "Voeg een optie toe", "app.containers.AdminPage.ProjectEdit.newPetition": "<PERSON><PERSON> petitie toe<PERSON>n", "app.containers.AdminPage.ProjectEdit.newProject": "Nieuw project", "app.containers.AdminPage.ProjectEdit.newProposal": "<PERSON><PERSON> voorstel toevoegen", "app.containers.AdminPage.ProjectEdit.newQuestion": "Voeg een vraag toe", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Meest recent", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "<PERSON><PERSON> geldig bedrag", "app.containers.AdminPage.ProjectEdit.noFolder": "Geen map", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "— Geen map —", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "<PERSON><PERSON> s<PERSON> gevonden", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Dit mag niet leeg zijn", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Geef het max. aantal toegestane stemmen per gebruiker", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Oudste", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "<PERSON><PERSON> voor beheerders", "app.containers.AdminPage.ProjectEdit.optionNo": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.optionYes": "Ja (selecteer map)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Participatieniveaus", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Wat wil je doen?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Be<PERSON><PERSON> hoe gebruikers kunnen deelnemen.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "Je kunt aangeven wie elke actie mag uit<PERSON>eren, en deelnemers aanvullende vragen stellen om meer informatie te verzamelen.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Vereisten & vragen voor deelname", "app.containers.AdminPage.ProjectEdit.pendingReview": "In afwachting van goedkeuring", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Toegangsrechten", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "Toegangsrechten", "app.containers.AdminPage.ProjectEdit.phaseEmails": "Meldingen", "app.containers.AdminPage.ProjectEdit.pollTab": "Peiling", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "De meeste reacties", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Projectafbeelding", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Deze afbeelding maakt deel uit van de projectkaart; de kaart die het project samenvat en bijvoorbeeld op de homepage wordt getoond. Voor meer informatie over aanbevolen beeldresoluties, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Map", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Deze afbeelding staat bovenaan de projectpagina. Voor meer informatie over aanbevolen beeldresoluties, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Projectkaart afbeelding alternatieve tekst", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "<PERSON><PERSON> een korte beschrij<PERSON> van de afbeelding voor gebruikers met een visuele beperking. <PERSON>t help<PERSON> schermlezers over te brengen waar de afbeelding over gaat.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Projectmanagers kunnen projecten bewerken, bijdragen beheren en deelnemers e-mailen. <PERSON> kan {moderationInfoCenterLink} meer informatie vinden over de rechten die aan projectmanagers zijn toegekend.", "app.containers.AdminPage.ProjectEdit.projectName": "Projectnaam", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Projecttype", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Bepaal of een project al dan niet een tijdslijn heeft. Projecten met een tijdslijn hebben een duidelijk begin en eind en kunnen verschillende fases hebben. Projecten zonder tijdslijn zijn continu.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Het projecttype kan later niet meer worden gewijzigd.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "Je kunt het project zo instellen dat het onzichtbaar is voor bepaalde gebruikers.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Zichtbaar<PERSON><PERSON> van het project", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Op zoek naar de projectstatus? Nu kun je die op elk moment direct vanuit de bovenkant van de projectpagina wijzigen.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Gepubliceerde projecten zijn zichtbaar voor iedereen of voor een subset van een groep als die geselecteerd is.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Gepubliceerd", "app.containers.AdminPage.ProjectEdit.purposes": "Doelstellingen", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Reset deelname<PERSON>s", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Er is een fout opgetreden bij het opslaan van je gegeven<PERSON>. Probeer het opnieuw.", "app.containers.AdminPage.ProjectEdit.saveProject": "Opsla<PERSON>", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Opgeslagen!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Je gegevens zijn opgeslagen!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Zoeken in de sjablonen", "app.containers.AdminPage.ProjectEdit.selectGroups": "<PERSON><PERSON> g<PERSON>(en)", "app.containers.AdminPage.ProjectEdit.setup": "Configuratie", "app.containers.AdminPage.ProjectEdit.shareInformation": "Informatie delen", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Snap Enquête", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Maak je project aan en personaliseer het.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "bekijk onze supportpagina", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Enquête-<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# keuzes} one {# keuze} other {# keuzes}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON><PERSON>, ik wil weg", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "Bewerk", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "De inzendingen voor deze vragenlijst beginnen binnen te komen. Wijzigingen aan de vragenlijst kunnen leiden tot gegevensverlies en onvolledige gegevens in de geëxporteerde bestanden.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "Bestand uploaden", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "Ga terug", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Importeren", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Importeren", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "AI samenvattingen voor korte antwoord, lange antwoord en sentiment schaal vervolgvragen zijn <PERSON> via de AI tab in de linker zijbalk.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Lineaire schaal", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "Matrix", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "Lang antwoord", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Meerkeuze - kies er veel", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Afbeelding keuze - kies er meer", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "Nieuwe inzending", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Nog geen reacties op de enquête", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Open voor reacties", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Open voor reacties", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Optioneel", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "Als er geen logica is toege<PERSON><PERSON>d, zal de enquête zijn normale verloop volgen. Als zowel de pagina als de vragen logica hebben, krijgt de logica van de vraag voorrang. Zorg ervoor dat dit overeenkomt met het beoogde vragenlijstontwerp. Ga voor meer informatie naar {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "Locatie", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Weet u zeker dat u wilt vertrekken?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Je huidige wijzigingen worden niet opgeslagen.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Rangschikking", "app.containers.AdminPage.ProjectEdit.survey.rating": "Beoordeling", "app.containers.AdminPage.ProjectEdit.survey.required2": "Vere<PERSON>", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {reacties} one {reactie} other {reacties}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# reacties} one {# reacties} other {# reacties}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Meerkeuze - kies er <PERSON>én", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Sentiment lineaire schaal", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Esri shapefile uploaden", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Enquête succesvol opgeslagen", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Antwoorden op de vragenlijst", "app.containers.AdminPage.ProjectEdit.survey.text2": "<PERSON><PERSON> antwoord", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Totaal {count} antwoorden", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "Bekijk", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Embed URL", "app.containers.AdminPage.ProjectEdit.surveyService": "Programma", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Be<PERSON>al welk enquêteprogramma je wil gebruiken. Je vindt alle informatie {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/nl/articles/7025887-een-extern-enqueteproject-maken", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "hier", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Deze tag kan niet verwijderd worden omdat hij gebruikt wordt om projecten weer te geven op de volgende meer aangepaste pagina('s). \nJe moet de tag van de pagina los<PERSON>, of de pagina verwijderen voordat je de tag kunt verwijderen.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Algemene projectinstellingen", "app.containers.AdminPage.ProjectEdit.titleLabel": "Projectnaam", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "<PERSON>es een korte, heldere en krachtige titel. Het wordt getoond op de projectkaarten op de homepagina en in de dropdown in de navbar.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Tags", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Selecteer {topicsCopy} voor dit project. Gebruikers kunnen deze gebruiken om projecten mee te filteren.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Totaalbedrag", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.unlimited": "Onbeperkt", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "<PERSON><PERSON> sja<PERSON><PERSON><PERSON> geb<PERSON>iken", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "Bekijk het project", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Vrijwilligersacties", "app.containers.AdminPage.ProjectEdit.voteTermError": "Stemvoorwaarden moeten worden opgegeven voor alle talen", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# groepen} one {# groep} other {# groepen}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Een activiteit toevoegen", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Extra informatie", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Adres 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "<PERSON><PERSON> van de locatie van het evenement", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Adres 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "Bijv. appartement, gebouw", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Aanvullende adresinformatie die kan helpen bij het identificeren van de locatie, zoa<PERSON> de naam van het gebouw, het verdiepingsnummer, etc.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/nl/articles/5481527-evenementen-toevoegen-aan-je-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "<PERSON>ie het support-artikel", "app.containers.AdminPage.ProjectEvents.customButtonLink": "Externe link", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Voeg een link naar een externe URL toe (bijv. een evenementenservice of ticketwebsite). <PERSON><PERSON> je dit instelt, wordt het standaardgedrag van de aanwezigheidsknop overschreven.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Tekst aangepaste knop", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "<PERSON><PERSON> de <PERSON> van de knop naar een andere waarde dan \"Registreren\" als er een externe URL is ingesteld.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Start", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "Einde", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Weet u zeker dat u de activiteit wilt verwijderen? Dit is definitief!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Beschrijving van de activiteit", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Bewerk", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Activiteit bewerken", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "Om geregistreerden rechtstreeks vanuit het platform te mailen, moeten beheerders een gebruikersgroep maken op het tabblad {userTabLink}. {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "Data evenement", "app.containers.AdminPage.ProjectEvents.eventImage": "Afbeelding event", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Evenement afbeelding alternatieve tekst", "app.containers.AdminPage.ProjectEvents.eventLocation": "<PERSON><PERSON><PERSON> evenement", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "<PERSON><PERSON><PERSON> g<PERSON>", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Bijlagen worden op de activiteitkaart zelf weergegeven.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Locatie", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Maximaal aantal geregistreerden", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Maak een nieuwe activiteit aan", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Link naar online evenement", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "Als je evenement online is, voeg hier dan een link naar het evenement toe.", "app.containers.AdminPage.ProjectEvents.preview": "Preview", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Locatie verfijnen op de kaart", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Locatie verfijnen op de kaart", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "Je kunt verfijnen waar de locatiemarker van je activiteit wordt weergegeven door op de kaart hieronder te klikken.", "app.containers.AdminPage.ProjectEvents.register": "Registreer", "app.containers.AdminPage.ProjectEvents.registerButton": "Registreerknop", "app.containers.AdminPage.ProjectEvents.registrant": "geregistreerde", "app.containers.AdminPage.ProjectEvents.registrants": "gere<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Limiet geregis<PERSON>rden", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Opsla<PERSON>", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "We konden je gegevens niet op<PERSON>, probeer het a.j.b. opnieuw.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Opgeslagen!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Je wijzigingen zijn opgeslagen.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "<PERSON><PERSON> naar een locatie", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Voeg offline activiteiten, <PERSON><PERSON><PERSON><PERSON><PERSON> of vergaderingen toe die gerelateerd zijn aan dit project.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Titel en data", "app.containers.AdminPage.ProjectEvents.titleEvents": "Projectactiviteiten", "app.containers.AdminPage.ProjectEvents.titleLabel": "<PERSON><PERSON> activiteit", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "<PERSON><PERSON> de knop aan een externe URL", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "Standaard wordt de in-platform activiteit-registratieknop getoond waarmee gebruikers zich voor een activiteit kunnen registreren. Je kunt dit wijzigen zodat er in plaats daarvan een link naar een externe URL wordt weergegeven.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Het aantal geregistreerden voor activiteiten beperken", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Stel een maximumaantal geregistreerden voor de activiteit in. Als de limiet is bereikt, worden er geen registraties meer geaccepteerd.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/users", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Gebruikers", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "<PERSON><PERSON>en toe<PERSON>n aan je project", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Voeg bestanden uit deze lijst toe aan je project, fasen en activiteiten.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Bestanden als context toevoegen aan Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Voeg bestanden toe aan je Sensemaking project om context en inzichten te geven.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Binnenkort beschikbaar", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Synchroniseer <PERSON>ragenli<PERSON><PERSON>, upload interviews en laat AI de punten verbinden in je gegevens.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Upload een bestand", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "AI gebruiken om bestanden te analyseren", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Transcripties verwerken, enz.", "app.containers.AdminPage.ProjectFiles.addFiles": "<PERSON><PERSON><PERSON> bestanden toe", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "AI-gestuurde inzichten", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "Analyseer geüploade bestanden om belangrijke onderwerpen aan het licht te brengen.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "<PERSON><PERSON> gea<PERSON> analyses van deze bestanden mogeli<PERSON> met <PERSON><PERSON><PERSON> van AI-verwerking.", "app.containers.AdminPage.ProjectFiles.askButton": "Vraag", "app.containers.AdminPage.ProjectFiles.categoryLabel": "Categorie", "app.containers.AdminPage.ProjectFiles.chooseFiles": "<PERSON><PERSON><PERSON> kiezen", "app.containers.AdminPage.ProjectFiles.close": "Sluiten", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Bevestigen en uploaden", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Weet je zeker dat je dit bestand wilt verwijderen?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "Markdown-bestand kon niet worden geladen.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "CSV-voorvertoning kon niet worden geladen.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "<PERSON>r worden maximaal 50 rijen getoond in CSV-voorvertoningen.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "CSV-bestand is te groot om te bekijken.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "Bestand verwijderen", "app.containers.AdminPage.ProjectFiles.description": "Beschrijving", "app.containers.AdminPage.ProjectFiles.done": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.downloadFile": "Bestand downloaden", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Volledig bestand downloaden", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Sleep bestanden hierheen of", "app.containers.AdminPage.ProjectFiles.editFile": "Bestand bewerken", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Beschrijving", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "Bestandsnaam mag geen punt bevatten.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "Bestandsnaam", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "<PERSON> bestand<PERSON> is vereist.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Bestand downloaden", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Voorvertoning", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "Dit bestand wordt niet geüpload, omdat het de maximale limiet van 50 MB overschrijdt.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "Alle bestanden succesvol geüpload", "app.containers.AdminPage.ProjectFiles.generatingPreview": "Voorbeeld genereren...", "app.containers.AdminPage.ProjectFiles.info_sheet": "Informatie delen", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "Bijv. WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Audio-interviews, opnames van in het stadhuis", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "Bijv. PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "<PERSON><PERSON><PERSON>, informatieve documenten", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "Bijv. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "Afbeeldingen", "app.containers.AdminPage.ProjectFiles.interview": "Interview", "app.containers.AdminPage.ProjectFiles.maxFilesError": "Je kunt maximaal {maxFiles} bestanden tegelijk uploaden.", "app.containers.AdminPage.ProjectFiles.meeting": "Vergadering", "app.containers.AdminPage.ProjectFiles.noFilesFound": "<PERSON><PERSON> bestanden gevonden.", "app.containers.AdminPage.ProjectFiles.other": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.policy": "Beleid", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Voorvertoning wordt nog niet ondersteund voor dit bestandstype.", "app.containers.AdminPage.ProjectFiles.report": "Rapport", "app.containers.AdminPage.ProjectFiles.retryUpload": "Uploaden opnieuw proberen", "app.containers.AdminPage.ProjectFiles.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "Bestand succesvol bijgewerkt.", "app.containers.AdminPage.ProjectFiles.searchFiles": "<PERSON><PERSON><PERSON> z<PERSON>", "app.containers.AdminPage.ProjectFiles.selectFileType": "<PERSON>rt bestand", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Strategisch plan", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "Je kunt maximaal {maxFiles} bestanden tegelijk uploaden.", "app.containers.AdminPage.ProjectFiles.unknown": "Onbekend", "app.containers.AdminPage.ProjectFiles.upload": "Uploaden", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# bestand} other {# bestanden}} succesvol geüpload, {numberOfErrors, plural, one {# fout} other {# fouten}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "<PERSON><PERSON> be<PERSON>jken", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Alle velden samen<PERSON>wen", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Beschrijving van het veld", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Bewerk invulformulier", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Ingeschakeld", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Dit veld toevoegen.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Er ging iets mis. Probeer het opnieuw.", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Alle velden uitvouwen", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Invulformulier", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Specificeer welke informatie moet worden verstrekt, voeg korte beschrijvingen of instructies toe om de antwoorden van deelnemers te sturen en specificeer of een veld optioneel of vereist is.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Specificeer welke informatie moet worden verstrekt, voeg korte beschrijvingen of instructies toe om de antwoorden van deelnemers te sturen en specificeer of elk veld optioneel of vereist is.", "app.containers.AdminPage.ProjectIdeaForm.required": "Vere<PERSON>", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Vereisen dat dit veld wordt ingevuld.", "app.containers.AdminPage.ProjectIdeaForm.save": "Opsla<PERSON>", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "<PERSON> wijzigingen zijn met succes opgeslagen.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Opgeslagen!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "<PERSON><PERSON><PERSON> formulier", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Geautomatiseerde e-mails", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Je kunt e-mails configureren die op faseniveau worden getriggerd", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Data", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "Vul de vragenlijst in", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Vragenlijst", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Weet u zeker dat u deze fase wilt verwijderen?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Fasebeschrijving", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "Fase bewerken", "app.containers.AdminPage.ProjectTimeline.endDate": "Einddatum", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Einddatum", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Maak een nieuwe fase aan", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "Deze fase heeft geen vooraf vastgestelde einddatum.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Bij sommige methoden wordt het delen van resultaten (zoals stemresultaten) pas geactiveerd als er een einddatum is geselecteerd.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "<PERSON><PERSON><PERSON> je een fase na deze fase toevoegt, wordt er een einddatum aan deze laatste fase toegevoegd.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "Als je geen e<PERSON>datum selecteert, betekent dit dat:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Preview", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "Wijzigingen opslaan", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Er is een fout opgetreden bij het verzenden, probeer het a.j.b. opnieuw.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Opgeslagen!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "<PERSON> wijzigingen zijn met succes opgeslagen.", "app.containers.AdminPage.ProjectTimeline.startDate": "Startdatum", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Begint op", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "Knop", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "<PERSON><PERSON><PERSON> van <PERSON> vragenlijst", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Fasenaam", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Bijlagen uploaden", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_opdeling", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologie (voorpaginafilter)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Hoe moeten tags in de voorpaginafilter genoemd worden? Bv. tags, categorieën, afdelingen, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Tags kunnen {topicManagerLink} aangepast worden.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "hier", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Term voor één tag (enkelvoud)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tag", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Term voor meerdere tags (meervoud)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "tags", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "<PERSON><PERSON> vraag <PERSON>n", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "<PERSON><PERSON> nieuw registratieveld toevoegen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Voeg een optie toe", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Antwoordformaat", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "<PERSON><PERSON>r een antwo<PERSON>sja<PERSON> in", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Antwoordmogelijkheid", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "<PERSON><PERSON>r een antwoordop<PERSON> in voor elke taal", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Antwoordoptie opslaan", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Antwoordoptie succesvol opgeslagen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Antwoordmogelijkheden", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Sleep de velden om te bepalen in welke volgorde deze getoond worden in het registratieformulier.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Standaard", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Verwijderen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Optionele tekst onder het veld tijdens de registratie.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "De antwoordopties voor de woonplaats kunnen worden ingesteld in de {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Bewerk", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Bewerk de antwoordoptie", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Beschrijving", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "<PERSON><PERSON> van het veld", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Voer een veldnaam voor elke taal in", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Veldinstellingen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "<PERSON><PERSON><PERSON><PERSON><PERSON> (selectievakje)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Datum", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Lang antwoord", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "<PERSON><PERSON><PERSON><PERSON> (selecteer meerdere)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Numerieke waarde", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "<PERSON><PERSON><PERSON><PERSON> (selecteer een)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "<PERSON><PERSON> antwoord", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "tab Geografische gebieden", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Verborgen veld", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Is het beantwoorden van dit veld verplicht?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Bepaal de registratievelden", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Voeg een optie toe", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Verwijderen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Weet je zeker dat je deze antwoordoptie voor registratievragen wilt verwijderen? Alle records die specifieke gebruikers met deze optie hebben beantwoord worden permanent verwijderd. Deze actie kan niet ongedaan worden gemaakt.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Weet je zeker dat je deze registratievraag wilt verwijderen? Alle antwoorden die gebruikers op deze vraag hebben gegeven zullen permanent worden verwijderd, en de vraag zal niet meer worden gesteld in projecten of voorstellen. Deze actie kan niet ongedaan worden gemaakt.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Vere<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Veld succesvol opgeslagen", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "<PERSON>wee kolommen", "app.containers.AdminPage.SettingsPage.addAreaButton": "Voeg een geografisch gebied toe", "app.containers.AdminPage.SettingsPage.addTopicButton": "Voeg een tag toe", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Gebruiker - bijv. Gebruiker 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "Selecteer welke beheerders meldingen ontvangen om projecten goed te keuren. Mapbeheerders zijn standaard goedkeurders voor alle projecten in hun mappen.", "app.containers.AdminPage.SettingsPage.approvalSave": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.approvalTitle": "Instellingen voor goedkeuring van projecten", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Weet je zeker dat je dit gebied wil verwijderen?", "app.containers.AdminPage.SettingsPage.areaTerm": "Term voor één <PERSON> (enkelvoud)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "geb<PERSON>", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Bewerk", "app.containers.AdminPage.SettingsPage.areasTerm": "Term voor meerdere gebieden (meervoud)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "gebieden", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Selecteer ten minste <PERSON><PERSON>.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Avatars weergeven", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "<PERSON><PERSON> pro<PERSON><PERSON><PERSON>'s en getalle<PERSON> van <PERSON> aan niet geregistreerde bezoekers", "app.containers.AdminPage.SettingsPage.bannerHeader": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Bannertitel voor niet-geregistreerde bezoekers", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Banner ondertitel voor niet-geregistreerde bezoekers", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Sub-header-tekst", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Bannertekst", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Toon voorvertoning voor", "app.containers.AdminPage.SettingsPage.brandingDescription": "Voeg je logo toe en stel de platformkleuren in.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Branding", "app.containers.AdminPage.SettingsPage.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.chooseLayout": "Lay-out", "app.containers.AdminPage.SettingsPage.color_primary": "<PERSON><PERSON>d<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.color_secondary": "Secundaire kleur", "app.containers.AdminPage.SettingsPage.color_text": "Tekstkleur", "app.containers.AdminPage.SettingsPage.colorsTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.confirmHeader": "Weet je zeker dat je deze tag wilt verwijderen?", "app.containers.AdminPage.SettingsPage.contentModeration": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.ctaHeader": "Knoppen", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "G<PERSON>son<PERSON><PERSON><PERSON> paginaheader | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Eigen", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Tekst op de knop", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Knoplink", "app.containers.AdminPage.SettingsPage.defaultTopic": "Standaard-tag", "app.containers.AdminPage.SettingsPage.delete": "Verwijderen", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Verwijderen", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Hier<PERSON> wordt het onderwerp verwijderd, inclusief alle bestaande bijdragen. Deze wijziging zal van toepassing zijn op alle projecten.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Tags kunnen worden toegevoegd om bijdrages te categoriseren. Hier kun je tags toevoegen en verwijderen die je op je platform wilt gebruiken. Je kunt de tags aan specifieke projecten toevoegen in de {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Bureaublad", "app.containers.AdminPage.SettingsPage.editFormTitle": "Bewerk gebied", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Bewerken", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Bewerk tag", "app.containers.AdminPage.SettingsPage.fieldDescription": "Besch<PERSON><PERSON><PERSON> van g<PERSON>", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "<PERSON><PERSON> be<PERSON>rijving is enkel voor interne samenwerking zodat het voor iedereen duidelijk is wat met dit gebied wordt bedoeld.", "app.containers.AdminPage.SettingsPage.fieldTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.fieldTitleError": "<PERSON><PERSON><PERSON> voor elke taal een naam in", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "De naam die je voor elk gebied kiest is zicht<PERSON>ar voor gebruikers tijdens hun registratie en bij het filteren van de projecten.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Tag opslaan", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "<PERSON><PERSON> tag", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "<PERSON><PERSON><PERSON> voor elke taal een tagnaam in", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "De naam die je voor elke tag kiest zal zichtbaar zijn voor de deelnemers.", "app.containers.AdminPage.SettingsPage.fixedRatio": "Vaste verhouding", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Dit bannertype werkt het beste met afbeeldingen die niet mogen worden bijgesneden, zoals afbeeldingen met teks<PERSON>, een logo of specifieke elementen die cruciaal zijn voor je burgers. Deze banner wordt vervangen door een effen kader in de primaire kleur wanneer gebruikers zijn aangemeld. Je kunt deze kleur instellen in de algemene instellingen. Meer info over het aanbevolen gebruik van afbeeldingen kun je vinden op onze {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "kennisbank", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Volledige breedte", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Deze banner strekt zich uit over de volle breedte voor een geweldig visueel effect. De afbeelding zal proberen zoveel mogelijk ruimte te bedekken, waardoor hij niet altijd zichtbaar is. Je kunt deze banner combineren met een filter in eender welke kleur. Meer info over het aanbevolen gebruik van afbeeldingen vind je op onze {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "kennisbank", "app.containers.AdminPage.SettingsPage.header": "Banner startpagina", "app.containers.AdminPage.SettingsPage.headerDescription": "Personaliseer de startpagina bannerafbeelding en -tekst.", "app.containers.AdminPage.SettingsPage.header_bg": "Bannerafbeelding", "app.containers.AdminPage.SettingsPage.helmetDescription": "Admin - instellingen", "app.containers.AdminPage.SettingsPage.helmetTitle": "Admin - instellingen", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Voeg je eigen inhoud toe aan de personaliseerbare sectie onderaan de startpagina.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Startpaginaheader | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "<PERSON><PERSON><PERSON> banner", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Opaciteit van de banner voor mensen die niet ingelogd zijn", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Detecteer ongepaste inhoud", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Automatisch detecteren van ongepaste inhoud die op het platform wordt geplaatst.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "Als deze functie is ingeschakeld, worden de door deelnemers geplaatste bijdragen, voorstellen en opmerkingen automatisch beoordeeld. Berichten die gemarkeerd zijn als mogelijk ongepaste inhoud worden niet geb<PERSON>, maar worden gemarkeerd voor beoordeling op de pagina {linkToActivityPage}.", "app.containers.AdminPage.SettingsPage.languages": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Bepaal de talen waarin je platform beschikbaar wordt gemaakt aan de gebruikers. Gebruikers kunnen zelf eenvoudig hun gewenste taal uit deze lijst selecteren via een knop in de navbar.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Activiteit", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Plaats hier de achtergrondfoto", "app.containers.AdminPage.SettingsPage.no_button": "<PERSON><PERSON> knop", "app.containers.AdminPage.SettingsPage.organizationName": "<PERSON><PERSON> gem<PERSON> of organisatie", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "<PERSON><PERSON> een organisatienaam of stad voor alle talen.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Filter inschakelen", "app.containers.AdminPage.SettingsPage.phone": "Telefoon", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Platform configuratie", "app.containers.AdminPage.SettingsPage.population": "Inwoneraantal", "app.containers.AdminPage.SettingsPage.populationMinError": "Inwoneraantal moet een positief getal zijn.", "app.containers.AdminPage.SettingsPage.populationTooltip": "Het totale aantal inwoners op je grondgebied. Dit wordt gebruikt om de participatiegraad te berekenen. Laat leeg als het niet van toepassing is.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Scheldwoordenfilter", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Blokkeer bijdragen, voorstellen en reacties die de vaakst voorkomende scheldwoorden bevatten", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Deze lijn tekst staat op de startpagina boven de projecten.", "app.containers.AdminPage.SettingsPage.projectsSettings": "projectinstellingen", "app.containers.AdminPage.SettingsPage.projects_header": "Koptekst projecten", "app.containers.AdminPage.SettingsPage.registrationFields": "Gebruikersgegevens", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "<PERSON><PERSON> een korte beschrijving aan de bovenkant van het registratieformulier.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registratiegegevens", "app.containers.AdminPage.SettingsPage.save": "Opsla<PERSON>", "app.containers.AdminPage.SettingsPage.saveArea": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Er ging iets mis. Probeer het opnieuw.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Opgeslagen!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Je wijzigingen zijn opgeslagen.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Goedkeurders selecteren", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "Selecteer de gebieden die worden getoond aan gebruikers om te volgen na registratie", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Selecteer de thema's die worden getoond aan gebruikers om te volgen na registratie", "app.containers.AdminPage.SettingsPage.settingsSavingError": "<PERSON>n niet opsla<PERSON>. Probeer de instelling opnieuw te veranderen.", "app.containers.AdminPage.SettingsPage.sign_up_button": "‘Aanmelden’", "app.containers.AdminPage.SettingsPage.signed_in": "Knop voor geregistreerde bezoekers", "app.containers.AdminPage.SettingsPage.signed_out": "Knop voor niet geregistreerde bezoekers", "app.containers.AdminPage.SettingsPage.signupFormText": "Hulptekst bij registratie", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Voeg een korte beschrijving toe bovenaan het registratieformulier.", "app.containers.AdminPage.SettingsPage.statuses": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.step1": "Stap 1: e-mail en wachtwoord", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Dit extra tekstje staat optioneel bovenaan bij de eerste stap tijdens het registreren (naam, e-mail, wachtwoord).", "app.containers.AdminPage.SettingsPage.step2": "Stap 2: bijkomende registratiegegevens", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Dit extra tekstje staat optioneel bovenaan bij de tweede stap tijdens het registreren (bijkomende registratiegegevens).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Definieer je geografi<PERSON> gebieden, zoa<PERSON> bij<PERSON><PERSON><PERSON><PERSON> wijk<PERSON>, deelgemeentes of kernen. Je kan gebieden koppelen aan projecten en je kan ze gebruiken om Slimme Groepen aan te maken en toegangsrechten te bepalen.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "<PERSON>oeg de naam van je organisatie, gemeente of stad, de url van je website toe, alsook de talen waarin dit platform beschikbaar moet zijn.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "De ondertitel overschrijdt het maximaal aantal toegestane 90 karakters", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "<PERSON><PERSON> aan welke informatie mensen wordt gevraagd te verstrekken wanneer ze zich aanmelden.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminologie", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Instellingen succesvol bijgewerkt.", "app.containers.AdminPage.SettingsPage.tabAreas1": "Gebieden", "app.containers.AdminPage.SettingsPage.tabBranding": "Branding", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Input statussen", "app.containers.AdminPage.SettingsPage.tabPolicies": "Beleidspagina's", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Goedkeuring project", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registratie", "app.containers.AdminPage.SettingsPage.tabSettings": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabTopics2": "Tags", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Hoe moeten de geografische gebieden naar gebruikers toe worden genoemd? bv. wi<PERSON><PERSON>, kernen, buurten, ...", "app.containers.AdminPage.SettingsPage.titleAreas": "Geografische gebieden", "app.containers.AdminPage.SettingsPage.titleBasic": "Algemene instellingen", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "<PERSON> ondertitel overschrijdt het maximaal aantal toegestane 35 karakters", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Tags", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Deze banner is vooral handig bij afbeeldingen die niet goed werken met tekst uit de titel, ondertitel of knop. Deze items worden onder de banner geschoven. Meer informatie over het aanbevolen gebruik van afbeeldingen kun je vinden op onze {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "kennisbank", "app.containers.AdminPage.SettingsPage.twoRowLayout": "Twee rijen", "app.containers.AdminPage.SettingsPage.urlError": "De URL is niet geldig.", "app.containers.AdminPage.SettingsPage.urlPatternError": "<PERSON><PERSON><PERSON> een geldige URL in.", "app.containers.AdminPage.SettingsPage.urlTitle": "Website", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Voeg de URL toe van de website die je aan dit platform wil linken. Deze wordt gebruikt in de footer op de homepagina.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "Kies hoe gebruikers zonder naam in hun profiel worden weergegeven in het platform. Dit gebeurt wanneer je de toegangsrechten voor een fase instelt op 'E-mailbevestiging'. In alle gevallen kunnen gebruikers bij deelname de profielnaam bijwerken die we automatisch voor hen hebben gegenereerd.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Weergave gebruikersnaam (voor gebruikers met alleen een bevestigd emailadres)", "app.containers.AdminPage.SideBar.administrator": "Platformbeheerder", "app.containers.AdminPage.SideBar.communityPlatform": "Community platform", "app.containers.AdminPage.SideBar.community_monitor": "Tevredenheidsmonitor", "app.containers.AdminPage.SideBar.customerPortal": "Klantenportaal", "app.containers.AdminPage.SideBar.dashboard": "Dashboard", "app.containers.AdminPage.SideBar.emails": "E-mails", "app.containers.AdminPage.SideBar.folderManager": "Mapbeheerder", "app.containers.AdminPage.SideBar.groups": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.guide": "Gids", "app.containers.AdminPage.SideBar.inputManager": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.insights": "Rapportering", "app.containers.AdminPage.SideBar.inspirationHub": "Inspiratiecentrum", "app.containers.AdminPage.SideBar.knowledgeBase": "Kennisbank", "app.containers.AdminPage.SideBar.language": "Taal", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Pa<PERSON><PERSON>'s en menu", "app.containers.AdminPage.SideBar.messaging": "Berichten", "app.containers.AdminPage.SideBar.moderation": "Activiteit", "app.containers.AdminPage.SideBar.notifications": "Meldingen", "app.containers.AdminPage.SideBar.processing": "Verwerking", "app.containers.AdminPage.SideBar.projectManager": "Projectbeheerder", "app.containers.AdminPage.SideBar.projects": "Projecten", "app.containers.AdminPage.SideBar.settings": "Instellingen", "app.containers.AdminPage.SideBar.signOut": "Afmelden", "app.containers.AdminPage.SideBar.support": "Support", "app.containers.AdminPage.SideBar.toPlatform": "Naar platform", "app.containers.AdminPage.SideBar.tools": "Tools", "app.containers.AdminPage.SideBar.user.myProfile": "<PERSON><PERSON> pro<PERSON>", "app.containers.AdminPage.SideBar.users": "Gebruikers", "app.containers.AdminPage.SideBar.workshops": "Workshops", "app.containers.AdminPage.Topics.addTopics": "Toevoegen", "app.containers.AdminPage.Topics.browseTopics": "Blader door de tags", "app.containers.AdminPage.Topics.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.confirmHeader": "Weet je zeker dat je deze tag wilt verwijderen?", "app.containers.AdminPage.Topics.delete": "Verwijderen", "app.containers.AdminPage.Topics.deleteTopicLabel": "Verwijderen", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "<PERSON>t onderwerp zal niet langer in projecten worden gebruikt.", "app.containers.AdminPage.Topics.inputForm": "Invulformulier", "app.containers.AdminPage.Topics.lastTopicWarning": "Er is minimaal één tag vereist. Als je geen tags wilt gebruiken, kun je deze uitschakelen op het tabblad {ideaFormLink}.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Je kunt de tags toevoegen en verwijderen die aan bijdragen in dit project kunnen worden toegewezen.", "app.containers.AdminPage.Topics.remove": "Verwijderen", "app.containers.AdminPage.Topics.title": "Toegestane invoer-tags", "app.containers.AdminPage.Topics.topicManager": "Tags", "app.containers.AdminPage.Topics.topicManagerInfo": "Als je extra project-tags wil toe<PERSON><PERSON><PERSON>, kan je dat doen op de pagina {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Voeg een groep toe", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "<PERSON><PERSON>", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "<PERSON>oer een gro<PERSON> in", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Maak een manuele groep aan", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Welk type groep heb je nodig?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/nl/articles/7043801-slimme-en-handmatige-gebruikersgroepen-gebruiken", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Maak een manuele groep aan", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Maak een slimme groep aan", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "<PERSON>er informatie over gro<PERSON>en", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Je geeft handmatig aan welke gebruikers deel uitmaken van deze groep.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Je stelt voorwaarden waarmee mensen automatisch en op continue wijze deel worden van deze groep.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "<PERSON><PERSON>", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "<PERSON><PERSON> g<PERSON>ep", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Er is nog niemand in deze groep", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Ga naar {allUsersLink} om handmatig gebruikers toe te voegen.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "<PERSON>r zijn geen gebruikers die overeenkomen met je zoekopdracht", "app.containers.AdminPage.Users.GroupsPanel.select": "Selecteer", "app.containers.AdminPage.Users.UsersGroup.exportAll": "<PERSON><PERSON><PERSON> alles", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Exporteer gebruikers in groep", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Geselecteerde export", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "<PERSON><PERSON> weten?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Er is een fout opgetreden tijdens het toevoegen van gebruikers aan de gro<PERSON>, gelieve opnieuw te proberen.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Verwijderen uit de groep", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Geselecteerde gebruikers verwijderen uit deze groep?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Er is een fout opgetreden tijdens het verwijderen van geb<PERSON> van de <PERSON>, gelieve opnieuw te proberen.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Voeg gebruikers toe aan groep", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Toevoegen", "app.containers.AdminPage.groups.permissions.add": "Voeg toe", "app.containers.AdminPage.groups.permissions.addAnswer": "Voeg antwoord toe", "app.containers.AdminPage.groups.permissions.addQuestion": "Demografische vragen toevoegen", "app.containers.AdminPage.groups.permissions.answerChoices": "Antwoordmogelijkheden", "app.containers.AdminPage.groups.permissions.answerFormat": "Antwoordformaat", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "Er moet ten minste één keuze worden gemaakt", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "Deze gebruiker modereert de map met dit project. Om hun moderatorrechten voor dit project te verwijderen, kun je hun mapbeheerrechten intrekken of het project naar een andere map verplaatsen.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Maak een nieuwe vraag", "app.containers.AdminPage.groups.permissions.createAQuestion": "Maak een vraag", "app.containers.AdminPage.groups.permissions.defaultField": "Standaard veld", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Verwijderen", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "<PERSON><PERSON> alle keuzes een titel", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "<PERSON><PERSON><PERSON><PERSON><PERSON> (selectievakje)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Datum", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Lang antwoord", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "<PERSON><PERSON><PERSON><PERSON> (selecteer meerdere)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Numerieke waarde", "app.containers.AdminPage.groups.permissions.fieldType_select": "<PERSON><PERSON><PERSON><PERSON> (kies er één)", "app.containers.AdminPage.groups.permissions.fieldType_text": "<PERSON><PERSON> antwoord", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Het wijzigen van rechten op gedetailleerd niveau maakt geen deel uit van je licentie. Neem contact op met je Government Success Manager voor meer informatie.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Weet je zeker dat je deze groep uit dit project wilt verwijderen?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Selecteer <PERSON><PERSON> of meer groepen", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {<PERSON><PERSON> leden} one {<PERSON><PERSON> lid} other {{count} leden}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Vul de titel alsjeblieft in alle talen in", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "<PERSON><PERSON> weten?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Geen projectbeheerders gevonden", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Er wordt niets getoond, want er zijn geen acties die de gebruiker kan nemen in dit project.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Alleen admins kunnen een nieuwe vraag aanmaken.", "app.containers.AdminPage.groups.permissions.option1": "Optie 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Uitnodiging in behandeling", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Wie kan het document annoteren?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Wie kan zich aanmelden voor een activiteit?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Wie kan reageren op bijdragen?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Wie kan reageren op voorstellen?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Wie kan een voorstel plaatsen?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Wie kan bijdragen liken/unliken?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Wie kan bijdragen indienen?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Wie kan deelnemen aan de peiling?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Wie kan de vragenlijst invullen?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Wie kan vrijwilliger worden?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Wie kan stemmen op voorstellen?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Wie mag stemmen?", "app.containers.AdminPage.groups.permissions.questionDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> van de v<PERSON>ag", "app.containers.AdminPage.groups.permissions.questionTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.save": "Opsla<PERSON>", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Er ging iets mis. Probeer het opnieuw.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Succes!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Je wijzigingen zijn opgeslagen.", "app.containers.AdminPage.groups.permissions.select": "Selecteer", "app.containers.AdminPage.groups.permissions.selectValueError": "Selecteer een type antwoord", "app.containers.AdminPage.new.createAProject": "Maak een project aan", "app.containers.AdminPage.new.fromScratch": "Start van nul", "app.containers.AdminPage.phase.methodPicker.addOn1": "Add-on", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "AI-gestuurde inzichten", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Help deelnemers om overeenstemming en onenigheid aan de opperv<PERSON>te te brengen, <PERSON><PERSON> idee per keer.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Zoek raakvlakken", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Voeg een interactieve PDF in en verzamel reacties en feedback met Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Verzamel feedback op een document", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Sluit een enquête van een derde partij in", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "<PERSON>terne enquête", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Maak gebruik van de collectieve intelligentie van je gebruikers. Nodig ze uit om ideeën in te sturen, te bespreken en/of feedback te geven in een openbaar forum.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "<PERSON><PERSON><PERSON><PERSON> input en feedback in het openbaar", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Informatie delen", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Mist ingebouwde AI-gestuurde inzichten", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Mist ingebouwde rapportage en datavisualisatie en -verwerking", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "<PERSON><PERSON> met ingebouwde rapport vorm<PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.logic1": "Logica", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Breed scala aan vraagtypes", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Laat deelnemers ideeën uploaden met een tijdslimiet en een stemmenlimiet.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "<PERSON><PERSON>ste<PERSON>, petities of initiatieven", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "<PERSON><PERSON>e peiling", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Stel een korte meerkeuzevragenlijst op.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "<PERSON>f informatie aan g<PERSON>, visualiseer resultaten uit eerdere fases, maak rapporten met veel gegevens.", "app.containers.AdminPage.phase.methodPicker.survey1": "Vragenlijst", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Begrijp de behoeften en denkwijze van je gebruikers via een breed scala aan persoonlijke vraagtypes.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Enquête-opties", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Maak een enquête", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Vraag gebruikers om zich op te geven als vrijwilliger voor activiteiten en doelen of zoek deelnemers voor een panel.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "<PERSON><PERSON> of vrijwilligers", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Kies een stemmethode en laat gebruikers prioriteiten stellen tussen een paar verschillende opties.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Stemmen of prioriteiten stellen", "app.containers.AdminPage.projects.all.all": "Alle", "app.containers.AdminPage.projects.all.createProjectFolder": "Nieuwe map", "app.containers.AdminPage.projects.all.existingProjects": "Bestaande projecten", "app.containers.AdminPage.projects.all.homepageWarning1": "Gebruik deze pagina om de volgorde van projecten in te stellen in de \"Alle projecten\" vervolgkeuzelijst in de navigatiebalk. Als je de widgets \"Gepubliceerde projecten en mappen\" of \"Projecten en mappen (legacy)\" op je startpagina gebruik<PERSON>, wordt de volgorde van de projecten in deze widgets ook bepaald door de volgorde die je hier instelt.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "<PERSON><PERSON> waar je Projectbeheerder van bent verschijnen hier.", "app.containers.AdminPage.projects.all.noProjects": "<PERSON>n projecten gevonden.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Alleen platformbeheerders kunnen projectmappen aanmaken.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projecten en mappen", "app.containers.AdminPage.projects.all.publishedTab": "Gepubliceerd", "app.containers.AdminPage.projects.all.searchProjects": "Zoek <PERSON>en", "app.containers.AdminPage.projects.all.yourProjects": "<PERSON><PERSON><PERSON> <PERSON>en", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "AI-analyse", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Nauwkeurigheid: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "Vraag", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "In plaats van samenvatten kun je ook relevante vragen stellen aan je data. Deze functionaliteit is niet in je huidige abonnement inbegrepen. Neem contact op met je Government Success Manager of platformbeheerder om deze functionaliteit te ontgrendelen.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Stel een vraag", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "Dit inzicht omvat de volgende vragen:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "Verwijder vraag", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "Weet je zeker dat je deze vraag wilt verwijderen?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "<PERSON>er<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Weet je zeker dat je deze samenvat<PERSON>en wilt verwijderen?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Je tekst samenvattingen worden hier weergegeven, maar je hebt er momenteel nog geen.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "Klik op de knop Automatisch samenvatten hierboven om aan de slag te gaan.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "geselecteerde bi<PERSON>gen", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Vragen stellen over minder bijdragen leidt tot een hogere nauwkeurigheid. Beperk de huidige selectie van bijdragen door tags, zoekfilters of demografische filters te gebruiken.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "Vragen voor", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Vraag voor alle bijdragen", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Beoordeel de kwaliteit van dit inzicht", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "Herstel filters", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Samenvatting voor", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Samenvatting voor alle bijdragen", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "Bedankt voor je feedback", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "De AI kan niet zoveel bijdragen in één keer verwerken. <PERSON>el ze in kleinere groepen.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "Binnen je huidige abonnement kun je maximaal 30 bijdragen tegelijk samenvatten. Neem contact op met je Government Success Manager of platformbeheerder om meer te ontgrendelen.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "<PERSON>k begri<PERSON>p het", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Ons platform stelt je in staat om de kernthema's te verkennen, de gegevens samen te vatten en verschillende perspectieven te onderzoeken. Als je op zoek bent naar specifieke antwoorden of inzichten, overweeg dan om de functie \"Stel een vraag\" te gebruiken om dieper te duiken dan de samenvatting.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "<PERSON><PERSON><PERSON> dit zelden voorkomt, kan de AI af en toe informatie genereren die niet expliciet aanwezig was in de oorspronkelijke dataset.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Hallucinaties:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "De AI kan bepaalde thema's of ideeën meer benadrukken dan andere, waardoor de algehele interpretatie mogelijk wordt vertekend.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Overdrijving:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Ons systeem is geoptimaliseerd voor het verwerken van 20-200 goed gedefinieerde bijdragen voor de meest nauwkeurige resultaten. Als het volume van de bijdragen buiten dit bereik toeneemt, kan de samenvatting algemener worden. Dit betekent niet dat de AI \"minder nauwkeurig\" wordt, maar eerder dat het zich zal richten op bredere trends en patronen. Voor meer genuanceerde inzichten raden we aan om de (auto)-tagging functie te gebruiken om grotere datasets te segmenteren in kleinere, beter hanteerbare deelverzamelingen.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Datavolume en nauwkeurigheid:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "We raden aan om AI-gegenereerde samenvattingen te gebruiken als een startpunt om grote datasets te begrijpen, maar niet als het laatste woord.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "<PERSON><PERSON> te werken met AI", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Geselecteerde bijdragen toevoegen aan tag", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "Voeg tag toe", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "Deze functionaliteit is niet in je huidige abonnement inbegrepen. Neem contact op met je Government Success Manager of platformbeheerder om deze functionaliteit te ontgrendelen.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "Alle bijdragen", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "Alle bijdragen", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "Alle tags", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "<PERSON><PERSON>, ik zal het doen", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Wil je automatisch bijdragen toewijzen aan je tag?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "<PERSON><PERSON> <PERSON><PERSON><PERSON> <b>verschillende methoden</b> om automatisch bijdragen toe te wijzen aan tags.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Gebruik <b>de automatisch taggen knop</b> om de methode van je voorkeur te starten.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "<PERSON>lik op een tag om deze toe te wijzen aan de huidig geselecteerde bijdrage.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "Ja, automatisch taggen", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Automatisch taggen", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Automatische tags worden automatisch afgeleid door de computer. Je kunt ze altijd wijzigen of verwijderen.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Automatisch taggen", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Bijdragen die al aan deze tags zijn gekoppeld, worden niet opnieuw geclassificeerd.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "De classificatie is alleen gebaseerd op de naam van de tag. Kies relevante trefwoorden voor de beste resultaten.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Tags: Per label", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "Je maakt de tags aan en wijst handmatig een paar bijdragen toe als voorbeeld, de computer wijst de rest toe", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Tags: <PERSON><PERSON> v<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met \"Tags: per label\", ma<PERSON> met g<PERSON><PERSON> nau<PERSON><PERSON>urigheid omdat je het systeem traint met go<PERSON> voor<PERSON>n.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "<PERSON><PERSON> ma<PERSON>t de <PERSON>, de computer wijst de bijdragen toe", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Tags: Per label", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "Dit werkt goed als je een vooraf gedefinieerde set tags hebt of als je project een beperkte reikwijdte heeft wat tags betreft.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "<PERSON>ectee<PERSON> <PERSON><PERSON><PERSON><PERSON> met een significante dislikes/likes verhouding", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Controversieel", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "<PERSON><PERSON><PERSON><PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "Weet je zeker dat je deze tag wilt verwijderen?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Laat dit niet meer zien", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Bewerk tag", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "<PERSON>oeg naam toe", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Selecteer maxima<PERSON> 9 tags wa<PERSON>ver je de bijdragen wilt verdelen.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "De classificatie is gebase<PERSON> op de bijdragen die momenteel aan de tags zijn toegewezen. De computer zal proberen jouw voorbeeld te volgen.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Tags: <PERSON><PERSON> v<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "Je hebt nog geen aangepaste tags.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "De computer detecteert automatisch tags en wijst ze toe aan je bijdragen.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Tags: <PERSON><PERSON><PERSON>eau<PERSON>ati<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "Werkt goed als je project een breed scala aan tags omvat. Goede plek om te beginnen.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "Hoe wil je taggen?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Bijdragen zonder tags", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Detecteer de taal van elke bijdrage", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "Taal", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "Start", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "<PERSON>n actieve filters", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Gebruik tags om de bijdragen onder te verdelen en te filteren, om nauwkeurigere of gerichtere samenvattingen te maken.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "Overige", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Tags: Platform tags", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Wij<PERSON> de bestaande platform tags toe die de auteur bij het plaatsen heeft gekozen", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Aanbevolen", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "<PERSON><PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "Opsla<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "<PERSON><PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "Alles selecteren", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Wijs een positief of negatief sentiment toe aan elke bijdrage, afgeleid uit de tekst", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Tag detectie", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Gebruik huidige filters", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "Welke bijdragen wil je taggen?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Automatisch taggen taak", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Controversieel", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "Aangepast", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Geëindigd", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "Mislukt", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "Naar voorbeeld", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "In uitvoering", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "Per label", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "Taal", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "NLP tag", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "<PERSON><PERSON>e AI-taken uitgevoerd", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Platform tag", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "In de wachtrij", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Gestart", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Geslaagd", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "<PERSON><PERSON><PERSON><PERSON> taak", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Gea<PERSON>erd", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "Alle", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "Waar woon je?", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Betrokkenheid", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "Filters", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "<PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "Geslacht", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "Bijdragen", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "Aantal opmerkingen", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Aantal likes/dislikes", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "Aantal stemmen", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Toevoegen aan analyse", "app.containers.AdminPage.projects.project.analysis.anonymous": "Anonieme bi<PERSON>gen", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Auteurs op leeftijd", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Auteurs op woonplaats", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Achtergrond Taken", "app.containers.AdminPage.projects.project.analysis.comments": "Reacties", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "De woonplaatsgrafiek is te groot om weer te geven", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "Verberg lege antwoorden", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Reacties", "app.containers.AdminPage.projects.project.analysis.end": "Einde", "app.containers.AdminPage.projects.project.analysis.filter": "<PERSON><PERSON> alleen bi<PERSON> met deze waarde", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Verberg reacties zonder antwoord", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Auto-inzichten", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Kolomwaarden", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "<PERSON><PERSON> zijn {count} g<PERSON><PERSON> van deze combinatie.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "Dislikes", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Verken", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "<PERSON><PERSON> waar", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "Invoeren", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON>s", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Volgende heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Volgend inzicht", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Geen statistisch significant inzicht.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Auto-inzichten zijn niet beschikbaar voor projecten met minder dan 30 deelnemers.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Vorige heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Vorig inzicht", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Statistisch significant inzicht.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Analyse tags", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "Eenheden", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "Bekijk alle inzichten", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Bekijk auto-inzichten", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Bijdragen zonder tags", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "De shapefile die werd geüpload is ongeldig en kan niet worden weergegeven.", "app.containers.AdminPage.projects.project.analysis.limit": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "Hoofdvraag", "app.containers.AdminPage.projects.project.analysis.manageInput": "<PERSON><PERSON><PERSON><PERSON><PERSON> beheren", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Volgende grafiek", "app.containers.AdminPage.projects.project.analysis.noAnswer": "<PERSON><PERSON> ant<PERSON>", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "<PERSON><PERSON> antwoord gegeven.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "Geen shapefile geüpload.", "app.containers.AdminPage.projects.project.analysis.noInputs": "Er komen geen bijdragen overeen met je huidige filters", "app.containers.AdminPage.projects.project.analysis.previousGraph": "<PERSON><PERSON><PERSON> gra<PERSON>k", "app.containers.AdminPage.projects.project.analysis.reactions": "Likes/dislikes", "app.containers.AdminPage.projects.project.analysis.remove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.removeFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON> filter", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "<PERSON><PERSON><PERSON><PERSON><PERSON> filter", "app.containers.AdminPage.projects.project.analysis.search": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Shape<PERSON>les worden hier weergegeven in GeoJSON formaat. Hierdoor wordt de styling in het oorspronkelijke bestand mogelijk niet correct weergegeven.", "app.containers.AdminPage.projects.project.analysis.start": "Start", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Supportartikel", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "Onbekend", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "Bekijk alle vragen", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "Bekijk geselecteerde vragen", "app.containers.AdminPage.projects.project.analysis.votes": "Stemmen", "app.containers.AdminPage.widgets.copied": "Gekopieerd", "app.containers.AdminPage.widgets.copyToClipboard": "Kopieer deze code", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Ko<PERSON>er de HTML-code", "app.containers.AdminPage.widgets.fieldAccentColor": "Acc<PERSON> kleur", "app.containers.AdminPage.widgets.fieldBackgroundColor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van <PERSON> widget", "app.containers.AdminPage.widgets.fieldButtonText": "Tekst op de knop", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldFont": "Lettertype", "app.containers.AdminPage.widgets.fieldFontDescription": "Dit moet een geldige naam van een lettertype zijn, be<PERSON><PERSON><PERSON><PERSON> via {googleFontsLink}. Laat dit veld leeg om het standaard lettertype te gebruiken.", "app.containers.AdminPage.widgets.fieldFontSize": "Tekengrootte (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Ondertitel", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Je kan deelnemen", "app.containers.AdminPage.widgets.fieldHeaderText": "Hoofding", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Ons participatieplatform", "app.containers.AdminPage.widgets.fieldHeight": "<PERSON><PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Aantal bijdragen", "app.containers.AdminPage.widgets.fieldProjects": "Projecten", "app.containers.AdminPage.widgets.fieldRelativeLink": "<PERSON><PERSON> naar", "app.containers.AdminPage.widgets.fieldShowFooter": "Knop weergeven", "app.containers.AdminPage.widgets.fieldShowHeader": "Hoofding weergeven", "app.containers.AdminPage.widgets.fieldShowLogo": "Logo weergeven", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Achtergrondkleur van de website", "app.containers.AdminPage.widgets.fieldSort": "Geordend volgens", "app.containers.AdminPage.widgets.fieldTextColor": "Tekstkleur", "app.containers.AdminPage.widgets.fieldTopics": "Tags", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.homepage": "Homepagina", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Kopieer volgende HTML-code en plak deze op de website waar de widget moet worden weergegeven.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Widget HTML code", "app.containers.AdminPage.widgets.previewTitle": "Voorvertoning", "app.containers.AdminPage.widgets.settingsTitle": "Instellingen", "app.containers.AdminPage.widgets.sortNewest": "Meest recent", "app.containers.AdminPage.widgets.sortPopular": "Meeste stemmen", "app.containers.AdminPage.widgets.sortTrending": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.subtitleWidgets": "<PERSON><PERSON> zoveel widgets als je wilt en voeg de html-code toe aan je homepagina of ergens anders op je website.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Afmetingen", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Hoofding en footer", "app.containers.AdminPage.widgets.titleInputSelection": "Bijdragen selecteren", "app.containers.AdminPage.widgets.titleStyle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Opsla<PERSON>", "app.containers.ContentBuilder.homepage.PageTitle": "Startpagina", "app.containers.ContentBuilder.homepage.SaveError": "Er is iets misgegaan bij het op<PERSON>an van de homepagina.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "<PERSON>wee kolommen", "app.containers.ContentBuilder.homepage.bannerImage": "Bannerafbeelding", "app.containers.ContentBuilder.homepage.bannerSubtext": "Banner ondertitel", "app.containers.ContentBuilder.homepage.bannerText": "Bannertitel", "app.containers.ContentBuilder.homepage.button": "Knop", "app.containers.ContentBuilder.homepage.chooseLayout": "Indeling", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Het aan<PERSON>en van andere instellingen dan de afbeelding en tekst op de homepage banner is niet inbegrepen in je huidige licentie. Neem contact op met je GovSuccess Manager voor meer informatie.", "app.containers.ContentBuilder.homepage.customized_button": "Aangepast", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Tekst op de knop", "app.containers.ContentBuilder.homepage.customized_button_url_label": "Link voor de knop", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Toont de eerstvolgende 3 aankomende activiteiten op je platform.", "app.containers.ContentBuilder.homepage.eventsDescription": "Toont de eerstvolgende 3 aankomende activiteiten op je platform.", "app.containers.ContentBuilder.homepage.fixedRatio": "Vaste verhouding", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Dit bannertype werkt het beste met afbeeldingen die niet mogen worden bijgesneden, zoals afbeeldingen met teks<PERSON>, een logo of specifieke elementen die cruciaal zijn voor je inwoners. Deze banner wordt vervangen door een effen kader in de primaire kleur wanneer gebruikers zijn ingelogd. Je kunt deze kleur instellen in de algemene instellingen. Meer info over het aanbevolen gebruik van afbeeldingen kun je vinden op onze {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "kennisbank", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "Volledige breedte", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Deze banner strekt zich uit over de volle breedte voor een geweldig visueel effect. De afbeelding zal proberen zoveel mogelijk ruimte te bedekken, waardoor hij niet altijd volledig zichtbaar is. Je kunt deze banner combineren met een filter in iedere kleur. Meer info over het aanbevolen gebruik van afbeeldingen vind je op onze {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "kennisbank", "app.containers.ContentBuilder.homepage.imageOverlayColor": "K<PERSON><PERSON> filter", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Doorzichtigheid filter", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/nl/articles/1346397-upload-je-afbeeldingen-in-het-juiste-formaat-en-volgens-de-voorgeschreven-afmetingen", "app.containers.ContentBuilder.homepage.invalidUrl": "Ongeldige URL", "app.containers.ContentBuilder.homepage.no_button": "<PERSON><PERSON> knop", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Niet-geregis<PERSON>rde bezoekers", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Filter inschakelen", "app.containers.ContentBuilder.homepage.projectsDescription": "Om de volgorde waarin je projecten worden weergegeven te configureren, kun je ze opnieuw ordenen op {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "<PERSON>en pagina", "app.containers.ContentBuilder.homepage.registeredUsersView": "Geregistreerde gebruikers", "app.containers.ContentBuilder.homepage.showAvatars": "Avatars weergeven", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "<PERSON><PERSON> pro<PERSON><PERSON><PERSON>'s en aantallen deelnemers aan niet-geregistreerde bezoekers", "app.containers.ContentBuilder.homepage.sign_up_button": "Registreren", "app.containers.ContentBuilder.homepage.signedInDescription": "Zo zien geregistreerde gebruikers de banner.", "app.containers.ContentBuilder.homepage.signedOutDescription": "Zo zien bezoekers die niet geregistreerd zijn op het platform de banner.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "Deze banner is vooral handig bij afbeeldingen die niet goed werken met tekst uit de titel, ondertitel of knop. Deze items worden onder de banner geschoven. Meer informatie over het aanbevolen gebruik van afbeeldingen kun je vinden op onze {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "kennisbank", "app.containers.ContentBuilder.homepage.twoRowLayout": "Twee rijen", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "<PERSON><PERSON><PERSON> van de insluiting (pixels)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "<PERSON><PERSON><PERSON> wa<PERSON>p je je ingesloten inhoud op de pagina wilt weergeven (in pixels).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Korte omschrijving van de inhoud die je insluit", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "He<PERSON> is nuttig om deze informatie te verstrekken aan gebruikers die afhankelijk zijn van een schermlezer of andere ondersteunende technologie.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Website adres", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "Volledige URL van de website die je in wilt sluiten.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "In<PERSON>d van een externe website op je pagina weergeven in een HTML iFrame. Niet elke pagina kan worden ingesloten. Als je problemen hebt met het insluiten van een pagina, controleer dan bij de eigenaar van de pagina of deze is geconfigureerd om insluiten toe te staan.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Bekijk onze supportpagina", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "Sorry, deze inhoud kan niet worden ingesloten. {visitLinkMessage} voor meer informatie.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/7025826-customizing-project-descriptions-with-the-content-builder", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "<PERSON><PERSON>r een geldig webadres in, bijvoorbeeld https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Insluiten", "app.containers.admin.ContentBuilder.accordionMultiloc": "Accordeon", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Standaard openen", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Tekst", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "<PERSON><PERSON> is openklapbare accordeoninhoud. Je kunt deze bewerken en opmaken met be<PERSON><PERSON> <PERSON> de editor in het paneel aan de rechterkant.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Titel", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Accordeon titel", "app.containers.admin.ContentBuilder.buttonMultiloc": "Knop", "app.containers.admin.ContentBuilder.delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.error": "fout", "app.containers.admin.ContentBuilder.errorMessage": "Er is een fout opgetreden in de {locale} inhoud, los het probleem op om je wijzigingen op te kunnen slaan", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Verberg avatars deelnemers", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "<PERSON><PERSON> is een doorlopend driemaandelijks onderzoek dat bijhoudt hoe je denkt over bestuur en openbare diensten.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Vul de vragenlijst in", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Help ons jou beter van dienst te zijn", "app.containers.admin.ContentBuilder.homepage.default": "standaard", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "Activiteiten", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "Activiteiten", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "<PERSON><PERSON><PERSON> tot actie", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Beschrijving", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "URL primaire knop", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Tekst primaire knop", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "URL voor secundaire knop", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Secundaire knop tekst", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Titel", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Startpagina banner", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Homepagina banner", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Afbeelding- & tekstkaarten", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 kolom", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projecten", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Activeer voorstellen in de sectie \"Voorstellen\" in het beheerderspaneel om ze te ontgrendelen op de homepagina", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Voorstellen", "app.containers.admin.ContentBuilder.imageMultiloc": "Afbeelding", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "<PERSON><PERSON> beschrijving van de a<PERSON>lding", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "Het toevoegen van \"alt-tekst\" voor afbeeldingen is belangrijk om uw platform toegankelijk te maken voor gebruikers die schermlezers gebruiken.", "app.containers.admin.ContentBuilder.participationBox": "Deelname Box", "app.containers.admin.ContentBuilder.textMultiloc": "Tekst", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 kolommen", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 kolommen", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 kolommen met respectievelijk 30% en 60% breedte", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 kolommen met respectievelijk 60% en 30% breedte", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 even kolommen", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "<PERSON><PERSON><PERSON><PERSON><PERSON> met de meeste likes/dislikes", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Ideeënvorming fase", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "Er zijn geen bijdragen beschikbaar voor dit project of deze fase.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "Aantal bijdragen", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Toon meer", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Titel", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Total aantal bijdragen: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Lange tekst samenvouwen", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "Er zijn geen bijdragen beschikbaar voor dit project of deze fase.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Selecteer fase", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "Likes/dislikes", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "Stemmen", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Titel", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Registratieratio", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Registraties", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Houd er rekening mee dat de deelnamecijfers mogelijk niet volledig accuraat zijn, omdat sommige gegevens worden vastgelegd in een externe vragenlijst die we niet bijhouden.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "<PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Datumbereik", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Titel", "app.containers.admin.ReportBuilder.charts.noData": "Er is geen data beschikbaar voor de door u geselecteerde filters.", "app.containers.admin.ReportBuilder.charts.trafficSources": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.users": "Gebruikers", "app.containers.admin.ReportBuilder.charts.usersByAge": "Gebruikers per leeftijd", "app.containers.admin.ReportBuilder.charts.usersByGender": "Gebruikers per geslacht", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Bezoekers tijdlijn", "app.containers.admin.ReportBuilder.managerLabel1": "Projectbeheerder", "app.containers.admin.ReportBuilder.periodLabel1": "Periode", "app.containers.admin.ReportBuilder.projectLabel1": "projecten", "app.containers.admin.ReportBuilder.quarterReport1": "Rapport van de tevredenheidsmonitor: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Start", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Koop 1 extra account", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Bevestig", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Weet je zeker dat je 1 persoon map- of projectbeheerdersrechten wilt geven?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Geef map- of projectbeheerdersrechten", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Je hebt de limiet van inbegrepen accounts binnen je licentie bereikt, {noOfSeats} extra {noOfSeats, plural, one {account zal} other {accounts zullen}} worden toegevoegd.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Voeg een status toe", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Standaard statussen kunnen niet worden verwijderd.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Bewerk", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Bewerk de status", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "<PERSON><PERSON> die momenteel aan een bijdrage zijn toe<PERSON>, kunnen niet worden verwijderd. Je kunt de status van bestaande bijdragen verwijderen / wijzigen op het tabblad {manageTab}.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Deze status kan niet worden verwijderd of verplaatst.", "app.containers.admin.ideaStatuses.all.manage": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Het configureren van aangepaste statussen is niet in je huidige abonnement inbegrepen. Neem contact op met je Government Success Manager of platformbeheerder om dit te ontgrendelen.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Beheer de status die kan worden toegekend aan de bijdragen van deelnemers binnen een project. De status is openbaar zichtbaar en helpt om deelnemers op de hoogte te houden.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Beheer de status die kan worden toegewezen aan voorstellen binnen een project. De status is publiekelijk zichtbaar en helpt om deelnemers op de hoogte te houden.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Bewerk input statussen", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "<PERSON><PERSON> van voorstellen bewerken", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Geselecteerd voor implementatie of volgende stappen", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Goedgekeurd", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Officiële feedback gegeven", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Beantwoord", "app.containers.admin.ideaStatuses.form.category": "Categorie", "app.containers.admin.ideaStatuses.form.categoryDescription": "Selecteer de categorie die je status het best omschrijft. Deze selectie zal onze analysetool helpen om berichten nauwkeuriger te verwerken en te analyseren.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Past niet bij een van de andere opties", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "Overige", "app.containers.admin.ideaStatuses.form.fieldColor": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescription": "Statusbeschrijving", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "<PERSON><PERSON><PERSON> voor elke taal een statusbeschrijving in", "app.containers.admin.ideaStatuses.form.fieldTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldTitleError": "<PERSON><PERSON><PERSON> voor elke taal een status<PERSON> in", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Succesvol uitgevoerd", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Uitgevoerd", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "Voorstel komt niet in aanmerking", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "<PERSON><PERSON>t niet in aanmerking", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Niet in aanmerking komend of niet geselecteerd om verder te gaan", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "<PERSON><PERSON>d", "app.containers.admin.ideaStatuses.form.saveStatus": "Status opslaan", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Overwogen voor implementatie of volgende stappen", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "In overwegIng", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "<PERSON><PERSON><PERSON> maar nog niet verwerkt", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Bijdragen en hun statussen beheren.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "<PERSON><PERSON><PERSON> bi<PERSON> | Participatieplatform van {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "<PERSON><PERSON><PERSON> hier de bijdragen van alle projecten. Geef feedback, voeg tags toe, verander de status of verplaats bijdragen van het ene project naar het andere.", "app.containers.admin.ideas.all.inputManagerPageTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideas.all.tabOverview": "Overzicht", "app.containers.admin.import.importInputs": "Importeer invoeren", "app.containers.admin.import.importNoLongerAvailable3": "Deze functionaliteit is hier niet langer besch<PERSON>. Om bijdragen in een ideeënverzamelingsfase te importeren, ga je naar de fase en selecteer je \"Importeren\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 extra platformbeheerder} other {# extra platformbeheerders}} en {managerSeats, plural, one {1 extra map- of projectbeheerder} other {# extra map- en projectbeheerders}} worden toegevoegd boven de limiet.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 extra platformbeheerder wordt toegevoegd boven de limiet} other {# extra platformbeheerders worden toegevoegd boven de limiet}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 extra map- of projectbeheerder wordt toegevoegd boven de limiet} other {# extra map- en projectbeheerders worden toegevoegd boven de limiet}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Uitnodigingen bevestigen en versturen", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Bevestig de aanpassing van het aantal accounts", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Je hebt de limiet van beschikbare accounts binnen jullie abonnement bereikt.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Platformbeheerders en projectbeheerders van dit project", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "<PERSON><PERSON> beheerders", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "<PERSON>een beheerders kunnen de actie uitvoeren", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "<PERSON><PERSON><PERSON>, ook niet-gere<PERSON><PERSON><PERSON> gebruikers, kan de<PERSON><PERSON>.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Be<PERSON><PERSON><PERSON> groepen gebruikers", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Gebruikers in specifieke gebruikersgroep(en) kunnen deelnemen. Je kunt gebruikersgroepen beheren in het tabblad \"Gebruikers\".", "app.containers.admin.project.permissions.viewingRightsTitle": "Wie kan dit project zien?", "app.containers.phaseConfig.enableSimilarInputDetection": "Detectie van vergelijkbare bijdragen inschakelen", "app.containers.phaseConfig.similarInputDetectionTitle": "Detectie van vergelijkbare bijdragen", "app.containers.phaseConfig.similarInputDetectionTooltip": "Laat deelnemers vergelijkbare bijdragen zien tijdens het typen om dubbele bijdragen te voorkomen.", "app.containers.phaseConfig.similarityThresholdBody": "Drempelwaarde gelijkheid (beschrijving)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "Dit bepaalt hoe gelijkaardig twee beschrijvingen moeten zijn om als gelijkaardig gemarkeerd te worden. Gebruik een waarde tussen 0 (streng) en 1 (mild). Lagere waarden leveren minder maar meer nauwkeurige overeenkomsten op.", "app.containers.phaseConfig.similarityThresholdTitle": "Drempelwaarde gelijkheid (titel)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "Dit bepaalt hoe gelijkaardig twee titels moeten zijn om als gelijkaardig gemarkeerd te worden. Gebruik een waarde tussen 0 (streng) en 1 (mild). Lagere waarden leveren minder maar nauwkeurigere overeenkomsten op.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "Deze functie is be<PERSON><PERSON><PERSON><PERSON> als onderdeel van een early access-aanbod tot 30 juni 2025. Als je deze functie ook na die datum wilt blijven g<PERSON>, neem dan contact op met je Government Success Manager of beheerder om de activeringsopties te bespreken.", "app.containers.survey.sentiment.noAnswers2": "<PERSON><PERSON> reacties op dit moment.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {geen reacties} one {1 reactie} other {# reacties}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Deelnemers zijn gebruikers of bezoekers die hebben deelgenomen aan een project, een voorstel hebben gepost of er interactie mee hebben gehad, of evenementen hebben bijgewoond.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Percentage bezoekers dat deelnemer wordt.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Totaal aantal deelnemers", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Geautomatiseerde campagnes", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Geautomatiseerde e-mails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "Vanaf {quantity} campagnes", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Campagnes", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Gepersonaliseerde campagnes", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Gepersonaliseerde e-mails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "E-mails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Totaal verzonden e-mails", "app.modules.commercial.analytics.admin.components.Events.completed": "Voltooid", "app.modules.commercial.analytics.admin.components.Events.events": "Activiteiten", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Totaal aantal toegevoegde activiteiten", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Binnenkort", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Uitnodigingen", "app.modules.commercial.analytics.admin.components.Invitations.pending": "In behandeling", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Totaal aantal verzonden uitnodigingen", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Ga naar alle bijdragen", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Invoeren", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Actief", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Projecten die niet zijn gearchiveerd en zichtbaar zijn in de tabel 'Actief' op de startpagina", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Afgerond", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Conceptprojecten", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Afgelopen", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Alle gearchiveerde projecten en actieve tijdlijnprojecten die zijn voltooid, worden hier geteld", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projecten", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Totale projecten", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Het aantal projecten dat zichtbaar is op het platform", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Nieuwe registraties", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Registratieratio", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Percentage bezoekers dat een geregistreerde gebruiker wordt.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registraties", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Totale registraties", "app.modules.commercial.analytics.admin.components.Tab": "Bezoekers", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Laatste 30 dagen:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Laatste 7 dagen:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Paginaweergaven per bezoek", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Bezoekers", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Bezoekers\" is het aantal unieke bezoekers. Als een persoon het platform meerdere keren bezoekt, wordt deze slechts één keer geteld.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Bezoeken", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Bezoeken\" is het aantal sessies. Als een persoon het platform meerdere keren heeft bezocht, wordt elk bezoek geteld.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Gisteren:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Aantal", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Taal", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Aantal bezoekers", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "<PERSON><PERSON><PERSON> van bezoekers", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "<PERSON><PERSON> hier", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Verwijzers", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "<PERSON>m de volledige lijst met verwi<PERSON><PERSON> te zien, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Bezoekers", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Verkeersbronnen", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Bezoeken", "app.modules.commercial.analytics.admin.components.totalParticipants": "Totaal aantal deelnemers", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Er zijn nog geen bezoekersgegevens.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "We hebben de manier waarop we bezoekersgegevens verzamelen en weergeven veranderd. Hierdoor zijn de bezoekersgegevens nauwkeuriger en zijn er meer soorten gegevens besch<PERSON>, terwijl we nog steeds voldoen aan de GDPR. Hoewel de gegevens die worden gebruikt voor de bezoekerstijdlijn langer teruggaan, zijn we pas in november 2024 begon<PERSON> met het verzamelen van de gegevens voor de \"Bezoekduur\", \"Paginaweergaven per bezoek\" en de andere grafieken, dus daarvoor zijn geen gegevens beschik<PERSON>ar. Als je gegevens selecteert van voor november 2024, houd er dan rekening mee dat sommige grafieken leeg kunnen zijn of er vreemd uitzien.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "E-mails geleverd in loop van tijd", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Deelnemers na verloop van tijd", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Registraties in de loop van de tijd", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Datum", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistiek", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Algemene statistieken", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Bezoeken en bezoekers in de loop van de tijd", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Totaal over periode", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Aantal", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Taal", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Campagnes", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Directe invoer", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "Aantal bezoekers", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "<PERSON><PERSON><PERSON> bezo<PERSON>en", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Verwijzende websites", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Zoekmachines", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Sociale netwerken", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO omleidingen", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Verkeersbron", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Aantal bezoeken", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Websites", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Je kunt deze markering verwijderen door dit item te selecteren en bovenaan op de knop Verwijderen te klikken. Het item zal dan opnieuw verschijnen in de tabbladen Gezien of Niet gezien", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Automatisch gedetecteerd als ongepaste inhoud.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Er zijn door andere deelnemers of door ons automatische detectie-systeem via Natural Language Processing (NLP) geen berichten gemarkeerd als ongepaste inhoud", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Verwijder {numberOfItems, plural, one {waarschuwing} other {# waarschuwingen}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "<PERSON><PERSON><PERSON><PERSON>eerd als ongepast door een gebruiker van het platform.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Waarschuwingen", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Rapportbouwer", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "<PERSON><PERSON><PERSON>'s weergegeven op je navigatiebalk", "app.modules.navbar.admin.containers.addProject": "Project toevoegen aan navigatiebalk", "app.modules.navbar.admin.containers.createCustomPageButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> pagina aanmaken", "app.modules.navbar.admin.containers.deletePageConfirmation": "Weet je zeker dat je deze pagina wilt verwijderen? Dit kan niet ongedaan gemaakt worden. Je kunt de pagina ook uit de navigatiebalk verwijderen als je nog niet klaar bent om deze te verwijderen.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Je kunt maximaal 5 items toevoegen aan de navigatiebalk", "app.modules.navbar.admin.containers.pageHeader": "Pa<PERSON><PERSON>'s en menu", "app.modules.navbar.admin.containers.pageSubtitle": "Je navigatiebalk kan maximaal vij<PERSON> pagina's weergeven naast de startpagina en de projectenpagina's. Je kunt menu-items hernoemen, opnieuw ordenen en nieuwe pagina's toe<PERSON><PERSON>n met uw eigen inhoud.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widgets", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "<PERSON>eb<PERSON>ik het ☰ icoon hieronder om AI-inhoud in je rapport te slepen.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "Er zijn geen AI-inzichten beschikbaar. Je kunt ze creëren in je project.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Ga naar project", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "Vraag", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Selecteer fase", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Ontgrendel AI-analyse", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Neem AI-gegenereerde inzichten op in je rapport", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Rapporteer <PERSON><PERSON><PERSON> met AI", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Rapportage met AI is niet in je huidige abonnement inbegrepen. Neem contact op met je Government Success Manager om deze functionaliteit te ontgrendelen.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "<PERSON><PERSON> is niet inbegrepen in je huidige plan. Neem contact op met je Government Success Manager of admin om het vrij te geven.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Groepeer per registratieveld", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "<PERSON><PERSON><PERSON><PERSON> per enquêtevraag", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Groepeer modus", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Groepeer enquêtereacties per gebruikersveld (geslacht, locatie, leeftijd, enz.) of andere enquêtevragen.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "<PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "Vraag", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Registratieveld", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Vragenlijst fase", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Weet je zeker dat je dit wilt verwijderen?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "Bewerk", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Plaats je reactie", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "<PERSON><PERSON><PERSON><PERSON><PERSON> je reactie hier", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Klik op de knoppen hieronder om te volgen of te ontvolgen. Het aantal projecten staat tussen haakjes.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "In jouw gebied", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Volgvoorkeuren", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "<PERSON>r zijn momenteel geen actieve projecten gezien je volgvoorkeuren.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "Deze widget toont projecten die geassocieerd zijn met de \"gebieden\" die de gebruiker volgt. Wees er bewust van dat jouw platform een andere naam kan gebruiken voor \"gebieden\" - zie de \"Gebieden\" tab in de platform instellingen. Als de gebruiker nog geen gebieden volgt, toont de widget de beschikbare gebieden om te volgen. In dit geval zal de widget maximaal 100 gebieden tonen.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "<PERSON><PERSON> gep<PERSON>erde projecten of mappen beschik<PERSON>ar", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Gepubliceerde projecten en mappen", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "Deze widget toont de projecten en mappen die op dat moment gep<PERSON><PERSON><PERSON> zijn, met in<PERSON><PERSON><PERSON><PERSON> van de volgorde die is gedefinieerd op de projectenpagina. Dit gedrag is hetzelfde als het \"actief\" tabblad van de \"oude\" projecten widget.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "<PERSON><PERSON> <PERSON>en of mappen geselecteerd", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Selecteer projecten of mappen", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Geselecteerde projecten en mappen", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "Met deze widget kun je de volgorde selecteren en bepalen waarin je projecten of mappen aan gebruikers wilt laten zien.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projecten", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "bekijk onze supportpagina", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Meer informatie over aanbevolen beeldresoluties vind je op {supportPageLink}."}
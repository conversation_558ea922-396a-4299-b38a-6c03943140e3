{"UI.FormComponents.required": "required", "app.Admin.ManagementFeed.action": "Action", "app.Admin.ManagementFeed.after": "After", "app.Admin.ManagementFeed.before": "Before", "app.Admin.ManagementFeed.changed": "Modified", "app.Admin.ManagementFeed.created": "Created", "app.Admin.ManagementFeed.date": "Date", "app.Admin.ManagementFeed.deleted": "Deleted", "app.Admin.ManagementFeed.folder": "Folder", "app.Admin.ManagementFeed.idea": "Idea", "app.Admin.ManagementFeed.in": "in project {project}", "app.Admin.ManagementFeed.item": "<PERSON><PERSON>", "app.Admin.ManagementFeed.key": "Key", "app.Admin.ManagementFeed.managementFeedNudge": "Accessing the management feed is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.Admin.ManagementFeed.noActivityFound": "No activity found", "app.Admin.ManagementFeed.phase": "Phase", "app.Admin.ManagementFeed.project": "Project", "app.Admin.ManagementFeed.projectReviewApproved": "Project approved", "app.Admin.ManagementFeed.projectReviewRequested": "Project review requested", "app.Admin.ManagementFeed.title": "Management feed", "app.Admin.ManagementFeed.user": "User", "app.Admin.ManagementFeed.userPlaceholder": "Select a user", "app.Admin.ManagementFeed.value": "Value", "app.Admin.ManagementFeed.viewDetails": "View details", "app.Admin.ManagementFeed.warning": "Experimental feature: A minimal list of selected actions performed by admins or managers in the last 30 days. Not all actions are included.", "app.Admin.Moderation.managementFeed": "Management feed", "app.Admin.Moderation.participationFeed": "Participation feed", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "Are you sure?", "app.components.Admin.Campaigns.clicked": "Clicked", "app.components.Admin.Campaigns.deleteCampaignButton": "Delete campaign", "app.components.Admin.Campaigns.deliveryStatus_accepted": "Accepted", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Bounced", "app.components.Admin.Campaigns.deliveryStatus_clicked": "Clicked", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "This shows how many recipients clicked a link in the email. Please note that some security systems may follow links automatically to scan them, which can result in false clicks.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "Delivered", "app.components.Admin.Campaigns.deliveryStatus_failed": "Failed", "app.components.Admin.Campaigns.deliveryStatus_opened": "Opened", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "This shows how many recipients opened the email. Please note that some security systems (like Microsoft Defender) may pre-load content for scanning, which can result in false opens.", "app.components.Admin.Campaigns.deliveryStatus_sent": "<PERSON><PERSON>", "app.components.Admin.Campaigns.draft": "Draft", "app.components.Admin.Campaigns.from": "From", "app.components.Admin.Campaigns.manageButtonLabel": "Manage", "app.components.Admin.Campaigns.opened": "Opened", "app.components.Admin.Campaigns.project": "Project", "app.components.Admin.Campaigns.recipientsTitle": "Recipients", "app.components.Admin.Campaigns.reply_to": "Reply-To", "app.components.Admin.Campaigns.sent": "<PERSON><PERSON>", "app.components.Admin.Campaigns.statsButton": "Statistics", "app.components.Admin.Campaigns.subject": "Subject", "app.components.Admin.Campaigns.to": "To", "app.components.Admin.ImageCropper.cropFinalSentence": "See also: {link}.", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "Keep key content inside the dotted lines to ensure it's always visible.", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "3:1 on mobile (only the area between the dotted lines is shown)", "app.components.Admin.ImageCropper.cropSentenceOne": "The image is cropped automatically:", "app.components.Admin.ImageCropper.cropSentenceTwo": "{aspect} on desktop (full width shown)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "recommended ratio", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Header text for registered users", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Warning: the colour you selected doesn't have a high enough contrast. This may result in text that's hard to read. Choose a darker colour to optimise readability.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Add Events to navigation bar", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "When enabled, a link to all project events will be added to the navigation bar.", "app.components.AdminPage.SettingsPage.eventsSection": "Events", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Homepage customisable section", "app.components.AnonymousPostingToggle.userAnonymity": "User anonymity", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Users name and email will be hidden from all other users, project managers and admins. You can still moderate contributions but demographic data will be anonymized and not recorded. ", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Allow users to participate anonymously", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Users may still choose to participate with their real name, but they will have the option to submit contributions anonymously if they choose to do so. All users will still need to comply with the requirements set in the Access Rights tab for their contributions to go through. User profile data will not be available on the participation data export.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Learn more about user anonymity in our {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "support article", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Once additional seats are added, your billing will be increased. Reach out to your GovSuccess Manager to learn more about it.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Thank you for completing the survey! You're welcome to take it again next quarter.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Download as pdf", "app.components.FormSync.downloadExcelTemplate": "Download an Excel template", "app.components.FormSync.downloadExcelTemplateTooltip2": "Excel templates will not include Ranking questions, Matrix questions, File upload questions and any mapping input questions (Drop Pin, Draw Route, Draw Area, ESRI file upload) as these are not supported for bulk importing at this time.", "app.components.ProjectTemplatePreview.close": "Close", "app.components.ProjectTemplatePreview.createProject": "Create project", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Create a project based on the template ''{templateTitle}''", "app.components.ProjectTemplatePreview.goBack": "Go back", "app.components.ProjectTemplatePreview.goBackTo": "Go back to the {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "Go Vocal expert", "app.components.ProjectTemplatePreview.infoboxLine1": "Want to use this template for your participation project?", "app.components.ProjectTemplatePreview.infoboxLine2": "Reach out to the responsible person in your city administration, or contact a {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Project folder", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "The selected date is invalid. Please provide a date in the following format: YYYY-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Please select a start date for the project", "app.components.ProjectTemplatePreview.projectStartDate": "The start date of your project", "app.components.ProjectTemplatePreview.projectTitle": "The title of your project", "app.components.ProjectTemplatePreview.projectTitleError": "Please type a project title", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Please type a project title for all languages", "app.components.ProjectTemplatePreview.projectsOverviewPage": "projects overview page", "app.components.ProjectTemplatePreview.responseError": "Oops, something went wrong.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "See more templates", "app.components.ProjectTemplatePreview.successMessage": "The project was successfully created!", "app.components.ProjectTemplatePreview.typeProjectName": "Type the name of the project", "app.components.ProjectTemplatePreview.useTemplate": "Use this template", "app.components.SeatInfo.additionalSeats": "Additional seats", "app.components.SeatInfo.additionalSeatsToolTip": "This shows the number of additional seats you have purchased on top of 'Included seats'.", "app.components.SeatInfo.adminSeats": "Admin seats", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} admin seats included", "app.components.SeatInfo.adminSeatsTooltip1": "Administrators are in charge of the platform and they have manager rights for all folders and projects. You can {visitHelpCenter} to learn more about the different roles.", "app.components.SeatInfo.currentAdminSeatsTitle": "Current admin seats", "app.components.SeatInfo.currentManagerSeatsTitle": "Current manager seats", "app.components.SeatInfo.includedAdminToolTip": "This shows the number of available seats for admins included in the yearly contract.", "app.components.SeatInfo.includedManagerToolTip": "This shows the number of available seats for managers included in the yearly contract.", "app.components.SeatInfo.includedSeats": "Included seats", "app.components.SeatInfo.managerSeats": "Manager seats", "app.components.SeatInfo.managerSeatsTooltip": "Folder/project managers can manage an unlimited number of folders/projects. You can {visitHelpCenter} to learn more about the different roles.", "app.components.SeatInfo.managersIncludedText": "{managerSeats} manager seats included", "app.components.SeatInfo.remainingSeats": "Remaining seats", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Total seats", "app.components.SeatInfo.totalSeatsTooltip": "This shows the summed number of seats within your plan and additional seats you have purchased.", "app.components.SeatInfo.usedSeats": "Used seats", "app.components.SeatInfo.view": "View", "app.components.SeatInfo.visitHelpCenter": "visit our help center", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Your plan has {adminSeatsIncluded}. Once you've used all the seats, extra seats will be added under 'Additional seats'.", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Your plan has {manager<PERSON><PERSON><PERSON><PERSON>ncluded}, eligible for folder managers and project managers. Once you've used all the seats, extra seats will be added under 'Additional seats'.", "app.components.UserSearch.addModerators": "Add", "app.components.UserSearch.searchUsers": "Type to search users...", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternative error message", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "By default, the following error message will be shown to users:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Customize error message", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "You can overwrite this message for each language using the \"Alternative error message\" text box below. If you leave the text box empty, the default message will be shown.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "Error message", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "This is what participants will see when they don't meet the participation requirements.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Save error message", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "No question selected. Please select a question first.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "No answer", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} responses", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "Survey question", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} until now", "app.components.admin.DatePhasePicker.Input.openEnded": "Open ended", "app.components.admin.DatePhasePicker.Input.selectDate": "Select date", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Clear end date", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Clear start date", "app.components.admin.Graphs": "No data available with the current filters.", "app.components.admin.Graphs.noDataShort": "No data available.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Comments over time", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Posts over time", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Reactions over time", "app.components.admin.InputManager.onePost": "1 input", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Offline picks adjustment", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Offline votes adjustment", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "This option allows you to include participation data from other sources, such as in-person or paper votes:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "It will be visually distinct from digital votes.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "It will affect the final vote results.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "It will not be reflected in participation data dashboards.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Offline votes for an option can only be set once in a project, and are shared between all phases of a project.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "You must enter the total offline participants first.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Total offline participants", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "In order to calculate the correct results, we need to know the <b>total amount of offline participants for this phase</b>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Please indicate only those that participated offline.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "Modified by {name}", "app.components.admin.PostManager.PostPreview.assignee": "Assignee", "app.components.admin.PostManager.PostPreview.cancelEdit": "Cancel edit", "app.components.admin.PostManager.PostPreview.currentStatus": "Current status", "app.components.admin.PostManager.PostPreview.delete": "Delete", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Are you sure you want to delete this input? This action cannot be undone.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Are you sure you want to delete this input? The input will be deleted from all project phases and cannot be recovered.", "app.components.admin.PostManager.PostPreview.edit": "Edit", "app.components.admin.PostManager.PostPreview.noOne": "Unassigned", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "The number of times this has been included in other participants' participatory budgets", "app.components.admin.PostManager.PostPreview.picks": "Picks: {picksN<PERSON>ber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Reaction counts:", "app.components.admin.PostManager.PostPreview.save": "Save", "app.components.admin.PostManager.PostPreview.submitError": "Error", "app.components.admin.PostManager.addFeatureLayer": "Add feature layer", "app.components.admin.PostManager.addFeatureLayerInstruction": "Copy the URL of the feature layer hosted on ArcGIS Online and paste it in the input below:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Add a new feature layer to the map", "app.components.admin.PostManager.addWebMap": "Add Web Map", "app.components.admin.PostManager.addWebMapInstruction": "Copy the portal ID of your Web Map from ArcGIS Online and paste it in the input below:", "app.components.admin.PostManager.allPhases": "All phases", "app.components.admin.PostManager.allProjects": "All projects", "app.components.admin.PostManager.allStatuses": "All statuses", "app.components.admin.PostManager.allTopics": "All tags", "app.components.admin.PostManager.anyAssignment": "Any administrator", "app.components.admin.PostManager.assignedTo": "Assigned to {assigneeName}", "app.components.admin.PostManager.assignedToMe": "Assigned to me", "app.components.admin.PostManager.assignee": "Assignee", "app.components.admin.PostManager.authenticationError": "An authentication error occured while trying to fetch this layer. Please check the URL and that your Esri API key has access to this layer.", "app.components.admin.PostManager.automatedStatusTooltipText": "This status updates automatically when conditions are met", "app.components.admin.PostManager.bodyTitle": "Description", "app.components.admin.PostManager.cancel": "Cancel", "app.components.admin.PostManager.cancel2": "Cancel", "app.components.admin.PostManager.co-sponsors": "Co-sponsors", "app.components.admin.PostManager.comments": "Comments", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "This means you will lose all data associated with these inputs, like comments, reactions and votes. This action cannot be undone.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Are you sure you want to delete these inputs?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "Remove topic", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "You are trying to remove this idea from a phase where it has received votes. If you do this, these votes will be lost. Are you sure you want to remove this idea from this phase?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "The votes associated with this idea will be lost", "app.components.admin.PostManager.components.goToInputManager": "Go to input manager", "app.components.admin.PostManager.components.goToProposalManager": "Go to proposal manager", "app.components.admin.PostManager.contributionFormTitle": "Edit contribution", "app.components.admin.PostManager.cost": "Cost", "app.components.admin.PostManager.createInput": "Create input", "app.components.admin.PostManager.createInputsDescription": "Create a new set of inputs from a past project", "app.components.admin.PostManager.currentLat": "Center latitude", "app.components.admin.PostManager.currentLng": "Center longitude", "app.components.admin.PostManager.currentZoomLevel": "Zoom level", "app.components.admin.PostManager.defaultEsriError": "An error occured while trying to fetch this layer. Please check your network connect and that the URL is correct.", "app.components.admin.PostManager.delete": "Delete", "app.components.admin.PostManager.deleteAllSelectedInputs": "Delete {count} posts", "app.components.admin.PostManager.deleteConfirmation": "Are you sure you want to delete this layer?", "app.components.admin.PostManager.dislikes": "Dislikes", "app.components.admin.PostManager.edit": "Edit", "app.components.admin.PostManager.editProjects": "Edit projects", "app.components.admin.PostManager.editStatuses": "Edit statuses", "app.components.admin.PostManager.editTags": "Edit tags", "app.components.admin.PostManager.editedPostSave": "Save", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Importing data from Esri ArcGIS Online is an add-on feature. Talk to your GS manager to unlock it.", "app.components.admin.PostManager.esriSideError": "An error occured on the ArcGIS application. Please wait a few minutes and try again later.", "app.components.admin.PostManager.esriWebMap": "Esri Web Map", "app.components.admin.PostManager.exportAllInputs": "Export all posts (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Export all comments (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Export comments for this project (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Export posts in this project (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Export selected posts (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Export comments for selected posts (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Export votes by input (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Export votes by user (.xslx)", "app.components.admin.PostManager.exports": "Exports", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "You may only upload map data as either GeoJSON layers or importing from ArcGIS Online. Please remove any current GeoJSON layers if you wish to add a Feature Layer.", "app.components.admin.PostManager.featureLayerTooltop": "You can find the Feature Layer URL on the right hand side of the item page on ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Choose how people will see your name", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Explain this status change", "app.components.admin.PostManager.fileUploadError": "One or more files failed to upload. Please check the file size and format and try again.", "app.components.admin.PostManager.formTitle": "Edit idea", "app.components.admin.PostManager.generalApiError2": "An error occured while trying to fetch this item. Please check that the URL or Portal ID is correct and you have access to this item.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "You may only upload map data as either GeoJSON layers or importing from ArcGIS Online. Please remove any ArcGIS data if you wish to upload a GeoJSON layer.", "app.components.admin.PostManager.goToDefaultMapView": "Go to default map center", "app.components.admin.PostManager.hiddenFieldsLink": "hidden fields", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Tip: If you're using Typeform, add {hiddenFieldsLink} to keep track of who has responded to your survey.", "app.components.admin.PostManager.import2": "Import", "app.components.admin.PostManager.importError": "The selected file could not be imported because it's not a valid GeoJSON file", "app.components.admin.PostManager.importEsriFeatureLayer": "Import Esri Feature Layer", "app.components.admin.PostManager.importEsriWebMap": "Import Esri Web Map", "app.components.admin.PostManager.importInputs": "Import inputs", "app.components.admin.PostManager.imported": "Imported", "app.components.admin.PostManager.initiativeFormTitle": "Edit initiative", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputImportProgress": "{importedCount} out of {totalCount} {totalCount, plural, one {input has} other {inputs have}} been imported. The import is still in progress, please check back later.", "app.components.admin.PostManager.inputManagerHeader": "Input", "app.components.admin.PostManager.inputs": "Input", "app.components.admin.PostManager.inputsExportFileName": "input", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Only show posts that need feedback", "app.components.admin.PostManager.issueFormTitle": "Edit issue", "app.components.admin.PostManager.latestFeedbackMode": "Use the latest existing official update as an explanation", "app.components.admin.PostManager.layerAdded": "Layer added successfully", "app.components.admin.PostManager.likes": "<PERSON>s", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Moving this input away from its current project will lose the information about its assigned phases. Do you want to proceed?", "app.components.admin.PostManager.mapData": "Map data", "app.components.admin.PostManager.multipleInputs": "{ideaCount} posts", "app.components.admin.PostManager.newFeedbackMode": "Write a new update to explain this change", "app.components.admin.PostManager.noFilteredResults": "The filters you selected did not return any results", "app.components.admin.PostManager.noInputs": "No inputs yet", "app.components.admin.PostManager.noInputsDescription": "You add your own input or start from a past participation project.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 inputs} one {1 input} other {# inputs}} will be imported from the selected project and phase. The import will run in the background, and the inputs will appear in the input manager once it is complete.", "app.components.admin.PostManager.noOne": "Unassigned", "app.components.admin.PostManager.noProject": "No project", "app.components.admin.PostManager.officialFeedbackModal.author": "Author", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "Choose how your name will appear", "app.components.admin.PostManager.officialFeedbackModal.description": "Providing official feedback helps keep the process transparent and builds trust in the platform.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Author is required", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Feedback is required", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Official feedback", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Explain the reason for the status change", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Post feedback", "app.components.admin.PostManager.officialFeedbackModal.skip": "Skip this time", "app.components.admin.PostManager.officialFeedbackModal.title": "Explain your decision", "app.components.admin.PostManager.officialUpdateAuthor": "Choose how people will see your name", "app.components.admin.PostManager.officialUpdateBody": "Explain this status change", "app.components.admin.PostManager.offlinePicks": "Offline picks", "app.components.admin.PostManager.offlineVotes": "Offline votes", "app.components.admin.PostManager.onlineVotes": "Online votes", "app.components.admin.PostManager.optionFormTitle": "Edit option", "app.components.admin.PostManager.participants": "Participants", "app.components.admin.PostManager.participatoryBudgettingPicks": "Picks", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Online picks", "app.components.admin.PostManager.pbItemCountTooltip": "The number of times this has been included in other participants' participatory budgets", "app.components.admin.PostManager.petitionFormTitle": "Edit petition", "app.components.admin.PostManager.postedIn": "Posted in {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Edit project", "app.components.admin.PostManager.projectsTab": "Projects", "app.components.admin.PostManager.projectsTabTooltipContent": "You can drag and drop posts to move them from one project to another. Note that for timeline projects, you will still need to add the post to a specific phase.", "app.components.admin.PostManager.proposalFormTitle": "Edit proposal", "app.components.admin.PostManager.proposedBudgetTitle": "Proposed budget", "app.components.admin.PostManager.publication_date": "Published on", "app.components.admin.PostManager.questionFormTitle": "Edit question", "app.components.admin.PostManager.reactions": "Reactions", "app.components.admin.PostManager.resetFiltersButton": "Reset filters", "app.components.admin.PostManager.resetInputFiltersDescription": "Reset the filters to see all input.", "app.components.admin.PostManager.saved": "Saved", "app.components.admin.PostManager.screeningTooltip": "Screening is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Screening is turned off for this phase. Go to phase setup to enable it", "app.components.admin.PostManager.selectAPhase": "Select a phase", "app.components.admin.PostManager.selectAProject": "Select a project", "app.components.admin.PostManager.setAsDefaultMapView": "Save the current center point and zoom level as the map defaults", "app.components.admin.PostManager.startFromPastInputs": "Start from past inputs", "app.components.admin.PostManager.statusChangeGenericError": "There was an error, please try again later or contact support.", "app.components.admin.PostManager.statusChangeSave": "Change status", "app.components.admin.PostManager.statusesTab": "Status", "app.components.admin.PostManager.statusesTabTooltipContent": "Change the status of a post using drag and drop. The original author and other contributors will receive a notification of the changed status.", "app.components.admin.PostManager.submitApiError": "There was an issue submitting the form. Please check for any errors and try again.", "app.components.admin.PostManager.timelineTab": "Timeline", "app.components.admin.PostManager.timelineTabTooltipText": "Drag and drop posts to copy them to different project phases.", "app.components.admin.PostManager.title": "Title", "app.components.admin.PostManager.topicsTab": "Tags", "app.components.admin.PostManager.topicsTabTooltipText": "Add tags to an input using drag and drop.", "app.components.admin.PostManager.view": "View", "app.components.admin.PostManager.votes": "Votes", "app.components.admin.PostManager.votesByInputExportFileName": "votes_by_input", "app.components.admin.PostManager.votesByUserExportFileName": "votes_by_user", "app.components.admin.PostManager.webMapAlreadyExists": "You can only add one Web Map at a time. Remove the current one to import a different one.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "You may only upload map data as either GeoJSON layers or importing from ArcGIS Online. Please remove any current GeoJSON layers if you wish to connect a Web Map.", "app.components.admin.PostManager.webMapTooltip": "You can find the Web Map portal ID on your ArcGIS Online item page, on the right hand side.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {Less than a day} one {One day} other {# days}} left", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "Cancel", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "Yes, delete survey results", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "This cannot be undone", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Delete survey results", "app.components.admin.ProjectEdit.survey.downloadResults2": "Download survey results", "app.components.admin.ReportExportMenu.FileName.fromFilter": "from", "app.components.admin.ReportExportMenu.FileName.groupFilter": "group", "app.components.admin.ReportExportMenu.FileName.projectFilter": "project", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tag", "app.components.admin.ReportExportMenu.FileName.untilFilter": "until", "app.components.admin.ReportExportMenu.downloadPng": "Download as PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Download as SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Download Excel", "app.components.admin.SlugInput.regexError": "The slug can only contain regular, lowercase letters (a-z), numbers (0-9) and hyphens (-). The first and last characters cannot be hyphens. Consecutive hyphens (--) are forbidden.", "app.components.admin.TerminologyConfig.saveButton": "Save", "app.components.admin.commonGroundInputManager.title": "Title", "app.components.admin.seatSetSuccess.admin": "Admin", "app.components.admin.seatSetSuccess.allDone": "All done", "app.components.admin.seatSetSuccess.close": "Close", "app.components.admin.seatSetSuccess.manager": "Manager", "app.components.admin.seatSetSuccess.orderCompleted": "Order completed", "app.components.admin.seatSetSuccess.reflectedMessage": "The changes on your plan will be reflected on your next billing cycle.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} rights have been granted to the selected user(s).", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Are you sure you want to delete all survey results?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "This participation method is in beta. We're gradually rolling it out to gather feedback and improve the experience.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Collecting feedback on a document is a custom feature, and is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Contribution", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Number of days is required", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "Number of days to reach minimum number of votes", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "More information on how to embed a link for Google Forms can be found in {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "this support article", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Idea", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Initiative", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "What should an input be called?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Comment", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Provide the link to your Konveio document here. Read our {supportArticleLink} for more information on setting up Konveio.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "support article", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "This is not included in your current plan. Reach out to your Government Success Manager or admin to unlock it.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "A maximum budget is required", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Maximum number of votes per option must be less than or equal to total number of votes", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "A maximum number of votes is required", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "Messaging", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "The minimum budget can't be larger than the maximum budget", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "A minimum budget is required", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "The minimum number of votes can't be larger than the maximum number", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "A minimum number of votes is required", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Missing end date", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Missing start date", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Option", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Input Manager tab", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Configure the voting options in the Input manager tab after creating a phase.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Configure the voting options in the {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Participation options", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "Participants", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Petition", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Admins & managers", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Annotating document:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} can participate in this phase.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "Cancel", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Comment:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Common ground phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Delete phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "Yes, delete this phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "Are you sure you want to delete this phase?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "All data relating to this phase will be deleted. This cannot be undone.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Document annotation phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Everyone", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "External survey phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Ideation phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "In platform survey phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Information phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "Mixed rights", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "No end date", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Poll phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Proposals phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>React:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Registered for event:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Registered users", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b>Submit inputs:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>Taking poll:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b>Taking survey:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Users with confirmed emails", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>Volunteering:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Volunteering phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Voting:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Voting phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Who can participate?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Inputs won’t be visible until an admin reviews and approves them. Authors can’t edit inputs after they are screened or reacted on.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "Admins only", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "Anyone with the link can interact with the draft project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "Approve", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Approving allows Project Managers to publish the project.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "Approved by {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Archived", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Draft", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Edit description", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "Everyone", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Groups", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "Hidden", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Offline voters", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Only admins{inFolder, select, true { or the Folder Managers} other {}} can publish the project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 participant} other {{participantsCount} participants}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Participants in embedded methods (e.g., external surveys)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Followers of a project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Note: Enabling anonymous or open participation permissions may allow users to participate multiple times, leading to misleading or incomplete user data.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "Participants <b>do not include</b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Participants include:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Event registrants", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "Users interacting with Go Vocal methods", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "Waiting for approval", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Project reviewers have been notified.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "Public", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Publish", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Published - Active", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Published - Finished", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Refresh project preview link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Regenerate project preview link. This will invalidate the previous link.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Old links will stop working but you can generate a new one at any time.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Are you sure? This will disable the current link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "Cancel", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "Yes, refresh link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Request approval", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "The project must be approved by an admin{inFolder, select, true { or one of the Folder Managers} other {}} before you can publish it. Click the button below to request approval.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Settings", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Share", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "Copy link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "Link copied", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Sharing private links is not included on your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Share this project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "Who has access", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "View", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Project", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Proposal", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "Question", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Minimum number of votes to be considered", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Minimum number of votes is required", "app.components.app.containers.AdminPage.ProjectEdit.report": "Report", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Require screening of inputs", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Timeline", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Traffic", "app.components.formBuilder.cancelMethodChange1": "Cancel", "app.components.formBuilder.changeMethodWarning1": "Changing methods can lead to hiding any input data generated or received while using the previous method.", "app.components.formBuilder.changingMethod1": "Changing method", "app.components.formBuilder.confirmMethodChange1": "Yes, continue", "app.components.formBuilder.copySurveyModal.cancel": "Cancel", "app.components.formBuilder.copySurveyModal.description": "This will copy all the questions and logic without the answers.", "app.components.formBuilder.copySurveyModal.duplicate": "Duplicate", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "No appropriate phases found in this project", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "No phase selected. Please select a phase first.", "app.components.formBuilder.copySurveyModal.noProject": "No project", "app.components.formBuilder.copySurveyModal.noProjectSelected": "No project selected. Please select a project first.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "You have already saved changes to this survey. If you duplicate another survey, the changes will be lost.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Survey phase", "app.components.formBuilder.copySurveyModal.title": "Select a survey to duplicate", "app.components.formBuilder.editWarningModal.addOrReorder": "Add or reorder questions", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Your response data may be inaccurate", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Edit text", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Fixing a typo? It won't affect your response data", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "You'll lose response data linked to that question", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Delete a question", "app.components.formBuilder.editWarningModal.exportYouResponses2": "export your responses.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Warning: You might lose response data forever. Before continuing,", "app.components.formBuilder.editWarningModal.noCancel": "No, cancel", "app.components.formBuilder.editWarningModal.title4": "Edit live survey", "app.components.formBuilder.editWarningModal.yesContinue": "Yes, continue", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "access rights settings for this survey", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "'Demographic fields in survey form' is enabled. When the survey form is displayed any configured demographic questions will be added on a new page immediately before the end of the survey. These questions can be changed in the {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "access rights settings for this phase.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Survey respondents will not be required to sign up or log in to submit survey answers, which may result in duplicate submissions", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "By skipping the sign up/log in step, you accept not to collect demographic information on survey respondents, which may impact your data analysis capabilities", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "This survey is set to allow access for \"Anyone\" under the Access Rights tab.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "If you wish to change this, you can do so in the {accessRightsSettingsLink}", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "You are asking the following demographic questions of survey respondents through the sign up/log in step.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "To streamline the collection of demographic information and ensure its integration into your user database, we advise incorporating any demographic questions directly into the sign-up/log-in process. To do so, please use the {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Ask users to follow areas or topics", "app.components.onboarding.followHelperText": "This activates a step in the registration process where users will be able to follow areas or topics that you select below", "app.components.onboarding.followPreferences": "Follow preferences", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} within plan, {noOfAdditionalSeats} additional", "app.components.seatsWithinPlan.seatsWithinPlanText": "Seats within plan", "app.containers.Admin.Campaigns.campaignFrom": "From:", "app.containers.Admin.Campaigns.campaignTo": "To:", "app.containers.Admin.Campaigns.customEmails": "Custom emails", "app.containers.Admin.Campaigns.customEmailsDescription": "Send out custom emails and check statistics.", "app.containers.Admin.Campaigns.noAccess": "We're sorry, but it seems like you don't have access to the emails section", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Automated emails", "app.containers.Admin.Insights.tabReports": "Reports", "app.containers.Admin.Invitations.a11y_removeInvite": "Remove invitation", "app.containers.Admin.Invitations.addToGroupLabel": "Add these people to specific manual user groups", "app.containers.Admin.Invitations.adminLabel1": "Give invitees admin rights", "app.containers.Admin.Invitations.adminLabelTooltip": "When you select this option, the people you're inviting will have access to all your platform settings.", "app.containers.Admin.Invitations.configureInvitations": "3. Configure the invitations", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "There are no invites that match your search", "app.containers.Admin.Invitations.deleteInvite": "Delete", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Are you sure you want to delete this invitation?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Cancelling an invitation will allow you to resend an invitation to this person.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Download and fill out the template", "app.containers.Admin.Invitations.downloadTemplate": "Download template", "app.containers.Admin.Invitations.email": "Email", "app.containers.Admin.Invitations.emailListLabel": "Manually enter the email addresses of the people you want to invite. Seperate each address by a comma.", "app.containers.Admin.Invitations.exportInvites": "Export all invitations", "app.containers.Admin.Invitations.fileRequirements": "Important: In order to send the invitations correctly, no column can be removed from the import template. Leave unused columns empty.", "app.containers.Admin.Invitations.filetypeError": "Incorrect file type. Only XLSX files are supported.", "app.containers.Admin.Invitations.groupsPlaceholder": "No group selected", "app.containers.Admin.Invitations.helmetDescription": "Invite users to the platform", "app.containers.Admin.Invitations.helmetTitle": "Admin invitation dashboard", "app.containers.Admin.Invitations.importOptionsInfo": "These options will only be taken into account when they are not defined in the Excel file.\n      Please visit the {supportPageLink} for more information.", "app.containers.Admin.Invitations.importTab": "Import email addresses", "app.containers.Admin.Invitations.invitationExpirationWarning": "Be aware that invitations expire after 30 days. After this period, you can still resend them.", "app.containers.Admin.Invitations.invitationOptions": "Invitation options", "app.containers.Admin.Invitations.invitationSubtitle": "Invite people to the platform at any point in time. They get a neutral invitation email with your logo, in which they are asked to register on the platform.", "app.containers.Admin.Invitations.invitePeople": "Invite people via email", "app.containers.Admin.Invitations.inviteStatus": "Status", "app.containers.Admin.Invitations.inviteStatusAccepted": "Accepted", "app.containers.Admin.Invitations.inviteStatusPending": "Pending", "app.containers.Admin.Invitations.inviteTextLabel": "Optionally type a message that will be added to the invitation mail.", "app.containers.Admin.Invitations.invitedSince": "Invited", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Select the language of the invitation", "app.containers.Admin.Invitations.moderatorLabel": "Give these people project management rights", "app.containers.Admin.Invitations.moderatorLabelTooltip": "When you select this option, the invitee(s) will be assigned project manager rights for the selected project(s). More information on project manager rights {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "here", "app.containers.Admin.Invitations.name": "Name", "app.containers.Admin.Invitations.processing": "Sending out invitations. Please wait...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "No project(s) selected", "app.containers.Admin.Invitations.save": "Send out invitations", "app.containers.Admin.Invitations.saveErrorMessage": "One or more errors occurred and the invitations were not sent out. Please correct the error(s) listed below and try again.", "app.containers.Admin.Invitations.saveSuccess": "Success!", "app.containers.Admin.Invitations.saveSuccessMessage": "Invitation successfully sent out.", "app.containers.Admin.Invitations.supportPage": "support page", "app.containers.Admin.Invitations.supportPageLinkText": "Visit the support page", "app.containers.Admin.Invitations.tabAllInvitations": "All invitations", "app.containers.Admin.Invitations.tabInviteUsers": "Invite users", "app.containers.Admin.Invitations.textTab": "Manually enter email addresses", "app.containers.Admin.Invitations.unknownError": "Something went wrong. Please try again later.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Upload your completed template file", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} if you want more info about all supported columns in the import template.", "app.containers.Admin.Moderation.all": "All", "app.containers.Admin.Moderation.belongsTo": "Belongs to", "app.containers.Admin.Moderation.collapse": "see less", "app.containers.Admin.Moderation.comment": "Comment", "app.containers.Admin.Moderation.commentDeletionCancelButton": "Cancel", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "Delete", "app.containers.Admin.Moderation.confirmCommentDeletion": "Are you sure you want to delete this comment? This is permanent and can't be undone.", "app.containers.Admin.Moderation.content": "Content", "app.containers.Admin.Moderation.date": "Date", "app.containers.Admin.Moderation.deleteComment": "Delete comment", "app.containers.Admin.Moderation.goToComment": "Open this comment in a new tab", "app.containers.Admin.Moderation.goToPost": "Open this post in a new tab", "app.containers.Admin.Moderation.goToProposal": "Open this proposal in a new tab", "app.containers.Admin.Moderation.markFlagsError": "Couldn't mark item(s). Try again.", "app.containers.Admin.Moderation.markNotSeen": "Mark {selectedItemsCount, plural, one {# item} other {# items}} as not seen", "app.containers.Admin.Moderation.markSeen": "Mark {selectedItemsCount, plural, one {# item} other {# items}} as seen", "app.containers.Admin.Moderation.moderationsTooltip": "This page allows you to quickly check all new posts that have been published to your platform, including ideas and comments. You can mark posts as being 'seen' so that others know what still needs to be processed.", "app.containers.Admin.Moderation.noUnviewedItems": "There are no unseen items", "app.containers.Admin.Moderation.noViewedItems": "There are no seen items", "app.containers.Admin.Moderation.pageTitle1": "Feed", "app.containers.Admin.Moderation.post": "Post", "app.containers.Admin.Moderation.profanityBlockerSetting": "Profanity blocker", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Block posts containing the most commonly reported offensive words.", "app.containers.Admin.Moderation.project": "Project", "app.containers.Admin.Moderation.read": "Seen", "app.containers.Admin.Moderation.readMore": "Read more", "app.containers.Admin.Moderation.removeFlagsError": "Couldn't remove warning(s). Try again.", "app.containers.Admin.Moderation.rowsPerPage": "Rows per page", "app.containers.Admin.Moderation.settings": "Settings", "app.containers.Admin.Moderation.settingsSavingError": "Couldn't save. Try changing the setting again.", "app.containers.Admin.Moderation.show": "Show", "app.containers.Admin.Moderation.status": "Status", "app.containers.Admin.Moderation.successfulUpdateSettings": "Settings updated successfully.", "app.containers.Admin.Moderation.type": "Type", "app.containers.Admin.Moderation.unread": "Not seen", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "This page consists of the following sections. You can turn them on/off and edit them as required.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Sections", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "View page", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Not shown on page", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Shown on page", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Attachments", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Add files (max. 50 MB) that will be available to download from the page.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Bottom info section", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Add your own content to the customizable section at the bottom of the page.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Edit", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Events list", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Show events related to the projects.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Hero banner", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Customise the page banner image and text.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Projects list", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Show the projects based on your page settings. You can preview the projects that will be shown.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Top info section", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Add your own content to the customizable section at the top of the page.", "app.containers.Admin.PagesAndMenu.addButton": "Add to navbar", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Name in navbar", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Are you sure you want to delete this page? This cannot be undone.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Provide a title for all languages", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Other available pages", "app.containers.Admin.PagesAndMenu.components.savePage": "Save page", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "<PERSON> successfully saved", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Attachments (max 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Couldn't save attachments", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the bottom of this page", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Attachments saved", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Attachments | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Attachments", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Save and enable attachments", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Save attachments", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Provide content for all languages", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Couldn't save bottom info section", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Bottom info section saved", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Bottom info section", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Save and enable bottom info section", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Save bottom info section", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Creating custom pages is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Please select at least one tag", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "By area", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "By tag(s)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Displaying projects by tag or area is not part of your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Edit custom page", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Linked projects", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Select which projects and related events can be displayed on the page.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "<PERSON> successfully created", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "<PERSON> successfully saved", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Custom page saved", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Title in navigation bar", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Create custom page | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Create custom page", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "None", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Page settings", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Save custom page", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Please select an area", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Selected area", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Selected tags", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "The slug can only contain regular, lowercase letters (a-z), numbers (0-9) and hyphens (-). The first and last characters cannot be hyphens. Consecutive hyphens (--) are forbidden.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "You must enter a slug", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Title", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Enter a title in every language", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Enter a title", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "View custom page", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Edit custom page | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Page content", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Edit", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "For single phase projects, if the end date is empty and the description is not filled in, a timeline will not be displayed on the project page.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "No available projects based on your {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "This project has no tag or area filter, so no projects will be displayed.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Projects list | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "page settings", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Projects list", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "The following projects will be shown on this page based on your {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "DEFAULT", "app.containers.Admin.PagesAndMenu.deleteButton": "Delete", "app.containers.Admin.PagesAndMenu.editButton": "Edit", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Success", "app.containers.Admin.PagesAndMenu.heroBannerError": "Couldn't save hero banner", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Hero banner saved", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Save hero banner", "app.containers.Admin.PagesAndMenu.homeTitle": "Home", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Provide content for at least one language", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "You can only add up to 5 items to the navigation bar", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Pages & menu | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Remove from navbar", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Save and enable hero banner", "app.containers.Admin.PagesAndMenu.title": "Pages & menu", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Success", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.topInfoError": "Couldn't save top info section", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Top info section saved", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Top info section | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Top info section", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Save and enable top info section", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Save top info section", "app.containers.Admin.PagesAndMenu.viewButton": "View", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "Age", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Community", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Executive summary", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Top-level inclusion indicators", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "The following section outlines inclusion indicators, highlighting your our progress towards fostering a more inclusive and representative participation platform.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "participants", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Top-level participation indicators", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "The following section outlines the key participation indicators for the selected time range, providing an overview of engagement trends and performance metrics.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projects", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "projects published", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Platform report", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "Your projects", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "The following section provides an overview of publicly visible projects that overlap with the selected time range, the most used methods in these projects, and metrics concerning the total amount of participation.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Registrations timeline", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Blocked users", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Manage blocked users.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Delete Group", "app.containers.Admin.Users.GroupsHeader.editGroup": "Edit Group", "app.containers.Admin.Users.GroupsPanel.admins": "Admins", "app.containers.Admin.Users.GroupsPanel.allUsers": "Registered users", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Groups", "app.containers.Admin.Users.GroupsPanel.managers": "Project managers", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Assigned items", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Get an overview of all the people and organisations that registered on the platform. Add a selection of users to Manual groups or Smart groups.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Invitation pending", "app.containers.Admin.Users.admin": "Admin", "app.containers.Admin.Users.assign": "Assign", "app.containers.Admin.Users.assignedItems": "Assigned items for {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "Buy one additional seat", "app.containers.Admin.Users.changeUserRights": "Change user rights", "app.containers.Admin.Users.confirm": "Confirm", "app.containers.Admin.Users.confirmAdminQuestion": "Are you sure you want to give {name} platform admin rights?", "app.containers.Admin.Users.confirmNormalUserQuestion": "Are you sure you want to set {name} as a normal user?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Are you sure you want to set {name} as a normal user? Please note that they will lose manager rights to all the projects and folders that they are assigned to on confirmation.", "app.containers.Admin.Users.deleteUser": "Delete user", "app.containers.Admin.Users.email": "Email", "app.containers.Admin.Users.folder": "Folder", "app.containers.Admin.Users.folderManager": "Folder manager", "app.containers.Admin.Users.helmetDescription": "User list in admin", "app.containers.Admin.Users.helmetTitle": "Admin - users dashboard", "app.containers.Admin.Users.inviteUsers": "Invite users", "app.containers.Admin.Users.joined": "Joined", "app.containers.Admin.Users.lastActive": "Last active", "app.containers.Admin.Users.name": "Name", "app.containers.Admin.Users.noAssignedItems": "No assigned items", "app.containers.Admin.Users.options": "Options", "app.containers.Admin.Users.permissionToBuy": "To give {name} admin rights, you need to buy 1 additional seat.", "app.containers.Admin.Users.platformAdmin": "Platform admin", "app.containers.Admin.Users.projectManager": "Project manager", "app.containers.Admin.Users.reachedLimitMessage": "You have reached the limit of seats within your plan, 1 additional seat for {name} will be added.", "app.containers.Admin.Users.registeredUser": "Registered user", "app.containers.Admin.Users.remove": "Remove", "app.containers.Admin.Users.removeModeratorFrom": "The user is moderating the folder this project belongs to. Remove assignment from \"{folderTitle}\" instead.", "app.containers.Admin.Users.role": "Role", "app.containers.Admin.Users.seeProfile": "See profile", "app.containers.Admin.Users.selectPublications": "Select projects or folders", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Type to search", "app.containers.Admin.Users.setAsAdmin": "Set as admin", "app.containers.Admin.Users.setAsNormalUser": "Set as normal user", "app.containers.Admin.Users.setAsProjectModerator": "Set as project manager", "app.containers.Admin.Users.setUserAsProjectModerator": "Assign {name} as project manager", "app.containers.Admin.Users.userBlockModal.allDone": "All done", "app.containers.Admin.Users.userBlockModal.blockAction": "Block user", "app.containers.Admin.Users.userBlockModal.blockInfo1": "The content of this user won't be removed through this action. Don't forget to moderate their content if needed.", "app.containers.Admin.Users.userBlockModal.blocked": "Blocked", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "This user has been blocked since {from}. The ban lasts until {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "Cancel", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Are you sure you want to unblock {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} is blocked until {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 day} other {{numberOfDays} days}}", "app.containers.Admin.Users.userBlockModal.header": "Block user", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Reason", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "This will be communicated to the blocked user.", "app.containers.Admin.Users.userBlockModal.subtitle1": "The selected user won't be able to log in to the platform for {daysBlocked}. If you wish to revert this, you can unblock them from the list of blocked users.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Unblock", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "Yes, I want to unblock this user", "app.containers.Admin.Users.userDeletionConfirmation": "Permanently remove this user?", "app.containers.Admin.Users.userDeletionFailed": "An error occurred while deleting this user, please try again.", "app.containers.Admin.Users.userDeletionProposalVotes": "This will also delete any votes by this user on proposals which are still open for voting.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.userInsights": "User insights", "app.containers.Admin.Users.youCantDeleteYourself": "You cannot delete your own account via the user admin page", "app.containers.Admin.Users.youCantUnadminYourself": "You cannot give up your role as an admin now", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Community monitor", "app.containers.Admin.communityMonitor.healthScore": "Health Score", "app.containers.Admin.communityMonitor.healthScoreDescription": "This score is the average of all sentiment-scale questions answered by participants for the period selected.", "app.containers.Admin.communityMonitor.lastQuarter": "last quarter", "app.containers.Admin.communityMonitor.liveMonitor": "Live monitor", "app.containers.Admin.communityMonitor.noResults": "No results for this period.", "app.containers.Admin.communityMonitor.noSurveyResponses": "No survey responses", "app.containers.Admin.communityMonitor.participants": "Participants", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Reports", "app.containers.Admin.communityMonitor.settings": "Settings", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Community Monitor Survey is accepting submissions.", "app.containers.Admin.communityMonitor.settings.accessRights2": "Access rights", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "After a user registers an event attendance, submits a vote, or returns to a project page after submitting a survey.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Community Monitor Managers can access and manage all Community Monitor settings and data.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Community Monitor Managers", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Managers can edit the Community Monitor survey & permissions, see response data and create reports.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "The default frequency value is 100%.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Popup frequency (0 to 100)", "app.containers.Admin.communityMonitor.settings.management2": "Management", "app.containers.Admin.communityMonitor.settings.popup": "Popup", "app.containers.Admin.communityMonitor.settings.popupDescription3": "A popup is periodically displayed to users encouraging them to complete the Community Monitor Survey. You can adjust the frequency which determines the percentage of users who will randomly see the popup when the conditions outlined below are met.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Popup settings", "app.containers.Admin.communityMonitor.settings.preview": "Preview", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "User has not already filled out the survey in the previous 3 months.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "User has not already seen the popup in the previous 3 months.", "app.containers.Admin.communityMonitor.settings.save": "Save", "app.containers.Admin.communityMonitor.settings.saved": "Saved", "app.containers.Admin.communityMonitor.settings.settings": "Settings", "app.containers.Admin.communityMonitor.settings.survey2": "Survey", "app.containers.Admin.communityMonitor.settings.surveySettings3": "General settings", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Upon loading the Homepage or a Custom Page.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonymize all user data", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "All of the survey's inputs from users will be anonymized before being recorded", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Users will still need to comply with participation requirements under the 'Access Rights'. User profile data will not be available in the survey data export.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "Under what conditions can the popup appear for users?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Who are the managers?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Total survey responses", "app.containers.Admin.communityMonitor.upsell.aiSummary": "AI summary", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Enable Community Monitor", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Health score", "app.containers.Admin.communityMonitor.upsell.learnMore": "Learn more", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Score over time", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "The Community Monitor helps you stay ahead by tracking resident trust, satisfaction with services, and community life — continuously.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Get clear scores, powerful quotes, and a quarterly report you can share with colleagues or elected officials.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Easy-to-read scores that evolve over time", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Key resident quotes, summarised by AI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Questions tailored to your city's context", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Residents recruited randomly on the platform", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Quarterly PDF reports, ready to share", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "Understand how your community feels before issues grow", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Count", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Desktop or Other", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Mobile", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tablet", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Device Types", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Device Type", "app.containers.Admin.earlyAccessLabel": "Early Access", "app.containers.Admin.earlyAccessLabelExplanation": "This is a newly released feature available in Early Access.", "app.containers.Admin.emails.addCampaign": "Create email", "app.containers.Admin.emails.addCampaignTitle": "Create a new email", "app.containers.Admin.emails.allParticipantsInProject": "All participants in project", "app.containers.Admin.emails.allUsers": "Registered users", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Automated emails are automatically sent out and are triggered by a user's actions. You can turn some of them off for all users of your platform. The other automated emails can not be turned off because they are required for the proper functioning of your platform.", "app.containers.Admin.emails.automatedEmails": "Automated emails", "app.containers.Admin.emails.automatedEmailsDigest": "The email will only be sent if there is content", "app.containers.Admin.emails.automatedEmailsRecipients": "Users who will receive this email", "app.containers.Admin.emails.automatedEmailsTriggers": "Event that triggers this email", "app.containers.Admin.emails.changeRecipientsButton": "Change recipients", "app.containers.Admin.emails.clickOnButtonForExamples": "Click on the button below to check examples of this email on our support page.", "app.containers.Admin.emails.confirmSendHeader": "Email to all users?", "app.containers.Admin.emails.deleteButtonLabel": "Delete", "app.containers.Admin.emails.draft": "Draft", "app.containers.Admin.emails.editButtonLabel": "Edit", "app.containers.Admin.emails.editCampaignTitle": "Edit campaign", "app.containers.Admin.emails.editDisabledTooltip2": "Coming soon: This email cannot currently be edited.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Button text", "app.containers.Admin.emails.editRegion_intro_multiloc": "Introduction", "app.containers.Admin.emails.editRegion_subject_multiloc": "Subject", "app.containers.Admin.emails.editRegion_title_multiloc": "Title", "app.containers.Admin.emails.emailCreated": "Email successfully created in draft", "app.containers.Admin.emails.emailUpdated": "Email successfully updated", "app.containers.Admin.emails.emptyCampaignsDescription": "Easily connect with your participants by sending them emails. Choose who to contact and track your engagement.", "app.containers.Admin.emails.emptyCampaignsHeader": "Send your first email", "app.containers.Admin.emails.failed": "Failed", "app.containers.Admin.emails.fieldBody": "Message", "app.containers.Admin.emails.fieldBodyError": "Provide an email message for all languages", "app.containers.Admin.emails.fieldGroupContent": "Email Content", "app.containers.Admin.emails.fieldReplyTo": "Replies should go to", "app.containers.Admin.emails.fieldReplyToEmailError": "Provide an email address in the correct format, <NAME_EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Provide an email address", "app.containers.Admin.emails.fieldReplyToTooltip": "You can choose where to send replies to your email.", "app.containers.Admin.emails.fieldSender": "From", "app.containers.Admin.emails.fieldSenderError": "Provide a sender of the email", "app.containers.Admin.emails.fieldSenderTooltip": "You can decide who the recipients will see as the sender of the email.", "app.containers.Admin.emails.fieldSubject": "Email Subject", "app.containers.Admin.emails.fieldSubjectError": "Provide an email subject for all languages", "app.containers.Admin.emails.fieldSubjectTooltip": "This will be shown in the subject line of the email and in the user’s inbox overview. Make it clear and engaging.", "app.containers.Admin.emails.fieldTo": "To", "app.containers.Admin.emails.fieldToTooltip": "You can select the user groups that will receive your email", "app.containers.Admin.emails.formSave": "Save as draft", "app.containers.Admin.emails.formSaveAsDraft": "Save as draft", "app.containers.Admin.emails.from": "From:", "app.containers.Admin.emails.groups": "Groups", "app.containers.Admin.emails.helmetDescription": "Send out manual emails to user groups and activate automated campaigns", "app.containers.Admin.emails.nameVariablesInfo2": "You can speak directly to citizens using the variables {firstName} {lastName}. E.g. \"Dear {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "A preview email has been sent to your email address", "app.containers.Admin.emails.previewTitle": "Preview", "app.containers.Admin.emails.regionMultilocError": "Please provide a value for all languages", "app.containers.Admin.emails.seeEmailHereText": "As soon as an email of this type is sent you'll be able to check it here.", "app.containers.Admin.emails.send": "Send", "app.containers.Admin.emails.sendNowButton": "Send now", "app.containers.Admin.emails.sendTestEmailButton": "Send me a test email", "app.containers.Admin.emails.sendTestEmailTooltip2": "When you click this link, a test email will be sent to your email address only. This allows you to check what the email looks like in ‘real life’.", "app.containers.Admin.emails.senderRecipients": "Sender and recipients", "app.containers.Admin.emails.sending": "Sending", "app.containers.Admin.emails.sent": "<PERSON><PERSON>", "app.containers.Admin.emails.sentToUsers": "These are emails sent to users", "app.containers.Admin.emails.subject": "Subject:", "app.containers.Admin.emails.supportButtonLabel": "See examples on our support page", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "To:", "app.containers.Admin.emails.toAllUsers": "Do you want to send this email to all registered users?", "app.containers.Admin.emails.viewExample": "View", "app.containers.Admin.ideas.import": "Import", "app.containers.Admin.inspirationHub.AllProjects": "All projects", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Community monitor", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Document annotation", "app.containers.Admin.inspirationHub.ExternalSurvey": "External survey", "app.containers.Admin.inspirationHub.Filters.Country": "Country", "app.containers.Admin.inspirationHub.Filters.Method": "Method", "app.containers.Admin.inspirationHub.Filters.Search": "Search", "app.containers.Admin.inspirationHub.Filters.Topic": "Topic", "app.containers.Admin.inspirationHub.Filters.population": "Population", "app.containers.Admin.inspirationHub.Highlighted": "Highlighted", "app.containers.Admin.inspirationHub.Ideation": "Ideation", "app.containers.Admin.inspirationHub.Information": "Information", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Please choose a country to see pinned projects", "app.containers.Admin.inspirationHub.PinnedProjects.country": "Country", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "No pinned projects found for this country. Change the country to see pinned projects for other countries", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Change the country to see more pinned projects", "app.containers.Admin.inspirationHub.Poll": "Poll", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "Open ended", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "Read more...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Phase {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "If you don't want your project to be included in the inspiration hub, reach out to your GovSuccess manager.", "app.containers.Admin.inspirationHub.Proposals": "Proposals", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Sort by", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Participants (lowest first)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Participants (highest first)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Start date (oldest first)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Start date (newest first)", "app.containers.Admin.inspirationHub.Survey": "Survey", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Curated list of the best projects around the globe.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Talk to, and learn from, fellow practitioners.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filter by method, city size & country.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Enable Inspiration Hub", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "https://support.govocal.com/en/articles/11093736-using-the-inspiration-hub", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "Learn more", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "The Inspiration Hub connects you to a curated feed of exceptional participation projects on Go Vocal platforms across the world. Learn how other cities run successful projects & talk to other practitioners.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Join a network of pioneering democracy practitioners", "app.containers.Admin.inspirationHub.Volunteering": "Volunteering", "app.containers.Admin.inspirationHub.Voting": "Voting", "app.containers.Admin.inspirationHub.commonGround": "Common ground", "app.containers.Admin.inspirationHub.filters": "filters", "app.containers.Admin.inspirationHub.resetFilters": "Reset filters", "app.containers.Admin.inspirationHub.seemsLike": "Seems like there are no more projects. Try changing the {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "Edit campaign fields", "app.containers.Admin.messaging.automated.variablesToolTip": "You can use the following variables in your message:", "app.containers.Admin.messaging.helmetTitle": "Messaging", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "This widget shows each user projects <b>based on their follow preferences</b>. This includes projects that they follow, as well as projects where they follow inputs, and projects related to topics or areas that they are interested in.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "This widget will only be shown to the user if there are projects where they can participate. If you see this message, it means that you (the admin) cannot participate in any projects at this moment. This message will not be visible on the real homepage.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Open to participation", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "This widget will showcase projects where the user can currently <b>take an action to participate</b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Title", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "For you", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "This widget will only be shown to the user if there are projects relevant for them based on their follow preferences. If you see this message, it means that you (the admin) are not following anything at the moment. This message will not be visible on the real homepage.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "Followed items", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Archived", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Filter by", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Finished", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Finished and archived", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "No data available", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "This widget shows <b>projects that are finished and/or archived.</b>. \"Finished\" also includes projects that are in the last phase, and where the last phase is a report.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Finished projects", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "You said, we did...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "Provide a name for all languages", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "The project cannot be empty", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Name", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Project", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Resulting URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "Save", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Add project", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "The navigation bar will only show projects to which users have access.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "This widget will only be visible on the Homepage when the Community Monitor is accepting responses.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Community Monitor", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Description", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Title", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Important:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Example of a sentiment survey question", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "No end date", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "Press escape to skip carrousel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projects and folders (legacy)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Projects title", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} is currently working on", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Button text", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Participate now!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Description", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Please select a project or folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Select project or folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Show avatars", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Spotlight", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Title", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "Starting in {days} days", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Starting in {weeks} weeks", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} days ago", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} weeks ago", "app.containers.Admin.project.Campaigns.campaignFrom": "From:", "app.containers.Admin.project.Campaigns.campaignTo": "To:", "app.containers.Admin.project.Campaigns.customEmails": "Custom emails", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Send out custom emails and check statistics.", "app.containers.Admin.project.Campaigns.noAccess": "We're sorry, but it seems like you don't have access to the emails section", "app.containers.Admin.project.emails.addCampaign": "Create email", "app.containers.Admin.project.emails.addCampaignTitle": "New campaign", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "All {participants} and followers from the project", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "This includes registered users that performed any action in the project. Unregistered or anonymized users are not included.", "app.containers.Admin.project.emails.dateSent": "Date sent", "app.containers.Admin.project.emails.deleteButtonLabel": "Delete", "app.containers.Admin.project.emails.draft": "Draft", "app.containers.Admin.project.emails.editButtonLabel": "Edit", "app.containers.Admin.project.emails.editCampaignTitle": "Edit campaign", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Easily connect with your participants by sending them emails. Choose who to contact and track your engagement.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Send your first email", "app.containers.Admin.project.emails.failed": "Failed", "app.containers.Admin.project.emails.fieldBody": "Email Message", "app.containers.Admin.project.emails.fieldBodyError": "Provide an email message for all languages", "app.containers.Admin.project.emails.fieldReplyTo": "Replies should go to", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Provide an email address in the correct format, <NAME_EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "Provide an email address", "app.containers.Admin.project.emails.fieldReplyToTooltip": "Choose what email address should receive direct replies from users on your email.", "app.containers.Admin.project.emails.fieldSender": "From", "app.containers.Admin.project.emails.fieldSenderError": "Provide a sender of the email", "app.containers.Admin.project.emails.fieldSenderTooltip": "Choose whom users will see as the sender of the email.", "app.containers.Admin.project.emails.fieldSubject": "Email Subject", "app.containers.Admin.project.emails.fieldSubjectError": "Provide an email subject for all languages", "app.containers.Admin.project.emails.fieldSubjectTooltip": "This will be shown in the subject line of the email and in the user’s inbox overview. Make it clear and engaging.", "app.containers.Admin.project.emails.fieldTo": "To", "app.containers.Admin.project.emails.formSave": "Save as draft", "app.containers.Admin.project.emails.from": "From:", "app.containers.Admin.project.emails.helmetDescription": "Send out manual emails to project participants", "app.containers.Admin.project.emails.infoboxAdminText": "From the Project Messaging tab you can only email all project participants.  To email other participants or subsets of users go to the {link} tab.", "app.containers.Admin.project.emails.infoboxLinkText": "Platform Messaging", "app.containers.Admin.project.emails.infoboxModeratorText": "From the Project Messaging tab you can only email all project participants. Admins can send emails to other participants or subsets of users via the Platform Messaging tab.", "app.containers.Admin.project.emails.message": "Message", "app.containers.Admin.project.emails.nameVariablesInfo2": "You can speak directly to citizens using the variables {firstName} {lastName}. E.g. \"Dear {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "participants", "app.containers.Admin.project.emails.previewSentConfirmation": "A preview email has been sent to your email address", "app.containers.Admin.project.emails.previewTitle": "Preview", "app.containers.Admin.project.emails.projectParticipants": "Project participants", "app.containers.Admin.project.emails.recipients": "Recipients", "app.containers.Admin.project.emails.send": "Send", "app.containers.Admin.project.emails.sendTestEmailButton": "Send a preview", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Send this draft email to the email address with which you are logged in, to check how it looks like in ‘real life’.", "app.containers.Admin.project.emails.senderRecipients": "Sender and recipients", "app.containers.Admin.project.emails.sending": "Sending", "app.containers.Admin.project.emails.sent": "<PERSON><PERSON>", "app.containers.Admin.project.emails.sentToUsers": "These are emails sent to users", "app.containers.Admin.project.emails.status": "Status", "app.containers.Admin.project.emails.subject": "Subject:", "app.containers.Admin.project.emails.to": "To:", "app.containers.Admin.project.messaging.helmetTitle": "Messaging", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "This image is part of the folder card; the card that summarizes the folder and is shown on the homepage for example. For more information on recommended image resolutions, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "This image is shown at the top of the folder page. For more information on recommended image resolutions, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "visit our support center", "app.containers.Admin.projects.all.askPersonalData3": "Add fields for name and email", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "Enable calendar view", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "Learn more", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "Get a visual overview of your project timelines in our calendar view. Quickly identify which projects and phases are starting or ending soon and require action.", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "Understand what is happening and when", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "All questions are shown on the PDF. However, the following are not currently supported for import via FormSync: Images, Tags and File Upload.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "All questions are shown on the PDF. However, the following are not currently supported for import via FormSync: Mapping questions (drop pin, draw route and draw area), ranking questions, matrix questions and file upload questions.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "End of the form", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Start of the form", "app.containers.Admin.projects.all.components.archived": "Archived", "app.containers.Admin.projects.all.components.draft": "Draft", "app.containers.Admin.projects.all.components.manageButtonLabel": "Edit", "app.containers.Admin.projects.all.copyProjectButton": "Copy project", "app.containers.Admin.projects.all.copyProjectError": "There was an error copying this project, please try again later.", "app.containers.Admin.projects.all.customiseEnd": "Customise the end of the form.", "app.containers.Admin.projects.all.customiseStart": "Customise the start of the form.", "app.containers.Admin.projects.all.deleteFolderButton1": "Delete folder", "app.containers.Admin.projects.all.deleteFolderConfirm": "Are you sure you want to delete this folder? All of the projects within the folder will also be deleted. This action cannot be undone.", "app.containers.Admin.projects.all.deleteFolderError": "There was an issue removing this folder. Please try again.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Delete project", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Are you sure you want to delete this project? This cannot be undone.", "app.containers.Admin.projects.all.deleteProjectError": "There was an error deleting this project, please try again later.", "app.containers.Admin.projects.all.exportAsPDF1": "Download PDF form", "app.containers.Admin.projects.all.itIsAlsoPossible1": "You can combine online and offline responses. To upload offline responses, go to the 'Input manager' tab of this project, and click 'Import'.", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "You can combine online and offline responses. To upload offline responses, go to the 'Survey' tab of this project, and click 'Import'.", "app.containers.Admin.projects.all.logicNotInPDF": "Survey logic will not be reflected in the downloaded PDF. Paper respondents will see all survey questions.", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "Search folders", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "All folders have been loaded", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Folder", "app.containers.Admin.projects.all.new.Folders.Table.managers": "Managers", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# project} other {# projects}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Project start date", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Discoverability", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "Hidden", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "Public", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Folders", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filter by the current phase participation method", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "Ideation", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Information", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Participation method", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Document annotation", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Common ground", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "Survey", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "Poll", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Proposals", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Volunteering", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Voting", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "Collecting data", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informing", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "Not started", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Participation state", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Past", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "Pending approval", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "Search projects", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Alphabetically (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Alphabetically (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "Manager", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Phase starting or ending soon", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc": "Recently created (new-old)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc": "Recently created (old-new)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "Recently viewed", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Admins", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Groups", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Visibility", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "Public", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "Add filter", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "Clear", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "No more filters to add", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Admins", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "All projects have been loaded", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "Anyone", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Archived", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "Current phase:", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d left", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d to start", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Discoverability:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Draft", "app.containers.Admin.projects.all.new.Projects.Table.end": "End", "app.containers.Admin.projects.all.new.Projects.Table.ended": "Ended", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "Ends today", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Groups", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "Hidden", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "Loading more…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "Manager", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}mo left", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}mo to start", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Next phase:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "Not assigned", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Phase", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "Pre-launch", "app.containers.Admin.projects.all.new.Projects.Table.project": "Project", "app.containers.Admin.projects.all.new.Projects.Table.public": "Public", "app.containers.Admin.projects.all.new.Projects.Table.published": "Published", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Scroll down to load more", "app.containers.Admin.projects.all.new.Projects.Table.start": "Start", "app.containers.Admin.projects.all.new.Projects.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Status:", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "This column uses cached participant data. To see the latest numbers, check the \"Participants\" tab of the project.", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Visibility", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Visibility:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} groups", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} managers", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}y left", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y to start", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Current phase: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} days left", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Folder: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "No current phase", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "No end date", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "No phases", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Phase {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Phases:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Project", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Start date: {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "Arrange projects", "app.containers.Admin.projects.all.new.calendar": "Calendar", "app.containers.Admin.projects.all.new.folders": "Folders", "app.containers.Admin.projects.all.new.projects": "Projects", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "Failed to load timeline.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Project has no end date", "app.containers.Admin.projects.all.new.timeline.project": "Project", "app.containers.Admin.projects.all.notes": "Notes", "app.containers.Admin.projects.all.personalDataExplanation5": "This option will add the first name, last name, and email fields to the exported PDF. Upon uploading the paper form, we will use that data to auto-generate an account for the offline survey respondent.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "AI Summary", "app.containers.Admin.projects.project.analysis.Comments.comments": "Comments", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "Comment summary is available when there are 5 or more comments.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Summarize comments", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {Refresh} =1 {1 new comment} other {# new comments}}", "app.containers.Admin.projects.project.analysis.aiSummary": "AI summary", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "This is AI-generated content. It may not be 100% accurate. Please review and cross-reference with the actual inputs for accuracy. Be aware that the accuracy is likely to improve if the number of selected inputs is reduced.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Email notifications only sent to participants", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "Hidden", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "Not indexed by search engines", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Not visible on the homepage or widgets", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Only accessible via direct URL", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "Public", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "Select how discoverable this project is.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "This project is visible to everyone who has access, and will appear on the homepage and in the widgets.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "This project will be hidden from the wider public, and will only be visible to those who have the link.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Who can find this project?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Open AI analysis", "app.containers.Admin.projects.project.ideas.analysisText2": "Explore AI-powered summaries and view individual submissions.", "app.containers.Admin.projects.project.ideas.importInputs": "Import", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "After creating a report, you can choose to share it with the public once the phase starts.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Create a more complex page for information sharing", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Create a report to:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "Create a report", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Create a report for a past phase, or start from scratch.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "This report is not public. To make it public, enable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "This phase has started, but the report is not public yet. To make it public, enable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Start with a phase template", "app.containers.Admin.projects.project.information.ReportTab.report": "Report", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Share results of a past survey or ideation phase", "app.containers.Admin.projects.project.information.ReportTab.visible": "Visible", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "This report will be public as soon as the phase starts. To make it not public, disable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "This report is currently public. To make it not public, disable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Are you sure you want to delete this report? This action cannot be undone.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Add to phase", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "You need to consent to this before you can continue", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "The form can be downloaded here.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "The uploaded form was created with the \"Personal data\" section", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Form language", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "I hereby consent to processing this file using the Google Cloud Form Parser", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Import Excel file", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Please upload a file to continue", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "The template can be downloaded here.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Upload", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Upload a completed <b>Excel file</b> (.xlsx). It must use the template provided for this project. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Upload a <b>PDF file of scanned forms</b>. It must use a form printed from this phase. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Use this email for the new user", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Enter a valid email to create a new account", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "A new account will be created for the author with these details. This input will be added to it.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "First name", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Last name", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Please enter an email address and/or a first name and last name to assign this input to an author. Or uncheck the consent box.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "There is already an account associated with this email. This input will be added to it.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "User consent (create user account)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "Approve", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Approve all inputs", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Author:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "Email:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Errors occurred during the import and some inputs have not imported. Please correct the errors and re-import any missing inputs.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Invalid form data. Check the form above for errors.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Import Excel file (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Import", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Import scanned forms (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Import scanned forms", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "Imported inputs", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importing. This process may take a few minutes.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "This input was imported anonymously.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} inputs have been imported and require approval.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} inputs could not be approved. Please check each input for validation issues and confirm individually.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Locale:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Nothing to review yet. Click \"{importFile}\" to import a PDF file containing scanned input forms or an Excel file containing inputs.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Nothing to review yet. Click \"{importFile}\" to import an Excel file containing inputs.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "Input", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "Page", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Cannot display the imported file. Imported file viewing is only available for PDF imports.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Phase:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "The selected phase cannot contain inputs. Please select another one.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "This project does not contain any phases that can contain ideas.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "Please select to which phase you want to add these inputs.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Input importer", "app.containers.Admin.projects.project.participation.comments": "Comments", "app.containers.Admin.projects.project.participation.inputs": "Inputs", "app.containers.Admin.projects.project.participation.participantsTimeline": "Participants timeline", "app.containers.Admin.projects.project.participation.reactions": "Reactions", "app.containers.Admin.projects.project.participation.selectPeriod": "Select period", "app.containers.Admin.projects.project.participation.usersByAge": "Users by age", "app.containers.Admin.projects.project.participation.usersByGender": "Users by gender", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "Required", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "Add a question", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "The ability to add or edit user fields at phase level is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} options", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "Field status", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "These questions will be added as the last page of the survey form, because 'Show fields in survey?' has been selected in phase settings.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "No extra questions will be asked.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Optional", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Optional - always enabled because referenced by group", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "Remove field", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Required - always enabled because referenced by group", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Authenticate with {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Complete the extra questions below", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Confirm your email", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Data returned from verification method:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Enter first name, last name, email and password", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "Enter your email", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "How recently should users be verified?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Identity verification with {verificationMethod} (based on user group)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "No actions are required to participate", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Use smart groups to restrict participation based on the verified data listed above", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Users must have been verified in the last 30 minutes.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Users must have been verified in the last {days} days.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "In the last 30 days", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "In the last 30 minutes", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "In the last 7 days", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "Once is enough", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Verified fields:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} verification", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Account creation", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Participants need to create a full account with their name, confirmed email and password.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Participants need to create a full account with their name, email and password.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Authentication", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "Edit", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Email confirmation", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Participants need to confirm their email with a one-time code.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Advanced spam detection", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "This feature helps prevent duplicate survey submissions from logged-out users by analyzing IP addresses and device data. While not as precise as requiring login, it can help to reduce the number of duplicate responses.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Note: On shared networks (like offices or public Wi-Fi), there is a small chance that different users could be flagged as duplicates.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Enable advanced spam detection", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Extra questions asked to participants", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "None", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Anyone can participate without signing up or logging in.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Reset extra questions and groups", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Restrict participation to user group(s)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO verification", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Participants need to verify their identify with {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Open AI analysis", "app.containers.Admin.projects.project.survey.allFiles": "All files", "app.containers.Admin.projects.project.survey.allResponses": "All responses", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Accuracy: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "There was an error generating the AI summary. Please try to regenerate it below.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Open AI analysis", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "<PERSON><PERSON> summaries for this question", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "inputs selected", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Open analysis actions", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} new responses", "app.containers.Admin.projects.project.survey.analysis.regenerate": "Regenerate", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Show AI insights", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "You can summarise a maximum of 30 inputs at a time on your current plan. Talk to your GovSuccess Manager or admin to unlock more.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Select related questions for analysis", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "Do you want to include any other related questions in your analysis of {question}?", "app.containers.Admin.projects.project.survey.cancel": "Cancel", "app.containers.Admin.projects.project.survey.consentModalButton": "Continue", "app.containers.Admin.projects.project.survey.consentModalCancel": "Cancel", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "I agree to using OpenAI as a data processor for this project", "app.containers.Admin.projects.project.survey.consentModalText1": "By continuing you agree to the using OpenAI as a data processor for this project.", "app.containers.Admin.projects.project.survey.consentModalText2": "The OpenAI APIs power the automated text summaries and parts of the automated tagging experience.", "app.containers.Admin.projects.project.survey.consentModalText3": "We only send what users wrote in their surveys, ideas and comments to the OpenAI APIs, never any information from their profile.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI will not use this data for further training of its models. More information on how OpenAI handles data privacy can be found {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "here", "app.containers.Admin.projects.project.survey.consentModalTitle": "Before you continue", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "You can't enter the analysis before you have edited the form", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Delete", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Are you sure you want to delete this analysis? This action cannot be undone.", "app.containers.Admin.projects.project.survey.explore": "Explore", "app.containers.Admin.projects.project.survey.followUpResponses": "Follow up responses", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> average", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Export as GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Export the responses to this question as a GeoJSON file. For each GeoJSON Feature, all of the related respondent's survey responses will be listed in that Feature's 'properties' object.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "Hide details", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} respondents} one {{respondentCount} respondent} other {{respondentCount} respondents}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "View details", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} choices} one {{numberChoices} choice} other {{numberChoices} choices}}", "app.containers.Admin.projects.project.survey.heatMap": "Heat map", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Learn more about heat maps generated using Esri Smart Mapping.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "The heat map is generated using Esri Smart Mapping. Heat maps are useful when there is a large amount of data points. For fewer points, it may be better to look at only the location points directly. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Heat map view", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Hidden by logic", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "When a user selects this answer logic skips all pages until page {pageNumber} ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "When a user selects this answer logic skips to the survey end ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "Logic on this page skips all pages until page {pageNumber} ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Logic on this page skips to the survey end ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.newAnalysis": "New analysis", "app.containers.Admin.projects.project.survey.nextInsight": "Next insight", "app.containers.Admin.projects.project.survey.openAnalysis": "Open AI analysis", "app.containers.Admin.projects.project.survey.otherResponses": "Other responses", "app.containers.Admin.projects.project.survey.page": "Page", "app.containers.Admin.projects.project.survey.previousInsight": "Previous insight", "app.containers.Admin.projects.project.survey.responses": "Responses", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "The number of responses for this page is lower than the total number of survey responses because some respondents will not have seen this page because of logic in the survey.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "The number of responses for this question is lower than the total number of survey responses because some respondents will not have seen this question because of logic in the survey.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Sentiment scale", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Instantly summarise all your responses.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Talk to your data in natural language.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Get references to individual responses from AI generated summaries.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Check our {link} for a full overview.", "app.containers.Admin.projects.project.survey.upsell.button": "Unlock AI analysis", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "support article", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Analyse data faster with AI", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.Admin.projects.project.survey.viewAnalysis": "View", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Explore AI-powered summaries and view individual submissions.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Select period", "app.containers.Admin.projects.project.traffic.trafficSources": "Traffic sources", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "We have changed the way we collect and display visitor data. As a result, visitor data is more accurate and more types of data are available, while still being GDPR compliant. We only started collecting this new data in November 2024, so before that no data is available.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Visitors timeline", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Phase report", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Add some text about the phase", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "This is some text. You can edit and format it by using the editor in the panel on the right.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Participants", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Project results", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Report summary", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Add the goal of the project, participation methods used, and the outcome", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Visitors", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "This report contains unsaved changes. Please save before printing.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Title is already taken", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "Compared to previous {days} days", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Hide statistics", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Participation rate", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Show comparison with last period", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "You need to select a date range first.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "Comments", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Inputs", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Participation", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Show comments", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "Show inputs", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Show votes", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "Votes", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Demographics", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Registration date range", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Registration field", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Unknown", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Users: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Active", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Archived", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "Finished", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "Open-ended", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "Planned", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projects", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Publication status", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Published", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "The data for this widget is missing. Reconfigure or delete it to be able to save the report.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Community Monitor Health Score", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "Quarter", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "Year", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "No appropriate phases found in this project", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "No phase selected. Please select a phase first.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "No project", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "No project selected. Please select a project first.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "You cannot duplicate this report because it contains data that you don't have access to.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "You cannot edit this report because it contains data that you don't have access to.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Are you sure you want to delete \"{reportName}\"? This action cannot be undone.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Are you sure you want to delete this report? This action cannot be undone.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Delete", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Duplicate", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Edit", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "Modified {days, plural, no {# days} one {# day} other {# days}} ago", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "An error occurred when trying to create this report. Please try again later.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Start with a blank page", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Start with a Community Monitor template", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Report title", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "Create a report", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Customise your report and share it with internal stakeholders or community as a PDF file.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Create a report", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Create your first report", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "No project selected", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Start with a platform template", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Print to PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Start with a project template", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Quarter {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Report template", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "A report with this title already exists. Please pick a different title.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Select quarter", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Select year", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Share as PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "To share with everyone, print the report as a PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Share as web link", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "This web link is only accessible to admin users.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Share", "app.containers.Admin.reporting.contactToAccess": "Creating a custom report is part of the premium license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "All reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Community monitor reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "These are reports are related to the Community Monitor. Reports are automatically generated every quarter.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Create a report", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Customise your report and share it with internal stakeholders or community with a web link.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Your reports will appear here.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Search reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Progress reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "These are reports created by your Government Success Manager", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "Your reports", "app.containers.Admin.reporting.deprecated": "DEPRECATED", "app.containers.Admin.reporting.helmetDescription": "Admin reporting page", "app.containers.Admin.reporting.helmetTitle": "Reporting", "app.containers.Admin.reporting.printPrepare": "Preparing to print...", "app.containers.Admin.reporting.reportBuilder": "Report builder", "app.containers.Admin.reporting.reportHeader": "Report header", "app.containers.Admin.reporting.warningBanner3": "Graphs and numbers in this report only update automatically on this page. Save the report to update them on other pages.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Common ground", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Ideation", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Information", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Methods Used", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "Survey", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "Poll", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "Previous {days} days: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Proposals", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "External survey", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Volunteering", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Voting", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Chart", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "Table", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "View", "app.containers.Admin.surveyFormTab.downloads": "Downloads", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "Duplicate another survey", "app.containers.Admin.surveyFormTab.editSurveyForm": "Edit survey form", "app.containers.Admin.surveyFormTab.inputFormDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required.", "app.containers.Admin.surveyFormTab.surveyForm": "Survey form", "app.containers.Admin.tools.apiTokens.createTokenButton": "Create new token", "app.containers.Admin.tools.apiTokens.createTokenCancel": "Cancel", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Your token has been created. Please copy the {secret} below and store it safely.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Create a new token to use with our public API.", "app.containers.Admin.tools.apiTokens.createTokenError": "Provide a name for your token", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Create token", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>Important!</b> You can only copy this {secret} once. If you close this window you will not be able to see it again.", "app.containers.Admin.tools.apiTokens.createTokenName": "Name", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Give your token a name", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Your token has been created", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Close", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "Copy {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Copied!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Create a new token", "app.containers.Admin.tools.apiTokens.createdAt": "Created", "app.containers.Admin.tools.apiTokens.delete": "Delete token", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Are you sure you want to delete this token?", "app.containers.Admin.tools.apiTokens.description": "Manage your API tokens for our public API. For more information, see our {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Last used", "app.containers.Admin.tools.apiTokens.link": "API documentation", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Name", "app.containers.Admin.tools.apiTokens.noTokens": "You do not have any tokens yet.", "app.containers.Admin.tools.apiTokens.title": "Public API tokens", "app.containers.Admin.tools.esriDisabled": "The Esri integration is an add-on feature. Contact your GovSuccess Manager if you want more information on this.", "app.containers.Admin.tools.esriIntegration2": "Esri integration", "app.containers.Admin.tools.esriIntegrationButton": "Enable <PERSON>", "app.containers.Admin.tools.esriIntegrationDescription3": "Connect your Esri account and import data from ArcGIS Online directly into your mapping projects.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri logo", "app.containers.Admin.tools.esriKeyInputDescription": "Add your Esri API key to allow importing your map layers from ArcGIS Online in the map tabs in projects.", "app.containers.Admin.tools.esriKeyInputLabel": "Esri API key", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Paste API key here", "app.containers.Admin.tools.esriMaps": "Esri Maps", "app.containers.Admin.tools.esriSaveButtonError": "There was an error saving your key, please try again.", "app.containers.Admin.tools.esriSaveButtonSuccess": "API key saved", "app.containers.Admin.tools.esriSaveButtonText": "Save key", "app.containers.Admin.tools.learnMore": "Learn more", "app.containers.Admin.tools.managePublicAPIKeys": "Manage API Keys", "app.containers.Admin.tools.manageWidget": "Manage widget", "app.containers.Admin.tools.manageWorkshops": "Manage workshops", "app.containers.Admin.tools.powerBIAPIImage": "Power BI image", "app.containers.Admin.tools.powerBIDescription": "Use our plug & play Power BI Templates to access Go Vocal data in your Microsoft Power BI Workspace.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI is not part of your license. Contact your GovSuccess Manager if you want more info on this.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Download templates", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "If you intend to use your Go Vocal data within a Power BI data flow, this template will allow you to set up a new data flow that connects to your Go Vocal data. Once you have downloaded this template you must first find and replace the following strings ##CLIENT_ID## and ##CLIENT_SECRET## in the template with your public API credentials before uploading to PowerBI.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Download data flow template", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Dataflow template", "app.containers.Admin.tools.powerBITemplates.intro": "Note: To use either of these Power BI templates, you must first {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "create a set of credentials for our public API", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "This template will create a Power BI report based on your Go Vocal data. It will set up all the data connections to your Go Vocal platform, create the data model and some default dashboards. When you open the template in Power BI you will be prompted to enter your public API credentials. You will also need to enter the Base Url for your platform, which is: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Download reporting template", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Report template", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Further details about using your Go Vocal data in Power BI can be found in our {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "support article", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Power BI templates", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "Manage the credentials to create custom integrations on our public API.", "app.containers.Admin.tools.publicAPIDisabled1": "The public API is not part of your current license. Contact your GovSuccess Manager if you want more info on this.", "app.containers.Admin.tools.publicAPIImage": "Public API image", "app.containers.Admin.tools.publicAPITitle": "Public API Access", "app.containers.Admin.tools.toolsLabel": "Tools", "app.containers.Admin.tools.widgetDescription": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.Admin.tools.widgetImage": "Widget image", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "Hold live video meetings, facilitate simultaneous group discussions and debates. <PERSON><PERSON> input, vote and reach consensus, just like you would offline.", "app.containers.Admin.tools.workshopsImage": "Workshops image", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Online deliberation workshops", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "total users on the platform", "app.containers.AdminPage.DashboardPage._blank": "unknown", "app.containers.AdminPage.DashboardPage.allGroups": "All Groups", "app.containers.AdminPage.DashboardPage.allProjects": "All Projects", "app.containers.AdminPage.DashboardPage.allTime": "All Time", "app.containers.AdminPage.DashboardPage.comments": "Comments", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Comments", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "A base dataset is required to measure the representativeness of platform users.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Coming soon", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "We're currently working on the {fieldName} dashboard, it will be available soon", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# item is} other {# items are}} hidden in this graph. Change to {tableViewLink} to view all data.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} for user registration", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} out of {total} users included ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Show {numberOfHiddenItems} more", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Optional", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Please provide a base dataset.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Representativeness score:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "This score reflects how accurately platform user data reflects the total population. Learn more about {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Required", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Submit base data", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "table view", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Total population", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Users", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Add an age group", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} and over", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Age group", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Age group(s) of {upperBound} and over is not included.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Age group {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Age groups", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "and over", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Apply example grouping", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Clear all", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "From", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Set age groups to align with your base dataset.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Range", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Save", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "To", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Edit age groups", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "This item will not be calculated.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "See less", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "See {numberOfHiddenItems} more...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Base month (optional)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Age groups (Year of birth)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Coming soon", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Complete", "app.containers.AdminPage.DashboardPage.components.Field.default": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Please fill out all enabled options, or disable the options you want to omit from the graph. At least one option must be filled out.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Incomplete", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Number of total residents", "app.containers.AdminPage.DashboardPage.components.Field.options": "Options", "app.containers.AdminPage.DashboardPage.components.Field.save": "Save", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Saved", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Please {setAgeGroupsLink} first to start entering base data.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "set age groups", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Avg. response time: {days} days", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Average amount of days to respond", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "Feedback given", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Input status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Inputs by status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Number of inputs", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Official update", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Percentage of inputs", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Response time", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Status changed", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Total", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Edit base data", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "how we calculate representativeness scores", "app.containers.AdminPage.DashboardPage.continuousType": "Without a timeline", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Cumulative total", "app.containers.AdminPage.DashboardPage.customDateRange": "Custom", "app.containers.AdminPage.DashboardPage.day": "day", "app.containers.AdminPage.DashboardPage.false": "false", "app.containers.AdminPage.DashboardPage.female": "female", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Top 5 inputs by reactions", "app.containers.AdminPage.DashboardPage.fromTo": "from {from} to {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Dashboard for activities on the platform", "app.containers.AdminPage.DashboardPage.helmetTitle": "Admin dashboard page", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Pick resource to show by project", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Pick a resource to show by tag", "app.containers.AdminPage.DashboardPage.inputs1": "Inputs", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Inputs by status", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Select user group", "app.containers.AdminPage.DashboardPage.male": "male", "app.containers.AdminPage.DashboardPage.month": "month", "app.containers.AdminPage.DashboardPage.noData": "There is no data to be shown.", "app.containers.AdminPage.DashboardPage.noPhase": "No phase created for this project", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "The number of participants that posted inputs, reacted or commented.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "Dislikes", "app.containers.AdminPage.DashboardPage.numberOfLikes": "<PERSON>s", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Total reactions", "app.containers.AdminPage.DashboardPage.overview.management": "Management", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projects & Participation", "app.containers.AdminPage.DashboardPage.overview.showLess": "Show less", "app.containers.AdminPage.DashboardPage.overview.showMore": "Show more", "app.containers.AdminPage.DashboardPage.participants": "Participants", "app.containers.AdminPage.DashboardPage.participationPerProject": "Participation per project", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Participation per tag", "app.containers.AdminPage.DashboardPage.perPeriod": "Per {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Previous 30 days", "app.containers.AdminPage.DashboardPage.previous90Days": "Previous 90 days", "app.containers.AdminPage.DashboardPage.previousWeek": "Previous week", "app.containers.AdminPage.DashboardPage.previousYear": "Previous year", "app.containers.AdminPage.DashboardPage.projectType": "Project type : {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "Reactions", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "This base dataset is required to calculate the representativeness of platform users compared to the total population.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Please provide a base dataset.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "See how representative your platform users are compared to the total population - based on data collected during user registration. Learn more about {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "See how representative your platform users are compared to the total population - based on data collected during user registration.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Community representativeness", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Back to dashboard", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "None of the enabled registration fields are supported at the moment.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Here you can show/hide items on the dashboard and enter the base data. Only the enabled fields for {userRegistrationLink} will appear here.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Edit base data", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "user registration", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Submit base data", "app.containers.AdminPage.DashboardPage.resolutionday": "in Days", "app.containers.AdminPage.DashboardPage.resolutionmonth": "in Months", "app.containers.AdminPage.DashboardPage.resolutionweek": "in Weeks", "app.containers.AdminPage.DashboardPage.selectProject": "Select project", "app.containers.AdminPage.DashboardPage.selectedProject": "current project filter", "app.containers.AdminPage.DashboardPage.selectedTopic": "current tag filter", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Discover what's happening on your platform.", "app.containers.AdminPage.DashboardPage.tabOverview": "Overview", "app.containers.AdminPage.DashboardPage.tabReports": "Reports", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Representativeness", "app.containers.AdminPage.DashboardPage.tabUsers": "Users", "app.containers.AdminPage.DashboardPage.timelineType": "Timeline", "app.containers.AdminPage.DashboardPage.titleDashboard": "Dashboard", "app.containers.AdminPage.DashboardPage.total": "Total", "app.containers.AdminPage.DashboardPage.totalForPeriod": "This {period}", "app.containers.AdminPage.DashboardPage.true": "true", "app.containers.AdminPage.DashboardPage.unspecified": "unspecified", "app.containers.AdminPage.DashboardPage.users": "Users", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Users by age", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Users by geographic area", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Users by gender", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Registrations", "app.containers.AdminPage.DashboardPage.week": "week", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Tips for choosing a favicon image: select a simple image, as the shown image size is very small. The image should be saved as a PNG, and should be square with a transparent background (or a white background if necessary). Your favicon should only be set once as changes will require some technical support.", "app.containers.AdminPage.FaviconPage.save": "Save", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Success!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Add", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Delete", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Folder managers", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Folder managers can edit the folder description, create new projects within the folder, and have project management rights over all projects within the folder. They cannot delete projects and they do not have access to projects that are not within their folder. You can {projectManagementInfoCenterLink} to find more information on project management rights.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "No match found", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "visit our Help Center", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Search users", "app.containers.AdminPage.FoldersEdit.addToFolder": "Add to folder", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Archived", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Delete this folder", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Description", "app.containers.AdminPage.FoldersEdit.draftStatus": "Draft", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Add files to this folder", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the folder page.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Descriptions", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "There are no projects in this folder. Go back to the main Projects tab to create and add projects.", "app.containers.AdminPage.FoldersEdit.folderName": "Folder name", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Header image", "app.containers.AdminPage.FoldersEdit.multilocError": "All text fields must be filled in for every language.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "There are no projects that you can add to this folder.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Folder card image", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Permissions", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Folder projects", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Settings", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projects added to this folder", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Projects you can add to this folder", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Choose whether this folder is \"draft\", \"published\" or \"archived\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Published", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Remove from folder", "app.containers.AdminPage.FoldersEdit.save": "Save", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Success!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Short description", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "shown on the homepage", "app.containers.AdminPage.FoldersEdit.statusLabel": "Publication status", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Explain why the projects belong together, define a visual identity and share information.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Explain why the projects belong together, define a visual identity and share information.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "All text fields must be filled in.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Title", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Create a new folder", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Settings", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "View Folder", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Customise the hero banner image and text.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Hero banner", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Save hero banner", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Inspiration Hub is a place where you can find inspiration for your projects by browsing through projects on other platforms.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Edit your platform's terms and conditions and privacy policy. Other pages, including the About and FAQ pages, can be edited in the {navigationLink} tab.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Platform policies", "app.containers.AdminPage.PagesEdition.privacy-policy": "Privacy Policy", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Terms and Conditions", "app.containers.AdminPage.Project.confirmation.description": "This action cannot be undone.", "app.containers.AdminPage.Project.confirmation.no": "Cancel", "app.containers.AdminPage.Project.confirmation.title": "Are you sure you want to reset all participation data?", "app.containers.AdminPage.Project.confirmation.yes": "Reset all participation data", "app.containers.AdminPage.Project.data.descriptionText1": "Clear ideas, comments, votes, reactions, survey responses, poll responses, volunteers and event registrants. In the case of voting phases, this action will clear the votes but not the options.", "app.containers.AdminPage.Project.data.title": "Clear all participation data from this project", "app.containers.AdminPage.Project.resetParticipationData": "Reset all participation data", "app.containers.AdminPage.Project.settings.accessRights": "Access rights", "app.containers.AdminPage.Project.settings.back": "Back", "app.containers.AdminPage.Project.settings.data": "Data", "app.containers.AdminPage.Project.settings.description": "Description", "app.containers.AdminPage.Project.settings.events": "Events", "app.containers.AdminPage.Project.settings.general": "General", "app.containers.AdminPage.Project.settings.projectTags": "Project tags", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "List of projects on the platform", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Projects dashboard", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Create new projects or manage existing projects.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projects", "app.containers.AdminPage.ProjectDashboard.published": "Published", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Close settings panel", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Center", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "Full width", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "Left", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "Button alignment", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "Right", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Button text", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Enter text for the button", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "Primary", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Button type", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Secondary", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "Button URL", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Enter a URL for the button", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Column layout", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Description", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Homepage description", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Shown on the project card on the home page.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Shown on the project page. Clearly describe what the project is about, what you expect from your users and what they can expect from you.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectDescription.preview": "Preview", "app.containers.AdminPage.ProjectDescription.save": "Save", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectDescription.saved": "Saved!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Decide on which message you want to give to your audience. Edit your project and enrich it with images, videos, file attachments,… This information helps visitors to understand what your project is about.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Project description", "app.containers.AdminPage.ProjectDescription.whiteSpace": "White space", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Include border", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "Vertical height", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "Large", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Medium", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Small", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Cancel editing", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "The default latitude of the map center point. Accepts a value between -90 and 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "The default longitude of the map center point. Accepts a value between -90 and 90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Edit map layer", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Edit layer", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectEdit.MapTab.here": "here", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Import GeoJSON file", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Default latitude", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Layer color", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "All features in the layer will be styled with this color. This color will also overwrite any existing styling in your GeoJSON file.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Marker icon", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Optionally select an icon that is displayed in the markers. Click {url} to see the list of icons you can select.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Layer name", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "This layer name is shown on the map legend", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Layer tooltip", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "This text is displayed as a tooltip when hovering over the layer features on the map", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Map layers", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "We currently support GeoJSON files. Read the {supportArticle} for tips on how to convert and style map layers.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Default longitude", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Map default center and zoom", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "The default center point and zoom level of the map. Manually adjust the values below, or click on the {button} button in the bottom left corner of the map to save the current center point and zoom level of the map as the default values.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Customize the map view, including uploading and styling map layers and setting the map center and zoom level.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Map configuration", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "The map configuration is currently shared across phases, you can't create different map configurations per phase.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Remove layer", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Save", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Save zoom", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "support article", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Unnamed layer", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Map zoom level", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "The default zoom level of the map. Accepts a value between 1 and 17, where 1 is fully zoomed out (the entire world is visible) and 17 is fully zoomed in (blocks and buildings are visible)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Anonymize all user data", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "All of the survey's inputs from users will be anonymized before being recorded", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Users will still need to comply with participation requirements under the access 'Access Rights' tab. User profile data will not be available in the survey data export.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "If you enable this option, user registration fields will be shown as the last page in the survey instead of as part of the signup process.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Demographic fields in survey form", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Show demographic fields in survey?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "Read more about how auto-sharing works in this article.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Auto-share results", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Voting results are shared on the platform and via email to participants when the phase ends. This ensures transparency by default.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Result sharing", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Add an answer option", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Add a poll question", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "Cancel", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Cancel", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Cancel", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Delete", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Delete", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Edit answer option", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Save answer options", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Edit answer options", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "Edit question", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Export the poll results", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "The maximum number of choices is greater than the number of options", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Multiple choice", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "No options", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "All questions must have answer choices", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Only one option", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Poll respondents have only one choice", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Manage answer options for: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Here you can create poll questions, set the answer choices for participants to choose from for each question, decide whether you want participants to only be able to select one answer choice (single choice) or multiple answer choices (multiple choice), and export the poll results. You can create multiple poll questions within one poll.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Save", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "Save", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "Save", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Single choice", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Polls settings and results", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Wrong maximum", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "Give feedback, add tags or copy posts to the next project phase.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "Manage proposals, give feedback and assign topics.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Input manager", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "Result sharing is turned off.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Voting results won't be shared at the end of the phase unless you modify it in the phase setup.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "These results will be automatically shared once the phase ends. Modify the end date of this phase to change when the results are shared.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Export the survey results (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Results", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Here, you can download the results of the Typeform survey(s) within this project as an Excel file.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "Survey form", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Survey Results", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Survey", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Consult the survey answers", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Add cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Are you sure?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Use this to explain what is required from volunteers and what they can expect.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Couldn't save because the form contains errors.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Image", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Title", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Delete", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "A cause is an action or activity that participants can volunteer for.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Edit cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Add a description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Add a title", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Export volunteers", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "A cause is an action or activity that participants can volunteer for.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "New cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Save", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Here, you can set up the causes users can volunteer for and download the volunteers.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Volunteering", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {no volunteers} one {# volunteer} other {# volunteers}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "budget allocation", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Assign a budget to options and ask participants to select their preferred options that fit within a total budget.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Budget allocation", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Allowing users to comment can bias the voting process.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "Credit", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Default view of options", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Actions for users", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Select what additional actions users can take.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Fixed amount", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "If left empty, this will default to \"vote\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "Learn more about when to use <b> {voteTypeDescription} </b> in our {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Maximum votes per option", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Maximum amount of votes", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "You can limit the number votes a user can cast in total (with a maximum of one vote per option).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "multiple votes per option", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Users are given an amount of tokens to distribute between options", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Multiple votes per option", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Number of votes per user", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Option analysis overview", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Options to vote on", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "Point", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "single vote per option", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Users can chose to approve any of the options", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "One vote per option", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Token", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Unlimited", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "What should a vote be called?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "E.g. tokens, points, carbon credits...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "E.g. token, point, carbon credit...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "Vote", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Each voting method has different pre-configurations", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "Voting method", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "The voting method determines the rules of how users vote", "app.containers.AdminPage.ProjectEdit.addNewInput": "Add an input", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "You can add your project to an folder now, or do it later in the project settings", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Project tags", "app.containers.AdminPage.ProjectEdit.altText": "Alt text", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Anonymous polling", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "When enabled it's impossible to see who voted on what. Users still need an account and can only vote once.", "app.containers.AdminPage.ProjectEdit.approved": "Approved", "app.containers.AdminPage.ProjectEdit.archived": "Archived", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Archived projects are still visible, but do not allow further participation", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Archived", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "This area cannot be deleted because it is being used to display projects on the following more custom page(s). You will need to unlink the area from the page, or delete the page before you can delete the area.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "All Areas", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "The project will show on every area filter.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Area filter", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projects can be filtered on the homepage using areas. Areas can be set {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "No specific area", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "The project will not show when filtering by area.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Selection", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "The project will show on selected area filter(s).", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Cards", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "Most discussed", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Add survey content", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Submissions to this survey have started to come in. Changes to the survey may result in data loss and incomplete data in the exported files.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Survey successfully saved", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "Survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "View survey", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Select a voting method, and have users prioritize between a few different options.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Conduct a voting or prioritization exercise", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Create a project from a template", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Embed an external survey", "app.containers.AdminPage.ProjectEdit.createInput": "Add new input", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Create an in-platform survey", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Set up a survey without leaving our platform.", "app.containers.AdminPage.ProjectEdit.createPoll": "Create a poll", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Set up a multiple-choice questionnaire.", "app.containers.AdminPage.ProjectEdit.createProject": "New project", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Embed a Typeform, Google Form, Enalyzer, SurveyXact, Qualtrics, SmartSurvey, Snap Survey or Microsoft Forms survey.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "You can set the default order for posts to be displayed on the main project page.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Sorting", "app.containers.AdminPage.ProjectEdit.departments": "Departments", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Description", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "This will enable or disable disliking, but liking will still be enabled. We recommend leaving this disabled unless you are carrying out an option analysis.", "app.containers.AdminPage.ProjectEdit.disabled": "Disabled", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Number of dislikes per participant", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Enable disliking", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Collect feedback on a document", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Embed an interactive PDF and collect comments and feedback with Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Disabled", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Enabled", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Draft projects are hidden for all people except admins and assigned project managers.", "app.containers.AdminPage.ProjectEdit.draft": "Draft", "app.containers.AdminPage.ProjectEdit.draftStatus": "Draft", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEdit.enabled": "Enabled", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Select what participative actions users can take.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Events", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Attachments (max 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the project information page.", "app.containers.AdminPage.ProjectEdit.filesTab": "Files", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Find volunteers", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Ask participants to volunteer for activities and causes.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "As a folder manager, you can choose a folder when creating the project, but only an admin can change it afterward", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Folder card image alternative text", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Select a folder to add this project to.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Custom content", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Submissions to this form have started to come in. Changes to the form may result in data loss and incomplete data in the exported files.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Form successfully saved", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Survey end", "app.containers.AdminPage.ProjectEdit.fromATemplate": "From a template", "app.containers.AdminPage.ProjectEdit.generalTab": "General", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Header image alt text", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Header image", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NEW", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Provide information to users, or use the report builder to share results on past phases.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Share information or results", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Collect input and feedback", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Create or collect inputs, reactions and/or comments. Pick between different types of inputs: idea collection, option analysis, question and answer, issue identification and more.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Who is responsible for processing the posts?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "All new input in this project will be assigned to this person. The assignee can be changed in the {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Commenting on posts", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Input form", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "input manager", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Input manager", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Submitting posts", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Reacting to inputs", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Default view", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Choose the default view to show input: cards in a grid view or pins on a map. Participants can manually switch between the two views.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Inspiration hub", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Embed Konveio URL", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Number of likes per participant", "app.containers.AdminPage.ProjectEdit.limited": "Limited", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Load more templates", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Map", "app.containers.AdminPage.ProjectEdit.mapTab": "Map", "app.containers.AdminPage.ProjectEdit.maxDislikes": "Maximum dislikes", "app.containers.AdminPage.ProjectEdit.maxLikes": "Maximum likes", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Maximum number of votes per option must be less than or equal to total number of votes", "app.containers.AdminPage.ProjectEdit.maximum": "Maximum", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Participants cannot exceed this budget when submitting their basket.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimum", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Require participants to meet a minimum budget to submit their basket (enter '0' if you would not like to set a minimum).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "visit our Help Center", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Who are the project managers?", "app.containers.AdminPage.ProjectEdit.moreDetails": "More details", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "Need inspiration? Explore similar projects from other cities in the {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "Add a contribution", "app.containers.AdminPage.ProjectEdit.newIdea": "New idea", "app.containers.AdminPage.ProjectEdit.newInitiative": "Add an initiative", "app.containers.AdminPage.ProjectEdit.newIssue": "Add an issue", "app.containers.AdminPage.ProjectEdit.newOption": "Add an option", "app.containers.AdminPage.ProjectEdit.newPetition": "Add a petition", "app.containers.AdminPage.ProjectEdit.newProject": "New Project", "app.containers.AdminPage.ProjectEdit.newProposal": "Add a proposal", "app.containers.AdminPage.ProjectEdit.newQuestion": "Add a question", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Most recent", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Not a valid amount", "app.containers.AdminPage.ProjectEdit.noFolder": "No folder", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "— No folder —", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "No templates found", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Please enter a project title", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Not a valid number", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Oldest", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Only visible to admin", "app.containers.AdminPage.ProjectEdit.optionNo": "No", "app.containers.AdminPage.ProjectEdit.optionYes": "Yes (select folder)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Participation levels", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "What do you want to do?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Choose how users can participate.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "You can specify who can take each action, and ask additional questions to participants to collect more information.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Participant requirements & questions", "app.containers.AdminPage.ProjectEdit.pendingReview": "Pending approval", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Access rights", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "Access rights", "app.containers.AdminPage.ProjectEdit.phaseEmails": "Notifications", "app.containers.AdminPage.ProjectEdit.pollTab": "Poll", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "Most reactions", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Project card image", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "This image is part of the project card; the card that summarizes the project and is shown on the homepage for example.\n\n    For more information on recommended image resolutions, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Folder", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "This image is shown at the top of the project page.\n\n    For more information on recommended image resolutions, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Project card image alternative text", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Provide a short description of the image for visually impaired users. This helps screen readers convey what the image is about.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Project management", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Project managers can edit projects, manage posts and email participants. You can {moderationInfoCenterLink} to find more information about the rights assigned to project managers.", "app.containers.AdminPage.ProjectEdit.projectName": "Project name", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Project type", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Projects with a timeline have a clear beginning and end and can have different phases. Projects without a timeline are continuous.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "The project type can not be changed later.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "You can set the project to be invisible to certain users.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Project visibility", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Looking for the project status? Now you can change it at any time directly from the project page header.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Published projects are visible to everyone or a group subset if selected.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Published", "app.containers.AdminPage.ProjectEdit.purposes": "Purposes", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Random", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Reset participation data", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "An error occurred while saving your data. Please try again.", "app.containers.AdminPage.ProjectEdit.saveProject": "Save", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Success!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Your form has been saved!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Search the templates", "app.containers.AdminPage.ProjectEdit.selectGroups": "Select group(s)", "app.containers.AdminPage.ProjectEdit.setup": "Setup", "app.containers.AdminPage.ProjectEdit.shareInformation": "Share information", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Snap Survey", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Set up and personalise your project.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "visit our support center", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Add survey content", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Cancel", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# choices} one {# choice} other {# choices}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "Yes, I want to leave", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "Edit", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Submissions to this survey have started to come in. Changes to the survey may result in data loss and incomplete data in the exported files.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "File upload", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "Go back", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Import", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "AI summaries for short answer, long answer, and sentiment scale follow up questions can be accessed from the AI tab in the left sidebar.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Linear scale", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "Matrix", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "Long answer", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Multiple choice - choose many", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Image choice - choose many", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "New submission", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "No survey responses yet", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Open for responses", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Open for responses", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Optional", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "If no logic is added, the survey will follow its normal flow. If both the page and its questions have logic, the question logic will take precedence. Ensure this aligns with your intended survey flow. For more information, visit {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "Location", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Are you sure you want to leave?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Your current changes won't be saved.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Ranking", "app.containers.AdminPage.ProjectEdit.survey.rating": "Rating", "app.containers.AdminPage.ProjectEdit.survey.required2": "Required", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {responses} one {response} other {responses}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# responses} one {# response} other {# responses}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Multiple choice - choose one", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Sentiment linear scale", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Esri shapefile upload", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Survey successfully saved", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Survey", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Survey responses", "app.containers.AdminPage.ProjectEdit.survey.text2": "Short answer", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Total {count} responses", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "View survey", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "View", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Embed URL", "app.containers.AdminPage.ProjectEdit.surveyService": "Service", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "You can find more information on how to embed a survey {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "This tag cannot be deleted because it is being used to display projects on the following more custom page(s). \nYou will need to unlink the tag from the page, or delete the page before you can delete the tag.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "General settings for the project", "app.containers.AdminPage.ProjectEdit.titleLabel": "Title", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Choose a title that is short, engaging and clear. It will be shown in the dropdown overview and on the project cards on the home page.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Tags", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Select {topicsCopy} for this project. Users can use these to filter projects by.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Total budget", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Trending", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Unassigned", "app.containers.AdminPage.ProjectEdit.unlimited": "Unlimited", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Use template", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "View project", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Volunteering", "app.containers.AdminPage.ProjectEdit.voteTermError": "Vote terms must be specified for all locales", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# groups can view} one {# group can view} other {# groups can view}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Add an event", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Additional information", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Address 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Street address of the event location", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Address 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "E.g. Apt, Suite, Building", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Additional address information that could help identify the location such as a building name, floor number, etc.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "See the support article", "app.containers.AdminPage.ProjectEvents.customButtonLink": "External link", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Add a link to an external URL (E.g. Event service or ticketing website). Setting this will override the default attendance button behavior.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Custom button text", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Set the button text to a value other than \"Register\" when an external URL is set.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Start", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "End", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Delete", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Are you sure you want to delete this event? There is no way to undo this!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Event description", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Edit Event", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "To email registrants directly from the platform, admins must create a user group in the {userTabLink} tab. {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "Event dates", "app.containers.AdminPage.ProjectEvents.eventImage": "Event image", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Event image alternative text", "app.containers.AdminPage.ProjectEvents.eventLocation": "Event location", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Export registrants", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Attachments (max 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Attachments are shown below the event description.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Location", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Maximum registrants", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Create a new event", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Online event link", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "If your event is online, add a link to it here.", "app.containers.AdminPage.ProjectEvents.preview": "Preview", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Refine map location", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Refine location on map", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "You can refine where your event location marker is shown by clicking on the map below.", "app.containers.AdminPage.ProjectEvents.register": "Register", "app.containers.AdminPage.ProjectEvents.registerButton": "Register button", "app.containers.AdminPage.ProjectEvents.registrant": "registrant", "app.containers.AdminPage.ProjectEvents.registrants": "registrants", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Registration limit", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Save", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "We could not save your changes, please try again.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Success!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "Search for a location", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Link upcoming events to this projects and show them on the project's event page.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Title and dates", "app.containers.AdminPage.ProjectEvents.titleEvents": "Project events", "app.containers.AdminPage.ProjectEvents.titleLabel": "Event name", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Link the button to an external URL", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "By default, the in-platform event register button will be shown allowing users to register for an event. You can change this to link to an external URL instead.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Limit the number of event registrants", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Set a maximum number of event registrants. If the limit is reached, no further registrations will be accepted.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/users", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Users", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "Add files to your project", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Attach files from this list to your project, phases, and events.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Add files as context to Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Add files to your Sensemaking project to provide context and insights.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Coming soon", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Sync surveys, upload interviews, and let AI connect the dots across your data.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Upload any file", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Use AI to analyze files", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Process transcripts, etc.", "app.containers.AdminPage.ProjectFiles.addFiles": "Add files", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "AI-powered insights", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "Analyze uploaded files to surface key topics.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Allow advanced analytics of these files using AI processing.", "app.containers.AdminPage.ProjectFiles.askButton": "Ask", "app.containers.AdminPage.ProjectFiles.categoryLabel": "Category", "app.containers.AdminPage.ProjectFiles.chooseFiles": "Choose files", "app.containers.AdminPage.ProjectFiles.close": "Close", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Confirm and upload", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Are you sure you want to delete this file?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "Could not load markdown file.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "Could not load CSV preview.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "Maximum 50 rows are shown in CSV previews.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "CSV file is too large to preview.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "Delete file", "app.containers.AdminPage.ProjectFiles.description": "Description", "app.containers.AdminPage.ProjectFiles.done": "Done", "app.containers.AdminPage.ProjectFiles.downloadFile": "Download file", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Download full file", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Drag and drop any files here or", "app.containers.AdminPage.ProjectFiles.editFile": "Edit file", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Description", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "File name may not contain a dot.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "File Name", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "File name is required.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Download file", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Preview", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "This file will not be uploaded, as it exceeds the maximum limit of 50 MB.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "All files uploaded successfully", "app.containers.AdminPage.ProjectFiles.generatingPreview": "Generating preview...", "app.containers.AdminPage.ProjectFiles.info_sheet": "Information", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "E.g. WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Audio interviews, Town Hall recordings", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "E.g. PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "Reports, informational documents", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "E.g. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "Images", "app.containers.AdminPage.ProjectFiles.interview": "Interview", "app.containers.AdminPage.ProjectFiles.maxFilesError": "You can only upload a maximum of {maxFiles} files at a time.", "app.containers.AdminPage.ProjectFiles.meeting": "Meeting", "app.containers.AdminPage.ProjectFiles.noFilesFound": "No files found.", "app.containers.AdminPage.ProjectFiles.other": "Other", "app.containers.AdminPage.ProjectFiles.policy": "Policy", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Preview not yet supported for this file type.", "app.containers.AdminPage.ProjectFiles.report": "Report", "app.containers.AdminPage.ProjectFiles.retryUpload": "Retry upload", "app.containers.AdminPage.ProjectFiles.save": "Save", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "File updated successfully.", "app.containers.AdminPage.ProjectFiles.searchFiles": "Search files", "app.containers.AdminPage.ProjectFiles.selectFileType": "File type", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Strategic plan", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "You can only upload a maximum of {maxFiles} files at a time.", "app.containers.AdminPage.ProjectFiles.unknown": "Unknown", "app.containers.AdminPage.ProjectFiles.upload": "Upload", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# file} other {# files}} uploaded successfully, {numberOfErrors, plural, one {# error} other {# errors}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "View file", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Collapse all fields", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Field description", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Edit input form", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Enabled", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Include this field.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Expand all fields", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Input form", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required", "app.containers.AdminPage.ProjectIdeaForm.required": "Required", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Require this field to be filled in.", "app.containers.AdminPage.ProjectIdeaForm.save": "Save", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Saved!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "View form", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Automated emails", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "You can configure emails triggered on a phase level", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Dates", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "Take the survey", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Survey", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Are you sure you want to delete this phase?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Phase description", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "Edit Phase", "app.containers.AdminPage.ProjectTimeline.endDate": "End date", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "End Date", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Attachments (max 50MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Create a new phase", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "This phase doesn't have a predefined end date.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Some methods' results sharing (such as voting results) won't be triggered until an end date is selected.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "As soon as you add a phase after this one, it will add an end date to this phase.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "Not selecting an end date for this implies that:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Preview", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "Save changes", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "There was an error submitting the form, please try again.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Saved!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectTimeline.startDate": "Start date", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Start date", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "Survey title", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Phase name", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Upload attachments", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartition", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminology (homepage filter)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "How should tags in the front page filter be called? E.g. tags, categories, departments, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Tags can be configured {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "here", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Term for one tag (singular)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tag", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Term for multiple tags (plural)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "tags", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Add field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Add a new registration field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Add option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Answer format", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Provide an answer format", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Provide an answer option for all languages", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Save answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Answer option successfully saved", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Answer choices", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Fields", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Drag and drop the fields to determine the order in which they appear in the sign-up form.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "De<PERSON>ult field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Optional text shown under the field name on the signup form.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Answer choices for place of residence can be set in the {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Edit answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Description", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Field name", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Provide a field name for all languages", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Field settings", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Yes-no (checkbox)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Date", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Long answer", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Multiple choice (select multiple)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Numeric value", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Multiple choice (select one)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Short answer", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Geographic areas tab", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Hidden field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Make answering this field required?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Custom fields", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Add answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Cancel", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Delete", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Are you sure you want to delete this registration question answer option? All records that specific users answered with this option will be permanently deleted. This action cannot be undone.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Are you sure you want to delete this registration question? All answers that users have given to this question will be permanently deleted, and it will no longer be asked in projects or proposals. This action cannot be undone.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Required", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Save field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "<PERSON> successfully saved", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "Two columns", "app.containers.AdminPage.SettingsPage.addAreaButton": "Add a geographic area", "app.containers.AdminPage.SettingsPage.addTopicButton": "Add tag", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Animal - eg Elephant Cat", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "User - eg User 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "Select which admins will receive notifications to approve projects. Folder Managers are by default approvers for all projects within their folders.", "app.containers.AdminPage.SettingsPage.approvalSave": "Save", "app.containers.AdminPage.SettingsPage.approvalTitle": "Project approval settings", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Are you sure you want to delete this area?", "app.containers.AdminPage.SettingsPage.areaTerm": "Term for one area (singular)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "area", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.areasTerm": "Term for multiple areas (plural)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "areas", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Select at least one language.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Display avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Show profile pictures of participants and number of them to non-registered visitors", "app.containers.AdminPage.SettingsPage.bannerHeader": "Header text", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Header text for non-registered visitors", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Sub-header text for non-registered visitors", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Sub-header text", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Banner text", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Show preview for", "app.containers.AdminPage.SettingsPage.brandingDescription": "Add your logo and set the platform colors.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Platform branding", "app.containers.AdminPage.SettingsPage.cancel": "Cancel", "app.containers.AdminPage.SettingsPage.chooseLayout": "Layout", "app.containers.AdminPage.SettingsPage.color_primary": "Primary colour", "app.containers.AdminPage.SettingsPage.color_secondary": "Secondary colour", "app.containers.AdminPage.SettingsPage.color_text": "Text colour", "app.containers.AdminPage.SettingsPage.colorsTitle": "Colors", "app.containers.AdminPage.SettingsPage.confirmHeader": "Are you sure you want to delete this tag?", "app.containers.AdminPage.SettingsPage.contentModeration": "Content moderation", "app.containers.AdminPage.SettingsPage.ctaHeader": "Buttons", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Custom page header | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Custom", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Button text", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Button link", "app.containers.AdminPage.SettingsPage.defaultTopic": "Default tag", "app.containers.AdminPage.SettingsPage.delete": "Delete", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "This will delete the tag from all existing posts. This change will apply to all projects.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Add and delete topics that you would like to use on your platform to categorise posts. You can add the topics to specific projects in the {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Desktop", "app.containers.AdminPage.SettingsPage.editFormTitle": "Edit area", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Edit tag", "app.containers.AdminPage.SettingsPage.fieldDescription": "Area description", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "This description is only for internal collaboration and is not shown to users.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Area name", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Provide an area name for all languages", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "The name you choose for each area can be used as a registration field option and to filter projects on the homepage.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Save tag", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Tag name", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Provide a tag name for all languages", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "The name you choose for each tag will be visible to the platform users", "app.containers.AdminPage.SettingsPage.fixedRatio": "Fixed ratio", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "This banner type works best with images that shouldn’t be cropped, such as images with text, a logo or specific elements that are crucial to your citizens. This banner is replaced with a solid box in the primary colour when users are signed in. You can set this colour in the general settings. More info on the recommended image usage can be found on our {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Full width", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "This banner stretches over the full width for a great visual effect. The image will try to cover as much space as possible, causing it to not always be visible at all times. You can combine this banner with an overlay of any colour. More info on the recommended image usage can be found on our {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.header": "Homepage banner", "app.containers.AdminPage.SettingsPage.headerDescription": "Customise the homepage banner image and text.", "app.containers.AdminPage.SettingsPage.header_bg": "Banner image", "app.containers.AdminPage.SettingsPage.helmetDescription": "Admin settings page", "app.containers.AdminPage.SettingsPage.helmetTitle": "Admin settings page", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Add your own content to the customizable section at the bottom of the homepage.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Homepage header | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Image overlay color", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Image overlay opacity", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Detect inappropriate content", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Auto-detect inappropriate content posted on the platform.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "While this feature is enabled, input, proposals and comments posted by participants will be automatically reviewed. Posts flagged as potentially containing inappropriate content will not be blocked, but will be highlighted for review on the {linkToActivityPage} page.", "app.containers.AdminPage.SettingsPage.languages": "Languages", "app.containers.AdminPage.SettingsPage.languagesTooltip": "You can select multiple languages in which you want to offer your platform to users. You will need to create content for every selected language.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Activity", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Please upload a header image", "app.containers.AdminPage.SettingsPage.no_button": "No button", "app.containers.AdminPage.SettingsPage.organizationName": "City or organisation name", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Provide an organization name or city for all languages.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Enable overlay", "app.containers.AdminPage.SettingsPage.phone": "Phone", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Platform configuration", "app.containers.AdminPage.SettingsPage.population": "Population", "app.containers.AdminPage.SettingsPage.populationMinError": "Population must be a positive number.", "app.containers.AdminPage.SettingsPage.populationTooltip": "The total number of inhabitants on your territory. This is used to calculate the participation rate. Leave empty if not applicable.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Profanity blocker", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Block input, proposals and comments containing the most commonly reported offensive words", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "This text is shown on the homepage above the projects.", "app.containers.AdminPage.SettingsPage.projectsSettings": "project settings", "app.containers.AdminPage.SettingsPage.projects_header": "Projects header", "app.containers.AdminPage.SettingsPage.registrationFields": "Registration fields", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Provide a short description at the top of your registration form.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registration", "app.containers.AdminPage.SettingsPage.save": "Save", "app.containers.AdminPage.SettingsPage.saveArea": "Save area", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Success!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Select approvers", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "Select the areas that will be shown to users to follow after registration", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Select the topics that will be shown to users to follow after registration", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Couldn't save. Try changing the setting again.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Sign up\"", "app.containers.AdminPage.SettingsPage.signed_in": "Button for registered visitors", "app.containers.AdminPage.SettingsPage.signed_out": "Button for non-registered visitors", "app.containers.AdminPage.SettingsPage.signupFormText": "Registration helper text", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Add a short description at the top of the sign-up form.", "app.containers.AdminPage.SettingsPage.statuses": "Statuses", "app.containers.AdminPage.SettingsPage.step1": "Email and password step", "app.containers.AdminPage.SettingsPage.step1Tooltip": "This is shown on the top of the first page of the sign-up form (name, email, password).", "app.containers.AdminPage.SettingsPage.step2": "Registration questions step", "app.containers.AdminPage.SettingsPage.step2Tooltip": "This is shown on the top of the second page of the sign-up form (additional registration fields).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Define the geographic areas that you would like to use for your platform, such as neighbourhoods, boroughs or districts. You can associate these geographic areas with projects (filterable on the landing page) or ask participants to select their area of residence as a registration field to create Smart Groups and define access rights.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Choose how people will see your organisation name, select the languages of your platform and link to your website.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "The provided subtitle exceeds the maximum allowed character limit (90 chars)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminology", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Settings updated successfully.", "app.containers.AdminPage.SettingsPage.tabAreas1": "Areas", "app.containers.AdminPage.SettingsPage.tabBranding": "Branding", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Input statuses", "app.containers.AdminPage.SettingsPage.tabPolicies": "Policies", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Project approval", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Proposal statuses", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registration", "app.containers.AdminPage.SettingsPage.tabSettings": "General", "app.containers.AdminPage.SettingsPage.tabTopics2": "Tags", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Define what geographic unit you would like to use for your projects (e.g., neighbourhoods, districts, boroughs, etc.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geographic areas", "app.containers.AdminPage.SettingsPage.titleBasic": "General settings", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "The provided title exceeds the maximum allowed character limit (35 chars)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Tag manager", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "This banner is in particular useful with images that don’t work well with text from the title, subtitle or button. These items will be pushed below the banner. More info on the recommended image usage can be found on our {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.twoRowLayout": "Two rows", "app.containers.AdminPage.SettingsPage.urlError": "The URL is not valid", "app.containers.AdminPage.SettingsPage.urlPatternError": "Enter a valid URL.", "app.containers.AdminPage.SettingsPage.urlTitle": "Website", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "You can add a link to your own website. This link will be used on the bottom of the homepage.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "Choose how users without a name in their profile will appear in the platform. This will occur when you set the access rights for a phase to ‘Email confirmation’. In all cases, upon participation, users will be able to update the profile name we autogenerated for them.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "User name display (for users with email confirmed only)", "app.containers.AdminPage.SideBar.administrator": "Administrator", "app.containers.AdminPage.SideBar.communityPlatform": "Community platform", "app.containers.AdminPage.SideBar.community_monitor": "Community monitor", "app.containers.AdminPage.SideBar.customerPortal": "Customer portal", "app.containers.AdminPage.SideBar.dashboard": "Dashboard", "app.containers.AdminPage.SideBar.emails": "Emails", "app.containers.AdminPage.SideBar.folderManager": "Folder manager", "app.containers.AdminPage.SideBar.groups": "Groups", "app.containers.AdminPage.SideBar.guide": "Guide", "app.containers.AdminPage.SideBar.inputManager": "Input manager", "app.containers.AdminPage.SideBar.insights": "Reporting", "app.containers.AdminPage.SideBar.inspirationHub": "Inspiration hub", "app.containers.AdminPage.SideBar.knowledgeBase": "Knowledge base", "app.containers.AdminPage.SideBar.language": "Language", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Pages & menu", "app.containers.AdminPage.SideBar.messaging": "Messaging", "app.containers.AdminPage.SideBar.moderation": "Activity", "app.containers.AdminPage.SideBar.notifications": "Notifications", "app.containers.AdminPage.SideBar.processing": "Processing", "app.containers.AdminPage.SideBar.projectManager": "Project manager", "app.containers.AdminPage.SideBar.projects": "Projects", "app.containers.AdminPage.SideBar.settings": "Settings", "app.containers.AdminPage.SideBar.signOut": "Sign out", "app.containers.AdminPage.SideBar.support": "Support", "app.containers.AdminPage.SideBar.toPlatform": "To platform", "app.containers.AdminPage.SideBar.tools": "Tools", "app.containers.AdminPage.SideBar.user.myProfile": "My profile", "app.containers.AdminPage.SideBar.users": "Users", "app.containers.AdminPage.SideBar.workshops": "Workshops", "app.containers.AdminPage.Topics.addTopics": "Add", "app.containers.AdminPage.Topics.browseTopics": "Browse tags", "app.containers.AdminPage.Topics.cancel": "Cancel", "app.containers.AdminPage.Topics.confirmHeader": "Are you sure you want to delete this project tag?", "app.containers.AdminPage.Topics.delete": "Delete", "app.containers.AdminPage.Topics.deleteTopicLabel": "Delete", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "This tag will no longer be able to be added to new posts in this project.", "app.containers.AdminPage.Topics.inputForm": "Input form", "app.containers.AdminPage.Topics.lastTopicWarning": "At least one tag is required. If you do not want to use tags, they can be disabled in the {ideaFormLink} tab.", "app.containers.AdminPage.Topics.projectTopicsDescription": "You can add and delete the tags that can be assigned to posts in this project.", "app.containers.AdminPage.Topics.remove": "Remove", "app.containers.AdminPage.Topics.title": "Project tags", "app.containers.AdminPage.Topics.topicManager": "Tag manager", "app.containers.AdminPage.Topics.topicManagerInfo": "If you would like to add additional project tags, you can do so in the {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Add a new group", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Group name", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Provide a group name", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Create a manual group", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "What type of group do you need?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Save group", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Create a manual group", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Create a smart group", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Learn more about groups", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "You can select users from the overview and add them to this group.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "You can define conditions and users who meet the conditions are automatically added to this group.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Manual group", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Smart group", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "There is no one in this group yet", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Go to {allUsersLink} to manually add some users.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "No user(s) match your search", "app.containers.AdminPage.Users.GroupsPanel.select": "Select", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Export all", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Export users in group", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Export selected", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Are you sure you want to delete this group?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "An error occurred while adding users to the groups, please try again.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Remove from group", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Delete selected users from this group?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "An error occurred while deleting users from the group, please try again.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Add users to group", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Add", "app.containers.AdminPage.groups.permissions.add": "Add", "app.containers.AdminPage.groups.permissions.addAnswer": "Add answer", "app.containers.AdminPage.groups.permissions.addQuestion": "Add demographic questions", "app.containers.AdminPage.groups.permissions.answerChoices": "Answer choices", "app.containers.AdminPage.groups.permissions.answerFormat": "Answer format", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "At least one choice must be provided", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "This user moderates the folder containing this project. To remove their moderator rights for this project, you can either revoke their folder rights or move the project to a different folder.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Create a new question", "app.containers.AdminPage.groups.permissions.createAQuestion": "Create a question", "app.containers.AdminPage.groups.permissions.defaultField": "De<PERSON>ult field", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Delete", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Delete", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Please provide a title for all choices", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "Yes-no (checkbox)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Date", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Long answer", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Multiple choice (select multiple)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Numeric value", "app.containers.AdminPage.groups.permissions.fieldType_select": "Multiple choice (select one)", "app.containers.AdminPage.groups.permissions.fieldType_text": "Short answer", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Changing granular permissions is not part of your license. Please contact your GovSuccess Manager to learn more about it.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Are you sure you want to remove this group from the project?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Select one or more groups", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {No members} one {1 member} other {{count} members}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Please fill in the title in all languages", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Are you sure?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Project managers not found", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Nothing is shown, because there are no actions the user can take in this project.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Only admins can create a new question.", "app.containers.AdminPage.groups.permissions.option1": "Option 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Pending invitation", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Who can annotate the document?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Who can sign up to attend an event?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Who can comment on inputs?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Who can comment on proposals?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Who can post a proposal?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Who can react to inputs?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Who can submit inputs?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Who can take the poll?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Who can take the survey?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Who can volunteer?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Who can vote on proposals?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Who can vote?", "app.containers.AdminPage.groups.permissions.questionDescription": "Question description", "app.containers.AdminPage.groups.permissions.questionTitle": "Question title", "app.containers.AdminPage.groups.permissions.save": "Save", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Success!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.groups.permissions.select": "Select", "app.containers.AdminPage.groups.permissions.selectValueError": "Please select an answer type", "app.containers.AdminPage.new.createAProject": "Create a project", "app.containers.AdminPage.new.fromScratch": "From scratch", "app.containers.AdminPage.phase.methodPicker.addOn1": "Add on", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "AI-powered insights", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Help participants surface agreement and disagreement, one idea at a time.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Find common ground", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Embed an interactive PDF and collect comments and feedback with Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Collect feedback on a document", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Embed a third-party survey", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "External survey", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Tap into your users' collective intelligence. Invite them to submit, discuss ideas, and/or provide feedback in a public forum.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Collect input and feedback in public", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Share information", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Lacks in-platform AI-powered insights", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Lacks in-platform reporting and data visualisation and processing", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Link with in-platform report builder", "app.containers.AdminPage.phase.methodPicker.logic1": "Logic", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Wide range of question types", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Allow participants to upload ideas with a time and vote limit.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "Proposals, petitions or initiatives", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "Quick poll", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Set up a short, multiple-choice questionnaire.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "Provide information to users, visualise results from other phases and create data rich reports.", "app.containers.AdminPage.phase.methodPicker.survey1": "Survey", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Understand your users' needs and thinking via a wide range of private question types.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Survey options", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Create a survey", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Ask users to volunteer for activities and causes or find participants for a panel.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Recruit participants or volunteers", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Select a voting method, and have users prioritise between a few different options.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Conduct a voting or prioritization exercise", "app.containers.AdminPage.projects.all.all": "All", "app.containers.AdminPage.projects.all.createProjectFolder": "New folder", "app.containers.AdminPage.projects.all.existingProjects": "Existing projects", "app.containers.AdminPage.projects.all.homepageWarning1": "Use this page to set the order of projects in the \"All projects\" dropdown in the navigation bar. If you are using the \"Published projects and folders\" or \"Projects and folders (legacy)\" widgets on your homepage, the order of projects in these widget will also be determined by the order you set here.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "Projects where you are a Project Manager will appear here.", "app.containers.AdminPage.projects.all.noProjects": "No projects found.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Only admins can create project folders.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projects and folders", "app.containers.AdminPage.projects.all.publishedTab": "Published", "app.containers.AdminPage.projects.all.searchProjects": "Search projects", "app.containers.AdminPage.projects.all.yourProjects": "Your projects", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "AI Analysis", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Accuracy: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "Ask", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "Instead of summarising, you can ask relevant questions to your data. This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Ask a question", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "This insight includes the following questions:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "Delete question", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "Are you sure you want to delete this question?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Delete summary", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Are you sure you want to delete these summaries?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Your text summaries will be displayed here, but you currently do not have any yet.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "Click the Auto-summarize button above to get started.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "inputs selected", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Asking questions about fewer inputs leads to a higher accuracy. Reduce the current input selection by using tags, search or demographic filters.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "Questions for", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Question for all inputs", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Rate the quality of this insight", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "Restore filters", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Summarize", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Summary for", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Summary for all inputs", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "Thank you for your feedback", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "The AI can’t process so many inputs in one go. Divide them into smaller groups.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "You can summarise a maximum of 30 inputs at a time on your current plan. Talk to your GovSuccess Manager or admin to unlock more.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "I understand", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Our platform enables you to explore the core themes, summarize the data, and examine various perspectives. If you are looking for specific answers or insights, consider using the \"Ask a Question\" feature to dive deeper beyond the summary.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "While rare, the AI might occasionally generate information that was not explicitly present in the original dataset.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Hallucinations:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "The AI might emphasize certain themes or ideas more than others, potentially skewing the overall interpretation.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Exaggeration:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Our system is optimized for handling 20-200 well-defined inputs for the most accurate results. As the volume of data increases beyond this range, the summary may become more high-level and generalized. This does not mean the AI becomes \"less accurate\", but rather that it will focus on broader trends and patterns. For more nuanced insights, we recommend using the (auto)-tagging feature to segment larger datasets into smaller, more manageable subsets.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Data Volume and Accuracy:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "We recommend using AI-generated summaries as a starting point for understanding large datasets, but not as the final word.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "How to work with AI", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Add selected inputs to tag", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "Add tag", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "All inputs", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "All inputs", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "All tags", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "No, I'll do it", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Do you want to automatically assign inputs to your tag?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "There are <b>different methods</b> to automatically assign inputs to tags.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Use <b>the auto-tag button</b> to launch your preferred method.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Click on a tag to assign it to the currently selected input.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "Yes, auto-tag", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Auto-tag", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Auto-tags are automatically derived by the computer. You can change or remove them at all times.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Auto-tag", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Inputs already associated with these tags will not be classified again.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "The classification is solely based on the name of the tag. Pick relevant keywords for the best results.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Tags: By label", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "You create the tags and manually assign a few inputs as an example, the computer assigns the rest", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Tags: By example", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "Similar to \"Tags: by label\" but with increased accuracy as you’re training the system with good examples.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "You create the tags, the computer assigns the inputs", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Tags: By label", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "This works well when you have a pre-defined set of tags or when your project has a limited scope in terms of tags.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Detect inputs with a significant dislikes/likes ratio", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Controversial", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Delete tag", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "Are you sure you want to delete this tag?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Don't show this again", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Edit tag", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "Add name", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Select maximum 9 tags you would like the inputs to be distributed between.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "The classification is based on the inputs currently assigned to the tags. The computer will try to follow your example.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Tags: By example", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "You do not have any custom tags yet.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "The computer automatically detects tags and assigns them to your inputs.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Tags: Fully automated", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "Works well when your projects covers a broad range of tags. Good place to start.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "How do you want to tag?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Inputs without tags", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Detect the language of each input", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "Language", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "Launch", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "No active filters", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Use tags to subdivide and filter the inputs, in order to make more accurate or targeted summaries.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "Other", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Tags: Platform tags", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Assign the existing platform tags that the author picked when posting", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Recommended", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "<PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "Cancel", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Name", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "Save", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "<PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "Select all", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Assign a positive or negative sentiment to each input, derived from the text", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Tag detection", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Use current filters", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "What inputs do you want to tag?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Autotagging task", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Controversial", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "Custom", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Ended at", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "Failed", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "By example", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "In progress", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "By label", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "Language", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "NLP tag", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "No recent AI tasks performed", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Platform tag", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "Queued", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Sentiment", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Started at", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Succeeded", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Summarization task", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Triggered at", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "Above", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "All", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "Author", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "Below", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "<PERSON><PERSON>ar", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Engagement", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "Filters", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "From", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "Gender", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "Input", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "Number of comments", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Number of reactions", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "Number of votes", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "To", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Add to analysis", "app.containers.AdminPage.projects.project.analysis.anonymous": "Anonymous input", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Authors by age", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Authors by domicile", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Background Jobs", "app.containers.AdminPage.projects.project.analysis.comments": "Comments", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "The domicile chart is too large to display", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "Hide empty answers", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Responses", "app.containers.AdminPage.projects.project.analysis.end": "End", "app.containers.AdminPage.projects.project.analysis.filter": "Only show inputs with this value", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Hide responses with no answer", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Auto-insights", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Column values", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "There are {count} instances of this combination.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "Dislikes", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Explore", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "False", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "Inputs", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON>s", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Next heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Next insight", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Not a statistically significant insight.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Auto insights are not available for projects with less than 30 participants.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "Participants", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Previous heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Previous insight", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "Row values", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Statistically significant insight.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Summarize", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Analysis tags", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "True", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "Units", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "View all insights", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "View auto-insights", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Inputs without tags", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "An invalid shapefile was uploaded and cannot be displayed.", "app.containers.AdminPage.projects.project.analysis.limit": "Limit", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "Main question", "app.containers.AdminPage.projects.project.analysis.manageInput": "Manage input", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Next graph", "app.containers.AdminPage.projects.project.analysis.noAnswer": "No answer", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "No answer provided.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "No shapefile uploaded.", "app.containers.AdminPage.projects.project.analysis.noInputs": "No inputs correspond to your current filters", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Previous graph", "app.containers.AdminPage.projects.project.analysis.reactions": "Reactions", "app.containers.AdminPage.projects.project.analysis.remove": "Remove", "app.containers.AdminPage.projects.project.analysis.removeFilter": "Remove filter", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "Remove filter", "app.containers.AdminPage.projects.project.analysis.search": "Search", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Shapefiles are displayed in GeoJSON format here. As such, styling in the original file may not display correctly.", "app.containers.AdminPage.projects.project.analysis.start": "Start", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Support Article", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "Unknown", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "View all questions", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "View selected questions", "app.containers.AdminPage.projects.project.analysis.votes": "Votes", "app.containers.AdminPage.widgets.copied": "Copied to clipboard", "app.containers.AdminPage.widgets.copyToClipboard": "Copy this code", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Copy the HTML code", "app.containers.AdminPage.widgets.fieldAccentColor": "Accent colour", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Widget background colour", "app.containers.AdminPage.widgets.fieldButtonText": "Button text", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Join now", "app.containers.AdminPage.widgets.fieldFont": "Font", "app.containers.AdminPage.widgets.fieldFontDescription": "This must be an existing font name from {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "Font size (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Header subtitle", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "You can have a say", "app.containers.AdminPage.widgets.fieldHeaderText": "Header title", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Our participation platform", "app.containers.AdminPage.widgets.fieldHeight": "Height (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Number of posts", "app.containers.AdminPage.widgets.fieldProjects": "Projects", "app.containers.AdminPage.widgets.fieldRelativeLink": "Links to", "app.containers.AdminPage.widgets.fieldShowFooter": "Show button", "app.containers.AdminPage.widgets.fieldShowHeader": "Show header", "app.containers.AdminPage.widgets.fieldShowLogo": "Show logo", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Site background colour", "app.containers.AdminPage.widgets.fieldSort": "Sorted by", "app.containers.AdminPage.widgets.fieldTextColor": "Text colour", "app.containers.AdminPage.widgets.fieldTopics": "Tags", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Homepage", "app.containers.AdminPage.widgets.htmlCodeExplanation": "You can copy this HTML code and paste it on that part of your website where you want to add your widget.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Widget HTML code", "app.containers.AdminPage.widgets.previewTitle": "Preview", "app.containers.AdminPage.widgets.settingsTitle": "Settings", "app.containers.AdminPage.widgets.sortNewest": "Newest", "app.containers.AdminPage.widgets.sortPopular": "Popular", "app.containers.AdminPage.widgets.sortTrending": "Trending", "app.containers.AdminPage.widgets.subtitleWidgets": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Dimensions", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Header & Footer", "app.containers.AdminPage.widgets.titleInputSelection": "Input selection", "app.containers.AdminPage.widgets.titleStyle": "Style", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Save", "app.containers.ContentBuilder.homepage.PageTitle": "Homepage", "app.containers.ContentBuilder.homepage.SaveError": "Something went wrong while saving the homepage.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "Two columns", "app.containers.ContentBuilder.homepage.bannerImage": "Banner image", "app.containers.ContentBuilder.homepage.bannerSubtext": "Banner subtext", "app.containers.ContentBuilder.homepage.bannerText": "Banner text", "app.containers.ContentBuilder.homepage.button": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.chooseLayout": "Layout", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Customizing settings other than the image and text on the homepage banner is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.ContentBuilder.homepage.customized_button": "Custom", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Button text", "app.containers.ContentBuilder.homepage.customized_button_url_label": "Button link", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Displays the next 3 upcoming events on your platform.", "app.containers.ContentBuilder.homepage.eventsDescription": "Displays the next 3 upcoming events on your platform.", "app.containers.ContentBuilder.homepage.fixedRatio": "Fixed-ratio banner", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "This banner type works best with images that shouldn’t be cropped, such as images with text, a logo or specific elements that are crucial to your citizens. This banner is replaced with a solid box in the primary colour when users are signed in. You can set this colour in the general settings. More info on the recommended image usage can be found on our {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "knowledge base", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "Full-width banner", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "This banner stretches over the full width for a great visual effect. The image will try to cover as much space as possible, causing it to not always be visible at all times. You can combine this banner with an overlay of any colour. More info on the recommended image usage can be found on our {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "knowledge base", "app.containers.ContentBuilder.homepage.imageOverlayColor": "Image overlay color", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Image overlay opacity", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "Invalid URL", "app.containers.ContentBuilder.homepage.no_button": "No button", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Non-registered users", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Enable overlay", "app.containers.ContentBuilder.homepage.projectsDescription": "To configure the order in which your projects are displayed, reorder them on the {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "Projects page", "app.containers.ContentBuilder.homepage.registeredUsersView": "Registered users", "app.containers.ContentBuilder.homepage.showAvatars": "Display avatars", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Show profile pictures of participants and number of them to non-registered visitors", "app.containers.ContentBuilder.homepage.sign_up_button": "Sign up", "app.containers.ContentBuilder.homepage.signedInDescription": "This is how registered users see the banner.", "app.containers.ContentBuilder.homepage.signedOutDescription": "This is how visitors that are not registered on the platform see the banner.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "This banner is in particular useful with images that don't work well with text from the title, subtitle or button. These items will be pushed below the banner. More info on the recommended image usage can be found on our {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "knowledge base", "app.containers.ContentBuilder.homepage.twoRowLayout": "Two rows", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "Embed height (pixels)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "Height you want your embedded content to appear on the page (in pixels).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Short description of the content you are embedding", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "It is useful to provide this information for users who rely on a screen reader or other assistive technology.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Website address", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "Full URL of the website you want to embed.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Display content from an external website on your page in an HTML iFrame. Note that not every page can be embedded. If you are having trouble embedding a page, check with the owner of the page if it is configured to allow embedding.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Visit our support page", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "Sorry, this content could not be embedded. {visitLinkMessage} to learn more.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Enter a valid web address, for example https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Embed", "app.containers.admin.ContentBuilder.accordionMultiloc": "Accordion", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Open by default", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Text", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "This is expandable accordion content. You can edit and format it by using the editor in the panel on the right.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Title", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Accordion title", "app.containers.admin.ContentBuilder.buttonMultiloc": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.delete": "Delete", "app.containers.admin.ContentBuilder.error": "error", "app.containers.admin.ContentBuilder.errorMessage": "There is an error on {locale} content, please fix the issue to be able to save your changes", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Hide participation avatars", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "This is a quarterly, ongoing survey that tracks how you feel about governance & public services.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Take the survey", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Help us serve you better", "app.containers.admin.ContentBuilder.homepage.default": "default", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "Events", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "Events", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Call to action", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Description", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "Primary button URL", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Primary button text", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "Secondary button URL", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Secondary button text", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Title", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Homepage banner", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Homepage banner", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Image & text cards", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 column", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projects", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Enable proposals in the “Proposals” section in the admin panel to unlock them in the homepage", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Proposals", "app.containers.admin.ContentBuilder.imageMultiloc": "Image", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Short description of the image", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "Adding \"alt text\" for images is important to make your platform accessible for users using screen readers.", "app.containers.admin.ContentBuilder.participationBox": "Participation Box", "app.containers.admin.ContentBuilder.textMultiloc": "Text", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 column", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 column", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 columns with 30% and 60% width respectively", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 columns with 60% and 30% width respectively", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 even columns", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "Most reacted inputs", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Ideation phase", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "There are no inputs available for this project or phase.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "Number of inputs", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Show more", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Title", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Total inputs: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Collapse long text", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "There are no inputs available for this project or phase.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Select phase", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Author", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "Content", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "Reactions", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "Votes", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "Input", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Title", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Registration rate", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Registrations", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Participants timeline", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Please note that participation numbers may not be fully accurate as some data is captured in an external survey that we do not track.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Chart", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Date range", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Title", "app.containers.admin.ReportBuilder.charts.noData": "There is no data available for the filters you have selected.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Traffic sources", "app.containers.admin.ReportBuilder.charts.users": "Users", "app.containers.admin.ReportBuilder.charts.usersByAge": "Users by age", "app.containers.admin.ReportBuilder.charts.usersByGender": "Users by gender", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Visitor timeline", "app.containers.admin.ReportBuilder.managerLabel1": "Project manager", "app.containers.admin.ReportBuilder.periodLabel1": "Period", "app.containers.admin.ReportBuilder.projectLabel1": "Project", "app.containers.admin.ReportBuilder.quarterReport1": "Community Monitor Report: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Start", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Buy 1 additional seat", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Confirm", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Are you sure you want to give 1 person manager rights?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Give manager rights", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "You have reached the limit of included seats within your plan, 1 additional seat will be added.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Add status", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Default statuses cannot be deleted.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Delete", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Edit", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Edit status", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Statuses currently assigned to participant input cannot be deleted. You can remove/change the status from existing input in the {manageTab} tab.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "This status cannot be deleted or moved.", "app.containers.admin.ideaStatuses.all.manage": "Edit", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Configuring custom input statuses is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Manage the status that can be assigned to participant input within a project. The status is publicly visible and helps in keeping participants informed.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Manage the status that can be assigned to proposals within a project. The status is publicly visible and helps in keeping participants informed.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Edit input statuses", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Edit proposal statuses", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Selected for implementation or next steps", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Approved", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Official feedback provided", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Answered", "app.containers.admin.ideaStatuses.form.category": "Category", "app.containers.admin.ideaStatuses.form.categoryDescription": "Please select the category that best represents your status. This selection will help our analytics tool to more accurately process and analyze posts.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Not matching any of the other options", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "Other", "app.containers.admin.ideaStatuses.form.fieldColor": "Colour", "app.containers.admin.ideaStatuses.form.fieldDescription": "Status Description", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Provide a status description for all lanugages", "app.containers.admin.ideaStatuses.form.fieldTitle": "Status Name", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Provide a status name for all lanugages", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Successfully implemented", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Implemented", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "Proposal is ineligible", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "Ineligible", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Ineligible or not selected to move forward", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Not Selected", "app.containers.admin.ideaStatuses.form.saveStatus": "Save status", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Considered for implementation or next steps", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Under Consideration", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Viewed but not yet processed", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Viewed", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Manage input and their statuses.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Input manager | Participation platform of {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Give feedback, add tags and move input from one project to another", "app.containers.admin.ideas.all.inputManagerPageTitle": "Input manager", "app.containers.admin.ideas.all.tabOverview": "Overview", "app.containers.admin.import.importInputs": "Import inputs", "app.containers.admin.import.importNoLongerAvailable3": "This feature is no longer available here. To import inputs to an ideation phase, go to the phase and select \"Import\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 additional admin seat} other {# additional admin seats}} and {managerSeats, plural, one {1 additional manager seat} other {# additional manager seats}} will be added over the limit.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 additional admin seat will be added over the limit} other {# additional admin seats will be added over the limit}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 additional manager seat will be added over the limit} other {# additional manager seats will be added over the limit}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Confirm and send out invitations", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Confirm impact on seat usage", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "You have reached the limit of available seats within your plan.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Administrators and the managers of this project", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Admins and collaborators only", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Only platform admins, folder managers and project managers can take the action", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Anyone", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "Anyone including unregistered users can participate.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Selection", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Users in specific user group(s) can participate. You can manage user groups in “Users” tab.", "app.containers.admin.project.permissions.viewingRightsTitle": "Who can see this project?", "app.containers.phaseConfig.enableSimilarInputDetection": "Enable similar input detection", "app.containers.phaseConfig.similarInputDetectionTitle": "Similar input detection", "app.containers.phaseConfig.similarInputDetectionTooltip": "Show participants similar input while they type to help avoid duplicates.", "app.containers.phaseConfig.similarityThresholdBody": "Similarity threshold (body)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "This controls how similar two descriptions must be to be flagged as similar. Use a value between 0 (strict) and 1 (lenient). Lower values return fewer but more accurate matches.", "app.containers.phaseConfig.similarityThresholdTitle": "Similarity threshold (title)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "This controls how similar two titles must be to be flagged as similar. Use a value between 0 (strict) and 1 (lenient). Lower values return fewer but more accurate matches.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "This feature is available as part of an early access offering until June 30th, 2025. If you'd like to continue using it beyond that date, please reach out to your Government Success Manager or admin to discuss activation options.", "app.containers.survey.sentiment.noAnswers2": "No responses at this time.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 comments} one {1 comment} other {# comments}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Participants are users or visitors that have participated in a project, posted or interacted with a proposal or attended events.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "Participants", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Participation rate", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Percentage of visitors that become participants.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Total participants", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Automated campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automated emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "From {quantity} campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Custom campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Custom emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "Emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Total emails sent", "app.modules.commercial.analytics.admin.components.Events.completed": "Completed", "app.modules.commercial.analytics.admin.components.Events.events": "Events", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Total events added", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Upcoming", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Accepted", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Invitations", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Pending", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Total invites sent", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Go to Input Manager", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Inputs", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Active", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Projects that are not archived and visible on the 'Active' table on the home page", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Archived", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Draft projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Finished", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "All archived projects and active timeline projects that have finished are counted here", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Total projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "The number of projects that are visible on the platform", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "New registrations", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Registration rate", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Percentage of visitors that become registered users.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registrations", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Total registrations", "app.modules.commercial.analytics.admin.components.Tab": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Last 30 days:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Last 7 days:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Pageviews per visit", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Visit duration", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Visitors\" is the number of unique visitors. If a person visits the platform multiple times, they are only counted once.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Visits", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Visits\" is the number of sessions. If a person visited the platform multiple times, each visit is counted.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Yesterday:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Count", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Language", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Number of visitors", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Percentage of visitors", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "click here", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Referrers", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "To view the full list of referrers, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Traffic sources", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Visits", "app.modules.commercial.analytics.admin.components.totalParticipants": "Total participants", "app.modules.commercial.analytics.admin.containers.visitors.noData": "There is no visitor data yet.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "We have changed the way we collect and display visitor data. As a result, visitor data is more accurate and more types of data are available, while still being GDPR compliant. While the data used for the visitors timeline goes back longer, we only started collecting the data for the \"Visit duration\", \"Pageviews per visit\" and the other graphs in November 2024, so before that no data is available. Therefore, if you select data before November 2024, be aware that some graphs might be empty or look odd.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Email deliveries over time", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Participants over time", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Registrations over time", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Date", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistic", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Overall statistics", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Visits and visitors over time", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Total over period", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Count", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Language", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Campaigns", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Direct entry", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "Number of visitors", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Percentage of visits", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Referrer websites", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Search engines", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Social networks", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO redirects", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Traffic source", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Number of visits", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Websites", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "You can remove this content flag by selecting this item and clicking the remove button at the top. It will then reappear in the Seen or Not seen tabs", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Inappropriate content auto-detected.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "There are no posts reported for review by the community or flagged for inappropriate content by our Natural Language Processing system", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Remove {numberOfItems, plural, one {content warning} other {# content warnings}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Reported as inappropriate by a platform user.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Content Warnings", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Report builder", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Pages shown on your navigation bar", "app.modules.navbar.admin.containers.addProject": "Add project to navbar", "app.modules.navbar.admin.containers.createCustomPageButton": "Create custom page", "app.modules.navbar.admin.containers.deletePageConfirmation": "Are you sure you want to delete this page? This cannot be undone. You can also remove the page from the navigation bar if you aren’t ready to delete it yet.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "You can only add up to 5 items to the navigation bar", "app.modules.navbar.admin.containers.pageHeader": "Pages & menu", "app.modules.navbar.admin.containers.pageSubtitle": "Your navigation bar can display up to five pages in addition to the Home and projects pages. You can rename menu items, re-order and add new pages  with your own content.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widgets", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Use the ☰ icon below to drag AI content into your report.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "There are no available AI insights. You can create them in your project.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Go to project", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "Question", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Select phase", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Unlock AI analysis", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Pull AI-generated insights into your report", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Report faster with AI", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Reporting with AI is not included in your current plan. Talk to your Government Success Manager to unlock this feature.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "This is not included in your current plan. Reach out to your Government Success Manager or admin to unlock it.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Group by registration field", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Group by survey question", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Group mode", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Group survey responses by registration fields (gender, location, age, etc) or other survey questions.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "None", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "Question", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Registration field", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Survey phase", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "Survey question", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Are you sure you want to delete this?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "Cancel", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "Delete", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "Edit", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Post your comment", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "Save", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "Write your comment here", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Click on the buttons below to follow or unfollow. The number of projects is shown in brackets.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "In your area", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "Done", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Follow preferences", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "There are currently no active projects given your follow preferences.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "This widget shows projects associated with the \"areas\" the user follows. Note that your platform might use a different name for \"areas\"- see the \"Areas\" tab in the platform settings. If the user does not follow any areas yet, the widget will show the available areas to follow. In this case the widget will show a maximum of 100 areas.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "No published projects or folders available", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Published projects and folders", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "This widget will case the projects and folders that are currently published, respecting the ordering defined on the projects page. This behavior is the same as the \"active\" tab of the \"legacy\" projects widget.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "No projects or folders selected", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Select projects or folders", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Selected projects and folders", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "With this widget, you can select and determine the order in which you want projects or folders to show to users.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projects", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "visit our support center", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "For more information on recommended image resolutions, {supportPageLink}."}
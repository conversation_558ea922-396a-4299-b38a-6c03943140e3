{"EmailSettingsPage.emailSettings": "E-mail instellingen", "EmailSettingsPage.initialUnsubscribeError": "Er was een probleem bij het u<PERSON><PERSON><PERSON><PERSON><PERSON> van de<PERSON> camp<PERSON>, probeer het later alsjeblieft opnieuw.", "EmailSettingsPage.initialUnsubscribeLoading": "Je verzoek wordt verwerkt, even geduld...", "EmailSettingsPage.initialUnsubscribeSuccess": "Je hebt je succesvol afgemeld voor {campaignTitle}.", "UI.FormComponents.optional": "optioneel", "app.closeIconButton.a11y_buttonActionMessage": "Sluiten", "app.components.Areas.areaUpdateError": "Er is een fout opgetreden tijdens het opslaan van je gebied. Probeer het opnieuw.", "app.components.Areas.followedArea": "Gevolgd gebied: {areaTitle}", "app.components.Areas.followedTopic": "Gevolgde tag: {topicTitle}", "app.components.Areas.topicUpdateError": "Er is een fout opgetreden tijdens het opslaan van je thema. Probeer het opnieuw.", "app.components.Areas.unfollowedArea": "Ontgevolgd gebied: {areaTitle}", "app.components.Areas.unfollowedTopic": "Ontvolgde tag: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Prijs:", "app.components.AssignBudgetControl.add": "Toevoegen", "app.components.AssignBudgetControl.added": "Toegevoegd", "app.components.AssignMultipleVotesControl.addVote": "Voeg stem toe", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Je hebt al je credits verdeeld.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Je hebt het maximale aantal credits voor deze optie verdeeld.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Je hebt al je punten verdeeld.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Je hebt het maximale aantal punten voor deze optie verdeeld.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Je hebt al je tokens verdeeld.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Je hebt het maximale aantal tokens voor deze optie verdeeld.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Je hebt al je stemmen verdeeld.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Je hebt het maximale aantal stemmen voor deze optie verdeeld.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Stemmen is niet mogel<PERSON>jk, omdat deze fase niet actief is.", "app.components.AssignMultipleVotesControl.removeVote": "<PERSON>er<PERSON><PERSON><PERSON> stem", "app.components.AssignMultipleVotesControl.select": "Selecteer", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Je hebt je stem al ingediend. Om het te wijzigen, klik je op \"Wijzig je inzending\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Je hebt je stem al ingediend. Om het te wijzigen, ga je terug naar de projectpagina en klik je op \"Wijzig je inzending\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {credit} other {credits}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {punt} other {punten}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {stemmen} other {stemmen}}", "app.components.AssignVoteControl.maxVotesReached1": "Je hebt al je stemmen verdeeld.", "app.components.AssignVoteControl.phaseNotActive": "Stemmen is niet mogel<PERSON>jk, omdat deze fase niet actief is.", "app.components.AssignVoteControl.select": "Selecteer", "app.components.AssignVoteControl.selected2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.voteForAtLeastOne": "Stem op minstens 1 optie", "app.components.AssignVoteControl.votesSubmitted1": "Je hebt je stem al ingediend. Om het te wijzigen, klik je op \"Wijzig je inzending\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Je hebt je stem al ingediend. Om het te wijzigen, ga je terug naar de projectpagina en klik je op \"Wijzig je inzending\".", "app.components.AuthProviders.continue": "Ga verder", "app.components.AuthProviders.continueWithAzure": "<PERSON>gaan met {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "<PERSON><PERSON><PERSON> met Facebook", "app.components.AuthProviders.continueWithFakeSSO": "<PERSON><PERSON><PERSON> met <PERSON><PERSON>", "app.components.AuthProviders.continueWithGoogle": "<PERSON><PERSON><PERSON> met Google", "app.components.AuthProviders.continueWithHoplr": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON>", "app.components.AuthProviders.continueWithIdAustria": "Doorgaan met ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Ga verder met {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "<PERSON><PERSON><PERSON> met MitID", "app.components.AuthProviders.franceConnectMergingFailed": "<PERSON>r bestaat al een account met dit e-mailadres.{br}{br}Je hebt geen toegang tot het platform via FranceConnect omdat de persoonlijke gegevens niet overeenkomen. Om je aan te melden met FranceConnect, moet je eerst je voornaam of achternaam op dit platform wijzigen zodat deze overeenkomt met je officiële gegeven<PERSON>.{br}{br}Je kunt je hieronder aanmelden zoals je normaal doet.", "app.components.AuthProviders.goToLogIn": "Heb je al een account? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Heb je geen account? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Inloggen", "app.components.AuthProviders.logInWithEmail": "Log in met e-mail", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Je moet de opgegeven minimumleeftijd of hoger hebben om geverifieerd te worden.", "app.components.AuthProviders.signUp2": "Registreren", "app.components.AuthProviders.signUpButtonAltText": "<PERSON><PERSON><PERSON> met {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Registreren met e-mail", "app.components.AuthProviders.verificationRequired": "Verificatie vereist", "app.components.Author.a11yPostedBy": "Geplaatst door", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 deelnemer} other {{numberOfParticipants} deelnemers}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} gebruikers", "app.components.AvatarBubbles.participant": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AvatarBubbles.participants1": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.cancel": "<PERSON><PERSON><PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "Reageren is in de huidige fase niet mogelijk.", "app.components.Comments.commentingDisabledInactiveProject": "Reageren is niet mogelijk omdat dit project momenteel niet actief is.", "app.components.Comments.commentingDisabledProject": "Reageren in dit project is momenteel uitgeschakeld.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} om te reageren.", "app.components.Comments.commentingMaybeNotPermitted": "<PERSON><PERSON> i<PERSON>reen kan reageren. {signInLink} om te zien of je aan de voorwa<PERSON>en voldoet.", "app.components.Comments.inputsAssociatedWithProfile": "Je inzendingen worden standaard aan je profiel gek<PERSON>d, tenzij je deze optie selecteert.", "app.components.Comments.invisibleTitleComments": "Reacties", "app.components.Comments.leastRecent": "Minst recent", "app.components.Comments.likeComment": "'Like' deze reactie", "app.components.Comments.mostLiked": "De meeste reacties", "app.components.Comments.mostRecent": "Meest recente", "app.components.Comments.official": "Officieel", "app.components.Comments.postAnonymously": "<PERSON><PERSON><PERSON>", "app.components.Comments.replyToComment": "Antwoord op de reactie", "app.components.Comments.reportAsSpam": "Rapporteer als spam", "app.components.Comments.seeOriginal": "Toon origineel", "app.components.Comments.seeTranslation": "<PERSON>n de vertaling", "app.components.Comments.yourComment": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "app.components.CommonGroundResults.divisiveDescription": "<PERSON><PERSON><PERSON> waar mensen het in gelijke mate mee eens en oneens zijn:", "app.components.CommonGroundResults.divisiveTitle": "<PERSON><PERSON>d", "app.components.CommonGroundResults.majorityDescription": "Meer dan 60% stemde voor of tegen het volgende:", "app.components.CommonGroundResults.majorityTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.participantLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.participantsLabel1": "deelnemers aan dit project", "app.components.CommonGroundResults.statementLabel": "stelling", "app.components.CommonGroundResults.statementsLabel1": "stellingen", "app.components.CommonGroundResults.votesLabe": "stem", "app.components.CommonGroundResults.votesLabel1": "stemmen", "app.components.CommonGroundStatements.agreeLabel": "<PERSON>e eens", "app.components.CommonGroundStatements.disagreeLabel": "<PERSON><PERSON>", "app.components.CommonGroundStatements.noMoreStatements": "<PERSON>r zijn op dit moment geen stellingen om op te reageren", "app.components.CommonGroundStatements.noResults": "Er zijn nog geen resultaten. Zorg ervoor dat je hebt deelgenomen aan de Raakvlakkenmethode-fase en kom daarna hier terug.", "app.components.CommonGroundStatements.unsureLabel": "Onzeker", "app.components.CommonGroundTabs.resultsTabLabel": "Resultaten", "app.components.CommonGroundTabs.statementsTabLabel": "Stellingen", "app.components.CommunityMonitorModal.formError": "Er is een fout opgetreden.", "app.components.CommunityMonitorModal.surveyDescription2": "Deze doorlopende vragenlijst houdt bij hoe je denkt over bestuur en openbare diensten.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {<PERSON><PERSON> <1 minuut} one {Duurt 1 minuut} other {Duurt # minuten}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Een e-mail met bevestigingscode is verzonden naar {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "<PERSON><PERSON> je e-mail.", "app.components.ConfirmationModal.codeInput": "Code", "app.components.ConfirmationModal.confirmationCodeSent": "Een nieuwe code is verzonden", "app.components.ConfirmationModal.didntGetAnEmail": "Heb je geen e-mail ontvangen?", "app.components.ConfirmationModal.foundYourCode": "Heb je je code gevonden?", "app.components.ConfirmationModal.goBack": "Ga terug.", "app.components.ConfirmationModal.sendEmailWithCode": "Stuur e-mail met code", "app.components.ConfirmationModal.sendNewCode": "Verstuur een nieuwe code.", "app.components.ConfirmationModal.verifyAndContinue": "Verifiëren en door<PERSON>an", "app.components.ConfirmationModal.wrongEmail": "Verkeerde e-mailadres?", "app.components.ConsentManager.Banner.accept": "Aanvaarden", "app.components.ConsentManager.Banner.ariaButtonClose2": "Beleid afwijzen en banner sluiten", "app.components.ConsentManager.Banner.close": "Sluiten", "app.components.ConsentManager.Banner.mainText": "Dit platform maakt gebruik van cookies in overeenstemming met ons {policyLink}.", "app.components.ConsentManager.Banner.manage": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.policyLink": "Cookiebeleid", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Advertenties", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "{tenant<PERSON><PERSON>, select, Noordwijk {We gebruiken dit om marketing campagnes op onze website te personaliseren en hun effectiviteit te meten. We tonen geen advertenties op het platform, maar de volgende diensten kunnen mogelijks gepersonaliseerde advertenties maken gebaseerd op de pagina's die je bezoekt op onze website. Dit platform stuurt de gegevens van Google Analytics door naar Siteimprove, de applicatie die door de gemeente Noordwijk wordt gebruikt.} other {We gebruiken dit om marketing campagnes op onze website te personaliseren en hun effectiviteit te meten. We tonen geen advertenties op het platform, maar de volgende diensten kunnen mogelijks gepersonaliseerde advertenties maken gebaseerd op de pagina's die je bezoekt op onze website.}}", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analytics", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "We gebruiken dit om beter te begrijpen hoe je ons platform gebruikt om je navigatie te verbeteren. Deze informatie wordt enkel in geaggregeerde vorm gebruikt, en nooit om individuele personen te volgen.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Ga terug", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Functioneel", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Deze zijn vereist om basisfunctionaliteiten van de website mogelijk te maken en te monitoren. Sommige van deze tools zijn mogelijks niet van toepassing op jou. Raadpleeg ons cookiebeleid voor meer informatie.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Vere<PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Om een functioneel platform te hebben, slaan we een authenticatie cookie op als je je a<PERSON>, en de taal waarin je dit platform gebruikt.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Opsla<PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.title": "<PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Tools", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Disclaimer voor het uploaden van content", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Door content te uploaden, verk<PERSON><PERSON> je dat deze content geen regelgeving of re<PERSON><PERSON> van der<PERSON> schendt, zoals intellectuele eigendomsrechten, privacyrechten, rechten op handelsgeheimen, enzovoort. Dientengevolge verplicht je je door het uploaden van deze content om de volledige en exclusieve aansprakelijkheid te dragen voor alle directe en indirecte schade die voortvloeit uit de geüploade content. Bovendien verplicht je je om de platformeigenaar en Go Vocal te vrijwaren van alle claims of aansprakelijkheden tegen derden, en alle bijbehorende kosten, die zouden voortvloeien uit of het gevolg zijn van de content die je hebt geüpload.", "app.components.ContentUploadDisclaimer.onAccept": "<PERSON>k begri<PERSON>p het", "app.components.ContentUploadDisclaimer.onCancel": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Vertel ons waarom", "app.components.CustomFieldsForm.addressInputAriaLabel": "<PERSON><PERSON> in<PERSON>", "app.components.CustomFieldsForm.addressInputPlaceholder6": "<PERSON><PERSON>r een adres in...", "app.components.CustomFieldsForm.adminFieldTooltip": "Veld alleen zichtbaar voor platformbeheerders", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Alle antwoorden op deze vragenlijst zijn geanonimiseerd.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Voor een gebied zijn minimaal drie punten nodig.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "Voor een lijn zijn minimaal twee punten nodig.", "app.components.CustomFieldsForm.attachmentRequired": "Minstens één bi<PERSON>lage is vereist", "app.components.CustomFieldsForm.authorFieldLabel": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.authorFieldPlaceholder": "<PERSON>gin met typen om te zoeken op email of naam...", "app.components.CustomFieldsForm.back": "Vorige", "app.components.CustomFieldsForm.budgetFieldLabel": "Budget", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "<PERSON>lik op de kaart om te tekenen. Sleep daarna punten om ze te verpla<PERSON>en.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "<PERSON><PERSON> op de kaart of typ hieronder een adres om je antwoord toe te voegen.", "app.components.CustomFieldsForm.confirm": "Bevestigen", "app.components.CustomFieldsForm.descriptionMinLength": "De beschrijving moet minstens {min} tekens lang zijn", "app.components.CustomFieldsForm.descriptionRequired": "De beschrijving is vereist", "app.components.CustomFieldsForm.fieldMaximumItems": "Maximaal {maxSelections, plural, one {# optie kan} other {# opties kunnen}} worden geselecteerd voor het veld \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "Ten minste {minSelections, plural, one {# optie kan} other {# opties kunnen}} worden geselecteerd voor het veld \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "Het veld \"{fieldName}\" is vereist", "app.components.CustomFieldsForm.fileSizeLimit": "De maximale bestandsgrootte is {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "De afbeelding is vereist", "app.components.CustomFieldsForm.minimumCoordinates2": "Een minimum van {numPoints} punten is vereist.", "app.components.CustomFieldsForm.notPublic1": "*Dit antwoord wordt alleen gedeeld met projectbeheerders en niet met het publiek.", "app.components.CustomFieldsForm.otherArea": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.progressBarLabel": "Voortgang", "app.components.CustomFieldsForm.removeAnswer": "Antwoord verwijderen", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Selecteer er zoveel als je wilt", "app.components.CustomFieldsForm.selectBetween": "*Selecteer tussen {minItems} en {maxItems} opties", "app.components.CustomFieldsForm.selectExactly2": "*Selecteer precies {selectExactly, plural, one {# optie} other {# opties}}", "app.components.CustomFieldsForm.selectMany": "*Kies er zo veel als je wilt", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Tik op de kaart om te tekenen. Sleep daarna punten om ze te verpla<PERSON>en.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Tik op de kaart om te tekenen.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Tik op de kaart om je antwoord toe te voegen.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Tik op de kaart of typ hieronder een adres om je antwoord toe te voegen.", "app.components.CustomFieldsForm.tapToAddALine": "Tik om een lijn toe te voegen", "app.components.CustomFieldsForm.tapToAddAPoint": "Tik om een punt toe te voegen", "app.components.CustomFieldsForm.tapToAddAnArea": "Tik om een gebied toe te voegen", "app.components.CustomFieldsForm.titleMaxLength": "De titel mag maximaal {max} tekens lang zijn", "app.components.CustomFieldsForm.titleMinLength": "De titel moet minimaal {min} tekens lang zijn", "app.components.CustomFieldsForm.titleRequired": "De titel is vereist", "app.components.CustomFieldsForm.topicRequired": "Minstens één tag is vereist", "app.components.CustomFieldsForm.typeYourAnswer": "<PERSON><PERSON> je antwoord", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Het is vereist om je antwoord te typen", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Upload een zip<PERSON>tand met een of meer shapefiles.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "Als de locatie niet wordt weergegeven bij de opties terwijl je typt, kun je geldige coördinaten toevoegen in het formaat 'breedtegraad, lengtegraad' om een precieze locatie op te geven (bijvoorbeeld: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Sommige velden zijn ongeldig. Corrigeer de fouten en probeer het opnieuw.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Er is een fout opgetreden bij het indienen van je rapport. Probeer het opnieuw.", "app.components.ErrorBoundary.errorFormLabelClose": "Sluiten", "app.components.ErrorBoundary.errorFormLabelComments": "Wat is er gebeurd?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-mail", "app.components.ErrorBoundary.errorFormLabelName": "<PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelSubmit": "Verzenden", "app.components.ErrorBoundary.errorFormSubtitle": "Ons team is op de hoogte g<PERSON>.", "app.components.ErrorBoundary.errorFormSubtitle2": "Het zou ons helpen als je hieronder kan beschrijven wat er gebeurd is.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Je feedback is verzonden. Bedankt!", "app.components.ErrorBoundary.errorFormTitle": "Het lijkt erop dat er een probleem is.", "app.components.ErrorBoundary.genericErrorWithForm": "Er is een fout opgetreden en we kunnen inhoud niet weergeven. Probeer het opnieuw of {openForm}!", "app.components.ErrorBoundary.openFormText": "help ons het op te lossen", "app.components.ErrorToast.budgetExceededError": "Je hebt niet genoeg budget", "app.components.ErrorToast.votesExceededError": "Je hebt niet genoeg stemmen over", "app.components.EventAttendanceButton.forwardToFriend": "Doorsturen naar een vriend", "app.components.EventAttendanceButton.maxRegistrationsReached": "Het maximum aantal geregistreerden voor de activiteit is bereikt. Er zijn geen plekken meer vrij.", "app.components.EventAttendanceButton.register": "Registreer", "app.components.EventAttendanceButton.registered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.EventAttendanceButton.seeYouThere": "<PERSON>ie je daar!", "app.components.EventAttendanceButton.seeYouThereName": "<PERSON><PERSON> je daar, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Er werd minder informatie z<PERSON> over de activiteiten", "app.components.EventCard.a11y_moreContentVisible": "Er werd meer informatie zich<PERSON> over de activiteiten", "app.components.EventCard.a11y_readMore": "<PERSON><PERSON> meer over het evenement \"{eventTitle}\".", "app.components.EventCard.endsAt": "Eindigt op", "app.components.EventCard.readMore": "<PERSON><PERSON> le<PERSON>", "app.components.EventCard.showLess": "Toon minder", "app.components.EventCard.showMore": "Toon meer", "app.components.EventCard.startsAt": "Begint op", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Komende en lopende evenementen in dit project", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Komende en lopende evenementen in deze fase", "app.components.FileUploader.a11y_file": "Bestand:", "app.components.FileUploader.a11y_filesToBeUploaded": "Bestanden die toegevoegd moeten worden: {fileNames}", "app.components.FileUploader.a11y_noFiles": "<PERSON><PERSON> bestanden toe<PERSON>.", "app.components.FileUploader.a11y_removeFile": "Dit bestand verwijderen", "app.components.FileUploader.fileInputDescription": "Klik om een bestand te kiezen", "app.components.FileUploader.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.components.FileUploader.file_too_large2": "<PERSON><PERSON><PERSON> groter dan {maxSizeMb}MB zijn niet toe<PERSON>.", "app.components.FileUploader.incorrect_extension": "{fileName} wordt niet ondersteund door ons systeem en wordt daarom niet geüpload.", "app.components.FilterBoxes.a11y_allFilterSelected": "Gekozen statusfilter: alle", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# bijdrage} other {# bijdragen}}", "app.components.FilterBoxes.a11y_removeFilter": "<PERSON><PERSON> ver<PERSON>", "app.components.FilterBoxes.a11y_selectedFilter": "Geselecteerde statusfilter: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "{numberOfSelectedTopics, plural,=0 {geen tagfilters} one {één tagfilter} other {# tagfilters}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Alle", "app.components.FilterBoxes.areas": "Filter op gebied", "app.components.FilterBoxes.inputs": "bijdragen", "app.components.FilterBoxes.noValuesFound": "<PERSON><PERSON> waarden be<PERSON>.", "app.components.FilterBoxes.showLess": "Toon minder", "app.components.FilterBoxes.showTagsWithNumber": "Toon alles ({numberTags})", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "Tags", "app.components.FiltersModal.filters": "Filters", "app.components.FolderFolderCard.a11y_folderDescription": "Mapbeschrijving:", "app.components.FolderFolderCard.a11y_folderTitle": "Maptitel:", "app.components.FolderFolderCard.archived": "Afgerond", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projecten} one {# project} other {# projecten}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "Het veldtype kan niet worden gewijzigd als er eenmaal inzendingen zijn.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Type", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Automatisch opslaan", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Automatisch opslaan staat standaard aan als je de formuliereditor opent. <PERSON><PERSON> keer dat je het veldinstellingenpaneel sluit met de \"X\" knop, wordt het automatisch opgeslagen.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "Kwart<PERSON>", "app.components.GanttChart.timeRange.timeRangeMultiyear": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.year": "Jaar", "app.components.GanttChart.today": "Vandaag", "app.components.GoBackButton.group.edit.goBack": "Ga terug", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Ga terug naar de vorige pagina", "app.components.HookForm.Feedback.errorTitle": "Er gaat iets mis.", "app.components.HookForm.Feedback.submissionError": "<PERSON>beer het nog eens. Neem contact met ons op als het probleem zich blijft voordoen", "app.components.HookForm.Feedback.submissionErrorTitle": "Er ging iets mis aan onze kant, excuses", "app.components.HookForm.Feedback.successMessage": "Formulier succesvol verstuurd", "app.components.HookForm.PasswordInput.passwordLabel": "Wachtwoord", "app.components.HorizontalScroll.scrollLeftLabel": "Scroll naar links.", "app.components.HorizontalScroll.scrollRightLabel": "<PERSON><PERSON> naar rechts.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} idee<PERSON>n zijn geladen.", "app.components.IdeaCards.filters": "Filters", "app.components.IdeaCards.filters.mostDiscussed": "Meest besproken", "app.components.IdeaCards.filters.newest": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.oldest": "Oud", "app.components.IdeaCards.filters.popular": "<PERSON><PERSON> geliket", "app.components.IdeaCards.filters.random": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.sortBy": "Sorteren op", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Sortering veranderd naar: {currentSortType}", "app.components.IdeaCards.filters.trending": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.showMore": "Toon meer", "app.components.IdeasMap.a11y_hideIdeaCard": "<PERSON><PERSON><PERSON> idee-kaart.", "app.components.IdeasMap.a11y_mapTitle": "Kaartoverzicht", "app.components.IdeasMap.clickOnMapToAdd": "<PERSON>lik op de kaart om je input toe te voegen", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Als beheerder kun je op de kaart klikken om je bijdrage toe te voegen, zelfs als deze fase niet actief is.", "app.components.IdeasMap.filters": "Filters", "app.components.IdeasMap.multipleInputsAtLocation": "Meerdere bijdragen op deze locatie", "app.components.IdeasMap.noFilteredResults": "De filters die je selecteerde hebben geen resultaten opgeleverd", "app.components.IdeasMap.noResults": "<PERSON>n resultaten gevonden", "app.components.IdeasMap.or": "of", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 { geen dislikes.} one {1 dislike.} other { # dislikes.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 { likes.} one { 1 like.} other { # likes.}}", "app.components.IdeasMap.signInLinkText": "inloggen", "app.components.IdeasMap.signUpLinkText": "registreren", "app.components.IdeasMap.submitIdea2": "Bijdrage indienen", "app.components.IdeasMap.tapOnMapToAdd": "Tik op de kaart om je input toe te voegen", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Als beheerder kun je op de kaart tikken om je bijdrage toe te voegen, zelfs als deze fase niet actief is.", "app.components.IdeasMap.userInputs2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 { geen reacties} one { 1 reactie} other { # reacties}}", "app.components.IdeasShow.bodyTitle": "Beschrijving", "app.components.IdeasShow.deletePost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.editPost": "Bewerk", "app.components.IdeasShow.goBack": "Ga terug", "app.components.IdeasShow.moreOptions": "Meer opties", "app.components.IdeasShow.or": "of", "app.components.IdeasShow.proposedBudgetTitle": "Budget", "app.components.IdeasShow.reportAsSpam": "Rapporteer als spam", "app.components.IdeasShow.send": "Verzenden", "app.components.IdeasShow.skipSharing": "<PERSON><PERSON> deze stap over, en deel het later", "app.components.IdeasShowPage.signIn2": "Inloggen", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON>, je hebt geen toegang tot deze pagina. Je moet mogelijk opnieuw inloggen of je aanmelden om toegang te krijgen.", "app.components.LocationInput.noOptions": "Geen opties", "app.components.Modal.closeWindow": "Venster sluiten", "app.components.MultiSelect.clearButtonAction": "<PERSON><PERSON> wissen", "app.components.MultiSelect.clearSearchButtonAction": "Zoekterm wissen", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Voer een nieuw e-mailadres in", "app.components.PageNotFound.goBackToHomePage": "Terug naar de startpagina", "app.components.PageNotFound.notFoundTitle": "<PERSON><PERSON><PERSON> niet gevonden", "app.components.PageNotFound.pageNotFoundDescription": "De verzochte pagina kon niet worden gevonden.", "app.components.PagesForm.descriptionMissingOneLanguageError": "<PERSON><PERSON> inhoud op voor ten minste één taal", "app.components.PagesForm.editContent": "<PERSON><PERSON><PERSON>", "app.components.PagesForm.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Bijlages worden onderaan de pagina weergegeven.", "app.components.PagesForm.navbarItemTitle": "Naam in navigatiebalk", "app.components.PagesForm.pageTitle": "Titel", "app.components.PagesForm.savePage": "<PERSON><PERSON><PERSON>", "app.components.PagesForm.saveSuccess": "<PERSON><PERSON><PERSON>.", "app.components.PagesForm.titleMissingOneLanguageError": "<PERSON><PERSON> een titel op voor ten minste één taal", "app.components.Pagination.back": "Vorige pagina", "app.components.Pagination.next": "Volgende pagina", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Je hebt {votesCast} uit<PERSON><PERSON><PERSON>, wat meer is dan de limiet van {votesLimit}. Verwijder enkele items uit je winkelmandje en probeer het opnieuw.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget}  over", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Je moet minimaal {votesMinimum}  uitgeven voordat je je mandje kunt versturen.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Je moet minstens één optie selecteren voordat je kunt verzenden.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Je moet iets aan je mandje toevoegen voordat je het kunt versturen.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {Geen credits over} other {# van {totalNumberOfVotes, plural, one {1 credit} other {# credits}} over}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {<PERSON><PERSON> punten over} other {# van {totalNumberOfVotes, plural, one {1 punt} other {# punten}} over}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Geen tokens over} other {# van {totalNumberOfVotes, plural, one {1 token} other {# tokens}} over}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Geen stemmen over} other {# van {totalNumberOfVotes, plural, one {1 stem} other {# stemmen}} over}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# stemmen} one {# stem} other {# stemmen}} uitgebracht", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Je hebt {votesCast} stemmen uit<PERSON><PERSON><PERSON>, wat de limiet van {votesLimit} overschrijdt. Verwijder enkele stemmen en probeer het opnieuw.", "app.components.ParticipationCTABars.addInput": "Bijdrage toe<PERSON>n", "app.components.ParticipationCTABars.allocateBudget": "Maak je keuze", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Je budget is succesvol ingediend.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Open voor participatie", "app.components.ParticipationCTABars.poll": "<PERSON><PERSON> de peiling", "app.components.ParticipationCTABars.reviewDocument": "Bekijk het document", "app.components.ParticipationCTABars.seeContributions": "Bekijk bijdragen", "app.components.ParticipationCTABars.seeEvents3": "Bekijk evenementen", "app.components.ParticipationCTABars.seeIdeas": "Bekijk ideeën", "app.components.ParticipationCTABars.seeInitiatives": "Bekijk initiatieven", "app.components.ParticipationCTABars.seeIssues": "Bekijk reacties", "app.components.ParticipationCTABars.seeOptions": "Bekijk opties", "app.components.ParticipationCTABars.seePetitions": "Bekijk petities", "app.components.ParticipationCTABars.seeProjects": "Bekijk projecten", "app.components.ParticipationCTABars.seeProposals": "Bekijk voorstellen", "app.components.ParticipationCTABars.seeQuestions": "Bekijk vragen", "app.components.ParticipationCTABars.submit": "Verzenden", "app.components.ParticipationCTABars.takeTheSurvey": "<PERSON><PERSON> <PERSON>", "app.components.ParticipationCTABars.userHasParticipated": "U heeft deelgenomen aan dit project.", "app.components.ParticipationCTABars.viewInputs": "Bekijk bijdragen", "app.components.ParticipationCTABars.volunteer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.votesCounter.vote": "stem", "app.components.ParticipationCTABars.votesCounter.votes": "stemmen", "app.components.PasswordInput.a11y_passwordHidden": "Wachtwoord verborgen", "app.components.PasswordInput.a11y_passwordVisible": "Wachtwoord zichtbaar", "app.components.PasswordInput.a11y_strength1Password": "Zeer zwak wachtwoord", "app.components.PasswordInput.a11y_strength2Password": "Zwak wachtwoord", "app.components.PasswordInput.a11y_strength3Password": "<PERSON><PERSON><PERSON><PERSON><PERSON> wachtwo<PERSON>", "app.components.PasswordInput.a11y_strength4Password": "Sterk wachtwoord", "app.components.PasswordInput.a11y_strength5Password": "Zeer sterk wachtwoord", "app.components.PasswordInput.hidePassword": "Verberg wachtwoord", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "Te kort (min. {minimumPasswordLength} tekens)", "app.components.PasswordInput.minimumPasswordLengthError": "<PERSON>oer een wachtwoord in dat minimaal {minimumPasswordLength} tekens lang is", "app.components.PasswordInput.passwordEmptyError": "<PERSON><PERSON><PERSON> uw wachtwoord in", "app.components.PasswordInput.passwordStrengthTooltip1": "Om je wachtwoord sterker te maken:", "app.components.PasswordInput.passwordStrengthTooltip2": "Gebruik een combinatie van niet-opeenvolgende kleine letters, hoofdletters, cijfers, speciale tekens en interpunctie", "app.components.PasswordInput.passwordStrengthTooltip3": "Vermijd veelgebruikte of makkelijk te raden woorden", "app.components.PasswordInput.passwordStrengthTooltip4": "Maak het wachtwoord langer", "app.components.PasswordInput.showPassword": "<PERSON>n wachtwoord", "app.components.PasswordInput.strength1Password": "Zeer zwak", "app.components.PasswordInput.strength2Password": "Zwak", "app.components.PasswordInput.strength3Password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength4Password": "Sterk", "app.components.PasswordInput.strength5Password": "Zeer sterk", "app.components.PostCardsComponents.list": "Lijst", "app.components.PostCardsComponents.map": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Voeg een update toe", "app.components.PostComponents.OfficialFeedback.cancel": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Ben je zeker dat je deze update wilt verwijderen?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Bewerk", "app.components.PostComponents.OfficialFeedback.lastEdition": "Laatst gewijzigd op {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Laatste update: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Officieel", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Kies welke naam getoond wordt", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Auteurs<PERSON><PERSON> van de officiële update", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Te<PERSON>t van de officiële update", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Officiële updates", "app.components.PostComponents.OfficialFeedback.postedOn": "Geplaatst op {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Publiceren", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Toon vorige updates", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Plaats een update...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "Sorry, er deed zich een probleem voor", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "<PERSON><PERSON><PERSON><PERSON> je bericht", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Je update is met succes opgeslagen!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "<PERSON><PERSON>n mijn onderwerp '{postTitle}' op {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "<PERSON><PERSON><PERSON> mijn onderwerp: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "<PERSON><PERSON><PERSON> mijn onderwerp: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Wat denk je van dit idee? Breng je stem uit en neem deel aan de discussie op {postUrl} om je stem te laten horen!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "<PERSON><PERSON><PERSON> mijn idee: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "<PERSON><PERSON><PERSON> mijn idee: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Wat denk je van dit voorste<PERSON>? Breng je stem uit en neem deel aan de discussie op {postUrl} om je stem te laten horen!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "<PERSON>eun mijn voorstel: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "<PERSON><PERSON><PERSON> mijn initiatief: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Ik heb net iets gepost: '{postTitle}' op {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Ik heb net iets gepost: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Ik heb net iets gepost: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "<PERSON>eun mijn voorgestelde optie '{postTitle}' op {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Steun mijn voorgestelde optie: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "<PERSON><PERSON><PERSON> mijn optie: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Steun mijn petitie '{postTitle}' op {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "<PERSON><PERSON><PERSON> mijn petitie: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "<PERSON><PERSON><PERSON> mijn petitie: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Steun mijn project '{postTitle}' op {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Steun mijn project: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Steun mijn project: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Steun mijn voorstel '{postTitle}' op {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "<PERSON>eun mijn voorstel: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Ik heb net een voorstel gepost voor {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "<PERSON><PERSON><PERSON> de<PERSON> aan de <PERSON> over deze vraag '{postTitle}' op {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "<PERSON><PERSON><PERSON> de<PERSON> aan de <PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "<PERSON><PERSON><PERSON> de<PERSON> aan de <PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Stem voor {postTitle} op", "app.components.PostComponents.linkToHomePage": "<PERSON> naar de homepagina", "app.components.PostComponents.readMore": "<PERSON><PERSON> meer...", "app.components.PostComponents.topics": "Tags", "app.components.ProjectArchivedIndicator.archivedProject": "Je kan niet langer deelnemen aan dit project omdat het gearchiveerd is", "app.components.ProjectArchivedIndicator.previewProject": "Conceptproject:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "<PERSON>een z<PERSON>ar voor moderators en degenen met een voorbeeldlink.", "app.components.ProjectCard.a11y_projectDescription": "Projectbeschrijving:", "app.components.ProjectCard.a11y_projectTitle": "Titel van het project:", "app.components.ProjectCard.addYourOption": "Voeg een optie toe", "app.components.ProjectCard.allocateYourBudget": "Maak je keuze", "app.components.ProjectCard.archived": "Gearchiveerd", "app.components.ProjectCard.comment": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.contributeYourInput": "Voeg je bijdrage toe", "app.components.ProjectCard.finished": "Afgelopen", "app.components.ProjectCard.joinDiscussion": "<PERSON><PERSON><PERSON> a<PERSON> de <PERSON>", "app.components.ProjectCard.learnMore": "<PERSON><PERSON> meer", "app.components.ProjectCard.reaction": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.readTheReport": "<PERSON><PERSON> het rapport", "app.components.ProjectCard.reviewDocument": "Bekijk het document", "app.components.ProjectCard.submitAnIssue": "{tenant<PERSON><PERSON>, select, gent {Voeg een verhaal toe} other {Voeg een reactie toe}}", "app.components.ProjectCard.submitYourIdea": "Voeg je idee toe", "app.components.ProjectCard.submitYourInitiative": "Dien je initiatief in", "app.components.ProjectCard.submitYourPetition": "Dien je petitie in", "app.components.ProjectCard.submitYourProject": "Voeg je project toe", "app.components.ProjectCard.submitYourProposal": "Dien je voorstel in", "app.components.ProjectCard.takeThePoll": "<PERSON><PERSON><PERSON><PERSON> aan de peiling", "app.components.ProjectCard.takeTheSurvey": "<PERSON><PERSON> <PERSON>", "app.components.ProjectCard.viewTheContributions": "Bekijk de onderwerpen", "app.components.ProjectCard.viewTheIdeas": "Bekijk de ideeën", "app.components.ProjectCard.viewTheInitiatives": "Bekijk de initiatieven", "app.components.ProjectCard.viewTheIssues": "{<PERSON><PERSON><PERSON>, select, gent {<PERSON><PERSON><PERSON>hale<PERSON>} other {<PERSON><PERSON>jk de stellingen}}", "app.components.ProjectCard.viewTheOptions": "Bekijk de opties", "app.components.ProjectCard.viewThePetitions": "Bekijk de petities", "app.components.ProjectCard.viewTheProjects": "Bekijk de projecten", "app.components.ProjectCard.viewTheProposals": "Bekijk de voorstellen", "app.components.ProjectCard.viewTheQuestions": "Bekijk de vragen", "app.components.ProjectCard.vote": "<PERSON><PERSON>", "app.components.ProjectCard.xComments": "{commentsCount, plural, no {# reacties} one {# reactie} other {# reacties}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# onderwerp} other {# onderwerpen}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {nog geen ideeën} one {# idee} other {# ideeën}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# initiatieven} one {# initiatief} other {# initiatieven}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# bijdrage} other {# bijdragen}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# optie} other {# opties}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petities} one {# petitie} other {# petities}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# project} other {# projecten}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# voorstellen} one {# voorstel} other {# voorstellen}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# vraag} other {# vragen}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# reacties} one {# reactie} other {# reacties}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# inputs} one {# input} other {# inputs}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projecten} one {# project} other {# projecten}}", "app.components.ProjectFolderCards.components.Topbar.all": "Alle", "app.components.ProjectFolderCards.components.Topbar.archived": "Afgerond", "app.components.ProjectFolderCards.components.Topbar.draft": "Concept", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filter op", "app.components.ProjectFolderCards.components.Topbar.published2": "Gepubliceerd", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tag", "app.components.ProjectFolderCards.noProjectYet": "<PERSON><PERSON> <PERSON>ijn hier momenteel geen actieve of open projecten voor jou", "app.components.ProjectFolderCards.noProjectsAvailable": "<PERSON><PERSON> projecten be<PERSON>", "app.components.ProjectFolderCards.showMore": "Toon meer", "app.components.ProjectFolderCards.stayTuned": "Registreer je e<PERSON>t of kom later nog eens kijken wanneer er zich nieuwe mogelijkheden aandienen", "app.components.ProjectFolderCards.tryChangingFilters": "<PERSON><PERSON><PERSON> het met andere <PERSON>.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Al gebruikt in deze gemeenten", "app.components.ProjectTemplatePreview.copied": "Gekopieerd", "app.components.ProjectTemplatePreview.copyLink": "<PERSON><PERSON><PERSON> de link", "app.components.QuillEditor.alignCenter": "<PERSON><PERSON> te<PERSON>t", "app.components.QuillEditor.alignLeft": "<PERSON><PERSON>", "app.components.QuillEditor.alignRight": "Rechts uitlijnen", "app.components.QuillEditor.bold": "Vet", "app.components.QuillEditor.clean": "Opmaak verwijderen", "app.components.QuillEditor.customLink": "Knop toevoegen", "app.components.QuillEditor.customLinkPrompt": "<PERSON><PERSON> de link in:", "app.components.QuillEditor.edit": "Bewerk", "app.components.QuillEditor.image": "Upload afbeelding", "app.components.QuillEditor.imageAltPlaceholder": "<PERSON><PERSON> beschrijving van de a<PERSON>lding", "app.components.QuillEditor.italic": "Cursief", "app.components.QuillEditor.link": "<PERSON>", "app.components.QuillEditor.linkPrompt": "<PERSON><PERSON> de link in:", "app.components.QuillEditor.normalText": "Normaal", "app.components.QuillEditor.orderedList": "Geordende lijst", "app.components.QuillEditor.remove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.save": "Opsla<PERSON>", "app.components.QuillEditor.subtitle": "Ondertitel", "app.components.QuillEditor.title": "Titel", "app.components.QuillEditor.unorderedList": "Ongeordende lijst", "app.components.QuillEditor.video": "Voeg een video toe", "app.components.QuillEditor.videoPrompt": "<PERSON><PERSON> de video in:", "app.components.QuillEditor.visitPrompt": "Bezoek de link:", "app.components.ReactionControl.completeProfileToReact": "Voltooi je profiel om te reageren", "app.components.ReactionControl.dislike": "Vind ik niet leuk", "app.components.ReactionControl.dislikingDisabledMaxReached": "Je hebt je maximale aantal dislikes bereikt in {projectName}", "app.components.ReactionControl.like": "Vind ik leuk", "app.components.ReactionControl.likingDisabledMaxReached": "Je hebt je maximale aantal likes in {projectName} bereikt", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Reageren wordt ingeschakeld zodra deze fase begint", "app.components.ReactionControl.reactingDisabledPhaseOver": "He<PERSON> is niet langer mogelijk om in deze fase te reageren", "app.components.ReactionControl.reactingDisabledProjectInactive": "Je kunt niet langer reageren op ideeën in {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Reageren is momenteel niet ingeschakeld voor dit project", "app.components.ReactionControl.reactingNotPermitted": "Reageren is alleen ingeschakeld voor bepaalde groepen", "app.components.ReactionControl.reactingNotSignedIn": "Log in om te reageren.", "app.components.ReactionControl.reactingPossibleLater": "Reageren begint op {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Verifieer je identiteit om te kunnen reageren.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Datum activiteit: {startDate} om {startTime} tot {endDate} om {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Datum activiteit: {eventDate} van {startTime} tot {endTime}.", "app.components.Sharing.linkCopied": "<PERSON> gek<PERSON>", "app.components.Sharing.or": "of", "app.components.Sharing.share": "<PERSON><PERSON>", "app.components.Sharing.shareByEmail": "Deel per e-mail", "app.components.Sharing.shareByLink": "<PERSON><PERSON><PERSON> de link", "app.components.Sharing.shareOnFacebook": "Delen op Facebook", "app.components.Sharing.shareOnTwitter": "Delen op Twitter", "app.components.Sharing.shareThisEvent": "Deel dit evenement", "app.components.Sharing.shareThisFolder": "<PERSON><PERSON>", "app.components.Sharing.shareThisProject": "Deel dit project", "app.components.Sharing.shareViaMessenger": "<PERSON><PERSON> via Messenger", "app.components.Sharing.shareViaWhatsApp": "<PERSON><PERSON> via WhatsApp", "app.components.SideModal.closeButtonAria": "Sluiten", "app.components.StatusModule.futurePhase": "Je bekijkt een fase die nog niet is begonnen. Je kunt deelnemen wanneer de fase begint.", "app.components.StatusModule.modifyYourSubmission1": "Je inzending wijzigen", "app.components.StatusModule.submittedUntil3": "Je kunt je stem uitbrengen tot en met", "app.components.TopicsPicker.numberOfSelectedTopics": "{numberOfSelectedTopics, plural,=0 {Geen tags} one {<PERSON><PERSON> tag} other {# tags}} geselecteerd. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Afbeelding vergroten", "app.components.UI.MoreActionsMenu.moreOptions": "Meer opties", "app.components.UI.MoreActionsMenu.showMoreActions": "Meer acties tonen", "app.components.UI.NewLabel.new": "NIEUW", "app.components.UI.PhaseFilter.noAppropriatePhases": "Geen geschikte fases gevonden voor dit project", "app.components.UI.RemoveImageButton.a11y_removeImage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.UI.TranslateButton.original": "Origineel", "app.components.UI.TranslateButton.translate": "<PERSON><PERSON><PERSON>", "app.components.Unauthorized.additionalInformationRequired": "<PERSON><PERSON> is aanvullende informatie nodig voordat je deel kunt nemen.", "app.components.Unauthorized.completeProfile": "Vul je profiel verder aan", "app.components.Unauthorized.completeProfileTitle": "Vul je profiel verder aan om deel te nemen", "app.components.Unauthorized.noPermission": "Je hebt geen toestemming om deze pagina te bekijken", "app.components.Unauthorized.notAuthorized": "Sorry, je bent niet gemachtigd om toegang tot deze pagina te krijgen.", "app.components.Upload.errorImageMaxSizeExceeded": "De afbeelding die je koos is groter dan {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "<PERSON><PERSON> of meer bestand(en) overschrijd(t)(en) de upload limiet van {maxFileSize} Mb", "app.components.Upload.onlyOneImage": "Je kan maximum 1 afbeelding uploaden", "app.components.Upload.onlyXImages": "Je kunt slechts {maxItemsCount} afbeeldingen uploaden", "app.components.Upload.remaining": "overblijvend", "app.components.Upload.uploadImageLabel": "Selecteer een afbeelding (max. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Selecteer een of meer afbeeldingen", "app.components.UpsellTooltip.tooltipContent": "Deze functionaliteit is niet inbegrepen in je huidige plan. <PERSON><PERSON><PERSON> met je Government Success Manager of beheerder om het in te schakelen.", "app.components.UserName.anonymous": "Anoni<PERSON>", "app.components.UserName.anonymousTooltip2": "Deze gebruiker heeft besloten zijn bijdrage te anonimiseren", "app.components.UserName.authorWithNoNameTooltip": "Je naam is automatisch gegenereerd omdat je je naam niet hebt ingevuld. Werk je profiel bij als je dit wilt veranderen.", "app.components.UserName.deletedUser": "onbekende auteur", "app.components.UserName.verified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.VerificationModal.verifyAuth0": "Verifieer met NemID", "app.components.VerificationModal.verifyBOSA": "Verifieer met itsme of e-ID", "app.components.VerificationModal.verifyBosaFas": "Verifieer met itsme of eID", "app.components.VerificationModal.verifyClaveUnica": "<PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON>e Unica", "app.components.VerificationModal.verifyFakeSSO": "<PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON>", "app.components.VerificationModal.verifyIdAustria": "Verifieer met <PERSON>", "app.components.VerificationModal.verifyKeycloak": "Verifi<PERSON>ren met <PERSON>-Porten", "app.components.VerificationModal.verifyNemLogIn": "<PERSON><PERSON><PERSON><PERSON> met MitID", "app.components.VerificationModal.verifyTwoday2": "Verifiëren met BankID of Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Verifieer je identiteit", "app.components.VoteControl.budgetingFutureEnabled": "Je kan je budget verdelen vanaf {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Burgerbegroting is alleen mogelijk voor bepaalde groepen.", "app.components.VoteControl.budgetingNotPossible": "Het maken van wijzigingen in je budget is op dit moment niet mogelijk.", "app.components.VoteControl.budgetingNotVerified": "{verifyAccountLink} om verder te gaan.", "app.components.VoteInputs._shared.currencyLeft1": "Je hebt {budgetLeft} / {totalBudget} over", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Je hebt {votesLeft, plural, =0 {geen credits over} other {# uit {totalNumberOfVotes, plural, one {1 credit} other {# credits}} over}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Je hebt {votesLeft, plural, =0 {geen punten over} other {# uit {totalNumberOfVotes, plural, one {1 punt} other {# punten}} over}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Je hebt {votesLeft, plural, =0 {geen tokens over} other {# uit {totalNumberOfVotes, plural, one {1 token} other {# tokens}} over}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Je hebt {votesLeft, plural, =0 {geen stemmen over} other {# uit {totalNumberOfVotes, plural, one {1 stem} other {# stemmen}} over}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Je hebt je budget al ingediend. Om het te wijzigen, klik je op \"Wijzig je inzending\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Je hebt je budget al ingediend. Om het te wijzigen, ga je terug naar de projectpagina en klik je op \"Wijzig indiening\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Burgerbegroting is niet be<PERSON>, omdat deze fase niet actief is.", "app.components.VoteInputs.single.youHaveVotedForX2": "Je hebt gestemd op {votes, plural, =0 {# opties} one {# optie} other {# opties}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "Dit betekent dat je alle gegevens kwijtraakt die bij deze bijdrage horen, zoa<PERSON> op<PERSON>, reacties en stemmen. Deze actie kan niet ongedaan worden gemaakt.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Weet je zeker dat je deze bijdrage wilt verwijderen?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Bevestigen", "app.components.admin.SlugInput.resultingURL": "Resulterende URL", "app.components.admin.SlugInput.slugTooltip": "De slug is de unieke reeks woorden aan het eind van het webadres van een pagina, of URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "<PERSON>s je de URL verandert, zullen links naar de pagina met de oude URL niet langer functioneren.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "Voeg een voorwa<PERSON>e toe", "app.components.admin.UserFilterConditions.field_email": "E-mail", "app.components.admin.UserFilterConditions.field_event_attendance": "Event registraties", "app.components.admin.UserFilterConditions.field_follow": "Volgen", "app.components.admin.UserFilterConditions.field_lives_in": "<PERSON>oon<PERSON> in", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Tevredenheidsmonitor vragenlijst", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "<PERSON><PERSON><PERSON><PERSON> met een bij<PERSON><PERSON> met status", "app.components.admin.UserFilterConditions.field_participated_in_project": "Project wa<PERSON>an gepart<PERSON>", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Bijgedragen aan tag", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Registratie", "app.components.admin.UserFilterConditions.field_role": "Rol", "app.components.admin.UserFilterConditions.field_verified": "Verificatie", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideeën<PERSON><PERSON>", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Voorstellen", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "is niet gere<PERSON><PERSON><PERSON> voor een van deze events", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "is niet geregistreerd voor een event", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "is geregistreerd voor een van deze events", "app.components.admin.UserFilterConditions.predicate_attends_something": "is geregistreerd voor ten minste één event", "app.components.admin.UserFilterConditions.predicate_begins_with": "start met", "app.components.admin.UserFilterConditions.predicate_commented_in": "gere<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_contains": "bevat", "app.components.admin.UserFilterConditions.predicate_ends_on": "eindigt op", "app.components.admin.UserFilterConditions.predicate_has_value": "heeft als waarde", "app.components.admin.UserFilterConditions.predicate_in": "voerde om het even welke actie uit", "app.components.admin.UserFilterConditions.predicate_is": "is", "app.components.admin.UserFilterConditions.predicate_is_admin": "is een beheerder", "app.components.admin.UserFilterConditions.predicate_is_after": "is na", "app.components.admin.UserFilterConditions.predicate_is_before": "is voor", "app.components.admin.UserFilterConditions.predicate_is_checked": "is aangev<PERSON>t", "app.components.admin.UserFilterConditions.predicate_is_empty": "is leeg", "app.components.admin.UserFilterConditions.predicate_is_equal": "is", "app.components.admin.UserFilterConditions.predicate_is_exactly": "is", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "is groter dan", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "is groter dan of gelijk aan", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "is een gewone gebruiker", "app.components.admin.UserFilterConditions.predicate_is_not_area": "sluit gebied uit", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "sluit map uit", "app.components.admin.UserFilterConditions.predicate_is_not_input": "sluit invoer uit", "app.components.admin.UserFilterConditions.predicate_is_not_project": "sluit project uit", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "sluit tag uit", "app.components.admin.UserFilterConditions.predicate_is_one_of": "is een van", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "e<PERSON> van de g<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "een van de mappen", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "een van de in<PERSON>n", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "een van de <PERSON>en", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "een van <PERSON>", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "is een projectbe<PERSON><PERSON>r", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "is kleiner dan", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "is kleiner dan of gelijk aan", "app.components.admin.UserFilterConditions.predicate_is_verified": "is geverifieerd", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "begint niet met", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "niet gere<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_contains": "bevat geen", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "eindigt niet op", "app.components.admin.UserFilterConditions.predicate_not_has_value": "heeft niet als waarde", "app.components.admin.UserFilterConditions.predicate_not_in": "geen actie uitgevoerd", "app.components.admin.UserFilterConditions.predicate_not_is": "is niet", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "is geen beheerder", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "is niet a<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "is niet leeg", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "is niet", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "is niet een gewone gebruiker", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "is niet een van", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "is geen projectbeheerder", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "is niet g<PERSON><PERSON>ieerd", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "heeft geen bijdrage gepla<PERSON>t", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "reageerde niet op reactie", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "reageerde niet op bijdrage", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "niet geregistreerd voor een activiteit", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "heeft de vragenlijst niet ingevuld", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "was geen <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "heeft niet deelgenomen aan de stemming", "app.components.admin.UserFilterConditions.predicate_nothing": "niets", "app.components.admin.UserFilterConditions.predicate_posted_input": "een bijdrage gep<PERSON>t", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reageerde op reactie", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reageerde op bijdrage", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "geregistreerd voor een activiteit", "app.components.admin.UserFilterConditions.predicate_something": "iets", "app.components.admin.UserFilterConditions.predicate_taken_survey": "heeft de vragenlijst ingevuld", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "is een v<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_voted_in3": "heeft deelgenomen aan de stemming", "app.components.admin.UserFilterConditions.rulesFormLabelField": "A", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "B", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "C", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Je krijgt geen meldingen over je bijdrage", "app.components.anonymousParticipationModal.cancel": "<PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.continue": "Ga verder", "app.components.anonymousParticipationModal.participateAnonymously": "Doe anoniem mee", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "<PERSON>t zal <PERSON>ig <b>je profiel verbergen</b> voor beheerders en andere bewoners voor deze specifieke bijdrage, zodat niemand deze bijdrage aan jou kan linken. Anonieme bijdragen kunnen niet bewerkt worden, en worden als definitief beschouwd.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Ons platform veilig maken voor elke gebruiker is voor ons een topprioriteit. Woorden doen ertoe, dus wees alsjeblieft aardig voor elkaar.", "app.components.avatar.titleForAccessibility": "<PERSON><PERSON> {fullName}", "app.components.customFields.mapInput.removeAnswer": "Antwoord verwijderen", "app.components.customFields.mapInput.undo": "Ongedaan maken", "app.components.customFields.mapInput.undoLastPoint": "Laatste punt on<PERSON>aan maken", "app.components.followUnfollow.follow": "Volgen", "app.components.followUnfollow.followADiscussion": "Volg de discussie", "app.components.followUnfollow.followTooltipInputPage2": "Door te volgen ontvang je e-mailupdates over statuswijzigingen, officiële updates en opmerkingen. Je kunt op elk moment {unsubscribeLink} gebruiken.", "app.components.followUnfollow.followTooltipProjects2": "Door te volgen ontvang je e-mailupdates over projectwijzigingen. Je kunt op elk moment {unsubscribeLink} gebruiken.", "app.components.followUnfollow.unFollow": "Ontvolgen", "app.components.followUnfollow.unsubscribe": "uitschrijven", "app.components.followUnfollow.unsubscribeUrl": "/profiel/bewerken", "app.components.form.ErrorDisplay.guidelinesLinkText": "<PERSON><PERSON>", "app.components.form.ErrorDisplay.next": "Volgende", "app.components.form.ErrorDisplay.previous": "Vorige", "app.components.form.ErrorDisplay.save": "<PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.userPickerPlaceholder": "<PERSON>gin met typen om te zoeken op email of naam...", "app.components.form.anonymousSurveyMessage2": "Alle antwoorden op deze vragenlijst zijn geanonimiseerd.", "app.components.form.backToInputManager": "<PERSON><PERSON>ar <PERSON><PERSON>", "app.components.form.backToProject": "Terug naar project", "app.components.form.components.controls.mapInput.removeAnswer": "Antwoord verwijderen", "app.components.form.components.controls.mapInput.undo": "Ongedaan maken", "app.components.form.components.controls.mapInput.undoLastPoint": "Laatste punt on<PERSON>aan maken", "app.components.form.controls.addressInputAriaLabel": "<PERSON><PERSON> in<PERSON>", "app.components.form.controls.addressInputPlaceholder6": "<PERSON><PERSON>r een adres in...", "app.components.form.controls.adminFieldTooltip": "Veld enkel zichtba<PERSON> voor beheerders", "app.components.form.controls.allStatementsError": "Voor alle stellingen moet een antwoord worden gekozen.", "app.components.form.controls.back": "Vorige", "app.components.form.controls.clearAll": "Alles wissen", "app.components.form.controls.clearAllScreenreader": "Wis alle antwoorden uit bovenstaande matrixvraag", "app.components.form.controls.clickOnMapMultipleToAdd3": "<PERSON>lik op de kaart om te tekenen. Sleep daarna punten om ze te verpla<PERSON>en.", "app.components.form.controls.clickOnMapToAddOrType": "<PERSON><PERSON> op de kaart of typ hieronder een adres om je antwoord toe te voegen.", "app.components.form.controls.confirm": "Bevestigen", "app.components.form.controls.cosponsorsPlaceholder": "<PERSON><PERSON> met het typen van een naam om te zoeken", "app.components.form.controls.currentRank": "Huidige rang:", "app.components.form.controls.minimumCoordinates2": "Een minimum van {numPoints} punten is vereist.", "app.components.form.controls.noRankSelected": "<PERSON><PERSON> rang geselecteerd", "app.components.form.controls.notPublic1": "*Dit antwoord wordt alleen gedeeld met projectbeheerders en niet met het publiek.", "app.components.form.controls.optionalParentheses": "(optioneel)", "app.components.form.controls.rankingInstructions": "Sleep om opties te rangschikken.", "app.components.form.controls.selectAsManyAsYouLike": "*Selecteer er zoveel als je wilt", "app.components.form.controls.selectBetween": "*Selecteer tussen {minItems} en {maxItems} opties", "app.components.form.controls.selectExactly2": "*Selecteer precies {selectExactly, plural, one {# optie} other {# opties}}", "app.components.form.controls.selectMany": "*Kies er zo veel als je wilt", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Tik op de kaart om te tekenen. Sleep daarna punten om ze te verpla<PERSON>en.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Tik op de kaart om te tekenen.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Tik op de kaart om je antwoord toe te voegen.", "app.components.form.controls.tapOnMapToAddOrType": "Tik op de kaart of typ hieronder een adres om je antwoord toe te voegen.", "app.components.form.controls.tapToAddALine": "Tik om een lijn toe te voegen", "app.components.form.controls.tapToAddAPoint": "Tik om een punt toe te voegen", "app.components.form.controls.tapToAddAnArea": "Tik om een gebied toe te voegen", "app.components.form.controls.uploadShapefileInstructions": "* Upload een zip<PERSON>tand met een of meer shapefiles.", "app.components.form.controls.validCordinatesTooltip2": "Als de locatie niet wordt weergegeven bij de opties terwijl je typt, kun je geldige coördinaten toevoegen in het formaat 'breedtegraad, lengtegraad' om een precieze locatie op te geven (bijvoorbeeld: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} van de {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} van de {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} van de {total}, waarbij {maxValue} {maxLabel} is", "app.components.form.error": "Fout", "app.components.form.locationGoogleUnavailable": "Kan locatieveld geleverd door google maps niet laden.", "app.components.form.progressBarLabel": "Voortgang onderzoek", "app.components.form.submit": "Verzenden", "app.components.form.submitApiError": "<PERSON><PERSON> was een <PERSON><PERSON><PERSON> met het verzend<PERSON> van het formulier. Controleer op eventuele fouten en probeer het opnieuw.", "app.components.form.verifiedBlocked": "Je kan dit veld niet aanpassen omdat het geverifieerde informatie bevat", "app.components.formBuilder.Page": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.accessibilityStatement": "toegankelijkheidsverklaring", "app.components.formBuilder.addAnswer": "Voeg antwoord toe", "app.components.formBuilder.addStatement": "Stelling toevoegen", "app.components.formBuilder.agree": "<PERSON>e eens", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "<PERSON>s je toegang hebt tot ons AI-pakket, kun je tekstreacties samenvatten en categoriseren met AI.", "app.components.formBuilder.askFollowUpToggleLabel": "Vraag follow-up", "app.components.formBuilder.bad": "Slecht", "app.components.formBuilder.buttonLabel": "Label voor de knop", "app.components.formBuilder.buttonLink": "Link voor de knop", "app.components.formBuilder.cancelLeaveBuilderButtonText": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.category": "Categorie", "app.components.formBuilder.chooseMany": "Kies er meer", "app.components.formBuilder.chooseOne": "<PERSON><PERSON> er <PERSON>", "app.components.formBuilder.close": "Sluiten", "app.components.formBuilder.closed": "Gesloten", "app.components.formBuilder.configureMap": "<PERSON><PERSON> configu<PERSON>en", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON><PERSON>, ik wil weg", "app.components.formBuilder.content": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.continuePageLabel": "Gaat door naar", "app.components.formBuilder.cosponsors": "Ondersteuners", "app.components.formBuilder.default": "Standaard", "app.components.formBuilder.defaultContent": "Standaard inhoud", "app.components.formBuilder.delete": "Verwijderen", "app.components.formBuilder.deleteButtonLabel": "Verwijderen", "app.components.formBuilder.description": "Beschrijving", "app.components.formBuilder.disabledBuiltInFieldTooltip": "<PERSON><PERSON> is al toegevoegd in het formulier. Standaard inhoud mag maar één keer gebruikt worden.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Het toe<PERSON><PERSON>n van aangepaste inhoud maakt geen deel uit van je huidige licentie. Neem contact op met je GovSuccess Manager voor meer informatie.", "app.components.formBuilder.disagree": "<PERSON><PERSON>", "app.components.formBuilder.displayAsDropdown": "Weergeven als dropdown", "app.components.formBuilder.displayAsDropdownTooltip": "<PERSON><PERSON> de opties weer in een dropdown. Als je veel opties hebt, is dit aan te raden.", "app.components.formBuilder.done": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.drawArea": "Tekengebied", "app.components.formBuilder.drawRoute": "Route tekenen", "app.components.formBuilder.dropPin": "<PERSON><PERSON> een pin", "app.components.formBuilder.editButtonLabel": "Bewerken", "app.components.formBuilder.emptyImageOptionError": "<PERSON><PERSON> minstens 1 antwoord. Let op dat elk antwoord een titel moet hebben.", "app.components.formBuilder.emptyOptionError": "<PERSON><PERSON>r ten minste één antwoord in", "app.components.formBuilder.emptyStatementError": "<PERSON>f minstens 1 stelling", "app.components.formBuilder.emptyTitleError": "<PERSON><PERSON>r een v<PERSON> in", "app.components.formBuilder.emptyTitleMessage": "<PERSON><PERSON> alle antwoorden een titel", "app.components.formBuilder.emptyTitleStatementMessage": "<PERSON><PERSON> alle stellingen een titel", "app.components.formBuilder.enable": "Ingeschakeld", "app.components.formBuilder.errorMessage": "Er is een probleem opgetreden, los de fout op om je wijzigingen op te slaan", "app.components.formBuilder.fieldGroup.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> (optioneel)", "app.components.formBuilder.fieldGroup.title": "Titel (optioneel)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "<PERSON><PERSON> zijn de antwoorden op deze vragen alleen beschik<PERSON>ar in het geëxporteerde Excel-bestand op de Input Manager, en niet zichtbaar voor de gebruikers.", "app.components.formBuilder.fieldLabel": "Antwoordmogelijkheden", "app.components.formBuilder.fieldLabelStatement": "Stellingen", "app.components.formBuilder.fileUpload": "Bestanden uploaden", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Pagina op <PERSON> van kaart", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Voeg een kaart toe als context of stel locatiegebonden vragen aan de<PERSON>.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Voor een optimale gebruikerservaring raden we af om vragen over punten, routes of gebieden toe te voegen aan pagina's met ka<PERSON><PERSON>.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normale pagina", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "<PERSON><PERSON>-gerelateerde vragen voor vragenlijsten zijn niet inbegrepen in je huidige licentie. Neem contact op met je GovSuccess Manager voor meer informatie.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Paginatype", "app.components.formBuilder.formEnd": "<PERSON><PERSON><PERSON> e<PERSON>e", "app.components.formBuilder.formField.cancelDeleteButtonText": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "<PERSON><PERSON>, ver<PERSON><PERSON><PERSON> pagina", "app.components.formBuilder.formField.copyNoun": "<PERSON><PERSON>", "app.components.formBuilder.formField.copyVerb": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Als je deze pagina verwij<PERSON>t, wordt ook de bijbehorende logica verwijderd. Weet je zeker dat je deze wilt verwijderen?", "app.components.formBuilder.formField.deleteResultsInfo": "Dit kan niet ongedaan worden gemaakt", "app.components.formBuilder.goToPageInputLabel": "Dan is de volgende pagina:", "app.components.formBuilder.good": "Goed", "app.components.formBuilder.helmetTitle": "Formulierbouwer", "app.components.formBuilder.imageFileUpload": "Afbeelding uploaden", "app.components.formBuilder.invalidLogicBadgeMessage": "Ongeldige logica", "app.components.formBuilder.labels2": "Labels (optioneel)", "app.components.formBuilder.labelsTooltipContent2": "Kies optionele labels voor de lineaire schaalwaarden.", "app.components.formBuilder.lastPage": "Einde", "app.components.formBuilder.layout": "Lay-out", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Weet je zeker dat je wilt vertrekken?", "app.components.formBuilder.leaveBuilderText": "Je hebt niet-opgeslagen wijzigingen. Sla ze op voordat je weggaat. <PERSON>s je weggaat, ben je je wijzigingen kwijt.", "app.components.formBuilder.limitAnswersTooltip": "Als dit is ingeschakeld, moeten respondenten het opgegeven aantal antwoorden selecteren om verder te kunnen gaan.", "app.components.formBuilder.limitNumberAnswers": "Beperk het aantal antwoorden", "app.components.formBuilder.linePolygonMapWarning2": "Het tekenen van lijnen en polygonen voldoet mogelijk niet aan de toegankelijkheidsnormen. Meer informatie is te vinden op {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Lineaire schaal", "app.components.formBuilder.locationDescription": "Locatie", "app.components.formBuilder.logic": "Logica", "app.components.formBuilder.logicAnyOtherAnswer": "Elk ander antwoord", "app.components.formBuilder.logicConflicts.conflictingLogic": "Tegenstrijdige logica", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Deze pagina bevat vragen die naar verschillende pagina's leiden. Als deelnemers meerdere vragen beantwoorden, wordt de verste pagina getoond. <PERSON>org ervoor dat dit gedrag overeen<PERSON>mt met het beoogde vragenlijstontwerp.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Op deze pagina zijn meerdere logicaregels toegepast: logica voor meerkeuzevragen, logica op paginaniveau en logica voor intervragen. Als deze voorwaarden elkaar overlappen, krijgt vraaglogica voorrang op paginalogica en wordt de verst verwijderde pagina getoond. Controleer de logica om er zeker van te zijn dat deze overeenkomt met het beoogde vragenlijstontwerp.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Deze pagina bevat een meerkeuzevraag waarbij de opties naar verschillende pagina's leiden. Als deelnemers meerdere opties selecteren, wordt de verste pagina getoond. Zorg ervoor dat dit gedrag overeenkomt met het beoogde vragenlijstontwerp.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Deze pagina bevat een meerkeuzevraag waarbij opties naar verschillende pagina's leiden en heeft vragen die naar andere pagina's leiden. De verste pagina wordt getoond als deze voorwaarden elkaar overlappen. Zorg ervoor dat dit gedrag overeenkomt met het beoogde vragenlijstontwerp.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Deze pagina bevat een meerkeuzevraag waarbij opties naar verschillende pagina's leiden en heeft logica ingesteld op zowel pagina- als vraagniveau. De logica van de vraag heeft voorrang en de verste pagina wordt getoond. Zorg ervoor dat dit gedrag overeenkomt met het beoogde vragenlijstontwerp.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Deze pagina heeft zowel logica op paginaniveau als op vraagniveau. Vraaglogica heeft voorrang op logica op paginaniveau. Zorg ervoor dat dit gedrag overeenkomt met het beoogde vragenlijstontwerp.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Deze pagina heeft logica ingesteld op zowel pagina- als vraagniveau, en meerdere vragen leiden naar verschillende pagina's. De logica van de vraag krijgt voorrang en de verste pagina wordt getoond. Zorg ervoor dat dit gedrag overeenkomt met het beoogde vragenlijstontwerp.", "app.components.formBuilder.logicNoAnswer2": "<PERSON><PERSON>", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Indien een ander antwoord", "app.components.formBuilder.logicPanelNoAnswer": "Indien niet bean<PERSON>woord", "app.components.formBuilder.logicValidationError": "Logica mag niet linken naar eerdere pagina's", "app.components.formBuilder.longAnswer": "Lang antwoord", "app.components.formBuilder.mapConfiguration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.mapping": "Ka<PERSON><PERSON>", "app.components.formBuilder.mappingNotInCurrentLicense": "<PERSON><PERSON>-gerelateerde vragen voor vragenlijsten zijn niet inbegrepen in je huidige licentie. Neem contact op met je GovSuccess Manager voor meer informatie.", "app.components.formBuilder.matrix": "Matrix", "app.components.formBuilder.matrixSettings.columns": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.matrixSettings.rows": "Rijen", "app.components.formBuilder.multipleChoice": "Meerdere keuzes", "app.components.formBuilder.multipleChoiceHelperText": "Als meerdere opties naar verschillende pagina's leiden en deelnemers er meer dan één selecteren, wordt de verst verwijderde pagina getoond. <PERSON>org ervoor dat dit gedrag overeen<PERSON>t met het beoogde vragenlijstontwerp.", "app.components.formBuilder.multipleChoiceImage": "Afbeelding keuze", "app.components.formBuilder.multiselect.maximum": "Maximum", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "Neutraal", "app.components.formBuilder.newField": "<PERSON><PERSON><PERSON> veld", "app.components.formBuilder.number": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Openen", "app.components.formBuilder.optional": "Optioneel", "app.components.formBuilder.other": "<PERSON><PERSON>", "app.components.formBuilder.otherOption": "\"Andere\" optie", "app.components.formBuilder.otherOptionTooltip": "<PERSON><PERSON><PERSON><PERSON>an een aangepast antwoord in te voeren als de verstrekte antwoorden niet over<PERSON><PERSON><PERSON> met hun voorkeur", "app.components.formBuilder.page": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.pageCannotBeDeleted": "Deze pagina kan niet worden verwijderd.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Deze pagina kan niet worden verwijderd en er kunnen geen extra velden worden toegevoegd.", "app.components.formBuilder.pageRuleLabel": "Volgende pagina is:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Als er geen logica is toegevoegd, volgt het formulier zijn normale verloop. Als zowel de pagina als de vragen logica hebben, heeft de logica van de vraag voorrang. Zorg ervoor dat dit overeenkomt met het beoogde vragenlijstontwerp. Ga voor meer informatie naar {supportPageLink}", "app.components.formBuilder.preview": "Voorvertoning:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Ondersteuners worden niet getoond op de gedownloade PDF en kunnen niet worden geïmporteerd via FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "Bestand upload vragen worden getoond als niet ondersteund op de gedownloade PDF en worden niet ondersteund voor importeren via FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "<PERSON><PERSON>-gerelateerde vragen worden getoond op de gedownloade PDF, maar kaartlagen zijn niet zichtba<PERSON>. Kaart-gerelateerde vragen worden niet ondersteund voor importeren via FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Matrixvragen worden getoond op de gedownloade PDF, maar worden momenteel niet ondersteund voor import via FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Titels en beschrij<PERSON><PERSON> van pagi<PERSON>'s worden in de gedownloade PDF weergegeven als koptekst van een sectie.", "app.components.formBuilder.printSupportTooltip.ranking": "Rangschikkingsvragen worden getoond op de gedownloade PDF, maar worden momenteel niet ondersteund voor import via FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Tags worden als niet-ondersteund weergegeven op de gedownloade PDF en worden niet ondersteund voor import via FormSync.", "app.components.formBuilder.proposedBudget": "Voorgesteld budget", "app.components.formBuilder.question": "Vraag", "app.components.formBuilder.questionCannotBeDeleted": "Deze vraag kan niet worden verwijderd.", "app.components.formBuilder.questionDescriptionOptional": "V<PERSON>agbesch<PERSON><PERSON><PERSON> (optioneel)", "app.components.formBuilder.questionTitle": "Vraagtitel", "app.components.formBuilder.randomize": "<PERSON><PERSON>", "app.components.formBuilder.randomizeToolTip": "De volgorde van de antwoorden wordt per gebruiker willekeurig gemaakt", "app.components.formBuilder.range": "Be<PERSON>ik", "app.components.formBuilder.ranking": "Rangschikking", "app.components.formBuilder.rating": "Beoordeling", "app.components.formBuilder.removeAnswer": "Antwoord verwijderen", "app.components.formBuilder.required": "Vere<PERSON>", "app.components.formBuilder.requiredToggleLabel": "Maak het beantwoorden van deze vraag verplicht", "app.components.formBuilder.ruleForAnswerLabel": "Als het antwoord is:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "Als de antwoorden zijn:", "app.components.formBuilder.save": "Opsla<PERSON>", "app.components.formBuilder.selectRangeTooltip": "<PERSON><PERSON> de maximale waarde voor uw schaal.", "app.components.formBuilder.sentiment": "Sentiments<PERSON><PERSON>", "app.components.formBuilder.shapefileUpload": "Esri shapefile uploaden", "app.components.formBuilder.shortAnswer": "<PERSON><PERSON> antwoord", "app.components.formBuilder.showResponseToUsersToggleLabel": "Toon antwoord aan gebruikers", "app.components.formBuilder.singleChoice": "<PERSON><PERSON>", "app.components.formBuilder.staleDataErrorMessage2": "<PERSON>r is een probleem opgetreden. <PERSON>t formulier is onlangs ergens anders opgeslagen. Dit kan komen doordat jij of een andere gebruiker het in een ander browservenster geopend heeft om te bewerken. Vernieuw de pagina om het meest recente formulier te krijgen en maak dan je wijzigingen opnieuw.", "app.components.formBuilder.stronglyAgree": "<PERSON>eer mee eens", "app.components.formBuilder.stronglyDisagree": "Zeer mee oneens", "app.components.formBuilder.supportArticleLinkText": "deze pagina", "app.components.formBuilder.tags": "Tags", "app.components.formBuilder.title": "Titel", "app.components.formBuilder.toLabel": "om", "app.components.formBuilder.unsavedChanges": "Je hebt niet-opgeslagen wijzigingen", "app.components.formBuilder.useCustomButton2": "Gebruik knop voor aangepaste pagina", "app.components.formBuilder.veryBad": "<PERSON><PERSON> slecht", "app.components.formBuilder.veryGood": "<PERSON><PERSON> goed", "app.components.ideas.similarIdeas.engageHere": "Doe hier mee", "app.components.ideas.similarIdeas.noSimilarSubmissions": "<PERSON>n vergelijkbare inzendingen gevonden.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "We hebben vergelijkbare inzendingen gevonden. Is dit inderdaad het geval? Dan kun je dat aangeven door hieronder de vergelijkbare inzending te selecteren. Vervolgens kun je op de knop 'Doe hier mee' klikken om naar de inzending te gaan en daarop te reageren. Wil je liever je idee apart indienen, klik dan op 'Volgende'.", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Soortgelijke inzendingen die al geplaatst zijn:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Op zoek naar vergelijkbare inzendingen ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON><PERSON> dan een dag} one {# dag} other {# dagen}} over", "app.components.phaseTimeLeft.xWeeksLeft": "nog {timeLeft} weken", "app.components.screenReaderCurrency.AED": "Verenigde Arabische Emiraten Dirham", "app.components.screenReaderCurrency.AFN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ALL": "Albanese Lek", "app.components.screenReaderCurrency.AMD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ANG": "Nederlandse Antilliaanse Gulden", "app.components.screenReaderCurrency.AOA": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ARS": "Argentijnse Peso", "app.components.screenReaderCurrency.AUD": "Australische Dollar", "app.components.screenReaderCurrency.AWG": "Arubaans<PERSON>", "app.components.screenReaderCurrency.AZN": "Azerbeidzjaanse Manat", "app.components.screenReaderCurrency.BAM": "Bosnië-Herzegovina Converteerbare Mark", "app.components.screenReaderCurrency.BBD": "Barbadiaanse Dollar", "app.components.screenReaderCurrency.BDT": "Bangladesische Taka", "app.components.screenReaderCurrency.BGN": "Bulgaarse Lev", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BMD": "Bermudiaanse Dollar", "app.components.screenReaderCurrency.BND": "Bruneise Dollar", "app.components.screenReaderCurrency.BOB": "Boliviaanse Boliviano", "app.components.screenReaderCurrency.BOV": "Boliviaanse Mvdol", "app.components.screenReaderCurrency.BRL": "Braziliaanse Real", "app.components.screenReaderCurrency.BSD": "Bahamaanse Dollar", "app.components.screenReaderCurrency.BTN": "Bhutanese Ngultrum", "app.components.screenReaderCurrency.BWP": "Botswaanse Pula", "app.components.screenReaderCurrency.BYR": "Wit-Russische Roebel", "app.components.screenReaderCurrency.BZD": "Belizaanse Dollar", "app.components.screenReaderCurrency.CAD": "Canadese Dollar", "app.components.screenReaderCurrency.CDF": "Congolese Frank", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CHW": "WIR Frank", "app.components.screenReaderCurrency.CLF": "Chileense rekeneenheid (UF)", "app.components.screenReaderCurrency.CLP": "Chileense Peso", "app.components.screenReaderCurrency.CNY": "Chinese Yuan", "app.components.screenReaderCurrency.COP": "Colombiaanse Peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Ricaanse Colón", "app.components.screenReaderCurrency.CRE": "Credit", "app.components.screenReaderCurrency.CUC": "Cubaanse converteerbare Peso", "app.components.screenReaderCurrency.CUP": "Cubaanse Peso", "app.components.screenReaderCurrency.CVE": "Kaapverdische Escudo", "app.components.screenReaderCurrency.CZK": "Tsjechische Kroon", "app.components.screenReaderCurrency.DJF": "Djiboutische Frank", "app.components.screenReaderCurrency.DKK": "Deense Kroon", "app.components.screenReaderCurrency.DOP": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.DZD": "Alger<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.EGP": "Egyptische Pond", "app.components.screenReaderCurrency.ERN": "Eritrese Nakfa", "app.components.screenReaderCurrency.ETB": "Ethiopische Birr", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Fijische Dollar", "app.components.screenReaderCurrency.FKP": "Falklandeilanden Pond", "app.components.screenReaderCurrency.GBP": "Britse Pond", "app.components.screenReaderCurrency.GEL": "Georgische Lari", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Gibraltar Pond", "app.components.screenReaderCurrency.GMD": "Gambia<PERSON><PERSON>", "app.components.screenReaderCurrency.GNF": "Guineese Frank", "app.components.screenReaderCurrency.GTQ": "Guatemalteekse <PERSON>tzal", "app.components.screenReaderCurrency.GYD": "Guyanese Dollar", "app.components.screenReaderCurrency.HKD": "Hongkongse Dollar", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "Kroatische Kuna", "app.components.screenReaderCurrency.HTG": "Haïtia<PERSON><PERSON>", "app.components.screenReaderCurrency.HUF": "Hongaarse Forint", "app.components.screenReaderCurrency.IDR": "Indonesische Roepia", "app.components.screenReaderCurrency.ILS": "Israëlische nieuwe Shekel", "app.components.screenReaderCurrency.INR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.IQD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.IRR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ISK": "IJslandse Króna", "app.components.screenReaderCurrency.JMD": "Jamaicaanse Dollar", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KES": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KGS": "Kirgizische Som", "app.components.screenReaderCurrency.KHR": "Cambodjaanse Riel", "app.components.screenReaderCurrency.KMF": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KPW": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Won", "app.components.screenReaderCurrency.KRW": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Won", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "Kaaimaneilandse Dollar", "app.components.screenReaderCurrency.KZT": "Kazachstaanse Tenge", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Libanese Pond", "app.components.screenReaderCurrency.LKR": "Sri Lanka<PERSON><PERSON>", "app.components.screenReaderCurrency.LRD": "Liberiaanse Dollar", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Litouwse Litas", "app.components.screenReaderCurrency.LVL": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LYD": "Libische Dinar", "app.components.screenReaderCurrency.MAD": "Marokkaanse Dirham", "app.components.screenReaderCurrency.MDL": "Moldavische Leu", "app.components.screenReaderCurrency.MGA": "Malagassische Ariary", "app.components.screenReaderCurrency.MKD": "Macedonische Denar", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "Mongoolse Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "Mauritaanse Ouguiya", "app.components.screenReaderCurrency.MUR": "Mauritia<PERSON><PERSON>", "app.components.screenReaderCurrency.MVR": "Maledivische Rufiyaa", "app.components.screenReaderCurrency.MWK": "Malawische Kwacha", "app.components.screenReaderCurrency.MXN": "Mexicaanse Peso", "app.components.screenReaderCurrency.MXV": "Mexicaanse Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Maleisische Ringgit", "app.components.screenReaderCurrency.MZN": "Mozambikaanse Metical", "app.components.screenReaderCurrency.NAD": "Namibische Dollar", "app.components.screenReaderCurrency.NGN": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NIO": "Nicaraguaans Córdoba", "app.components.screenReaderCurrency.NOK": "Noorse K<PERSON>", "app.components.screenReaderCurrency.NPR": "Nepalese Roepie", "app.components.screenReaderCurrency.NZD": "Nieuw-Zeelandse Dollar", "app.components.screenReaderCurrency.OMR": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.PAB": "Panamese Balboa", "app.components.screenReaderCurrency.PEN": "Peruaanse Sol", "app.components.screenReaderCurrency.PGK": "Papoea-Nieuw-G<PERSON><PERSON>", "app.components.screenReaderCurrency.PHP": "Filipijnse Peso", "app.components.screenReaderCurrency.PKR": "Pakistaanse Roepie", "app.components.screenReaderCurrency.PLN": "Poolse <PERSON>y", "app.components.screenReaderCurrency.PYG": "Paraguayaanse Guaraní", "app.components.screenReaderCurrency.QAR": "Qatarese Riyal", "app.components.screenReaderCurrency.RON": "Roemeense Leu", "app.components.screenReaderCurrency.RSD": "Servische Dinar", "app.components.screenReaderCurrency.RUB": "Russische Roebel", "app.components.screenReaderCurrency.RWF": "<PERSON><PERSON><PERSON><PERSON> Frank", "app.components.screenReaderCurrency.SAR": "Saudische Riyal", "app.components.screenReaderCurrency.SBD": "Salomonseilandse Dollar", "app.components.screenReaderCurrency.SCR": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SDG": "Soedanese Pond", "app.components.screenReaderCurrency.SEK": "Zweedse Kroon", "app.components.screenReaderCurrency.SGD": "Singaporese Dollar", "app.components.screenReaderCurrency.SHP": "Sint-<PERSON>", "app.components.screenReaderCurrency.SLL": "Sierra Leoonse Leone", "app.components.screenReaderCurrency.SOS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.SRD": "Surinaamse Dollar", "app.components.screenReaderCurrency.SSP": "Zuid-Soedanese Pond", "app.components.screenReaderCurrency.STD": "São Tomé en Principe Dobra", "app.components.screenReaderCurrency.SYP": "Syrische Pond", "app.components.screenReaderCurrency.SZL": "Swazische Lilangeni", "app.components.screenReaderCurrency.THB": "<PERSON><PERSON>", "app.components.screenReaderCurrency.TJS": "Tadz<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TMT": "Turkmenistaanse Manat", "app.components.screenReaderCurrency.TND": "Tunesische Dinar", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Tongaanse Paʻanga", "app.components.screenReaderCurrency.TRY": "<PERSON>e Lira", "app.components.screenReaderCurrency.TTD": "Trinidad en Tobago Dollar", "app.components.screenReaderCurrency.TWD": "Nieuw-Taiwanese Dollar", "app.components.screenReaderCurrency.TZS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.UAH": "Oekraïense Hryvnia", "app.components.screenReaderCurrency.UGX": "Oegandese <PERSON>", "app.components.screenReaderCurrency.USD": "Amerikaanse Dollar", "app.components.screenReaderCurrency.USN": "Amerikaanse Dollar (volgende dag)", "app.components.screenReaderCurrency.USS": "Amerikaanse Dollar (dezelfde dag)", "app.components.screenReaderCurrency.UYI": "Uruguayaanse Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguayaanse Peso", "app.components.screenReaderCurrency.UZS": "Oezbeekse Som", "app.components.screenReaderCurrency.VEF": "Venezolaanse Bolívar", "app.components.screenReaderCurrency.VND": "Vietnamese Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.XAF": "Centraal-Afrikaanse CFA-Frank", "app.components.screenReaderCurrency.XAG": "Zilver (één troy ounce)", "app.components.screenReaderCurrency.XAU": "Goud (één troy ounce)", "app.components.screenReaderCurrency.XBA": "Europese samengestelde eenheid (EURCO)", "app.components.screenReaderCurrency.XBB": "Europese Monetaire <PERSON> (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "Europese rekeneenheid 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "Europese rekeneenheid 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "Oost-Caribische Dollar", "app.components.screenReaderCurrency.XDR": "Special Drawing Rights", "app.components.screenReaderCurrency.XFU": "UIC Frank", "app.components.screenReaderCurrency.XOF": "West-Afrikaanse CFA-Frank", "app.components.screenReaderCurrency.XPD": "Palladium (één troy ounce)", "app.components.screenReaderCurrency.XPF": "CFP <PERSON>", "app.components.screenReaderCurrency.XPT": "Platina (één troy ounce)", "app.components.screenReaderCurrency.XTS": "Codes specifiek gereserveerd voor testdoeleinden", "app.components.screenReaderCurrency.XXX": "<PERSON><PERSON> valuta", "app.components.screenReaderCurrency.YER": "Jemenitische Rial", "app.components.screenReaderCurrency.ZAR": "Zuid-Afrikaanse Rand", "app.components.screenReaderCurrency.ZMW": "Zambiaanse K<PERSON>cha", "app.components.screenReaderCurrency.amount": "Bedrag", "app.components.screenReaderCurrency.currency": "Valuta", "app.components.trendIndicator.lastQuarter2": "laatste kwartaal", "app.containers.AccessibilityStatement.applicability": "Deze toegankelijkheidsverklaring is van toepassing op een {demoPlatformLink} die representatief is voor deze website; het gebruikt dezelfde broncode en heeft dezelfde functionaliteit.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Evaluatiemethode", "app.containers.AccessibilityStatement.assesmentText2022": "De toegankelijkheid van deze site is geëvalueerd door een externe entiteit die niet betrokken was bij het ontwerp- en ontwikkelingsproces. De conformiteit van bovengenoemde {demoPlatformLink} kan worden geïdentificeerd op deze {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "je voor<PERSON><PERSON> a<PERSON>en", "app.containers.AccessibilityStatement.changePreferencesText": "Je kunt op elk moment {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Uitzonderingen conformiteit", "app.containers.AccessibilityStatement.conformanceStatus": "Toegankelijkheidsstatus", "app.containers.AccessibilityStatement.contentConformanceExceptions": "We streven ernaar om onze inhoud voor iedereen toegankelijk te maken. In sommige gevallen kan er echter ontoegankelijke inhoud op het platform staan, zoals:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demowebsite", "app.containers.AccessibilityStatement.email": "E-mail:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Ingesloten enquêtetools", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "De ingesloten enquêtetools die beschikbaar zijn voor gebruik op dit platform zijn software van derden en zijn mogelijk niet toegankelijk.", "app.containers.AccessibilityStatement.exception_1": "Gebruikers kunnen zelf content op onze digitale participatieplatformen plaatsen. Het is mogelijk dat individuen en organisaties pdf-bestanden, afbeeldingen of andere multimedia-bestandstypen op het platform uploaden als bijlage of toevoegen aan een tekst. Deze documenten zijn mogelijk niet volledig toegankelijk.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Wij horen graag jouw feedback over de toegankelijkheid van deze site. Neem contact met ons op een van de volgende manieren:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "<PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Brussel, België", "app.containers.AccessibilityStatement.headTitle": "Toegankelijkheidsverklaring | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} zet zich in om een platform te bieden dat toegankelijk is voor alle gebruikers, ongeacht technologie of bekwaamheid. De huidige relevante toegankelijkheidsnormen worden nageleefd bij onze voortdurende inspanningen om de toegankelijkheid en bruikbaarheid van onze platforms voor alle gebruikers te maximaliseren.", "app.containers.AccessibilityStatement.mapping": "Ka<PERSON><PERSON>", "app.containers.AccessibilityStatement.mapping_1": "Kaarten op het platform voldoen gedeeltelijk aan de toegankelijkheidsnormen. <PERSON><PERSON><PERSON>otte, zoom en UI-widgets kunnen worden bediend met een toetsenbord bij het bekijken van kaarten. Beheerders kunnen ook de stijl van kaartlagen configureren in de backoffice, of met behul<PERSON> van de Esri-integratie, om toegankelijkere kleurenpaletten en symbolen te maken. Het gebruik van verschillende lijn- of polygoonstijlen (bijv. stippellijnen) helpt ook om kaartlagen waar mogelijk te onderscheiden en hoewel een dergelijke stijl op dit moment niet binnen ons platform kan worden geconfigureerd, kan dit wel als je kaarten gebruikt met de Esri-integratie.", "app.containers.AccessibilityStatement.mapping_2": "<PERSON><PERSON>n in het platform zijn niet volledig toegankelijk, omdat ze geen beluisterbare representatie geven van basiskaarten, kaartlagen of trends in de gegevens voor gebruikers die schermlezers gebruiken. Volledig toegankelijke kaarten zouden de kaartlagen beluisterbaar moeten presenteren en relevante trends in de gegevens moeten beschrijven. Verder is het tekenen van lijnen en veelhoeken in overzichten niet toegankelijk, omdat vormen niet met een toetsenbord getekend kunnen worden. Alternatieve invoermethoden zijn op dit moment niet beschikbaar vanwege de technische complexiteit.", "app.containers.AccessibilityStatement.mapping_3": "Om het tekenen van lijn- en polygoonkaarten toegankelijker te maken, raden we aan om in de enquêtev<PERSON>ag of paginabeschrijving een inleiding of uitleg op te nemen over wat de kaart laat zien en eventuele relevante trends. Verder zou er een korte of lange antwoordvraag kunnen worden opgenomen, zodat respondenten hun antwoord zo nodig in duidelijke bewoordingen kunnen beschrijven (in plaats van op de kaart te klikken). We raden ook aan om contactinformatie voor de projectmanager op te nemen, zodat respondenten die een vraag op de kaart niet kunnen invullen om een alternatieve methode kunnen vragen om de vraag te beantwoorden (bijv. videovergadering).", "app.containers.AccessibilityStatement.mapping_4": "Voor Ideeënprojecten en voorstellen is er een optie om bijdragen weer te geven in een kaartweergave, die niet toegankelijk is. Voor deze methoden is er een alternatieve lijstweergave van bijdragen beschik<PERSON>ar, die wel toegankelijk is.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Onze online workshops bevatten een live videostreaming-component dat momenteel geen ondertiteling ondersteunt.", "app.containers.AccessibilityStatement.pageDescription": "Verklaring over de toegankelijkheid van deze website", "app.containers.AccessibilityStatement.postalAddress": "Adres:", "app.containers.AccessibilityStatement.publicationDate": "Publicatiedatum", "app.containers.AccessibilityStatement.publicationDate2024": "<PERSON><PERSON>ijkheidsverklaring is gepubliceerd op 21 augustus 2024.", "app.containers.AccessibilityStatement.responsiveness": "We proberen je een antwoord te bezorgen binnen 1 tot 2 werkdagen.", "app.containers.AccessibilityStatement.statusPageText": "statuspagin<PERSON>", "app.containers.AccessibilityStatement.technologiesIntro": "<PERSON> toegankelijkheid van de site is afhankelijk van de volgende technologie:", "app.containers.AccessibilityStatement.technologiesTitle": "Technologie", "app.containers.AccessibilityStatement.title": "Toegankelijkheidsverklaring", "app.containers.AccessibilityStatement.userGeneratedContent": "<PERSON><PERSON><PERSON> gegenereerd door gebruikers", "app.containers.AccessibilityStatement.workshops": "Workshops", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Selecteer project", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Met de content builder kun je geavanceerdere layout-opties gebruiken. <PERSON><PERSON> talen waarin geen inhoud be<PERSON> is in de content builder, wordt in plaats daarvan de normale inhoud van de projectbeschrijving weergegeven.", "app.containers.AdminPage.ProjectDescription.linkText": "W<PERSON>jzig beschrijving in de content builder", "app.containers.AdminPage.ProjectDescription.saveError": "Er is iets misgegaan bij het opslaan van de projectbeschrijving.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Gebruik de content builder voor de beschrijving", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Als je de content builder g<PERSON><PERSON><PERSON><PERSON>, kun je geavanceerdere layout-opties gebruiken.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "Bekijk het project", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "<PERSON><PERSON> en<PERSON>u<PERSON>", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Maak een slimme groep aan", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Gebruikers die voldoen aan al deze voorwaarden zullen automatisch aan de groep worden toegevoegd:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "<PERSON><PERSON>r ten minste één regel in", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Sommige voorwaarden zijn onvolledig", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Het configureren van slimme groepen maakt geen deel uit van je huidige licentie. Neem contact op met je GovSuccess Manager voor meer informatie.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "<PERSON>oer een gro<PERSON> in", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Verificatie is niet ingeschakeld op je platform. Verwijder de verificatievoorwaarde of neem contact op met support.", "app.containers.App.appMetaDescription": "Welkom bij het online participatieplatform van {orgName}.\nOntdek de projecten en neem deel aan de discussie!", "app.containers.App.loading": "Bezig met laden...", "app.containers.App.metaTitle1": "Burgerparticipatieplatform | {orgName}", "app.containers.App.skipLinkText": "Ga naar de hoof<PERSON>", "app.containers.AreaTerms.areaTerm": "geb<PERSON>", "app.containers.AreaTerms.areasTerm": "gebieden", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "<PERSON>r bestaat al een account met dit e-mailadres. Je kunt je afmelden, inloggen met dit e-mailadres en je account verifiëren op de instellingenpagina.", "app.containers.Authentication.steps.AccessDenied.close": "Sluiten", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Je voldoet niet aan de voorwa<PERSON>en om deel te nemen aan dit proces.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Ga terug naar verificatie met eenmalige aanmelding", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "<PERSON><PERSON><PERSON> een token in", "app.containers.Authentication.steps.Invitation.token": "Token", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Heb je al een account? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Inloggen", "app.containers.CampaignsConsentForm.ally_categoryLabel": "E-mails in deze categorie", "app.containers.CampaignsConsentForm.messageError": "Er ging iets fout bij het opslaan van je e-mailvoorkeuren.", "app.containers.CampaignsConsentForm.messageSuccess": "Je e-mailvoorkeuren werden opgeslagen.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Wat voor soort e-mailmeldingen wil je ontvangen?", "app.containers.CampaignsConsentForm.notificationsTitle": "Meldingen", "app.containers.CampaignsConsentForm.submit": "Opsla<PERSON>", "app.containers.ChangeEmail.backToProfile": "Terug naar profielinstellingen", "app.containers.ChangeEmail.confirmationModalTitle": "Bevestig je e-mail", "app.containers.ChangeEmail.emailEmptyError": "<PERSON>f een e-mailadres", "app.containers.ChangeEmail.emailInvalidError": "<PERSON>f een e-mailadres in het juiste formaat, bijvoorbeeld <EMAIL>", "app.containers.ChangeEmail.emailRequired": "<PERSON>oer een e-mailadres in.", "app.containers.ChangeEmail.emailTaken": "Dit e-mailadres is al in gebruik.", "app.containers.ChangeEmail.emailUpdateCancelled": "E-mail update is gean<PERSON><PERSON><PERSON>.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Start het proces opnieuw om je e-mail bij te werken.", "app.containers.ChangeEmail.helmetDescription": "<PERSON><PERSON><PERSON><PERSON> je e-mailpagina", "app.containers.ChangeEmail.helmetTitle": "Wijzig je e-mail", "app.containers.ChangeEmail.newEmailLabel": "Nieuwe e-mail", "app.containers.ChangeEmail.submitButton": "Verzenden", "app.containers.ChangeEmail.titleAddEmail": "Voeg je e-mail toe", "app.containers.ChangeEmail.titleChangeEmail": "<PERSON>nder je e-mail", "app.containers.ChangeEmail.updateSuccessful": "Je e-mail is succesvol bijgewerkt.", "app.containers.ChangePassword.currentPasswordLabel": "<PERSON><PERSON><PERSON> wa<PERSON>", "app.containers.ChangePassword.currentPasswordRequired": "<PERSON><PERSON><PERSON> uw huidige wachtwoord in", "app.containers.ChangePassword.goHome": "<PERSON>a naar huis", "app.containers.ChangePassword.helmetDescription": "<PERSON><PERSON><PERSON><PERSON> uw wachtwoord pagina", "app.containers.ChangePassword.helmetTitle": "<PERSON><PERSON><PERSON><PERSON> uw wachtwoord", "app.containers.ChangePassword.newPasswordLabel": "<PERSON><PERSON><PERSON> wa<PERSON>", "app.containers.ChangePassword.newPasswordRequired": "<PERSON><PERSON><PERSON> uw nieuwe wachtwoord in", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Geef een wachtwoord dat ten minste {minimumPasswordLength} tekens lang is.", "app.containers.ChangePassword.passwordChangeSuccessMessage": "<PERSON>w wachtwoord is succesvol bijgewerkt", "app.containers.ChangePassword.passwordEmptyError": "<PERSON><PERSON><PERSON> uw wachtwoord in", "app.containers.ChangePassword.passwordsDontMatch": "Nieuw wachtwoord bevestigen", "app.containers.ChangePassword.titleAddPassword": "Voeg een wachtwoord toe", "app.containers.ChangePassword.titleChangePassword": "<PERSON><PERSON><PERSON><PERSON> je wachtwoord", "app.containers.Comments.a11y_commentDeleted": "Reactie verwi<PERSON>d", "app.containers.Comments.a11y_commentPosted": "<PERSON><PERSON><PERSON> g<PERSON>", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {geen likes} one {1 like} other {# likes}}", "app.containers.Comments.a11y_undoLike": "Like on<PERSON>aan maken", "app.containers.Comments.addCommentError": "Er ging iets mis. Probeer het opnieuw.", "app.containers.Comments.adminCommentDeletionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.Comments.adminCommentDeletionConfirmButton": "Verwijder deze reactie", "app.containers.Comments.cancelCommentEdit": "<PERSON><PERSON><PERSON>", "app.containers.Comments.childCommentBodyPlaceholder": "Reageer...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "Deze reactie werd verwijderd.", "app.containers.Comments.commentDeletionCancelButton": "<PERSON><PERSON><PERSON> mijn <PERSON>", "app.containers.Comments.commentDeletionConfirmButton": "Verwijder mijn react<PERSON>", "app.containers.Comments.commentLike": "Like", "app.containers.Comments.commentReplyButton": "<PERSON><PERSON><PERSON>", "app.containers.Comments.commentsSortTitle": "Rangschik reacties volgens", "app.containers.Comments.completeProfileLinkText": "voltooi je profiel", "app.containers.Comments.completeProfileToComment": "Graag {completeRegistrationLink} om te reageren.", "app.containers.Comments.confirmCommentDeletion": "Ben je zeker dat je deze reactie wil verwijderen? Er is geen weg terug!", "app.containers.Comments.deleteComment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.deleteReasonDescriptionError": "<PERSON><PERSON> meer informatie over je reden", "app.containers.Comments.deleteReasonError": "<PERSON><PERSON> een reden", "app.containers.Comments.deleteReason_inappropriate": "He<PERSON> is ongepast of aanstootgevend", "app.containers.Comments.deleteReason_irrelevant": "Dit hoort hier niet thuis", "app.containers.Comments.deleteReason_other": "Andere reden", "app.containers.Comments.editComment": "Bewerk", "app.containers.Comments.guidelinesLinkText": "on<PERSON>", "app.containers.Comments.ideaCommentBodyPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> je reactie hier", "app.containers.Comments.internalCommentingNudgeMessage": "Het maken van interne reacties is niet inbegrepen in je huidige licentie. Neem contact op met je GovSuccess Manager voor meer informatie.", "app.containers.Comments.internalConversation": "Intern gesprek", "app.containers.Comments.loadMoreComments": "Meer reacties laden", "app.containers.Comments.loadingComments": "Reacties laden...", "app.containers.Comments.loadingMoreComments": "Reacties laden...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Deze reactie is niet zichtba<PERSON> voor gewone gebruikers", "app.containers.Comments.postInternalComment": "Plaats interne reactie", "app.containers.Comments.postPublicComment": "Plaats openbare reactie", "app.containers.Comments.profanityError": "Oeps! Het lijkt erop dat je bericht taal bevat die niet voldoet aan {guidelinesLink}. We proberen iedereen op dit platform zich veilig te laten voelen. Pas je bijdrage aan en probeer opnieuw.", "app.containers.Comments.publicDiscussion": "Openbare discussie", "app.containers.Comments.publishComment": "Plaats je reactie", "app.containers.Comments.reportAsSpamModalTitle": "Waarom wil je dit als spam markeren?", "app.containers.Comments.saveComment": "Opsla<PERSON>", "app.containers.Comments.signInLinkText": "Log in", "app.containers.Comments.signInToComment": "{signInLink} om te kunnen reageren.", "app.containers.Comments.signUpLinkText": "registreren", "app.containers.Comments.verifyIdentityLinkText": "Verifieer je identiteit", "app.containers.Comments.visibleToUsersPlaceholder": "Deze reactie is zichtbaar voor gewone gebruikers", "app.containers.Comments.visibleToUsersWarning": "Reacties die hier worden geplaatst, zijn zichtbaar voor gewone gebruikers.", "app.containers.ContentBuilder.PageTitle": "Projectbeschrijving", "app.containers.CookiePolicy.advertisingContent": "Advertentiecookies kunnen worden gebruikt om de effectiviteit van externe marketingcampagnes op het gebruik van dit platform te meten en deze te personaliseren. We zullen je geen advertenties tonen op dit platform, maar je kunt mogelijk gepersonaliseerde advertenties ontvangen gebaseerd op de pagina’s die je bezoekt.", "app.containers.CookiePolicy.advertisingTitle": "Advertentiecookies", "app.containers.CookiePolicy.analyticsContents": "Analytics-cookies houden bezoekersgedrag bij, zoals welke pagina's worden bezocht en voor hoe lang. Ze kunnen ook enkele technische gegevens verzamelen, waaronder browserinformatie, geschatte locatie en IP-adressen. We gebruiken deze gegevens alleen intern om de algehele gebruikerservaring en werking van het platform te blijven verbeteren. Dergelijke gegevens kunnen ook worden gedeeld tussen Go Vocal en {orgName} om de betrokkenheid bij projecten op het platform te beoordelen en te verbeteren. Houd er rekening mee dat de gegevens anoniem zijn en op geaggregeerd niveau worden gebruikt - ze identificeren je niet persoonlijk. Het is echter mogelijk dat als deze gegevens zouden worden gecombineerd met andere gegevensbronnen, een dergelijke identificatie zou kunnen plaatsvinden.", "app.containers.CookiePolicy.analyticsTitle": "Analytics", "app.containers.CookiePolicy.cookiePolicyDescription": "<PERSON>en gedetailleerde omschrijving van hoe we cookies gebruiken op dit platform", "app.containers.CookiePolicy.cookiePolicyTitle": "Cookiebeleid", "app.containers.CookiePolicy.essentialContent": "Sommige cookies zijn essentieel voor het correct functioneren van dit platform. Deze essentiële cookies worden voornamelijk gebruikt om je account te verifiëren wanneer je het platform bezoekt en om je voorkeurs<PERSON>al op te slaan.", "app.containers.CookiePolicy.essentialTitle": "Essentiële cookies", "app.containers.CookiePolicy.externalContent": "So<PERSON><PERSON> van onze pagina’s kunnen inhoud van externe aanbieders tonen, bijvoorbeeld YouTube of Typeform. We hebben geen controle over deze cookies van derden en het bekijken van de inhoud van deze externe aanbieders kan er toe leiden dat er cookies op je apparaat geïnstalleerd worden.", "app.containers.CookiePolicy.externalTitle": "Externe cookies", "app.containers.CookiePolicy.functionalContents": "Functionele cookies kunnen worden ingeschakeld voor bezoekers om meldingen over updates te ontvangen en om rechtstreeks vanaf het platform toegang te krijgen tot ondersteuningskanalen.", "app.containers.CookiePolicy.functionalTitle": "Functioneel", "app.containers.CookiePolicy.headCookiePolicyTitle": "Cookiebeleid | {orgName}", "app.containers.CookiePolicy.intro": "Cookies zijn tekstbestanden die in de browser of op de harde schijf van je computer of mobiele apparaat worden opgeslagen wanneer je een website bezoekt en waarnaar de website bij volgende bezoeken kan verwijzen. We gebruiken cookies om te begrijpen hoe bezoekers dit platform gebruiken om het ontwerp en de ervaring te verbeteren, om je voorkeuren te onthouden (zoals je voorkeurstaal) en om belangrijke functies voor geregistreerde gebruikers en platformbeheerders te ondersteunen.", "app.containers.CookiePolicy.manageCookiesDescription": "Je kunt analytics-, marketing- en functionele cookies in- en uitschakelen in je cookie-voorkeuren. Je kunt bestaande cookies ook handmatig of automatisch verwijderen via je internetbrowser. De cookies kunnen echter met jouw toestemming opnieuw geïnstalleerd worden bij volgende bezoeken aan dit platform. Als je de cookies niet verwijdert, worden jouw cookie-voorkeuren opgeslagen voor 60 dagen, waarna je opnieuw om toestemming wordt gevraagd.", "app.containers.CookiePolicy.manageCookiesPreferences": "Ga naar je {manageCookiesPreferencesButtonText} om een volledige lijst te zien van integraties van derden die op dit platform worden gebruikt en om jouw voorkeuren te beheren.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "cookie-instellingen", "app.containers.CookiePolicy.manageCookiesTitle": "Jouw cookies beheren", "app.containers.CookiePolicy.viewPreferencesButtonText": "<PERSON>ie-instelling<PERSON>", "app.containers.CookiePolicy.viewPreferencesText": "De onderstaande cookiecategorieën zijn mogelijk niet van toepassing op alle bezoekers of platforms; bekijk je {viewPreferencesButton} voor een volledige lijst van integraties van derden die op jou van toepassing zijn.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Waar gebruiken we cookies voor?", "app.containers.CustomPageShow.editPage": "<PERSON><PERSON><PERSON>", "app.containers.CustomPageShow.goBack": "Ga terug", "app.containers.CustomPageShow.notFound": "<PERSON><PERSON><PERSON> niet gevonden", "app.containers.DisabledAccount.bottomText": "Je kunt je opnieuw aan<PERSON>den vanaf {date}.", "app.containers.DisabledAccount.termsAndConditions": "gebruiksvoorwaarden", "app.containers.DisabledAccount.text2": "Je account op het participatieplatform van {orgName} is tij<PERSON><PERSON><PERSON> uitgeschakeld wegens overtreding van de communityrichtlijnen. Voor meer informatie hierover kun je de {TermsAndConditions} raadplegen.", "app.containers.DisabledAccount.title": "Je account is ti<PERSON><PERSON><PERSON><PERSON> uitgeschakeld", "app.containers.EventsShow.addToCalendar": "<PERSON><PERSON><PERSON><PERSON> aan kalender", "app.containers.EventsShow.editEvent": "Event bewerken", "app.containers.EventsShow.emailSharingBody2": "Woon deze activiteit bij: {eventTitle}. <PERSON><PERSON> meer op {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Activiteit datum en tijd", "app.containers.EventsShow.eventFrom2": "<PERSON> \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Ga terug", "app.containers.EventsShow.goToProject": "Ga naar het project", "app.containers.EventsShow.haveRegistered": "hebben gere<PERSON>", "app.containers.EventsShow.icsError": "Fout bij het downloaden van het ICS-bestand", "app.containers.EventsShow.linkToOnlineEvent": "Link naar online evenement", "app.containers.EventsShow.locationIconAltText": "Locatie", "app.containers.EventsShow.metaTitle": "Activiteit: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Online bijeenkomst", "app.containers.EventsShow.onlineLinkIconAltText": "Link naar online vergadering", "app.containers.EventsShow.registered": "gere<PERSON><PERSON><PERSON>", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 geregistreerden} one {1 geregistreerde} other {# geregistreerden}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} geregistreerden", "app.containers.EventsShow.registrantsIconAltText": "Geregistreerden", "app.containers.EventsShow.socialMediaSharingMessage": "Woon dit evenement bij: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, no {# deelnemers} one {# deelnemer} other {# deelnemers}}", "app.containers.EventsViewer.allTime": "Altijd", "app.containers.EventsViewer.date": "Datum", "app.containers.EventsViewer.thisMonth2": "<PERSON><PERSON><PERSON> ma<PERSON>", "app.containers.EventsViewer.thisWeek2": "Komende week", "app.containers.EventsViewer.today": "Vandaag", "app.containers.IdeaButton.addAContribution": "Voeg een onderwerp toe", "app.containers.IdeaButton.addAPetition": "<PERSON><PERSON> petitie toe<PERSON>n", "app.containers.IdeaButton.addAProject": "Voeg een project toe", "app.containers.IdeaButton.addAProposal": "<PERSON><PERSON> voorstel toevoegen", "app.containers.IdeaButton.addAQuestion": "Voeg een vraag toe", "app.containers.IdeaButton.addAnInitiative": "<PERSON>en initiatief toe<PERSON>n", "app.containers.IdeaButton.addAnOption": "Voeg een optie toe", "app.containers.IdeaButton.postingDisabled": "Nieuwe bijdragen plaatsen is moment<PERSON> niet mogelijk", "app.containers.IdeaButton.postingInNonActivePhases": "Nieuwe bijdragen kunnen alleen in actieve fasen worden toegevoegd.", "app.containers.IdeaButton.postingInactive": "Nieuwe bijdragen plaatsen is moment<PERSON> niet mogelijk", "app.containers.IdeaButton.postingLimitedMaxReached": "Je hebt deze enquête al ingevuld. Bedankt voor je reactie!", "app.containers.IdeaButton.postingNoPermission": "Nieuwe bijdragen plaatsen is moment<PERSON> niet mogelijk", "app.containers.IdeaButton.postingNotYetPossible": "Nieuwe bijdragen plaatsen is moment<PERSON> niet mogelijk", "app.containers.IdeaButton.signInLinkText": "Log in", "app.containers.IdeaButton.signUpLinkText": "registreren", "app.containers.IdeaButton.submitAnIssue": "{tenant<PERSON><PERSON>, select, gent {Voeg een verhaal toe} other {Voeg een reactie toe}}", "app.containers.IdeaButton.submitYourIdea": "Plaats je idee", "app.containers.IdeaButton.takeTheSurvey": "<PERSON><PERSON> <PERSON>", "app.containers.IdeaButton.verificationLinkText": "Verifieer nu je identiteit.", "app.containers.IdeaCard.readMore": "<PERSON><PERSON> le<PERSON>", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {geen reacties} one {1 reactie} other {# reacties}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {geen stemmen} one {1 stem} other {# stemmen}} van de {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Filters sluiten", "app.containers.IdeaCards.a11y_totalItems": "Totaal aantal items: {ideasCount}", "app.containers.IdeaCards.all": "Alle", "app.containers.IdeaCards.allStatuses": "Alle statussen", "app.containers.IdeaCards.contributions": "Bijdragen", "app.containers.IdeaCards.ideaTerm": "Ideeën", "app.containers.IdeaCards.initiatives": "Initiatieven", "app.containers.IdeaCards.issueTerm": "Stellingen", "app.containers.IdeaCards.list": "Lijst", "app.containers.IdeaCards.map": "<PERSON><PERSON>", "app.containers.IdeaCards.mostDiscussed": "Meest besproken", "app.containers.IdeaCards.newest": "Meest recent", "app.containers.IdeaCards.noFilteredResults": "<PERSON><PERSON> resultaten gevonden. Probeer een andere filter of zoekterm.", "app.containers.IdeaCards.numberResults": "Resultaten ({postCount})", "app.containers.IdeaCards.oldest": "Oudste", "app.containers.IdeaCards.optionTerm": "Opties", "app.containers.IdeaCards.petitions": "Petities", "app.containers.IdeaCards.popular": "Meeste stemmen", "app.containers.IdeaCards.projectFilterTitle": "Projecten", "app.containers.IdeaCards.projectTerm": "Projecten", "app.containers.IdeaCards.proposals": "Voorstellen", "app.containers.IdeaCards.questionTerm": "Vragen", "app.containers.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.resetFilters": "Reset de filters", "app.containers.IdeaCards.showXResults": "Toon {ideasCount, plural, no {# results} één {# result} andere {# results}}", "app.containers.IdeaCards.sortTitle": "So<PERSON><PERSON>", "app.containers.IdeaCards.statusTitle": "Status", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "Tags", "app.containers.IdeaCards.topicsTitle": "Tags", "app.containers.IdeaCards.trending": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.tryDifferentFilters": "<PERSON><PERSON> resultaten gevonden. Probeer een andere filter of zoekterm.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} reacties} one {{ideasCount} reactie} other {{ideasCount} reacties}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} bijdragen} one {{ideasCount} bijdrage} other {{ideasCount} bijdragen}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ideeën} one {{ideasCount} idee} other {{ideasCount} ideeën}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} initiatieven} one {{ideasCount} initiatief} other {{ideasCount} initiatieven}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} opties} one {{ideasCount} optie} other {{ideasCount} opties}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} petities} one {{ideasCount} petitie} other {{ideasCount} petities}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projecten} one {{ideasCount} project} other {{ideasCount} projecten}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} voorstellen} one {{ideasCount} voorstel} other {{ideasCount} voorstellen}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} vragen} one {{ideasCount} vraag} other {{ideasCount} vragen}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, no {# results} één {# result} andere {# results}}", "app.containers.IdeasEditPage.contributionFormTitle": "Bewerk dit onderwerp", "app.containers.IdeasEditPage.editedPostSave": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.fileUploadError": "<PERSON><PERSON> of meer bestanden zijn niet geüpload. <PERSON><PERSON><PERSON> bestand<PERSON>grootte en -indeling te controleren en het opnieuw te proberen.", "app.containers.IdeasEditPage.formTitle": "Bewerk dit idee", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Bewerk je bijdrage. Voeg nieuwe informatie toe en wijzig oude informatie.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Bewerk {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Bewerk dit initiatief", "app.containers.IdeasEditPage.issueFormTitle": "{<PERSON><PERSON><PERSON>, select, gent {Bewerk dit verhaal} other {Bewerk deze reactie}}", "app.containers.IdeasEditPage.optionFormTitle": "Bewerk deze optie", "app.containers.IdeasEditPage.petitionFormTitle": "Bewerk deze petitie", "app.containers.IdeasEditPage.projectFormTitle": "Project bewerken", "app.containers.IdeasEditPage.proposalFormTitle": "Bewerk dit voorstel", "app.containers.IdeasEditPage.questionFormTitle": "Bewerk deze vraag", "app.containers.IdeasEditPage.save": "Opsla<PERSON>", "app.containers.IdeasEditPage.submitApiError": "<PERSON><PERSON> was een <PERSON><PERSON><PERSON> met het verzend<PERSON> van het formulier. Controleer op eventuele fouten en probeer het opnieuw.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Alle geplaatste bijdragen", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Bekijk alle bijdragen die op het participatieplatform van {orgName} zijn geplaatst.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Bijdragen | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Bijdragen", "app.containers.IdeasIndexPage.loadMore": "<PERSON><PERSON> meer ideeën...", "app.containers.IdeasIndexPage.loading": "Bezig met laden...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Je inzendingen worden standaard aan je profiel gek<PERSON>d, tenzij je deze optie selecteert.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van het profiel", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "De<PERSON> vragenlijst is momenteel niet open voor reacties. Ga terug naar het project voor meer informatie.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "<PERSON><PERSON> vrage<PERSON> is momenteel niet actief.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Terug naar project", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Je hebt deze vragenlijst al ingevuld.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Vragenlijst ingediend", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Bedankt voor je reactie!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "De omschrijving van de bijdrage mag niet langer zijn dan {limit} tekens", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "De hoofdtekst van het idee dient meer dan {limit} tekens lang te zijn", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "De titel van de bijdrage mag niet langer zijn dan {limit} tekens", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "De titel van de bijdrage moet meer dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Selecteer ten minste één medesponsor", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "De omschrijving van het idee mag niet langer zijn dan {limit} tekens", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "De omschrijving van het idee dient meer dan {limit} tekens lang te zijn", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "G<PERSON><PERSON> een beschrijving te geven", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "De titel van het idee moet minder dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "De titel van het idee dient meer dan {limit} tekens lang te zijn", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "De beschrijving van het initiatief moet minder dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "De beschrijving van het initiatief moet meer dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "De titel van het initiatief moet minder dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "De titel van het initiatief moet meer dan {limit} tekens lang zijn.", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "De omschrijving van het probleem mag niet langer zijn dan {limit} tekens", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "De omschrijving van het probleem dient meer dan {limit} tekens lang te zijn", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "De titel van het probleem mag niet langer zijn dan {limit} tekens", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "De titel van de stelling moet meer dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_number_required": "Dit veld is verplicht, voer een geldig nummer in", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "De optie-omschrijving mag niet langer zijn dan {limit} tekens", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "De optiebeschrijving moet meer dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "De optie-titel mag niet langer zijn dan {limit} tekens", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "De titel van de optie moet meer dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Selecteer ten minste één tag", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "De beschrij<PERSON> van de petitie moet minder dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "De beschrij<PERSON> van de petitie moet meer dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "De titel van de petitie moet minder dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "De titel van de petitie moet meer dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "De projectomschrijving mag niet langer zijn dan {limit} tekens", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "De projectomschrijving dient meer dan {limit} tekens lang te zijn", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "De titel van het project mag niet langer zijn dan {limit} tekens", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "De titel van het project moet meer dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "De beschrijving van het voorstel moet minder dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "De beschrijving van het voorstel moet meer dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "De titel van het voorstel moet minder dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "De titel van het voorstel moet meer dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "<PERSON><PERSON>r een nummer in", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "<PERSON><PERSON>r een nummer in", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "De omschrijving van de vraag mag niet langer zijn dan {limit} tekens", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "De omschrij<PERSON> van de vraag dient meer dan {limit} tekens lang te zijn", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "De titel van de vraag mag niet langer zijn dan {limit} tekens", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "De titel van de vraag moet meer dan {limit} tekens lang zijn", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Voeg a. u. b. een titel toe aan je bijdrage", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "De omschrijving van de bijdrage mag niet langer zijn dan 80 tekens", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "De omschrijving van de bijdrage dient meer dan 30 tekens lang te zijn", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "De bijdragetitel mag uit maximaal 80 karakters bestaan", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "De bijdragetitel moet uit minimaal 10 karakters bestaan", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "De idee-beschrijving dient minder dan 80 tekens lang te zijn", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "De beschrijving van de bijdrage moet minstens 30 tekens lang zijn", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Voeg a. u. b. een titel toe aan je bijdrage", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "De ideetitel mag uit maximaal 80 karakters bestaan", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "De titel van de bijdrage moet ten minste 10 tekens lang zijn", "app.containers.IdeasNewPage.api_error_includes_banned_words": "<PERSON><PERSON><PERSON><PERSON> heb je een of meer woorden gebruikt die door {guidelinesLink} als volgair worden beschouwd. Wijzig uw tekst om eventueel vulgair taalgebruik te verwijderen.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "De beschrijving van het initiatief moet minder dan 80 tekens lang zijn", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "De beschrijving van het initiatief moet minstens 30 tekens lang zijn", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "De titel van het initiatief moet minder dan 80 tekens lang zijn", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "De titel van het initiatief moet minstens 10 tekens lang zijn", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "De omschrijving van het probleem mag niet langer zijn dan 80 tekens", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "De omschrijving van het probleem dient meer dan 30 tekens lang te zijn", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "De titel van het probleem mag niet langer zijn dan 80 tekens", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "De titel van het probleem dient meer dan 10 tekens lang te zijn", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "De optie-omschrijving mag niet langer zijn dan 80 tekens", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "De optie-omschrijving dient meer dan 30 tekens lang te zijn", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "De optie-titel mag niet langer zijn dan 80 tekens", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "De optie-titel dient meer dan 10 tekens lang te zijn", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "De beschrij<PERSON> van de petitie moet minder dan 80 tekens lang zijn", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "De beschrijving van de petitie moet minstens 30 tekens lang zijn", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "De titel van het petitie moet minder dan 80 tekens lang zijn", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "De titel van de petitie moet minstens 10 tekens lang zijn", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "De projectomschrijving mag niet langer zijn dan 80 tekens", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "De projectomschrijving dient meer dan 30 tekens lang te zijn", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "De titel van het project mag niet langer zijn dan 80 tekens", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "De titel van het project dient meer dan 10 tekens lang te zijn", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "De beschrijving van het voorstel moet minder dan 80 tekens lang zijn", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "De beschrijving van het voorstel moet minimaal 30 tekens lang zijn", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "De titel van het voorstel moet minder dan 80 tekens lang zijn", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "De titel van het voorstel moet minimaal 10 tekens lang zijn", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "G<PERSON><PERSON> een beschrijving te geven", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "De omschrijving van de vraag mag niet langer zijn dan 80 tekens", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "De omschrijving van de vraag dient meer dan 30 tekens lang te zijn", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "De titel van de vraag mag niet langer zijn dan 80 tekens", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "De titel van de vraag dient meer dan 10 tekens lang te zijn", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON>, ik wil de pagina verlaten", "app.containers.IdeasNewPage.contributionMetaTitle1": "Voeg nieuwe bijdrage toe aan project | {orgName}", "app.containers.IdeasNewPage.editSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON> wi<PERSON><PERSON>en", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Een bijdrage toevoegen en deelnemen aan het gesprek op het participatieplatform van {orgName}.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Voeg nieuw idee toe aan project | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Nieuw initiatief toevoegen aan project | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Voeg nieuwe reactie toe aan project | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Weet je zeker dat je de pagina wilt verlaten?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Je conceptantwoorden zijn vertrouwelijk opgeslagen en je kunt later terugkomen om ze aan te vullen.", "app.containers.IdeasNewPage.leaveSurvey": "Verlaat vragenlijst", "app.containers.IdeasNewPage.leaveSurveyText": "Je antwoorden worden niet opgeslagen.", "app.containers.IdeasNewPage.optionMetaTitle1": "Voeg nieuwe optie toe aan project | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Nieuwe petitie toevoegen aan project | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Voeg nieuwe project toe aan project | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Nieuw voorstel toevoegen aan project | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Voeg nieuwe vraag toe aan project | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Uitnodiging accepteren", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Uitnodiging tot ondersteuner", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Ondersteuners", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Je bent uitgenodigd om ondersteuner te worden.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Uitnodiging geaccepteerd", "app.containers.IdeasShow.Cosponsorship.pending": "in behandeling", "app.containers.IdeasShow.MetaInformation.attachments": "Bijlagen", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} op {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Huidige status", "app.containers.IdeasShow.MetaInformation.location": "Locatie", "app.containers.IdeasShow.MetaInformation.postedBy": "Geplaatst door", "app.containers.IdeasShow.MetaInformation.similar": "Vergelijkbare bijdragen", "app.containers.IdeasShow.MetaInformation.topics": "Tags", "app.containers.IdeasShow.commentCTA": "Voeg een reactie toe", "app.containers.IdeasShow.contributionEmailSharingBody": "Dit onderwerp '{postTitle}' op {postUrl} steunen!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Dit onderwerp steunen: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Bedankt voor het inzenden van je onderwerp!", "app.containers.IdeasShow.contributionTwitterMessage": "Dit onderwerp steunen: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Dit onderwerp steunen: {postTitle}", "app.containers.IdeasShow.currentStatus": "Huidige status", "app.containers.IdeasShow.deletedUser": "onbekende auteur", "app.containers.IdeasShow.ideaEmailSharingBody": "<PERSON><PERSON>n mijn idee '{ideaTitle}' bij {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "<PERSON><PERSON><PERSON> mijn idee : {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Steun dit idee: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Steun dit idee: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "'{postTitle}': {postUrl}", "app.containers.IdeasShow.imported": "Geïmporteerd", "app.containers.IdeasShow.importedTooltip": "Deze {inputTerm} werd offline verzameld en automatisch geüpload naar het platform.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Steun dit initiatief '{ideaTitle}' op {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Steun dit initiatief: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Bedankt voor het indienen van je initiatief!", "app.containers.IdeasShow.initiativeTwitterMessage": "Steun dit initiatief : {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Steun dit initiatief : {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "'{postTitle}': {postUrl}", "app.containers.IdeasShow.issueEmailSharingSubject": "'{postTitle}': {postUrl}", "app.containers.IdeasShow.issueSharingModalTitle": "{tenant<PERSON><PERSON>, select, gent {Bedankt voor het plaatsen en van je verhaal!} other {Bedankt voor het plaatsen en van je reactie!}}", "app.containers.IdeasShow.issueTwitterMessage": "'{postTitle}': {postUrl}", "app.containers.IdeasShow.metaTitle": "Bijdrage: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Ondersteun deze optie '{postTitle}' op {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Steun deze optie: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Je optie is succesvol gepost!", "app.containers.IdeasShow.optionTwitterMessage": "Steun deze optie: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Steun deze optie: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Steun deze petitie '{ideaTitle}' op {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "<PERSON>eun deze petitie: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Bedankt voor het indienen van je petitie!", "app.containers.IdeasShow.petitionTwitterMessage": "Steun deze petitie: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Steun deze petitie: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Steun dit project '{postTitle}' op {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Steun dit project: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Bedankt voor het inzenden van je project!", "app.containers.IdeasShow.projectTwitterMessage": "Steun dit project: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Steun dit project: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Steun dit voorstel '{ideaTitle}' op {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Steun dit voorstel: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Bedankt voor het indienen van je voorstel!", "app.containers.IdeasShow.proposalTwitterMessage": "Steun dit voorstel: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Steun dit voorstel: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Tijd om nog te stemmen:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} van de {votingThreshold} vereiste stemmen", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "<PERSON><PERSON><PERSON> je stem", "app.containers.IdeasShow.proposals.VoteControl.days": "dagen", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "<PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.hours": "uren", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status en stemmen", "app.containers.IdeasShow.proposals.VoteControl.minutes": "min", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Meer informatie", "app.containers.IdeasShow.proposals.VoteControl.vote": "<PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.voted": "Gestemd", "app.containers.IdeasShow.proposals.VoteControl.votedText": "Je wordt op de hoogte gebracht wanneer dit initiatief naar de volgende stap gaat. {x, plural, =0 {Er zijn {xDays} over.} one {Er is nog {xDays} over.} other {Er zijn nog {xDays} over.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Je stem werd uitgebracht!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "<PERSON><PERSON><PERSON> kun je niet stemmen op dit voorstel. Lees op {link} waarom niet.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {minder dan een dag} one {<PERSON><PERSON> dag} other {# dagen}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {geen stemmen} one {1 stem} other {# stemmen}}", "app.containers.IdeasShow.questionEmailSharingBody": "<PERSON><PERSON><PERSON> de<PERSON> aan de <PERSON> over deze vraag '{postTitle}' op {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "<PERSON><PERSON> mee met de <PERSON><PERSON>: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Je vraag is succesvol gepost!", "app.containers.IdeasShow.questionTwitterMessage": "<PERSON><PERSON> mee met de <PERSON><PERSON>: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "<PERSON><PERSON> mee met de <PERSON><PERSON>: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Waarom wil je dit als spam markeren?", "app.containers.IdeasShow.share": "<PERSON><PERSON>", "app.containers.IdeasShow.sharingModalSubtitle": "<PERSON><PERSON><PERSON> meer mensen en laat je stem horen.", "app.containers.IdeasShow.sharingModalTitle": "Bedankt voor het inzenden van je idee!", "app.containers.Navbar.completeOnboarding": "Voltooi registratie", "app.containers.Navbar.completeProfile": "<PERSON><PERSON>", "app.containers.Navbar.confirmEmail2": "Bevestig e-mailadres", "app.containers.Navbar.unverified": "<PERSON><PERSON>", "app.containers.Navbar.verified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.beforeYouFollow": "Voordat je volgt", "app.containers.NewAuthModal.beforeYouParticipate": "Voordat u deelnee<PERSON>t", "app.containers.NewAuthModal.completeYourProfile": "Vul je profiel verder aan", "app.containers.NewAuthModal.confirmYourEmail": "Bevestig uw e-mail", "app.containers.NewAuthModal.logIn": "Inloggen", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Bekijk de voorwaarden hieronder om verder te gaan.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Vul je profiel alsjeblieft verder aan.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Ga terug naar de inlogopties", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Heb je geen account? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Aanmelden", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "De code moet uit 4 cijfers bestaan.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Doorgaan met FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Op dit platform zijn geen authenticatiemethoden ingeschakeld.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "Door verder te gaan, ga je <PERSON><PERSON><PERSON><PERSON> met het ontvangen van e-mails van dit platform. Je kunt in de pagina 'Mijn instellingen' selecteren welke e-mails je wilt ontvangen.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "E-mail", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "<PERSON>f een e-mailadres in het juiste formaat, bijvoorbeeld <EMAIL>.", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "<PERSON>f een e-mailadres", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "<PERSON><PERSON>r uw e-mailadres in om verder te gaan.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Wachtwoord vergeten?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Log in op uw account: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "<PERSON><PERSON><PERSON> uw wachtwoord in", "app.containers.NewAuthModal.steps.Password.password": "Wachtwoord", "app.containers.NewAuthModal.steps.Password.rememberMe": "Ingel<PERSON>d blijven", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "<PERSON>et selecteren als u een openbare computer gebruikt", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON><PERSON> k<PERSON>.", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "<PERSON>a nu verder met je de<PERSON><PERSON>.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Je identiteit is geverifieerd. Je bent nu een volwaardig lid van de community op dit platform.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Je bent nu geverifieerd!", "app.containers.NewAuthModal.steps.close": "Sluit", "app.containers.NewAuthModal.steps.continue": "Ga verder", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Waar ben je in geïnteresseerd?", "app.containers.NewAuthModal.youCantParticipate": "Je kunt niet deelnemen", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {geen ongelezen meldingen} one {1 ongelezen melding} other {# ongelezen meldingen}}", "app.containers.NotificationMenu.adminRightsReceived": "Je bent nu een beheerder van het platform", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Je reactie op \"{postTitle}\" is verwijderd door een platformbeheerder omdat deze\n      {reasonCode, select, irrelevant {irrelevant is} inappropriate {ongepast is} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} je uitnodiging voor co-sponsorschap geaccepteerd", "app.containers.NotificationMenu.deletedUser": "Onbekende auteur", "app.containers.NotificationMenu.error": "Kan meldingen niet laden", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} heeft een interne reactie gegeven op een aan jou toegewezen bijdrage", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} heeft een interne reactie gegeven op een bijdrage waarop je intern hebt gereageerd", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} heeft een interne reactie gegeven op een bijdrage in een project dat je beheert", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} heeft een interne reactie gegeven op een niet-toegewezen bijdrage in een onbeheerd project", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} heeft gereageerd op je interne reactie", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} je uitgenodigd om een bijdrage te sponsoren", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} je uitgenodigd om een idee te sponsoren", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} heeft je uitgenodigd om een voorstel te steunen", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} je uitgenodigd om een kwestie te sponsoren", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} je uitgenodigd om een optie mede te sponsoren", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} nodigde je uit om een petitie te sponsoren", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} je uitgenodigd om een project te sponsoren", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} heeft je uitgenodigd om een voorstel te steunen", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} je uitgenodigd om een vraag mee te sponsoren", "app.containers.NotificationMenu.loadMore": "<PERSON>ad meer...", "app.containers.NotificationMenu.loading": "Meldingen laden...", "app.containers.NotificationMenu.mentionInComment": "{name} heeft u vermeld in een reactie", "app.containers.NotificationMenu.mentionInInternalComment": "{name} noemde je in een interne reactie", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{officialName} vernoemde je in een officiële update", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Je hebt je vragenlijst niet ingediend", "app.containers.NotificationMenu.noNotifications": "Je hebt nog geen meldingen", "app.containers.NotificationMenu.notificationsLabel": "Meldingen", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} gaf een officiële update over een bijdrage die je volgt", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} gaf een officiële update over een idee dat je volgt", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} gaf een officiële update over een voorstel dat je volgt", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} gaf een officiële update over een stelling die je volgt", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} gaf een officiële update over een optie die je volgt", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} gaf een officiële update over een petitie die je volgt", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} gaf een officiële update over een project dat je volgt", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} gaf een officiële update over een voorstel dat je volgt", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} gaf een officiële update over een vraag die je volgt", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} is aan jou <PERSON>", "app.containers.NotificationMenu.projectModerationRightsReceived": "Je bent nu een projectbe<PERSON><PERSON><PERSON> van {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "<PERSON><PERSON> nieuwe fase ging van start in {projectTitle}", "app.containers.NotificationMenu.projectPhaseUpcoming": "Op {phaseStartAt} gaat een nieuwe fase van start in {projectTitle}", "app.containers.NotificationMenu.projectPublished": "Er is een nieuw project gepubliceerd", "app.containers.NotificationMenu.projectReviewRequest": "{name} heeft toestemming gevraagd om het project \"{projectTitle}\" te publiceren", "app.containers.NotificationMenu.projectReviewStateChange": "{name} heeft \"{projectTitle}\" goedgekeurd voor publicatie", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "De status van {ideaTitle} is veranderd naar {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} bereikte het benodigd aantal stemmen", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} aan<PERSON><PERSON><PERSON> de uitnodiging", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} heeft gereageerd op een bijdrage die je volgt", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} heeft gere<PERSON>erd op een idee dat je volgt", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} heeft gereageerd op een voorstel dat je volgt", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} heeft gereageerd op een stelling die je volgt", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} heeft gereageerd op een optie die je volgt", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} reageerde op een petitie die je volgt", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} heeft gereageerd op een project dat je volgt", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} reageerde op een voorstel dat je volgt", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} heeft gereageerd op een vraag die je volgt", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} meldde \"{postTitle}\" als spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} heeft gereageerd op je reactie", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} duidde een reactie op '{postTitle}' aan als spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Je hebt je stemmen niet verzonden", "app.containers.NotificationMenu.votingBasketSubmitted": "Je hebt succesvol gestemd", "app.containers.NotificationMenu.votingLastChance": "Laatste kans om te stemmen voor {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} stemresultaten bekendgemaakt", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} wees {postTitle} aan jou <PERSON>", "app.containers.PasswordRecovery.emailError": "Dit lijkt geen geldige e-mail te zijn", "app.containers.PasswordRecovery.emailLabel": "E-mail", "app.containers.PasswordRecovery.emailPlaceholder": "Mijn e-mailadres", "app.containers.PasswordRecovery.helmetDescription": "<PERSON> wachtwoord wijzigen", "app.containers.PasswordRecovery.helmetTitle": "<PERSON><PERSON><PERSON><PERSON> je wachtwoord", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Als dit e-mailadres is geregistreerd op het platform, is een link voor het resetten van het wachtwoord verzonden.", "app.containers.PasswordRecovery.resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.PasswordRecovery.submitError": "We vonden geen profiel dat gelinkt is met deze e-mail. Je kan proberen om je te registeren.", "app.containers.PasswordRecovery.subtitle": "Naar welke email kunnen we een link sturen om een nieuw wachtwoord te kiezen?", "app.containers.PasswordRecovery.title": "<PERSON><PERSON><PERSON> wacht<PERSON>", "app.containers.PasswordReset.helmetDescription": "<PERSON> wachtwoord wijzigen", "app.containers.PasswordReset.helmetTitle": "Je wachtwoord opnieuw instellen", "app.containers.PasswordReset.login": "Inloggen", "app.containers.PasswordReset.passwordError": "Je wachtwoord moet minstens 8 karakters bevatten", "app.containers.PasswordReset.passwordLabel": "Wachtwoord", "app.containers.PasswordReset.passwordPlaceholder": "<PERSON><PERSON><PERSON> wa<PERSON>", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "<PERSON> wachtwoord is succesvol bijgewerkt.", "app.containers.PasswordReset.pleaseLogInMessage": "<PERSON>g alsjeblieft met je nieuwe wachtwo<PERSON> in.", "app.containers.PasswordReset.requestNewPasswordReset": "Vraag een nieuw wachtwoord aan", "app.containers.PasswordReset.submitError": "Er ging iets mis. Probeer het opnieuw.", "app.containers.PasswordReset.title": "Je wachtwoord opnieuw instellen", "app.containers.PasswordReset.updatePassword": "Bevestig wachtwoord", "app.containers.ProjectFolderCards.allProjects": "Alle projecten", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} werkt momenteel aan", "app.containers.ProjectFolderShowPage.editFolder": "Bewerk deze map", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informatie over dit project", "app.containers.ProjectFolderShowPage.metaTitle1": "Map: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON> meer", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON><PERSON> minder", "app.containers.ProjectFolderShowPage.share": "<PERSON><PERSON>", "app.containers.Projects.PollForm.document": "Document", "app.containers.Projects.PollForm.formCompleted": "Bedankt voor het invullen!", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Je hebt deze peiling al ingevuld.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Deze peiling kan alleen ingevuld worden als deze fase actief is.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "<PERSON><PERSON><PERSON>, je hebt geen rechten om deel te nemen aan deze peiling.", "app.containers.Projects.PollForm.pollDisabledNotPossible": "<PERSON><PERSON> is <PERSON><PERSON> onmo<PERSON>jk om deel te nemen aan deze peiling.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "De peiling is niet langer be<PERSON>, omdat het project niet langer actief is.", "app.containers.Projects.PollForm.sendAnswer": "Verzenden", "app.containers.Projects.a11y_phase": "Fase {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "<PERSON><PERSON><PERSON> van de fases", "app.containers.Projects.a11y_titleInputs": "Alle bijdragen voor dit project", "app.containers.Projects.a11y_titleInputsPhase": "Alle bijdragen voor deze fase", "app.containers.Projects.accessRights": "Toegangsrechten", "app.containers.Projects.addedToBasket": "<PERSON><PERSON> je mandje <PERSON>d", "app.containers.Projects.allocateBudget": "Maak je keuze", "app.containers.Projects.archived": "Gearchiveerd", "app.containers.Projects.basketSubmitted": "Je mandje is ingediend!", "app.containers.Projects.contributions": "Onderwerpen", "app.containers.Projects.createANewPhase": "Maak een nieuwe fase aan", "app.containers.Projects.currentPhase": "Huidige fase", "app.containers.Projects.document": "Document", "app.containers.Projects.editProject": "Bewerk dit project", "app.containers.Projects.emailSharingBody": "Wat vind je van dit voorstel? Breng je stem uit en deel de discussie op {initiativeUrl} om je mening te laten horen!", "app.containers.Projects.emailSharingSubject": "Steun mijn voorstel: {initiativeTitle}.", "app.containers.Projects.endedOn": "Beëindigd op {date}", "app.containers.Projects.events": "Activiteiten", "app.containers.Projects.header": "Projecten", "app.containers.Projects.ideas": "Ideeën", "app.containers.Projects.information": "Info", "app.containers.Projects.initiatives": "Voorstellen", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Bekijk het document", "app.containers.Projects.invisibleTitlePhaseAbout": "Over deze fase", "app.containers.Projects.invisibleTitlePoll": "<PERSON><PERSON><PERSON><PERSON> aan de peiling", "app.containers.Projects.invisibleTitleSurvey": "<PERSON><PERSON><PERSON><PERSON> aan de enqu<PERSON>", "app.containers.Projects.issues": "{<PERSON><PERSON><PERSON>, select, gent {<PERSON><PERSON><PERSON><PERSON>} other {Reacties}}", "app.containers.Projects.liveDataMessage": "Je bekijkt realtime data. Het aantal deelnemers wordt voortdurend geüpdatet voor beheerders. Houd er rekening mee dat voor gewone gebruikers een gecached aantal wordt getoond, waardoor de cijfers licht kunnen afwijken.", "app.containers.Projects.location": "Locatie:", "app.containers.Projects.manageBasket": "<PERSON><PERSON><PERSON>", "app.containers.Projects.meetMinBudgetRequirement": "<PERSON><PERSON><PERSON> aan het minimumbudget om je mandje in te dienen.", "app.containers.Projects.meetMinSelectionRequirement": "Je moet aan de vereiste selectie voldoen om je mandje te kunnen indienen.", "app.containers.Projects.metaTitle1": "Project: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Minimumbudget vereist", "app.containers.Projects.myBasket": "Mand", "app.containers.Projects.navPoll": "Peiling", "app.containers.Projects.navSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.newPhase": "Nieuwe fase", "app.containers.Projects.nextPhase": "Volgende fase", "app.containers.Projects.noEndDate": "<PERSON><PERSON>", "app.containers.Projects.noItems": "Je hebt nog geen items geselecteerd", "app.containers.Projects.noPastEvents": "Geen afgelopen activiteiten om te tonen", "app.containers.Projects.noPhaseSelected": "<PERSON><PERSON> fase geselecteerd", "app.containers.Projects.noUpcomingOrOngoingEvents": "<PERSON><PERSON> <PERSON><PERSON>jn <PERSON>eel geen aanko<PERSON> of gestarte activiteiten.", "app.containers.Projects.offlineVotersTooltip": "Onder dit aantal worden offline stemmen niet meege<PERSON>end.", "app.containers.Projects.options": "Opties", "app.containers.Projects.participants": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.participantsTooltip4": "Dit aantal weerspiegelt ook anoniem ingevulde enquêtes. Anoniem ingevulde enquêtes zijn mogelijk als enquêtes voor iedereen toegankelijk zijn (zie het tabblad {accessRightsLink} van dit project).", "app.containers.Projects.pastEvents": "Afgelopen activiteiten", "app.containers.Projects.petitions": "Petities", "app.containers.Projects.phases": "Fases", "app.containers.Projects.previousPhase": "Vorige fase", "app.containers.Projects.project": "Project", "app.containers.Projects.projectTwitterMessage": "Laat je stem horen! <PERSON><PERSON><PERSON> deel aan {projectName} | {orgName}", "app.containers.Projects.projects": "Projecten", "app.containers.Projects.proposals": "Voorstellen", "app.containers.Projects.questions": "Vragen", "app.containers.Projects.readLess": "<PERSON><PERSON>", "app.containers.Projects.readMore": "<PERSON><PERSON> meer", "app.containers.Projects.removeItem": "<PERSON><PERSON> ver<PERSON>", "app.containers.Projects.requiredSelection": "Vereiste selectie", "app.containers.Projects.reviewDocument": "Bekijk het document", "app.containers.Projects.seeTheContributions": "Bekijk de onderwerpen", "app.containers.Projects.seeTheIdeas": "Bekijk de ideeën", "app.containers.Projects.seeTheInitiatives": "Bekijk de initiatieven", "app.containers.Projects.seeTheIssues": "{<PERSON><PERSON><PERSON>, select, gent {<PERSON><PERSON><PERSON> verhalen} other {<PERSON><PERSON>jk de reacties}}", "app.containers.Projects.seeTheOptions": "Bekijk de opties", "app.containers.Projects.seeThePetitions": "Bekijk de petities", "app.containers.Projects.seeTheProjects": "Bekijk de projecten", "app.containers.Projects.seeTheProposals": "Bekijk de voorstellen", "app.containers.Projects.seeTheQuestions": "Bekijk de vragen", "app.containers.Projects.seeUpcomingEvents": "Komende evenementen bekijken", "app.containers.Projects.share": "<PERSON><PERSON>", "app.containers.Projects.shareThisProject": "Deel dit project", "app.containers.Projects.submitMyBasket": "<PERSON><PERSON><PERSON>", "app.containers.Projects.survey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.takeThePoll": "<PERSON><PERSON><PERSON><PERSON> aan de peiling", "app.containers.Projects.takeTheSurvey": "<PERSON><PERSON><PERSON><PERSON> aan de enqu<PERSON>", "app.containers.Projects.timeline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.upcomingAndOngoingEvents": "Aankomende en gestarte activiteiten", "app.containers.Projects.upcomingEvents": "Aankomende activiteiten", "app.containers.Projects.whatsAppMessage": "{projectName} | van het participatieplatform van {orgName}", "app.containers.Projects.yourBudget": "Totale budget", "app.containers.ProjectsIndexPage.metaDescription": "Verken alle lopende projecten van {orgName} en ontdek hoe jij kan deel<PERSON>.\nBespreek die lokale projecten die voor jou het belangrijkst zijn.", "app.containers.ProjectsIndexPage.metaTitle1": "Projecten • {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projecten", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "{tenant<PERSON><PERSON>, select, <PERSON><PERSON> {Ik schrijf me in voor dit Stadsgesprek} other {Ik geef me op}}", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "{tenant<PERSON><PERSON>, select, Almere {Graag eerst {signInLink} of {signUpLink} om je in te schrijven voor het Stadsgesprek} other {Graag eerst {signInLink} of {signUpLink} om je aan te melden}}", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "<PERSON><PERSON><PERSON> is momenteel niet mogelijk voor deze activiteit.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "in te loggen", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "aan te melden", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "{<PERSON><PERSON><PERSON>, select, <PERSON><PERSON> {Ik wil me u<PERSON><PERSON><PERSON><PERSON><PERSON>} other {Ik wil mijn aanmelding intrekken}}", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {nog geen aanmeldingen} one {# aanmelding} other {# aanmeldingen}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Waarschuwing: De ingesloten enquête kan toegankelijkheidsproblemen veroorzaken voor gebruikers van screenreaders. <PERSON><PERSON> je problemen on<PERSON>, neem dan contact op met de platformbeheerder om een link naar de enquête op het oorspronkelijke platform te ontvangen. Je kunt ook andere manieren aanvragen om de enquête in te vullen.", "app.containers.ProjectsShowPage.process.survey.survey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Om te weten of je kunt deelnemen aan deze enquête, moet je eerst even {logInLink} op het platform.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Aan deze enquête kan alleen worden deelgenomen wanneer de desbetreffende fase van de tijdlijn actief is.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "<PERSON><PERSON><PERSON> {completeRegistrationLink} om de enquête in te vullen.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "<PERSON><PERSON><PERSON>, je hebt geen rechten om deel te nemen aan deze enquête.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Om te kunnen deel<PERSON>men aan deze enquête moet je je identiteit verifiëren. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "De vragenlijst is niet langer besch<PERSON>, omdat het project niet langer actief is.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "voltooi registratie", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "inloggen", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "registreer", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Verifieer je account nu.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Alleen bepaalde gebruikers kunnen dit document bekijken. <PERSON><PERSON><PERSON> e<PERSON>t {signUpLink} of {logInLink}.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Dit document kan alleen bekeken worden als deze fase actief is.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "{completeRegistrationLink} om het document te bekijken.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "<PERSON><PERSON><PERSON> heb je niet de rechten om dit document te bekijken.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Het bekijken van dit document vereist verificatie van je account. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Het document is niet meer besch<PERSON>, omdat dit project niet meer actief is.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 keuze} other {# keuzes}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "Het percentage deelnemers dat deze optie koos.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "Het percentage van het totale aantal stemmen dat deze optie heeft gekregen.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Kosten:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Toon meer", "app.containers.ReactionControl.a11y_likesDislikes": "Totaal aantal likes: {likesCount}, totaal aantal dislikes: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Je hebt je dislike voor deze bijdrage met succes gean<PERSON><PERSON><PERSON>.", "app.containers.ReactionControl.cancelLikeSuccess": "Je hebt je like voor deze bij<PERSON>ge met succes gean<PERSON><PERSON><PERSON>.", "app.containers.ReactionControl.dislikeSuccess": "Je hebt deze bijdrage met succes een dislike gegeven.", "app.containers.ReactionControl.likeSuccess": "Je hebt deze bijdrage met succes geliked.", "app.containers.ReactionControl.reactionErrorSubTitle": "Door een fout kon je reactie niet worden geregistreerd. <PERSON><PERSON><PERSON> het over een paar minuten opnieuw.", "app.containers.ReactionControl.reactionSuccessTitle": "Je reactie is succesvol geregistreerd!", "app.containers.ReactionControl.vote": "<PERSON><PERSON>", "app.containers.ReactionControl.voted": "Gestemd", "app.containers.SearchInput.a11y_cancelledPostingComment": "Reactie pla<PERSON>en gean<PERSON>.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} reacties zijn geladen.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# gebeurtenissen zijn geladen} one {# gebeurtenis is geladen} other {# gebeurtenissen zijn geladen}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# resultaten zijn geladen} one {# resultaat is geladen} other {# resultaten zijn geladen}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# zoekresultaten zijn geladen} one {# zoekresultaat is geladen} other {# zoekresultaten zijn geladen}}.", "app.containers.SearchInput.removeSearchTerm": "Zoekterm verwijderen", "app.containers.SearchInput.searchAriaLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchPlaceholder": "<PERSON><PERSON>", "app.containers.SearchInput.searchTerm": "Zoekterm: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect is de door de Franse staat voorgestelde oplossing om de toegang tot meer dan 700 onlinediensten te beveiligen en te vereenvoudigen.", "app.containers.SignIn.or": "Of", "app.containers.SignIn.signInError": "De ingevoerde informatie is niet correct. <PERSON><PERSON> op 'Wachtwoord vergeten' om je wachtwoord opnieuw in te stellen.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Gebruik FranceConnect om in te loggen, je aan te melden of jouw account te verifiëren.", "app.containers.SignIn.whatIsFranceConnect": "Wat is France Connect?", "app.containers.SignUp.adminOptions2": "Voor beheerders en projectmanagers", "app.containers.SignUp.backToSignUpOptions": "Ga terug naar de registratieopties", "app.containers.SignUp.continue": "Ga verder", "app.containers.SignUp.emailConsent": "Door je te registreren, ga je ermee akkoord e-mails van dit platform te ontvangen. Je kunt aanduiden welke e-mails je wil ontvangen op je profielpagina.", "app.containers.SignUp.emptyFirstNameError": "Vul je voornaam in", "app.containers.SignUp.emptyLastNameError": "Vul je achternaam in", "app.containers.SignUp.firstNamesLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.goToLogIn": "Heb je al een account? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "{tenant<PERSON><PERSON>, select, veere {Ik heb {link} gelezen en ik ga akkoord met het verwerken van mijn persoonsgegevens} other {Ik heb {link} gelezen en ga ermee akkoord}}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "{tenant<PERSON><PERSON>, select, veere {Ik heb {link} gelezen en ga ermee akkoord} other {Ik heb {link} gelezen en ga ermee akkoord}}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "Ik ga ermee akkoord dat de gegevens worden gebruikt op mitgestalten.wien.gv.at. Meer informatie vindt u op {link}.", "app.containers.SignUp.invitationErrorText": "Je uitnodiging is verlopen of al gebruikt. Als je de uitnodigingslink al hebt gebruikt om een account aan te maken, probeer dan in te loggen. Registreer je anders om een nieuw account aan te maken.", "app.containers.SignUp.lastNameLabel": "Achternaam", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Volg je favoriete gebieden om hiervan op de hoogte te blijven:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Volg je favoriete tags om hiervan op de hoogte te blijven:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Accepteer ons privacybeleid om door te gaan", "app.containers.SignUp.signUp2": "Registreren", "app.containers.SignUp.skip": "<PERSON><PERSON> deze stap over", "app.containers.SignUp.tacError": "Het aanva<PERSON>en van onze gebruiksvoorwaarden is nodig om verder te gaan", "app.containers.SignUp.thePrivacyPolicy": "het privacybeleid", "app.containers.SignUp.theTermsAndConditions": "de gebruiksvoorwaarden", "app.containers.SignUp.unknownError": "Er ging iets mis. Probeer het opnieuw.", "app.containers.SignUp.viennaConsentEmail": "E-mailadres", "app.containers.SignUp.viennaConsentFirstName": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.viennaConsentFooter": "U kunt uw profielgegevens wijzigen nadat u zich heeft aangemeld. Als u al een account heeft met hetzelfde e-mailadres op mitgestalten.wien.gv.at, wordt deze gekoppeld aan uw huidige account.", "app.containers.SignUp.viennaConsentHeader": "De volgende gegevens worden verzonden:", "app.containers.SignUp.viennaConsentLastName": "Achternaam", "app.containers.SignUp.viennaConsentUserName": "Gebruikersnaam", "app.containers.SignUp.viennaDataProtection": "het <PERSON><PERSON><PERSON><PERSON> van <PERSON>", "app.containers.SiteMap.contributions": "Onderwerpen", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "{<PERSON><PERSON><PERSON>, select, gent {<PERSON><PERSON><PERSON><PERSON>} other {Reacties}}", "app.containers.SiteMap.options": "Opties", "app.containers.SiteMap.projects": "Projecten", "app.containers.SiteMap.questions": "Vragen", "app.containers.SpamReport.buttonSave": "Rapporteer", "app.containers.SpamReport.buttonSuccess": "Succes", "app.containers.SpamReport.inappropriate": "He<PERSON> is ongepast of aanstootgevend", "app.containers.SpamReport.messageError": "Er is een fout opgetreden bij het verzenden, probeer het a.j.b. opnieuw.", "app.containers.SpamReport.messageSuccess": "Je melding is verstuurd", "app.containers.SpamReport.other": "Andere reden", "app.containers.SpamReport.otherReasonPlaceholder": "Beschrijving", "app.containers.SpamReport.wrong_content": "Dit hoort hier niet thuis", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> de profielfoto", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Je stemmen op voorstellen waarover nog gestemd kan worden, worden verwijderd. Stemmen op voorstellen waarvan de stemperiode is gesloten worden niet verwijderd.", "app.containers.UsersEditPage.addPassword": "<PERSON><PERSON>g wachtwoord toe", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Om deel te nemen aan projecten voor geverifieerde burgers.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Verifieer je identiteit", "app.containers.UsersEditPage.bio": "Over jou", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Je kan dit veld niet aanpassen omdat het geverifieerde informatie bevat.", "app.containers.UsersEditPage.buttonSuccessLabel": "Opgeslagen", "app.containers.UsersEditPage.cancel": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.changeEmail": "Wijzig e-mail", "app.containers.UsersEditPage.changePassword2": "W<PERSON><PERSON>zig wachtwoord", "app.containers.UsersEditPage.clickHereToUpdateVerification": "<PERSON><PERSON> hier om je verificatie bij te werken.", "app.containers.UsersEditPage.conditionsLinkText": "onze voorwaarden", "app.containers.UsersEditPage.contactUs": "Een andere reden om te vertrekken? {feedbackLink} en misschien kunnen we je helpen.", "app.containers.UsersEditPage.deleteAccountSubtext": "We vinden het jammer je te zien vertrekken.", "app.containers.UsersEditPage.deleteMyAccount": "Verwijder mijn profiel", "app.containers.UsersEditPage.deleteYourAccount": "Verwijder je profiel", "app.containers.UsersEditPage.deletionSection": "Verwijder je profiel", "app.containers.UsersEditPage.deletionSubtitle": "Dit kan niet ongedaan worden gemaakt. Alles wat je op dit platform plaatste, wordt anoniem gemaakt. Als je het ook wil verwijderen, kan je ons <NAME_EMAIL>.", "app.containers.UsersEditPage.email": "E-mail", "app.containers.UsersEditPage.emailEmptyError": "<PERSON><PERSON><PERSON> een email<PERSON>res in", "app.containers.UsersEditPage.emailInvalidError": "<PERSON><PERSON><PERSON> een email<PERSON><PERSON> met de juiste vorm in, bijvoorbeeld <EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Laat het ons weten", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/AWSAgY?source={url}", "app.containers.UsersEditPage.firstNames": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.firstNamesEmptyError": "<PERSON><PERSON><PERSON> voornamen in", "app.containers.UsersEditPage.h1": "Je accountinformatie", "app.containers.UsersEditPage.h1sub": "<PERSON><PERSON><PERSON><PERSON> je profiel", "app.containers.UsersEditPage.image": "Avatar-afbeelding", "app.containers.UsersEditPage.imageDropzonePlaceholder": "<PERSON><PERSON> hier om een profielafbeelding toe te voegen (max. 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Alle instellingen voor jouw profiel", "app.containers.UsersEditPage.language": "Taal", "app.containers.UsersEditPage.lastName": "Achternaam", "app.containers.UsersEditPage.lastNameEmptyError": "V<PERSON>r achternamen in", "app.containers.UsersEditPage.loading": "Bezig met laden...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Je kunt je e-mail of wachtwoord hier wijzigen.", "app.containers.UsersEditPage.loginCredentialsTitle": "Inloggegevens", "app.containers.UsersEditPage.messageError": "We konden je profiel niet opslaan. <PERSON>beer het later opnieuw <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Je profielgegevens zijn opgeslagen.", "app.containers.UsersEditPage.metaDescription": "Dit is de instellingen pagina voor het profiel van {firstName} {lastName} op het online participatieplatform van {tenantName}. Hier kan je je identiteit verifiëren, je profielinformatie aanpassen, je profiel verwijderen en je e-mailvoorkeuren aanpassen.", "app.containers.UsersEditPage.metaTitle1": "Profielinstellingenpa<PERSON><PERSON> van {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "<PERSON><PERSON> je op deze knop klikt, wordt je profiel definitief verwijderd.", "app.containers.UsersEditPage.noNameWarning2": "Je naam wordt momenteel op het platform weergegeven als: \"{displayName}\" omdat je je naam niet hebt ingevuld. Dit is een automatisch gegenereerde naam. Als je deze wilt wijzigen, vul dan hieronder je naam in.", "app.containers.UsersEditPage.notificationsSubTitle": "Wat voor soort e-mailmeldingen wil je ontvangen?", "app.containers.UsersEditPage.notificationsTitle": "E-mail notificaties", "app.containers.UsersEditPage.password": "<PERSON><PERSON> een nieuw wachtwoord", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "<PERSON>oer een wachtwoord in dat minimaal {minimumPasswordLength} tekens lang is", "app.containers.UsersEditPage.passwordAddSection": "Voeg een wachtwoord toe", "app.containers.UsersEditPage.passwordAddSubtitle2": "<PERSON><PERSON> een wachtwoord in en log gemakkelijk in op het platform, zonder telkens je e-mail te moeten bevestigen.", "app.containers.UsersEditPage.passwordChangeSection": "<PERSON><PERSON><PERSON><PERSON> uw wachtwoord", "app.containers.UsersEditPage.passwordChangeSubtitle": "Bevestig uw huidige wachtwoord en verander het in een nieuw wachtwoord.", "app.containers.UsersEditPage.privacyReasons": "Als je bezorgd bent om je privacy, kan je {conditionsLink} lezen.", "app.containers.UsersEditPage.processing": "Verzenden...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "<PERSON><PERSON>naam is vereist bij het opgeven van de achternaam", "app.containers.UsersEditPage.reasonsToStayListTitle": "Voor je gaat...", "app.containers.UsersEditPage.submit": "Wijzigingen opslaan", "app.containers.UsersEditPage.tooManyEmails": "Ontvang je teveel e-mails? Je kan je voorkeuren bepalen bij je profielinstellingen.", "app.containers.UsersEditPage.updateverification": "Is je officiële informatie veranderd? {reverifyB<PERSON>on}", "app.containers.UsersEditPage.user": "<PERSON><PERSON> wil je per e-mail op de hoogte worden gebracht?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Je kan deelnemen aan projecten die alleen toegankelijk zijn voor geverifieerde burgers.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Je bent een geverifieerde burger", "app.containers.UsersEditPage.verifyNow": "Verifieer je nu", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Download je antwoorden (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {geen likes} one {1 like} other {# likes}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Bijdrage waarop deze reactie is gepost:", "app.containers.UsersShowPage.areas": "Gebieden", "app.containers.UsersShowPage.commentsWithCount": "Reacties ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Bewerk mijn profiel", "app.containers.UsersShowPage.emptyInfoText": "Je volgt geen items van het opgegeven filter hierboven.", "app.containers.UsersShowPage.eventsWithCount": "Evenementen ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Volgen ({followingCount})", "app.containers.UsersShowPage.inputs": "Bijdragen", "app.containers.UsersShowPage.invisibleTitlePostsList": "Alle bijdrage die door deze deelnemer zijn ingediend", "app.containers.UsersShowPage.invisibleTitleUserComments": "Alle reacties die door deze gebruiker zijn gepla<PERSON>t", "app.containers.UsersShowPage.loadMore": "<PERSON><PERSON> <PERSON>", "app.containers.UsersShowPage.loadMoreComments": "Meer reacties laden", "app.containers.UsersShowPage.loadingComments": "Reacties laden...", "app.containers.UsersShowPage.loadingEvents": "Evenementen aan het laden...", "app.containers.UsersShowPage.memberSince": "Lid sinds {date}", "app.containers.UsersShowPage.metaTitle1": "<PERSON><PERSON><PERSON><PERSON><PERSON> van {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Deze persoon heeft nog geen reacties geplaatst.", "app.containers.UsersShowPage.noCommentsForYou": "<PERSON>r zijn hier nog geen reacties.", "app.containers.UsersShowPage.noEventsForUser": "Je hebt nog geen evenementen bijgewoond.", "app.containers.UsersShowPage.postsWithCount": "Bijdragen ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Projectmappen", "app.containers.UsersShowPage.projects": "Projecten", "app.containers.UsersShowPage.proposals": "Voorstellen", "app.containers.UsersShowPage.seePost": "<PERSON><PERSON>", "app.containers.UsersShowPage.surveyResponses": "Reacties ({responses})", "app.containers.UsersShowPage.topics": "Tags", "app.containers.UsersShowPage.tryAgain": "Er ging iets fout, probeer het later opnieuw.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Dit is de profielpagina van {firstName} {lastName} op het online participatieplatform van {orgName}. Hier vind je een overzicht van al hun bijdragen.", "app.containers.VoteControl.close": "Sluiten", "app.containers.VoteControl.voteErrorTitle": "Er ging iets mis", "app.containers.admin.ContentBuilder.default": "standaard", "app.containers.admin.ContentBuilder.imageTextCards": "Afbeelding- en tekstkaarten", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info & accordeons", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 kolom", "app.containers.admin.ContentBuilder.projectDescription": "Projectbeschrijving", "app.containers.app.navbar.admin": "Beheer platform", "app.containers.app.navbar.allProjects": "Alle projecten", "app.containers.app.navbar.ariaLabel": "Primair", "app.containers.app.navbar.closeMobileNavMenu": "Menu mobiele navigatie sluiten", "app.containers.app.navbar.editProfile": "<PERSON><PERSON> instelling<PERSON>", "app.containers.app.navbar.fullMobileNavigation": "Volledig mobiele", "app.containers.app.navbar.logIn": "Inloggen", "app.containers.app.navbar.logoImgAltText": "{orgName} Home", "app.containers.app.navbar.myProfile": "Mijn activiteit", "app.containers.app.navbar.search": "<PERSON><PERSON>", "app.containers.app.navbar.showFullMenu": "Toon volledig menu", "app.containers.app.navbar.signOut": "Afmelden", "app.containers.eventspage.errorWhenFetchingEvents": "Er is een fout opgetreden. Probeer de pagina opnieuw te laden.", "app.containers.eventspage.events": "Activiteiten", "app.containers.eventspage.eventsPageDescription": "Bekijk alle activiteiten die op het participatieplatform van {orgName} zijn geplaatst.", "app.containers.eventspage.eventsPageTitle1": "Activiteiten | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projecten", "app.containers.eventspage.noPastEvents": "Geen afgelopen activiteiten om te tonen", "app.containers.eventspage.noUpcomingOrOngoingEvents": "<PERSON><PERSON> <PERSON><PERSON>jn <PERSON>eel geen aanko<PERSON> of gestarte activiteiten.", "app.containers.eventspage.pastEvents": "Afgelopen activiteiten", "app.containers.eventspage.upcomingAndOngoingEvents": "Aankomende en gestarte activiteiten", "app.containers.footer.accessibility-statement": "Toegankelijkheidsverklaring", "app.containers.footer.ariaLabel": "Secundair", "app.containers.footer.cookie-policy": "Cookiebeleid", "app.containers.footer.cookieSettings": "<PERSON>ie-instelling<PERSON>", "app.containers.footer.feedbackEmptyError": "Dit veld mag niet leeg zijn.", "app.containers.footer.poweredBy": "Mogelijk gemaakt door", "app.containers.footer.privacy-policy": "Privacybeleid", "app.containers.footer.siteMap": "Sitemap", "app.containers.footer.terms-and-conditions": "Gebruiksvoorwaarden", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "<PERSON><PERSON><PERSON>", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON>, ik wil de pagina verlaten", "app.containers.ideaHeading.editForm": "Formulier bewerken", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Weet je zeker dat je de pagina wilt verlaten?", "app.containers.ideaHeading.leaveIdeaForm": "Ideeënformuli<PERSON> verlaten", "app.containers.ideaHeading.leaveIdeaText": "Je reacties worden niet opgeslagen.", "app.containers.landing.cityProjects": "Projecten", "app.containers.landing.completeProfile": "Vul je profiel verder aan", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON>, {firstName}. Het is tijd om je profiel aan te vullen.", "app.containers.landing.createAccount": "Registreren", "app.containers.landing.defaultSignedInMessage": "{orgName} luistert naar je. Laat je stem horen!", "app.containers.landing.doItLater": "Ik doe het later", "app.containers.landing.new": "<PERSON><PERSON>w", "app.containers.landing.subtitleCity": "Welkom op het participatieplatform van {orgName}", "app.containers.landing.titleCity": "Laten we samen de toe<PERSON>t van {orgName} vormgeven", "app.containers.landing.twitterMessage": "Stem voor {ideaTitle} op", "app.containers.landing.upcomingEventsWidgetTitle": "Aankomende en gestarte activiteiten", "app.containers.landing.userDeletedSubtitle": "Je kan op elk moment een nieuw profiel aanmaken of {contactLink} om ons te laten weten wat we kunnen verbeteren.", "app.containers.landing.userDeletedSubtitleLinkText": "stuur ons een bericht", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/AWSAgY?source={url}", "app.containers.landing.userDeletedTitle": "Je account is verwijderd.", "app.containers.landing.userDeletionFailed": "Er ging iets fout bij het verwijderen van je profiel. We hebben hiervan een melding ontvangen en lossen het zo snel mogelijk op. Probeer het later nog eens.", "app.containers.landing.verifyNow": "Verifieer je nu", "app.containers.landing.verifyYourIdentity": "Laat je stem harder weerklinken, verifieer je identiteit", "app.containers.landing.viewAllEventsText": "Bekijk alle activiteiten", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Terug naar map", "app.errors.after_end_at": "De begindatum start na de einddatum", "app.errors.avatar_carrierwave_download_error": "Kon de profielafbeelding niet downloaden.", "app.errors.avatar_carrierwave_integrity_error": "Het formaa<PERSON> van de profielafbeelding is niet toe<PERSON>.", "app.errors.avatar_carrierwave_processing_error": "We konden de profielafbeelding niet verwerken.", "app.errors.avatar_extension_blacklist_error": "Het bestandsformaa<PERSON> van de profielbeelding is niet toe<PERSON>. Toegestane formaten zijn: jpg, jpeg, gif en png.", "app.errors.avatar_extension_whitelist_error": "Het bestandsformaa<PERSON> van de profielbeelding is niet toe<PERSON>. Toegestane formaten zijn: jpg, jpeg, gif en png.", "app.errors.banner_cta_button_multiloc_blank": "<PERSON><PERSON>r een knoptekst in.", "app.errors.banner_cta_button_url_blank": "<PERSON><PERSON><PERSON> een link in", "app.errors.banner_cta_button_url_url": "<PERSON><PERSON><PERSON> een geldige link in. Zorg dat deze begint met 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "<PERSON><PERSON>r een knoptekst in.", "app.errors.banner_cta_signed_in_url_blank": "<PERSON><PERSON><PERSON> een link in.", "app.errors.banner_cta_signed_in_url_url": "<PERSON><PERSON><PERSON> een geldige link in. Zorg dat deze begint met 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "<PERSON><PERSON>r een knoptekst in.", "app.errors.banner_cta_signed_out_url_blank": "<PERSON><PERSON><PERSON> een link in.", "app.errors.banner_cta_signed_out_url_url": "<PERSON><PERSON><PERSON> een geldige link in. Zorg dat deze begint met 'https://'.", "app.errors.base_includes_banned_words": "Het kan zijn dat je een of meer woorden hebt gebruikt die als vulgair taalgebruik worden beschouwd. Wijzig je tekst om eventueel vulgair taalgebruik te verwijderen.", "app.errors.body_multiloc_includes_banned_words": "De beschrijving bevat woorden die als ongepast worden beschouwd.", "app.errors.bulk_import_idea_not_valid": "Het resulterende idee is niet geldig: {value}.", "app.errors.bulk_import_image_url_not_valid": "Er kan geen afbeelding worden gedownload van {value}. Zorg ervoor dat de URL geldig is en eindigt met een bestandsextensie zoals .png of .jpg. Dit probleem doet zich voor in de rij met ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "<PERSON><PERSON><PERSON><PERSON><PERSON> met een ontbrekend coördinaat in {value}. Dit probleem doet zich voor in de rij met ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "<PERSON><PERSON><PERSON><PERSON><PERSON> met een niet-numerieke coördinaat in {value}. Dit probleem doet zich voor in de rij met ID {row}.", "app.errors.bulk_import_malformed_pdf": "Het geüploade PDF-bestand lijkt misvormd te zijn. Probeer de PDF opnieuw te exporteren vanuit je bron en dan opnieuw te uploaden.", "app.errors.bulk_import_maximum_ideas_exceeded": "Het maximum van {value} ideeën is overschreden.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Het maximum van {value} pagina's in een PDF is overschreden.", "app.errors.bulk_import_not_enough_pdf_pages": "De geüploade PDF heeft niet genoeg pagina's - hij moet minstens evenveel pagina's hebben als de gedownloade sjabloon.", "app.errors.bulk_import_publication_date_invalid_format": "<PERSON><PERSON><PERSON> met on<PERSON><PERSON><PERSON> datumformaat \"{value}\". Gebruik het formaat \"DD-MM-JJJJ\".", "app.errors.cannot_contain_ideas": "Er {ideasCount, plural, one {is <PERSON><PERSON> idee} other {zijn {ideasCount} ideeën}} in deze fase en de participatiemethode die je koos ondersteunt geen ideeën. <PERSON><PERSON><PERSON> daarom {ideasCount, plural, one {het idee} other {de ideeën}} eerst uit deze fase te verwijderen en opnieuw te proberen.", "app.errors.cant_change_after_first_response": "Dit kan niet meer worden gewijzigd omdat er al mensen deelnamen", "app.errors.category_name_taken": "<PERSON><PERSON> <PERSON><PERSON><PERSON> al een categorie met deze naam", "app.errors.confirmation_code_expired": "Code verlopen. Vraag een nieuwe code aan.", "app.errors.confirmation_code_invalid": "Ongeldige bevestigingscode. Controleer je e-mail voor de juiste code of probeer 'Verstuur een nieuwe code'", "app.errors.confirmation_code_too_many_resets": "U heeft de bevestigingscode te vaak opnieuw verzonden. Neem contact met ons op om een uitnodigingscode te ontvangen.\n", "app.errors.confirmation_code_too_many_retries": "Je hebt het te vaak gep<PERSON>. Verstuur de code opnieuw of probeer je e-mailadres te wijzigen.", "app.errors.email_already_active": "Het e-mailadres {value} in rij {row} behoort tot een geregistreerde gebruiker\n\n", "app.errors.email_already_invited": "Het e-mailadres {value} in rij {row} kreeg e<PERSON>er al een uitnodiging", "app.errors.email_blank": "Dit mag niet leeg zijn", "app.errors.email_domain_blacklisted": "<PERSON><PERSON><PERSON> een ander e-maildomein te gebruiken om te registreren.", "app.errors.email_invalid": "<PERSON><PERSON><PERSON> een geldig e-mailadres te gebruiken.", "app.errors.email_taken": "<PERSON>r bestaat al een profiel met deze e-mail. Probeer je in te loggen.", "app.errors.email_taken_by_invite": "{value} wordt al gebruikt door een bestaande uitnodiging. Controleer je spamfolder of contacteer {supportEmail} als je je uitnodiging niet te<PERSON>vindt.", "app.errors.emails_duplicate": "In de rij(en): {rows} s<PERSON><PERSON> of meer dubbele waarden voor het e-mailadres {value}", "app.errors.extension_whitelist_error": "Het formaat van het bestand dat je probeerde toe te voegen, wordt niet ondersteund.", "app.errors.file_extension_whitelist_error": "Het formaat van het bestand dat je probeerde toe te voegen, wordt niet ondersteund.", "app.errors.first_name_blank": "Dit mag niet leeg zijn", "app.errors.generics.blank": "Dit mag niet leeg zijn.", "app.errors.generics.invalid": "Dit ziet er niet uit als een geldige waarde", "app.errors.generics.taken": "Deze e-mail bestaat al. Een ander profiel is eraan gek<PERSON>d.", "app.errors.generics.unsupported_locales": "Dit veld biedt geen ondersteuning voor de huidige landinstelling.", "app.errors.group_ids_unauthorized_choice_moderator": "Als projectbehee<PERSON>r, kun je enkel een e-mail verzenden naar mensen die toegang hebben tot jouw project(en)", "app.errors.has_other_overlapping_phases": "Projecten mogen geen overlappende fasen hebben.", "app.errors.invalid_email": "Het e-mailadres {value} in rij {row} is geen geldig e-mailadres", "app.errors.invalid_row": "Er trad een onbekend fout op bij het verwerken van rij {row}", "app.errors.is_not_timeline_project": "In dit project kunnen geen tijdslijnfases worden toegevoegd. ", "app.errors.key_invalid": "De sleutel kan alleen letters, cijfers en underscores (_) bevatten", "app.errors.last_name_blank": "Dit mag niet leeg zijn", "app.errors.locale_blank": "<PERSON><PERSON> een taal", "app.errors.locale_inclusion": "Kies een taal die ondersteund wordt", "app.errors.malformed_admin_value": "De waarde {value} voor de kolom 'admin' in rij {row} is niet geldig", "app.errors.malformed_groups_value": "De waarde {value} voor de kolom 'groep' in rij {row} is niet geldig", "app.errors.max_invites_limit_exceeded1": "Het aantal uitnodigingen overschrijdt de limiet van 1000.", "app.errors.maximum_attendees_greater_than1": "Het maximum aantal geregistreerden moet groter zijn dan 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Het maximum aantal geregistreerden moet groter zijn dan of gelijk aan het huidige aantal geregistreerden.", "app.errors.no_invites_specified": "Kon geen e-mailadres(sen) vinden.", "app.errors.no_recipients": "De campagne kan niet worden verzonden omdat er geen ontvangers zijn. De groep waarnaar je verstuurt, is leeg, of niemand heeft toestemming gegeven om e-mails te ontvangen.", "app.errors.number_invalid": "<PERSON>oer een geldig nummer in.", "app.errors.password_blank": "Dit mag niet leeg zijn", "app.errors.password_invalid": "Controleer uw huidige wachtwoord opnieuw.", "app.errors.password_too_short": "Het wachtwoord moet minstens 8 tekens lang zijn", "app.errors.resending_code_failed": "Er is iets misgegaan bij het verzenden van de bevestigingscode.", "app.errors.slug_taken": "Deze project-URL bestaat al. Verander de slug in iets anders.", "app.errors.tag_name_taken": "<PERSON><PERSON> <PERSON><PERSON><PERSON> al een tag met deze naam", "app.errors.title_multiloc_blank": "De titel kan niet leeg zijn.", "app.errors.title_multiloc_includes_banned_words": "De titel bevat woorden die als ongepast worden beschouwd.", "app.errors.token_invalid": "De link om je wachtwoord opnieuw in te stellen kan slechts één keer worden gebruikt en is geldig tot een uur na verzending. {passwordResetLink}.", "app.errors.too_common": "<PERSON>t wachtwoord is makkelijk te raden. <PERSON><PERSON> een sterker wachtwoord.", "app.errors.too_long": "Het wachtwoord mag maximaal 72 tekens lang zijn", "app.errors.too_short": "Het wachtwoord moet minstens 8 tekens lang zijn", "app.errors.uncaught_error": "Er is een onbekende fout opgetreden.", "app.errors.unknown_group": "De groep {value} in rij {row} is geen geldige groep", "app.errors.unknown_locale": "De {value} van de taal in rij {row} is geen geconfigureerde taal", "app.errors.unparseable_excel": "Het geselecteerde Excel-bestand kan niet worden verwerkt.", "app.errors.url": "<PERSON>oer een geldige link in. Zorg dat de link begint met https://", "app.errors.verification_taken": "Verificatie kan niet worden voltooid omdat een andere account is g<PERSON><PERSON><PERSON><PERSON> met de<PERSON><PERSON><PERSON> gege<PERSON>.", "app.errors.view_name_taken": "<PERSON><PERSON> <PERSON><PERSON><PERSON> al een inzicht met deze naam", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Ongepaste inhoud werd automatisch gedetecteerd in een post of reactie", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Inloggen met StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Aanmelden met StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Maak nu een Stadt Wien-account en gebruik één login voor meerdere digitale diensten in Wenen.", "app.modules.id_cow.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_cow.emptyFieldError": "Dit veld mag niet leeg zijn.", "app.modules.id_cow.helpAltText": "Toont waar men het serienummer-ID op een identiteitskaart vindt", "app.modules.id_cow.invalidIdSerialError": "Ongeldig ID-serienummer", "app.modules.id_cow.invalidRunError": "Ongeldige RUN", "app.modules.id_cow.noMatchFormError": "Geen match gevonden.", "app.modules.id_cow.notEntitledFormError": "<PERSON><PERSON> g<PERSON>.", "app.modules.id_cow.showCOWHelp": "Waar kan ik mijn ID-serienummer vinden ?", "app.modules.id_cow.somethingWentWrongError": "We konden je niet verifiëren omdat er iets mis ging", "app.modules.id_cow.submit": "Verzenden", "app.modules.id_cow.takenFormError": "<PERSON><PERSON> gebru<PERSON>t.", "app.modules.id_cow.verifyCow": "Verifiëren via COW", "app.modules.id_franceconnect.verificationButtonAltText": "Verifieer met FranceConnect", "app.modules.id_gent_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "Dit veld mag niet leeg zijn.", "app.modules.id_gent_rrn.gentRrnHelp": "We vragen je Rijksregisternummer op om te verifiëren dat je een inwoner bent van Gent, en ouder bent dan 14 jaar. Zo weten we dat je voldoet aan de criteria voor deelname aan dit project.", "app.modules.id_gent_rrn.invalidRrnError": "Dit rijksregisternummer is ongeldig. Gelieve te controleren op typefouten en probeer het opnieuw.", "app.modules.id_gent_rrn.noMatchFormError": "We konden geen informatie vinden over jouw rijksregisternummer.", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "<PERSON><PERSON> inwon<PERSON> van Gent mogen deelnemen aan dit project. Contacteer <EMAIL> voor meer informatie.", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Enkel personen ouder dan 14 jaar mogen deelnemen aan dit project. Contacteer <EMAIL> voor meer informatie.", "app.modules.id_gent_rrn.rrnLabel": "Rijksregisternummer", "app.modules.id_gent_rrn.rrnTooltip": "Je vindt dit nummer op de achterkant van je identiteitskaart, links bovenaan onder “Identificatienummer van het Rijksregister.” Geef 11 cijfers in zonder puntjes of streepjes.", "app.modules.id_gent_rrn.showGentRrnHelp": "Waarom hebben we je rijksregisternummer nodig?", "app.modules.id_gent_rrn.somethingWentWrongError": "We konden je niet verifiëren omdat er iets mis ging. Contacteer <EMAIL> voor meer informatie.", "app.modules.id_gent_rrn.submit": "Verzenden", "app.modules.id_gent_rrn.takenFormError": "Dit rijksregisternummer is al eens gebruikt door een ander account", "app.modules.id_gent_rrn.verifyGentRrn": "Verifieer je identiteit met je rijksregisternummer", "app.modules.id_id_card_lookup.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.emptyFieldError": "Dit veld mag niet leeg zijn.", "app.modules.id_id_card_lookup.helpAltText": "ID-kaart uitleg", "app.modules.id_id_card_lookup.invalidCardIdError": "Deze id is niet geldig.", "app.modules.id_id_card_lookup.noMatchFormError": "Geen match gevonden.", "app.modules.id_id_card_lookup.showHelp": "Waar kan ik mijn ID-serienummer vinden?", "app.modules.id_id_card_lookup.somethingWentWrongError": "We konden je niet verifiëren omdat er iets mis ging", "app.modules.id_id_card_lookup.submit": "Verzenden", "app.modules.id_id_card_lookup.takenFormError": "<PERSON><PERSON> gebru<PERSON>t.", "app.modules.id_oostende_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.emptyFieldError": "Dit mag niet leeg zijn.", "app.modules.id_oostende_rrn.invalidRrnError": "Dit rijksregisternummer is ongeldig. Gelieve te controleren op typefouten en probeer het opnieuw.", "app.modules.id_oostende_rrn.noMatchFormError": "We konden geen informatie vinden over jouw rijksregisternummer", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Enkel inwoners en tweedeverblijvers van Oostende mogen deelnemen aan dit project. Contacteer <EMAIL> voor meer informatie.", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "<PERSON><PERSON> personen die 14 jaar of ouder zijn mogen deelnemen aan dit project. Contacteer <EMAIL> voor meer informatie.", "app.modules.id_oostende_rrn.oostendeRrnHelp": "We vragen je Rijksregisternummer op om te verifiëren dat je een inwoner of tweedeverblijver bent van O<PERSON>ende, en ouder bent dan 16 jaar. Zo weten we dat je voldoet aan de criteria voor deelname aan dit project.", "app.modules.id_oostende_rrn.rrnLabel": "Rijksregisternummer", "app.modules.id_oostende_rrn.rrnTooltip": "Je vindt dit nummer op de achterkant van je identiteitskaart, links bovenaan onder “Identificatienummer van het Rijksregister”. Geef 11 cijfers in zonder puntjes of streepjes.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Waarom hebben we je rijksregisternummer nodig?", "app.modules.id_oostende_rrn.somethingWentWrongError": "We konden je niet verifiëren omdat er iets mis ging", "app.modules.id_oostende_rrn.submit": "Verzenden", "app.modules.id_oostende_rrn.takenFormError": "Dit rijksregisternummer is al eens gebruikt door een ander account", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Verifieer je identiteit met je rijksregisternummer.", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Je hebt admin rechten gekregen over de \"{folderName}\" map.", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "<PERSON><PERSON>", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Bekijk de projecten op {folderUrl} om je mening te laten horen!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | van het participatieplatform van {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | van het participatieplatform van {orgName}", "app.sessionRecording.accept": "<PERSON><PERSON>, ik accepteer", "app.sessionRecording.modalDescription1": "Om onze gebruikers beter te begrijpen, vragen we willekeurig een klein percentage van de bezoekers om hun surfsessie in detail te volgen.", "app.sessionRecording.modalDescription2": "Het enige doel van de geregistreerde gegevens is om de website te verbeteren. Je gegevens worden niet gedeeld met derden. Alle gevoelige informatie die je invoert, wordt gefilterd.", "app.sessionRecording.modalDescription3": "Accepteer je dit?", "app.sessionRecording.modalDescriptionFaq": "FAQ hier.", "app.sessionRecording.modalTitle": "Help ons deze website te verbeteren", "app.sessionRecording.reject": "<PERSON><PERSON>, ik weiger", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "<PERSON>en budgetspel opzetten", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Verzamel feedback op een document", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Maak een enquête op het platform", "app.utils.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON> poll aan<PERSON>ken", "app.utils.AdminPage.ProjectEdit.createSurveyText": "<PERSON>en externe enquête embedden", "app.utils.AdminPage.ProjectEdit.findVolunteers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Input en/of feedback verzamelen", "app.utils.AdminPage.ProjectEdit.shareInformation": "Informatie delen", "app.utils.FormattedCurrency.credits": "credits", "app.utils.FormattedCurrency.tokens": "penningen", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# credits} one {# credit} other {# credits}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokens} one {# token} other {# tokens}}", "app.utils.IdeaCards.mostDiscussed": "Meest besproken", "app.utils.IdeaCards.mostReacted": "De meeste reacties", "app.utils.IdeaCards.newest": "Nieuwste", "app.utils.IdeaCards.oldest": "Oudste", "app.utils.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.trending": "Trending", "app.utils.IdeasNewPage.contributionFormTitle": "Voeg een nieuw onderwerp toe", "app.utils.IdeasNewPage.ideaFormTitle": "Nieuwe bijdrage toevoegen", "app.utils.IdeasNewPage.initiativeFormTitle": "Nieuw initiatief toevoegen", "app.utils.IdeasNewPage.issueFormTitle1": "Nieuwe reactie toe<PERSON>n", "app.utils.IdeasNewPage.optionFormTitle": "Nieuwe optie toevoegen", "app.utils.IdeasNewPage.petitionFormTitle": "Nieuwe petitie toe<PERSON>n", "app.utils.IdeasNewPage.projectFormTitle": "Nieuw project toevoegen", "app.utils.IdeasNewPage.proposalFormTitle": "Nieuw voorstel toevoegen", "app.utils.IdeasNewPage.questionFormTitle": "Nieuwe vraag toevoegen", "app.utils.IdeasNewPage.surveyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourComment": "Bekijk je reactie", "app.utils.IdeasNewPage.viewYourContribution": "Bekijk je bijdrage", "app.utils.IdeasNewPage.viewYourIdea": "Bekijk je idee", "app.utils.IdeasNewPage.viewYourInitiative": "Bekijk je initiatief", "app.utils.IdeasNewPage.viewYourInput": "Je bijdrage bekijken", "app.utils.IdeasNewPage.viewYourIssue": "Bekijk je stelling", "app.utils.IdeasNewPage.viewYourOption": "Bekijk je optie", "app.utils.IdeasNewPage.viewYourPetition": "Bekijk je petitie", "app.utils.IdeasNewPage.viewYourProject": "Bekijk je project", "app.utils.IdeasNewPage.viewYourProposal": "Bekijk je voorstel", "app.utils.IdeasNewPage.viewYourQuestion": "Bekijk je vraag", "app.utils.Projects.sendSubmission": "Identificatiecode voor inzending naar mijn e-mail sturen", "app.utils.Projects.sendSurveySubmission": "Stuur de identificatiecode van de vragenlijst naar mijn e-mail", "app.utils.Projects.surveySubmission": "Inzending vragenlijst", "app.utils.Projects.yourResponseHasTheFollowingId": "Je inzending heeft de volgende identificatiecode: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "Als je later besluit dat je je reactie wilt laten verwijderen, neem dan contact met ons op met de volgende unieke identificatiecode:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Vul je profiel verder aan om bij deze activiteit aanwezig te kunnen zijn.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Je voldoet niet aan de vereisten om bij deze activiteit aanwezig te zijn.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Je kunt niet aanwezig zijn bij deze activiteit.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Je moet inloggen of registreren om bij deze activiteit aanwezig te zijn.", "app.utils.actionDescriptors.attendingEventNotVerified": "Je moet je account verifiëren voordat je bij deze activiteit aanwezig kunt zijn.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "Vul je profiel aan om vri<PERSON>iger te worden.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Je voldoet niet aan de voorwa<PERSON>en om vrijwilliger te worden.", "app.utils.actionDescriptors.volunteeringNotPermitted": "Het is niet mogelijk om je als vrijwilliger aan te melden.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Je moet inloggen of je registreren om je als vrijwilliger aan te melden.", "app.utils.actionDescriptors.volunteeringNotVerified": "Je moet je account verifiëren voordat je je als vrijwilliger kunt a<PERSON>den.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "<PERSON>lik op {completeRegistrationLink} om je aan te melden als vrijwill<PERSON>.", "app.utils.errors.api_error_default.in": "<PERSON><PERSON><PERSON> niet", "app.utils.errors.default.ajv_error_birthyear_required": "<PERSON><PERSON> je geb<PERSON>te<PERSON> in", "app.utils.errors.default.ajv_error_date_any": "Vul een geldige datum in", "app.utils.errors.default.ajv_error_domicile_required": "<PERSON><PERSON> je woonp<PERSON>ats in", "app.utils.errors.default.ajv_error_gender_required": "Vul je geslacht in", "app.utils.errors.default.ajv_error_invalid": "Is ongeldig", "app.utils.errors.default.ajv_error_maxItems": "Kan niet meer dan {limit, plural, one {# artikel} other {# artikelen}} bevatten", "app.utils.errors.default.ajv_error_minItems": "Moet ten minste {limit, plural, one {# artikel} other {# artikelen}}", "app.utils.errors.default.ajv_error_number_any": "Vul een geldig nummer in", "app.utils.errors.default.ajv_error_politician_required": "Vul a.u.b in of je actief bent als politicus", "app.utils.errors.default.ajv_error_required3": "Veld is verplicht: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Kan niet leeg zijn", "app.utils.errors.default.api_error_accepted": "<PERSON><PERSON> geaccepteerd worden", "app.utils.errors.default.api_error_blank": "Kan niet leeg zijn", "app.utils.errors.default.api_error_confirmation": "<PERSON><PERSON>t niet overeen", "app.utils.errors.default.api_error_empty": "Kan niet leeg zijn", "app.utils.errors.default.api_error_equal_to": "<PERSON><PERSON><PERSON> niet", "app.utils.errors.default.api_error_even": "<PERSON><PERSON> even zijn", "app.utils.errors.default.api_error_exclusion": "Is gereserveerd", "app.utils.errors.default.api_error_greater_than": "Is te klein", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Is te klein", "app.utils.errors.default.api_error_inclusion": "Is niet opgenomen in de lijst", "app.utils.errors.default.api_error_invalid": "Is ongeldig", "app.utils.errors.default.api_error_less_than": "Is te groot", "app.utils.errors.default.api_error_less_than_or_equal_to": "Is te groot", "app.utils.errors.default.api_error_not_a_number": "Is geen getal", "app.utils.errors.default.api_error_not_an_integer": "<PERSON><PERSON> een heel getal zijn", "app.utils.errors.default.api_error_other_than": "<PERSON><PERSON><PERSON> niet", "app.utils.errors.default.api_error_present": "<PERSON><PERSON> leeg zijn", "app.utils.errors.default.api_error_too_long": "Is te lang", "app.utils.errors.default.api_error_too_short": "Is te kort", "app.utils.errors.default.api_error_wrong_length": "Is de verkeerde lengte", "app.utils.errors.defaultapi_error_.odd": "<PERSON><PERSON> oneven zijn", "app.utils.notInGroup": "Je voldoet niet aan de eisen om deel te nemen.", "app.utils.participationMethod.onSurveySubmission": "Bedankt! Je antwoord is goed ontvangen.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Er kan niet meer gestemd worden, omdat deze fase niet meer actief is.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Je voldoet niet aan de vereisten om te mogen stemmen.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Je mag niet stemmen.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Je moet inloggen of je registreren om te kunnen stemmen.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Je moet je account verifiëren voordat je kunt stemmen.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b><PERSON><PERSON> indie<PERSON> van <PERSON> sloot op {endDate}.</b> <PERSON><PERSON> had in totaal <b>{maxBudget} te verdelen over {optionCount} opties.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Budget ingediend", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Budget ingediend 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Je voldoet niet aan de voorwa<PERSON>en om budgetten toe te wijzen.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Je mag geen budgetten toe<PERSON>.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Je moet inloggen of je registreren om budgetten toe te wijzen.", "app.utils.votingMethodUtils.budgetingNotVerified": "Je moet je account verifiëren voordat je budgetten kunt toewijzen.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Je budget wordt niet geteld</b> totdat je op \"Verzenden\" klikt", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Het minimaal vereiste budget is {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "<PERSON>s je klaar bent, klik je op \"Verzenden\" om je budget in te dienen.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Selecteer je voorkeursopties door op \"Toevoegen\" te klikken.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Je hebt in totaal <b>{maxBudget} te verdelen over {optionCount} opties</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, je budget is ingediend!</b> Je kan de onderstaande opties op elk moment bekijken of wijzigen voor <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, je budget is ingediend!</b> Je kunt hieronder op elk moment je opties bekijken.", "app.utils.votingMethodUtils.castYourVote": "Breng je stem uit", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Je kunt maximaal {maxVotes, plural, one {# credits} other {# credits}} per optie toevoegen.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Je kunt maximaal {maxVotes, plural, one {# punt} other {# punten}} per optie toevoegen.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Je kunt maximaal {maxVotes, plural, one {# token} other {# tokens}} per optie toevoegen.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Je kunt maximaal {maxVotes, plural, one {# stemmen} other {# stemmen}} per optie toevoegen.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON>s je klaar bent, klik je op \"Verzenden\" om je stem uit te brengen.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Selecteer je voorkeursopties door op \"Selecteer\" te klikken.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "Je hebt in totaal <b>{totalVotes, plural, one {# credit} other {# credits}} om te verdelen over {optionCount, plural, one {# optie} other {# opties}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "Je hebt in totaal <b>{totalVotes, plural, one {# punt} other {# punten}} om te verdelen over {optionCount, plural, one {# optie} other {# opties}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "Je hebt in totaal <b>{totalVotes, plural, one {# token} other {# tokens}} om te verdelen over {optionCount, plural, one {# optie} other {# opties}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "Je hebt in totaal <b>{totalVotes, plural, one {# stem} other {# stemmen}} om te verdelen over {optionCount, plural, one {# optie} other {# opties}}</b>.", "app.utils.votingMethodUtils.finalResults": "Eindresultaten", "app.utils.votingMethodUtils.finalTally": "Eindtotaal", "app.utils.votingMethodUtils.howToParticipate": "Hoe deel te nemen", "app.utils.votingMethodUtils.howToVote": "Hoe te stemmen", "app.utils.votingMethodUtils.multipleVotingEnded1": "De stemming is gesloten op <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 credits} one {1 credit} other {# credits}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 punten} one {1 punt} other {# punten}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokens} one {1 token} other {# tokens}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 stemmen} one {1 stem} other {# stemmen}}", "app.utils.votingMethodUtils.results": "Resultaten", "app.utils.votingMethodUtils.singleVotingEnded": "De stemming werd gesloten op <b>{endDate}.</b> <PERSON><PERSON><PERSON><PERSON> konden <b>stemmen op {maxVotes} opties.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Selecteer je voorkeursopties door op \"Stem\" te klikken", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Je hebt <b>{totalVotes} stemmen</b> die je aan de opties kunt toekennen.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON>s je klaar bent, klik je op \"Verzenden\" om je stem uit te brengen.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "De stemming werd gesloten op <b>{endDate}.</b> <PERSON><PERSON><PERSON><PERSON> konden <b>stemmen op 1 optie.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "<PERSON><PERSON> de optie van je voorkeur door op \"Selecteer\" te tikken.", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Je hebt <b>1 stem</b> die je aan een van de opties kunt toekennen.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "De stemming werd gesloten op <b>{endDate}.</b> <PERSON><PERSON><PERSON><PERSON> konden <b>stemmen op zoveel opties als ze wilden.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Je kunt op zoveel opties stemmen als je wilt.", "app.utils.votingMethodUtils.submitYourBudget": "Dien je budget in", "app.utils.votingMethodUtils.submittedBudgetCountText2": "persoon diende zijn budget online in", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "mensen dienden hun budget online in", "app.utils.votingMethodUtils.submittedVoteCountText2": "persoon diende zijn stem online in", "app.utils.votingMethodUtils.submittedVotesCountText2": "mensen hebben online gestemd", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "<PERSON><PERSON> verz<PERSON> 🎉", "app.utils.votingMethodUtils.votesCast": "Uitgebrachte stemmen", "app.utils.votingMethodUtils.votingClosed": "<PERSON><PERSON><PERSON> ges<PERSON>en", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Je stem wordt pas geteld</b> als je op \"Verzenden\" klikt.", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, je stem is uitgebracht!</b> Je kunt je inzending controleren of wijzigen tot <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, je stem is uitgebracht!</b> Je kunt je inzending hieronder op elk moment controleren of wijzigen.", "components.UI.IdeaSelect.noIdeaAvailable": "<PERSON>r zijn geen idee<PERSON>n be<PERSON>.", "components.UI.IdeaSelect.selectIdea": "Selecteer idee", "containers.SiteMap.allProjects": "Alle projecten", "containers.SiteMap.customPageSection": "Aangepaste pagina's", "containers.SiteMap.folderInfo": "Meer informatie", "containers.SiteMap.headSiteMapTitle": "Sitemap | {orgName}", "containers.SiteMap.homeSection": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.pageContents": "Pagina-inhoud", "containers.SiteMap.profilePage": "Je profielpagina", "containers.SiteMap.profileSettings": "Je instellingen", "containers.SiteMap.projectEvents": "Activiteiten", "containers.SiteMap.projectIdeas": "Ideeën", "containers.SiteMap.projectInfo": "Informatie", "containers.SiteMap.projectPoll": "Peiling", "containers.SiteMap.projectSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectsArchived": "Gearchiveerde projecten", "containers.SiteMap.projectsCurrent": "Lopende projecten", "containers.SiteMap.projectsDraft": "Conceptprojecten", "containers.SiteMap.projectsSection": "<PERSON><PERSON> van {orgName}", "containers.SiteMap.signInPage": "Inloggen", "containers.SiteMap.signUpPage": "Registreren", "containers.SiteMap.siteMapDescription": "Vanaf deze pagina kan je naar alle inhoud op het platform navigeren.", "containers.SiteMap.siteMapTitle": "Sitemap van het participatieplatform van {orgName}", "containers.SiteMap.successStories": "Succesverhalen", "containers.SiteMap.timeline": "Projectfasen", "containers.SiteMap.userSpaceSection": "Je profiel", "containers.SubscriptionEndedPage.accessDenied": "Je hebt niet langer toegang", "containers.SubscriptionEndedPage.subscriptionEnded": "Je hebt niet langer toegang omdat je licentie ten einde liep."}
{"EmailSettingsPage.emailSettings": "Indstillinger for e-mail", "EmailSettingsPage.initialUnsubscribeError": "Der var et problem med at afmelde dig fra denne kampagne, prøv venligst igen.", "EmailSettingsPage.initialUnsubscribeLoading": "Din anmodning bliver behandlet, vent venligst...", "EmailSettingsPage.initialUnsubscribeSuccess": "Du har afmeldt dig fra {campaignTitle}.", "UI.FormComponents.optional": "valg<PERSON><PERSON>", "app.closeIconButton.a11y_buttonActionMessage": "Luk", "app.components.Areas.areaUpdateError": "Der opstod en fejl, da du gemte dit område. Prøv venligst igen.", "app.components.Areas.followedArea": "Fulgt område: {areaTitle}", "app.components.Areas.followedTopic": "Fulgte emnet: {topicTitle}", "app.components.Areas.topicUpdateError": "Der opstod en fejl, da du gemte dit emne. Prøv venligst igen.", "app.components.Areas.unfollowedArea": "<PERSON><PERSON><PERSON> fulgt område: {areaTitle}", "app.components.Areas.unfollowedTopic": "<PERSON><PERSON>, der ikke er fulgt: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "<PERSON><PERSON>", "app.components.AssignBudgetControl.add": "Tilføj", "app.components.AssignBudgetControl.added": "Tilføjet", "app.components.AssignMultipleVotesControl.addVote": "<PERSON><PERSON><PERSON><PERSON><PERSON> stemme", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Du har fordelt alle dine kreditter.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "<PERSON> har fordelt det maksimale antal kreditter for denne mulighed.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Du har fordelt alle dine kreditter.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "<PERSON> har fordelt det maksimale antal kreditter for denne mulighed.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Du har fordelt alle dine kreditter.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "<PERSON> har fordelt det maksimale antal kreditter for denne mulighed.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Du har fordelt alle dine stemmer.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Du har fordelt det maksimale antal stemmer for denne mulighed.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(inkl. 1 offline)} other {(inkl. # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Der kan ikke stemmes, da denne fase ikke er aktiv.", "app.components.AssignMultipleVotesControl.removeVote": "<PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.select": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Du har allerede afgivet din stemme. H<PERSON> du vil ændre den, skal du klikke på \"Ændr din indsendelse\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Du har allerede indsendt din stemme. Hvis du vil ændre den, skal du gå tilbage til projektsiden og klikke på \"Ændre din indsendelse\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {kredit} other {kreditter}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {point} other {point}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {stemme} other {stemmer}}", "app.components.AssignVoteControl.maxVotesReached1": "Du har fordelt alle dine stemmer.", "app.components.AssignVoteControl.phaseNotActive": "Der kan ikke stemmes, da denne fase ikke er aktiv.", "app.components.AssignVoteControl.select": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.selected2": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.voteForAtLeastOne": "<PERSON>em på mindst 1 mulighed", "app.components.AssignVoteControl.votesSubmitted1": "Du har allerede afgivet din stemme. H<PERSON> du vil ændre den, skal du klikke på \"Ændre din indsendelse\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Du har allerede indsendt din stemme. Hvis du vil ændre den, skal du gå tilbage til projektsiden og klikke på \"Ændre din indsendelse\".", "app.components.AuthProviders.continue": "Fortsæt", "app.components.AuthProviders.continueWithAzure": "Fortsæt med {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Fortsæt med Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Fortsæt med falsk SSO", "app.components.AuthProviders.continueWithGoogle": "Fortsæt med Google", "app.components.AuthProviders.continueWithHoplr": "Fortsæt med Hoplr", "app.components.AuthProviders.continueWithIdAustria": "Fortsæt med ID Østrig", "app.components.AuthProviders.continueWithLoginMechanism": "Fortsæt med {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Fortsæt med MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Der findes allerede en konto med denne e-mail-adresse.{br}{br}Du kan ikke få adgang til platformen via FranceConnect, da de personlige oplysninger ikke stemmer overens. For at logge ind via FranceConnect skal du først ændre dit fornavn eller efternavn på denne platform, så det passer til dine officielle oplysninger.{br}{br}Du kan logge ind som du plejer nedenfor.", "app.components.AuthProviders.goToLogIn": "Har du allerede en konto? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Har du ikke en konto? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Log ind", "app.components.AuthProviders.logInWithEmail": "Log ind med Email", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Du skal have den angivne minimumsalder eller derover for at blive verificeret.", "app.components.AuthProviders.signUp2": "Registrer dig", "app.components.AuthProviders.signUpButtonAltText": "Tilmeld dig med {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Registrer dig med Email", "app.components.AuthProviders.verificationRequired": "Bekræftelse påkrævet", "app.components.Author.a11yPostedBy": "Sendt af", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 deltager} other {{numberOfParticipants} deltagere}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} brugere\n", "app.components.AvatarBubbles.participant": "deltager", "app.components.AvatarBubbles.participants1": "deltagere", "app.components.Comments.cancel": "<PERSON><PERSON><PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "Det er ikke muligt at kommentere i den aktuelle fase.", "app.components.Comments.commentingDisabledInactiveProject": "Det er ikke muligt at kommentere, fordi dette projekt i øjeblikket ikke er aktivt.", "app.components.Comments.commentingDisabledProject": "Kommentering på idéer er deaktiveret i øjeblikket.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} for at kommentere.", "app.components.Comments.commentingMaybeNotPermitted": "Brug venligst {signInLink} for at se hvilke handlinger der kan udføres", "app.components.Comments.inputsAssociatedWithProfile": "Som standard vil dine indlæg være knyttet til din profil, medmindre du vælger denne mulighed.", "app.components.Comments.invisibleTitleComments": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.leastRecent": "Mindst nye", "app.components.Comments.likeComment": "<PERSON><PERSON><PERSON> godt om denne kommentar", "app.components.Comments.mostLiked": "<PERSON><PERSON><PERSON> reaktioner", "app.components.Comments.mostRecent": "Seneste", "app.components.Comments.official": "Officiel", "app.components.Comments.postAnonymously": "Anonymt indlæg", "app.components.Comments.replyToComment": "<PERSON><PERSON><PERSON> kom<PERSON>ar", "app.components.Comments.reportAsSpam": "Rapportér som spam", "app.components.Comments.seeOriginal": "Vis original", "app.components.Comments.seeTranslation": "<PERSON> <PERSON><PERSON><PERSON>", "app.components.Comments.yourComment": "<PERSON> kom<PERSON>ar", "app.components.CommonGroundResults.divisiveDescription": "Udsagn hvor folk er lige enige og uenige:", "app.components.CommonGroundResults.divisiveTitle": "Splittende", "app.components.CommonGroundResults.majorityDescription": "Mere end 60 % stemte på den ene eller den anden måde om følgende:", "app.components.CommonGroundResults.majorityTitle": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.participantLabel": "deltager", "app.components.CommonGroundResults.participantsLabel1": "deltagere", "app.components.CommonGroundResults.statementLabel": "udtalelse", "app.components.CommonGroundResults.statementsLabel1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.votesLabe": "stemme", "app.components.CommonGroundResults.votesLabel1": "stemmer", "app.components.CommonGroundStatements.agreeLabel": "<PERSON><PERSON>", "app.components.CommonGroundStatements.disagreeLabel": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundStatements.noMoreStatements": "Der er ingen udsagn at reagere på lige nu", "app.components.CommonGroundStatements.noResults": "Der er ingen resultater at vise endnu. <PERSON><PERSON><PERSON> for, at du har deltaget i Fælles Grundlag-fasen, og kom tilbage her bagefter.", "app.components.CommonGroundStatements.unsureLabel": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundTabs.resultsTabLabel": "Resultater", "app.components.CommonGroundTabs.statementsTabLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommunityMonitorModal.formError": "Oplevede en fejl.", "app.components.CommunityMonitorModal.surveyDescription2": "<PERSON>ne løbende unders<PERSON><PERSON><PERSON> viser, hvad du mener om ledelse og offentlige tjenester.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {<PERSON><PERSON> <1 minut} one {Tager 1 minut} other {Tager # minutter}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "En e-mail med en bekræftelseskode er sendt til {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Skift din email.", "app.components.ConfirmationModal.codeInput": "<PERSON><PERSON>", "app.components.ConfirmationModal.confirmationCodeSent": "<PERSON>y kode er sendt", "app.components.ConfirmationModal.didntGetAnEmail": "Modtog du ikke en email?", "app.components.ConfirmationModal.foundYourCode": "Fandt du din kode?", "app.components.ConfirmationModal.goBack": "<PERSON><PERSON> tilbage.", "app.components.ConfirmationModal.sendEmailWithCode": "Send email med koden", "app.components.ConfirmationModal.sendNewCode": "Send en ny kode.", "app.components.ConfirmationModal.verifyAndContinue": "Tjek og bekræft", "app.components.ConfirmationModal.wrongEmail": "Forkert email?", "app.components.ConsentManager.Banner.accept": "Accepter", "app.components.ConsentManager.Banner.ariaButtonClose2": "Afvis politik og luk banner", "app.components.ConsentManager.Banner.close": "Luk", "app.components.ConsentManager.Banner.mainText": "Denne platform anvender cookies i overensstemmelse med vores {policyLink}.", "app.components.ConsentManager.Banner.manage": "Administrer", "app.components.ConsentManager.Banner.policyLink": "Cookiepolitik", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Reklamer og annoncering", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "{tenant<PERSON><PERSON>, select, <PERSON><PERSON><PERSON> {For at personliggøre og måle reklamekampagnernes effektivitet på vores webside. Vi viser ikke nogen reklamer på denne platform, men følgende tjenester kan give dig en personlig reklame baseret på de sider du besøger på vores side. Denne platform sender data fra Google Analytics videre til Siteimprove, som er den applikation, der bruges af Hørsholm Kommune.} other {For at personliggøre og måle reklamekampagnernes effektivitet på vores webside. Vi viser ikke nogen reklamer på denne platform, men følgende tjenester kan give dig en personlig reklame baseret på de sider du besøger på vores side.}}", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "<PERSON><PERSON><PERSON>værktøj", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Vi bruger denne sporing for bedre at forstå hvordan du bruger platformen og for at kunne lære og forbedre din navigation. Denne information bruges kun til masseanalyser og på ingen måde til at spore enkeltpersoner.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funktionalitet", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for at aktivere og overvåge grundlæggende funktioner på websiden. Nogle af de værktøjer som er angivet her gælder måske ikke for dig. Se venligst vores cookie-politik for mere information.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Påkrævet", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "For at have en funktionel platform gemmer vi en godkendende cookie, hvis du tilmelder dig, og det sprog, du bruger denne platform på.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Gem", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Dine cookie præferencer", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "<PERSON><PERSON><PERSON>tøjer", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Ansvarsfraskrivelse for upload af indhold", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Ved at uploade indhold erklærer du, at dette indhold ikke krænker nogen regler eller rettigheder for tredjeparter, såsom intellektuelle ejendomsrettigheder, privatlivets fred, rettigheder til forretningshemmeligheder osv. Ved at uploade dette indhold forpligter du dig derfor til at bære det fulde og eksklusive ansvar for alle direkte og indirekte skader som følge af det uploadede indhold. Desuden forpligter du dig til at holde platformens ejer og Go Vocal skadesløs for alle krav fra tredjepart eller ansvar over for tredjepart, og alle tilknyttede omkostninger, der måtte opstå eller være resultatet af det indhold, du har uploadet.", "app.components.ContentUploadDisclaimer.onAccept": "<PERSON><PERSON>", "app.components.ContentUploadDisclaimer.onCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "<PERSON><PERSON><PERSON> os hvorfor", "app.components.CustomFieldsForm.addressInputAriaLabel": "Indtastning af adresse", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Indtast en adresse...", "app.components.CustomFieldsForm.adminFieldTooltip": "<PERSON>lt kun synligt for administratorer", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Alle svar på denne undersøgelse er anonymiseret.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "<PERSON> kræves mindst tre punkter til en polygon.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "<PERSON> kræves mindst to punkter for en linje.", "app.components.CustomFieldsForm.attachmentRequired": "Mindst én vedhæftet fil er påkrævet", "app.components.CustomFieldsForm.authorFieldLabel": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Begynd at skrive for at søge efter brugerens e-mail eller navn...", "app.components.CustomFieldsForm.back": "Tilbage", "app.components.CustomFieldsForm.budgetFieldLabel": "Budget", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Klik på kortet for at tegne. Træk derefter i punkterne for at flytte dem.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "<PERSON><PERSON> på kortet eller skriv en adresse nedenfor for at tilføje dit svar.", "app.components.CustomFieldsForm.confirm": "Bekræft", "app.components.CustomFieldsForm.descriptionMinLength": "Beskrivelsen skal være mindst {min} tegn lang", "app.components.CustomFieldsForm.descriptionRequired": "Beskrivelsen er påkrævet", "app.components.CustomFieldsForm.fieldMaximumItems": "Højst {maxSelections, plural, one {# option} other {# options}} kan vælges for feltet \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "Mindst {minSelections, plural, one {# option} other {# options}} kan vælges for feltet \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "Feltet \"{fieldName}\" er påkrævet.", "app.components.CustomFieldsForm.fileSizeLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> for filstørrelse er {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "<PERSON><PERSON><PERSON> er påkrævet", "app.components.CustomFieldsForm.minimumCoordinates2": "<PERSON> kræves mindst {numPoints} kortpunkter.", "app.components.CustomFieldsForm.notPublic1": "*<PERSON><PERSON> svar vil kun blive delt med projektledere og ikke med offentligheden.", "app.components.CustomFieldsForm.otherArea": "Et andet sted", "app.components.CustomFieldsForm.progressBarLabel": "Fremskridt", "app.components.CustomFieldsForm.removeAnswer": "<PERSON><PERSON><PERSON> svar", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*<PERSON><PERSON><PERSON>g så mange, som du vil", "app.components.CustomFieldsForm.selectBetween": "*Vælg mellem {minItems} og {maxItems} muligheder", "app.components.CustomFieldsForm.selectExactly2": "*Væ<PERSON>g nøjagtigt {selectExactly, plural, one {# mulighed} other {# muligheder}}", "app.components.CustomFieldsForm.selectMany": "*Vælg så mange som du vil", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Tryk på kortet for at tegne. Træk derefter i punkterne for at flytte dem.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Tryk på kortet for at tegne.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Tryk på kortet for at tilføje dit svar.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "<PERSON><PERSON> på kortet, eller skriv en adresse nedenfor for at tilføje dit svar.", "app.components.CustomFieldsForm.tapToAddALine": "Tryk for at tilføje en linje", "app.components.CustomFieldsForm.tapToAddAPoint": "Tryk for at tilføje et punkt", "app.components.CustomFieldsForm.tapToAddAnArea": "Tryk for at tilføje et område", "app.components.CustomFieldsForm.titleMaxLength": "Titlen må højst være {max} tegn lang", "app.components.CustomFieldsForm.titleMinLength": "Titlen skal være mindst {min} tegn lang", "app.components.CustomFieldsForm.titleRequired": "Titlen er påkrævet", "app.components.CustomFieldsForm.topicRequired": "Mindst ét tag er påkrævet", "app.components.CustomFieldsForm.typeYourAnswer": "Skriv dit svar", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Det er nødvendigt at skrive dit svar", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Upload en zip-fil, der indeholder en eller flere shapefiler.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "<PERSON><PERSON> place<PERSON>en ikke vises blandt m<PERSON>, mens du skriver, kan du tilføje gyldige koordinater i formatet 'breddegrad, længdegrad' for at angive en præcis placering (f.eks.: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Nogle felter var ugyldige. Ret venligst fejlene og prøv igen.", "app.components.ErrorBoundary.errorFormErrorGeneric": "En ukendt fejl opstod under indsendelse af din rapport. Prøv venligst igen.", "app.components.ErrorBoundary.errorFormLabelClose": "Luk", "app.components.ErrorBoundary.errorFormLabelComments": "Hvad skete der?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-mail", "app.components.ErrorBoundary.errorFormLabelName": "Navn", "app.components.ErrorBoundary.errorFormLabelSubmit": "Indsend", "app.components.ErrorBoundary.errorFormSubtitle": "Vores team har fået besked.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON><PERSON> du gerne vil hjæ<PERSON><PERSON>, skal du fortælle os hvad der skete nedenfor.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Din feedback er blevet sendt. Tak!", "app.components.ErrorBoundary.errorFormTitle": "Det ser ud til at vi har problemer.", "app.components.ErrorBoundary.genericErrorWithForm": "En fejl opstod og vi kan ikke vise dette indhold. Prøv venligst igen eller {openForm}!", "app.components.ErrorBoundary.openFormText": "hjæ<PERSON><PERSON> os med at løse problemet", "app.components.ErrorToast.budgetExceededError": "Du har ikke nok budget", "app.components.ErrorToast.votesExceededError": "Du har ikke nok stemmer tilbage", "app.components.EventAttendanceButton.forwardToFriend": "<PERSON><PERSON><PERSON>nd til en ven", "app.components.EventAttendanceButton.maxRegistrationsReached": "Det maksimale antal tilmeldinger til arrangementet er nået. Der er ingen pladser tilbage.", "app.components.EventAttendanceButton.register": "Registrer dig", "app.components.EventAttendanceButton.registered": "Regis<PERSON>ret", "app.components.EventAttendanceButton.seeYouThere": "Vi ses der!", "app.components.EventAttendanceButton.seeYouThereName": "Vi ses der, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Færre oplysninger blev synlige om begivenheder.", "app.components.EventCard.a11y_moreContentVisible": "Flere oplysninger om begivenheder blev synlige.", "app.components.EventCard.a11y_readMore": "<PERSON>æs mere om arrangementet \"{eventTitle}\".", "app.components.EventCard.endsAt": "Slutter den", "app.components.EventCard.readMore": "<PERSON><PERSON><PERSON> mere", "app.components.EventCard.showLess": "Vis mindre", "app.components.EventCard.showMore": "Vis mere", "app.components.EventCard.startsAt": "Starter den", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Kommende og igangværende begivenheder i dette projekt", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Kommende og igangværende begivenheder i denne fase", "app.components.FileUploader.a11y_file": "Fil:", "app.components.FileUploader.a11y_filesToBeUploaded": "<PERSON>r, der skal uploades: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Ingen filer tilføjet.", "app.components.FileUploader.a11y_removeFile": "<PERSON><PERSON><PERSON> denne fil", "app.components.FileUploader.fileInputDescription": "<PERSON><PERSON> for at vælge en fil", "app.components.FileUploader.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.components.FileUploader.file_too_large2": "Filer stø<PERSON> end {maxSizeMb}MB er ikke tilladt.", "app.components.FileUploader.incorrect_extension": "{fileName} understøttes ikke af vores system, det vil ikke blive uploadet.", "app.components.FilterBoxes.a11y_allFilterSelected": "Valgt statusfilter: alle", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, no {# inputs} one {# input} other {# inputs}}", "app.components.FilterBoxes.a11y_removeFilter": "<PERSON><PERSON><PERSON> filter", "app.components.FilterBoxes.a11y_selectedFilter": "Valgt statusfilter: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Valgte {numberOfSelectedTopics, plural, =0 {zero topic filters} et {one topic filter} andre {# topic filters}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Alle", "app.components.FilterBoxes.areas": "<PERSON>ltrer efter områder", "app.components.FilterBoxes.inputs": "Indlæg", "app.components.FilterBoxes.noValuesFound": "Ingen tilgængelige værdier.", "app.components.FilterBoxes.showLess": "Vis mindre", "app.components.FilterBoxes.showTagsWithNumber": "Vis alle ({numberTags})", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "<PERSON><PERSON>", "app.components.FiltersModal.filters": "Filtre", "app.components.FolderFolderCard.a11y_folderDescription": "Mappebeskrivelse:", "app.components.FolderFolderCard.a11y_folderTitle": "Mappetitel:", "app.components.FolderFolderCard.archived": "Arkiveret", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projekter} one {# projekt} other {# projekter}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "Felttypen kan ikke æ<PERSON>, når der er besvarelser.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Type", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Automatisk lagring", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Automatisk lagring er aktiveret som standard, når du åbner formulareditoren. Hver gang du lukker panelet med feltindstillinger ved hjælp af \"X\"-knappen, vil det automatisk udløse en lagring.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "<PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.timeRangeMultiyear": "F<PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON>", "app.components.GanttChart.today": "I dag", "app.components.GoBackButton.group.edit.goBack": "<PERSON><PERSON>", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Gå tilbage til forrige side", "app.components.HookForm.Feedback.errorTitle": "Der er et problem", "app.components.HookForm.Feedback.submissionError": "Prøv igen. Hvis problemet fortsætter, skal du kontakte os", "app.components.HookForm.Feedback.submissionErrorTitle": "Der var et problem i vores ende, beklager", "app.components.HookForm.Feedback.successMessage": "<PERSON>ren er indsendt med succes", "app.components.HookForm.PasswordInput.passwordLabel": "Adgangskode", "app.components.HorizontalScroll.scrollLeftLabel": "Rul til venstre.", "app.components.HorizontalScroll.scrollRightLabel": "Rul til højre.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} ideer er blevet indlæst.", "app.components.IdeaCards.filters": "Filtre", "app.components.IdeaCards.filters.mostDiscussed": "Mest diskuteret", "app.components.IdeaCards.filters.newest": "Ny", "app.components.IdeaCards.filters.oldest": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.popular": "De mest populære", "app.components.IdeaCards.filters.random": "Tilfældige", "app.components.IdeaCards.filters.sortBy": "Sorter efter", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Sortering ændret til: {currentSortType}", "app.components.IdeaCards.filters.trending": "Populære", "app.components.IdeaCards.showMore": "Vis mere ", "app.components.IdeasMap.a11y_hideIdeaCard": "Skjul idékortet.", "app.components.IdeasMap.a11y_mapTitle": "<PERSON><PERSON> overblik", "app.components.IdeasMap.clickOnMapToAdd": "Klik på kortet for at tilføje dit bidrag", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Som administrator kan du klikke på kortet for at tilføje dit input, selv om denne fase ikke er aktiv.", "app.components.IdeasMap.filters": "Filtre", "app.components.IdeasMap.multipleInputsAtLocation": "Flere input på dette sted", "app.components.IdeasMap.noFilteredResults": "De valgte filtre returnerede ingen resultater", "app.components.IdeasMap.noResults": "Ingen resultater fundet", "app.components.IdeasMap.or": "eller", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, ingen dislikes.} one {1 dislike.} other {, # dislikes.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, ingen likes.} one {, 1 like.} other {, # likes.}}", "app.components.IdeasMap.signInLinkText": "logge ind", "app.components.IdeasMap.signUpLinkText": "registrer dig", "app.components.IdeasMap.submitIdea2": "Indsend input", "app.components.IdeasMap.tapOnMapToAdd": "Tryk på kortet for at tilføje dit input", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Som administrator kan du trykke på kortet for at tilføje dit input, selv om denne fase ikke er aktiv.", "app.components.IdeasMap.userInputs2": "Input fra deltagerne", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, ingen kommentarer} one {, 1 kommentar} other {, #kommentarer}}", "app.components.IdeasShow.bodyTitle": "Beskrivelse", "app.components.IdeasShow.deletePost": "Slet", "app.components.IdeasShow.editPost": "<PERSON><PERSON>", "app.components.IdeasShow.goBack": "<PERSON><PERSON>", "app.components.IdeasShow.moreOptions": "<PERSON><PERSON><PERSON> v<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.or": "eller", "app.components.IdeasShow.proposedBudgetTitle": "Estimeret budget", "app.components.IdeasShow.reportAsSpam": "Rapporter som spam", "app.components.IdeasShow.send": "Send", "app.components.IdeasShow.skipSharing": "<PERSON><PERSON> deler det senere", "app.components.IdeasShowPage.signIn2": "Log ind", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON>, du kan ikke få adgang til denne side. Du skal muligvis logge ind eller tilmelde dig for at få adgang til den.", "app.components.LocationInput.noOptions": "<PERSON><PERSON> muligheder", "app.components.Modal.closeWindow": "Luk vindue", "app.components.MultiSelect.clearButtonAction": "<PERSON><PERSON> valg", "app.components.MultiSelect.clearSearchButtonAction": "<PERSON><PERSON>", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Indtast en ny e-mail-adresse", "app.components.PageNotFound.goBackToHomePage": "Tilbage til hjemmesiden", "app.components.PageNotFound.notFoundTitle": "Side ikke fundet", "app.components.PageNotFound.pageNotFoundDescription": "Den ønskede side kunne ikke findes.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Tilvejebringe indhold på mindst ét sprog", "app.components.PagesForm.editContent": "Indhold", "app.components.PagesForm.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Filerne bør ikke være større end 50 Mb. Tilføjede filer vil blive vist nederst på denne side.", "app.components.PagesForm.navbarItemTitle": "Navn i navbar", "app.components.PagesForm.pageTitle": "Titel", "app.components.PagesForm.savePage": "Gem side", "app.components.PagesForm.saveSuccess": "Siden er gemt.", "app.components.PagesForm.titleMissingOneLanguageError": "<PERSON><PERSON> titel for mindst ét sprog", "app.components.Pagination.back": "Forrige side", "app.components.Pagination.next": "Næste side", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Du har brugt {votesCast}, hvilket overskrider græ<PERSON><PERSON> på {votesLimit}. <PERSON>jern venligst nogle varer fra din kurv, og prøv igen.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget}  tilbage", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Du skal bruge mindst {votesMinimum} før du kan indsende din kurv.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Du skal vælge mindst én mulighed, før du kan sende.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Du skal tilføje noget til din kurv, før du kan sende den.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {Ingen kreditter tilbage} other {# ud af {totalNumberOfVotes, plural, one {1 kredit} other {# kreditter}} tilbage}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {Ingen point tilbage} other {# ud af {totalNumberOfVotes, plural, one {1 point} other {# point}} tilbage}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Ingen tokens tilbage} other {# ud af {totalNumberOfVotes, plural, one {1 token} other {# tokens}} tilbage}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Ingen stemmer tilbage} other {# ud af {totalNumberOfVotes, plural, one {1 stemme} other {# stemmer}} tilbage}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# stemmer} one {# stemme} other {# stemmer}} afgivet", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Du har afgivet {votesCast} stemmer, hvilket overskrider grænsen på {votesLimit}. <PERSON>jern venligst nogle stemmer og prøv igen.", "app.components.ParticipationCTABars.addInput": "Tilføj input", "app.components.ParticipationCTABars.allocateBudget": "Fordel dit budget ", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Dit budget er blevet indsendt med succes.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Åben for deltagelse", "app.components.ParticipationCTABars.poll": "Deltag i afstemningen", "app.components.ParticipationCTABars.reviewDocument": "Gennemgå dokumentet", "app.components.ParticipationCTABars.seeContributions": "Se bidrag", "app.components.ParticipationCTABars.seeEvents3": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeIdeas": "Se idéer", "app.components.ParticipationCTABars.seeInitiatives": "Se initiativer", "app.components.ParticipationCTABars.seeIssues": "Se problemer", "app.components.ParticipationCTABars.seeOptions": "<PERSON> muligheder", "app.components.ParticipationCTABars.seePetitions": "Se underskriftsindsamlinger", "app.components.ParticipationCTABars.seeProjects": "Se projekter", "app.components.ParticipationCTABars.seeProposals": "Se forslag", "app.components.ParticipationCTABars.seeQuestions": "Se spørgsmål", "app.components.ParticipationCTABars.submit": "Indsend", "app.components.ParticipationCTABars.takeTheSurvey": "<PERSON><PERSON><PERSON> spørgeskema<PERSON> ", "app.components.ParticipationCTABars.userHasParticipated": "Du har deltaget i dette projekt.", "app.components.ParticipationCTABars.viewInputs": "Se indgange", "app.components.ParticipationCTABars.volunteer": "Friv<PERSON>ig", "app.components.ParticipationCTABars.votesCounter.vote": "stem", "app.components.ParticipationCTABars.votesCounter.votes": "stemmer", "app.components.PasswordInput.a11y_passwordHidden": "Password er skjult", "app.components.PasswordInput.a11y_passwordVisible": "Password er synligt", "app.components.PasswordInput.a11y_strength1Password": "Password er for kort", "app.components.PasswordInput.a11y_strength2Password": "Password er ikke stærkt nok", "app.components.PasswordInput.a11y_strength3Password": "Password styrke er middel", "app.components.PasswordInput.a11y_strength4Password": "Password er stærkt", "app.components.PasswordInput.a11y_strength5Password": "<PERSON><PERSON> stærkt password", "app.components.PasswordInput.hidePassword": "Skjul password", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "For kort (min. {minimumPasswordLength} karakterer)", "app.components.PasswordInput.minimumPasswordLengthError": "Angiv en adgangskode, der er mindst {minimumPasswordLength} tegn lang", "app.components.PasswordInput.passwordEmptyError": "Indtast din adgangskode", "app.components.PasswordInput.passwordStrengthTooltip1": "<PERSON><PERSON> du vil have et mere sikkert password:", "app.components.PasswordInput.passwordStrengthTooltip2": "Brug en kombination af ikke-sammenhængende små bogstaver, store bogstaver, cifre, specialtegn og tegnsætning", "app.components.PasswordInput.passwordStrengthTooltip3": "Undgå almindelige ord eller ord, der er nemme at gætte", "app.components.PasswordInput.passwordStrengthTooltip4": "<PERSON><PERSON> læ<PERSON>", "app.components.PasswordInput.showPassword": "Vis password", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "Svag", "app.components.PasswordInput.strength3Password": "Middel", "app.components.PasswordInput.strength4Password": "Stærkt", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON> stæ<PERSON>t", "app.components.PostCardsComponents.list": "Liste", "app.components.PostCardsComponents.map": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Tilføj en officiel opdatering", "app.components.PostComponents.OfficialFeedback.cancel": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Slet", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Er du sikker på at du vil slette denne officielle opdatering?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "Sidst redigeret d. {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Sidste opdatering: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Officiel", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Vælg hvordan folk ser dit navn", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Forfatternavn på officiel opdatering", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Brødtekst på officiel opdatering", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Officielle op<PERSON>", "app.components.PostComponents.OfficialFeedback.postedOn": "<PERSON>lået op den {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Udgiv", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Vis tidligere op<PERSON><PERSON>er", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Publicér en opdatering...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON><PERSON>, der var et problem", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "<PERSON><PERSON><PERSON> besked", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Din opdatering blev udgivet!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "<PERSON><PERSON>t mit bidrag '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "<PERSON><PERSON><PERSON> mit bidrag: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "<PERSON><PERSON><PERSON> mit bidrag: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Hvad synes du om denne idé? Stem på den og del diskussionen på {postUrl} for at blive hørt!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "{tenantN<PERSON>, select, DeloitteDK {Giv dit input: {postTitle}} other {Støt min idé: {ideaTitle}}}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "{tenantName, select, DeloitteDK {Giv dit input: {postTitle}.} other {Støt min idé: {postTitle}.}}", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Hvad synes du om dette forslag? Stem på den og del diskussionen på {postUrl} for at blive hørt!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "<PERSON><PERSON><PERSON> mit forslag: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "<PERSON><PERSON><PERSON> mit forslag: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Jeg sendte dette hø<PERSON> '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Jeg har lige sendt et hø<PERSON>var til {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Jeg har lige sendt et hø<PERSON>var til {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "<PERSON><PERSON><PERSON> min forslag '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "<PERSON><PERSON><PERSON> min forslag '{postTitle}'.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "<PERSON><PERSON><PERSON> mit forslag: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "<PERSON>øt min underskriftsindsamling '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "<PERSON><PERSON>t min underskriftsindsamling: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "<PERSON><PERSON>t min underskriftsindsamling: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "<PERSON><PERSON>t min projekt '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "<PERSON><PERSON><PERSON> min projekt '{postTitle}'.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "<PERSON><PERSON><PERSON> min projekt '{postTitle}'.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "<PERSON><PERSON>t mit forslag '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "<PERSON><PERSON><PERSON> mit forslag: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Jeg har lige sendt et forslag til {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Deltag i diskussionen om dette spørgsmål '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "<PERSON>il dit spørgsm<PERSON>l: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "<PERSON>il dit spørgsm<PERSON>l: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "{tenantName, select, DeloitteDK {Giv dit input {postTitle}} other {Stem på {postTitle} på}}", "app.components.PostComponents.linkToHomePage": "<PERSON> til hjemmeside", "app.components.PostComponents.readMore": "<PERSON>æs mere...", "app.components.PostComponents.topics": "<PERSON><PERSON>", "app.components.ProjectArchivedIndicator.archivedProject": "{tenant<PERSON><PERSON>, select, DeloitteDK {Desværre kan du ikke længere deltage i dette verden<PERSON>l, da det er blevet arkiveret} other {Desværre kan du ikke længere deltage i dette projekt, da det er blevet arkiveret}}", "app.components.ProjectArchivedIndicator.previewProject": "Projektudkast:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "<PERSON><PERSON> synlig for moderatorer og dem med et preview-link.", "app.components.ProjectCard.a11y_projectDescription": "Projektbeskrivelse:", "app.components.ProjectCard.a11y_projectTitle": "Projekttitel", "app.components.ProjectCard.addYourOption": "<PERSON><PERSON><PERSON><PERSON><PERSON> dit forslag", "app.components.ProjectCard.allocateYourBudget": "Fordel dit budget ", "app.components.ProjectCard.archived": "Arkiveret", "app.components.ProjectCard.comment": "Kommentar", "app.components.ProjectCard.contributeYourInput": "Bidrag med dit input", "app.components.ProjectCard.finished": "Afsluttet", "app.components.ProjectCard.joinDiscussion": "<PERSON>il dit spørgsm<PERSON><PERSON>", "app.components.ProjectCard.learnMore": "<PERSON><PERSON><PERSON> mere ", "app.components.ProjectCard.reaction": "Reaktion", "app.components.ProjectCard.readTheReport": "<PERSON><PERSON><PERSON> rap<PERSON>", "app.components.ProjectCard.reviewDocument": "Gennemgå dokumentet", "app.components.ProjectCard.submitAnIssue": "Send dit hørings<PERSON>var", "app.components.ProjectCard.submitYourIdea": "Beskriv din idé", "app.components.ProjectCard.submitYourInitiative": "Indsend dit initiativ", "app.components.ProjectCard.submitYourPetition": "Indsend din underskriftsindsamling", "app.components.ProjectCard.submitYourProject": "Send dit projekt", "app.components.ProjectCard.submitYourProposal": "Indsend dit forslag", "app.components.ProjectCard.takeThePoll": "Tag afstemning", "app.components.ProjectCard.takeTheSurvey": "<PERSON><PERSON><PERSON> spørgeskema<PERSON> ", "app.components.ProjectCard.viewTheContributions": "Se alle bidrag", "app.components.ProjectCard.viewTheIdeas": "{tenantName, select, DeloitteDK {Vis indlæg} other {Se idéerne}}", "app.components.ProjectCard.viewTheInitiatives": "Se initiativerne", "app.components.ProjectCard.viewTheIssues": "<PERSON>ø<PERSON>", "app.components.ProjectCard.viewTheOptions": "<PERSON> mulighederne", "app.components.ProjectCard.viewThePetitions": "Se underskriftsindsamlingerne", "app.components.ProjectCard.viewTheProjects": "Se projekterne", "app.components.ProjectCard.viewTheProposals": "Se forslagene", "app.components.ProjectCard.viewTheQuestions": "Se spørgsmålene", "app.components.ProjectCard.vote": "<PERSON><PERSON>", "app.components.ProjectCard.xComments": "{commentsCount, plural, no {# kommentarer} one {# kommentar} other {# kommentarer}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, no {# bidrag} one {# bidrag} other {# bidrag}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {ingen idéer} one {# idé} other {# idéer}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# borgerforslag} one {# borgerforslag} other {# borgerforslag}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, no {# hørings<PERSON>var} one {# høringssvar} other {# høringssvar}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, no {# forslag} one {# forslag} other {# forslag}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petitions} one {# petition} other {# petitions}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, no {# projekter} one {# projekt} other {# projekter}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# forslag} one {# forslag} other {# forslag}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, no {# spørgsm<PERSON>l} one {# spørgsmål} other {# spørgsmål}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# kommentarer} one {# kommentar} other {# kommentarer}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# inputs} one {# input} other {# inputs}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projekter} one {# projekt} other {# projekter}}", "app.components.ProjectFolderCards.components.Topbar.all": "Alle", "app.components.ProjectFolderCards.components.Topbar.archived": "Arkiveret", "app.components.ProjectFolderCards.components.Topbar.draft": "Udkast", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filtrere efter", "app.components.ProjectFolderCards.components.Topbar.published2": "Offentliggjort", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tag", "app.components.ProjectFolderCards.noProjectYet": "Der er endnu ikke noget projekt", "app.components.ProjectFolderCards.noProjectsAvailable": "Ingen tilgængelige projekter", "app.components.ProjectFolderCards.showMore": "Vis mere", "app.components.ProjectFolderCards.stayTuned": "<PERSON><PERSON><PERSON><PERSON> med, der dukker snart et projekt op.", "app.components.ProjectFolderCards.tryChangingFilters": "<PERSON><PERSON><PERSON><PERSON> at ændre de valgte filtre.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Også brugt i disse byer:", "app.components.ProjectTemplatePreview.copied": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.copyLink": "Kopier link", "app.components.QuillEditor.alignCenter": "Centrer te<PERSON>t", "app.components.QuillEditor.alignLeft": "Venstrejuster", "app.components.QuillEditor.alignRight": "Højrejuster", "app.components.QuillEditor.bold": "Fed", "app.components.QuillEditor.clean": "Fjern formatering", "app.components.QuillEditor.customLink": "<PERSON><PERSON><PERSON><PERSON><PERSON> knap", "app.components.QuillEditor.customLinkPrompt": "Indtast link:", "app.components.QuillEditor.edit": "<PERSON><PERSON>", "app.components.QuillEditor.image": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.imageAltPlaceholder": "<PERSON><PERSON> beskrivelse af billedet", "app.components.QuillEditor.italic": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.link": "Tilføj link", "app.components.QuillEditor.linkPrompt": "Indtast link:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Fastsat liste", "app.components.QuillEditor.remove": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.save": "Gem", "app.components.QuillEditor.subtitle": "Undertitel", "app.components.QuillEditor.title": "Titel", "app.components.QuillEditor.unorderedList": "Ikke-fastsat liste", "app.components.QuillEditor.video": "Til<PERSON>øj video", "app.components.QuillEditor.videoPrompt": "Indtast video:", "app.components.QuillEditor.visitPrompt": "Besøg link:", "app.components.ReactionControl.completeProfileToReact": "Udfyld din profil for at reagere", "app.components.ReactionControl.dislike": "Kan ikke lide", "app.components.ReactionControl.dislikingDisabledMaxReached": "Du har nået dit maksimale antal dislikes i {projectName}", "app.components.ReactionControl.like": "<PERSON><PERSON><PERSON> godt om", "app.components.ReactionControl.likingDisabledMaxReached": "Du har opnået dit maksimale antal stemmer i {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "<PERSON>fs<PERSON><PERSON> aktiveres, n<PERSON><PERSON> denne fase starter", "app.components.ReactionControl.reactingDisabledPhaseOver": "Det er ikke længere muligt at stemme i denne fase", "app.components.ReactionControl.reactingDisabledProjectInactive": "Du kan ikke længere reagere på ideer i {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Afstemning er i øjeblikket ikke aktiveret for dette projekt", "app.components.ReactionControl.reactingNotPermitted": "Afstemning er kun aktiveret for bestemte grupper", "app.components.ReactionControl.reactingNotSignedIn": "Log ind for at stemme.", "app.components.ReactionControl.reactingPossibleLater": "Afstemningen starter den {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Bekræft din identitet for at kunne stemme.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Begivenhedsdato: {startDate} på {startTime} til {endDate} på {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Begivenhedsdato: {eventDate} fra {startTime} til {endTime}.", "app.components.Sharing.linkCopied": "<PERSON> k<PERSON>", "app.components.Sharing.or": "eller", "app.components.Sharing.share": "Del", "app.components.Sharing.shareByEmail": "Del via e-mail", "app.components.Sharing.shareByLink": "Kopier link", "app.components.Sharing.shareOnFacebook": "<PERSON>", "app.components.Sharing.shareOnTwitter": "<PERSON>", "app.components.Sharing.shareThisEvent": "<PERSON> beg<PERSON>", "app.components.Sharing.shareThisFolder": "Del", "app.components.Sharing.shareThisProject": "{tenant<PERSON><PERSON>, select, DeloitteDK {<PERSON> dette} other {Del dette projekt}}", "app.components.Sharing.shareViaMessenger": "<PERSON> via Messenger", "app.components.Sharing.shareViaWhatsApp": "Del med WhatsApp", "app.components.SideModal.closeButtonAria": "Luk", "app.components.StatusModule.futurePhase": "Du ser på en fase, der ikke er startet endnu. <PERSON> vil kunne deltage, n<PERSON>r fasen starter.", "app.components.StatusModule.modifyYourSubmission1": "<PERSON><PERSON><PERSON> din indsendelse", "app.components.StatusModule.submittedUntil3": "Din stemme kan afgives indtil", "app.components.TopicsPicker.numberOfSelectedTopics": "Valgt {numberOfSelectedTopics, plural, =0 {zero topics} et {zero topics} andet {# topics}}.{selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "<PERSON><PERSON><PERSON>", "app.components.UI.MoreActionsMenu.moreOptions": "<PERSON><PERSON><PERSON> v<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.UI.MoreActionsMenu.showMoreActions": "<PERSON>is flere <PERSON>er", "app.components.UI.NewLabel.new": "NY", "app.components.UI.PhaseFilter.noAppropriatePhases": "Ingen passende faser fundet til dette projekt", "app.components.UI.RemoveImageButton.a11y_removeImage": "<PERSON><PERSON><PERSON>", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "Oversæt", "app.components.Unauthorized.additionalInformationRequired": "Yderligere oplysninger er nødvendige for, at du kan deltage.", "app.components.Unauthorized.completeProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> profil", "app.components.Unauthorized.completeProfileTitle": "Udfyld din profil for at deltage", "app.components.Unauthorized.noPermission": "Du har ikke tilladelse til at se denne side", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON>, du er ikke autoriseret til at få adgang til denne side.", "app.components.Upload.errorImageMaxSizeExceeded": "<PERSON><PERSON><PERSON>, du har valgt, er større end {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Et eller flere valgte billede(r) overstiger den maksimalt tilladte størrelse på {maxFileSize} Mb pr. billede", "app.components.Upload.onlyOneImage": "Du kan kun overføre 1 billede", "app.components.Upload.onlyXImages": "Du kan kun overføre {maxItemsCount} billeder", "app.components.Upload.remaining": "resterende", "app.components.Upload.uploadImageLabel": "Væ<PERSON><PERSON> et billede (max. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "<PERSON><PERSON><PERSON><PERSON> et eller flere billeder", "app.components.UpsellTooltip.tooltipContent": "Denne funktion er ikke inkluderet i din nuværende plan. Tal med S<PERSON><PERSON> eller jeres administrator for at låse den op.", "app.components.UserName.anonymous": "Anonym", "app.components.UserName.anonymousTooltip2": "<PERSON><PERSON> bruger har besluttet at anonymisere sit bidrag", "app.components.UserName.authorWithNoNameTooltip": "Dit navn er blevet autogenereret, fordi du ikke har indtastet dit navn. Opdater venligst din profil, hvis du ønsker at ændre det.", "app.components.UserName.deletedUser": "uk<PERSON>t forfatter", "app.components.UserName.verified": "Verificeret", "app.components.VerificationModal.verifyAuth0": "Bekræft med MitID", "app.components.VerificationModal.verifyBOSA": "Verificer med itsme eller eID", "app.components.VerificationModal.verifyBosaFas": "Verificer med itsme eller eID", "app.components.VerificationModal.verifyClaveUnica": "Verificer med Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "Bekræft med falsk SSO", "app.components.VerificationModal.verifyIdAustria": "Bekræft med ID Østrig", "app.components.VerificationModal.verifyKeycloak": "Bekræft med ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Bekræft med MitID", "app.components.VerificationModal.verifyTwoday2": "Bekræft med BankID eller Freja eID+.", "app.components.VerificationModal.verifyYourIdentity": "{tenant<PERSON><PERSON>, select, DeloitteDK {Bekræft din bruger} other {Bekræft din identitet}}", "app.components.VoteControl.budgetingFutureEnabled": "Du kan tildele dit budget startende fra {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Deltagende budgettering er kun aktiveret for bestemte grupper.", "app.components.VoteControl.budgetingNotPossible": "Det er ikke muligt at foretage ændringer i dit budget på nuværende tidspunkt.", "app.components.VoteControl.budgetingNotVerified": "Venligst {verifyAccountLink} for at fortsætte.", "app.components.VoteInputs._shared.currencyLeft1": "Du har {budgetLeft} / {totalBudget} tilbage", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Du har {votesLeft, plural, =0 {ingen kreditter tilbage} other {# ud af {totalNumberOfVotes, plural, one {1 kredit} other {# kreditter}} tilbage}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Du har {votesLeft, plural, =0 {ingen point tilbage} other {# ud af {totalNumberOfVotes, plural, one {1 point} other {# point}} tilbage}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Du har {votesLeft, plural, =0 {ingen tokens tilbage} other {# ud af {totalNumberOfVotes, plural, one {1 token} other {# tokens}} tilbage}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Du har {votesLeft, plural, =0 {ingen stemmer tilbage} other {# ud af {totalNumberOfVotes, plural, one {1 stemme} other {# stemmer}} tilbage}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Du har allerede indsendt dit budget. H<PERSON> du vil ændre det, skal du klikke på \"Ænd<PERSON> din indsendelse\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Du har allerede indsendt dit budget. Hvis du vil ændre det, skal du gå tilbage til projektsiden og klikke på \"Ændre din indsendelse\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budgettering er ikke til<PERSON>, da denne fase ikke er aktiv.", "app.components.VoteInputs.single.youHaveVotedForX2": "Du har stemt på {votes, plural, =0 {# muligheder} one {# mulighed} other {# muligheder}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON> betyder, at du mister alle data, der er forbundet med dette input, såsom kommentarer, reaktioner og stemmer. <PERSON><PERSON> handling kan ikke fortrydes.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "<PERSON>r du sikker på, at du ønsker at slette dette input?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Bekræft", "app.components.admin.SlugInput.resultingURL": "Resulterende URL", "app.components.admin.SlugInput.slugTooltip": "Sluggen er det unikke sæt ord i slutningen af sidens webadresse eller URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "<PERSON><PERSON> du ændrer URL'en, vil links til siden med den gamle URL ikke længere fungere.", "app.components.admin.SlugInput.urlSlugLabel": "Snegl", "app.components.admin.UserFilterConditions.addCondition": "Tilføj en betingelse", "app.components.admin.UserFilterConditions.field_email": "E-mail", "app.components.admin.UserFilterConditions.field_event_attendance": "Registrering af begivenheder", "app.components.admin.UserFilterConditions.field_follow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_lives_in": "<PERSON><PERSON> i", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Undersøgelse af Borgerbarometer", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Interagerede med et input med status", "app.components.admin.UserFilterConditions.field_participated_in_project": "Bidrog til projektet", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Har sendt noget med et mærke", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Registreringsdato", "app.components.admin.UserFilterConditions.field_role": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_verified": "Verifikation", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideation", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Borgerforslag", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "er ikke tilmeldt nogen af disse begivenheder", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "er ikke tilmeldt nogen begivenhed", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "er tilmeldt en af disse begivenheder", "app.components.admin.UserFilterConditions.predicate_attends_something": "er tilmeldt mindst én begivenhed", "app.components.admin.UserFilterConditions.predicate_begins_with": "begynder med", "app.components.admin.UserFilterConditions.predicate_commented_in": "kommenteret", "app.components.admin.UserFilterConditions.predicate_contains": "indeholder", "app.components.admin.UserFilterConditions.predicate_ends_on": "slutter på", "app.components.admin.UserFilterConditions.predicate_has_value": "har værdi", "app.components.admin.UserFilterConditions.predicate_in": "har udført en hvilken som helst handling", "app.components.admin.UserFilterConditions.predicate_is": "er", "app.components.admin.UserFilterConditions.predicate_is_admin": "er en administrator", "app.components.admin.UserFilterConditions.predicate_is_after": "er efter", "app.components.admin.UserFilterConditions.predicate_is_before": "er før", "app.components.admin.UserFilterConditions.predicate_is_checked": "er kontrolleret", "app.components.admin.UserFilterConditions.predicate_is_empty": "er tom", "app.components.admin.UserFilterConditions.predicate_is_equal": "er", "app.components.admin.UserFilterConditions.predicate_is_exactly": "er præcis", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "er større end", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "er større end eller lig med", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "er en normal bruger", "app.components.admin.UserFilterConditions.predicate_is_not_area": "ekskluderer område", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "udelukker mappe", "app.components.admin.UserFilterConditions.predicate_is_not_input": "udelukker input", "app.components.admin.UserFilterConditions.predicate_is_not_project": "udelukker projekt", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "udelukker emne", "app.components.admin.UserFilterConditions.predicate_is_one_of": "er en af", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "et af områderne", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "en af mapperne", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "et input", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "et af projekterne", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "et af emnerne", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "er en projektleder", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "er mindre end", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "er mindre end eller lig med", "app.components.admin.UserFilterConditions.predicate_is_verified": "er verificeret", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "begynder ikke med", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "ikke kommenterede ikke", "app.components.admin.UserFilterConditions.predicate_not_contains": "indeholder ikke", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "slutter ikke på", "app.components.admin.UserFilterConditions.predicate_not_has_value": "ikke har værdi", "app.components.admin.UserFilterConditions.predicate_not_in": "ikke bidrog ikke", "app.components.admin.UserFilterConditions.predicate_not_is": "er ikke", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "er ikke en admin", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "er ikke kontrolleret", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "er ikke tom", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "er ikke", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "er ikke en normal bruger", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "er ikke en af", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "er ikke en projektleder", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "er ikke verificeret", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "sendte ikke et input", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "reagerede ikke på kommentar", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "stemte ikke på input", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "har ikke tilmeldt sig en begivenhed", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "har ikke deltaget i undersøgelsen", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "har ikke meldt sig frivilligt", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "deltog ikke i afstemningen", "app.components.admin.UserFilterConditions.predicate_nothing": "ingenting", "app.components.admin.UserFilterConditions.predicate_posted_input": "sendte et input", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "stemte på en kommentar", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "stemte på et input", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "tilmeldt en begivenhed", "app.components.admin.UserFilterConditions.predicate_something": "noget", "app.components.admin.UserFilterConditions.predicate_taken_survey": "har taget under<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "meldte sig frivilligt", "app.components.admin.UserFilterConditions.predicate_voted_in3": "deltog i afstemningen", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Attribut", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Betingelse", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "<PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Du vil ikke få meddelelser om dit bidrag", "app.components.anonymousParticipationModal.cancel": "<PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.continue": "Fortsæt", "app.components.anonymousParticipationModal.participateAnonymously": "Deltag anonymt", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "<PERSON><PERSON> vil sikkert <b>sk<PERSON><PERSON> din profil</b> for <PERSON><PERSON>, moderatorer og andre borgere for dette specifikke bidrag, så ingen er i stand til at linke dette bidrag til dig. Anonyme bidrag kan ikke redigeres og betragtes som endelige.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "At gøre vores platform sikker for alle brugere er en topprioritet for os. <PERSON><PERSON> bet<PERSON> noget, s<PERSON> vær venlige over for hi<PERSON><PERSON>.", "app.components.avatar.titleForAccessibility": "Profil af {fullName}", "app.components.customFields.mapInput.removeAnswer": "<PERSON><PERSON><PERSON> svar", "app.components.customFields.mapInput.undo": "<PERSON><PERSON><PERSON>", "app.components.customFields.mapInput.undoLastPoint": "<PERSON><PERSON><PERSON> sids<PERSON>t", "app.components.followUnfollow.follow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.followUnfollow.followADiscussion": "<PERSON><PERSON><PERSON><PERSON>", "app.components.followUnfollow.followTooltipInputPage2": "Følgende udløser e-mailopdateringer om statusændringer, officielle opdateringer og kommentarer. Du kan til enhver tid gå til  {unsubscribeLink} .", "app.components.followUnfollow.followTooltipProjects2": "Følgende udløser e-mailopdateringer om projektændringer. Du kan til enhver tid gå til {unsubscribeLink} .", "app.components.followUnfollow.unFollow": "Følg ikke", "app.components.followUnfollow.unsubscribe": "afmeld abonnement", "app.components.followUnfollow.unsubscribeUrl": "/profil/rediger", "app.components.form.ErrorDisplay.guidelinesLinkText": "vores retning<PERSON>linjer", "app.components.form.ErrorDisplay.next": "<PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.previous": "Tidligere", "app.components.form.ErrorDisplay.save": "Gem", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Begynd at skrive for at søge efter brugerens e-mail eller navn...", "app.components.form.anonymousSurveyMessage2": "Alle svar på denne undersøgelse er anonymiseret.", "app.components.form.backToInputManager": "Tilbage til input manager", "app.components.form.backToProject": "Tilbage til projektet", "app.components.form.components.controls.mapInput.removeAnswer": "<PERSON><PERSON><PERSON> svar", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "<PERSON><PERSON><PERSON> sids<PERSON>t", "app.components.form.controls.addressInputAriaLabel": "Indtastning af adresse", "app.components.form.controls.addressInputPlaceholder6": "Indtast en adresse...", "app.components.form.controls.adminFieldTooltip": "<PERSON>lt kun synligt for administratorer", "app.components.form.controls.allStatementsError": "Der skal vælges et svar for alle udsagn.", "app.components.form.controls.back": "Tilbage", "app.components.form.controls.clearAll": "Ryd alt", "app.components.form.controls.clearAllScreenreader": "<PERSON><PERSON> alle svar fra ovenstående matrixspørgsmål", "app.components.form.controls.clickOnMapMultipleToAdd3": "Klik på kortet for at tegne. Træk derefter i punkterne for at flytte dem.", "app.components.form.controls.clickOnMapToAddOrType": "<PERSON><PERSON> på kortet eller skriv en adresse nedenfor for at tilføje dit svar.", "app.components.form.controls.confirm": "Bekræft", "app.components.form.controls.cosponsorsPlaceholder": "Begynd at skrive et navn for at søge", "app.components.form.controls.currentRank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rang:", "app.components.form.controls.minimumCoordinates2": "<PERSON> kræves mindst {numPoints} kortpunkter.", "app.components.form.controls.noRankSelected": "Ingen rang valgt", "app.components.form.controls.notPublic1": "*<PERSON><PERSON> svar vil kun blive delt med projektledere og ikke med offentligheden.", "app.components.form.controls.optionalParentheses": "(valgfri)", "app.components.form.controls.rankingInstructions": "<PERSON><PERSON><PERSON><PERSON> og slip for at rangordne muligheder.", "app.components.form.controls.selectAsManyAsYouLike": "*<PERSON><PERSON><PERSON>g så mange, som du vil", "app.components.form.controls.selectBetween": "*Vælg mellem {minItems} og {maxItems} muligheder", "app.components.form.controls.selectExactly2": "*Væ<PERSON>g nøjagtigt {selectExactly, plural, one {# mulighed} other {# muligheder}}", "app.components.form.controls.selectMany": "*Vælg så mange som du vil", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Tryk på kortet for at tegne. Træk derefter i punkterne for at flytte dem.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Tryk på kortet for at tegne.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Tryk på kortet for at tilføje dit svar.", "app.components.form.controls.tapOnMapToAddOrType": "<PERSON><PERSON> på kortet, eller skriv en adresse nedenfor for at tilføje dit svar.", "app.components.form.controls.tapToAddALine": "Tryk for at tilføje en linje", "app.components.form.controls.tapToAddAPoint": "Tryk for at tilføje et punkt", "app.components.form.controls.tapToAddAnArea": "Tryk for at tilføje et område", "app.components.form.controls.uploadShapefileInstructions": "* Upload en zip-fil, der indeholder en eller flere shapefiler.", "app.components.form.controls.validCordinatesTooltip2": "<PERSON><PERSON> place<PERSON>en ikke vises blandt m<PERSON>, mens du skriver, kan du tilføje gyldige koordinater i formatet 'breddegrad, længdegrad' for at angive en præcis placering (f.eks.: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} af {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} ud af {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} ud af {total}, hvor {maxValue} er {maxLabel}", "app.components.form.error": "<PERSON><PERSON><PERSON>", "app.components.form.locationGoogleUnavailable": "<PERSON>nne ikke indlæse feltet for placering leveret af Google Maps.", "app.components.form.progressBarLabel": "Fremdrift i undersøgelsen", "app.components.form.submit": "Indsend", "app.components.form.submitApiError": "Der opstod et problem med at indsende formularen. Kontroller for eventuelle fejl, og prøv igen.", "app.components.form.verifiedBlocked": "Du kan ikke redigere dette felt da det indeholder verificerede informationer", "app.components.formBuilder.Page": "Side", "app.components.formBuilder.accessibilityStatement": "{tenant<PERSON><PERSON>, select, HillerodKommune {WAS} other {Webtilgængelighed}}", "app.components.formBuilder.addAnswer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.addStatement": "Tilføj udsagn", "app.components.formBuilder.agree": "<PERSON><PERSON>", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "<PERSON><PERSON> du har adgang til vores AI-pakke, vil du kunne opsummere og kategorisere tekstsvar med AI.", "app.components.formBuilder.askFollowUpToggleLabel": "<PERSON><PERSON><PERSON><PERSON> om opfølgning", "app.components.formBuilder.bad": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.buttonLabel": "Etiket til knap", "app.components.formBuilder.buttonLink": "Link til knap", "app.components.formBuilder.cancelLeaveBuilderButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.category": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.chooseMany": "<PERSON><PERSON><PERSON><PERSON> mange", "app.components.formBuilder.chooseOne": "Væ<PERSON>g en", "app.components.formBuilder.close": "Luk", "app.components.formBuilder.closed": "Lukket", "app.components.formBuilder.configureMap": "Konfigurer kort", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON><PERSON>, jeg vil gerne logge af", "app.components.formBuilder.content": "Indhold", "app.components.formBuilder.continuePageLabel": "Fortsætter til", "app.components.formBuilder.cosponsors": "Medstillere", "app.components.formBuilder.default": "Standard", "app.components.formBuilder.defaultContent": "Standardindhold", "app.components.formBuilder.delete": "Slet", "app.components.formBuilder.deleteButtonLabel": "Slet", "app.components.formBuilder.description": "Beskrivelse", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Dette er allerede blevet tilføjet i formularen. Standardindhold kan kun bruges én gang.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Tilføjelse af brugerdefineret indhold er ikke en del af din nuværende licens. Kontakt Søren <PERSON>t for at få mere at vide om det.", "app.components.formBuilder.disagree": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.displayAsDropdown": "Vis som dropdown", "app.components.formBuilder.displayAsDropdownTooltip": "<PERSON>is mulighederne i en dropdown. <PERSON><PERSON> du har mange muligheder, anbe<PERSON><PERSON> de<PERSON>.", "app.components.formBuilder.done": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.drawArea": "Tegn område", "app.components.formBuilder.drawRoute": "Tegn en rute", "app.components.formBuilder.dropPin": "Drop pin", "app.components.formBuilder.editButtonLabel": "<PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "Giv mindst 1 svar. Bemærk venligst, at hvert svar skal have en titel.", "app.components.formBuilder.emptyOptionError": "Giv mindst 1 svar", "app.components.formBuilder.emptyStatementError": "Giv mindst 1 erklæring", "app.components.formBuilder.emptyTitleError": "Angiv en spørgsmålstitel", "app.components.formBuilder.emptyTitleMessage": "Giv en titel til alle svarene", "app.components.formBuilder.emptyTitleStatementMessage": "Giv en titel for alle udsagnene", "app.components.formBuilder.enable": "Aktiver", "app.components.formBuilder.errorMessage": "Der er et problem, ud<PERSON><PERSON> problemet for at kunne gemme dine ændringer", "app.components.formBuilder.fieldGroup.description": "Beskrivelse (valgfrit)", "app.components.formBuilder.fieldGroup.title": "Titel (valgfrit)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "I øjeblikket er svarene på disse spørgsmål kun tilgængelige i den eksporterede excel-fil på Input Manager og ikke synlige for brugerne.", "app.components.formBuilder.fieldLabel": "<PERSON>g af svar", "app.components.formBuilder.fieldLabelStatement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.fileUpload": "Upload af fil", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Kortbaseret side", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Indsæt kort som kontekst, eller stil stedsbaserede spørgsmål til deltagerne.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "For at opnå en optimal brugeroplevelse anbefaler vi ikke at tilføje spørgsmål om punkter, ruter eller områder til kortbaserede sider.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normal side", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Kortlægningsfunktioner er ikke inkluderet i din nuværende licens. Kontakt din GovSuccess Manager for at få mere at vide.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Sidetype", "app.components.formBuilder.formEnd": "<PERSON><PERSON><PERSON> a<PERSON>", "app.components.formBuilder.formField.cancelDeleteButtonText": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "<PERSON><PERSON>, slet siden", "app.components.formBuilder.formField.copyNoun": "<PERSON><PERSON>", "app.components.formBuilder.formField.copyVerb": "<PERSON><PERSON>", "app.components.formBuilder.formField.delete": "Slet", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "<PERSON><PERSON> du sletter denne side, slettes og<PERSON><PERSON> den logik, der er knyttet til den. Er du sikker på, at du ønsker at slette den?", "app.components.formBuilder.formField.deleteResultsInfo": "<PERSON>te kan ikke fortrydes", "app.components.formBuilder.goToPageInputLabel": "Så er næste side:", "app.components.formBuilder.good": "God", "app.components.formBuilder.helmetTitle": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.imageFileUpload": "Upload af billede", "app.components.formBuilder.invalidLogicBadgeMessage": "Ugyldig logik", "app.components.formBuilder.labels2": "<PERSON><PERSON><PERSON><PERSON> (valgfrit)", "app.components.formBuilder.labelsTooltipContent2": "Væ<PERSON>g valgfrie etiketter til enhver af de lineære skalaværdier.", "app.components.formBuilder.lastPage": "Afslutning", "app.components.formBuilder.layout": "Layout", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "<PERSON>r du sikker på, at du vil forlade os?", "app.components.formBuilder.leaveBuilderText": "<PERSON> har ænd<PERSON>, der ikke er gemt. <PERSON><PERSON> ven<PERSON>, før du går. <PERSON><PERSON> du går, mister du dine ændringer.", "app.components.formBuilder.limitAnswersTooltip": "<PERSON><PERSON><PERSON> det er slået til, skal respondenterne vælge det angivne antal svar for at komme videre.", "app.components.formBuilder.limitNumberAnswers": "<PERSON><PERSON><PERSON><PERSON><PERSON> antallet af svar", "app.components.formBuilder.linePolygonMapWarning2": "Linje- og polygontegninger opfylder muligvis ikke tilgængelighedsstandarderne. Du kan finde flere oplysninger på {accessibilityStatement}.", "app.components.formBuilder.linearScale": "<PERSON><PERSON><PERSON> skala", "app.components.formBuilder.locationDescription": "Placering", "app.components.formBuilder.logic": "Logik", "app.components.formBuilder.logicAnyOtherAnswer": "<PERSON><PERSON><PERSON> andet svar", "app.components.formBuilder.logicConflicts.conflictingLogic": "Modstridende logik", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Denne side indeholder sp<PERSON><PERSON><PERSON><PERSON><PERSON>, der fører til forskellige sider. <PERSON><PERSON> deltagerne besvarer flere spørgsmål, vises den side, der er længst væk. <PERSON><PERSON><PERSON> for, at denne adfærd stemmer overens med dit tilsigtede flow.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Denne side har flere logiske regler: spørgsmålslogik med flere valg, logik på sideniveau og logik mellem spørgsmålene. Når disse betingelser overlapper hi<PERSON><PERSON>, vil spørgsmålslogikken have forrang for sidelogikken, og den side, der er længst væk, vil blive vist. Gennemgå logikken for at sikre, at den stemmer overens med dit tilsigtede flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Denne side indeholder et spørgsmål med flere valgmuligheder, som fører til forskellige sider. <PERSON>vis deltagerne vælger flere muligheder, vises den side, der er længst væk. <PERSON><PERSON><PERSON> for, at denne adfærd stemmer overens med dit tilsigtede flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Denne side indeholder et spørgsmål med flere valgmuligheder, der fører til forskellige sider, og har spørgsm<PERSON><PERSON>, der fører til andre sider. Den fjerneste side vil blive vist, hvis disse betingelser overlapper hinanden. <PERSON><PERSON><PERSON> for, at denne adfærd stemmer overens med dit tilsigtede flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Denne side indeholder et spørgsmål med flere valgmuligheder, hvor valgmulighederne fører til forskellige sider, og hvor logikken er indstillet på både side- og spørgsmålsniveau. Spørgsmålslogikken har forrang, og den side, der er længst væk, vises. <PERSON><PERSON><PERSON> for, at denne adfærd stemmer overens med dit tilsigtede flow.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Denne side har logik indstillet på både sideniveau og spørgsmålsniveau. Spørgsmålslogik vil have forrang for logik på sideniveau. <PERSON><PERSON><PERSON> for, at denne adfærd stemmer overens med dit tilsigtede flow.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Denne side har logik indstillet på både side- og spørgsmålsniveau, og flere spørgsmål leder til forskellige sider. Spørgsmålslogikken har forrang, og den side, der ligger længst væk, vises. <PERSON><PERSON><PERSON> for, at denne adfærd stemmer overens med dit tilsigtede flow.", "app.components.formBuilder.logicNoAnswer2": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.logicPanelAnyOtherAnswer": "H<PERSON> et andet svar", "app.components.formBuilder.logicPanelNoAnswer": "<PERSON><PERSON> ikke be<PERSON>", "app.components.formBuilder.logicValidationError": "Logik må ikke indeholde links til tidligere sider", "app.components.formBuilder.longAnswer": "<PERSON><PERSON> svar", "app.components.formBuilder.mapConfiguration": "Konfiguration af kortet", "app.components.formBuilder.mapping": "Kortlægning", "app.components.formBuilder.mappingNotInCurrentLicense": "Kortlægningsfunktioner er ikke inkluderet i din nuværende licens. Kontakt din GovSuccess Manager for at få mere at vide.", "app.components.formBuilder.matrix": "Matrix", "app.components.formBuilder.matrixSettings.columns": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "Flere valg", "app.components.formBuilder.multipleChoiceHelperText": "<PERSON><PERSON> flere muligheder fører til forskellige sider, og deltagerne vælger mere end én, vises den side, der er længst væk. <PERSON><PERSON><PERSON> for, at denne adfærd stemmer overens med dit tilsigtede flow.", "app.components.formBuilder.multipleChoiceImage": "<PERSON><PERSON> af <PERSON>e", "app.components.formBuilder.multiselect.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "Neutral", "app.components.formBuilder.newField": "<PERSON><PERSON> felt", "app.components.formBuilder.number": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Åbn", "app.components.formBuilder.optional": "Valg<PERSON><PERSON>", "app.components.formBuilder.other": "And<PERSON>", "app.components.formBuilder.otherOption": "Valgmuligheden \"Andet\"", "app.components.formBuilder.otherOptionTooltip": "<PERSON><PERSON> <PERSON><PERSON><PERSON> mulighed for at indtaste et tilpasset svar, hvis de angivne svar ikke matcher deres præferencer.", "app.components.formBuilder.page": "Side", "app.components.formBuilder.pageCannotBeDeleted": "Denne side kan ikke slettes.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Denne side kan ikke slettes, og det er ikke muligt at tilføje yderligere felter.", "app.components.formBuilder.pageRuleLabel": "Næste side er:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "<PERSON>vis der ikke tilføjes logik, vil formularen følge sit normale flow. <PERSON>vis både siden og dens spørgsmål har logik, vil spørgsmålslogikken have forrang. <PERSON><PERSON><PERSON> for, at dette stemmer overens med dit tilsigtede flow For mere information, besøg {supportPageLink}", "app.components.formBuilder.preview": "Eksempel:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Medsponsorer vises ikke på den downloadede PDF og understøttes ikke til import via FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "Spørgsmål om filupload vises som ikke-understøttet i den downloadede PDF og understøttes ikke til import via FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Kortlægningsspørgsmål vises på den downloadede PDF, men lagene vil ikke være synlige. Mapping-spørgsmål understøttes ikke til import via FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Matrixspørgsmål vises på den downloadede PDF, men understøttes ikke i øjeblikket til import via FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Sidetitler og beskrivelser vises som en sektionsoverskrift i den downloadede PDF.", "app.components.formBuilder.printSupportTooltip.ranking": "Rangeringsspørgsmål vises på den downloadede PDF, men understøttes ikke i øjeblikket til import via FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Tags vises som ikke-understøttede i den downloadede PDF og understøttes ikke til import via FormSync.", "app.components.formBuilder.proposedBudget": "Foreslået budget", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "<PERSON><PERSON> s<PERSON>ø<PERSON><PERSON> kan ikke slettes.", "app.components.formBuilder.questionDescriptionOptional": "Beskrivelse af spørgsmålet (valgfrit)", "app.components.formBuilder.questionTitle": "Spørgsmålets titel", "app.components.formBuilder.randomize": "Randomiser", "app.components.formBuilder.randomizeToolTip": "Rækkefølgen af svarene vil blive randomiseret pr. bruger.", "app.components.formBuilder.range": "<PERSON><PERSON>r<PERSON><PERSON>", "app.components.formBuilder.ranking": "Rangering", "app.components.formBuilder.rating": "Bed<PERSON><PERSON>lse", "app.components.formBuilder.removeAnswer": "<PERSON><PERSON><PERSON> svar", "app.components.formBuilder.required": "Krævet", "app.components.formBuilder.requiredToggleLabel": "<PERSON><PERSON><PERSON> af dette spørgsmål obligatorisk", "app.components.formBuilder.ruleForAnswerLabel": "<PERSON><PERSON> svaret er:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "<PERSON><PERSON> s<PERSON> inkluderer:", "app.components.formBuilder.save": "Gem", "app.components.formBuilder.selectRangeTooltip": "Vælg den maksimale værdi for din skala.", "app.components.formBuilder.sentiment": "Sentiment-skala", "app.components.formBuilder.shapefileUpload": "Esri shapefile upload", "app.components.formBuilder.shortAnswer": "<PERSON><PERSON> svar", "app.components.formBuilder.showResponseToUsersToggleLabel": "<PERSON>is svar til brugerne", "app.components.formBuilder.singleChoice": "Enkelt valg", "app.components.formBuilder.staleDataErrorMessage2": "Der er opstået et problem. Denne inputformular er for nylig blevet gemt et andet sted. Det kan skyldes, at du eller en anden bruger har den åben til redigering i et andet browservindue. Opdater venligst siden for at få den nyeste formular, og foretag derefter dine ændringer igen.", "app.components.formBuilder.stronglyAgree": "<PERSON><PERSON> enig", "app.components.formBuilder.stronglyDisagree": "<PERSON><PERSON> uenig", "app.components.formBuilder.supportArticleLinkText": "denne side", "app.components.formBuilder.tags": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.title": "Titel", "app.components.formBuilder.toLabel": "til", "app.components.formBuilder.unsavedChanges": "<PERSON> har æ<PERSON><PERSON>, der ikke er gemt", "app.components.formBuilder.useCustomButton2": "Brug brugerdefineret sideknap", "app.components.formBuilder.veryBad": "<PERSON><PERSON>", "app.components.formBuilder.veryGood": "Meget god", "app.components.ideas.similarIdeas.engageHere": "Engager dig her", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Ingen lignende indlæg fundet.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Vi fandt lignende bidrag - at engagere sig i dem, kan hjælpe med at gøre dem stærkere!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Lignende indlæg er allerede indsendt:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Leder efter lignende indlæg ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON><PERSON> end en dag} one {# dag} other {# dage}} tilbage", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft} uger tilbage", "app.components.screenReaderCurrency.AED": "De Forenede Arabiske Emiraters Dirham", "app.components.screenReaderCurrency.AFN": "Afghansk Afghansk", "app.components.screenReaderCurrency.ALL": "Albansk Lek", "app.components.screenReaderCurrency.AMD": "Armensk drama", "app.components.screenReaderCurrency.ANG": "Hollandsk antillisk gylden", "app.components.screenReaderCurrency.AOA": "Angolansk Kwanza", "app.components.screenReaderCurrency.ARS": "Argentinsk peso", "app.components.screenReaderCurrency.AUD": "Australske dollar", "app.components.screenReaderCurrency.AWG": "Arubansk florin", "app.components.screenReaderCurrency.AZN": "Aserbajdsjansk manat", "app.components.screenReaderCurrency.BAM": "Bosnien-Her<PERSON><PERSON>vina Convertible Mark", "app.components.screenReaderCurrency.BBD": "Barbadisk dollar", "app.components.screenReaderCurrency.BDT": "Bangladeshi Taka", "app.components.screenReaderCurrency.BGN": "Bulgarsk Lev", "app.components.screenReaderCurrency.BHD": "Bahrainsk dinar", "app.components.screenReaderCurrency.BIF": "Burundiske franc", "app.components.screenReaderCurrency.BMD": "Bermudiansk dollar", "app.components.screenReaderCurrency.BND": "Brunei-dollar", "app.components.screenReaderCurrency.BOB": "Boliviansk Boliviano", "app.components.screenReaderCurrency.BOV": "Boliviansk Mvdol", "app.components.screenReaderCurrency.BRL": "Brasiliansk real", "app.components.screenReaderCurrency.BSD": "Bahamas-dollar", "app.components.screenReaderCurrency.BTN": "Bhutanesisk Ngultrum", "app.components.screenReaderCurrency.BWP": "Botswansk pula", "app.components.screenReaderCurrency.BYR": "Hviderussisk rubel", "app.components.screenReaderCurrency.BZD": "Belize-dollar", "app.components.screenReaderCurrency.CAD": "Canadisk dollar", "app.components.screenReaderCurrency.CDF": "Congolesisk franc", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Schweizisk franc", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Chilensk regningsenhed (UF)", "app.components.screenReaderCurrency.CLP": "Chilensk peso", "app.components.screenReaderCurrency.CNY": "Kinesiske Yuan", "app.components.screenReaderCurrency.COP": "Colombiansk peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Rica Colón", "app.components.screenReaderCurrency.CRE": "Kredit", "app.components.screenReaderCurrency.CUC": "Cubansk konvertibel peso", "app.components.screenReaderCurrency.CUP": "Cubansk peso", "app.components.screenReaderCurrency.CVE": "Kapverdisk escudo", "app.components.screenReaderCurrency.CZK": "Tjekkisk koruna", "app.components.screenReaderCurrency.DJF": "Djiboutisk franc", "app.components.screenReaderCurrency.DKK": "Dansk krone", "app.components.screenReaderCurrency.DOP": "Dominikansk peso", "app.components.screenReaderCurrency.DZD": "Algerisk dinar", "app.components.screenReaderCurrency.EGP": "Egyptisk pund", "app.components.screenReaderCurrency.ERN": "Eritreisk Nakfa", "app.components.screenReaderCurrency.ETB": "Etiopisk birr", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Fijiansk dollar", "app.components.screenReaderCurrency.FKP": "Falklandsøernes pund", "app.components.screenReaderCurrency.GBP": "Britisk pund", "app.components.screenReaderCurrency.GEL": "Georgisk lari", "app.components.screenReaderCurrency.GHS": "Ghanesisk Cedi", "app.components.screenReaderCurrency.GIP": "Gibraltar-pund", "app.components.screenReaderCurrency.GMD": "Gambiansk dalasi", "app.components.screenReaderCurrency.GNF": "Guineansk franc", "app.components.screenReaderCurrency.GTQ": "Guatemalansk quetzal", "app.components.screenReaderCurrency.GYD": "Guyanesisk dollar", "app.components.screenReaderCurrency.HKD": "Hong Kong Dollar", "app.components.screenReaderCurrency.HNL": "Honduransk Lempira", "app.components.screenReaderCurrency.HRK": "Kroatisk Kuna", "app.components.screenReaderCurrency.HTG": "Haitiansk gurde", "app.components.screenReaderCurrency.HUF": "Ungarsk forint", "app.components.screenReaderCurrency.IDR": "Indonesisk rupiah", "app.components.screenReaderCurrency.ILS": "<PERSON>s nye shekel", "app.components.screenReaderCurrency.INR": "Indisk rupee", "app.components.screenReaderCurrency.IQD": "Irakisk dinar", "app.components.screenReaderCurrency.IRR": "Iransk rial", "app.components.screenReaderCurrency.ISK": "Islandsk Króna", "app.components.screenReaderCurrency.JMD": "Jamaicansk dollar", "app.components.screenReaderCurrency.JOD": "Jordansk dinar", "app.components.screenReaderCurrency.JPY": "Japansk yen", "app.components.screenReaderCurrency.KES": "Kenyansk shilling", "app.components.screenReaderCurrency.KGS": "Kirgisisk Som", "app.components.screenReaderCurrency.KHR": "Cambodjansk Riel", "app.components.screenReaderCurrency.KMF": "Comorisk franc", "app.components.screenReaderCurrency.KPW": "Nordkoreansk Won", "app.components.screenReaderCurrency.KRW": "Sydkoreansk Won", "app.components.screenReaderCurrency.KWD": "Kuwaitisk dinar", "app.components.screenReaderCurrency.KYD": "Caymanøernes dollar", "app.components.screenReaderCurrency.KZT": "Kasakhstansk tenge", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Libanesisk pund", "app.components.screenReaderCurrency.LKR": "Sri Lankas Rupee", "app.components.screenReaderCurrency.LRD": "Liberiansk dollar", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Litauisk litas", "app.components.screenReaderCurrency.LVL": "Lettiske lats", "app.components.screenReaderCurrency.LYD": "Libysk dinar", "app.components.screenReaderCurrency.MAD": "Marokkansk dirham", "app.components.screenReaderCurrency.MDL": "Moldovisk leu", "app.components.screenReaderCurrency.MGA": "Malagasy Ariary", "app.components.screenReaderCurrency.MKD": "Makedonsk denar", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "Mongolsk Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "Mauretanisk Ouguiya", "app.components.screenReaderCurrency.MUR": "Mauritisk rupee", "app.components.screenReaderCurrency.MVR": "Maldivisk Rufiyaa", "app.components.screenReaderCurrency.MWK": "Malawisk Kwacha", "app.components.screenReaderCurrency.MXN": "Mexicansk peso", "app.components.screenReaderCurrency.MXV": "Mexicansk Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malaysisk ringgit", "app.components.screenReaderCurrency.MZN": "Mozambikansk metical", "app.components.screenReaderCurrency.NAD": "Namibisk dollar", "app.components.screenReaderCurrency.NGN": "Nigeriansk naira", "app.components.screenReaderCurrency.NIO": "Nicaraguanske Córdoba", "app.components.screenReaderCurrency.NOK": "Norske kroner", "app.components.screenReaderCurrency.NPR": "Nepalesisk Rupee", "app.components.screenReaderCurrency.NZD": "New Zealandske dollar", "app.components.screenReaderCurrency.OMR": "Omansk rial", "app.components.screenReaderCurrency.PAB": "Panamansk Balboa", "app.components.screenReaderCurrency.PEN": "Peruviansk sol", "app.components.screenReaderCurrency.PGK": "Papua Ny Guineas Kina", "app.components.screenReaderCurrency.PHP": "Filippinsk peso", "app.components.screenReaderCurrency.PKR": "Pakistansk rupee", "app.components.screenReaderCurrency.PLN": "Polsk Złoty", "app.components.screenReaderCurrency.PYG": "Paraguayansk guaraní", "app.components.screenReaderCurrency.QAR": "Qatari Riyal", "app.components.screenReaderCurrency.RON": "Rumænsk leu", "app.components.screenReaderCurrency.RSD": "Serbisk dinar", "app.components.screenReaderCurrency.RUB": "Russisk rubel", "app.components.screenReaderCurrency.RWF": "Rwandisk franc", "app.components.screenReaderCurrency.SAR": "Saudi-riyal", "app.components.screenReaderCurrency.SBD": "Salomonøernes dollar", "app.components.screenReaderCurrency.SCR": "Seychellisk rupee", "app.components.screenReaderCurrency.SDG": "Sudanesisk pund", "app.components.screenReaderCurrency.SEK": "Svenske kroner", "app.components.screenReaderCurrency.SGD": "Singapore Dollar", "app.components.screenReaderCurrency.SHP": "Sankt Helena-pund", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "Somalisk shilling", "app.components.screenReaderCurrency.SRD": "Surinamesisk dollar", "app.components.screenReaderCurrency.SSP": "Sydsudanesisk pund", "app.components.screenReaderCurrency.STD": "São Tomé og Príncipe Do<PERSON>", "app.components.screenReaderCurrency.SYP": "Syrisk pund", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "Thailandske baht", "app.components.screenReaderCurrency.TJS": "Tadsjikistansk somoni", "app.components.screenReaderCurrency.TMT": "Turkmenistansk manat", "app.components.screenReaderCurrency.TND": "Tunesisk dinar", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Tongansk Paʻanga", "app.components.screenReaderCurrency.TRY": "Tyrkisk lira", "app.components.screenReaderCurrency.TTD": "Trinidad- og Tobago-dollar", "app.components.screenReaderCurrency.TWD": "Ny Taiwan-dollar", "app.components.screenReaderCurrency.TZS": "Tanzaniansk shilling", "app.components.screenReaderCurrency.UAH": "Ukrainsk Hryvnia", "app.components.screenReaderCurrency.UGX": "Ugandisk shilling", "app.components.screenReaderCurrency.USD": "Amerikanske dollar", "app.components.screenReaderCurrency.USN": "Amerikanske dollar (næste dag)", "app.components.screenReaderCurrency.USS": "Amerikanske dollar (samme dag)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguayansk peso", "app.components.screenReaderCurrency.UZS": "Usbekisk Som", "app.components.screenReaderCurrency.VEF": "Venezuelanske Bolívar", "app.components.screenReaderCurrency.VND": "Vietnamesisk Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoansk Tala", "app.components.screenReaderCurrency.XAF": "Centralafrikansk CFA-franc", "app.components.screenReaderCurrency.XAG": "Sølv (en troy ounce)", "app.components.screenReaderCurrency.XAU": "Guld (en troy ounce)", "app.components.screenReaderCurrency.XBA": "Europæisk sammensat enhed (EURCO)", "app.components.screenReaderCurrency.XBB": "Den Europæiske Monetære Enhed (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "Europæisk regningsenhed 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "Europæisk regningsenhed 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "Østcaribisk dollar", "app.components.screenReaderCurrency.XDR": "Særlige trækningsrettigheder", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "Vestafrikansk CFA-franc", "app.components.screenReaderCurrency.XPD": "Palladium (en troy ounce)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "Platin (en troy ounce)", "app.components.screenReaderCurrency.XTS": "Koder specifikt reserveret til testformål", "app.components.screenReaderCurrency.XXX": "Ingen valuta", "app.components.screenReaderCurrency.YER": "Yemenitisk rial", "app.components.screenReaderCurrency.ZAR": "Sydafrikanske Rand", "app.components.screenReaderCurrency.ZMW": "Zambisk Kwacha", "app.components.screenReaderCurrency.amount": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.currency": "Valuta", "app.components.trendIndicator.lastQuarter2": "sids<PERSON> k<PERSON>tal", "app.containers.AccessibilityStatement.applicability": "<PERSON><PERSON> tilgængelighedserklæring gælder for en {demoPlatformLink}, som er repræsentativ for dette websted; den bruger den samme kildekode og har den samme funktionalitet.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Vurderingsmetode", "app.containers.AccessibilityStatement.assesmentText2022": "Tilgængeligheden af dette websted er blevet vurderet af en ekstern enhed, der ikke er involveret i design- og udviklingsprocessen. Overensstemmelsen af førnævnte {demoPlatformLink} kan identificeres på denne {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "du kan ændre dine indstillinger", "app.containers.AccessibilityStatement.changePreferencesText": "{changePreferencesButton} til enhver tid.", "app.containers.AccessibilityStatement.conformanceExceptions": "Undtagelser fra overensstemmelsen", "app.containers.AccessibilityStatement.conformanceStatus": "Overensstemmelsesstatus", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Vi stræber efter at gøre vores indhold inkluderende for alle. I nogle tilfælde kan der imidlertid være utilgængeligt indhold på platformen som beskrevet nedenfor:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demo-websted", "app.containers.AccessibilityStatement.email": "E-mail:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "indlejrede surveyværktøjer", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "De indbyggede undersøgelsesværktøjer, der er tilgængelige til brug på denne platform, er tredjepartssoftware og er muligvis ikke tilgængelige.", "app.containers.AccessibilityStatement.exception_1": "Vores digitale borgerinddragelsesplatform håndterer brugergenereret indhold sendt af enkeltpersoner og organisationer. Det er muligt, at PDF-filer, billeder eller andre filtyper inklusive multimedier uploades til platformen som vedhæftede filer eller tilføjes til tekstfelter af platformbrugere. Disse dokumenter er muligvis ikke fuldt tilgængelige.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Vi glæder os over din feedback om tilgængeligheden af denne platform. Kontakt os via en af følgende metoder:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Proces for svar", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Bruxelles, Belgien", "app.containers.AccessibilityStatement.headTitle": "Erklæring om tilgængelighed | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} har forpligtet sig til at levere en platform, der er tilgængelig for alle brugere, uanset teknologi eller evne. De gældende relevante standarder for tilgængelighed overholdes i vores løbende bestræbelser på at maksimere tilgængeligheden og anvendeligheden af vores platforme for alle brugere.", "app.containers.AccessibilityStatement.mapping": "Kortlægning", "app.containers.AccessibilityStatement.mapping_1": "Kort på platformen lever delvist op til tilgængelighedsstandarder. Kortets udstrækning, zoom og UI-widgets kan styres ved hjælp af et tastatur, når man ser på kort. Administratorer kan også konfigurere stilen på kortlag i backoffice eller ved hjælp af Esri-integrationen for at skabe mere tilgængelige farvepaletter og symbologi. Brug af forskellige linje- eller polygonstile (f.eks. stiplede linjer) vil også hjælpe med at differentiere kortlag, hvor det er muligt, og selvom en sådan styling ikke kan konfigureres i vores platform på nuværende tidspunkt, kan den konfigureres, hvis man bruger kort med Esri-integrationen.", "app.containers.AccessibilityStatement.mapping_2": "Kort i platformen er ikke fuldt tilgængelige, da de ikke præsenterer basiskort, kortlag eller tendenser i dataene for brugere, der bruger skærmlæsere. Fuldt tilgængelige kort skal præsentere kortlagene hørbart og beskrive alle relevante tendenser i dataene. Desuden er linje- og polygonkorttegning i undersøgelser ikke tilgængelige, da figurer ikke kan tegnes ved hjælp af et tastatur. Alternative inputmetoder er ikke tilgængelige på nuværende tidspunkt på grund af teknisk kompleksitet.", "app.containers.AccessibilityStatement.mapping_3": "For at gøre linje- og polygonkort mere tilgængelige anbefaler vi at inkludere en introduktion eller forklaring i undersøgelsesspørgsmålet eller sidebeskrivelsen af, hvad kortet viser, og eventuelle relevante tendenser. Desuden kan der stilles et kort eller langt tekstspørgsmål, så respondenterne kan beskrive deres svar i almindelige vendinger, hvis det er nødvendigt (i stedet for at klikke på kortet). Vi anbefaler også at inkludere kontaktoplysninger til projektlederen, så respondenter, der ikke kan udfylde et kortspørgsmål, kan anmode om en alternativ metode til at besvare spørgsmålet (f.eks. videomøde).", "app.containers.AccessibilityStatement.mapping_4": "For Ideation-projekter og forslag er der en mulighed for at vise input i en kortvisning, som ikke er tilgængelig. For disse metoder er der dog en alternativ listevisning af input, som er tilgængelig.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Vores onlineworkshops har en komponent til live videostreaming, som i øjeblikket ikke understøtter undertekster.", "app.containers.AccessibilityStatement.pageDescription": "En erklæring om tilgængeligheden af denne platform", "app.containers.AccessibilityStatement.postalAddress": "Post adresse:", "app.containers.AccessibilityStatement.publicationDate": "Udgivelsesdato", "app.containers.AccessibilityStatement.publicationDate2024": "<PERSON><PERSON> tilgængelighedserklæring blev offentliggjort den 21. august 2024.", "app.containers.AccessibilityStatement.responsiveness": "<PERSON>i tilstræber at svare indenfor 1-2 arbejdsdage.", "app.containers.AccessibilityStatement.statusPageText": "statusside", "app.containers.AccessibilityStatement.technologiesIntro": "Tilgængeligheden af denne platform er afhængig af, at følgende teknologier fungerer:", "app.containers.AccessibilityStatement.technologiesTitle": "Teknologier", "app.containers.AccessibilityStatement.title": "Tilgængelighedserklæring", "app.containers.AccessibilityStatement.userGeneratedContent": "Brugergenereret indhold", "app.containers.AccessibilityStatement.workshops": "Værksted", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "<PERSON>vis du bruger Content Builder, kan du bruge mere avancerede layoutmuligheder. <PERSON><PERSON> sprog, hvor der ikke er noget indhold tilgængeligt i content builderen, vises den almindelige projektbeskrivelse i stedet.", "app.containers.AdminPage.ProjectDescription.linkText": "Rediger beskrivelse i Content Builder", "app.containers.AdminPage.ProjectDescription.saveError": "<PERSON><PERSON> gik galt, da jeg gemte projektbeskrivelsen.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Brug Content Builder til beskrivelse", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Ved at bruge Content Builder kan du bruge mere avancerede layoutmuligheder.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "<PERSON>is projekt", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "<PERSON><PERSON><PERSON><PERSON>sens afslutning", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Opret en smart gruppe", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "<PERSON><PERSON><PERSON>, der opfylder alle følgende betingelser, vil automatisk blive tilføjet gruppen:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "<PERSON><PERSON>t én regel", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Nogle betingelser er ufuldstændige", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Gem gruppe", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Konfiguration af smarte grupper er ikke en del af din nuværende licens. Kontakt Søren <PERSON>t for at få mere at vide om det.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Angiv et gruppenavn", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Bekræftelse er deaktiveret for din platform, fjern verificeringsreglen eller kontakt support.", "app.containers.App.appMetaDescription": "Velkommen til borgerinddragelsesplatformen for {orgName}.\nUdforsk lokale projekter og deltag i diskussionen!", "app.containers.App.loading": "Indlæser...", "app.containers.App.metaTitle1": "Platform for borgerinddragelse | {orgName}", "app.containers.App.skipLinkText": "Spring til hovedindhold", "app.containers.AreaTerms.areaTerm": "område", "app.containers.AreaTerms.areasTerm": "<PERSON><PERSON>r<PERSON><PERSON>", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Der findes allerede en konto med denne e-mailadresse. Du kan logge ud, logge ind med denne e-mailadresse og bekræfte din konto på indstillingssiden.", "app.containers.Authentication.steps.AccessDenied.close": "Luk", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Du opfylder ikke kravene til at deltage i denne proces.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Gå tilbage til single sign-on-verifikation", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Indtast venligst et token", "app.containers.Authentication.steps.Invitation.token": "Token", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Har du allerede en konto? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Log ind", "app.containers.CampaignsConsentForm.ally_categoryLabel": "E-mails i denne kategori", "app.containers.CampaignsConsentForm.messageError": "Der opstod en fejl ved lagring af dine notifikationer.", "app.containers.CampaignsConsentForm.messageSuccess": "Dine e-mailpræferencer er blevet gemt.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Hvilke slags e-mail-meddelelser vil du modtage?", "app.containers.CampaignsConsentForm.notificationsTitle": "Notifikationer", "app.containers.CampaignsConsentForm.submit": "Gem", "app.containers.ChangeEmail.backToProfile": "Tilbage til profilindstillinger", "app.containers.ChangeEmail.confirmationModalTitle": "Bekræft din e-mail", "app.containers.ChangeEmail.emailEmptyError": "Angiv en e-mail-adresse", "app.containers.ChangeEmail.emailInvalidError": "Angiv en e-mail-adresse i det korrekte format, f.eks. <EMAIL>", "app.containers.ChangeEmail.emailRequired": "Indtast venligst en e-mailadresse.", "app.containers.ChangeEmail.emailTaken": "Denne e-mail er allerede i brug.", "app.containers.ChangeEmail.emailUpdateCancelled": "E-mailopdatering er blevet annulleret.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "For at opdatere din e-mail skal du starte processen igen.", "app.containers.ChangeEmail.helmetDescription": "Ændre din e-mail side", "app.containers.ChangeEmail.helmetTitle": "Ændre din e-mail", "app.containers.ChangeEmail.newEmailLabel": "Ny e-mail", "app.containers.ChangeEmail.submitButton": "Indsend", "app.containers.ChangeEmail.titleAddEmail": "T<PERSON><PERSON><PERSON><PERSON> din e-mail", "app.containers.ChangeEmail.titleChangeEmail": "Ændre din e-mail", "app.containers.ChangeEmail.updateSuccessful": "Din e-mail er blevet opdateret med succes.", "app.containers.ChangePassword.currentPasswordLabel": "Nuværende adgangskode", "app.containers.ChangePassword.currentPasswordRequired": "Indtast din nuværende adgangskode", "app.containers.ChangePassword.goHome": "G<PERSON> til hjem", "app.containers.ChangePassword.helmetDescription": "Side om ændring af din adgangskode", "app.containers.ChangePassword.helmetTitle": "Ændre din adgangskode", "app.containers.ChangePassword.newPasswordLabel": "Ny adgangskode", "app.containers.ChangePassword.newPasswordRequired": "Indtast din nye adgangskode", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Angiv en adgangskode, der er mindst {minimumPasswordLength} tegn lang", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Din adgangskode er blevet opdateret med succes", "app.containers.ChangePassword.passwordEmptyError": "Indtast din adgangskode", "app.containers.ChangePassword.passwordsDontMatch": "Bekræft den nye adgangskode", "app.containers.ChangePassword.titleAddPassword": "Tilføj en adgangskode", "app.containers.ChangePassword.titleChangePassword": "Ændre din adgangskode", "app.containers.Comments.a11y_commentDeleted": "<PERSON><PERSON><PERSON><PERSON> slettet", "app.containers.Comments.a11y_commentPosted": "Kommentar opslået", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {ingen likes} one {1 like} other {# likes}}", "app.containers.Comments.a11y_undoLike": "<PERSON><PERSON><PERSON> like", "app.containers.Comments.addCommentError": "Noget gik galt. Prøv venligst igen senere.", "app.containers.Comments.adminCommentDeletionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.Comments.adminCommentDeletionConfirmButton": "<PERSON><PERSON> denne kommentar", "app.containers.Comments.cancelCommentEdit": "<PERSON><PERSON><PERSON>", "app.containers.Comments.childCommentBodyPlaceholder": "<PERSON><PERSON><PERSON><PERSON> et svar...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "<PERSON>ne kommentar er blevet slettet.", "app.containers.Comments.commentDeletionCancelButton": "<PERSON><PERSON> min kommentar", "app.containers.Comments.commentDeletionConfirmButton": "Slette min kommentar", "app.containers.Comments.commentLike": "<PERSON><PERSON><PERSON> godt om", "app.containers.Comments.commentReplyButton": "<PERSON><PERSON>", "app.containers.Comments.commentsSortTitle": "<PERSON><PERSON><PERSON><PERSON> kommentarer efter", "app.containers.Comments.completeProfileLinkText": "Udfylde din profil", "app.containers.Comments.completeProfileToComment": "{completeRegistrationLink} for at kommentere.", "app.containers.Comments.confirmCommentDeletion": "Er du sikker på at du vil slette denne kommentar? Der er ingen vej tilbage!", "app.containers.Comments.deleteComment": "Slet", "app.containers.Comments.deleteReasonDescriptionError": "Giv flere oplysninger om din begrundelse", "app.containers.Comments.deleteReasonError": "Angiv en grund", "app.containers.Comments.deleteReason_inappropriate": "Det er upassende eller stødende", "app.containers.Comments.deleteReason_irrelevant": "<PERSON><PERSON> hører ikke til her", "app.containers.Comments.deleteReason_other": "<PERSON><PERSON>", "app.containers.Comments.editComment": "<PERSON><PERSON>", "app.containers.Comments.guidelinesLinkText": "vores retning<PERSON>linjer", "app.containers.Comments.ideaCommentBodyPlaceholder": "<PERSON><PERSON><PERSON><PERSON> din kommentar her", "app.containers.Comments.internalCommentingNudgeMessage": "At komme med interne kommentarer er ikke inkluderet i din nuværende licens. Kontakt Søren <PERSON>t for at få mere at vide om det.", "app.containers.Comments.internalConversation": "<PERSON>n sam<PERSON>", "app.containers.Comments.loadMoreComments": "Hent flere kommentarer", "app.containers.Comments.loadingComments": "Indlæser kommentarer...", "app.containers.Comments.loadingMoreComments": "Henter flere kommentarer...", "app.containers.Comments.notVisibleToUsersPlaceholder": "<PERSON>ne kommentar er ikke synlig for almindelige brugere", "app.containers.Comments.postInternalComment": "Send en intern kommentar", "app.containers.Comments.postPublicComment": "Offentlig kommentar", "app.containers.Comments.profanityError": "Du har muligvis brugt et eller flere ord, der betragtes som bandeord af {guidelinesLink}. Du kan ændre din tekst ved at fjerne eventuelle bandeord, der måtte være anvendt.", "app.containers.Comments.publicDiscussion": "Offentlig diskussion", "app.containers.Comments.publishComment": "Indsend din kommentar", "app.containers.Comments.reportAsSpamModalTitle": "Hvorfor vil du rapportere dette som spam?", "app.containers.Comments.saveComment": "Gemt", "app.containers.Comments.signInLinkText": "log ind", "app.containers.Comments.signInToComment": "{signInLink} for at kommentere.", "app.containers.Comments.signUpLinkText": "registrer dig", "app.containers.Comments.verifyIdentityLinkText": "Bliv en verificeret borger", "app.containers.Comments.visibleToUsersPlaceholder": "<PERSON>ne kommentar er synlig for almindelige brugere", "app.containers.Comments.visibleToUsersWarning": "<PERSON><PERSON><PERSON><PERSON> der postes her, vil være synlige for almindelige brugere.", "app.containers.ContentBuilder.PageTitle": "Projektbeskrivelse", "app.containers.CookiePolicy.advertisingContent": "Reklamecookies kan bruges til at tilpasse og måle effektiviteten af eksterne markedsføringskampagner i forhold til forlovelse på denne platform. Vi viser ingen reklamer på denne platform, men du kan modtage personligt tilpassede reklamer baseret på de sider, du bes<PERSON><PERSON>.", "app.containers.CookiePolicy.advertisingTitle": "Cookies til reklame", "app.containers.CookiePolicy.analyticsContents": "Analytics-cookies sporer <PERSON><PERSON>, f. eks. hvilke sider der besøges og hvor længe. De kan også indsamle nogle tekniske data, herunder browseroplysninger, omtrentlig placering og IP-adresser. Vi bruger kun disse data internt for at fortsætte med at forbedre den overordnede brugeroplevelse og platformens funktion. Sådanne data kan også deles mellem Go Vocal og {orgName} for at vurdere og forbedre projekter på platformen. Bemærk, at dataene er anonyme og bruges på et aggregeret niveau - de identificerer dig ikke personligt. Det er dog muligt, at hvis disse data kombineres med andre datakilder, kan en sådan identifikation finde sted.", "app.containers.CookiePolicy.analyticsTitle": "Analyse-cookies", "app.containers.CookiePolicy.cookiePolicyDescription": "En detaljeret forklaring på, hvordan vi bruger cookies på denne platform", "app.containers.CookiePolicy.cookiePolicyTitle": "Cookiepolitik", "app.containers.CookiePolicy.essentialContent": "Nogle cookies er vigtige for at sikre, at denne platform fungerer korrekt. Disse vigtige cookies bruges primært til at autentificere din konto, når du besøger platformen, og til at gemme dit foretrukne sprog.", "app.containers.CookiePolicy.essentialTitle": "Vigtige cookies", "app.containers.CookiePolicy.externalContent": "Nogle af vores sider kan vise indhold fra eksterne udbydere, f.eks. YouTube eller Typeform. Vi har ikke kontrol over disse cookies fra tredjeparter, og visning af indhold fra disse eksterne udbydere kan også resultere i, at der installeres cookies på din enhed.", "app.containers.CookiePolicy.externalTitle": "Eksterne cookies", "app.containers.CookiePolicy.functionalContents": "Funktionelle cookies kan være aktiveret for at give besøgende mulighed for at modtage meddelelser om opdateringer og få adgang til supportkanaler direkte fra platformen.", "app.containers.CookiePolicy.functionalTitle": "Funktionelle cookies", "app.containers.CookiePolicy.headCookiePolicyTitle": "Cookiepolitik | {orgName}", "app.containers.CookiePolicy.intro": "Cookies er tekstfiler, der gemmes i browseren eller på harddisken på din computer eller mobilenhed, når du besøger et websted, og som webstedet kan henvise til ved efterfølgende besøg. Vi bruger cookies til at forstå, hvordan besøgende bruger denne platform for at forbedre dens design og anvendelse, til at huske dine præferencer (f. eks. dit foretrukne sprog) og til at understøtte nøglefunktioner for registrerede brugere og platformsadministratorer.", "app.containers.CookiePolicy.manageCookiesDescription": "Du kan til enhver tid aktivere eller deaktivere analyse-, markedsf<PERSON><PERSON>- og funktionelle cookies i dine cookiepræferencer. Du kan også slette alle eksisterende cookies manuelt eller automatisk via din internetbrowser. Cookies kan dog placeres igen efter dit samtykke ved eventuelle efterfølgende besøg på denne platform. Hvis du ikke sletter cookies, gemmes dine cookiepræferencer i 60 dage, hvorefter du vil blive bedt om dit samtykke igen.", "app.containers.CookiePolicy.manageCookiesPreferences": "Gå til dine {manageCookiesPreferencesButtonText} for at se en komplet liste over integrationer af tredjeparter, der anvendes på denne platform, og for at administrere dine præferencer.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "cookie-indstillinger", "app.containers.CookiePolicy.manageCookiesTitle": "H<PERSON>ndtering af dine cookies", "app.containers.CookiePolicy.viewPreferencesButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.viewPreferencesText": "Nedenstående cookie-kategorier gælder muligvis ikke for alle besøgende eller platforme; se dine {viewPreferencesButton} for at få en komplet liste over integrationer af tredjeparter, der gælder for dig.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Hvad bruger vi cookies til?", "app.containers.CustomPageShow.editPage": "Rediger side", "app.containers.CustomPageShow.goBack": "<PERSON><PERSON>", "app.containers.CustomPageShow.notFound": "Side ikke fundet", "app.containers.DisabledAccount.bottomText": "Du kan logge på igen fra {date}.", "app.containers.DisabledAccount.termsAndConditions": "vilkår og betingelser", "app.containers.DisabledAccount.text2": "Din konto på borgerinddragelsesplatformen {orgName} er blevet midlertidigt deaktiveret på grund af overtrædelse af fællesskabsretningslinjerne. Du kan få flere oplysninger om dette på {TermsAndConditions}.", "app.containers.DisabledAccount.title": "<PERSON> konto er blevet midlertidigt deaktiveret", "app.containers.EventsShow.addToCalendar": "Tilføj til kalender", "app.containers.EventsShow.editEvent": "<PERSON><PERSON> begivenhed", "app.containers.EventsShow.emailSharingBody2": "<PERSON><PERSON> i denne begivenhed: {eventTitle}. <PERSON><PERSON><PERSON> mere på {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Dato og klokkeslæt for begivenheden", "app.containers.EventsShow.eventFrom2": "Fra \"{projectTitle}\"", "app.containers.EventsShow.goBack": "<PERSON><PERSON>", "app.containers.EventsShow.goToProject": "Gå til projektet", "app.containers.EventsShow.haveRegistered": "har registreret sig", "app.containers.EventsShow.icsError": "Fejl ved download af ICS-filen", "app.containers.EventsShow.linkToOnlineEvent": "Link til online begivenhed", "app.containers.EventsShow.locationIconAltText": "Placering", "app.containers.EventsShow.metaTitle": "Begivenhed: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Online møde", "app.containers.EventsShow.onlineLinkIconAltText": "Link til online-møde", "app.containers.EventsShow.registered": "registreret", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 deltagere} one {1 deltager} other {# deltagere}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} deltagere", "app.containers.EventsShow.registrantsIconAltText": "<PERSON><PERSON><PERSON>", "app.containers.EventsShow.socialMediaSharingMessage": "Deltag i dette arrangement: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# deltager} other {# deltagere}}", "app.containers.EventsViewer.allTime": "<PERSON><PERSON> tiden", "app.containers.EventsViewer.date": "Da<PERSON>", "app.containers.EventsViewer.thisMonth2": "Kommende måned", "app.containers.EventsViewer.thisWeek2": "Kommende uge", "app.containers.EventsViewer.today": "I dag", "app.containers.IdeaButton.addAContribution": "Kom med dit bidrag", "app.containers.IdeaButton.addAPetition": "Tilføj en underskriftsindsamling", "app.containers.IdeaButton.addAProject": "Tilføj et projekt", "app.containers.IdeaButton.addAProposal": "Tilføj et forslag", "app.containers.IdeaButton.addAQuestion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAnInitiative": "Tilføj et initiativ", "app.containers.IdeaButton.addAnOption": "<PERSON><PERSON><PERSON><PERSON><PERSON> muli<PERSON>", "app.containers.IdeaButton.postingDisabled": "Nye bidrag accepteres ikke i øjeblikket", "app.containers.IdeaButton.postingInNonActivePhases": "Nye bidrag kan kun tilføjes i aktive faser.", "app.containers.IdeaButton.postingInactive": "Nye bidrag accepteres ikke i øjeblikket.", "app.containers.IdeaButton.postingLimitedMaxReached": "Du har allerede gennemført denne undersøgelse. Tak for dit svar!", "app.containers.IdeaButton.postingNoPermission": "Nye indlæg er ikke aktiveret i øjeblikket", "app.containers.IdeaButton.postingNotYetPossible": "<PERSON>ye bidrag accepteres endnu ikke her.", "app.containers.IdeaButton.signInLinkText": "logge ind", "app.containers.IdeaButton.signUpLinkText": "registrer dig", "app.containers.IdeaButton.submitAnIssue": "Send dit hørings<PERSON>var", "app.containers.IdeaButton.submitYourIdea": "Beskriv din idé", "app.containers.IdeaButton.takeTheSurvey": "<PERSON><PERSON><PERSON> spørgeskema<PERSON> ", "app.containers.IdeaButton.verificationLinkText": "Bekræft din konto nu.", "app.containers.IdeaCard.readMore": "<PERSON><PERSON><PERSON> mere", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {ingen kommentarer} one {1 kommentar} other {# kommentarer}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {ingen stemmer} one {1 stemme} other {# stemmer}} ud af {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Luk filterpanelet", "app.containers.IdeaCards.a11y_totalItems": "I<PERSON><PERSON><PERSON> i alt: {ideasCount}", "app.containers.IdeaCards.all": "Alle", "app.containers.IdeaCards.allStatuses": "Alle statusser", "app.containers.IdeaCards.contributions": "Bidrag", "app.containers.IdeaCards.ideaTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.initiatives": "Forslag", "app.containers.IdeaCards.issueTerm": "<PERSON><PERSON>", "app.containers.IdeaCards.list": "Liste", "app.containers.IdeaCards.map": "<PERSON><PERSON>", "app.containers.IdeaCards.mostDiscussed": "Mest diskuteret", "app.containers.IdeaCards.newest": "Nyeste", "app.containers.IdeaCards.noFilteredResults": "Ingen resultater er fundet. Prøv et andet filter eller et andet søgeord.", "app.containers.IdeaCards.numberResults": "Resultater ({postCount})", "app.containers.IdeaCards.oldest": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.optionTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.petitions": "Underskriftindsamlinger", "app.containers.IdeaCards.popular": "<PERSON><PERSON>t stemmer", "app.containers.IdeaCards.projectFilterTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.projectTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.proposals": "Borgerforslag", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "Tilfældige", "app.containers.IdeaCards.resetFilters": "Nulstil filtre", "app.containers.IdeaCards.showXResults": "Vis {ideasCount, plural, no {# resultater} one {# resultat} other {# resultater}}", "app.containers.IdeaCards.sortTitle": "Sortering", "app.containers.IdeaCards.statusTitle": "Status", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "<PERSON><PERSON>", "app.containers.IdeaCards.topicsTitle": "<PERSON><PERSON>", "app.containers.IdeaCards.trending": "Populære", "app.containers.IdeaCards.tryDifferentFilters": "Ingen resultater er fundet. Prøv et andet filter eller et andet søgeord.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} kommentarer} one {{ideasCount} kommentar} other {{ideasCount} kommentarer}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} bidrag} one {{ideasCount} bidrag} other {{ideasCount} bidrag}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ~ ideer} one {{ideasCount} ide} other {{ideasCount} ideer}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} initiativer} one {{ideasCount} initiativ} other {{ideasCount} initiativer}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} muligheder} one {{ideasCount} mulighed} other {{ideasCount} muligheder}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} afstemninger} one {{ideasCount} afstemning} other {{ideasCount} afstemninger}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projekter} one {{ideasCount} projekt} other {{ideasCount} projekter}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} forslag} one {{ideasCount} forslag} other {{ideasCount} forslag}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} spørg<PERSON><PERSON><PERSON>} one {{ideasCount} spørgsm<PERSON><PERSON>} other {{ideasCount} spørg<PERSON><PERSON><PERSON>}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, no {# resultater} one {# resultat} other {# resultater}}", "app.containers.IdeasEditPage.contributionFormTitle": "Rediger bidrag", "app.containers.IdeasEditPage.editedPostSave": "Gemt", "app.containers.IdeasEditPage.fileUploadError": "En eller flere filer kunne ikke uploades. Kontroller filstørrelse og format, og prøv igen.", "app.containers.IdeasEditPage.formTitle": "{tenantName, select, DeloitteDK {Rediger indlæg} other {Rediger idé}}", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Rediger dit indlæg. Du kan tilføje nyt og ændre gamle oplysninger.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Rediger {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "<PERSON><PERSON><PERSON><PERSON> forslag", "app.containers.IdeasEditPage.issueFormTitle": "<PERSON><PERSON> h<PERSON>", "app.containers.IdeasEditPage.optionFormTitle": "<PERSON><PERSON> mulighed", "app.containers.IdeasEditPage.petitionFormTitle": "<PERSON><PERSON> begæring", "app.containers.IdeasEditPage.projectFormTitle": "Rediger dette projekt", "app.containers.IdeasEditPage.proposalFormTitle": "<PERSON><PERSON><PERSON><PERSON> forslag", "app.containers.IdeasEditPage.questionFormTitle": "<PERSON><PERSON> s<PERSON>ø<PERSON>", "app.containers.IdeasEditPage.save": "Gem", "app.containers.IdeasEditPage.submitApiError": "Der opstod et problem med at indsende formularen. Kontroller for eventuelle fejl, og prøv igen.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Alle input sendt", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Udforsk alle input, der er blevet posted på bogerinddragelsesplatformen for {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Indlæg | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Inputs", "app.containers.IdeasIndexPage.loadMore": "Indlæs flere...", "app.containers.IdeasIndexPage.loading": "Indlæser...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Som standard vil dine indlæg være knyttet til din profil, medmindre du vælger denne mulighed.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Indlæg anonymt", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "<PERSON>il synlighed", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "<PERSON>ne undersøgelse er ikke åben for svar i øjeblikket. Vend venligst tilbage til projektet for mere information.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "<PERSON>ne undersøgelse er ikke aktiv i øjeblikket.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Tilbage til projektet", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Du har allerede gennemført denne under<PERSON>ø<PERSON>.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Undersøgelse indsendt", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Tak for dit svar!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Beskrivelsen af bidraget skal være mindre end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Idéteksten skal være mere end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Bidragets titel skal være mere end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "Bidragets titel skal være mere end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "<PERSON><PERSON><PERSON>g venligst mindst én medstiller", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Idébeskrivelsen skal være mindre end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Idébeskrivelsen skal være mere end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Angiv venligst en beskrivelse", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Idéens titel skal være på mere end {limit} tegn", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Idétitlen skal være mere end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "Initiativbeskrivelsen skal være mindre end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "Initiativbeskrivelsen skal være mere end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "Initiativets titel skal være mindre end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "Initiativets titel skal være mere end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Beskrivelsen af emnet skal være mindre end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Beskrivelsen af emnet skal være mere end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Titlen på emnet skal være mindre end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "Titlen på emnet skal være på over {limit} tegn", "app.containers.IdeasNewPage.ajv_error_number_required": "<PERSON><PERSON> felt er påkrævet, indtast venligst et gyldigt nummer.", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Beskrivelsen af en indstilling skal være mindre end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "Indstillingsbeskrivelsen skal være mere end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Titlen på optionen skal være mindre end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Titlen på indstillingen skal være mere end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "<PERSON><PERSON><PERSON>g venligst mindst ét tag", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "Beskrivelsen af underskriftsindsamlingen skal være mindre end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "Beskrivelsen af underskriftsindsamlingen skal være mere end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "Underskriftsindsamlingens titel skal være mindre end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "Underskriftsindsamlingens titel skal være mere end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Projektbeskrivelsen skal være mindre end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Projektbeskrivelsen skal være mere end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Projektets titel skal være mindre end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Projektets titel skal være på mere end {limit} tegn", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "Forslagsbeskrivelsen skal være mindre end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "Forslagsbeskrivelsen skal være mere end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "Forslagets titel skal være mindre end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "Forslagets titel skal være mere end {limit} tegn lang.", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Indtast venligst et tal", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Indtast venligst et tal", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "Spørgsmålsbeskrivelsen skal være mindre end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "Spørgsmålsbeskrivelsen skal være mere end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "Spørgsmålets titel skal være mindre end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "Spørgsmålets titel skal være mere end {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "<PERSON><PERSON> venligst en titel", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Beskrivelsen af bidraget skal være mindre end 80 tegn lang", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Beskrivelsen af bidraget skal være mindst 30 tegn lang", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Bidragets titel skal være på under 80 tegn", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Bidragets titel skal være på mindst 10 tegn", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Idébeskrivelsen skal være mindre end 80 tegn lang", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Idébeskrivelsen skal fylde mindst 30 tegn", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "<PERSON><PERSON> venligst en titel", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Idétitlen skal være mindre end 80 tegn lang", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Idétitlen skal fylde mindst 10 tegn", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Du har muligvis brugt et eller flere ord, som {guidelinesLink} betragter som blasfemi. Du bedes ændre din tekst for at fjerne eventuelle blasfemiske ord.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "Initiativbeskrivelsen skal være mindre end 80 tegn lang.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "Beskrivelsen af forslaget skal være mindst 30 tegn lang", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Initiativets titel skal være mindre end 80 tegn lang.", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Initiativets titel skal være mindst 10 tegn lang.", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Spørgsmålsbeskrivelsen skal være mindre end 80 tegn lang", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Problembeskrivelsen skal være på mindst 30 tegn", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Titlen på emnet skal være på mindre end 80 tegn", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Titlen på emnet skal være mindst 10 tegn lang", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Beskrivelsen af en indstilling skal være på under 80 tegn", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Beskrivelsen af en indstilling skal være på mindst 30 tegn", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Titlen på optionen skal være på under 80 tegn", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Titlen på optionen skal være mindst 10 tegn lang", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "Beskrivelsen af underskriftsindsamlingen skal være mindre end 80 tegn lang.", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "Beskrivelsen af underskriftsindsamlingen skal være mindst 30 tegn lang.", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Underskriftsindsamlingens titel skal være mindre end 80 tegn lang", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Petitionens titel skal være mindst 10 tegn lang.", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Projektbeskrivelsen skal være på mindre end 80 tegn", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Projektbeskrivelsen skal være på mindst 30 tegn", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Projektets titel skal være på under 80 tegn", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Projektets titel skal være på mindst 10 tegn", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "Forslagsbeskrivelsen skal være mindre end 80 tegn lang.", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "Forslagsbeskrivelsen skal være mindst 30 tegn lang.", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Forslagets titel skal være mindre end 80 tegn lang.", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Forslagets titel skal være mindst 10 tegn lang.", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Angiv venligst en beskrivelse", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Spørgsmålsbeskrivelsen skal være på under 80 tegn", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Spørgsmålsbeskrivelsen skal være på mindst 30 tegn", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Spørgsmålets titel skal være på mindre end 80 tegn", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Spørgsmålets titel skal være mindst 10 tegn lang", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON>, jeg vil gerne logge af", "app.containers.IdeasNewPage.contributionMetaTitle1": "Tilføj nyt bidrag til projektet | {orgName}", "app.containers.IdeasNewPage.editSurvey": "<PERSON>iger undersøgelse", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Send et indlæg, og deltag i samtalen på {orgName}'s borgerinddragelsesplatform.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Tilføj ny idé til projektet | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Tilføj nyt initiativ til projektet | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Til<PERSON><PERSON><PERSON> et nyt problem til projektet | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "<PERSON>r du sikker på, at du vil forlade os?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Dit udkast til svar er blevet gemt privat, og du kan vende tilbage for at færdiggøre det senere.", "app.containers.IdeasNewPage.leaveSurvey": "Undersøgelse af orlov", "app.containers.IdeasNewPage.leaveSurveyText": "<PERSON>e svar vil ikke blive gemt.", "app.containers.IdeasNewPage.optionMetaTitle1": "Tilføj ny mulighed til projektet | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Tilføj ny underskriftsindsamling til projektet | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Tilføj nyt projekt til projekt | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Tilføj nyt forslag til projektet | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Tilføj nyt spørgsmål til projektet | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Accepter invitation", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Invitation til at blive medstiller", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Medstillere", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Du er blevet inviteret til at blive medstiller.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Invitationer er accepteret", "app.containers.IdeasShow.Cosponsorship.pending": "a<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.attachments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> filer", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "Af {userName} den {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Nuværende status", "app.containers.IdeasShow.MetaInformation.location": "Beliggenhed", "app.containers.IdeasShow.MetaInformation.postedBy": "Sendt af", "app.containers.IdeasShow.MetaInformation.similar": "Lignende input", "app.containers.IdeasShow.MetaInformation.topics": "<PERSON><PERSON>", "app.containers.IdeasShow.commentCTA": "Tilføj kommentar", "app.containers.IdeasShow.contributionEmailSharingBody": "<PERSON><PERSON><PERSON> dette forslag '{postTitle}' på {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Support dette forslag: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Tak for at du har indsendt et forslag!", "app.containers.IdeasShow.contributionTwitterMessage": "<PERSON><PERSON><PERSON> dette forslag: '{postTitle}'", "app.containers.IdeasShow.contributionWhatsAppMessage": "<PERSON><PERSON><PERSON> dette forslag: {postTitle}", "app.containers.IdeasShow.currentStatus": "Nuværende status", "app.containers.IdeasShow.deletedUser": "uk<PERSON>t forfatter", "app.containers.IdeasShow.ideaEmailSharingBody": "<PERSON><PERSON>t min idé '{ideaTitle}' på {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "{tenantName, select, DeloitteDK {Giv dit input: {ideaTitle}.} other {Støt min idé: {postTitle}.}}", "app.containers.IdeasShow.ideaTwitterMessage": "<PERSON><PERSON><PERSON> denne idé: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "<PERSON><PERSON><PERSON> denne idé: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "<PERSON><PERSON><PERSON> hø<PERSON>: {postTitle}", "app.containers.IdeasShow.imported": "Importeret", "app.containers.IdeasShow.importedTooltip": "<PERSON><PERSON> {inputTerm} blev indsamlet offline og automatisk uploadet til platformen.", "app.containers.IdeasShow.initiativeEmailSharingBody": "<PERSON><PERSON><PERSON> dette initiativ '{ideaTitle}' på {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "<PERSON><PERSON><PERSON> dette forslag: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "<PERSON><PERSON>, fordi du har indsendt dit initiativ!", "app.containers.IdeasShow.initiativeTwitterMessage": "<PERSON><PERSON><PERSON> dette forslag: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "<PERSON><PERSON><PERSON> dette forslag: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "<PERSON><PERSON><PERSON> de<PERSON> hø<PERSON> '{postTitle}' på {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "<PERSON><PERSON><PERSON> hø<PERSON>: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Tak for at du har indsendt et forslag!", "app.containers.IdeasShow.issueTwitterMessage": "<PERSON><PERSON><PERSON> hø<PERSON>: {postTitle}", "app.containers.IdeasShow.metaTitle": "Input: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "<PERSON><PERSON><PERSON> denne mulighed '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "<PERSON><PERSON><PERSON> denne mulighed: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Dit forslag er blevet postet med succes!", "app.containers.IdeasShow.optionTwitterMessage": "<PERSON><PERSON><PERSON> denne mulighed: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "<PERSON><PERSON><PERSON> denne mulighed: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "<PERSON><PERSON><PERSON> denne underskriftsindsamling '{ideaTitle}' på {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "<PERSON><PERSON><PERSON> denne underskriftsindsamling: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "<PERSON><PERSON>, fordi du sendte din underskriftsindsamling!", "app.containers.IdeasShow.petitionTwitterMessage": "<PERSON><PERSON><PERSON> denne underskriftsindsamling: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "<PERSON><PERSON><PERSON> denne underskriftsindsamling: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "<PERSON><PERSON>t dette projekt '{postTitle}' på {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "<PERSON><PERSON><PERSON> dette projekt '{postTitle}'", "app.containers.IdeasShow.projectSharingModalTitle": "Tak for at du har indsendt et forslag!", "app.containers.IdeasShow.projectTwitterMessage": "<PERSON><PERSON><PERSON> dette forslag '{postTitle}'", "app.containers.IdeasShow.projectWhatsAppMessage": "<PERSON><PERSON><PERSON> dette forslag '{postTitle}'", "app.containers.IdeasShow.proposalEmailSharingBody": "<PERSON><PERSON><PERSON> dette forslag '{ideaTitle}' på {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "<PERSON><PERSON><PERSON> dette forslag: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "<PERSON><PERSON>, fordi du har indsendt dit forslag!", "app.containers.IdeasShow.proposalTwitterMessage": "<PERSON><PERSON><PERSON> dette forslag: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "<PERSON><PERSON><PERSON> dette forslag: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Der er tid tilbage til at stemme:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} ud af {votingThreshold} nødvendige stemmer", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "<PERSON><PERSON><PERSON><PERSON> stemme", "app.containers.IdeasShow.proposals.VoteControl.days": "dage", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "<PERSON><PERSON> <PERSON><PERSON>ø<PERSON>", "app.containers.IdeasShow.proposals.VoteControl.hours": "timer", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status og stemmer\n", "app.containers.IdeasShow.proposals.VoteControl.minutes": "min", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Mere info", "app.containers.IdeasShow.proposals.VoteControl.vote": "<PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.voted": "Stemt", "app.containers.IdeasShow.proposals.VoteControl.votedText": "Du vil blive underrettet når dette borgerforslag når til næste trin. {x, plural, =0 {Der er {xDage} tilbage.} one {Der er {xDage} tilbage.} other {Der er {xDage} tilbage.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Din stemme blev indsendt!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Du kan desværre ikke stemme på et borgerforslag. Læs hvorfor i ''{link}''.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {mindre end en dag} one {en dag} other {# dage}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {ingen stemmer} one {1 stemme} other {# stemmer}}", "app.containers.IdeasShow.questionEmailSharingBody": "Deltag i diskussionen om dette spørgsmål '{postTitle}' på {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "<PERSON>il dit spørgsm<PERSON><PERSON>: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Dit forslag er blevet postet med succes!", "app.containers.IdeasShow.questionTwitterMessage": "<PERSON>il dit spørgsm<PERSON><PERSON>: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "<PERSON>il dit spørgsm<PERSON><PERSON>: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "<PERSON><PERSON><PERSON><PERSON> ø<PERSON><PERSON> du at rapportere dette som spam?", "app.containers.IdeasShow.share": "Del", "app.containers.IdeasShow.sharingModalSubtitle": "<PERSON><PERSON> flere og bliv hørt.", "app.containers.IdeasShow.sharingModalTitle": "Tak fordi du har indsendt din idé!", "app.containers.Navbar.completeOnboarding": "<PERSON><PERSON><PERSON><PERSON><PERSON> onboarding", "app.containers.Navbar.completeProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> profil", "app.containers.Navbar.confirmEmail2": "Bekræft e-mail", "app.containers.Navbar.unverified": "<PERSON>kke verificeret", "app.containers.Navbar.verified": "Verificeret", "app.containers.NewAuthModal.beforeYouFollow": "<PERSON><PERSON><PERSON>ølge<PERSON>", "app.containers.NewAuthModal.beforeYouParticipate": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.completeYourProfile": "Udfyld din profil", "app.containers.NewAuthModal.confirmYourEmail": "Bekræft din e-mailadresse", "app.containers.NewAuthModal.logIn": "Log ind", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "<PERSON><PERSON><PERSON> vil<PERSON> nedenfor for at fortsætte.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Udfyld venligst din profil.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "G<PERSON> tilbage til indstillingerne for login", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Har du ikke en konto? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "<PERSON><PERSON><PERSON><PERSON> dig", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Koden skal have 4 cifre.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Fortsæt med FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Der er ikke aktiveret nogen godkendelsesmetoder på denne platform.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "Ved at fortsætte accepterer du at modtage e-mails fra denne platform. <PERSON> kan vælge, hvilke e-mails du ønsker at modtage, på siden \"Mine indstillinger\".", "app.containers.NewAuthModal.steps.EmailSignUp.email": "E-mail", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Angiv en e-mail-adresse i det korrekte format, f.eks. <EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Angiv en e-mail-adresse", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Indtast din e-mail-adresse for at fortsætte.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Glemt adgangskode?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Log ind på din konto: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Indtast venligst din adgangskode", "app.containers.NewAuthModal.steps.Password.password": "Adgangskode", "app.containers.NewAuthModal.steps.Password.rememberMe": "Husk mig", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "<PERSON><PERSON><PERSON><PERSON>, hvis du bruger en offentlig computer", "app.containers.NewAuthModal.steps.Success.allDone": "Alt er gjort", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Fortsæt nu din deltagelse.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Din identitet er blevet bekræftet. Du er nu et fuldt medlem af fællesskabet på denne platform.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Du er nu verificeret!", "app.containers.NewAuthModal.steps.close": "Luk", "app.containers.NewAuthModal.steps.continue": "Fortsæt", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Hvad er du interesseret i?", "app.containers.NewAuthModal.youCantParticipate": "Du kan ikke deltage", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {ingen uviste meddelelser} one {1 uviste meddelelse} other {# uviste meddelelser}}", "app.containers.NotificationMenu.adminRightsReceived": "Du er nu administrator af platformen", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Din kommentar til '{postTitle}' er blevet slettet af en administrator, fordi\n       {reasonCode, vælg, irrelevant {det er irrelevant} upassende {dets indhold er upassende} andet {{otherReason}}}\n", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} har accepteret din invitation til medsponsorat", "app.containers.NotificationMenu.deletedUser": "Ukendt forfatter", "app.containers.NotificationMenu.error": "Kunne ikke indlæse notifikationer", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} har kommenteret internt på et input, som du har fået tildelt", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} kommenterede internt på et input, som du kommenterede internt på", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} har kommenteret internt på et input i et projekt, du er ansvarlig for", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} kommenterede internt på et ikke-tildelt input i et ikke-administreret projekt", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} kommenterede på din interne kommentar", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} har <PERSON>ret dig til at være medstiller af et forslag", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} har <PERSON>ret dig til at være medstiller for en idé", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} op<PERSON><PERSON>e dig til at støtte et forslag", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} har <PERSON>ret dig til at være medstiller på et emne", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} har <PERSON>ret dig til at være medstiller for en mulighed", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} op<PERSON><PERSON>e dig til at være medstiller for en underskriftsindsamling", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} har <PERSON>ret dig til at være medstiller for et projekt", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} op<PERSON><PERSON>e dig til at støtte et forslag", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} <PERSON><PERSON>e dig til at være medstiller for et spørgsmål", "app.containers.NotificationMenu.loadMore": "Indlæs flere...", "app.containers.NotificationMenu.loading": "Indlæser notifikationer...", "app.containers.NotificationMenu.mentionInComment": "{name} nævnte dig i en kommentar", "app.containers.NotificationMenu.mentionInInternalComment": "{name} nævnte dig i en intern kommentar", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{officialName} nævnte dig i en officiel opdatering", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Du har ikke indsendt din undersøgelse", "app.containers.NotificationMenu.noNotifications": "Du har ikke nogen notifikationer endnu", "app.containers.NotificationMenu.notificationsLabel": "Notifikationer", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} gav en officiel opdatering på et bidrag, du følger", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} gav en officiel opdatering på en idé, du følger", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} gav en officiel opdatering på et forslag, du følger", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} gav en officiel opdatering på et emne, du følger", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} gav en officiel opdatering på en mulighed, du følger", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} gav en officiel opdatering på en underskriftsindsamling, du følger", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} gav en officiel opdatering på et projekt, du følger", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} gav en officiel opdatering på et forslag, du følger", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} gav en officiel opdatering på et spø<PERSON><PERSON><PERSON><PERSON>, du følger", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} tildelt dig", "app.containers.NotificationMenu.projectModerationRightsReceived": "Du er nu projektleder på {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} trådte ind i en ny fase", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} påbegynder en ny fase på {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "Et nyt projekt blev offentliggjort", "app.containers.NotificationMenu.projectReviewRequest": "{name} anmodede om godkendelse til at offentliggøre projektet \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} har god<PERSON><PERSON> \"{projectTitle}\" til offentliggørelse", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} status er ændret til {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} n<PERSON>ede afstemningstærsklen\n", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} accepterede din invitation", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} kommenterede på et bidrag, som du følger", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} komment<PERSON>e på en idé, som du følger", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} kommenterede på et initiativ, som du følger", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} kommenterede på et emne, du følger", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} komment<PERSON>e på en mulighed, som du følger", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} kommenterede en underskriftsindsamling, som du følger", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} kommenterede et projekt, som du følger", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} kommenterede et forslag, som du følger", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} kommenterede på et spø<PERSON><PERSON><PERSON><PERSON>, som du følger", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} rapporterede \"{postTitle}\" som spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} reagerede på din kommentar", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} rapporterede en kommentar på '{postTitle}' som spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Du har ikke indsendt dine stemmer", "app.containers.NotificationMenu.votingBasketSubmitted": "Du har stemt med succes", "app.containers.NotificationMenu.votingLastChance": "Sidste chance for at stemme på {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} afstemningsresultater offentliggjort", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} tildelt {postTitle} dig", "app.containers.PasswordRecovery.emailError": "Det ser ikke ud til, at dette er en gyldig e-mail.", "app.containers.PasswordRecovery.emailLabel": "E-mail", "app.containers.PasswordRecovery.emailPlaceholder": "Min e-mailadresse", "app.containers.PasswordRecovery.helmetDescription": "Side for nulstilling af adgangskode", "app.containers.PasswordRecovery.helmetTitle": "Nulstil din adgangskode", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "<PERSON><PERSON> denne e-mailadresse er registreret på platformen, er der sendt et link til nulstilling af adgangskode.", "app.containers.PasswordRecovery.resetPassword": "Send link for at nulstille password.", "app.containers.PasswordRecovery.submitError": "Vi kunne ikke finde en konto tilknyttet denne e-mail. Du kan prøve at tilmelde dig i stedet. ", "app.containers.PasswordRecovery.subtitle": "<PERSON>vor skal vi sende linket for at vælge et nyt password?", "app.containers.PasswordRecovery.title": "Nulstilling af adgangskode", "app.containers.PasswordReset.helmetDescription": "Side for nulstilling af adgangskode", "app.containers.PasswordReset.helmetTitle": "Nulstil din adgangskode", "app.containers.PasswordReset.login": "Log ind", "app.containers.PasswordReset.passwordError": "Din adgangskode skal indeholde mindst 8 tegn", "app.containers.PasswordReset.passwordLabel": "Adgangskode", "app.containers.PasswordReset.passwordPlaceholder": "Ny adgangskode", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Din adgangskode er blevet opdateret med succes.", "app.containers.PasswordReset.pleaseLogInMessage": "Log venligst ind med dit nye password.", "app.containers.PasswordReset.requestNewPasswordReset": "Anmod om at få nulstillet din adgangskode", "app.containers.PasswordReset.submitError": "Noget gik galt. Prøv venligst igen senere.", "app.containers.PasswordReset.title": "Nulstil din adgangskode", "app.containers.PasswordReset.updatePassword": "Bekræft ny adgangskode", "app.containers.ProjectFolderCards.allProjects": "Alle projekter", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} arbejder i øjeblikket med", "app.containers.ProjectFolderShowPage.editFolder": "Rediger folder", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Oplysninger om dette projekt", "app.containers.ProjectFolderShowPage.metaTitle1": "Folder: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON><PERSON> mere", "app.containers.ProjectFolderShowPage.seeLess": "Se mindre", "app.containers.ProjectFolderShowPage.share": "Del", "app.containers.Projects.PollForm.document": "Dokument", "app.containers.Projects.PollForm.formCompleted": "Tak! Vi har modtaget din besvarelse.", "app.containers.Projects.PollForm.maxOptions": "maks. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Du har allerede taget denne afstemning.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "<PERSON>ne afs<PERSON>ning kan kun foretages, når denne fase er aktiv.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Desv<PERSON><PERSON> har du ikke rettigheder til at tage denne afstemning.", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Det er i øjeblikket ikke muligt at tage denne afstemning.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Afs<PERSON>ning er ikke længere tilgænge<PERSON>g, da dette projekt ikke længere er aktivt.", "app.containers.Projects.PollForm.sendAnswer": "Send", "app.containers.Projects.a11y_phase": "Fase {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Faseoversigt", "app.containers.Projects.a11y_titleInputs": "Alle bidrag indsendt til dette projekt", "app.containers.Projects.a11y_titleInputsPhase": "Alle bidrag indsendt til denne fase", "app.containers.Projects.accessRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.addedToBasket": "Tilføjet til din kurv", "app.containers.Projects.allocateBudget": "Fordel dit budget", "app.containers.Projects.archived": "Arkiveret", "app.containers.Projects.basketSubmitted": "Din kurv er blevet indsendt!", "app.containers.Projects.contributions": "Bidrag", "app.containers.Projects.createANewPhase": "Opret en ny fase", "app.containers.Projects.currentPhase": "Igangværende fase", "app.containers.Projects.document": "Dokument", "app.containers.Projects.editProject": "Rediger dette projekt", "app.containers.Projects.emailSharingBody": "Hvad synes du om dette initiativ? Stem på det, og del diskussionen på {initiativeUrl} for at gøre din stemme hørt!", "app.containers.Projects.emailSharingSubject": "<PERSON><PERSON>t mit initiativ: {initiativeTitle}.", "app.containers.Projects.endedOn": "Sluttede den {date}", "app.containers.Projects.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.header": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.ideas": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.information": "information", "app.containers.Projects.initiatives": "Forslag", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Gennemgå dokumentet", "app.containers.Projects.invisibleTitlePhaseAbout": "Om denne fase\n", "app.containers.Projects.invisibleTitlePoll": "Tag afstemningen", "app.containers.Projects.invisibleTitleSurvey": "<PERSON><PERSON><PERSON> spørgeskema<PERSON>", "app.containers.Projects.issues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.liveDataMessage": "Du ser data i realtid. Deltagerantallet opdateres løbende for administratorer. Bemærk, at almindelige brugere ser cachelagrede data, hvilket kan resultere i små forskelle i tallene.", "app.containers.Projects.location": "Lokalitet:", "app.containers.Projects.manageBasket": "Administrer kurv", "app.containers.Projects.meetMinBudgetRequirement": "Opfyld minimumsbudgettet for at indsende dine valg.", "app.containers.Projects.meetMinSelectionRequirement": "<PERSON>d<PERSON>ld det krævede valg for at sende din kurv.", "app.containers.Projects.metaTitle1": "Projekt: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Der kræves et minimumsbudget", "app.containers.Projects.myBasket": "<PERSON><PERSON><PERSON>", "app.containers.Projects.navPoll": "Afstemning", "app.containers.Projects.navSurvey": "Spørgeundersøgelse", "app.containers.Projects.newPhase": "Ny fase", "app.containers.Projects.nextPhase": "<PERSON><PERSON>ste fase", "app.containers.Projects.noEndDate": "Ingen slutdato", "app.containers.Projects.noItems": "Du har ikke foretaget nogle valg endnu", "app.containers.Projects.noPastEvents": "Ingen tidligere begivenheder at vise", "app.containers.Projects.noPhaseSelected": "Ingen fase valgt", "app.containers.Projects.noUpcomingOrOngoingEvents": "Der er ingen kommende eller igangværende begivenheder planlagt i øjeblikket.", "app.containers.Projects.offlineVotersTooltip": "Dette tal afspejler ikke eventuelle offline stemme optællinger.", "app.containers.Projects.options": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.participants": "<PERSON><PERSON><PERSON>", "app.containers.Projects.participantsTooltip4": "Dette tal afspejler også anonyme indsendelser af spørgeskemaer. Anonyme besvarelser er mulige, hvis undersøgelserne er åbne for alle (se {accessRightsLink} fanen for dette projekt).", "app.containers.Projects.pastEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.petitions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.phases": "<PERSON><PERSON><PERSON>", "app.containers.Projects.previousPhase": "Foregående fase", "app.containers.Projects.project": "{tenantName, select, DeloitteDK {Verdensmål} other {Projekt}}", "app.containers.Projects.projectTwitterMessage": "<PERSON><PERSON><PERSON> din stemme hørt! Deltag i {projectName} | {orgName}", "app.containers.Projects.projects": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.proposals": "Forslag", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "<PERSON><PERSON><PERSON> mindre", "app.containers.Projects.readMore": "<PERSON><PERSON><PERSON> mere", "app.containers.Projects.removeItem": "<PERSON><PERSON><PERSON> em<PERSON>\n", "app.containers.Projects.requiredSelection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> valg", "app.containers.Projects.reviewDocument": "Gennemgå dokumentet", "app.containers.Projects.seeTheContributions": "Se alle bidrag", "app.containers.Projects.seeTheIdeas": "Se idéerne", "app.containers.Projects.seeTheInitiatives": "Se initiativerne", "app.containers.Projects.seeTheIssues": "<PERSON>ø<PERSON>", "app.containers.Projects.seeTheOptions": "<PERSON> mulighederne", "app.containers.Projects.seeThePetitions": "Se underskriftsindsamlingerne", "app.containers.Projects.seeTheProjects": "Se projekterne", "app.containers.Projects.seeTheProposals": "Se forslagene", "app.containers.Projects.seeTheQuestions": "Se spørgsmålene", "app.containers.Projects.seeUpcomingEvents": "Se kommende begive<PERSON><PERSON>r", "app.containers.Projects.share": "Del", "app.containers.Projects.shareThisProject": "Del dette projekt", "app.containers.Projects.submitMyBasket": "Indsend kurv", "app.containers.Projects.survey": "Spørgeundersøgelse", "app.containers.Projects.takeThePoll": "Tag afstemningen", "app.containers.Projects.takeTheSurvey": "<PERSON><PERSON><PERSON> spørgeskema<PERSON>", "app.containers.Projects.timeline": "Tidslinje", "app.containers.Projects.upcomingAndOngoingEvents": "Kommende og igangværende begivenheder", "app.containers.Projects.upcomingEvents": "Kommende begivenheder", "app.containers.Projects.whatsAppMessage": "{projectName} | fra borgerinddragelsesplatformen hos {orgName}", "app.containers.Projects.yourBudget": "Samlet budget", "app.containers.ProjectsIndexPage.metaDescription": "Udforsk alle igangværende projekter af {orgName} for at forstå, hvordan du kan deltage.\nKom og diskutér lokale projekter, der har betydning for dig.", "app.containers.ProjectsIndexPage.metaTitle1": "{tenantName, select, DeloitteDK {Verdensmål • {orgName}} other {Projekt • {orgName}}}", "app.containers.ProjectsIndexPage.pageTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "<PERSON>g vil være frivillig ", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Venligst {signInLink} eller {signUpLink} først for at være frivillig til denne aktivitet ", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Der er ikke åbent for deltagelse i denne aktivitet i øjeblikket.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "log ind ", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "tilmeld dig ", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Jeg fortryder mit tilbud om at være frivillig ", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {ingen frivillige} one {# frivillig} other {# frivillige}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Advarsel: Den indlejrede undersøgelse kan have tilgængelighedsproblemer for brugere af skærmlæsere. <PERSON><PERSON> du oplever udfordringer, bedes du kontakte platformens administrator for at modtage et link til undersøgelsen fra den oprindelige platform. Alternativt kan du anmode om andre måder at udfylde undersøgelsen på.", "app.containers.ProjectsShowPage.process.survey.survey": "Spørgeundersøgelse", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "For at finde ud af, om du kan deltage i denne undersøgelse, skal du først {logInLink} på platformen.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "<PERSON>ne under<PERSON>øgelse kan kun tages, når denne fase i tidslinjen er aktiv.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Du bedes henvende dig til {completeRegistrationLink} for at deltage i undersøgelsen.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "<PERSON><PERSON><PERSON><PERSON> har du ikke rettigheder til at tage denne undersøgelse.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "At gennemføre denne undersøgelse kræver bekræftelse af din konto. \n{verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "<PERSON><PERSON><PERSON><PERSON><PERSON> er ikke længere tilgænge<PERSON>g, da dette projekt ikke længere er aktivt.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON> registrering", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "Log ind", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "registrer dig", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Bekræft din konto nu.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Kun bestemte brugere kan se dette dokument. Du bedes først skrive til {signUpLink} eller {logInLink} .", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "<PERSON>te dokument kan kun gennemses, n<PERSON>r denne fase er aktiv.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Se venligst {completeRegistrationLink} for at gennemgå dokumentet.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "<PERSON><PERSON><PERSON><PERSON> har du ikke ret til at gennemse dette dokument.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Gennemgang af dette dokument kræver bekræftelse af din konto. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Dokumentet er ikke længere tilgængeligt, da dette projekt ikke længere er aktivt.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(inkl. 1 offline)} other {(inkl. # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 valg} other {# valg}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "Den procentdel af deltagerne, der valgte denne mulighed.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "Den procentdel af det samlede antal stemmer, som denne mulighed fik.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Omkostninger:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Vis mere ", "app.containers.ReactionControl.a11y_likesDislikes": "Antal likes i alt: {likesCount}, total dislikes: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Du har annulleret din uvilje mod dette input.", "app.containers.ReactionControl.cancelLikeSuccess": "Du har annulleret dit like for dette input med succes.", "app.containers.ReactionControl.dislikeSuccess": "Du kunne ikke lide dette input med succes.", "app.containers.ReactionControl.likeSuccess": "Du syntes godt om dette input.", "app.containers.ReactionControl.reactionErrorSubTitle": "På grund af en fejl kunne din stemme ikke registreres. Prøv venligst igen om et par minutter.", "app.containers.ReactionControl.reactionSuccessTitle": "Din stemte blev registreret med succes!", "app.containers.ReactionControl.vote": "<PERSON><PERSON>", "app.containers.ReactionControl.voted": "Stemt", "app.containers.SearchInput.a11y_cancelledPostingComment": "Annulleret udstationering af kommentar.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} kommentarer er indlæst.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# events have loaded} one {# event has loaded} other {# events have loaded}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# resultater er indlæst} one {# resultat er indlæst} other {# resultater er indlæst}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# søgeresultater er indlæst} one {# søgeresultat er indlæst} other {# søgeresultater er indlæst}}.", "app.containers.SearchInput.removeSearchTerm": "<PERSON><PERSON><PERSON> sø<PERSON>\n", "app.containers.SearchInput.searchAriaLabel": "<PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchLabel": "<PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchTerm": "Søgeudtryk: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect er den løsning, som den franske stat har foreslået for at sikre og forenkle tilmeldingen til mere end 700 onlinetjenester.", "app.containers.SignIn.or": "Eller", "app.containers.SignIn.signInError": "De angivne oplysninger er ikke korrekte. Klik \"Glemt dit password\" for at nulstille dit password.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Brug FranceConnect til at logge ind, tilmelde dig eller verificere din konto.", "app.containers.SignIn.whatIsFranceConnect": "Hvad er France Connect?", "app.containers.SignUp.adminOptions2": "Til administratorer og projektledere", "app.containers.SignUp.backToSignUpOptions": "Gå tilbage til registrerings indstillinger", "app.containers.SignUp.continue": "Fortsæt", "app.containers.SignUp.emailConsent": "Ved at registrere dig, accepterer du at modtage e-mails fra denne platform. Du kan vælge, hvilke e-mails du øns<PERSON> at modtage i dine bruge<PERSON>ds<PERSON>linger.", "app.containers.SignUp.emptyFirstNameError": "Indtast dit fornavn", "app.containers.SignUp.emptyLastNameError": "Indtast dit efternavn", "app.containers.SignUp.firstNamesLabel": "Fornavn", "app.containers.SignUp.goToLogIn": "Har du allerede en konto? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "<PERSON>g har læst og accepterer {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "<PERSON>g har læst og accepterer {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "<PERSON><PERSON>, at oplysningerne anvendes på mitgestalten.wien.gv.at. Yderligere oplysninger kan findes {link}.", "app.containers.SignUp.invitationErrorText": "Din invitation er udløbet eller er allerede blevet brugt. <PERSON><PERSON> du allerede har brugt invitationslinket til at oprette en konto, kan du prøve at logge ind. Ellers skal du registrere dig for at oprette en ny konto.", "app.containers.SignUp.lastNameLabel": "Efternavn", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "<PERSON><PERSON><PERSON><PERSON> dine fokusområder for at få besked om dem:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "<PERSON><PERSON><PERSON><PERSON> dine y<PERSON><PERSON><PERSON>ner for at få besked om dem:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "<PERSON>em præ<PERSON>r", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Spring over for nu", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Accepter vores privatlivspolitik for at fortsætte", "app.containers.SignUp.signUp2": "Registrer dig", "app.containers.SignUp.skip": "Spring dette trin over", "app.containers.SignUp.tacError": "Accept af vores vilkår og betingelser er nødvendigt for at fortsætte", "app.containers.SignUp.thePrivacyPolicy": "privatlivspolitik", "app.containers.SignUp.theTermsAndConditions": "vilkår", "app.containers.SignUp.unknownError": "Noget gik galt. Prøv venligst igen senere.", "app.containers.SignUp.viennaConsentEmail": "E-mail-adresse", "app.containers.SignUp.viennaConsentFirstName": "Fornavn", "app.containers.SignUp.viennaConsentFooter": "Du kan ændre dine profi<PERSON><PERSON><PERSON><PERSON>, når du har logget ind. Hvis du allerede har en konto med den samme e-mailadresse på mitgestalten.wien.gv.at, vil den blive knyttet til din nuværende konto.", "app.containers.SignUp.viennaConsentHeader": "Følgende data vil blive overført:", "app.containers.SignUp.viennaConsentLastName": "Efternavn", "app.containers.SignUp.viennaConsentUserName": "Brugernavn", "app.containers.SignUp.viennaDataProtection": "wiener privatlivspolitik", "app.containers.SiteMap.contributions": "Bidrag", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "Høringssvar:", "app.containers.SiteMap.options": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.projects": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "Rapporter", "app.containers.SpamReport.buttonSuccess": "Succes", "app.containers.SpamReport.inappropriate": "Det er upassende eller stødende", "app.containers.SpamReport.messageError": "Der opstod en fejl ved indsendelse af formularen, prøv venligst igen.", "app.containers.SpamReport.messageSuccess": "Din rapport er blevet sendt", "app.containers.SpamReport.other": "<PERSON><PERSON>", "app.containers.SpamReport.otherReasonPlaceholder": "Beskrivelse", "app.containers.SpamReport.wrong_content": "<PERSON><PERSON> hører ikke hjemme her", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "<PERSON><PERSON><PERSON> pro<PERSON>", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "{tenant<PERSON><PERSON>, select, KøbenhavnsKommune {Dine stemmer på københavnerforslag, der stadig er åbne for afstemning, vil blive slettet. Stemmer på københavnerforslag, hvor afstemningsperioden er afsluttet, vil ikke blive slettet.} other {Dine stemmer på forslag, der stadig er åbne for afstemning, vil blive slettet. Stemmer på forslag, hvor afstemningsperioden er afsluttet, vil ikke blive slettet.}}", "app.containers.UsersEditPage.addPassword": "Tilføj adgangskode", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "For at deltage i projekter for verificerede borgere.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Bliv en verificeret borger", "app.containers.UsersEditPage.bio": "Om dig", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Du kan ikke redigere dette felt da det indeholder verificerede informationer ", "app.containers.UsersEditPage.buttonSuccessLabel": "Succes", "app.containers.UsersEditPage.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.changeEmail": "Ændre e-mail", "app.containers.UsersEditPage.changePassword2": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.clickHereToUpdateVerification": "<PERSON><PERSON> venligst her for at opdatere din bekræftelse.", "app.containers.UsersEditPage.conditionsLinkText": "vores vilk<PERSON>r ", "app.containers.UsersEditPage.contactUs": "<PERSON><PERSON> en grund til at gå? {feedbackLink}, så kan vi måske hjælpe.", "app.containers.UsersEditPage.deleteAccountSubtext": "Vi er kede af, at du forlader os. ", "app.containers.UsersEditPage.deleteMyAccount": "Slet min konto ", "app.containers.UsersEditPage.deleteYourAccount": "Slet din konto ", "app.containers.UsersEditPage.deletionSection": "Slet din konto", "app.containers.UsersEditPage.deletionSubtitle": "<PERSON><PERSON> handling kan ikke fortrydes. Det indhold du udgiver på platformen vil blive anonymiseret. H<PERSON> du ønsker at slette alt dit indhold, kan du kontakte os på <EMAIL>.", "app.containers.UsersEditPage.email": "E-mail", "app.containers.UsersEditPage.emailEmptyError": "Angiv en e-mail-adresse", "app.containers.UsersEditPage.emailInvalidError": "Angiv en e-mail-adresse i det korrekte format, f.eks. <EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Klik her", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Fornavn", "app.containers.UsersEditPage.firstNamesEmptyError": "<PERSON><PERSON> forna<PERSON>", "app.containers.UsersEditPage.h1": "<PERSON><PERSON>", "app.containers.UsersEditPage.h1sub": "<PERSON><PERSON> dine kontooplysninger", "app.containers.UsersEditPage.image": "Avatar-billede", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Klik for at vælge et profilbillede (max. 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "<PERSON>e inds<PERSON>linger for din konto\n", "app.containers.UsersEditPage.language": "Sp<PERSON>", "app.containers.UsersEditPage.lastName": "Efternavn", "app.containers.UsersEditPage.lastNameEmptyError": "Angiv et efternavn", "app.containers.UsersEditPage.loading": "Indlæser...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Du kan ændre din e-mail eller adgangskode her.", "app.containers.UsersEditPage.loginCredentialsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.messageError": "Vi kunne ikke gemme din profil. Prøv igen senere <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "<PERSON> profil er blevet gemt.", "app.containers.UsersEditPage.metaDescription": "Dette er siden med profilindstillinger for {firstName} {lastName} på online dialogplatformen for {tenantName}. Her kan du bekræfte din identitet, redigere dine kontooply<PERSON>ninger, slette din konto og redigere dine e-mailpræferencer.", "app.containers.UsersEditPage.metaTitle1": "Siden med profilindst<PERSON>inger på {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "<PERSON><PERSON>r du klikker på denne knap, vil vi ikke kunne genskabe din konto. ", "app.containers.UsersEditPage.noNameWarning2": "Dit navn vises i øjeblikket på platformen som: \"{displayName}\", fordi du ikke har indtastet dit navn. Dette er et autogenereret navn. Hvis du gerne vil ændre det, skal du indtaste dit navn nedenfor.", "app.containers.UsersEditPage.notificationsSubTitle": "Hvilke slags e-mail-meddelelser vil du modtage?", "app.containers.UsersEditPage.notificationsTitle": "E-mail notifikationer", "app.containers.UsersEditPage.password": "Vælg en ny adgangskode", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Angiv en adgangskode, der er mindst {minimumPasswordLength} tegn lang", "app.containers.UsersEditPage.passwordAddSection": "Tilføj en adgangskode", "app.containers.UsersEditPage.passwordAddSubtitle2": "Indstil en adgangskode og log nemt ind på platformen uden at skulle bekræfte din e-mail hver gang.", "app.containers.UsersEditPage.passwordChangeSection": "Ændre din adgangskode", "app.containers.UsersEditPage.passwordChangeSubtitle": "Bekræft din nuværende adgangskode og ændr den til en ny adgangskode.", "app.containers.UsersEditPage.privacyReasons": "<PERSON><PERSON> du er bekymret for dit privatliv kan du læse {conditionsLink}.", "app.containers.UsersEditPage.processing": "Sender...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Fornavn er påk<PERSON>ævet, når du angiver efternavn", "app.containers.UsersEditPage.reasonsToStayListTitle": "<PERSON><PERSON><PERSON> du <PERSON>å<PERSON>...", "app.containers.UsersEditPage.submit": "<PERSON><PERSON>", "app.containers.UsersEditPage.tooManyEmails": "<PERSON><PERSON>r du for mange e-mails? Du kan administrere din e-mailpræferencer i dine profilindstillinger. ", "app.containers.UsersEditPage.updateverification": "<PERSON><PERSON><PERSON><PERSON> du dine officielle oplysninger? {reverifyButton}", "app.containers.UsersEditPage.user": "<PERSON><PERSON><PERSON><PERSON><PERSON> vil du have os til at sende dig en e-mail for at give dig besked?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Du kan nu deltage i projekter, der kræver verifikation.", "app.containers.UsersEditPage.verifiedIdentityTitle": "<PERSON> brugerprofil er blevet verificeret", "app.containers.UsersEditPage.verifyNow": "Verificer nu", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Download dine svar (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {ingen likes} one {1 like} other {# likes}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "<PERSON><PERSON><PERSON>, hvor<PERSON> denne kommentar blev slået op:", "app.containers.UsersShowPage.areas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "Kommentarer ({commentsCount}) ", "app.containers.UsersShowPage.editProfile": "Rediger min profil ", "app.containers.UsersShowPage.emptyInfoText": "Du følger ikke nogen elementer i det angivne filter ovenfor.", "app.containers.UsersShowPage.eventsWithCount": "Begivenheder ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "<PERSON><PERSON><PERSON><PERSON> ({followingCount})", "app.containers.UsersShowPage.inputs": "Inputs", "app.containers.UsersShowPage.invisibleTitlePostsList": "Alle indlæg indsendt af denne deltager", "app.containers.UsersShowPage.invisibleTitleUserComments": "Alle kommentarer slået op af denne bruger\n \n", "app.containers.UsersShowPage.loadMore": "<PERSON><PERSON><PERSON><PERSON> flere", "app.containers.UsersShowPage.loadMoreComments": "Hent flere kommentarer ", "app.containers.UsersShowPage.loadingComments": "He<PERSON> kommentarer... ", "app.containers.UsersShowPage.loadingEvents": "Indl<PERSON><PERSON> begivenheder...", "app.containers.UsersShowPage.memberSince": "Medlem siden {date}", "app.containers.UsersShowPage.metaTitle1": "Profilside for {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Denne person har endnu ikke slået nogen kommentarer op.", "app.containers.UsersShowPage.noCommentsForYou": "Her er der ingen kommentarer endnu.", "app.containers.UsersShowPage.noEventsForUser": "Du har ikke deltaget i nogen begivenheder endnu.", "app.containers.UsersShowPage.postsWithCount": "Indlæg ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Projektmapper", "app.containers.UsersShowPage.projects": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.proposals": "Borgerforslag", "app.containers.UsersShowPage.seePost": "Se indlæg", "app.containers.UsersShowPage.surveyResponses": "Svar ({responses})", "app.containers.UsersShowPage.topics": "<PERSON><PERSON>", "app.containers.UsersShowPage.tryAgain": "Der opstod en fejl, prøv venligst igen senere. ", "app.containers.UsersShowPage.userShowPageMetaDescription": "<PERSON><PERSON> profil <PERSON><PERSON> {firstName} {lastName} på borgerinddragelsesplatformen for {orgName}. Her kan du finde et overblik over deres idéer.", "app.containers.VoteControl.close": "<PERSON><PERSON><PERSON>", "app.containers.VoteControl.voteErrorTitle": "Noget gik galt", "app.containers.admin.ContentBuilder.default": "standard", "app.containers.admin.ContentBuilder.imageTextCards": "Billed- og tekstkort", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info og harmonikaer", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 kolonne", "app.containers.admin.ContentBuilder.projectDescription": "Projektbeskrivelse", "app.containers.app.navbar.admin": "Administrator", "app.containers.app.navbar.allProjects": "{tenant<PERSON><PERSON>, select, DeloitteDK {Alle verdensmål} other {Alle projekter}}", "app.containers.app.navbar.ariaLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.closeMobileNavMenu": "Luk mobilnavigationsmenuen", "app.containers.app.navbar.editProfile": "Mine indstillinger", "app.containers.app.navbar.fullMobileNavigation": "Fuld mobil", "app.containers.app.navbar.logIn": "Log ind", "app.containers.app.navbar.logoImgAltText": "{orgName} Hjem", "app.containers.app.navbar.myProfile": "Min aktivitet", "app.containers.app.navbar.search": "<PERSON><PERSON><PERSON>", "app.containers.app.navbar.showFullMenu": "Vis hele menuen", "app.containers.app.navbar.signOut": "Log ud", "app.containers.eventspage.errorWhenFetchingEvents": "Der opstod en fejl under indlæsning af begivenheder. Prøv venligst at genindlæse siden.", "app.containers.eventspage.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.eventsPageDescription": "Vis alle beg<PERSON><PERSON><PERSON><PERSON>, der er blevet slået op på bogerinddragelsesplatformen for {orgName}.", "app.containers.eventspage.eventsPageTitle1": "Begivenheder | {orgName}", "app.containers.eventspage.filterDropdownTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.noPastEvents": "Ingen tidligere begivenheder at vise", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Der er ingen kommende eller igangværende begivenheder planlagt i øjeblikket.", "app.containers.eventspage.pastEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.upcomingAndOngoingEvents": "Kommende og igangværende begivenheder", "app.containers.footer.accessibility-statement": "{tenant<PERSON><PERSON>, select, HillerodKommune {WAS} other {Webtilgængelighed}}", "app.containers.footer.ariaLabel": "Sekundære", "app.containers.footer.cookie-policy": "Cookiepolitik", "app.containers.footer.cookieSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.feedbackEmptyError": "<PERSON>te kan ikke være tomt.", "app.containers.footer.poweredBy": "Drevet af", "app.containers.footer.privacy-policy": "Privatlivspolitik", "app.containers.footer.siteMap": "Sitemap", "app.containers.footer.terms-and-conditions": "Betingelser og vilkår", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON>, jeg vil gerne logge af", "app.containers.ideaHeading.editForm": "Rediger formular", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "<PERSON>r du sikker på, at du vil forlade os?", "app.containers.ideaHeading.leaveIdeaForm": "Forlad idéformularen", "app.containers.ideaHeading.leaveIdeaText": "<PERSON>e svar vil ikke blive gemt.", "app.containers.landing.cityProjects": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.landing.completeProfile": "Færdiggør din profil", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {firstName}. Det er tid til at færdiggøre din profil.", "app.containers.landing.createAccount": "Registrer dig", "app.containers.landing.defaultSignedInMessage": "{orgName} lytter til dig. Nu er det din tur til at blive hørt!", "app.containers.landing.doItLater": "<PERSON><PERSON> gør det senere", "app.containers.landing.new": "ny", "app.containers.landing.subtitleCity": "Velkommen til borgerinddragelsesplatformen for {orgName}", "app.containers.landing.titleCity": "Lad os forme fremtiden for {orgName} sammen", "app.containers.landing.twitterMessage": "<PERSON><PERSON> på {ideaTitle} på", "app.containers.landing.upcomingEventsWidgetTitle": "Kommende og igangværende begivenheder", "app.containers.landing.userDeletedSubtitle": "Du kan oprette en ny konto når som helst eller {contactLink} og fortælle os hvad vi kan forbedre. ", "app.containers.landing.userDeletedSubtitleLinkText": "skriv til os ", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "<PERSON> konto er blevet slettet.", "app.containers.landing.userDeletionFailed": "Der var problemer med af slette din konto, vi har fået besked om problemet og vil gøre vores bedste for at løse det. Prøv venligst igen senere. ", "app.containers.landing.verifyNow": "Verificer nu", "app.containers.landing.verifyYourIdentity": "Bliv en verificeret borger", "app.containers.landing.viewAllEventsText": "Se alle begive<PERSON><PERSON>r", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Tilbage til mappen", "app.errors.after_end_at": "Startdatoen ligger efter slutdatoen", "app.errors.avatar_carrierwave_download_error": "Kunne ikke downloade avatar-filen.", "app.errors.avatar_carrierwave_integrity_error": "Avatar-filen er ikke af en tilladt filtype.", "app.errors.avatar_carrierwave_processing_error": "<PERSON><PERSON> ikke behandle avatar.", "app.errors.avatar_extension_blacklist_error": "Filtypen til avatarbilledet er ikke tilladt. Tilladte filtyper er: jpg, jpeg, gif og png.", "app.errors.avatar_extension_whitelist_error": "Filtypen til avatarbilledet er ikke tilladt. Tilladte filtyper er: jpg, jpeg, gif og png.", "app.errors.banner_cta_button_multiloc_blank": "Indtast en knaptekst.", "app.errors.banner_cta_button_url_blank": "Indtast et link.", "app.errors.banner_cta_button_url_url": "Indtast et gyldigt link. <PERSON><PERSON>rg for, at linket starter med 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Indtast en knaptekst.", "app.errors.banner_cta_signed_in_url_blank": "Indtast et link.", "app.errors.banner_cta_signed_in_url_url": "Indtast et gyldigt link. <PERSON><PERSON>rg for, at linket starter med 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Indtast en knaptekst.", "app.errors.banner_cta_signed_out_url_blank": "Indtast et link.", "app.errors.banner_cta_signed_out_url_url": "Indtast et gyldigt link. <PERSON><PERSON>rg for, at linket starter med 'https://'.", "app.errors.base_includes_banned_words": "Du har måske brugt et eller flere ord, der betragtes som bandeord. Du bedes ændre din tekst for at fjerne eventuelle bandeord.", "app.errors.body_multiloc_includes_banned_words": "Beskrivelsen indeholder ord, der anses for upassende.", "app.errors.bulk_import_idea_not_valid": "Den resulterende idé er ikke gyldig: {value}.", "app.errors.bulk_import_image_url_not_valid": "Der kunne ikke downloades noget billede fra {value}. <PERSON><PERSON><PERSON> for, at URL-adressen er gyldig og slutter med f.eks. .png eller .jpg. Dette problem opstår i rækken med ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Idea location med en manglende koordinat i {value}. Dette problem opstår i rækken med ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Idéplacering med en ikke-numerisk koordinat i {value}. Dette problem opstår i rækken med ID {row}.", "app.errors.bulk_import_malformed_pdf": "Den uploadede PDF-fil ser ud til at være misdannet. Prøv at eksportere PDF'en igen fra din kilde, og upload derefter igen.", "app.errors.bulk_import_maximum_ideas_exceeded": "Det maksimale antal {value} idéer er overskredet.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Det maksimale antal {value} sider i en PDF er overskredet.", "app.errors.bulk_import_not_enough_pdf_pages": "Den uploadede PDF har ikke nok sider - den skal have mindst det samme antal sider som den downloadede skabelon.", "app.errors.bulk_import_publication_date_invalid_format": "Idé med ugyldigt format for udgivelsesdato \"{value}\". Brug venligst formatet \"DD-MM-YYYY\".", "app.errors.cannot_contain_ideas": "Den valgte metode til borgerinddragelse understøtter ikke denne type indlæg. Redigér dit valg, og prøv igen.", "app.errors.cant_change_after_first_response": "Du kan ikke længere ændre <PERSON>, da nogle brugere allerede har reageret", "app.errors.category_name_taken": "Der findes allerede en kategori med dette navn", "app.errors.confirmation_code_expired": "Koden er udløbet. Send venligst en ny kode.", "app.errors.confirmation_code_invalid": "Ugyldig bekræftelseskode. Tjek din e-mail for den korrekte kode, eller prøv 'Send ny kode'", "app.errors.confirmation_code_too_many_resets": "Du har anvendt bekræftelseskoden for mange gange. Kontakt os for at modtage en invitationskode i stedet.", "app.errors.confirmation_code_too_many_retries": "Du har prøvet for mange gange. Send en kode igen eller prøv at ændre din e-mail.", "app.errors.email_already_active": "E-mailadressen {value} fundet i række {row} tilhører allerede en registreret bruger", "app.errors.email_already_invited": "E-mailadressen {value} fundet i række {row} er allerede inviteret", "app.errors.email_blank": "<PERSON>te kan ikke være tomt", "app.errors.email_domain_blacklisted": "Brug et andet e-mail-domæne for at registrere dig.", "app.errors.email_invalid": "Indtast venligst en gyldig e-mailadresse.", "app.errors.email_taken": "En konto med denne e-mail eksisterer allerede. Du kan logge på i stedet. ", "app.errors.email_taken_by_invite": "{value} er allerede taget af en afventende invitation. Tjek din spam-indbakke, eller kontakt os på {supportEmail}, hvis du ikke kan finde den.", "app.errors.emails_duplicate": "En eller flere duplikatværdier for e-mailadressen {value} blev fundet i følgende række(r): {rows}", "app.errors.extension_whitelist_error": "Formatet på den fil du prøvede at overføre er ikke understøttet.", "app.errors.file_extension_whitelist_error": "Formatet på den fil du prøvede at overføre er ikke understøttet.", "app.errors.first_name_blank": "<PERSON>te kan ikke være tomt", "app.errors.generics.blank": "<PERSON>te kan ikke være tomt", "app.errors.generics.invalid": "<PERSON><PERSON> ligner ikke en gyldig værdi", "app.errors.generics.taken": "Denne e-mail eksisterer allerede. En anden konto er allerede tilknyttet den.", "app.errors.generics.unsupported_locales": "<PERSON><PERSON> felt understøtter ikke den nuværende lokalitet.", "app.errors.group_ids_unauthorized_choice_moderator": "Som projektleder kan du kun sende e-mails til deltagere, der kan få adgang til dine projekter", "app.errors.has_other_overlapping_phases": "Projekter kan ikke have overlappende faser.", "app.errors.invalid_email": "The email {value} fundet i række {row} er ikke en gyldig e-mailadresse", "app.errors.invalid_row": "En ukendt fejl opstod ved forsøg på at behandle række {row}", "app.errors.is_not_timeline_project": "Det aktuelle projekt understøtter ikke faser.", "app.errors.key_invalid": "Nøglen må kun indeholde bogstaver, tal og underscore(_)", "app.errors.last_name_blank": "<PERSON>te kan ikke være tomt", "app.errors.locale_blank": "<PERSON><PERSON><PERSON><PERSON> venligst et sprog", "app.errors.locale_inclusion": "Vælg venligst et understøttet sprog", "app.errors.malformed_admin_value": "Den admin-værdi {value}, der er fundet i række {row}, er ikke gyldig", "app.errors.malformed_groups_value": "Gruppen {value} fundet i række {row} er ikke en gyldig gruppe", "app.errors.max_invites_limit_exceeded1": "Antallet af invitationer overskrider grænsen på 1000.", "app.errors.maximum_attendees_greater_than1": "Det maksimale antal deltagere skal være større end 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Det maksimale antal deltagere skal være større end eller lig med det aktuelle antal deltagere.", "app.errors.no_invites_specified": "Kunne ikke finde nogen e-mailadresse.", "app.errors.no_recipients": "Kampagnen kan ikke sendes ud, fordi der ikke er nogen modtagere. Den gruppe, du sender til, er enten tom, eller ingen har givet samtykke til at modtage e-mails.", "app.errors.number_invalid": "Indtast venligst et gyldigt nummer.", "app.errors.password_blank": "<PERSON>te kan ikke være tomt", "app.errors.password_invalid": "Kontroller venligst din nuværende adgangskode igen.", "app.errors.password_too_short": "Adgangskoden skal være på mindst 8 tegn", "app.errors.resending_code_failed": "<PERSON>get gik galt, da vi sendte bekræftelseskoden.", "app.errors.slug_taken": "Denne projekt-URL findes allerede. Ændr venligst projektets slug til noget andet.", "app.errors.tag_name_taken": "Der findes allerede et tag med dette navn", "app.errors.title_multiloc_blank": "Titlen skal være udfyldt", "app.errors.title_multiloc_includes_banned_words": "Titlen indeholder ord, der anses for upassende.", "app.errors.token_invalid": "Links til nulstilling af adgangskode kan kun bruges en gang og er gyldige i en time efter afsendelse. {passwordResetLink}.", "app.errors.too_common": "Det er for nemt at gætte", "app.errors.too_long": "Må maximum være 72 karakterer langt", "app.errors.too_short": "Adgangskoden skal være på mindst 8 tegn", "app.errors.uncaught_error": "Der opstod en ukendt fejl.", "app.errors.unknown_group": "Gruppen {value} fundet i række {row} er ikke en kendt gruppe", "app.errors.unknown_locale": "Sproget {value} fundet i række {row} er ikke et konfigureret sprog", "app.errors.unparseable_excel": "Den valgte Excel-fil kunne ikke behandles", "app.errors.url": "Indtast et gyldigt link. <PERSON><PERSON><PERSON> for, at linket starter med 'https://", "app.errors.verification_taken": "Verificeringen kan ikke g<PERSON>, da en anden konto er blevet verificeret med de samme oplysninger.", "app.errors.view_name_taken": "Der findes allerede en visning med dette navn", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Uhensigtsmæssigt indhold blev automatisk opdaget i et indlæg eller en kommentar", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Log ind med StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Tilmeld dig med StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Opret en Stadt Wien-konto nu, og brug ét login til mange digitale tjenester i Wien.", "app.modules.id_cow.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_cow.emptyFieldError": "<PERSON>te kan ikke være tomt", "app.modules.id_cow.helpAltText": "<PERSON><PERSON>, hvor man kan finde ID-serienummeret på et identitetskort", "app.modules.id_cow.invalidIdSerialError": "Ugyldig ID-serie", "app.modules.id_cow.invalidRunError": "Ugyldig RUN", "app.modules.id_cow.noMatchFormError": "Der blev ikke fundet nogen match.", "app.modules.id_cow.notEntitledFormError": "<PERSON><PERSON><PERSON> be<PERSON>.", "app.modules.id_cow.showCOWHelp": "<PERSON><PERSON> kan jeg finde mit ID-Serienummer?", "app.modules.id_cow.somethingWentWrongError": "Vi kan ikke bekræfte dig, fordi noget gik galt", "app.modules.id_cow.submit": "Indsend", "app.modules.id_cow.takenFormError": "Allerede taget.", "app.modules.id_cow.verifyCow": "Bekræft ved hjælp af COW", "app.modules.id_franceconnect.verificationButtonAltText": "Verificer med FranceConnect", "app.modules.id_gent_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "<PERSON>te kan ikke være tomt", "app.modules.id_gent_rrn.gentRrnHelp": "Din personnummer er vist på bagsiden af din digitale identitets liste", "app.modules.id_gent_rrn.invalidRrnError": "Ugyldigt personnummer", "app.modules.id_gent_rrn.noMatchFormError": "Vi kunne ikke finde information om din personnummer på bagsiden", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Vi kan ikke verificere dig, fordi du bor uden for Gent", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Vi kan ikke verificere dig, fordi du er yngre end 14 år", "app.modules.id_gent_rrn.rrnLabel": "Socialsikringsnummer", "app.modules.id_gent_rrn.rrnTooltip": "Vi beder om din personnummer for at verificere, om du er borger i Gent og ældre end 14 år.", "app.modules.id_gent_rrn.showGentRrnHelp": "<PERSON><PERSON> kan jeg finde mit ID-Serienummer?", "app.modules.id_gent_rrn.somethingWentWrongError": "Vi kan ikke bekræfte dig, fordi noget gik galt", "app.modules.id_gent_rrn.submit": "Indsend", "app.modules.id_gent_rrn.takenFormError": "Din personnummer er allerede blevet brugt til at verificere en anden konto", "app.modules.id_gent_rrn.verifyGentRrn": "Bekræft ved hjælp af GentRrn", "app.modules.id_id_card_lookup.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.emptyFieldError": "<PERSON>te kan ikke være tomt", "app.modules.id_id_card_lookup.helpAltText": "Forklaring på id-kort", "app.modules.id_id_card_lookup.invalidCardIdError": "<PERSON>te id er ikke gyldigt.", "app.modules.id_id_card_lookup.noMatchFormError": "Der blev ikke fundet nogen match.", "app.modules.id_id_card_lookup.showHelp": "<PERSON><PERSON> kan jeg finde mit ID-Serienummer?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Vi kan ikke bekræfte dig, fordi noget gik galt", "app.modules.id_id_card_lookup.submit": "Indsend", "app.modules.id_id_card_lookup.takenFormError": "Allerede taget.", "app.modules.id_oostende_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.emptyFieldError": "<PERSON>te kan ikke være tomt", "app.modules.id_oostende_rrn.invalidRrnError": "Ugyldigt personnummer", "app.modules.id_oostende_rrn.noMatchFormError": "Vi kunne ikke finde information om din personnummer på bagsiden", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Vi kan ikke verificere dig, fordi du bor uden for Oostende", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Vi kan ikke verificere dig, fordi du er yngre end 14 år", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Din personnummer er vist på bagsiden af din digitale identitets liste", "app.modules.id_oostende_rrn.rrnLabel": "Socialsikringsnummer", "app.modules.id_oostende_rrn.rrnTooltip": "Vi beder dig om dit personnummer for at bekræfte, om du er borger i Oostende og over 14 år gammel.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "H<PERSON> kan jeg finde mit personnummer?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Vi kan ikke bekræfte dig, fordi noget gik galt", "app.modules.id_oostende_rrn.submit": "Indsend", "app.modules.id_oostende_rrn.takenFormError": "Din personnummer er allerede blevet brugt til at verificere en anden konto", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Bekræftelse ved hjælp af socialsikringsnummer", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Du har fået administratorrettigheder til mappen \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Del", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Se projekterne på {folderUrl} for at gøre din stemme hørt!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | fra borgerinddragelsesplatformen hos {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | fra borgerinddragelsesplatformen hos {orgName}", "app.sessionRecording.accept": "<PERSON><PERSON>, jeg accepterer", "app.sessionRecording.modalDescription1": "For bedre at forstå vores brugere beder vi tilfældigt en lille procentdel af de besøgende om at spore deres browsersession i detaljer.", "app.sessionRecording.modalDescription2": "Det eneste formål med de registrerede data er at forbedre hjemmesiden. Ingen af dine data vil blive delt med en tredjepart. Alle følsomme oplysninger, du indtaster, vil blive filtreret fra.", "app.sessionRecording.modalDescription3": "Accepterer du det?", "app.sessionRecording.modalDescriptionFaq": "<PERSON><PERSON> <PERSON><PERSON> spø<PERSON>l her.", "app.sessionRecording.modalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> os med at forbedre denne hjemmeside", "app.sessionRecording.reject": "<PERSON><PERSON>, jeg af<PERSON>r", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Gennemfør en øvelse i budgettildeling", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Indsaml feedback på et dokument", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Opret en undersøgelse på platformen", "app.utils.AdminPage.ProjectEdit.createPoll": "Opret en afstemning", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Indlejring af en ekstern spørgeskemaundersøgelse", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Find frivillige", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Indsaml input og feedback", "app.utils.AdminPage.ProjectEdit.shareInformation": "Del information", "app.utils.FormattedCurrency.credits": "kreditter", "app.utils.FormattedCurrency.tokens": "tokens", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# credits} one {# credit} other {# credits}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokens} one {# token} other {# tokens}}", "app.utils.IdeaCards.mostDiscussed": "Mest diskuteret", "app.utils.IdeaCards.mostReacted": "<PERSON><PERSON>t stemmer", "app.utils.IdeaCards.newest": "Nyeste", "app.utils.IdeaCards.oldest": "<PERSON><PERSON><PERSON>", "app.utils.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.trending": "Trending", "app.utils.IdeasNewPage.contributionFormTitle": "Tilføj nyt bidrag", "app.utils.IdeasNewPage.ideaFormTitle": "Til<PERSON>øj en ny ide", "app.utils.IdeasNewPage.initiativeFormTitle": "Tilføj nyt initiativ", "app.utils.IdeasNewPage.issueFormTitle1": "Tilføj nyt høringssvar", "app.utils.IdeasNewPage.optionFormTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> ny mulighed", "app.utils.IdeasNewPage.petitionFormTitle": "Tilføj ny underskriftsindsamling", "app.utils.IdeasNewPage.projectFormTitle": "Tilføj nyt projekt", "app.utils.IdeasNewPage.proposalFormTitle": "Tilføj nyt forslag", "app.utils.IdeasNewPage.questionFormTitle": "Tilføj nyt spørgsmål", "app.utils.IdeasNewPage.surveyTitle": "Spørgeundersøgelse", "app.utils.IdeasNewPage.viewYourComment": "<PERSON> <PERSON>", "app.utils.IdeasNewPage.viewYourContribution": "Se dit bidrag", "app.utils.IdeasNewPage.viewYourIdea": "Se din idé", "app.utils.IdeasNewPage.viewYourInitiative": "Se dit initiativ", "app.utils.IdeasNewPage.viewYourInput": "Se dine input", "app.utils.IdeasNewPage.viewYourIssue": "<PERSON> <PERSON>", "app.utils.IdeasNewPage.viewYourOption": "<PERSON> <PERSON> mulighed", "app.utils.IdeasNewPage.viewYourPetition": "Se din tilkendegivelse", "app.utils.IdeasNewPage.viewYourProject": "Se dit projekt", "app.utils.IdeasNewPage.viewYourProposal": "Se dit forslag", "app.utils.IdeasNewPage.viewYourQuestion": "Se dit spørgsm<PERSON>l", "app.utils.Projects.sendSubmission": "Send referencekode til min e-mail", "app.utils.Projects.sendSurveySubmission": "Send kvittering for indsendelse af survey til min e-mail", "app.utils.Projects.surveySubmission": "Indsendelse af undersøgelse", "app.utils.Projects.yourResponseHasTheFollowingId": "Dit svar har følgende identifikation: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "<PERSON><PERSON> du senere beslutter, at du vil have dit svar fjernet, bedes du kontakte os med følgende unikke identifikation:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Du skal udfylde din profil for at deltage i dette arrangement.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Du opfylder ikke kravene til at deltage i dette arrangement.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Du må ikke deltage i dette arrangement.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Du skal logge ind eller registrere dig for at deltage i dette arrangement.", "app.utils.actionDescriptors.attendingEventNotVerified": "Du skal bekræfte din konto, før du kan deltage i dette arrangement.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "Du skal udfylde din profil for at blive frivillig.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Du opfylder ikke kravene til at være frivillig.", "app.utils.actionDescriptors.volunteeringNotPermitted": "Du må ikke melde dig som frivillig.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Du skal logge ind eller registrere dig for at være frivillig.", "app.utils.actionDescriptors.volunteeringNotVerified": "Du skal bekræfte din konto, før du kan melde dig som frivillig.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Venligst {completeRegistrationLink} for at melde dig som frivillig.", "app.utils.errors.api_error_default.in": "<PERSON><PERSON> <PERSON><PERSON><PERSON> rig<PERSON>gt", "app.utils.errors.default.ajv_error_birthyear_required": "Udfyld venligst dit fødselsår", "app.utils.errors.default.ajv_error_date_any": "Udfyld venligst en gyldig dato", "app.utils.errors.default.ajv_error_domicile_required": "<PERSON>d<PERSON>ld venligst dit bopælssted", "app.utils.errors.default.ajv_error_gender_required": "Udfyld venligst dit køn", "app.utils.errors.default.ajv_error_invalid": "<PERSON><PERSON> <PERSON>", "app.utils.errors.default.ajv_error_maxItems": "Kan ikke indeholde mere end {limit, plural, one {# genstand} other {# genstande}}", "app.utils.errors.default.ajv_error_minItems": "Skal mindst indeholde {limit, plural, one {# genstand} other {# genstande}}", "app.utils.errors.default.ajv_error_number_any": "Udfyld venligst et gyldigt nummer", "app.utils.errors.default.ajv_error_politician_required": "<PERSON><PERSON>, om du er politiker", "app.utils.errors.default.ajv_error_required3": "Feltet er påkrævet: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Kan ikke være tom", "app.utils.errors.default.api_error_accepted": "Skal accepteres", "app.utils.errors.default.api_error_blank": "Kan ikke være tom", "app.utils.errors.default.api_error_confirmation": "Passer ikke sammen", "app.utils.errors.default.api_error_empty": "Må ikke være tomt", "app.utils.errors.default.api_error_equal_to": "<PERSON><PERSON> <PERSON><PERSON><PERSON> rig<PERSON>gt", "app.utils.errors.default.api_error_even": "Skal være lige", "app.utils.errors.default.api_error_exclusion": "Er reserveret", "app.utils.errors.default.api_error_greater_than": "Er for lille", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Er for lille", "app.utils.errors.default.api_error_inclusion": "Er ikke medtaget i listen", "app.utils.errors.default.api_error_invalid": "<PERSON><PERSON> <PERSON>", "app.utils.errors.default.api_error_less_than": "Er for stor", "app.utils.errors.default.api_error_less_than_or_equal_to": "Er for stor", "app.utils.errors.default.api_error_not_a_number": "<PERSON>r ikke et tal", "app.utils.errors.default.api_error_not_an_integer": "Skal være et heltal", "app.utils.errors.default.api_error_other_than": "<PERSON><PERSON> <PERSON><PERSON><PERSON> rig<PERSON>gt", "app.utils.errors.default.api_error_present": "Skal være tom", "app.utils.errors.default.api_error_too_long": "Er for lang", "app.utils.errors.default.api_error_too_short": "Er for kort", "app.utils.errors.default.api_error_wrong_length": "Er af den forkerte længde", "app.utils.errors.defaultapi_error_.odd": "Skal være ulige", "app.utils.notInGroup": "Du opfylder ikke betingelserne for at deltage.", "app.utils.participationMethod.onSurveySubmission": "Tak. Vi har modtaget dit svar.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Der er ikke længere mulighed for at stemme, da denne fase ikke længere er aktiv.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Du opfylder ikke kravene til at stemme.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Du har ikke lov til at stemme.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Du skal logge ind eller registrere dig for at stemme.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Du skal bekræfte din konto, før du kan stemme.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Der blev lukket for indsendelse af budgetter på {endDate}.</b> Deltagerne havde i alt <b>{maxBudget} hver til at fordele mellem {optionCount} muligheder.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Indsendt budget", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Budget indsendt 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Du opfylder ikke kravene til at tildele budgetter.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Det er ikke tilladt at tildele budgetter.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Du skal logge ind eller registrere dig for at tildele budgetter.", "app.utils.votingMethodUtils.budgetingNotVerified": "Du skal bekræfte din konto, før du kan tildele budgetter.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Dit budget vil ikke blive talt med</b> , fø<PERSON> du klikker på \"Send\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Det krævede minimumsbudget er {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "<PERSON><PERSON><PERSON> du er færdig, skal du klikke på \"Send\" for at indsende dit budget.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "<PERSON><PERSON><PERSON><PERSON> dine foretrukne inds<PERSON> ved at trykke på \"Tilføj\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Du har i alt <b>{maxBudget} at fordele mellem {optionCount} muligheder.</b>", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON>, dit budget er blevet indsendt!</b> Du kan til enhver tid tjekke dine valg nedenfor eller ændre dem før <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON>, dit budget er blevet indsendt!</b> Du kan til enhver tid tjekke dine muligheder nedenfor.", "app.utils.votingMethodUtils.castYourVote": "Afgiv din stemme", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Du kan maksimalt tilføje {maxVotes, plural, one {# kredit} other {# kreditter}} pr. mulighed.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Du kan højst tilføje {maxVotes, plural, one {# point} other {# points}} pr. mulighed.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Du kan højst tilføje {maxVotes, plural, one {# token} other {# tokens}} pr. mulighed.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Du kan højst tilføje {maxVotes, plural, one {# stemme} other {# stimmer}} pr. mulighed.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON><PERSON><PERSON> du er færdig, skal du klikke på \"Send\" for at afgive din stemme.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "<PERSON><PERSON><PERSON><PERSON> dine foretrukne muligheder ved at trykke på \"Stem\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "Du har i alt <b>{totalVotes, plural, one {# kredit} other {# kreditter}} at fordele mellem {optionCount, plural, one {# mulighed} other {# muligheder}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "Du har i alt <b>{totalVotes, plural, one {# point} other {# point}} at fordele mellem {optionCount, plural, one {# mulighed} other {# muligheder}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "Du har i alt <b>{totalVotes, plural, one {# token} other {# tokens}} at fordele mellem {optionCount, plural, one {# mulighed} other {# muligheder}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "Du har i alt <b>{totalVotes, plural, one {# stemme} other {# stemmer}} at fordele mellem {optionCount, plural, one {# mulighed} other {# muligheder}}</b>.", "app.utils.votingMethodUtils.finalResults": "Endelige resultater", "app.utils.votingMethodUtils.finalTally": "<PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.howToParticipate": "<PERSON><PERSON>dan deltager du", "app.utils.votingMethodUtils.howToVote": "<PERSON><PERSON><PERSON> stemmer du", "app.utils.votingMethodUtils.multipleVotingEnded1": "Afstemningen sluttede på <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 kreditter} one {1 kredit} other {# kreditter}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 point} one {1 point} other {# point}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokens} one {1 token} other {# tokens}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 stemmer} one {1 stemme} other {# stemmer}}", "app.utils.votingMethodUtils.results": "Resultater", "app.utils.votingMethodUtils.singleVotingEnded": "Afstemningen sluttede den <b>{endDate}.</b> <PERSON><PERSON><PERSON> kunne <b>stemme på {maxVotes} muligheder.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "<PERSON><PERSON><PERSON><PERSON> dine foretrukne muligheder ved at trykke på \"Stem\".", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Du har <b>{totalVotes} stemmer</b>, som du kan tildele til mulighederne.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON><PERSON><PERSON> du er færdig, skal du klikke på \"Send\" for at afgive din stemme.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Afstemningen sluttede den <b>{endDate}.</b> <PERSON><PERSON><PERSON> kunne <b>stemme på 1 mulighed.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "<PERSON><PERSON><PERSON><PERSON> din foretrukne mulighed ved at trykke på \"Stem\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "<PERSON> har <b>1 stemme</b> , som du kan tildele en af valgmulighederne.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Afstemningen sluttede den <b>{endDate}.</b> <PERSON><PERSON><PERSON> kunne <b>stemme på så mange muligheder, som de ønskede.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Du kan stemme på lige så mange muligheder, som du vil.", "app.utils.votingMethodUtils.submitYourBudget": "Indsend dit budget", "app.utils.votingMethodUtils.submittedBudgetCountText2": "person indsendte sit budget online", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "folk indsendte deres budgetter online", "app.utils.votingMethodUtils.submittedVoteCountText2": "person afgav sin stemme online", "app.utils.votingMethodUtils.submittedVotesCountText2": "folk afgav deres stemme online", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "<PERSON><PERSON>me er afgivet 🎉", "app.utils.votingMethodUtils.votesCast": "<PERSON><PERSON><PERSON><PERSON><PERSON> stemmer", "app.utils.votingMethodUtils.votingClosed": "Afstemning lukket", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Din stemme vil ikke blive talt med</b>, fø<PERSON> du k<PERSON> på \"Send\".", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON>, din stemme er indsendt!</b> Du kan kontrollere eller ændre din indsendelse før <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON>, din stemme er afgivet!</b> Du kan til enhver tid kontrollere eller ændre din indsendelse nedenfor.", "components.UI.IdeaSelect.noIdeaAvailable": "Der er ingen idéer til rådighed.", "components.UI.IdeaSelect.selectIdea": "<PERSON><PERSON><PERSON><PERSON> id<PERSON>", "containers.SiteMap.allProjects": "Alle projekter", "containers.SiteMap.customPageSection": "Brugerdefinerede sider", "containers.SiteMap.folderInfo": "Mere info", "containers.SiteMap.headSiteMapTitle": "Kort over hjemmesiden | {orgName}", "containers.SiteMap.homeSection": "Generelt", "containers.SiteMap.pageContents": "Sideindhold", "containers.SiteMap.profilePage": "<PERSON> profilside", "containers.SiteMap.profileSettings": "<PERSON><PERSON>", "containers.SiteMap.projectEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectIdeas": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectInfo": "Information", "containers.SiteMap.projectPoll": "Afstemning", "containers.SiteMap.projectSurvey": "Spørgeundersøgelse", "containers.SiteMap.projectsArchived": "<PERSON><PERSON><PERSON> projekter", "containers.SiteMap.projectsCurrent": "Nuværen<PERSON> projekter", "containers.SiteMap.projectsDraft": "Projekter i udkast", "containers.SiteMap.projectsSection": "Projekter fra {orgName}", "containers.SiteMap.signInPage": "Log ind", "containers.SiteMap.signUpPage": "Registrer dig", "containers.SiteMap.siteMapDescription": "Fra denne side kan du navigere til alt indhold på siden.", "containers.SiteMap.siteMapTitle": "Sitemap over dialogplatformen for {orgName}", "containers.SiteMap.successStories": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.timeline": "Projektfaser", "containers.SiteMap.userSpaceSection": "<PERSON> konto", "containers.SubscriptionEndedPage.accessDenied": "Du har ikke længere adgang", "containers.SubscriptionEndedPage.subscriptionEnded": "Denne side er kun tilgængelig for platforme med et aktivt abonnement."}
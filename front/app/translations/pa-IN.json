{"EmailSettingsPage.emailSettings": "ਈਮੇਲ ਸੈਟਿੰਗਾਂ", "EmailSettingsPage.initialUnsubscribeError": "ਇਸ ਮੁਹਿੰਮ ਤੋਂ ਗਾਹਕੀ ਹਟਾਉਣ ਵਿੱਚ ਇੱਕ ਸਮੱਸਿਆ ਆਈ ਸੀ, ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "EmailSettingsPage.initialUnsubscribeLoading": "ਤੁਹਾਡੀ ਬੇਨਤੀ 'ਤੇ ਕਾਰਵਾਈ ਕੀਤੀ ਜਾ ਰਹੀ ਹੈ, ਕਿਰਪਾ ਕਰਕੇ ਉਡੀਕ ਕਰੋ...", "EmailSettingsPage.initialUnsubscribeSuccess": "ਤੁਸੀਂ ਸਫਲਤਾਪੂਰਵਕ {campaignTitle}ਤੋਂ ਗਾਹਕੀ ਹਟਾ ਦਿੱਤੀ ਹੈ।", "UI.FormComponents.optional": "ਵਿਕਲਪਿਕ", "app.closeIconButton.a11y_buttonActionMessage": "ਬੰਦ ਕਰੋ", "app.components.Areas.areaUpdateError": "ਤੁਹਾਡੇ ਖੇਤਰ ਨੂੰ ਸੁਰੱਖਿਅਤ ਕਰਨ ਦੌਰਾਨ ਇੱਕ ਤਰੁੱਟੀ ਉਤਪੰਨ ਹੋਈ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.components.Areas.followedArea": "ਅਨੁਸਰਣ ਕੀਤਾ ਖੇਤਰ: {areaTitle}", "app.components.Areas.followedTopic": "ਅਨੁਸਰਣ ਕੀਤਾ ਵਿਸ਼ਾ: {topicTitle}", "app.components.Areas.topicUpdateError": "ਤੁਹਾਡੇ ਵਿਸ਼ੇ ਨੂੰ ਸੰਭਾਲਣ ਦੌਰਾਨ ਇੱਕ ਤਰੁੱਟੀ ਉਤਪੰਨ ਹੋਈ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.components.Areas.unfollowedArea": "ਅਨੁਸਰਣ ਨਾ ਕੀਤਾ ਗਿਆ ਖੇਤਰ: {areaTitle}", "app.components.Areas.unfollowedTopic": "ਅਨੁਸਰਣ ਨਾ ਕੀਤਾ ਗਿਆ ਵਿਸ਼ਾ: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "ਕੀਮਤ:", "app.components.AssignBudgetControl.add": "ਸ਼ਾਮਲ ਕਰੋ", "app.components.AssignBudgetControl.added": "ਜੋੜਿਆ ਗਿਆ", "app.components.AssignMultipleVotesControl.addVote": "ਵੋਟ ਸ਼ਾਮਲ ਕਰੋ", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "ਤੁਸੀਂ ਆਪਣੇ ਸਾਰੇ ਕ੍ਰੈਡਿਟ ਵੰਡ ਦਿੱਤੇ ਹਨ।", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "ਤੁਸੀਂ ਇਸ ਵਿਕਲਪ ਲਈ ਵੱਧ ਤੋਂ ਵੱਧ ਕ੍ਰੈਡਿਟ ਵੰਡ ਦਿੱਤੇ ਹਨ।", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "ਤੁਸੀਂ ਆਪਣੇ ਸਾਰੇ ਅੰਕ ਵੰਡ ਦਿੱਤੇ ਹਨ।", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "ਤੁਸੀਂ ਇਸ ਵਿਕਲਪ ਲਈ ਵੱਧ ਤੋਂ ਵੱਧ ਅੰਕ ਵੰਡ ਦਿੱਤੇ ਹਨ।", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "ਤੁਸੀਂ ਆਪਣੇ ਸਾਰੇ ਟੋਕਨ ਵੰਡ ਦਿੱਤੇ ਹਨ।", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "ਤੁਸੀਂ ਇਸ ਵਿਕਲਪ ਲਈ ਟੋਕਨਾਂ ਦੀ ਵੱਧ ਤੋਂ ਵੱਧ ਗਿਣਤੀ ਵੰਡ ਦਿੱਤੀ ਹੈ।", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "ਤੁਸੀਂ ਆਪਣੀਆਂ ਸਾਰੀਆਂ ਵੋਟਾਂ ਵੰਡ ਦਿੱਤੀਆਂ ਹਨ।", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "ਤੁਸੀਂ ਇਸ ਵਿਕਲਪ ਲਈ ਵੱਧ ਤੋਂ ਵੱਧ ਵੋਟਾਂ ਵੰਡ ਦਿੱਤੀਆਂ ਹਨ।", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(1 ਔਫਲਾਈਨ ਸਮੇਤ)} other {(# ਔਫਲਾਈਨ ਸਮੇਤ)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "ਵੋਟਿੰਗ ਉਪਲਬਧ ਨਹੀਂ ਹੈ, ਕਿਉਂਕਿ ਇਹ ਪੜਾਅ ਕਿਰਿਆਸ਼ੀਲ ਨਹੀਂ ਹੈ।", "app.components.AssignMultipleVotesControl.removeVote": "ਵੋਟ ਹਟਾਓ", "app.components.AssignMultipleVotesControl.select": "ਚੁਣੋ", "app.components.AssignMultipleVotesControl.votesSubmitted1": "ਤੁਸੀਂ ਪਹਿਲਾਂ ਹੀ ਆਪਣੀ ਵੋਟ ਜਮ੍ਹਾਂ ਕਰ ਦਿੱਤੀ ਹੈ। ਇਸਨੂੰ ਸੋਧਣ ਲਈ, \"ਆਪਣੀ ਜਮ੍ਹਾਂ ਰਕਮ ਨੂੰ ਸੋਧੋ\" 'ਤੇ ਕਲਿੱਕ ਕਰੋ।", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "ਤੁਸੀਂ ਪਹਿਲਾਂ ਹੀ ਆਪਣੀ ਵੋਟ ਜਮ੍ਹਾਂ ਕਰ ਦਿੱਤੀ ਹੈ। ਇਸਨੂੰ ਸੋਧਣ ਲਈ, ਪ੍ਰੋਜੈਕਟ ਪੰਨੇ 'ਤੇ ਵਾਪਸ ਜਾਓ ਅਤੇ \"ਆਪਣੀ ਜਮ੍ਹਾਂ ਨੂੰ ਸੋਧੋ\" 'ਤੇ ਕਲਿੱਕ ਕਰੋ।", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {ਕ੍ਰੈਡਿਟ} other {ਕ੍ਰੈਡਿਟ}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {ਪੁਆਇੰਟ} other {ਪੁਆਇੰਟ}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {ਟੋਕਨ} other {ਟੋਕਨ}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {ਵੋਟ} other {ਵੋਟਾਂ}}", "app.components.AssignVoteControl.maxVotesReached1": "ਤੁਸੀਂ ਆਪਣੀਆਂ ਸਾਰੀਆਂ ਵੋਟਾਂ ਵੰਡ ਦਿੱਤੀਆਂ ਹਨ।", "app.components.AssignVoteControl.phaseNotActive": "ਵੋਟਿੰਗ ਉਪਲਬਧ ਨਹੀਂ ਹੈ, ਕਿਉਂਕਿ ਇਹ ਪੜਾਅ ਕਿਰਿਆਸ਼ੀਲ ਨਹੀਂ ਹੈ।", "app.components.AssignVoteControl.select": "ਚੁਣੋ", "app.components.AssignVoteControl.selected2": "ਚੁਣਿਆ ਗਿਆ", "app.components.AssignVoteControl.voteForAtLeastOne": "ਘੱਟੋ-ਘੱਟ 1 ਵਿਕਲਪ ਲਈ ਵੋਟ ਕਰੋ", "app.components.AssignVoteControl.votesSubmitted1": "ਤੁਸੀਂ ਪਹਿਲਾਂ ਹੀ ਆਪਣੀ ਵੋਟ ਜਮ੍ਹਾਂ ਕਰ ਦਿੱਤੀ ਹੈ। ਇਸਨੂੰ ਸੋਧਣ ਲਈ, \"ਆਪਣੀ ਜਮ੍ਹਾਂ ਰਕਮ ਨੂੰ ਸੋਧੋ\" 'ਤੇ ਕਲਿੱਕ ਕਰੋ।", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "ਤੁਸੀਂ ਪਹਿਲਾਂ ਹੀ ਆਪਣੀ ਵੋਟ ਜਮ੍ਹਾਂ ਕਰ ਦਿੱਤੀ ਹੈ। ਇਸਨੂੰ ਸੋਧਣ ਲਈ, ਪ੍ਰੋਜੈਕਟ ਪੰਨੇ 'ਤੇ ਵਾਪਸ ਜਾਓ ਅਤੇ \"ਆਪਣੀ ਜਮ੍ਹਾਂ ਨੂੰ ਸੋਧੋ\" 'ਤੇ ਕਲਿੱਕ ਕਰੋ।", "app.components.AuthProviders.continue": "ਜਾਰੀ ਰੱਖੋ", "app.components.AuthProviders.continueWithAzure": "{azureProviderName}ਨਾਲ ਜਾਰੀ ਰੱਖੋ", "app.components.AuthProviders.continueWithFacebook": "Facebook ਦੇ ਨਾਲ ਜਾਰੀ ਰੱਖੋ", "app.components.AuthProviders.continueWithFakeSSO": "ਜਾਅਲੀ SSO ਨਾਲ ਜਾਰੀ ਰੱਖੋ", "app.components.AuthProviders.continueWithGoogle": "Google ਨਾਲ ਜਾਰੀ ਰੱਖੋ", "app.components.AuthProviders.continueWithHoplr": "Hoplr ਨਾਲ ਜਾਰੀ ਰੱਖੋ", "app.components.AuthProviders.continueWithIdAustria": "ID Austria ਨਾਲ ਜਾਰੀ ਰੱਖੋ", "app.components.AuthProviders.continueWithLoginMechanism": "{loginMechanismName}ਨਾਲ ਜਾਰੀ ਰੱਖੋ", "app.components.AuthProviders.continueWithNemlogIn": "MitID ਨਾਲ ਜਾਰੀ ਰੱਖੋ", "app.components.AuthProviders.franceConnectMergingFailed": "ਇਸ ਈਮੇਲ ਪਤੇ ਨਾਲ ਇੱਕ ਖਾਤਾ ਪਹਿਲਾਂ ਹੀ ਮੌਜੂਦ ਹੈ।{br}{br}ਤੁਸੀਂ FranceConnect ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਪਲੇਟਫਾਰਮ ਤੱਕ ਪਹੁੰਚ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਨਿੱਜੀ ਵੇਰਵੇ ਮੇਲ ਨਹੀਂ ਖਾਂਦੇ। FranceConnect ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਲੌਗ ਇਨ ਕਰਨ ਲਈ, ਤੁਹਾਨੂੰ ਆਪਣੇ ਅਧਿਕਾਰਤ ਵੇਰਵਿਆਂ ਨਾਲ ਮੇਲ ਕਰਨ ਲਈ ਪਹਿਲਾਂ ਇਸ ਪਲੇਟਫਾਰਮ 'ਤੇ ਆਪਣਾ ਪਹਿਲਾ ਨਾਮ ਜਾਂ ਆਖਰੀ ਨਾਮ ਬਦਲਣਾ ਹੋਵੇਗਾ।{br}{br}ਤੁਸੀਂ ਹੇਠਾਂ ਲੌਗਇਨ ਕਰ ਸਕਦੇ ਹੋ ਜਿਵੇਂ ਤੁਸੀਂ ਆਮ ਤੌਰ 'ਤੇ ਕਰਦੇ ਹੋ।", "app.components.AuthProviders.goToLogIn": "ਕੀ ਪਹਿਲਾਂ ਤੋਂ ਹੀ ਖਾਤਾ ਹੈ? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "ਕੀ ਤੁਹਾਡੇ ਕੋਲ ਖਾਤਾ ਨਹੀਂ ਹੈ? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "ਲਾਗਿਨ", "app.components.AuthProviders.logInWithEmail": "ਈਮੇਲ ਨਾਲ ਲੌਗ ਇਨ ਕਰੋ", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "ਤਸਦੀਕ ਕੀਤੇ ਜਾਣ ਲਈ ਤੁਹਾਡੀ ਨਿਸ਼ਚਿਤ ਘੱਟੋ-ਘੱਟ ਉਮਰ ਜਾਂ ਵੱਧ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ।", "app.components.AuthProviders.signUp2": "ਸਾਇਨ ਅਪ", "app.components.AuthProviders.signUpButtonAltText": "{loginMechanismName}ਨਾਲ ਸਾਈਨ ਅੱਪ ਕਰੋ", "app.components.AuthProviders.signUpWithEmail": "ਈਮੇਲ ਨਾਲ ਸਾਈਨ ਅੱਪ ਕਰੋ", "app.components.AuthProviders.verificationRequired": "ਪੁਸ਼ਟੀਕਰਨ ਦੀ ਲੋੜ ਹੈ", "app.components.Author.a11yPostedBy": "ਵੱਲੋਂ ਪੋਸਟ ਕੀਤਾ ਗਿਆ", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 ਭਾਗੀਦਾਰ} other {{numberOfParticipants} ਭਾਗੀਦਾਰ}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} ਉਪਭੋਗਤਾ", "app.components.AvatarBubbles.participant": "ਭਾਗੀਦਾਰ", "app.components.AvatarBubbles.participants1": "ਭਾਗੀਦਾਰ", "app.components.Comments.cancel": "ਰੱਦ ਕਰੋ", "app.components.Comments.commentingDisabledInCurrentPhase": "ਮੌਜੂਦਾ ਦੌਰ ਵਿੱਚ ਟਿੱਪਣੀ ਸੰਭਵ ਨਹੀਂ ਹੈ।", "app.components.Comments.commentingDisabledInactiveProject": "ਟਿੱਪਣੀ ਕਰਨਾ ਸੰਭਵ ਨਹੀਂ ਹੈ ਕਿਉਂਕਿ ਇਹ ਪ੍ਰੋਜੈਕਟ ਵਰਤਮਾਨ ਵਿੱਚ ਕਿਰਿਆਸ਼ੀਲ ਨਹੀਂ ਹੈ।", "app.components.Comments.commentingDisabledProject": "ਇਸ ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਟਿੱਪਣੀ ਕਰਨਾ ਫਿਲਹਾਲ ਅਸਮਰੱਥ ਹੈ।", "app.components.Comments.commentingDisabledUnverified": "ਟਿੱਪਣੀ ਕਰਨ ਲਈ {verifyIdentityLink} ।", "app.components.Comments.commentingMaybeNotPermitted": "ਕਿਰਪਾ ਕਰਕੇ ਇਹ ਦੇਖਣ ਲਈ {signInLink} ਕੀ ਕਾਰਵਾਈਆਂ ਕੀਤੀਆਂ ਜਾ ਸਕਦੀਆਂ ਹਨ।", "app.components.Comments.inputsAssociatedWithProfile": "ਮੂਲ ਰੂਪ ਵਿੱਚ ਤੁਹਾਡੀਆਂ ਬੇਨਤੀਆਂ ਤੁਹਾਡੇ ਪ੍ਰੋਫਾਈਲ ਨਾਲ ਜੁੜੀਆਂ ਹੋਣਗੀਆਂ, ਜਦੋਂ ਤੱਕ ਤੁਸੀਂ ਇਸ ਵਿਕਲਪ ਨੂੰ ਨਹੀਂ ਚੁਣਦੇ।", "app.components.Comments.invisibleTitleComments": "ਟਿੱਪਣੀਆਂ", "app.components.Comments.leastRecent": "ਸਭ ਤੋਂ ਤਾਜ਼ਾ", "app.components.Comments.likeComment": "ਇਸ ਟਿੱਪਣੀ ਨੂੰ ਪਸੰਦ ਕਰੋ", "app.components.Comments.mostLiked": "ਜ਼ਿਆਦਾਤਰ ਪ੍ਰਤੀਕਰਮ", "app.components.Comments.mostRecent": "ਸਭ ਤੋਂ ਤਾਜ਼ਾ", "app.components.Comments.official": "ਅਧਿਕਾਰੀ", "app.components.Comments.postAnonymously": "ਅਗਿਆਤ ਰੂਪ ਵਿੱਚ ਪੋਸਟ ਕਰੋ", "app.components.Comments.replyToComment": "ਟਿੱਪਣੀ ਦਾ ਜਵਾਬ", "app.components.Comments.reportAsSpam": "ਸਪੈਮ ਵਜੋਂ ਰਿਪੋਰਟ ਕਰੋ", "app.components.Comments.seeOriginal": "ਅਸਲੀ ਦੇਖੋ", "app.components.Comments.seeTranslation": "ਅਨੁਵਾਦ ਦੇਖੋ", "app.components.Comments.yourComment": "ਤੁਹਾਡੀ ਟਿੱਪਣੀ", "app.components.CommonGroundResults.divisiveDescription": "ਉਹ ਬਿਆਨ ਜਿੱਥੇ ਲੋਕ ਬਰਾਬਰ ਸਹਿਮਤ ਅਤੇ ਅਸਹਿਮਤ ਹਨ:", "app.components.CommonGroundResults.divisiveTitle": "ਫੁੱਟ ਪਾਉਣ ਵਾਲਾ", "app.components.CommonGroundResults.majorityDescription": "60% ਤੋਂ ਵੱਧ ਲੋਕਾਂ ਨੇ ਹੇਠ ਲਿਖਿਆਂ 'ਤੇ ਕਿਸੇ ਨਾ ਕਿਸੇ ਤਰੀਕੇ ਨਾਲ ਵੋਟ ਪਾਈ:", "app.components.CommonGroundResults.majorityTitle": "ਬਹੁਮਤ", "app.components.CommonGroundResults.participantLabel": "ਭਾਗੀਦਾਰ", "app.components.CommonGroundResults.participantsLabel1": "ਭਾਗੀਦਾਰ", "app.components.CommonGroundResults.statementLabel": "ਬਿਆਨ", "app.components.CommonGroundResults.statementsLabel1": "ਬਿਆਨ", "app.components.CommonGroundResults.votesLabe": "ਵੋਟ", "app.components.CommonGroundResults.votesLabel1": "ਵੋਟਾਂ", "app.components.CommonGroundStatements.agreeLabel": "ਸਹਿਮਤ", "app.components.CommonGroundStatements.disagreeLabel": "ਅਸਹਿਮਤ", "app.components.CommonGroundStatements.noMoreStatements": "ਇਸ ਵੇਲੇ ਜਵਾਬ ਦੇਣ ਲਈ ਕੋਈ ਬਿਆਨ ਨਹੀਂ ਹਨ।", "app.components.CommonGroundStatements.noResults": "ਅਜੇ ਦਿਖਾਉਣ ਲਈ ਕੋਈ ਨਤੀਜੇ ਨਹੀਂ ਹਨ। ਕਿਰਪਾ ਕਰਕੇ ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਤੁਸੀਂ ਕਾਮਨ ਗਰਾਊਂਡ ਪੜਾਅ ਵਿੱਚ ਹਿੱਸਾ ਲਿਆ ਹੈ ਅਤੇ ਬਾਅਦ ਵਿੱਚ ਇੱਥੇ ਦੁਬਾਰਾ ਜਾਂਚ ਕਰੋ।", "app.components.CommonGroundStatements.unsureLabel": "ਅਨਿਸ਼ਚਿਤ", "app.components.CommonGroundTabs.resultsTabLabel": "ਨਤੀਜੇ", "app.components.CommonGroundTabs.statementsTabLabel": "ਬਿਆਨ", "app.components.CommunityMonitorModal.formError": "ਇੱਕ ਗਲਤੀ ਆਈ।", "app.components.CommunityMonitorModal.surveyDescription2": "ਇਹ ਚੱਲ ਰਿਹਾ ਸਰਵੇਖਣ ਇਸ ਗੱਲ ਦਾ ਪਤਾ ਲਗਾਉਂਦਾ ਹੈ ਕਿ ਤੁਸੀਂ ਸ਼ਾਸਨ ਅਤੇ ਜਨਤਕ ਸੇਵਾਵਾਂ ਬਾਰੇ ਕਿਵੇਂ ਮਹਿਸੂਸ ਕਰਦੇ ਹੋ।", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {<1 ਮਿੰਟ ਲੈਂਦਾ ਹੈ} one {1 ਮਿੰਟ ਲੈਂਦਾ ਹੈ} other {# ਮਿੰਟ ਲੈਂਦਾ ਹੈ}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "ਪੁਸ਼ਟੀਕਰਨ ਕੋਡ ਵਾਲੀ ਇੱਕ ਈਮੇਲ {userEmail}'ਤੇ ਭੇਜੀ ਗਈ ਹੈ।", "app.components.ConfirmationModal.changeYourEmail": "ਆਪਣੀ ਈਮੇਲ ਬਦਲੋ।", "app.components.ConfirmationModal.codeInput": "ਕੋਡ", "app.components.ConfirmationModal.confirmationCodeSent": "ਨਵਾਂ ਕੋਡ ਭੇਜਿਆ ਗਿਆ", "app.components.ConfirmationModal.didntGetAnEmail": "ਕੀ ਕੋਈ ਈਮੇਲ ਪ੍ਰਾਪਤ ਨਹੀਂ ਹੋਈ?", "app.components.ConfirmationModal.foundYourCode": "ਤੁਹਾਡਾ ਕੋਡ ਮਿਲਿਆ?", "app.components.ConfirmationModal.goBack": "ਵਾਪਸ ਜਾਓ.", "app.components.ConfirmationModal.sendEmailWithCode": "ਕੋਡ ਨਾਲ ਈਮੇਲ ਭੇਜੋ", "app.components.ConfirmationModal.sendNewCode": "ਨਵਾਂ ਕੋਡ ਭੇਜੋ।", "app.components.ConfirmationModal.verifyAndContinue": "ਪੁਸ਼ਟੀ ਕਰੋ ਅਤੇ ਜਾਰੀ ਰੱਖੋ", "app.components.ConfirmationModal.wrongEmail": "ਗਲਤ ਈਮੇਲ?", "app.components.ConsentManager.Banner.accept": "ਸਵੀਕਾਰ ਕਰੋ", "app.components.ConsentManager.Banner.ariaButtonClose2": "ਨੀਤੀ ਨੂੰ ਰੱਦ ਕਰੋ ਅਤੇ ਬੈਨਰ ਬੰਦ ਕਰੋ", "app.components.ConsentManager.Banner.close": "ਬੰਦ ਕਰੋ", "app.components.ConsentManager.Banner.mainText": "ਇਹ ਪਲੇਟਫਾਰਮ ਸਾਡੇ {policyLink}ਦੇ ਅਨੁਸਾਰ ਕੂਕੀਜ਼ ਦੀ ਵਰਤੋਂ ਕਰਦਾ ਹੈ।", "app.components.ConsentManager.Banner.manage": "ਪ੍ਰਬੰਧਿਤ ਕਰੋ", "app.components.ConsentManager.Banner.policyLink": "ਕੂਕੀ ਨੀਤੀ", "app.components.ConsentManager.Banner.reject": "ਅਸਵੀਕਾਰ ਕਰੋ", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "ਇਸ਼ਤਿਹਾਰਬਾਜ਼ੀ", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "ਅਸੀਂ ਇਸਦੀ ਵਰਤੋਂ ਸਾਡੀ ਵੈਬਸਾਈਟ ਦੇ ਵਿਗਿਆਪਨ ਮੁਹਿੰਮਾਂ ਦੀ ਪ੍ਰਭਾਵਸ਼ੀਲਤਾ ਨੂੰ ਵਿਅਕਤੀਗਤ ਬਣਾਉਣ ਅਤੇ ਮਾਪਣ ਲਈ ਕਰਦੇ ਹਾਂ। ਅਸੀਂ ਇਸ ਪਲੇਟਫਾਰਮ 'ਤੇ ਕੋਈ ਵਿਗਿਆਪਨ ਨਹੀਂ ਦਿਖਾਵਾਂਗੇ, ਪਰ ਹੇਠਾਂ ਦਿੱਤੀਆਂ ਸੇਵਾਵਾਂ ਤੁਹਾਨੂੰ ਸਾਡੀ ਸਾਈਟ 'ਤੇ ਵਿਜ਼ਿਟ ਕੀਤੇ ਪੰਨਿਆਂ ਦੇ ਆਧਾਰ 'ਤੇ ਵਿਅਕਤੀਗਤ ਵਿਗਿਆਪਨ ਦੀ ਪੇਸ਼ਕਸ਼ ਕਰ ਸਕਦੀਆਂ ਹਨ।", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "ਇਜਾਜ਼ਤ ਦਿਓ", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "ਵਿਸ਼ਲੇਸ਼ਣ", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "ਅਸੀਂ ਇਸ ਟਰੈਕਿੰਗ ਦੀ ਵਰਤੋਂ ਇਹ ਸਮਝਣ ਲਈ ਕਰਦੇ ਹਾਂ ਕਿ ਤੁਸੀਂ ਆਪਣੇ ਨੈਵੀਗੇਸ਼ਨ ਨੂੰ ਸਿੱਖਣ ਅਤੇ ਬਿਹਤਰ ਬਣਾਉਣ ਲਈ ਪਲੇਟਫਾਰਮ ਦੀ ਵਰਤੋਂ ਕਿਵੇਂ ਕਰਦੇ ਹੋ। ਇਹ ਜਾਣਕਾਰੀ ਸਿਰਫ਼ ਪੁੰਜ ਵਿਸ਼ਲੇਸ਼ਣ ਵਿੱਚ ਵਰਤੀ ਜਾਂਦੀ ਹੈ, ਅਤੇ ਕਿਸੇ ਵੀ ਤਰੀਕੇ ਨਾਲ ਵਿਅਕਤੀਗਤ ਲੋਕਾਂ ਨੂੰ ਟਰੈਕ ਕਰਨ ਲਈ ਨਹੀਂ।", "app.components.ConsentManager.Modal.PreferencesDialog.back": "ਵਾਪਸ ਜਾਓ", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "ਰੱਦ ਕਰੋ", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "ਅਸਵੀਕਾਰ ਕਰੋ", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "ਕਾਰਜਸ਼ੀਲ", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "ਵੈੱਬਸਾਈਟ ਦੀਆਂ ਬੁਨਿਆਦੀ ਕਾਰਜਕੁਸ਼ਲਤਾਵਾਂ ਨੂੰ ਸਮਰੱਥ ਅਤੇ ਨਿਗਰਾਨੀ ਕਰਨ ਲਈ ਇਹ ਲੋੜੀਂਦਾ ਹੈ। ਹੋ ਸਕਦਾ ਹੈ ਕਿ ਇੱਥੇ ਸੂਚੀਬੱਧ ਕੁਝ ਟੂਲ ਤੁਹਾਡੇ 'ਤੇ ਲਾਗੂ ਨਾ ਹੋਣ। ਹੋਰ ਜਾਣਕਾਰੀ ਲਈ ਕਿਰਪਾ ਕਰਕੇ ਸਾਡੀ ਕੂਕੀ ਨੀਤੀ ਨੂੰ ਪੜ੍ਹੋ।", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "ਗੂਗਲ ਟੈਗ ਮੈਨੇਜਰ ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "ਲੋੜੀਂਦਾ ਹੈ", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "ਇੱਕ ਕਾਰਜਸ਼ੀਲ ਪਲੇਟਫਾਰਮ ਪ੍ਰਾਪਤ ਕਰਨ ਲਈ, ਜੇਕਰ ਤੁਸੀਂ ਸਾਈਨ ਅਪ ਕਰਦੇ ਹੋ, ਤਾਂ ਅਸੀਂ ਇੱਕ ਪ੍ਰਮਾਣਿਤ ਕੁਕੀ ਨੂੰ ਸੁਰੱਖਿਅਤ ਕਰਦੇ ਹਾਂ, ਅਤੇ ਉਹ ਭਾਸ਼ਾ ਜਿਸ ਵਿੱਚ ਤੁਸੀਂ ਇਸ ਪਲੇਟਫਾਰਮ ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹੋ।", "app.components.ConsentManager.Modal.PreferencesDialog.save": "ਸੇਵ ਕਰੋ", "app.components.ConsentManager.Modal.PreferencesDialog.title": "ਤੁਹਾਡੀਆਂ ਕੂਕੀਜ਼ ਤਰਜੀਹਾਂ", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "ਸੰਦ", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "ਸਮੱਗਰੀ ਅਪਲੋਡ ਬੇਦਾਅਵਾ", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "ਸਮਗਰੀ ਨੂੰ ਅਪਲੋਡ ਕਰਕੇ, ਤੁਸੀਂ ਘੋਸ਼ਣਾ ਕਰਦੇ ਹੋ ਕਿ ਇਹ ਸਮਗਰੀ ਤੀਜੀ ਧਿਰ ਦੇ ਕਿਸੇ ਵੀ ਨਿਯਮਾਂ ਜਾਂ ਅਧਿਕਾਰਾਂ ਦੀ ਉਲੰਘਣਾ ਨਹੀਂ ਕਰਦੀ, ਜਿਵੇਂ ਕਿ ਬੌਧਿਕ ਸੰਪੱਤੀ ਦੇ ਅਧਿਕਾਰ, ਗੋਪਨੀਯਤਾ ਅਧਿਕਾਰ, ਵਪਾਰਕ ਰਾਜ਼ਾਂ ਦੇ ਅਧਿਕਾਰ ਆਦਿ। ਸਿੱਟੇ ਵਜੋਂ, ਇਸ ਸਮਗਰੀ ਨੂੰ ਅਪਲੋਡ ਕਰਕੇ, ਤੁਸੀਂ ਅਪਲੋਡ ਕੀਤੀ ਸਮਗਰੀ ਦੇ ਨਤੀਜੇ ਵਜੋਂ ਹੋਣ ਵਾਲੇ ਸਾਰੇ ਸਿੱਧੇ ਅਤੇ ਅਸਿੱਧੇ ਨੁਕਸਾਨਾਂ ਲਈ ਪੂਰੀ ਅਤੇ ਨਿਵੇਕਲੀ ਜ਼ਿੰਮੇਵਾਰੀ ਚੁੱਕਣ ਦਾ ਵਾਅਦਾ ਕਰਦੇ ਹੋ। ਇਸ ਤੋਂ ਇਲਾਵਾ, ਤੁਸੀਂ ਪਲੇਟਫਾਰਮ ਦੇ ਮਾਲਕ ਅਤੇ ਗੋ ਵੋਕਲ ਨੂੰ ਕਿਸੇ ਵੀ ਤੀਜੀ ਧਿਰ ਦੇ ਦਾਅਵਿਆਂ ਜਾਂ ਤੀਜੀਆਂ ਧਿਰਾਂ ਦੇ ਵਿਰੁੱਧ ਦੇਣਦਾਰੀਆਂ, ਅਤੇ ਕਿਸੇ ਵੀ ਸੰਬੰਧਿਤ ਲਾਗਤਾਂ, ਜੋ ਤੁਹਾਡੇ ਦੁਆਰਾ ਅੱਪਲੋਡ ਕੀਤੀ ਸਮੱਗਰੀ ਤੋਂ ਪੈਦਾ ਹੋਣਗੀਆਂ ਜਾਂ ਨਤੀਜੇ ਵਜੋਂ ਹੋਣਗੀਆਂ, ਨੂੰ ਮੁਆਵਜ਼ਾ ਦੇਣ ਦਾ ਵਾਅਦਾ ਕਰਦੇ ਹੋ।", "app.components.ContentUploadDisclaimer.onAccept": "ਮੈਂ ਸੱਮਝਦਾ ਹਾਂ", "app.components.ContentUploadDisclaimer.onCancel": "ਰੱਦ ਕਰੋ", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "ਸਾਨੂੰ ਦੱਸੋ ਕਿਉਂ", "app.components.CustomFieldsForm.addressInputAriaLabel": "ਪਤਾ ਇਨਪੁੱਟ", "app.components.CustomFieldsForm.addressInputPlaceholder6": "ਪਤਾ ਦਰਜ ਕਰੋ...", "app.components.CustomFieldsForm.adminFieldTooltip": "ਖੇਤਰ ਸਿਰਫ਼ ਪ੍ਰਸ਼ਾਸਕਾਂ ਨੂੰ ਦਿਖਾਈ ਦੇਵੇਗਾ", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "ਇਸ ਸਰਵੇਖਣ ਦੇ ਸਾਰੇ ਜਵਾਬ ਗੁਮਨਾਮ ਹਨ।", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "ਇੱਕ ਬਹੁਭੁਜ ਲਈ ਘੱਟੋ-ਘੱਟ ਤਿੰਨ ਬਿੰਦੂਆਂ ਦੀ ਲੋੜ ਹੁੰਦੀ ਹੈ।", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "ਇੱਕ ਲਾਈਨ ਲਈ ਘੱਟੋ-ਘੱਟ ਦੋ ਬਿੰਦੂਆਂ ਦੀ ਲੋੜ ਹੁੰਦੀ ਹੈ।", "app.components.CustomFieldsForm.attachmentRequired": "ਘੱਟੋ-ਘੱਟ ਇੱਕ ਅਟੈਚਮੈਂਟ ਲੋੜੀਂਦੀ ਹੈ।", "app.components.CustomFieldsForm.authorFieldLabel": "ਲੇਖਕ", "app.components.CustomFieldsForm.authorFieldPlaceholder": "ਯੂਜ਼ਰ ਈਮੇਲ ਜਾਂ ਨਾਮ ਨਾਲ ਖੋਜ ਕਰਨ ਲਈ ਟਾਈਪ ਕਰਨਾ ਸ਼ੁਰੂ ਕਰੋ...", "app.components.CustomFieldsForm.back": "ਪਿੱਛੇ", "app.components.CustomFieldsForm.budgetFieldLabel": "ਬਜਟ", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "ਖਿੱਚਣ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਕਲਿੱਕ ਕਰੋ। ਫਿਰ, ਬਿੰਦੂਆਂ ਨੂੰ ਹਿਲਾਉਣ ਲਈ ਉਨ੍ਹਾਂ 'ਤੇ ਘਸੀਟੋ।", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "ਆਪਣਾ ਜਵਾਬ ਜੋੜਨ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਕਲਿੱਕ ਕਰੋ ਜਾਂ ਹੇਠਾਂ ਇੱਕ ਪਤਾ ਟਾਈਪ ਕਰੋ।", "app.components.CustomFieldsForm.confirm": "ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.CustomFieldsForm.descriptionMinLength": "ਵਰਣਨ ਘੱਟੋ-ਘੱਟ {min} ਅੱਖਰ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ।", "app.components.CustomFieldsForm.descriptionRequired": "ਵੇਰਵਾ ਲੋੜੀਂਦਾ ਹੈ", "app.components.CustomFieldsForm.fieldMaximumItems": "\"{fieldName}\" ਖੇਤਰ ਲਈ ਵੱਧ ਤੋਂ ਵੱਧ {maxSelections, plural, one {# ਵਿਕਲਪ} other {# ਵਿਕਲਪ}} ਚੁਣੇ ਜਾ ਸਕਦੇ ਹਨ।", "app.components.CustomFieldsForm.fieldMinimumItems": "\"{fieldName}\" ਖੇਤਰ ਲਈ ਘੱਟੋ-ਘੱਟ {minSelections, plural, one {# ਵਿਕਲਪ} other {# ਵਿਕਲਪ}} ਚੁਣੇ ਜਾ ਸਕਦੇ ਹਨ।", "app.components.CustomFieldsForm.fieldRequired": "\"{fieldName}\" ਖੇਤਰ ਲੋੜੀਂਦਾ ਹੈ।", "app.components.CustomFieldsForm.fileSizeLimit": "ਫਾਈਲ ਆਕਾਰ ਦੀ ਸੀਮਾ {maxFileSize} MB ਹੈ।", "app.components.CustomFieldsForm.imageRequired": "ਚਿੱਤਰ ਲੋੜੀਂਦਾ ਹੈ", "app.components.CustomFieldsForm.minimumCoordinates2": "ਘੱਟੋ-ਘੱਟ {numPoints} ਨਕਸ਼ੇ ਦੇ ਬਿੰਦੂਆਂ ਦੀ ਲੋੜ ਹੈ।", "app.components.CustomFieldsForm.notPublic1": "*ਇਹ ਜਵਾਬ ਸਿਰਫ਼ ਪ੍ਰੋਜੈਕਟ ਮੈਨੇਜਰਾਂ ਨਾਲ ਸਾਂਝਾ ਕੀਤਾ ਜਾਵੇਗਾ, ਜਨਤਾ ਨਾਲ ਨਹੀਂ।", "app.components.CustomFieldsForm.otherArea": "ਕਿਤੇ ਹੋਰ", "app.components.CustomFieldsForm.progressBarLabel": "ਤਰੱਕੀ", "app.components.CustomFieldsForm.removeAnswer": "ਜਵਾਬ ਹਟਾਓ", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*ਜਿੰਨੇ ਮਰਜ਼ੀ ਚੁਣੋ", "app.components.CustomFieldsForm.selectBetween": "* {minItems} ਅਤੇ {maxItems} ਵਿਕਲਪਾਂ ਵਿੱਚੋਂ ਚੁਣੋ", "app.components.CustomFieldsForm.selectExactly2": "*ਬਿਲਕੁਲ ਚੁਣੋ {selectExactly, plural, one {# ਵਿਕਲਪ} other {# ਵਿਕਲਪ}}", "app.components.CustomFieldsForm.selectMany": "*ਜਿੰਨੇ ਮਰਜ਼ੀ ਚੁਣੋ", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "ਖਿੱਚਣ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਟੈਪ ਕਰੋ। ਫਿਰ, ਬਿੰਦੂਆਂ ਨੂੰ ਹਿਲਾਉਣ ਲਈ ਉਹਨਾਂ 'ਤੇ ਘਸੀਟੋ।", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "ਖਿੱਚਣ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਟੈਪ ਕਰੋ।", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "ਆਪਣਾ ਜਵਾਬ ਜੋੜਨ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਟੈਪ ਕਰੋ।", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "ਆਪਣਾ ਜਵਾਬ ਜੋੜਨ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਟੈਪ ਕਰੋ ਜਾਂ ਹੇਠਾਂ ਇੱਕ ਪਤਾ ਟਾਈਪ ਕਰੋ।", "app.components.CustomFieldsForm.tapToAddALine": "ਲਾਈਨ ਜੋੜਨ ਲਈ ਟੈਪ ਕਰੋ", "app.components.CustomFieldsForm.tapToAddAPoint": "ਬਿੰਦੂ ਜੋੜਨ ਲਈ ਟੈਪ ਕਰੋ", "app.components.CustomFieldsForm.tapToAddAnArea": "ਕੋਈ ਖੇਤਰ ਜੋੜਨ ਲਈ ਟੈਪ ਕਰੋ", "app.components.CustomFieldsForm.titleMaxLength": "ਸਿਰਲੇਖ ਵੱਧ ਤੋਂ ਵੱਧ {max} ਅੱਖਰ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ।", "app.components.CustomFieldsForm.titleMinLength": "ਸਿਰਲੇਖ ਘੱਟੋ-ਘੱਟ {min} ਅੱਖਰ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ।", "app.components.CustomFieldsForm.titleRequired": "ਸਿਰਲੇਖ ਲੋੜੀਂਦਾ ਹੈ", "app.components.CustomFieldsForm.topicRequired": "ਘੱਟੋ-ਘੱਟ ਇੱਕ ਟੈਗ ਲੋੜੀਂਦਾ ਹੈ।", "app.components.CustomFieldsForm.typeYourAnswer": "ਆਪਣਾ ਜਵਾਬ ਟਾਈਪ ਕਰੋ", "app.components.CustomFieldsForm.typeYourAnswerRequired": "ਆਪਣਾ ਜਵਾਬ ਟਾਈਪ ਕਰਨਾ ਜ਼ਰੂਰੀ ਹੈ।", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* ਇੱਕ ਜਾਂ ਵੱਧ ਸ਼ੇਪਫਾਈਲਾਂ ਵਾਲੀ ਜ਼ਿਪ ਫਾਈਲ ਅਪਲੋਡ ਕਰੋ।", "app.components.CustomFieldsForm.validCordinatesTooltip2": "ਜੇਕਰ ਤੁਹਾਡੇ ਟਾਈਪ ਕਰਦੇ ਸਮੇਂ ਵਿਕਲਪਾਂ ਵਿੱਚ ਸਥਾਨ ਪ੍ਰਦਰਸ਼ਿਤ ਨਹੀਂ ਹੁੰਦਾ ਹੈ, ਤਾਂ ਤੁਸੀਂ ਇੱਕ ਸਟੀਕ ਸਥਾਨ (ਜਿਵੇਂ: -33.019808, -71.495676) ਨਿਰਧਾਰਤ ਕਰਨ ਲਈ 'ਅਕਸ਼ਾਂਸ਼, ਰੇਖਾਂਸ਼' ਫਾਰਮੈਟ ਵਿੱਚ ਵੈਧ ਨਿਰਦੇਸ਼ਾਂਕ ਜੋੜ ਸਕਦੇ ਹੋ।", "app.components.ErrorBoundary.errorFormErrorFormEntry": "ਕੁਝ ਖੇਤਰ ਅਵੈਧ ਸਨ। ਕਿਰਪਾ ਕਰਕੇ ਗਲਤੀਆਂ ਨੂੰ ਠੀਕ ਕਰੋ ਅਤੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.components.ErrorBoundary.errorFormErrorGeneric": "ਤੁਹਾਡੀ ਰਿਪੋਰਟ ਸਪੁਰਦ ਕਰਨ ਦੌਰਾਨ ਇੱਕ ਅਗਿਆਤ ਤਰੁੱਟੀ ਆਈ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.components.ErrorBoundary.errorFormLabelClose": "ਬੰਦ ਕਰੋ", "app.components.ErrorBoundary.errorFormLabelComments": "ਕੀ ਹੋਇਆ?", "app.components.ErrorBoundary.errorFormLabelEmail": "ਈਮੇਲ", "app.components.ErrorBoundary.errorFormLabelName": "ਨਾਮ", "app.components.ErrorBoundary.errorFormLabelSubmit": "ਜਮ੍ਹਾਂ ਕਰੋ", "app.components.ErrorBoundary.errorFormSubtitle": "ਸਾਡੀ ਟੀਮ ਨੂੰ ਸੂਚਿਤ ਕੀਤਾ ਗਿਆ ਹੈ।", "app.components.ErrorBoundary.errorFormSubtitle2": "ਜੇਕਰ ਤੁਸੀਂ ਸਾਡੀ ਮਦਦ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ, ਤਾਂ ਸਾਨੂੰ ਹੇਠਾਂ ਦੱਸੋ ਕਿ ਕੀ ਹੋਇਆ।", "app.components.ErrorBoundary.errorFormSuccessMessage": "ਤੁਹਾਡਾ ਫੀਡਬੈਕ ਭੇਜਿਆ ਗਿਆ ਹੈ। ਤੁਹਾਡਾ ਧੰਨਵਾਦ!", "app.components.ErrorBoundary.errorFormTitle": "ਅਜਿਹਾ ਲਗਦਾ ਹੈ ਕਿ ਕੋਈ ਸਮੱਸਿਆ ਹੈ।", "app.components.ErrorBoundary.genericErrorWithForm": "ਇੱਕ ਗਲਤੀ ਆਈ ਹੈ ਅਤੇ ਅਸੀਂ ਇਸ ਸਮੱਗਰੀ ਨੂੰ ਪ੍ਰਦਰਸ਼ਿਤ ਨਹੀਂ ਕਰ ਸਕਦੇ ਹਾਂ। ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ, ਜਾਂ {openForm}", "app.components.ErrorBoundary.openFormText": "ਇਸਦਾ ਪਤਾ ਲਗਾਉਣ ਵਿੱਚ ਸਾਡੀ ਮਦਦ ਕਰੋ", "app.components.ErrorToast.budgetExceededError": "ਤੁਹਾਡੇ ਕੋਲ ਲੋੜੀਂਦਾ ਬਜਟ ਨਹੀਂ ਹੈ", "app.components.ErrorToast.votesExceededError": "ਤੁਹਾਡੇ ਕੋਲ ਲੋੜੀਂਦੀਆਂ ਵੋਟਾਂ ਨਹੀਂ ਬਚੀਆਂ ਹਨ", "app.components.EventAttendanceButton.forwardToFriend": "ਇੱਕ ਦੋਸਤ ਨੂੰ ਅੱਗੇ", "app.components.EventAttendanceButton.maxRegistrationsReached": "ਇਵੈਂਟ ਰਜਿਸਟ੍ਰੇਸ਼ਨਾਂ ਦੀ ਵੱਧ ਤੋਂ ਵੱਧ ਗਿਣਤੀ ਪੂਰੀ ਹੋ ਗਈ ਹੈ। ਕੋਈ ਵੀ ਜਗ੍ਹਾ ਨਹੀਂ ਬਚੀ ਹੈ।", "app.components.EventAttendanceButton.register": "ਰਜਿਸਟਰ", "app.components.EventAttendanceButton.registered": "ਰਜਿਸਟਰਡ", "app.components.EventAttendanceButton.seeYouThere": "ਉੱਥੇ ਮਿਲਦੇ ਹਾਂ!", "app.components.EventAttendanceButton.seeYouThereName": "ਉੱਥੇ ਮਿਲਦੇ ਹਾਂ, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "ਘੱਟ ਘਟਨਾ ਦੀ ਜਾਣਕਾਰੀ ਦਿਖਾਈ ਦੇਣ ਲੱਗੀ।", "app.components.EventCard.a11y_moreContentVisible": "ਹੋਰ ਘਟਨਾ ਦੀ ਜਾਣਕਾਰੀ ਦਿਸਦੀ ਹੋ ਗਈ.", "app.components.EventCard.a11y_readMore": "\"{eventTitle}\" ਘਟਨਾ ਬਾਰੇ ਹੋਰ ਪੜ੍ਹੋ।", "app.components.EventCard.endsAt": "'ਤੇ ਸਮਾਪਤ ਹੁੰਦਾ ਹੈ", "app.components.EventCard.readMore": "ਹੋਰ ਪੜ੍ਹੋ", "app.components.EventCard.showLess": "ਘੱਟ ਦਿਖਾਓ", "app.components.EventCard.showMore": "ਹੋਰ ਦਿਖਾਓ", "app.components.EventCard.startsAt": "'ਤੇ ਸ਼ੁਰੂ ਹੁੰਦਾ ਹੈ", "app.components.EventPreviews.eventPreviewContinuousTitle2": "ਇਸ ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਆਉਣ ਵਾਲੀਆਂ ਅਤੇ ਚੱਲ ਰਹੀਆਂ ਘਟਨਾਵਾਂ", "app.components.EventPreviews.eventPreviewTimelineTitle3": "ਇਸ ਪੜਾਅ ਵਿੱਚ ਆਉਣ ਵਾਲੀਆਂ ਅਤੇ ਚੱਲ ਰਹੀਆਂ ਘਟਨਾਵਾਂ", "app.components.FileUploader.a11y_file": "ਫਾਈਲ:", "app.components.FileUploader.a11y_filesToBeUploaded": "ਅੱਪਲੋਡ ਕੀਤੀਆਂ ਜਾਣ ਵਾਲੀਆਂ ਫ਼ਾਈਲਾਂ: {fileNames}", "app.components.FileUploader.a11y_noFiles": "ਕੋਈ ਫ਼ਾਈਲਾਂ ਸ਼ਾਮਲ ਨਹੀਂ ਕੀਤੀਆਂ ਗਈਆਂ।", "app.components.FileUploader.a11y_removeFile": "ਇਸ ਫਾਈਲ ਨੂੰ ਹਟਾਓ", "app.components.FileUploader.fileInputDescription": "ਇੱਕ ਫਾਈਲ ਚੁਣਨ ਲਈ ਕਲਿੱਕ ਕਰੋ", "app.components.FileUploader.fileUploadLabel": "ਅਟੈਚਮੈਂਟ (ਅਧਿਕਤਮ 50MB)", "app.components.FileUploader.file_too_large2": "{maxSizeMb}MB ਤੋਂ ਵੱਡੀਆਂ ਫਾਈਲਾਂ ਦੀ ਇਜਾਜ਼ਤ ਨਹੀਂ ਹੈ।", "app.components.FileUploader.incorrect_extension": "{fileName} ਸਾਡੇ ਸਿਸਟਮ ਦੁਆਰਾ ਸਮਰਥਿਤ ਨਹੀਂ ਹੈ, ਇਸਨੂੰ ਅਪਲੋਡ ਨਹੀਂ ਕੀਤਾ ਜਾਵੇਗਾ।", "app.components.FilterBoxes.a11y_allFilterSelected": "ਚੁਣਿਆ ਗਿਆ ਸਥਿਤੀ ਫਿਲਟਰ: ਸਾਰੇ", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# ਅਧੀਨਗੀ} other {# ਬੇਨਤੀਆਂ}}", "app.components.FilterBoxes.a11y_removeFilter": "ਫਿਲਟਰ ਹਟਾਓ", "app.components.FilterBoxes.a11y_selectedFilter": "ਚੁਣਿਆ ਗਿਆ ਸਥਿਤੀ ਫਿਲਟਰ: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "ਚੁਣੇ ਗਏ {numberOfSelectedTopics, plural, =0 {ਜ਼ੀਰੋ ਟੈਗ ਫਿਲਟਰ} one {ਇੱਕ ਟੈਗ ਫਿਲਟਰ} other {# ਟੈਗ ਫਿਲਟਰ}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "ਸਾਰੇ", "app.components.FilterBoxes.areas": "ਖੇਤਰ ਦੁਆਰਾ ਫਿਲਟਰ ਕਰੋ", "app.components.FilterBoxes.inputs": "ਇਨਪੁੱਟ", "app.components.FilterBoxes.noValuesFound": "ਕੋਈ ਮੁੱਲ ਉਪਲਬਧ ਨਹੀਂ ਹੈ।", "app.components.FilterBoxes.showLess": "ਘੱਟ ਦਿਖਾਓ", "app.components.FilterBoxes.showTagsWithNumber": "ਸਭ ਦਿਖਾਓ ({numberTags})", "app.components.FilterBoxes.statusTitle": "ਸਥਿਤੀ", "app.components.FilterBoxes.topicsTitle": "ਟੈਗਸ", "app.components.FiltersModal.filters": "ਫਿਲਟਰ", "app.components.FolderFolderCard.a11y_folderDescription": "ਫੋਲਡਰ ਵੇਰਵਾ:", "app.components.FolderFolderCard.a11y_folderTitle": "ਫੋਲਡਰ ਸਿਰਲੇਖ:", "app.components.FolderFolderCard.archived": "ਪੁਰਾਲੇਖ", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# ਪ੍ਰੋਜੈਕਟ} one {# ਪ੍ਰੋਜੈਕਟ} other {# ਪ੍ਰੋਜੈਕਟ}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "ਇੱਕ ਵਾਰ ਸਬਮਿਸ਼ਨ ਹੋਣ ਤੋਂ ਬਾਅਦ ਖੇਤਰ ਦੀ ਕਿਸਮ ਨੂੰ ਬਦਲਿਆ ਨਹੀਂ ਜਾ ਸਕਦਾ ਹੈ।", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "ਟਾਈਪ ਕਰੋ", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "ਆਟੋ ਸੇਵ", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "ਜਦੋਂ ਤੁਸੀਂ ਫਾਰਮ ਸੰਪਾਦਕ ਖੋਲ੍ਹਦੇ ਹੋ ਤਾਂ ਸਵੈ-ਸੰਭਾਲ ਮੂਲ ਰੂਪ ਵਿੱਚ ਸਮਰੱਥ ਹੁੰਦਾ ਹੈ। ਜਦੋਂ ਵੀ ਤੁਸੀਂ \"X\" ਬਟਨ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਫੀਲਡ ਸੈਟਿੰਗ ਪੈਨਲ ਨੂੰ ਬੰਦ ਕਰਦੇ ਹੋ, ਤਾਂ ਇਹ ਸਵੈਚਲਿਤ ਤੌਰ 'ਤੇ ਇੱਕ ਸੇਵ ਨੂੰ ਟਰਿੱਗਰ ਕਰੇਗਾ।", "app.components.GanttChart.timeRange.month": "ਮਹੀਨਾ", "app.components.GanttChart.timeRange.quarter": "ਤਿਮਾਹੀ", "app.components.GanttChart.timeRange.timeRangeMultiyear": "ਬਹੁ-ਸਾਲਾ", "app.components.GanttChart.timeRange.year": "ਸਾਲ", "app.components.GanttChart.today": "ਅੱਜ", "app.components.GoBackButton.group.edit.goBack": "ਵਾਪਸ ਜਾਓ", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "ਪਿਛਲੇ ਪੰਨੇ 'ਤੇ ਵਾਪਸ ਜਾਓ", "app.components.HookForm.Feedback.errorTitle": "ਕੋਈ ਸਮੱਸਿਆ ਹੈ", "app.components.HookForm.Feedback.submissionError": "ਫਿਰ ਕੋਸ਼ਿਸ਼ ਕਰੋ. ਜੇਕਰ ਸਮੱਸਿਆ ਬਣੀ ਰਹਿੰਦੀ ਹੈ, ਤਾਂ ਸਾਡੇ ਨਾਲ ਸੰਪਰਕ ਕਰੋ", "app.components.HookForm.Feedback.submissionErrorTitle": "ਸਾਡੇ ਪਾਸੇ ਕੋਈ ਸਮੱਸਿਆ ਸੀ, ਮਾਫ਼ ਕਰਨਾ", "app.components.HookForm.Feedback.successMessage": "ਫਾਰਮ ਸਫਲਤਾਪੂਰਵਕ ਸਪੁਰਦ ਕੀਤਾ ਗਿਆ", "app.components.HookForm.PasswordInput.passwordLabel": "ਪਾਸਵਰਡ", "app.components.HorizontalScroll.scrollLeftLabel": "ਖੱਬੇ ਪਾਸੇ ਸਕ੍ਰੋਲ ਕਰੋ।", "app.components.HorizontalScroll.scrollRightLabel": "ਸੱਜੇ ਸਕ੍ਰੋਲ ਕਰੋ।", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} ਵਿਚਾਰ ਲੋਡ ਹੋ ਗਏ ਹਨ।", "app.components.IdeaCards.filters": "ਫਿਲਟਰ", "app.components.IdeaCards.filters.mostDiscussed": "ਸਭ ਤੋਂ ਵੱਧ ਚਰਚਾ ਕੀਤੀ", "app.components.IdeaCards.filters.newest": "ਨਵਾਂ", "app.components.IdeaCards.filters.oldest": "ਪੁਰਾਣਾ", "app.components.IdeaCards.filters.popular": "ਸਭ ਤੋਂ ਵੱਧ ਪਸੰਦ ਕੀਤਾ", "app.components.IdeaCards.filters.random": "ਬੇਤਰਤੀਬ", "app.components.IdeaCards.filters.sortBy": "ਦੇ ਨਾਲ ਕ੍ਰਮਬੱਧ", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "ਛਾਂਟੀ ਨੂੰ ਇਸ ਵਿੱਚ ਬਦਲਿਆ ਗਿਆ: {currentSortType}", "app.components.IdeaCards.filters.trending": "ਪ੍ਰਚਲਿਤ", "app.components.IdeaCards.showMore": "ਹੋਰ ਦਿਖਾਓ", "app.components.IdeasMap.a11y_hideIdeaCard": "ਆਈਡੀਆ ਕਾਰਡ ਲੁਕਾਓ।", "app.components.IdeasMap.a11y_mapTitle": "ਨਕਸ਼ਾ ਸੰਖੇਪ ਜਾਣਕਾਰੀ", "app.components.IdeasMap.clickOnMapToAdd": "ਆਪਣਾ ਇਨਪੁਟ ਜੋੜਨ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਕਲਿੱਕ ਕਰੋ", "app.components.IdeasMap.clickOnMapToAddAdmin2": "ਇੱਕ ਪ੍ਰਸ਼ਾਸਕ ਵਜੋਂ, ਤੁਸੀਂ ਆਪਣਾ ਇਨਪੁਟ ਜੋੜਨ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਕਲਿੱਕ ਕਰ ਸਕਦੇ ਹੋ, ਭਾਵੇਂ ਇਹ ਪੜਾਅ ਕਿਰਿਆਸ਼ੀਲ ਨਾ ਹੋਵੇ।", "app.components.IdeasMap.filters": "ਫਿਲਟਰ", "app.components.IdeasMap.multipleInputsAtLocation": "ਇਸ ਟਿਕਾਣੇ 'ਤੇ ਕਈ ਇਨਪੁਟਸ", "app.components.IdeasMap.noFilteredResults": "ਤੁਹਾਡੇ ਦੁਆਰਾ ਚੁਣੇ ਗਏ ਫਿਲਟਰਾਂ ਨੇ ਕੋਈ ਨਤੀਜਾ ਨਹੀਂ ਦਿੱਤਾ", "app.components.IdeasMap.noResults": "ਕੋਈ ਨਤੀਜੇ ਨਹੀਂ ਮਿਲੇ", "app.components.IdeasMap.or": "ਜਾਂ", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, ਕੋਈ ਨਾਪਸੰਦ ਨਹੀਂ।} one {1 ਨਾਪਸੰਦ।} other {, # ਨਾਪਸੰਦ।}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, ਕੋਈ ਪਸੰਦ ਨਹੀਂ।} one {, 1 ਪਸੰਦ।} other {, # ਪਸੰਦ।}}", "app.components.IdeasMap.signInLinkText": "ਲਾਗਿਨ", "app.components.IdeasMap.signUpLinkText": "ਸਾਇਨ ਅਪ", "app.components.IdeasMap.submitIdea2": "ਇਨਪੁਟ ਸਪੁਰਦ ਕਰੋ", "app.components.IdeasMap.tapOnMapToAdd": "ਆਪਣਾ ਇਨਪੁਟ ਜੋੜਨ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਟੈਪ ਕਰੋ", "app.components.IdeasMap.tapOnMapToAddAdmin2": "ਇੱਕ ਪ੍ਰਸ਼ਾਸਕ ਵਜੋਂ, ਤੁਸੀਂ ਆਪਣਾ ਇਨਪੁਟ ਜੋੜਨ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਟੈਪ ਕਰ ਸਕਦੇ ਹੋ, ਭਾਵੇਂ ਇਹ ਪੜਾਅ ਕਿਰਿਆਸ਼ੀਲ ਨਾ ਹੋਵੇ।", "app.components.IdeasMap.userInputs2": "ਭਾਗੀਦਾਰਾਂ ਤੋਂ ਇਨਪੁਟਸ", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, ਕੋਈ ਟਿੱਪਣੀ ਨਹੀਂ} one {, 1 ਟਿੱਪਣੀ} other {, # ਟਿੱਪਣੀਆਂ}}", "app.components.IdeasShow.bodyTitle": "ਵਰਣਨ", "app.components.IdeasShow.deletePost": "ਮਿਟਾਓ", "app.components.IdeasShow.editPost": "ਸੰਪਾਦਿਤ ਕਰੋ", "app.components.IdeasShow.goBack": "ਵਾਪਸ ਜਾਓ", "app.components.IdeasShow.moreOptions": "ਹੋਰ ਵਿਕਲਪ", "app.components.IdeasShow.or": "ਜਾਂ", "app.components.IdeasShow.proposedBudgetTitle": "ਪ੍ਰਸਤਾਵਿਤ ਬਜਟ", "app.components.IdeasShow.reportAsSpam": "ਸਪੈਮ ਵਜੋਂ ਰਿਪੋਰਟ ਕਰੋ", "app.components.IdeasShow.send": "ਭੇਜੋ", "app.components.IdeasShow.skipSharing": "ਇਸਨੂੰ ਛੱਡੋ, ਮੈਂ ਇਸਨੂੰ ਬਾਅਦ ਵਿੱਚ ਕਰਾਂਗਾ", "app.components.IdeasShowPage.signIn2": "ਲਾਗਿਨ", "app.components.IdeasShowPage.sorryNoAccess": "ਮਾਫ਼ ਕਰਨਾ, ਤੁਸੀਂ ਇਸ ਪੰਨੇ ਤੱਕ ਨਹੀਂ ਪਹੁੰਚ ਸਕਦੇ। ਇਸ ਨੂੰ ਐਕਸੈਸ ਕਰਨ ਲਈ ਤੁਹਾਨੂੰ ਲੌਗ ਇਨ ਜਾਂ ਸਾਈਨ ਅੱਪ ਕਰਨ ਦੀ ਲੋੜ ਹੋ ਸਕਦੀ ਹੈ।", "app.components.LocationInput.noOptions": "ਕੋਈ ਵਿਕਲਪ ਨਹੀਂ", "app.components.Modal.closeWindow": "ਵਿੰਡੋ ਬੰਦ ਕਰੋ", "app.components.MultiSelect.clearButtonAction": "ਚੋਣ ਸਾਫ਼ ਕਰੋ", "app.components.MultiSelect.clearSearchButtonAction": "ਖੋਜ ਸਾਫ਼ ਕਰੋ", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "ਇੱਕ ਨਵਾਂ ਈਮੇਲ ਪਤਾ ਦਾਖਲ ਕਰੋ", "app.components.PageNotFound.goBackToHomePage": "ਹੋਮਪੇਜ 'ਤੇ ਵਾਪਸ ਜਾਓ", "app.components.PageNotFound.notFoundTitle": "ਪੰਨਾ ਨਹੀਂ ਮਿਲਿਆ", "app.components.PageNotFound.pageNotFoundDescription": "ਬੇਨਤੀ ਕੀਤਾ ਪੰਨਾ ਲੱਭਿਆ ਨਹੀਂ ਜਾ ਸਕਿਆ ਹੈ।", "app.components.PagesForm.descriptionMissingOneLanguageError": "ਘੱਟੋ-ਘੱਟ ਇੱਕ ਭਾਸ਼ਾ ਲਈ ਸਮੱਗਰੀ ਪ੍ਰਦਾਨ ਕਰੋ", "app.components.PagesForm.editContent": "ਸਮੱਗਰੀ", "app.components.PagesForm.fileUploadLabel": "ਅਟੈਚਮੈਂਟ (ਅਧਿਕਤਮ 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "ਫ਼ਾਈਲਾਂ 50Mb ਤੋਂ ਵੱਡੀਆਂ ਨਹੀਂ ਹੋਣੀਆਂ ਚਾਹੀਦੀਆਂ। ਜੋੜੀਆਂ ਗਈਆਂ ਫਾਈਲਾਂ ਇਸ ਪੰਨੇ ਦੇ ਹੇਠਾਂ ਦਿਖਾਈਆਂ ਜਾਣਗੀਆਂ।", "app.components.PagesForm.navbarItemTitle": "ਨਵਬਾਰ ਵਿੱਚ ਨਾਮ", "app.components.PagesForm.pageTitle": "ਸਿਰਲੇਖ", "app.components.PagesForm.savePage": "ਪੰਨਾ ਸੁਰੱਖਿਅਤ ਕਰੋ", "app.components.PagesForm.saveSuccess": "ਪੰਨਾ ਸਫਲਤਾਪੂਰਵਕ ਸੁਰੱਖਿਅਤ ਕੀਤਾ ਗਿਆ।", "app.components.PagesForm.titleMissingOneLanguageError": "ਘੱਟੋ-ਘੱਟ ਇੱਕ ਭਾਸ਼ਾ ਲਈ ਸਿਰਲੇਖ ਪ੍ਰਦਾਨ ਕਰੋ", "app.components.Pagination.back": "ਪਿਛਲਾ ਪੰਨਾ", "app.components.Pagination.next": "ਅਗਲਾ ਪੰਨਾ", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "ਤੁਸੀਂ {votesCast}ਖਰਚ ਕੀਤਾ, ਜੋ {votesLimit}ਦੀ ਸੀਮਾ ਤੋਂ ਵੱਧ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੀ ਟੋਕਰੀ ਵਿੱਚੋਂ ਕੁਝ ਆਈਟਮਾਂ ਹਟਾਓ ਅਤੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} ਖੱਬੇ", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "ਆਪਣੀ ਟੋਕਰੀ ਜਮ੍ਹਾਂ ਕਰਾਉਣ ਤੋਂ ਪਹਿਲਾਂ ਤੁਹਾਨੂੰ ਘੱਟੋ-ਘੱਟ {votesMinimum} ਖਰਚ ਕਰਨ ਦੀ ਲੋੜ ਹੈ।", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "ਸਪੁਰਦ ਕਰਨ ਤੋਂ ਪਹਿਲਾਂ ਤੁਹਾਨੂੰ ਘੱਟੋ-ਘੱਟ ਇੱਕ ਵਿਕਲਪ ਚੁਣਨ ਦੀ ਲੋੜ ਹੈ।", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "ਇਸ ਤੋਂ ਪਹਿਲਾਂ ਕਿ ਤੁਸੀਂ ਇਸਨੂੰ ਜਮ੍ਹਾਂ ਕਰ ਸਕੋ, ਤੁਹਾਨੂੰ ਆਪਣੀ ਟੋਕਰੀ ਵਿੱਚ ਕੁਝ ਜੋੜਨ ਦੀ ਲੋੜ ਹੈ।", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {ਕੋਈ ਕ੍ਰੈਡਿਟ ਨਹੀਂ ਬਚਿਆ} other {# {totalNumberOfVotes, plural, one {ਵਿੱਚੋਂ 1 ਕ੍ਰੈਡਿਟ} other {# ਕ੍ਰੈਡਿਟ}} ਬਾਕੀ}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {ਕੋਈ ਅੰਕ ਨਹੀਂ ਬਚੇ} other {# {totalNumberOfVotes, plural, one {ਵਿੱਚੋਂ 1 ਅੰਕ} other {# ਅੰਕ}} ਬਾਕੀ}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {ਕੋਈ ਟੋਕਨ ਨਹੀਂ ਬਚਿਆ} other {# {totalNumberOfVotes, plural, one {ਵਿੱਚੋਂ 1 ਟੋਕਨ} other {# ਟੋਕਨ}} ਬਾਕੀ}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {ਕੋਈ ਵੋਟ ਨਹੀਂ ਬਚੀ} other {# {totalNumberOfVotes, plural, one {ਵਿੱਚੋਂ 1 ਵੋਟ} other {# ਵੋਟਾਂ}} ਬਾਕੀ}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# ਵੋਟਾਂ} one {# ਵੋਟ} other {# ਵੋਟਾਂ}} ਕਾਸਟ", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "ਤੁਸੀਂ {votesCast} ਵੋਟਾਂ ਪਾਈਆਂ, ਜੋ {votesLimit}ਦੀ ਸੀਮਾ ਤੋਂ ਵੱਧ ਗਈਆਂ। ਕਿਰਪਾ ਕਰਕੇ ਕੁਝ ਵੋਟਾਂ ਹਟਾਓ ਅਤੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.components.ParticipationCTABars.addInput": "ਇਨਪੁੱਟ ਸ਼ਾਮਲ ਕਰੋ", "app.components.ParticipationCTABars.allocateBudget": "ਆਪਣਾ ਬਜਟ ਨਿਰਧਾਰਤ ਕਰੋ", "app.components.ParticipationCTABars.budgetSubmitSuccess": "ਤੁਹਾਡਾ ਬਜਟ ਸਫਲਤਾਪੂਰਵਕ ਜਮ੍ਹਾਂ ਹੋ ਗਿਆ ਹੈ।", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "ਭਾਗੀਦਾਰੀ ਲਈ ਖੁੱਲ੍ਹਾ ਹੈ", "app.components.ParticipationCTABars.poll": "ਪੋਲ ਲਓ", "app.components.ParticipationCTABars.reviewDocument": "ਦਸਤਾਵੇਜ਼ ਦੀ ਸਮੀਖਿਆ ਕਰੋ", "app.components.ParticipationCTABars.seeContributions": "ਯੋਗਦਾਨ ਦੇਖੋ", "app.components.ParticipationCTABars.seeEvents3": "ਘਟਨਾਵਾਂ ਵੇਖੋ", "app.components.ParticipationCTABars.seeIdeas": "ਵਿਚਾਰ ਦੇਖੋ", "app.components.ParticipationCTABars.seeInitiatives": "ਪਹਿਲਕਦਮੀਆਂ ਦੇਖੋ", "app.components.ParticipationCTABars.seeIssues": "ਮੁੱਦੇ ਵੇਖੋ", "app.components.ParticipationCTABars.seeOptions": "ਵਿਕਲਪ ਦੇਖੋ", "app.components.ParticipationCTABars.seePetitions": "ਪਟੀਸ਼ਨਾਂ ਦੇਖੋ", "app.components.ParticipationCTABars.seeProjects": "ਪ੍ਰੋਜੈਕਟ ਵੇਖੋ", "app.components.ParticipationCTABars.seeProposals": "ਪ੍ਰਸਤਾਵ ਦੇਖੋ", "app.components.ParticipationCTABars.seeQuestions": "ਸਵਾਲ ਦੇਖੋ", "app.components.ParticipationCTABars.submit": "ਜਮ੍ਹਾਂ ਕਰੋ", "app.components.ParticipationCTABars.takeTheSurvey": "ਸਰਵੇਖਣ ਲਵੋ", "app.components.ParticipationCTABars.userHasParticipated": "ਤੁਸੀਂ ਇਸ ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਹਿੱਸਾ ਲਿਆ ਹੈ।", "app.components.ParticipationCTABars.viewInputs": "ਇਨਪੁੱਟ ਵੇਖੋ", "app.components.ParticipationCTABars.volunteer": "ਵਲੰਟੀਅਰ", "app.components.ParticipationCTABars.votesCounter.vote": "ਵੋਟ", "app.components.ParticipationCTABars.votesCounter.votes": "ਵੋਟਾਂ", "app.components.PasswordInput.a11y_passwordHidden": "ਪਾਸਵਰਡ ਲੁਕਾਇਆ ਗਿਆ", "app.components.PasswordInput.a11y_passwordVisible": "ਪਾਸਵਰਡ ਦਿਸਦਾ ਹੈ", "app.components.PasswordInput.a11y_strength1Password": "ਮਾੜੀ ਪਾਸਵਰਡ ਤਾਕਤ", "app.components.PasswordInput.a11y_strength2Password": "ਕਮਜ਼ੋਰ ਪਾਸਵਰਡ ਤਾਕਤ", "app.components.PasswordInput.a11y_strength3Password": "ਦਰਮਿਆਨੀ ਪਾਸਵਰਡ ਤਾਕਤ", "app.components.PasswordInput.a11y_strength4Password": "ਮਜ਼ਬੂਤ ਪਾਸਵਰਡ ਤਾਕਤ", "app.components.PasswordInput.a11y_strength5Password": "ਬਹੁਤ ਮਜ਼ਬੂਤ ਪਾਸਵਰਡ ਤਾਕਤ", "app.components.PasswordInput.hidePassword": "ਪਾਸਵਰਡ ਲੁਕਾਓ", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "ਬਹੁਤ ਛੋਟਾ (ਘੱਟੋ-ਘੱਟ {minimumPasswordLength} ਅੱਖਰ)", "app.components.PasswordInput.minimumPasswordLengthError": "ਇੱਕ ਪਾਸਵਰਡ ਪ੍ਰਦਾਨ ਕਰੋ ਜੋ ਘੱਟੋ-ਘੱਟ {minimumPasswordLength} ਅੱਖਰਾਂ ਦਾ ਹੋਵੇ", "app.components.PasswordInput.passwordEmptyError": "ਆਪਣਾ ਪਾਸਵਰਡ ਦਰਜ ਕਰੋ", "app.components.PasswordInput.passwordStrengthTooltip1": "ਆਪਣੇ ਪਾਸਵਰਡ ਨੂੰ ਮਜ਼ਬੂਤ ਬਣਾਉਣ ਲਈ:", "app.components.PasswordInput.passwordStrengthTooltip2": "ਗੈਰ-ਲਗਾਤਾਰ ਛੋਟੇ ਅੱਖਰਾਂ, ਵੱਡੇ ਅੱਖਰਾਂ, ਅੰਕਾਂ, ਵਿਸ਼ੇਸ਼ ਅੱਖਰਾਂ ਅਤੇ ਵਿਰਾਮ ਚਿੰਨ੍ਹਾਂ ਦੇ ਸੁਮੇਲ ਦੀ ਵਰਤੋਂ ਕਰੋ", "app.components.PasswordInput.passwordStrengthTooltip3": "ਆਮ ਜਾਂ ਆਸਾਨੀ ਨਾਲ ਅੰਦਾਜ਼ਾ ਲਗਾਉਣ ਵਾਲੇ ਸ਼ਬਦਾਂ ਤੋਂ ਬਚੋ", "app.components.PasswordInput.passwordStrengthTooltip4": "ਲੰਬਾਈ ਵਧਾਓ", "app.components.PasswordInput.showPassword": "ਪਾਸਵਰਡ ਦਿਖਾਓ", "app.components.PasswordInput.strength1Password": "ਗਰੀਬ", "app.components.PasswordInput.strength2Password": "ਕਮਜ਼ੋਰ", "app.components.PasswordInput.strength3Password": "ਦਰਮਿਆਨਾ", "app.components.PasswordInput.strength4Password": "ਮਜ਼ਬੂਤ", "app.components.PasswordInput.strength5Password": "ਬਹੁਤ ਮਜ਼ਬੂਤ", "app.components.PostCardsComponents.list": "ਸੂਚੀ", "app.components.PostCardsComponents.map": "ਨਕਸ਼ਾ", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "ਇੱਕ ਅਧਿਕਾਰਤ ਅੱਪਡੇਟ ਸ਼ਾਮਲ ਕਰੋ", "app.components.PostComponents.OfficialFeedback.cancel": "ਰੱਦ ਕਰੋ", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "ਮਿਟਾਓ", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਤੌਰ 'ਤੇ ਇਸ ਅਧਿਕਾਰਤ ਅੱਪਡੇਟ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "ਸੰਪਾਦਿਤ ਕਰੋ", "app.components.PostComponents.OfficialFeedback.lastEdition": "ਆਖਰੀ ਵਾਰ {date}ਨੂੰ ਸੰਪਾਦਿਤ ਕੀਤਾ ਗਿਆ", "app.components.PostComponents.OfficialFeedback.lastUpdate": "ਆਖਰੀ ਅੱਪਡੇਟ: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "ਅਧਿਕਾਰੀ", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "ਚੁਣੋ ਕਿ ਲੋਕ ਤੁਹਾਡਾ ਨਾਮ ਕਿਵੇਂ ਦੇਖਦੇ ਹਨ", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "ਅਧਿਕਾਰਤ ਅਪਡੇਟ ਲੇਖਕ ਦਾ ਨਾਮ", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "ਅਧਿਕਾਰਤ ਅੱਪਡੇਟ ਬਾਡੀ ਟੈਕਸਟ", "app.components.PostComponents.OfficialFeedback.officialUpdates": "ਅਧਿਕਾਰਤ ਅੱਪਡੇਟ", "app.components.PostComponents.OfficialFeedback.postedOn": "{date}'ਤੇ ਪੋਸਟ ਕੀਤਾ ਗਿਆ", "app.components.PostComponents.OfficialFeedback.publishButtonText": "ਪ੍ਰਕਾਸ਼ਿਤ ਕਰੋ", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "ਪਿਛਲੇ ਅੱਪਡੇਟ ਦਿਖਾਓ", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "ਅਪਡੇਟ ਦਿਓ...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "ਮਾਫ਼ ਕਰਨਾ, ਇੱਕ ਸਮੱਸਿਆ ਸੀ", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "ਸੁਨੇਹਾ ਅੱਪਡੇਟ ਕਰੋ", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "ਤੁਹਾਡਾ ਅੱਪਡੇਟ ਸਫਲਤਾਪੂਰਵਕ ਪ੍ਰਕਾਸ਼ਿਤ ਕੀਤਾ ਗਿਆ ਸੀ!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "{postUrl}'ਤੇ ਮੇਰੇ ਯੋਗਦਾਨ '{postTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "ਮੇਰੇ ਯੋਗਦਾਨ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "ਮੇਰੇ ਯੋਗਦਾਨ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "{postUrl}'ਤੇ ਮੇਰੇ ਵਿਚਾਰ '{postTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "ਮੇਰੇ ਵਿਚਾਰ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "ਮੇਰੇ ਵਿਚਾਰ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "ਤੁਸੀਂ ਇਸ ਪ੍ਰਸਤਾਵ ਬਾਰੇ ਕੀ ਸੋਚਦੇ ਹੋ? ਇਸ 'ਤੇ ਵੋਟ ਦਿਓ ਅਤੇ ਆਪਣੀ ਆਵਾਜ਼ ਸੁਣਨ ਲਈ {postUrl} 'ਤੇ ਚਰਚਾ ਨੂੰ ਸਾਂਝਾ ਕਰੋ!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "ਮੇਰੇ ਪ੍ਰਸਤਾਵ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "ਮੇਰੀ ਪਹਿਲ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "ਮੈਂ {postUrl}'ਤੇ '{postTitle}' ਟਿੱਪਣੀ ਪੋਸਟ ਕੀਤੀ!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "ਮੈਂ ਹੁਣੇ ਇੱਕ ਟਿੱਪਣੀ ਪੋਸਟ ਕੀਤੀ ਹੈ: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "ਮੈਂ ਹੁਣੇ ਇੱਕ ਟਿੱਪਣੀ ਪੋਸਟ ਕੀਤੀ ਹੈ: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "{postUrl}'ਤੇ ਮੇਰੇ ਪ੍ਰਸਤਾਵਿਤ ਵਿਕਲਪ '{postTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "ਮੇਰੇ ਪ੍ਰਸਤਾਵਿਤ ਵਿਕਲਪ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "ਮੇਰੇ ਵਿਕਲਪ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "{postUrl}'ਤੇ ਮੇਰੀ ਪਟੀਸ਼ਨ '{postTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "ਮੇਰੀ ਪਟੀਸ਼ਨ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "ਮੇਰੀ ਪਟੀਸ਼ਨ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "{postUrl}'ਤੇ ਮੇਰੇ ਪ੍ਰੋਜੈਕਟ '{postTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "ਮੇਰੇ ਪ੍ਰੋਜੈਕਟ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "ਮੇਰੇ ਪ੍ਰੋਜੈਕਟ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "{postUrl}'ਤੇ ਮੇਰੇ ਪ੍ਰਸਤਾਵ '{postTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "ਮੇਰੇ ਪ੍ਰਸਤਾਵ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "ਮੈਂ ਹੁਣੇ ਹੀ {orgName}: {postTitle}ਲਈ ਇੱਕ ਪ੍ਰਸਤਾਵ ਪੋਸਟ ਕੀਤਾ ਹੈ", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "{postUrl}'ਤੇ ਇਸ ਸਵਾਲ '{postTitle}' ਬਾਰੇ ਚਰਚਾ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "ਚਰਚਾ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "ਚਰਚਾ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "{postTitle} ਲਈ ਵੋਟ ਕਰੋ", "app.components.PostComponents.linkToHomePage": "ਹੋਮ ਪੇਜ ਨਾਲ ਲਿੰਕ ਕਰੋ", "app.components.PostComponents.readMore": "ਹੋਰ ਪੜ੍ਹੋ...", "app.components.PostComponents.topics": "ਵਿਸ਼ੇ", "app.components.ProjectArchivedIndicator.archivedProject": "ਬਦਕਿਸਮਤੀ ਨਾਲ, ਤੁਸੀਂ ਹੁਣ ਇਸ ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਹਿੱਸਾ ਨਹੀਂ ਲੈ ਸਕਦੇ ਕਿਉਂਕਿ ਇਸਨੂੰ ਪੁਰਾਲੇਖਬੱਧ ਕੀਤਾ ਗਿਆ ਹੈ", "app.components.ProjectArchivedIndicator.previewProject": "ਡਰਾਫਟ ਪ੍ਰੋਜੈਕਟ:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "ਸਿਰਫ਼ ਸੰਚਾਲਕਾਂ ਅਤੇ ਪੂਰਵਦਰਸ਼ਨ ਲਿੰਕ ਵਾਲੇ ਲੋਕਾਂ ਨੂੰ ਦਿਖਾਈ ਦਿੰਦਾ ਹੈ।", "app.components.ProjectCard.a11y_projectDescription": "ਪ੍ਰੋਜੈਕਟ ਦਾ ਵੇਰਵਾ:", "app.components.ProjectCard.a11y_projectTitle": "ਪ੍ਰੋਜੈਕਟ ਸਿਰਲੇਖ:", "app.components.ProjectCard.addYourOption": "ਆਪਣਾ ਵਿਕਲਪ ਸ਼ਾਮਲ ਕਰੋ", "app.components.ProjectCard.allocateYourBudget": "ਆਪਣਾ ਬਜਟ ਨਿਰਧਾਰਤ ਕਰੋ", "app.components.ProjectCard.archived": "ਪੁਰਾਲੇਖ", "app.components.ProjectCard.comment": "ਟਿੱਪਣੀ", "app.components.ProjectCard.contributeYourInput": "ਆਪਣਾ ਯੋਗਦਾਨ ਦਿਓ", "app.components.ProjectCard.finished": "ਸਮਾਪਤ", "app.components.ProjectCard.joinDiscussion": "ਚਰਚਾ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ", "app.components.ProjectCard.learnMore": "ਜਿਆਦਾ ਜਾਣੋ", "app.components.ProjectCard.reaction": "ਪ੍ਰਤੀਕਰਮ", "app.components.ProjectCard.readTheReport": "ਰਿਪੋਰਟ ਪੜ੍ਹੋ", "app.components.ProjectCard.reviewDocument": "ਦਸਤਾਵੇਜ਼ ਦੀ ਸਮੀਖਿਆ ਕਰੋ", "app.components.ProjectCard.submitAnIssue": "ਇੱਕ ਟਿੱਪਣੀ ਦਰਜ ਕਰੋ", "app.components.ProjectCard.submitYourIdea": "ਆਪਣਾ ਵਿਚਾਰ ਪੇਸ਼ ਕਰੋ", "app.components.ProjectCard.submitYourInitiative": "ਆਪਣੀ ਪਹਿਲ ਪੇਸ਼ ਕਰੋ", "app.components.ProjectCard.submitYourPetition": "ਆਪਣੀ ਪਟੀਸ਼ਨ ਦਰਜ ਕਰੋ", "app.components.ProjectCard.submitYourProject": "ਆਪਣਾ ਪ੍ਰੋਜੈਕਟ ਜਮ੍ਹਾਂ ਕਰੋ", "app.components.ProjectCard.submitYourProposal": "ਆਪਣਾ ਪ੍ਰਸਤਾਵ ਦਰਜ ਕਰੋ", "app.components.ProjectCard.takeThePoll": "ਪੋਲ ਲਓ", "app.components.ProjectCard.takeTheSurvey": "ਸਰਵੇਖਣ ਲਵੋ", "app.components.ProjectCard.viewTheContributions": "ਯੋਗਦਾਨਾਂ ਨੂੰ ਦੇਖੋ", "app.components.ProjectCard.viewTheIdeas": "ਵਿਚਾਰ ਵੇਖੋ", "app.components.ProjectCard.viewTheInitiatives": "ਪਹਿਲਕਦਮੀਆਂ ਦੇਖੋ", "app.components.ProjectCard.viewTheIssues": "ਟਿੱਪਣੀਆਂ ਦੇਖੋ", "app.components.ProjectCard.viewTheOptions": "ਵਿਕਲਪ ਵੇਖੋ", "app.components.ProjectCard.viewThePetitions": "ਪਟੀਸ਼ਨਾਂ ਦੇਖੋ", "app.components.ProjectCard.viewTheProjects": "ਪ੍ਰੋਜੈਕਟ ਵੇਖੋ", "app.components.ProjectCard.viewTheProposals": "ਪ੍ਰਸਤਾਵ ਵੇਖੋ", "app.components.ProjectCard.viewTheQuestions": "ਸਵਾਲ ਵੇਖੋ", "app.components.ProjectCard.vote": "ਵੋਟ ਕਰੋ", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# ਟਿੱਪਣੀਆਂ} other {# ਟਿੱਪਣੀਆਂ}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# ਯੋਗਦਾਨ} other {# ਯੋਗਦਾਨ}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {ਹਾਲੇ ਕੋਈ ਵਿਚਾਰ ਨਹੀਂ} one {# idea} other {# ideas}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {#ਪਹਿਲ} one {#ਪਹਿਲ} other {#ਪਹਿਲ}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# ਟਿੱਪਣੀ} other {# ਟਿੱਪਣੀ}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# ਵਿਕਲਪ} other {# ਵਿਕਲਪ}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# ਪਟੀਸ਼ਨਾਂ} one {# ਪਟੀਸ਼ਨ} other {# ਪਟੀਸ਼ਨਾਂ}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# ਪ੍ਰੋਜੈਕਟ} other {# ਪ੍ਰੋਜੈਕਟ}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# ਪ੍ਰਸਤਾਵ} one {# ਪ੍ਰਸਤਾਵ} other {# ਪ੍ਰਸਤਾਵ}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# ਸਵਾਲ} other {# ਸਵਾਲ}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# ਟਿੱਪਣੀਆਂ} one {# ਟਿੱਪਣੀਆਂ} other {# ਟਿੱਪਣੀਆਂ}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# ਇਨਪੁਟਸ} one {# ਇਨਪੁਟ} other {# ਇਨਪੁੱਟ}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# ਪ੍ਰੋਜੈਕਟ} one {# ਪ੍ਰੋਜੈਕਟ} other {# ਪ੍ਰੋਜੈਕਟ}}", "app.components.ProjectFolderCards.components.Topbar.all": "ਸਾਰੇ", "app.components.ProjectFolderCards.components.Topbar.archived": "ਪੁਰਾਲੇਖ", "app.components.ProjectFolderCards.components.Topbar.draft": "ਡਰਾਫਟ", "app.components.ProjectFolderCards.components.Topbar.filterBy": "ਦੁਆਰਾ ਫਿਲਟਰ ਕਰੋ", "app.components.ProjectFolderCards.components.Topbar.published2": "ਪ੍ਰਕਾਸ਼ਿਤ", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "ਟੈਗ ਕਰੋ", "app.components.ProjectFolderCards.noProjectYet": "ਫਿਲਹਾਲ ਕੋਈ ਵੀ ਓਪਨ ਪ੍ਰੋਜੈਕਟ ਨਹੀਂ ਹਨ", "app.components.ProjectFolderCards.noProjectsAvailable": "ਕੋਈ ਪ੍ਰੋਜੈਕਟ ਉਪਲਬਧ ਨਹੀਂ ਹਨ", "app.components.ProjectFolderCards.showMore": "ਹੋਰ ਦਿਖਾਓ", "app.components.ProjectFolderCards.stayTuned": "ਨਵੇਂ ਰੁਝੇਵੇਂ ਦੇ ਮੌਕਿਆਂ ਲਈ ਦੁਬਾਰਾ ਜਾਂਚ ਕਰੋ", "app.components.ProjectFolderCards.tryChangingFilters": "ਚੁਣੇ ਹੋਏ ਫਿਲਟਰਾਂ ਨੂੰ ਬਦਲਣ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.components.ProjectTemplatePreview.alsoUsedIn": "ਇਹਨਾਂ ਸ਼ਹਿਰਾਂ ਵਿੱਚ ਵੀ ਵਰਤਿਆ ਜਾਂਦਾ ਹੈ:", "app.components.ProjectTemplatePreview.copied": "ਕਾਪੀ ਕੀਤਾ", "app.components.ProjectTemplatePreview.copyLink": "ਲਿੰਕ ਕਾਪੀ ਕਰੋ", "app.components.QuillEditor.alignCenter": "ਕੇਂਦਰ ਟੈਕਸਟ", "app.components.QuillEditor.alignLeft": "ਖੱਬੇ ਪਾਸੇ ਇਕਸਾਰ ਕਰੋ", "app.components.QuillEditor.alignRight": "ਸੱਜੇ ਪਾਸੇ ਇਕਸਾਰ ਕਰੋ", "app.components.QuillEditor.bold": "ਬੋਲਡ", "app.components.QuillEditor.clean": "ਫਾਰਮੈਟਿੰਗ ਹਟਾਓ", "app.components.QuillEditor.customLink": "ਬਟਨ ਸ਼ਾਮਲ ਕਰੋ", "app.components.QuillEditor.customLinkPrompt": "ਲਿੰਕ ਦਰਜ ਕਰੋ:", "app.components.QuillEditor.edit": "ਸੰਪਾਦਿਤ ਕਰੋ", "app.components.QuillEditor.image": "ਚਿੱਤਰ ਅੱਪਲੋਡ ਕਰੋ", "app.components.QuillEditor.imageAltPlaceholder": "ਚਿੱਤਰ ਦਾ ਛੋਟਾ ਵੇਰਵਾ", "app.components.QuillEditor.italic": "ਇਟਾਲਿਕ", "app.components.QuillEditor.link": "ਲਿੰਕ ਸ਼ਾਮਲ ਕਰੋ", "app.components.QuillEditor.linkPrompt": "ਲਿੰਕ ਦਰਜ ਕਰੋ:", "app.components.QuillEditor.normalText": "ਸਧਾਰਣ", "app.components.QuillEditor.orderedList": "ਆਰਡਰ ਕੀਤੀ ਸੂਚੀ", "app.components.QuillEditor.remove": "ਹਟਾਓ", "app.components.QuillEditor.save": "ਸੇਵ ਕਰੋ", "app.components.QuillEditor.subtitle": "ਉਪਸਿਰਲੇਖ", "app.components.QuillEditor.title": "ਸਿਰਲੇਖ", "app.components.QuillEditor.unorderedList": "ਬਿਨਾਂ ਕ੍ਰਮਬੱਧ ਸੂਚੀ", "app.components.QuillEditor.video": "ਵੀਡੀਓ ਸ਼ਾਮਲ ਕਰੋ", "app.components.QuillEditor.videoPrompt": "ਵੀਡੀਓ ਦਰਜ ਕਰੋ:", "app.components.QuillEditor.visitPrompt": "ਲਿੰਕ 'ਤੇ ਜਾਓ:", "app.components.ReactionControl.completeProfileToReact": "ਪ੍ਰਤੀਕਿਰਿਆ ਕਰਨ ਲਈ ਆਪਣੀ ਪ੍ਰੋਫਾਈਲ ਨੂੰ ਪੂਰਾ ਕਰੋ", "app.components.ReactionControl.dislike": "ਨਾਪਸੰਦ", "app.components.ReactionControl.dislikingDisabledMaxReached": "ਤੁਸੀਂ {projectName}ਵਿੱਚ ਨਾਪਸੰਦਾਂ ਦੀ ਆਪਣੀ ਅਧਿਕਤਮ ਸੰਖਿਆ ਤੱਕ ਪਹੁੰਚ ਗਏ ਹੋ", "app.components.ReactionControl.like": "ਪਸੰਦ ਹੈ", "app.components.ReactionControl.likingDisabledMaxReached": "ਤੁਸੀਂ {projectName}ਵਿੱਚ ਪਸੰਦਾਂ ਦੀ ਆਪਣੀ ਅਧਿਕਤਮ ਸੰਖਿਆ 'ਤੇ ਪਹੁੰਚ ਗਏ ਹੋ", "app.components.ReactionControl.reactingDisabledFutureEnabled": "ਇਹ ਪੜਾਅ ਸ਼ੁਰੂ ਹੋਣ 'ਤੇ ਪ੍ਰਤੀਕਿਰਿਆ ਕਰਨਾ ਯੋਗ ਹੋ ਜਾਵੇਗਾ", "app.components.ReactionControl.reactingDisabledPhaseOver": "ਇਸ ਪੜਾਅ ਵਿੱਚ ਪ੍ਰਤੀਕਿਰਿਆ ਕਰਨਾ ਹੁਣ ਸੰਭਵ ਨਹੀਂ ਹੈ", "app.components.ReactionControl.reactingDisabledProjectInactive": "ਤੁਸੀਂ ਹੁਣ {projectName}ਵਿੱਚ ਵਿਚਾਰਾਂ 'ਤੇ ਪ੍ਰਤੀਕਿਰਿਆ ਨਹੀਂ ਕਰ ਸਕਦੇ", "app.components.ReactionControl.reactingNotEnabled": "ਇਸ ਪ੍ਰੋਜੈਕਟ ਲਈ ਵਰਤਮਾਨ ਵਿੱਚ ਪ੍ਰਤੀਕਿਰਿਆ ਯੋਗ ਨਹੀਂ ਹੈ", "app.components.ReactionControl.reactingNotPermitted": "ਪ੍ਰਤੀਕਿਰਿਆ ਕਰਨਾ ਸਿਰਫ਼ ਕੁਝ ਸਮੂਹਾਂ ਲਈ ਹੀ ਯੋਗ ਹੈ", "app.components.ReactionControl.reactingNotSignedIn": "ਪ੍ਰਤੀਕਿਰਿਆ ਕਰਨ ਲਈ ਸਾਈਨ ਇਨ ਕਰੋ।", "app.components.ReactionControl.reactingPossibleLater": "ਪ੍ਰਤੀਕਿਰਿਆ {enabledFromDate}ਨੂੰ ਸ਼ੁਰੂ ਹੋਵੇਗੀ", "app.components.ReactionControl.reactingVerifyToReact": "ਪ੍ਰਤੀਕਿਰਿਆ ਕਰਨ ਲਈ ਆਪਣੀ ਪਛਾਣ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ।", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "ਇਵੈਂਟ ਮਿਤੀ: {startDate} at {startTime} to {endDate} at {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "ਇਵੈਂਟ ਮਿਤੀ: {eventDate} {startTime} ਤੋਂ {endTime}ਤੱਕ।", "app.components.Sharing.linkCopied": "ਲਿੰਕ ਕਾਪੀ ਕੀਤਾ ਗਿਆ", "app.components.Sharing.or": "ਜਾਂ", "app.components.Sharing.share": "ਸ਼ੇਅਰ ਕਰੋ", "app.components.Sharing.shareByEmail": "ਈਮੇਲ ਦੁਆਰਾ ਸਾਂਝਾ ਕਰੋ", "app.components.Sharing.shareByLink": "ਲਿੰਕ ਕਾਪੀ ਕਰੋ", "app.components.Sharing.shareOnFacebook": "Facebook 'ਤੇ ਸਾਂਝਾ ਕਰੋ", "app.components.Sharing.shareOnTwitter": "ਟਵਿੱਟਰ 'ਤੇ ਸਾਂਝਾ ਕਰੋ", "app.components.Sharing.shareThisEvent": "ਇਸ ਘਟਨਾ ਨੂੰ ਸਾਂਝਾ ਕਰੋ", "app.components.Sharing.shareThisFolder": "ਸ਼ੇਅਰ ਕਰੋ", "app.components.Sharing.shareThisProject": "ਇਸ ਪ੍ਰੋਜੈਕਟ ਨੂੰ ਸਾਂਝਾ ਕਰੋ", "app.components.Sharing.shareViaMessenger": "ਮੈਸੇਂਜਰ ਰਾਹੀਂ ਸਾਂਝਾ ਕਰੋ", "app.components.Sharing.shareViaWhatsApp": "ਵਟਸਐਪ ਰਾਹੀਂ ਸਾਂਝਾ ਕਰੋ", "app.components.SideModal.closeButtonAria": "ਬੰਦ ਕਰੋ", "app.components.StatusModule.futurePhase": "ਤੁਸੀਂ ਇੱਕ ਪੜਾਅ ਦੇਖ ਰਹੇ ਹੋ ਜੋ ਅਜੇ ਸ਼ੁਰੂ ਨਹੀਂ ਹੋਇਆ ਹੈ। ਪੜਾਅ ਸ਼ੁਰੂ ਹੋਣ 'ਤੇ ਤੁਸੀਂ ਭਾਗ ਲੈਣ ਦੇ ਯੋਗ ਹੋਵੋਗੇ।", "app.components.StatusModule.modifyYourSubmission1": "ਆਪਣੀ ਸਪੁਰਦਗੀ ਨੂੰ ਸੋਧੋ", "app.components.StatusModule.submittedUntil3": "ਤੱਕ ਤੁਹਾਡੀ ਵੋਟ ਜਮ੍ਹਾਂ ਹੋ ਸਕਦੀ ਹੈ", "app.components.TopicsPicker.numberOfSelectedTopics": "ਚੁਣੇ ਗਏ {numberOfSelectedTopics, plural, =0 {ਜ਼ੀਰੋ ਟੈਗ} one {ਇੱਕ ਟੈਗ} other {# ਟੈਗ}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "ਚਿੱਤਰ ਦਾ ਵਿਸਤਾਰ ਕਰੋ", "app.components.UI.MoreActionsMenu.moreOptions": "ਹੋਰ ਵਿਕਲਪ", "app.components.UI.MoreActionsMenu.showMoreActions": "ਹੋਰ ਕਾਰਵਾਈਆਂ ਦਿਖਾਓ", "app.components.UI.NewLabel.new": "ਨਵਾਂ", "app.components.UI.PhaseFilter.noAppropriatePhases": "ਇਸ ਪ੍ਰੋਜੈਕਟ ਲਈ ਕੋਈ ਢੁਕਵੇਂ ਪੜਾਅ ਨਹੀਂ ਮਿਲੇ", "app.components.UI.RemoveImageButton.a11y_removeImage": "ਹਟਾਓ", "app.components.UI.TranslateButton.original": "ਮੂਲ", "app.components.UI.TranslateButton.translate": "ਅਨੁਵਾਦ ਕਰੋ", "app.components.Unauthorized.additionalInformationRequired": "ਤੁਹਾਡੇ ਭਾਗ ਲੈਣ ਲਈ ਵਾਧੂ ਜਾਣਕਾਰੀ ਦੀ ਲੋੜ ਹੈ।", "app.components.Unauthorized.completeProfile": "ਪੂਰਾ ਪ੍ਰੋਫਾਈਲ", "app.components.Unauthorized.completeProfileTitle": "ਹਿੱਸਾ ਲੈਣ ਲਈ ਆਪਣੀ ਪ੍ਰੋਫਾਈਲ ਨੂੰ ਪੂਰਾ ਕਰੋ", "app.components.Unauthorized.noPermission": "ਤੁਹਾਨੂੰ ਇਸ ਪੰਨੇ ਨੂੰ ਦੇਖਣ ਦੀ ਇਜਾਜ਼ਤ ਨਹੀਂ ਹੈ", "app.components.Unauthorized.notAuthorized": "ਮਾਫ਼ ਕਰਨਾ, ਤੁਸੀਂ ਇਸ ਪੰਨੇ ਨੂੰ ਐਕਸੈਸ ਕਰਨ ਲਈ ਅਧਿਕਾਰਤ ਨਹੀਂ ਹੋ।", "app.components.Upload.errorImageMaxSizeExceeded": "ਤੁਹਾਡੇ ਦੁਆਰਾ ਚੁਣਿਆ ਗਿਆ ਚਿੱਤਰ {maxFileSize}MB ਤੋਂ ਵੱਡਾ ਹੈ", "app.components.Upload.errorImagesMaxSizeExceeded": "ਤੁਹਾਡੇ ਵੱਲੋਂ ਚੁਣੀਆਂ ਗਈਆਂ ਇੱਕ ਜਾਂ ਕਈ ਤਸਵੀਰਾਂ {maxFileSize}MB ਤੋਂ ਵੱਡੀਆਂ ਹਨ", "app.components.Upload.onlyOneImage": "ਤੁਸੀਂ ਸਿਰਫ਼ 1 ਚਿੱਤਰ ਅੱਪਲੋਡ ਕਰ ਸਕਦੇ ਹੋ", "app.components.Upload.onlyXImages": "ਤੁਸੀਂ ਸਿਰਫ਼ {maxItemsCount} ਚਿੱਤਰ ਹੀ ਅੱਪਲੋਡ ਕਰ ਸਕਦੇ ਹੋ", "app.components.Upload.remaining": "ਬਾਕੀ", "app.components.Upload.uploadImageLabel": "ਇੱਕ ਚਿੱਤਰ ਚੁਣੋ (ਅਧਿਕਤਮ {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "ਇੱਕ ਜਾਂ ਇੱਕ ਤੋਂ ਵੱਧ ਚਿੱਤਰ ਚੁਣੋ", "app.components.UpsellTooltip.tooltipContent": "ਇਹ ਵਿਸ਼ੇਸ਼ਤਾ ਤੁਹਾਡੇ ਮੌਜੂਦਾ ਪਲਾਨ ਵਿੱਚ ਸ਼ਾਮਲ ਨਹੀਂ ਹੈ। ਇਸਨੂੰ ਅਨਲੌਕ ਕਰਨ ਲਈ ਆਪਣੇ ਸਰਕਾਰੀ ਸਫਲਤਾ ਪ੍ਰਬੰਧਕ ਜਾਂ ਪ੍ਰਸ਼ਾਸਕ ਨਾਲ ਗੱਲ ਕਰੋ।", "app.components.UserName.anonymous": "ਅਗਿਆਤ", "app.components.UserName.anonymousTooltip2": "ਇਸ ਉਪਭੋਗਤਾ ਨੇ ਆਪਣੇ ਯੋਗਦਾਨ ਨੂੰ ਗੁਪਤ ਰੱਖਣ ਦਾ ਫੈਸਲਾ ਕੀਤਾ ਹੈ", "app.components.UserName.authorWithNoNameTooltip": "ਤੁਹਾਡਾ ਨਾਮ ਸਵੈਚਲਿਤ ਹੋ ਗਿਆ ਹੈ ਕਿਉਂਕਿ ਤੁਸੀਂ ਆਪਣਾ ਨਾਮ ਦਰਜ ਨਹੀਂ ਕੀਤਾ ਹੈ। ਜੇਕਰ ਤੁਸੀਂ ਇਸ ਨੂੰ ਬਦਲਣਾ ਚਾਹੁੰਦੇ ਹੋ ਤਾਂ ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੀ ਪ੍ਰੋਫਾਈਲ ਨੂੰ ਅੱਪਡੇਟ ਕਰੋ।", "app.components.UserName.deletedUser": "ਅਗਿਆਤ ਲੇਖਕ", "app.components.UserName.verified": "ਪ੍ਰਮਾਣਿਤ", "app.components.VerificationModal.verifyAuth0": "NemID ਨਾਲ ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.VerificationModal.verifyBOSA": "itme ਜਾਂ eID ਨਾਲ ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.VerificationModal.verifyBosaFas": "itme ਜਾਂ eID ਨਾਲ ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.VerificationModal.verifyClaveUnica": "ਕਲੇਵ ਯੂਨਿਕਾ ਨਾਲ ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.VerificationModal.verifyFakeSSO": "ਜਾਅਲੀ SSO ਨਾਲ ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.VerificationModal.verifyIdAustria": "ID ਆਸਟਰੀਆ ਨਾਲ ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.VerificationModal.verifyKeycloak": "ID-Porten ਨਾਲ ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.VerificationModal.verifyNemLogIn": "MitID ਨਾਲ ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.VerificationModal.verifyTwoday2": "BankID ਜਾਂ Freja eID+ ਨਾਲ ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.VerificationModal.verifyYourIdentity": "ਆਪਣੀ ਪਛਾਣ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.VoteControl.budgetingFutureEnabled": "ਤੁਸੀਂ ਆਪਣਾ ਬਜਟ {enabledFromDate}ਤੋਂ ਸ਼ੁਰੂ ਕਰਕੇ ਨਿਰਧਾਰਤ ਕਰ ਸਕਦੇ ਹੋ।", "app.components.VoteControl.budgetingNotPermitted": "ਭਾਗੀਦਾਰੀ ਬਜਟਿੰਗ ਵਰਤਮਾਨ ਵਿੱਚ ਸਮਰੱਥ ਨਹੀਂ ਹੈ।", "app.components.VoteControl.budgetingNotPossible": "ਤੁਹਾਡੇ ਬਜਟ ਵਿੱਚ ਬਦਲਾਅ ਕਰਨਾ ਇਸ ਸਮੇਂ ਸੰਭਵ ਨਹੀਂ ਹੈ।", "app.components.VoteControl.budgetingNotVerified": "ਜਾਰੀ ਰੱਖਣ ਲਈ ਕਿਰਪਾ ਕਰਕੇ {verifyAccountLink} ।", "app.components.VoteInputs._shared.currencyLeft1": "ਤੁਹਾਡੇ ਕੋਲ {budgetLeft} / {totalBudget} ਬਾਕੀ ਹੈ", "app.components.VoteInputs._shared.numberOfCreditsLeft": "ਤੁਹਾਡੇ ਕੋਲ {votesLeft, plural, =0 {ਕੋਈ ਕ੍ਰੈਡਿਟ ਨਹੀਂ ਬਚਿਆ ਹੈ} other {# {totalNumberOfVotes, plural, one {ਵਿੱਚੋਂ 1 ਕ੍ਰੈਡਿਟ} other {# ਕ੍ਰੈਡਿਟ}} ਬਾਕੀ ਹਨ}}।", "app.components.VoteInputs._shared.numberOfPointsLeft": "ਤੁਹਾਡੇ ਕੋਲ {votesLeft, plural, =0 {ਕੋਈ ਅੰਕ ਨਹੀਂ ਬਚੇ ਹਨ} other {# {totalNumberOfVotes, plural, one {ਵਿੱਚੋਂ 1 ਅੰਕ} other {# ਅੰਕ}} ਬਾਕੀ ਹਨ}}।", "app.components.VoteInputs._shared.numberOfTokensLeft": "ਤੁਹਾਡੇ ਕੋਲ {votesLeft, plural, =0 {ਕੋਈ ਟੋਕਨ ਨਹੀਂ ਬਚਿਆ ਹੈ} other {# {totalNumberOfVotes, plural, one {ਵਿੱਚੋਂ 1 ਟੋਕਨ} other {# ਟੋਕਨ}} ਬਾਕੀ ਹਨ}}।", "app.components.VoteInputs._shared.numberOfVotesLeft": "ਤੁਹਾਡੇ ਕੋਲ {votesLeft, plural, =0 {ਕੋਈ ਵੋਟ ਨਹੀਂ ਬਚੀ ਹੈ} other {# {totalNumberOfVotes, plural, one {ਵਿੱਚੋਂ 1 ਵੋਟ} other {# ਵੋਟਾਂ}} ਬਾਕੀ ਹਨ}}।", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "ਤੁਸੀਂ ਪਹਿਲਾਂ ਹੀ ਆਪਣਾ ਬਜਟ ਜਮ੍ਹਾਂ ਕਰ ਦਿੱਤਾ ਹੈ। ਇਸਨੂੰ ਸੋਧਣ ਲਈ, \"ਆਪਣੀ ਜਮ੍ਹਾਂ ਰਕਮ ਨੂੰ ਸੋਧੋ\" 'ਤੇ ਕਲਿੱਕ ਕਰੋ।", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "ਤੁਸੀਂ ਪਹਿਲਾਂ ਹੀ ਆਪਣਾ ਬਜਟ ਜਮ੍ਹਾਂ ਕਰ ਦਿੱਤਾ ਹੈ। ਇਸਨੂੰ ਸੋਧਣ ਲਈ, ਪ੍ਰੋਜੈਕਟ ਪੰਨੇ 'ਤੇ ਵਾਪਸ ਜਾਓ ਅਤੇ \"ਆਪਣੀ ਜਮ੍ਹਾਂ ਰਕਮ ਨੂੰ ਸੋਧੋ\" 'ਤੇ ਕਲਿੱਕ ਕਰੋ।", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "ਬਜਟ ਉਪਲਬਧ ਨਹੀਂ ਹੈ, ਕਿਉਂਕਿ ਇਹ ਪੜਾਅ ਕਿਰਿਆਸ਼ੀਲ ਨਹੀਂ ਹੈ।", "app.components.VoteInputs.single.youHaveVotedForX2": "ਤੁਸੀਂ {votes, plural, =0 {# ਵਿਕਲਪਾਂ ਲਈ ਵੋਟ ਕੀਤੀ ਹੈ} one {# ਵਿਕਲਪ} other {# ਵਿਕਲਪ}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "ਇਸਦਾ ਮਤਲਬ ਹੈ ਕਿ ਤੁਸੀਂ ਇਸ ਇਨਪੁਟ ਨਾਲ ਸੰਬੰਧਿਤ ਸਾਰਾ ਡਾਟਾ ਗੁਆ ਦੇਵੋਗੇ, ਜਿਵੇਂ ਕਿ ਟਿੱਪਣੀਆਂ, ਪ੍ਰਤੀਕਰਮਾਂ ਅਤੇ ਵੋਟਾਂ। ਇਸ ਕਾਰਵਾਈ ਨੂੰ ਅਣਕੀਤਾ ਨਹੀਂ ਕੀਤਾ ਜਾ ਸਕਦਾ।", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਤੌਰ 'ਤੇ ਇਸ ਇਨਪੁਟ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "ਰੱਦ ਕਰੋ", "app.components.admin.PostManager.components.PostTable.Row.confirm": "ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.admin.SlugInput.resultingURL": "ਨਤੀਜਾ URL", "app.components.admin.SlugInput.slugTooltip": "ਸਲੱਗ ਪੰਨੇ ਦੇ ਵੈੱਬ ਪਤੇ, ਜਾਂ URL ਦੇ ਅੰਤ ਵਿੱਚ ਸ਼ਬਦਾਂ ਦਾ ਵਿਲੱਖਣ ਸਮੂਹ ਹੈ।", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "ਜੇਕਰ ਤੁਸੀਂ URL ਬਦਲਦੇ ਹੋ, ਤਾਂ ਪੁਰਾਣੇ URL ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹੋਏ ਪੰਨੇ ਦੇ ਲਿੰਕ ਹੁਣ ਕੰਮ ਨਹੀਂ ਕਰਨਗੇ।", "app.components.admin.SlugInput.urlSlugLabel": "ਸਲੱਗ", "app.components.admin.UserFilterConditions.addCondition": "ਇੱਕ ਸ਼ਰਤ ਜੋੜੋ", "app.components.admin.UserFilterConditions.field_email": "ਈਮੇਲ", "app.components.admin.UserFilterConditions.field_event_attendance": "ਇਵੈਂਟ ਰਜਿਸਟ੍ਰੇਸ਼ਨਾਂ", "app.components.admin.UserFilterConditions.field_follow": "ਦਾ ਪਾਲਣ ਕਰੋ", "app.components.admin.UserFilterConditions.field_lives_in": "ਵਿਚ ਰਹਿੰਦਾ ਹੈ", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "ਕਮਿਊਨਿਟੀ ਮਾਨੀਟਰ ਸਰਵੇਖਣ", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "ਸਥਿਤੀ ਦੇ ਨਾਲ ਇੱਕ ਇਨਪੁਟ ਨਾਲ ਇੰਟਰੈਕਟ ਕੀਤਾ", "app.components.admin.UserFilterConditions.field_participated_in_project": "ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਯੋਗਦਾਨ ਪਾਇਆ", "app.components.admin.UserFilterConditions.field_participated_in_topic": "ਟੈਗ ਦੇ ਨਾਲ ਕੁਝ ਪੋਸਟ ਕੀਤਾ", "app.components.admin.UserFilterConditions.field_registration_completed_at": "ਰਜਿਸਟ੍ਰੇਸ਼ਨ ਮਿਤੀ", "app.components.admin.UserFilterConditions.field_role": "ਭੂਮਿਕਾ", "app.components.admin.UserFilterConditions.field_verified": "ਪੁਸ਼ਟੀਕਰਨ", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "ਵਿਚਾਰ", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "ਪ੍ਰਸਤਾਵ", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "ਇਹਨਾਂ ਵਿੱਚੋਂ ਕਿਸੇ ਵੀ ਘਟਨਾ ਲਈ ਰਜਿਸਟਰਡ ਨਹੀਂ ਹੈ", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "ਕਿਸੇ ਵੀ ਘਟਨਾ ਲਈ ਰਜਿਸਟਰਡ ਨਹੀਂ ਹੈ", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "ਇਹਨਾਂ ਘਟਨਾਵਾਂ ਵਿੱਚੋਂ ਇੱਕ ਲਈ ਰਜਿਸਟਰਡ ਹੈ", "app.components.admin.UserFilterConditions.predicate_attends_something": "ਘੱਟੋ-ਘੱਟ ਇੱਕ ਘਟਨਾ ਲਈ ਰਜਿਸਟਰਡ ਹੈ", "app.components.admin.UserFilterConditions.predicate_begins_with": "ਨਾਲ ਸ਼ੁਰੂ ਹੁੰਦਾ ਹੈ", "app.components.admin.UserFilterConditions.predicate_commented_in": "ਟਿੱਪਣੀ ਕੀਤੀ", "app.components.admin.UserFilterConditions.predicate_contains": "ਸ਼ਾਮਿਲ ਹੈ", "app.components.admin.UserFilterConditions.predicate_ends_on": "'ਤੇ ਖਤਮ ਹੁੰਦਾ ਹੈ", "app.components.admin.UserFilterConditions.predicate_has_value": "ਮੁੱਲ ਹੈ", "app.components.admin.UserFilterConditions.predicate_in": "ਕੋਈ ਕਾਰਵਾਈ ਕੀਤੀ", "app.components.admin.UserFilterConditions.predicate_is": "ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_admin": "ਇੱਕ ਐਡਮਿਨ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_after": "ਦੇ ਬਾਅਦ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_before": "ਅੱਗੇ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_checked": "ਦੀ ਜਾਂਚ ਕੀਤੀ ਜਾਂਦੀ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_empty": "ਖਾਲੀ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_equal": "ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_exactly": "ਬਿਲਕੁਲ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "ਤੋਂ ਵੱਡਾ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "ਤੋਂ ਵੱਡਾ ਜਾਂ ਬਰਾਬਰ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "ਇੱਕ ਆਮ ਉਪਭੋਗਤਾ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_not_area": "ਖੇਤਰ ਨੂੰ ਸ਼ਾਮਲ ਨਹੀਂ ਕਰਦਾ", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "ਫੋਲਡਰ ਨੂੰ ਸ਼ਾਮਲ ਨਹੀਂ ਕਰਦਾ", "app.components.admin.UserFilterConditions.predicate_is_not_input": "ਇਨਪੁਟ ਨੂੰ ਸ਼ਾਮਲ ਨਹੀਂ ਕਰਦਾ", "app.components.admin.UserFilterConditions.predicate_is_not_project": "ਪ੍ਰੋਜੈਕਟ ਨੂੰ ਸ਼ਾਮਲ ਨਹੀਂ ਕਰਦਾ", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "ਵਿਸ਼ੇ ਨੂੰ ਸ਼ਾਮਲ ਨਹੀਂ ਕਰਦਾ", "app.components.admin.UserFilterConditions.predicate_is_one_of": "ਦੇ ਇੱਕ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "ਖੇਤਰਾਂ ਵਿੱਚੋਂ ਇੱਕ", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "ਫੋਲਡਰਾਂ ਵਿੱਚੋਂ ਇੱਕ", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "ਇਨਪੁਟਸ ਵਿੱਚੋਂ ਇੱਕ", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "ਪ੍ਰੋਜੈਕਟਾਂ ਵਿੱਚੋਂ ਇੱਕ", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "ਵਿਸ਼ਿਆਂ ਵਿੱਚੋਂ ਇੱਕ", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "ਇੱਕ ਪ੍ਰੋਜੈਕਟ ਮੈਨੇਜਰ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "ਤੋਂ ਛੋਟਾ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "ਤੋਂ ਛੋਟਾ ਜਾਂ ਬਰਾਬਰ ਹੈ", "app.components.admin.UserFilterConditions.predicate_is_verified": "ਪ੍ਰਮਾਣਿਤ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "ਨਾਲ ਸ਼ੁਰੂ ਨਹੀਂ ਹੁੰਦਾ", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "ਟਿੱਪਣੀ ਨਹੀਂ ਕੀਤੀ", "app.components.admin.UserFilterConditions.predicate_not_contains": "ਸ਼ਾਮਿਲ ਨਹੀ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "'ਤੇ ਖਤਮ ਨਹੀਂ ਹੁੰਦਾ", "app.components.admin.UserFilterConditions.predicate_not_has_value": "ਦਾ ਮੁੱਲ ਨਹੀਂ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_in": "ਯੋਗਦਾਨ ਨਹੀਂ ਪਾਇਆ", "app.components.admin.UserFilterConditions.predicate_not_is": "ਨਹੀਂ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "ਐਡਮਿਨ ਨਹੀਂ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "ਦੀ ਜਾਂਚ ਨਹੀਂ ਕੀਤੀ ਜਾਂਦੀ", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "ਖਾਲੀ ਨਹੀਂ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "ਨਹੀਂ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "ਇੱਕ ਆਮ ਉਪਭੋਗਤਾ ਨਹੀਂ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "ਵਿੱਚੋਂ ਇੱਕ ਨਹੀਂ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "ਇੱਕ ਪ੍ਰੋਜੈਕਟ ਮੈਨੇਜਰ ਨਹੀਂ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "ਪ੍ਰਮਾਣਿਤ ਨਹੀਂ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "ਕੋਈ ਇੰਪੁੱਟ ਪੋਸਟ ਨਹੀਂ ਕੀਤਾ", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "ਟਿੱਪਣੀ 'ਤੇ ਪ੍ਰਤੀਕਿਰਿਆ ਨਹੀਂ ਕੀਤੀ", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "ਇੰਪੁੱਟ 'ਤੇ ਪ੍ਰਤੀਕਿਰਿਆ ਨਹੀਂ ਦਿੱਤੀ", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "ਕਿਸੇ ਇਵੈਂਟ ਲਈ ਰਜਿਸਟਰ ਨਹੀਂ ਕੀਤਾ", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "ਨੇ ਸਰਵੇਖਣ ਨਹੀਂ ਕੀਤਾ ਹੈ", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "ਵਲੰਟੀਅਰ ਨਹੀਂ ਕੀਤਾ", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "ਵੋਟਿੰਗ ਵਿੱਚ ਹਿੱਸਾ ਨਹੀਂ ਲਿਆ", "app.components.admin.UserFilterConditions.predicate_nothing": "ਕੁਝ ਨਹੀਂ", "app.components.admin.UserFilterConditions.predicate_posted_input": "ਇੱਕ ਇਨਪੁਟ ਪੋਸਟ ਕੀਤਾ", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "ਟਿੱਪਣੀ 'ਤੇ ਪ੍ਰਤੀਕਿਰਿਆ ਦਿੱਤੀ", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "ਇੰਪੁੱਟ 'ਤੇ ਪ੍ਰਤੀਕਿਰਿਆ ਦਿੱਤੀ", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "ਇੱਕ ਘਟਨਾ ਲਈ ਰਜਿਸਟਰ ਕੀਤਾ", "app.components.admin.UserFilterConditions.predicate_something": "ਕੁਝ", "app.components.admin.UserFilterConditions.predicate_taken_survey": "ਨੇ ਸਰਵੇਖਣ ਕੀਤਾ ਹੈ", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "ਸਵੈਸੇਵੀ", "app.components.admin.UserFilterConditions.predicate_voted_in3": "ਵੋਟਿੰਗ ਵਿੱਚ ਹਿੱਸਾ ਲਿਆ", "app.components.admin.UserFilterConditions.rulesFormLabelField": "ਗੁਣ", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "ਹਾਲਤ", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "ਮੁੱਲ", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "ਤੁਹਾਨੂੰ ਤੁਹਾਡੇ ਯੋਗਦਾਨ 'ਤੇ ਸੂਚਨਾਵਾਂ ਪ੍ਰਾਪਤ ਨਹੀਂ ਹੋਣਗੀਆਂ", "app.components.anonymousParticipationModal.cancel": "ਰੱਦ ਕਰੋ", "app.components.anonymousParticipationModal.continue": "ਜਾਰੀ ਰੱਖੋ", "app.components.anonymousParticipationModal.participateAnonymously": "ਅਗਿਆਤ ਰੂਪ ਵਿੱਚ ਹਿੱਸਾ ਲਓ", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "This will safely <b>hide your profile</b> from admins, project managers and other residents for this specific contribution so that nobody is able to link this contribution to you. Anonymous contributions cannot be edited, and are considered final.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "ਸਾਡੇ ਪਲੇਟਫਾਰਮ ਨੂੰ ਹਰੇਕ ਉਪਭੋਗਤਾ ਲਈ ਸੁਰੱਖਿਅਤ ਬਣਾਉਣਾ ਸਾਡੇ ਲਈ ਇੱਕ ਪ੍ਰਮੁੱਖ ਤਰਜੀਹ ਹੈ। ਸ਼ਬਦ ਮਾਇਨੇ ਰੱਖਦੇ ਹਨ, ਇਸ ਲਈ ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਦੂਜੇ ਨਾਲ ਦਿਆਲੂ ਬਣੋ।", "app.components.avatar.titleForAccessibility": "{fullName}ਦਾ ਪ੍ਰੋਫਾਈਲ", "app.components.customFields.mapInput.removeAnswer": "ਜਵਾਬ ਹਟਾਓ", "app.components.customFields.mapInput.undo": "ਅਣਡੂ ਕਰੋ", "app.components.customFields.mapInput.undoLastPoint": "ਆਖਰੀ ਬਿੰਦੂ ਨੂੰ ਅਣਕੀਤਾ ਕਰੋ", "app.components.followUnfollow.follow": "ਦਾ ਪਾਲਣ ਕਰੋ", "app.components.followUnfollow.followADiscussion": "ਚਰਚਾ ਦੀ ਪਾਲਣਾ ਕਰੋ", "app.components.followUnfollow.followTooltipInputPage2": "ਇਸ ਤੋਂ ਬਾਅਦ ਸਥਿਤੀ ਤਬਦੀਲੀਆਂ, ਅਧਿਕਾਰਤ ਅੱਪਡੇਟਾਂ, ਅਤੇ ਟਿੱਪਣੀਆਂ ਬਾਰੇ ਈਮੇਲ ਅੱਪਡੇਟ ਸ਼ੁਰੂ ਹੁੰਦੇ ਹਨ। ਤੁਸੀਂ ਕਿਸੇ ਵੀ ਸਮੇਂ {unsubscribeLink} ਕਰ ਸਕਦੇ ਹੋ।", "app.components.followUnfollow.followTooltipProjects2": "ਹੇਠ ਦਿੱਤੇ ਪ੍ਰੋਜੈਕਟ ਤਬਦੀਲੀਆਂ ਬਾਰੇ ਈਮੇਲ ਅਪਡੇਟਾਂ ਨੂੰ ਚਾਲੂ ਕਰਦਾ ਹੈ। ਤੁਸੀਂ ਕਿਸੇ ਵੀ ਸਮੇਂ {unsubscribeLink} ਕਰ ਸਕਦੇ ਹੋ।", "app.components.followUnfollow.unFollow": "ਅਨਫਾਲੋ ਕਰੋ", "app.components.followUnfollow.unsubscribe": "ਗਾਹਕੀ ਰੱਦ ਕਰੋ", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "ਸਾਡੇ ਦਿਸ਼ਾ-ਨਿਰਦੇਸ਼", "app.components.form.ErrorDisplay.next": "ਅਗਲਾ", "app.components.form.ErrorDisplay.previous": "ਪਿਛਲਾ", "app.components.form.ErrorDisplay.save": "ਸੇਵ ਕਰੋ", "app.components.form.ErrorDisplay.userPickerPlaceholder": "ਉਪਭੋਗਤਾ ਈਮੇਲ ਜਾਂ ਨਾਮ ਦੁਆਰਾ ਖੋਜ ਕਰਨ ਲਈ ਟਾਈਪ ਕਰਨਾ ਸ਼ੁਰੂ ਕਰੋ...", "app.components.form.anonymousSurveyMessage2": "ਇਸ ਸਰਵੇਖਣ ਦੇ ਸਾਰੇ ਜਵਾਬ ਅਗਿਆਤ ਹਨ।", "app.components.form.backToInputManager": "ਇਨਪੁੱਟ ਮੈਨੇਜਰ ਤੇ ਵਾਪਸ ਜਾਓ", "app.components.form.backToProject": "ਪ੍ਰੋਜੈਕਟ 'ਤੇ ਵਾਪਸ ਜਾਓ", "app.components.form.components.controls.mapInput.removeAnswer": "ਜਵਾਬ ਹਟਾਓ", "app.components.form.components.controls.mapInput.undo": "ਅਣਡੂ", "app.components.form.components.controls.mapInput.undoLastPoint": "ਆਖਰੀ ਬਿੰਦੂ ਨੂੰ ਅਣਡੂ ਕਰੋ", "app.components.form.controls.addressInputAriaLabel": "ਪਤਾ ਇੰਪੁੱਟ", "app.components.form.controls.addressInputPlaceholder6": "ਕੋਈ ਪਤਾ ਦਾਖਲ ਕਰੋ...", "app.components.form.controls.adminFieldTooltip": "ਖੇਤਰ ਸਿਰਫ਼ ਪ੍ਰਸ਼ਾਸਕਾਂ ਨੂੰ ਦਿਖਾਈ ਦਿੰਦਾ ਹੈ", "app.components.form.controls.allStatementsError": "ਸਾਰੇ ਬਿਆਨਾਂ ਲਈ ਇੱਕ ਜਵਾਬ ਚੁਣਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ।", "app.components.form.controls.back": "ਵਾਪਸ", "app.components.form.controls.clearAll": "ਸਭ ਸਾਫ਼ ਕਰੋ", "app.components.form.controls.clearAllScreenreader": "ਉਪਰੋਕਤ ਮੈਟਰਿਕਸ ਪ੍ਰਸ਼ਨ ਵਿੱਚੋਂ ਸਾਰੇ ਜਵਾਬਾਂ ਨੂੰ ਸਾਫ਼ ਕਰੋ", "app.components.form.controls.clickOnMapMultipleToAdd3": "ਖਿੱਚਣ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਕਲਿੱਕ ਕਰੋ। ਫਿਰ, ਉਹਨਾਂ ਨੂੰ ਮੂਵ ਕਰਨ ਲਈ ਬਿੰਦੂਆਂ 'ਤੇ ਖਿੱਚੋ।", "app.components.form.controls.clickOnMapToAddOrType": "ਆਪਣਾ ਜਵਾਬ ਜੋੜਨ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਕਲਿੱਕ ਕਰੋ ਜਾਂ ਹੇਠਾਂ ਕੋਈ ਪਤਾ ਟਾਈਪ ਕਰੋ।", "app.components.form.controls.confirm": "ਪੁਸ਼ਟੀ ਕਰੋ", "app.components.form.controls.cosponsorsPlaceholder": "ਖੋਜ ਕਰਨ ਲਈ ਇੱਕ ਨਾਮ ਟਾਈਪ ਕਰਨਾ ਸ਼ੁਰੂ ਕਰੋ", "app.components.form.controls.currentRank": "ਮੌਜੂਦਾ ਰੈਂਕ:", "app.components.form.controls.minimumCoordinates2": "ਘੱਟੋ-ਘੱਟ {numPoints} ਨਕਸ਼ਾ ਪੁਆਇੰਟਾਂ ਦੀ ਲੋੜ ਹੈ।", "app.components.form.controls.noRankSelected": "ਕੋਈ ਰੈਂਕ ਨਹੀਂ ਚੁਣਿਆ ਗਿਆ", "app.components.form.controls.notPublic1": "*ਇਹ ਜਵਾਬ ਸਿਰਫ਼ ਪ੍ਰੋਜੈਕਟ ਪ੍ਰਬੰਧਕਾਂ ਨਾਲ ਸਾਂਝਾ ਕੀਤਾ ਜਾਵੇਗਾ, ਨਾ ਕਿ ਜਨਤਾ ਨਾਲ।", "app.components.form.controls.optionalParentheses": "(ਵਿਕਲਪਿਕ)", "app.components.form.controls.rankingInstructions": "ਰੈਂਕ ਵਿਕਲਪਾਂ ਲਈ ਖਿੱਚੋ ਅਤੇ ਸੁੱਟੋ।", "app.components.form.controls.selectAsManyAsYouLike": "* ਜਿੰਨੇ ਚਾਹੋ ਚੁਣੋ", "app.components.form.controls.selectBetween": "* {minItems} ਅਤੇ {maxItems} ਵਿਕਲਪਾਂ ਵਿਚਕਾਰ ਚੁਣੋ", "app.components.form.controls.selectExactly2": "*ਬਿਲਕੁਲ ਚੁਣੋ {selectExactly, plural, one {# ਵਿਕਲਪ} other {# ਵਿਕਲਪ}}", "app.components.form.controls.selectMany": "* ਜਿੰਨੇ ਚਾਹੋ ਚੁਣੋ", "app.components.form.controls.tapOnFullscreenMapToAdd4": "ਖਿੱਚਣ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਟੈਪ ਕਰੋ। ਫਿਰ, ਉਹਨਾਂ ਨੂੰ ਮੂਵ ਕਰਨ ਲਈ ਬਿੰਦੂਆਂ 'ਤੇ ਖਿੱਚੋ।", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "ਖਿੱਚਣ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਟੈਪ ਕਰੋ।", "app.components.form.controls.tapOnMapMultipleToAdd3": "ਆਪਣਾ ਜਵਾਬ ਸ਼ਾਮਲ ਕਰਨ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਟੈਪ ਕਰੋ।", "app.components.form.controls.tapOnMapToAddOrType": "ਆਪਣਾ ਜਵਾਬ ਜੋੜਨ ਲਈ ਨਕਸ਼ੇ 'ਤੇ ਟੈਪ ਕਰੋ ਜਾਂ ਹੇਠਾਂ ਕੋਈ ਪਤਾ ਟਾਈਪ ਕਰੋ।", "app.components.form.controls.tapToAddALine": "ਇੱਕ ਲਾਈਨ ਜੋੜਨ ਲਈ ਟੈਪ ਕਰੋ", "app.components.form.controls.tapToAddAPoint": "ਬਿੰਦੂ ਜੋੜਨ ਲਈ ਟੈਪ ਕਰੋ", "app.components.form.controls.tapToAddAnArea": "ਕੋਈ ਖੇਤਰ ਸ਼ਾਮਲ ਕਰਨ ਲਈ ਟੈਪ ਕਰੋ", "app.components.form.controls.uploadShapefileInstructions": "* ਇੱਕ ਜ਼ਿਪ ਫਾਈਲ ਅਪਲੋਡ ਕਰੋ ਜਿਸ ਵਿੱਚ ਇੱਕ ਜਾਂ ਇੱਕ ਤੋਂ ਵੱਧ ਆਕਾਰ ਫਾਈਲਾਂ ਹਨ।", "app.components.form.controls.validCordinatesTooltip2": "ਜੇਕਰ ਤੁਹਾਡੇ ਦੁਆਰਾ ਟਾਈਪ ਕੀਤੇ ਵਿਕਲਪਾਂ ਵਿੱਚ ਟਿਕਾਣਾ ਪ੍ਰਦਰਸ਼ਿਤ ਨਹੀਂ ਹੁੰਦਾ ਹੈ, ਤਾਂ ਤੁਸੀਂ ਇੱਕ ਸਟੀਕ ਸਥਾਨ (ਜਿਵੇਂ: -33.019808, -71.495676) ਨੂੰ ਨਿਰਧਾਰਤ ਕਰਨ ਲਈ ਫਾਰਮੈਟ 'ਅਕਸ਼ਾਂਸ਼, ਲੰਬਕਾਰ' ਵਿੱਚ ਵੈਧ ਕੋਆਰਡੀਨੇਟਸ ਜੋੜ ਸਕਦੇ ਹੋ।", "app.components.form.controls.valueOutOfTotal": "{total}ਵਿੱਚੋਂ {value}", "app.components.form.controls.valueOutOfTotalWithLabel": "{total}ਵਿੱਚੋਂ {value} , {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{total}ਵਿੱਚੋਂ {value} , ਜਿੱਥੇ {maxValue} ਹੈ {maxLabel}", "app.components.form.error": "ਗਲਤੀ", "app.components.form.locationGoogleUnavailable": "ਗੂਗਲ ਮੈਪਸ ਦੁਆਰਾ ਪ੍ਰਦਾਨ ਕੀਤੇ ਗਏ ਸਥਾਨ ਖੇਤਰ ਨੂੰ ਲੋਡ ਨਹੀਂ ਕੀਤਾ ਜਾ ਸਕਿਆ।", "app.components.form.progressBarLabel": "ਸਰਵੇਖਣ ਦੀ ਪ੍ਰਗਤੀ", "app.components.form.submit": "ਜਮ੍ਹਾਂ ਕਰੋ", "app.components.form.submitApiError": "ਫਾਰਮ ਸਪੁਰਦ ਕਰਨ ਵਿੱਚ ਇੱਕ ਸਮੱਸਿਆ ਆਈ ਸੀ। ਕਿਰਪਾ ਕਰਕੇ ਕਿਸੇ ਵੀ ਤਰੁੱਟੀ ਦੀ ਜਾਂਚ ਕਰੋ ਅਤੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.components.form.verifiedBlocked": "ਤੁਸੀਂ ਇਸ ਖੇਤਰ ਨੂੰ ਸੰਪਾਦਿਤ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਇਸ ਵਿੱਚ ਪ੍ਰਮਾਣਿਤ ਜਾਣਕਾਰੀ ਸ਼ਾਮਲ ਹੈ", "app.components.formBuilder.Page": "ਪੰਨਾ", "app.components.formBuilder.accessibilityStatement": "ਪਹੁੰਚਯੋਗਤਾ ਬਿਆਨ", "app.components.formBuilder.addAnswer": "ਜਵਾਬ ਸ਼ਾਮਲ ਕਰੋ", "app.components.formBuilder.addStatement": "ਸਟੇਟਮੈਂਟ ਸ਼ਾਮਲ ਕਰੋ", "app.components.formBuilder.agree": "ਸਹਿਮਤ ਹੋ", "app.components.formBuilder.ai1": "ਏ.ਆਈ", "app.components.formBuilder.aiUpsellText1": "ਜੇਕਰ ਤੁਹਾਡੇ ਕੋਲ ਸਾਡੇ AI ਪੈਕੇਜ ਤੱਕ ਪਹੁੰਚ ਹੈ, ਤਾਂ ਤੁਸੀਂ AI ਨਾਲ ਟੈਕਸਟ ਜਵਾਬਾਂ ਨੂੰ ਸੰਖੇਪ ਅਤੇ ਸ਼੍ਰੇਣੀਬੱਧ ਕਰਨ ਦੇ ਯੋਗ ਹੋਵੋਗੇ।", "app.components.formBuilder.askFollowUpToggleLabel": "ਫਾਲੋ-ਅੱਪ ਲਈ ਪੁੱਛੋ", "app.components.formBuilder.bad": "ਮਾੜਾ", "app.components.formBuilder.buttonLabel": "ਬਟਨ ਲੇਬਲ", "app.components.formBuilder.buttonLink": "ਬਟਨ ਲਿੰਕ", "app.components.formBuilder.cancelLeaveBuilderButtonText": "ਰੱਦ ਕਰੋ", "app.components.formBuilder.category": "ਸ਼੍ਰੇਣੀ", "app.components.formBuilder.chooseMany": "ਬਹੁਤ ਸਾਰੇ ਚੁਣੋ", "app.components.formBuilder.chooseOne": "ਇੱਕ ਚੁਣੋ", "app.components.formBuilder.close": "ਬੰਦ ਕਰੋ", "app.components.formBuilder.closed": "ਬੰਦ", "app.components.formBuilder.configureMap": "ਨਕਸ਼ਾ ਕੌਂਫਿਗਰ ਕਰੋ", "app.components.formBuilder.confirmLeaveBuilderButtonText": "ਹਾਂ, ਮੈਂ ਛੱਡਣਾ ਚਾਹੁੰਦਾ ਹਾਂ", "app.components.formBuilder.content": "ਸਮੱਗਰੀ", "app.components.formBuilder.continuePageLabel": "ਤੱਕ ਜਾਰੀ ਹੈ", "app.components.formBuilder.cosponsors": "ਸਹਿ-ਪ੍ਰਾਯੋਜਕ", "app.components.formBuilder.default": "ਡਿਫਾਲਟ", "app.components.formBuilder.defaultContent": "ਪੂਰਵ-ਨਿਰਧਾਰਤ ਸਮੱਗਰੀ", "app.components.formBuilder.delete": "ਮਿਟਾਓ", "app.components.formBuilder.deleteButtonLabel": "ਮਿਟਾਓ", "app.components.formBuilder.description": "ਵਰਣਨ", "app.components.formBuilder.disabledBuiltInFieldTooltip": "ਇਹ ਪਹਿਲਾਂ ਹੀ ਫਾਰਮ ਵਿੱਚ ਸ਼ਾਮਲ ਕੀਤਾ ਗਿਆ ਹੈ। ਪੂਰਵ-ਨਿਰਧਾਰਤ ਸਮੱਗਰੀ ਸਿਰਫ਼ ਇੱਕ ਵਾਰ ਵਰਤੀ ਜਾ ਸਕਦੀ ਹੈ।", "app.components.formBuilder.disabledCustomFieldsTooltip1": "ਕਸਟਮ ਸਮੱਗਰੀ ਸ਼ਾਮਲ ਕਰਨਾ ਤੁਹਾਡੇ ਮੌਜੂਦਾ ਲਾਇਸੰਸ ਦਾ ਹਿੱਸਾ ਨਹੀਂ ਹੈ। ਇਸ ਬਾਰੇ ਹੋਰ ਜਾਣਨ ਲਈ ਆਪਣੇ GovSuccess ਮੈਨੇਜਰ ਨਾਲ ਸੰਪਰਕ ਕਰੋ।", "app.components.formBuilder.disagree": "ਅਸਹਿਮਤ", "app.components.formBuilder.displayAsDropdown": "ਡ੍ਰੌਪਡਾਉਨ ਦੇ ਰੂਪ ਵਿੱਚ ਪ੍ਰਦਰਸ਼ਿਤ ਕਰੋ", "app.components.formBuilder.displayAsDropdownTooltip": "ਡ੍ਰੌਪਡਾਉਨ ਵਿੱਚ ਵਿਕਲਪਾਂ ਨੂੰ ਪ੍ਰਦਰਸ਼ਿਤ ਕਰੋ। ਜੇ ਤੁਹਾਡੇ ਕੋਲ ਬਹੁਤ ਸਾਰੇ ਵਿਕਲਪ ਹਨ, ਤਾਂ ਇਹ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ।", "app.components.formBuilder.done": "ਹੋ ਗਿਆ", "app.components.formBuilder.drawArea": "ਖੇਤਰ ਖਿੱਚੋ", "app.components.formBuilder.drawRoute": "ਰਸਤਾ ਖਿੱਚੋ", "app.components.formBuilder.dropPin": "ਡ੍ਰੌਪ ਪਿੰਨ", "app.components.formBuilder.editButtonLabel": "ਸੰਪਾਦਿਤ ਕਰੋ", "app.components.formBuilder.emptyImageOptionError": "ਘੱਟੋ-ਘੱਟ 1 ਜਵਾਬ ਦਿਓ। ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਦਿਓ ਕਿ ਹਰੇਕ ਜਵਾਬ ਦਾ ਇੱਕ ਸਿਰਲੇਖ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ।", "app.components.formBuilder.emptyOptionError": "ਘੱਟੋ-ਘੱਟ 1 ਜਵਾਬ ਦਿਓ", "app.components.formBuilder.emptyStatementError": "ਘੱਟੋ-ਘੱਟ 1 ਬਿਆਨ ਪ੍ਰਦਾਨ ਕਰੋ", "app.components.formBuilder.emptyTitleError": "ਇੱਕ ਸਵਾਲ ਦਾ ਸਿਰਲੇਖ ਪ੍ਰਦਾਨ ਕਰੋ", "app.components.formBuilder.emptyTitleMessage": "ਸਾਰੇ ਜਵਾਬਾਂ ਲਈ ਇੱਕ ਸਿਰਲੇਖ ਪ੍ਰਦਾਨ ਕਰੋ", "app.components.formBuilder.emptyTitleStatementMessage": "ਸਾਰੇ ਬਿਆਨਾਂ ਲਈ ਸਿਰਲੇਖ ਪ੍ਰਦਾਨ ਕਰੋ", "app.components.formBuilder.enable": "ਯੋਗ ਕਰੋ", "app.components.formBuilder.errorMessage": "ਇੱਕ ਸਮੱਸਿਆ ਹੈ, ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੀਆਂ ਤਬਦੀਲੀਆਂ ਨੂੰ ਸੁਰੱਖਿਅਤ ਕਰਨ ਦੇ ਯੋਗ ਹੋਣ ਲਈ ਸਮੱਸਿਆ ਨੂੰ ਠੀਕ ਕਰੋ", "app.components.formBuilder.fieldGroup.description": "ਵਰਣਨ (ਵਿਕਲਪਿਕ)", "app.components.formBuilder.fieldGroup.title": "ਸਿਰਲੇਖ (ਵਿਕਲਪਿਕ)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "ਵਰਤਮਾਨ ਵਿੱਚ, ਇਹਨਾਂ ਸਵਾਲਾਂ ਦੇ ਜਵਾਬ ਕੇਵਲ ਇਨਪੁਟ ਮੈਨੇਜਰ 'ਤੇ ਨਿਰਯਾਤ ਐਕਸਲ ਫਾਈਲ ਵਿੱਚ ਉਪਲਬਧ ਹਨ, ਅਤੇ ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਦਿਖਾਈ ਨਹੀਂ ਦਿੰਦੇ ਹਨ।", "app.components.formBuilder.fieldLabel": "ਜਵਾਬ ਵਿਕਲਪ", "app.components.formBuilder.fieldLabelStatement": "ਬਿਆਨ", "app.components.formBuilder.fileUpload": "ਫਾਈਲ ਅਪਲੋਡ ਕਰੋ", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "ਨਕਸ਼ਾ-ਆਧਾਰਿਤ ਪੰਨਾ", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "ਸੰਦਰਭ ਵਜੋਂ ਨਕਸ਼ੇ ਨੂੰ ਏਮਬੇਡ ਕਰੋ ਜਾਂ ਭਾਗੀਦਾਰਾਂ ਨੂੰ ਸਥਾਨ ਅਧਾਰਤ ਪ੍ਰਸ਼ਨ ਪੁੱਛੋ।", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "ਅਨੁਕੂਲ ਉਪਭੋਗਤਾ ਅਨੁਭਵ ਲਈ, ਅਸੀਂ ਨਕਸ਼ੇ-ਅਧਾਰਿਤ ਪੰਨਿਆਂ ਵਿੱਚ ਬਿੰਦੂ, ਰੂਟ, ਜਾਂ ਖੇਤਰ ਦੇ ਸਵਾਲਾਂ ਨੂੰ ਜੋੜਨ ਦੀ ਸਿਫ਼ਾਰਿਸ਼ ਨਹੀਂ ਕਰਦੇ ਹਾਂ।", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "ਸਧਾਰਨ ਪੰਨਾ", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "ਸਰਵੇਖਣ ਮੈਪਿੰਗ ਵਿਸ਼ੇਸ਼ਤਾਵਾਂ ਤੁਹਾਡੇ ਮੌਜੂਦਾ ਲਾਇਸੰਸ ਵਿੱਚ ਸ਼ਾਮਲ ਨਹੀਂ ਹਨ। ਹੋਰ ਜਾਣਨ ਲਈ ਆਪਣੇ GovSuccess ਮੈਨੇਜਰ ਨਾਲ ਸੰਪਰਕ ਕਰੋ।", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "ਪੰਨਾ ਕਿਸਮ", "app.components.formBuilder.formEnd": "ਫਾਰਮ ਅੰਤ", "app.components.formBuilder.formField.cancelDeleteButtonText": "ਰੱਦ ਕਰੋ", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "ਹਾਂ, ਪੰਨਾ ਮਿਟਾਓ", "app.components.formBuilder.formField.copyNoun": "ਕਾਪੀ ਕਰੋ", "app.components.formBuilder.formField.copyVerb": "ਕਾਪੀ ਕਰੋ", "app.components.formBuilder.formField.delete": "ਮਿਟਾਓ", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "ਇਸ ਪੰਨੇ ਨੂੰ ਮਿਟਾਉਣ ਨਾਲ ਇਸ ਨਾਲ ਜੁੜੇ ਤਰਕ ਵੀ ਮਿਟ ਜਾਣਗੇ। ਕੀ ਤੁਸੀਂ ਯਕੀਨਨ ਇਸਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?", "app.components.formBuilder.formField.deleteResultsInfo": "ਇਸਨੂੰ ਅਣਕੀਤਾ ਨਹੀਂ ਕੀਤਾ ਜਾ ਸਕਦਾ", "app.components.formBuilder.goToPageInputLabel": "ਫਿਰ ਅਗਲਾ ਪੰਨਾ ਹੈ:", "app.components.formBuilder.good": "ਚੰਗਾ", "app.components.formBuilder.helmetTitle": "ਫਾਰਮ ਬਿਲਡਰ", "app.components.formBuilder.imageFileUpload": "ਚਿੱਤਰ ਅੱਪਲੋਡ", "app.components.formBuilder.invalidLogicBadgeMessage": "ਅਵੈਧ ਤਰਕ", "app.components.formBuilder.labels2": "ਲੇਬਲ (ਵਿਕਲਪਿਕ)", "app.components.formBuilder.labelsTooltipContent2": "ਕਿਸੇ ਵੀ ਰੇਖਿਕ ਪੈਮਾਨੇ ਦੇ ਮੁੱਲਾਂ ਲਈ ਵਿਕਲਪਿਕ ਲੇਬਲ ਚੁਣੋ।", "app.components.formBuilder.lastPage": "ਸਮਾਪਤ", "app.components.formBuilder.layout": "ਖਾਕਾ", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "ਕੀ ਤੁਸੀਂ ਪੱਕਾ ਛੱਡਣਾ ਚਾਹੁੰਦੇ ਹੋ?", "app.components.formBuilder.leaveBuilderText": "ਤੁਹਾਡੇ ਕੋਲ ਅਣਰੱਖਿਅਤ ਤਬਦੀਲੀਆਂ ਹਨ। ਕਿਰਪਾ ਕਰਕੇ ਜਾਣ ਤੋਂ ਪਹਿਲਾਂ ਬਚਾਓ। ਜੇਕਰ ਤੁਸੀਂ ਛੱਡ ਦਿੰਦੇ ਹੋ, ਤਾਂ ਤੁਸੀਂ ਆਪਣੀਆਂ ਤਬਦੀਲੀਆਂ ਗੁਆ ਬੈਠੋਗੇ।", "app.components.formBuilder.limitAnswersTooltip": "ਚਾਲੂ ਹੋਣ 'ਤੇ, ਉੱਤਰਦਾਤਾਵਾਂ ਨੂੰ ਅੱਗੇ ਵਧਣ ਲਈ ਜਵਾਬਾਂ ਦੀ ਨਿਰਧਾਰਤ ਸੰਖਿਆ ਚੁਣਨ ਦੀ ਲੋੜ ਹੁੰਦੀ ਹੈ।", "app.components.formBuilder.limitNumberAnswers": "ਜਵਾਬਾਂ ਦੀ ਗਿਣਤੀ ਸੀਮਤ ਕਰੋ", "app.components.formBuilder.linePolygonMapWarning2": "ਲਾਈਨ ਅਤੇ ਬਹੁਭੁਜ ਡਰਾਇੰਗ ਪਹੁੰਚਯੋਗਤਾ ਮਿਆਰਾਂ ਨੂੰ ਪੂਰਾ ਨਹੀਂ ਕਰ ਸਕਦੇ ਹਨ। ਵਧੇਰੇ ਜਾਣਕਾਰੀ {accessibilityStatement}ਵਿੱਚ ਲੱਭੀ ਜਾ ਸਕਦੀ ਹੈ।", "app.components.formBuilder.linearScale": "ਰੇਖਿਕ ਪੈਮਾਨਾ", "app.components.formBuilder.locationDescription": "ਟਿਕਾਣਾ", "app.components.formBuilder.logic": "ਤਰਕ", "app.components.formBuilder.logicAnyOtherAnswer": "ਕੋਈ ਹੋਰ ਜਵਾਬ", "app.components.formBuilder.logicConflicts.conflictingLogic": "ਵਿਰੋਧੀ ਤਰਕ", "app.components.formBuilder.logicConflicts.interQuestionConflict": "ਇਸ ਪੰਨੇ ਵਿੱਚ ਅਜਿਹੇ ਸਵਾਲ ਹਨ ਜੋ ਵੱਖ-ਵੱਖ ਪੰਨਿਆਂ 'ਤੇ ਲੈ ਜਾਂਦੇ ਹਨ। ਜੇਕਰ ਭਾਗੀਦਾਰ ਕਈ ਸਵਾਲਾਂ ਦੇ ਜਵਾਬ ਦਿੰਦੇ ਹਨ, ਤਾਂ ਸਭ ਤੋਂ ਦੂਰ ਦਾ ਪੰਨਾ ਦਿਖਾਇਆ ਜਾਵੇਗਾ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਇਹ ਵਿਵਹਾਰ ਤੁਹਾਡੇ ਇੱਛਤ ਪ੍ਰਵਾਹ ਨਾਲ ਮੇਲ ਖਾਂਦਾ ਹੈ।", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "ਇਸ ਪੰਨੇ 'ਤੇ ਕਈ ਤਰਕ ਨਿਯਮ ਲਾਗੂ ਕੀਤੇ ਗਏ ਹਨ: ਬਹੁ-ਚੋਣ ਵਾਲੇ ਸਵਾਲ ਤਰਕ, ਪੰਨਾ-ਪੱਧਰ ਦਾ ਤਰਕ, ਅਤੇ ਅੰਤਰ-ਪ੍ਰਸ਼ਨ ਤਰਕ। ਜਦੋਂ ਇਹ ਸ਼ਰਤਾਂ ਓਵਰਲੈਪ ਹੋ ਜਾਂਦੀਆਂ ਹਨ, ਸਵਾਲ ਤਰਕ ਨੂੰ ਪੰਨਾ ਤਰਕ ਨਾਲੋਂ ਤਰਜੀਹ ਦਿੱਤੀ ਜਾਵੇਗੀ, ਅਤੇ ਸਭ ਤੋਂ ਦੂਰ ਦਾ ਪੰਨਾ ਦਿਖਾਇਆ ਜਾਵੇਗਾ। ਇਹ ਯਕੀਨੀ ਬਣਾਉਣ ਲਈ ਤਰਕ ਦੀ ਸਮੀਖਿਆ ਕਰੋ ਕਿ ਇਹ ਤੁਹਾਡੇ ਇੱਛਤ ਪ੍ਰਵਾਹ ਨਾਲ ਇਕਸਾਰ ਹੈ।", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "ਇਸ ਪੰਨੇ ਵਿੱਚ ਇੱਕ ਬਹੁ-ਚੋਣ ਵਾਲੇ ਸਵਾਲ ਹਨ ਜਿੱਥੇ ਵਿਕਲਪ ਵੱਖ-ਵੱਖ ਪੰਨਿਆਂ 'ਤੇ ਲੈ ਜਾਂਦੇ ਹਨ। ਜੇਕਰ ਭਾਗੀਦਾਰ ਕਈ ਵਿਕਲਪ ਚੁਣਦੇ ਹਨ, ਤਾਂ ਸਭ ਤੋਂ ਦੂਰ ਦਾ ਪੰਨਾ ਦਿਖਾਇਆ ਜਾਵੇਗਾ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਇਹ ਵਿਵਹਾਰ ਤੁਹਾਡੇ ਇੱਛਤ ਪ੍ਰਵਾਹ ਨਾਲ ਮੇਲ ਖਾਂਦਾ ਹੈ।", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "ਇਸ ਪੰਨੇ ਵਿੱਚ ਇੱਕ ਬਹੁ-ਚੋਣ ਵਾਲੇ ਸਵਾਲ ਹਨ ਜਿੱਥੇ ਵਿਕਲਪ ਵੱਖ-ਵੱਖ ਪੰਨਿਆਂ 'ਤੇ ਲੈ ਜਾਂਦੇ ਹਨ ਅਤੇ ਅਜਿਹੇ ਸਵਾਲ ਹਨ ਜੋ ਦੂਜੇ ਪੰਨਿਆਂ 'ਤੇ ਲੈ ਜਾਂਦੇ ਹਨ। ਜੇਕਰ ਇਹ ਸ਼ਰਤਾਂ ਓਵਰਲੈਪ ਹੁੰਦੀਆਂ ਹਨ ਤਾਂ ਸਭ ਤੋਂ ਦੂਰ ਦਾ ਪੰਨਾ ਦਿਖਾਇਆ ਜਾਵੇਗਾ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਇਹ ਵਿਵਹਾਰ ਤੁਹਾਡੇ ਇੱਛਤ ਪ੍ਰਵਾਹ ਨਾਲ ਮੇਲ ਖਾਂਦਾ ਹੈ।", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "ਇਸ ਪੰਨੇ ਵਿੱਚ ਇੱਕ ਬਹੁ-ਚੋਣ ਵਾਲੇ ਸਵਾਲ ਹਨ ਜਿੱਥੇ ਵਿਕਲਪ ਵੱਖ-ਵੱਖ ਪੰਨਿਆਂ 'ਤੇ ਲੈ ਜਾਂਦੇ ਹਨ ਅਤੇ ਪੰਨੇ ਅਤੇ ਪ੍ਰਸ਼ਨ ਪੱਧਰ ਦੋਵਾਂ 'ਤੇ ਤਰਕ ਸੈੱਟ ਕੀਤਾ ਗਿਆ ਹੈ। ਪ੍ਰਸ਼ਨ ਤਰਕ ਨੂੰ ਤਰਜੀਹ ਦਿੱਤੀ ਜਾਵੇਗੀ, ਅਤੇ ਸਭ ਤੋਂ ਦੂਰ ਦਾ ਪੰਨਾ ਦਿਖਾਇਆ ਜਾਵੇਗਾ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਇਹ ਵਿਵਹਾਰ ਤੁਹਾਡੇ ਇੱਛਤ ਪ੍ਰਵਾਹ ਨਾਲ ਮੇਲ ਖਾਂਦਾ ਹੈ।", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "ਇਸ ਪੰਨੇ ਵਿੱਚ ਪੰਨਾ ਪੱਧਰ ਅਤੇ ਪ੍ਰਸ਼ਨ ਪੱਧਰ ਦੋਵਾਂ 'ਤੇ ਤਰਕ ਸੈੱਟ ਕੀਤਾ ਗਿਆ ਹੈ। ਪ੍ਰਸ਼ਨ ਤਰਕ ਪੰਨਾ-ਪੱਧਰ ਦੇ ਤਰਕ ਨਾਲੋਂ ਪਹਿਲ ਕਰੇਗਾ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਇਹ ਵਿਵਹਾਰ ਤੁਹਾਡੇ ਇੱਛਤ ਪ੍ਰਵਾਹ ਨਾਲ ਮੇਲ ਖਾਂਦਾ ਹੈ।", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "ਇਸ ਪੰਨੇ ਵਿੱਚ ਪੰਨੇ ਅਤੇ ਪ੍ਰਸ਼ਨ ਪੱਧਰਾਂ ਦੋਵਾਂ 'ਤੇ ਤਰਕ ਸੈੱਟ ਕੀਤਾ ਗਿਆ ਹੈ, ਅਤੇ ਵੱਖ-ਵੱਖ ਪੰਨਿਆਂ 'ਤੇ ਸਿੱਧੇ ਕਈ ਸਵਾਲ ਹਨ। ਪ੍ਰਸ਼ਨ ਤਰਕ ਨੂੰ ਤਰਜੀਹ ਦਿੱਤੀ ਜਾਵੇਗੀ, ਅਤੇ ਸਭ ਤੋਂ ਦੂਰ ਦਾ ਪੰਨਾ ਦਿਖਾਇਆ ਜਾਵੇਗਾ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਇਹ ਵਿਵਹਾਰ ਤੁਹਾਡੇ ਇੱਛਤ ਪ੍ਰਵਾਹ ਨਾਲ ਮੇਲ ਖਾਂਦਾ ਹੈ।", "app.components.formBuilder.logicNoAnswer2": "ਜਵਾਬ ਨਹੀਂ ਦਿੱਤਾ", "app.components.formBuilder.logicPanelAnyOtherAnswer": "ਜੇ ਕੋਈ ਹੋਰ ਜਵਾਬ", "app.components.formBuilder.logicPanelNoAnswer": "ਜੇ ਜਵਾਬ ਨਾ ਦਿੱਤਾ", "app.components.formBuilder.logicValidationError": "ਤਰਕ ਪਿਛਲੇ ਪੰਨਿਆਂ ਨਾਲ ਲਿੰਕ ਨਹੀਂ ਹੋ ਸਕਦਾ", "app.components.formBuilder.longAnswer": "ਲੰਮਾ ਜਵਾਬ", "app.components.formBuilder.mapConfiguration": "ਨਕਸ਼ਾ ਸੰਰਚਨਾ", "app.components.formBuilder.mapping": "ਮੈਪਿੰਗ", "app.components.formBuilder.mappingNotInCurrentLicense": "ਸਰਵੇਖਣ ਮੈਪਿੰਗ ਵਿਸ਼ੇਸ਼ਤਾਵਾਂ ਤੁਹਾਡੇ ਮੌਜੂਦਾ ਲਾਇਸੰਸ ਵਿੱਚ ਸ਼ਾਮਲ ਨਹੀਂ ਹਨ। ਹੋਰ ਜਾਣਨ ਲਈ ਆਪਣੇ GovSuccess ਮੈਨੇਜਰ ਨਾਲ ਸੰਪਰਕ ਕਰੋ।", "app.components.formBuilder.matrix": "ਮੈਟ੍ਰਿਕਸ", "app.components.formBuilder.matrixSettings.columns": "ਕਾਲਮ", "app.components.formBuilder.matrixSettings.rows": "ਕਤਾਰਾਂ", "app.components.formBuilder.multipleChoice": "ਬਹੁ-ਚੋਣ", "app.components.formBuilder.multipleChoiceHelperText": "ਜੇਕਰ ਕਈ ਵਿਕਲਪ ਵੱਖ-ਵੱਖ ਪੰਨਿਆਂ 'ਤੇ ਲੈ ਜਾਂਦੇ ਹਨ ਅਤੇ ਭਾਗੀਦਾਰ ਇੱਕ ਤੋਂ ਵੱਧ ਚੁਣਦੇ ਹਨ, ਤਾਂ ਸਭ ਤੋਂ ਦੂਰ ਦਾ ਪੰਨਾ ਦਿਖਾਇਆ ਜਾਵੇਗਾ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਇਹ ਵਿਵਹਾਰ ਤੁਹਾਡੇ ਇੱਛਤ ਪ੍ਰਵਾਹ ਨਾਲ ਮੇਲ ਖਾਂਦਾ ਹੈ।", "app.components.formBuilder.multipleChoiceImage": "ਚਿੱਤਰ ਦੀ ਚੋਣ", "app.components.formBuilder.multiselect.maximum": "ਅਧਿਕਤਮ", "app.components.formBuilder.multiselect.minimum": "ਘੱਟੋ-ਘੱਟ", "app.components.formBuilder.neutral": "ਨਿਰਪੱਖ", "app.components.formBuilder.newField": "ਨਵਾਂ ਖੇਤਰ", "app.components.formBuilder.number": "ਨੰਬਰ", "app.components.formBuilder.ok": "ਠੀਕ ਹੈ", "app.components.formBuilder.open": "ਖੋਲ੍ਹੋ", "app.components.formBuilder.optional": "ਵਿਕਲਪਿਕ", "app.components.formBuilder.other": "ਹੋਰ", "app.components.formBuilder.otherOption": "\"ਹੋਰ\" ਵਿਕਲਪ", "app.components.formBuilder.otherOptionTooltip": "ਭਾਗੀਦਾਰਾਂ ਨੂੰ ਇੱਕ ਕਸਟਮ ਜਵਾਬ ਦਾਖਲ ਕਰਨ ਦੀ ਆਗਿਆ ਦਿਓ ਜੇਕਰ ਪ੍ਰਦਾਨ ਕੀਤੇ ਜਵਾਬ ਉਹਨਾਂ ਦੀ ਤਰਜੀਹ ਨਾਲ ਮੇਲ ਨਹੀਂ ਖਾਂਦੇ", "app.components.formBuilder.page": "ਪੰਨਾ", "app.components.formBuilder.pageCannotBeDeleted": "ਇਸ ਪੰਨੇ ਨੂੰ ਮਿਟਾਇਆ ਨਹੀਂ ਜਾ ਸਕਦਾ।", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "ਇਸ ਪੰਨੇ ਨੂੰ ਮਿਟਾਇਆ ਨਹੀਂ ਜਾ ਸਕਦਾ ਅਤੇ ਇਹ ਕਿਸੇ ਵੀ ਵਾਧੂ ਖੇਤਰ ਨੂੰ ਜੋੜਨ ਦੀ ਆਗਿਆ ਨਹੀਂ ਦਿੰਦਾ।", "app.components.formBuilder.pageRuleLabel": "ਅਗਲਾ ਪੰਨਾ ਹੈ:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "ਜੇਕਰ ਕੋਈ ਤਰਕ ਨਹੀਂ ਜੋੜਿਆ ਜਾਂਦਾ ਹੈ, ਤਾਂ ਫਾਰਮ ਆਪਣੇ ਆਮ ਵਹਾਅ ਦੀ ਪਾਲਣਾ ਕਰੇਗਾ। ਜੇਕਰ ਪੰਨੇ ਅਤੇ ਇਸਦੇ ਸਵਾਲਾਂ ਵਿੱਚ ਤਰਕ ਹੈ, ਤਾਂ ਪ੍ਰਸ਼ਨ ਤਰਕ ਨੂੰ ਤਰਜੀਹ ਦਿੱਤੀ ਜਾਵੇਗੀ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਇਹ ਤੁਹਾਡੇ ਇੱਛਤ ਪ੍ਰਵਾਹ ਨਾਲ ਇਕਸਾਰ ਹੈ ਹੋਰ ਜਾਣਕਾਰੀ ਲਈ, {supportPageLink}'ਤੇ ਜਾਓ", "app.components.formBuilder.preview": "ਝਲਕ:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "ਡਾਊਨਲੋਡ ਕੀਤੀ PDF 'ਤੇ ਸਹਿ-ਪ੍ਰਾਯੋਜਕ ਨਹੀਂ ਦਿਖਾਏ ਗਏ ਹਨ ਅਤੇ FormSync ਰਾਹੀਂ ਆਯਾਤ ਕਰਨ ਲਈ ਸਮਰਥਿਤ ਨਹੀਂ ਹਨ।", "app.components.formBuilder.printSupportTooltip.fileupload": "ਡਾਊਨਲੋਡ ਕੀਤੀ PDF 'ਤੇ ਫਾਈਲ ਅਪਲੋਡ ਸਵਾਲਾਂ ਨੂੰ ਅਸਮਰਥਿਤ ਵਜੋਂ ਦਿਖਾਇਆ ਗਿਆ ਹੈ ਅਤੇ FormSync ਰਾਹੀਂ ਆਯਾਤ ਕਰਨ ਲਈ ਸਮਰਥਿਤ ਨਹੀਂ ਹਨ।", "app.components.formBuilder.printSupportTooltip.mapping": "ਮੈਪਿੰਗ ਸਵਾਲ ਡਾਊਨਲੋਡ ਕੀਤੀ PDF 'ਤੇ ਦਿਖਾਏ ਗਏ ਹਨ, ਪਰ ਪਰਤਾਂ ਦਿਖਾਈ ਨਹੀਂ ਦੇਣਗੀਆਂ। ਮੈਪਿੰਗ ਸਵਾਲ FormSync ਰਾਹੀਂ ਆਯਾਤ ਕਰਨ ਲਈ ਸਮਰਥਿਤ ਨਹੀਂ ਹਨ।", "app.components.formBuilder.printSupportTooltip.matrix": "ਮੈਟ੍ਰਿਕਸ ਸਵਾਲ ਡਾਊਨਲੋਡ ਕੀਤੀ PDF 'ਤੇ ਦਿਖਾਏ ਗਏ ਹਨ ਪਰ ਵਰਤਮਾਨ ਵਿੱਚ FormSync ਰਾਹੀਂ ਆਯਾਤ ਲਈ ਸਮਰਥਿਤ ਨਹੀਂ ਹਨ।", "app.components.formBuilder.printSupportTooltip.page": "ਪੰਨੇ ਦੇ ਸਿਰਲੇਖ ਅਤੇ ਵਰਣਨ ਡਾਊਨਲੋਡ ਕੀਤੀ PDF ਵਿੱਚ ਇੱਕ ਭਾਗ ਸਿਰਲੇਖ ਵਜੋਂ ਦਿਖਾਏ ਗਏ ਹਨ।", "app.components.formBuilder.printSupportTooltip.ranking": "ਰੈਂਕਿੰਗ ਸਵਾਲ ਡਾਊਨਲੋਡ ਕੀਤੀ PDF 'ਤੇ ਦਿਖਾਏ ਗਏ ਹਨ ਪਰ ਵਰਤਮਾਨ ਵਿੱਚ FormSync ਰਾਹੀਂ ਆਯਾਤ ਲਈ ਸਮਰਥਿਤ ਨਹੀਂ ਹਨ।", "app.components.formBuilder.printSupportTooltip.topics2": "ਡਾਊਨਲੋਡ ਕੀਤੀ PDF 'ਤੇ ਟੈਗਸ ਨੂੰ ਅਸਮਰਥਿਤ ਵਜੋਂ ਦਿਖਾਇਆ ਗਿਆ ਹੈ ਅਤੇ FormSync ਰਾਹੀਂ ਆਯਾਤ ਕਰਨ ਲਈ ਸਮਰਥਿਤ ਨਹੀਂ ਹਨ।", "app.components.formBuilder.proposedBudget": "ਪ੍ਰਸਤਾਵਿਤ ਬਜਟ", "app.components.formBuilder.question": "ਸਵਾਲ", "app.components.formBuilder.questionCannotBeDeleted": "ਇਸ ਸਵਾਲ ਨੂੰ ਮਿਟਾਇਆ ਨਹੀਂ ਜਾ ਸਕਦਾ।", "app.components.formBuilder.questionDescriptionOptional": "ਸਵਾਲ ਦਾ ਵੇਰਵਾ (ਵਿਕਲਪਿਕ)", "app.components.formBuilder.questionTitle": "ਸਵਾਲ ਦਾ ਸਿਰਲੇਖ", "app.components.formBuilder.randomize": "ਰੈਂਡਮਾਈਜ਼ ਕਰੋ", "app.components.formBuilder.randomizeToolTip": "ਜਵਾਬਾਂ ਦਾ ਕ੍ਰਮ ਪ੍ਰਤੀ ਉਪਭੋਗਤਾ ਲਈ ਬੇਤਰਤੀਬ ਕੀਤਾ ਜਾਵੇਗਾ", "app.components.formBuilder.range": "ਰੇਂਜ", "app.components.formBuilder.ranking": "ਦਰਜਾਬੰਦੀ", "app.components.formBuilder.rating": "ਰੇਟਿੰਗ", "app.components.formBuilder.removeAnswer": "ਜਵਾਬ ਹਟਾਓ", "app.components.formBuilder.required": "ਲੋੜੀਂਦਾ ਹੈ", "app.components.formBuilder.requiredToggleLabel": "ਇਸ ਸਵਾਲ ਦਾ ਜਵਾਬ ਦੇਣਾ ਜ਼ਰੂਰੀ ਬਣਾਓ", "app.components.formBuilder.ruleForAnswerLabel": "ਜੇਕਰ ਜਵਾਬ ਹੈ:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "ਜੇਕਰ ਜਵਾਬਾਂ ਵਿੱਚ ਸ਼ਾਮਲ ਹਨ:", "app.components.formBuilder.save": "ਸੇਵ ਕਰੋ", "app.components.formBuilder.selectRangeTooltip": "ਆਪਣੇ ਪੈਮਾਨੇ ਲਈ ਵੱਧ ਤੋਂ ਵੱਧ ਮੁੱਲ ਚੁਣੋ।", "app.components.formBuilder.sentiment": "ਭਾਵਨਾ ਦਾ ਪੈਮਾਨਾ", "app.components.formBuilder.shapefileUpload": "<PERSON>sri ਸ਼ੇਪਫਾਈਲ ਅੱਪਲੋਡ", "app.components.formBuilder.shortAnswer": "ਛੋਟਾ ਜਵਾਬ", "app.components.formBuilder.showResponseToUsersToggleLabel": "ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਜਵਾਬ ਦਿਖਾਓ", "app.components.formBuilder.singleChoice": "ਸਿੰਗਲ ਚੋਣ", "app.components.formBuilder.staleDataErrorMessage2": "ਕੋਈ ਸਮੱਸਿਆ ਆਈ ਹੈ। ਇਹ ਇਨਪੁਟ ਫਾਰਮ ਹਾਲ ਹੀ ਵਿੱਚ ਕਿਤੇ ਹੋਰ ਸੁਰੱਖਿਅਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਇਹ ਇਸ ਲਈ ਹੋ ਸਕਦਾ ਹੈ ਕਿਉਂਕਿ ਤੁਸੀਂ ਜਾਂ ਕਿਸੇ ਹੋਰ ਉਪਭੋਗਤਾ ਨੇ ਇਸਨੂੰ ਕਿਸੇ ਹੋਰ ਬ੍ਰਾਊਜ਼ਰ ਵਿੰਡੋ ਵਿੱਚ ਸੰਪਾਦਨ ਲਈ ਖੋਲ੍ਹਿਆ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਨਵੀਨਤਮ ਫਾਰਮ ਪ੍ਰਾਪਤ ਕਰਨ ਲਈ ਪੰਨੇ ਨੂੰ ਤਾਜ਼ਾ ਕਰੋ ਅਤੇ ਫਿਰ ਆਪਣੇ ਬਦਲਾਅ ਦੁਬਾਰਾ ਕਰੋ।", "app.components.formBuilder.stronglyAgree": "ਪੂਰੀ ਤਰ੍ਹਾਂ ਸਹਿਮਤ ਹਾਂ", "app.components.formBuilder.stronglyDisagree": "ਜ਼ੋਰਦਾਰ ਅਸਹਿਮਤ", "app.components.formBuilder.supportArticleLinkText": "ਇਹ ਪੰਨਾ", "app.components.formBuilder.tags": "ਟੈਗਸ", "app.components.formBuilder.title": "ਸਿਰਲੇਖ", "app.components.formBuilder.toLabel": "ਨੂੰ", "app.components.formBuilder.unsavedChanges": "ਤੁਹਾਡੇ ਕੋਲ ਅਣਰੱਖਿਅਤ ਤਬਦੀਲੀਆਂ ਹਨ", "app.components.formBuilder.useCustomButton2": "ਕਸਟਮ ਪੇਜ ਬਟਨ ਦੀ ਵਰਤੋਂ ਕਰੋ", "app.components.formBuilder.veryBad": "ਬਹੁਤ ਬੁਰਾ", "app.components.formBuilder.veryGood": "ਬਹੁਤ ਅੱਛਾ", "app.components.ideas.similarIdeas.engageHere": "ਇੱਥੇ ਜੁੜੋ", "app.components.ideas.similarIdeas.noSimilarSubmissions": "ਕੋਈ ਮਿਲਦੀ-ਜੁਲਦੀ ਸਪੁਰਦਗੀ ਨਹੀਂ ਮਿਲੀ।", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "ਸਾਨੂੰ ਇਸੇ ਤਰ੍ਹਾਂ ਦੇ ਸਬਮਿਸ਼ਨ ਮਿਲੇ ਹਨ - ਉਹਨਾਂ ਨਾਲ ਜੁੜਨਾ ਉਹਨਾਂ ਨੂੰ ਮਜ਼ਬੂਤ ਬਣਾਉਣ ਵਿੱਚ ਮਦਦ ਕਰ ਸਕਦਾ ਹੈ!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "ਇਸੇ ਤਰ੍ਹਾਂ ਦੀਆਂ ਬੇਨਤੀਆਂ ਪਹਿਲਾਂ ਹੀ ਪੋਸਟ ਕੀਤੀਆਂ ਜਾ ਚੁੱਕੀਆਂ ਹਨ:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "ਇਸੇ ਤਰ੍ਹਾਂ ਦੀਆਂ ਸਪੁਰਦਗੀਆਂ ਦੀ ਭਾਲ ਕਰ ਰਿਹਾ ਹਾਂ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {ਇੱਕ ਦਿਨ ਤੋਂ ਘੱਟ} one {# ਦਿਨ} other {# ਦਿਨ}} ਬਾਕੀ", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  ਹਫ਼ਤੇ ਬਾਕੀ", "app.components.screenReaderCurrency.AED": "ਸੰਯੁਕਤ ਅਰਬ ਅਮੀਰਾਤ ਦਿਰਹਾਮ", "app.components.screenReaderCurrency.AFN": "ਅਫਗਾਨ ਅਫਗਾਨੀ", "app.components.screenReaderCurrency.ALL": "ਅਲਬਾਨੀਅਨ ਲੇਕ", "app.components.screenReaderCurrency.AMD": "ਅਰਮੀਨੀਆਈ ਡਰਾਮ", "app.components.screenReaderCurrency.ANG": "ਨੀਦਰਲੈਂਡਜ਼ ਐਂਟੀਲੀਅਨ ਗਿਲਡਰ", "app.components.screenReaderCurrency.AOA": "ਅੰਗੋਲਨ ਕਵਾਂਜ਼ਾ", "app.components.screenReaderCurrency.ARS": "ਅਰਜਨਟੀਨਾ ਪੇਸੋ", "app.components.screenReaderCurrency.AUD": "ਆਸਟ੍ਰੇਲੀਆਈ ਡਾਲਰ", "app.components.screenReaderCurrency.AWG": "ਅਰੁਬਨ ਫਲੋਰਿਨ", "app.components.screenReaderCurrency.AZN": "ਅਜ਼ਰਬਾਈਜਾਨੀ ਮਨਤ", "app.components.screenReaderCurrency.BAM": "ਬੋਸਨੀਆ-ਹਰਜ਼ੇਗੋਵੀਨਾ ਪਰਿਵਰਤਨਸ਼ੀਲ ਮਾਰਕ", "app.components.screenReaderCurrency.BBD": "ਬਾਰਬਾਡੀਅਨ ਡਾਲਰ", "app.components.screenReaderCurrency.BDT": "ਬੰਗਲਾਦੇਸ਼ੀ ਟਕਾ", "app.components.screenReaderCurrency.BGN": "ਬਲਗੇਰੀਅਨ ਲੇਵ", "app.components.screenReaderCurrency.BHD": "ਬਹਿਰੀਨ ਦਿਨਾਰ", "app.components.screenReaderCurrency.BIF": "ਬੁਰੂੰਡੀਅਨ ਫ੍ਰੈਂਕ", "app.components.screenReaderCurrency.BMD": "ਬਰਮੂਡੀਅਨ ਡਾਲਰ", "app.components.screenReaderCurrency.BND": "ਬਰੂਨੇਈ ਡਾਲਰ", "app.components.screenReaderCurrency.BOB": "ਬੋਲੀਵੀਅਨ ਬੋਲੀਵੀਆਨੋ", "app.components.screenReaderCurrency.BOV": "ਬੋਲੀਵੀਆਈ Mvdol", "app.components.screenReaderCurrency.BRL": "ਬ੍ਰਾਜ਼ੀਲੀਅਨ ਰੀਅਲ", "app.components.screenReaderCurrency.BSD": "ਬਹਾਮੀਅਨ ਡਾਲਰ", "app.components.screenReaderCurrency.BTN": "ਭੂਟਾਨੀ <PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BWP": "ਬੋਟਸਵਾਨਨ ਪੁਲਾ", "app.components.screenReaderCurrency.BYR": "ਬੇਲਾਰੂਸੀ ਰੂਬਲ", "app.components.screenReaderCurrency.BZD": "ਬੇਲੀਜ਼ ਡਾਲਰ", "app.components.screenReaderCurrency.CAD": "ਕੈਨੇਡੀਅਨ ਡਾਲਰ", "app.components.screenReaderCurrency.CDF": "ਕਾਂਗੋਲੀਜ਼ ਫ੍ਰੈਂਕ", "app.components.screenReaderCurrency.CHE": "WIR ਯੂਰੋ", "app.components.screenReaderCurrency.CHF": "ਸਵਿਸ ਫ੍ਰੈਂਕ", "app.components.screenReaderCurrency.CHW": "WIR ਫ੍ਰੈਂਕ", "app.components.screenReaderCurrency.CLF": "ਚਿਲੀ ਖਾਤੇ ਦੀ ਇਕਾਈ (UF)", "app.components.screenReaderCurrency.CLP": "ਚਿਲੀ ਪੇਸੋ", "app.components.screenReaderCurrency.CNY": "ਚੀਨੀ ਯੂਆਨ", "app.components.screenReaderCurrency.COP": "ਕੋਲੰਬੀਅਨ ਪੇਸੋ", "app.components.screenReaderCurrency.COU": "ਯੂਨੀਡਾਡ ਡੀ ਵੈਲੋਰ ਰੀਅਲ", "app.components.screenReaderCurrency.CRC": "ਕੋਸਟਾ ਰੀਕਨ ਕੋਲੋਨ", "app.components.screenReaderCurrency.CRE": "ਕ੍ਰੈਡਿਟ", "app.components.screenReaderCurrency.CUC": "ਕਿਊਬਨ ਪਰਿਵਰਤਨਸ਼ੀਲ ਪੇਸੋ", "app.components.screenReaderCurrency.CUP": "ਕਿਊਬਨ ਪੇਸੋ", "app.components.screenReaderCurrency.CVE": "ਕੇਪ ਵਰਡੀਅਨ ਐਸਕੂਡੋ", "app.components.screenReaderCurrency.CZK": "ਚੈੱਕ ਕੋਰੁਨਾ", "app.components.screenReaderCurrency.DJF": "ਜਿਬੂਟੀਅਨ ਫ੍ਰੈਂਕ", "app.components.screenReaderCurrency.DKK": "ਡੈਨਿਸ਼ ਕ੍ਰੋਨ", "app.components.screenReaderCurrency.DOP": "ਡੋਮਿਨਿਕਨ ਪੇਸੋ", "app.components.screenReaderCurrency.DZD": "ਅਲਜੀਰੀਅਨ ਦਿਨਾਰ", "app.components.screenReaderCurrency.EGP": "ਮਿਸਰੀ ਪੌਂਡ", "app.components.screenReaderCurrency.ERN": "ਏਰੀਟਰੀਅਨ ਨਕਫਾ", "app.components.screenReaderCurrency.ETB": "ਇਥੋਪੀਅਨ ਬਿਰਰ", "app.components.screenReaderCurrency.EUR": "ਯੂਰੋ", "app.components.screenReaderCurrency.FJD": "ਫਿਜੀਅਨ ਡਾਲਰ", "app.components.screenReaderCurrency.FKP": "ਫਾਕਲੈਂਡ ਟਾਪੂ ਪੌਂਡ", "app.components.screenReaderCurrency.GBP": "ਬ੍ਰਿਟਿਸ਼ ਪਾਉਂਡ", "app.components.screenReaderCurrency.GEL": "ਜਾਰਜੀਅਨ ਲਾਰੀ", "app.components.screenReaderCurrency.GHS": "ਘਾਨਾਈ ਸੇਡੀ", "app.components.screenReaderCurrency.GIP": "ਜਿਬਰਾਲਟਰ ਪੌਂਡ", "app.components.screenReaderCurrency.GMD": "ਗੈਂਬੀਅਨ ਦਲਾਸੀ", "app.components.screenReaderCurrency.GNF": "ਗਿੰਨੀ ਫ੍ਰੈਂਕ", "app.components.screenReaderCurrency.GTQ": "ਗੁਆਟੇਮਾਲਾ Quetzal", "app.components.screenReaderCurrency.GYD": "ਗੁਆਨੀਜ਼ ਡਾਲਰ", "app.components.screenReaderCurrency.HKD": "ਹਾਂਗਕਾਂਗ ਡਾਲਰ", "app.components.screenReaderCurrency.HNL": "ਹੋਂਡੂਰਨ ਲੈਮਪੀਰਾ", "app.components.screenReaderCurrency.HRK": "ਕਰੋਸ਼ੀਅਨ ਕੁਨਾ", "app.components.screenReaderCurrency.HTG": "ਹੈਤੀਆਈ ਗੋਰਡੇ", "app.components.screenReaderCurrency.HUF": "ਹੰਗਰੀਆਈ ਫੋਰਿੰਟ", "app.components.screenReaderCurrency.IDR": "ਇੰਡੋਨੇਸ਼ੀਆਈ ਰੁਪਿਆ", "app.components.screenReaderCurrency.ILS": "ਇਜ਼ਰਾਈਲੀ ਨਿਊ ਸ਼ੇਕੇਲ", "app.components.screenReaderCurrency.INR": "ਭਾਰਤੀ ਰੁਪਿਆ", "app.components.screenReaderCurrency.IQD": "ਇਰਾਕੀ ਦਿਨਾਰ", "app.components.screenReaderCurrency.IRR": "ਈਰਾਨੀ ਰਿਆਲ", "app.components.screenReaderCurrency.ISK": "ਆਈਸਲੈਂਡਿਕ ਕਰੋਨਾ", "app.components.screenReaderCurrency.JMD": "ਜਮੈਕਨ ਡਾਲਰ", "app.components.screenReaderCurrency.JOD": "ਜਾਰਡਨ ਦੀਨਾਰ", "app.components.screenReaderCurrency.JPY": "ਜਾਪਾਨੀ ਯੇਨ", "app.components.screenReaderCurrency.KES": "ਕੀਨੀਆ ਸ਼ਿਲਿੰਗ", "app.components.screenReaderCurrency.KGS": "ਕਿਰਗਿਸਤਾਨੀ ਸੋਮ", "app.components.screenReaderCurrency.KHR": "ਕੰਬੋਡੀਅਨ ਰੀਲ", "app.components.screenReaderCurrency.KMF": "ਕੋਮੋਰੀਅਨ ਫ੍ਰੈਂਕ", "app.components.screenReaderCurrency.KPW": "ਉੱਤਰੀ ਕੋਰੀਆਈ ਵੋਨ", "app.components.screenReaderCurrency.KRW": "ਦੱਖਣੀ ਕੋਰੀਆਈ ਵੌਨ", "app.components.screenReaderCurrency.KWD": "ਕੁਵੈਤੀ ਦਿਨਾਰ", "app.components.screenReaderCurrency.KYD": "ਕੇਮੈਨ ਟਾਪੂ ਡਾਲਰ", "app.components.screenReaderCurrency.KZT": "ਕਜ਼ਾਕਿਸਤਾਨੀ ਟੇਂਗੇ", "app.components.screenReaderCurrency.LAK": "ਲਾਓ ਕਿਪ", "app.components.screenReaderCurrency.LBP": "ਲੇਬਨਾਨੀ ਪੌਂਡ", "app.components.screenReaderCurrency.LKR": "ਸ਼੍ਰੀਲੰਕਾਈ ਰੁਪਿਆ", "app.components.screenReaderCurrency.LRD": "ਲਾਇਬੇਰੀਅਨ ਡਾਲਰ", "app.components.screenReaderCurrency.LSL": "ਲੈਸੋਥੋ ਲੋਟੀ", "app.components.screenReaderCurrency.LTL": "ਲਿਥੁਆਨੀਅਨ ਲਿਟਾਸ", "app.components.screenReaderCurrency.LVL": "ਲਾਤਵੀਅਨ ਲੈਟਸ", "app.components.screenReaderCurrency.LYD": "ਲੀਬੀਆ ਦੀਨਾਰ", "app.components.screenReaderCurrency.MAD": "ਮੋਰੱਕੋ ਦੇ ਦਿਰਹਾਮ", "app.components.screenReaderCurrency.MDL": "ਮੋਲਡੋਵਨ ਲਿਊ", "app.components.screenReaderCurrency.MGA": "ਮਾਲਾਗਾਸੀ ਏਰੀਰੀ", "app.components.screenReaderCurrency.MKD": "ਮੈਸੇਡੋਨੀਅਨ ਡੇਨਾਰ", "app.components.screenReaderCurrency.MMK": "ਮਿਆਂਮਾਰ ਕਯਾਤ", "app.components.screenReaderCurrency.MNT": "ਮੰਗੋਲੀਆਈ ਟਾਗਰੋਗ", "app.components.screenReaderCurrency.MOP": "ਮੈਕਨੀਜ਼ ਪਟਾਕਾ", "app.components.screenReaderCurrency.MRO": "ਮੌਰੀਟਾਨੀਅਨ ਓਗੁਈਆ", "app.components.screenReaderCurrency.MUR": "ਮੌਰੀਸ਼ੀਅਨ ਰੁਪਿਆ", "app.components.screenReaderCurrency.MVR": "ਮਾਲਦੀਵੀਅਨ ਰੁਫੀਆ", "app.components.screenReaderCurrency.MWK": "ਮਲਾਵੀਅਨ ਕਵਾਚਾ", "app.components.screenReaderCurrency.MXN": "ਮੈਕਸੀਕਨ ਪੇਸੋ", "app.components.screenReaderCurrency.MXV": "ਮੈਕਸੀਕਨ ਯੂਨੀਡਾਡ ਡੀ ਇਨਵਰਸ਼ਨ (UDI)", "app.components.screenReaderCurrency.MYR": "ਮਲੇਸ਼ੀਅਨ ਰਿੰਗਿਟ", "app.components.screenReaderCurrency.MZN": "ਮੋਜ਼ਾਮਬੀਕਨ ਮੈਟਿਕਲ", "app.components.screenReaderCurrency.NAD": "ਨਾਮੀਬੀਆਈ ਡਾਲਰ", "app.components.screenReaderCurrency.NGN": "ਨਾਈਜੀਰੀਅਨ ਨਾਇਰਾ", "app.components.screenReaderCurrency.NIO": "ਨਿਕਾਰਾਗੁਆਨ ਕੋਰਡੋਬਾ", "app.components.screenReaderCurrency.NOK": "ਨਾਰਵੇਜਿਅਨ ਕ੍ਰੋਨ", "app.components.screenReaderCurrency.NPR": "ਨੇਪਾਲੀ ਰੁਪਿਆ", "app.components.screenReaderCurrency.NZD": "ਨਿਊਜ਼ੀਲੈਂਡ ਡਾਲਰ", "app.components.screenReaderCurrency.OMR": "ਓਮਾਨੀ ਰਿਆਲ", "app.components.screenReaderCurrency.PAB": "ਪਨਾਮੇਨੀਅਨ ਬਾਲਬੋਆ", "app.components.screenReaderCurrency.PEN": "ਪੇਰੂਵੀਅਨ ਸੋਲ", "app.components.screenReaderCurrency.PGK": "ਪਾਪੂਆ ਨਿਊ ਗਿਨੀ ਕਿਨਾ", "app.components.screenReaderCurrency.PHP": "ਫਿਲੀਪੀਨ ਪੇਸੋ", "app.components.screenReaderCurrency.PKR": "ਪਾਕਿਸਤਾਨੀ ਰੁਪਿਆ", "app.components.screenReaderCurrency.PLN": "ਪੋਲਿਸ਼ ਜ਼ਲੋਟੀ", "app.components.screenReaderCurrency.PYG": "ਪੈਰਾਗੁਏਨ ਗੁਆਰਾਨੀ", "app.components.screenReaderCurrency.QAR": "ਕਤਾਰੀ ਰਿਆਲ", "app.components.screenReaderCurrency.RON": "ਰੋਮਾਨੀਅਨ ਲਿਊ", "app.components.screenReaderCurrency.RSD": "ਸਰਬੀਆਈ ਦਿਨਾਰ", "app.components.screenReaderCurrency.RUB": "ਰੂਸੀ ਰੂਬਲ", "app.components.screenReaderCurrency.RWF": "ਰਵਾਂਡਾ ਫ੍ਰੈਂਕ", "app.components.screenReaderCurrency.SAR": "ਸਾਊਦੀ ਰਿਆਲ", "app.components.screenReaderCurrency.SBD": "ਸੋਲੋਮਨ ਟਾਪੂ ਡਾਲਰ", "app.components.screenReaderCurrency.SCR": "ਸੇਸ਼ੇਲੋਇਸ ਰੁਪਿਆ", "app.components.screenReaderCurrency.SDG": "ਸੁਡਾਨੀ ਪੌਂਡ", "app.components.screenReaderCurrency.SEK": "ਸਵੀਡਿਸ਼ ਕਰੋਨਾ", "app.components.screenReaderCurrency.SGD": "ਸਿੰਗਾਪੁਰ ਡਾਲਰ", "app.components.screenReaderCurrency.SHP": "ਸੇਂਟ ਹੇਲੇਨਾ ਪਾਊਂਡ", "app.components.screenReaderCurrency.SLL": "ਸੀਅਰਾ ਲਿਓਨੀਅਨ ਲਿਓਨ", "app.components.screenReaderCurrency.SOS": "ਸੋਮਾਲੀ ਸ਼ਿਲਿੰਗ", "app.components.screenReaderCurrency.SRD": "ਸੂਰੀਨਾਮੀ ਡਾਲਰ", "app.components.screenReaderCurrency.SSP": "ਦੱਖਣੀ ਸੂਡਾਨੀ ਪੌਂਡ", "app.components.screenReaderCurrency.STD": "ਸਾਓ ਟੋਮੇ ਅਤੇ ਪ੍ਰਿੰਸੀਪ ਡੋਬਰਾ", "app.components.screenReaderCurrency.SYP": "ਸੀਰੀਆਈ ਪਾਊਂਡ", "app.components.screenReaderCurrency.SZL": "ਸਵਾਜ਼ੀ ਲਿਲਾਂਗੇਨੀ", "app.components.screenReaderCurrency.THB": "ਥਾਈ ਬਾਠ", "app.components.screenReaderCurrency.TJS": "ਤਾਜਿਕਸਤਾਨੀ ਸੋਮੋਨੀ", "app.components.screenReaderCurrency.TMT": "ਤੁਰਕਮੇਨਿਸਤਾਨੀ ਮਨਤ", "app.components.screenReaderCurrency.TND": "ਟਿਊਨੀਸ਼ੀਅਨ ਦਿਨਾਰ", "app.components.screenReaderCurrency.TOK": "ਟੋਕਨ", "app.components.screenReaderCurrency.TOP": "ਟੋਂਗਨ ਪਾ'ਆਂਗਾ", "app.components.screenReaderCurrency.TRY": "ਤੁਰਕੀ ਲੀਰਾ", "app.components.screenReaderCurrency.TTD": "ਤ੍ਰਿਨੀਦਾਦ ਅਤੇ ਟੋਬੈਗੋ ਡਾਲਰ", "app.components.screenReaderCurrency.TWD": "ਨਵਾਂ ਤਾਈਵਾਨ ਡਾਲਰ", "app.components.screenReaderCurrency.TZS": "ਤਨਜ਼ਾਨੀਅਨ ਸ਼ਿਲਿੰਗ", "app.components.screenReaderCurrency.UAH": "ਯੂਕਰੇਨੀ ਰਿਵਨੀਆ", "app.components.screenReaderCurrency.UGX": "ਯੂਗਾਂਡਾ ਸ਼ਿਲਿੰਗ", "app.components.screenReaderCurrency.USD": "ਸੰਯੁਕਤ ਰਾਜ ਡਾਲਰ", "app.components.screenReaderCurrency.USN": "ਸੰਯੁਕਤ ਰਾਜ ਡਾਲਰ (ਅਗਲਾ ਦਿਨ)", "app.components.screenReaderCurrency.USS": "ਸੰਯੁਕਤ ਰਾਜ ਡਾਲਰ (ਉਸੇ ਦਿਨ)", "app.components.screenReaderCurrency.UYI": "ਉਰੂਗਵੇ ਪੇਸੋ en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "ਉਰੂਗੁਏਆਈ ਪੇਸੋ", "app.components.screenReaderCurrency.UZS": "ਉਜ਼ਬੇਕਿਸਤਾਨੀ ਸੋਮ", "app.components.screenReaderCurrency.VEF": "ਵੈਨੇਜ਼ੁਏਲਾ ਬੋਲਿਵਰ", "app.components.screenReaderCurrency.VND": "ਵੀਅਤਨਾਮੀ Đồng", "app.components.screenReaderCurrency.VUV": "ਵਣੁ ਵਟੁ", "app.components.screenReaderCurrency.WST": "ਸਮੋਣ ਤਾਲਾ", "app.components.screenReaderCurrency.XAF": "ਮੱਧ ਅਫ਼ਰੀਕੀ CFA ਫ੍ਰੈਂਕ", "app.components.screenReaderCurrency.XAG": "ਚਾਂਦੀ (ਇੱਕ ਟਰੌਏ ਔਂਸ)", "app.components.screenReaderCurrency.XAU": "ਸੋਨਾ (ਇਕ ਟਰਾਯ ਔਂਸ)", "app.components.screenReaderCurrency.XBA": "ਯੂਰਪੀਅਨ ਕੰਪੋਜ਼ਿਟ ਯੂਨਿਟ (EURCO)", "app.components.screenReaderCurrency.XBB": "ਯੂਰਪੀ ਮੁਦਰਾ ਇਕਾਈ (EMU-6)", "app.components.screenReaderCurrency.XBC": "ਖਾਤੇ 9 ਦੀ ਯੂਰਪੀ ਇਕਾਈ (EUA-9)", "app.components.screenReaderCurrency.XBD": "ਖਾਤੇ 17 ਦੀ ਯੂਰਪੀ ਇਕਾਈ (EUA-17)", "app.components.screenReaderCurrency.XCD": "ਪੂਰਬੀ ਕੈਰੇਬੀਅਨ ਡਾਲਰ", "app.components.screenReaderCurrency.XDR": "ਵਿਸ਼ੇਸ਼ ਡਰਾਇੰਗ ਅਧਿਕਾਰ", "app.components.screenReaderCurrency.XFU": "UIC ਫ੍ਰੈਂਕ", "app.components.screenReaderCurrency.XOF": "ਪੱਛਮੀ ਅਫ਼ਰੀਕੀ CFA ਫ੍ਰੈਂਕ", "app.components.screenReaderCurrency.XPD": "ਪੈਲੇਡੀਅਮ (ਇੱਕ ਟਰੌਏ ਔਂਸ)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "ਪਲੈਟੀਨਮ (ਇੱਕ ਟਰੌਏ ਔਂਸ)", "app.components.screenReaderCurrency.XTS": "ਕੋਡ ਖਾਸ ਤੌਰ 'ਤੇ ਜਾਂਚ ਦੇ ਉਦੇਸ਼ਾਂ ਲਈ ਰਾਖਵੇਂ ਹਨ", "app.components.screenReaderCurrency.XXX": "ਕੋਈ ਮੁਦਰਾ ਨਹੀਂ", "app.components.screenReaderCurrency.YER": "ਯਮੇਨੀ ਰਿਆਲ", "app.components.screenReaderCurrency.ZAR": "ਦੱਖਣੀ ਅਫ਼ਰੀਕੀ ਰੈਂਡ", "app.components.screenReaderCurrency.ZMW": "ਜ਼ੈਂਬੀਅਨ ਕਵਾਚਾ", "app.components.screenReaderCurrency.amount": "ਰਕਮ", "app.components.screenReaderCurrency.currency": "ਮੁਦਰਾ", "app.components.trendIndicator.lastQuarter2": "ਪਿਛਲੀ ਤਿਮਾਹੀ", "app.containers.AccessibilityStatement.applicability": "ਇਹ ਪਹੁੰਚਯੋਗਤਾ ਬਿਆਨ ਇੱਕ {demoPlatformLink} 'ਤੇ ਲਾਗੂ ਹੁੰਦਾ ਹੈ ਜੋ ਇਸ ਵੈੱਬਸਾਈਟ ਦਾ ਪ੍ਰਤੀਨਿਧ ਹੈ; ਇਹ ਇੱਕੋ ਸਰੋਤ ਕੋਡ ਦੀ ਵਰਤੋਂ ਕਰਦਾ ਹੈ ਅਤੇ ਉਸੇ ਤਰ੍ਹਾਂ ਦੀ ਕਾਰਜਸ਼ੀਲਤਾ ਹੈ।", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "ਮੁਲਾਂਕਣ ਵਿਧੀ", "app.containers.AccessibilityStatement.assesmentText2022": "ਇਸ ਸਾਈਟ ਦੀ ਪਹੁੰਚਯੋਗਤਾ ਦਾ ਮੁਲਾਂਕਣ ਕਿਸੇ ਬਾਹਰੀ ਇਕਾਈ ਦੁਆਰਾ ਕੀਤਾ ਗਿਆ ਸੀ ਜੋ ਡਿਜ਼ਾਈਨ ਅਤੇ ਵਿਕਾਸ ਪ੍ਰਕਿਰਿਆ ਵਿੱਚ ਸ਼ਾਮਲ ਨਹੀਂ ਹੈ। ਉਪਰੋਕਤ {demoPlatformLink} ਦੀ ਪਾਲਣਾ ਨੂੰ ਇਸ {statusPageLink}'ਤੇ ਪਛਾਣਿਆ ਜਾ ਸਕਦਾ ਹੈ।", "app.containers.AccessibilityStatement.changePreferencesButtonText": "ਤੁਸੀਂ ਆਪਣੀਆਂ ਤਰਜੀਹਾਂ ਬਦਲ ਸਕਦੇ ਹੋ", "app.containers.AccessibilityStatement.changePreferencesText": "ਕਿਸੇ ਵੀ ਸਮੇਂ, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "ਅਨੁਕੂਲਤਾ ਅਪਵਾਦ", "app.containers.AccessibilityStatement.conformanceStatus": "ਅਨੁਕੂਲਤਾ ਸਥਿਤੀ", "app.containers.AccessibilityStatement.contentConformanceExceptions": "ਅਸੀਂ ਆਪਣੀ ਸਮਗਰੀ ਨੂੰ ਸਾਰਿਆਂ ਲਈ ਸ਼ਾਮਲ ਕਰਨ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ। ਹਾਲਾਂਕਿ, ਕੁਝ ਸਥਿਤੀਆਂ ਵਿੱਚ ਪਲੇਟਫਾਰਮ 'ਤੇ ਪਹੁੰਚਯੋਗ ਸਮੱਗਰੀ ਹੋ ਸਕਦੀ ਹੈ ਜਿਵੇਂ ਕਿ ਹੇਠਾਂ ਦੱਸਿਆ ਗਿਆ ਹੈ:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "ਡੈਮੋ ਵੈੱਬਸਾਈਟ", "app.containers.AccessibilityStatement.email": "ਈਮੇਲ:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "ਏਮਬੈਡਡ ਸਰਵੇਖਣ ਟੂਲ", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "ਏਮਬੇਡ ਕੀਤੇ ਸਰਵੇਖਣ ਟੂਲ ਜੋ ਇਸ ਪਲੇਟਫਾਰਮ 'ਤੇ ਵਰਤੋਂ ਲਈ ਉਪਲਬਧ ਹਨ, ਤੀਜੀ-ਧਿਰ ਦੇ ਸੌਫਟਵੇਅਰ ਹਨ ਅਤੇ ਹੋ ਸਕਦਾ ਹੈ ਕਿ ਪਹੁੰਚਯੋਗ ਨਾ ਹੋਵੇ।", "app.containers.AccessibilityStatement.exception_1": "ਸਾਡੇ ਡਿਜੀਟਲ ਸ਼ਮੂਲੀਅਤ ਪਲੇਟਫਾਰਮ ਵਿਅਕਤੀਆਂ ਅਤੇ ਸੰਸਥਾਵਾਂ ਦੁਆਰਾ ਪੋਸਟ ਕੀਤੀ ਉਪਭੋਗਤਾ ਦੁਆਰਾ ਤਿਆਰ ਕੀਤੀ ਸਮੱਗਰੀ ਦੀ ਸਹੂਲਤ ਦਿੰਦੇ ਹਨ। ਇਹ ਸੰਭਵ ਹੈ ਕਿ PDF, ਚਿੱਤਰ ਜਾਂ ਮਲਟੀ-ਮੀਡੀਆ ਸਮੇਤ ਹੋਰ ਫਾਈਲ ਕਿਸਮਾਂ ਨੂੰ ਪਲੇਟਫਾਰਮ 'ਤੇ ਅਟੈਚਮੈਂਟ ਵਜੋਂ ਅੱਪਲੋਡ ਕੀਤਾ ਜਾਂਦਾ ਹੈ ਜਾਂ ਪਲੇਟਫਾਰਮ ਉਪਭੋਗਤਾਵਾਂ ਦੁਆਰਾ ਟੈਕਸਟ ਖੇਤਰਾਂ ਵਿੱਚ ਜੋੜਿਆ ਜਾਂਦਾ ਹੈ। ਇਹ ਦਸਤਾਵੇਜ਼ ਪੂਰੀ ਤਰ੍ਹਾਂ ਪਹੁੰਚਯੋਗ ਨਹੀਂ ਹੋ ਸਕਦੇ ਹਨ।", "app.containers.AccessibilityStatement.feedbackProcessIntro": "ਅਸੀਂ ਇਸ ਸਾਈਟ ਦੀ ਪਹੁੰਚਯੋਗਤਾ 'ਤੇ ਤੁਹਾਡੇ ਫੀਡਬੈਕ ਦਾ ਸੁਆਗਤ ਕਰਦੇ ਹਾਂ। ਕਿਰਪਾ ਕਰਕੇ ਹੇਠਾਂ ਦਿੱਤੇ ਤਰੀਕਿਆਂ ਵਿੱਚੋਂ ਇੱਕ ਦੁਆਰਾ ਸਾਡੇ ਨਾਲ ਸੰਪਰਕ ਕਰੋ:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "ਫੀਡਬੈਕ ਪ੍ਰਕਿਰਿਆ", "app.containers.AccessibilityStatement.govocalAddress2022": "ਬੁਲੇਵਾਰਡ ਪਚੇਕੋ 34, 1000 ਬ੍ਰਸੇਲਜ਼, ਬੈਲਜੀਅਮ", "app.containers.AccessibilityStatement.headTitle": "ਪਹੁੰਚਯੋਗਤਾ ਬਿਆਨ | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} ਇੱਕ ਅਜਿਹਾ ਪਲੇਟਫਾਰਮ ਪ੍ਰਦਾਨ ਕਰਨ ਲਈ ਵਚਨਬੱਧ ਹੈ ਜੋ ਤਕਨਾਲੋਜੀ ਜਾਂ ਯੋਗਤਾ ਦੀ ਪਰਵਾਹ ਕੀਤੇ ਬਿਨਾਂ, ਸਾਰੇ ਉਪਭੋਗਤਾਵਾਂ ਲਈ ਪਹੁੰਚਯੋਗ ਹੈ। ਸਾਰੇ ਉਪਭੋਗਤਾਵਾਂ ਲਈ ਸਾਡੇ ਪਲੇਟਫਾਰਮਾਂ ਦੀ ਪਹੁੰਚਯੋਗਤਾ ਅਤੇ ਉਪਯੋਗਤਾ ਨੂੰ ਵੱਧ ਤੋਂ ਵੱਧ ਕਰਨ ਲਈ ਸਾਡੇ ਚੱਲ ਰਹੇ ਯਤਨਾਂ ਵਿੱਚ ਵਰਤਮਾਨ ਸੰਬੰਧਿਤ ਪਹੁੰਚਯੋਗਤਾ ਮਾਪਦੰਡਾਂ ਦੀ ਪਾਲਣਾ ਕੀਤੀ ਜਾਂਦੀ ਹੈ।", "app.containers.AccessibilityStatement.mapping": "ਮੈਪਿੰਗ", "app.containers.AccessibilityStatement.mapping_1": "ਪਲੇਟਫਾਰਮ 'ਤੇ ਨਕਸ਼ੇ ਅੰਸ਼ਕ ਤੌਰ 'ਤੇ ਪਹੁੰਚਯੋਗਤਾ ਮਿਆਰਾਂ ਨੂੰ ਪੂਰਾ ਕਰਦੇ ਹਨ। ਨਕਸ਼ੇ ਦੀ ਹੱਦ, ਜ਼ੂਮ, ਅਤੇ UI ਵਿਜੇਟਸ ਨੂੰ ਨਕਸ਼ੇ ਦੇਖਣ ਵੇਲੇ ਕੀਬੋਰਡ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਨਿਯੰਤਰਿਤ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ। ਪ੍ਰਸ਼ਾਸਕ ਬੈਕ ਆਫਿਸ ਵਿੱਚ ਮੈਪ ਲੇਅਰਾਂ ਦੀ ਸ਼ੈਲੀ ਨੂੰ ਵੀ ਸੰਰਚਿਤ ਕਰ ਸਕਦੇ ਹਨ, ਜਾਂ Esri ਏਕੀਕਰਣ ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹੋਏ, ਵਧੇਰੇ ਪਹੁੰਚਯੋਗ ਰੰਗ ਪੈਲੇਟ ਅਤੇ ਪ੍ਰਤੀਕ ਵਿਗਿਆਨ ਬਣਾਉਣ ਲਈ। ਵੱਖ-ਵੱਖ ਲਾਈਨਾਂ ਜਾਂ ਬਹੁਭੁਜ ਸ਼ੈਲੀਆਂ (ਜਿਵੇਂ ਕਿ ਡੈਸ਼ਡ ਲਾਈਨਾਂ) ਦੀ ਵਰਤੋਂ ਕਰਨਾ ਜਿੱਥੇ ਵੀ ਸੰਭਵ ਹੋਵੇ, ਨਕਸ਼ੇ ਦੀਆਂ ਪਰਤਾਂ ਨੂੰ ਵੱਖ ਕਰਨ ਵਿੱਚ ਮਦਦ ਕਰੇਗਾ, ਅਤੇ ਹਾਲਾਂਕਿ ਇਸ ਸਮੇਂ ਸਾਡੇ ਪਲੇਟਫਾਰਮ ਦੇ ਅੰਦਰ ਅਜਿਹੀ ਸਟਾਈਲ ਨੂੰ ਸੰਰਚਿਤ ਨਹੀਂ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ, ਇਸ ਨੂੰ ਸੰਰਚਿਤ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ ਜੇਕਰ Esri ਏਕੀਕਰਣ ਨਾਲ ਨਕਸ਼ੇ ਦੀ ਵਰਤੋਂ ਕੀਤੀ ਜਾ ਰਹੀ ਹੈ।", "app.containers.AccessibilityStatement.mapping_2": "ਪਲੇਟਫਾਰਮ ਵਿੱਚ ਨਕਸ਼ੇ ਪੂਰੀ ਤਰ੍ਹਾਂ ਪਹੁੰਚਯੋਗ ਨਹੀਂ ਹਨ ਕਿਉਂਕਿ ਉਹ ਸਕ੍ਰੀਨ ਰੀਡਰ ਦੀ ਵਰਤੋਂ ਕਰਨ ਵਾਲੇ ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਬੇਸਮੈਪ, ਨਕਸ਼ੇ ਦੀਆਂ ਪਰਤਾਂ, ਜਾਂ ਡੇਟਾ ਵਿੱਚ ਰੁਝਾਨਾਂ ਨੂੰ ਸੁਣਨ ਵਿੱਚ ਨਹੀਂ ਪੇਸ਼ ਕਰਦੇ ਹਨ। ਪੂਰੀ ਤਰ੍ਹਾਂ ਪਹੁੰਚਯੋਗ ਨਕਸ਼ਿਆਂ ਨੂੰ ਨਕਸ਼ੇ ਦੀਆਂ ਪਰਤਾਂ ਨੂੰ ਸੁਣਨ ਵਿੱਚ ਪੇਸ਼ ਕਰਨ ਅਤੇ ਡੇਟਾ ਵਿੱਚ ਕਿਸੇ ਵੀ ਸੰਬੰਧਿਤ ਰੁਝਾਨਾਂ ਦਾ ਵਰਣਨ ਕਰਨ ਦੀ ਲੋੜ ਹੋਵੇਗੀ। ਇਸ ਤੋਂ ਇਲਾਵਾ, ਸਰਵੇਖਣਾਂ ਵਿੱਚ ਰੇਖਾ ਅਤੇ ਬਹੁਭੁਜ ਨਕਸ਼ਾ ਡਰਾਇੰਗ ਪਹੁੰਚਯੋਗ ਨਹੀਂ ਹੈ ਕਿਉਂਕਿ ਕੀਬੋਰਡ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਆਕਾਰ ਨਹੀਂ ਬਣਾਏ ਜਾ ਸਕਦੇ ਹਨ। ਤਕਨੀਕੀ ਜਟਿਲਤਾ ਦੇ ਕਾਰਨ ਇਸ ਸਮੇਂ ਵਿਕਲਪਿਕ ਇਨਪੁਟ ਵਿਧੀਆਂ ਉਪਲਬਧ ਨਹੀਂ ਹਨ।", "app.containers.AccessibilityStatement.mapping_3": "ਰੇਖਾ ਅਤੇ ਬਹੁਭੁਜ ਨਕਸ਼ੇ ਦੇ ਡਰਾਇੰਗ ਨੂੰ ਵਧੇਰੇ ਪਹੁੰਚਯੋਗ ਬਣਾਉਣ ਲਈ, ਅਸੀਂ ਸਰਵੇਖਣ ਪ੍ਰਸ਼ਨ ਵਿੱਚ ਇੱਕ ਜਾਣ-ਪਛਾਣ ਜਾਂ ਵਿਆਖਿਆ ਸ਼ਾਮਲ ਕਰਨ ਦੀ ਸਿਫਾਰਸ਼ ਕਰਦੇ ਹਾਂ ਜਾਂ ਨਕਸ਼ਾ ਕੀ ਦਿਖਾ ਰਿਹਾ ਹੈ ਅਤੇ ਕਿਸੇ ਵੀ ਸੰਬੰਧਿਤ ਰੁਝਾਨਾਂ ਦਾ ਪੰਨਾ ਵੇਰਵਾ ਸ਼ਾਮਲ ਕਰੋ। ਇਸ ਤੋਂ ਇਲਾਵਾ, ਇੱਕ ਛੋਟਾ ਜਾਂ ਲੰਮਾ ਜਵਾਬ ਪਾਠ ਪ੍ਰਸ਼ਨ ਪ੍ਰਦਾਨ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ ਤਾਂ ਜੋ ਲੋੜ ਪੈਣ 'ਤੇ ਉੱਤਰਦਾਤਾ ਆਪਣੇ ਜਵਾਬ ਨੂੰ ਸਾਦੇ ਸ਼ਬਦਾਂ ਵਿੱਚ ਬਿਆਨ ਕਰ ਸਕਣ (ਨਕਸ਼ੇ 'ਤੇ ਕਲਿੱਕ ਕਰਨ ਦੀ ਬਜਾਏ)। ਅਸੀਂ ਪ੍ਰੋਜੈਕਟ ਮੈਨੇਜਰ ਲਈ ਸੰਪਰਕ ਜਾਣਕਾਰੀ ਸ਼ਾਮਲ ਕਰਨ ਦੀ ਵੀ ਸਿਫ਼ਾਰਿਸ਼ ਕਰਦੇ ਹਾਂ ਤਾਂ ਜੋ ਉੱਤਰਦਾਤਾ ਜੋ ਨਕਸ਼ੇ ਦੇ ਸਵਾਲ ਨੂੰ ਨਹੀਂ ਭਰ ਸਕਦੇ ਹਨ, ਸਵਾਲ ਦਾ ਜਵਾਬ ਦੇਣ ਲਈ ਇੱਕ ਵਿਕਲਪਿਕ ਢੰਗ ਦੀ ਬੇਨਤੀ ਕਰ ਸਕਦੇ ਹਨ (ਜਿਵੇਂ ਕਿ ਵੀਡੀਓ ਮੀਟਿੰਗ)।", "app.containers.AccessibilityStatement.mapping_4": "Ideation ਪ੍ਰੋਜੈਕਟਾਂ ਅਤੇ ਪ੍ਰਸਤਾਵਾਂ ਲਈ, ਇੱਕ ਨਕਸ਼ੇ ਦੇ ਦ੍ਰਿਸ਼ ਵਿੱਚ ਇਨਪੁਟਸ ਪ੍ਰਦਰਸ਼ਿਤ ਕਰਨ ਦਾ ਵਿਕਲਪ ਹੈ, ਜੋ ਪਹੁੰਚਯੋਗ ਨਹੀਂ ਹੈ। ਹਾਲਾਂਕਿ, ਇਹਨਾਂ ਤਰੀਕਿਆਂ ਲਈ ਇਨਪੁਟਸ ਦਾ ਇੱਕ ਵਿਕਲਪਿਕ ਸੂਚੀ ਦ੍ਰਿਸ਼ ਉਪਲਬਧ ਹੈ, ਜੋ ਪਹੁੰਚਯੋਗ ਹੈ।", "app.containers.AccessibilityStatement.onlineWorkshopsException": "ਸਾਡੀਆਂ ਔਨਲਾਈਨ ਵਰਕਸ਼ਾਪਾਂ ਵਿੱਚ ਇੱਕ ਲਾਈਵ ਵੀਡੀਓ ਸਟ੍ਰੀਮਿੰਗ ਭਾਗ ਹੈ, ਜੋ ਵਰਤਮਾਨ ਵਿੱਚ ਉਪਸਿਰਲੇਖਾਂ ਦਾ ਸਮਰਥਨ ਨਹੀਂ ਕਰਦਾ ਹੈ।", "app.containers.AccessibilityStatement.pageDescription": "ਇਸ ਵੈਬਸਾਈਟ ਦੀ ਪਹੁੰਚਯੋਗਤਾ 'ਤੇ ਇੱਕ ਬਿਆਨ", "app.containers.AccessibilityStatement.postalAddress": "ਡਾਕ ਪਤਾ:", "app.containers.AccessibilityStatement.publicationDate": "ਪ੍ਰਕਾਸ਼ਨ ਦੀ ਮਿਤੀ", "app.containers.AccessibilityStatement.publicationDate2024": "ਇਹ ਪਹੁੰਚਯੋਗਤਾ ਬਿਆਨ 21 ਅਗਸਤ, 2024 ਨੂੰ ਪ੍ਰਕਾਸ਼ਿਤ ਕੀਤਾ ਗਿਆ ਸੀ।", "app.containers.AccessibilityStatement.responsiveness": "ਅਸੀਂ 1-2 ਕਾਰੋਬਾਰੀ ਦਿਨਾਂ ਦੇ ਅੰਦਰ ਫੀਡਬੈਕ ਦਾ ਜਵਾਬ ਦੇਣਾ ਚਾਹੁੰਦੇ ਹਾਂ।", "app.containers.AccessibilityStatement.statusPageText": "ਸਥਿਤੀ ਪੰਨਾ", "app.containers.AccessibilityStatement.technologiesIntro": "ਇਸ ਸਾਈਟ ਦੀ ਪਹੁੰਚਯੋਗਤਾ ਕੰਮ ਕਰਨ ਲਈ ਹੇਠ ਲਿਖੀਆਂ ਤਕਨੀਕਾਂ 'ਤੇ ਨਿਰਭਰ ਕਰਦੀ ਹੈ:", "app.containers.AccessibilityStatement.technologiesTitle": "ਤਕਨਾਲੋਜੀਆਂ", "app.containers.AccessibilityStatement.title": "ਪਹੁੰਚਯੋਗਤਾ ਬਿਆਨ", "app.containers.AccessibilityStatement.userGeneratedContent": "ਉਪਭੋਗਤਾ ਦੁਆਰਾ ਤਿਆਰ ਕੀਤੀ ਸਮੱਗਰੀ", "app.containers.AccessibilityStatement.workshops": "ਵਰਕਸ਼ਾਪਾਂ", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "ਪ੍ਰੋਜੈਕਟ ਚੁਣੋ", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "ਸਮਗਰੀ ਨਿਰਮਾਤਾ ਦੀ ਵਰਤੋਂ ਕਰਨ ਨਾਲ ਤੁਸੀਂ ਵਧੇਰੇ ਉੱਨਤ ਖਾਕਾ ਵਿਕਲਪਾਂ ਦੀ ਵਰਤੋਂ ਕਰ ਸਕੋਗੇ। ਉਹਨਾਂ ਭਾਸ਼ਾਵਾਂ ਲਈ ਜਿੱਥੇ ਸਮੱਗਰੀ ਬਿਲਡਰ ਵਿੱਚ ਕੋਈ ਸਮੱਗਰੀ ਉਪਲਬਧ ਨਹੀਂ ਹੈ, ਇਸਦੀ ਬਜਾਏ ਨਿਯਮਤ ਪ੍ਰੋਜੈਕਟ ਵਰਣਨ ਸਮੱਗਰੀ ਪ੍ਰਦਰਸ਼ਿਤ ਕੀਤੀ ਜਾਵੇਗੀ।", "app.containers.AdminPage.ProjectDescription.linkText": "ਸਮਗਰੀ ਨਿਰਮਾਤਾ ਵਿੱਚ ਵਰਣਨ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.AdminPage.ProjectDescription.saveError": "ਪ੍ਰੋਜੈਕਟ ਵਰਣਨ ਨੂੰ ਰੱਖਿਅਤ ਕਰਨ ਦੌਰਾਨ ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ।", "app.containers.AdminPage.ProjectDescription.toggleLabel": "ਵਰਣਨ ਲਈ ਸਮੱਗਰੀ ਨਿਰਮਾਤਾ ਦੀ ਵਰਤੋਂ ਕਰੋ", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "ਸਮਗਰੀ ਨਿਰਮਾਤਾ ਦੀ ਵਰਤੋਂ ਕਰਨ ਨਾਲ ਤੁਸੀਂ ਵਧੇਰੇ ਉੱਨਤ ਖਾਕਾ ਵਿਕਲਪਾਂ ਦੀ ਵਰਤੋਂ ਕਰ ਸਕੋਗੇ।", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "ਪ੍ਰੋਜੈਕਟ ਦੇਖੋ", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "ਸਰਵੇਖਣ ਸਮਾਪਤ", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "ਇੱਕ ਸਮਾਰਟ ਗਰੁੱਪ ਬਣਾਓ", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "ਹੇਠ ਲਿਖੀਆਂ ਸਾਰੀਆਂ ਸ਼ਰਤਾਂ ਨਾਲ ਮੇਲ ਖਾਂਦੇ ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਆਪਣੇ ਆਪ ਸਮੂਹ ਵਿੱਚ ਸ਼ਾਮਲ ਕੀਤਾ ਜਾਵੇਗਾ:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "ਘੱਟੋ-ਘੱਟ ਇੱਕ ਨਿਯਮ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.AdminPage.Users.UsersGroup.rulesError": "ਕੁਝ ਸ਼ਰਤਾਂ ਅਧੂਰੀਆਂ ਹਨ", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "ਸਮੂਹ ਨੂੰ ਸੁਰੱਖਿਅਤ ਕਰੋ", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "ਸਮਾਰਟ ਗਰੁੱਪਾਂ ਨੂੰ ਕੌਂਫਿਗਰ ਕਰਨਾ ਤੁਹਾਡੇ ਮੌਜੂਦਾ ਲਾਇਸੰਸ ਦਾ ਹਿੱਸਾ ਨਹੀਂ ਹੈ। ਇਸ ਬਾਰੇ ਹੋਰ ਜਾਣਨ ਲਈ ਆਪਣੇ GovSuccess ਮੈਨੇਜਰ ਨਾਲ ਸੰਪਰਕ ਕਰੋ।", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "ਇੱਕ ਸਮੂਹ ਦਾ ਨਾਮ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "ਪੁਸ਼ਟੀਕਰਨ ਤੁਹਾਡੇ ਪਲੇਟਫਾਰਮ ਲਈ ਅਸਮਰੱਥ ਹੈ, ਪੁਸ਼ਟੀਕਰਨ ਨਿਯਮ ਹਟਾਓ ਜਾਂ ਸਹਾਇਤਾ ਨਾਲ ਸੰਪਰਕ ਕਰੋ।", "app.containers.App.appMetaDescription": "{orgName}ਦੇ ਔਨਲਾਈਨ ਭਾਗੀਦਾਰੀ ਪਲੇਟਫਾਰਮ ਵਿੱਚ ਤੁਹਾਡਾ ਸੁਆਗਤ ਹੈ। \nਸਥਾਨਕ ਪ੍ਰੋਜੈਕਟਾਂ ਦੀ ਪੜਚੋਲ ਕਰੋ ਅਤੇ ਚਰਚਾ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ!", "app.containers.App.loading": "ਲੋਡ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ...", "app.containers.App.metaTitle1": "ਨਾਗਰਿਕ ਸ਼ਮੂਲੀਅਤ ਪਲੇਟਫਾਰਮ | {orgName}", "app.containers.App.skipLinkText": "ਮੁੱਖ ਸਮੱਗਰੀ 'ਤੇ ਜਾਓ", "app.containers.AreaTerms.areaTerm": "ਖੇਤਰ", "app.containers.AreaTerms.areasTerm": "ਖੇਤਰ", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "ਇਸ ਈਮੇਲ ਵਾਲਾ ਖਾਤਾ ਪਹਿਲਾਂ ਹੀ ਮੌਜੂਦ ਹੈ। ਤੁਸੀਂ ਸਾਈਨ ਆਉਟ ਕਰ ਸਕਦੇ ਹੋ, ਇਸ ਈਮੇਲ ਪਤੇ ਨਾਲ ਲੌਗਇਨ ਕਰ ਸਕਦੇ ਹੋ ਅਤੇ ਸੈਟਿੰਗਾਂ ਪੰਨੇ 'ਤੇ ਆਪਣੇ ਖਾਤੇ ਦੀ ਪੁਸ਼ਟੀ ਕਰ ਸਕਦੇ ਹੋ।", "app.containers.Authentication.steps.AccessDenied.close": "ਬੰਦ ਕਰੋ", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "ਤੁਸੀਂ ਇਸ ਪ੍ਰਕਿਰਿਆ ਵਿੱਚ ਹਿੱਸਾ ਲੈਣ ਲਈ ਲੋੜਾਂ ਨੂੰ ਪੂਰਾ ਨਹੀਂ ਕਰਦੇ ਹੋ।", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "ਸਿੰਗਲ ਸਾਈਨ-ਆਨ ਵੈਰੀਫਿਕੇਸ਼ਨ 'ਤੇ ਵਾਪਸ ਜਾਓ", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਟੋਕਨ ਦਾਖਲ ਕਰੋ", "app.containers.Authentication.steps.Invitation.token": "ਟੋਕਨ", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "ਕੀ ਪਹਿਲਾਂ ਤੋਂ ਹੀ ਖਾਤਾ ਹੈ? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "ਲਾਗਿਨ", "app.containers.CampaignsConsentForm.ally_categoryLabel": "ਇਸ ਸ਼੍ਰੇਣੀ ਵਿੱਚ ਈਮੇਲ", "app.containers.CampaignsConsentForm.messageError": "ਤੁਹਾਡੀਆਂ ਈਮੇਲ ਤਰਜੀਹਾਂ ਨੂੰ ਸੁਰੱਖਿਅਤ ਕਰਨ ਵਿੱਚ ਇੱਕ ਤਰੁੱਟੀ ਸੀ।", "app.containers.CampaignsConsentForm.messageSuccess": "ਤੁਹਾਡੀਆਂ ਈਮੇਲ ਤਰਜੀਹਾਂ ਨੂੰ ਸੁਰੱਖਿਅਤ ਕੀਤਾ ਗਿਆ ਹੈ।", "app.containers.CampaignsConsentForm.notificationsSubTitle": "ਤੁਸੀਂ ਕਿਸ ਕਿਸਮ ਦੀਆਂ ਈਮੇਲ ਸੂਚਨਾਵਾਂ ਪ੍ਰਾਪਤ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ? ", "app.containers.CampaignsConsentForm.notificationsTitle": "ਸੂਚਨਾਵਾਂ", "app.containers.CampaignsConsentForm.submit": "ਸੇਵ ਕਰੋ", "app.containers.ChangeEmail.backToProfile": "ਪ੍ਰੋਫਾਈਲ ਸੈਟਿੰਗਾਂ 'ਤੇ ਵਾਪਸ ਜਾਓ", "app.containers.ChangeEmail.confirmationModalTitle": "ਆਪਣੀ ਈਮੇਲ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ", "app.containers.ChangeEmail.emailEmptyError": "ਇੱਕ ਈ-ਮੇਲ ਪਤਾ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.ChangeEmail.emailInvalidError": "ਸਹੀ ਫਾਰਮੈਟ ਵਿੱਚ ਇੱਕ ਈਮੇਲ ਪਤਾ ਪ੍ਰਦਾਨ ਕਰੋ, ਉਦਾਹਰਨ ਲਈ <EMAIL>", "app.containers.ChangeEmail.emailRequired": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਈਮੇਲ ਪਤਾ ਦਾਖਲ ਕਰੋ।", "app.containers.ChangeEmail.emailTaken": "ਇਹ ਈਮੇਲ ਪਹਿਲਾਂ ਹੀ ਵਰਤੋਂ ਵਿੱਚ ਹੈ।", "app.containers.ChangeEmail.emailUpdateCancelled": "ਈਮੇਲ ਅੱਪਡੇਟ ਰੱਦ ਕਰ ਦਿੱਤਾ ਗਿਆ ਹੈ।", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "ਆਪਣੀ ਈਮੇਲ ਨੂੰ ਅੱਪਡੇਟ ਕਰਨ ਲਈ, ਕਿਰਪਾ ਕਰਕੇ ਪ੍ਰਕਿਰਿਆ ਨੂੰ ਮੁੜ-ਸ਼ੁਰੂ ਕਰੋ।", "app.containers.ChangeEmail.helmetDescription": "ਆਪਣਾ ਈਮੇਲ ਪੰਨਾ ਬਦਲੋ", "app.containers.ChangeEmail.helmetTitle": "ਆਪਣੀ ਈਮੇਲ ਬਦਲੋ", "app.containers.ChangeEmail.newEmailLabel": "ਨਵੀਂ ਈਮੇਲ", "app.containers.ChangeEmail.submitButton": "ਜਮ੍ਹਾਂ ਕਰੋ", "app.containers.ChangeEmail.titleAddEmail": "ਆਪਣੀ ਈਮੇਲ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.ChangeEmail.titleChangeEmail": "ਆਪਣੀ ਈਮੇਲ ਬਦਲੋ", "app.containers.ChangeEmail.updateSuccessful": "ਤੁਹਾਡੀ ਈਮੇਲ ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤੀ ਗਈ ਹੈ।", "app.containers.ChangePassword.currentPasswordLabel": "ਵਰਤਮਾਨ ਪਾਸਵਰਡ", "app.containers.ChangePassword.currentPasswordRequired": "ਆਪਣਾ ਮੌਜੂਦਾ ਪਾਸਵਰਡ ਦਰਜ ਕਰੋ", "app.containers.ChangePassword.goHome": "ਘਰ ਜਾਓ", "app.containers.ChangePassword.helmetDescription": "ਆਪਣਾ ਪਾਸਵਰਡ ਪੰਨਾ ਬਦਲੋ", "app.containers.ChangePassword.helmetTitle": "ਆਪਣਾ ਪਾਸਵਰਡ ਬਦਲੋ", "app.containers.ChangePassword.newPasswordLabel": "ਨਵਾਂ ਪਾਸਵਰਡ", "app.containers.ChangePassword.newPasswordRequired": "ਆਪਣਾ ਨਵਾਂ ਪਾਸਵਰਡ ਦਰਜ ਕਰੋ", "app.containers.ChangePassword.password.minimumPasswordLengthError": "ਇੱਕ ਪਾਸਵਰਡ ਪ੍ਰਦਾਨ ਕਰੋ ਜੋ ਘੱਟੋ-ਘੱਟ {minimumPasswordLength} ਅੱਖਰਾਂ ਦਾ ਹੋਵੇ", "app.containers.ChangePassword.passwordChangeSuccessMessage": "ਤੁਹਾਡਾ ਪਾਸਵਰਡ ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤਾ ਗਿਆ ਹੈ", "app.containers.ChangePassword.passwordEmptyError": "ਆਪਣਾ ਪਾਸਵਰਡ ਦਰਜ ਕਰੋ", "app.containers.ChangePassword.passwordsDontMatch": "ਨਵੇਂ ਪਾਸਵਰਡ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ", "app.containers.ChangePassword.titleAddPassword": "ਇੱਕ ਪਾਸਵਰਡ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.ChangePassword.titleChangePassword": "ਆਪਣਾ ਪਾਸਵਰਡ ਬਦਲੋ", "app.containers.Comments.a11y_commentDeleted": "ਟਿੱਪਣੀ ਮਿਟਾਈ ਗਈ", "app.containers.Comments.a11y_commentPosted": "ਟਿੱਪਣੀ ਪੋਸਟ ਕੀਤੀ ਗਈ", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {ਕੋਈ ਪਸੰਦ ਨਹੀਂ} one {1 ਪਸੰਦ} other {# ਪਸੰਦ}}", "app.containers.Comments.a11y_undoLike": "ਪਸੰਦ ਨੂੰ ਅਣਕੀਤਾ ਕਰੋ", "app.containers.Comments.addCommentError": "ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ। ਕਿਰਪਾ ਕਰਕੇ ਬਾਅਦ ਵਿੱਚ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.containers.Comments.adminCommentDeletionCancelButton": "ਰੱਦ ਕਰੋ", "app.containers.Comments.adminCommentDeletionConfirmButton": "ਇਸ ਟਿੱਪਣੀ ਨੂੰ ਮਿਟਾਓ", "app.containers.Comments.cancelCommentEdit": "ਰੱਦ ਕਰੋ", "app.containers.Comments.childCommentBodyPlaceholder": "ਜਵਾਬ ਲਿਖੋ...", "app.containers.Comments.commentCancelUpvote": "ਅਣਡੂ", "app.containers.Comments.commentDeletedPlaceholder": "ਇਹ ਟਿੱਪਣੀ ਮਿਟਾ ਦਿੱਤੀ ਗਈ ਹੈ।", "app.containers.Comments.commentDeletionCancelButton": "ਮੇਰੀ ਟਿੱਪਣੀ ਰੱਖੋ", "app.containers.Comments.commentDeletionConfirmButton": "ਮੇਰੀ ਟਿੱਪਣੀ ਨੂੰ ਮਿਟਾਓ", "app.containers.Comments.commentLike": "ਪਸੰਦ ਹੈ", "app.containers.Comments.commentReplyButton": "ਜਵਾਬ", "app.containers.Comments.commentsSortTitle": "ਟਿੱਪਣੀਆਂ ਨੂੰ ਇਸ ਅਨੁਸਾਰ ਕ੍ਰਮਬੱਧ ਕਰੋ", "app.containers.Comments.completeProfileLinkText": "ਆਪਣਾ ਪ੍ਰੋਫਾਈਲ ਪੂਰਾ ਕਰੋ", "app.containers.Comments.completeProfileToComment": "ਟਿੱਪਣੀ ਕਰਨ ਲਈ ਕਿਰਪਾ ਕਰਕੇ {completeRegistrationLink} .", "app.containers.Comments.confirmCommentDeletion": "ਕੀ ਤੁਸੀਂ ਯਕੀਨਨ ਇਸ ਟਿੱਪਣੀ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ? ਪਿੱਛੇ ਮੁੜਨ ਦੀ ਕੋਈ ਲੋੜ ਨਹੀਂ ਹੈ!", "app.containers.Comments.deleteComment": "ਮਿਟਾਓ", "app.containers.Comments.deleteReasonDescriptionError": "ਆਪਣੇ ਕਾਰਨ ਬਾਰੇ ਹੋਰ ਜਾਣਕਾਰੀ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.Comments.deleteReasonError": "ਕੋਈ ਕਾਰਨ ਦਿਓ", "app.containers.Comments.deleteReason_inappropriate": "ਇਹ ਅਣਉਚਿਤ ਜਾਂ ਅਪਮਾਨਜਨਕ ਹੈ", "app.containers.Comments.deleteReason_irrelevant": "ਇਹ ਢੁਕਵਾਂ ਨਹੀਂ ਹੈ", "app.containers.Comments.deleteReason_other": "ਹੋਰ ਕਾਰਨ", "app.containers.Comments.editComment": "ਸੰਪਾਦਿਤ ਕਰੋ", "app.containers.Comments.guidelinesLinkText": "ਸਾਡੇ ਭਾਈਚਾਰਕ ਦਿਸ਼ਾ-ਨਿਰਦੇਸ਼", "app.containers.Comments.ideaCommentBodyPlaceholder": "ਆਪਣੀ ਟਿੱਪਣੀ ਇੱਥੇ ਲਿਖੋ", "app.containers.Comments.internalCommentingNudgeMessage": "ਅੰਦਰੂਨੀ ਟਿੱਪਣੀਆਂ ਕਰਨਾ ਤੁਹਾਡੇ ਮੌਜੂਦਾ ਲਾਇਸੈਂਸ ਵਿੱਚ ਸ਼ਾਮਲ ਨਹੀਂ ਹੈ। ਇਸ ਬਾਰੇ ਹੋਰ ਜਾਣਨ ਲਈ ਆਪਣੇ GovSuccess ਮੈਨੇਜਰ ਨਾਲ ਸੰਪਰਕ ਕਰੋ।", "app.containers.Comments.internalConversation": "ਅੰਦਰੂਨੀ ਗੱਲਬਾਤ", "app.containers.Comments.loadMoreComments": "ਹੋਰ ਟਿੱਪਣੀਆਂ ਲੋਡ ਕਰੋ", "app.containers.Comments.loadingComments": "ਟਿੱਪਣੀਆਂ ਲੋਡ ਕੀਤੀਆਂ ਜਾ ਰਹੀਆਂ ਹਨ...", "app.containers.Comments.loadingMoreComments": "ਹੋਰ ਟਿੱਪਣੀਆਂ ਲੋਡ ਕੀਤੀਆਂ ਜਾ ਰਹੀਆਂ ਹਨ...", "app.containers.Comments.notVisibleToUsersPlaceholder": "ਇਹ ਟਿੱਪਣੀ ਨਿਯਮਤ ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਦਿਖਾਈ ਨਹੀਂ ਦਿੰਦੀ", "app.containers.Comments.postInternalComment": "ਅੰਦਰੂਨੀ ਟਿੱਪਣੀ ਪੋਸਟ ਕਰੋ", "app.containers.Comments.postPublicComment": "ਜਨਤਕ ਟਿੱਪਣੀ ਪੋਸਟ ਕਰੋ", "app.containers.Comments.profanityError": "ਓਹ! ਇੰਝ ਜਾਪਦਾ ਹੈ ਕਿ ਤੁਹਾਡੀ ਪੋਸਟ ਵਿੱਚ ਕੁਝ ਭਾਸ਼ਾ ਸ਼ਾਮਲ ਹੈ ਜੋ {guidelinesLink}ਨੂੰ ਪੂਰਾ ਨਹੀਂ ਕਰਦੀ ਹੈ। ਅਸੀਂ ਇਸ ਨੂੰ ਹਰ ਕਿਸੇ ਲਈ ਸੁਰੱਖਿਅਤ ਥਾਂ ਰੱਖਣ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ। ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੇ ਇਨਪੁਟ ਨੂੰ ਸੰਪਾਦਿਤ ਕਰੋ ਅਤੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.containers.Comments.publicDiscussion": "ਜਨਤਕ ਚਰਚਾ", "app.containers.Comments.publishComment": "ਆਪਣੀ ਟਿੱਪਣੀ ਪੋਸਟ ਕਰੋ", "app.containers.Comments.reportAsSpamModalTitle": "ਤੁਸੀਂ ਇਸਦੀ ਸਪੈਮ ਵਜੋਂ ਰਿਪੋਰਟ ਕਿਉਂ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?", "app.containers.Comments.saveComment": "ਸੇਵ ਕਰੋ", "app.containers.Comments.signInLinkText": "ਲਾਗਿਨ", "app.containers.Comments.signInToComment": "ਟਿੱਪਣੀ ਕਰਨ ਲਈ ਕਿਰਪਾ ਕਰਕੇ {signInLink} .", "app.containers.Comments.signUpLinkText": "ਸਾਇਨ ਅਪ", "app.containers.Comments.verifyIdentityLinkText": "ਆਪਣੀ ਪਛਾਣ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ", "app.containers.Comments.visibleToUsersPlaceholder": "ਇਹ ਟਿੱਪਣੀ ਨਿਯਮਤ ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਦਿਖਾਈ ਦਿੰਦੀ ਹੈ", "app.containers.Comments.visibleToUsersWarning": "ਇੱਥੇ ਪੋਸਟ ਕੀਤੀਆਂ ਟਿੱਪਣੀਆਂ ਨਿਯਮਤ ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਦਿਖਾਈ ਦੇਣਗੀਆਂ।", "app.containers.ContentBuilder.PageTitle": "ਪ੍ਰੋਜੈਕਟ ਦਾ ਵੇਰਵਾ", "app.containers.CookiePolicy.advertisingContent": "ਵਿਗਿਆਪਨ ਕੂਕੀਜ਼ ਦੀ ਵਰਤੋਂ ਵਿਅਕਤੀਗਤ ਬਣਾਉਣ ਅਤੇ ਪ੍ਰਭਾਵ ਨੂੰ ਮਾਪਣ ਲਈ ਕੀਤੀ ਜਾ ਸਕਦੀ ਹੈ ਜੋ ਬਾਹਰੀ ਮਾਰਕੀਟਿੰਗ ਮੁਹਿੰਮਾਂ ਦੀ ਇਸ ਪਲੇਟਫਾਰਮ ਨਾਲ ਸ਼ਮੂਲੀਅਤ 'ਤੇ ਹੈ। ਅਸੀਂ ਇਸ ਪਲੇਟਫਾਰਮ 'ਤੇ ਕੋਈ ਵੀ ਵਿਗਿਆਪਨ ਨਹੀਂ ਦਿਖਾਵਾਂਗੇ, ਪਰ ਤੁਸੀਂ ਉਹਨਾਂ ਪੰਨਿਆਂ ਦੇ ਆਧਾਰ 'ਤੇ ਵਿਅਕਤੀਗਤ ਵਿਗਿਆਪਨ ਪ੍ਰਾਪਤ ਕਰ ਸਕਦੇ ਹੋ ਜੋ ਤੁਸੀਂ ਦੇਖਦੇ ਹੋ।", "app.containers.CookiePolicy.advertisingTitle": "ਇਸ਼ਤਿਹਾਰਬਾਜ਼ੀ", "app.containers.CookiePolicy.analyticsContents": "ਵਿਸ਼ਲੇਸ਼ਣ ਕੂਕੀਜ਼ ਵਿਜ਼ਟਰ ਵਿਵਹਾਰ ਨੂੰ ਟਰੈਕ ਕਰਦੀਆਂ ਹਨ, ਜਿਵੇਂ ਕਿ ਕਿਹੜੇ ਪੰਨਿਆਂ 'ਤੇ ਵਿਜ਼ਿਟ ਕੀਤਾ ਜਾਂਦਾ ਹੈ ਅਤੇ ਕਿੰਨੇ ਸਮੇਂ ਲਈ। ਉਹ ਬ੍ਰਾਊਜ਼ਰ ਜਾਣਕਾਰੀ, ਅੰਦਾਜ਼ਨ ਟਿਕਾਣਾ ਅਤੇ IP ਪਤੇ ਸਮੇਤ ਕੁਝ ਤਕਨੀਕੀ ਡਾਟਾ ਵੀ ਇਕੱਤਰ ਕਰ ਸਕਦੇ ਹਨ। ਅਸੀਂ ਪਲੇਟਫਾਰਮ ਦੇ ਸਮੁੱਚੇ ਉਪਭੋਗਤਾ ਅਨੁਭਵ ਅਤੇ ਕੰਮਕਾਜ ਨੂੰ ਬਿਹਤਰ ਬਣਾਉਣ ਲਈ ਅੰਦਰੂਨੀ ਤੌਰ 'ਤੇ ਇਸ ਡੇਟਾ ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਾਂ। ਪਲੇਟਫਾਰਮ 'ਤੇ ਪ੍ਰੋਜੈਕਟਾਂ ਦੇ ਨਾਲ ਰੁਝੇਵਿਆਂ ਦਾ ਮੁਲਾਂਕਣ ਕਰਨ ਅਤੇ ਉਨ੍ਹਾਂ ਨੂੰ ਬਿਹਤਰ ਬਣਾਉਣ ਲਈ ਗੋ ਵੋਕਲ ਅਤੇ {orgName} ਵਿਚਕਾਰ ਅਜਿਹਾ ਡੇਟਾ ਸਾਂਝਾ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ। ਨੋਟ ਕਰੋ ਕਿ ਡੇਟਾ ਅਗਿਆਤ ਹੈ ਅਤੇ ਇੱਕ ਸਮੂਹਿਕ ਪੱਧਰ 'ਤੇ ਵਰਤਿਆ ਜਾਂਦਾ ਹੈ - ਇਹ ਤੁਹਾਡੀ ਵਿਅਕਤੀਗਤ ਤੌਰ 'ਤੇ ਪਛਾਣ ਨਹੀਂ ਕਰਦਾ ਹੈ। ਹਾਲਾਂਕਿ, ਇਹ ਸੰਭਵ ਹੈ ਕਿ ਜੇਕਰ ਇਸ ਡੇਟਾ ਨੂੰ ਹੋਰ ਡੇਟਾ ਸਰੋਤਾਂ ਨਾਲ ਜੋੜਿਆ ਜਾਵੇ, ਤਾਂ ਅਜਿਹੀ ਪਛਾਣ ਹੋ ਸਕਦੀ ਹੈ।", "app.containers.CookiePolicy.analyticsTitle": "ਵਿਸ਼ਲੇਸ਼ਣ ਕੂਕੀਜ਼", "app.containers.CookiePolicy.cookiePolicyDescription": "ਅਸੀਂ ਇਸ ਪਲੇਟਫਾਰਮ 'ਤੇ ਕੂਕੀਜ਼ ਦੀ ਵਰਤੋਂ ਕਿਵੇਂ ਕਰਦੇ ਹਾਂ ਇਸ ਬਾਰੇ ਵਿਸਤ੍ਰਿਤ ਵਿਆਖਿਆ", "app.containers.CookiePolicy.cookiePolicyTitle": "ਕੂਕੀ ਨੀਤੀ", "app.containers.CookiePolicy.essentialContent": "ਇਸ ਪਲੇਟਫਾਰਮ ਦੇ ਸਹੀ ਕੰਮਕਾਜ ਨੂੰ ਯਕੀਨੀ ਬਣਾਉਣ ਲਈ ਕੁਝ ਕੁਕੀਜ਼ ਜ਼ਰੂਰੀ ਹਨ। ਜਦੋਂ ਤੁਸੀਂ ਪਲੇਟਫਾਰਮ 'ਤੇ ਜਾਂਦੇ ਹੋ ਤਾਂ ਇਹ ਜ਼ਰੂਰੀ ਕੂਕੀਜ਼ ਮੁੱਖ ਤੌਰ 'ਤੇ ਤੁਹਾਡੇ ਖਾਤੇ ਨੂੰ ਪ੍ਰਮਾਣਿਤ ਕਰਨ ਅਤੇ ਤੁਹਾਡੀ ਤਰਜੀਹੀ ਭਾਸ਼ਾ ਨੂੰ ਸੁਰੱਖਿਅਤ ਕਰਨ ਲਈ ਵਰਤੀਆਂ ਜਾਂਦੀਆਂ ਹਨ।", "app.containers.CookiePolicy.essentialTitle": "ਜ਼ਰੂਰੀ ਕੂਕੀਜ਼", "app.containers.CookiePolicy.externalContent": "ਸਾਡੇ ਕੁਝ ਪੰਨੇ ਬਾਹਰੀ ਪ੍ਰਦਾਤਾਵਾਂ ਤੋਂ ਸਮੱਗਰੀ ਪ੍ਰਦਰਸ਼ਿਤ ਕਰ ਸਕਦੇ ਹਨ, ਉਦਾਹਰਨ ਲਈ, YouTube ਜਾਂ Typeform। ਸਾਡੇ ਕੋਲ ਇਹਨਾਂ ਤੀਜੀ-ਧਿਰ ਕੂਕੀਜ਼ 'ਤੇ ਨਿਯੰਤਰਣ ਨਹੀਂ ਹੈ ਅਤੇ ਇਹਨਾਂ ਬਾਹਰੀ ਪ੍ਰਦਾਤਾਵਾਂ ਤੋਂ ਸਮੱਗਰੀ ਨੂੰ ਦੇਖਣ ਦੇ ਨਤੀਜੇ ਵਜੋਂ ਤੁਹਾਡੀ ਡਿਵਾਈਸ 'ਤੇ ਕੂਕੀਜ਼ ਸਥਾਪਤ ਹੋ ਸਕਦੀਆਂ ਹਨ।", "app.containers.CookiePolicy.externalTitle": "ਬਾਹਰੀ ਕੂਕੀਜ਼", "app.containers.CookiePolicy.functionalContents": "ਵਿਜ਼ਟਰਾਂ ਨੂੰ ਅੱਪਡੇਟ ਬਾਰੇ ਸੂਚਨਾਵਾਂ ਪ੍ਰਾਪਤ ਕਰਨ ਅਤੇ ਪਲੇਟਫਾਰਮ ਤੋਂ ਸਿੱਧੇ ਸਹਾਇਤਾ ਚੈਨਲਾਂ ਤੱਕ ਪਹੁੰਚ ਕਰਨ ਲਈ ਕਾਰਜਸ਼ੀਲ ਕੂਕੀਜ਼ ਨੂੰ ਸਮਰੱਥ ਬਣਾਇਆ ਜਾ ਸਕਦਾ ਹੈ।", "app.containers.CookiePolicy.functionalTitle": "ਕਾਰਜਸ਼ੀਲ ਕੂਕੀਜ਼", "app.containers.CookiePolicy.headCookiePolicyTitle": "ਕੂਕੀ ਨੀਤੀ | {orgName}", "app.containers.CookiePolicy.intro": "ਕੂਕੀਜ਼ ਉਹ ਟੈਕਸਟ ਫਾਈਲਾਂ ਹੁੰਦੀਆਂ ਹਨ ਜੋ ਬ੍ਰਾਊਜ਼ਰ ਜਾਂ ਤੁਹਾਡੇ ਕੰਪਿਊਟਰ ਜਾਂ ਮੋਬਾਈਲ ਡਿਵਾਈਸ ਦੀ ਹਾਰਡ ਡਰਾਈਵ 'ਤੇ ਸਟੋਰ ਕੀਤੀਆਂ ਜਾਂਦੀਆਂ ਹਨ ਜਦੋਂ ਤੁਸੀਂ ਕਿਸੇ ਵੈਬਸਾਈਟ 'ਤੇ ਜਾਂਦੇ ਹੋ ਅਤੇ ਜੋ ਬਾਅਦ ਵਿੱਚ ਆਉਣ ਵਾਲੇ ਦੌਰਿਆਂ ਦੌਰਾਨ ਵੈਬਸਾਈਟ ਦੁਆਰਾ ਹਵਾਲਾ ਦਿੱਤਾ ਜਾ ਸਕਦਾ ਹੈ। ਅਸੀਂ ਇਹ ਸਮਝਣ ਲਈ ਕੂਕੀਜ਼ ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਾਂ ਕਿ ਵਿਜ਼ਟਰ ਇਸ ਪਲੇਟਫਾਰਮ ਦੀ ਵਰਤੋਂ ਇਸਦੇ ਡਿਜ਼ਾਈਨ ਅਤੇ ਅਨੁਭਵ ਨੂੰ ਬਿਹਤਰ ਬਣਾਉਣ ਲਈ, ਤੁਹਾਡੀਆਂ ਤਰਜੀਹਾਂ (ਜਿਵੇਂ ਕਿ ਤੁਹਾਡੀ ਤਰਜੀਹੀ ਭਾਸ਼ਾ) ਨੂੰ ਯਾਦ ਰੱਖਣ ਲਈ ਅਤੇ ਰਜਿਸਟਰਡ ਉਪਭੋਗਤਾਵਾਂ ਅਤੇ ਪਲੇਟਫਾਰਮ ਪ੍ਰਸ਼ਾਸਕਾਂ ਲਈ ਮੁੱਖ ਕਾਰਜਾਂ ਦਾ ਸਮਰਥਨ ਕਰਨ ਲਈ ਕਰਦੇ ਹਨ।", "app.containers.CookiePolicy.manageCookiesDescription": "ਤੁਸੀਂ ਆਪਣੀ ਕੂਕੀ ਤਰਜੀਹਾਂ ਵਿੱਚ ਕਿਸੇ ਵੀ ਸਮੇਂ ਵਿਸ਼ਲੇਸ਼ਣ, ਮਾਰਕੀਟਿੰਗ ਅਤੇ ਕਾਰਜਸ਼ੀਲ ਕੂਕੀਜ਼ ਨੂੰ ਸਮਰੱਥ ਜਾਂ ਅਯੋਗ ਕਰ ਸਕਦੇ ਹੋ। ਤੁਸੀਂ ਆਪਣੇ ਇੰਟਰਨੈਟ ਬ੍ਰਾਊਜ਼ਰ ਰਾਹੀਂ ਕਿਸੇ ਵੀ ਮੌਜੂਦਾ ਕੂਕੀਜ਼ ਨੂੰ ਹੱਥੀਂ ਜਾਂ ਆਪਣੇ ਆਪ ਮਿਟਾ ਸਕਦੇ ਹੋ। ਹਾਲਾਂਕਿ, ਇਸ ਪਲੇਟਫਾਰਮ 'ਤੇ ਆਉਣ ਵਾਲੇ ਕਿਸੇ ਵੀ ਦੌਰੇ 'ਤੇ ਤੁਹਾਡੀ ਸਹਿਮਤੀ ਤੋਂ ਬਾਅਦ ਕੂਕੀਜ਼ ਨੂੰ ਦੁਬਾਰਾ ਰੱਖਿਆ ਜਾ ਸਕਦਾ ਹੈ। ਜੇਕਰ ਤੁਸੀਂ ਕੂਕੀਜ਼ ਨੂੰ ਨਹੀਂ ਮਿਟਾਉਂਦੇ ਹੋ, ਤਾਂ ਤੁਹਾਡੀਆਂ ਕੂਕੀਜ਼ ਤਰਜੀਹਾਂ 60 ਦਿਨਾਂ ਲਈ ਸਟੋਰ ਕੀਤੀਆਂ ਜਾਂਦੀਆਂ ਹਨ, ਜਿਸ ਤੋਂ ਬਾਅਦ ਤੁਹਾਨੂੰ ਤੁਹਾਡੀ ਸਹਿਮਤੀ ਲਈ ਦੁਬਾਰਾ ਕਿਹਾ ਜਾਵੇਗਾ।", "app.containers.CookiePolicy.manageCookiesPreferences": "ਇਸ ਪਲੇਟਫਾਰਮ 'ਤੇ ਵਰਤੇ ਗਏ ਤੀਜੀ ਧਿਰ ਦੇ ਏਕੀਕਰਣਾਂ ਦੀ ਪੂਰੀ ਸੂਚੀ ਦੇਖਣ ਅਤੇ ਆਪਣੀਆਂ ਤਰਜੀਹਾਂ ਦਾ ਪ੍ਰਬੰਧਨ ਕਰਨ ਲਈ ਆਪਣੇ {manageCookiesPreferencesButtonText} 'ਤੇ ਜਾਓ।", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "ਕੂਕੀ ਸੈਟਿੰਗ", "app.containers.CookiePolicy.manageCookiesTitle": "ਤੁਹਾਡੀਆਂ ਕੂਕੀਜ਼ ਦਾ ਪ੍ਰਬੰਧਨ ਕਰਨਾ", "app.containers.CookiePolicy.viewPreferencesButtonText": "ਕੂਕੀ ਸੈਟਿੰਗਾਂ", "app.containers.CookiePolicy.viewPreferencesText": "ਹੇਠਾਂ ਦਿੱਤੀਆਂ ਕੂਕੀ ਸ਼੍ਰੇਣੀਆਂ ਸਾਰੇ ਵਿਜ਼ਟਰਾਂ ਜਾਂ ਪਲੇਟਫਾਰਮਾਂ 'ਤੇ ਲਾਗੂ ਨਹੀਂ ਹੋ ਸਕਦੀਆਂ; ਤੁਹਾਡੇ 'ਤੇ ਲਾਗੂ ਤੀਜੀ ਧਿਰ ਦੇ ਏਕੀਕਰਣਾਂ ਦੀ ਪੂਰੀ ਸੂਚੀ ਲਈ ਆਪਣਾ {viewPreferencesButton} ਦੇਖੋ।", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "ਅਸੀਂ ਕੂਕੀਜ਼ ਦੀ ਵਰਤੋਂ ਕਿਸ ਲਈ ਕਰਦੇ ਹਾਂ?", "app.containers.CustomPageShow.editPage": "ਪੰਨਾ ਸੰਪਾਦਿਤ ਕਰੋ", "app.containers.CustomPageShow.goBack": "ਵਾਪਸ ਜਾਓ", "app.containers.CustomPageShow.notFound": "ਪੰਨਾ ਨਹੀਂ ਮਿਲਿਆ", "app.containers.DisabledAccount.bottomText": "ਤੁਸੀਂ {date}ਤੋਂ ਦੁਬਾਰਾ ਸਾਈਨ ਇਨ ਕਰ ਸਕਦੇ ਹੋ।", "app.containers.DisabledAccount.termsAndConditions": "ਨਿਯਮ ਅਤੇ ਸ਼ਰਤਾਂ", "app.containers.DisabledAccount.text2": "ਭਾਈਚਾਰਕ ਦਿਸ਼ਾ-ਨਿਰਦੇਸ਼ਾਂ ਦੀ ਉਲੰਘਣਾ ਕਰਕੇ {orgName} ਦੇ ਭਾਗੀਦਾਰੀ ਪਲੇਟਫਾਰਮ 'ਤੇ ਤੁਹਾਡੇ ਖਾਤੇ ਨੂੰ ਅਸਥਾਈ ਤੌਰ 'ਤੇ ਅਸਮਰੱਥ ਕਰ ਦਿੱਤਾ ਗਿਆ ਹੈ। ਇਸ ਬਾਰੇ ਹੋਰ ਜਾਣਕਾਰੀ ਲਈ, ਤੁਸੀਂ {TermsAndConditions}ਨਾਲ ਸਲਾਹ ਕਰ ਸਕਦੇ ਹੋ।", "app.containers.DisabledAccount.title": "ਤੁਹਾਡਾ ਖਾਤਾ ਅਸਥਾਈ ਤੌਰ 'ਤੇ ਅਯੋਗ ਕਰ ਦਿੱਤਾ ਗਿਆ ਹੈ", "app.containers.EventsShow.addToCalendar": "ਕੈਲੰਡਰ ਵਿੱਚ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.EventsShow.editEvent": "ਘਟਨਾ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.EventsShow.emailSharingBody2": "ਇਸ ਇਵੈਂਟ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ: {eventTitle}. {eventUrl}'ਤੇ ਹੋਰ ਪੜ੍ਹੋ", "app.containers.EventsShow.eventDateTimeIcon": "ਇਵੈਂਟ ਮਿਤੀ ਅਤੇ ਸਮਾਂ", "app.containers.EventsShow.eventFrom2": "\"{projectTitle}\" ਤੋਂ", "app.containers.EventsShow.goBack": "ਵਾਪਸ ਜਾਓ", "app.containers.EventsShow.goToProject": "ਪ੍ਰੋਜੈਕਟ 'ਤੇ ਜਾਓ", "app.containers.EventsShow.haveRegistered": "ਰਜਿਸਟਰ ਕੀਤਾ ਹੈ", "app.containers.EventsShow.icsError": "ICS ਫਾਈਲ ਨੂੰ ਡਾਊਨਲੋਡ ਕਰਨ ਵਿੱਚ ਤਰੁੱਟੀ", "app.containers.EventsShow.linkToOnlineEvent": "ਔਨਲਾਈਨ ਇਵੈਂਟ ਲਈ ਲਿੰਕ", "app.containers.EventsShow.locationIconAltText": "ਟਿਕਾਣਾ", "app.containers.EventsShow.metaTitle": "ਘਟਨਾ: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "ਆਨਲਾਈਨ ਮੀਟਿੰਗ", "app.containers.EventsShow.onlineLinkIconAltText": "ਔਨਲਾਈਨ ਮੀਟਿੰਗ ਲਿੰਕ", "app.containers.EventsShow.registered": "ਰਜਿਸਟਰਡ", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 ਰਜਿਸਟਰਾਰ} one {1 ਰਜਿਸਟਰਾਰ} other {# ਰਜਿਸਟਰਾਰ}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} ਰਜਿਸਟਰ ਕਰਨ ਵਾਲੇ", "app.containers.EventsShow.registrantsIconAltText": "ਰਜਿਸਟਰ ਕਰਨ ਵਾਲੇ", "app.containers.EventsShow.socialMediaSharingMessage": "ਇਸ ਇਵੈਂਟ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# ਭਾਗੀਦਾਰ} other {# ਭਾਗੀਦਾਰ}}", "app.containers.EventsViewer.allTime": "ਸਾਰਾ ਸਮਾਂ", "app.containers.EventsViewer.date": "ਮਿਤੀ", "app.containers.EventsViewer.thisMonth2": "ਆਉਣ ਵਾਲਾ ਮਹੀਨਾ", "app.containers.EventsViewer.thisWeek2": "ਆਉਣ ਵਾਲਾ ਹਫ਼ਤਾ", "app.containers.EventsViewer.today": "ਅੱਜ", "app.containers.IdeaButton.addAContribution": "ਇੱਕ ਯੋਗਦਾਨ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.IdeaButton.addAPetition": "ਇੱਕ ਪਟੀਸ਼ਨ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.IdeaButton.addAProject": "ਇੱਕ ਪ੍ਰੋਜੈਕਟ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.IdeaButton.addAProposal": "ਇੱਕ ਪ੍ਰਸਤਾਵ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.IdeaButton.addAQuestion": "ਕੋਈ ਸਵਾਲ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.IdeaButton.addAnInitiative": "ਇੱਕ ਪਹਿਲ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.IdeaButton.addAnOption": "ਇੱਕ ਵਿਕਲਪ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.IdeaButton.postingDisabled": "ਨਵੀਆਂ ਬੇਨਤੀਆਂ ਵਰਤਮਾਨ ਵਿੱਚ ਸਵੀਕਾਰ ਨਹੀਂ ਕੀਤੀਆਂ ਜਾ ਰਹੀਆਂ ਹਨ", "app.containers.IdeaButton.postingInNonActivePhases": "ਨਵੀਆਂ ਸਬਮਿਸ਼ਨਾਂ ਸਿਰਫ਼ ਸਰਗਰਮ ਪੜਾਵਾਂ ਵਿੱਚ ਹੀ ਸ਼ਾਮਲ ਕੀਤੀਆਂ ਜਾ ਸਕਦੀਆਂ ਹਨ।", "app.containers.IdeaButton.postingInactive": "ਨਵੀਆਂ ਸਬਮਿਸ਼ਨਾਂ ਵਰਤਮਾਨ ਵਿੱਚ ਸਵੀਕਾਰ ਨਹੀਂ ਕੀਤੀਆਂ ਜਾ ਰਹੀਆਂ ਹਨ।", "app.containers.IdeaButton.postingLimitedMaxReached": "ਤੁਸੀਂ ਪਹਿਲਾਂ ਹੀ ਇਹ ਸਰਵੇਖਣ ਪੂਰਾ ਕਰ ਲਿਆ ਹੈ। ਤੁਹਾਡੇ ਜਵਾਬ ਲਈ ਧੰਨਵਾਦ!", "app.containers.IdeaButton.postingNoPermission": "ਨਵੀਆਂ ਬੇਨਤੀਆਂ ਵਰਤਮਾਨ ਵਿੱਚ ਸਵੀਕਾਰ ਨਹੀਂ ਕੀਤੀਆਂ ਜਾ ਰਹੀਆਂ ਹਨ", "app.containers.IdeaButton.postingNotYetPossible": "ਨਵੀਆਂ ਬੇਨਤੀਆਂ ਅਜੇ ਸਵੀਕਾਰ ਨਹੀਂ ਕੀਤੀਆਂ ਜਾ ਰਹੀਆਂ ਹਨ।", "app.containers.IdeaButton.signInLinkText": "ਲਾਗਿਨ", "app.containers.IdeaButton.signUpLinkText": "ਸਾਇਨ ਅਪ", "app.containers.IdeaButton.submitAnIssue": "ਇੱਕ ਟਿੱਪਣੀ ਦਰਜ ਕਰੋ", "app.containers.IdeaButton.submitYourIdea": "ਆਪਣਾ ਵਿਚਾਰ ਪੇਸ਼ ਕਰੋ", "app.containers.IdeaButton.takeTheSurvey": "ਸਰਵੇਖਣ ਲਵੋ", "app.containers.IdeaButton.verificationLinkText": "ਹੁਣੇ ਆਪਣੀ ਪਛਾਣ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ।", "app.containers.IdeaCard.readMore": "ਹੋਰ ਪੜ੍ਹੋ", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {ਕੋਈ ਟਿੱਪਣੀ ਨਹੀਂ} one {1 ਟਿੱਪਣੀ} other {# ਟਿੱਪਣੀਆਂ}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {ਕੋਈ ਵੋਟ ਨਹੀਂ} one {1 ਵੋਟ} other {# ਵੋਟਾਂ}} {votingThreshold}ਵਿੱਚੋਂ", "app.containers.IdeaCards.a11y_closeFilterPanel": "ਫਿਲਟਰ ਪੈਨਲ ਬੰਦ ਕਰੋ", "app.containers.IdeaCards.a11y_totalItems": "ਕੁੱਲ ਪੋਸਟਾਂ: {ideasCount}", "app.containers.IdeaCards.all": "ਸਾਰੇ", "app.containers.IdeaCards.allStatuses": "ਸਾਰੀਆਂ ਸਥਿਤੀਆਂ", "app.containers.IdeaCards.contributions": "ਯੋਗਦਾਨ", "app.containers.IdeaCards.ideaTerm": "ਵਿਚਾਰ", "app.containers.IdeaCards.initiatives": "ਪਹਿਲਕਦਮੀਆਂ", "app.containers.IdeaCards.issueTerm": "ਮੁੱਦੇ", "app.containers.IdeaCards.list": "ਸੂਚੀ", "app.containers.IdeaCards.map": "ਨਕਸ਼ਾ", "app.containers.IdeaCards.mostDiscussed": "ਸਭ ਤੋਂ ਵੱਧ ਚਰਚਾ ਕੀਤੀ", "app.containers.IdeaCards.newest": "ਸਭ ਤੋਂ ਤਾਜ਼ਾ", "app.containers.IdeaCards.noFilteredResults": "ਕੋਈ ਨਤੀਜੇ ਨਹੀਂ ਮਿਲੇ। ਕਿਰਪਾ ਕਰਕੇ ਕੋਈ ਵੱਖਰਾ ਫਿਲਟਰ ਜਾਂ ਖੋਜ ਸ਼ਬਦ ਅਜ਼ਮਾਓ।", "app.containers.IdeaCards.numberResults": "ਨਤੀਜੇ ({postCount})", "app.containers.IdeaCards.oldest": "ਸਭ ਤੋਂ ਪੁਰਾਣਾ", "app.containers.IdeaCards.optionTerm": "ਵਿਕਲਪ", "app.containers.IdeaCards.petitions": "ਪਟੀਸ਼ਨਾਂ", "app.containers.IdeaCards.popular": "ਸਭ ਤੋਂ ਵੱਧ ਵੋਟਾਂ ਪਈਆਂ", "app.containers.IdeaCards.projectFilterTitle": "ਪ੍ਰੋਜੈਕਟਸ", "app.containers.IdeaCards.projectTerm": "ਪ੍ਰੋਜੈਕਟਸ", "app.containers.IdeaCards.proposals": "ਪ੍ਰਸਤਾਵ", "app.containers.IdeaCards.questionTerm": "ਸਵਾਲ", "app.containers.IdeaCards.random": "ਬੇਤਰਤੀਬ", "app.containers.IdeaCards.resetFilters": "ਫਿਲਟਰ ਰੀਸੈਟ ਕਰੋ", "app.containers.IdeaCards.showXResults": "ਦਿਖਾਓ {ideasCount, plural, one {# ਨਤੀਜਾ} other {# ਨਤੀਜਾ}}", "app.containers.IdeaCards.sortTitle": "ਛਾਂਟੀ", "app.containers.IdeaCards.statusTitle": "ਸਥਿਤੀ", "app.containers.IdeaCards.statusesTitle": "ਸਥਿਤੀ", "app.containers.IdeaCards.topics": "ਟੈਗਸ", "app.containers.IdeaCards.topicsTitle": "ਟੈਗਸ", "app.containers.IdeaCards.trending": "ਪ੍ਰਚਲਿਤ", "app.containers.IdeaCards.tryDifferentFilters": "ਕੋਈ ਨਤੀਜੇ ਨਹੀਂ ਮਿਲੇ। ਕਿਰਪਾ ਕਰਕੇ ਕੋਈ ਵੱਖਰਾ ਫਿਲਟਰ ਜਾਂ ਖੋਜ ਸ਼ਬਦ ਅਜ਼ਮਾਓ।", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} ਟਿੱਪਣੀਆਂ} one {{ideasCount} ਟਿੱਪਣੀ} other {{ideasCount} ਟਿੱਪਣੀਆਂ}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} ਯੋਗਦਾਨ} one {{ideasCount} ਯੋਗਦਾਨ} other {{ideasCount} ਯੋਗਦਾਨ}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ਵਿਚਾਰ} one {{ideasCount} ਵਿਚਾਰ} other {{ideasCount} ਵਿਚਾਰ}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} ਪਹਿਲਕਦਮੀ} one {{ideasCount} ਪਹਿਲਕਦਮੀ} other {{ideasCount} ਪਹਿਲਕਦਮੀ}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} ਵਿਕਲਪ} one {{ideasCount} ਵਿਕਲਪ} other {{ideasCount} ਵਿਕਲਪ}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} ਪਟੀਸ਼ਨਾਂ} one {{ideasCount} ਪਟੀਸ਼ਨ} other {{ideasCount} ਪਟੀਸ਼ਨਾਂ}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} ਪ੍ਰੋਜੈਕਟ} one {{ideasCount} ਪ੍ਰੋਜੈਕਟ} other {{ideasCount} ਪ੍ਰੋਜੈਕਟ}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} ਪ੍ਰਸਤਾਵ} one {{ideasCount} ਪ੍ਰਸਤਾਵ} other {{ideasCount} ਪ੍ਰਸਤਾਵ}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} ਸਵਾਲ} one {{ideasCount} ਸਵਾਲ} other {{ideasCount} ਸਵਾਲ}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# ਨਤੀਜਾ} other {# ਨਤੀਜਾ}}", "app.containers.IdeasEditPage.contributionFormTitle": "ਯੋਗਦਾਨ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.IdeasEditPage.editedPostSave": "ਸੇਵ ਕਰੋ", "app.containers.IdeasEditPage.fileUploadError": "ਇੱਕ ਜਾਂ ਵੱਧ ਫ਼ਾਈਲਾਂ ਅੱਪਲੋਡ ਕਰਨ ਵਿੱਚ ਅਸਫਲ ਰਹੀਆਂ। ਕਿਰਪਾ ਕਰਕੇ ਫ਼ਾਈਲ ਦੇ ਆਕਾਰ ਅਤੇ ਫਾਰਮੈਟ ਦੀ ਜਾਂਚ ਕਰੋ ਅਤੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.containers.IdeasEditPage.formTitle": "ਵਿਚਾਰ ਸੰਪਾਦਿਤ ਕਰੋ", "app.containers.IdeasEditPage.ideasEditMetaDescription": "ਆਪਣੀ ਪੋਸਟ ਦਾ ਸੰਪਾਦਨ ਕਰੋ। ਨਵੀਂ ਜਾਣਕਾਰੀ ਸ਼ਾਮਲ ਕਰੋ ਅਤੇ ਪੁਰਾਣੀ ਜਾਣਕਾਰੀ ਨੂੰ ਬਦਲੋ।", "app.containers.IdeasEditPage.ideasEditMetaTitle": "ਸੰਪਾਦਿਤ ਕਰੋ {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "ਪਹਿਲ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.IdeasEditPage.issueFormTitle": "ਟਿੱਪਣੀ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.IdeasEditPage.optionFormTitle": "ਸੰਪਾਦਨ ਵਿਕਲਪ", "app.containers.IdeasEditPage.petitionFormTitle": "ਪਟੀਸ਼ਨ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.IdeasEditPage.projectFormTitle": "ਪ੍ਰੋਜੈਕਟ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.IdeasEditPage.proposalFormTitle": "ਪ੍ਰਸਤਾਵ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.IdeasEditPage.questionFormTitle": "ਸਵਾਲ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.IdeasEditPage.save": "ਸੇਵ ਕਰੋ", "app.containers.IdeasEditPage.submitApiError": "ਫਾਰਮ ਸਪੁਰਦ ਕਰਨ ਵਿੱਚ ਇੱਕ ਸਮੱਸਿਆ ਆਈ ਸੀ। ਕਿਰਪਾ ਕਰਕੇ ਕਿਸੇ ਵੀ ਤਰੁੱਟੀ ਦੀ ਜਾਂਚ ਕਰੋ ਅਤੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "ਸਾਰੇ ਇਨਪੁਟਸ ਪੋਸਟ ਕੀਤੇ ਗਏ ਹਨ", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "{orgName}ਦੇ ਭਾਗੀਦਾਰੀ ਪਲੇਟਫਾਰਮ 'ਤੇ ਪੋਸਟ ਕੀਤੇ ਸਾਰੇ ਇਨਪੁਟ ਦੀ ਪੜਚੋਲ ਕਰੋ।", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "ਪੋਸਟਾਂ | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "ਪੋਸਟਾਂ", "app.containers.IdeasIndexPage.loadMore": "ਹੋਰ ਲੋਡ ਕਰੋ...", "app.containers.IdeasIndexPage.loading": "ਲੋਡ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "ਮੂਲ ਰੂਪ ਵਿੱਚ ਤੁਹਾਡੀਆਂ ਬੇਨਤੀਆਂ ਤੁਹਾਡੇ ਪ੍ਰੋਫਾਈਲ ਨਾਲ ਜੁੜੀਆਂ ਹੋਣਗੀਆਂ, ਜਦੋਂ ਤੱਕ ਤੁਸੀਂ ਇਸ ਵਿਕਲਪ ਨੂੰ ਨਹੀਂ ਚੁਣਦੇ।", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "ਅਗਿਆਤ ਰੂਪ ਵਿੱਚ ਪੋਸਟ ਕਰੋ", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "ਪ੍ਰੋਫਾਈਲ ਦਿਖਣਯੋਗਤਾ", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "ਇਹ ਸਰਵੇਖਣ ਫਿਲਹਾਲ ਜਵਾਬਾਂ ਲਈ ਖੁੱਲ੍ਹਾ ਨਹੀਂ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਹੋਰ ਜਾਣਕਾਰੀ ਲਈ ਪ੍ਰੋਜੈਕਟ 'ਤੇ ਵਾਪਸ ਜਾਓ।", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "ਇਹ ਸਰਵੇਖਣ ਵਰਤਮਾਨ ਵਿੱਚ ਸਰਗਰਮ ਨਹੀਂ ਹੈ।", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "ਪ੍ਰੋਜੈਕਟ 'ਤੇ ਵਾਪਸ ਜਾਓ", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "ਤੁਸੀਂ ਇਹ ਸਰਵੇਖਣ ਪਹਿਲਾਂ ਹੀ ਪੂਰਾ ਕਰ ਲਿਆ ਹੈ।", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "ਸਰਵੇਖਣ ਪੇਸ਼ ਕੀਤਾ", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "ਤੁਹਾਡੇ ਜਵਾਬ ਲਈ ਧੰਨਵਾਦ!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "ਯੋਗਦਾਨ ਦਾ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "ਵਿਚਾਰ ਮੁੱਖ ਭਾਗ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "ਯੋਗਦਾਨ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "ਯੋਗਦਾਨ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "ਕਿਰਪਾ ਕਰਕੇ ਘੱਟੋ-ਘੱਟ ਇੱਕ ਸਹਿਯੋਗੀ ਚੁਣੋ", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "ਵਿਚਾਰ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "ਵਿਚਾਰ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਵੇਰਵਾ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "ਵਿਚਾਰ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "ਵਿਚਾਰ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "ਪਹਿਲਕਦਮੀ ਦਾ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "ਪਹਿਲ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "ਪਹਿਲ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "ਪਹਿਲ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "ਮੁੱਦੇ ਦਾ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "ਮੁੱਦੇ ਦਾ ਵੇਰਵਾ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "ਮੁੱਦੇ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "ਮੁੱਦੇ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_number_required": "ਇਹ ਖੇਤਰ ਲੋੜੀਂਦਾ ਹੈ, ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਵੈਧ ਨੰਬਰ ਦਾਖਲ ਕਰੋ", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "ਵਿਕਲਪ ਦਾ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "ਵਿਕਲਪ ਦਾ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "ਵਿਕਲਪ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "ਵਿਕਲਪ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "ਕਿਰਪਾ ਕਰਕੇ ਘੱਟੋ-ਘੱਟ ਇੱਕ ਟੈਗ ਚੁਣੋ", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "ਪਟੀਸ਼ਨ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "ਪਟੀਸ਼ਨ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "ਪਟੀਸ਼ਨ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "ਪਟੀਸ਼ਨ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "ਪ੍ਰੋਜੈਕਟ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "ਪ੍ਰੋਜੈਕਟ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "ਪ੍ਰੋਜੈਕਟ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "ਪ੍ਰੋਜੈਕਟ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "ਪ੍ਰਸਤਾਵ ਦਾ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "ਪ੍ਰਸਤਾਵ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "ਪ੍ਰਸਤਾਵ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "ਪ੍ਰਸਤਾਵ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "ਕਿਰਪਾ ਕਰਕੇ ਕੋਈ ਨੰਬਰ ਦਾਖਲ ਕਰੋ", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "ਕਿਰਪਾ ਕਰਕੇ ਕੋਈ ਨੰਬਰ ਦਾਖਲ ਕਰੋ", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "ਸਵਾਲ ਦਾ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "ਸਵਾਲ ਦਾ ਵਰਣਨ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "ਸਵਾਲ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "ਸਵਾਲ ਦਾ ਸਿਰਲੇਖ {limit} ਅੱਖਰਾਂ ਤੋਂ ਵੱਧ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਸਿਰਲੇਖ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "ਯੋਗਦਾਨ ਵਰਣਨ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "ਯੋਗਦਾਨ ਦਾ ਵਰਣਨ ਘੱਟੋ-ਘੱਟ 30 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "ਯੋਗਦਾਨ ਸਿਰਲੇਖ ਦੀ ਲੰਬਾਈ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "ਯੋਗਦਾਨ ਦਾ ਸਿਰਲੇਖ ਘੱਟੋ-ਘੱਟ 10 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "ਵਿਚਾਰ ਵਰਣਨ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "ਵਿਚਾਰ ਵਰਣਨ ਘੱਟੋ-ਘੱਟ 30 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਸਿਰਲੇਖ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "ਵਿਚਾਰ ਸਿਰਲੇਖ ਦੀ ਲੰਬਾਈ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "ਵਿਚਾਰ ਸਿਰਲੇਖ ਘੱਟੋ-ਘੱਟ 10 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_includes_banned_words": "ਤੁਸੀਂ ਇੱਕ ਜਾਂ ਵੱਧ ਸ਼ਬਦਾਂ ਦੀ ਵਰਤੋਂ ਕੀਤੀ ਹੋ ਸਕਦੀ ਹੈ ਜੋ {guidelinesLink}ਦੁਆਰਾ ਅਪਮਾਨਜਨਕ ਮੰਨੇ ਜਾਂਦੇ ਹਨ। ਕਿਰਪਾ ਕਰਕੇ ਕਿਸੇ ਵੀ ਅਪਮਾਨਜਨਕ ਸ਼ਬਦਾਂ ਨੂੰ ਹਟਾਉਣ ਲਈ ਆਪਣੇ ਟੈਕਸਟ ਨੂੰ ਬਦਲੋ ਜੋ ਮੌਜੂਦ ਹੋ ਸਕਦੀ ਹੈ।", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "ਪਹਿਲ ਵਰਣਨ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "ਪਹਿਲਕਦਮੀ ਦਾ ਵਰਣਨ ਘੱਟੋ-ਘੱਟ 30 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "ਪਹਿਲ ਦਾ ਸਿਰਲੇਖ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "ਪਹਿਲ ਦਾ ਸਿਰਲੇਖ ਘੱਟੋ-ਘੱਟ 10 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "ਮੁੱਦੇ ਦਾ ਵਰਣਨ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "ਮੁੱਦੇ ਦਾ ਵਰਣਨ ਘੱਟੋ-ਘੱਟ 30 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "ਅੰਕ ਦਾ ਸਿਰਲੇਖ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "ਅੰਕ ਦਾ ਸਿਰਲੇਖ ਘੱਟੋ-ਘੱਟ 10 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "ਵਿਕਲਪ ਦਾ ਵਰਣਨ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "ਵਿਕਲਪ ਦਾ ਵਰਣਨ ਘੱਟੋ-ਘੱਟ 30 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "ਵਿਕਲਪ ਦਾ ਸਿਰਲੇਖ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "ਵਿਕਲਪ ਦਾ ਸਿਰਲੇਖ ਘੱਟੋ-ਘੱਟ 10 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "ਪਟੀਸ਼ਨ ਵਰਣਨ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "ਪਟੀਸ਼ਨ ਦਾ ਵੇਰਵਾ ਘੱਟੋ-ਘੱਟ 30 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "ਪਟੀਸ਼ਨ ਦਾ ਸਿਰਲੇਖ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "ਪਟੀਸ਼ਨ ਦਾ ਸਿਰਲੇਖ ਘੱਟੋ-ਘੱਟ 10 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "ਪ੍ਰੋਜੈਕਟ ਵਰਣਨ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "ਪ੍ਰੋਜੈਕਟ ਵਰਣਨ ਘੱਟੋ-ਘੱਟ 30 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "ਪ੍ਰੋਜੈਕਟ ਸਿਰਲੇਖ ਦੀ ਲੰਬਾਈ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "ਪ੍ਰੋਜੈਕਟ ਸਿਰਲੇਖ ਘੱਟੋ-ਘੱਟ 10 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "ਪ੍ਰਸਤਾਵ ਵਰਣਨ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "ਪ੍ਰਸਤਾਵ ਦਾ ਵਰਣਨ ਘੱਟੋ-ਘੱਟ 30 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "ਪ੍ਰਸਤਾਵ ਸਿਰਲੇਖ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "ਪ੍ਰਸਤਾਵ ਦਾ ਸਿਰਲੇਖ ਘੱਟੋ-ਘੱਟ 10 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਵੇਰਵਾ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "ਪ੍ਰਸ਼ਨ ਵਰਣਨ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "ਸਵਾਲ ਦਾ ਵਰਣਨ ਘੱਟੋ-ਘੱਟ 30 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "ਸਵਾਲ ਦਾ ਸਿਰਲੇਖ 80 ਅੱਖਰਾਂ ਤੋਂ ਘੱਟ ਲੰਬਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "ਸਵਾਲ ਦਾ ਸਿਰਲੇਖ ਘੱਟੋ-ਘੱਟ 10 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "ਰੱਦ ਕਰੋ", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "ਹਾਂ, ਮੈਂ ਛੱਡਣਾ ਚਾਹੁੰਦਾ ਹਾਂ", "app.containers.IdeasNewPage.contributionMetaTitle1": "ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਨਵਾਂ ਯੋਗਦਾਨ ਸ਼ਾਮਲ ਕਰੋ | {orgName}", "app.containers.IdeasNewPage.editSurvey": "ਸਰਵੇਖਣ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.IdeasNewPage.ideaNewMetaDescription": "ਇੱਕ ਸਬਮਿਸ਼ਨ ਪੋਸਟ ਕਰੋ ਅਤੇ {orgName}ਦੇ ਭਾਗੀਦਾਰੀ ਪਲੇਟਫਾਰਮ 'ਤੇ ਗੱਲਬਾਤ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ।", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਨਵਾਂ ਵਿਚਾਰ ਸ਼ਾਮਲ ਕਰੋ | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਨਵੀਂ ਪਹਿਲਕਦਮੀ ਸ਼ਾਮਲ ਕਰੋ | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਨਵਾਂ ਮੁੱਦਾ ਸ਼ਾਮਲ ਕਰੋ | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "ਕੀ ਤੁਸੀਂ ਪੱਕਾ ਛੱਡਣਾ ਚਾਹੁੰਦੇ ਹੋ?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "ਤੁਹਾਡੇ ਡਰਾਫਟ ਜਵਾਬਾਂ ਨੂੰ ਨਿੱਜੀ ਤੌਰ 'ਤੇ ਸੁਰੱਖਿਅਤ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਤੁਸੀਂ ਇਸਨੂੰ ਬਾਅਦ ਵਿੱਚ ਪੂਰਾ ਕਰਨ ਲਈ ਵਾਪਸ ਆ ਸਕਦੇ ਹੋ।", "app.containers.IdeasNewPage.leaveSurvey": "ਸਰਵੇਖਣ ਛੱਡੋ", "app.containers.IdeasNewPage.leaveSurveyText": "ਤੁਹਾਡੇ ਜਵਾਬ ਸੁਰੱਖਿਅਤ ਨਹੀਂ ਕੀਤੇ ਜਾਣਗੇ।", "app.containers.IdeasNewPage.optionMetaTitle1": "ਪ੍ਰੋਜੈਕਟ ਲਈ ਨਵਾਂ ਵਿਕਲਪ ਸ਼ਾਮਲ ਕਰੋ | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਨਵੀਂ ਪਟੀਸ਼ਨ ਸ਼ਾਮਲ ਕਰੋ | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਨਵਾਂ ਪ੍ਰੋਜੈਕਟ ਸ਼ਾਮਲ ਕਰੋ | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਨਵਾਂ ਪ੍ਰਸਤਾਵ ਸ਼ਾਮਲ ਕਰੋ | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਨਵਾਂ ਸਵਾਲ ਸ਼ਾਮਲ ਕਰੋ | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "ਸੱਦਾ ਸਵੀਕਾਰ ਕਰੋ", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "ਸਹਿ-ਸਪਾਂਸਰਸ਼ਿਪ ਦਾ ਸੱਦਾ", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "ਸਹਿ-ਪ੍ਰਾਯੋਜਕ", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "ਤੁਹਾਨੂੰ ਸਹਿ-ਪ੍ਰਾਯੋਜਕ ਬਣਨ ਲਈ ਸੱਦਾ ਦਿੱਤਾ ਗਿਆ ਹੈ।", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "ਸੱਦਾ ਸਵੀਕਾਰ ਕਰ ਲਿਆ", "app.containers.IdeasShow.Cosponsorship.pending": "ਬਕਾਇਆ", "app.containers.IdeasShow.MetaInformation.attachments": "ਅਟੈਚਮੈਂਟਸ", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{date}'ਤੇ {userName}", "app.containers.IdeasShow.MetaInformation.currentStatus": "ਮੌਜੂਦਾ ਸਥਿਤੀ", "app.containers.IdeasShow.MetaInformation.location": "ਟਿਕਾਣਾ", "app.containers.IdeasShow.MetaInformation.postedBy": "ਵੱਲੋਂ ਪੋਸਟ ਕੀਤਾ ਗਿਆ", "app.containers.IdeasShow.MetaInformation.similar": "ਸਮਾਨ ਇਨਪੁੱਟ", "app.containers.IdeasShow.MetaInformation.topics": "ਟੈਗਸ", "app.containers.IdeasShow.commentCTA": "ਇੱਕ ਟਿੱਪਣੀ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.IdeasShow.contributionEmailSharingBody": "{postUrl}'ਤੇ ਇਸ ਯੋਗਦਾਨ '{postTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.containers.IdeasShow.contributionEmailSharingSubject": "ਇਸ ਯੋਗਦਾਨ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "ਆਪਣਾ ਯੋਗਦਾਨ ਦਰਜ ਕਰਨ ਲਈ ਧੰਨਵਾਦ!", "app.containers.IdeasShow.contributionTwitterMessage": "ਇਸ ਯੋਗਦਾਨ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "ਇਸ ਯੋਗਦਾਨ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.currentStatus": "ਮੌਜੂਦਾ ਸਥਿਤੀ", "app.containers.IdeasShow.deletedUser": "ਅਗਿਆਤ ਲੇਖਕ", "app.containers.IdeasShow.ideaEmailSharingBody": "{ideaUrl}'ਤੇ ਮੇਰੇ ਵਿਚਾਰ '{ideaTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.containers.IdeasShow.ideaEmailSharingSubject": "ਮੇਰੇ ਵਿਚਾਰ ਦਾ ਸਮਰਥਨ ਕਰੋ: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "ਇਸ ਵਿਚਾਰ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "ਇਸ ਵਿਚਾਰ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "ਇਸ ਟਿੱਪਣੀ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.imported": "ਆਯਾਤ ਕੀਤਾ", "app.containers.IdeasShow.importedTooltip": "ਇਹ {inputTerm} ਔਫਲਾਈਨ ਇਕੱਠਾ ਕੀਤਾ ਗਿਆ ਸੀ ਅਤੇ ਪਲੇਟਫਾਰਮ 'ਤੇ ਆਪਣੇ ਆਪ ਅੱਪਲੋਡ ਕੀਤਾ ਗਿਆ ਸੀ।", "app.containers.IdeasShow.initiativeEmailSharingBody": "{ideaUrl}'ਤੇ ਇਸ ਪਹਿਲ '{ideaTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "ਇਸ ਪਹਿਲ ਦਾ ਸਮਰਥਨ ਕਰੋ: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "ਆਪਣੀ ਪਹਿਲ ਦਰਜ ਕਰਨ ਲਈ ਧੰਨਵਾਦ!", "app.containers.IdeasShow.initiativeTwitterMessage": "ਇਸ ਪਹਿਲ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "ਇਸ ਪਹਿਲ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "{postUrl}'ਤੇ ਇਸ ਟਿੱਪਣੀ '{postTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.containers.IdeasShow.issueEmailSharingSubject": "ਇਸ ਟਿੱਪਣੀ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "ਆਪਣੀ ਟਿੱਪਣੀ ਦਰਜ ਕਰਨ ਲਈ ਧੰਨਵਾਦ!", "app.containers.IdeasShow.issueTwitterMessage": "ਇਸ ਟਿੱਪਣੀ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.metaTitle": "ਇਨਪੁਟ: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "{postUrl}'ਤੇ ਇਸ ਵਿਕਲਪ '{postTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.containers.IdeasShow.optionEmailSharingSubject": "ਇਸ ਵਿਕਲਪ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "ਤੁਹਾਡਾ ਵਿਕਲਪ ਸਫਲਤਾਪੂਰਵਕ ਪੋਸਟ ਕੀਤਾ ਗਿਆ ਹੈ!", "app.containers.IdeasShow.optionTwitterMessage": "ਇਸ ਵਿਕਲਪ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "ਇਸ ਵਿਕਲਪ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "{ideaUrl}'ਤੇ ਇਸ ਪਟੀਸ਼ਨ '{ideaTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.containers.IdeasShow.petitionEmailSharingSubject": "ਇਸ ਪਟੀਸ਼ਨ ਦਾ ਸਮਰਥਨ ਕਰੋ: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "ਆਪਣੀ ਪਟੀਸ਼ਨ ਦਰਜ ਕਰਨ ਲਈ ਧੰਨਵਾਦ!", "app.containers.IdeasShow.petitionTwitterMessage": "ਇਸ ਪਟੀਸ਼ਨ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "ਇਸ ਪਟੀਸ਼ਨ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "{postUrl}'ਤੇ ਇਸ ਪ੍ਰੋਜੈਕਟ '{postTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.containers.IdeasShow.projectEmailSharingSubject": "ਇਸ ਪ੍ਰੋਜੈਕਟ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "ਆਪਣਾ ਪ੍ਰੋਜੈਕਟ ਦਰਜ ਕਰਨ ਲਈ ਧੰਨਵਾਦ!", "app.containers.IdeasShow.projectTwitterMessage": "ਇਸ ਪ੍ਰੋਜੈਕਟ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "ਇਸ ਪ੍ਰੋਜੈਕਟ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "{ideaUrl}'ਤੇ ਇਸ ਪ੍ਰਸਤਾਵ '{ideaTitle}' ਦਾ ਸਮਰਥਨ ਕਰੋ!", "app.containers.IdeasShow.proposalEmailSharingSubject": "ਇਸ ਪ੍ਰਸਤਾਵ ਦਾ ਸਮਰਥਨ ਕਰੋ: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "ਆਪਣਾ ਪ੍ਰਸਤਾਵ ਪੇਸ਼ ਕਰਨ ਲਈ ਧੰਨਵਾਦ!", "app.containers.IdeasShow.proposalTwitterMessage": "ਇਸ ਪ੍ਰਸਤਾਵ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "ਇਸ ਪ੍ਰਸਤਾਵ ਦਾ ਸਮਰਥਨ ਕਰੋ: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "ਵੋਟ ਪਾਉਣ ਲਈ ਬਚਿਆ ਸਮਾਂ:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{votingThreshold} ਲੋੜੀਂਦੀਆਂ ਵੋਟਾਂ ਵਿੱਚੋਂ {xVotes}", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "ਵੋਟ ਰੱਦ ਕਰੋ", "app.containers.IdeasShow.proposals.VoteControl.days": "ਦਿਨ", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "ਸਾਡੇ ਦਿਸ਼ਾ-ਨਿਰਦੇਸ਼", "app.containers.IdeasShow.proposals.VoteControl.hours": "ਘੰਟੇ", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "ਸਥਿਤੀ ਅਤੇ ਵੋਟਾਂ", "app.containers.IdeasShow.proposals.VoteControl.minutes": "ਮਿੰਟ", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "ਹੋਰ ਜਾਣਕਾਰੀ", "app.containers.IdeasShow.proposals.VoteControl.vote": "ਵੋਟ ਕਰੋ", "app.containers.IdeasShow.proposals.VoteControl.voted": "ਵੋਟ ਪਾਈ", "app.containers.IdeasShow.proposals.VoteControl.votedText": "ਜਦੋਂ ਇਹ ਪਹਿਲ ਅਗਲੇ ਪੜਾਅ 'ਤੇ ਜਾਂਦੀ ਹੈ ਤਾਂ ਤੁਹਾਨੂੰ ਸੂਚਿਤ ਕੀਤਾ ਜਾਵੇਗਾ। {x, plural, =0 {ਇੱਥੇ {xDays} ਬਾਕੀ ਹੈ।} one {{xDays} ਬਾਕੀ ਹੈ।} other {{xDays} ਬਾਕੀ ਹਨ।}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "ਤੁਹਾਡੀ ਵੋਟ ਸਪੁਰਦ ਕਰ ਦਿੱਤੀ ਗਈ ਹੈ!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "ਬਦਕਿਸਮਤੀ ਨਾਲ, ਤੁਸੀਂ ਇਸ ਪ੍ਰਸਤਾਵ 'ਤੇ ਵੋਟ ਨਹੀਂ ਕਰ ਸਕਦੇ। {link}ਵਿੱਚ ਕਿਉਂ ਪੜ੍ਹੋ।", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {ਇੱਕ ਦਿਨ ਤੋਂ ਘੱਟ} one {ਇੱਕ ਦਿਨ} other {# ਦਿਨ}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {ਕੋਈ ਵੋਟ ਨਹੀਂ} one {1 ਵੋਟ} other {# ਵੋਟਾਂ}}", "app.containers.IdeasShow.questionEmailSharingBody": "{postUrl}'ਤੇ ਇਸ ਸਵਾਲ '{postTitle}' ਬਾਰੇ ਚਰਚਾ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ!", "app.containers.IdeasShow.questionEmailSharingSubject": "ਚਰਚਾ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "ਤੁਹਾਡਾ ਸਵਾਲ ਸਫਲਤਾਪੂਰਵਕ ਪੋਸਟ ਕੀਤਾ ਗਿਆ ਹੈ!", "app.containers.IdeasShow.questionTwitterMessage": "ਚਰਚਾ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "ਚਰਚਾ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "ਤੁਸੀਂ ਇਸਦੀ ਸਪੈਮ ਵਜੋਂ ਰਿਪੋਰਟ ਕਿਉਂ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?", "app.containers.IdeasShow.share": "ਸ਼ੇਅਰ ਕਰੋ", "app.containers.IdeasShow.sharingModalSubtitle": "ਵੱਧ ਤੋਂ ਵੱਧ ਲੋਕਾਂ ਤੱਕ ਪਹੁੰਚੋ ਅਤੇ ਆਪਣੀ ਆਵਾਜ਼ ਸੁਣਾਓ।", "app.containers.IdeasShow.sharingModalTitle": "ਆਪਣਾ ਵਿਚਾਰ ਪੇਸ਼ ਕਰਨ ਲਈ ਧੰਨਵਾਦ!", "app.containers.Navbar.completeOnboarding": "ਆਨ-ਬੋਰਡਿੰਗ ਨੂੰ ਪੂਰਾ ਕਰੋ", "app.containers.Navbar.completeProfile": "ਪੂਰਾ ਪ੍ਰੋਫਾਈਲ", "app.containers.Navbar.confirmEmail2": "ਈਮੇਲ ਦੀ ਪੁਸ਼ਟੀ ਕਰਨਾ", "app.containers.Navbar.unverified": "ਅਸਪਸ਼ਟ", "app.containers.Navbar.verified": "ਪ੍ਰਮਾਣਿਤ", "app.containers.NewAuthModal.beforeYouFollow": "ਇਸ ਤੋਂ ਪਹਿਲਾਂ ਕਿ ਤੁਸੀਂ ਪਾਲਣਾ ਕਰੋ", "app.containers.NewAuthModal.beforeYouParticipate": "ਇਸ ਤੋਂ ਪਹਿਲਾਂ ਕਿ ਤੁਸੀਂ ਹਿੱਸਾ ਲਓ", "app.containers.NewAuthModal.completeYourProfile": "ਆਪਣੀ ਪ੍ਰੋਫਾਈਲ ਨੂੰ ਪੂਰਾ ਕਰੋ", "app.containers.NewAuthModal.confirmYourEmail": "ਆਪਣੀ ਈਮੇਲ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ", "app.containers.NewAuthModal.logIn": "ਲਾਗਿਨ", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "ਜਾਰੀ ਰੱਖਣ ਲਈ ਹੇਠਾਂ ਦਿੱਤੀਆਂ ਸ਼ਰਤਾਂ ਦੀ ਸਮੀਖਿਆ ਕਰੋ।", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਪ੍ਰੋਫਾਈਲ ਪੂਰਾ ਕਰੋ।", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "ਲੌਗਇਨ ਵਿਕਲਪਾਂ 'ਤੇ ਵਾਪਸ ਜਾਓ", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "ਕੀ ਤੁਹਾਡੇ ਕੋਲ ਖਾਤਾ ਨਹੀਂ ਹੈ? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "ਸਾਇਨ ਅਪ", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "ਕੋਡ ਵਿੱਚ 4 ਅੰਕ ਹੋਣੇ ਚਾਹੀਦੇ ਹਨ।", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "FranceConnect ਨਾਲ ਜਾਰੀ ਰੱਖੋ", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "ਇਸ ਪਲੇਟਫਾਰਮ 'ਤੇ ਕੋਈ ਪ੍ਰਮਾਣੀਕਰਨ ਵਿਧੀਆਂ ਯੋਗ ਨਹੀਂ ਹਨ।", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "ਜਾਰੀ ਰੱਖ ਕੇ, ਤੁਸੀਂ ਇਸ ਪਲੇਟਫਾਰਮ ਤੋਂ ਈਮੇਲਾਂ ਪ੍ਰਾਪਤ ਕਰਨ ਲਈ ਸਹਿਮਤ ਹੁੰਦੇ ਹੋ। ਤੁਸੀਂ 'ਮੇਰੀ ਸੈਟਿੰਗ' ਪੰਨੇ ਵਿੱਚ ਚੁਣ ਸਕਦੇ ਹੋ ਕਿ ਤੁਸੀਂ ਕਿਹੜੀਆਂ ਈਮੇਲਾਂ ਪ੍ਰਾਪਤ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ।", "app.containers.NewAuthModal.steps.EmailSignUp.email": "ਈਮੇਲ", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "ਸਹੀ ਫਾਰਮੈਟ ਵਿੱਚ ਇੱਕ ਈਮੇਲ ਪਤਾ ਪ੍ਰਦਾਨ ਕਰੋ, ਉਦਾਹਰਨ ਲਈ <EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "ਇੱਕ ਈਮੇਲ ਪਤਾ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "ਜਾਰੀ ਰੱਖਣ ਲਈ ਆਪਣਾ ਈਮੇਲ ਪਤਾ ਦਾਖਲ ਕਰੋ।", "app.containers.NewAuthModal.steps.Password.forgotPassword": "ਪਾਸਵਰਡ ਭੁੱਲ ਗਏ?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "ਆਪਣੇ ਖਾਤੇ ਵਿੱਚ ਲੌਗ ਇਨ ਕਰੋ: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਪਾਸਵਰਡ ਦਾਖਲ ਕਰੋ", "app.containers.NewAuthModal.steps.Password.password": "ਪਾਸਵਰਡ", "app.containers.NewAuthModal.steps.Password.rememberMe": "ਮੇਰੀ ਯਾਦ ਹੈ", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "ਜੇਕਰ ਕੋਈ ਜਨਤਕ ਕੰਪਿਊਟਰ ਵਰਤ ਰਿਹਾ ਹੋਵੇ ਤਾਂ ਚੋਣ ਨਾ ਕਰੋ", "app.containers.NewAuthModal.steps.Success.allDone": "ਸਭ ਕੀਤਾ", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "ਹੁਣ ਆਪਣੀ ਭਾਗੀਦਾਰੀ ਜਾਰੀ ਰੱਖੋ।", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "ਤੁਹਾਡੀ ਪਛਾਣ ਦੀ ਪੁਸ਼ਟੀ ਕੀਤੀ ਗਈ ਹੈ। ਤੁਸੀਂ ਹੁਣ ਇਸ ਪਲੇਟਫਾਰਮ 'ਤੇ ਭਾਈਚਾਰੇ ਦੇ ਪੂਰੇ ਮੈਂਬਰ ਹੋ।", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "ਤੁਸੀਂ ਹੁਣ ਪ੍ਰਮਾਣਿਤ ਹੋ!", "app.containers.NewAuthModal.steps.close": "ਬੰਦ ਕਰੋ", "app.containers.NewAuthModal.steps.continue": "ਜਾਰੀ ਰੱਖੋ", "app.containers.NewAuthModal.whatAreYouInterestedIn": "ਤੁਹਾਨੂੰ ਕਿਸ ਵਿੱਚ ਦਿਲਚਸਪੀ ਹੈ?", "app.containers.NewAuthModal.youCantParticipate": "ਤੁਸੀਂ ਹਿੱਸਾ ਨਹੀਂ ਲੈ ਸਕਦੇ", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {ਕੋਈ ਅਣਦੇਖੀ ਸੂਚਨਾਵਾਂ} one {1 ਅਣਦੇਖੀ ਸੂਚਨਾ} other {# ਅਣਦੇਖੀ ਸੂਚਨਾਵਾਂ}}", "app.containers.NotificationMenu.adminRightsReceived": "ਤੁਸੀਂ ਹੁਣ ਪਲੇਟਫਾਰਮ ਦੇ ਪ੍ਰਸ਼ਾਸਕ ਹੋ", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "\"{postTitle}\" ਉੱਤੇ ਤੁਹਾਡੀ ਟਿੱਪਣੀ ਨੂੰ ਇੱਕ ਐਡਮਿਨ ਦੁਆਰਾ ਮਿਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ ਕਿਉਂਕਿ\n      {reasonCode, select, irrelevant {ਇਹ ਅਪ੍ਰਸੰਗਿਕ ਹੈ} inappropriate {ਇਸਦੀ ਸਮੱਗਰੀ ਅਣਉਚਿਤ ਹੈ} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} ਨੇ ਤੁਹਾਡਾ ਸਹਿ-ਸਪਾਂਸਰਸ਼ਿਪ ਸੱਦਾ ਸਵੀਕਾਰ ਕਰ ਲਿਆ ਹੈ", "app.containers.NotificationMenu.deletedUser": "ਅਗਿਆਤ ਲੇਖਕ", "app.containers.NotificationMenu.error": "ਸੂਚਨਾਵਾਂ ਲੋਡ ਨਹੀਂ ਕੀਤੀਆਂ ਜਾ ਸਕੀਆਂ", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} ਨੇ ਤੁਹਾਨੂੰ ਸੌਂਪੇ ਗਏ ਇੱਕ ਇਨਪੁਟ 'ਤੇ ਅੰਦਰੂਨੀ ਤੌਰ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} ਨੇ ਅੰਦਰੂਨੀ ਤੌਰ 'ਤੇ ਉਸ ਇਨਪੁਟ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ ਜਿਸ 'ਤੇ ਤੁਸੀਂ ਅੰਦਰੂਨੀ ਤੌਰ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ ਸੀ", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} ਨੇ ਤੁਹਾਡੇ ਦੁਆਰਾ ਪ੍ਰਬੰਧਿਤ ਕੀਤੇ ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਇੱਕ ਇਨਪੁਟ 'ਤੇ ਅੰਦਰੂਨੀ ਤੌਰ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} ਨੇ ਇੱਕ ਗੈਰ-ਪ੍ਰਬੰਧਿਤ ਪ੍ਰੋਜੈਕਟ ਵਿੱਚ ਇੱਕ ਅਸਾਈਨ ਕੀਤੇ ਇਨਪੁਟ 'ਤੇ ਅੰਦਰੂਨੀ ਤੌਰ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} ਨੇ ਤੁਹਾਡੀ ਅੰਦਰੂਨੀ ਟਿੱਪਣੀ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} ਨੇ ਤੁਹਾਨੂੰ ਇੱਕ ਯੋਗਦਾਨ ਸਹਿ-ਪ੍ਰਾਯੋਜਿਤ ਕਰਨ ਲਈ ਸੱਦਾ ਦਿੱਤਾ", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} ਨੇ ਤੁਹਾਨੂੰ ਇੱਕ ਵਿਚਾਰ ਸਹਿ-ਪ੍ਰਾਯੋਜਿਤ ਕਰਨ ਲਈ ਸੱਦਾ ਦਿੱਤਾ", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} ਨੇ ਤੁਹਾਨੂੰ ਇੱਕ ਪ੍ਰਸਤਾਵ ਨੂੰ ਸਹਿਯੋਗੀ ਕਰਨ ਲਈ ਸੱਦਾ ਦਿੱਤਾ", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} ਨੇ ਤੁਹਾਨੂੰ ਕਿਸੇ ਮੁੱਦੇ ਨੂੰ ਸਹਿ-ਪ੍ਰਾਯੋਜਿਤ ਕਰਨ ਲਈ ਸੱਦਾ ਦਿੱਤਾ", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} ਨੇ ਤੁਹਾਨੂੰ ਇੱਕ ਵਿਕਲਪ ਸਹਿ-ਪ੍ਰਾਯੋਜਿਤ ਕਰਨ ਲਈ ਸੱਦਾ ਦਿੱਤਾ", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} ਨੇ ਤੁਹਾਨੂੰ ਇੱਕ ਪਟੀਸ਼ਨ ਸਹਿ-ਪ੍ਰਾਯੋਜਿਤ ਕਰਨ ਲਈ ਸੱਦਾ ਦਿੱਤਾ", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} ਨੇ ਤੁਹਾਨੂੰ ਇੱਕ ਪ੍ਰੋਜੈਕਟ ਸਹਿ-ਪ੍ਰਾਯੋਜਿਤ ਕਰਨ ਲਈ ਸੱਦਾ ਦਿੱਤਾ", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} ਨੇ ਤੁਹਾਨੂੰ ਪ੍ਰਸਤਾਵ ਨੂੰ ਸਹਿ-ਪ੍ਰਾਯੋਜਿਤ ਕਰਨ ਲਈ ਸੱਦਾ ਦਿੱਤਾ", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} ਨੇ ਤੁਹਾਨੂੰ ਇੱਕ ਪ੍ਰਸ਼ਨ ਸਹਿ-ਪ੍ਰਾਯੋਜਿਤ ਕਰਨ ਲਈ ਸੱਦਾ ਦਿੱਤਾ", "app.containers.NotificationMenu.loadMore": "ਹੋਰ ਲੋਡ ਕਰੋ...", "app.containers.NotificationMenu.loading": "ਸੂਚਨਾਵਾਂ ਲੋਡ ਕੀਤੀਆਂ ਜਾ ਰਹੀਆਂ ਹਨ...", "app.containers.NotificationMenu.mentionInComment": "{name} ਨੇ ਇੱਕ ਟਿੱਪਣੀ ਵਿੱਚ ਤੁਹਾਡਾ ਜ਼ਿਕਰ ਕੀਤਾ", "app.containers.NotificationMenu.mentionInInternalComment": "{name} ਨੇ ਇੱਕ ਅੰਦਰੂਨੀ ਟਿੱਪਣੀ ਵਿੱਚ ਤੁਹਾਡਾ ਜ਼ਿਕਰ ਕੀਤਾ", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} ਨੇ ਇੱਕ ਅਧਿਕਾਰਤ ਅੱਪਡੇਟ ਵਿੱਚ ਤੁਹਾਡਾ ਜ਼ਿਕਰ ਕੀਤਾ ਹੈ", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "ਤੁਸੀਂ ਆਪਣਾ ਸਰਵੇਖਣ ਸਪੁਰਦ ਨਹੀਂ ਕੀਤਾ", "app.containers.NotificationMenu.noNotifications": "ਤੁਹਾਡੇ ਕੋਲ ਅਜੇ ਕੋਈ ਸੂਚਨਾਵਾਂ ਨਹੀਂ ਹਨ", "app.containers.NotificationMenu.notificationsLabel": "ਸੂਚਨਾਵਾਂ", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} ਨੇ ਤੁਹਾਡੇ ਦੁਆਰਾ ਅਨੁਸਰਣ ਕੀਤੇ ਯੋਗਦਾਨ 'ਤੇ ਇੱਕ ਅਧਿਕਾਰਤ ਅਪਡੇਟ ਦਿੱਤਾ", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} ਨੇ ਤੁਹਾਡੇ ਦੁਆਰਾ ਅਨੁਸਰਣ ਕੀਤੇ ਵਿਚਾਰ 'ਤੇ ਇੱਕ ਅਧਿਕਾਰਤ ਅਪਡੇਟ ਦਿੱਤਾ", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} ਨੇ ਤੁਹਾਡੇ ਦੁਆਰਾ ਅਨੁਸਰਣ ਕੀਤੀ ਪਹਿਲਕਦਮੀ 'ਤੇ ਅਧਿਕਾਰਤ ਅਪਡੇਟ ਦਿੱਤੀ", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} ਨੇ ਤੁਹਾਡੇ ਦੁਆਰਾ ਅਨੁਸਰਣ ਕੀਤੇ ਗਏ ਮੁੱਦੇ 'ਤੇ ਅਧਿਕਾਰਤ ਅਪਡੇਟ ਦਿੱਤਾ", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} ਨੇ ਤੁਹਾਡੇ ਦੁਆਰਾ ਅਨੁਸਰਣ ਕੀਤੇ ਵਿਕਲਪ 'ਤੇ ਇੱਕ ਅਧਿਕਾਰਤ ਅਪਡੇਟ ਦਿੱਤਾ", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} ਨੇ ਤੁਹਾਡੇ ਦੁਆਰਾ ਅਨੁਸਰਣ ਕੀਤੀ ਪਟੀਸ਼ਨ 'ਤੇ ਇੱਕ ਅਧਿਕਾਰਤ ਅਪਡੇਟ ਦਿੱਤਾ", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} ਨੇ ਤੁਹਾਡੇ ਦੁਆਰਾ ਅਨੁਸਰਣ ਕੀਤੇ ਪ੍ਰੋਜੈਕਟ 'ਤੇ ਇੱਕ ਅਧਿਕਾਰਤ ਅਪਡੇਟ ਦਿੱਤਾ", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} ਨੇ ਤੁਹਾਡੇ ਦੁਆਰਾ ਅਨੁਸਰਣ ਕੀਤੇ ਪ੍ਰਸਤਾਵ 'ਤੇ ਅਧਿਕਾਰਤ ਅਪਡੇਟ ਦਿੱਤਾ", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} ਨੇ ਤੁਹਾਡੇ ਦੁਆਰਾ ਅਨੁਸਰਣ ਕੀਤੇ ਸਵਾਲ 'ਤੇ ਅਧਿਕਾਰਤ ਅੱਪਡੇਟ ਦਿੱਤਾ", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} ਤੁਹਾਨੂੰ ਸੌਂਪਿਆ ਗਿਆ ਸੀ", "app.containers.NotificationMenu.projectModerationRightsReceived": "ਤੁਸੀਂ ਹੁਣ {projectLink}ਦੇ ਪ੍ਰੋਜੈਕਟ ਮੈਨੇਜਰ ਹੋ", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} ਇੱਕ ਨਵੇਂ ਪੜਾਅ ਵਿੱਚ ਦਾਖਲ ਹੋਇਆ", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} {phaseStartAt}'ਤੇ ਇੱਕ ਨਵੇਂ ਪੜਾਅ ਵਿੱਚ ਦਾਖਲ ਹੋਵੇਗਾ", "app.containers.NotificationMenu.projectPublished": "ਇੱਕ ਨਵਾਂ ਪ੍ਰੋਜੈਕਟ ਪ੍ਰਕਾਸ਼ਿਤ ਕੀਤਾ ਗਿਆ ਸੀ", "app.containers.NotificationMenu.projectReviewRequest": "{name} ਨੇ ਪ੍ਰੋਜੈਕਟ \"{projectTitle}\" ਨੂੰ ਪ੍ਰਕਾਸ਼ਿਤ ਕਰਨ ਲਈ ਮਨਜ਼ੂਰੀ ਦੀ ਬੇਨਤੀ ਕੀਤੀ।", "app.containers.NotificationMenu.projectReviewStateChange": "{name} ਪ੍ਰਕਾਸ਼ਨ ਲਈ \"{projectTitle}\" ਨੂੰ ਮਨਜ਼ੂਰੀ ਦਿੱਤੀ ਗਈ", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} ਸਥਿਤੀ {status}ਵਿੱਚ ਬਦਲ ਗਈ ਹੈ", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} ਵੋਟਿੰਗ ਥ੍ਰੈਸ਼ਹੋਲਡ 'ਤੇ ਪਹੁੰਚ ਗਿਆ", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} ਨੇ ਤੁਹਾਡਾ ਸੱਦਾ ਸਵੀਕਾਰ ਕਰ ਲਿਆ", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} ਨੇ ਉਸ ਯੋਗਦਾਨ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ ਜਿਸਦਾ ਤੁਸੀਂ ਅਨੁਸਰਣ ਕਰਦੇ ਹੋ", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} ਨੇ ਇੱਕ ਵਿਚਾਰ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ ਜਿਸਦਾ ਤੁਸੀਂ ਅਨੁਸਰਣ ਕਰਦੇ ਹੋ", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} ਨੇ ਇੱਕ ਪਹਿਲਕਦਮੀ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ ਜਿਸਦਾ ਤੁਸੀਂ ਅਨੁਸਰਣ ਕਰਦੇ ਹੋ", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} ਨੇ ਉਸ ਮੁੱਦੇ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ ਜਿਸਦਾ ਤੁਸੀਂ ਅਨੁਸਰਣ ਕਰਦੇ ਹੋ", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} ਨੇ ਉਸ ਵਿਕਲਪ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ ਜਿਸਦਾ ਤੁਸੀਂ ਅਨੁਸਰਣ ਕਰਦੇ ਹੋ", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} ਨੇ ਇੱਕ ਪਟੀਸ਼ਨ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ ਜਿਸਦਾ ਤੁਸੀਂ ਅਨੁਸਰਣ ਕਰਦੇ ਹੋ", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} ਨੇ ਉਸ ਪ੍ਰੋਜੈਕਟ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ ਜਿਸਦਾ ਤੁਸੀਂ ਅਨੁਸਰਣ ਕਰਦੇ ਹੋ", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} ਨੇ ਉਸ ਪ੍ਰਸਤਾਵ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ ਜਿਸਦਾ ਤੁਸੀਂ ਅਨੁਸਰਣ ਕਰਦੇ ਹੋ", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} ਨੇ ਉਸ ਸਵਾਲ 'ਤੇ ਟਿੱਪਣੀ ਕੀਤੀ ਜਿਸਦਾ ਤੁਸੀਂ ਅਨੁਸਰਣ ਕਰਦੇ ਹੋ", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} ਨੇ \"{postTitle}\" ਨੂੰ ਸਪੈਮ ਵਜੋਂ ਰਿਪੋਰਟ ਕੀਤਾ", "app.containers.NotificationMenu.userReactedToYourComment": "{name} ਨੇ ਤੁਹਾਡੀ ਟਿੱਪਣੀ 'ਤੇ ਪ੍ਰਤੀਕਿਰਿਆ ਦਿੱਤੀ", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} ਨੇ \"{postTitle}\" 'ਤੇ ਸਪੈਮ ਵਜੋਂ ਇੱਕ ਟਿੱਪਣੀ ਦੀ ਰਿਪੋਰਟ ਕੀਤੀ", "app.containers.NotificationMenu.votingBasketNotSubmitted": "ਤੁਸੀਂ ਆਪਣੀਆਂ ਵੋਟਾਂ ਸਪੁਰਦ ਨਹੀਂ ਕੀਤੀਆਂ", "app.containers.NotificationMenu.votingBasketSubmitted": "ਤੁਸੀਂ ਸਫਲਤਾਪੂਰਵਕ ਵੋਟ ਕੀਤੀ", "app.containers.NotificationMenu.votingLastChance": "{phaseTitle}ਲਈ ਵੋਟ ਪਾਉਣ ਦਾ ਆਖਰੀ ਮੌਕਾ", "app.containers.NotificationMenu.votingResults": "{phaseTitle} ਵੋਟਾਂ ਦੇ ਨਤੀਜੇ ਸਾਹਮਣੇ ਆਏ", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} ਤੁਹਾਨੂੰ {postTitle} ਸੌਂਪਿਆ ਗਿਆ", "app.containers.PasswordRecovery.emailError": "ਇਹ ਇੱਕ ਵੈਧ ਈਮੇਲ ਨਹੀਂ ਜਾਪਦਾ", "app.containers.PasswordRecovery.emailLabel": "ਈਮੇਲ", "app.containers.PasswordRecovery.emailPlaceholder": "ਮੇਰਾ ਈਮੇਲ ਪਤਾ", "app.containers.PasswordRecovery.helmetDescription": "ਆਪਣਾ ਪਾਸਵਰਡ ਪੰਨਾ ਰੀਸੈਟ ਕਰੋ", "app.containers.PasswordRecovery.helmetTitle": "ਆਪਣਾ ਪਾਸਵਰਡ ਰੀਸੈਟ ਕਰੋ", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "ਜੇਕਰ ਇਹ ਈਮੇਲ ਪਤਾ ਪਲੇਟਫਾਰਮ 'ਤੇ ਰਜਿਸਟਰਡ ਹੈ, ਤਾਂ ਇੱਕ ਪਾਸਵਰਡ ਰੀਸੈਟ ਲਿੰਕ ਭੇਜਿਆ ਗਿਆ ਹੈ।", "app.containers.PasswordRecovery.resetPassword": "ਇੱਕ ਪਾਸਵਰਡ ਰੀਸੈਟ ਲਿੰਕ ਭੇਜੋ", "app.containers.PasswordRecovery.submitError": "ਅਸੀਂ ਇਸ ਈਮੇਲ ਨਾਲ ਲਿੰਕ ਕੀਤਾ ਖਾਤਾ ਨਹੀਂ ਲੱਭ ਸਕੇ। ਤੁਸੀਂ ਇਸਦੀ ਬਜਾਏ ਸਾਈਨ ਅੱਪ ਕਰਨ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰ ਸਕਦੇ ਹੋ।", "app.containers.PasswordRecovery.subtitle": "ਅਸੀਂ ਨਵਾਂ ਪਾਸਵਰਡ ਚੁਣਨ ਲਈ ਲਿੰਕ ਕਿੱਥੇ ਭੇਜ ਸਕਦੇ ਹਾਂ?", "app.containers.PasswordRecovery.title": "ਪਾਸਵਰਡ ਰੀਸੈੱਟ", "app.containers.PasswordReset.helmetDescription": "ਆਪਣਾ ਪਾਸਵਰਡ ਪੰਨਾ ਰੀਸੈਟ ਕਰੋ", "app.containers.PasswordReset.helmetTitle": "ਆਪਣਾ ਪਾਸਵਰਡ ਰੀਸੈਟ ਕਰੋ", "app.containers.PasswordReset.login": "ਲਾਗਿਨ", "app.containers.PasswordReset.passwordError": "ਪਾਸਵਰਡ ਘੱਟੋ-ਘੱਟ 8 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.containers.PasswordReset.passwordLabel": "ਪਾਸਵਰਡ", "app.containers.PasswordReset.passwordPlaceholder": "ਨਵਾਂ ਪਾਸਵਰਡ", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "ਤੁਹਾਡਾ ਪਾਸਵਰਡ ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤਾ ਗਿਆ ਹੈ।", "app.containers.PasswordReset.pleaseLogInMessage": "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੇ ਨਵੇਂ ਪਾਸਵਰਡ ਨਾਲ ਲਾਗਇਨ ਕਰੋ।", "app.containers.PasswordReset.requestNewPasswordReset": "ਇੱਕ ਨਵਾਂ ਪਾਸਵਰਡ ਰੀਸੈਟ ਕਰਨ ਦੀ ਬੇਨਤੀ ਕਰੋ", "app.containers.PasswordReset.submitError": "ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ। ਕਿਰਪਾ ਕਰਕੇ ਬਾਅਦ ਵਿੱਚ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.containers.PasswordReset.title": "ਆਪਣਾ ਪਾਸਵਰਡ ਰੀਸੈਟ ਕਰੋ", "app.containers.PasswordReset.updatePassword": "ਨਵੇਂ ਪਾਸਵਰਡ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ", "app.containers.ProjectFolderCards.allProjects": "ਸਾਰੇ ਪ੍ਰੋਜੈਕਟ", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} ਵਰਤਮਾਨ ਵਿੱਚ ਕੰਮ ਕਰ ਰਿਹਾ ਹੈ", "app.containers.ProjectFolderShowPage.editFolder": "ਫੋਲਡਰ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "ਇਸ ਪ੍ਰੋਜੈਕਟ ਬਾਰੇ ਜਾਣਕਾਰੀ ਦਿੱਤੀ", "app.containers.ProjectFolderShowPage.metaTitle1": "ਫੋਲਡਰ: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "ਹੋਰ ਪੜ੍ਹੋ", "app.containers.ProjectFolderShowPage.seeLess": "ਘੱਟ ਵੇਖੋ", "app.containers.ProjectFolderShowPage.share": "ਸ਼ੇਅਰ ਕਰੋ", "app.containers.Projects.PollForm.document": "ਦਸਤਾਵੇਜ਼", "app.containers.Projects.PollForm.formCompleted": "ਤੁਹਾਡਾ ਧੰਨਵਾਦ! ਤੁਹਾਡਾ ਜਵਾਬ ਮਿਲ ਗਿਆ ਹੈ।", "app.containers.Projects.PollForm.maxOptions": "ਅਧਿਕਤਮ {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "ਤੁਸੀਂ ਪਹਿਲਾਂ ਹੀ ਇਹ ਪੋਲ ਲੈ ਚੁੱਕੇ ਹੋ।", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "ਇਹ ਪੋਲ ਤਾਂ ਹੀ ਲਿਆ ਜਾ ਸਕਦਾ ਹੈ ਜਦੋਂ ਇਹ ਪੜਾਅ ਕਿਰਿਆਸ਼ੀਲ ਹੋਵੇ।", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "ਇਹ ਪੋਲ ਵਰਤਮਾਨ ਵਿੱਚ ਸਮਰੱਥ ਨਹੀਂ ਹੈ", "app.containers.Projects.PollForm.pollDisabledNotPossible": "ਫਿਲਹਾਲ ਇਸ ਪੋਲ ਨੂੰ ਲੈਣਾ ਅਸੰਭਵ ਹੈ।", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "ਪੋਲ ਹੁਣ ਉਪਲਬਧ ਨਹੀਂ ਹੈ ਕਿਉਂਕਿ ਇਹ ਪ੍ਰੋਜੈਕਟ ਹੁਣ ਕਿਰਿਆਸ਼ੀਲ ਨਹੀਂ ਹੈ।", "app.containers.Projects.PollForm.sendAnswer": "ਭੇਜੋ", "app.containers.Projects.a11y_phase": "ਪੜਾਅ {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "ਪੜਾਵਾਂ ਦੀ ਸੰਖੇਪ ਜਾਣਕਾਰੀ", "app.containers.Projects.a11y_titleInputs": "ਇਸ ਪ੍ਰੋਜੈਕਟ ਲਈ ਸਾਰੇ ਇਨਪੁਟ ਸਪੁਰਦ ਕੀਤੇ ਗਏ ਹਨ", "app.containers.Projects.a11y_titleInputsPhase": "ਇਸ ਪੜਾਅ ਵਿੱਚ ਸਾਰੇ ਇੰਪੁੱਟ ਜਮ੍ਹਾਂ ਕੀਤੇ ਗਏ ਹਨ", "app.containers.Projects.accessRights": "ਪਹੁੰਚ ਅਧਿਕਾਰ", "app.containers.Projects.addedToBasket": "ਟੋਕਰੀ ਵਿੱਚ ਸ਼ਾਮਲ ਕੀਤਾ ਗਿਆ", "app.containers.Projects.allocateBudget": "ਆਪਣਾ ਬਜਟ ਨਿਰਧਾਰਤ ਕਰੋ", "app.containers.Projects.archived": "ਪੁਰਾਲੇਖ", "app.containers.Projects.basketSubmitted": "ਟੋਕਰੀ ਜਮ੍ਹਾ ਕਰ ਦਿੱਤੀ ਗਈ ਹੈ!", "app.containers.Projects.contributions": "ਯੋਗਦਾਨ", "app.containers.Projects.createANewPhase": "ਇੱਕ ਨਵਾਂ ਪੜਾਅ ਬਣਾਓ", "app.containers.Projects.currentPhase": "ਮੌਜੂਦਾ ਪੜਾਅ", "app.containers.Projects.document": "ਦਸਤਾਵੇਜ਼", "app.containers.Projects.editProject": "ਪ੍ਰੋਜੈਕਟ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.Projects.emailSharingBody": "ਤੁਸੀਂ ਇਸ ਪਹਿਲ ਬਾਰੇ ਕੀ ਸੋਚਦੇ ਹੋ? ਇਸ 'ਤੇ ਵੋਟ ਦਿਓ ਅਤੇ ਆਪਣੀ ਆਵਾਜ਼ ਸੁਣਨ ਲਈ {initiativeUrl} 'ਤੇ ਚਰਚਾ ਨੂੰ ਸਾਂਝਾ ਕਰੋ!", "app.containers.Projects.emailSharingSubject": "ਮੇਰੀ ਪਹਿਲ ਦਾ ਸਮਰਥਨ ਕਰੋ: {initiativeTitle}.", "app.containers.Projects.endedOn": "{date}ਨੂੰ ਸਮਾਪਤ ਹੋਇਆ", "app.containers.Projects.events": "ਸਮਾਗਮ", "app.containers.Projects.header": "ਪ੍ਰੋਜੈਕਟਸ", "app.containers.Projects.ideas": "ਵਿਚਾਰ", "app.containers.Projects.information": "ਜਾਣਕਾਰੀ", "app.containers.Projects.initiatives": "ਪਹਿਲਕਦਮੀਆਂ", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "ਦਸਤਾਵੇਜ਼ ਦੀ ਸਮੀਖਿਆ ਕਰੋ", "app.containers.Projects.invisibleTitlePhaseAbout": "ਇਸ ਪੜਾਅ ਬਾਰੇ", "app.containers.Projects.invisibleTitlePoll": "ਪੋਲ ਲਓ", "app.containers.Projects.invisibleTitleSurvey": "ਸਰਵੇਖਣ ਲਵੋ", "app.containers.Projects.issues": "ਟਿੱਪਣੀਆਂ", "app.containers.Projects.liveDataMessage": "ਤੁਸੀਂ ਰੀਅਲ-ਟਾਈਮ ਡਾਟਾ ਦੇਖ ਰਹੇ ਹੋ। ਭਾਗੀਦਾਰਾਂ ਦੀ ਗਿਣਤੀ ਪ੍ਰਬੰਧਕਾਂ ਲਈ ਲਗਾਤਾਰ ਅੱਪਡੇਟ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਨੋਟ ਕਰੋ ਕਿ ਨਿਯਮਤ ਉਪਭੋਗਤਾ ਕੈਸ਼ ਕੀਤਾ ਡੇਟਾ ਦੇਖਦੇ ਹਨ, ਜਿਸਦੇ ਨਤੀਜੇ ਵਜੋਂ ਸੰਖਿਆਵਾਂ ਵਿੱਚ ਮਾਮੂਲੀ ਅੰਤਰ ਹੋ ਸਕਦਾ ਹੈ।", "app.containers.Projects.location": "ਟਿਕਾਣਾ:", "app.containers.Projects.manageBasket": "ਟੋਕਰੀ ਦਾ ਪ੍ਰਬੰਧਨ ਕਰੋ", "app.containers.Projects.meetMinBudgetRequirement": "ਟੋਕਰੀ ਜਮ੍ਹਾ ਕਰਨ ਲਈ ਘੱਟੋ-ਘੱਟ ਬਜਟ ਨੂੰ ਪੂਰਾ ਕਰੋ।", "app.containers.Projects.meetMinSelectionRequirement": "ਟੋਕਰੀ ਜਮ੍ਹਾ ਕਰਨ ਲਈ ਘੱਟੋ-ਘੱਟ ਬਜਟ ਨੂੰ ਪੂਰਾ ਕਰੋ।", "app.containers.Projects.metaTitle1": "ਪ੍ਰੋਜੈਕਟ: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "ਘੱਟੋ-ਘੱਟ ਬਜਟ ਦੀ ਲੋੜ ਹੈ", "app.containers.Projects.myBasket": "ਟੋਕਰੀ", "app.containers.Projects.navPoll": "ਪੋਲ", "app.containers.Projects.navSurvey": "ਸਰਵੇਖਣ", "app.containers.Projects.newPhase": "ਨਵਾਂ ਪੜਾਅ", "app.containers.Projects.nextPhase": "ਅਗਲਾ ਪੜਾਅ", "app.containers.Projects.noEndDate": "ਕੋਈ ਸਮਾਪਤੀ ਮਿਤੀ ਨਹੀਂ", "app.containers.Projects.noItems": "ਤੁਸੀਂ ਅਜੇ ਤੱਕ ਕੋਈ ਆਈਟਮਾਂ ਨਹੀਂ ਚੁਣੀਆਂ ਹਨ", "app.containers.Projects.noPastEvents": "ਪ੍ਰਦਰਸ਼ਿਤ ਕਰਨ ਲਈ ਕੋਈ ਪਿਛਲੀਆਂ ਘਟਨਾਵਾਂ ਨਹੀਂ ਹਨ", "app.containers.Projects.noPhaseSelected": "ਕੋਈ ਪੜਾਅ ਨਹੀਂ ਚੁਣਿਆ ਗਿਆ", "app.containers.Projects.noUpcomingOrOngoingEvents": "ਵਰਤਮਾਨ ਵਿੱਚ ਕੋਈ ਆਗਾਮੀ ਜਾਂ ਚੱਲ ਰਹੇ ਸਮਾਗਮਾਂ ਦਾ ਸਮਾਂ ਨਿਯਤ ਨਹੀਂ ਕੀਤਾ ਗਿਆ ਹੈ।", "app.containers.Projects.offlineVotersTooltip": "ਇਹ ਨੰਬਰ ਕਿਸੇ ਵੀ ਔਫਲਾਈਨ ਵੋਟਰ ਗਿਣਤੀ ਨੂੰ ਦਰਸਾਉਂਦਾ ਨਹੀਂ ਹੈ।", "app.containers.Projects.options": "ਵਿਕਲਪ", "app.containers.Projects.participants": "ਭਾਗ ਲੈਣ ਵਾਲੇ", "app.containers.Projects.participantsTooltip4": "ਇਹ ਸੰਖਿਆ ਅਗਿਆਤ ਸਰਵੇਖਣ ਬੇਨਤੀਆਂ ਨੂੰ ਵੀ ਦਰਸਾਉਂਦੀ ਹੈ। ਅਗਿਆਤ ਸਰਵੇਖਣ ਸਬਮਿਸ਼ਨ ਸੰਭਵ ਹਨ ਜੇਕਰ ਸਰਵੇਖਣ ਹਰ ਕਿਸੇ ਲਈ ਖੁੱਲ੍ਹੇ ਹਨ (ਇਸ ਪ੍ਰੋਜੈਕਟ ਲਈ {accessRightsLink} ਟੈਬ ਦੇਖੋ)।", "app.containers.Projects.pastEvents": "ਪਿਛਲੀਆਂ ਘਟਨਾਵਾਂ", "app.containers.Projects.petitions": "ਪਟੀਸ਼ਨਾਂ", "app.containers.Projects.phases": "ਪੜਾਅ", "app.containers.Projects.previousPhase": "ਪਿਛਲਾ ਪੜਾਅ", "app.containers.Projects.project": "ਪ੍ਰੋਜੈਕਟ", "app.containers.Projects.projectTwitterMessage": "ਆਪਣੀ ਆਵਾਜ਼ ਸੁਣਾਓ! {projectName} | ਵਿੱਚ ਭਾਗ ਲਓ {orgName}", "app.containers.Projects.projects": "ਪ੍ਰੋਜੈਕਟਸ", "app.containers.Projects.proposals": "ਪ੍ਰਸਤਾਵ", "app.containers.Projects.questions": "ਸਵਾਲ", "app.containers.Projects.readLess": "ਘੱਟ ਪੜ੍ਹੋ", "app.containers.Projects.readMore": "ਹੋਰ ਪੜ੍ਹੋ", "app.containers.Projects.removeItem": "ਆਈਟਮ ਹਟਾਓ", "app.containers.Projects.requiredSelection": "ਲੋੜੀਂਦੀ ਚੋਣ", "app.containers.Projects.reviewDocument": "ਦਸਤਾਵੇਜ਼ ਦੀ ਸਮੀਖਿਆ ਕਰੋ", "app.containers.Projects.seeTheContributions": "ਯੋਗਦਾਨ ਦੇਖੋ", "app.containers.Projects.seeTheIdeas": "ਵਿਚਾਰ ਦੇਖੋ", "app.containers.Projects.seeTheInitiatives": "ਪਹਿਲਕਦਮੀਆਂ ਦੇਖੋ", "app.containers.Projects.seeTheIssues": "ਟਿੱਪਣੀਆਂ ਦੇਖੋ", "app.containers.Projects.seeTheOptions": "ਵਿਕਲਪ ਦੇਖੋ", "app.containers.Projects.seeThePetitions": "ਪਟੀਸ਼ਨਾਂ ਵੇਖੋ", "app.containers.Projects.seeTheProjects": "ਪ੍ਰੋਜੈਕਟ ਵੇਖੋ", "app.containers.Projects.seeTheProposals": "ਪ੍ਰਸਤਾਵ ਵੇਖੋ", "app.containers.Projects.seeTheQuestions": "ਸਵਾਲ ਦੇਖੋ", "app.containers.Projects.seeUpcomingEvents": "ਆਗਾਮੀ ਸਮਾਗਮ ਵੇਖੋ", "app.containers.Projects.share": "ਸ਼ੇਅਰ ਕਰੋ", "app.containers.Projects.shareThisProject": "ਇਸ ਪ੍ਰੋਜੈਕਟ ਨੂੰ ਸਾਂਝਾ ਕਰੋ", "app.containers.Projects.submitMyBasket": "ਟੋਕਰੀ ਜਮ੍ਹਾਂ ਕਰੋ", "app.containers.Projects.survey": "ਸਰਵੇਖਣ", "app.containers.Projects.takeThePoll": "ਪੋਲ ਲਓ", "app.containers.Projects.takeTheSurvey": "ਸਰਵੇਖਣ ਲਵੋ", "app.containers.Projects.timeline": "ਸਮਾਂਰੇਖਾ", "app.containers.Projects.upcomingAndOngoingEvents": "ਆਗਾਮੀ ਅਤੇ ਚੱਲ ਰਹੇ ਸਮਾਗਮ", "app.containers.Projects.upcomingEvents": "ਆਗਾਮੀ ਸਮਾਗਮ", "app.containers.Projects.whatsAppMessage": "{projectName} | {orgName}ਦੇ ਭਾਗੀਦਾਰੀ ਪਲੇਟਫਾਰਮ ਤੋਂ", "app.containers.Projects.yourBudget": "ਕੁੱਲ ਬਜਟ", "app.containers.ProjectsIndexPage.metaDescription": "ਇਹ ਸਮਝਣ ਲਈ {orgName} ਦੇ ਸਾਰੇ ਚੱਲ ਰਹੇ ਪ੍ਰੋਜੈਕਟਾਂ ਦੀ ਪੜਚੋਲ ਕਰੋ ਕਿ ਤੁਸੀਂ ਕਿਵੇਂ ਭਾਗ ਲੈ ਸਕਦੇ ਹੋ।\n ਆਉ ਉਹਨਾਂ ਸਥਾਨਕ ਪ੍ਰੋਜੈਕਟਾਂ 'ਤੇ ਚਰਚਾ ਕਰੋ ਜੋ ਤੁਹਾਡੇ ਲਈ ਸਭ ਤੋਂ ਮਹੱਤਵਪੂਰਨ ਹਨ।", "app.containers.ProjectsIndexPage.metaTitle1": "ਪ੍ਰੋਜੈਕਟ | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "ਪ੍ਰੋਜੈਕਟਸ", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "ਮੈਂ ਹਿੱਸਾ ਲੈਣਾ ਚਾਹੁੰਦਾ ਹਾਂ", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਗਤੀਵਿਧੀ ਲਈ ਵਲੰਟੀਅਰ ਬਣਨ ਲਈ ਪਹਿਲਾਂ {signInLink} ਜਾਂ {signUpLink}", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "ਭਾਗੀਦਾਰੀ ਵਰਤਮਾਨ ਵਿੱਚ ਇਸ ਗਤੀਵਿਧੀ ਲਈ ਖੁੱਲੀ ਨਹੀਂ ਹੈ।", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "ਲਾਗਿਨ", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "ਸਾਇਨ ਅਪ", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "ਮੈਂ ਵਲੰਟੀਅਰ ਦੀ ਆਪਣੀ ਪੇਸ਼ਕਸ਼ ਵਾਪਸ ਲੈਂਦਾ ਹਾਂ", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {ਕੋਈ ਭਾਗੀਦਾਰ ਨਹੀਂ} one {# ਭਾਗੀਦਾਰ} other {# ਭਾਗੀਦਾਰ}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "ਚੇਤਾਵਨੀ: ਏਮਬੇਡ ਕੀਤੇ ਸਰਵੇਖਣ ਵਿੱਚ ਸਕ੍ਰੀਨ ਰੀਡਰ ਉਪਭੋਗਤਾਵਾਂ ਲਈ ਪਹੁੰਚਯੋਗਤਾ ਸਮੱਸਿਆਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਜੇਕਰ ਤੁਹਾਨੂੰ ਕੋਈ ਚੁਣੌਤੀਆਂ ਆਉਂਦੀਆਂ ਹਨ, ਤਾਂ ਕਿਰਪਾ ਕਰਕੇ ਮੂਲ ਪਲੇਟਫਾਰਮ ਤੋਂ ਸਰਵੇਖਣ ਦਾ ਲਿੰਕ ਪ੍ਰਾਪਤ ਕਰਨ ਲਈ ਪਲੇਟਫਾਰਮ ਪ੍ਰਸ਼ਾਸਕ ਨਾਲ ਸੰਪਰਕ ਕਰੋ। ਵਿਕਲਪਕ ਤੌਰ 'ਤੇ, ਤੁਸੀਂ ਸਰਵੇਖਣ ਨੂੰ ਭਰਨ ਲਈ ਹੋਰ ਤਰੀਕਿਆਂ ਦੀ ਬੇਨਤੀ ਕਰ ਸਕਦੇ ਹੋ।", "app.containers.ProjectsShowPage.process.survey.survey": "ਸਰਵੇਖਣ", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "ਇਹ ਜਾਣਨ ਲਈ ਕਿ ਕੀ ਤੁਸੀਂ ਇਸ ਸਰਵੇਖਣ ਵਿੱਚ ਹਿੱਸਾ ਲੈ ਸਕਦੇ ਹੋ, ਕਿਰਪਾ ਕਰਕੇ ਪਹਿਲਾਂ ਪਲੇਟਫਾਰਮ 'ਤੇ {logInLink} ਪਹੁੰਚੋ।", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "ਇਹ ਸਰਵੇਖਣ ਉਦੋਂ ਹੀ ਲਿਆ ਜਾ ਸਕਦਾ ਹੈ ਜਦੋਂ ਸਮਾਂ-ਸੀਮਾ ਵਿੱਚ ਇਹ ਪੜਾਅ ਸਰਗਰਮ ਹੋਵੇ।", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "ਸਰਵੇਖਣ ਲੈਣ ਲਈ ਕਿਰਪਾ ਕਰਕੇ {completeRegistrationLink} ।", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "ਇਹ ਸਰਵੇਖਣ ਵਰਤਮਾਨ ਵਿੱਚ ਸਮਰੱਥ ਨਹੀਂ ਹੈ", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "ਇਸ ਸਰਵੇਖਣ ਨੂੰ ਲੈਣ ਲਈ ਤੁਹਾਡੀ ਪਛਾਣ ਦੀ ਪੁਸ਼ਟੀ ਕਰਨ ਦੀ ਲੋੜ ਹੁੰਦੀ ਹੈ। {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "ਸਰਵੇਖਣ ਹੁਣ ਉਪਲਬਧ ਨਹੀਂ ਹੈ, ਕਿਉਂਕਿ ਇਹ ਪ੍ਰੋਜੈਕਟ ਹੁਣ ਕਿਰਿਆਸ਼ੀਲ ਨਹੀਂ ਹੈ।", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "ਪੂਰੀ ਰਜਿਸਟਰੇਸ਼ਨ", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "ਲਾਗਿਨ", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "ਸਾਇਨ ਅਪ", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "ਹੁਣੇ ਆਪਣੇ ਖਾਤੇ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ।", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "ਸਿਰਫ਼ ਕੁਝ ਉਪਭੋਗਤਾ ਇਸ ਦਸਤਾਵੇਜ਼ ਦੀ ਸਮੀਖਿਆ ਕਰ ਸਕਦੇ ਹਨ। ਕਿਰਪਾ ਕਰਕੇ ਪਹਿਲਾਂ {signUpLink} ਜਾਂ {logInLink} ।", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "ਇਸ ਦਸਤਾਵੇਜ਼ ਦੀ ਸਿਰਫ਼ ਉਦੋਂ ਹੀ ਸਮੀਖਿਆ ਕੀਤੀ ਜਾ ਸਕਦੀ ਹੈ ਜਦੋਂ ਇਹ ਪੜਾਅ ਕਿਰਿਆਸ਼ੀਲ ਹੁੰਦਾ ਹੈ।", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "ਕਿਰਪਾ ਕਰਕੇ ਦਸਤਾਵੇਜ਼ ਦੀ ਸਮੀਖਿਆ ਕਰਨ ਲਈ {completeRegistrationLink} ਕਰੋ।", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "ਬਦਕਿਸਮਤੀ ਨਾਲ, ਤੁਹਾਡੇ ਕੋਲ ਇਸ ਦਸਤਾਵੇਜ਼ ਦੀ ਸਮੀਖਿਆ ਕਰਨ ਦੇ ਅਧਿਕਾਰ ਨਹੀਂ ਹਨ।", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "ਇਸ ਦਸਤਾਵੇਜ਼ ਦੀ ਸਮੀਖਿਆ ਕਰਨ ਲਈ ਤੁਹਾਡੇ ਖਾਤੇ ਦੀ ਪੁਸ਼ਟੀ ਦੀ ਲੋੜ ਹੈ। {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "ਦਸਤਾਵੇਜ਼ ਹੁਣ ਉਪਲਬਧ ਨਹੀਂ ਹੈ, ਕਿਉਂਕਿ ਇਹ ਪ੍ਰੋਜੈਕਟ ਹੁਣ ਕਿਰਿਆਸ਼ੀਲ ਨਹੀਂ ਹੈ।", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(1 ਔਫਲਾਈਨ ਸਮੇਤ)} other {(# ਔਫਲਾਈਨ ਸਮੇਤ)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 ਪਿਕ} other {# ਪਿਕ}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "ਇਸ ਵਿਕਲਪ ਨੂੰ ਚੁਣਨ ਵਾਲੇ ਭਾਗੀਦਾਰਾਂ ਦੀ ਪ੍ਰਤੀਸ਼ਤਤਾ।", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "ਇਸ ਵਿਕਲਪ ਨੂੰ ਪ੍ਰਾਪਤ ਹੋਈਆਂ ਕੁੱਲ ਵੋਟਾਂ ਦੀ ਪ੍ਰਤੀਸ਼ਤਤਾ।", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "ਲਾਗਤ:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "ਹੋਰ ਦਿਖਾਓ", "app.containers.ReactionControl.a11y_likesDislikes": "ਕੁੱਲ ਪਸੰਦਾਂ: {likesCount}, ਕੁੱਲ ਨਾਪਸੰਦਾਂ: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "ਤੁਸੀਂ ਇਸ ਇਨਪੁਟ ਲਈ ਆਪਣੀ ਨਾਪਸੰਦ ਨੂੰ ਸਫਲਤਾਪੂਰਵਕ ਰੱਦ ਕਰ ਦਿੱਤਾ ਹੈ।", "app.containers.ReactionControl.cancelLikeSuccess": "ਤੁਸੀਂ ਇਸ ਇਨਪੁਟ ਲਈ ਆਪਣੀ ਪਸੰਦ ਨੂੰ ਸਫਲਤਾਪੂਰਵਕ ਰੱਦ ਕਰ ਦਿੱਤਾ ਹੈ।", "app.containers.ReactionControl.dislikeSuccess": "ਤੁਸੀਂ ਇਸ ਇਨਪੁਟ ਨੂੰ ਸਫਲਤਾਪੂਰਵਕ ਨਾਪਸੰਦ ਕੀਤਾ।", "app.containers.ReactionControl.likeSuccess": "ਤੁਸੀਂ ਇਸ ਇਨਪੁਟ ਨੂੰ ਸਫਲਤਾਪੂਰਵਕ ਪਸੰਦ ਕੀਤਾ ਹੈ।", "app.containers.ReactionControl.reactionErrorSubTitle": "ਗਲਤੀ ਕਾਰਨ ਤੁਹਾਡੀ ਪ੍ਰਤੀਕਿਰਿਆ ਦਰਜ ਨਹੀਂ ਕੀਤੀ ਜਾ ਸਕੀ। ਕਿਰਪਾ ਕਰਕੇ ਕੁਝ ਮਿੰਟਾਂ ਵਿੱਚ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.containers.ReactionControl.reactionSuccessTitle": "ਤੁਹਾਡੀ ਪ੍ਰਤੀਕਿਰਿਆ ਸਫਲਤਾਪੂਰਵਕ ਦਰਜ ਕੀਤੀ ਗਈ ਸੀ!", "app.containers.ReactionControl.vote": "ਵੋਟ ਕਰੋ", "app.containers.ReactionControl.voted": "ਵੋਟ ਪਾਈ", "app.containers.SearchInput.a11y_cancelledPostingComment": "ਟਿੱਪਣੀ ਪੋਸਟ ਕਰਨਾ ਰੱਦ ਕੀਤਾ।", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} ਟਿੱਪਣੀਆਂ ਲੋਡ ਹੋ ਗਈਆਂ ਹਨ।", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# ਇਵੈਂਟ ਲੋਡ ਹੋ ਗਏ ਹਨ} one {# ਇਵੈਂਟ ਲੋਡ ਹੋ ਗਏ ਹਨ} other {# ਇਵੈਂਟ ਲੋਡ ਹੋ ਗਏ ਹਨ}}।", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# ਨਤੀਜੇ ਲੋਡ ਹੋ ਗਏ ਹਨ} one {# ਨਤੀਜਾ ਲੋਡ ਹੋ ਗਿਆ ਹੈ} other {# ਨਤੀਜੇ ਲੋਡ ਹੋ ਗਏ ਹਨ}}।", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# ਖੋਜ ਨਤੀਜੇ ਲੋਡ ਹੋ ਗਏ ਹਨ} one {# ਖੋਜ ਨਤੀਜਾ ਲੋਡ ਹੋ ਗਿਆ ਹੈ} other {# ਖੋਜ ਨਤੀਜੇ ਲੋਡ ਹੋ ਗਏ ਹਨ}}।", "app.containers.SearchInput.removeSearchTerm": "ਖੋਜ ਸ਼ਬਦ ਨੂੰ ਹਟਾਓ", "app.containers.SearchInput.searchAriaLabel": "ਖੋਜ", "app.containers.SearchInput.searchLabel": "ਖੋਜ", "app.containers.SearchInput.searchPlaceholder": "ਖੋਜ", "app.containers.SearchInput.searchTerm": "ਖੋਜ ਸ਼ਬਦ: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect 700 ਤੋਂ ਵੱਧ ਔਨਲਾਈਨ ਸੇਵਾਵਾਂ ਲਈ ਸਾਈਨ ਅੱਪ ਨੂੰ ਸੁਰੱਖਿਅਤ ਅਤੇ ਸਰਲ ਬਣਾਉਣ ਲਈ ਫਰਾਂਸੀਸੀ ਰਾਜ ਦੁਆਰਾ ਪ੍ਰਸਤਾਵਿਤ ਹੱਲ ਹੈ।", "app.containers.SignIn.or": "ਜਾਂ", "app.containers.SignIn.signInError": "ਦਿੱਤੀ ਗਈ ਜਾਣਕਾਰੀ ਸਹੀ ਨਹੀਂ ਹੈ। 'ਪਾਸਵਰਡ ਭੁੱਲ ਗਏ?' 'ਤੇ ਕਲਿੱਕ ਕਰੋ? ਆਪਣਾ ਪਾਸਵਰਡ ਰੀਸੈਟ ਕਰਨ ਲਈ।", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "ਲੌਗ ਇਨ ਕਰਨ, ਸਾਈਨ ਅੱਪ ਕਰਨ ਜਾਂ ਆਪਣੇ ਖਾਤੇ ਦੀ ਪੁਸ਼ਟੀ ਕਰਨ ਲਈ FranceConnect ਦੀ ਵਰਤੋਂ ਕਰੋ।", "app.containers.SignIn.whatIsFranceConnect": "ਫਰਾਂਸ ਕਨੈਕਟ ਕੀ ਹੈ?", "app.containers.SignUp.adminOptions2": "ਪ੍ਰਬੰਧਕਾਂ ਅਤੇ ਪ੍ਰੋਜੈਕਟ ਪ੍ਰਬੰਧਕਾਂ ਲਈ", "app.containers.SignUp.backToSignUpOptions": "ਸਾਈਨ ਅੱਪ ਵਿਕਲਪਾਂ 'ਤੇ ਵਾਪਸ ਜਾਓ", "app.containers.SignUp.continue": "ਜਾਰੀ ਰੱਖੋ", "app.containers.SignUp.emailConsent": "ਸਾਈਨ ਅੱਪ ਕਰਕੇ, ਤੁਸੀਂ ਇਸ ਪਲੇਟਫਾਰਮ ਤੋਂ ਈਮੇਲ ਪ੍ਰਾਪਤ ਕਰਨ ਲਈ ਸਹਿਮਤ ਹੁੰਦੇ ਹੋ। ਤੁਸੀਂ 'ਮੇਰੀ ਸੈਟਿੰਗ' ਪੰਨੇ ਵਿੱਚ ਚੁਣ ਸਕਦੇ ਹੋ ਕਿ ਤੁਸੀਂ ਕਿਹੜੀਆਂ ਈਮੇਲਾਂ ਪ੍ਰਾਪਤ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ।", "app.containers.SignUp.emptyFirstNameError": "ਆਪਣਾ ਪਹਿਲਾ ਨਾਮ ਦਰਜ ਕਰੋ", "app.containers.SignUp.emptyLastNameError": "ਆਪਣਾ ਆਖਰੀ ਨਾਮ ਦਰਜ ਕਰੋ", "app.containers.SignUp.firstNamesLabel": "ਪਹਿਲਾ ਨਾਂ", "app.containers.SignUp.goToLogIn": "ਕੀ ਪਹਿਲਾਂ ਤੋਂ ਹੀ ਖਾਤਾ ਹੈ? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "ਮੈਂ {link}ਨੂੰ ਪੜ੍ਹ ਲਿਆ ਹੈ ਅਤੇ ਇਸ ਨਾਲ ਸਹਿਮਤ ਹਾਂ।", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "ਮੈਂ {link}ਨੂੰ ਪੜ੍ਹ ਲਿਆ ਹੈ ਅਤੇ ਇਸ ਨਾਲ ਸਹਿਮਤ ਹਾਂ।", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "ਮੈਂ ਸਵੀਕਾਰ ਕਰਦਾ/ਕਰਦੀ ਹਾਂ ਕਿ ਡਾਟਾ mitgestalten.wien.gv.at 'ਤੇ ਵਰਤਿਆ ਜਾਵੇਗਾ। ਹੋਰ ਜਾਣਕਾਰੀ {link}ਮਿਲ ਸਕਦੀ ਹੈ।", "app.containers.SignUp.invitationErrorText": "ਤੁਹਾਡੇ ਸੱਦੇ ਦੀ ਮਿਆਦ ਪੁੱਗ ਗਈ ਹੈ ਜਾਂ ਪਹਿਲਾਂ ਹੀ ਵਰਤੀ ਜਾ ਚੁੱਕੀ ਹੈ। ਜੇਕਰ ਤੁਸੀਂ ਪਹਿਲਾਂ ਹੀ ਇੱਕ ਖਾਤਾ ਬਣਾਉਣ ਲਈ ਸੱਦਾ ਲਿੰਕ ਦੀ ਵਰਤੋਂ ਕਰ ਚੁੱਕੇ ਹੋ, ਤਾਂ ਸਾਈਨ ਇਨ ਕਰਨ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰੋ। ਨਹੀਂ ਤਾਂ, ਇੱਕ ਨਵਾਂ ਖਾਤਾ ਬਣਾਉਣ ਲਈ ਸਾਈਨ ਅੱਪ ਕਰੋ।", "app.containers.SignUp.lastNameLabel": "ਆਖਰੀ ਨਾਂਮ", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "ਉਹਨਾਂ ਬਾਰੇ ਸੂਚਿਤ ਕਰਨ ਲਈ ਆਪਣੇ ਫੋਕਸ ਦੇ ਖੇਤਰਾਂ ਦਾ ਪਾਲਣ ਕਰੋ:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "ਉਹਨਾਂ ਬਾਰੇ ਸੂਚਿਤ ਕਰਨ ਲਈ ਆਪਣੇ ਮਨਪਸੰਦ ਵਿਸ਼ਿਆਂ ਦਾ ਪਾਲਣ ਕਰੋ:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "ਤਰਜੀਹਾਂ ਨੂੰ ਸੁਰੱਖਿਅਤ ਕਰੋ", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "ਹੁਣ ਲਈ ਛੱਡੋ", "app.containers.SignUp.privacyPolicyNotAcceptedError": "ਅੱਗੇ ਵਧਣ ਲਈ ਸਾਡੀ ਗੋਪਨੀਯਤਾ ਨੀਤੀ ਨੂੰ ਸਵੀਕਾਰ ਕਰੋ", "app.containers.SignUp.signUp2": "ਸਾਇਨ ਅਪ", "app.containers.SignUp.skip": "ਇਸ ਪੜਾਅ ਨੂੰ ਛੱਡੋ", "app.containers.SignUp.tacError": "ਅੱਗੇ ਵਧਣ ਲਈ ਸਾਡੇ ਨਿਯਮਾਂ ਅਤੇ ਸ਼ਰਤਾਂ ਨੂੰ ਸਵੀਕਾਰ ਕਰੋ", "app.containers.SignUp.thePrivacyPolicy": "ਗੋਪਨੀਯਤਾ ਨੀਤੀ", "app.containers.SignUp.theTermsAndConditions": "ਨਿਯਮ ਅਤੇ ਸ਼ਰਤਾਂ", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {ਅਜਿਹਾ ਲੱਗਦਾ ਹੈ ਕਿ ਤੁਸੀਂ ਪ੍ਰਕਿਰਿਆ ਨੂੰ ਪੂਰਾ ਕੀਤੇ ਬਿਨਾਂ ਪਹਿਲਾਂ ਸਾਈਨ ਅੱਪ ਕਰਨ ਦੀ ਕੋਸ਼ਿਸ਼ ਕੀਤੀ ਸੀ। ਪਿਛਲੀ ਕੋਸ਼ਿਸ਼ ਦੌਰਾਨ ਚੁਣੇ ਗਏ ਪ੍ਰਮਾਣ ਪੱਤਰਾਂ ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹੋਏ, ਇਸਦੀ ਬਜਾਏ ਲੌਗ ਇਨ 'ਤੇ ਕਲਿੱਕ ਕਰੋ।} other {ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ। ਕਿਰਪਾ ਕਰਕੇ ਬਾਅਦ ਵਿੱਚ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।}}", "app.containers.SignUp.viennaConsentEmail": "ਈਮੇਲ ਪਤਾ", "app.containers.SignUp.viennaConsentFirstName": "ਪਹਿਲਾ ਨਾਂ", "app.containers.SignUp.viennaConsentFooter": "ਤੁਸੀਂ ਸਾਈਨ-ਇਨ ਕਰਨ ਤੋਂ ਬਾਅਦ ਆਪਣੀ ਪ੍ਰੋਫਾਈਲ ਜਾਣਕਾਰੀ ਨੂੰ ਬਦਲ ਸਕਦੇ ਹੋ। ਜੇਕਰ ਤੁਹਾਡੇ ਕੋਲ ਪਹਿਲਾਂ ਹੀ mitgestalten.wien.gv.at 'ਤੇ ਇੱਕੋ ਈਮੇਲ ਪਤੇ ਵਾਲਾ ਖਾਤਾ ਹੈ, ਤਾਂ ਇਹ ਤੁਹਾਡੇ ਮੌਜੂਦਾ ਖਾਤੇ ਨਾਲ ਲਿੰਕ ਕੀਤਾ ਜਾਵੇਗਾ।", "app.containers.SignUp.viennaConsentHeader": "ਹੇਠਾਂ ਦਿੱਤੇ ਡੇਟਾ ਨੂੰ ਪ੍ਰਸਾਰਿਤ ਕੀਤਾ ਜਾਵੇਗਾ:", "app.containers.SignUp.viennaConsentLastName": "ਆਖਰੀ ਨਾਂਮ", "app.containers.SignUp.viennaConsentUserName": "ਉਪਭੋਗਤਾ ਨਾਮ", "app.containers.SignUp.viennaDataProtection": "ਵਿਯੇਨ੍ਨਾ ਗੋਪਨੀਯਤਾ ਨੀਤੀ", "app.containers.SiteMap.contributions": "ਯੋਗਦਾਨ", "app.containers.SiteMap.cookiePolicyLinkTitle": "ਕੂਕੀਜ਼", "app.containers.SiteMap.issues": "ਟਿੱਪਣੀਆਂ", "app.containers.SiteMap.options": "ਵਿਕਲਪ", "app.containers.SiteMap.projects": "ਪ੍ਰੋਜੈਕਟਸ", "app.containers.SiteMap.questions": "ਸਵਾਲ", "app.containers.SpamReport.buttonSave": "ਰਿਪੋਰਟ", "app.containers.SpamReport.buttonSuccess": "ਸਫਲਤਾ", "app.containers.SpamReport.inappropriate": "ਇਹ ਅਣਉਚਿਤ ਜਾਂ ਅਪਮਾਨਜਨਕ ਹੈ", "app.containers.SpamReport.messageError": "ਫਾਰਮ ਸਪੁਰਦ ਕਰਨ ਵਿੱਚ ਇੱਕ ਤਰੁੱਟੀ ਸੀ, ਕਿਰਪਾ ਕਰਕੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.containers.SpamReport.messageSuccess": "ਤੁਹਾਡੀ ਰਿਪੋਰਟ ਭੇਜ ਦਿੱਤੀ ਗਈ ਹੈ", "app.containers.SpamReport.other": "ਹੋਰ ਕਾਰਨ", "app.containers.SpamReport.otherReasonPlaceholder": "ਵਰਣਨ", "app.containers.SpamReport.wrong_content": "ਇਹ ਸੰਬੰਧਿਤ ਨਹੀਂ ਹੈ", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "ਪ੍ਰੋਫਾਈਲ ਤਸਵੀਰ ਹਟਾਓ", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "ਉਹਨਾਂ ਪ੍ਰਸਤਾਵਾਂ 'ਤੇ ਤੁਹਾਡੀਆਂ ਵੋਟਾਂ ਜੋ ਅਜੇ ਵੀ ਵੋਟਿੰਗ ਲਈ ਖੁੱਲ੍ਹੀਆਂ ਹਨ, ਮਿਟਾ ਦਿੱਤੀਆਂ ਜਾਣਗੀਆਂ। ਉਹਨਾਂ ਪ੍ਰਸਤਾਵਾਂ 'ਤੇ ਵੋਟਾਂ ਜਿੱਥੇ ਵੋਟਿੰਗ ਦੀ ਮਿਆਦ ਬੰਦ ਹੋ ਗਈ ਹੈ, ਨੂੰ ਮਿਟਾਇਆ ਨਹੀਂ ਜਾਵੇਗਾ।", "app.containers.UsersEditPage.addPassword": "ਪਾਸਵਰਡ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "ਤਸਦੀਕ ਦੀ ਲੋੜ ਵਾਲੇ ਪ੍ਰੋਜੈਕਟਾਂ ਵਿੱਚ ਹਿੱਸਾ ਲੈਣ ਲਈ।", "app.containers.UsersEditPage.becomeVerifiedTitle": "ਆਪਣੀ ਪਛਾਣ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ", "app.containers.UsersEditPage.bio": "ਤੁਹਾਡੇ ਬਾਰੇ", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "ਤੁਸੀਂ ਇਸ ਖੇਤਰ ਨੂੰ ਸੰਪਾਦਿਤ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਇਸ ਵਿੱਚ ਪ੍ਰਮਾਣਿਤ ਜਾਣਕਾਰੀ ਸ਼ਾਮਲ ਹੈ।", "app.containers.UsersEditPage.buttonSuccessLabel": "ਸਫਲਤਾ", "app.containers.UsersEditPage.cancel": "ਰੱਦ ਕਰੋ", "app.containers.UsersEditPage.changeEmail": "ਈਮੇਲ ਬਦਲੋ", "app.containers.UsersEditPage.changePassword2": "ਪਾਸਵਰਡ ਬਦਲੋ", "app.containers.UsersEditPage.clickHereToUpdateVerification": "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੀ ਪੁਸ਼ਟੀਕਰਨ ਨੂੰ ਅੱਪਡੇਟ ਕਰਨ ਲਈ ਇੱਥੇ ਕਲਿੱਕ ਕਰੋ।", "app.containers.UsersEditPage.conditionsLinkText": "ਸਾਡੇ ਹਾਲਾਤ", "app.containers.UsersEditPage.contactUs": "ਛੱਡਣ ਦਾ ਇੱਕ ਹੋਰ ਕਾਰਨ? {feedbackLink} ਅਤੇ ਸ਼ਾਇਦ ਅਸੀਂ ਮਦਦ ਕਰ ਸਕਦੇ ਹਾਂ।", "app.containers.UsersEditPage.deleteAccountSubtext": "ਤੁਹਾਨੂੰ ਜਾਂਦੇ ਹੋਏ ਸਾਨੂੰ ਅਫ਼ਸੋਸ ਹੈ।", "app.containers.UsersEditPage.deleteMyAccount": "ਮੇਰਾ ਖਾਤਾ ਮਿਟਾਓ", "app.containers.UsersEditPage.deleteYourAccount": "ਆਪਣਾ ਖਾਤਾ ਮਿਟਾਓ", "app.containers.UsersEditPage.deletionSection": "ਆਪਣਾ ਖਾਤਾ ਮਿਟਾਓ", "app.containers.UsersEditPage.deletionSubtitle": "ਇਸ ਕਾਰਵਾਈ ਨੂੰ ਅਣਕੀਤਾ ਨਹੀਂ ਕੀਤਾ ਜਾ ਸਕਦਾ। ਪਲੇਟਫਾਰਮ 'ਤੇ ਤੁਹਾਡੇ ਦੁਆਰਾ ਪ੍ਰਕਾਸ਼ਿਤ ਕੀਤੀ ਸਮੱਗਰੀ ਨੂੰ ਅਗਿਆਤ ਕੀਤਾ ਜਾਵੇਗਾ। ਜੇਕਰ ਤੁਸੀਂ ਆਪਣੀ ਸਾਰੀ ਸਮੱਗਰੀ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ, ਤਾਂ ਤੁਸੀਂ <EMAIL> 'ਤੇ ਸਾਡੇ ਨਾਲ ਸੰਪਰਕ ਕਰ ਸਕਦੇ ਹੋ।", "app.containers.UsersEditPage.email": "ਈਮੇਲ", "app.containers.UsersEditPage.emailEmptyError": "ਇੱਕ ਈ-ਮੇਲ ਪਤਾ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.UsersEditPage.emailInvalidError": "ਸਹੀ ਫਾਰਮੈਟ ਵਿੱਚ ਇੱਕ ਈਮੇਲ ਪਤਾ ਪ੍ਰਦਾਨ ਕਰੋ, ਉਦਾਹਰਨ ਲਈ <EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "ਆਓ ਜਾਣਦੇ ਹਾਂ", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "ਪਹਿਲਾ ਨਾਂ", "app.containers.UsersEditPage.firstNamesEmptyError": "ਇੱਕ ਪਹਿਲਾ ਨਾਮ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.UsersEditPage.h1": "ਤੁਹਾਡੀ ਖਾਤਾ ਜਾਣਕਾਰੀ", "app.containers.UsersEditPage.h1sub": "ਆਪਣੀ ਖਾਤਾ ਜਾਣਕਾਰੀ ਸੰਪਾਦਿਤ ਕਰੋ", "app.containers.UsersEditPage.image": "ਅਵਤਾਰ ਚਿੱਤਰ", "app.containers.UsersEditPage.imageDropzonePlaceholder": "ਪ੍ਰੋਫਾਈਲ ਤਸਵੀਰ ਚੁਣਨ ਲਈ ਕਲਿੱਕ ਕਰੋ (ਅਧਿਕਤਮ 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "ਤੁਹਾਡੇ ਪ੍ਰੋਫਾਈਲ ਲਈ ਸਾਰੀਆਂ ਸੈਟਿੰਗਾਂ", "app.containers.UsersEditPage.language": "ਭਾਸ਼ਾ", "app.containers.UsersEditPage.lastName": "ਆਖਰੀ ਨਾਂਮ", "app.containers.UsersEditPage.lastNameEmptyError": "ਇੱਕ ਆਖਰੀ ਨਾਮ ਪ੍ਰਦਾਨ ਕਰੋ", "app.containers.UsersEditPage.loading": "ਲੋਡ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "ਤੁਸੀਂ ਇੱਥੇ ਆਪਣਾ ਈਮੇਲ ਜਾਂ ਪਾਸਵਰਡ ਬਦਲ ਸਕਦੇ ਹੋ।", "app.containers.UsersEditPage.loginCredentialsTitle": "ਲੌਗਇਨ ਪ੍ਰਮਾਣ ਪੱਤਰ", "app.containers.UsersEditPage.messageError": "ਅਸੀਂ ਤੁਹਾਡੀ ਪ੍ਰੋਫਾਈਲ ਨੂੰ ਸੁਰੱਖਿਅਤ ਨਹੀਂ ਕਰ ਸਕੇ। ਬਾਅਦ ਵਿੱਚ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ ਜਾਂ <EMAIL> ਨਾਲ ਸੰਪਰਕ ਕਰੋ।", "app.containers.UsersEditPage.messageSuccess": "ਤੁਹਾਡਾ ਪ੍ਰੋਫਾਈਲ ਸੁਰੱਖਿਅਤ ਕੀਤਾ ਗਿਆ ਹੈ।", "app.containers.UsersEditPage.metaDescription": "ਇਹ {tenantName}ਦੇ ਔਨਲਾਈਨ ਭਾਗੀਦਾਰੀ ਪਲੇਟਫਾਰਮ 'ਤੇ {firstName} {lastName} ਦਾ ਪ੍ਰੋਫਾਈਲ ਸੈਟਿੰਗ ਪੰਨਾ ਹੈ। ਇੱਥੇ ਤੁਸੀਂ ਆਪਣੀ ਪਛਾਣ ਦੀ ਪੁਸ਼ਟੀ ਕਰ ਸਕਦੇ ਹੋ, ਆਪਣੀ ਖਾਤਾ ਜਾਣਕਾਰੀ ਨੂੰ ਸੰਪਾਦਿਤ ਕਰ ਸਕਦੇ ਹੋ, ਆਪਣਾ ਖਾਤਾ ਮਿਟਾ ਸਕਦੇ ਹੋ ਅਤੇ ਆਪਣੀਆਂ ਈਮੇਲ ਤਰਜੀਹਾਂ ਨੂੰ ਸੰਪਾਦਿਤ ਕਰ ਸਕਦੇ ਹੋ।", "app.containers.UsersEditPage.metaTitle1": "{firstName} {lastName} ਦਾ ਪ੍ਰੋਫਾਈਲ ਸੈਟਿੰਗ ਸਫ਼ਾ | {orgName}", "app.containers.UsersEditPage.noGoingBack": "ਇੱਕ ਵਾਰ ਜਦੋਂ ਤੁਸੀਂ ਇਸ ਬਟਨ 'ਤੇ ਕਲਿੱਕ ਕਰਦੇ ਹੋ, ਤਾਂ ਸਾਡੇ ਕੋਲ ਤੁਹਾਡੇ ਖਾਤੇ ਨੂੰ ਬਹਾਲ ਕਰਨ ਦਾ ਕੋਈ ਤਰੀਕਾ ਨਹੀਂ ਹੋਵੇਗਾ।", "app.containers.UsersEditPage.noNameWarning2": "ਤੁਹਾਡਾ ਨਾਮ ਵਰਤਮਾਨ ਵਿੱਚ ਪਲੇਟਫਾਰਮ 'ਤੇ ਇਸ ਤਰ੍ਹਾਂ ਪ੍ਰਦਰਸ਼ਿਤ ਕੀਤਾ ਗਿਆ ਹੈ: \"{displayName}\" ਕਿਉਂਕਿ ਤੁਸੀਂ ਆਪਣਾ ਨਾਮ ਦਰਜ ਨਹੀਂ ਕੀਤਾ ਹੈ। ਇਹ ਇੱਕ ਸਵੈ-ਤਿਆਰ ਨਾਮ ਹੈ। ਜੇਕਰ ਤੁਸੀਂ ਇਸਨੂੰ ਬਦਲਣਾ ਚਾਹੁੰਦੇ ਹੋ, ਤਾਂ ਕਿਰਪਾ ਕਰਕੇ ਹੇਠਾਂ ਆਪਣਾ ਨਾਮ ਦਰਜ ਕਰੋ।", "app.containers.UsersEditPage.notificationsSubTitle": "ਤੁਸੀਂ ਕਿਸ ਕਿਸਮ ਦੀਆਂ ਈਮੇਲ ਸੂਚਨਾਵਾਂ ਪ੍ਰਾਪਤ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ? ", "app.containers.UsersEditPage.notificationsTitle": "ਈਮੇਲ ਸੂਚਨਾਵਾਂ", "app.containers.UsersEditPage.password": "ਇੱਕ ਨਵਾਂ ਪਾਸਵਰਡ ਚੁਣੋ", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "ਇੱਕ ਪਾਸਵਰਡ ਪ੍ਰਦਾਨ ਕਰੋ ਜੋ ਘੱਟੋ-ਘੱਟ {minimumPasswordLength} ਅੱਖਰਾਂ ਦਾ ਹੋਵੇ", "app.containers.UsersEditPage.passwordAddSection": "ਇੱਕ ਪਾਸਵਰਡ ਸ਼ਾਮਲ ਕਰੋ", "app.containers.UsersEditPage.passwordAddSubtitle2": "ਹਰ ਵਾਰ ਆਪਣੀ ਈਮੇਲ ਦੀ ਪੁਸ਼ਟੀ ਕੀਤੇ ਬਿਨਾਂ, ਇੱਕ ਪਾਸਵਰਡ ਸੈੱਟ ਕਰੋ ਅਤੇ ਪਲੇਟਫਾਰਮ 'ਤੇ ਆਸਾਨੀ ਨਾਲ ਲੌਗਇਨ ਕਰੋ।", "app.containers.UsersEditPage.passwordChangeSection": "ਆਪਣਾ ਪਾਸਵਰਡ ਬਦਲੋ", "app.containers.UsersEditPage.passwordChangeSubtitle": "ਆਪਣੇ ਮੌਜੂਦਾ ਪਾਸਵਰਡ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ ਅਤੇ ਨਵੇਂ ਪਾਸਵਰਡ ਵਿੱਚ ਬਦਲੋ।", "app.containers.UsersEditPage.privacyReasons": "ਜੇਕਰ ਤੁਸੀਂ ਆਪਣੀ ਗੋਪਨੀਯਤਾ ਬਾਰੇ ਚਿੰਤਤ ਹੋ, ਤਾਂ ਤੁਸੀਂ {conditionsLink}ਨੂੰ ਪੜ੍ਹ ਸਕਦੇ ਹੋ।", "app.containers.UsersEditPage.processing": "ਭੇਜਿਆ ਜਾ ਰਿਹਾ ਹੈ...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "ਆਖਰੀ ਨਾਮ ਪ੍ਰਦਾਨ ਕਰਦੇ ਸਮੇਂ ਪਹਿਲਾ ਨਾਮ ਲੋੜੀਂਦਾ ਹੈ", "app.containers.UsersEditPage.reasonsToStayListTitle": "ਤੁਹਾਡੇ ਜਾਣ ਤੋਂ ਪਹਿਲਾਂ...", "app.containers.UsersEditPage.submit": "ਤਬਦੀਲੀਆਂ ਨੂੰ ਸੁਰੱਖਿਅਤ ਕਰੋ", "app.containers.UsersEditPage.tooManyEmails": "ਬਹੁਤ ਸਾਰੀਆਂ ਈਮੇਲਾਂ ਪ੍ਰਾਪਤ ਕਰ ਰਹੇ ਹੋ? ਤੁਸੀਂ ਆਪਣੀਆਂ ਪ੍ਰੋਫਾਈਲ ਸੈਟਿੰਗਾਂ ਵਿੱਚ ਆਪਣੀਆਂ ਈਮੇਲ ਤਰਜੀਹਾਂ ਦਾ ਪ੍ਰਬੰਧਨ ਕਰ ਸਕਦੇ ਹੋ।", "app.containers.UsersEditPage.updateverification": "ਕੀ ਤੁਹਾਡੀ ਅਧਿਕਾਰਤ ਜਾਣਕਾਰੀ ਬਦਲ ਗਈ ਹੈ? {reverifyButton}", "app.containers.UsersEditPage.user": "ਤੁਸੀਂ ਕਦੋਂ ਚਾਹੁੰਦੇ ਹੋ ਕਿ ਅਸੀਂ ਤੁਹਾਨੂੰ ਸੂਚਿਤ ਕਰਨ ਲਈ ਇੱਕ ਈਮੇਲ ਭੇਜੀਏ?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "ਤੁਸੀਂ ਉਹਨਾਂ ਪ੍ਰੋਜੈਕਟਾਂ ਵਿੱਚ ਹਿੱਸਾ ਲੈ ਸਕਦੇ ਹੋ ਜਿਹਨਾਂ ਲਈ ਤਸਦੀਕ ਦੀ ਲੋੜ ਹੁੰਦੀ ਹੈ।", "app.containers.UsersEditPage.verifiedIdentityTitle": "ਤੁਸੀਂ ਪ੍ਰਮਾਣਿਤ ਹੋ", "app.containers.UsersEditPage.verifyNow": "ਹੁਣੇ ਪੁਸ਼ਟੀ ਕਰੋ", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "ਆਪਣੇ ਜਵਾਬ ਡਾਊਨਲੋਡ ਕਰੋ (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {ਕੋਈ ਪਸੰਦ ਨਹੀਂ} one {1 ਪਸੰਦ} other {# ਪਸੰਦ}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "ਇਨਪੁਟ ਜੋ ਕਿ ਇਹ ਟਿੱਪਣੀ ਇਸਦੇ ਜਵਾਬ ਵਿੱਚ ਪੋਸਟ ਕੀਤੀ ਗਈ ਸੀ:", "app.containers.UsersShowPage.areas": "ਖੇਤਰ", "app.containers.UsersShowPage.commentsWithCount": "ਟਿੱਪਣੀਆਂ ({commentsCount})", "app.containers.UsersShowPage.editProfile": "ਮੇਰੀ ਪ੍ਰੋਫਾਈਲ ਨੂੰ ਸੰਪਾਦਿਤ ਕਰੋ", "app.containers.UsersShowPage.emptyInfoText": "ਤੁਸੀਂ ਉੱਪਰ ਦਿੱਤੇ ਫਿਲਟਰ ਦੀਆਂ ਕਿਸੇ ਵੀ ਆਈਟਮਾਂ ਦਾ ਅਨੁਸਰਣ ਨਹੀਂ ਕਰ ਰਹੇ ਹੋ।", "app.containers.UsersShowPage.eventsWithCount": "ਇਵੈਂਟਸ ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "ਅਨੁਸਰਣ ਕਰ ਰਹੇ ਹਨ ({followingCount})", "app.containers.UsersShowPage.inputs": "ਇਨਪੁਟਸ", "app.containers.UsersShowPage.invisibleTitlePostsList": "ਇਸ ਭਾਗੀਦਾਰ ਦੁਆਰਾ ਸਪੁਰਦ ਕੀਤੇ ਸਾਰੇ ਇਨਪੁਟ", "app.containers.UsersShowPage.invisibleTitleUserComments": "ਇਸ ਭਾਗੀਦਾਰ ਦੁਆਰਾ ਪੋਸਟ ਕੀਤੀਆਂ ਸਾਰੀਆਂ ਟਿੱਪਣੀਆਂ", "app.containers.UsersShowPage.loadMore": "ਹੋਰ ਲੋਡ ਕਰੋ", "app.containers.UsersShowPage.loadMoreComments": "ਹੋਰ ਟਿੱਪਣੀਆਂ ਲੋਡ ਕਰੋ", "app.containers.UsersShowPage.loadingComments": "ਟਿੱਪਣੀਆਂ ਲੋਡ ਕੀਤੀਆਂ ਜਾ ਰਹੀਆਂ ਹਨ...", "app.containers.UsersShowPage.loadingEvents": "ਇਵੈਂਟਾਂ ਨੂੰ ਲੋਡ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ...", "app.containers.UsersShowPage.memberSince": "{date}ਤੋਂ ਮੈਂਬਰ", "app.containers.UsersShowPage.metaTitle1": "{firstName} {lastName} | ਦਾ ਪ੍ਰੋਫਾਈਲ ਪੰਨਾ {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "ਇਸ ਵਿਅਕਤੀ ਨੇ ਅਜੇ ਤੱਕ ਕੋਈ ਟਿੱਪਣੀ ਪੋਸਟ ਨਹੀਂ ਕੀਤੀ ਹੈ।", "app.containers.UsersShowPage.noCommentsForYou": "ਇੱਥੇ ਅਜੇ ਤੱਕ ਕੋਈ ਟਿੱਪਣੀਆਂ ਨਹੀਂ ਹਨ।", "app.containers.UsersShowPage.noEventsForUser": "ਤੁਸੀਂ ਅਜੇ ਤੱਕ ਕਿਸੇ ਵੀ ਸਮਾਗਮ ਵਿੱਚ ਸ਼ਾਮਲ ਨਹੀਂ ਹੋਏ।", "app.containers.UsersShowPage.postsWithCount": "ਬੇਨਤੀਆਂ ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "ਪ੍ਰੋਜੈਕਟ ਫੋਲਡਰ", "app.containers.UsersShowPage.projects": "ਪ੍ਰੋਜੈਕਟਸ", "app.containers.UsersShowPage.proposals": "ਪ੍ਰਸਤਾਵ", "app.containers.UsersShowPage.seePost": "ਸਬਮਿਸ਼ਨ ਦੇਖੋ", "app.containers.UsersShowPage.surveyResponses": "ਜਵਾਬ ({responses})", "app.containers.UsersShowPage.topics": "ਵਿਸ਼ੇ", "app.containers.UsersShowPage.tryAgain": "ਕੋਈ ਗੜਬੜ ਹੋ ਗਈ ਹੈ, ਕਿਰਪਾ ਕਰਕੇ ਬਾਅਦ ਵਿੱਚ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.containers.UsersShowPage.userShowPageMetaDescription": "ਇਹ {orgName}ਦੇ ਔਨਲਾਈਨ ਭਾਗੀਦਾਰੀ ਪਲੇਟਫਾਰਮ 'ਤੇ {firstName} {lastName} ਦਾ ਪ੍ਰੋਫਾਈਲ ਪੰਨਾ ਹੈ। ਇੱਥੇ ਉਹਨਾਂ ਦੇ ਸਾਰੇ ਇੰਪੁੱਟ ਦੀ ਇੱਕ ਸੰਖੇਪ ਜਾਣਕਾਰੀ ਹੈ।", "app.containers.VoteControl.close": "ਬੰਦ ਕਰੋ", "app.containers.VoteControl.voteErrorTitle": "ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ", "app.containers.admin.ContentBuilder.default": "ਡਿਫਾਲਟ", "app.containers.admin.ContentBuilder.imageTextCards": "ਚਿੱਤਰ ਅਤੇ ਟੈਕਸਟ ਕਾਰਡ", "app.containers.admin.ContentBuilder.infoWithAccordions": "ਜਾਣਕਾਰੀ ਅਤੇ ਅਨੁਕੂਲਤਾ", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 ਕਾਲਮ", "app.containers.admin.ContentBuilder.projectDescription": "ਪ੍ਰੋਜੈਕਟ ਦਾ ਵੇਰਵਾ", "app.containers.app.navbar.admin": "ਪਲੇਟਫਾਰਮ ਦਾ ਪ੍ਰਬੰਧਨ ਕਰੋ", "app.containers.app.navbar.allProjects": "ਸਾਰੇ ਪ੍ਰੋਜੈਕਟ", "app.containers.app.navbar.ariaLabel": "ਪ੍ਰਾਇਮਰੀ", "app.containers.app.navbar.closeMobileNavMenu": "ਮੋਬਾਈਲ ਨੈਵੀਗੇਸ਼ਨ ਮੀਨੂ ਬੰਦ ਕਰੋ", "app.containers.app.navbar.editProfile": "ਮੇਰੀ ਸੈਟਿੰਗ", "app.containers.app.navbar.fullMobileNavigation": "ਪੂਰਾ ਮੋਬਾਈਲ", "app.containers.app.navbar.logIn": "ਲਾਗਿਨ", "app.containers.app.navbar.logoImgAltText": "{orgName} ਘਰ", "app.containers.app.navbar.myProfile": "ਮੇਰੀ ਗਤੀਵਿਧੀ", "app.containers.app.navbar.search": "ਖੋਜ", "app.containers.app.navbar.showFullMenu": "ਪੂਰਾ ਮੀਨੂ ਦਿਖਾਓ", "app.containers.app.navbar.signOut": "ਸਾਇਨ ਆਉਟ", "app.containers.eventspage.errorWhenFetchingEvents": "ਇਵੈਂਟਾਂ ਨੂੰ ਲੋਡ ਕਰਨ ਦੌਰਾਨ ਇੱਕ ਤਰੁੱਟੀ ਉਤਪੰਨ ਹੋਈ। ਕਿਰਪਾ ਕਰਕੇ ਪੰਨੇ ਨੂੰ ਮੁੜ ਲੋਡ ਕਰਨ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.containers.eventspage.events": "ਸਮਾਗਮ", "app.containers.eventspage.eventsPageDescription": "{orgName}ਦੇ ਪਲੇਟਫਾਰਮ 'ਤੇ ਪੋਸਟ ਕੀਤੇ ਸਾਰੇ ਇਵੈਂਟ ਦਿਖਾਓ।", "app.containers.eventspage.eventsPageTitle1": "ਸਮਾਗਮ | {orgName}", "app.containers.eventspage.filterDropdownTitle": "ਪ੍ਰੋਜੈਕਟਸ", "app.containers.eventspage.noPastEvents": "ਪ੍ਰਦਰਸ਼ਿਤ ਕਰਨ ਲਈ ਕੋਈ ਪਿਛਲੀਆਂ ਘਟਨਾਵਾਂ ਨਹੀਂ ਹਨ", "app.containers.eventspage.noUpcomingOrOngoingEvents": "ਵਰਤਮਾਨ ਵਿੱਚ ਕੋਈ ਆਗਾਮੀ ਜਾਂ ਚੱਲ ਰਹੇ ਸਮਾਗਮਾਂ ਦਾ ਸਮਾਂ ਨਿਯਤ ਨਹੀਂ ਕੀਤਾ ਗਿਆ ਹੈ।", "app.containers.eventspage.pastEvents": "ਪਿਛਲੀਆਂ ਘਟਨਾਵਾਂ", "app.containers.eventspage.upcomingAndOngoingEvents": "ਆਗਾਮੀ ਅਤੇ ਚੱਲ ਰਹੇ ਸਮਾਗਮ", "app.containers.footer.accessibility-statement": "ਪਹੁੰਚਯੋਗਤਾ ਬਿਆਨ", "app.containers.footer.ariaLabel": "ਸੈਕੰਡਰੀ", "app.containers.footer.cookie-policy": "ਕੂਕੀ ਨੀਤੀ", "app.containers.footer.cookieSettings": "ਕੂਕੀ ਸੈਟਿੰਗਾਂ", "app.containers.footer.feedbackEmptyError": "ਫੀਡਬੈਕ ਖੇਤਰ ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ ਹੈ।", "app.containers.footer.poweredBy": "ਦੁਆਰਾ ਸੰਚਾਲਿਤ", "app.containers.footer.privacy-policy": "ਪਰਾਈਵੇਟ ਨੀਤੀ", "app.containers.footer.siteMap": "ਸਾਈਟ ਦਾ ਨਕਸ਼ਾ", "app.containers.footer.terms-and-conditions": "ਨਿਬੰਧਨ ਅਤੇ ਸ਼ਰਤਾਂ", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "ਰੱਦ ਕਰੋ", "app.containers.ideaHeading.confirmLeaveFormButtonText": "ਹਾਂ, ਮੈਂ ਜਾਣਾ ਚਾਹੁੰਦਾ ਹਾਂ।", "app.containers.ideaHeading.editForm": "ਫਾਰਮ ਦਾ ਸੰਪਾਦਨ ਕਰੋ", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "ਕੀ ਤੁਸੀਂ ਪੱਕਾ ਜਾਣਾ ਚਾਹੁੰਦੇ ਹੋ?", "app.containers.ideaHeading.leaveIdeaForm": "ਵਿਚਾਰ ਫਾਰਮ ਛੱਡੋ", "app.containers.ideaHeading.leaveIdeaText": "ਤੁਹਾਡੇ ਜਵਾਬ ਰੱਖਿਅਤ ਨਹੀਂ ਕੀਤੇ ਜਾਣਗੇ।", "app.containers.landing.cityProjects": "ਪ੍ਰੋਜੈਕਟਸ", "app.containers.landing.completeProfile": "ਆਪਣੀ ਪ੍ਰੋਫਾਈਲ ਨੂੰ ਪੂਰਾ ਕਰੋ", "app.containers.landing.completeYourProfile": "ਜੀ ਆਇਆਂ ਨੂੰ, {firstName}. ਇਹ ਤੁਹਾਡੀ ਪ੍ਰੋਫਾਈਲ ਨੂੰ ਪੂਰਾ ਕਰਨ ਦਾ ਸਮਾਂ ਹੈ।", "app.containers.landing.createAccount": "ਸਾਇਨ ਅਪ", "app.containers.landing.defaultSignedInMessage": "{orgName} ਤੁਹਾਨੂੰ ਸੁਣ ਰਿਹਾ ਹੈ। ਆਪਣੀ ਆਵਾਜ਼ ਸੁਣਾਉਣ ਦੀ ਤੁਹਾਡੀ ਵਾਰੀ ਹੈ!", "app.containers.landing.doItLater": "ਮੈਂ ਇਸਨੂੰ ਬਾਅਦ ਵਿੱਚ ਕਰਾਂਗਾ", "app.containers.landing.new": "ਨਵਾਂ", "app.containers.landing.subtitleCity": "{orgName}ਦੇ ਭਾਗੀਦਾਰੀ ਪਲੇਟਫਾਰਮ ਵਿੱਚ ਤੁਹਾਡਾ ਸੁਆਗਤ ਹੈ", "app.containers.landing.titleCity": "ਆਉ ਮਿਲ ਕੇ {orgName} ਦੇ ਭਵਿੱਖ ਨੂੰ ਆਕਾਰ ਦੇਈਏ", "app.containers.landing.twitterMessage": "{ideaTitle} ਲਈ ਵੋਟ ਕਰੋ", "app.containers.landing.upcomingEventsWidgetTitle": "ਆਗਾਮੀ ਅਤੇ ਚੱਲ ਰਹੇ ਸਮਾਗਮ", "app.containers.landing.userDeletedSubtitle": "ਤੁਸੀਂ ਕਿਸੇ ਵੀ ਸਮੇਂ ਨਵਾਂ ਖਾਤਾ ਬਣਾ ਸਕਦੇ ਹੋ ਜਾਂ {contactLink} ਸਾਨੂੰ ਇਹ ਦੱਸਣ ਲਈ ਕਿ ਅਸੀਂ ਕੀ ਸੁਧਾਰ ਕਰ ਸਕਦੇ ਹਾਂ।", "app.containers.landing.userDeletedSubtitleLinkText": "ਸਾਨੂੰ ਇੱਕ ਲਾਈਨ ਸੁੱਟੋ", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "ਤੁਹਾਡਾ ਖਾਤਾ ਮਿਟਾ ਦਿੱਤਾ ਗਿਆ ਹੈ।", "app.containers.landing.userDeletionFailed": "ਤੁਹਾਡੇ ਖਾਤੇ ਨੂੰ ਮਿਟਾਉਣ ਵਿੱਚ ਇੱਕ ਤਰੁੱਟੀ ਉਤਪੰਨ ਹੋਈ, ਸਾਨੂੰ ਸਮੱਸਿਆ ਬਾਰੇ ਸੂਚਿਤ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਇਸਨੂੰ ਠੀਕ ਕਰਨ ਦੀ ਪੂਰੀ ਕੋਸ਼ਿਸ਼ ਕਰਾਂਗੇ। ਕਿਰਪਾ ਕਰਕੇ ਬਾਅਦ ਵਿੱਚ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.containers.landing.verifyNow": "ਹੁਣੇ ਪੁਸ਼ਟੀ ਕਰੋ", "app.containers.landing.verifyYourIdentity": "ਆਪਣੀ ਪਛਾਣ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ", "app.containers.landing.viewAllEventsText": "ਸਾਰੀਆਂ ਘਟਨਾਵਾਂ ਵੇਖੋ", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "ਫੋਲਡਰ 'ਤੇ ਵਾਪਸ ਜਾਓ", "app.errors.after_end_at": "ਸ਼ੁਰੂਆਤੀ ਮਿਤੀ ਸਮਾਪਤੀ ਮਿਤੀ ਤੋਂ ਬਾਅਦ ਹੁੰਦੀ ਹੈ", "app.errors.avatar_carrierwave_download_error": "ਅਵਤਾਰ ਫ਼ਾਈਲ ਡਾਊਨਲੋਡ ਨਹੀਂ ਕੀਤੀ ਜਾ ਸਕੀ।", "app.errors.avatar_carrierwave_integrity_error": "ਅਵਤਾਰ ਫਾਈਲ ਇੱਕ ਪ੍ਰਵਾਨਿਤ ਕਿਸਮ ਦੀ ਨਹੀਂ ਹੈ।", "app.errors.avatar_carrierwave_processing_error": "ਅਵਤਾਰ ਦੀ ਪ੍ਰਕਿਰਿਆ ਨਹੀਂ ਕੀਤੀ ਜਾ ਸਕੀ।", "app.errors.avatar_extension_blacklist_error": "ਅਵਤਾਰ ਚਿੱਤਰ ਦੀ ਫਾਈਲ ਐਕਸਟੈਂਸ਼ਨ ਦੀ ਇਜਾਜ਼ਤ ਨਹੀਂ ਹੈ। ਮਨਜ਼ੂਰਸ਼ੁਦਾ ਐਕਸਟੈਂਸ਼ਨਾਂ ਹਨ: jpg, jpeg, gif ਅਤੇ png।", "app.errors.avatar_extension_whitelist_error": "ਅਵਤਾਰ ਚਿੱਤਰ ਦੀ ਫਾਈਲ ਐਕਸਟੈਂਸ਼ਨ ਦੀ ਇਜਾਜ਼ਤ ਨਹੀਂ ਹੈ। ਮਨਜ਼ੂਰਸ਼ੁਦਾ ਐਕਸਟੈਂਸ਼ਨਾਂ ਹਨ: jpg, jpeg, gif ਅਤੇ png।", "app.errors.banner_cta_button_multiloc_blank": "ਇੱਕ ਬਟਨ ਟੈਕਸਟ ਦਰਜ ਕਰੋ।", "app.errors.banner_cta_button_url_blank": "ਇੱਕ ਲਿੰਕ ਦਾਖਲ ਕਰੋ।", "app.errors.banner_cta_button_url_url": "ਇੱਕ ਵੈਧ ਲਿੰਕ ਦਾਖਲ ਕਰੋ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਲਿੰਕ 'https://' ਨਾਲ ਸ਼ੁਰੂ ਹੁੰਦਾ ਹੈ।", "app.errors.banner_cta_signed_in_text_multiloc_blank": "ਇੱਕ ਬਟਨ ਟੈਕਸਟ ਦਰਜ ਕਰੋ।", "app.errors.banner_cta_signed_in_url_blank": "ਇੱਕ ਲਿੰਕ ਦਾਖਲ ਕਰੋ।", "app.errors.banner_cta_signed_in_url_url": "ਇੱਕ ਵੈਧ ਲਿੰਕ ਦਾਖਲ ਕਰੋ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਲਿੰਕ 'https://' ਨਾਲ ਸ਼ੁਰੂ ਹੁੰਦਾ ਹੈ।", "app.errors.banner_cta_signed_out_text_multiloc_blank": "ਇੱਕ ਬਟਨ ਟੈਕਸਟ ਦਰਜ ਕਰੋ।", "app.errors.banner_cta_signed_out_url_blank": "ਇੱਕ ਲਿੰਕ ਦਾਖਲ ਕਰੋ।", "app.errors.banner_cta_signed_out_url_url": "ਇੱਕ ਵੈਧ ਲਿੰਕ ਦਾਖਲ ਕਰੋ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਲਿੰਕ 'https://' ਨਾਲ ਸ਼ੁਰੂ ਹੁੰਦਾ ਹੈ।", "app.errors.base_includes_banned_words": "ਤੁਸੀਂ ਇੱਕ ਜਾਂ ਵੱਧ ਸ਼ਬਦਾਂ ਦੀ ਵਰਤੋਂ ਕੀਤੀ ਹੋ ਸਕਦੀ ਹੈ ਜਿਨ੍ਹਾਂ ਨੂੰ ਅਪਮਾਨਜਨਕ ਮੰਨਿਆ ਜਾਂਦਾ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਮੌਜੂਦ ਕਿਸੇ ਵੀ ਅਪਮਾਨਜਨਕ ਸ਼ਬਦਾਂ ਨੂੰ ਹਟਾਉਣ ਲਈ ਆਪਣੇ ਟੈਕਸਟ ਨੂੰ ਬਦਲੋ।", "app.errors.body_multiloc_includes_banned_words": "ਵਰਣਨ ਵਿੱਚ ਅਜਿਹੇ ਸ਼ਬਦ ਹਨ ਜੋ ਅਣਉਚਿਤ ਮੰਨੇ ਜਾਂਦੇ ਹਨ।", "app.errors.bulk_import_idea_not_valid": "ਨਤੀਜਾ ਵਿਚਾਰ ਵੈਧ ਨਹੀਂ ਹੈ: {value}.", "app.errors.bulk_import_image_url_not_valid": "{value}ਤੋਂ ਕੋਈ ਚਿੱਤਰ ਡਾਊਨਲੋਡ ਨਹੀਂ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ URL ਵੈਧ ਹੈ ਅਤੇ ਇੱਕ ਫਾਈਲ ਐਕਸਟੈਂਸ਼ਨ ਜਿਵੇਂ ਕਿ .png ਜਾਂ .jpg ਨਾਲ ਖਤਮ ਹੁੰਦਾ ਹੈ। ਇਹ ਮੁੱਦਾ ID {row}ਵਾਲੀ ਕਤਾਰ ਵਿੱਚ ਹੁੰਦਾ ਹੈ।", "app.errors.bulk_import_location_point_blank_coordinate": "{value}ਵਿੱਚ ਇੱਕ ਗੁੰਮ ਕੋਆਰਡੀਨੇਟ ਦੇ ਨਾਲ ਆਈਡੀਆ ਟਿਕਾਣਾ। ਇਹ ਮੁੱਦਾ ID {row}ਵਾਲੀ ਕਤਾਰ ਵਿੱਚ ਹੁੰਦਾ ਹੈ।", "app.errors.bulk_import_location_point_non_numeric_coordinate": "{value}ਵਿੱਚ ਇੱਕ ਗੈਰ-ਸੰਖਿਆਤਮਕ ਕੋਆਰਡੀਨੇਟ ਦੇ ਨਾਲ ਆਈਡੀਆ ਟਿਕਾਣਾ। ਇਹ ਮੁੱਦਾ ID {row}ਵਾਲੀ ਕਤਾਰ ਵਿੱਚ ਹੁੰਦਾ ਹੈ।", "app.errors.bulk_import_malformed_pdf": "ਅਪਲੋਡ ਕੀਤੀ PDF ਫਾਈਲ ਨੁਕਸਦਾਰ ਜਾਪਦੀ ਹੈ। ਆਪਣੇ ਸਰੋਤ ਤੋਂ PDF ਨੂੰ ਦੁਬਾਰਾ ਨਿਰਯਾਤ ਕਰਨ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰੋ ਅਤੇ ਫਿਰ ਦੁਬਾਰਾ ਅੱਪਲੋਡ ਕਰੋ।", "app.errors.bulk_import_maximum_ideas_exceeded": "{value} ਵਿਚਾਰਾਂ ਦੀ ਅਧਿਕਤਮ ਸੀਮਾ ਪਾਰ ਹੋ ਗਈ ਹੈ।", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "ਇੱਕ PDF ਵਿੱਚ {value} ਪੰਨਿਆਂ ਦੀ ਅਧਿਕਤਮ ਸੀਮਾ ਪਾਰ ਹੋ ਗਈ ਹੈ।", "app.errors.bulk_import_not_enough_pdf_pages": "ਅੱਪਲੋਡ ਕੀਤੀ PDF ਵਿੱਚ ਲੋੜੀਂਦੇ ਪੰਨੇ ਨਹੀਂ ਹਨ - ਇਸ ਵਿੱਚ ਡਾਊਨਲੋਡ ਕੀਤੇ ਟੈਮਪਲੇਟ ਵਾਂਗ ਘੱਟੋ-ਘੱਟ ਪੰਨਿਆਂ ਦੀ ਗਿਣਤੀ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ।", "app.errors.bulk_import_publication_date_invalid_format": "ਅਵੈਧ ਪ੍ਰਕਾਸ਼ਨ ਮਿਤੀ ਫਾਰਮੈਟ \"{value}\" ਵਾਲਾ ਵਿਚਾਰ। ਕਿਰਪਾ ਕਰਕੇ \"DD-MM-YYYY\" ਫਾਰਮੈਟ ਦੀ ਵਰਤੋਂ ਕਰੋ।", "app.errors.cannot_contain_ideas": "ਤੁਹਾਡੇ ਦੁਆਰਾ ਚੁਣੀ ਗਈ ਭਾਗੀਦਾਰੀ ਵਿਧੀ ਇਸ ਕਿਸਮ ਦੀ ਪੋਸਟ ਦਾ ਸਮਰਥਨ ਨਹੀਂ ਕਰਦੀ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੀ ਚੋਣ ਨੂੰ ਸੰਪਾਦਿਤ ਕਰੋ ਅਤੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.errors.cant_change_after_first_response": "ਤੁਸੀਂ ਹੁਣ ਇਸਨੂੰ ਬਦਲ ਨਹੀਂ ਸਕਦੇ, ਕਿਉਂਕਿ ਕੁਝ ਉਪਭੋਗਤਾ ਪਹਿਲਾਂ ਹੀ ਜਵਾਬ ਦੇ ਚੁੱਕੇ ਹਨ", "app.errors.category_name_taken": "ਇਸ ਨਾਮ ਵਾਲੀ ਸ਼੍ਰੇਣੀ ਪਹਿਲਾਂ ਹੀ ਮੌਜੂਦ ਹੈ", "app.errors.confirmation_code_expired": "ਕੋਡ ਦੀ ਮਿਆਦ ਪੁੱਗ ਗਈ। ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਨਵੇਂ ਕੋਡ ਦੀ ਬੇਨਤੀ ਕਰੋ।", "app.errors.confirmation_code_invalid": "ਅਵੈਧ ਪੁਸ਼ਟੀਕਰਨ ਕੋਡ। ਕਿਰਪਾ ਕਰਕੇ ਸਹੀ ਕੋਡ ਲਈ ਆਪਣੀ ਈਮੇਲ ਦੀ ਜਾਂਚ ਕਰੋ ਜਾਂ 'ਨਵਾਂ ਕੋਡ ਭੇਜੋ' ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.errors.confirmation_code_too_many_resets": "ਤੁਸੀਂ ਪੁਸ਼ਟੀਕਰਨ ਕੋਡ ਨੂੰ ਕਈ ਵਾਰ ਮੁੜ ਭੇਜਿਆ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇਸਦੀ ਬਜਾਏ ਇੱਕ ਸੱਦਾ ਕੋਡ ਪ੍ਰਾਪਤ ਕਰਨ ਲਈ ਸਾਡੇ ਨਾਲ ਸੰਪਰਕ ਕਰੋ।", "app.errors.confirmation_code_too_many_retries": "ਤੁਸੀਂ ਬਹੁਤ ਵਾਰ ਕੋਸ਼ਿਸ਼ ਕੀਤੀ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਨਵੇਂ ਕੋਡ ਦੀ ਬੇਨਤੀ ਕਰੋ ਜਾਂ ਆਪਣੀ ਈਮੇਲ ਬਦਲਣ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰੋ।", "app.errors.email_already_active": "ਕਤਾਰ {row} ਵਿੱਚ ਪਾਇਆ ਗਿਆ ਈਮੇਲ ਪਤਾ {value} ਪਹਿਲਾਂ ਹੀ ਇੱਕ ਰਜਿਸਟਰਡ ਭਾਗੀਦਾਰ ਦਾ ਹੈ", "app.errors.email_already_invited": "{row} ਕਤਾਰ ਵਿੱਚ ਮਿਲਿਆ ਈਮੇਲ ਪਤਾ {value} ਪਹਿਲਾਂ ਹੀ ਸੱਦਾ ਦਿੱਤਾ ਗਿਆ ਸੀ", "app.errors.email_blank": "ਇਹ ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ", "app.errors.email_domain_blacklisted": "ਕਿਰਪਾ ਕਰਕੇ ਰਜਿਸਟਰ ਕਰਨ ਲਈ ਇੱਕ ਵੱਖਰਾ ਈਮੇਲ ਡੋਮੇਨ ਵਰਤੋ।", "app.errors.email_invalid": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਵੈਧ ਈਮੇਲ ਪਤਾ ਵਰਤੋ।", "app.errors.email_taken": "ਇਸ ਈਮੇਲ ਵਾਲਾ ਖਾਤਾ ਪਹਿਲਾਂ ਹੀ ਮੌਜੂਦ ਹੈ। ਤੁਸੀਂ ਇਸ ਦੀ ਬਜਾਏ ਲੌਗਇਨ ਕਰ ਸਕਦੇ ਹੋ।", "app.errors.email_taken_by_invite": "{value} ਪਹਿਲਾਂ ਹੀ ਇੱਕ ਲੰਬਿਤ ਸੱਦਾ ਦੁਆਰਾ ਲਿਆ ਗਿਆ ਹੈ। ਆਪਣੇ ਸਪੈਮ ਫੋਲਡਰ ਦੀ ਜਾਂਚ ਕਰੋ ਜਾਂ ਜੇਕਰ ਤੁਹਾਨੂੰ ਇਹ ਨਹੀਂ ਮਿਲਦਾ ਤਾਂ {supportEmail} ਨਾਲ ਸੰਪਰਕ ਕਰੋ।", "app.errors.emails_duplicate": "ਈਮੇਲ ਪਤੇ {value} ਲਈ ਇੱਕ ਜਾਂ ਇੱਕ ਤੋਂ ਵੱਧ ਡੁਪਲੀਕੇਟ ਮੁੱਲ ਹੇਠਾਂ ਦਿੱਤੀਆਂ ਕਤਾਰਾਂ ਵਿੱਚ ਮਿਲੇ ਹਨ: {rows}", "app.errors.extension_whitelist_error": "ਤੁਹਾਡੇ ਦੁਆਰਾ ਅੱਪਲੋਡ ਕਰਨ ਦੀ ਕੋਸ਼ਿਸ਼ ਕੀਤੀ ਗਈ ਫਾਈਲ ਦਾ ਫਾਰਮੈਟ ਸਮਰਥਿਤ ਨਹੀਂ ਹੈ।", "app.errors.file_extension_whitelist_error": "ਤੁਹਾਡੇ ਦੁਆਰਾ ਅੱਪਲੋਡ ਕਰਨ ਦੀ ਕੋਸ਼ਿਸ਼ ਕੀਤੀ ਫਾਈਲ ਦਾ ਫਾਰਮੈਟ ਸਮਰਥਿਤ ਨਹੀਂ ਹੈ।", "app.errors.first_name_blank": "ਇਹ ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ", "app.errors.generics.blank": "ਇਹ ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ।", "app.errors.generics.invalid": "ਇਹ ਵੈਧ ਮੁੱਲ ਨਹੀਂ ਜਾਪਦਾ", "app.errors.generics.taken": "ਇਹ ਈਮੇਲ ਪਹਿਲਾਂ ਹੀ ਮੌਜੂਦ ਹੈ। ਇੱਕ ਹੋਰ ਖਾਤਾ ਇਸ ਨਾਲ ਜੁੜਿਆ ਹੋਇਆ ਹੈ।", "app.errors.generics.unsupported_locales": "ਇਹ ਖੇਤਰ ਮੌਜੂਦਾ ਲੋਕੇਲ ਦਾ ਸਮਰਥਨ ਨਹੀਂ ਕਰਦਾ ਹੈ।", "app.errors.group_ids_unauthorized_choice_moderator": "ਇੱਕ ਪ੍ਰੋਜੈਕਟ ਮੈਨੇਜਰ ਵਜੋਂ, ਤੁਸੀਂ ਸਿਰਫ਼ ਉਹਨਾਂ ਲੋਕਾਂ ਨੂੰ ਈਮੇਲ ਕਰ ਸਕਦੇ ਹੋ ਜੋ ਤੁਹਾਡੇ ਪ੍ਰੋਜੈਕਟ (ਪ੍ਰੋਜੈਕਟਾਂ) ਤੱਕ ਪਹੁੰਚ ਕਰ ਸਕਦੇ ਹਨ", "app.errors.has_other_overlapping_phases": "ਪ੍ਰੋਜੈਕਟਾਂ ਵਿੱਚ ਓਵਰਲੈਪਿੰਗ ਪੜਾਅ ਨਹੀਂ ਹੋ ਸਕਦੇ ਹਨ।", "app.errors.invalid_email": "ਕਤਾਰ {row} ਵਿੱਚ ਮਿਲੀ ਈਮੇਲ {value} ਇੱਕ ਵੈਧ ਈਮੇਲ ਪਤਾ ਨਹੀਂ ਹੈ", "app.errors.invalid_row": "ਕਤਾਰ {row}ਦੀ ਪ੍ਰਕਿਰਿਆ ਕਰਨ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਸਮੇਂ ਇੱਕ ਅਗਿਆਤ ਤਰੁੱਟੀ ਆਈ ਹੈ", "app.errors.is_not_timeline_project": "ਮੌਜੂਦਾ ਪ੍ਰੋਜੈਕਟ ਪੜਾਵਾਂ ਦਾ ਸਮਰਥਨ ਨਹੀਂ ਕਰਦਾ ਹੈ।", "app.errors.key_invalid": "ਕੁੰਜੀ ਵਿੱਚ ਸਿਰਫ਼ ਅੱਖਰ, ਨੰਬਰ ਅਤੇ ਅੰਡਰਸਕੋਰ (_) ਸ਼ਾਮਲ ਹੋ ਸਕਦੇ ਹਨ", "app.errors.last_name_blank": "ਇਹ ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ", "app.errors.locale_blank": "ਕਿਰਪਾ ਕਰਕੇ ਕੋਈ ਭਾਸ਼ਾ ਚੁਣੋ", "app.errors.locale_inclusion": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਸਮਰਥਿਤ ਭਾਸ਼ਾ ਚੁਣੋ", "app.errors.malformed_admin_value": "ਕਤਾਰ {row} ਵਿੱਚ ਮਿਲਿਆ ਪ੍ਰਸ਼ਾਸਕ ਮੁੱਲ {value} ਵੈਧ ਨਹੀਂ ਹੈ", "app.errors.malformed_groups_value": "ਕਤਾਰ {row} ਵਿੱਚ ਪਾਇਆ ਗਿਆ ਸਮੂਹ {value} ਇੱਕ ਵੈਧ ਸਮੂਹ ਨਹੀਂ ਹੈ", "app.errors.max_invites_limit_exceeded1": "ਸੱਦਿਆਂ ਦੀ ਗਿਣਤੀ 1000 ਦੀ ਸੀਮਾ ਤੋਂ ਵੱਧ ਗਈ ਹੈ।", "app.errors.maximum_attendees_greater_than1": "ਰਜਿਸਟਰ ਕਰਨ ਵਾਲਿਆਂ ਦੀ ਵੱਧ ਤੋਂ ਵੱਧ ਗਿਣਤੀ 0 ਤੋਂ ਵੱਧ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ।", "app.errors.maximum_attendees_greater_than_attendees_count1": "ਰਜਿਸਟਰਾਂ ਦੀ ਵੱਧ ਤੋਂ ਵੱਧ ਗਿਣਤੀ ਮੌਜੂਦਾ ਰਜਿਸਟਰਾਂ ਦੀ ਗਿਣਤੀ ਤੋਂ ਵੱਧ ਜਾਂ ਇਸਦੇ ਬਰਾਬਰ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ।", "app.errors.no_invites_specified": "ਕੋਈ ਈਮੇਲ ਪਤਾ ਨਹੀਂ ਲੱਭ ਸਕਿਆ।", "app.errors.no_recipients": "ਮੁਹਿੰਮ ਨੂੰ ਬਾਹਰ ਨਹੀਂ ਭੇਜਿਆ ਜਾ ਸਕਦਾ ਕਿਉਂਕਿ ਕੋਈ ਪ੍ਰਾਪਤਕਰਤਾ ਨਹੀਂ ਹਨ। ਜਿਸ ਗਰੁੱਪ ਨੂੰ ਤੁਸੀਂ ਭੇਜ ਰਹੇ ਹੋ, ਉਹ ਜਾਂ ਤਾਂ ਖਾਲੀ ਹੈ, ਜਾਂ ਕਿਸੇ ਨੇ ਵੀ ਈਮੇਲਾਂ ਪ੍ਰਾਪਤ ਕਰਨ ਲਈ ਸਹਿਮਤੀ ਨਹੀਂ ਦਿੱਤੀ ਹੈ।", "app.errors.number_invalid": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਵੈਧ ਨੰਬਰ ਦਾਖਲ ਕਰੋ।", "app.errors.password_blank": "ਇਹ ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ", "app.errors.password_invalid": "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੇ ਮੌਜੂਦਾ ਪਾਸਵਰਡ ਦੀ ਦੁਬਾਰਾ ਜਾਂਚ ਕਰੋ।", "app.errors.password_too_short": "ਪਾਸਵਰਡ ਘੱਟੋ-ਘੱਟ 8 ਅੱਖਰਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.errors.resending_code_failed": "ਪੁਸ਼ਟੀਕਰਨ ਕੋਡ ਭੇਜਣ ਦੌਰਾਨ ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ।", "app.errors.slug_taken": "ਇਹ ਪ੍ਰੋਜੈਕਟ URL ਪਹਿਲਾਂ ਹੀ ਮੌਜੂਦ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਪ੍ਰੋਜੈਕਟ ਸਲੱਗ ਨੂੰ ਕਿਸੇ ਹੋਰ ਚੀਜ਼ ਵਿੱਚ ਬਦਲੋ।", "app.errors.tag_name_taken": "ਇਸ ਨਾਮ ਦਾ ਇੱਕ ਟੈਗ ਪਹਿਲਾਂ ਹੀ ਮੌਜੂਦ ਹੈ", "app.errors.title_multiloc_blank": "ਸਿਰਲੇਖ ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ।", "app.errors.title_multiloc_includes_banned_words": "ਸਿਰਲੇਖ ਵਿੱਚ ਅਜਿਹੇ ਸ਼ਬਦ ਹਨ ਜੋ ਅਣਉਚਿਤ ਮੰਨੇ ਜਾਂਦੇ ਹਨ।", "app.errors.token_invalid": "ਪਾਸਵਰਡ ਰੀਸੈਟ ਲਿੰਕ ਸਿਰਫ਼ ਇੱਕ ਵਾਰ ਵਰਤੇ ਜਾ ਸਕਦੇ ਹਨ ਅਤੇ ਭੇਜੇ ਜਾਣ ਤੋਂ ਬਾਅਦ ਇੱਕ ਘੰਟੇ ਲਈ ਵੈਧ ਹੁੰਦੇ ਹਨ। {passwordResetLink}.", "app.errors.too_common": "ਇਸ ਪਾਸਵਰਡ ਦਾ ਆਸਾਨੀ ਨਾਲ ਅੰਦਾਜ਼ਾ ਲਗਾਇਆ ਜਾ ਸਕਦਾ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਮਜ਼ਬੂਤ ਪਾਸਵਰਡ ਚੁਣੋ।", "app.errors.too_long": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਛੋਟਾ ਪਾਸਵਰਡ ਚੁਣੋ (ਵੱਧ ਤੋਂ ਵੱਧ 72 ਅੱਖਰ)", "app.errors.too_short": "ਕਿਰਪਾ ਕਰਕੇ ਘੱਟੋ-ਘੱਟ 8 ਅੱਖਰਾਂ ਵਾਲਾ ਪਾਸਵਰਡ ਚੁਣੋ", "app.errors.uncaught_error": "ਇੱਕ ਅਗਿਆਤ ਤਰੁੱਟੀ ਆਈ ਹੈ।", "app.errors.unknown_group": "ਕਤਾਰ {row} ਵਿੱਚ ਪਾਇਆ ਗਿਆ ਸਮੂਹ {value} ਇੱਕ ਜਾਣਿਆ ਸਮੂਹ ਨਹੀਂ ਹੈ", "app.errors.unknown_locale": "ਕਤਾਰ {row} ਵਿੱਚ ਪਾਈ ਗਈ ਭਾਸ਼ਾ {value} ਇੱਕ ਸੰਰਚਿਤ ਭਾਸ਼ਾ ਨਹੀਂ ਹੈ", "app.errors.unparseable_excel": "ਚੁਣੀ ਗਈ ਐਕਸਲ ਫਾਈਲ 'ਤੇ ਕਾਰਵਾਈ ਨਹੀਂ ਕੀਤੀ ਜਾ ਸਕੀ।", "app.errors.url": "ਇੱਕ ਵੈਧ ਲਿੰਕ ਦਾਖਲ ਕਰੋ। ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਲਿੰਕ https:// ਨਾਲ ਸ਼ੁਰੂ ਹੁੰਦਾ ਹੈ", "app.errors.verification_taken": "ਪੁਸ਼ਟੀਕਰਨ ਨੂੰ ਪੂਰਾ ਨਹੀਂ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ ਕਿਉਂਕਿ ਉਸੇ ਵੇਰਵਿਆਂ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਕਿਸੇ ਹੋਰ ਖਾਤੇ ਦੀ ਪੁਸ਼ਟੀ ਕੀਤੀ ਗਈ ਹੈ।", "app.errors.view_name_taken": "ਇਸ ਨਾਮ ਨਾਲ ਇੱਕ ਦ੍ਰਿਸ਼ ਪਹਿਲਾਂ ਹੀ ਮੌਜੂਦ ਹੈ", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "ਇੱਕ ਪੋਸਟ ਜਾਂ ਟਿੱਪਣੀ ਵਿੱਚ ਅਣਉਚਿਤ ਸਮਗਰੀ ਦਾ ਸਵੈ-ਪਛਾਣ ਕੀਤਾ ਗਿਆ ਸੀ", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "ਸਟੈਂਡਰਡਪੋਰਟਲ ਨਾਲ ਸਾਈਨ ਇਨ ਕਰੋ", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "ਸਟੈਂਡਰਡਪੋਰਟਲ ਨਾਲ ਸਾਈਨ ਅੱਪ ਕਰੋ", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "ਹੁਣੇ ਇੱਕ Stadt Wien ਖਾਤਾ ਬਣਾਓ ਅਤੇ ਵਿਏਨਾ ਦੀਆਂ ਕਈ ਡਿਜੀਟਲ ਸੇਵਾਵਾਂ ਲਈ ਇੱਕ ਲੌਗਇਨ ਦੀ ਵਰਤੋਂ ਕਰੋ।", "app.modules.id_cow.cancel": "ਰੱਦ ਕਰੋ", "app.modules.id_cow.emptyFieldError": "ਇਹ ਖੇਤਰ ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ ਹੈ।", "app.modules.id_cow.helpAltText": "ਇਹ ਦਿਖਾਉਂਦਾ ਹੈ ਕਿ ਪਛਾਣ ਪੱਤਰ 'ਤੇ ਆਈਡੀ ਸੀਰੀਅਲ ਨੰਬਰ ਕਿੱਥੇ ਲੱਭਣਾ ਹੈ", "app.modules.id_cow.invalidIdSerialError": "ਅਵੈਧ ਆਈਡੀ ਸੀਰੀਅਲ", "app.modules.id_cow.invalidRunError": "ਅਵੈਧ RUN", "app.modules.id_cow.noMatchFormError": "ਕੋਈ ਮੇਲ ਨਹੀਂ ਮਿਲਿਆ।", "app.modules.id_cow.notEntitledFormError": "ਹੱਕਦਾਰ ਨਹੀਂ।", "app.modules.id_cow.showCOWHelp": "ਮੈਨੂੰ ਆਪਣਾ ਆਈਡੀ ਸੀਰੀਅਲ ਨੰਬਰ ਕਿੱਥੇ ਮਿਲ ਸਕਦਾ ਹੈ?", "app.modules.id_cow.somethingWentWrongError": "ਅਸੀਂ ਤੁਹਾਡੀ ਪੁਸ਼ਟੀ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ ਹੈ", "app.modules.id_cow.submit": "ਜਮ੍ਹਾਂ ਕਰੋ", "app.modules.id_cow.takenFormError": "ਪਹਿਲਾਂ ਤੋਂ ਹੀ ਲਿਆ.", "app.modules.id_cow.verifyCow": "COW ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਪੁਸ਼ਟੀ ਕਰੋ", "app.modules.id_franceconnect.verificationButtonAltText": "FranceConnect ਨਾਲ ਪੁਸ਼ਟੀ ਕਰੋ", "app.modules.id_gent_rrn.cancel": "ਰੱਦ ਕਰੋ", "app.modules.id_gent_rrn.emptyFieldError": "ਇਹ ਖੇਤਰ ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ ਹੈ।", "app.modules.id_gent_rrn.gentRrnHelp": "ਤੁਹਾਡਾ ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ ਤੁਹਾਡੇ ਡਿਜੀਟਲ ਪਛਾਣ ਪੱਤਰ ਦੇ ਪਿਛਲੇ ਪਾਸੇ ਦਿਖਾਇਆ ਗਿਆ ਹੈ", "app.modules.id_gent_rrn.invalidRrnError": "ਅਵੈਧ ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ", "app.modules.id_gent_rrn.noMatchFormError": "ਅਸੀਂ ਤੁਹਾਡੇ ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ 'ਤੇ ਵਾਪਸ ਜਾਣਕਾਰੀ ਨਹੀਂ ਲੱਭ ਸਕੇ", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "ਅਸੀਂ ਤੁਹਾਡੀ ਪੁਸ਼ਟੀ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਤੁਸੀਂ ਗੈਂਟ ਤੋਂ ਬਾਹਰ ਰਹਿੰਦੇ ਹੋ", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "ਅਸੀਂ ਤੁਹਾਡੀ ਪੁਸ਼ਟੀ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਤੁਹਾਡੀ ਉਮਰ 14 ਸਾਲ ਤੋਂ ਘੱਟ ਹੈ", "app.modules.id_gent_rrn.rrnLabel": "ਸਮਾਜਕ ਸੁਰੱਖਿਆ ਨੰਬਰ", "app.modules.id_gent_rrn.rrnTooltip": "ਅਸੀਂ ਇਹ ਪੁਸ਼ਟੀ ਕਰਨ ਲਈ ਤੁਹਾਡੇ ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ ਨੂੰ ਪੁੱਛਦੇ ਹਾਂ ਕਿ ਕੀ ਤੁਸੀਂ 14 ਸਾਲ ਤੋਂ ਵੱਧ ਉਮਰ ਦੇ ਗੈਂਟ ਦੇ ਨਾਗਰਿਕ ਹੋ।", "app.modules.id_gent_rrn.showGentRrnHelp": "ਮੈਨੂੰ ਆਪਣਾ ਆਈਡੀ ਸੀਰੀਅਲ ਨੰਬਰ ਕਿੱਥੇ ਮਿਲ ਸਕਦਾ ਹੈ?", "app.modules.id_gent_rrn.somethingWentWrongError": "ਅਸੀਂ ਤੁਹਾਡੀ ਪੁਸ਼ਟੀ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ ਹੈ", "app.modules.id_gent_rrn.submit": "ਜਮ੍ਹਾਂ ਕਰੋ", "app.modules.id_gent_rrn.takenFormError": "ਤੁਹਾਡਾ ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ ਪਹਿਲਾਂ ਹੀ ਕਿਸੇ ਹੋਰ ਖਾਤੇ ਦੀ ਪੁਸ਼ਟੀ ਕਰਨ ਲਈ ਵਰਤਿਆ ਜਾ ਚੁੱਕਾ ਹੈ", "app.modules.id_gent_rrn.verifyGentRrn": "GentRrn ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਪੁਸ਼ਟੀ ਕਰੋ", "app.modules.id_id_card_lookup.cancel": "ਰੱਦ ਕਰੋ", "app.modules.id_id_card_lookup.emptyFieldError": "ਇਹ ਖੇਤਰ ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ ਹੈ।", "app.modules.id_id_card_lookup.helpAltText": "ਆਈਡੀ ਕਾਰਡ ਦੀ ਵਿਆਖਿਆ", "app.modules.id_id_card_lookup.invalidCardIdError": "ਇਹ ਆਈਡੀ ਵੈਧ ਨਹੀਂ ਹੈ।", "app.modules.id_id_card_lookup.noMatchFormError": "ਕੋਈ ਮੇਲ ਨਹੀਂ ਮਿਲਿਆ।", "app.modules.id_id_card_lookup.showHelp": "ਮੈਨੂੰ ਆਪਣਾ ਆਈਡੀ ਸੀਰੀਅਲ ਨੰਬਰ ਕਿੱਥੇ ਮਿਲ ਸਕਦਾ ਹੈ?", "app.modules.id_id_card_lookup.somethingWentWrongError": "ਅਸੀਂ ਤੁਹਾਡੀ ਪੁਸ਼ਟੀ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ ਹੈ", "app.modules.id_id_card_lookup.submit": "ਜਮ੍ਹਾਂ ਕਰੋ", "app.modules.id_id_card_lookup.takenFormError": "ਪਹਿਲਾਂ ਤੋਂ ਹੀ ਲਿਆ.", "app.modules.id_oostende_rrn.cancel": "ਰੱਦ ਕਰੋ", "app.modules.id_oostende_rrn.emptyFieldError": "ਇਹ ਖੇਤਰ ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ ਹੈ।", "app.modules.id_oostende_rrn.invalidRrnError": "ਅਵੈਧ ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ", "app.modules.id_oostende_rrn.noMatchFormError": "ਅਸੀਂ ਤੁਹਾਡੇ ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ 'ਤੇ ਵਾਪਸ ਜਾਣਕਾਰੀ ਨਹੀਂ ਲੱਭ ਸਕੇ", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "ਅਸੀਂ ਤੁਹਾਡੀ ਪੁਸ਼ਟੀ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਤੁਸੀਂ Oostende ਤੋਂ ਬਾਹਰ ਰਹਿੰਦੇ ਹੋ", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "ਅਸੀਂ ਤੁਹਾਡੀ ਪੁਸ਼ਟੀ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਤੁਹਾਡੀ ਉਮਰ 14 ਸਾਲ ਤੋਂ ਘੱਟ ਹੈ", "app.modules.id_oostende_rrn.oostendeRrnHelp": "ਤੁਹਾਡਾ ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ ਤੁਹਾਡੇ ਡਿਜੀਟਲ ਪਛਾਣ ਪੱਤਰ ਦੇ ਪਿਛਲੇ ਪਾਸੇ ਦਿਖਾਇਆ ਗਿਆ ਹੈ", "app.modules.id_oostende_rrn.rrnLabel": "ਸਮਾਜਕ ਸੁਰੱਖਿਆ ਨੰਬਰ", "app.modules.id_oostende_rrn.rrnTooltip": "ਅਸੀਂ ਇਹ ਪੁਸ਼ਟੀ ਕਰਨ ਲਈ ਤੁਹਾਡੇ ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ ਨੂੰ ਪੁੱਛਦੇ ਹਾਂ ਕਿ ਕੀ ਤੁਸੀਂ 14 ਸਾਲ ਤੋਂ ਵੱਧ ਉਮਰ ਦੇ Oostende ਦੇ ਨਾਗਰਿਕ ਹੋ।", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "ਮੈਂ ਆਪਣਾ ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ ਕਿੱਥੇ ਲੱਭ ਸਕਦਾ/ਸਕਦੀ ਹਾਂ?", "app.modules.id_oostende_rrn.somethingWentWrongError": "ਅਸੀਂ ਤੁਹਾਡੀ ਪੁਸ਼ਟੀ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ ਹੈ", "app.modules.id_oostende_rrn.submit": "ਜਮ੍ਹਾਂ ਕਰੋ", "app.modules.id_oostende_rrn.takenFormError": "ਤੁਹਾਡਾ ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ ਪਹਿਲਾਂ ਹੀ ਕਿਸੇ ਹੋਰ ਖਾਤੇ ਦੀ ਪੁਸ਼ਟੀ ਕਰਨ ਲਈ ਵਰਤਿਆ ਜਾ ਚੁੱਕਾ ਹੈ", "app.modules.id_oostende_rrn.verifyOostendeRrn": "ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਪੁਸ਼ਟੀ ਕਰੋ", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "ਤੁਹਾਨੂੰ \"{folderName}\" ਫੋਲਡਰ ਉੱਤੇ ਪ੍ਰਬੰਧਕ ਅਧਿਕਾਰ ਪ੍ਰਾਪਤ ਹੋਏ ਹਨ।", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "ਸ਼ੇਅਰ ਕਰੋ", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "ਆਪਣੀ ਆਵਾਜ਼ ਸੁਣਨ ਲਈ {folderUrl} 'ਤੇ ਪ੍ਰੋਜੈਕਟ ਦੇਖੋ!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | {orgName}ਦੇ ਭਾਗੀਦਾਰੀ ਪਲੇਟਫਾਰਮ ਤੋਂ", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | {orgName}ਦੇ ਭਾਗੀਦਾਰੀ ਪਲੇਟਫਾਰਮ ਤੋਂ", "app.sessionRecording.accept": "ਹਾਂ, ਮੈਂ ਸਵੀਕਾਰ ਕਰਦਾ ਹਾਂ", "app.sessionRecording.modalDescription1": "ਸਾਡੇ ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਬਿਹਤਰ ਢੰਗ ਨਾਲ ਸਮਝਣ ਲਈ, ਅਸੀਂ ਬੇਤਰਤੀਬੇ ਤੌਰ 'ਤੇ ਵਿਜ਼ਟਰਾਂ ਦੇ ਇੱਕ ਛੋਟੇ ਪ੍ਰਤੀਸ਼ਤ ਨੂੰ ਉਹਨਾਂ ਦੇ ਬ੍ਰਾਊਜ਼ਿੰਗ ਸੈਸ਼ਨ ਨੂੰ ਵਿਸਥਾਰ ਵਿੱਚ ਟਰੈਕ ਕਰਨ ਲਈ ਕਹਿੰਦੇ ਹਾਂ।", "app.sessionRecording.modalDescription2": "ਰਿਕਾਰਡ ਕੀਤੇ ਡੇਟਾ ਦਾ ਇੱਕੋ ਇੱਕ ਉਦੇਸ਼ ਵੈਬਸਾਈਟ ਨੂੰ ਬਿਹਤਰ ਬਣਾਉਣਾ ਹੈ। ਤੁਹਾਡਾ ਕੋਈ ਵੀ ਡੇਟਾ ਤੀਜੀ ਧਿਰ ਨਾਲ ਸਾਂਝਾ ਨਹੀਂ ਕੀਤਾ ਜਾਵੇਗਾ। ਤੁਹਾਡੇ ਵੱਲੋਂ ਦਾਖਲ ਕੀਤੀ ਗਈ ਕੋਈ ਵੀ ਸੰਵੇਦਨਸ਼ੀਲ ਜਾਣਕਾਰੀ ਫਿਲਟਰ ਕੀਤੀ ਜਾਵੇਗੀ।", "app.sessionRecording.modalDescription3": "ਕੀ ਤੁਸੀਂ ਸਵੀਕਾਰ ਕਰਦੇ ਹੋ?", "app.sessionRecording.modalDescriptionFaq": "ਇੱਥੇ FAQ.", "app.sessionRecording.modalTitle": "ਇਸ ਵੈੱਬਸਾਈਟ ਨੂੰ ਬਿਹਤਰ ਬਣਾਉਣ ਵਿੱਚ ਸਾਡੀ ਮਦਦ ਕਰੋ", "app.sessionRecording.reject": "ਨਹੀਂ, ਮੈਂ ਅਸਵੀਕਾਰ ਕਰਦਾ ਹਾਂ", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "ਬਜਟ ਵੰਡ ਅਭਿਆਸ ਕਰੋ", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "ਇੱਕ ਦਸਤਾਵੇਜ਼ 'ਤੇ ਫੀਡਬੈਕ ਇਕੱਠਾ ਕਰੋ", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "ਇੱਕ ਇਨ-ਪਲੇਟਫਾਰਮ ਸਰਵੇਖਣ ਬਣਾਓ", "app.utils.AdminPage.ProjectEdit.createPoll": "ਇੱਕ ਪੋਲ ਬਣਾਓ", "app.utils.AdminPage.ProjectEdit.createSurveyText": "ਇੱਕ ਬਾਹਰੀ ਸਰਵੇਖਣ ਸ਼ਾਮਲ ਕਰੋ", "app.utils.AdminPage.ProjectEdit.findVolunteers": "ਵਲੰਟੀਅਰ ਲੱਭੋ", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "ਇਨਪੁਟ ਅਤੇ ਫੀਡਬੈਕ ਇਕੱਠਾ ਕਰੋ", "app.utils.AdminPage.ProjectEdit.shareInformation": "ਜਾਣਕਾਰੀ ਸਾਂਝੀ ਕਰੋ", "app.utils.FormattedCurrency.credits": "ਕ੍ਰੈਡਿਟ", "app.utils.FormattedCurrency.tokens": "ਟੋਕਨ", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# ਕ੍ਰੈਡਿਟ} one {# ਕ੍ਰੈਡਿਟ} other {# ਕ੍ਰੈਡਿਟ}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# ਟੋਕਨ} one {# ਟੋਕਨ} other {# ਟੋਕਨ}}", "app.utils.IdeaCards.mostDiscussed": "ਸਭ ਤੋਂ ਵੱਧ ਚਰਚਾ ਕੀਤੀ", "app.utils.IdeaCards.mostReacted": "ਜ਼ਿਆਦਾਤਰ ਪ੍ਰਤੀਕਰਮ", "app.utils.IdeaCards.newest": "ਸਭ ਤੋਂ ਨਵਾਂ", "app.utils.IdeaCards.oldest": "ਸਭ ਤੋਂ ਪੁਰਾਣਾ", "app.utils.IdeaCards.random": "ਬੇਤਰਤੀਬ", "app.utils.IdeaCards.trending": "ਪ੍ਰਚਲਿਤ", "app.utils.IdeasNewPage.contributionFormTitle": "ਨਵਾਂ ਯੋਗਦਾਨ ਸ਼ਾਮਲ ਕਰੋ", "app.utils.IdeasNewPage.ideaFormTitle": "ਨਵਾਂ ਵਿਚਾਰ ਸ਼ਾਮਲ ਕਰੋ", "app.utils.IdeasNewPage.initiativeFormTitle": "ਨਵੀਂ ਪਹਿਲ ਸ਼ਾਮਲ ਕਰੋ", "app.utils.IdeasNewPage.issueFormTitle1": "ਨਵੀਂ ਟਿੱਪਣੀ ਸ਼ਾਮਲ ਕਰੋ", "app.utils.IdeasNewPage.optionFormTitle": "ਨਵਾਂ ਵਿਕਲਪ ਸ਼ਾਮਲ ਕਰੋ", "app.utils.IdeasNewPage.petitionFormTitle": "ਨਵੀਂ ਪਟੀਸ਼ਨ ਸ਼ਾਮਲ ਕਰੋ", "app.utils.IdeasNewPage.projectFormTitle": "ਨਵਾਂ ਪ੍ਰੋਜੈਕਟ ਸ਼ਾਮਲ ਕਰੋ", "app.utils.IdeasNewPage.proposalFormTitle": "ਨਵਾਂ ਪ੍ਰਸਤਾਵ ਸ਼ਾਮਲ ਕਰੋ", "app.utils.IdeasNewPage.questionFormTitle": "ਨਵਾਂ ਸਵਾਲ ਸ਼ਾਮਲ ਕਰੋ", "app.utils.IdeasNewPage.surveyTitle": "ਸਰਵੇਖਣ", "app.utils.IdeasNewPage.viewYourComment": "ਆਪਣੀ ਟਿੱਪਣੀ ਵੇਖੋ", "app.utils.IdeasNewPage.viewYourContribution": "ਆਪਣਾ ਯੋਗਦਾਨ ਦੇਖੋ", "app.utils.IdeasNewPage.viewYourIdea": "ਆਪਣਾ ਵਿਚਾਰ ਵੇਖੋ", "app.utils.IdeasNewPage.viewYourInitiative": "ਆਪਣੀ ਪਹਿਲ ਵੇਖੋ", "app.utils.IdeasNewPage.viewYourInput": "ਆਪਣਾ ਇਨਪੁੱਟ ਵੇਖੋ", "app.utils.IdeasNewPage.viewYourIssue": "ਆਪਣੀ ਸਮੱਸਿਆ ਵੇਖੋ", "app.utils.IdeasNewPage.viewYourOption": "ਆਪਣਾ ਵਿਕਲਪ ਵੇਖੋ", "app.utils.IdeasNewPage.viewYourPetition": "ਆਪਣੀ ਪਟੀਸ਼ਨ ਵੇਖੋ", "app.utils.IdeasNewPage.viewYourProject": "ਆਪਣਾ ਪ੍ਰੋਜੈਕਟ ਵੇਖੋ", "app.utils.IdeasNewPage.viewYourProposal": "ਆਪਣਾ ਪ੍ਰਸਤਾਵ ਵੇਖੋ", "app.utils.IdeasNewPage.viewYourQuestion": "ਆਪਣਾ ਸਵਾਲ ਵੇਖੋ", "app.utils.Projects.sendSubmission": "ਮੇਰੇ ਈਮੇਲ 'ਤੇ ਸਬਮਿਸ਼ਨ ਪਛਾਣਕਰਤਾ ਭੇਜੋ", "app.utils.Projects.sendSurveySubmission": "ਸਰਵੇਖਣ ਸਬਮਿਸ਼ਨ ਪਛਾਣਕਰਤਾ ਨੂੰ ਮੇਰੀ ਈਮੇਲ 'ਤੇ ਭੇਜੋ", "app.utils.Projects.surveySubmission": "ਸਰਵੇਖਣ ਸਪੁਰਦਗੀ", "app.utils.Projects.yourResponseHasTheFollowingId": "ਤੁਹਾਡੇ ਜਵਾਬ ਵਿੱਚ ਨਿਮਨਲਿਖਤ ਪਛਾਣਕਰਤਾ ਹੈ: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "ਜੇਕਰ ਤੁਸੀਂ ਬਾਅਦ ਵਿੱਚ ਫੈਸਲਾ ਕਰਦੇ ਹੋ ਕਿ ਤੁਸੀਂ ਆਪਣੇ ਜਵਾਬ ਨੂੰ ਹਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ, ਤਾਂ ਕਿਰਪਾ ਕਰਕੇ ਹੇਠਾਂ ਦਿੱਤੇ ਵਿਲੱਖਣ ਪਛਾਣਕਰਤਾ ਨਾਲ ਸਾਡੇ ਨਾਲ ਸੰਪਰਕ ਕਰੋ:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "ਇਸ ਇਵੈਂਟ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਣ ਲਈ ਤੁਹਾਨੂੰ ਆਪਣਾ ਪ੍ਰੋਫਾਈਲ ਪੂਰਾ ਕਰਨਾ ਪਵੇਗਾ।", "app.utils.actionDescriptors.attendingEventNotInGroup": "ਤੁਸੀਂ ਇਸ ਇਵੈਂਟ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਣ ਲਈ ਲੋੜਾਂ ਨੂੰ ਪੂਰਾ ਨਹੀਂ ਕਰਦੇ ਹੋ।", "app.utils.actionDescriptors.attendingEventNotPermitted": "ਤੁਹਾਨੂੰ ਇਸ ਸਮਾਗਮ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਣ ਦੀ ਇਜਾਜ਼ਤ ਨਹੀਂ ਹੈ।", "app.utils.actionDescriptors.attendingEventNotSignedIn": "ਤੁਹਾਨੂੰ ਇਸ ਇਵੈਂਟ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਣ ਲਈ ਲੌਗ ਇਨ ਜਾਂ ਰਜਿਸਟਰ ਕਰਨਾ ਪਵੇਗਾ।", "app.utils.actionDescriptors.attendingEventNotVerified": "ਇਸ ਇਵੈਂਟ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਣ ਤੋਂ ਪਹਿਲਾਂ ਤੁਹਾਨੂੰ ਆਪਣੇ ਖਾਤੇ ਦੀ ਪੁਸ਼ਟੀ ਕਰਨੀ ਚਾਹੀਦੀ ਹੈ।", "app.utils.actionDescriptors.volunteeringMissingRequirements": "ਵਲੰਟੀਅਰ ਬਣਨ ਲਈ ਤੁਹਾਨੂੰ ਆਪਣਾ ਪ੍ਰੋਫਾਈਲ ਪੂਰਾ ਕਰਨਾ ਚਾਹੀਦਾ ਹੈ।", "app.utils.actionDescriptors.volunteeringNotInGroup": "ਤੁਸੀਂ ਵਲੰਟੀਅਰ ਲਈ ਲੋੜਾਂ ਨੂੰ ਪੂਰਾ ਨਹੀਂ ਕਰਦੇ ਹੋ।", "app.utils.actionDescriptors.volunteeringNotPermitted": "ਤੁਹਾਨੂੰ ਵਲੰਟੀਅਰ ਕਰਨ ਦੀ ਇਜਾਜ਼ਤ ਨਹੀਂ ਹੈ।", "app.utils.actionDescriptors.volunteeringNotSignedIn": "ਤੁਹਾਨੂੰ ਵਲੰਟੀਅਰ ਲਈ ਲੌਗ ਇਨ ਜਾਂ ਰਜਿਸਟਰ ਕਰਨਾ ਚਾਹੀਦਾ ਹੈ।", "app.utils.actionDescriptors.volunteeringNotVerified": "ਵਲੰਟੀਅਰ ਬਣਨ ਤੋਂ ਪਹਿਲਾਂ ਤੁਹਾਨੂੰ ਆਪਣੇ ਖਾਤੇ ਦੀ ਪੁਸ਼ਟੀ ਕਰਨੀ ਚਾਹੀਦੀ ਹੈ।", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "ਕਿਰਪਾ ਕਰਕੇ ਵਲੰਟੀਅਰ ਬਣਨ ਲਈ {completeRegistrationLink} ।", "app.utils.errors.api_error_default.in": "ਸਹੀ ਨਹੀਂ ਹੈ", "app.utils.errors.default.ajv_error_birthyear_required": "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਜਨਮ ਸਾਲ ਭਰੋ", "app.utils.errors.default.ajv_error_date_any": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਵੈਧ ਮਿਤੀ ਭਰੋ", "app.utils.errors.default.ajv_error_domicile_required": "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੇ ਨਿਵਾਸ ਸਥਾਨ ਨੂੰ ਭਰੋ", "app.utils.errors.default.ajv_error_gender_required": "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਲਿੰਗ ਭਰੋ", "app.utils.errors.default.ajv_error_invalid": "ਅਵੈਧ ਹੈ", "app.utils.errors.default.ajv_error_maxItems": "{limit, plural, one {ਤੋਂ ਵੱਧ ਆਈਟਮਾਂ ਨੂੰ ਸ਼ਾਮਲ ਨਹੀਂ ਕੀਤਾ ਜਾ ਸਕਦਾ} other {# ਆਈਟਮਾਂ}}", "app.utils.errors.default.ajv_error_minItems": "ਘੱਟੋ-ਘੱਟ {limit, plural, one {# ਆਈਟਮ} other {# ਆਈਟਮਾਂ}}ਨੂੰ ਸ਼ਾਮਲ ਕਰਨਾ ਲਾਜ਼ਮੀ ਹੈ", "app.utils.errors.default.ajv_error_number_any": "ਕਿਰਪਾ ਕਰਕੇ ਇੱਕ ਵੈਧ ਨੰਬਰ ਭਰੋ", "app.utils.errors.default.ajv_error_politician_required": "ਕਿਰਪਾ ਕਰਕੇ ਭਰੋ ਕਿ ਕੀ ਤੁਸੀਂ ਇੱਕ ਸਿਆਸਤਦਾਨ ਹੋ", "app.utils.errors.default.ajv_error_required3": "ਖੇਤਰ ਦੀ ਲੋੜ ਹੈ: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ", "app.utils.errors.default.api_error_accepted": "ਸਵੀਕਾਰ ਕੀਤਾ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ", "app.utils.errors.default.api_error_blank": "ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ", "app.utils.errors.default.api_error_confirmation": "ਮੇਲ ਨਹੀਂ ਖਾਂਦਾ", "app.utils.errors.default.api_error_empty": "ਖਾਲੀ ਨਹੀਂ ਹੋ ਸਕਦਾ", "app.utils.errors.default.api_error_equal_to": "ਸਹੀ ਨਹੀਂ ਹੈ", "app.utils.errors.default.api_error_even": "ਬਰਾਬਰ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.utils.errors.default.api_error_exclusion": "ਰਾਖਵਾਂ ਹੈ", "app.utils.errors.default.api_error_greater_than": "ਬਹੁਤ ਛੋਟਾ ਹੈ", "app.utils.errors.default.api_error_greater_than_or_equal_to": "ਬਹੁਤ ਛੋਟਾ ਹੈ", "app.utils.errors.default.api_error_inclusion": "ਸੂਚੀ ਵਿੱਚ ਸ਼ਾਮਲ ਨਹੀਂ ਹੈ", "app.utils.errors.default.api_error_invalid": "ਅਵੈਧ ਹੈ", "app.utils.errors.default.api_error_less_than": "ਬਹੁਤ ਵੱਡਾ ਹੈ", "app.utils.errors.default.api_error_less_than_or_equal_to": "ਬਹੁਤ ਵੱਡਾ ਹੈ", "app.utils.errors.default.api_error_not_a_number": "ਨੰਬਰ ਨਹੀਂ ਹੈ", "app.utils.errors.default.api_error_not_an_integer": "ਇੱਕ ਪੂਰਨ ਅੰਕ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.utils.errors.default.api_error_other_than": "ਸਹੀ ਨਹੀਂ ਹੈ", "app.utils.errors.default.api_error_present": "ਖਾਲੀ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.utils.errors.default.api_error_too_long": "ਬਹੁਤ ਲੰਮਾ ਹੈ", "app.utils.errors.default.api_error_too_short": "ਬਹੁਤ ਛੋਟਾ ਹੈ", "app.utils.errors.default.api_error_wrong_length": "ਗਲਤ ਲੰਬਾਈ ਹੈ", "app.utils.errors.defaultapi_error_.odd": "ਅਜੀਬ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ", "app.utils.notInGroup": "ਤੁਸੀਂ ਭਾਗ ਲੈਣ ਲਈ ਲੋੜਾਂ ਨੂੰ ਪੂਰਾ ਨਹੀਂ ਕਰਦੇ ਹੋ।", "app.utils.participationMethod.onSurveySubmission": "ਤੁਹਾਡਾ ਧੰਨਵਾਦ. ਤੁਹਾਡਾ ਜਵਾਬ ਮਿਲ ਗਿਆ ਹੈ।", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "ਵੋਟਿੰਗ ਹੁਣ ਉਪਲਬਧ ਨਹੀਂ ਹੈ, ਕਿਉਂਕਿ ਇਹ ਪੜਾਅ ਹੁਣ ਕਿਰਿਆਸ਼ੀਲ ਨਹੀਂ ਹੈ।", "app.utils.participationMethodConfig.voting.votingNotInGroup": "ਤੁਸੀਂ ਵੋਟ ਪਾਉਣ ਲਈ ਲੋੜਾਂ ਨੂੰ ਪੂਰਾ ਨਹੀਂ ਕਰਦੇ ਹੋ।", "app.utils.participationMethodConfig.voting.votingNotPermitted": "ਤੁਹਾਨੂੰ ਵੋਟ ਪਾਉਣ ਦੀ ਇਜਾਜ਼ਤ ਨਹੀਂ ਹੈ।", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "ਵੋਟ ਪਾਉਣ ਲਈ ਤੁਹਾਨੂੰ ਲੌਗ ਇਨ ਜਾਂ ਰਜਿਸਟਰ ਕਰਨਾ ਪਵੇਗਾ।", "app.utils.participationMethodConfig.voting.votingNotVerified": "ਵੋਟ ਪਾਉਣ ਤੋਂ ਪਹਿਲਾਂ ਤੁਹਾਨੂੰ ਆਪਣੇ ਖਾਤੇ ਦੀ ਪੁਸ਼ਟੀ ਕਰਨੀ ਚਾਹੀਦੀ ਹੈ।", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>ਬਜਟ ਪੇਸ਼ ਕਰਨਾ {endDate}ਨੂੰ ਬੰਦ ਹੋ ਗਿਆ।</b> ਭਾਗੀਦਾਰਾਂ ਕੋਲ ਕੁੱਲ <b>{maxBudget} ਹਰ ਇੱਕ ਨੂੰ {optionCount} ਵਿਕਲਪਾਂ ਵਿਚਕਾਰ ਵੰਡਣ ਲਈ ਸੀ।</b>", "app.utils.votingMethodUtils.budgetSubmitted": "ਬਜਟ ਪੇਸ਼ ਕੀਤਾ", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "ਬਜਟ ਪੇਸ਼ ਕੀਤਾ ਗਿਆ 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "ਤੁਸੀਂ ਬਜਟ ਨਿਰਧਾਰਤ ਕਰਨ ਦੀਆਂ ਲੋੜਾਂ ਨੂੰ ਪੂਰਾ ਨਹੀਂ ਕਰਦੇ ਹੋ।", "app.utils.votingMethodUtils.budgetingNotPermitted": "ਤੁਹਾਨੂੰ ਬਜਟ ਨਿਰਧਾਰਤ ਕਰਨ ਦੀ ਇਜਾਜ਼ਤ ਨਹੀਂ ਹੈ।", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "ਤੁਹਾਨੂੰ ਬਜਟ ਨਿਰਧਾਰਤ ਕਰਨ ਲਈ ਲੌਗ ਇਨ ਜਾਂ ਰਜਿਸਟਰ ਕਰਨਾ ਚਾਹੀਦਾ ਹੈ।", "app.utils.votingMethodUtils.budgetingNotVerified": "ਬਜਟ ਨਿਰਧਾਰਤ ਕਰਨ ਤੋਂ ਪਹਿਲਾਂ ਤੁਹਾਨੂੰ ਆਪਣੇ ਖਾਤੇ ਦੀ ਪੁਸ਼ਟੀ ਕਰਨੀ ਚਾਹੀਦੀ ਹੈ।", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>ਤੁਹਾਡੇ ਬਜਟ ਨੂੰ</b> ਨਹੀਂ ਗਿਣਿਆ ਜਾਵੇਗਾ ਜਦੋਂ ਤੱਕ ਤੁਸੀਂ \"ਸਬਮਿਟ\" 'ਤੇ ਕਲਿੱਕ ਨਹੀਂ ਕਰਦੇ", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "ਘੱਟੋ-ਘੱਟ ਲੋੜੀਂਦਾ ਬਜਟ {amount}ਹੈ।", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "ਇੱਕ ਵਾਰ ਜਦੋਂ ਤੁਸੀਂ ਪੂਰਾ ਕਰ ਲੈਂਦੇ ਹੋ, ਤਾਂ ਆਪਣਾ ਬਜਟ ਜਮ੍ਹਾ ਕਰਨ ਲਈ \"ਸਬਮਿਟ\" 'ਤੇ ਕਲਿੱਕ ਕਰੋ।", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "\"ਜੋੜੋ\" 'ਤੇ ਟੈਪ ਕਰਕੇ ਆਪਣੇ ਪਸੰਦੀਦਾ ਵਿਕਲਪਾਂ ਦੀ ਚੋਣ ਕਰੋ।", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "ਤੁਹਾਡੇ ਕੋਲ {optionCount} ਵਿਕਲਪਾਂ</b>ਵਿਚਕਾਰ ਵੰਡਣ ਲਈ ਕੁੱਲ <b>{maxBudget} ਹਨ।", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b>ਵਧਾਈਆਂ, ਤੁਹਾਡਾ ਬਜਟ ਜਮ੍ਹਾਂ ਕਰ ਦਿੱਤਾ ਗਿਆ ਹੈ!</b> ਤੁਸੀਂ ਕਿਸੇ ਵੀ ਸਮੇਂ ਹੇਠਾਂ ਆਪਣੇ ਵਿਕਲਪਾਂ ਦੀ ਜਾਂਚ ਕਰ ਸਕਦੇ ਹੋ ਜਾਂ ਉਹਨਾਂ ਨੂੰ <b>{endDate}</b>ਤੋਂ ਪਹਿਲਾਂ ਸੋਧ ਸਕਦੇ ਹੋ।", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b>ਵਧਾਈਆਂ, ਤੁਹਾਡਾ ਬਜਟ ਜਮ੍ਹਾਂ ਕਰ ਦਿੱਤਾ ਗਿਆ ਹੈ!</b> ਤੁਸੀਂ ਕਿਸੇ ਵੀ ਸਮੇਂ ਹੇਠਾਂ ਆਪਣੇ ਵਿਕਲਪਾਂ ਦੀ ਜਾਂਚ ਕਰ ਸਕਦੇ ਹੋ।", "app.utils.votingMethodUtils.castYourVote": "ਆਪਣੀ ਵੋਟ ਪਾਓ", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "ਤੁਸੀਂ ਪ੍ਰਤੀ ਵਿਕਲਪ ਵੱਧ ਤੋਂ ਵੱਧ {maxVotes, plural, one {# ਕ੍ਰੈਡਿਟ} other {# ਕ੍ਰੈਡਿਟ}} ਜੋੜ ਸਕਦੇ ਹੋ।", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "ਤੁਸੀਂ ਪ੍ਰਤੀ ਵਿਕਲਪ ਵੱਧ ਤੋਂ ਵੱਧ {maxVotes, plural, one {# ਅੰਕ} other {# ਅੰਕ}} ਜੋੜ ਸਕਦੇ ਹੋ।", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "ਤੁਸੀਂ ਪ੍ਰਤੀ ਵਿਕਲਪ ਵੱਧ ਤੋਂ ਵੱਧ {maxVotes, plural, one {# ਟੋਕਨ} other {# ਟੋਕਨ}} ਜੋੜ ਸਕਦੇ ਹੋ।", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "ਤੁਸੀਂ ਪ੍ਰਤੀ ਵਿਕਲਪ ਵੱਧ ਤੋਂ ਵੱਧ {maxVotes, plural, one {# ਵੋਟ} other {# ਵੋਟਾਂ}} ਜੋੜ ਸਕਦੇ ਹੋ।", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "ਇੱਕ ਵਾਰ ਜਦੋਂ ਤੁਸੀਂ ਪੂਰਾ ਕਰ ਲੈਂਦੇ ਹੋ, ਤਾਂ ਆਪਣੀ ਵੋਟ ਪਾਉਣ ਲਈ \"ਸਬਮਿਟ\" 'ਤੇ ਕਲਿੱਕ ਕਰੋ।", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "\"ਚੁਣੋ\" 'ਤੇ ਟੈਪ ਕਰਕੇ ਆਪਣੇ ਪਸੰਦੀਦਾ ਵਿਕਲਪਾਂ ਨੂੰ ਚੁਣੋ।", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "ਤੁਹਾਡੇ ਕੋਲ ਕੁੱਲ <b>{totalVotes, plural, one {# ਕ੍ਰੈਡਿਟ} other {# ਕ੍ਰੈਡਿਟ}} {optionCount, plural, one {# ਵਿਕਲਪ} other {# ਵਿਕਲਪ}}</b>ਵਿੱਚ ਵੰਡਣ ਲਈ ਹਨ।", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "ਤੁਹਾਡੇ ਕੋਲ ਕੁੱਲ <b>{totalVotes, plural, one {# ਅੰਕ} other {# ਅੰਕ}} {optionCount, plural, one {# ਵਿਕਲਪ} other {# ਵਿਕਲਪ}}</b>ਵਿੱਚ ਵੰਡਣ ਲਈ ਹਨ।", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "ਤੁਹਾਡੇ ਕੋਲ ਕੁੱਲ <b>{totalVotes, plural, one {# ਟੋਕਨ} other {# ਟੋਕਨ}} {optionCount, plural, one {# ਵਿਕਲਪ} other {# ਵਿਕਲਪ}}</b>ਵਿੱਚ ਵੰਡਣ ਲਈ ਹਨ।", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "ਤੁਹਾਡੇ ਕੋਲ ਕੁੱਲ <b>{totalVotes, plural, one {# ਵੋਟ} other {# ਵੋਟਾਂ}} {optionCount, plural, one {# ਵਿਕਲਪ} other {# ਵਿਕਲਪ}}</b>ਵਿੱਚ ਵੰਡਣ ਲਈ ਹਨ।", "app.utils.votingMethodUtils.finalResults": "ਅੰਤਿਮ ਨਤੀਜੇ", "app.utils.votingMethodUtils.finalTally": "ਅੰਤਮ ਗਿਣਤੀ", "app.utils.votingMethodUtils.howToParticipate": "ਕਿਵੇਂ ਭਾਗ ਲੈਣਾ ਹੈ", "app.utils.votingMethodUtils.howToVote": "ਵੋਟ ਕਿਵੇਂ ਪਾਉਣੀ ਹੈ", "app.utils.votingMethodUtils.multipleVotingEnded1": "ਵੋਟਿੰਗ <b>{endDate}ਨੂੰ ਬੰਦ ਹੋ ਗਈ।</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 ਕ੍ਰੈਡਿਟ} one {1 ਕ੍ਰੈਡਿਟ} other {# ਕ੍ਰੈਡਿਟ}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 ਅੰਕ} one {1 ਅੰਕ} other {# ਅੰਕ}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 ਟੋਕਨ} one {1 ਟੋਕਨ} other {# ਟੋਕਨ}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 ਵੋਟਾਂ} one {1 ਵੋਟ} other {# ਵੋਟਾਂ}}", "app.utils.votingMethodUtils.results": "ਨਤੀਜੇ", "app.utils.votingMethodUtils.singleVotingEnded": "<b>{endDate}ਨੂੰ ਵੋਟਿੰਗ ਬੰਦ ਹੋਈ।</b> ਭਾਗੀਦਾਰ <b> {maxVotes} ਵਿਕਲਪਾਂ ਲਈ ਵੋਟ ਦੇ ਸਕਦੇ ਹਨ।</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "\"ਵੋਟ\" 'ਤੇ ਟੈਪ ਕਰਕੇ ਆਪਣੇ ਪਸੰਦੀਦਾ ਵਿਕਲਪਾਂ ਦੀ ਚੋਣ ਕਰੋ", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "ਤੁਹਾਡੇ ਕੋਲ <b>{totalVotes} ਵੋਟਾਂ ਹਨ</b> ਜੋ ਤੁਸੀਂ ਵਿਕਲਪਾਂ ਨੂੰ ਨਿਰਧਾਰਤ ਕਰ ਸਕਦੇ ਹੋ।", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "ਇੱਕ ਵਾਰ ਜਦੋਂ ਤੁਸੀਂ ਪੂਰਾ ਕਰ ਲੈਂਦੇ ਹੋ, ਤਾਂ ਆਪਣੀ ਵੋਟ ਪਾਉਣ ਲਈ \"ਸਬਮਿਟ\" 'ਤੇ ਕਲਿੱਕ ਕਰੋ।", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "<b>{endDate}ਨੂੰ ਵੋਟਿੰਗ ਬੰਦ ਹੋਈ।</b> ਭਾਗੀਦਾਰ 1 ਵਿਕਲਪ ਲਈ <b>ਵੋਟ ਦੇ ਸਕਦੇ ਹਨ।</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "\"ਵੋਟ\" 'ਤੇ ਟੈਪ ਕਰਕੇ ਆਪਣਾ ਪਸੰਦੀਦਾ ਵਿਕਲਪ ਚੁਣੋ।", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "ਤੁਹਾਡੇ ਕੋਲ <b>1 ਵੋਟ</b> ਹੈ ਜੋ ਤੁਸੀਂ ਕਿਸੇ ਇੱਕ ਵਿਕਲਪ ਨੂੰ ਸੌਂਪ ਸਕਦੇ ਹੋ।", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "<b>{endDate}ਨੂੰ ਵੋਟਿੰਗ ਬੰਦ ਹੋਈ।</b> ਭਾਗੀਦਾਰ <b>ਜਿੰਨੇ ਮਰਜ਼ੀ ਵਿਕਲਪਾਂ ਲਈ ਵੋਟ ਪਾ ਸਕਦੇ ਹਨ।</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "ਤੁਸੀਂ ਜਿੰਨੇ ਮਰਜ਼ੀ ਵਿਕਲਪਾਂ ਲਈ ਵੋਟ ਕਰ ਸਕਦੇ ਹੋ।", "app.utils.votingMethodUtils.submitYourBudget": "ਆਪਣਾ ਬਜਟ ਜਮ੍ਹਾਂ ਕਰੋ", "app.utils.votingMethodUtils.submittedBudgetCountText2": "ਵਿਅਕਤੀ ਨੇ ਆਪਣਾ ਬਜਟ ਔਨਲਾਈਨ ਜਮ੍ਹਾ ਕੀਤਾ", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "ਲੋਕਾਂ ਨੇ ਆਪਣੇ ਬਜਟ ਆਨਲਾਈਨ ਜਮ੍ਹਾ ਕਰਵਾਏ", "app.utils.votingMethodUtils.submittedVoteCountText2": "ਵਿਅਕਤੀ ਨੇ ਆਪਣੀ ਵੋਟ ਆਨਲਾਈਨ ਜਮ੍ਹਾ ਕਰਵਾਈ", "app.utils.votingMethodUtils.submittedVotesCountText2": "ਲੋਕਾਂ ਨੇ ਆਪਣੀਆਂ ਵੋਟਾਂ ਆਨਲਾਈਨ ਜਮ੍ਹਾਂ ਕਰਵਾਈਆਂ", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "ਵੋਟ ਸਪੁਰਦ ਕੀਤੀ ਗਈ 🎉", "app.utils.votingMethodUtils.votesCast": "ਵੋਟਾਂ ਪਾਈਆਂ", "app.utils.votingMethodUtils.votingClosed": "ਵੋਟਿੰਗ ਬੰਦ", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>ਤੁਹਾਡੀ ਵੋਟ</b> ਉਦੋਂ ਤੱਕ ਨਹੀਂ ਗਿਣੀ ਜਾਵੇਗੀ ਜਦੋਂ ਤੱਕ ਤੁਸੀਂ \"ਸਬਮਿਟ\" 'ਤੇ ਕਲਿੱਕ ਨਹੀਂ ਕਰਦੇ।", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b>ਵਧਾਈਆਂ, ਤੁਹਾਡੀ ਵੋਟ ਜਮ੍ਹਾਂ ਹੋ ਗਈ ਹੈ!</b> ਤੁਸੀਂ <b>{endDate}</b>ਤੋਂ ਪਹਿਲਾਂ ਆਪਣੀ ਜਮ੍ਹਾਂ ਰਕਮ ਦੀ ਜਾਂਚ ਜਾਂ ਸੋਧ ਕਰ ਸਕਦੇ ਹੋ।", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b>ਵਧਾਈਆਂ, ਤੁਹਾਡੀ ਵੋਟ ਜਮ੍ਹਾਂ ਹੋ ਗਈ ਹੈ!</b> ਤੁਸੀਂ ਕਿਸੇ ਵੀ ਸਮੇਂ ਹੇਠਾਂ ਆਪਣੀ ਜਮ੍ਹਾਂ ਰਕਮ ਦੀ ਜਾਂਚ ਜਾਂ ਸੋਧ ਕਰ ਸਕਦੇ ਹੋ।", "components.UI.IdeaSelect.noIdeaAvailable": "ਕੋਈ ਵਿਚਾਰ ਉਪਲਬਧ ਨਹੀਂ ਹਨ।", "components.UI.IdeaSelect.selectIdea": "ਵਿਚਾਰ ਚੁਣੋ", "containers.SiteMap.allProjects": "ਸਾਰੇ ਪ੍ਰੋਜੈਕਟ", "containers.SiteMap.customPageSection": "ਕਸਟਮ ਪੰਨੇ", "containers.SiteMap.folderInfo": "ਹੋਰ ਜਾਣਕਾਰੀ", "containers.SiteMap.headSiteMapTitle": "ਸਾਈਟ ਦਾ ਨਕਸ਼ਾ | {orgName}", "containers.SiteMap.homeSection": "ਜਨਰਲ", "containers.SiteMap.pageContents": "ਪੰਨਾ ਸਮੱਗਰੀ", "containers.SiteMap.profilePage": "ਤੁਹਾਡਾ ਪ੍ਰੋਫਾਈਲ ਪੰਨਾ", "containers.SiteMap.profileSettings": "ਤੁਹਾਡੀਆਂ ਸੈਟਿੰਗਾਂ", "containers.SiteMap.projectEvents": "ਸਮਾਗਮ", "containers.SiteMap.projectIdeas": "ਵਿਚਾਰ", "containers.SiteMap.projectInfo": "ਜਾਣਕਾਰੀ", "containers.SiteMap.projectPoll": "ਪੋਲ", "containers.SiteMap.projectSurvey": "ਸਰਵੇਖਣ", "containers.SiteMap.projectsArchived": "ਆਰਕਾਈਵ ਕੀਤੇ ਪ੍ਰੋਜੈਕਟ", "containers.SiteMap.projectsCurrent": "ਮੌਜੂਦਾ ਪ੍ਰੋਜੈਕਟ", "containers.SiteMap.projectsDraft": "ਡਰਾਫਟ ਪ੍ਰੋਜੈਕਟ", "containers.SiteMap.projectsSection": "{orgName}ਦੇ ਪ੍ਰੋਜੈਕਟ", "containers.SiteMap.signInPage": "ਸਾਈਨ - ਇਨ", "containers.SiteMap.signUpPage": "ਸਾਇਨ ਅਪ", "containers.SiteMap.siteMapDescription": "ਇਸ ਪੰਨੇ ਤੋਂ, ਤੁਸੀਂ ਪਲੇਟਫਾਰਮ 'ਤੇ ਕਿਸੇ ਵੀ ਸਮੱਗਰੀ 'ਤੇ ਨੈਵੀਗੇਟ ਕਰ ਸਕਦੇ ਹੋ।", "containers.SiteMap.siteMapTitle": "{orgName}ਦੇ ਭਾਗੀਦਾਰੀ ਪਲੇਟਫਾਰਮ ਦਾ ਸਾਈਟ ਮੈਪ", "containers.SiteMap.successStories": "ਸਫ਼ਲਤਾ ਦੀਆਂ ਕਹਾਣੀਆਂ", "containers.SiteMap.timeline": "ਪ੍ਰੋਜੈਕਟ ਪੜਾਅ", "containers.SiteMap.userSpaceSection": "ਤੁਹਾਡਾ ਖਾਤਾ", "containers.SubscriptionEndedPage.accessDenied": "ਤੁਹਾਡੇ ਕੋਲ ਹੁਣ ਪਹੁੰਚ ਨਹੀਂ ਹੈ", "containers.SubscriptionEndedPage.subscriptionEnded": "ਇਹ ਪੰਨਾ ਸਿਰਫ਼ ਸਰਗਰਮ ਗਾਹਕੀ ਵਾਲੇ ਪਲੇਟਫਾਰਮਾਂ ਲਈ ਹੀ ਪਹੁੰਚਯੋਗ ਹੈ।"}
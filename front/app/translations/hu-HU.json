{"EmailSettingsPage.emailSettings": "E-mail beállítások", "EmailSettingsPage.initialUnsubscribeError": "Hiba történt a kampányról való leiratkozáskor. Kérjük, próbá<PERSON>ja <PERSON>.", "EmailSettingsPage.initialUnsubscribeLoading": "Kérésének feldolgozása folyamatban van, kérjük, várjon...", "EmailSettingsPage.initialUnsubscribeSuccess": "<PERSON><PERSON><PERSON><PERSON> le<PERSON> a {campaignTitle}csatornáról.", "UI.FormComponents.optional": "választható", "app.closeIconButton.a11y_buttonActionMessage": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Areas.areaUpdateError": "Hiba történt a terület mentése közben. Kérjük, próbá<PERSON>ja <PERSON>.", "app.components.Areas.followedArea": "A követett terület: {areaTitle}", "app.components.Areas.followedTopic": "<PERSON><PERSON><PERSON><PERSON> téma: {topicTitle}", "app.components.Areas.topicUpdateError": "Hiba történt a téma mentése közben. Kérjük, prób<PERSON><PERSON><PERSON>.", "app.components.Areas.unfollowedArea": "<PERSON><PERSON> terület: {areaTitle}", "app.components.Areas.unfollowedTopic": "<PERSON><PERSON> té<PERSON>: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Ár:", "app.components.AssignBudgetControl.add": "Hozzáadás", "app.components.AssignBudgetControl.added": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.addVote": "Szavazat hozzáadása", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Kiosztottad az összes kreditedet.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Elosztottad a maximális számú kreditet ehhez az opcióhoz.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Elosztottad az összes pontodat.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Elosztottad a maximális pontszámot erre a lehetőségre.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Kiosztottad az összes tokenedet.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Elosztottad a maximális számú tokent ehhez az opcióhoz.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Elosztottad az összes szavazatodat.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Elosztottad a maximális s<PERSON>ámú szavazatot erre a lehetőségre.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(1 offline is)} other {(# offline is)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Szavazás nem lehetséges, mivel ez a szakasz nem aktív.", "app.components.AssignMultipleVotesControl.removeVote": "Szavazat eltávolítása", "app.components.AssignMultipleVotesControl.select": "Válassza ki", "app.components.AssignMultipleVotesControl.votesSubmitted1": "<PERSON><PERSON><PERSON> leadtad a szavazatodat. A módosításhoz kattints a „Beküldött szavazat módosítása” gombra.", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "<PERSON><PERSON><PERSON> leadtad a szavazatodat. A módosításhoz menj vissza a projekt oldalára, és kattints a „Beküldés módosítása” gombra.", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {kredit} other {kredit}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {pont} other {pont}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokenek}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {szavazat} other {szavazat}}", "app.components.AssignVoteControl.maxVotesReached1": "Elosztottad az összes szavazatodat.", "app.components.AssignVoteControl.phaseNotActive": "Szavazás nem lehetséges, mivel ez a szakasz nem aktív.", "app.components.AssignVoteControl.select": "Válassza ki", "app.components.AssignVoteControl.selected2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.voteForAtLeastOne": "Szavazz legalább 1 lehetőségre", "app.components.AssignVoteControl.votesSubmitted1": "<PERSON><PERSON><PERSON> leadtad a szavazatodat. A módosításhoz kattints a „Beküldött szavazat módosítása” gombra.", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "<PERSON><PERSON><PERSON> leadtad a szavazatodat. A módosításhoz menj vissza a projekt oldalára, és kattints a „Beküldés módosítása” gombra.", "app.components.AuthProviders.continue": "Folytatás", "app.components.AuthProviders.continueWithAzure": "Folytassa a következővel: {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Folytatás a Facebookon", "app.components.AuthProviders.continueWithFakeSSO": "Folytassa a Fake SSO-val", "app.components.AuthProviders.continueWithGoogle": "Folytassa a Google-lal", "app.components.AuthProviders.continueWithHoplr": "Folytassa a <PERSON>lr-rel", "app.components.AuthProviders.continueWithIdAustria": "Folytassa az ID Austria-val", "app.components.AuthProviders.continueWithLoginMechanism": "Folytassa a következővel: {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Folytassa a MitID-vel", "app.components.AuthProviders.franceConnectMergingFailed": "<PERSON><PERSON>r l<PERSON>te<PERSON>k fiók ezzel az e-mail címmel.{br}{br}Nem férhet hozzá a platformhoz a FranceConnect használatával, mivel a személyes adatok nem egyeznek. A FranceConnect használatával történő bejelentkezéshez először meg kell változtatnia keresztnevét vagy vezetéknevét ezen a platformon, hogy megfeleljen hivatalos adatainak.{br}{br}Az alábbiakban a szokásos módon bejelentkezhet.", "app.components.AuthProviders.goToLogIn": "<PERSON><PERSON><PERSON>? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "<PERSON><PERSON><PERSON>? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Jelentkezzen be", "app.components.AuthProviders.logInWithEmail": "Jelentkezzen be e-mail címmel", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Az igazoláshoz legalább a megadott alsó korhatárt elértnek kell lennie.", "app.components.AuthProviders.signUp2": "Regisztr<PERSON><PERSON><PERSON>", "app.components.AuthProviders.signUpButtonAltText": "Regisztráljon a {loginMechanismName}számmal", "app.components.AuthProviders.signUpWithEmail": "Regisztráljon e-mailben", "app.components.AuthProviders.verificationRequired": "Ellenőrzés szükséges", "app.components.Author.a11yPostedBy": "Írta:", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 résztvevő} other {{numberOfParticipants} résztvevő}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} felhasználók", "app.components.AvatarBubbles.participant": "résztvevő", "app.components.AvatarBubbles.participants1": "résztvevők", "app.components.Comments.cancel": "M<PERSON>gs<PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "Hozzászólás a jelenlegi szakaszban nem lehetséges.", "app.components.Comments.commentingDisabledInactiveProject": "Hozzászólás nem lehetséges, mert ez a projekt jelenleg nem aktív.", "app.components.Comments.commentingDisabledProject": "A kommentelés ebben a projektben jele<PERSON>leg <PERSON> tiltva.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} megjegyzéshez.", "app.components.Comments.commentingMaybeNotPermitted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {signInLink} , ho<PERSON>, mi<PERSON>en m<PERSON>ket lehet tenni.", "app.components.Comments.inputsAssociatedWithProfile": "Alapértelmezés szerint beküldései a profilodhoz lesznek társítva, hacsak nem választod ezt a lehetőséget.", "app.components.Comments.invisibleTitleComments": "Megjegyzések", "app.components.Comments.leastRecent": "A legfrissebb", "app.components.Comments.likeComment": "Tetszik ez a komment", "app.components.Comments.mostLiked": "A legtöbb reakció", "app.components.Comments.mostRecent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.official": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.postAnonymously": "Anonim k<PERSON>", "app.components.Comments.replyToComment": "Válasz a megjegyzésre", "app.components.Comments.reportAsSpam": "Jelent<PERSON> spam<PERSON>", "app.components.Comments.seeOriginal": "Lásd az erede<PERSON>t", "app.components.Comments.seeTranslation": "Lásd a fordítást", "app.components.Comments.yourComment": "Az Ön megjegyzése", "app.components.CommonGroundResults.divisiveDescription": "<PERSON><PERSON>, amelyekben az emberek egyformán egyetértenek és egyet sem értenek:", "app.components.CommonGroundResults.divisiveTitle": "Megosztó", "app.components.CommonGroundResults.majorityDescription": "A megkérdezettek több mint 60%-a szavazott így vagy úgy a következő kérdésekben:", "app.components.CommonGroundResults.majorityTitle": "Többség", "app.components.CommonGroundResults.participantLabel": "résztvevő", "app.components.CommonGroundResults.participantsLabel1": "résztvevők", "app.components.CommonGroundResults.statementLabel": "nyilatkozat", "app.components.CommonGroundResults.statementsLabel1": "állí<PERSON>ások", "app.components.CommonGroundResults.votesLabe": "szavaz<PERSON>", "app.components.CommonGroundResults.votesLabel1": "szavazatok", "app.components.CommonGroundStatements.agreeLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundStatements.disagreeLabel": "<PERSON><PERSON>rt egyet", "app.components.CommonGroundStatements.noMoreStatements": "<PERSON><PERSON><PERSON> ninc<PERSON>ek reagálásra váró kijelentések", "app.components.CommonGroundStatements.noResults": "Még nincsenek eredmények. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON>ződjön meg róla, hogy részt vett a Közös Talaj <PERSON>, és nézzen vissza k<PERSON>bb.", "app.components.CommonGroundStatements.unsureLabel": "Bizonytalan", "app.components.CommonGroundTabs.resultsTabLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundTabs.statementsTabLabel": "Nyilatkozatok", "app.components.CommunityMonitorModal.formError": "<PERSON><PERSON>.", "app.components.CommunityMonitorModal.surveyDescription2": "Ez a folyamatban lévő felmérés nyomon követ<PERSON>, hogyan vélekedik a kormányzásról és a közszolgáltatásokról.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {<1 percet vesz igénybe} one {1 percet vesz igénybe} other {# percet vesz igénybe}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "A megerősítő kódot tartalmazó e-mailt elküldtük a {userEmail}címre.", "app.components.ConfirmationModal.changeYourEmail": "Módosítsa az e-mail címét.", "app.components.ConfirmationModal.codeInput": "<PERSON><PERSON><PERSON>", "app.components.ConfirmationModal.confirmationCodeSent": "<PERSON><PERSON> k<PERSON>", "app.components.ConfirmationModal.didntGetAnEmail": "<PERSON>em kapott e-mailt?", "app.components.ConfirmationModal.foundYourCode": "Megtaláltad a kódodat?", "app.components.ConfirmationModal.goBack": "<PERSON><PERSON>.", "app.components.ConfirmationModal.sendEmailWithCode": "E-mail küldése kóddal", "app.components.ConfirmationModal.sendNewCode": "<PERSON>j kód <PERSON>.", "app.components.ConfirmationModal.verifyAndContinue": "Ellenőrzés és folytatás", "app.components.ConfirmationModal.wrongEmail": "<PERSON>z email cím?", "app.components.ConsentManager.Banner.accept": "Elfogadás", "app.components.ConsentManager.Banner.ariaButtonClose2": "Az irányelv elutasítása és a szalaghirdetés bezárása", "app.components.ConsentManager.Banner.close": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.mainText": "Ez a platform cookie-kat használ a {policyLink}szerint.", "app.components.ConsentManager.Banner.manage": "Kezelése", "app.components.ConsentManager.Banner.policyLink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Ezt arra has<PERSON>, ho<PERSON> sze<PERSON><PERSON>re szabjuk és mérjük weboldalunk reklámkampányainak hatékonyságát. Ezen a platformon nem jelenítünk meg hirdetéseket, de a következő szolgáltatások személyre szabott hirdetést kínálhatnak Önnek az oldalunkon meglátogatott oldalak alapján.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "En<PERSON><PERSON><PERSON><PERSON>ze", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Ezt a nyomkövetést arra has<PERSON>l<PERSON>k, hogy <PERSON><PERSON> megé<PERSON>k, hogyan használ<PERSON> a platformot a navigáció elsajátítása és fejlesztése érdekében. Ezt az információt csak tömegelemzésben használják fel, se<PERSON><PERSON><PERSON> módon nem az egyes személyek nyomon követésére.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "M<PERSON>gs<PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funkcion<PERSON>lis", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Ez szükséges a webhely alapvető funkcióinak engedélyezéséhez és ellenőrzéséhez. Előfordulhat, hogy néhány itt felsorolt eszköz nem vonatkozik Önre. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, to<PERSON><PERSON><PERSON><PERSON> információkért olvassa el cookie-kra vonatko<PERSON> szabályzatunkat.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Címkekezelő ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "A működőképes platform érdekében elmentünk egy hitelesítő cookie-t, ha regisztr<PERSON>l, és azt a nyelvet, amelyen ezt a platformot használja.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Megtakarítás", "app.components.ConsentManager.Modal.PreferencesDialog.title": "<PERSON>z <PERSON> cookie-be<PERSON>ll<PERSON>t<PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Eszközök", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Tartalomfeltöltési felelősség kizárása", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "A tartalom feltöltésével kijelenti, hogy ez a tartalom nem sért semmilyen előírást vagy harmadik felek jogait, mint például a szellemi tulajdonjogokat, a személyiségi jogokat, az üzleti titkokhoz való jogokat stb. Ebből következően a tartalom feltöltésével teljes és kizárólagos felelősséget vállal a feltöltött tartalomból eredő minden közvetlen és közvetett kárért. Ezen túlmenően Ön vállalja, hogy kártalanítja a platform tulajdonosát és a Go Vocalt a harmadik felek harmadik felekkel szembeni követelései vagy kötelezettségei, valamint minden kapcsolódó költség tekintetében, amely az Ön által feltöltött tartalomból származna vagy abból fakadna.", "app.components.ContentUploadDisclaimer.onAccept": "értem én", "app.components.ContentUploadDisclaimer.onCancel": "M<PERSON>gs<PERSON>", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "<PERSON><PERSON>, mi<PERSON>rt", "app.components.CustomFieldsForm.addressInputAriaLabel": "Cím mega<PERSON>", "app.components.CustomFieldsForm.addressInputPlaceholder6": "<PERSON><PERSON> meg egy címet...", "app.components.CustomFieldsForm.adminFieldTooltip": "Csak az adminok láthatják a mezőt", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "A felmérésre adott összes válasz anonimizált.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Egy sokszöghöz legalább három pont szükséges.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "<PERSON><PERSON> két pont szükséges.", "app.components.CustomFieldsForm.attachmentRequired": "Legalább egy melléklet szükséges", "app.components.CustomFieldsForm.authorFieldLabel": "Szerző", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Kezdjen el gépelni a felhasználó e-mail címe vagy neve szerinti kereséshez...", "app.components.CustomFieldsForm.back": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.budgetFieldLabel": "Költségvetés", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Kattintson a térképre a rajzoláshoz. Ezután húzással mozgassa a pontokat.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "<PERSON><PERSON><PERSON><PERSON> a térk<PERSON><PERSON>, vagy <PERSON> be egy címet al<PERSON> a válasz hozzáadásához.", "app.components.CustomFieldsForm.confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.descriptionMinLength": "A leírásnak legalább {min} karakter hosszúnak kell lennie", "app.components.CustomFieldsForm.descriptionRequired": "A leírás kitöltése kötelező", "app.components.CustomFieldsForm.fieldMaximumItems": "Legfeljebb {maxSelections, plural, one {# opció} other {# opció}} választható ki a \"{fieldName}\" mezőhöz", "app.components.CustomFieldsForm.fieldMinimumItems": "Legalább {minSelections, plural, one {# opció} other {# opció}} választható ki a \"{fieldName}\" mezőhöz", "app.components.CustomFieldsForm.fieldRequired": "A(z) „{fieldName}” mező kitöltése kötelező", "app.components.CustomFieldsForm.fileSizeLimit": "A fájlméret korl<PERSON>tja {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "A kép megadása kötelező", "app.components.CustomFieldsForm.minimumCoordinates2": "Minimum {numPoints} térképpont szükséges.", "app.components.CustomFieldsForm.notPublic1": "*Ez a válasz csak a projektmenedzserekkel lesz megosztva, a nyilvánosság számára nem.", "app.components.CustomFieldsForm.otherArea": "Valahol máshol", "app.components.CustomFieldsForm.progressBarLabel": "Előrehalad", "app.components.CustomFieldsForm.removeAnswer": "Válasz eltávolítása", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Válassz ki annyit, amennyit szeretnél", "app.components.CustomFieldsForm.selectBetween": "*Válasszon a {minItems} és a {maxItems} lehetőségek közül", "app.components.CustomFieldsForm.selectExactly2": "*Pontosan válasszon {selectExactly, plural, one {# opció} other {# opció}}", "app.components.CustomFieldsForm.selectMany": "*<PERSON><PERSON><PERSON><PERSON> annyit, amennyit szeretnél", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Koppintson a térképre a rajzoláshoz. Ezután húzással helyezze át a pontokat.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "<PERSON><PERSON><PERSON><PERSON> a térképre a rajzoláshoz.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "<PERSON><PERSON><PERSON>son a térképre a válasz hozzáadásához.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "<PERSON><PERSON><PERSON><PERSON> a térk<PERSON><PERSON>, vagy <PERSON> be egy címet al<PERSON> a válasz hozzáadásához.", "app.components.CustomFieldsForm.tapToAddALine": "<PERSON><PERSON><PERSON><PERSON> egy sor hozzáadásához", "app.components.CustomFieldsForm.tapToAddAPoint": "<PERSON><PERSON><PERSON><PERSON> egy pont hozzáadásához", "app.components.CustomFieldsForm.tapToAddAnArea": "<PERSON><PERSON><PERSON><PERSON> egy terület hozzáadásához", "app.components.CustomFieldsForm.titleMaxLength": "A cím legfeljebb {max} karakter hossz<PERSON> lehet.", "app.components.CustomFieldsForm.titleMinLength": "A címnek legalább {min} karakter hosszúnak kell lennie", "app.components.CustomFieldsForm.titleRequired": "A cím megadása kötelező", "app.components.CustomFieldsForm.topicRequired": "Legalább egy címke megadása kötelező", "app.components.CustomFieldsForm.typeYourAnswer": "<PERSON>rd be a válaszod", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Kötelező beírni a választ", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Töltsön fel egy zip fájlt, amely egy vagy több shapefájlt tartalmaz.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "Ha a helyszín nem jelenik meg a lehetőségek között gépelés közben, érvényes koordinátákat adhat meg „szélesség, hosszúság” formátumban a pontos helyszín megadásához (pl.: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Néhány mező érvénytelen volt. K<PERSON>rj<PERSON>k, javítsa ki a hibákat, és próbálja újra.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Ismeretlen hiba történt a jelentés elküldése közben. Kérjük, prób<PERSON><PERSON><PERSON>.", "app.components.ErrorBoundary.errorFormLabelClose": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelComments": "Mi történt?", "app.components.ErrorBoundary.errorFormLabelEmail": "Email", "app.components.ErrorBoundary.errorFormLabelName": "Név", "app.components.ErrorBoundary.errorFormLabelSubmit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormSubtitle": "Csapatunk értesítést kapott.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON>, ho<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, mi történt az alábbiakban.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Visszajelzését elküldtük. Köszönöm!", "app.components.ErrorBoundary.errorFormTitle": "<PERSON><PERSON>, vala<PERSON> pro<PERSON>.", "app.components.ErrorBoundary.genericErrorWithForm": "<PERSON><PERSON><PERSON>, és nem tudjuk megjeleníteni ezt a tartalmat. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vagy {openForm}", "app.components.ErrorBoundary.openFormText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ErrorToast.budgetExceededError": "<PERSON><PERSON><PERSON> k<PERSON>égvetése", "app.components.ErrorToast.votesExceededError": "<PERSON><PERSON><PERSON>", "app.components.EventAttendanceButton.forwardToFriend": "Továbbítás egy <PERSON>", "app.components.EventAttendanceButton.maxRegistrationsReached": "Elértük az eseményre való regisztrációk maximális számát. <PERSON><PERSON><PERSON><PERSON> több he<PERSON>.", "app.components.EventAttendanceButton.register": "Nyilvántartás", "app.components.EventAttendanceButton.registered": "Bejegyzett", "app.components.EventAttendanceButton.seeYouThere": "Találkozunk ott!", "app.components.EventAttendanceButton.seeYouThereName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ott, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Kevesebb eseményinformáció vált láthatóvá.", "app.components.EventCard.a11y_moreContentVisible": "Az eseményről több információ is láthatóvá vált.", "app.components.EventCard.a11y_readMore": "<PERSON><PERSON><PERSON><PERSON><PERSON> a „{eventTitle}” eseményről.", "app.components.EventCard.endsAt": "<PERSON>rak<PERSON> é<PERSON> v<PERSON>get", "app.components.EventCard.readMore": "<PERSON><PERSON><PERSON><PERSON>", "app.components.EventCard.showLess": "<PERSON><PERSON><PERSON>", "app.components.EventCard.showMore": "<PERSON><PERSON><PERSON>", "app.components.EventCard.startsAt": "Kezdés:", "app.components.EventPreviews.eventPreviewContinuousTitle2": "A projekt közelgő és folyamatban lévő eseményei", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Közelgő és folyamatban lévő események ebben a fázisban", "app.components.FileUploader.a11y_file": "Fájl:", "app.components.FileUploader.a11y_filesToBeUploaded": "Feltöltendő fájlok: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Nincsenek hozzáadva fájlok.", "app.components.FileUploader.a11y_removeFile": "Távolítsa el ezt a fájlt", "app.components.FileUploader.fileInputDescription": "<PERSON><PERSON><PERSON><PERSON> egy fájl kiválasztásához", "app.components.FileUploader.fileUploadLabel": "Mellékletek (max. 50 MB)", "app.components.FileUploader.file_too_large2": "A {maxSizeMb}MB-nál nagyobb fájlok nem engedélyezettek.", "app.components.FileUploader.incorrect_extension": "A {fileName} a rendszerünk nem tá<PERSON>gat<PERSON>, ez<PERSON>rt nem kerül feltöltésre.", "app.components.FilterBoxes.a11y_allFilterSelected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>szű<PERSON>: mind", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# beküldés} other {# beküldés}}", "app.components.FilterBoxes.a11y_removeFilter": "Távolítsa el a szűrőt", "app.components.FilterBoxes.a11y_selectedFilter": "Kiv<PERSON><PERSON><PERSON><PERSON>tt állapotszűrő: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "A kiválasztott {numberOfSelectedTopics, plural, =0 {nulla cí<PERSON>ű<PERSON>} one {egy cí<PERSON>} other {# cí<PERSON><PERSON>}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Minden", "app.components.FilterBoxes.areas": "Szűrés terület szerint", "app.components.FilterBoxes.inputs": "bemenetek", "app.components.FilterBoxes.noValuesFound": "Nincsenek elérhető értékek.", "app.components.FilterBoxes.showLess": "<PERSON><PERSON><PERSON>", "app.components.FilterBoxes.showTagsWithNumber": "Az összes megjelenítése ({numberTags})", "app.components.FilterBoxes.statusTitle": "<PERSON><PERSON><PERSON>", "app.components.FilterBoxes.topicsTitle": "Címkék", "app.components.FiltersModal.filters": "Szűrők", "app.components.FolderFolderCard.a11y_folderDescription": "Mappa leírása:", "app.components.FolderFolderCard.a11y_folderTitle": "Mappa címe:", "app.components.FolderFolderCard.archived": "Archivált", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projekt} one {# projekt} other {# projekt}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "A mező típusa nem módosítható, ha már vannak beküldések.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "<PERSON><PERSON><PERSON> be", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "<PERSON><PERSON><PERSON><PERSON>", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Az űrlapszerkesztő megnyitásakor az automatikus mentés alapértelmezés szerint engedélyezve van. Amikor az \"X\" gombbal bezárja a mezőbeállítások panelt, az automatikusan mentést indít el.", "app.components.GanttChart.timeRange.month": "Hónap", "app.components.GanttChart.timeRange.quarter": "<PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.timeRangeMultiyear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON>", "app.components.GanttChart.today": "Ma", "app.components.GoBackButton.group.edit.goBack": "<PERSON><PERSON>", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Vissza az előző oldalra", "app.components.HookForm.Feedback.errorTitle": "<PERSON> egy probléma", "app.components.HookForm.Feedback.submissionError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. Ha a probléma to<PERSON> is fen<PERSON><PERSON>, lé<PERSON><PERSON><PERSON> ka<PERSON> velünk", "app.components.HookForm.Feedback.submissionErrorTitle": "Probléma volt a mi oldalunkon, eln<PERSON><PERSON>ést", "app.components.HookForm.Feedback.successMessage": "Az űrlap sikeresen elküldve", "app.components.HookForm.PasswordInput.passwordLabel": "Je<PERSON><PERSON><PERSON>", "app.components.HorizontalScroll.scrollLeftLabel": "<PERSON><PERSON><PERSON><PERSON> balra.", "app.components.HorizontalScroll.scrollRightLabel": "<PERSON><PERSON><PERSON><PERSON> jobbra.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} ötletek betöltve.", "app.components.IdeaCards.filters": "Szűrők", "app.components.IdeaCards.filters.mostDiscussed": "A legtöbbet megvitatták", "app.components.IdeaCards.filters.newest": "<PERSON><PERSON>", "app.components.IdeaCards.filters.oldest": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.popular": "Leginkább tetszett", "app.components.IdeaCards.filters.random": "Véletlen", "app.components.IdeaCards.filters.sortBy": "Rendez<PERSON>", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "A rendezés a következőre módosult: {currentSortType}", "app.components.IdeaCards.filters.trending": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.showMore": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.a11y_hideIdeaCard": "Ötletkártya elrejtése.", "app.components.IdeasMap.a11y_mapTitle": "<PERSON><PERSON>rk<PERSON><PERSON>", "app.components.IdeasMap.clickOnMapToAdd": "Kattintson a térképre a bevitel hozzáadásához", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Adminisztrátorként rák<PERSON>inthat a térképre a bevitel hozzáadásához, még akkor is, ha ez a fázis nem aktív.", "app.components.IdeasMap.filters": "Szűrők", "app.components.IdeasMap.multipleInputsAtLocation": "<PERSON><PERSON><PERSON> bemenet ezen a helyen", "app.components.IdeasMap.noFilteredResults": "A kiválasztott szűrők nem adtak eredményt", "app.components.IdeasMap.noResults": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.or": "vagy", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, nincs ellenszenv.} one {1 nem tetszik.} other {, # nem tetszik.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, nincs lájk.} one {, 1 lájk.} other {, # like.}}", "app.components.IdeasMap.signInLinkText": "jelentkezz be", "app.components.IdeasMap.signUpLinkText": "iratkozz fel", "app.components.IdeasMap.submitIdea2": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.tapOnMapToAdd": "Érintse meg a térképet a bevitel hozzáadásához", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Adminisztrátorként megérintheti a térképet a bevitel hozzáadásához, még akkor is, ha ez a fázis nem aktív.", "app.components.IdeasMap.userInputs2": "Bemenetek a résztvevőktől", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, nincs <PERSON>} one {, 1 megjegyzés} other {, # megjegyzés}}", "app.components.IdeasShow.bodyTitle": "Le<PERSON><PERSON><PERSON>", "app.components.IdeasShow.deletePost": "T<PERSON>r<PERSON><PERSON>", "app.components.IdeasShow.editPost": "Szerkesztés", "app.components.IdeasShow.goBack": "<PERSON><PERSON>", "app.components.IdeasShow.moreOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.or": "vagy", "app.components.IdeasShow.proposedBudgetTitle": "Javasolt költségvetés", "app.components.IdeasShow.reportAsSpam": "Jelent<PERSON> spam<PERSON>", "app.components.IdeasShow.send": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.skipSharing": "Ha<PERSON><PERSON> ki, k<PERSON><PERSON>bb megcsinálom", "app.components.IdeasShowPage.signIn2": "Jelentkezzen be", "app.components.IdeasShowPage.sorryNoAccess": "Sajnos nem férhet hozzá ehhez az oldalhoz. Előfordulhat, hogy a hozzáféréshez be kell jelentkeznie vagy regisztrálnia kell.", "app.components.LocationInput.noOptions": "<PERSON><PERSON><PERSON>", "app.components.Modal.closeWindow": "Z<PERSON><PERSON>ja be az ablakot", "app.components.MultiSelect.clearButtonAction": "Kijelölés törlése", "app.components.MultiSelect.clearSearchButtonAction": "Keresés törlése", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Adjon meg egy új e-mail címet", "app.components.PageNotFound.goBackToHomePage": "Vissza a kezdőlapra", "app.components.PageNotFound.notFoundTitle": "<PERSON>z oldal nem található", "app.components.PageNotFound.pageNotFoundDescription": "A keresett oldal nem található.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Biztosítson tartalmat legalább egy nyelven", "app.components.PagesForm.editContent": "Tartalom", "app.components.PagesForm.fileUploadLabel": "Mellékletek (max. 50 MB)", "app.components.PagesForm.fileUploadLabelTooltip": "A fájlok nem lehetnek nagyobbak 50 Mb-nál. A hozzáadott fájlok az oldal alján jelennek meg.", "app.components.PagesForm.navbarItemTitle": "Név a navigációs sávban", "app.components.PagesForm.pageTitle": "Cím", "app.components.PagesForm.savePage": "<PERSON><PERSON> mentése", "app.components.PagesForm.saveSuccess": "Az oldal sikeresen mentve.", "app.components.PagesForm.titleMissingOneLanguageError": "<PERSON>jon címet <PERSON>bb egy nyelvhez", "app.components.Pagination.back": "Előző oldal", "app.components.Pagination.next": "Következő oldal", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "{votesCast}<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ami meghala<PERSON>ja a {votesLimit}korlátot. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vegyen ki néhány terméket a kosárból, és próbálja ú<PERSON>.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} balra", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Legalább {votesMinimum} -t k<PERSON>, mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Legalább egy lehetőséget ki kell választania a beküldés előtt.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "<PERSON><PERSON><PERSON><PERSON>, hozzá kell adnia valamit a kosarához.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {<PERSON><PERSON><PERSON><PERSON> kreditek} other {# / {totalNumberOfVotes, plural, one {1 kredit} other {# kreditek}} van <PERSON>}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {<PERSON><PERSON>senek pontok hátra} other {# / {totalNumberOfVotes, plural, one {1 pont} other {# pont}} van h<PERSON>}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {<PERSON><PERSON><PERSON><PERSON>} other {# a {totalNumberOfVotes, plural, one {-ból 1 zseton} other {# zsetonok}} van}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {<PERSON><PERSON><PERSON><PERSON> szavazatok} other {# / {totalNumberOfVotes, plural, one {1 szavazat} other {# szavazatok}} van}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# szavazat} one {# szavazat} other {# szavazat}} leadott", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "{votesCast} szava<PERSON><PERSON><PERSON> adott le, ami meghaladja a {votesLimit}korlátot. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tá<PERSON>l<PERSON>tson el néhány s<PERSON>t, és próbálja ú<PERSON>.", "app.components.ParticipationCTABars.addInput": "Bevitel hozzáadása", "app.components.ParticipationCTABars.allocateBudget": "Ossza ki a költségvetését", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Költségkerete sikeresen elküldve.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Részvétel<PERSON> n<PERSON>", "app.components.ParticipationCTABars.poll": "Vegye ki a szavazást", "app.components.ParticipationCTABars.reviewDocument": "Tekintse át a dokumentumot", "app.components.ParticipationCTABars.seeContributions": "Lásd a hozzájárulásokat", "app.components.ParticipationCTABars.seeEvents3": "Lásd az eseményeket", "app.components.ParticipationCTABars.seeIdeas": "Lásd ötleteket", "app.components.ParticipationCTABars.seeInitiatives": "Lásd a kezdeményezéseket", "app.components.ParticipationCTABars.seeIssues": "Lásd a problémákat", "app.components.ParticipationCTABars.seeOptions": "Lásd a lehetőségeket", "app.components.ParticipationCTABars.seePetitions": "Lásd a petíciókat", "app.components.ParticipationCTABars.seeProjects": "Lásd a projekteket", "app.components.ParticipationCTABars.seeProposals": "Lásd a javaslatokat", "app.components.ParticipationCTABars.seeQuestions": "Lásd a kérdéseket", "app.components.ParticipationCTABars.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.takeTheSurvey": "Vegye ki a felmérést", "app.components.ParticipationCTABars.userHasParticipated": "<PERSON>n r<PERSON>t vett ebben a projektben.", "app.components.ParticipationCTABars.viewInputs": "Bemenetek megtekintése", "app.components.ParticipationCTABars.volunteer": "Ö<PERSON>én<PERSON>", "app.components.ParticipationCTABars.votesCounter.vote": "szavaz<PERSON>", "app.components.ParticipationCTABars.votesCounter.votes": "szavazatokat", "app.components.PasswordInput.a11y_passwordHidden": "Jelszó rejtve", "app.components.PasswordInput.a11y_passwordVisible": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "app.components.PasswordInput.a11y_strength1Password": "Gyenge jelszóerősség", "app.components.PasswordInput.a11y_strength2Password": "Gyenge jelszóerősség", "app.components.PasswordInput.a11y_strength3Password": "Közepes jelszóerősség", "app.components.PasswordInput.a11y_strength4Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength5Password": "Nagyon er<PERSON>", "app.components.PasswordInput.hidePassword": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON> (min. {minimumPasswordLength} karakter)", "app.components.PasswordInput.minimumPasswordLengthError": "<PERSON>jon meg egy legalább {minimumPasswordLength} karakter hoss<PERSON> j<PERSON>", "app.components.PasswordInput.passwordEmptyError": "<PERSON><PERSON> meg j<PERSON>", "app.components.PasswordInput.passwordStrengthTooltip1": "A jelszó erősebbé tételéhez:", "app.components.PasswordInput.passwordStrengthTooltip2": "Használjon nem egymást követő kisbetűk, nagybetűk, s<PERSON><PERSON><PERSON><PERSON>gyek, speci<PERSON><PERSON> karak<PERSON> és írásjelek kombinációját", "app.components.PasswordInput.passwordStrengthTooltip3": "Kerülje a gyakori vagy könnyen kitalálható sza<PERSON>kat", "app.components.PasswordInput.passwordStrengthTooltip4": "Növelje a hosszt", "app.components.PasswordInput.showPassword": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>", "app.components.PasswordInput.strength1Password": "Szegény", "app.components.PasswordInput.strength2Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength3Password": "Közepes", "app.components.PasswordInput.strength4Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength5Password": "<PERSON>gy<PERSON> er<PERSON>", "app.components.PostCardsComponents.list": "Lista", "app.components.PostCardsComponents.map": "Térkép", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "<PERSON>jon hozzá egy hivatalos frissí<PERSON>", "app.components.PostComponents.OfficialFeedback.cancel": "M<PERSON>gs<PERSON>", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "T<PERSON>r<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Biztosan törli ezt a hivatalos frissítést?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Szerkesztés", "app.components.PostComponents.OfficialFeedback.lastEdition": "Utoljára szerkesztve: {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "<PERSON><PERSON><PERSON><PERSON> fris<PERSON>: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Válassza ki, hogyan lássák az emberek a nevét", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Hivatalos frissí<PERSON>s szerzőjének neve", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Hivatalos frissítés törzsszövege", "app.components.PostComponents.OfficialFeedback.officialUpdates": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.postedOn": "Közzétéve: {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Közzététel", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "<PERSON><PERSON><PERSON><PERSON>i frissítések megjelenítése", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "<PERSON>j f<PERSON>...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hiba t<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Üzenet frissítése", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Frissítését si<PERSON>esen közzétettük!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Támogassa hozzájárulásomat '{postTitle}' {postUrl}címen!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Támogassa hozzájárulásomat: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Támogassa hozzájárulásomat: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Támogassa az ötletemet '{postTitle}' a {postUrl}helyen!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Támogassa az ötletemet: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Támogassa az ötletemet: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Mi a véleménye erről a javaslatról? Szavazz rá, és oszd meg a beszélgetést a {postUrl} címen, hogy hallasd a hangod!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Támogassa javaslatomat: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Támogasd kezdeményezésemet: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "'{postTitle}' megjegyzést tettem közzé a {postUrl}címen!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Most tettem közzé egy megjegyzést: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Most tettem közzé egy megjegyzést: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Tá<PERSON>gassa a javasolt '{postTitle}' opciót a {postUrl}helyen!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Támogassa a javasolt opciómat: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Támogassa az én opciómat: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Támogassa a '{postTitle}' petíciómat a {postUrl}címen!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Támogassa petíciómat: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Támogassa petíciómat: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Támogassa a '{postTitle}' projektemet a {postUrl}címen!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Támogassa a projektemet: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Támogassa a projektemet: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Tá<PERSON>gas<PERSON> a '{postTitle}' javaslatomat a {postUrl}helyen!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Támogassa javaslatomat: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Most tettem közzé egy ajánlatot a {orgName}számára: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Csatlakozzon a '{postTitle}' kérdésről szóló vitához a {postUrl}címen!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Csatlakozz a beszélgetéshez: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Csatlakozz a beszélgetéshez: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Szavazz a {postTitle} -ra", "app.components.PostComponents.linkToHomePage": "Link a kezdőlapra", "app.components.PostComponents.readMore": "Bővebben...", "app.components.PostComponents.topics": "Témák", "app.components.ProjectArchivedIndicator.archivedProject": "<PERSON><PERSON><PERSON> már nem vehet részt ebben a projektben, mert archiv<PERSON><PERSON><PERSON> lett", "app.components.ProjectArchivedIndicator.previewProject": "Projekttervezet:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Csak a moderátorok és az előnézeti linkkel rendelkezők láthatják.", "app.components.ProjectCard.a11y_projectDescription": "Projekt leírása:", "app.components.ProjectCard.a11y_projectTitle": "Projekt címe:", "app.components.ProjectCard.addYourOption": "Adja hozzá a lehetőséget", "app.components.ProjectCard.allocateYourBudget": "Ossza ki a költségvetését", "app.components.ProjectCard.archived": "Archivált", "app.components.ProjectCard.comment": "Megjegyzés", "app.components.ProjectCard.contributeYourInput": "Hozzájáruljon a hozzájárulásához", "app.components.ProjectCard.finished": "Befejezett", "app.components.ProjectCard.joinDiscussion": "Csatlakozzon a vitához", "app.components.ProjectCard.learnMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.reaction": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.readTheReport": "Olvassa el a jelentést", "app.components.ProjectCard.reviewDocument": "Tekintse át a dokumentumot", "app.components.ProjectCard.submitAnIssue": "Hozzászólás küldése", "app.components.ProjectCard.submitYourIdea": "<PERSON><PERSON><PERSON><PERSON> be <PERSON>tletét", "app.components.ProjectCard.submitYourInitiative": "Nyújtsa be kezdeményezését", "app.components.ProjectCard.submitYourPetition": "Nyújtsa be petícióját", "app.components.ProjectCard.submitYourProject": "<PERSON><PERSON><PERSON><PERSON> be projektjét", "app.components.ProjectCard.submitYourProposal": "Nyújtsa be a<PERSON>", "app.components.ProjectCard.takeThePoll": "Vegye ki a szavazást", "app.components.ProjectCard.takeTheSurvey": "Vegye ki a felmérést", "app.components.ProjectCard.viewTheContributions": "Tekintse meg a hozzájárulásokat", "app.components.ProjectCard.viewTheIdeas": "Tekintse meg az ötleteket", "app.components.ProjectCard.viewTheInitiatives": "Tekintse meg a kezdeményezéseket", "app.components.ProjectCard.viewTheIssues": "Tekintse meg a megjegyzéseket", "app.components.ProjectCard.viewTheOptions": "Tekintse meg a lehetőségeket", "app.components.ProjectCard.viewThePetitions": "Tekintse meg a petíciókat", "app.components.ProjectCard.viewTheProjects": "Tekintse meg a projekteket", "app.components.ProjectCard.viewTheProposals": "Tekintse meg az ajánlatokat", "app.components.ProjectCard.viewTheQuestions": "Tekintse meg a kérdéseket", "app.components.ProjectCard.vote": "Szavazás", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# megjegyzés} other {# megjegyzés}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# hozzáj<PERSON><PERSON><PERSON>} other {# hozzáj<PERSON>rul<PERSON>}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {még nincsenek ötletek} one {# ötlet} other {# ötletek}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# kezde<PERSON>nyezések} one {# kezdeményez<PERSON>} other {# kezdeményez<PERSON>ek}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# megjegyzés} other {# megjegyzés}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# opció} other {# opció}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petíció} one {# petíció} other {# petíció}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# projekt} other {# projekt}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# javaslat} one {# javaslat} other {# javaslat}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# kérdés} other {# kérdés}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# megjegyzés} one {# megjegyzés} other {# megjegyzés}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# bemenet} one {# bemenet} other {# bemenet}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projekt} one {# projekt} other {# projekt}}", "app.components.ProjectFolderCards.components.Topbar.all": "Minden", "app.components.ProjectFolderCards.components.Topbar.archived": "Archivált", "app.components.ProjectFolderCards.components.Topbar.draft": "Piszkozat", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Szűrés s<PERSON>", "app.components.ProjectFolderCards.components.Topbar.published2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.noProjectYet": "<PERSON><PERSON>leg nincsenek nyitott projektek", "app.components.ProjectFolderCards.noProjectsAvailable": "<PERSON><PERSON><PERSON> elér<PERSON> projekt", "app.components.ProjectFolderCards.showMore": "<PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.stayTuned": "Térjen v<PERSON> az új elköteleződési lehetőségekért", "app.components.ProjectFolderCards.tryChangingFilters": "Próbálja megváltoztatni a kiválasztott szűrőket.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Ezekben a városokban is használatos:", "app.components.ProjectTemplatePreview.copied": "M<PERSON>ol<PERSON>", "app.components.ProjectTemplatePreview.copyLink": "<PERSON>", "app.components.QuillEditor.alignCenter": "Középső szöveg", "app.components.QuillEditor.alignLeft": "Balra igazítás", "app.components.QuillEditor.alignRight": "Igazítsa jobbra", "app.components.QuillEditor.bold": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.clean": "Távolítsa el a formázást", "app.components.QuillEditor.customLink": "Hozzá<PERSON><PERSON> gomb", "app.components.QuillEditor.customLinkPrompt": "<PERSON><PERSON><PERSON> be a linket:", "app.components.QuillEditor.edit": "Szerkesztés", "app.components.QuillEditor.image": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.imageAltPlaceholder": "A kép rö<PERSON>", "app.components.QuillEditor.italic": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.link": "<PERSON>", "app.components.QuillEditor.linkPrompt": "<PERSON><PERSON><PERSON> be a linket:", "app.components.QuillEditor.normalText": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.orderedList": "<PERSON><PERSON><PERSON> lista", "app.components.QuillEditor.remove": "Távolítsa el", "app.components.QuillEditor.save": "Megtakarítás", "app.components.QuillEditor.subtitle": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.title": "Cím", "app.components.QuillEditor.unorderedList": "Rendezetlen lista", "app.components.QuillEditor.video": "Videó hozzáadása lehetőségre", "app.components.QuillEditor.videoPrompt": "<PERSON><PERSON><PERSON>:", "app.components.QuillEditor.visitPrompt": "Látogassa meg a linket:", "app.components.ReactionControl.completeProfileToReact": "Töltse ki profilját a reagáláshoz", "app.components.ReactionControl.dislike": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ReactionControl.dislikingDisabledMaxReached": "Elérted a nemtetszések maximális számát {projectName}hónapban", "app.components.ReactionControl.like": "Mint", "app.components.ReactionControl.likingDisabledMaxReached": "{projectName}-ban elérted a maximális kedvelések számát", "app.components.ReactionControl.reactingDisabledFutureEnabled": "A reagálás engedélyezve lesz, amint ez a fázis elindul", "app.components.ReactionControl.reactingDisabledPhaseOver": "<PERSON><PERSON><PERSON> a fá<PERSON>ban már nem lehet reag<PERSON>lni", "app.components.ReactionControl.reactingDisabledProjectInactive": "<PERSON><PERSON><PERSON><PERSON> nem tud reagálni a {projectName}ötleteire", "app.components.ReactionControl.reactingNotEnabled": "A reagálás jelenleg nincs engedélyezve ennél a projektnél", "app.components.ReactionControl.reactingNotPermitted": "A reagálás csak bizonyos csoportok számára engedélyezett", "app.components.ReactionControl.reactingNotSignedIn": "Jelentkezzen be a reagáláshoz.", "app.components.ReactionControl.reactingPossibleLater": "A reagálás ekkor kezdődik: {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "A reagáláshoz igazolja személyazonosságát.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>: {startDate} {startTime} -tól {endDate} {endTime}-ig.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "<PERSON>se<PERSON><PERSON> d<PERSON>: {eventDate} {startTime} és {endTime}k<PERSON><PERSON><PERSON>tt.", "app.components.Sharing.linkCopied": "<PERSON>", "app.components.Sharing.or": "vagy", "app.components.Sharing.share": "Részesedés", "app.components.Sharing.shareByEmail": "Megosztás e-mailben", "app.components.Sharing.shareByLink": "<PERSON>", "app.components.Sharing.shareOnFacebook": "Oszd meg a Facebookon", "app.components.Sharing.shareOnTwitter": "Oszd meg a Twitteren", "app.components.Sharing.shareThisEvent": "Oszd meg ezt az eseményt", "app.components.Sharing.shareThisFolder": "Részesedés", "app.components.Sharing.shareThisProject": "Oszd meg ezt a projektet", "app.components.Sharing.shareViaMessenger": "Megosztás <PERSON> k<PERSON>", "app.components.Sharing.shareViaWhatsApp": "Oszd meg a WhatsApp-on keresztül", "app.components.SideModal.closeButtonAria": "<PERSON><PERSON><PERSON><PERSON>", "app.components.StatusModule.futurePhase": "<PERSON><PERSON> olyan <PERSON> n<PERSON>, amely még nem kezd<PERSON>dött el. Akkor tudsz majd részt venni, amikor a szakasz elkezdődik.", "app.components.StatusModule.modifyYourSubmission1": "Módosítsa a beküldött anyagot", "app.components.StatusModule.submittedUntil3": "Szava<PERSON><PERSON><PERSON><PERSON> eddig lehet leadni", "app.components.TopicsPicker.numberOfSelectedTopics": "A kiválasztott {numberOfSelectedTopics, plural, =0 {nulla címke} one {egy címke} other {# címke}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "<PERSON><PERSON><PERSON>", "app.components.UI.MoreActionsMenu.moreOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.UI.MoreActionsMenu.showMoreActions": "További műveletek megjelenítése", "app.components.UI.NewLabel.new": "ÚJ", "app.components.UI.PhaseFilter.noAppropriatePhases": "<PERSON><PERSON> megfelelő fázis ehhez a projekthez", "app.components.UI.RemoveImageButton.a11y_removeImage": "Távolítsa el", "app.components.UI.TranslateButton.original": "Eredeti", "app.components.UI.TranslateButton.translate": "Fordítás", "app.components.Unauthorized.additionalInformationRequired": "A részvételhez további információk szükségesek.", "app.components.Unauthorized.completeProfile": "<PERSON><PERSON><PERSON> profil", "app.components.Unauthorized.completeProfileTitle": "A részvételhez töltse ki profilját", "app.components.Unauthorized.noPermission": "<PERSON><PERSON><PERSON> en<PERSON><PERSON><PERSON><PERSON> ennek az oldalnak a megtekintéséhez", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nincs j<PERSON> az oldal eléréséhez.", "app.components.Upload.errorImageMaxSizeExceeded": "A kiválasztott kép na<PERSON>, mint {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "<PERSON><PERSON> vagy több kiv<PERSON>lasztott kép nagyobb, mint {maxFileSize}MB", "app.components.Upload.onlyOneImage": "Csak 1 képet tölthet fel", "app.components.Upload.onlyXImages": "Csak {maxItemsCount} képeket tölthet fel", "app.components.Upload.remaining": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Upload.uploadImageLabel": "Válasszon ki egy képet (max. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Válasszon ki egy vagy több képet", "app.components.UpsellTooltip.tooltipContent": "Ez a funkció nem szerepel a jelenlegi csomagban. A zárolás feloldásához forduljon kormányzati sikermenedzseréhez vagy rendszergazdájához.", "app.components.UserName.anonymous": "Névtelen", "app.components.UserName.anonymousTooltip2": "Ez a felhasználó úgy d<PERSON>, hogy anonimizálja hozzájárulását", "app.components.UserName.authorWithNoNameTooltip": "A rendszer automatikusan generálta a nevét, mert nem adta meg a nevét. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, f<PERSON><PERSON><PERSON><PERSON><PERSON>, ha módosítani szeretné.", "app.components.UserName.deletedUser": "ismeretlen szerző", "app.components.UserName.verified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.VerificationModal.verifyAuth0": "Ellenőrizze a NemID segítségével", "app.components.VerificationModal.verifyBOSA": "Igazolás itme vagy eID segítségével", "app.components.VerificationModal.verifyBosaFas": "Igazolás itme vagy eID segítségével", "app.components.VerificationModal.verifyClaveUnica": "Ellenőrizze a Clave Unica segítségével", "app.components.VerificationModal.verifyFakeSSO": "Ellen<PERSON>rz<PERSON> hamis SSO-val", "app.components.VerificationModal.verifyIdAustria": "Igazoljon Austria ID-vel", "app.components.VerificationModal.verifyKeycloak": "Ellenőrizze az ID-Porten segítségével", "app.components.VerificationModal.verifyNemLogIn": "Ellenőrizze a MitID segítségével", "app.components.VerificationModal.verifyTwoday2": "Igazolás BankID vagy Freja eID+ segítségével", "app.components.VerificationModal.verifyYourIdentity": "Igazolja személyazonosságát", "app.components.VoteControl.budgetingFutureEnabled": "Költségkeretét {enabledFromDate}kezdettel oszthatja fel.", "app.components.VoteControl.budgetingNotPermitted": "A részvételi költségvetés jelenleg nincs engedélyezve.", "app.components.VoteControl.budgetingNotPossible": "A költségkeret módosítása jelenleg nem lehetséges.", "app.components.VoteControl.budgetingNotVerified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {verifyAccountLink} a folytatáshoz.", "app.components.VoteInputs._shared.currencyLeft1": "{budgetLeft} / {totalBudget} maradt", "app.components.VoteInputs._shared.numberOfCreditsLeft": "{votesLeft, plural, =0 {nincs több kredited} other {# a {totalNumberOfVotes, plural, one {1 kreditből} other {# kredit}} van <PERSON>}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "{votesLeft, plural, =0 {nincs több pontod} other {# a {totalNumberOfVotes, plural, one {1 pontból} other {# pont}} van még}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "{votesLeft, plural, =0 {nincs több tokened} other {# a {totalNumberOfVotes, plural, one {-ból 1 token} other {# token}} van}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "{votesLeft, plural, =0 {nincs több szavazatod} other {# a {totalNumberOfVotes, plural, one {szavazatból 1 szavazat} other {# szavazat}} van még}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "M<PERSON>r beküld<PERSON> a költségvetését. A módosításához kattintson a „Beküldés módosítása” gombra.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "<PERSON><PERSON><PERSON> bekü<PERSON><PERSON> a költségvetésedet. A módosításához menj vissza a projekt oldalára, és kattints a „Beküldés módosítása” gombra.", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Költségvetés nem érhető el, mivel ez a fázis nem aktív.", "app.components.VoteInputs.single.youHaveVotedForX2": "Ön a {votes, plural, =0 {# opcióra s<PERSON>tt} one {# opció} other {# opció}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON>z azt jele<PERSON>, hogy elveszíti az ehhez a bevitelhez kapcsolódó összes adatot, például a megjegyzéseket, reakciókat és szavazatokat. Ez a művelet nem vonható vissza.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Biztosan törli ezt a bemenetet?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "M<PERSON>gs<PERSON>", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Erősítse meg", "app.components.admin.SlugInput.resultingURL": "Eredmény URL", "app.components.admin.SlugInput.slugTooltip": "A slug az oldal webcímének vagy URL-jének végén található egyedi szavak halmaza.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "Ha módosítja az URL-t, a régi URL-t használó oldalra mutató hivatkozások többé nem működnek.", "app.components.admin.SlugInput.urlSlugLabel": "Meztelen csiga", "app.components.admin.UserFilterConditions.addCondition": "<PERSON>jon hozzá egy <PERSON>", "app.components.admin.UserFilterConditions.field_email": "Email", "app.components.admin.UserFilterConditions.field_event_attendance": "Regisztráció az eseményekre", "app.components.admin.UserFilterConditions.field_follow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_lives_in": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Közösségi monitor felmé<PERSON>", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Interakcióba lép egy állapottal rendelkező bemenettel", "app.components.admin.UserFilterConditions.field_participated_in_project": "Hozzájárult a projekthez", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Elhelyezett valamit címkével", "app.components.admin.UserFilterConditions.field_registration_completed_at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_role": "Szerep", "app.components.admin.UserFilterConditions.field_verified": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ötletezés", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Javaslatok", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "nem regisztrált egyik eseményre sem", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "nincs regisztrá<PERSON>va egyetlen eseményre sem", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "regisztrált ezen események egyikére", "app.components.admin.UserFilterConditions.predicate_attends_something": "legalább egy eseményre regisztr<PERSON>lt", "app.components.admin.UserFilterConditions.predicate_begins_with": "-vel kezd<PERSON>dik", "app.components.admin.UserFilterConditions.predicate_commented_in": "kommentálta", "app.components.admin.UserFilterConditions.predicate_contains": "tartalmaz", "app.components.admin.UserFilterConditions.predicate_ends_on": "-on végződik", "app.components.admin.UserFilterConditions.predicate_has_value": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_in": "b<PERSON><PERSON><PERSON><PERSON> m<PERSON> végrehajtott", "app.components.admin.UserFilterConditions.predicate_is": "van", "app.components.admin.UserFilterConditions.predicate_is_admin": "egy adminisztr<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_after": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_before": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_checked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_empty": "üres", "app.components.admin.UserFilterConditions.predicate_is_equal": "van", "app.components.admin.UserFilterConditions.predicate_is_exactly": "pontosan van", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "nagyobb, mint", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "nagy<PERSON><PERSON> vagy e<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_not_area": "területet kizár", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "mapp<PERSON><PERSON> nem tartalmazza", "app.components.admin.UserFilterConditions.predicate_is_not_input": "kiz<PERSON><PERSON>ja a bevitelt", "app.components.admin.UserFilterConditions.predicate_is_not_project": "projektet kizárja", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "kizárja a témát", "app.components.admin.UserFilterConditions.predicate_is_one_of": "az egyik", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "az egyik terület", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "az egyik <PERSON>", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "az egyik bemenet", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "az egyik projektet", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "az egyik téma", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "projekt<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "kisebb, mint", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "kise<PERSON> vagy e<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_verified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "nem azzal k<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "nem komment<PERSON>lta", "app.components.admin.UserFilterConditions.predicate_not_contains": "nem tartalmaz", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "nem ér véget", "app.components.admin.UserFilterConditions.predicate_not_has_value": "nincs <PERSON>", "app.components.admin.UserFilterConditions.predicate_not_in": "nem j<PERSON><PERSON>lt hozz<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is": "nem", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "nem adminisztrátor", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "ninc<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "nem ü<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "nem", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "nem norm<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "nem egy<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "nem projektmen<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "ninc<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "nem tett közzé be<PERSON>t", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "nem reagált a hozzászólásra", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "nem reagált a bevitelre", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "nem regisz<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "nem vett részt felmé<PERSON>ben", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "nem j<PERSON>nt<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "nem vett részt a szavazásban", "app.components.admin.UserFilterConditions.predicate_nothing": "semmi", "app.components.admin.UserFilterConditions.predicate_posted_input": "közzétett egy bemenetet", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reagált a kommentre", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reagált a bemenetre", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "regisztr<PERSON>lt egy <PERSON>", "app.components.admin.UserFilterConditions.predicate_something": "valami", "app.components.admin.UserFilterConditions.predicate_taken_survey": "felmérést végzett", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "önként j<PERSON>nt<PERSON>tt", "app.components.admin.UserFilterConditions.predicate_voted_in3": "részt vett a szavazásban", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Attribútum", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "<PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "<PERSON>em ka<PERSON>rtesítést a hozzájárulásáról", "app.components.anonymousParticipationModal.cancel": "M<PERSON>gs<PERSON>", "app.components.anonymousParticipationModal.continue": "Folytatás", "app.components.anonymousParticipationModal.participateAnonymously": "Névtelenül vegyen részt", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "Ezzel bi<PERSON> <b>el<PERSON><PERSON><PERSON> profil<PERSON></b> az adminisztrátorok, projektmenedzserek és más lakók elől ehhez a konkrét hozzájáru<PERSON>ás<PERSON>z, <PERSON><PERSON> senki sem tudja összekapcsolni Önnel ezt a hozzájárulást. Az anonim hozzászólások nem szerkeszthetők, véglegesnek minősülnek.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Számunkra kie<PERSON>ten font<PERSON>, hogy platformunkat minden felhasználó számára biztonságossá tegyük. A szavak számítanak, e<PERSON><PERSON><PERSON>, legyetek kedvesek egymáshoz.", "app.components.avatar.titleForAccessibility": "{fullName}profilja", "app.components.customFields.mapInput.removeAnswer": "Válasz eltávolítása", "app.components.customFields.mapInput.undo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.customFields.mapInput.undoLastPoint": "Utolsó pont visszavonása", "app.components.followUnfollow.follow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.followUnfollow.followADiscussion": "Kövesse a vitát", "app.components.followUnfollow.followTooltipInputPage2": "A követés e-mailben értesíti az állapotváltozásokról, a hivatalos frissítésekről és a megjegyzésekről. Bármikor {unsubscribeLink} teheti meg.", "app.components.followUnfollow.followTooltipProjects2": "A követés e-mailben értesíti a projekt változásairól. Bármikor {unsubscribeLink} teheti meg.", "app.components.followUnfollow.unFollow": "Követés megszüntetése", "app.components.followUnfollow.unsubscribe": "leiratkozás", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "irányelveinket", "app.components.form.ErrorDisplay.next": "Következő", "app.components.form.ErrorDisplay.previous": "Előző", "app.components.form.ErrorDisplay.save": "Megtakarítás", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Kezdjen el gépelni a felhasználói e-mail vagy név szerinti kereséshez...", "app.components.form.anonymousSurveyMessage2": "A felmérésre adott összes válasz anonimizált.", "app.components.form.backToInputManager": "Vissza a beviteli kezelőhöz", "app.components.form.backToProject": "Vissza a projekthez", "app.components.form.components.controls.mapInput.removeAnswer": "Válasz eltávolítása", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "Az utolsó pont visszavonása", "app.components.form.controls.addressInputAriaLabel": "<PERSON><PERSON><PERSON> bevitele", "app.components.form.controls.addressInputPlaceholder6": "<PERSON><PERSON> meg egy címet...", "app.components.form.controls.adminFieldTooltip": "A mezőt csak a rendszergazdák láthatják", "app.components.form.controls.allStatementsError": "Minden állításhoz választ kell választani.", "app.components.form.controls.back": "<PERSON><PERSON><PERSON>", "app.components.form.controls.clearAll": "Minden törlése", "app.components.form.controls.clearAllScreenreader": "Törölje az összes választ a fenti mátrixkérdésből", "app.components.form.controls.clickOnMapMultipleToAdd3": "Rajzolás<PERSON>z katti<PERSON> a térképre. Ezután hú<PERSON> a pontokat a mozgatáshoz.", "app.components.form.controls.clickOnMapToAddOrType": "Válaszának hozzáadásához katti<PERSON> a térképre, vagy <PERSON>jon be egy címet al<PERSON>.", "app.components.form.controls.confirm": "Erősítse meg", "app.components.form.controls.cosponsorsPlaceholder": "Kezdjen el beírni egy nevet a kereséshez", "app.components.form.controls.currentRank": "<PERSON><PERSON><PERSON><PERSON> rang:", "app.components.form.controls.minimumCoordinates2": "Legalább {numPoints} térképpont szükséges.", "app.components.form.controls.noRankSelected": "<PERSON><PERSON><PERSON> k<PERSON>lasztva rang", "app.components.form.controls.notPublic1": "*Ezt a választ csak a projektmenedzserekkel osztjuk meg, a nyilvánossággal nem.", "app.components.form.controls.optionalParentheses": "(választható)", "app.components.form.controls.rankingInstructions": "Húzással rangsorolhatja a lehetőségeket.", "app.components.form.controls.selectAsManyAsYouLike": "* Válasszon ki annyit, amennyit szeretne", "app.components.form.controls.selectBetween": "*Válasszon a {minItems} és {maxItems} lehetőségek közül", "app.components.form.controls.selectExactly2": "*Válassza ki pontosan a {selectExactly, plural, one {# opciót} other {# opciók}}", "app.components.form.controls.selectMany": "* Válass<PERSON> ki annyit, amenny<PERSON> akar", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Érintse meg a térképet a rajzoláshoz. <PERSON><PERSON><PERSON><PERSON> hú<PERSON> a pontokat a mozgatáshoz.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Érintse meg a térképet a rajzoláshoz.", "app.components.form.controls.tapOnMapMultipleToAdd3": "A válasz hozzáadásához érintse meg a térképet.", "app.components.form.controls.tapOnMapToAddOrType": "A válasz hozzáadásához érintse meg a térképet, vagy <PERSON>jon be egy címet al<PERSON>.", "app.components.form.controls.tapToAddALine": "<PERSON><PERSON><PERSON><PERSON> egy sor hozzáadásához", "app.components.form.controls.tapToAddAPoint": "Érintse meg a pont hozzáadásához", "app.components.form.controls.tapToAddAnArea": "Érintse meg egy terület hozzáadásához", "app.components.form.controls.uploadShapefileInstructions": "* Töltsön fel egy vagy több shape fájlt tartalmazó zip fájlt.", "app.components.form.controls.validCordinatesTooltip2": "Ha a hely nem jelenik meg a lehetőségek között gépelés közben, érvényes koordinátákat adhat hozz<PERSON> 'sz<PERSON>lesség, hosszúság' form<PERSON><PERSON>ban a pontos hely megadásához (pl.: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} / {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} / {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} az {total}k<PERSON><PERSON><PERSON><PERSON>, ahol a {maxValue} <PERSON>rt<PERSON><PERSON> {maxLabel}", "app.components.form.error": "Hiba", "app.components.form.locationGoogleUnavailable": "<PERSON><PERSON> bet<PERSON>lten<PERSON> a google maps által biztosított helymezőt.", "app.components.form.progressBarLabel": "A felmérés előrehaladása", "app.components.form.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.form.submitApiError": "Hiba történt az űrlap elküldésekor. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ellenőrizze az esetleges hibákat, és próbálja újra.", "app.components.form.verifiedBlocked": "Ezt a mezőt nem szerkesztheti, mert ellenőrzött információkat tartalmaz", "app.components.formBuilder.Page": "oldal", "app.components.formBuilder.accessibilityStatement": "akadálymentesítési nyilatkozat", "app.components.formBuilder.addAnswer": "<PERSON><PERSON><PERSON>z <PERSON>", "app.components.formBuilder.addStatement": "Nyilatkozat hozzáadása", "app.components.formBuilder.agree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "Ha hozzáfér a mesterséges intelligencia csomagunkhoz, akkor összefoglalhatja és kategorizálhatja a szöveges válaszokat az AI segítségével", "app.components.formBuilder.askFollowUpToggleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.bad": "<PERSON><PERSON>", "app.components.formBuilder.buttonLabel": "<PERSON><PERSON>", "app.components.formBuilder.buttonLink": "Gomb link", "app.components.formBuilder.cancelLeaveBuilderButtonText": "M<PERSON>gs<PERSON>", "app.components.formBuilder.category": "Kategória", "app.components.formBuilder.chooseMany": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.chooseOne": "Válassz egyet", "app.components.formBuilder.close": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.closed": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.configureMap": "Térkép konfigurálása", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON><PERSON>, el akarok menni", "app.components.formBuilder.content": "Tartalom", "app.components.formBuilder.continuePageLabel": "Továbbra is", "app.components.formBuilder.cosponsors": "Társszponzorok", "app.components.formBuilder.default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.defaultContent": "Alapértelmezett tartalom", "app.components.formBuilder.delete": "T<PERSON>r<PERSON><PERSON>", "app.components.formBuilder.deleteButtonLabel": "T<PERSON>r<PERSON><PERSON>", "app.components.formBuilder.description": "Le<PERSON><PERSON><PERSON>", "app.components.formBuilder.disabledBuiltInFieldTooltip": "<PERSON>z már felkerült az űrlapra. Az alapértelmezett tartalom csak egyszer használható fel.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Egyéni tartalom hozzáadása nem része a jelenlegi licencének. Forduljon GovSuccess <PERSON>z, ha többet szeretne megtudni erről.", "app.components.formBuilder.disagree": "<PERSON><PERSON>rt egyet", "app.components.formBuilder.displayAsDropdown": "Megjelenítés legörd<PERSON><PERSON><PERSON>", "app.components.formBuilder.displayAsDropdownTooltip": "Jelenítse meg a lehetőségeket egy legördülő menüben. Ha sok lehetőséged van, ez ajánlott.", "app.components.formBuilder.done": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.drawArea": "Rajzolj területet", "app.components.formBuilder.drawRoute": "Útvonal rajzolása", "app.components.formBuilder.dropPin": "Csepp tű", "app.components.formBuilder.editButtonLabel": "Szerkesztés", "app.components.formBuilder.emptyImageOptionError": "Adjon meg legalább 1 választ. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ve<PERSON><PERSON>, hogy minden válasznak címet kell <PERSON>ni.", "app.components.formBuilder.emptyOptionError": "Adjon meg legalább 1 választ", "app.components.formBuilder.emptyStatementError": "Adjon meg legalább 1 állítást", "app.components.formBuilder.emptyTitleError": "Adja meg a kérdés c<PERSON>", "app.components.formBuilder.emptyTitleMessage": "Adj címet az összes válasznak", "app.components.formBuilder.emptyTitleStatementMessage": "Adjon címet az összes állításnak", "app.components.formBuilder.enable": "Engedélyezés", "app.components.formBuilder.errorMessage": "Hiba történt. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jav<PERSON><PERSON><PERSON> ki a <PERSON>b<PERSON><PERSON>, hogy el tudja menteni a módosításokat", "app.components.formBuilder.fieldGroup.description": "<PERSON><PERSON><PERSON><PERSON> (nem kötelező)", "app.components.formBuilder.fieldGroup.title": "Cím (nem kötelező)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Jelenleg ezekre a kérdésekre a válaszok csak az Input Manager exportált Excel fájljában érhetők el, a felhasználók számára nem láthatók.", "app.components.formBuilder.fieldLabel": "Válaszlehetőségek", "app.components.formBuilder.fieldLabelStatement": "Nyilatkozatok", "app.components.formBuilder.fileUpload": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Térkép alap<PERSON>al", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Illessze be a térképet kontextusként, vagy tegyen fel helyalapú kérdéseket a résztvevőknek.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Az optimális felhasználói élmény érdekében nem javasoljuk, hogy a térképalapú oldalakon pontokkal, útvonalakkal vagy területekkel kapcsolatos kérdéseket adjon hozzá.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "A jelenlegi licenc nem tartalmazza a felmérési leképezési funkciókat. További információért forduljon GovSuccess menedzseréhez.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Oldal típusa", "app.components.formBuilder.formEnd": "Forma vége", "app.components.formBuilder.formField.cancelDeleteButtonText": "M<PERSON>gs<PERSON>", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "Igen, törölje az oldalt", "app.components.formBuilder.formField.copyNoun": "Másolat", "app.components.formBuilder.formField.copyVerb": "Másolat", "app.components.formBuilder.formField.delete": "T<PERSON>r<PERSON><PERSON>", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Az oldal törlésével a hozzá tartozó logika is törlődik. Biztosan törölni akarod?", "app.components.formBuilder.formField.deleteResultsInfo": "Ezt nem lehet v<PERSON>vonni", "app.components.formBuilder.goToPageInputLabel": "Akkor a következő oldal:", "app.components.formBuilder.good": "<PERSON><PERSON>", "app.components.formBuilder.helmetTitle": "Űrlapépítő", "app.components.formBuilder.imageFileUpload": "Képfeltöltés", "app.components.formBuilder.invalidLogicBadgeMessage": "Érvénytelen logika", "app.components.formBuilder.labels2": "Címkék (opcionális)", "app.components.formBuilder.labelsTooltipContent2": "Válasszon opcionális címkéket a lineáris skálaértékek bármelyikéhez.", "app.components.formBuilder.lastPage": "Befejező", "app.components.formBuilder.layout": "Elrendezés", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Biztos, hogy el akarsz menni?", "app.components.formBuilder.leaveBuilderText": "Nem mentett módosításai vannak. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mentse el indulás előtt. Ha kilép, a módosítások elvesznek.", "app.components.formBuilder.limitAnswersTooltip": "Ha be van ka<PERSON>, a válaszadóknak ki kell választaniuk a megadott számú választ a folytatáshoz.", "app.components.formBuilder.limitNumberAnswers": "Korlátozza a válaszok számát", "app.components.formBuilder.linePolygonMapWarning2": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a vonal- és sokszögrajz nem felel meg a hozzáférhetőségi szabványoknak. További információ a {accessibilityStatement}oldalon található.", "app.components.formBuilder.linearScale": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.locationDescription": "Elhelyezkedés", "app.components.formBuilder.logic": "Logika", "app.components.formBuilder.logicAnyOtherAnswer": "B<PERSON>rmilyen más válasz", "app.components.formBuilder.logicConflicts.conflictingLogic": "<PERSON><PERSON><PERSON><PERSON> log<PERSON>", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Ez az oldal olyan kérdéseket tartalmaz, am<PERSON><PERSON>böző oldalakra vezetnek. Ha a résztvevők több kérdésre válaszolnak, a legtávolabbi oldal jelenik meg. Győződjön meg arról, hogy ez a viselkedés összhangban van a tervezett áramlással.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Ezen az oldalon több logikai szabályt alkalmaznak: a többszörös kijelölésű kérdések logikáját, az oldalszintű logikát és a kérdések közötti logikát. Ha ezek a feltételek átfedésben vannak, a kérdéslogika elsőbbséget élvez az oldal logikájával szemben, és a legtávolabbi oldal jelenik meg. Tekintse át a logikát, hogy megbizonyosodjon arról, hogy igazodik a tervezett folyamathoz.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Ez az oldal egy többször<PERSON><PERSON> kiválasztható kérdést tartalmaz, ahol a lehetőségek különböző oldalakhoz vezetnek. Ha a résztvevők több lehetőséget választanak, a legtávolabbi oldal jelenik meg. Győződjön meg arról, hogy ez a viselkedés összhangban van a tervezett áramlással.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Ez az oldal egy többször<PERSON><PERSON> kiválasztható kérdést tartalmaz, ahol a lehetőségek különböző oldalakra vezetnek, és vannak olyan kérdések, amelyek más oldalakra vezetnek. A legtávolabbi oldal jelenik meg, ha ezek a feltételek átfedésben vannak. Győződjön meg arról, hogy ez a viselkedés összhangban van a tervezett áramlással.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Ez az oldal egy többször<PERSON><PERSON> kiválasztható kérdést tartalmaz, ahol a lehetőségek különböző oldalakra vezetnek, és logikája oldal és kérdés szinten is be van állítva. A kérdéslogika élvez elsőbbséget, és a legtávolabbi oldal jelenik meg. Győződjön meg arról, hogy ez a viselkedés összhangban van a tervezett áramlással.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Ennek az oldalnak logikája van oldalszinten és kérdésszinten is beállítva. A kérdéslogika elsőbbséget élvez az oldalszintű logikával szemben. Győződjön meg arró<PERSON>, hogy ez a viselkedés összhangban van a tervezett áramlással.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Ennek az oldalnak logikája van beállítva mind az oldal, mind a kérdés szintjén, és több kérdés is közvetlenül a különböző oldalakra irányul. A kérdéslogika élvez elsőbbséget, és a legtávolabbi oldal jelenik meg. Győződjön meg arról, hogy ez a viselkedés összhangban van a tervezett áramlással.", "app.components.formBuilder.logicNoAnswer2": "<PERSON><PERSON>", "app.components.formBuilder.logicPanelAnyOtherAnswer": "<PERSON> m<PERSON> válasz", "app.components.formBuilder.logicPanelNoAnswer": "Ha nem válaszol", "app.components.formBuilder.logicValidationError": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a logika nem hivatkozik korábbi oldalakra", "app.components.formBuilder.longAnswer": "Hosszú válasz", "app.components.formBuilder.mapConfiguration": "Térkép konfigu<PERSON>", "app.components.formBuilder.mapping": "Térképezés", "app.components.formBuilder.mappingNotInCurrentLicense": "A jelenlegi licenc nem tartalmazza a felmérési leképezési funkciókat. További információért forduljon GovSuccess menedzseréhez.", "app.components.formBuilder.matrix": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.matrixSettings.columns": "Oszlopok", "app.components.formBuilder.matrixSettings.rows": "So<PERSON>", "app.components.formBuilder.multipleChoice": "Több választás", "app.components.formBuilder.multipleChoiceHelperText": "Ha több lehetőség különböző oldalakra vezet, és a résztvevők egynél többet választanak ki, a legtávolabbi oldal jelenik meg. Győződjön meg arról, hogy ez a viselkedés összhangban van a tervezett áramlással.", "app.components.formBuilder.multipleChoiceImage": "Képválasztás", "app.components.formBuilder.multiselect.maximum": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.minimum": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.neutral": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.newField": "<PERSON><PERSON>", "app.components.formBuilder.number": "Szám", "app.components.formBuilder.ok": "Rendben", "app.components.formBuilder.open": "Nyitott", "app.components.formBuilder.optional": "Választható", "app.components.formBuilder.other": "Más", "app.components.formBuilder.otherOption": "\"Egyéb\" opció", "app.components.formBuilder.otherOptionTooltip": "Lehetővé teszi a résztvevők számára, hogy egyéni választ adjanak meg, ha a megadott válaszok nem egyeznek a preferenciájukkal", "app.components.formBuilder.page": "oldal", "app.components.formBuilder.pageCannotBeDeleted": "<PERSON>z az oldal nem törölhető.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Ez az oldal nem törö<PERSON>ő, és nem engedélyezi további mezők hozzáadását.", "app.components.formBuilder.pageRuleLabel": "A következő oldal:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Ha nem adunk hozzá logik<PERSON>, az űrlap a normál folyamát követi. Ha az oldalnak és a kérdéseinek is van logik<PERSON>ja, akkor a kérdéslogika élvez elsőbbséget. Győződjön meg arról, hogy ez igazodik a tervezett folyamathoz. További információért látogasson el a {supportPageLink}oldalra", "app.components.formBuilder.preview": "Előnézet:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "A társszponzorok nem jelennek meg a letöltött PDF-ben, és a FormSync-en keresztüli importálásuk nem támogatott.", "app.components.formBuilder.printSupportTooltip.fileupload": "A fájlfeltöltési kérdések nem támogatottként jelennek meg a letöltött PDF-ben, és a FormSync-en keresztüli importálásuk sem támogatott.", "app.components.formBuilder.printSupportTooltip.mapping": "A letöltött PDF-ben megjelennek a megfeleltetési kérdések, de a rétegek nem lesznek láthatók. A megfeleltetési kérdések FormSync-en keresztüli importálása nem támogatott.", "app.components.formBuilder.printSupportTooltip.matrix": "A mátrixkérdések megjelennek a letöltött PDF-ben, de jelenleg nem támogatottak a FormSync-en keresztüli importálásuk.", "app.components.formBuilder.printSupportTooltip.page": "Az oldalak címei és leírásai szakaszfejlécként jelennek meg a letöltött PDF-ben.", "app.components.formBuilder.printSupportTooltip.ranking": "A rangsoroló kérdések megjelennek a letöltött PDF-ben, de jelenleg nem támogatottak az FormSync-en keresztüli importálásuk.", "app.components.formBuilder.printSupportTooltip.topics2": "A címkék nem támogatottként jelennek meg a letöltött PDF-ben, és a FormSync-en keresztüli importálásuk nem támogatott.", "app.components.formBuilder.proposedBudget": "Javasolt költségvetés", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "Ezt a kérdést nem lehet törölni.", "app.components.formBuilder.questionDescriptionOptional": "<PERSON><PERSON><PERSON><PERSON> (nem kötelező)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.randomize": "Véletlenszerűvé", "app.components.formBuilder.randomizeToolTip": "A válaszok sorrendje felhasználónként véletlenszerű lesz", "app.components.formBuilder.range": "Hatótávolság", "app.components.formBuilder.ranking": "Rangsorol<PERSON>", "app.components.formBuilder.rating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.removeAnswer": "Válasz eltávolítása", "app.components.formBuilder.required": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.requiredToggleLabel": "Tedd kötelezővé ennek a kérdésnek a megválaszolását", "app.components.formBuilder.ruleForAnswerLabel": "Ha a válasz:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "Ha a válaszok a következőket tartalmazzák:", "app.components.formBuilder.save": "Megtakarítás", "app.components.formBuilder.selectRangeTooltip": "Válassza ki a skála maximális értékét.", "app.components.formBuilder.sentiment": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.shapefileUpload": "Esri shapefile feltöltése", "app.components.formBuilder.shortAnswer": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.showResponseToUsersToggleLabel": "Válasz megjelenítése a felhasználóknak", "app.components.formBuilder.singleChoice": "Egyetlen választás", "app.components.formBuilder.staleDataErrorMessage2": "Probléma lépett fel. Ezt a beviteli űrlapot a közelmúltban máshová mentették. Ennek az lehet az oka, hogy Ön vagy egy másik felhasználó megnyitotta szerkesztésre egy másik böngészőablakban. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, friss<PERSON><PERSON><PERSON> az oldalt a legfrissebb űrlap eléréséhez, majd hajtsa végre újra a módosításokat.", "app.components.formBuilder.stronglyAgree": "<PERSON><PERSON>sen egyetértek", "app.components.formBuilder.stronglyDisagree": "határozottan nem értek egyet", "app.components.formBuilder.supportArticleLinkText": "ezt az oldalt", "app.components.formBuilder.tags": "Címkék", "app.components.formBuilder.title": "Cím", "app.components.formBuilder.toLabel": "hogy", "app.components.formBuilder.unsavedChanges": "<PERSON><PERSON> mentett módosí<PERSON><PERSON><PERSON>", "app.components.formBuilder.useCustomButton2": "<PERSON><PERSON><PERSON><PERSON> oldal gomb has<PERSON>", "app.components.formBuilder.veryBad": "Nagyon rossz", "app.components.formBuilder.veryGood": "Nagyon jó", "app.components.ideas.similarIdeas.engageHere": "Vegyen részt itt", "app.components.ideas.similarIdeas.noSimilarSubmissions": "<PERSON><PERSON> has<PERSON><PERSON> be<PERSON>.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Has<PERSON><PERSON>ó beadványokat találtunk – ha velük foglalkozunk, az megerősítheti őket!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "<PERSON><PERSON><PERSON><PERSON> has<PERSON><PERSON><PERSON> be<PERSON>:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Hasonló pályaműveket keresek...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON><PERSON><PERSON><PERSON> mint egy nap} one {# nap} other {# nap}} maradt", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  h<PERSON><PERSON>", "app.components.screenReaderCurrency.AED": "Egyesült Arab Emírségek dirham", "app.components.screenReaderCurrency.AFN": "afgán afgán", "app.components.screenReaderCurrency.ALL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.AMD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ANG": "Holland Antillák gulden", "app.components.screenReaderCurrency.AOA": "Angolai Kwanza", "app.components.screenReaderCurrency.ARS": "Argentin peso", "app.components.screenReaderCurrency.AUD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "Azerbajdzsán<PERSON>", "app.components.screenReaderCurrency.BAM": "Bosznia-Hercegovina kabrió márka", "app.components.screenReaderCurrency.BBD": "bar<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BDT": "Bangladesi Taka", "app.components.screenReaderCurrency.BGN": "b<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "burundi frank", "app.components.screenReaderCurrency.BMD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BND": "Brunei dollár", "app.components.screenReaderCurrency.BOB": "Bolíviai Boliviano", "app.components.screenReaderCurrency.BOV": "bolíviai mvdol", "app.components.screenReaderCurrency.BRL": "brazil real", "app.components.screenReaderCurrency.BSD": "bah<PERSON><PERSON>", "app.components.screenReaderCurrency.BTN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BWP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "feh<PERSON><PERSON><PERSON>z rubel", "app.components.screenReaderCurrency.BZD": "Belize dollár", "app.components.screenReaderCurrency.CAD": "Kana<PERSON>", "app.components.screenReaderCurrency.CDF": "kong<PERSON>i frank", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "s<PERSON><PERSON><PERSON><PERSON> frank", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Chilei elszámolási egység (UF)", "app.components.screenReaderCurrency.CLP": "chilei peso", "app.components.screenReaderCurrency.CNY": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.COP": "kolumbiai peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Rica-i Colón", "app.components.screenReaderCurrency.CRE": "<PERSON><PERSON>", "app.components.screenReaderCurrency.CUC": "Ku<PERSON>i kabrió peso", "app.components.screenReaderCurrency.CUP": "kubai peso", "app.components.screenReaderCurrency.CVE": "Zöld-foki-szigeteki escudo", "app.components.screenReaderCurrency.CZK": "cseh korona", "app.components.screenReaderCurrency.DJF": "dzsi<PERSON>i frank", "app.components.screenReaderCurrency.DKK": "d<PERSON> korona", "app.components.screenReaderCurrency.DOP": "Dominikai peso", "app.components.screenReaderCurrency.DZD": "algé<PERSON><PERSON>", "app.components.screenReaderCurrency.EGP": "egyiptomi font", "app.components.screenReaderCurrency.ERN": "Eritreai <PERSON>", "app.components.screenReaderCurrency.ETB": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.FKP": "Falkland-szigetek font", "app.components.screenReaderCurrency.GBP": "brit font", "app.components.screenReaderCurrency.GEL": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Gibraltári font", "app.components.screenReaderCurrency.GMD": "Gam<PERSON><PERSON>", "app.components.screenReaderCurrency.GNF": "guineai frank", "app.components.screenReaderCurrency.GTQ": "guatemalai Quetzal", "app.components.screenReaderCurrency.GYD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.HKD": "hongkongi dollár", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "horvát kuna", "app.components.screenReaderCurrency.HTG": "Haiti Gourde", "app.components.screenReaderCurrency.HUF": "magyar forint", "app.components.screenReaderCurrency.IDR": "indonéz rúpia", "app.components.screenReaderCurrency.ILS": "<PERSON>z<PERSON><PERSON>", "app.components.screenReaderCurrency.INR": "indiai rúpia", "app.components.screenReaderCurrency.IQD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.IRR": "ir<PERSON><PERSON> r<PERSON>l", "app.components.screenReaderCurrency.ISK": "izlandi korona", "app.components.screenReaderCurrency.JMD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "j<PERSON><PERSON> jen", "app.components.screenReaderCurrency.KES": "kenyai shilling", "app.components.screenReaderCurrency.KGS": "Kirgizstani <PERSON>", "app.components.screenReaderCurrency.KHR": "Kambodzsai Riel", "app.components.screenReaderCurrency.KMF": "Comore-i frank", "app.components.screenReaderCurrency.KPW": "észak-k<PERSON>ai von", "app.components.screenReaderCurrency.KRW": "dél-koreai won", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "<PERSON><PERSON><PERSON>-szig<PERSON><PERSON>", "app.components.screenReaderCurrency.KZT": "kazah tenge", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "libanoni font", "app.components.screenReaderCurrency.LKR": "Srí Lanka-i rúpia", "app.components.screenReaderCurrency.LRD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "litván litas", "app.components.screenReaderCurrency.LVL": "lett lat", "app.components.screenReaderCurrency.LYD": "lí<PERSON>i <PERSON>", "app.components.screenReaderCurrency.MAD": "ma<PERSON><PERSON><PERSON><PERSON> dirham", "app.components.screenReaderCurrency.MDL": "moldovai lej", "app.components.screenReaderCurrency.MGA": "Madagasz<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MKD": "macedón dén<PERSON>r", "app.components.screenReaderCurrency.MMK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MNT": "Mongol Tögrög", "app.components.screenReaderCurrency.MOP": "Ma<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MRO": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MUR": "mauri<PERSON><PERSON> rú<PERSON>", "app.components.screenReaderCurrency.MVR": "Maldív Rufiyaa", "app.components.screenReaderCurrency.MWK": "malawi kwacha", "app.components.screenReaderCurrency.MXN": "mexikói peso", "app.components.screenReaderCurrency.MXV": "Mexikói Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "malajziai ringgit", "app.components.screenReaderCurrency.MZN": "Mozambiki Metical", "app.components.screenReaderCurrency.NAD": "nam<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NGN": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NIO": "nicaraguai Córdoba", "app.components.screenReaderCurrency.NOK": "nor<PERSON><PERSON>g korona", "app.components.screenReaderCurrency.NPR": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NZD": "<PERSON>j<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.OMR": "ománi ri<PERSON>l", "app.components.screenReaderCurrency.PAB": "Panamai Balboa", "app.components.screenReaderCurrency.PEN": "perui szol", "app.components.screenReaderCurrency.PGK": "Pápua Új-Guineai <PERSON>", "app.components.screenReaderCurrency.PHP": "Fülöp-szigeteki peso", "app.components.screenReaderCurrency.PKR": "pakisztáni rú<PERSON>", "app.components.screenReaderCurrency.PLN": "le<PERSON><PERSON><PERSON>y", "app.components.screenReaderCurrency.PYG": "Paraguayi Guaraní", "app.components.screenReaderCurrency.QAR": "katari ri<PERSON>l", "app.components.screenReaderCurrency.RON": "r<PERSON><PERSON> lej", "app.components.screenReaderCurrency.RSD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.RUB": "or<PERSON>z rubel", "app.components.screenReaderCurrency.RWF": "ruandai frank", "app.components.screenReaderCurrency.SAR": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>l", "app.components.screenReaderCurrency.SBD": "Salamon-szig<PERSON><PERSON>", "app.components.screenReaderCurrency.SCR": "Seychellois rú<PERSON>", "app.components.screenReaderCurrency.SDG": "szudáni font", "app.components.screenReaderCurrency.SEK": "sv<PERSON>d korona", "app.components.screenReaderCurrency.SGD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SHP": "Szent Ilona font", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "szomáliai shilling", "app.components.screenReaderCurrency.SRD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SSP": "dél-szudáni font", "app.components.screenReaderCurrency.STD": "São Tomé és Príncipe <PERSON>", "app.components.screenReaderCurrency.SYP": "Szíriai font", "app.components.screenReaderCurrency.SZL": "Szvázi <PERSON>", "app.components.screenReaderCurrency.THB": "thai baht", "app.components.screenReaderCurrency.TJS": "Tádzsikisztáni Somoni", "app.components.screenReaderCurrency.TMT": "Türkmenisztáni Manat", "app.components.screenReaderCurrency.TND": "tunéziai d<PERSON>", "app.components.screenReaderCurrency.TOK": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TOP": "Tonga Paʻanga", "app.components.screenReaderCurrency.TRY": "t<PERSON>rök líra", "app.components.screenReaderCurrency.TTD": "Trinidad és Tobago dollár", "app.components.screenReaderCurrency.TWD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.TZS": "tanzániai shilling", "app.components.screenReaderCurrency.UAH": "Ukrán hriv<PERSON>", "app.components.screenReaderCurrency.UGX": "Ugandai shilling", "app.components.screenReaderCurrency.USD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.USN": "ameri<PERSON> (másnap)", "app.components.screenReaderCurrency.USS": "amerikai dollár (ugyanazon a napon)", "app.components.screenReaderCurrency.UYI": "Uruguay peso és Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "uruguayi peso", "app.components.screenReaderCurrency.UZS": "Uzbekistan Som", "app.components.screenReaderCurrency.VEF": "<PERSON>i Bolívar", "app.components.screenReaderCurrency.VND": "vietnami Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "s<PERSON><PERSON><PERSON> tala", "app.components.screenReaderCurrency.XAF": "Közép-afrikai CFA frank", "app.components.screenReaderCurrency.XAG": "<PERSON><PERSON><PERSON><PERSON> (egy troy uncia)", "app.components.screenReaderCurrency.XAU": "<PERSON><PERSON> (egy troy uncia)", "app.components.screenReaderCurrency.XBA": "Európai összetett egység (EURCO)", "app.components.screenReaderCurrency.XBB": "Európai monetá<PERSON> (EMU-6)", "app.components.screenReaderCurrency.XBC": "Európai elszámolási egység 9 (EUA-9)", "app.components.screenReaderCurrency.XBD": "Európai elszámolási egység 17 (EUA-17)", "app.components.screenReaderCurrency.XCD": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.XDR": "Különleges lehívási jogok", "app.components.screenReaderCurrency.XFU": "UIC frank", "app.components.screenReaderCurrency.XOF": "nyugat-afrikai CFA frank", "app.components.screenReaderCurrency.XPD": "Palládium (egy troy uncia)", "app.components.screenReaderCurrency.XPF": "CFP frank", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON><PERSON> (egy troy uncia)", "app.components.screenReaderCurrency.XTS": "Kifejezetten tesztelési célokra fenntartott kódok", "app.components.screenReaderCurrency.XXX": "<PERSON><PERSON><PERSON> valuta", "app.components.screenReaderCurrency.YER": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ZAR": "dél-afrikai rand", "app.components.screenReaderCurrency.ZMW": "Zambiai Kwacha", "app.components.screenReaderCurrency.amount": "Összeg", "app.components.screenReaderCurrency.currency": "Valuta", "app.components.trendIndicator.lastQuarter2": "u<PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.applicability": "Ez a hozzáférhetőségi nyilatkozat egy {demoPlatformLink} -r<PERSON> <PERSON>, amely reprezentálja ezt a webhelyet; ugyanazt a forráskódot használja, és ugyanazokkal a funkciókkal rendelkezik.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.assesmentText2022": "Az oldal elérhetőségét egy külső szervezet értékelte, amely nem vett részt a tervezési és fejlesztési folyamatban. A fent említett {demoPlatformLink} megfelelősége ezen az {statusPageLink}-on azonosítható.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "módosíthatja a preferenciáit", "app.containers.AccessibilityStatement.changePreferencesText": "Bármikor {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Megfelelőségi kivételek", "app.containers.AccessibilityStatement.conformanceStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.contentConformanceExceptions": "<PERSON><PERSON>, hogy tartalmainkat mindenki számára befogadóvá tegyük. Bizonyos esetekben azonban előfordulhat, hogy a platformon elérhetetlen tartalom található, az alábbiak szerint:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "<PERSON><PERSON> weboldal", "app.containers.AccessibilityStatement.email": "Email:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Beágyazott felmérési eszközök", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Az ezen a platformon használható beágyazott felmérési eszközök harmadik féltől származó szoftverek, <PERSON>s előfordulhat, hogy nem érhetők el.", "app.containers.AccessibilityStatement.exception_1": "Digitális <PERSON>öteleződési platformjaink megkönnyítik az egyének és szervezetek által közzétett, felhasználók által létrehozott tartalmakat. Előfordulhat, hogy PDF-eket, képeket vagy egyéb fájltípus<PERSON>t, bele<PERSON><PERSON><PERSON> a multimédiát, mellékletként töltik fel a platformra, vagy a platform felhasználói szövegmezőkbe adják. Előfordulhat, hogy ezek a dokumentumok nem teljes mértékben hozzáférhetők.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Örömmel fogadjuk visszajelzését az oldal elérhetőségével kapcsolatban. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vegye fel velünk a kapcsolatot az alábbi módok egyikén:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Visszajelzési folyamat", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Brüsszel, Belgium", "app.containers.AccessibilityStatement.headTitle": "Hozzáférhetőségi nyilatkozat | {orgName}", "app.containers.AccessibilityStatement.intro2022": "A {goVocalLink} el<PERSON><PERSON><PERSON><PERSON><PERSON> amellett, hogy olyan platformot bi<PERSON><PERSON><PERSON><PERSON><PERSON>, amely minden felhasz<PERSON><PERSON><PERSON> szá<PERSON>ára elérhető, technológiától és képességektől függetlenül. Folyamatos erőfeszítéseink során betartjuk a jelenlegi vonatkozó akadálymentesítési szabványokat, hogy maximalizáljuk platformjaink elérhetőségét és használhatóságát minden felhasználó számára.", "app.containers.AccessibilityStatement.mapping": "Térképezés", "app.containers.AccessibilityStatement.mapping_1": "A platformon lévő térképek részben megfelelnek az akadálymentesítési szabványoknak. A térkép kiterjedése, nagyítása és a felhasználói felület widgetek billentyűzettel vezérelhetők térképek megtekintése közben. Az adminisztrátorok a háttérirodában vagy az Esri-integráció segítségével is beállíthatják a térképrétegek stílusát, hogy könnyebben hozzáférhető színpalettákat és szimbólumokat hozzanak létre. A különböző vonal- vagy sokszögstílusok (pl. szagg<PERSON><PERSON>) használata szintén segít a térképrétegek megkülönböztetésében, ahol csak lehetséges, és bár az ilyen stílus jelenleg nem konfigurálható a platformunkon belül, az Esri-integrációval rendelkező térképek használata esetén beállítható.", "app.containers.AccessibilityStatement.mapping_2": "A platformon lévő térképek nem érhetők el teljesen, mivel nem mutatják be hallhatóan az alaptérképeket, a térképrétegeket vagy az adatok trendjeit a képernyőolvasókat használó felhasználók számára. A teljesen hozzáférhető térképeknek hallhatóan be kell mutatniuk a térképrétegeket, és le kell írniuk az adatok bármely releváns trendjét. Ezenkívül a vonalas és sokszögű térképrajz a felmérésekben nem érhető el, mivel az alakzatokat nem lehet billentyűzet segítségével megrajzolni. Alternatív beviteli módok jelenleg nem állnak rendelkezésre a technikai bonyolultság miatt.", "app.containers.AccessibilityStatement.mapping_3": "A vonalas és sokszögű térképrajz könnyebb hozzáférhetővé tétele érdekében javasoljuk, hogy a felmérés kérdésében vagy az oldalleírásban szerepeltessen egy bevezetést vagy magyarázatot a térképen megjelenített dolgokról és a vonatkozó trendekről. Ezen túlmenően rövid vagy hosszú válaszszöveges kérdés is megadható, így a válaszadók szükség esetén (a térképre kattint<PERSON> helyett) egyszerűen leírhatják válaszukat. Javasoljuk továbbá a projektmenedzser elérhetőségének megadását, hogy azok a válaszadók, akik nem tudnak térképkérd<PERSON>t kitölteni, alternatív megoldást kérhessenek a kérdés megválaszolására (pl. v<PERSON><PERSON> me<PERSON>zélé<PERSON>).", "app.containers.AccessibilityStatement.mapping_4": "Ötletprojektek és javaslatok esetén lehetőség van a bemenetek megjelenítésére térképnézetben, amely nem érhető el. Azonban ezekhez a módszerekhez létezik egy alternatív listanézet a bemenetekről, amely elérhető.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Online workshopjaink élő videó streaming komponenssel rendelkeznek, amely jelenleg nem támogatja a feliratozást.", "app.containers.AccessibilityStatement.pageDescription": "Nyilatkozat a weboldal elérhetőségéről", "app.containers.AccessibilityStatement.postalAddress": "Postacím:", "app.containers.AccessibilityStatement.publicationDate": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.publicationDate2024": "Ez a hozzáférhetőségi nyilatkozat 2024. augusztus 21-<PERSON>n jelent meg.", "app.containers.AccessibilityStatement.responsiveness": "<PERSON><PERSON><PERSON><PERSON>, hogy a visszajelzésekre 1-2 munkanapon belül válaszoljunk.", "app.containers.AccessibilityStatement.statusPageText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.technologiesIntro": "Az oldal elérhetősége a következő technológiákon múlik:", "app.containers.AccessibilityStatement.technologiesTitle": "Technológiák", "app.containers.AccessibilityStatement.title": "Hozzáférhetőségi nyilatkozat", "app.containers.AccessibilityStatement.userGeneratedContent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>l generált tartalom", "app.containers.AccessibilityStatement.workshops": "Workshopok", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Válassza ki a projektet", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "A Content Builder hasz<PERSON><PERSON><PERSON><PERSON> fejlettebb elrendezési beállításokat használhat. Azokon a nyelveken, ahol nem érhető el tartalom a tartalomkészítőben, he<PERSON><PERSON> a szokásos projektleírási tartalom jelenik meg.", "app.containers.AdminPage.ProjectDescription.linkText": "Szerkessze a leírást a Content Builderben", "app.containers.AdminPage.ProjectDescription.saveError": "Hiba történt a projektleírás mentése közben.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "A leíráshoz használja a Tartalomkészítőt", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "A Content Builder használat<PERSON>val fejlettebb elrendezési beállításokat használhat.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "Projekt megtekintése", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "A felmérés vége", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Hozzon létre egy intelligens csoportot", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "A következő feltételek mindegyikének megfelelő felhasználók automatikusan hozzáadódnak a csoporthoz:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "<PERSON><PERSON> meg <PERSON>bb egy <PERSON>", "app.containers.AdminPage.Users.UsersGroup.rulesError": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Csoport mentése", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Az intelligens csoportok konfigurálása nem része a jelenlegi licencének. Forduljon GovSuccess men<PERSON>zseréhez, ha többet szeretne megtudni erről.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Adja meg a csoport nevét", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "<PERSON>z ellenőrzés <PERSON> tiltva az Ön platform<PERSON>, távolítsa el az ellenőrzési <PERSON>, vagy lépjen kapcsolatba az ügyfélszolgálattal.", "app.containers.App.appMetaDescription": "Üdvözöljük a {orgName}online részvételi platformján. \nFedezze fel a helyi projekteket, és vegyen részt a vitában!", "app.containers.App.loading": "Terhelés...", "app.containers.App.metaTitle1": "Állampolgári el<PERSON>ötelezettség platform | {orgName}", "app.containers.App.skipLinkText": "Ugrás a fő tartalomhoz", "app.containers.AreaTerms.areaTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AreaTerms.areasTerm": "területeken", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Már l<PERSON>tezik fiók ezzel az e-mail címmel. Kijelentkezhet, bejelentkezhet ezzel az e-mail címmel, és igazolhatja fiókját a beállítások oldalon.", "app.containers.Authentication.steps.AccessDenied.close": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "<PERSON>em felel meg a folyamatban való részvétel feltételeinek.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Térjen vissza az egyszeri bejelentkezéses ellenőrzéshez", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy <PERSON>t", "app.containers.Authentication.steps.Invitation.token": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "<PERSON><PERSON><PERSON>? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Jelentkezzen be", "app.containers.CampaignsConsentForm.ally_categoryLabel": "E-mail<PERSON> ebben a kategóriában", "app.containers.CampaignsConsentForm.messageError": "Hiba történt az e-mail-beállítások mentésekor.", "app.containers.CampaignsConsentForm.messageSuccess": "Az e-mail beállításait elmentettük.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "<PERSON><PERSON><PERSON> t<PERSON> e-mailes értesítéseket szeretne kapni? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Értesítések", "app.containers.CampaignsConsentForm.submit": "Megtakarítás", "app.containers.ChangeEmail.backToProfile": "Vissza a profilbeállításokhoz", "app.containers.ChangeEmail.confirmationModalTitle": "Erősítse meg e-mail címét", "app.containers.ChangeEmail.emailEmptyError": "Adjon meg egy e-mail címet", "app.containers.ChangeEmail.emailInvalidError": "Adjon meg egy e-mail címet a megfelelő formátumban, például név@szolgáltató.com", "app.containers.ChangeEmail.emailRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy e-mail címet.", "app.containers.ChangeEmail.emailTaken": "Ez az e-mail már has<PERSON> van.", "app.containers.ChangeEmail.emailUpdateCancelled": "Az e-mail frissítést törölték.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Az e-mail-cím frissítéséhez indítsa újra a folyamatot.", "app.containers.ChangeEmail.helmetDescription": "Változtassa meg az e-mail oldalát", "app.containers.ChangeEmail.helmetTitle": "Módosítsa az e-mail címét", "app.containers.ChangeEmail.newEmailLabel": "Új email", "app.containers.ChangeEmail.submitButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangeEmail.titleAddEmail": "Adja hozzá az e-mail címét", "app.containers.ChangeEmail.titleChangeEmail": "Módosítsa az e-mail címét", "app.containers.ChangeEmail.updateSuccessful": "E-mail-c<PERSON><PERSON> si<PERSON>n f<PERSON>.", "app.containers.ChangePassword.currentPasswordLabel": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "app.containers.ChangePassword.currentPasswordRequired": "<PERSON><PERSON> meg jelenleg<PERSON> j<PERSON>", "app.containers.ChangePassword.goHome": "<PERSON><PERSON> haza", "app.containers.ChangePassword.helmetDescription": "Módosítsa a jelszó oldalt", "app.containers.ChangePassword.helmetTitle": "Változtassa meg j<PERSON>v<PERSON>t", "app.containers.ChangePassword.newPasswordLabel": "<PERSON><PERSON>", "app.containers.ChangePassword.newPasswordRequired": "<PERSON><PERSON><PERSON> be az ú<PERSON>", "app.containers.ChangePassword.password.minimumPasswordLengthError": "<PERSON>jon meg egy legalább {minimumPasswordLength} karakter hoss<PERSON> j<PERSON>", "app.containers.ChangePassword.passwordChangeSuccessMessage": "<PERSON><PERSON><PERSON>va si<PERSON>esen frissítve", "app.containers.ChangePassword.passwordEmptyError": "<PERSON><PERSON> meg j<PERSON>", "app.containers.ChangePassword.passwordsDontMatch": "Erősítse meg az új j<PERSON>zó<PERSON>", "app.containers.ChangePassword.titleAddPassword": "<PERSON>jon hozzá egy j<PERSON>", "app.containers.ChangePassword.titleChangePassword": "Változtassa meg j<PERSON>v<PERSON>t", "app.containers.Comments.a11y_commentDeleted": "Megjegyzés törölve", "app.containers.Comments.a11y_commentPosted": "Megjegyzés közzétéve", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {nincs l<PERSON>} one {1 tetszik} other {# tetszik}}", "app.containers.Comments.a11y_undoLike": "Tetszés v<PERSON>zavonása", "app.containers.Comments.addCommentError": "Valami elromlott. K<PERSON><PERSON><PERSON><PERSON>k, próbá<PERSON>ja <PERSON>.", "app.containers.Comments.adminCommentDeletionCancelButton": "M<PERSON>gs<PERSON>", "app.containers.Comments.adminCommentDeletionConfirmButton": "Törölje ezt a megjegyzést", "app.containers.Comments.cancelCommentEdit": "M<PERSON>gs<PERSON>", "app.containers.Comments.childCommentBodyPlaceholder": "<PERSON><PERSON>j választ...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "Ezt a megjegyzést törölték.", "app.containers.Comments.commentDeletionCancelButton": "Tartsd meg a megjegyzésemet", "app.containers.Comments.commentDeletionConfirmButton": "Töröld a hozzászólásomat", "app.containers.Comments.commentLike": "Mint", "app.containers.Comments.commentReplyButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.commentsSortTitle": "A megjegyzések rendezése szerint", "app.containers.Comments.completeProfileLinkText": "töltse ki a profilj<PERSON>t", "app.containers.Comments.completeProfileToComment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {completeRegistrationLink} kommentáljon.", "app.containers.Comments.confirmCommentDeletion": "Biztosan törlöd ezt a megjegyzést? <PERSON><PERSON><PERSON> v<PERSON>!", "app.containers.Comments.deleteComment": "T<PERSON>r<PERSON><PERSON>", "app.containers.Comments.deleteReasonDescriptionError": "Adjon meg további információkat az okáról", "app.containers.Comments.deleteReasonError": "<PERSON><PERSON>", "app.containers.Comments.deleteReason_inappropriate": "<PERSON><PERSON> v<PERSON>", "app.containers.Comments.deleteReason_irrelevant": "<PERSON>z nem releváns", "app.containers.Comments.deleteReason_other": "Más ok", "app.containers.Comments.editComment": "Szerkesztés", "app.containers.Comments.guidelinesLinkText": "közösségi irányelveinket", "app.containers.Comments.ideaCommentBodyPlaceholder": "Írja meg észrevételét ide", "app.containers.Comments.internalCommentingNudgeMessage": "A jelenlegi licenc nem tartalmazza a belső megjegyzések megtételét. Forduljon GovSuccess <PERSON>éhez, ha többet szeretne megtudni erről.", "app.containers.Comments.internalConversation": "Bel<PERSON>ő beszé<PERSON>", "app.containers.Comments.loadMoreComments": "További megjegyzések betöltése", "app.containers.Comments.loadingComments": "Megjegyzések betöltése...", "app.containers.Comments.loadingMoreComments": "További megjegyzések betöltése...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Ez a megjegyzés nem látható a normál felhasználók számára", "app.containers.Comments.postInternalComment": "Belső megjegyzés közzététele", "app.containers.Comments.postPublicComment": "Nyilvános megjegyzés közzététele", "app.containers.Comments.profanityError": "Hoppá! <PERSON><PERSON> t<PERSON>, hogy bejegyzése olyan nyelvet tartalmaz, amely nem felel meg a {guidelinesLink}. Igyekszünk mindenki számára biztonságos helyet bi<PERSON>tani. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, módosíts<PERSON> a bevitelt, és próbálja újra.", "app.containers.Comments.publicDiscussion": "Nyilvános vita", "app.containers.Comments.publishComment": "<PERSON><PERSON><PERSON> közzé megjegyzését", "app.containers.Comments.reportAsSpamModalTitle": "<PERSON><PERSON><PERSON> akarod ezt spamként jelenteni?", "app.containers.Comments.saveComment": "Megtakarítás", "app.containers.Comments.signInLinkText": "jelentkezz be", "app.containers.Comments.signInToComment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {signInLink} kommentáljon.", "app.containers.Comments.signUpLinkText": "iratkozz fel", "app.containers.Comments.verifyIdentityLinkText": "Igazolja személyazonosságát", "app.containers.Comments.visibleToUsersPlaceholder": "Ez a megjegyzés a normál felhasználók számára látható", "app.containers.Comments.visibleToUsersWarning": "Az itt közzétett megjegyzéseket a rendszeres felhasználók láthatják.", "app.containers.ContentBuilder.PageTitle": "Projekt leírása", "app.containers.CookiePolicy.advertisingContent": "A hirdetési cookie-k segítségével személyre szabható és mérhető a külső marketingkampányok ezen platformmal való kapcsolattartása eredményessége. Ezen a platformon nem jelenítünk meg hirdetéseket, de a meglátogatott oldalak alapján személyre szabott hirdetéseket kaphat.", "app.containers.CookiePolicy.advertisingTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.analyticsContents": "Az analitikai cookie-k nyomon követik a látogatók viselkedését, p<PERSON><PERSON><PERSON><PERSON> azt, hogy mely oldalakat keresik fel és mennyi ideig. Ezenkívül bizonyos technikai adatokat is g<PERSON><PERSON>j<PERSON>tnek, bele<PERSON><PERSON><PERSON> a böngészőadatokat, a hozzávetőleges helyet és az IP-címeket. Ezeket az adatokat csak belső használatra használjuk fel, hogy tov<PERSON><PERSON>ra is javítsuk az általános felhasználói élményt és a platform működését. Az ilyen adatok a Go Vocal és a {orgName} között is megoszthatók, hogy értékeljék és javítsák a platformon lévő projektekkel való együttműködést. Vegye figyelembe, hogy az adatok névtelenek és összesített szinten kerülnek felhasználásra – nem azonosítj<PERSON> sze<PERSON>esen. Lehetséges azonban, hogy ha ezeket az adatokat más adatforrásokkal kombinálnák, akkor előfordulhat ilyen azonosítás.", "app.containers.CookiePolicy.analyticsTitle": "Analitikai cookie-k", "app.containers.CookiePolicy.cookiePolicyDescription": "R<PERSON><PERSON><PERSON> magyarázat a<PERSON>, ho<PERSON>an <PERSON> a cookie-kat ezen a platformon", "app.containers.CookiePolicy.cookiePolicyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.essentialContent": "Egyes cookie-k elengedhetetlenek a platform megfelelő működéséhez. Ezeket az alapvető sütiket elsősorban a fiók hitelesítésére has<PERSON>, amikor fel<PERSON> a platformot, és elmentik az Ön által választott nyelvet.", "app.containers.CookiePolicy.essentialTitle": "Nélkülözhetetlen sütik", "app.containers.CookiePolicy.externalContent": "<PERSON><PERSON><PERSON> küls<PERSON> szolgáltatóktól, például YouTube-tól vagy Typeform-tól származó tartalmat jeleníthetnek meg. Ezekre a harmadik féltől származó cookie-kra nincs be<PERSON>, és az ezektől a külső szolgáltatóktól származó tartalom megtekintése azt is eredményezheti, hogy cookie-k telepíthetők az Ön készülékére.", "app.containers.CookiePolicy.externalTitle": "Külső sütik", "app.containers.CookiePolicy.functionalContents": "A funkcionális cookie-k engedélyezhetők a látogatók számára, hogy értesítéseket kapjanak a frissítésekről, és közvetlenül a platformról hozzáférjenek a támogatási csatornákhoz.", "app.containers.CookiePolicy.functionalTitle": "Funkcionális sütik", "app.containers.CookiePolicy.headCookiePolicyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> | {orgName}", "app.containers.CookiePolicy.intro": "A sütik olyan szöveges fájlok, amely<PERSON> a böngészőben vagy az Ön számítógépének vagy mobileszközének merevlemezén tárolódnak, amikor Ön egy weboldalt látogat, és amelyekre a webhely hivatkozhat a későbbi látogatások során. Cookie-kat használunk annak megértésére, hogy a látogatók hogyan használják ezt a platformot, hogy javítsuk annak kialakítását és élményét, emlékezzünk az Ön preferenciáira (például a választott nyelvre), valamint hogy támogassuk a regisztrált felhasználók és a platform adminisztrátorai számára a legfontosabb funkciókat.", "app.containers.CookiePolicy.manageCookiesDescription": "A cookie-beállításokban bármikor engedélyezheti vagy letilthatja az analitikai, marketing és funkcionális sütiket. Az internetböngészőjén keresztül manuálisan vagy automatikusan törölheti a meglévő sütiket. A cookie-k azonban az Ön hozzájárulását követően újra elhelyezhetők a platformon tett további látogatások során. Ha nem törli a cookie-kat, akkor a cookie-beállításait 60 napig tároljuk, ezut<PERSON> újra beleegyezését kérik.", "app.containers.CookiePolicy.manageCookiesPreferences": "Nyissa meg a {manageCookiesPreferencesButtonText} oldalt, hogy megtekinthesse az ezen a platformon használt harmadik féltől származó integrációk teljes listáj<PERSON>t, és kezelje beállításait.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "cookie-be<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.manageCookiesTitle": "A cookie-k kezelése", "app.containers.CookiePolicy.viewPreferencesButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.viewPreferencesText": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az alábbi cookie-kategóriák nem vonatkoznak minden látogatóra vagy platformra; Tekintse meg a {viewPreferencesButton} -t az <PERSON><PERSON><PERSON> vonat<PERSON>ó harmadik féltől származó integrációk teljes listájáért.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Mire használjuk a sütiket?", "app.containers.CustomPageShow.editPage": "Oldal szerkesztése", "app.containers.CustomPageShow.goBack": "<PERSON><PERSON>", "app.containers.CustomPageShow.notFound": "<PERSON>z oldal nem található", "app.containers.DisabledAccount.bottomText": "<PERSON><PERSON>ra be<PERSON>zhet a {date}címről.", "app.containers.DisabledAccount.termsAndConditions": "szerződés<PERSON>", "app.containers.DisabledAccount.text2": "A {orgName} részvételi platformon lévő fiókodat ideiglenesen letiltottuk a közösségi irányelvek megsértése miatt. További információért tekintse meg a {TermsAndConditions}oldalt.", "app.containers.DisabledAccount.title": "Fiókját ideiglenesen letiltottuk", "app.containers.EventsShow.addToCalendar": "Hozzáadás a naptárhoz", "app.containers.EventsShow.editEvent": "Esemény szerkesztése", "app.containers.EventsShow.emailSharingBody2": "Vegyen részt ezen az eseményen: {eventTitle}. További információ: {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Esemény dátuma és időpontja", "app.containers.EventsShow.eventFrom2": "innen: \"{projectTitle}\"", "app.containers.EventsShow.goBack": "<PERSON><PERSON>", "app.containers.EventsShow.goToProject": "Menj a projekthez", "app.containers.EventsShow.haveRegistered": "regisztráltak", "app.containers.EventsShow.icsError": "Hiba az ICS-fájl letöltése során", "app.containers.EventsShow.linkToOnlineEvent": "Link az online eseményhez", "app.containers.EventsShow.locationIconAltText": "Elhelyezkedés", "app.containers.EventsShow.metaTitle": "Esemény: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Online találkozó", "app.containers.EventsShow.onlineLinkIconAltText": "Online találkozó linkje", "app.containers.EventsShow.registered": "bejegyzett", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 regisztrál<PERSON>} one {1 regisztráló} other {# regisztrálók}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} regisztr<PERSON><PERSON><PERSON>k", "app.containers.EventsShow.registrantsIconAltText": "Regisztráltak", "app.containers.EventsShow.socialMediaSharingMessage": "Vegyen részt ezen az eseményen: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# résztvevő} other {# résztvevő}}", "app.containers.EventsViewer.allTime": "Minden alkalommal", "app.containers.EventsViewer.date": "<PERSON><PERSON><PERSON>", "app.containers.EventsViewer.thisMonth2": "Következő hónap", "app.containers.EventsViewer.thisWeek2": "Következő hét", "app.containers.EventsViewer.today": "Ma", "app.containers.IdeaButton.addAContribution": "Hozzáj<PERSON><PERSON><PERSON><PERSON> hozz<PERSON>", "app.containers.IdeaButton.addAPetition": "<PERSON><PERSON> ho<PERSON>", "app.containers.IdeaButton.addAProject": "Adjon hozzá egy projektet", "app.containers.IdeaButton.addAProposal": "Adjon hozzá egy javaslatot", "app.containers.IdeaButton.addAQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAnInitiative": "Kezdeményezés hozzáadása", "app.containers.IdeaButton.addAnOption": "Adjon hozzá egy lehetőséget", "app.containers.IdeaButton.postingDisabled": "Új beadványokat jelenleg nem fogadunk el", "app.containers.IdeaButton.postingInNonActivePhases": "Új beadványok csak az aktív fázisokban adhatók hozzá.", "app.containers.IdeaButton.postingInactive": "Új pályaműveket jelenleg nem fogadunk el.", "app.containers.IdeaButton.postingLimitedMaxReached": "Ön már <PERSON> ezt a felmérést. Köszönöm válaszát!", "app.containers.IdeaButton.postingNoPermission": "Új beadványokat jelenleg nem fogadunk el", "app.containers.IdeaButton.postingNotYetPossible": "Új pályaműveket még nem fogadunk el.", "app.containers.IdeaButton.signInLinkText": "jelentkezz be", "app.containers.IdeaButton.signUpLinkText": "iratkozz fel", "app.containers.IdeaButton.submitAnIssue": "Hozzászólás küldése", "app.containers.IdeaButton.submitYourIdea": "<PERSON><PERSON><PERSON><PERSON> be <PERSON>tletét", "app.containers.IdeaButton.takeTheSurvey": "Vegye ki a felmérést", "app.containers.IdeaButton.verificationLinkText": "Igazolja személyazonosságát most.", "app.containers.IdeaCard.readMore": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {nincs <PERSON>} one {1 megjegyzés} other {# megjegyzés}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {nincs szavazat} one {1 szavazat} other {# szavazat}} / {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Zárja be a szűrőpanelt", "app.containers.IdeaCards.a11y_totalItems": "Összes hozzászólás: {ideasCount}", "app.containers.IdeaCards.all": "Minden", "app.containers.IdeaCards.allStatuses": "<PERSON><PERSON>", "app.containers.IdeaCards.contributions": "Hozzájáru<PERSON>ok", "app.containers.IdeaCards.ideaTerm": "Ötletek", "app.containers.IdeaCards.initiatives": "Kezdeményezések", "app.containers.IdeaCards.issueTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.list": "Lista", "app.containers.IdeaCards.map": "Térkép", "app.containers.IdeaCards.mostDiscussed": "A legtöbbet megvitatták", "app.containers.IdeaCards.newest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.noFilteredResults": "<PERSON><PERSON><PERSON> ta<PERSON>. Próbálkozzon másik szűrővel vagy keresési kifejezéssel.", "app.containers.IdeaCards.numberResults": "<PERSON><PERSON><PERSON><PERSON><PERSON> ({postCount})", "app.containers.IdeaCards.oldest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.optionTerm": "Opciók", "app.containers.IdeaCards.petitions": "Petíciók", "app.containers.IdeaCards.popular": "A legtöbben s<PERSON>ztak", "app.containers.IdeaCards.projectFilterTitle": "Projektek", "app.containers.IdeaCards.projectTerm": "Projektek", "app.containers.IdeaCards.proposals": "Javaslatok", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "Véletlen", "app.containers.IdeaCards.resetFilters": "Szűrők visszaállítása", "app.containers.IdeaCards.showXResults": "{ideasCount, plural, one {# ered<PERSON>ny} other {# tal<PERSON><PERSON> me<PERSON>}}", "app.containers.IdeaCards.sortTitle": "Osztályozás", "app.containers.IdeaCards.statusTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.statusesTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.topics": "Címkék", "app.containers.IdeaCards.topicsTitle": "Címkék", "app.containers.IdeaCards.trending": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.tryDifferentFilters": "<PERSON><PERSON><PERSON> ta<PERSON>. Próbálkozzon másik szűrővel vagy keresési kifejezéssel.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} megjegyzések} one {{ideasCount} megjegyzés} other {{ideasCount} megjegyzés}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} hozzáj<PERSON>rulás} one {{ideasCount} hozz<PERSON>j<PERSON><PERSON><PERSON><PERSON>} other {{ideasCount} hozzáj<PERSON>ru<PERSON><PERSON>}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ötletek} one {{ideasCount} ötlet} other {{ideasCount} ötletek}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} kezde<PERSON><PERSON><PERSON><PERSON><PERSON>} one {{ideasCount} kezde<PERSON>nyez<PERSON>} other {{ideasCount} kezde<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} opciók} one {{ideasCount} opció} other {{ideasCount} opciók}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} pet<PERSON><PERSON><PERSON><PERSON>} one {{ideasCount} pet<PERSON><PERSON><PERSON>} other {{ideasCount} pet<PERSON><PERSON><PERSON><PERSON>}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projektek} one {{ideasCount} projekt} other {{ideasCount} projektek}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} javaslatok} one {{ideasCount} javaslat} other {{ideasCount} javaslatok}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} k<PERSON><PERSON><PERSON><PERSON>} one {{ideasCount} kérd<PERSON>} other {{ideasCount} kérd<PERSON>}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# ered<PERSON>ny} other {# tal<PERSON><PERSON>}}", "app.containers.IdeasEditPage.contributionFormTitle": "Hozzájá<PERSON><PERSON><PERSON> szerkesztése", "app.containers.IdeasEditPage.editedPostSave": "Megtakarítás", "app.containers.IdeasEditPage.fileUploadError": "<PERSON>gy vagy több fájl feltöltése nem sikerült. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, el<PERSON><PERSON><PERSON><PERSON> a fájl méretét és formátumát, majd prób<PERSON><PERSON>.", "app.containers.IdeasEditPage.formTitle": "Ötlet szerkesztése", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Szerkessze a bejegyzést. Új adatok hozzáadása és régi adatok módosítása.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Szerkesztés {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Kezdeményezés szerkesztése", "app.containers.IdeasEditPage.issueFormTitle": "Megjegyzés szerkesztése", "app.containers.IdeasEditPage.optionFormTitle": "Szerkesztési lehetőség", "app.containers.IdeasEditPage.petitionFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.projectFormTitle": "Projekt szerkesztése", "app.containers.IdeasEditPage.proposalFormTitle": "Javaslat szerkesztése", "app.containers.IdeasEditPage.questionFormTitle": "Kérdés szerkesztése", "app.containers.IdeasEditPage.save": "Megtakarítás", "app.containers.IdeasEditPage.submitApiError": "Hiba történt az űrlap elküldésekor. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ellenőrizze az esetleges hibákat, és próbálja újra.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Minden bemenet fel<PERSON>ü<PERSON>", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Fedezze fel a {orgName}részvételi platformján kö<PERSON>étett összes bemenetet.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Hozzászólások | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Hozzászólások", "app.containers.IdeasIndexPage.loadMore": "Továbbiak betöltése...", "app.containers.IdeasIndexPage.loading": "Terhelés...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Alapértelmezés szerint beküldései a profilodhoz lesznek társítva, hacsak nem választod ezt a lehetőséget.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Anonim k<PERSON>", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "<PERSON><PERSON>", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Ez a felmérés jelenleg nem nyitott megválaszolásra. Kérjük, térjen vissza a projekthez további információkért.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Ez a felmérés jelenleg nem aktív.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Vissza a projekthez", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "<PERSON><PERSON> már <PERSON> ezt a felmérést.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Fe<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Köszönöm válaszát!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "A hozzájárulás leír<PERSON>ak {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "<PERSON>z ötlettörzsnek {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "A hozzájárulás cí<PERSON>k {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "A hozzájárulás cí<PERSON>k {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> ki legalább egy támogatót", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "<PERSON>z ö<PERSON>t leí<PERSON> {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "<PERSON>z ö<PERSON> le<PERSON> {limit} karakternél ho<PERSON>zabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "<PERSON>z ötlet cí<PERSON>nek {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "<PERSON>z ö<PERSON>t cí<PERSON>k {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "A kezdeményezés leírásának {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "A kezdeményezés leírásának {limit} karak<PERSON>él ho<PERSON>zabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "A kezdeményezés címének {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "A kezdeményezés címének {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "A probléma leí<PERSON>ak {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "A probléma le<PERSON> {limit} karak<PERSON><PERSON>l hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "A kiadás címének {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "A kiadás címének {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_number_required": "A mező kitöltése kötelező, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy érvényes számot", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "<PERSON>z opció leír<PERSON> {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "<PERSON>z opció leír<PERSON> {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "<PERSON>z opció cí<PERSON>k {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "<PERSON>z opció cí<PERSON>k {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> legalább egy cím<PERSON>t", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "A petíció leí<PERSON>ak {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "A petíció leí<PERSON>ak {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "A petíció c<PERSON> {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "A petíció c<PERSON> {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "A projekt leírásának {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "A projekt leírás<PERSON>ak {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "A projekt címének {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "A projekt címének {limit} karak<PERSON><PERSON>l hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "<PERSON><PERSON> a<PERSON> {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "<PERSON><PERSON> a<PERSON> {limit} karak<PERSON><PERSON>l ho<PERSON>zabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "<PERSON><PERSON> a<PERSON> c<PERSON> {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "<PERSON><PERSON> a<PERSON> c<PERSON> {limit} karak<PERSON><PERSON>l ho<PERSON>zabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy s<PERSON>", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy s<PERSON>", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "A kérdés leírásának {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "A kérdés leírásának {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "A kérdés címének {limit} karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "A kérdés címének {limit} karakternél hosszabbnak kell lennie", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy címet", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "A hozzájárulás leírásának 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "A hozzájárulás leírásának legalább 30 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "A hozzászólás címének 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "A hozzászólás címének legalább 10 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Az ötlet leírásának 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Az ötlet leírásának legalább 30 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy címet", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Az ötlet címének 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Az ötlet címének legalább 10 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_includes_banned_words": "<PERSON><PERSON><PERSON>, hogy egy vagy több s<PERSON><PERSON><PERSON>, amelyeket {guidelinesLink}trágárságnak minősít. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, módosítsa a <PERSON>zöveget, hogy távolítsa el az esetlegesen előforduló káromkodásokat.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "A kezdeményezés leírásának 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "A kezdeményezés leírásának legalább 30 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "A kezdeményezés címének 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "A kezdeményezés címének legalább 10 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "A probléma leírásának 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "A probléma leírásának legalább 30 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "A kiadás címének 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "A kiadás címének legalább 10 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Az opció leírásának 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Az opció leírásának legalább 30 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Az opció címének 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Az opció címének legalább 10 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "A petíció leírásának 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "A petíció leírásának legalább 30 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "A petíció címének 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "A petíció címének legalább 10 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "A projekt leírásának 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "A projekt leírásának legalább 30 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "A projekt címének 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "A projekt címének legalább 10 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "Az ajánlat leírásának 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "Az ajánlat leírásának legalább 30 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Az ajánlat címének 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Az ajánlat címének legalább 10 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "A kérdés leírásának 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "A kérdés leírásának legalább 30 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "A kérdés címének 80 karakternél rövidebbnek kell lennie", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "A kérdés címének legalább 10 karakter hosszúnak kell lennie", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "M<PERSON>gs<PERSON>", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON>, el akarok menni", "app.containers.IdeasNewPage.contributionMetaTitle1": "Új hozzájárulás hozzáadása a projekthez | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Kérdőív szerkesztése", "app.containers.IdeasNewPage.ideaNewMetaDescription": "<PERSON>gyen közzé egy be<PERSON>, és csatlakozzon a beszélgetéshez {orgName}részvételi platformján.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Új ötlet hozzáadása a projekthez | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Új kezdeményezés hozzáadása a projekthez | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Új szám hozzáadása a projekthez | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Biztos, hogy el akarsz menni?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Válaszpiszkozatait privát mó<PERSON>, és később v<PERSON>zatérhet, hogy befejezze.", "app.containers.IdeasNewPage.leaveSurvey": "Hagyja el a felmérést", "app.containers.IdeasNewPage.leaveSurveyText": "A válaszaid nem kerülnek mentésre.", "app.containers.IdeasNewPage.optionMetaTitle1": "Új opció hozzáadása a projekthez | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Új petíció hozzáadása a projekthez | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Új projekt hozzáadása a projekthez | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Új javaslat hozzáadása a projekthez | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Új kérdés hozzáadása a projekthez | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Meghívás elfogadása", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Társszponzori felhívás", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Társszponzorok", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON><PERSON><PERSON>, hogy leg<PERSON><PERSON><PERSON> t<PERSON>.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Meghívás elfogadva", "app.containers.IdeasShow.Cosponsorship.pending": "függőben levő", "app.containers.IdeasShow.MetaInformation.attachments": "Mellékletek", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} a {date}-n", "app.containers.IdeasShow.MetaInformation.currentStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.location": "Elhelyezkedés", "app.containers.IdeasShow.MetaInformation.postedBy": "Írta:", "app.containers.IdeasShow.MetaInformation.similar": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.topics": "Címkék", "app.containers.IdeasShow.commentCTA": "Megjegyzés hozzáadása", "app.containers.IdeasShow.contributionEmailSharingBody": "Támogassa ezt a hozzájárulást '{postTitle}' a {postUrl}címen!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Támogassa ezt a hozzájárulást: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Köszönjük hozzájárulását!", "app.containers.IdeasShow.contributionTwitterMessage": "Támogassa ezt a hozzájárulást: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Támogassa ezt a hozzájárulást: {postTitle}", "app.containers.IdeasShow.currentStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.deletedUser": "ismeretlen szerző", "app.containers.IdeasShow.ideaEmailSharingBody": "Támogassa az ötletemet '{ideaTitle}' a {ideaUrl}helyen!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Támogassa az ötletemet: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Támogassa ezt az ötletet: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Támogassa ezt az ötletet: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Támogassa ezt a megjegyzést: {postTitle}", "app.containers.IdeasShow.imported": "Import<PERSON>lt", "app.containers.IdeasShow.importedTooltip": "Ezt a {inputTerm} -t offline gyűjtöttük, és automatikusan feltöltöttük a platformra.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Támogassa ezt a kezdeményezést '{ideaTitle}' a {ideaUrl}címen!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Támogassa ezt a kezdeményezést: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Köszönjük kezdeményezését!", "app.containers.IdeasShow.initiativeTwitterMessage": "Támogassa ezt a kezdeményezést: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Támogassa ezt a kezdeményezést: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Támogassa ezt a megjegyzést '{postTitle}' a {postUrl}címen!", "app.containers.IdeasShow.issueEmailSharingSubject": "Támogassa ezt a megjegyzést: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Köszönjük észrevételét!", "app.containers.IdeasShow.issueTwitterMessage": "Támogassa ezt a megjegyzést: {postTitle}", "app.containers.IdeasShow.metaTitle": "Bemenet: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Támogassa ezt a '{postTitle}' opciót a {postUrl}helyen!", "app.containers.IdeasShow.optionEmailSharingSubject": "Támogassa ezt az opciót: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Az Ön opciója sikeresen elküldve!", "app.containers.IdeasShow.optionTwitterMessage": "Támogassa ezt az opciót: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Támogassa ezt az opciót: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Támogassa ezt a „{ideaTitle}” petíciót a {ideaUrl}címen!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Támogassa ezt a petíciót: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy benyújtotta petícióját!", "app.containers.IdeasShow.petitionTwitterMessage": "Támogassa ezt a petíciót: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Támogassa ezt a petíciót: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Támogassa ezt a projektet '{postTitle}' a {postUrl}címen!", "app.containers.IdeasShow.projectEmailSharingSubject": "Támogassa ezt a projektet: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy benyújtotta projektjét!", "app.containers.IdeasShow.projectTwitterMessage": "Támogassa ezt a projektet: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Támogassa ezt a projektet: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Támogassa ezt a javaslatot '{ideaTitle}' a {ideaUrl}helyen!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Támogassa ezt a javaslatot: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy benyújtotta a<PERSON>ánlatát!", "app.containers.IdeasShow.proposalTwitterMessage": "Támogassa ezt a javaslatot: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Támogassa ezt a javaslatot: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Szavazásig hátralévő idő:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} {votingThreshold} szükséges szavazatból", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Szavazás v<PERSON>zavonása", "app.containers.IdeasShow.proposals.VoteControl.days": "napokon", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "irányelveinket", "app.containers.IdeasShow.proposals.VoteControl.hours": "<PERSON>ra", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Stá<PERSON>z és s<PERSON>zatok", "app.containers.IdeasShow.proposals.VoteControl.minutes": "perc", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.vote": "Szavazás", "app.containers.IdeasShow.proposals.VoteControl.voted": "Sza<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.votedText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, amikor ez a kezdeményezés a következő lépésre lép. {x, plural, =0 {{xDays} maradt.} one {{xDays} maradt.} other {{xDays} maradt.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Szavazatát leadtuk!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Sajnos erről a javaslatról nem lehet szavazni. Olvassa el, miért: {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {<PERSON><PERSON><PERSON><PERSON> mint egy nap} one {egy nap} other {# nap}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {nincs szavazat} one {1 szavazat} other {# szavazat}}", "app.containers.IdeasShow.questionEmailSharingBody": "Csatlakozzon a '{postTitle}' kérdésről szóló vitához a {postUrl}címen!", "app.containers.IdeasShow.questionEmailSharingSubject": "Csatlakozz a beszélgetéshez: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "K<PERSON>rd<PERSON><PERSON>t si<PERSON>esen elküldtük!", "app.containers.IdeasShow.questionTwitterMessage": "Csatlakozz a beszélgetéshez: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Csatlakozz a beszélgetéshez: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "<PERSON><PERSON><PERSON> akarod ezt spamként jelenteni?", "app.containers.IdeasShow.share": "Részesedés", "app.containers.IdeasShow.sharingModalSubtitle": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> hall<PERSON>.", "app.containers.IdeasShow.sharingModalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy elküldte ötletét!", "app.containers.Navbar.completeOnboarding": "<PERSON><PERSON><PERSON>", "app.containers.Navbar.completeProfile": "<PERSON><PERSON><PERSON> profil", "app.containers.Navbar.confirmEmail2": "Erősítse meg az e-mailt", "app.containers.Navbar.unverified": "Ellenőrizetlen", "app.containers.Navbar.verified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.beforeYouFollow": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>éd", "app.containers.NewAuthModal.beforeYouParticipate": "Mielőtt részt vesz", "app.containers.NewAuthModal.completeYourProfile": "Töltse ki profilj<PERSON>t", "app.containers.NewAuthModal.confirmYourEmail": "Erősítse meg e-mail címét", "app.containers.NewAuthModal.logIn": "Jelentkezzen be", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "A folytatáshoz tekintse át az alábbi feltételeket.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, töltse ki profilj<PERSON>.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Térjen v<PERSON> a bejelentkezési lehetőségekhez", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "<PERSON><PERSON><PERSON>? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Regisztr<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "A kódnak 4 számjegyből kell állnia.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Folytassa a FranceConnect segítségével", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Ezen a platformon nincsenek engedélyezve hitelesítési módszerek.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "A folytatással beleegyezik, hogy e-maileket kapjon erről a platformról. A \"Saját beállítások\" oldalon kiválaszthatja, hogy mely e-maileket szeretné megkapni.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "Email", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Adjon meg egy e-mail címet a megfelelő formátumban, például név@szolgáltató.com", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Adjon meg egy e-mail címet", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "A folytatáshoz adja meg e-mail címét.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>tted a jelszavad?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Jelentkezzen be fiókjába: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg j<PERSON>", "app.containers.NewAuthModal.steps.Password.password": "Je<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.Password.rememberMe": "Emlékezz rám", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "<PERSON><PERSON> vá<PERSON>za, ha nyilvános számítógépet használ", "app.containers.NewAuthModal.steps.Success.allDone": "Minden kész", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Most folytassa a részvételt.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Személyazonosságát ellenőriztük. Mostantól a közösség teljes jogú tagja vagy ezen a platformon.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Most igazoltad!", "app.containers.NewAuthModal.steps.close": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.continue": "Folytatás", "app.containers.NewAuthModal.whatAreYouInterestedIn": "mi <PERSON><PERSON>?", "app.containers.NewAuthModal.youCantParticipate": "<PERSON>em ve<PERSON>z részt", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {nincsenek megtekintett értesítések} one {1 nem megtekintett értesítés} other {# nem megtekintett értesítés}}", "app.containers.NotificationMenu.adminRightsReceived": "Ön most a platform rendszergazdája", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "\"{postTitle}\"-hoz írt megjegyzését egy adminisztrá<PERSON> tö<PERSON>, mert\n      {reasonCode, select, irrelevant {nem releváns} inappropriate {tartalma nem meg<PERSON>l<PERSON>} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} elfogadta a társszponzori meghívást", "app.containers.NotificationMenu.deletedUser": "Ismeretlen szerző", "app.containers.NotificationMenu.error": "<PERSON><PERSON> betölteni az értesítéseket", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} be<PERSON><PERSON> megjegyzést fűzött egy Önhöz rendelt bemenethez", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} be<PERSON><PERSON> megjegyzést fűzött egy olyan be<PERSON>, amelyhez Ön belsőleg megjegyzést fűzött", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} belsőleg megjegyzést fűzött egy Ön által kezelt projekt bemenetéhez", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} be<PERSON><PERSON> megjegyzést fűzött egy nem felügyelt projekt hozzá nem rendelt bemenetéhez", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} megjegyzést fűzött a belső megjegyzésedhez", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} me<PERSON><PERSON><PERSON><PERSON> egy ho<PERSON>j<PERSON><PERSON><PERSON><PERSON> tá<PERSON>szponzorálására", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} meghívott egy ötlet társszponzorálás<PERSON>ra", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} me<PERSON><PERSON><PERSON><PERSON> egy kezdeményezés társszponzorálására", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} me<PERSON><PERSON><PERSON><PERSON> egy probléma tá<PERSON>szponzor<PERSON><PERSON>", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} me<PERSON><PERSON><PERSON><PERSON> egy lehetőség társszponzorálás<PERSON>ra", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} me<PERSON><PERSON><PERSON><PERSON> egy pet<PERSON> t<PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} meghívott egy projekt társszponzorá<PERSON>ására", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} me<PERSON><PERSON><PERSON><PERSON> egy javaslat tá<PERSON>szponzorá<PERSON>", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} me<PERSON><PERSON><PERSON><PERSON> egy kérd<PERSON> tá<PERSON>szponzorálás<PERSON>ra", "app.containers.NotificationMenu.loadMore": "Továbbiak betöltése...", "app.containers.NotificationMenu.loading": "Értesítések betöltése...", "app.containers.NotificationMenu.mentionInComment": "{name} megemlített egy megjegyzésben", "app.containers.NotificationMenu.mentionInInternalComment": "{name} megemlített egy belső megjegyzésben", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} megemlített téged egy hivatalos friss<PERSON>ben", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "<PERSON><PERSON> be a felmé<PERSON>st", "app.containers.NotificationMenu.noNotifications": "Még nincsenek <PERSON>sei", "app.containers.NotificationMenu.notificationsLabel": "Értesítések", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} hivat<PERSON>s frissíté<PERSON> adott az Ön által követett hozzászólásról", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} hivat<PERSON><PERSON> frissí<PERSON> adott egy általad követett ötletről", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} hivat<PERSON><PERSON> frissí<PERSON> adott egy általad követett kezdeményezésről", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "A {officialName} hivatalos frissí<PERSON>st adott egy Ön <PERSON>l követett problémáról", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "A {officialName} hivatalos frissítést adott az Ö<PERSON> á<PERSON>l követett opcióról", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} hivat<PERSON>s frissíté<PERSON> adott az Ön á<PERSON>l követett petícióról", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} hivat<PERSON><PERSON> frissí<PERSON> adott egy általad követett projektről", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} hivat<PERSON><PERSON> frissí<PERSON> adott az Ö<PERSON>l követett javaslatról", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} hivat<PERSON><PERSON> frissí<PERSON> adott egy általad követett kérdés<PERSON>ől", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} hozz<PERSON> lett rendelve", "app.containers.NotificationMenu.projectModerationRightsReceived": "Ön most a {projectLink}projektmenedzsere", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} <PERSON><PERSON>", "app.containers.NotificationMenu.projectPhaseUpcoming": "A {projectTitle} új f<PERSON> lép a {phaseStartAt}-n", "app.containers.NotificationMenu.projectPublished": "Egy új projektet tettek közzé", "app.containers.NotificationMenu.projectReviewRequest": "{name} jóváhagyást kért a \"{projectTitle}\" projekt közzétételéhez", "app.containers.NotificationMenu.projectReviewStateChange": "{name} jóváhagyta a \"{projectTitle}\" közzétételét", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "A {ideaTitle} állapota {status}érték<PERSON> v<PERSON>", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} elérte a s<PERSON>zati küszöböt", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} elfogadta a meghívást", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} megjegyzést fűzött egy Ön által követett hozzászóláshoz", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} megjegyzést fűzött egy Ön által követett ötlethez", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} megjegyzést fűzött egy Ön által követett kezdeményezéshez", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} megjegyzést fűzött egy Ön által követett problémához", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} megjegyzést fűzött egy Ön által követett opcióhoz", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} megjegyzést fűzött egy Ön által követett petícióhoz", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} megjegyzést fűzött egy Ön által követett projekthez", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} megjegyzést fűzött egy Ön által követett javaslathoz", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} megjegyzést fűzött egy Ön által követett kérdéshez", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} spamként jelentette: \"{postTitle}\"", "app.containers.NotificationMenu.userReactedToYourComment": "{name} reagált a megjegyzésedre", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} spamként jelentett egy megjegyzést a következőhöz: \"{postTitle}\"", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Nem adtad le a <PERSON>tai<PERSON>t", "app.containers.NotificationMenu.votingBasketSubmitted": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.votingLastChance": "U<PERSON><PERSON><PERSON> le<PERSON>őség s<PERSON>zni {phaseTitle}-ra", "app.containers.NotificationMenu.votingResults": "{phaseTitle} szavazás eredménye kiderült", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} {postTitle} hozz<PERSON><PERSON> rendelve", "app.containers.PasswordRecovery.emailError": "Ez nem tűnik érvényes e-mailnek", "app.containers.PasswordRecovery.emailLabel": "Email", "app.containers.PasswordRecovery.emailPlaceholder": "Az email címem", "app.containers.PasswordRecovery.helmetDescription": "Állítsa vissza a jelszóoldalt", "app.containers.PasswordRecovery.helmetTitle": "Állítsa v<PERSON>", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Ha ez az e-mail cím regisztrálva van a platformon, akkor a rendszer elküldte a jelszó-visszaállítási linket.", "app.containers.PasswordRecovery.resetPassword": "Jelszó-visszaállítási link küldése", "app.containers.PasswordRecovery.submitError": "Nem találtunk ehhez az e-mailhez kapcsolódó fiókot. Ehelyett megpróbálhatsz regisztrálni.", "app.containers.PasswordRecovery.subtitle": "Hova küldhetünk linket az új jelszó kiválasztásához?", "app.containers.PasswordRecovery.title": "Je<PERSON><PERSON><PERSON> v<PERSON>zaállítása", "app.containers.PasswordReset.helmetDescription": "Állítsa vissza a jelszóoldalt", "app.containers.PasswordReset.helmetTitle": "Állítsa v<PERSON>", "app.containers.PasswordReset.login": "Jelentkezzen be", "app.containers.PasswordReset.passwordError": "A jelszónak legalább 8 karakter hosszúnak kell lennie", "app.containers.PasswordReset.passwordLabel": "Je<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordPlaceholder": "<PERSON><PERSON>", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "<PERSON><PERSON><PERSON>va sikeresen frissítve.", "app.containers.PasswordReset.pleaseLogInMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jelentkezzen be <PERSON>j <PERSON>.", "app.containers.PasswordReset.requestNewPasswordReset": "<PERSON><PERSON><PERSON><PERSON><PERSON>visszaáll<PERSON>t<PERSON>t", "app.containers.PasswordReset.submitError": "Valami elromlott. K<PERSON><PERSON><PERSON><PERSON>k, próbá<PERSON>ja <PERSON>.", "app.containers.PasswordReset.title": "Állítsa v<PERSON>", "app.containers.PasswordReset.updatePassword": "Erősítse meg az új j<PERSON>zó<PERSON>", "app.containers.ProjectFolderCards.allProjects": "Minden projekt", "app.containers.ProjectFolderCards.currentlyWorkingOn": "A {orgName} jelenleg a következőn dolgozik", "app.containers.ProjectFolderShowPage.editFolder": "Mappa szerkesztése", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Információ a projektről", "app.containers.ProjectFolderShowPage.metaTitle1": "Mappa: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON><PERSON><PERSON>", "app.containers.ProjectFolderShowPage.share": "Részesedés", "app.containers.Projects.PollForm.document": "Dokumentum", "app.containers.Projects.PollForm.formCompleted": "Köszönöm! Válaszát megkaptuk.", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "<PERSON><PERSON><PERSON> ezt a szavazást.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Ez a szavazás csak akkor végezhető el, ha ez a fázis aktív.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Ez a szavazás jelenleg nincs engedélyezve", "app.containers.Projects.PollForm.pollDisabledNotPossible": "<PERSON><PERSON><PERSON> nem lehet rés<PERSON>t venni ezen a s<PERSON>vazáson.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "A szavazás már nem érhető el, mivel ez a projekt már nem aktív.", "app.containers.Projects.PollForm.sendAnswer": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.a11y_phase": "{phaseNumber}fázis: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.a11y_titleInputs": "A projekthez benyújtott összes bemenet", "app.containers.Projects.a11y_titleInputsPhase": "<PERSON><PERSON> ebben a fá<PERSON>sban benyújtott összes bemenet", "app.containers.Projects.accessRights": "Hozzáférési j<PERSON>", "app.containers.Projects.addedToBasket": "Hozzáadva a kosárhoz", "app.containers.Projects.allocateBudget": "Ossza ki a költségvetését", "app.containers.Projects.archived": "Archivált", "app.containers.Projects.basketSubmitted": "A kosár beküldve!", "app.containers.Projects.contributions": "Hozzájáru<PERSON>ok", "app.containers.Projects.createANewPhase": "Hozzon létre egy új s<PERSON>", "app.containers.Projects.currentPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.document": "Dokumentum", "app.containers.Projects.editProject": "Projekt szerkesztése", "app.containers.Projects.emailSharingBody": "Mi a véleményed erről a kezdeményezésről? Szavazz rá, és oszd meg a beszélgetést a {initiativeUrl} címen, hogy hallasd a hangod!", "app.containers.Projects.emailSharingSubject": "Támogasd kezdeményezésemet: {initiativeTitle}.", "app.containers.Projects.endedOn": "{date}-n ért véget", "app.containers.Projects.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.header": "Projektek", "app.containers.Projects.ideas": "Ötletek", "app.containers.Projects.information": "Információ", "app.containers.Projects.initiatives": "Kezdeményezések", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Tekintse át a dokumentumot", "app.containers.Projects.invisibleTitlePhaseAbout": "Erről a fázisról", "app.containers.Projects.invisibleTitlePoll": "Vegye ki a szavazást", "app.containers.Projects.invisibleTitleSurvey": "Vegye ki a felmérést", "app.containers.Projects.issues": "Megjegyzések", "app.containers.Projects.liveDataMessage": "Valós idejű adatokat tekint meg. A résztvevők száma folyamatosan frissül az adminisztrátorok számára. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ve<PERSON><PERSON>, hogy a rendszeres felhasználók gyorsítótárazott adatokat látnak, ami enyhe eltéréseket eredményezhet a számokban.", "app.containers.Projects.location": "Elhelyezkedés:", "app.containers.Projects.manageBasket": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.meetMinBudgetRequirement": "A kosár elküldéséhez teljesítse a minimális költségvetést.", "app.containers.Projects.meetMinSelectionRequirement": "A kosár elküldéséhez teljesítse a minimális költségvetést.", "app.containers.Projects.metaTitle1": "Projekt: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Minimális költségvetés szükséges", "app.containers.Projects.myBasket": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.navPoll": "Szavazás", "app.containers.Projects.navSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.newPhase": "<PERSON><PERSON>", "app.containers.Projects.nextPhase": "Következő fázis", "app.containers.Projects.noEndDate": "<PERSON><PERSON><PERSON> d<PERSON>", "app.containers.Projects.noItems": "Még nem választott ki elemeket", "app.containers.Projects.noPastEvents": "Nincsenek megjeleníthető múltbeli események", "app.containers.Projects.noPhaseSelected": "<PERSON><PERSON><PERSON> k<PERSON>lasztva fázis", "app.containers.Projects.noUpcomingOrOngoingEvents": "Jelenleg nincsenek betervezve közelgő vagy folyamatban lévő események.", "app.containers.Projects.offlineVotersTooltip": "Ez a szám nem tükrözi az offline szavazók számát.", "app.containers.Projects.options": "Opciók", "app.containers.Projects.participants": "Résztvevők", "app.containers.Projects.participantsTooltip4": "Ez a szám az anonim felméréseket is tükr<PERSON>zi. Anonim felmérések beküldése akkor lehetséges, ha a felmérések mindenki számára nyitottak (lásd a projekt {accessRightsLink} lapját).", "app.containers.Projects.pastEvents": "Múltbeli <PERSON>", "app.containers.Projects.petitions": "Petíciók", "app.containers.Projects.phases": "Fázisok", "app.containers.Projects.previousPhase": "Előző fázis", "app.containers.Projects.project": "Projekt", "app.containers.Projects.projectTwitterMessage": "Hallasd a hangod! Vegyen részt a {projectName} | {orgName}", "app.containers.Projects.projects": "Projektek", "app.containers.Projects.proposals": "Javaslatok", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.readMore": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.removeItem": "<PERSON><PERSON>", "app.containers.Projects.requiredSelection": "Kötelező választás", "app.containers.Projects.reviewDocument": "Tekintse át a dokumentumot", "app.containers.Projects.seeTheContributions": "Lásd a hozzájárulásokat", "app.containers.Projects.seeTheIdeas": "Lásd az ötleteket", "app.containers.Projects.seeTheInitiatives": "Lásd a kezdeményezéseket", "app.containers.Projects.seeTheIssues": "Lásd a megjegyzéseket", "app.containers.Projects.seeTheOptions": "Lásd a lehetőségeket", "app.containers.Projects.seeThePetitions": "Lásd a petíciókat", "app.containers.Projects.seeTheProjects": "Lásd a projekteket", "app.containers.Projects.seeTheProposals": "Lásd a javaslatokat", "app.containers.Projects.seeTheQuestions": "Lásd a kérdéseket", "app.containers.Projects.seeUpcomingEvents": "Tekintse meg a közelgő eseményeket", "app.containers.Projects.share": "Részesedés", "app.containers.Projects.shareThisProject": "Oszd meg ezt a projektet", "app.containers.Projects.submitMyBasket": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.survey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.takeThePoll": "Vegye ki a szavazást", "app.containers.Projects.takeTheSurvey": "Vegye ki a felmérést", "app.containers.Projects.timeline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.upcomingAndOngoingEvents": "Közelgő és folyamatban lévő események", "app.containers.Projects.upcomingEvents": "Közelgő események", "app.containers.Projects.whatsAppMessage": "{projectName} | a {orgName}részvételi platformjáról", "app.containers.Projects.yourBudget": "Teljes költségvetés", "app.containers.ProjectsIndexPage.metaDescription": "Fedezze fel a {orgName} összes folyamatban lévő projektjét, hogy me<PERSON>, hogyan vehet részt.\n <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, és beszélje meg az Ön számára legfontosabb helyi projekteket.", "app.containers.ProjectsIndexPage.metaTitle1": "Projektek | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projektek", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "szeretnék részt venni", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> {signInLink} vag<PERSON> {signUpLink} , hogy önkéntesként jelentkezzen erre a tevékenységre", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "<PERSON><PERSON><PERSON> nem lehet részt venni ebben a tevékenységben.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "jelentkezz be", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "iratkozz fel", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Visszavonom az önkéntességre vonatkozó aján<PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {nincs résztvevő} one {# résztvevő} other {# résztvevő}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Figyelmeztetés: A beágyazott felmérés akadálymentesítési problémákat okozhat a képernyőolvasót használó felhasználók számára. Ha bármilyen kihívást tapasztal, forduljon a platform adminisztrátorához, hogy megkapja a kérdőív linkjét az eredeti platformról. Alternatív megoldásként más módokat is kérhet a kérdőív kitöltésére.", "app.containers.ProjectsShowPage.process.survey.survey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Ha szeretné tud<PERSON>, hogy r<PERSON>t vehet-e ebben a fel<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> {logInLink} lépjen be a platformra.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Ez a felmérés csak akkor tölthető ki, ha az idővonal ezen szakasza aktív.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {completeRegistrationLink} töltse ki a felmérést.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Ez a felmérés jelenleg nincs engedélyezve", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "A felmérés kitöltéséhez igazolnia kell személyazonosságát. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "A felmérés már nem érhető el, mivel ez a projekt már nem aktív.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "teljes regis<PERSON>", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "jelentkezz be", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "iratkozz fel", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Igazolja fiókját most.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Csak bizonyos felhasználók tekinthetik meg ezt a dokumentumot. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, el<PERSON>sz<PERSON>r {signUpLink} vagy {logInLink} .", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Ez a dokumentum csak akkor tekinthető át, ha ez a fázis aktív.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {completeRegistrationLink} nézze át a dokumentumot.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Sajnos nincs jogo<PERSON>tsága a dokumentum áttekintésére.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Ennek a dokumentumnak az áttekintéséhez igazolnia kell fiókját. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "A dokumentum már nem érhető el, mivel ez a projekt már nem aktív.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(1 offline is)} other {(# offline is)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 pick} other {# pick}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "Azon résztvevők százalékos aránya, akik ezt a lehetőséget választották.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "Az összes szavazat százalékos aránya, amelyet ez a lehetőség kapott.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Költség:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "<PERSON><PERSON><PERSON>", "app.containers.ReactionControl.a11y_likesDislikes": "Összes kedvelés: {likesCount}, összes nemtetszés: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "<PERSON><PERSON><PERSON>n törölted a nemtetszésedet ehhez a bemenethez.", "app.containers.ReactionControl.cancelLikeSuccess": "<PERSON><PERSON><PERSON>n törölted a tetszésnyilvánítást ehhez a bevitelhez.", "app.containers.ReactionControl.dislikeSuccess": "<PERSON><PERSON>esen nem tetszett a bemenet.", "app.containers.ReactionControl.likeSuccess": "Sikeresen tetszett Önnek ez a bemenet.", "app.containers.ReactionControl.reactionErrorSubTitle": "Egy hiba miatt a reakcióját nem sikerült regisztrálni. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, próbá<PERSON>ja újra néhány perc múlva.", "app.containers.ReactionControl.reactionSuccessTitle": "Reakcióját sikeresen regisztráltuk!", "app.containers.ReactionControl.vote": "Szavazás", "app.containers.ReactionControl.voted": "Sza<PERSON><PERSON><PERSON>", "app.containers.SearchInput.a11y_cancelledPostingComment": "A megjegyzés közzététele visszavonva.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} megjegyzések betöltve.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# események betöltve} one {# esemény betöltve} other {# események betöltve}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# találat betöltve} one {# ered<PERSON>ny betöltve} other {# találat betöltve}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# keresési eredmény betöltődött} one {# keresési eredmény betöltődött} other {# keresési eredmény betöltődött}}.", "app.containers.SearchInput.removeSearchTerm": "Keresési kifejezés eltávolítása", "app.containers.SearchInput.searchAriaLabel": "Keresés", "app.containers.SearchInput.searchLabel": "Keresés", "app.containers.SearchInput.searchPlaceholder": "Keresés", "app.containers.SearchInput.searchTerm": "<PERSON><PERSON>ett k<PERSON>: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "A FranceConnect a francia állam által javasolt megoldás a több mint 700 online szolgáltatásra történő regisztráció biztosítására és egyszerűsítésére.", "app.containers.SignIn.or": "<PERSON><PERSON><PERSON>", "app.containers.SignIn.signInError": "A megadott információ nem helytálló. Kattintson az \"Elfelejtette a jelszavát?\" j<PERSON><PERSON><PERSON> visszaállításához.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "A FranceConnect segítségével <PERSON>, regisztr<PERSON>l<PERSON> vagy igazolhatja fiókj<PERSON>.", "app.containers.SignIn.whatIsFranceConnect": "Mi az a France Connect?", "app.containers.SignUp.adminOptions2": "Adminoknak és projektmenedzsereknek", "app.containers.SignUp.backToSignUpOptions": "Menjen v<PERSON> a regisztrációs lehe<PERSON>őségekhez", "app.containers.SignUp.continue": "Folytatás", "app.containers.SignUp.emailConsent": "A regisztrációval beleegyezik, hogy e-maileket kapjon erről a platformról. A \"Saját beállítások\" oldalon kiválaszthatja, hogy mely e-maileket szeretné megkapni.", "app.containers.SignUp.emptyFirstNameError": "<PERSON><PERSON><PERSON> be a keresztnevét", "app.containers.SignUp.emptyLastNameError": "<PERSON><PERSON><PERSON> be vezetéknevét", "app.containers.SignUp.firstNamesLabel": "Keresztnév", "app.containers.SignUp.goToLogIn": "<PERSON><PERSON><PERSON>? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "Elolvastam és elfogadom a következőt: {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "Elolvastam és elfogadom a következőt: {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "<PERSON><PERSON><PERSON><PERSON>, hogy az adatokat a mitgestalten.wien.gv.at oldalon használjuk fel. További információ: {link}.", "app.containers.SignUp.invitationErrorText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON> m<PERSON><PERSON>. Ha már hasz<PERSON>lta a meghívó linket fiók létrehozásához, próbáljon meg bejelentkezni. Ellenkező esetben regisztráljon új fiók létrehozásához.", "app.containers.SignUp.lastNameLabel": "Vezetéknév", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Kövesse a fókuszterületeit, hogy értesítést kapjon róluk:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Kö<PERSON>se <PERSON>, hogy értesítést kapjon róluk:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Mentse el a beállításokat", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Most hagyd ki", "app.containers.SignUp.privacyPolicyNotAcceptedError": "A folytatáshoz fogadja el adatvédelmi szabályzatunkat", "app.containers.SignUp.signUp2": "Regisztr<PERSON><PERSON><PERSON>", "app.containers.SignUp.skip": "Hagyja ki ezt a lépést", "app.containers.SignUp.tacError": "A folytatáshoz fogadja el általános szerződési feltételeinket", "app.containers.SignUp.thePrivacyPolicy": "az adat<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.theTermsAndConditions": "a feltételeket", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {<PERSON><PERSON>, kor<PERSON><PERSON><PERSON> prób<PERSON>lt meg regisztrá<PERSON>i <PERSON>, hogy befejezte volna a folyamatot. Ehelyett kattintson a Bejelentkezés lehetőségre az előző kísérlet során kiválasztott hitelesítő adatok használatával.} other {Hiba történt. Kér<PERSON>, próbálja újra később.}}", "app.containers.SignUp.viennaConsentEmail": "E-mail cím", "app.containers.SignUp.viennaConsentFirstName": "Keresztnév", "app.containers.SignUp.viennaConsentFooter": "Profiladatait a bejelentkezés után módosíthatja. Ha már van fiókja ugyanazzal az e-mail címmel a mitgestalten.wien.gv.at oldalon, akkor az össze lesz kapcsolva jelenlegi fiókjával.", "app.containers.SignUp.viennaConsentHeader": "A következő adatok kerülnek továbbításra:", "app.containers.SignUp.viennaConsentLastName": "Vezetéknév", "app.containers.SignUp.viennaConsentUserName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.viennaDataProtection": "a bécsi adatv<PERSON><PERSON><PERSON>", "app.containers.SiteMap.contributions": "Hozzájáru<PERSON>ok", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "Megjegyzések", "app.containers.SiteMap.options": "Opciók", "app.containers.SiteMap.projects": "Projektek", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "Jelentés", "app.containers.SpamReport.buttonSuccess": "Siker", "app.containers.SpamReport.inappropriate": "<PERSON><PERSON> v<PERSON>", "app.containers.SpamReport.messageError": "Hiba történt az űrlap elküldésekor. Kérjük, prób<PERSON><PERSON><PERSON>.", "app.containers.SpamReport.messageSuccess": "Jelentését elküldtük", "app.containers.SpamReport.other": "Más ok", "app.containers.SpamReport.otherReasonPlaceholder": "Le<PERSON><PERSON><PERSON>", "app.containers.SpamReport.wrong_content": "<PERSON>z nem releváns", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Profilkép eltávolítása", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "A még szavazásra váró javaslatokra leadott szavazatait töröljük. Azokra a javaslatokra vonatkozó szavazatokat, amelyeknél a szavazási időszak lejárt, nem töröljük.", "app.containers.UsersEditPage.addPassword": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Ellenőrzést igénylő projektekben való részvétel.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Igazolja személyazonosságát", "app.containers.UsersEditPage.bio": "Rólad", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Ezt a mezőt nem szerkesztheti, mert ellenőrzött információkat tartalmaz.", "app.containers.UsersEditPage.buttonSuccessLabel": "Siker", "app.containers.UsersEditPage.cancel": "M<PERSON>gs<PERSON>", "app.containers.UsersEditPage.changeEmail": "E-mail módosítás", "app.containers.UsersEditPage.changePassword2": "Jelszó módosítása", "app.containers.UsersEditPage.clickHereToUpdateVerification": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, katti<PERSON><PERSON> ide az igazolás frissítéséhez.", "app.containers.UsersEditPage.conditionsLinkText": "feltételeinket", "app.containers.UsersEditPage.contactUs": "Még egy ok a t<PERSON>ás<PERSON>? {feedbackLink} és talán tudunk segíteni.", "app.containers.UsersEditPage.deleteAccountSubtext": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ho<PERSON> elmentél.", "app.containers.UsersEditPage.deleteMyAccount": "Fiókom törlése", "app.containers.UsersEditPage.deleteYourAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.deletionSection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.deletionSubtitle": "Ez a művelet nem von<PERSON>ó vissza. A platformon közzétett tartalom anonim lesz. Ha törölni szeretné az összes tartalmát, l<PERSON><PERSON><PERSON><PERSON> ka<PERSON> velü<NAME_EMAIL> címen.", "app.containers.UsersEditPage.email": "Email", "app.containers.UsersEditPage.emailEmptyError": "Adjon meg egy e-mail címet", "app.containers.UsersEditPage.emailInvalidError": "Adjon meg egy e-mail címet a megfelelő formátumban, például név@szolgáltató.com", "app.containers.UsersEditPage.feedbackLinkText": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Keresztnév", "app.containers.UsersEditPage.firstNamesEmptyError": "Adjon meg egy kere<PERSON>nevet", "app.containers.UsersEditPage.h1": "<PERSON>z Ön fi<PERSON>", "app.containers.UsersEditPage.h1sub": "Szerkessze fiókadatait", "app.containers.UsersEditPage.image": "Avatar kép", "app.containers.UsersEditPage.imageDropzonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> a profilk<PERSON>p kiválasztásához (max. 5 MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "A profil összes beállítása", "app.containers.UsersEditPage.language": "Nyelv", "app.containers.UsersEditPage.lastName": "Vezetéknév", "app.containers.UsersEditPage.lastNameEmptyError": "<PERSON>jon meg egy vezetéknevet", "app.containers.UsersEditPage.loading": "Terhelés...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Itt módosíthatja e-mail címét vagy j<PERSON>.", "app.containers.UsersEditPage.loginCredentialsTitle": "Bejelentkezési hitelesítő adatok", "app.containers.UsersEditPage.messageError": "<PERSON>em tud<PERSON> menteni a profilját. Próbálja ú<PERSON>, vagy <PERSON><PERSON> a <EMAIL> címre.", "app.containers.UsersEditPage.messageSuccess": "A profilod el lett mentve.", "app.containers.UsersEditPage.metaDescription": "Ez a {firstName} {lastName} profilbeállítási oldala a {tenantName}online részvételi platformján. Itt igazolhatja személyazonosságát, szerkesztheti fiókadatait, törölheti fiókját és szerkesztheti e-mail beállításait.", "app.containers.UsersEditPage.metaTitle1": "{firstName} {lastName} | profilbeállítási oldala {orgName}", "app.containers.UsersEditPage.noGoingBack": "Ha erre a gombra kattint, nem <PERSON>ll módu<PERSON> v<PERSON>za<PERSON><PERSON><PERSON><PERSON>.", "app.containers.UsersEditPage.noNameWarning2": "<PERSON>z Ön neve jelenleg a következőképpen jelenik meg a platformon: \"{displayName}\", mert nem adta meg a nevét. Ez egy automatikusan generált név. Ha meg szeretné változtatni, k<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> be a nevét alább.", "app.containers.UsersEditPage.notificationsSubTitle": "<PERSON><PERSON><PERSON> t<PERSON> e-mailes értesítéseket szeretne kapni? ", "app.containers.UsersEditPage.notificationsTitle": "E-mail értesítések", "app.containers.UsersEditPage.password": "Válasszon új j<PERSON>", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "<PERSON>jon meg egy legalább {minimumPasswordLength} karakter hoss<PERSON> j<PERSON>", "app.containers.UsersEditPage.passwordAddSection": "<PERSON>jon hozzá egy j<PERSON>", "app.containers.UsersEditPage.passwordAddSubtitle2": "<PERSON><PERSON><PERSON><PERSON><PERSON> be j<PERSON><PERSON><PERSON><PERSON>, és egyszerűen jelentkezzen be a platformra anélkül, hogy minden alkalommal meg kellene erősítenie e-mailjét.", "app.containers.UsersEditPage.passwordChangeSection": "Változtassa meg j<PERSON>v<PERSON>t", "app.containers.UsersEditPage.passwordChangeSubtitle": "Erősítse meg jelenlegi j<PERSON>zavát, és változtassa meg az új j<PERSON>zót.", "app.containers.UsersEditPage.privacyReasons": "Ha aggódik a magánélete miatt, olvassa el a {conditionsLink}című részt.", "app.containers.UsersEditPage.processing": "<PERSON><PERSON><PERSON><PERSON>...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "A vezetéknév megadásakor a keresztnév megadása kötelező", "app.containers.UsersEditPage.reasonsToStayListTitle": "<PERSON><PERSON><PERSON><PERSON>...", "app.containers.UsersEditPage.submit": "Módosítások mentése", "app.containers.UsersEditPage.tooManyEmails": "Túl sok e-mailt kap? E-mail beállításait a profilbeállításokban kezelheti.", "app.containers.UsersEditPage.updateverification": "Változtak a hivatalos adatok? {reverifyButton}", "app.containers.UsersEditPage.user": "<PERSON><PERSON>, hogy értesítő e-mailt küldjünk?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Részt vehet olyan projektek<PERSON>, amelyek el<PERSON>őrz<PERSON>t igényelnek.", "app.containers.UsersEditPage.verifiedIdentityTitle": "<PERSON><PERSON>", "app.containers.UsersEditPage.verifyNow": "Igazoljon most", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "<PERSON><PERSON><PERSON><PERSON> le válaszait (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {nincs l<PERSON>} one {1 tetszik} other {# tetszik}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "<PERSON><PERSON><PERSON> be, hogy ezt a megjegyzést válaszként tették közzé:", "app.containers.UsersShowPage.areas": "Területek", "app.containers.UsersShowPage.commentsWithCount": "Megjegyzések ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Szerkessze a profilomat", "app.containers.UsersShowPage.emptyInfoText": "Ön nem követi a fent megadott szűrő egyik elemét sem.", "app.containers.UsersShowPage.eventsWithCount": "<PERSON><PERSON><PERSON><PERSON><PERSON> ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Követve ({followingCount})", "app.containers.UsersShowPage.inputs": "Bemenetek", "app.containers.UsersShowPage.invisibleTitlePostsList": "A résztvevő által beküldött összes bemenet", "app.containers.UsersShowPage.invisibleTitleUserComments": "A résztvevő által közzétett összes megjegyzés", "app.containers.UsersShowPage.loadMore": "<PERSON><PERSON><PERSON><PERSON><PERSON> be többet", "app.containers.UsersShowPage.loadMoreComments": "További megjegyzések betöltése", "app.containers.UsersShowPage.loadingComments": "Megjegyzések betöltése...", "app.containers.UsersShowPage.loadingEvents": "Események betöltése...", "app.containers.UsersShowPage.memberSince": "Tagság {date}óta", "app.containers.UsersShowPage.metaTitle1": "{firstName} {lastName} | profiloldala {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Ez a sze<PERSON>ly még nem írt megjegyzést.", "app.containers.UsersShowPage.noCommentsForYou": "Itt még nincsenek hozzászólások.", "app.containers.UsersShowPage.noEventsForUser": "Még nem vettél részt eseményen.", "app.containers.UsersShowPage.postsWithCount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Projekt mappák", "app.containers.UsersShowPage.projects": "Projektek", "app.containers.UsersShowPage.proposals": "Javaslatok", "app.containers.UsersShowPage.seePost": "Lásd a benyújtást", "app.containers.UsersShowPage.surveyResponses": "Válaszok ({responses})", "app.containers.UsersShowPage.topics": "Témák", "app.containers.UsersShowPage.tryAgain": "Hiba történt. <PERSON><PERSON><PERSON><PERSON>, pró<PERSON><PERSON><PERSON><PERSON>.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Ez a {firstName} {lastName} profiloldala a {orgName}online részvételi platformján. Íme egy áttekintés az összes bevitelükről.", "app.containers.VoteControl.close": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.VoteControl.voteErrorTitle": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageTextCards": "Képes és szöveges kártyák", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info és harmonika", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 oszlop", "app.containers.admin.ContentBuilder.projectDescription": "Projekt leírása", "app.containers.app.navbar.admin": "Platform kezelése", "app.containers.app.navbar.allProjects": "Minden projekt", "app.containers.app.navbar.ariaLabel": "Elsődleges", "app.containers.app.navbar.closeMobileNavMenu": "<PERSON><PERSON><PERSON><PERSON> be a mobil navigá<PERSON><PERSON>", "app.containers.app.navbar.editProfile": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.fullMobileNavigation": "Teljes mobil", "app.containers.app.navbar.logIn": "Jelentkezzen be", "app.containers.app.navbar.logoImgAltText": "{orgName} <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.myProfile": "Az én tevékenységem", "app.containers.app.navbar.search": "Keresés", "app.containers.app.navbar.showFullMenu": "<PERSON><PERSON><PERSON> meg<PERSON>", "app.containers.app.navbar.signOut": "Jelentkezzen ki", "app.containers.eventspage.errorWhenFetchingEvents": "Hiba történt az események betöltése közben. Kérjük, próbálja meg újratölteni az oldalt.", "app.containers.eventspage.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.eventsPageDescription": "A {orgName}platformján k<PERSON>tett összes esemény megjelenítése.", "app.containers.eventspage.eventsPageTitle1": "Események | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projektek", "app.containers.eventspage.noPastEvents": "Nincsenek megjeleníthető múltbeli események", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Jelenleg nincsenek betervezve közelgő vagy folyamatban lévő események.", "app.containers.eventspage.pastEvents": "Múltbeli <PERSON>", "app.containers.eventspage.upcomingAndOngoingEvents": "Közelgő és folyamatban lévő események", "app.containers.footer.accessibility-statement": "Hozzáférhetőségi nyilatkozat", "app.containers.footer.ariaLabel": "Másodlagos", "app.containers.footer.cookie-policy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.cookieSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.feedbackEmptyError": "A visszajelzés mező nem lehet üres.", "app.containers.footer.poweredBy": "Powered by", "app.containers.footer.privacy-policy": "Adatvé<PERSON><PERSON>", "app.containers.footer.siteMap": "Oldaltérkép", "app.containers.footer.terms-and-conditions": "Feltételek", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "M<PERSON>gs<PERSON>", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON>, el akarok menni", "app.containers.ideaHeading.editForm": "Űrlap szerkesztése", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Biztos, hogy el akarsz menni?", "app.containers.ideaHeading.leaveIdeaForm": "Hagyja el az ötlet űrlapot", "app.containers.ideaHeading.leaveIdeaText": "<PERSON><PERSON><PERSON><PERSON><PERSON> nem menti a rendszer.", "app.containers.landing.cityProjects": "Projektek", "app.containers.landing.completeProfile": "Töltse ki profilj<PERSON>t", "app.containers.landing.completeYourProfile": "Üdvöz<PERSON><PERSON><PERSON><PERSON><PERSON>, {firstName}. Itt az ideje, hogy kitöltse profilját.", "app.containers.landing.createAccount": "Regisztr<PERSON><PERSON><PERSON>", "app.containers.landing.defaultSignedInMessage": "{orgName} hallgat téged. <PERSON><PERSON> a sor, hogy hallasd a hangod!", "app.containers.landing.doItLater": "később megcsinálom", "app.containers.landing.new": "<PERSON><PERSON>", "app.containers.landing.subtitleCity": "Üdvözöljük a {orgName}részvételi platformján", "app.containers.landing.titleCity": "Alakítsuk együtt {orgName} jövőjét", "app.containers.landing.twitterMessage": "Szavazz a {ideaTitle} -ra", "app.containers.landing.upcomingEventsWidgetTitle": "Közelgő és folyamatban lévő események", "app.containers.landing.userDeletedSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> létre<PERSON>, vagy {contactLink} , hogy tuda<PERSON>, mit jav<PERSON><PERSON><PERSON>.", "app.containers.landing.userDeletedSubtitleLinkText": "<PERSON><PERSON><PERSON> ne<PERSON><PERSON>nk egy <PERSON>", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "Fiókját törölték.", "app.containers.landing.userDeletionFailed": "Hiba történt a fiók törlésekor. Értesítettünk minket a problémáról, és mindent megteszünk a megoldása érdekében. K<PERSON>rj<PERSON>k, prób<PERSON><PERSON><PERSON>.", "app.containers.landing.verifyNow": "Igazoljon most", "app.containers.landing.verifyYourIdentity": "Igazolja személyazonosságát", "app.containers.landing.viewAllEventsText": "Az összes esemény megtekintése", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Vissza a mappához", "app.errors.after_end_at": "A kezdő dátum a befejezési dátum után következik be", "app.errors.avatar_carrierwave_download_error": "<PERSON><PERSON> letölteni az avatar fájlt.", "app.errors.avatar_carrierwave_integrity_error": "Az Avatar fájl típusa nem engedélyezett.", "app.errors.avatar_carrierwave_processing_error": "<PERSON><PERSON> feldolgozni az avatart.", "app.errors.avatar_extension_blacklist_error": "Az avatarkép fájlkiterjesztése nem engedélyezett. Az engedélyezett kiterjesztések: jpg, jpeg, gif <PERSON>s png.", "app.errors.avatar_extension_whitelist_error": "Az avatarkép fájlkiterjesztése nem engedélyezett. Az engedélyezett kiterjesztések: jpg, jpeg, gif <PERSON>s png.", "app.errors.banner_cta_button_multiloc_blank": "<PERSON><PERSON><PERSON> be egy gomb s<PERSON>ö<PERSON>.", "app.errors.banner_cta_button_url_blank": "<PERSON><PERSON><PERSON> be egy <PERSON>.", "app.errors.banner_cta_button_url_url": "Adjon meg egy érvényes linket. Győződjön meg arról, hogy a link „https://” előtaggal kezdődik.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "<PERSON><PERSON><PERSON> be egy gomb s<PERSON>ö<PERSON>.", "app.errors.banner_cta_signed_in_url_blank": "<PERSON><PERSON><PERSON> be egy <PERSON>.", "app.errors.banner_cta_signed_in_url_url": "Adjon meg egy érvényes linket. Győződjön meg arról, hogy a link „https://” előtaggal kezdődik.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "<PERSON><PERSON><PERSON> be egy gomb s<PERSON>ö<PERSON>.", "app.errors.banner_cta_signed_out_url_blank": "<PERSON><PERSON><PERSON> be egy <PERSON>.", "app.errors.banner_cta_signed_out_url_url": "Adjon meg egy érvényes linket. Győződjön meg arról, hogy a link „https://” előtaggal kezdődik.", "app.errors.base_includes_banned_words": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy egy vagy több olyan s<PERSON><PERSON><PERSON>, amely káromkodásnak minősül. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, módosítsd a szöveget az esetleges káromkodások eltávolítása érdekében.", "app.errors.body_multiloc_includes_banned_words": "A leírás nem megfelelőnek tartott szavakat tartalmaz.", "app.errors.bulk_import_idea_not_valid": "A kapott ötlet nem érvényes: {value}.", "app.errors.bulk_import_image_url_not_valid": "Nem le<PERSON>tt képet letölteni a {value}címről. Győződjön meg arr<PERSON>, hogy az URL érvényes, és fájlkiterjesztéssel végződik, például .png vagy .jpg. Ez a probléma a {row}azonosítójú sorban jelentkezik.", "app.errors.bulk_import_location_point_blank_coordinate": "Az ötlet helye hi<PERSON> k<PERSON> a {value}helyen. Ez a probléma a {row}azonosítójú sorban jelentkezik.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Az ötlet helye nem numerikus k<PERSON>din<PERSON>á<PERSON> {value}. Ez a probléma a {row}azonosítójú sorban jelentkezik.", "app.errors.bulk_import_malformed_pdf": "A feltöltött PDF-fájl hibásnak tűnik. Próbálja újra exportálni a PDF-fájlt a forrásból, majd töltse fel újra.", "app.errors.bulk_import_maximum_ideas_exceeded": "Túllépte a maximális {value} ötletet.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Túllépte a PDF-fájl maximális {value} oldalát.", "app.errors.bulk_import_not_enough_pdf_pages": "A feltöltött PDF-nek nincs elég old<PERSON> – legalább annyi oldalnak kell lennie, mint a letöltött sablonnak.", "app.errors.bulk_import_publication_date_invalid_format": "Érvénytelen megjelenési dátumformátumú ötlet \"{value}\". K<PERSON>r<PERSON><PERSON>k, használja a „DD-MM-YYYY” formátumot.", "app.errors.cannot_contain_ideas": "Az Ön által kiválasztott részvételi mód nem támogatja az ilyen típusú bejegyzéseket. K<PERSON><PERSON><PERSON><PERSON><PERSON>, módosítsa a kijelölést, és próbálja újra.", "app.errors.cant_change_after_first_response": "Ezt már nem mó<PERSON>, mivel n<PERSON> f<PERSON>z<PERSON>ó már v<PERSON>", "app.errors.category_name_taken": "Már létezik ilyen nevű kategória", "app.errors.confirmation_code_expired": "A kód lej<PERSON>rt. Kérjen új kódot.", "app.errors.confirmation_code_invalid": "Érvénytelen megerősítő kód. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> e-mailben a hely<PERSON> k<PERSON>, vagy próbálkozzon az \"Új kód küldése\" lehetőséggel", "app.errors.confirmation_code_too_many_resets": "Túl sokszor küldte el újra a megerősítő kódot. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vegye fel velünk a ka<PERSON>, hogy helyette meghívók<PERSON><PERSON><PERSON> ka<PERSON>.", "app.errors.confirmation_code_too_many_retries": "Túl so<PERSON> próbáltad. <PERSON><PERSON><PERSON><PERSON><PERSON>, vagy próbálja meg megváltoztatni az e-mail címét.", "app.errors.email_already_active": "A {row} sorban található {value} e-mail cím már egy regisztrált résztvevőé", "app.errors.email_already_invited": "A {row} sorban található {value} e-mail cím már megh<PERSON><PERSON>t ka<PERSON>t", "app.errors.email_blank": "Ez nem lehet üres", "app.errors.email_domain_blacklisted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, használjon másik e-mail domaint a regisztrációhoz.", "app.errors.email_invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, használjon érvényes e-mail címet.", "app.errors.email_taken": "<PERSON><PERSON><PERSON> l<PERSON>k fiók ezzel az e-mail címmel. <PERSON><PERSON><PERSON> be<PERSON>.", "app.errors.email_taken_by_invite": "A {value} egy függ<PERSON>ben lévő meghívó már <PERSON>t. <PERSON><PERSON><PERSON><PERSON> a spam mappát, vagy lép<PERSON>n kap<PERSON>olatba a {supportEmail} c<PERSON><PERSON><PERSON>, ha nem találja.", "app.errors.emails_duplicate": "A {value} e-mail cím egy vagy több ismétlődő értékét találtuk a következő sor(ok)ban: {rows}", "app.errors.extension_whitelist_error": "A feltölteni kívánt fájl formátuma nem támogatott.", "app.errors.file_extension_whitelist_error": "A feltölteni próbált fájl formátuma nem támogatott.", "app.errors.first_name_blank": "Ez nem lehet üres", "app.errors.generics.blank": "Ez nem lehet üres.", "app.errors.generics.invalid": "Ez nem tűnik érvényes értéknek", "app.errors.generics.taken": "Ez az e-mail már lé<PERSON>. Egy másik fiók kapcsolódik hozzá.", "app.errors.generics.unsupported_locales": "Ez a mező nem támogatja az aktuális területi beállítást.", "app.errors.group_ids_unauthorized_choice_moderator": "Projektmenedzserként csak azoknak küldhet e-mailt, akik hozzáférhetnek a projektjeihez", "app.errors.has_other_overlapping_phases": "A projekteknek nem lehetnek átfedő szakaszai.", "app.errors.invalid_email": "A {value} a {row} sorban található e-mail cím nem érvényes e-mail cím", "app.errors.invalid_row": "Ismeretlen hiba történt a {row}sor feldolgozása közben", "app.errors.is_not_timeline_project": "A jelenlegi projekt nem támogatja a fázisokat.", "app.errors.key_invalid": "A kulcs csak betűket, számokat és aláhúzásjeleket (_) tartalmazhat", "app.errors.last_name_blank": "Ez nem lehet üres", "app.errors.locale_blank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, válasszon nyelvet", "app.errors.locale_inclusion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> egy támogatott nyelvet", "app.errors.malformed_admin_value": "A {row} sorban található {value} adminisztrátori érték nem érvényes", "app.errors.malformed_groups_value": "A {row} sorban található {value} csoport nem érvényes csoport", "app.errors.max_invites_limit_exceeded1": "A meghívók száma meghaladja az 1000-es határt.", "app.errors.maximum_attendees_greater_than1": "A regisztrálók maximális számának nagyobbnak kell lennie, mint 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "A regisztráltak maximális számának nagyobbnak vagy egyenlőnek kell lennie a jelenlegi regisztráltak számával.", "app.errors.no_invites_specified": "<PERSON>em <PERSON> e-mail cím.", "app.errors.no_recipients": "A kampányt nem lehet k<PERSON>, mert nincs c<PERSON>. A csoport, amelynek küld, vagy <PERSON>, vagy senki sem járult hozzá az e-mailek fogadásához.", "app.errors.number_invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy érvényes számot.", "app.errors.password_blank": "Ez nem lehet üres", "app.errors.password_invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> újra jelenlegi jelszavát.", "app.errors.password_too_short": "A jelszónak legalább 8 karakter hosszúnak kell lennie", "app.errors.resending_code_failed": "Hiba történt a megerősítő kód elküldése közben.", "app.errors.slug_taken": "Ez a projekt URL már létezik. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, módosítsa a projekt slug-ját valami másra.", "app.errors.tag_name_taken": "Már létezik ilyen nevű címke", "app.errors.title_multiloc_blank": "A cím nem lehet üres.", "app.errors.title_multiloc_includes_banned_words": "A cím nem megfelelőnek tartott szavakat tartalmaz.", "app.errors.token_invalid": "A jelszó-visszaállító hivatkozások csak egyszer használhatók, és az elküldés után egy óráig érvényesek. {passwordResetLink}.", "app.errors.too_common": "Ez a jelszó könnyen kitalálható. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> er<PERSON>sebb jelszót.", "app.errors.too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> (maximum 72 karakter)", "app.errors.too_short": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, válasszon egy legalább 8 karakterből álló j<PERSON>", "app.errors.uncaught_error": "Ismeretlen hiba történ<PERSON>.", "app.errors.unknown_group": "A {row} sorban található {value} csoport nem ismert csoport", "app.errors.unknown_locale": "A {value} a {row} sorban található nyelv nem konfigurált nyelv", "app.errors.unparseable_excel": "A kiválasztott Excel-fájlt nem lehetett feldolgozni.", "app.errors.url": "Adjon meg egy érvényes linket. Győződjön meg arról, hogy a link https://-vel kezdődik", "app.errors.verification_taken": "<PERSON>z ellenőrzést nem lehet befe<PERSON>zni, mivel egy másik fiókot igazoltak ugyanazokkal az adatokkal.", "app.errors.view_name_taken": "Már létezik ilyen nevű nézet", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "A rendszer automatikusan észlelt nem megfelelő tartalmat egy bejegyzésben vagy megjegyzésben", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Jelentkezzen be a StandardPortal segítségével", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Regisztráljon a StandardPortal oldalon", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Hozzon létre Stadt Wien-fió<PERSON> most, és használjon egyetlen bejelentkezést Bécs számos digitális szolgáltatásához.", "app.modules.id_cow.cancel": "M<PERSON>gs<PERSON>", "app.modules.id_cow.emptyFieldError": "Ez a mező nem lehet üres.", "app.modules.id_cow.helpAltText": "<PERSON><PERSON><PERSON><PERSON>, hol található az azonosító sorozatszáma a személyi igazolványon", "app.modules.id_cow.invalidIdSerialError": "Érvénytelen <PERSON>zonosító", "app.modules.id_cow.invalidRunError": "Érvénytelen RUN", "app.modules.id_cow.noMatchFormError": "<PERSON><PERSON> e<PERSON>ez<PERSON>.", "app.modules.id_cow.notEntitledFormError": "<PERSON><PERSON>.", "app.modules.id_cow.showCOWHelp": "Hol találom meg az azonosítóm sorozatszámát?", "app.modules.id_cow.somethingWentWrongError": "<PERSON><PERSON> t<PERSON>, mert valami hiba tört<PERSON>", "app.modules.id_cow.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_cow.takenFormError": "<PERSON><PERSON><PERSON>.", "app.modules.id_cow.verifyCow": "Ellenőrizze a COW segítségével", "app.modules.id_franceconnect.verificationButtonAltText": "Ellenőrizze a FranceConnect segítségével", "app.modules.id_gent_rrn.cancel": "M<PERSON>gs<PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "Ez a mező nem lehet üres.", "app.modules.id_gent_rrn.gentRrnHelp": "Társadalombiztosítási száma a digitális személyi igazolvány hátoldalán látható", "app.modules.id_gent_rrn.invalidRrnError": "Érvénytelen társadalombiztosítási szám", "app.modules.id_gent_rrn.noMatchFormError": "Nem ta<PERSON> vissza információt a társadalombiztosítási számáról", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "<PERSON><PERSON> t<PERSON>, mert Gent városán kívül él", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "<PERSON><PERSON>, mert 14 évesnél fiatalabb", "app.modules.id_gent_rrn.rrnLabel": "Társadalombiztosítási szám", "app.modules.id_gent_rrn.rrnTooltip": "Megkérjük társadalombiztosítási számát, hogy ellenőrizzük, hogy Ön 14 évesnél idősebb genti állampolgár-e.", "app.modules.id_gent_rrn.showGentRrnHelp": "Hol találom meg az azonosítóm sorozatszámát?", "app.modules.id_gent_rrn.somethingWentWrongError": "<PERSON><PERSON> t<PERSON>, mert valami hiba tört<PERSON>", "app.modules.id_gent_rrn.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.takenFormError": "Társadalombiztosítási számát már felhasználták egy másik fiók ellenőrzésére", "app.modules.id_gent_rrn.verifyGentRrn": "Ellenőrizze a GentRrn használatával", "app.modules.id_id_card_lookup.cancel": "M<PERSON>gs<PERSON>", "app.modules.id_id_card_lookup.emptyFieldError": "Ez a mező nem lehet üres.", "app.modules.id_id_card_lookup.helpAltText": "Személyi igazolvány magyarázata", "app.modules.id_id_card_lookup.invalidCardIdError": "Ez az azonosító nem érvényes.", "app.modules.id_id_card_lookup.noMatchFormError": "<PERSON><PERSON> e<PERSON>ez<PERSON>.", "app.modules.id_id_card_lookup.showHelp": "Hol találom meg az azonosítóm sorozatszámát?", "app.modules.id_id_card_lookup.somethingWentWrongError": "<PERSON><PERSON> t<PERSON>, mert valami hiba tört<PERSON>", "app.modules.id_id_card_lookup.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.takenFormError": "<PERSON><PERSON><PERSON>.", "app.modules.id_oostende_rrn.cancel": "M<PERSON>gs<PERSON>", "app.modules.id_oostende_rrn.emptyFieldError": "Ez a mező nem lehet üres.", "app.modules.id_oostende_rrn.invalidRrnError": "Érvénytelen társadalombiztosítási szám", "app.modules.id_oostende_rrn.noMatchFormError": "Nem ta<PERSON> vissza információt a társadalombiztosítási számáról", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "<PERSON><PERSON> t<PERSON>, mert O<PERSON>enden kívül él", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "<PERSON><PERSON>, mert 14 évesnél fiatalabb", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Társadalombiztosítási száma a digitális személyi igazolvány hátoldalán látható", "app.modules.id_oostende_rrn.rrnLabel": "Társadalombiztosítási szám", "app.modules.id_oostende_rrn.rrnTooltip": "Arra kérjük társadalombiztosítási számát, hogy igazolja, hogy Ön 14 évesnél idősebb oostendei állampolgár-e.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Hol találom a társadalombiztosítási számomat?", "app.modules.id_oostende_rrn.somethingWentWrongError": "<PERSON><PERSON> t<PERSON>, mert valami hiba tört<PERSON>", "app.modules.id_oostende_rrn.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.takenFormError": "Társadalombiztosítási számát már felhasználták egy másik fiók ellenőrzésére", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Igazoljon társadalombiztosítási számmal", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Adminisztrátori jogokat kapott a \"{folderName}\" mappához.", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Részesedés", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Tekintse meg a projekteket a {folderUrl} címen, hogy hallhassa hangj<PERSON>t!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | a {orgName}részvételi platformjáról", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | a {orgName}részvételi platformjáról", "app.sessionRecording.accept": "Igen, elfogadom", "app.sessionRecording.modalDescription1": "Felhasználóink jobb megértése érdekében véletlenszerűen megkérjük a látogatók egy kis s<PERSON>, hogy részletesen kövesse nyomon a böngészési munkamenetet.", "app.sessionRecording.modalDescription2": "A rögzített adatok kizárólagos célja a weboldal fejlesztése. Az Ön adatait nem osztjuk meg harmadik féllel. Az Ön által megadott bizalmas információkat kiszűrjük.", "app.sessionRecording.modalDescription3": "elfo<PERSON>d?", "app.sessionRecording.modalDescriptionFaq": "GYIK itt.", "app.sessionRecording.modalTitle": "Segítsen nekünk a weboldal fejlesztésében", "app.sessionRecording.reject": "<PERSON><PERSON>, elutasítom", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Végezzen költségvetési elosztási gyakorlatot", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Visszajelzés gyűjtése egy dokumentumról", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Hozzon létre egy <PERSON>on bel<PERSON><PERSON> fel<PERSON>", "app.utils.AdminPage.ProjectEdit.createPoll": "Hozzon létre egy <PERSON>", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Külső felmérés beágyazása", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Keressen önkénteseket", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Gyűjtsön bemeneteket és visszajelzéseket", "app.utils.AdminPage.ProjectEdit.shareInformation": "Ossza meg az információkat", "app.utils.FormattedCurrency.credits": "krediteket", "app.utils.FormattedCurrency.tokens": "zsetonok", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# kredit} one {# kredit} other {# kredit}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokenek} one {# token} other {# tokenek}}", "app.utils.IdeaCards.mostDiscussed": "A legtöbbet megvitatták", "app.utils.IdeaCards.mostReacted": "A legtöbb reakció", "app.utils.IdeaCards.newest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.oldest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.random": "Véletlen", "app.utils.IdeaCards.trending": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.contributionFormTitle": "Új hozzájá<PERSON><PERSON><PERSON> hozz<PERSON>ad<PERSON>a", "app.utils.IdeasNewPage.ideaFormTitle": "<PERSON><PERSON>t ho<PERSON>ad<PERSON>", "app.utils.IdeasNewPage.initiativeFormTitle": "Új kezdeményezés hozzáadása", "app.utils.IdeasNewPage.issueFormTitle1": "Új megjegyzés hozzáadása", "app.utils.IdeasNewPage.optionFormTitle": "Új opció hozzáadása", "app.utils.IdeasNewPage.petitionFormTitle": "<PERSON><PERSON> <PERSON>í<PERSON>ó <PERSON>", "app.utils.IdeasNewPage.projectFormTitle": "Új projekt hozzáadása", "app.utils.IdeasNewPage.proposalFormTitle": "<PERSON><PERSON> ho<PERSON>", "app.utils.IdeasNewPage.questionFormTitle": "Új kérdés hozz<PERSON>adása", "app.utils.IdeasNewPage.surveyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourComment": "Tekintse meg megjegyzését", "app.utils.IdeasNewPage.viewYourContribution": "Tekintse meg hozzájáru<PERSON>t", "app.utils.IdeasNewPage.viewYourIdea": "Tekintse meg ö<PERSON>t", "app.utils.IdeasNewPage.viewYourInitiative": "Tekintse meg kezdeményezését", "app.utils.IdeasNewPage.viewYourInput": "Tekintse meg a bevitelét", "app.utils.IdeasNewPage.viewYourIssue": "<PERSON><PERSON>tse meg pro<PERSON>", "app.utils.IdeasNewPage.viewYourOption": "Tekintse meg az opciót", "app.utils.IdeasNewPage.viewYourPetition": "Tekintse meg petí<PERSON>", "app.utils.IdeasNewPage.viewYourProject": "Tekintse meg projekt<PERSON>t", "app.utils.IdeasNewPage.viewYourProposal": "Tekintse meg a<PERSON>", "app.utils.IdeasNewPage.viewYourQuestion": "Tekintse meg k<PERSON>", "app.utils.Projects.sendSubmission": "Küldje el a beküldés azonosítóját az e-mailemre", "app.utils.Projects.sendSurveySubmission": "Küldje el a kérdőív benyújtásának azonosítóját az e-mail címemre", "app.utils.Projects.surveySubmission": "Felm<PERSON><PERSON><PERSON>", "app.utils.Projects.yourResponseHasTheFollowingId": "Válaszának a következő azonosítója van: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "Ha később úgy dö<PERSON>, hogy válaszát el kívánja távolítani, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vegye fel velünk a kapcsolatot a következő egyedi azonosítóval:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Az eseményen való részvételhez ki kell töltenie a profilját.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Nem felel meg az eseményen való részvétel feltételeinek.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Ön nem vehet részt ezen az eseményen.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Az eseményen való részvételhez be kell jelentkeznie vagy regisztrálnia kell.", "app.utils.actionDescriptors.attendingEventNotVerified": "Az eseményen való részvétel előtt igazolnia kell fiókját.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "<PERSON><PERSON> önkéntességhez ki kell töltened a profilodat.", "app.utils.actionDescriptors.volunteeringNotInGroup": "<PERSON>em felel meg az önkéntesség követelményeinek.", "app.utils.actionDescriptors.volunteeringNotPermitted": "<PERSON>em s<PERSON> önkénteskedni.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "<PERSON>z önkéntességhez be kell jelentkezned vagy regisztrálnod kell.", "app.utils.actionDescriptors.volunteeringNotVerified": "Az önkéntesség megkezdése előtt igazolnia kell fiókját.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {completeRegistrationLink} jelentkezzen önkéntesként.", "app.utils.errors.api_error_default.in": "<PERSON>em <PERSON>", "app.utils.errors.default.ajv_error_birthyear_required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg születési évét", "app.utils.errors.default.ajv_error_date_any": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy érvényes d<PERSON>", "app.utils.errors.default.ajv_error_domicile_required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, töltse ki lakóhelyét", "app.utils.errors.default.ajv_error_gender_required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg ne<PERSON>", "app.utils.errors.default.ajv_error_invalid": "Ér<PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.ajv_error_maxItems": "Leg<PERSON>ljebb {limit, plural, one {# tétel} other {# tétel}}", "app.utils.errors.default.ajv_error_minItems": "Tartalmaznia kell legalább {limit, plural, one {# elemet} other {# elemet}}", "app.utils.errors.default.ajv_error_number_any": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy érvényes számot", "app.utils.errors.default.ajv_error_politician_required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>, hogy Ön politikus", "app.utils.errors.default.ajv_error_required3": "A mező kitöltése kötelező: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "<PERSON><PERSON> le<PERSON>", "app.utils.errors.default.api_error_accepted": "El kell fogadni", "app.utils.errors.default.api_error_blank": "<PERSON><PERSON> le<PERSON>", "app.utils.errors.default.api_error_confirmation": "<PERSON><PERSON>", "app.utils.errors.default.api_error_empty": "<PERSON><PERSON> le<PERSON>", "app.utils.errors.default.api_error_equal_to": "<PERSON>em <PERSON>", "app.utils.errors.default.api_error_even": "Egyenletesnek kell lennie", "app.utils.errors.default.api_error_exclusion": "fenntartva", "app.utils.errors.default.api_error_greater_than": "<PERSON><PERSON>", "app.utils.errors.default.api_error_greater_than_or_equal_to": "<PERSON><PERSON>", "app.utils.errors.default.api_error_inclusion": "<PERSON><PERSON> szerepel a listán", "app.utils.errors.default.api_error_invalid": "Ér<PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_less_than": "<PERSON><PERSON>", "app.utils.errors.default.api_error_less_than_or_equal_to": "<PERSON><PERSON>", "app.utils.errors.default.api_error_not_a_number": "<PERSON><PERSON> e<PERSON>", "app.utils.errors.default.api_error_not_an_integer": "<PERSON><PERSON>sz számnak kell lennie", "app.utils.errors.default.api_error_other_than": "<PERSON>em <PERSON>", "app.utils.errors.default.api_error_present": "Üresnek kell lennie", "app.utils.errors.default.api_error_too_long": "<PERSON><PERSON>", "app.utils.errors.default.api_error_too_short": "<PERSON><PERSON>", "app.utils.errors.default.api_error_wrong_length": "Rossz ho<PERSON>zúságú", "app.utils.errors.defaultapi_error_.odd": "Biz<PERSON>", "app.utils.notInGroup": "<PERSON>em felel meg a részvétel feltételeinek.", "app.utils.participationMethod.onSurveySubmission": "Köszönöm. Válaszát megkaptuk.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Szavazás már nem elé<PERSON>, mivel ez a szakasz már nem aktív.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "<PERSON>em felel meg a sza<PERSON><PERSON><PERSON> feltételeinek.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "<PERSON><PERSON>.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "A szavazáshoz be kell jelentkezned vagy regisztrálnod kell.", "app.utils.participationMethodConfig.voting.votingNotVerified": "A szavazás előtt igazolnia kell fiókját.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Költségkeretek benyújtása: {endDate}.</b> A résztvevőknek összesen <b>{maxBudget} volt, amit {optionCount} opciók között oszthattak el.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Költségvetés benyújtva", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Költségvetés elküldve 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "<PERSON>em felel meg a költségvetés hozzárendelésének követelményeinek.", "app.utils.votingMethodUtils.budgetingNotPermitted": "<PERSON>em rende<PERSON>t hozzá költségvetést.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "A költségvetés hozzárendeléséhez be kell jelentkeznie vagy regisztrálnia kell.", "app.utils.votingMethodUtils.budgetingNotVerified": "A költségkeret hozzárendelése előtt igazolnia kell fiókját.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Költségkerete nem számítható</b> <PERSON><PERSON><PERSON><PERSON>, amíg a \"Küldés\" gombra nem kattint.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "A minimálisan szükséges költségkeret {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "Ha elkészült, katti<PERSON><PERSON> a \"Küldés\" gombra a költségkeret elküldéséhez.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Válassza ki a kívánt opciókat a \"Hozzáadás\" gomb megérin<PERSON>vel.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Összesen <b>{maxBudget} van elosztva {optionCount}</b>op<PERSON><PERSON><PERSON> kö<PERSON>ö<PERSON>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, költségkeretét elküldtük!</b> <PERSON><PERSON> alábbi be<PERSON>llításokat bárm<PERSON><PERSON> ellenőrizheti, vagy <b>{endDate}</b><PERSON><PERSON><PERSON> módosíthatja.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, költségkeretét elküldtük!</b> <PERSON>z alábbi lehetőségeket bármikor ellenőrizheti.", "app.utils.votingMethodUtils.castYourVote": "<PERSON><PERSON>", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Maximum {maxVotes, plural, one {# kreditet} other {# kreditet}} adhatsz hozzá opciónként.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Maximum {maxVotes, plural, one {# pont} other {# pont}} ad<PERSON><PERSON> ho<PERSON> opciónként.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Maximum {maxVotes, plural, one {# tokent} other {# tokent}} adhatsz hozzá opciónként.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Maximum {maxVotes, plural, one {# szavazatot} other {# szavazatot}} adhatsz hozzá opciónként.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "Ha elkészült, katti<PERSON><PERSON> a „Küldés” gombra a szavazás leadásához.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Válassza ki a kívánt opciókat a „Kiválasztás” gomb megérin<PERSON>sével.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "Összesen <b>{totalVotes, plural, one {# kredited} other {# kredited}} van, amit el kell osztanod a {optionCount, plural, one {# opció} other {# opció}}</b>k<PERSON><PERSON><PERSON><PERSON>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "Összesen <b>{totalVotes, plural, one {# pontod} other {# pontod}} van, amit el kell osztanod a {optionCount, plural, one {# opció} other {# opció}}</b>k<PERSON><PERSON><PERSON><PERSON>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "Összesen <b>{totalVotes, plural, one {# token} other {# token}} van, amit el kell <PERSON>tani {optionCount, plural, one {# opció} other {# opció}}</b>k<PERSON><PERSON><PERSON><PERSON>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "Összesen <b>{totalVotes, plural, one {# szavazatod} other {# szavazat}} van, amit el kell osztanod {optionCount, plural, one {# opció} other {# opció}}</b>k<PERSON><PERSON><PERSON><PERSON>.", "app.utils.votingMethodUtils.finalResults": "Végső eredmények", "app.utils.votingMethodUtils.finalTally": "Végső eredmény", "app.utils.votingMethodUtils.howToParticipate": "<PERSON><PERSON><PERSON> vehet rés<PERSON>t", "app.utils.votingMethodUtils.howToVote": "<PERSON><PERSON><PERSON> kell <PERSON>", "app.utils.votingMethodUtils.multipleVotingEnded1": "A szavazás lezárult: <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 kredit} one {1 kredit} other {# kredit}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 pont} one {1 pont} other {# pont}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 token} one {1 token} other {# tokenek}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 szavazat} one {1 szavazat} other {# szavazatok}}", "app.utils.votingMethodUtils.results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.singleVotingEnded": "A szavazást <b>{endDate}napon zá<PERSON>uk.</b> A résztvevők <b>szavazhattak a {maxVotes} opciókra.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Válassza ki a kívánt opciókat a „Szavazás” gomb megérin<PERSON>sével", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "<b>{totalVotes}</b> s<PERSON><PERSON><PERSON><PERSON><PERSON>, amelyeket hozzárendelhetsz az opciókhoz.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "Ha elkészült, katti<PERSON><PERSON> a „Küldés” gombra a szavazás leadásához.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "A szavazást <b>{endDate}napon zá<PERSON>uk.</b> A résztvevők <b>1 lehetőségre szavazhattak.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Válassza ki a kívánt opciót a „Szavazás” gomb megérintésével.", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "<b>1 s<PERSON><PERSON><PERSON><PERSON> van</b> , am<PERSON><PERSON> ho<PERSON>árendelhet az egyik lehetőséghez.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "A szavazást <b>{endDate}napon zá<PERSON>uk.</b> A résztvevők <b>annyi lehetőségre s<PERSON>hattak, amennyire csak akartak.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Tetszőleges számú lehetőségre szavazhatsz.", "app.utils.votingMethodUtils.submitYourBudget": "Adja meg költségvetését", "app.utils.votingMethodUtils.submittedBudgetCountText2": "személy online benyújtotta költségvetését", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "az emberek online adták be költségvetésüket", "app.utils.votingMethodUtils.submittedVoteCountText2": "személy online adta le s<PERSON>vazatát", "app.utils.votingMethodUtils.submittedVotesCountText2": "az emberek online adták le szavazatukat", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "<PERSON><PERSON><PERSON><PERSON><PERSON> 🎉", "app.utils.votingMethodUtils.votesCast": "<PERSON><PERSON>", "app.utils.votingMethodUtils.votingClosed": "A szavazás lezárult", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>A szavazatod nem lesz megszámolva</b> amíg a \"Küldés\" gombra nem kattintasz", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a szavazatodat elküldtük!</b> A beküldött szavazatodat eddig ellenőrizheted vagy mó<PERSON>thatod: <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a szavazatodat elküldtük!</b> A beküldött szavazatodat bármikor ellenőrizheted vagy módosíthatod.", "components.UI.IdeaSelect.noIdeaAvailable": "Nincsenek elérhető ötletek.", "components.UI.IdeaSelect.selectIdea": "Válassza ki az ötletet", "containers.SiteMap.allProjects": "Minden projekt", "containers.SiteMap.customPageSection": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.folderInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.headSiteMapTitle": "Oldaltérkép | {orgName}", "containers.SiteMap.homeSection": "<PERSON><PERSON>lán<PERSON>", "containers.SiteMap.pageContents": "Az oldal tartalma", "containers.SiteMap.profilePage": "<PERSON><PERSON> <PERSON><PERSON> profilo<PERSON>", "containers.SiteMap.profileSettings": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectIdeas": "Ötletek", "containers.SiteMap.projectInfo": "Információ", "containers.SiteMap.projectPoll": "Szavazás", "containers.SiteMap.projectSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectsArchived": "Archivált proje<PERSON>ek", "containers.SiteMap.projectsCurrent": "Jelenlegi projektek", "containers.SiteMap.projectsDraft": "Projekttervezetek", "containers.SiteMap.projectsSection": "A {orgName}projektjei", "containers.SiteMap.signInPage": "Jelentkezzen be", "containers.SiteMap.signUpPage": "Regisztr<PERSON><PERSON><PERSON>", "containers.SiteMap.siteMapDescription": "Erről az oldalról a platform bármely tartalmához navigálhat.", "containers.SiteMap.siteMapTitle": "A {orgName}részvételi platformjának webhelytérképe", "containers.SiteMap.successStories": "Sikertörténetek", "containers.SiteMap.timeline": "A projekt fázisai", "containers.SiteMap.userSpaceSection": "Az Ön fi<PERSON>", "containers.SubscriptionEndedPage.accessDenied": "<PERSON><PERSON><PERSON>", "containers.SubscriptionEndedPage.subscriptionEnded": "Ez az oldal csak aktív előfizetéssel rendelkező platformok számára érhető el."}
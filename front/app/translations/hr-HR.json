{"EmailSettingsPage.emailSettings": "Postavke e-pošte", "EmailSettingsPage.initialUnsubscribeError": "Došlo je do pogreške prilikom otkazivanja pretplate na ovu kampanju. Pokušajte ponovo.", "EmailSettingsPage.initialUnsubscribeLoading": "Vaš se zahtjev obrađuje, pričekajte...", "EmailSettingsPage.initialUnsubscribeSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> ste se od<PERSON><PERSON> s {campaignTitle}.", "UI.FormComponents.optional": "<PERSON><PERSON><PERSON><PERSON>", "app.closeIconButton.a11y_buttonActionMessage": "Zatvori", "app.components.Areas.areaUpdateError": "Doš<PERSON> je do pogreške prilikom spremanja vašeg područja. Molim te pokušaj ponovno.", "app.components.Areas.followedArea": "Praćeno područje: {areaTitle}", "app.components.Areas.followedTopic": "Pratite temu: {topicTitle}", "app.components.Areas.topicUpdateError": "Do<PERSON><PERSON> je do pogreške prilikom spremanja vaše teme. Molim te pokušaj ponovno.", "app.components.Areas.unfollowedArea": "Podru<PERSON><PERSON> koje se ne prati: {areaTitle}", "app.components.Areas.unfollowedTopic": "Nepraćena tema: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Cijena:", "app.components.AssignBudgetControl.add": "<PERSON><PERSON><PERSON>", "app.components.AssignBudgetControl.added": "Dodano", "app.components.AssignMultipleVotesControl.addVote": "<PERSON><PERSON><PERSON> glas", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Podijelili ste sve svoje kredite.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Dodi<PERSON><PERSON>li ste maksimalan broj bodova za ovu opciju.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Podijelili ste sve svoje bodove.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Za ovu opciju ste dodijelili maks<PERSON> broj bodova.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Podijelili ste sve svoje tokene.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Podijelili ste maksimalan broj <PERSON>a za ovu opciju.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Podijelili ste sve svoje glasove.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Za ovu opciju ste podijelili maksimalan broj g<PERSON>.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(uklj. 1 offline)} other {(uklj. # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Glasovanje nije dostu<PERSON>no jer ova faza nije aktivna.", "app.components.AssignMultipleVotesControl.removeVote": "Ukloni glas", "app.components.AssignMultipleVotesControl.select": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Već ste poslali svoj glas. Da biste ga izmijenili, kliknite \"Izmijeni svoj prijedlog\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Već ste poslali svoj glas. Da biste ga izmijenili, vratite se na stranicu projekta i kliknite \"Izmijeni svoj prijedlog\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {kredit} other {krediti}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {bod} other {bodova}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokeni}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {glas} other {glasova}}", "app.components.AssignVoteControl.maxVotesReached1": "Podijelili ste sve svoje glasove.", "app.components.AssignVoteControl.phaseNotActive": "Glasovanje nije dostu<PERSON>no jer ova faza nije aktivna.", "app.components.AssignVoteControl.select": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.selected2": "Odabran", "app.components.AssignVoteControl.voteForAtLeastOne": "Glasajte za najmanje 1 opciju", "app.components.AssignVoteControl.votesSubmitted1": "Već ste poslali svoj glas. Da biste ga izmijenili, kliknite \"Izmijeni svoj prijedlog\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Već ste poslali svoj glas. Da biste ga izmijenili, vratite se na stranicu projekta i kliknite \"Izmijeni svoj prijedlog\".", "app.components.AuthProviders.continue": "<PERSON><PERSON><PERSON>", "app.components.AuthProviders.continueWithAzure": "Nastavite putem {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Nastavite uz Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Nastavite s Lažnim SSO-om", "app.components.AuthProviders.continueWithGoogle": "Nastavite uz Google", "app.components.AuthProviders.continueWithHoplr": "Nastavi<PERSON> s <PERSON>", "app.components.AuthProviders.continueWithIdAustria": "Nastavite s ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Nastavite s {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Nastavite s MitID-om", "app.components.AuthProviders.franceConnectMergingFailed": "Već postoji račun s ovom adresom e-pošte.{br}{br}Ne možete pristupiti platformi koristeći FranceConnect jer se podaci ne poklapaju. Za prijavu koristeći FranceConnect morat ćete prvo promijeniti soje ime ili prezime na platformi kako bi odgovarale službenim podacima.{br}{br}U nastavku se možete prijaviti kako to inače činite.", "app.components.AuthProviders.goToLogIn": "Ve<PERSON> imate račun? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "<PERSON><PERSON><PERSON> ra<PERSON>un? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.logInWithEmail": "Prijavi se putem e-pošte", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Morate imati navedenu minimalnu dob ili više da biste bili potvrđeni.", "app.components.AuthProviders.signUp2": "Registracija", "app.components.AuthProviders.signUpButtonAltText": "Registrirajte se pomoću {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Registrirajte se putem e-pošte", "app.components.AuthProviders.verificationRequired": "Potrebna verifikacija", "app.components.Author.a11yPostedBy": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 sudionik} other {{numberOfParticipants} sudionika}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} k<PERSON><PERSON>a", "app.components.AvatarBubbles.participant": "sudionik", "app.components.AvatarBubbles.participants1": "sudionik<PERSON>", "app.components.Comments.cancel": "Odustani", "app.components.Comments.commentingDisabledInCurrentPhase": "Komentiranje nije moguće u trenutnoj fazi.", "app.components.Comments.commentingDisabledInactiveProject": "Komentiranje nije moguće jer projekt trenutačno nije aktivan.", "app.components.Comments.commentingDisabledProject": "Komentiranje u ovom projektu trenutno je onemogućeno.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} kako biste komentirali.", "app.components.Comments.commentingMaybeNotPermitted": "Prijavite se {signInLink} kako biste mogli vidjeti koje je radnje moguće poduzeti.", "app.components.Comments.inputsAssociatedWithProfile": "Prema zadanim postavkama vaši podnesci bit će povezani s vašim profilom, osim ako ne odaberete ovu opciju.", "app.components.Comments.invisibleTitleComments": "Komentari", "app.components.Comments.leastRecent": "Najmanje <PERSON>", "app.components.Comments.likeComment": "Lajkajte ovaj komentar", "app.components.Comments.mostLiked": "Većina reakci<PERSON>", "app.components.Comments.mostRecent": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.official": "S<PERSON>žbeno", "app.components.Comments.postAnonymously": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.replyToComment": "Odgovori na komentar", "app.components.Comments.reportAsSpam": "Prijavite neželjeni sadržaj", "app.components.Comments.seeOriginal": "Prikaži izvornu verziju", "app.components.Comments.seeTranslation": "Pogledajte prijevod", "app.components.Comments.yourComment": "<PERSON><PERSON><PERSON> k<PERSON>", "app.components.CommonGroundResults.divisiveDescription": "Izjave u kojima se ljudi podjednako slažu i ne slažu:", "app.components.CommonGroundResults.divisiveTitle": "Razdorno", "app.components.CommonGroundResults.majorityDescription": "Više od 60% glasalo je na jednu ili drugu stranu o sljedećem:", "app.components.CommonGroundResults.majorityTitle": "Većina", "app.components.CommonGroundResults.participantLabel": "sudionik", "app.components.CommonGroundResults.participantsLabel1": "sudionici", "app.components.CommonGroundResults.statementLabel": "izjava", "app.components.CommonGroundResults.statementsLabel1": "izjave", "app.components.CommonGroundResults.votesLabe": "glasanje", "app.components.CommonGroundResults.votesLabel1": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundStatements.agreeLabel": "Slažem se", "app.components.CommonGroundStatements.disagreeLabel": "Ne slažem se", "app.components.CommonGroundStatements.noMoreStatements": "Trenutno nema izjava na koje bi se moglo odgovoriti", "app.components.CommonGroundStatements.noResults": "Još nema rezultata za prikaz. Molimo vas da provjerite jeste li sudjelovali u fazi Zajedničkog temelja i provjerite ovdje nakon toga.", "app.components.CommonGroundStatements.unsureLabel": "Nisam siguran/sigurna", "app.components.CommonGroundTabs.resultsTabLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundTabs.statementsTabLabel": "Izjave", "app.components.CommunityMonitorModal.formError": "<PERSON><PERSON><PERSON> je do pogreške.", "app.components.CommunityMonitorModal.surveyDescription2": "Ovo istraživanje koje je u tijeku prati vaše mišljenje o upravljanju i javnim uslugama.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Traje <1 minute} one {Traje 1 minutu} other {Traje # minute}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "E-poruka s potvrdnim kodom poslana je na {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Promijenite svoju adresu e-pošte.", "app.components.ConfirmationModal.codeInput": "Kod:", "app.components.ConfirmationModal.confirmationCodeSent": "Poslan je novi kod", "app.components.ConfirmationModal.didntGetAnEmail": "Niste primili e-poruku?", "app.components.ConfirmationModal.foundYourCode": "<PERSON>našli ste svoj kod?", "app.components.ConfirmationModal.goBack": "<PERSON><PERSON> natrag.", "app.components.ConfirmationModal.sendEmailWithCode": "Pošalji e-poruku s potvrdnim kodom", "app.components.ConfirmationModal.sendNewCode": "Pošalji novi kod.", "app.components.ConfirmationModal.verifyAndContinue": "Potvrdite i nastavite", "app.components.ConfirmationModal.wrongEmail": "Pogrešna e-adresa?", "app.components.ConsentManager.Banner.accept": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.ariaButtonClose2": "Odbaci pravila i zatvori banner", "app.components.ConsentManager.Banner.close": "Zatvori", "app.components.ConsentManager.Banner.mainText": "<PERSON>va platforma koristi kolačiće u skladu s našim {policyLink}.", "app.components.ConsentManager.Banner.manage": "Upravljanje", "app.components.ConsentManager.Banner.policyLink": "Pravila o kolačićima", "app.components.ConsentManager.Banner.reject": "Odbiti", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Oglaša<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "<PERSON>vo koristimo kako bismo personalizovali oglašavanje na našem sajtu i merili njegovu efikasnost. Mi ne prikazujemo oglase, ali to na osnovu stranica koje na našem sajtu posećujete mogu učiniti sledeći servisi. ", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Pratimo upotrebu platforme kako bismo bolje razumjeli kako je koristite te poboljšali vašu navigaciju. Te informacije koriste se samo u masovnoj analitici, a pritom nikad ne pratimo pojedinačne osobe.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "<PERSON><PERSON> natrag", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Odustani", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funkcionalno", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Ovo je neophodno omogućiti kako bi se nadzirale osnovne funkcionalnosti web-mjesta. Pojedini alati koji su nabrojani ovdje ne moraju se odnositi na vas. Molimo vas da pročitate naša pravila o kolačićima za više informacija.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Potrebno", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Kako bi <PERSON>a bila funkcionalna, ukoliko se registrirate čuvat ćemo k<PERSON>čić za autentifikaciju te odabrani jezik na kojem koristite platformu.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Sp<PERSON>i", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Vaš<PERSON> kola<PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "<PERSON><PERSON>", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Odricanje od odgovornosti za prijenos sadržaja", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Učitavanjem sadržaja izjavljujete da taj sadržaj ne krši nikakve propise ili prava trećih strana, kao što su prava intelektualnog vlasništva, prava na privatnost, prava na poslovne tajne i tako dalje. Sli<PERSON>dom toga, učitavanjem ovog sadržaja preuzimate punu i isključivu odgovornost za svu izravnu i neizravnu štetu koja proizlazi iz učitanog sadržaja. Nadalje, obvezujete se obeštetiti vlasnika platforme i Go Vocal od bilo kakvih potraživanja ili odgovornosti trećih strana prema trećim stranama i svih povezanih troškova koji bi nastali ili proizašli iz sadržaja koji ste prenijeli.", "app.components.ContentUploadDisclaimer.onAccept": "razumijem", "app.components.ContentUploadDisclaimer.onCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Recite nam zašto", "app.components.CustomFieldsForm.addressInputAriaLabel": "Unos adrese", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Unesite adresu...", "app.components.CustomFieldsForm.adminFieldTooltip": "Polje vidljivo samo administratorima", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Svi odgovori na ovu anketu su anonimizirani.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Za poligon su potrebne najmanje tri točke.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "Za liniju su potrebne najmanje dvije točke.", "app.components.CustomFieldsForm.attachmentRequired": "Potreban je barem jedan prilog", "app.components.CustomFieldsForm.authorFieldLabel": "Autor", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Počnite tipkati za pretraživanje po e-pošti ili imenu korisnika...", "app.components.CustomFieldsForm.back": "<PERSON><PERSON>", "app.components.CustomFieldsForm.budgetFieldLabel": "Proračun", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Kliknite na kartu za crtanje. Zatim povucite točke da biste ih premjestili.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Kliknite na kartu ili upišite adresu ispod kako biste dodali svoj odgovor.", "app.components.CustomFieldsForm.confirm": "Potvrdi", "app.components.CustomFieldsForm.descriptionMinLength": "Opis mora imati najmanje {min} z<PERSON><PERSON>", "app.components.CustomFieldsForm.descriptionRequired": "Opis je obavezan", "app.components.CustomFieldsForm.fieldMaximumItems": "<PERSON>a polje \"{fieldName}\" može se odabrati najviše {maxSelections, plural, one {# opcija} other {# opcija}}", "app.components.CustomFieldsForm.fieldMinimumItems": "<PERSON>a polje \"{fieldN<PERSON>}\" može se odabrati barem {minSelections, plural, one {# opcija} other {# opcija}}", "app.components.CustomFieldsForm.fieldRequired": "<PERSON><PERSON> \"{field<PERSON><PERSON>}\" je o<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.fileSizeLimit": "Ograničenje veličine datoteke je {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "Slika je obavezna", "app.components.CustomFieldsForm.minimumCoordinates2": "Potrebno je minimalno {numPoints} točaka na mapi.", "app.components.CustomFieldsForm.notPublic1": "*O<PERSON><PERSON> odgovor bit će podijeljen samo s voditeljima projekata, a ne s javno<PERSON>u.", "app.components.CustomFieldsForm.otherArea": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.progressBarLabel": "Napredak", "app.components.CustomFieldsForm.removeAnswer": "Ukloni odgovor", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Odaberite koliko god <PERSON>", "app.components.CustomFieldsForm.selectBetween": "*Odaberite između opcija {minItems} i {maxItems}", "app.components.CustomFieldsForm.selectExactly2": "*Odaberi točno {selectExactly, plural, one {# opcija} other {# opcije}}", "app.components.CustomFieldsForm.selectMany": "*Izaberite koliko god <PERSON>", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Dodirnite kartu za crtanje. Zatim povucite točke da biste ih premjestili.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Dodirnite kartu za crtanje.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Dodirnite kartu da biste dodali svoj odgovor.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Dodirnite kartu ili upišite adresu ispod da biste dodali svoj odgovor.", "app.components.CustomFieldsForm.tapToAddALine": "Dodirnite za dodavanje retka", "app.components.CustomFieldsForm.tapToAddAPoint": "Dodirnite za dodavanje točke", "app.components.CustomFieldsForm.tapToAddAnArea": "Dodirnite za dodavanje područja", "app.components.CustomFieldsForm.titleMaxLength": "<PERSON><PERSON><PERSON> mora imati najvi<PERSON>e {max} z<PERSON><PERSON>", "app.components.CustomFieldsForm.titleMinLength": "<PERSON><PERSON><PERSON> mora imati najmanje {min} z<PERSON><PERSON>", "app.components.CustomFieldsForm.titleRequired": "<PERSON><PERSON><PERSON> je oba<PERSON>an", "app.components.CustomFieldsForm.topicRequired": "Potrebna je barem jedna oznaka", "app.components.CustomFieldsForm.typeYourAnswer": "Upišite svoj odgovor", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Obavezno je upisati svoj odgovor", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Prenesite zip datoteku koja sadrži jednu ili više shapefileova.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "Ako se lokacija ne prikazuje među opcijama dok tipkate, mož<PERSON> dodati valjane koordinate u formatu 'geografska širina, du<PERSON><PERSON>' kako biste odredili točnu lo<PERSON>ciju (npr.: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Pojedina polja su nevaljana. Molimo da ispravite pogreške i pokušate ponovo.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Došlo je do nepoznate pogreške prilikom slanja izvješća. Molimo vas da pokušate ponovo.", "app.components.ErrorBoundary.errorFormLabelClose": "Zatvori", "app.components.ErrorBoundary.errorFormLabelComments": "Što se dogodilo?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-pošta", "app.components.ErrorBoundary.errorFormLabelName": "Ime", "app.components.ErrorBoundary.errorFormLabelSubmit": "Pošalji", "app.components.ErrorBoundary.errorFormSubtitle": "<PERSON><PERSON> tim je oba<PERSON><PERSON>.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON><PERSON> da vam pomognemo, opišite nam što se dogodilo u nastavku.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Uspješno ste poslali povratne informacije. Hvala vam!", "app.components.ErrorBoundary.errorFormTitle": "<PERSON>ini se da je došlo do problema.", "app.components.ErrorBoundary.genericErrorWithForm": "<PERSON><PERSON><PERSON> je do pogreške te ne možemo prikazati ovaj sadržaj. Pokušajte ponovo ili {openForm}", "app.components.ErrorBoundary.openFormText": "pomognite nam da shvatimo", "app.components.ErrorToast.budgetExceededError": "Nemate dovoljno proračuna", "app.components.ErrorToast.votesExceededError": "<PERSON><PERSON><PERSON>", "app.components.EventAttendanceButton.forwardToFriend": "Proslijedi prijatelju", "app.components.EventAttendanceButton.maxRegistrationsReached": "Dosegnut je maksimalan broj prijava za događaj. Nema više slobodnih mjesta.", "app.components.EventAttendanceButton.register": "Registar", "app.components.EventAttendanceButton.registered": "Registriran", "app.components.EventAttendanceButton.seeYouThere": "Vidimo se tamo!", "app.components.EventAttendanceButton.seeYouThereName": "<PERSON><PERSON><PERSON> se tamo, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Vidljivo je manje podataka o događaju.", "app.components.EventCard.a11y_moreContentVisible": "Vidljivo je više podataka o događaju.", "app.components.EventCard.a11y_readMore": "Pročitajte više o događaju \"{eventTitle}\".", "app.components.EventCard.endsAt": "Završava u", "app.components.EventCard.readMore": "Čitaj više", "app.components.EventCard.showLess": "Prika<PERSON><PERSON> manje", "app.components.EventCard.showMore": "Prikaži više", "app.components.EventCard.startsAt": "Počinje u", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Nadolazeći i tekući događaji u ovom projektu", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Nadolazeći i tekući događaji u ovoj fazi", "app.components.FileUploader.a11y_file": "Datoteka:", "app.components.FileUploader.a11y_filesToBeUploaded": "Datoteke koje će biti prenesene: {fileNames}", "app.components.FileUploader.a11y_noFiles": "<PERSON><PERSON> do<PERSON> da<PERSON>.", "app.components.FileUploader.a11y_removeFile": "<PERSON>k<PERSON><PERSON> ovu datoteku", "app.components.FileUploader.fileInputDescription": "Kliknite kako biste odabrali datoteku", "app.components.FileUploader.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (maks. 50MB)", "app.components.FileUploader.file_too_large2": "Datoteke veće od {maxSizeMb}MB nisu dopuštene.", "app.components.FileUploader.incorrect_extension": "<PERSON><PERSON> sustav ne podržava {fileName} i stoga neće biti prenesen.", "app.components.FilterBoxes.a11y_allFilterSelected": "Odabrani filtar statusa: svi", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# predaja} other {# predaje}}", "app.components.FilterBoxes.a11y_removeFilter": "Uklon<PERSON> filtar", "app.components.FilterBoxes.a11y_selectedFilter": "Odabrani filtar statusa: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Odabrani {numberOfSelectedTopics, plural, =0 {nula filtara oznaka} one {jedna filtar oznake} other {# filtara oznaka}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Sve", "app.components.FilterBoxes.areas": "Filtriraj po području", "app.components.FilterBoxes.inputs": "<PERSON><PERSON>", "app.components.FilterBoxes.noValuesFound": "<PERSON><PERSON> vrijednosti.", "app.components.FilterBoxes.showLess": "Prika<PERSON><PERSON> manje", "app.components.FilterBoxes.showTagsWithNumber": "Prikaži sve ({numberTags})", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "Oznake", "app.components.FiltersModal.filters": "<PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.a11y_folderDescription": "Opis mape:", "app.components.FolderFolderCard.a11y_folderTitle": "Naziv mape:", "app.components.FolderFolderCard.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projekata} one {# projekt} other {# projekta}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "Vrsta polja ne može se promijeniti nakon što postoje podnes<PERSON>.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Tip", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Automatsko spremanje", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Automatsko spremanje omogućeno je prema zadanim postavkama kada otvorite uređivač obrazaca. <PERSON><PERSON><PERSON> put kad zatvorite ploču s postavkama polja pomoću gumba \"X\", automatski će se pokrenuti spremanje.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "Četvrt", "app.components.GanttChart.timeRange.timeRangeMultiyear": "Višegodišnji", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON>", "app.components.GanttChart.today": "<PERSON><PERSON>", "app.components.GoBackButton.group.edit.goBack": "<PERSON><PERSON> natrag", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Povratak na prethodnu stranicu", "app.components.HookForm.Feedback.errorTitle": "<PERSON><PERSON><PERSON> je do problema", "app.components.HookForm.Feedback.submissionError": "Pokušajte ponovno. Ako se <PERSON> ne riješi, obra<PERSON> nam se", "app.components.HookForm.Feedback.submissionErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se, do<PERSON><PERSON> je do problema s naše strane", "app.components.HookForm.Feedback.successMessage": "<PERSON><PERSON><PERSON> p<PERSON>lan", "app.components.HookForm.PasswordInput.passwordLabel": "Lozinka", "app.components.HorizontalScroll.scrollLeftLabel": "Pomaknite se lijevo.", "app.components.HorizontalScroll.scrollRightLabel": "Pomaknite se desno.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "Učitano je {sortOder} ideja.", "app.components.IdeaCards.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.mostDiscussed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.newest": "Novi", "app.components.IdeaCards.filters.oldest": "Star", "app.components.IdeaCards.filters.popular": "Najviše se sviđao", "app.components.IdeaCards.filters.random": "Slučajno", "app.components.IdeaCards.filters.sortBy": "Poredaj po", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Razvrstavanje je promijenjeno u: {currentSortType}", "app.components.IdeaCards.filters.trending": "<PERSON> <PERSON>u", "app.components.IdeaCards.showMore": "Prikaži više", "app.components.IdeasMap.a11y_hideIdeaCard": "<PERSON><PERSON><PERSON><PERSON> karticu ideje.", "app.components.IdeasMap.a11y_mapTitle": "Pre<PERSON> mape", "app.components.IdeasMap.clickOnMapToAdd": "Kliknite na mapu kako biste dodali svoj unos", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Kao administrator, možete kliknuti na kartu da dodate svoj unos, čak i ako ova faza nije aktivna.", "app.components.IdeasMap.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.multipleInputsAtLocation": "Višestruki ulazi na ovoj lokaciji", "app.components.IdeasMap.noFilteredResults": "Filtri koje ste odabrali vratili rezultate", "app.components.IdeasMap.noResults": "<PERSON>su pronađeni re<PERSON>ltati", "app.components.IdeasMap.or": "ili", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, nema nesviđanja.} one {1 ne sviđa mi se.} other {, # nesviđanja.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, nema lajk<PERSON>.} one {, 1 lajk.} other {, # sviđanja.}}", "app.components.IdeasMap.signInLinkText": "pri<PERSON><PERSON>", "app.components.IdeasMap.signUpLinkText": "registracija", "app.components.IdeasMap.submitIdea2": "Pošaljite unos", "app.components.IdeasMap.tapOnMapToAdd": "Dodirnite mapu kako biste dodali svoj unos", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Kao administrator, možete dodirnuti kartu da dodate svoj unos, čak i ako ova faza nije aktivna.", "app.components.IdeasMap.userInputs2": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, bez komentara} one {, 1 komentar} other {, # komentara}}", "app.components.IdeasShow.bodyTitle": "Opis", "app.components.IdeasShow.deletePost": "Izbriši", "app.components.IdeasShow.editPost": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.goBack": "<PERSON><PERSON> natrag", "app.components.IdeasShow.moreOptions": "Više opcija", "app.components.IdeasShow.or": "ili", "app.components.IdeasShow.proposedBudgetTitle": "Predloženi proračun", "app.components.IdeasShow.reportAsSpam": "Prijavite neželjeni sadržaj", "app.components.IdeasShow.send": "Pošalji", "app.components.IdeasShow.skipSharing": "Preskačem korak, učinit ću ovo kasnije", "app.components.IdeasShowPage.signIn2": "<PERSON><PERSON><PERSON><PERSON><PERSON> se", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON><PERSON>, nemate pristup ovoj stranici. Možda ćete se morati prijaviti ili registrirati za pristup.", "app.components.LocationInput.noOptions": "Bez mogućnosti", "app.components.Modal.closeWindow": "Zatvori prozor", "app.components.MultiSelect.clearButtonAction": "Poništi odabir", "app.components.MultiSelect.clearSearchButtonAction": "<PERSON><PERSON>š<PERSON> pretragu", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Unesite novu adresu e-pošte", "app.components.PageNotFound.goBackToHomePage": "Natrag na početnu stranicu", "app.components.PageNotFound.notFoundTitle": "Stranica nije pronađena", "app.components.PageNotFound.pageNotFoundDescription": "Tražena stranica nije pronađena.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Unesite sadržaj za barem jedan jezik", "app.components.PagesForm.editContent": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PagesForm.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (maks. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Datoteke ne smiju biti veće od 50 MB. Dodane datoteke će se prikazivati na dnu stranice.", "app.components.PagesForm.navbarItemTitle": "Naziv u navigacijskoj traci", "app.components.PagesForm.pageTitle": "<PERSON><PERSON><PERSON>", "app.components.PagesForm.savePage": "Spremi stranicu", "app.components.PagesForm.saveSuccess": "Stranica uspješno spremljena.", "app.components.PagesForm.titleMissingOneLanguageError": "Unesite naslov za barem jedan jezik", "app.components.Pagination.back": "Prethodna stranica", "app.components.Pagination.next": "Sljedeća stranica", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "<PERSON><PERSON><PERSON><PERSON> ste {votesCast}, što premašuje ograničenje od {votesLimit}. Uklonite neke stavke iz košarice i pokušajte ponovno.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} lijevo", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Morate potrošiti najmanje {votesMinimum} prije nego što možete poslati svoju košaricu.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Morate odabrati barem jednu opciju prije nego što možete poslati.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Morate nešto dodati u svoju košaricu prije nego što to možete poslati.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {Nema preostalih kredita} other {# od {totalNumberOfVotes, plural, one {1 kredit} other {# preostalih kredita}}}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {<PERSON><PERSON> preostalih bodova} other {# od {totalNumberOfVotes, plural, one {1 bod} other {# preostalih bodova}}}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Nema preostalih žetona} other {# od {totalNumberOfVotes, plural, one {1 žeton} other {# preostalih žetona}}}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {<PERSON><PERSON> preostalih glasova} other {# od {totalNumberOfVotes, plural, one {1 glas} other {# preostalih glasova}}}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# glasova} one {# glas} other {# glasova}} cast", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "<PERSON><PERSON> ste {votesCast} g<PERSON><PERSON>, što premašuje ograni<PERSON>je od {votesLimit}. Uklonite neke glasove i pokušajte ponovo.", "app.components.ParticipationCTABars.addInput": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.allocateBudget": "Rasporedite svoj <PERSON>", "app.components.ParticipationCTABars.budgetSubmitSuccess": "<PERSON><PERSON><PERSON> je usp<PERSON>š<PERSON> poslan.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Otvoreno za sudjelovanje", "app.components.ParticipationCTABars.poll": "Ispunite anketu", "app.components.ParticipationCTABars.reviewDocument": "Pregledajte dokument", "app.components.ParticipationCTABars.seeContributions": "<PERSON><PERSON><PERSON> do<PERSON>", "app.components.ParticipationCTABars.seeEvents3": "Pogledajte događaje", "app.components.ParticipationCTABars.seeIdeas": "Pogledajte ideje", "app.components.ParticipationCTABars.seeInitiatives": "Vidi inicijative", "app.components.ParticipationCTABars.seeIssues": "<PERSON>idi probleme", "app.components.ParticipationCTABars.seeOptions": "Pogledajte opcije", "app.components.ParticipationCTABars.seePetitions": "<PERSON><PERSON><PERSON> petici<PERSON>", "app.components.ParticipationCTABars.seeProjects": "Pogledajte projekte", "app.components.ParticipationCTABars.seeProposals": "Pogledajte prijedloge", "app.components.ParticipationCTABars.seeQuestions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.submit": "podnijeti", "app.components.ParticipationCTABars.takeTheSurvey": "Popunite upitnik", "app.components.ParticipationCTABars.userHasParticipated": "Sudjelovali ste u ovom projektu.", "app.components.ParticipationCTABars.viewInputs": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.volunteer": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.votesCounter.vote": "glasanje", "app.components.ParticipationCTABars.votesCounter.votes": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_passwordHidden": "Zaporka je skrivena", "app.components.PasswordInput.a11y_passwordVisible": "Zaporka je vidljiva", "app.components.PasswordInput.a11y_strength1Password": "Preslaba lozinka", "app.components.PasswordInput.a11y_strength2Password": "Slaba zaporka", "app.components.PasswordInput.a11y_strength3Password": "Srednje jaka zaporka", "app.components.PasswordInput.a11y_strength4Password": "<PERSON><PERSON>", "app.components.PasswordInput.a11y_strength5Password": "<PERSON><PERSON>o jaka lozinka", "app.components.PasswordInput.hidePassword": "Sakrijte zaporku", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON><PERSON><PERSON> (min. {minimumPasswordLength} zna<PERSON>)", "app.components.PasswordInput.minimumPasswordLengthError": "Unesite zaporku koja sadrži najmanje {minimumPasswordLength} znakova", "app.components.PasswordInput.passwordEmptyError": "Unesite zaporku", "app.components.PasswordInput.passwordStrengthTooltip1": "Kako biste lozinku učinili jačom:", "app.components.PasswordInput.passwordStrengthTooltip2": "Koristite kombinaciju malih slova koja nisu poredana po abecedi, velikih slova, z<PERSON>nki, posebnih znakova i interpunkcije", "app.components.PasswordInput.passwordStrengthTooltip3": "Izbjegavajte česte riječi koje se lako pogode", "app.components.PasswordInput.passwordStrengthTooltip4": "Povećaj<PERSON> duljinu", "app.components.PasswordInput.showPassword": "Prikaži zaporku", "app.components.PasswordInput.strength1Password": "Preslaba", "app.components.PasswordInput.strength2Password": "Slaba", "app.components.PasswordInput.strength3Password": "Srednje", "app.components.PasswordInput.strength4Password": "<PERSON><PERSON>", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON><PERSON> jaka", "app.components.PostCardsComponents.list": "<PERSON><PERSON>", "app.components.PostCardsComponents.map": "Karta", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Dodajte službenu obavijest", "app.components.PostComponents.OfficialFeedback.cancel": "Odustani", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Izbriši", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Jeste li sigurni da želite izbrisati ovu službenu obavijest?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "Posljednji put izmijenjeno dana {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Posljednji put a<PERSON><PERSON><PERSON>: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "S<PERSON>žbeno", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Odaberite kako drugi vide vaše ime", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Ime autora službene obavijesti", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Tekst službene obavijesti", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Službene obavijesti", "app.components.PostComponents.OfficialFeedback.postedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dana {date} ", "app.components.PostComponents.OfficialFeedback.publishButtonText": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Prikaži prethodne obavijesti", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Objavite novost...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON> nam je, do<PERSON><PERSON> je do pogreške", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Vaše obavijest uspješno je objavljena!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Podržite moj doprinos '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Podržite moj doprinos: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Podržite moj doprinos: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Podržite moju ideju '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Podržite moju ideju: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Podržite moju ideju: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Što mislite o ovom prijedlogu? Glasajte i podijelite raspravu na {postUrl} kako bi se vaš glas čuo!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Podržite moj prijedlog: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Podržite moju inicijativu: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Ob<PERSON><PERSON>/la sam komentar '{postTitle}' ovdje {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Upravo sam objavio/la komentar: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Upravo sam objavio/la komentar: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Podržite moju predloženu opciju '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Podržite moju predloženu opciju: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Podržite moju opciju: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Podržite moju peticiju '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Podržite moju peticiju: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Podržite moju peticiju: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Podržite moj projekt '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Podržite moj projekt: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Podržite moj projekt: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Podržite moj prijedlog '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Podržite moj prijedlog: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Upravo sam objavio prijedlog za {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Pridružite se raspravi o ovom pitanju '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Pridružite se raspravi: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Pridružite se raspravi: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Glas<PERSON>jte za {postTitle} na", "app.components.PostComponents.linkToHomePage": "Veza do početne stranice", "app.components.PostComponents.readMore": "Pročitajte više...", "app.components.PostComponents.topics": "teme", "app.components.ProjectArchivedIndicator.archivedProject": "<PERSON><PERSON><PERSON><PERSON>, više ne možete sudjelovati u ovom projektu jer je arhiviran", "app.components.ProjectArchivedIndicator.previewProject": "Nacrt projekta:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Vidljivo samo moderatorima i onima s vezom za pregled.", "app.components.ProjectCard.a11y_projectDescription": "Opis projekta:", "app.components.ProjectCard.a11y_projectTitle": "Naziv projekta:", "app.components.ProjectCard.addYourOption": "Dodajte svoju opciju", "app.components.ProjectCard.allocateYourBudget": "Rasporedite svoj <PERSON>", "app.components.ProjectCard.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.comment": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.contributeYourInput": "Doprinesite svojim unosom", "app.components.ProjectCard.finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.joinDiscussion": "Uključite se u raspravu", "app.components.ProjectCard.learnMore": "Saznajte više", "app.components.ProjectCard.reaction": "Reakcija", "app.components.ProjectCard.readTheReport": "Pročitajte izvješće", "app.components.ProjectCard.reviewDocument": "Pregledajte dokument", "app.components.ProjectCard.submitAnIssue": "Pošaljite komentar", "app.components.ProjectCard.submitYourIdea": "Pošaljite svoju ideju", "app.components.ProjectCard.submitYourInitiative": "Pošaljite svoju inicijativu", "app.components.ProjectCard.submitYourPetition": "Pošaljite svoju peticiju", "app.components.ProjectCard.submitYourProject": "Pošaljite svoj projekt", "app.components.ProjectCard.submitYourProposal": "Pošaljite svoj prijedlog", "app.components.ProjectCard.takeThePoll": "Popunite anketu", "app.components.ProjectCard.takeTheSurvey": "Popunite upitnik", "app.components.ProjectCard.viewTheContributions": "Prikaz svih doprinosa", "app.components.ProjectCard.viewTheIdeas": "Prikaz ideja", "app.components.ProjectCard.viewTheInitiatives": "Pogledajte inicijative", "app.components.ProjectCard.viewTheIssues": "Prikaz komentara", "app.components.ProjectCard.viewTheOptions": "Prikaz opcija", "app.components.ProjectCard.viewThePetitions": "Pogledajte peticije", "app.components.ProjectCard.viewTheProjects": "Prikaz projekata", "app.components.ProjectCard.viewTheProposals": "Pogledajte prijedloge", "app.components.ProjectCard.viewTheQuestions": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.vote": "Glasanje", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# komentar} other {# komentara}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# doprinos} other {# doprinosa}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {nema ideja} one {# ideja} other {# ideje}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# inicijative} one {# inicijativa} other {# inicijative}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# komentar} other {# komentar}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# opcija} other {# opcije}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# peticije} one {# peticija} other {# peticije}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# projekt} other {# projekta}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# prijedlozi} one {# prijedlog} other {# prijedlozi}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# pitanje} other {# pitanja}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# komentar} one {# komentar} other {# komentar}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# ulaza} one {# unos} other {# ulaza}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projekata} one {# projekt} other {# projekta}}", "app.components.ProjectFolderCards.components.Topbar.all": "Sve", "app.components.ProjectFolderCards.components.Topbar.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.draft": "Nacrt", "app.components.ProjectFolderCards.components.Topbar.filterBy": "<PERSON><PERSON><PERSON><PERSON> prema", "app.components.ProjectFolderCards.components.Topbar.published2": "Objavljeno", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Oznaka", "app.components.ProjectFolderCards.noProjectYet": "Trenutačno nema otvorenih projekata", "app.components.ProjectFolderCards.noProjectsAvailable": "<PERSON><PERSON> proje<PERSON>", "app.components.ProjectFolderCards.showMore": "Prikaži više", "app.components.ProjectFolderCards.stayTuned": "Provjerite kasnije postoje li nove prilike za angažman", "app.components.ProjectFolderCards.tryChangingFilters": "Pokušajte izmijeniti odabrane filtre.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Također se koristi u ovim gradovima:", "app.components.ProjectTemplatePreview.copied": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.copyLink": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.alignCenter": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "app.components.QuillEditor.alignLeft": "Poravnajte ulijevo", "app.components.QuillEditor.alignRight": "Poravnajte udesno", "app.components.QuillEditor.bold": "Podebljano", "app.components.QuillEditor.clean": "Ukloni formatiranje", "app.components.QuillEditor.customLink": "<PERSON><PERSON><PERSON><PERSON> gumb", "app.components.QuillEditor.customLinkPrompt": "Unesite vezu:", "app.components.QuillEditor.edit": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.image": "Prenesite sliku", "app.components.QuillEditor.imageAltPlaceholder": "Kratak opis slike", "app.components.QuillEditor.italic": "Kurziv", "app.components.QuillEditor.link": "<PERSON><PERSON><PERSON><PERSON> vezu", "app.components.QuillEditor.linkPrompt": "Unesite vezu:", "app.components.QuillEditor.normalText": "<PERSON><PERSON>", "app.components.QuillEditor.orderedList": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.remove": "Ukloni", "app.components.QuillEditor.save": "Sp<PERSON>i", "app.components.QuillEditor.subtitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.title": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.unorderedList": "<PERSON><PERSON><PERSON><PERSON><PERSON> popis", "app.components.QuillEditor.video": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.videoPrompt": "Unesite videozapis:", "app.components.QuillEditor.visitPrompt": "Posjetite vezu:", "app.components.ReactionControl.completeProfileToReact": "Ispunite svoj profil da biste reagirali", "app.components.ReactionControl.dislike": "Ne sviđa mi se", "app.components.ReactionControl.dislikingDisabledMaxReached": "Dosegli ste maksimalan broj negativnih ocjena u {projectName}", "app.components.ReactionControl.like": "<PERSON><PERSON>", "app.components.ReactionControl.likingDisabledMaxReached": "Dosegli ste maksimalan broj sviđanja u {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Reagiranje će biti omogućeno kada ova faza započne", "app.components.ReactionControl.reactingDisabledPhaseOver": "U ovoj fazi više nije moguće reagirati", "app.components.ReactionControl.reactingDisabledProjectInactive": "Više ne možete reagirati na ideje u {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Reagiranje trenutno nije omogućeno za ovaj projekt", "app.components.ReactionControl.reactingNotPermitted": "Reagiranje je omogućeno samo određenim grupama", "app.components.ReactionControl.reactingNotSignedIn": "Prijavite se kako biste reagirali.", "app.components.ReactionControl.reactingPossibleLater": "Reagiranje će započeti {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Potvrdite svoj identitet kako biste mogli reagirati.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "<PERSON><PERSON> dog<PERSON>: {startDate} u {startTime} do {endDate} u {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "<PERSON><PERSON> dog<PERSON>: {eventDate} od {startTime} do {endTime}.", "app.components.Sharing.linkCopied": "<PERSON><PERSON><PERSON> k<PERSON>na", "app.components.Sharing.or": "ili", "app.components.Sharing.share": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Sharing.shareByEmail": "Podijelite putem e-pošte", "app.components.Sharing.shareByLink": "<PERSON><PERSON><PERSON>", "app.components.Sharing.shareOnFacebook": "Podijelite putem <PERSON>a", "app.components.Sharing.shareOnTwitter": "Podijelite putem Twittera", "app.components.Sharing.shareThisEvent": "Podijelite ovaj dog<PERSON>đ<PERSON>", "app.components.Sharing.shareThisFolder": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Sharing.shareThisProject": "Podijelite ovaj projekt", "app.components.Sharing.shareViaMessenger": "Podijelite putem <PERSON>", "app.components.Sharing.shareViaWhatsApp": "Podijelite putem WhatsAppa", "app.components.SideModal.closeButtonAria": "Zatvori", "app.components.StatusModule.futurePhase": "Gledate fazu koja još nije započ<PERSON>. Moći ćete sudjelovati kada faza počne.", "app.components.StatusModule.modifyYourSubmission1": "Izmijenite svoj podnesak", "app.components.StatusModule.submittedUntil3": "Vaš glas može biti poslan do", "app.components.TopicsPicker.numberOfSelectedTopics": "Odabrani {numberOfSelectedTopics, plural, =0 {nula oznaka} one {jedna oznaka} other {# oznaka}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Proširi sliku", "app.components.UI.MoreActionsMenu.moreOptions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.UI.MoreActionsMenu.showMoreActions": "Prikaži više radnji", "app.components.UI.NewLabel.new": "NOVI", "app.components.UI.PhaseFilter.noAppropriatePhases": "Nisu pronađene odgovarajuće faze za ovaj projekt", "app.components.UI.RemoveImageButton.a11y_removeImage": "Ukloni", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "<PERSON><PERSON><PERSON>", "app.components.Unauthorized.additionalInformationRequired": "Za sudjelovanje su potrebne dodatne informacije.", "app.components.Unauthorized.completeProfile": "Kompletan profil", "app.components.Unauthorized.completeProfileTitle": "Za sudjelovanje ispunite svoj profil", "app.components.Unauthorized.noPermission": "Nemate dozvolu za prikaz ove stranice", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON> nam je, niste ovlašteni za pristup ovoj stranici.", "app.components.Upload.errorImageMaxSizeExceeded": "Slika koju ste odabrali veća je od {maxFileSize} MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Jedna ili više slika koje ste odabrali veće su od {maxFileSize} MB", "app.components.Upload.onlyOneImage": "Možete prenijeti samo 1 sliku", "app.components.Upload.onlyXImages": "Možete prenijeti samo {maxItemsCount} slika", "app.components.Upload.remaining": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Upload.uploadImageLabel": "Odaberite sliku (maks. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Odaberite jednu ili više slika", "app.components.UpsellTooltip.tooltipContent": "Ova značajka nije uključena u vaš trenutni plan. Razgovarajte sa svojim upraviteljem uspjeha vlade ili administratorom da ga otključate.", "app.components.UserName.anonymous": "Anonimno", "app.components.UserName.anonymousTooltip2": "Ovaj korisnik je odlučio anonimizirati svoj doprinos", "app.components.UserName.authorWithNoNameTooltip": "Vaše ime je automatski generirano jer niste unijeli svoje ime. Ažurirajte svoj profil ako ga želite promijeniti.", "app.components.UserName.deletedUser": "nepoznat autor", "app.components.UserName.verified": "Potvrđeno", "app.components.VerificationModal.verifyAuth0": "Potvrdite s NemID-om", "app.components.VerificationModal.verifyBOSA": "Potvrđivanje putem itsme ili eID-ja", "app.components.VerificationModal.verifyBosaFas": "Potvrdite s itsme ili eID-om", "app.components.VerificationModal.verifyClaveUnica": "Potvrđivanje putem Clave Unice", "app.components.VerificationModal.verifyFakeSSO": "Potvrdite lažnim SSO-om", "app.components.VerificationModal.verifyIdAustria": "Potvrdite s ID Austria", "app.components.VerificationModal.verifyKeycloak": "Potvrdite s ID-Portenom", "app.components.VerificationModal.verifyNemLogIn": "Potvrdite pomoću MitID-a", "app.components.VerificationModal.verifyTwoday2": "Potvrdite s BankID-om ili Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Potvrdite vaš identitet", "app.components.VoteControl.budgetingFutureEnabled": "Bit ćete u mogućnosti da rasporedite proračunska sredstva od dana {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Participativni proračuni trenutno nisu omogućeni.", "app.components.VoteControl.budgetingNotPossible": "U ovom trenutku nije moguće izmijeniti proračun.", "app.components.VoteControl.budgetingNotVerified": "<PERSON><PERSON><PERSON> vas da {verifyAccountLink} ka<PERSON> bi<PERSON>.", "app.components.VoteInputs._shared.currencyLeft1": "Ostalo vam je {budgetLeft} / {totalBudget}", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Imate {votesLeft, plural, =0 {bez preostalih kredita} other {# od {totalNumberOfVotes, plural, one {1 kredit} other {# preostalih kredita}}}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Nemate {votesLeft, plural, =0 {preost<PERSON><PERSON> bodova} other {# od {totalNumberOfVotes, plural, one {1 bod} other {# bodova}} preostalo}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "<PERSON><PERSON><PERSON> više {votesLeft, plural, =0 {žetona} other {# od {totalNumberOfVotes, plural, one {1 žeton} other {# žetona}} preostalo}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Nemate {votesLeft, plural, =0 {preost<PERSON><PERSON> glasova} other {# od {totalNumberOfVotes, plural, one {1 glas} other {# glasova}} preostalo}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Već ste poslali svoj proračun. Da biste ga izmijenili, kliknite \"Izmijeni svoj prijedlog\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Već ste poslali svoj proračun. Da biste ga izmijenili, vratite se na stranicu projekta i kliknite \"Izmijeni svoj prijedlog\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Proračun nije dostupan jer ova faza nije aktivna.", "app.components.VoteInputs.single.youHaveVotedForX2": "<PERSON><PERSON><PERSON><PERSON> ste za {votes, plural, =0 {# opcija} one {#opcija} other {# opcija}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "To znači da ćete izgubiti sve podatke povezane s ovim unosom, poput komentara, reakcija i glasova. Ova se radnja ne može poništiti.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Jeste li sigurni da želite izbrisati ovaj unos?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Potvrdi", "app.components.admin.SlugInput.resultingURL": "Rezultirajući URL", "app.components.admin.SlugInput.slugTooltip": "„P<PERSON>ž“ je jedinstveni skup riječi na kraju web adrese stranice ili URL-a.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "<PERSON>ko promijenite URL, veze prema ovoj stranici pomoću starog URL-a više neće raditi.", "app.components.admin.SlugInput.urlSlugLabel": "P<PERSON>ž", "app.components.admin.UserFilterConditions.addCondition": "Do<PERSON>j uvjet", "app.components.admin.UserFilterConditions.field_email": "E-pošta", "app.components.admin.UserFilterConditions.field_event_attendance": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_follow": "s<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_lives_in": "Živi u", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Monitoring ankete zajednice", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Dodao je status unosu", "app.components.admin.UserFilterConditions.field_participated_in_project": "Dao je doprinos projektu", "app.components.admin.UserFilterConditions.field_participated_in_topic": "<PERSON><PERSON><PERSON><PERSON> je nešto s <PERSON>na<PERSON>m", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Datum registracije", "app.components.admin.UserFilterConditions.field_role": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_verified": "Potvrda", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideacija", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Prijedlozi", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "nije registriran ni za jedan od ovih događaja", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "nije prijavljen ni za jedan događaj", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "je prijavljen za jedan od ovih događaja", "app.components.admin.UserFilterConditions.predicate_attends_something": "je prijavljen za najmanje jedan događaj", "app.components.admin.UserFilterConditions.predicate_begins_with": "po<PERSON><PERSON><PERSON> s", "app.components.admin.UserFilterConditions.predicate_commented_in": "komentirao", "app.components.admin.UserFilterConditions.predicate_contains": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_ends_on": "završava se na", "app.components.admin.UserFilterConditions.predicate_has_value": "ima vrijednost", "app.components.admin.UserFilterConditions.predicate_in": "o<PERSON><PERSON> bilo koju radnju", "app.components.admin.UserFilterConditions.predicate_is": "je", "app.components.admin.UserFilterConditions.predicate_is_admin": "je administrator", "app.components.admin.UserFilterConditions.predicate_is_after": "je nakon", "app.components.admin.UserFilterConditions.predicate_is_before": "je prije", "app.components.admin.UserFilterConditions.predicate_is_checked": "je provjeren", "app.components.admin.UserFilterConditions.predicate_is_empty": "je prazno", "app.components.admin.UserFilterConditions.predicate_is_equal": "je", "app.components.admin.UserFilterConditions.predicate_is_exactly": "je to<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "je veće od", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "je veće ili jednako", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "je obi<PERSON><PERSON> koris<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_not_area": "isključuje <PERSON>ruč<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "isključuje mapu", "app.components.admin.UserFilterConditions.predicate_is_not_input": "isključ<PERSON><PERSON> un<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_not_project": "isključuje projekt", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "isključuje temu", "app.components.admin.UserFilterConditions.predicate_is_one_of": "je jedan od", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "jedno od područja", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "jedan od <PERSON>a", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "jedan od ul<PERSON>a", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "jedan od projekata", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "jedna od tema", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "je voditelj projekta", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "je manje od", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "je manje ili jednako", "app.components.admin.UserFilterConditions.predicate_is_verified": "je provjer<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "ne počinje s", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "nije komenti<PERSON>o", "app.components.admin.UserFilterConditions.predicate_not_contains": "ne <PERSON><PERSON><PERSON>i", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "ne zav<PERSON>va se na", "app.components.admin.UserFilterConditions.predicate_not_has_value": "ne sadrži vrijednost", "app.components.admin.UserFilterConditions.predicate_not_in": "nije doprinio", "app.components.admin.UserFilterConditions.predicate_not_is": "nije", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "nije administrator", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "nije provjerio", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "nije p<PERSON>no", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "nije", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "nije obi<PERSON> koris<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "nije jedan od", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "nije voditelj projekta", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "nije obja<PERSON> unos", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "nije reagirao na komentar", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "nije reagirao na unos", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "nije se registrirao za događaj", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "nije prist<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "nije se dobrovoljno prijavio/la", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "nije sudjelovao u glasovanju", "app.components.admin.UserFilterConditions.predicate_nothing": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_posted_input": "obja<PERSON> je unos", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reagirao na komentar", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reagirao na unos", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "prijavljeni na događaj", "app.components.admin.UserFilterConditions.predicate_something": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_taken_survey": "<PERSON><PERSON><PERSON> je anketu", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "dobrovoljno se prijavio", "app.components.admin.UserFilterConditions.predicate_voted_in3": "sudjelovali u glasovanju", "app.components.admin.UserFilterConditions.rulesFormLabelField": "atribut", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "Vrijednost", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Nećete dobivati obavijesti o svom doprinosu", "app.components.anonymousParticipationModal.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.participateAnonymously": "Sudjelujte anonimno", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "<PERSON><PERSON> će sigurno <b>sakriti va<PERSON> profil</b> od administratora, voditelja projekta i drugih stanovnika za ovaj specifični doprinos tako da nitko ne može povezati ovaj doprinos s vama. Anonimni doprinosi se ne mogu uređivati i smatraju se konačnima.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Učiniti našu platformu sigurnom za svakog korisnika glavni nam je prioritet. Riječi su važne, stoga budite ljubazni jedni prema drugima.", "app.components.avatar.titleForAccessibility": "Profil od {fullName}", "app.components.customFields.mapInput.removeAnswer": "Ukloni odgovor", "app.components.customFields.mapInput.undo": "<PERSON><PERSON><PERSON><PERSON>", "app.components.customFields.mapInput.undoLastPoint": "Poništi posljednju točku", "app.components.followUnfollow.follow": "s<PERSON><PERSON><PERSON><PERSON>", "app.components.followUnfollow.followADiscussion": "<PERSON><PERSON><PERSON> raspra<PERSON>", "app.components.followUnfollow.followTooltipInputPage2": "Sljedeće pokreće ažuriranja e-poštom o promjenama statusa, službenim ažuriranjima i komentarima. Možete {unsubscribeLink} bilo kada.", "app.components.followUnfollow.followTooltipProjects2": "Slijedeći pokreće ažuriranja e-poštom o promjenama projekta. Možete {unsubscribeLink} bilo kada.", "app.components.followUnfollow.unFollow": "<PERSON><PERSON><PERSON> pratiti", "app.components.followUnfollow.unsubscribe": "o<PERSON><PERSON><PERSON> pre<PERSON>u", "app.components.followUnfollow.unsubscribeUrl": "/profil/uredi", "app.components.form.ErrorDisplay.guidelinesLinkText": "na<PERSON><PERSON> s<PERSON>e", "app.components.form.ErrorDisplay.next": "Sljedeći", "app.components.form.ErrorDisplay.previous": "Natrag", "app.components.form.ErrorDisplay.save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Započnite pretragu unosom korisnikove e-pošte ili imena...", "app.components.form.anonymousSurveyMessage2": "Svi odgovori na ovu anketu su anonimizirani.", "app.components.form.backToInputManager": "Natrag na upravitelj unosa", "app.components.form.backToProject": "Povratak na projekt", "app.components.form.components.controls.mapInput.removeAnswer": "Ukloni odgovor", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON><PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "Poništavanje zadnje točke", "app.components.form.controls.addressInputAriaLabel": "Unos adrese", "app.components.form.controls.addressInputPlaceholder6": "Unesite adresu...", "app.components.form.controls.adminFieldTooltip": "Polje je vidljivo samo administratorima", "app.components.form.controls.allStatementsError": "Za sve tvrdnje potrebno je odabrati odgovor.", "app.components.form.controls.back": "<PERSON><PERSON><PERSON>", "app.components.form.controls.clearAll": "Obriši sve", "app.components.form.controls.clearAllScreenreader": "Izbrišite sve odgovore iz gornjeg pitanja matrice", "app.components.form.controls.clickOnMapMultipleToAdd3": "Kliknite na kartu za crtanje. Zatim povucite točke da biste ih pomaknuli.", "app.components.form.controls.clickOnMapToAddOrType": "Kliknite na kartu ili upišite adresu ispod kako biste dodali svoj odgovor.", "app.components.form.controls.confirm": "Potvrdi", "app.components.form.controls.cosponsorsPlaceholder": "Počnite upisivati ime za pretraživanje", "app.components.form.controls.currentRank": "<PERSON><PERSON><PERSON><PERSON><PERSON> rang:", "app.components.form.controls.minimumCoordinates2": "Potreb<PERSON> je najmanje {numPoints} to<PERSON><PERSON> karte.", "app.components.form.controls.noRankSelected": "<PERSON><PERSON> o<PERSON> rang", "app.components.form.controls.notPublic1": "*Ovaj odgovor će biti podijeljen samo s voditeljima projekta, a ne javnosti.", "app.components.form.controls.optionalParentheses": "(neobavezno)", "app.components.form.controls.rankingInstructions": "Povucite i ispustite za opcije rangiranja.", "app.components.form.controls.selectAsManyAsYouLike": "*Odaberite onoliko koliko želite", "app.components.form.controls.selectBetween": "*Odaberite između {minItems} i {maxItems} opcije", "app.components.form.controls.selectExactly2": "*Odaberi točno {selectExactly, plural, one {#opcija} other {# opcija}}", "app.components.form.controls.selectMany": "*Odaberite koliko god <PERSON>", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Dodirnite kartu za crtanje. Zatim povucite točke da biste ih pomaknuli.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Dodirnite kartu za crtanje.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Dodirnite kartu da dodate svoj odgovor.", "app.components.form.controls.tapOnMapToAddOrType": "Dodirnite kartu ili upišite adresu ispod kako biste dodali svoj odgovor.", "app.components.form.controls.tapToAddALine": "Dodirnite za dodavanje linije", "app.components.form.controls.tapToAddAPoint": "Dodirnite za dodavanje točke", "app.components.form.controls.tapToAddAnArea": "Dodirnite za dodavanje područja", "app.components.form.controls.uploadShapefileInstructions": "* Učitajte zip datoteku koja sadrži jednu ili više datoteka oblika.", "app.components.form.controls.validCordinatesTooltip2": "Ako se lokacija ne prikazuje među opcijama dok upisujete, mož<PERSON> dodati valjane koordinate u formatu 'geografska širina, du<PERSON><PERSON>' kako biste odredili to<PERSON><PERSON> lo<PERSON> (npr.: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} od {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} od {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} od {total}, gdje je {maxValue} {maxLabel}", "app.components.form.error": "Pogreška", "app.components.form.locationGoogleUnavailable": "Učitavanje polja s lokacijom s Google Mapa nije uspjelo.", "app.components.form.progressBarLabel": "Napredak ankete", "app.components.form.submit": "Pošalji", "app.components.form.submitApiError": "<PERSON><PERSON><PERSON> je do problema prilikom slanja obrasca. Molimo vas da provjerite pogreške i pokušate ponovo.", "app.components.form.verifiedBlocked": "Ne možete uređivati ovo polje jer sadrži provjerene podatke", "app.components.formBuilder.Page": "Stranica", "app.components.formBuilder.accessibilityStatement": "izjava o pristupačnosti", "app.components.formBuilder.addAnswer": "Dodaj odgovor", "app.components.formBuilder.addStatement": "Do<PERSON>j <PERSON>", "app.components.formBuilder.agree": "Slažem se", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "<PERSON><PERSON> imate pristup našem AI paketu, moći ćete sažeti i kategorizirati tekstualne odgovore pomoću AI", "app.components.formBuilder.askFollowUpToggleLabel": "Zatražite praćenje", "app.components.formBuilder.bad": "<PERSON><PERSON>", "app.components.formBuilder.buttonLabel": "Oznaka gumba", "app.components.formBuilder.buttonLink": "<PERSON>eza gumba", "app.components.formBuilder.cancelLeaveBuilderButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.category": "Kategorija", "app.components.formBuilder.chooseMany": "Odaberite mnogo", "app.components.formBuilder.chooseOne": "Odaberite jedan", "app.components.formBuilder.close": "Zatvori", "app.components.formBuilder.closed": "Zatvoreno", "app.components.formBuilder.configureMap": "Konfigurirajte kartu", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON>, <PERSON><PERSON><PERSON>", "app.components.formBuilder.content": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.continuePageLabel": "Nastavlja se", "app.components.formBuilder.cosponsors": "Supokrovitelji", "app.components.formBuilder.default": "Zadano", "app.components.formBuilder.defaultContent": "Zadani <PERSON>", "app.components.formBuilder.delete": "Izbriši", "app.components.formBuilder.deleteButtonLabel": "Izbriši", "app.components.formBuilder.description": "Opis", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Ovo je već dodano u obrazac. Zadani sadržaj može se koristiti samo jednom.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Dodavanje prilagođenog sadržaja nije dio vaše trenutne licence. Obratite se svom GovSuccess Manageru da saznate više o tome.", "app.components.formBuilder.disagree": "Ne slažem se", "app.components.formBuilder.displayAsDropdown": "Prikaži kao padajući izbornik", "app.components.formBuilder.displayAsDropdownTooltip": "Prikažite opcije u padajućem izborniku. Ako imate mnogo opcija, ovo se preporučuje.", "app.components.formBuilder.done": "Gotovo", "app.components.formBuilder.drawArea": "Područje crtanja", "app.components.formBuilder.drawRoute": "Nac<PERSON>j rutu", "app.components.formBuilder.dropPin": "Spustite iglu", "app.components.formBuilder.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "Navedite barem 1 odgovor. Napominjemo da svaki odgovor mora imati naslov.", "app.components.formBuilder.emptyOptionError": "Navedite barem 1 odgovor", "app.components.formBuilder.emptyStatementError": "Navedite barem 1 izjavu", "app.components.formBuilder.emptyTitleError": "Navedite na<PERSON>lov <PERSON>an<PERSON>", "app.components.formBuilder.emptyTitleMessage": "Navedite naslov za sve odgovore", "app.components.formBuilder.emptyTitleStatementMessage": "Navedite naslov za sve izjave", "app.components.formBuilder.enable": "Aktivirano", "app.components.formBuilder.errorMessage": "<PERSON><PERSON><PERSON> je do problema, riješite problem kako biste mogli spremiti svoje promjene", "app.components.formBuilder.fieldGroup.description": "Opis (nije obvezno)", "app.components.formBuilder.fieldGroup.title": "Na<PERSON>lov (nije obvezno)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Trenutačno su odgovori na ova pitanja dostupni samo u izvezenoj excel datoteci na Input Manageru i nisu vidljivi korisnicima.", "app.components.formBuilder.fieldLabel": "Izbori odgovora", "app.components.formBuilder.fieldLabelStatement": "Izjave", "app.components.formBuilder.fileUpload": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Stranica temeljena na karti", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Ugradite kartu kao kontekst ili postavite sudionicima pitanja o lokaciji.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Za optimalno korisničko iskustvo ne preporučujemo dodavanje pitanja o točki, ruti ili području na stranice temeljene na karti.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normalna stranica", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Značajke mapiranja ankete nisu uključene u vašu trenutnu licencu. Obratite se svom GovSuccess Manageru da biste saznali više.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "<PERSON>rsta stranice", "app.components.formBuilder.formEnd": "<PERSON><PERSON>", "app.components.formBuilder.formField.cancelDeleteButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "Da, izbriši stranicu", "app.components.formBuilder.formField.copyNoun": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.copyVerb": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.delete": "Izbrisati", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Brisanje ove stranice također će izbrisati logiku povezanu s njom. Jeste li sigurni da ga želite izbrisati?", "app.components.formBuilder.formField.deleteResultsInfo": "To se ne može poništiti", "app.components.formBuilder.goToPageInputLabel": "Sljedeća stranica je:", "app.components.formBuilder.good": "Dobro", "app.components.formBuilder.helmetTitle": "Program za izradu obrazaca", "app.components.formBuilder.imageFileUpload": "Prijenos slike", "app.components.formBuilder.invalidLogicBadgeMessage": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.labels2": "Oznake (neobavezno)", "app.components.formBuilder.labelsTooltipContent2": "Odaberite izborne oznake za bilo koju vrijednost linearnog mjerila.", "app.components.formBuilder.lastPage": "Završetak", "app.components.formBuilder.layout": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Jeste li sigurni da ž<PERSON>te otići?", "app.components.formBuilder.leaveBuilderText": "Imate nespremljene promjene. Spremite prije odlaska. <PERSON><PERSON> o<PERSON>, izgu<PERSON> ćete promjene.", "app.components.formBuilder.limitAnswersTooltip": "<PERSON><PERSON> je <PERSON><PERSON><PERSON><PERSON><PERSON>, ispitanici moraju odabrati određeni broj odgovora da bi nastavili.", "app.components.formBuilder.limitNumberAnswers": "Ograničite broj odgovora", "app.components.formBuilder.linePolygonMapWarning2": "Crtanje linija i poligona možda neće zadovoljiti standarde pristupačnosti. Više informacija možete pronaći u {accessibilityStatement}.", "app.components.formBuilder.linearScale": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.locationDescription": "Lokacija", "app.components.formBuilder.logic": "Logika", "app.components.formBuilder.logicAnyOtherAnswer": "Bilo koji drugi odgovor", "app.components.formBuilder.logicConflicts.conflictingLogic": "Konfliktna logika", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Ova stranica sadrži pitanja koja vode do različitih stranica. Ako sudionici odgovore na više pitanja, prikazat će se najudaljenija stranica. Osigurajte da je ovo ponašanje usklađeno s vašim namjeravanim tokom.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Na ovoj se stranici primjenjuje više logičkih pravila: logika pitanja s višestrukim odabirom, logika na razini stranice i logika između pitanja. Kada se ti uvjeti preklapaju, logika pitanja imat će prednost nad logikom stranice i prikazat će se najudaljenija stranica. Pregledajte logiku kako biste bili sigurni da je usklađena s planiranim tokom.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Ova stranica sadrži pitanje s višestrukim odabirom gdje opcije vode do različitih stranica. Ako sudionici odaberu više opcija, prikazat će se najudaljenija stranica. Osigurajte da je ovo ponašanje usklađeno s vašim namjeravanim tokom.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Ova stranica sadrži pitanje s višestrukim odabirom gdje opcije vode na različite stranice i ima pitanja koja vode na druge stranice. Prikazat će se najudaljenija stranica ako se ovi uvjeti preklapaju. Osigurajte da je ovo ponašanje usklađeno s vašim namjeravanim tokom.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Ova stranica sadrži pitanje s višestrukim odabirom gdje opcije vode do različitih stranica i ima logiku postavljenu i na razini stranice i na razini pitanja. Logika pitanja će imati prednost i bit će prikazana najdalja stranica. Osigurajte da je ovo ponašanje usklađeno s vašim namjeravanim tokom.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Ova stranica ima logiku postavljenu i na razini stranice i na razini pitanja. Logika pitanja imat će prednost nad logikom na razini stranice. Osigurajte da je ovo ponašanje usklađeno s vašim namjeravanim tokom.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Ova stranica ima logiku postavljenu na razini stranice i pitanja, a više pitanja usmjerava na različite stranice. Logika pitanja će imati prednost i bit će prikazana najdalja stranica. Osigurajte da je ovo ponašanje usklađeno s vašim namjeravanim tokom.", "app.components.formBuilder.logicNoAnswer2": "<PERSON><PERSON> o<PERSON>", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Ako neki drugi odgovor", "app.components.formBuilder.logicPanelNoAnswer": "Ako se ne odgovori", "app.components.formBuilder.logicValidationError": "Logika možda neće povezivati na prethodne stranice", "app.components.formBuilder.longAnswer": "<PERSON>gi odgovor", "app.components.formBuilder.mapConfiguration": "Konfiguracija karte", "app.components.formBuilder.mapping": "Mapiranje", "app.components.formBuilder.mappingNotInCurrentLicense": "Značajke mapiranja ankete nisu uključene u vašu trenutnu licencu. Obratite se svom GovSuccess Manageru da biste saznali više.", "app.components.formBuilder.matrix": "Matrica", "app.components.formBuilder.matrixSettings.columns": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "Višestruki izbor", "app.components.formBuilder.multipleChoiceHelperText": "Ako više opcija vodi do različitih stranica i sudionici odaberu više od jedne, prikazat će se najudaljenija stranica. Osigurajte da je ovo ponašanje usklađeno s vašim namjeravanim tokom.", "app.components.formBuilder.multipleChoiceImage": "<PERSON>z<PERSON> slike", "app.components.formBuilder.multiselect.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.newField": "Novo polje", "app.components.formBuilder.number": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.ok": "U redu", "app.components.formBuilder.open": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.optional": "<PERSON><PERSON>", "app.components.formBuilder.other": "ostalo", "app.components.formBuilder.otherOption": "Opcija \"Ostalo\".", "app.components.formBuilder.otherOptionTooltip": "Dopustite sudionicima da unesu prilagođeni odgovor ako ponuđeni odgovori ne odgovaraju n<PERSON><PERSON>el<PERSON>", "app.components.formBuilder.page": "Stranica", "app.components.formBuilder.pageCannotBeDeleted": "Ovu stranicu nije moguće izbrisati.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Ova se stranica ne može izbrisati i ne dopušta dodavanje dodatnih polja.", "app.components.formBuilder.pageRuleLabel": "Sljedeća stranica je:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Ako se ne doda nikakva logika, obra<PERSON> će slijediti svoj normalni tijek. Ako i stranica i njena pitanja imaju logiku, logika pitanja će imati prednost. Provjerite je li ovo usklađeno s vašim planiranim protokom. Za više informacija posjetite {supportPageLink}", "app.components.formBuilder.preview": "Pregled:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Su-sponzori nisu prikazani na preuzetom PDF-u i nije podržan njihov uvoz putem FormSynca.", "app.components.formBuilder.printSupportTooltip.fileupload": "Pitanja za prijenos datoteka prikazana su kao nepodržana u preuzetom PDF-u i nije ih moguće uvoziti putem FormSynca.", "app.components.formBuilder.printSupportTooltip.mapping": "Pitanja za mapiranje prikazana su u preuzetom PDF-u, ali slojevi neće biti vidljivi. Pitanja za mapiranje nije podržano za uvoz putem FormSynca.", "app.components.formBuilder.printSupportTooltip.matrix": "Matrična pitanja prikazana su u preuzetom PDF-u, ali trenutno nije podržan njihov uvoz putem FormSynca.", "app.components.formBuilder.printSupportTooltip.page": "Naslovi i opisi stranica prikazuju se kao zaglavlje odjeljka u preuzetom PDF-u.", "app.components.formBuilder.printSupportTooltip.ranking": "Pitanja za rangiranje prikazana su u preuzetom PDF-u, ali trenutno nije podržan njihov uvoz putem FormSynca.", "app.components.formBuilder.printSupportTooltip.topics2": "Oznake su prikazane kao nepodržane u preuzetom PDF-u i nisu podržane za uvoz putem FormSynca.", "app.components.formBuilder.proposedBudget": "Predloženi proračun", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "Ovo se pitanje ne može izbrisati.", "app.components.formBuilder.questionDescriptionOptional": "<PERSON><PERSON> pitanja (nije o<PERSON>)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.randomize": "Nasumič<PERSON> odredi", "app.components.formBuilder.randomizeToolTip": "Redoslijed odgovora bit će nasumičan po korisniku", "app.components.formBuilder.range": "<PERSON><PERSON>", "app.components.formBuilder.ranking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.rating": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.removeAnswer": "Ukloni odgovor", "app.components.formBuilder.required": "Neoph<PERSON><PERSON>", "app.components.formBuilder.requiredToggleLabel": "Neka odgovor na ovo pitanje bude obavezan", "app.components.formBuilder.ruleForAnswerLabel": "Ako je odgovor:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "Ako odgovori uključuju:", "app.components.formBuilder.save": "Sp<PERSON>i", "app.components.formBuilder.selectRangeTooltip": "Odaberite maksimalnu vrijednost za vašu ljestvicu.", "app.components.formBuilder.sentiment": "S<PERSON><PERSON>", "app.components.formBuilder.shapefileUpload": "Prijenos Esri shapefile", "app.components.formBuilder.shortAnswer": "Kraći odgovor", "app.components.formBuilder.showResponseToUsersToggleLabel": "Prikaži odgovor k<PERSON>", "app.components.formBuilder.singleChoice": "<PERSON><PERSON>", "app.components.formBuilder.staleDataErrorMessage2": "<PERSON><PERSON><PERSON> je do problema. <PERSON><PERSON><PERSON> obrazac za unos nedavno je spremljen negdje drugdje. To može biti zato što ste ga vi ili drugi korisnik otvorili za uređivanje u drugom prozoru preglednika. Osvježite stranicu kako biste dobili najnoviji obrazac i zatim ponovno izvršite promjene.", "app.components.formBuilder.stronglyAgree": "U potpunosti se slažem", "app.components.formBuilder.stronglyDisagree": "Uopće se ne slažem", "app.components.formBuilder.supportArticleLinkText": "ova stranica", "app.components.formBuilder.tags": "Oznake", "app.components.formBuilder.title": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.toLabel": "sa", "app.components.formBuilder.unsavedChanges": "<PERSON><PERSON> promjene", "app.components.formBuilder.useCustomButton2": "<PERSON><PERSON><PERSON> prilagođeni gumb stranice", "app.components.formBuilder.veryBad": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.veryGood": "Vrlo dobro", "app.components.ideas.similarIdeas.engageHere": "Uključite se ovdje", "app.components.ideas.similarIdeas.noSimilarSubmissions": "<PERSON><PERSON>.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Pronašli smo slične podneske - interakcija s njima može pomoći da ojačaju!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Slični podnesci već su objavljeni:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Traženje sličnih prijava...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON><PERSON> od jednog dana} one {# dan} other {Još # dana}}", "app.components.phaseTimeLeft.xWeeksLeft": "<PERSON><PERSON> {timeLeft}  tje<PERSON>a", "app.components.screenReaderCurrency.AED": "<PERSON><PERSON><PERSON> Emira<PERSON>", "app.components.screenReaderCurrency.AFN": "afganistanski afgani", "app.components.screenReaderCurrency.ALL": "albanski lek", "app.components.screenReaderCurrency.AMD": "armenski dram", "app.components.screenReaderCurrency.ANG": "nizozemski antilski gulden", "app.components.screenReaderCurrency.AOA": "angolska kvanza", "app.components.screenReaderCurrency.ARS": "argentinski pezo", "app.components.screenReaderCurrency.AUD": "australski dolar", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "azerbajdžanski manat", "app.components.screenReaderCurrency.BAM": "bosanskohercegovačka konvertibilna marka", "app.components.screenReaderCurrency.BBD": "Barbadoš<PERSON> dolar", "app.components.screenReaderCurrency.BDT": "bangladeška taka", "app.components.screenReaderCurrency.BGN": "bug<PERSON><PERSON> lev", "app.components.screenReaderCurrency.BHD": "bahreinski dinar", "app.components.screenReaderCurrency.BIF": "burunds<PERSON> franak", "app.components.screenReaderCurrency.BMD": "Bermuds<PERSON> dolar", "app.components.screenReaderCurrency.BND": "<PERSON><PERSON><PERSON>ski dolar", "app.components.screenReaderCurrency.BOB": "bolivijski bolivijano", "app.components.screenReaderCurrency.BOV": "bolivijski Mvdol", "app.components.screenReaderCurrency.BRL": "Brazilski Real", "app.components.screenReaderCurrency.BSD": "Bahamski dolar", "app.components.screenReaderCurrency.BTN": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BWP": "Bocvanska Pula", "app.components.screenReaderCurrency.BYR": "Bjeloruska rublja", "app.components.screenReaderCurrency.BZD": "<PERSON><PERSON><PERSON> dolar", "app.components.screenReaderCurrency.CAD": "kanadski dolar", "app.components.screenReaderCurrency.CDF": "kongoanski franak", "app.components.screenReaderCurrency.CHE": "WIR euro", "app.components.screenReaderCurrency.CHF": "švicarski franak", "app.components.screenReaderCurrency.CHW": "WIR franak", "app.components.screenReaderCurrency.CLF": "Čileanska obračunska jedinica (UF)", "app.components.screenReaderCurrency.CLP": "Čileanski pezo", "app.components.screenReaderCurrency.CNY": "kineski juan", "app.components.screenReaderCurrency.COP": "kolumbijski pezo", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Kostarikanski Colón", "app.components.screenReaderCurrency.CRE": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CUC": "Kubanski konvertibilni pezo", "app.components.screenReaderCurrency.CUP": "kubanski pezo", "app.components.screenReaderCurrency.CVE": "Zelenortski Eskudo", "app.components.screenReaderCurrency.CZK": "češka kruna", "app.components.screenReaderCurrency.DJF": "džibutski franak", "app.components.screenReaderCurrency.DKK": "danska kruna", "app.components.screenReaderCurrency.DOP": "dominikanski pezo", "app.components.screenReaderCurrency.DZD": "<PERSON><PERSON><PERSON><PERSON> dinar", "app.components.screenReaderCurrency.EGP": "Egipatska funta", "app.components.screenReaderCurrency.ERN": "Eritrejska nakfa", "app.components.screenReaderCurrency.ETB": "etiopski bir", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "fidžijski dolar", "app.components.screenReaderCurrency.FKP": "Falklandska funta", "app.components.screenReaderCurrency.GBP": "Britanska funta", "app.components.screenReaderCurrency.GEL": "gru<PERSON><PERSON><PERSON> lari", "app.components.screenReaderCurrency.GHS": "ganski cedi", "app.components.screenReaderCurrency.GIP": "Gibraltarska funta", "app.components.screenReaderCurrency.GMD": "gambijski dalasi", "app.components.screenReaderCurrency.GNF": "gvinejski franak", "app.components.screenReaderCurrency.GTQ": "Gvatemalski Quetzal", "app.components.screenReaderCurrency.GYD": "<PERSON><PERSON><PERSON><PERSON> dolar", "app.components.screenReaderCurrency.HKD": "hongkonški dolar", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "hrvatska kuna", "app.components.screenReaderCurrency.HTG": "Haić<PERSON><PERSON>", "app.components.screenReaderCurrency.HUF": "<PERSON><PERSON><PERSON><PERSON> forinta", "app.components.screenReaderCurrency.IDR": "indonezijska rupija", "app.components.screenReaderCurrency.ILS": "Izraelski novi šekel", "app.components.screenReaderCurrency.INR": "indijska rupija", "app.components.screenReaderCurrency.IQD": "irački dinar", "app.components.screenReaderCurrency.IRR": "Iranski rijal", "app.components.screenReaderCurrency.ISK": "islandska kruna", "app.components.screenReaderCurrency.JMD": "Jamajkanski dolar", "app.components.screenReaderCurrency.JOD": "jordanski dinar", "app.components.screenReaderCurrency.JPY": "japanski jen", "app.components.screenReaderCurrency.KES": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KGS": "kirgistanski som", "app.components.screenReaderCurrency.KHR": "kambodžanski rijel", "app.components.screenReaderCurrency.KMF": "komorski franak", "app.components.screenReaderCurrency.KPW": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "app.components.screenReaderCurrency.KRW": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "app.components.screenReaderCurrency.KWD": "kuvajtski dinar", "app.components.screenReaderCurrency.KYD": "<PERSON><PERSON>a", "app.components.screenReaderCurrency.KZT": "kazahstanski tenge", "app.components.screenReaderCurrency.LAK": "lao kip", "app.components.screenReaderCurrency.LBP": "libanonska funta", "app.components.screenReaderCurrency.LKR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.LRD": "Liberijski dolar", "app.components.screenReaderCurrency.LSL": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LTL": "litavski litas", "app.components.screenReaderCurrency.LVL": "latvijski lat", "app.components.screenReaderCurrency.LYD": "libijski dinar", "app.components.screenReaderCurrency.MAD": "marokanski dirham", "app.components.screenReaderCurrency.MDL": "moldavski lej", "app.components.screenReaderCurrency.MGA": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "app.components.screenReaderCurrency.MKD": "<PERSON><PERSON><PERSON> denar", "app.components.screenReaderCurrency.MMK": "<PERSON><PERSON><PERSON><PERSON> kjat", "app.components.screenReaderCurrency.MNT": "mongolski Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "mauritanska Ouguiya", "app.components.screenReaderCurrency.MUR": "mauricijanska rupija", "app.components.screenReaderCurrency.MVR": "Maldivska rufija", "app.components.screenReaderCurrency.MWK": "malavijska kvača", "app.components.screenReaderCurrency.MXN": "meksički pezo", "app.components.screenReaderCurrency.MXV": "Meksički Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "<PERSON><PERSON><PERSON><PERSON> ringit", "app.components.screenReaderCurrency.MZN": "mozambijski metikal", "app.components.screenReaderCurrency.NAD": "namibijski dolar", "app.components.screenReaderCurrency.NGN": "Nigerijska Naira", "app.components.screenReaderCurrency.NIO": "Nikaragvanska Córdoba", "app.components.screenReaderCurrency.NOK": "Norve<PERSON><PERSON> k<PERSON>a", "app.components.screenReaderCurrency.NPR": "nepalska rupija", "app.components.screenReaderCurrency.NZD": "novozelandski dolar", "app.components.screenReaderCurrency.OMR": "o<PERSON><PERSON> rijal", "app.components.screenReaderCurrency.PAB": "panamski balboa", "app.components.screenReaderCurrency.PEN": "peruanski sol", "app.components.screenReaderCurrency.PGK": "Papua Nova Gvineja Kina", "app.components.screenReaderCurrency.PHP": "filipinski pezo", "app.components.screenReaderCurrency.PKR": "pakistanska rupija", "app.components.screenReaderCurrency.PLN": "poljski zloti", "app.components.screenReaderCurrency.PYG": "paragvajski guaraní", "app.components.screenReaderCurrency.QAR": "katarski rijal", "app.components.screenReaderCurrency.RON": "rumunjski lej", "app.components.screenReaderCurrency.RSD": "srpski dinar", "app.components.screenReaderCurrency.RUB": "<PERSON><PERSON><PERSON> rubl<PERSON>", "app.components.screenReaderCurrency.RWF": "ruandski franak", "app.components.screenReaderCurrency.SAR": "saudijski rijal", "app.components.screenReaderCurrency.SBD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.SCR": "sejšelska rupija", "app.components.screenReaderCurrency.SDG": "Sudanska funta", "app.components.screenReaderCurrency.SEK": "švedska kruna", "app.components.screenReaderCurrency.SGD": "singap<PERSON><PERSON> dolar", "app.components.screenReaderCurrency.SHP": "funta Svete Helene", "app.components.screenReaderCurrency.SLL": "Sijera Leone Leone", "app.components.screenReaderCurrency.SOS": "so<PERSON><PERSON> š<PERSON>", "app.components.screenReaderCurrency.SRD": "Surinamski dolar", "app.components.screenReaderCurrency.SSP": "južnosudanska funta", "app.components.screenReaderCurrency.STD": "São Tomé i Príncipe Dobra", "app.components.screenReaderCurrency.SYP": "sir<PERSON>j<PERSON> funta", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "tajlandski bat", "app.components.screenReaderCurrency.TJS": "tadžikistanski somoni", "app.components.screenReaderCurrency.TMT": "Turkmenistanski manat", "app.components.screenReaderCurrency.TND": "tuniski dinar", "app.components.screenReaderCurrency.TOK": "Znak", "app.components.screenReaderCurrency.TOP": "tonganska Paʻanga", "app.components.screenReaderCurrency.TRY": "Turska lira", "app.components.screenReaderCurrency.TTD": "dolar Trinidada i Tobaga", "app.components.screenReaderCurrency.TWD": "Novi tajvanski dolar", "app.components.screenReaderCurrency.TZS": "tan<PERSON><PERSON><PERSON><PERSON> š<PERSON>", "app.components.screenReaderCurrency.UAH": "Ukrajinska grivna", "app.components.screenReaderCurrency.UGX": "ugandski šiling", "app.components.screenReaderCurrency.USD": "ameri<PERSON><PERSON> dolar", "app.components.screenReaderCurrency.USN": "Američ<PERSON> do<PERSON> (sljedeći dan)", "app.components.screenReaderCurrency.USS": "Ameri<PERSON><PERSON> do<PERSON> (isti dan)", "app.components.screenReaderCurrency.UYI": "urugvajski pezo en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "urugvajski pezo", "app.components.screenReaderCurrency.UZS": "uzbekistanski som", "app.components.screenReaderCurrency.VEF": "venezuelanski bolivar", "app.components.screenReaderCurrency.VND": "vijetnamski Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "samoanska tala", "app.components.screenReaderCurrency.XAF": "Srednjoafrički CFA franak", "app.components.screenReaderCurrency.XAG": "Srebro (jedna troy unca)", "app.components.screenReaderCurrency.XAU": "<PERSON><PERSON><PERSON> (jedna troy unca)", "app.components.screenReaderCurrency.XBA": "Europska kompozitna jedinica (EURCO)", "app.components.screenReaderCurrency.XBB": "Europska monetarna jedinica (EMU-6)", "app.components.screenReaderCurrency.XBC": "Europska obračunska jedinica 9 (EUA-9)", "app.components.screenReaderCurrency.XBD": "Europska obračunska jedinica 17 (EUA-17)", "app.components.screenReaderCurrency.XCD": "istočnokaripski dolar", "app.components.screenReaderCurrency.XDR": "Posebna prava vučenja", "app.components.screenReaderCurrency.XFU": "UIC franc", "app.components.screenReaderCurrency.XOF": "Zapadnoafrički CFA franak", "app.components.screenReaderCurrency.XPD": "<PERSON><PERSON><PERSON><PERSON> (jedna troy unca)", "app.components.screenReaderCurrency.XPF": "CFP franak", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON><PERSON> (jedna troy unca)", "app.components.screenReaderCurrency.XTS": "<PERSON><PERSON><PERSON> rezervirani za potrebe testiranja", "app.components.screenReaderCurrency.XXX": "<PERSON><PERSON> valute", "app.components.screenReaderCurrency.YER": "jemenski rijal", "app.components.screenReaderCurrency.ZAR": "južnoafrič<PERSON> rand", "app.components.screenReaderCurrency.ZMW": "zambijska kvača", "app.components.screenReaderCurrency.amount": "Iznos", "app.components.screenReaderCurrency.currency": "Valuta", "app.components.trendIndicator.lastQuarter2": "zadnja četvrtina", "app.containers.AccessibilityStatement.applicability": "Ova se izjava o pristupačnosti odnosi na {demoPlatformLink}, a koji predstavlja ovu web-lokaciju; koristi isti izvorni kod i ima istu funkcionalnost.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Metoda procjene", "app.containers.AccessibilityStatement.assesmentText2022": "Pristupačnost ovog web-mjesta procijenjena je od strane vanjskog subjekta koji nije uključen u postupak dizajna i razvoja Usklađenost prije spomenutog {demoPlatformLink} može se identificirati ovdje {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "možete izmijeniti svoje postavke", "app.containers.AccessibilityStatement.changePreferencesText": "U bilo kojem trenutku, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Iznimke kod usklađenosti", "app.containers.AccessibilityStatement.conformanceStatus": "Status sukladnosti", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Nastojimo da naš sadržaj bude prilagođen svima. Ipak, u pojedinim situacijama može se dogoditi da određeni sadržaj ne bude dostupan, što je naglašeno u nastavku:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demo web-mjesta", "app.containers.AccessibilityStatement.email": "E-pošta:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Ugrađeni alati za istraživanje", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Ugrađeni alati za ankete koji su dostupni za korištenje na ovoj platformi softver su treće strane i možda neće biti dostupni.", "app.containers.AccessibilityStatement.exception_1": "Naše platforme za digitalno sudjelovanje upravljaju korisničkim sadržajima koje objavljuju pojedinci i organizacije. Može se dogoditi da su PDF dokumenti, slike i druge vrste dokumenata (uključujući i multimediju) dodani na platformu kao privici ili su uneseni u tekstna polja od strane korisnika. Ovakvi dokumenti možda nisu u potpunosti dostupni.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Vaše povratne informacije o pristupačnosti ove platforme su dobrodošle. Molimo vas da nas kontaktirate na jedan od sljedećih načina:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Postupak davanja povratnih informacija", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Bruxelles, Belgija", "app.containers.AccessibilityStatement.headTitle": "Izjava o pristupačnosti | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} je predan pružanju platforme koja je dostupna svim korisnicima, bez obzira na tehnologiju ili sposobnosti. Pridržavamo se trenutačnih odgovarajućih standarda pristupačnosti kao dio naših stalnih nastojanja da maksimalno povećamo dostupnost i uporabljivost naših platformi za sve korisnike.", "app.containers.AccessibilityStatement.mapping": "Mapiranje", "app.containers.AccessibilityStatement.mapping_1": "Karte na platformi djelomično zadovoljavaju standarde pristupačnosti. Proširenost karte, zumiranje i widgeti korisničkog sučelja mogu se kontrolirati pomoću tipkovnice tijekom pregledavanja karata. Administratori također mogu konfigurirati stil slojeva karte u pozadinskom uredu ili korištenjem Esri integracije za stvaranje pristupačnijih paleta boja i simbologije. Korištenje različitih stilova linija ili poligona (npr. isprekidane linije) također će pomoći u razlikovanju slojeva karte gdje god je to moguće, i iako se takav stil trenutno ne može konfigurirati unutar naše platforme, može se konfigurirati ako koristite karte s Esri integracijom.", "app.containers.AccessibilityStatement.mapping_2": "Karte na platformi nisu u potpunosti dostupne jer korisnicima koji koriste čitače zaslona ne prikazuju zvučno osnovne karte, slojeve karte ili trendove u podacima. Potpuno dostupne karte trebale bi zvučno prikazati slojeve karte i opisati sve relevantne trendove u podacima. Nadalje, crtanje karata linija i poligona u anketama nije dostupno jer se oblici ne mogu crtati pomoću tipkovnice. Alternativni načini unosa trenutačno nisu dostupni zbog tehničke složenosti.", "app.containers.AccessibilityStatement.mapping_3": "Kako bi crtanje karata linija i poligona bilo pristupačnije, preporučujemo uključivanje uvoda ili objašnjenja u pitanje ankete ili opis stranice o tome što karta prikazuje i sve relevantne trendove. Nadalje, može se dati kratki ili dugi tekstualni odgovor na pitanje kako bi ispitanici mogli opisati svoj odgovor jednostavnim riječima ako je potrebno (umjesto klikanja na kartu). Također preporučujemo uključivanje podataka za kontakt voditelja projekta kako bi ispitanici koji ne mogu ispuniti pitanje na karti mogli zatražiti alternativnu metodu odgovora na pitanje (npr. videosastanak).", "app.containers.AccessibilityStatement.mapping_4": "Za Ideation projekte i prijedloge postoji opcija za prikaz unosa u prikazu karte, koji nije dostupan. Međutim, za ove metode postoji alternativni prikaz popisa dostupnih unosa, koji je dostupan.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Naše online radionice imaju komponentu streaminga videozapisa uživo koja trenutno ne podržava titlove.", "app.containers.AccessibilityStatement.pageDescription": "Izjava o pristupačnosti ovog web-mjesta", "app.containers.AccessibilityStatement.postalAddress": "Poštanska adresa:", "app.containers.AccessibilityStatement.publicationDate": "<PERSON><PERSON> objave", "app.containers.AccessibilityStatement.publicationDate2024": "Ova izjava o pristupačnosti objavljena je 21. kolovoza 2024.", "app.containers.AccessibilityStatement.responsiveness": "Nastojimo odgovoriti na povratne informacije u roku od 1 – 2 radna dana.", "app.containers.AccessibilityStatement.statusPageText": "stranica sa statusom", "app.containers.AccessibilityStatement.technologiesIntro": "Pristupačnost ovog web-mjesta ovisi o sljedećim tehnologijama:", "app.containers.AccessibilityStatement.technologiesTitle": "Tehnologije", "app.containers.AccessibilityStatement.title": "Izjava o pristupačnosti", "app.containers.AccessibilityStatement.userGeneratedContent": "<PERSON><PERSON><PERSON><PERSON> kojeg generira korisnik", "app.containers.AccessibilityStatement.workshops": "Radionice", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Odaberite projekt", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Korištenje Content Buildera omogućit će vam korištenje naprednijih opcija izgleda. Za jezike u kojima sadržaj nije dostupan u alatu za izgradnju sadržaja, umjesto toga prikazat će se uobičajeni sadržaj opisa projekta.", "app.containers.AdminPage.ProjectDescription.linkText": "Uredite opis u Content Builderu", "app.containers.AdminPage.ProjectDescription.saveError": "Nešto nije u redu prilikom spremanja opisa projekta.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Koristite Content Builder za opis", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Korištenje Content Buildera omogućit će vam korištenje naprednijih opcija izgleda.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "Pogledaj projekt", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Završetak upitnika", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Kreirajte pametnu grupu", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Korisnici koji zadovoljavaju navedene uvjete automatski će biti dodani u grupu:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Unesite barem jedno pravilo", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Pojedini uvjeti nisu dovršeni", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Spremi grupu", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Konfiguriranje pametnih grupa nije dio vaše trenutne licence. Obratite se svom GovSuccess Manageru da saznate više o tome.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Navedite naziv grupe", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Potvrđivanje je onemogućeno na vašoj platformi, promijenite način potvrđivanja ili kontaktirajte podršku.", "app.containers.App.appMetaDescription": "Dobrodošli na platformu za sudjelovanje {orgName}.\nOtkrijte lokalne projekte i priključite se raspravi!", "app.containers.App.loading": "Učitavanje...", "app.containers.App.metaTitle1": "Platforma za angažman građana | {orgName}", "app.containers.App.skipLinkText": "Prijeđi na glavni sadržaj", "app.containers.AreaTerms.areaTerm": "područje", "app.containers.AreaTerms.areasTerm": "područja", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Račun s ovom e-poštom već postoji. Možete se odjaviti, prijaviti s ovom adresom e-pošte i potvrditi svoj račun na stranici postavki.", "app.containers.Authentication.steps.AccessDenied.close": "Zatvoriti", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Ne ispunjavate uvjete za sudjelovanje u ovom procesu.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Vratite se na potvrdu jedinstvene prijave", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Unesite token", "app.containers.Authentication.steps.Invitation.token": "Znak", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "<PERSON><PERSON><PERSON> imate račun? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Prijavite se", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Poruke e-pošte u ovoj kategoriji", "app.containers.CampaignsConsentForm.messageError": "Došlo je do pogreške prilikom spremanja postavki e-pošte.", "app.containers.CampaignsConsentForm.messageSuccess": "Postavke e-pošte su spremljene.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Kakvu vrstu obavijesti e-poštom želite primati? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Obavijesti", "app.containers.CampaignsConsentForm.submit": "Sp<PERSON>i", "app.containers.ChangeEmail.backToProfile": "Povratak na postavke profila", "app.containers.ChangeEmail.confirmationModalTitle": "potvrdi svoj email", "app.containers.ChangeEmail.emailEmptyError": "Navedite e-mail adresu", "app.containers.ChangeEmail.emailInvalidError": "Navedite adresu e-pošte u ispravnom formatu, <NAME_EMAIL>", "app.containers.ChangeEmail.emailRequired": "<PERSON><PERSON>o unesite adresu e-p<PERSON>š<PERSON>.", "app.containers.ChangeEmail.emailTaken": "Ovaj email se već koristi.", "app.containers.ChangeEmail.emailUpdateCancelled": "Ažuriranje putem e-pošte je otkazano.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Da biste ažurirali svoju e-poštu, ponovno pokrenite postupak.", "app.containers.ChangeEmail.helmetDescription": "Promijenite stranicu e-pošte", "app.containers.ChangeEmail.helmetTitle": "Promijenite svoju e-poštu", "app.containers.ChangeEmail.newEmailLabel": "Novi e-mail", "app.containers.ChangeEmail.submitButton": "podnijeti", "app.containers.ChangeEmail.titleAddEmail": "Dodajte svoju e-poštu", "app.containers.ChangeEmail.titleChangeEmail": "Promijenite svoju e-poštu", "app.containers.ChangeEmail.updateSuccessful": "<PERSON><PERSON><PERSON> <PERSON> je uspješno ažuriran.", "app.containers.ChangePassword.currentPasswordLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.currentPasswordRequired": "Unesite trenutačnu lozinku", "app.containers.ChangePassword.goHome": "Idi na početak", "app.containers.ChangePassword.helmetDescription": "Stranica <PERSON>", "app.containers.ChangePassword.helmetTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.newPasswordLabel": "Nova lozinka", "app.containers.ChangePassword.newPasswordRequired": "Unesite novu lozinku.", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Unesite zaporku koja sadrži najmanje {minimumPasswordLength} znakova", "app.containers.ChangePassword.passwordChangeSuccessMessage": "<PERSON><PERSON>ša lozinka uspješno je ažurirana", "app.containers.ChangePassword.passwordEmptyError": "Unesite zaporku", "app.containers.ChangePassword.passwordsDontMatch": "Potvrdite novu zaporku", "app.containers.ChangePassword.titleAddPassword": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON>", "app.containers.ChangePassword.titleChangePassword": "prom<PERSON><PERSON> lo<PERSON>", "app.containers.Comments.a11y_commentDeleted": "Komentar je izbrisan", "app.containers.Comments.a11y_commentPosted": "Komentiranje objava", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {<PERSON><PERSON>} one {1 sviđa mi se} other {# sviđanja}}", "app.containers.Comments.a11y_undoLike": "<PERSON><PERSON><PERSON><PERSON>jk", "app.containers.Comments.addCommentError": "Došlo je do pogreške. Pokušajte kasnije.", "app.containers.Comments.adminCommentDeletionCancelButton": "Odustani", "app.containers.Comments.adminCommentDeletionConfirmButton": "Izbriši ovaj komentar", "app.containers.Comments.cancelCommentEdit": "Odustani", "app.containers.Comments.childCommentBodyPlaceholder": "Napišite odgovor...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "<PERSON><PERSON><PERSON> komentar je obri<PERSON>.", "app.containers.Comments.commentDeletionCancelButton": "Spremi moj komentar", "app.containers.Comments.commentDeletionConfirmButton": "Izbriši moj komentar", "app.containers.Comments.commentLike": "<PERSON><PERSON>", "app.containers.Comments.commentReplyButton": "Odgovori", "app.containers.Comments.commentsSortTitle": "Razvrstaj komentare prema", "app.containers.Comments.completeProfileLinkText": "ispunite svoj profil", "app.containers.Comments.completeProfileToComment": "Molimo {completeRegistrationLink} za komentar.", "app.containers.Comments.confirmCommentDeletion": "Jeste li sigurni da želite obrisati ovaj komentar? Ovakva odluka je konačna!", "app.containers.Comments.deleteComment": "Izbriši", "app.containers.Comments.deleteReasonDescriptionError": "Navedite više informacija o razlogu", "app.containers.Comments.deleteReasonError": "Navedite razlog", "app.containers.Comments.deleteReason_inappropriate": "<PERSON><PERSON> p<PERSON> ili je uvredljivo", "app.containers.Comments.deleteReason_irrelevant": "<PERSON><PERSON>", "app.containers.Comments.deleteReason_other": "<PERSON>i razlog", "app.containers.Comments.editComment": "<PERSON><PERSON><PERSON>", "app.containers.Comments.guidelinesLinkText": "naše smjer<PERSON>e zajed<PERSON>e", "app.containers.Comments.ideaCommentBodyPlaceholder": "Napišite svoj komentar ovdje", "app.containers.Comments.internalCommentingNudgeMessage": "Izrada internih komentara nije uključena u vašu trenutnu licencu. Obratite se svom GovSuccess Manageru da saznate više o tome.", "app.containers.Comments.internalConversation": "<PERSON>ni <PERSON>", "app.containers.Comments.loadMoreComments": "Učitaj više komentara", "app.containers.Comments.loadingComments": "Učitavanje komentara...", "app.containers.Comments.loadingMoreComments": "Učitavanje više komentara...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Ovaj komentar nije vidljiv običnim korisnicima", "app.containers.Comments.postInternalComment": "<PERSON><PERSON><PERSON>vi interni komentar", "app.containers.Comments.postPublicComment": "Obja<PERSON>te javni komentar", "app.containers.Comments.profanityError": "Ups! Iz<PERSON>a da vaša objava sadrži riječi koje nisu u skladu sa našim {guidelinesLink}. Trudimo se da ovo mjesto bude sigurno za sve. Uredite unos i pokušajte ponovo.", "app.containers.Comments.publicDiscussion": "<PERSON><PERSON><PERSON>", "app.containers.Comments.publishComment": "Objavite svoj komentar", "app.containers.Comments.reportAsSpamModalTitle": "Zašto želite ovo prijaviti kao neželjeni sadržaj?", "app.containers.Comments.saveComment": "Sp<PERSON>i", "app.containers.Comments.signInLinkText": "pri<PERSON><PERSON>", "app.containers.Comments.signInToComment": "Molimo vas da se prijavite {signInLink} kako biste komentirali.", "app.containers.Comments.signUpLinkText": "registracija", "app.containers.Comments.verifyIdentityLinkText": "Potvrdite vaš identitet", "app.containers.Comments.visibleToUsersPlaceholder": "Ovaj komentar vidljiv je običnim korisnicima", "app.containers.Comments.visibleToUsersWarning": "Ovdje postavljeni komentari bit će vidljivi običnim korisnicima.", "app.containers.ContentBuilder.PageTitle": "Opis projekta", "app.containers.CookiePolicy.advertisingContent": "Reklamni kolačići mogu se koristiti za personalizaciju i mjerenje efikasnosti koju vanjske marketinške kampanje imaju kroz angažiranje na ovoj platformi. Na ovoj platformi nećemo prikazivati bilo kakvo oglašavanje, ali može se događati da primate personalizirane oglase na osnovu stranica koje posjećujete.", "app.containers.CookiePolicy.advertisingTitle": "Oglaša<PERSON><PERSON>", "app.containers.CookiePolicy.analyticsContents": "Analitički kolačići prate ponašanje posjetitelja, kao što su stranice koje posjećuju i koliko dugo se zadržavaju na njima. Oni također mogu prikupljati neke tehničke podatke, uključujući informacije o pregledniku, približnu lokaciju i IP adrese. Ove podatke koristimo samo interno kako bismo nastavili poboljšavati cjelokupno korisničko iskustvo i funkcioniranje platforme. Ovakvi podaci također se mogu dijeliti između Go Vocala i {orgName} za procjenu i poboljšanje angažmana na projektima na platformi. Imajte u vidu da su podaci anonimni i da se koriste na agregiranoj razini – ne identificiraju vas osobno. Međutim, moglo bi doći do osobne identifikacije ako ovi podaci dođu u kombinaciju s ostalim izvorima podataka.", "app.containers.CookiePolicy.analyticsTitle": "Analitički kolačići", "app.containers.CookiePolicy.cookiePolicyDescription": "Detaljno objašnjenje o načinu upotrebe kolačića na ovoj platformi", "app.containers.CookiePolicy.cookiePolicyTitle": "Pravila o kolačićima", "app.containers.CookiePolicy.essentialContent": "Neki kolačići ključni su za ispravno funkcioniranje ove platforme. Ovi ključni kolačići prvenstveno se koriste za potvrdu identiteta vašeg računa kada posjetite platformu i za čuvanje vašeg željenog jezika.", "app.containers.CookiePolicy.essentialTitle": "Ključni kolačići", "app.containers.CookiePolicy.externalContent": "Na nekim od naših stranica može se prikazati sadržaj vanjskih dobavljača kao što su YouTube ili Typeform. Nemamo kontrolu nad ovim kolačićima treće strane i pregledavanje sadržaja ovih vanjskih dobavljača također može dovesti do instaliranja kolačića na vaš uređaj.", "app.containers.CookiePolicy.externalTitle": "Vanjski kolačići", "app.containers.CookiePolicy.functionalContents": "Funkcionalni kolačići mogu se omogućiti posjetiteljima kako bi primali obavijesti o izmjenama i pristupali kanalima podrške izravno s platforme.", "app.containers.CookiePolicy.functionalTitle": "Funkcionalni kolačići", "app.containers.CookiePolicy.headCookiePolicyTitle": "Politika k<PERSON>čića | {orgName}", "app.containers.CookiePolicy.intro": "Kolačići su tekstne datoteke koje se spremaju u pregledniku ili na tvrdom disku svojeg računala ili mobilnog telefona kada posjetite web-mjesto na koje može upućivati u toku sljedećih posjeta. Mi koristimo kolačiće da bismo razumjeli kako posjetitelji koriste ovu platformu kako bismo poboljšali njen izgled i iskustvo na njoj, zapamtili vaše postavke (kao što je željeni jezik) i podržali ključne funkcije za registrirane korisnike i administratore platforme.", "app.containers.CookiePolicy.manageCookiesDescription": "Možete omogućiti ili onemogućiti analitike, marketinške i funkcionalne kolačiće u bilo kom trenutku u postavkama kolačića. Također možete izbrisati bilo koje postojeće kolačiće ručno ili automatski putem svog internetskog preglednika. Međutim, kolačići se ponovo mogu ponovno postaviti nakon vašeg pristanka prilikom naknadnog posjeta ovoj platformi. Ako ne izbrišete kolačiće, vaše postavke kolačića čuvaju se 60 dana, nakon čega će se od vas ponovo tražiti pristanak.", "app.containers.CookiePolicy.manageCookiesPreferences": "Idite u svoje {manageCookiesPreferencesButtonText} za pregled kompletnog popisa integracija s trećom stranom koje se koriste na ovoj platformi i za upravljanje svojim postavkama.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "postav<PERSON> k<PERSON>", "app.containers.CookiePolicy.manageCookiesTitle": "Uprav<PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.viewPreferencesButtonText": "<PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.viewPreferencesText": "<PERSON><PERSON><PERSON> navedene kategorije kolačića možda se ne odnose na sve posjetitelje ili platforme. Pogledajte svoje {viewPreferencesButton} za potpuni popis integracija s trećim stranama koje se odnose na vas.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Za što nam služe kolačići?", "app.containers.CustomPageShow.editPage": "<PERSON><PERSON><PERSON> stra<PERSON>u", "app.containers.CustomPageShow.goBack": "<PERSON><PERSON> natrag", "app.containers.CustomPageShow.notFound": "Stranica nije pronađena", "app.containers.DisabledAccount.bottomText": "Možete se ponovno prijaviti od {date}.", "app.containers.DisabledAccount.termsAndConditions": "Uvjeti korištenja", "app.containers.DisabledAccount.text2": "Vaš račun na platformi za sudjelovanje {orgName} privremeno je onemogućen zbog kršenja smjernica zajednice. Za više informacija o tome, možete konzultirati {TermsAndConditions}.", "app.containers.DisabledAccount.title": "<PERSON><PERSON><PERSON> ra<PERSON> je privre<PERSON>o <PERSON>", "app.containers.EventsShow.addToCalendar": "Dodaj u kalendar", "app.containers.EventsShow.editEvent": "<PERSON><PERSON><PERSON>", "app.containers.EventsShow.emailSharingBody2": "Prisustvujte ovom događaju: {eventTitle}. Pročitajte više na {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Datum i vrijeme događaja", "app.containers.EventsShow.eventFrom2": "Od \"{projectTitle}\"", "app.containers.EventsShow.goBack": "<PERSON><PERSON> natrag", "app.containers.EventsShow.goToProject": "Idi na projekt", "app.containers.EventsShow.haveRegistered": "registrirao/la sam se", "app.containers.EventsShow.icsError": "Pogreška pri preuzimanju ICS datoteke", "app.containers.EventsShow.linkToOnlineEvent": "Link na online događaj", "app.containers.EventsShow.locationIconAltText": "<PERSON><PERSON><PERSON>", "app.containers.EventsShow.metaTitle": "<PERSON><PERSON><PERSON><PERSON>: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Online sastanak", "app.containers.EventsShow.onlineLinkIconAltText": "Link za online sastanak", "app.containers.EventsShow.registered": "registriran", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 prijavl<PERSON>nih} one {1 prijavlje<PERSON>} other {# prijavljenih}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} registriranih", "app.containers.EventsShow.registrantsIconAltText": "<PERSON><PERSON><PERSON>", "app.containers.EventsShow.socialMediaSharingMessage": "Prisustvujte ovom događaju: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# sudionik} other {# sudionika}}", "app.containers.EventsViewer.allTime": "Cijelo vrijeme", "app.containers.EventsViewer.date": "Datum", "app.containers.EventsViewer.thisMonth2": "Nadola<PERSON><PERSON><PERSON> m<PERSON>", "app.containers.EventsViewer.thisWeek2": "Nadola<PERSON><PERSON><PERSON> t<PERSON>dan", "app.containers.EventsViewer.today": "<PERSON><PERSON>", "app.containers.IdeaButton.addAContribution": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAPetition": "Dodajte peticiju", "app.containers.IdeaButton.addAProject": "<PERSON><PERSON><PERSON> projekt", "app.containers.IdeaButton.addAProposal": "Dodajte prijedlog", "app.containers.IdeaButton.addAQuestion": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAnInitiative": "Dodajte inicijativu", "app.containers.IdeaButton.addAnOption": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.postingDisabled": "Trenutno ne prihvaćamo nove predaje", "app.containers.IdeaButton.postingInNonActivePhases": "Novi unosi mogu biti dodani samo u sklopu aktivnih faza.", "app.containers.IdeaButton.postingInactive": "Trenutno ne prihvaćamo nove predaje.", "app.containers.IdeaButton.postingLimitedMaxReached": "Već ste ispunili ovu anketu. Hvala na odgovoru!", "app.containers.IdeaButton.postingNoPermission": "Trenutno ne prihvaćamo nove predaje", "app.containers.IdeaButton.postingNotYetPossible": "Još uvi<PERSON> ne prihvaćamo nove predaje.", "app.containers.IdeaButton.signInLinkText": "pri<PERSON><PERSON>", "app.containers.IdeaButton.signUpLinkText": "registracija", "app.containers.IdeaButton.submitAnIssue": "Pošaljite komentar", "app.containers.IdeaButton.submitYourIdea": "Pošaljite svoju ideju", "app.containers.IdeaButton.takeTheSurvey": "Popunite upitnik", "app.containers.IdeaButton.verificationLinkText": "Potvrdite svoj identitet sad.", "app.containers.IdeaCard.readMore": "Čitaj više", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {nema komentara} one {1 komentar} other {# komentara}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {nema glas<PERSON>} one {1 glas} other {# glasova}} od {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Zatvori ploču s filtrima", "app.containers.IdeaCards.a11y_totalItems": "U<PERSON><PERSON><PERSON> objava: {ideasCount}", "app.containers.IdeaCards.all": "Sve", "app.containers.IdeaCards.allStatuses": "Svi statusi", "app.containers.IdeaCards.contributions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.ideaTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.initiatives": "Inicijative", "app.containers.IdeaCards.issueTerm": "<PERSON>i", "app.containers.IdeaCards.list": "<PERSON><PERSON>", "app.containers.IdeaCards.map": "Karta", "app.containers.IdeaCards.mostDiscussed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.newest": "Najnovije", "app.containers.IdeaCards.noFilteredResults": "Nisu pronađeni rezultati. Molimo vas pokušajte s drugim filtrom ili pojmom za pretraživanje.", "app.containers.IdeaCards.numberResults": "<PERSON><PERSON><PERSON><PERSON> ({postCount})", "app.containers.IdeaCards.oldest": "Najstarije", "app.containers.IdeaCards.optionTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.petitions": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.popular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.projectFilterTitle": "Projekti", "app.containers.IdeaCards.projectTerm": "Projekti", "app.containers.IdeaCards.proposals": "Prijedlozi", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "Slučajni odabir", "app.containers.IdeaCards.resetFilters": "Poništi filtre", "app.containers.IdeaCards.showXResults": "Prika<PERSON>i {ideasCount, plural, one {# rezultat} other {# rezultata}}", "app.containers.IdeaCards.sortTitle": "Razvrstavanje", "app.containers.IdeaCards.statusTitle": "<PERSON><PERSON>", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "Oznake", "app.containers.IdeaCards.topicsTitle": "Oznake", "app.containers.IdeaCards.trending": "<PERSON> <PERSON>u", "app.containers.IdeaCards.tryDifferentFilters": "Nisu pronađeni rezultati. Molimo vas pokušajte s drugim filtrom ili pojmom za pretraživanje.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} komentari} one {{ideasCount} komentar} other {{ideasCount} komentari}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} doprinosi} one {{ideasCount} doprinos} other {{ideasCount} doprinosi}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ideje} one {{ideasCount} ideja} other {{ideasCount} ideje}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} inicijative} one {{ideasCount} inicijativa} other {{ideasCount} inicijative}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} opcije} one {{ideasCount} opcija} other {{ideasCount} opcije}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} peticije} one {{ideasCount} peticije} other {{ideasCount} peticije}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projekti} one {{ideasCount} projekt} other {{ideasCount} projekti}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} prijedlozi} one {{ideasCount} prijedlog} other {{ideasCount} prijedlozi}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} pitanja} one {{ideasCount} pitanje} other {{ideasCount} pitanja}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# rezultat} other {# rezultata}}", "app.containers.IdeasEditPage.contributionFormTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.editedPostSave": "Sp<PERSON>i", "app.containers.IdeasEditPage.fileUploadError": "Jedna ili više datoteka nije prenesena. Provjerite veličinu i format datoteke i pokušajte ponovo.", "app.containers.IdeasEditPage.formTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Uredi vašu objavu. Dodajte nove ili promijenite postojeće informacije.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Uredi {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Uredi inicijativu", "app.containers.IdeasEditPage.issueFormTitle": "<PERSON><PERSON><PERSON> komenta<PERSON>", "app.containers.IdeasEditPage.optionFormTitle": "<PERSON><PERSON><PERSON> opci<PERSON>", "app.containers.IdeasEditPage.petitionFormTitle": "<PERSON><PERSON><PERSON> petici<PERSON>", "app.containers.IdeasEditPage.projectFormTitle": "<PERSON><PERSON><PERSON> projekt", "app.containers.IdeasEditPage.proposalFormTitle": "Uredi prijedlog", "app.containers.IdeasEditPage.questionFormTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.save": "Sp<PERSON>i", "app.containers.IdeasEditPage.submitApiError": "<PERSON><PERSON><PERSON> je do problema prilikom slanja obrasca. Molimo vas da provjerite pogreške i pokušate ponovo.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Svi unosi objavljeni", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Istražite sve unose koji su objavljeni na platformi za sudjelovanje organizacije {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Postovi | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Objave", "app.containers.IdeasIndexPage.loadMore": "Učitaj više...", "app.containers.IdeasIndexPage.loading": "Učitavanje...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Prema zadanim postavkama vaši podnesci bit će povezani s vašim profilom, osim ako ne odaberete ovu opciju.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Vidljivost profila", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Ova anketa trenutno nije otvorena za odgovore. Vratite se na projekt za više informacija.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "<PERSON><PERSON> anketa trenutno nije aktivna.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Povratak na projekt", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Već ste ispunili ovu anketu.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "<PERSON><PERSON><PERSON> p<PERSON>", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Hvala na odgovoru!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Opis doprinosa mora biti kraći od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON> ideje mora sadržavati najmanje {limit} znakova", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "<PERSON><PERSON>lov doprinosa mora biti kraći od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "<PERSON><PERSON><PERSON> doprinosa mora imati više od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Odaberite barem jednog susponzora", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Opis ideje mora biti kraći od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Opis ideje mora sadržavati najmanje {limit} znakova", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Unesite opis", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Na<PERSON>lov ideje mora biti kraći od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Naslov ideje mora sadržavati najmanje {limit} znakova", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "Opis inicijative mora imati manje od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "Opis inicijative mora imati više od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "Naslov inicijative mora imati manje od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "Naslov inicijative mora imati više od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Opis problema mora biti kraći od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Opis problema mora biti dulji od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Naslov problema mora biti kraći od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "<PERSON><PERSON><PERSON> izdanja mora imati više od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_number_required": "<PERSON>vo polje je o<PERSON>, moli<PERSON> unesite važeći broj", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Opis opcije mora biti kraći od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "Opis opcije mora imati više od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "<PERSON><PERSON><PERSON> opcije mora biti kraći od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "<PERSON><PERSON><PERSON> opcije mora imati više od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Dodajte najmanje jednu <PERSON>", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "Opis peticije mora imati manje od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "Opis peticije mora imati više od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "<PERSON><PERSON>lov peticije mora imati manje od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "<PERSON><PERSON><PERSON> peticije mora imati više od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Opis projekta mora biti kraći od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Opis projekta mora biti dulji od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "<PERSON><PERSON>lov projekta mora biti kraći od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "<PERSON>v projekta mora imati više od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "Opis ponude mora sadržavati manje od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "Opis prijedloga mora imati više od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "<PERSON><PERSON>lov prijedl<PERSON> mora imati manje od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "Na<PERSON>lov prijedl<PERSON> mora imati više od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Unesite broj", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Unesite broj", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "Opis pitanja mora biti kraći od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "Opis pitanja mora biti dulji od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "<PERSON><PERSON><PERSON> pitanja mora biti kraći od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "<PERSON><PERSON><PERSON> pitanja mora sadržavati više od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "<PERSON><PERSON>o vas unesite naslov", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Opis doprinosa mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Opis doprinosa mora sad<PERSON><PERSON><PERSON><PERSON> barem 30 znakova", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Naslov doprinosa mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Naslov doprinosa mora sadržati najmanje 10 znakova", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Opis ideje mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Opis ideje mora sadržati najmanje 30 znakova", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "<PERSON><PERSON>o vas unesite naslov", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Naslov ideje mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Naslov ideje mora sadržati najmanje 10 znakova", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Možda ste upotrijebili jednu ili više riječi koje {guidelinesLink} se smatraju vulgarnima. Izmijenite tekst tako da izbacite sve što se potencijalno smatra vulgarnim.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "Opis inicijative mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "Opis inicijative mora imati najmanje 30 znakova", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Naslov inicijative mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Naslov inicijative mora imati najmanje 10 znakova", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Opis problema mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Opis problema mora sad<PERSON><PERSON><PERSON><PERSON> barem 30 znakova", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Naslov problema mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Naslov problema mora sadržavati barem 10 znakova", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Opis opcije mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Opis opcije mora sadržavati barem 30 znakova", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Naslov opcije mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Naslov opcije mora sadržavati najmanje 10 znakova", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "Opis peticije mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "Opis peticije mora imati najmanje 30 znakova", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Naslov peticije mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Naslov peticije mora imati najmanje 10 znakova", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Opis projekta mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Opis projekta mora sadržavati barem 30 znakova", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Naslov projekta mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Naslov projekta mora sadržati najmanje 10 znakova", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "Opis ponude mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "Opis ponude mora imati najmanje 30 znakova", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Naslov prijedloga mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Naslov prijedloga mora imati najmanje 10 znakova", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Unesite opis", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Opis pitanja mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Opis pitanja mora sad<PERSON><PERSON><PERSON><PERSON> barem 30 znakova", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Na<PERSON>lov pitanja mora biti kraći od 80 znakova", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Naslov pitanja mora sadržati najmanje 10 znakova", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "Odustani", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON>, <PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.contributionMetaTitle1": "Dodaj novi doprinos projektu | {orgName}", "app.containers.IdeasNewPage.editSurvey": "<PERSON><PERSON><PERSON> up<PERSON>", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Objavite i pridružite se razgovoru na platformi za sudjelovanje {orgName}.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Dodajte novu ideju projektu | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Dodaj novu inicijativu projektu | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Dodaj novi problem projektu | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Jeste li sigurni da ž<PERSON>te otići?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Vaši nacrti odgovora spremljeni su privatno i možete se vratiti kasnije da to dovršite.", "app.containers.IdeasNewPage.leaveSurvey": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.leaveSurveyText": "Vaši odgovori neće biti spremljeni.", "app.containers.IdeasNewPage.optionMetaTitle1": "Dodaj novu opciju projektu | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Dodaj novu peticiju projektu | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Dodaj novi projekt projektu | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Dodaj novi prijedlog projektu | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Dodaj novo pitanje projektu | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Prihvatite poziv", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Poziv za supokroviteljstvo", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Supokrovitelji", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Pozvani ste da postanete susponzor.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Pozivnica prihvaćena", "app.containers.IdeasShow.Cosponsorship.pending": "na čekanju", "app.containers.IdeasShow.MetaInformation.attachments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "Od {userName} dana {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Trenutačni status", "app.containers.IdeasShow.MetaInformation.location": "Lokacija", "app.containers.IdeasShow.MetaInformation.postedBy": "Autor objave:", "app.containers.IdeasShow.MetaInformation.similar": "Slič<PERSON> ulazi", "app.containers.IdeasShow.MetaInformation.topics": "Oznake", "app.containers.IdeasShow.commentCTA": "<PERSON><PERSON><PERSON> k<PERSON>", "app.containers.IdeasShow.contributionEmailSharingBody": "Podrži ovaj doprinos '{postTitle}' na {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Podrži ovaj doprinos: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "H<PERSON>a vam što ste redali svoj doprinos!", "app.containers.IdeasShow.contributionTwitterMessage": "Podrži ovaj doprinos: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Podrži ovaj doprinos: {postTitle}", "app.containers.IdeasShow.currentStatus": "Trenutačni status", "app.containers.IdeasShow.deletedUser": "nepoznat autor", "app.containers.IdeasShow.ideaEmailSharingBody": "Podržite moju ideju '{ideaTitle}' na {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Podržite moju ideju: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "<PERSON><PERSON><PERSON><PERSON> ovu ideju: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON> ovu ideju: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Podržite ovaj komentar: {postTitle}", "app.containers.IdeasShow.imported": "Uvozni", "app.containers.IdeasShow.importedTooltip": "Ovaj {inputTerm} je prikupljen offline i automatski prenesen na platformu.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Podržite ovu inicijativu '{ideaTitle}' na {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Podržite ovu inicijativu: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "<PERSON><PERSON><PERSON>to ste poslali inicijativu!", "app.containers.IdeasShow.initiativeTwitterMessage": "Podržite ovu inicijativu: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Podržite ovu inicijativu: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Podržite ovaj komentar '{postTitle}' na {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Podržite ovaj komentar: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "<PERSON><PERSON>a vam što ste predali svoj komentar!", "app.containers.IdeasShow.issueTwitterMessage": "Podržite ovaj komentar: {postTitle}", "app.containers.IdeasShow.metaTitle": "Unos: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Podržite ovu opciju '{postTitle}' na {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Podržite ovu opciju: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Vaša opcija uspješno je objavljena!", "app.containers.IdeasShow.optionTwitterMessage": "Podržite ovu opciju: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Podržite ovu opciju: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Podržite ovu peticiju '{ideaTitle}' na {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Podržite ovu peticiju: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "<PERSON><PERSON><PERSON> što ste poslali svoju peticiju!", "app.containers.IdeasShow.petitionTwitterMessage": "Podržite ovu peticiju: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Podržite ovu peticiju: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Podržite ovaj projekt '{postTitle}' na {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Podržite ovaj projekt: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "<PERSON><PERSON>a vam što ste predali svoj projekt!", "app.containers.IdeasShow.projectTwitterMessage": "Podržite ovaj projekt: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Podržite ovaj projekt: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Podržite ovaj prijedlog '{ideaTitle}' na {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Podržite ovaj prijedlog: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "<PERSON><PERSON><PERSON> što ste poslali svoj prijedlog!", "app.containers.IdeasShow.proposalTwitterMessage": "Podržite ovaj prijedlog: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Podržite ovaj prijedlog: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Preostalo vrijeme za glasanje:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} od {votingThreshold} pot<PERSON><PERSON><PERSON><PERSON> glasova", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Otkaži glasovanje", "app.containers.IdeasShow.proposals.VoteControl.days": "dana", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "na<PERSON><PERSON> s<PERSON>e", "app.containers.IdeasShow.proposals.VoteControl.hours": "sati", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status i glasovi", "app.containers.IdeasShow.proposals.VoteControl.minutes": "min", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Više informacija", "app.containers.IdeasShow.proposals.VoteControl.vote": "Glasanje", "app.containers.IdeasShow.proposals.VoteControl.voted": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.votedText": "Dobit ćete obavijest kada ova inicijativa prijeđe na sljedeći korak. {x, plural, =0 {Ostalo je {xDays} .} one {Ostalo je {xDays} .} other {Ostalo je {xDays} .}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "<PERSON>a<PERSON> glas je poslan!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "<PERSON><PERSON><PERSON><PERSON>, ne možete glasovati o ovom prijedlogu. Pročitajte zašto u {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {manje od jednog dana} one {jedan dan} other {# dana}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {nema glas<PERSON>} one {1 glas} other {# glasova}}", "app.containers.IdeasShow.questionEmailSharingBody": "Pridružite se raspravi o ovom pitanju '{postTitle}' na {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Pridružite se raspravi: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Vaše pitanje uspješno je objavljeno!", "app.containers.IdeasShow.questionTwitterMessage": "Pridružite se raspravi: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Pridružite se raspravi: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Zašto želite ovo prijaviti kao neželjeni sadržaj?", "app.containers.IdeasShow.share": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.sharingModalSubtitle": "Neka se vaš glas čuje.", "app.containers.IdeasShow.sharingModalTitle": "H<PERSON>a vam što ste predali svoju ideju!", "app.containers.Navbar.completeOnboarding": "Potpuna integracija", "app.containers.Navbar.completeProfile": "Kompletan profil", "app.containers.Navbar.confirmEmail2": "Potvrdite email", "app.containers.Navbar.unverified": "Nepotvrđeno", "app.containers.Navbar.verified": "Potvrđeno", "app.containers.NewAuthModal.beforeYouFollow": "P<PERSON>je nego š<PERSON> slije<PERSON>e", "app.containers.NewAuthModal.beforeYouParticipate": "Prije vašeg sudjelovanja", "app.containers.NewAuthModal.completeYourProfile": "Ispunite svoj profil", "app.containers.NewAuthModal.confirmYourEmail": "Potvrdite svoju e-poštu", "app.containers.NewAuthModal.logIn": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Za nastavak pregledajte donje uvjete.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Molimo ispunite svoj profil.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Vratite se na opcije prijave", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "<PERSON><PERSON><PERSON> ra<PERSON>un? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Prijavite se", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Kod mora sadržavati 4 znamenke.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Nastavite s FranceConnectom", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Na ovoj platformi nisu omogućene metode provjere autentičnosti.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "<PERSON><PERSON> na<PERSON>, pristajete na primanje e-pošte s ove platforme. Na stranici \"Moje postav<PERSON>\" možete odabrati koje e-poruke želite primati.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "E-pošta", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Navedite adresu e-pošte u pravilnom formatu, <NAME_EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Navedite adresu e-p<PERSON>šte", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Za nastavak unesite svoju adresu e-pošte.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Zaboravljena lozinka?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Prijavite se na svoj račun: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Unesite svoju lozinku.", "app.containers.NewAuthModal.steps.Password.password": "Lozinka", "app.containers.NewAuthModal.steps.Password.rememberMe": "<PERSON><PERSON><PERSON><PERSON> me", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Nemojte odabrati ako koristite javno računalo", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON> je dov<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Sada nastavite sudjelovati.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Vaš identitet je potvrđen. Sada ste punopravni član zajednice na ovoj platformi.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Sada ste potvrđeni!", "app.containers.NewAuthModal.steps.close": "Zatvoriti", "app.containers.NewAuthModal.steps.continue": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.whatAreYouInterestedIn": "<PERSON><PERSON> vas zanima?", "app.containers.NewAuthModal.youCantParticipate": "Ne možete sudjelovati", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {nema nepregledanih obavijesti} one {1 nepregledano obavijest} other {# nepregledanih obavijesti}}", "app.containers.NotificationMenu.adminRightsReceived": "Sada ste administrator platforme", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "<PERSON><PERSON><PERSON> komentar na \"{postTitle}\" je obrisan od strane administratora jer\n      {reasonCode, select, irrelevant {to je nebitno} inappropriate {njegov sadr<PERSON>aj je neprikladan} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} je prihvatio vaš poziv za susponzorstvo", "app.containers.NotificationMenu.deletedUser": "Nepoznat autor", "app.containers.NotificationMenu.error": "Neuspješno učitavanje obavijesti", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} je interno komentirao unos koji vam je dodije<PERSON>jen", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} je interno komentirao unos koji ste vi interno komentirali", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} je interno komentirao unos u projektu kojim upravljate", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} je interno komentirao nedodijeljeni unos u neupravljanom projektu", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} je komentirao vaš interni komentar", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} pozvali su vas da susponzorirate doprinos", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} vas je pozvao da budete sponzor ideje", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} vas je pozvao da susponzorirate prijedlog", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} vas je pozvao da supokrovite izdanje", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} vas je pozvao da susponzorirate opciju", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} vas je pozvao da supokrovite peticiju", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} vas je pozvao da budete susponzor projekta", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} vas je pozvao da budete supokrovitelj prijedloga", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} vas je pozvao da supokrovite pitanje", "app.containers.NotificationMenu.loadMore": "Učitaj više...", "app.containers.NotificationMenu.loading": "Učitavanje obavijesti...", "app.containers.NotificationMenu.mentionInComment": "{name} vas je spomenuo u komentaru", "app.containers.NotificationMenu.mentionInInternalComment": "{name} vas je spomenuo u internom komentaru", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} vas je spomenuo u službenoj obavijesti", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Niste poslali svoju anketu", "app.containers.NotificationMenu.noNotifications": "<PERSON><PERSON> nemate obavijesti", "app.containers.NotificationMenu.notificationsLabel": "Obavijesti", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} je službeno obavijestio o doprinosu koji pratite", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} je dao službeno ažuriranje ideje koju slijedite", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} je službeno obavijestio o inicijativi koju pratite", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} je dao službeno ažuriranje o problemu koji pratite", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} je dao službeno ažuriranje opcije koju slijedite", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} dao je službeno ažuriranje peticije koju pratite", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} je dao službeno ažuriranje projekta koji pratite", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} dao je službeno ažuriranje o prijedlogu koji pratite", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} je dao službenu obavijest o pitanju koje pratite", "app.containers.NotificationMenu.postAssignedToYou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vam je {postTitle}", "app.containers.NotificationMenu.projectModerationRightsReceived": "<PERSON>a ste voditelj projekta {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} je u<PERSON>o u novu fazu", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} će ući u novu fazu {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "Objavljen je novi projekt", "app.containers.NotificationMenu.projectReviewRequest": "{name} je zatražio odobrenje za objavu projekta \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} o<PERSON><PERSON><PERSON> \"{projectTitle}\" za objavljivanje", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "Status {ideaTitle} promijenjen je u {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} je dosegao prag broj glasova", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} je prihvatio vašu pozivnicu", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} je komentiralo prilog koji pratite", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} je komentirao ideju koju slijedite", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} je komentirao inicijativu koju pratite", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} je komentirao problem koji pratite", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} je komentirao opciju koju slijedite", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} komentirao je peticiju koju pratite", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} komentirao je projekt koji pratite", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} komentirao je prijedlog koji slijedite", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} je komentirao pitanje koje pratite", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} je pri<PERSON><PERSON> \"{postTitle}\" kao ne<PERSON><PERSON> po<PERSON>", "app.containers.NotificationMenu.userReactedToYourComment": "{name} je reagirao/la na vaš komentar", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} je prija<PERSON> komentar na \"{postTitle}\" kao ne<PERSON><PERSON> po<PERSON>", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Niste predali svoje glasove", "app.containers.NotificationMenu.votingBasketSubmitted": "Uspješno ste glasali", "app.containers.NotificationMenu.votingLastChance": "Zadnja prilika da glasate za {phaseTitle}", "app.containers.NotificationMenu.votingResults": "Objavljeno {phaseTitle} rezultata glasovanja", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} vam je dodi<PERSON><PERSON>/la {postTitle}", "app.containers.PasswordRecovery.emailError": "Ovo ne izgleda kao ispravna adresa e-pošte", "app.containers.PasswordRecovery.emailLabel": "E-pošta", "app.containers.PasswordRecovery.emailPlaceholder": "Moja adresa e-pošte", "app.containers.PasswordRecovery.helmetDescription": "Stranica za poništavanje lozinke", "app.containers.PasswordRecovery.helmetTitle": "Poništite svoju lozinku", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Ako je ova adresa e-pošte registrirana na platformi, poslana je veza za ponovno postavljanje lozinke.", "app.containers.PasswordRecovery.resetPassword": "Pošaljite vezu za ponovno postavljanje lozinke", "app.containers.PasswordRecovery.submitError": "Nismo mogli pronaći račun povezan s ovom adresom e-pošte. Umjesto toga, možete se pokušati registrirati.", "app.containers.PasswordRecovery.subtitle": "<PERSON><PERSON> možemo poslati vezu za odabir nove lozinke?", "app.containers.PasswordRecovery.title": "Ponovno postavljan<PERSON> lo<PERSON>", "app.containers.PasswordReset.helmetDescription": "Stranica za poništavanje lozinke", "app.containers.PasswordReset.helmetTitle": "Poništite svoju lozinku", "app.containers.PasswordReset.login": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordError": "Lozinka mora sadržati najmanje 8 znakova", "app.containers.PasswordReset.passwordLabel": "Zaporka", "app.containers.PasswordReset.passwordPlaceholder": "Nova zaporka", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "<PERSON><PERSON>ša lozinka uspješno je ažurirana.", "app.containers.PasswordReset.pleaseLogInMessage": "Prijavite se sa svojom novom lozinkom.", "app.containers.PasswordReset.requestNewPasswordReset": "Zatražite novo ponovno postavljanje lozinke", "app.containers.PasswordReset.submitError": "Došlo je do pogreške. Pokušajte kasnije.", "app.containers.PasswordReset.title": "Poništite svoju lozinku", "app.containers.PasswordReset.updatePassword": "Potvrdite novu zaporku", "app.containers.ProjectFolderCards.allProjects": "Svi projekti", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} trenutačno radi na", "app.containers.ProjectFolderShowPage.editFolder": "<PERSON><PERSON><PERSON> mapu", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informacije o ovom projektu", "app.containers.ProjectFolderShowPage.metaTitle1": "Mapa: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Pročitajte više", "app.containers.ProjectFolderShowPage.seeLess": "Prika<PERSON><PERSON> manje", "app.containers.ProjectFolderShowPage.share": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.PollForm.document": "Dokument", "app.containers.Projects.PollForm.formCompleted": "H<PERSON>a vam. <PERSON><PERSON><PERSON> smo vaš odgovor.", "app.containers.Projects.PollForm.maxOptions": "maks. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Već ste sudjelovali u ovoj anketi.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "<PERSON>va anketa se može provesti samo kada je ova faza aktivna.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "<PERSON>va anketa trenutačno nije omogućena", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Trenutno je nemoguće sudjelovati u ovoj anketi.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Anketa više nije dostupna jer ovaj projekt više nije aktivan.", "app.containers.Projects.PollForm.sendAnswer": "Pošalji", "app.containers.Projects.a11y_phase": "Faza {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Pre<PERSON> faza", "app.containers.Projects.a11y_titleInputs": "Svi unosi u ovom projektu", "app.containers.Projects.a11y_titleInputsPhase": "Svi unosi u ovoj fazi", "app.containers.Projects.accessRights": "<PERSON><PERSON><PERSON> pristupa", "app.containers.Projects.addedToBasket": "Dodano u vašu košaricu", "app.containers.Projects.allocateBudget": "Rasporedite svoj <PERSON>", "app.containers.Projects.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.basketSubmitted": "Vaši prijedlozi su poslani!", "app.containers.Projects.contributions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.createANewPhase": "Stvorite novu fazu", "app.containers.Projects.currentPhase": "Trenutačna faza", "app.containers.Projects.document": "Dokument", "app.containers.Projects.editProject": "<PERSON><PERSON><PERSON> projekt", "app.containers.Projects.emailSharingBody": "Što mislite o ovoj inicijativi? Glasajte o njoj o sudjelujte u raspravi na {initiativeUrl} kako bi se čuo vaš glas!", "app.containers.Projects.emailSharingSubject": "Podržite moju inicijativu: {initiativeTitle}.", "app.containers.Projects.endedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "app.containers.Projects.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.header": "Projekti", "app.containers.Projects.ideas": "<PERSON><PERSON><PERSON>", "app.containers.Projects.information": "Informacije", "app.containers.Projects.initiatives": "Inicijative", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Pregledajte dokument", "app.containers.Projects.invisibleTitlePhaseAbout": "O ovoj fazi", "app.containers.Projects.invisibleTitlePoll": "Popunite anketu", "app.containers.Projects.invisibleTitleSurvey": "Popunite upitnik", "app.containers.Projects.issues": "Komentari", "app.containers.Projects.liveDataMessage": "Gledate podatke u stvarnom vremenu. Broj sudionika se kontinuirano ažurira za administratore. Imajte na umu da obični korisnici vide predmemorirane podatke, što može rezultirati malim razlikama u brojevima.", "app.containers.Projects.location": "Lokacija:", "app.containers.Projects.manageBasket": "Upravljajte košaricom", "app.containers.Projects.meetMinBudgetRequirement": "Ispunite minimalni proračun kako biste predali košaricu.", "app.containers.Projects.meetMinSelectionRequirement": "Ispunite potrebni odabir kako biste predali košaricu.", "app.containers.Projects.metaTitle1": "Projekt: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Minimalni proračun je obavezan", "app.containers.Projects.myBasket": "<PERSON><PERSON><PERSON>", "app.containers.Projects.navPoll": "<PERSON><PERSON><PERSON>", "app.containers.Projects.navSurvey": "Upitnik", "app.containers.Projects.newPhase": "Nova faza", "app.containers.Projects.nextPhase": "Sljedeća faza", "app.containers.Projects.noEndDate": "<PERSON><PERSON> da<PERSON> završ<PERSON>", "app.containers.Projects.noItems": "<PERSON>š u<PERSON>k niste odabrali bilo koju stavku", "app.containers.Projects.noPastEvents": "<PERSON><PERSON> pro<PERSON> događaja za prikaz", "app.containers.Projects.noPhaseSelected": "<PERSON><PERSON> odabrana nijedna faza", "app.containers.Projects.noUpcomingOrOngoingEvents": "Trenutno nema zakazanih predstojećih događaja ili događaja u tijeku.", "app.containers.Projects.offlineVotersTooltip": "Ovaj broj ne odražava brojanje glasača izvan mreže.", "app.containers.Projects.options": "Opcije", "app.containers.Projects.participants": "Sudionici", "app.containers.Projects.participantsTooltip4": "Ovaj broj također odražava anonimne ankete. Anonimno slanje anketa moguće je ako su ankete otvorene za sve (pogledajte karticu {accessRightsLink} za ovaj projekt).", "app.containers.Projects.pastEvents": "Prethodni dog<PERSON>đaji", "app.containers.Projects.petitions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.phases": "Faze", "app.containers.Projects.previousPhase": "<PERSON><PERSON><PERSON><PERSON> faza", "app.containers.Projects.project": "Projekt", "app.containers.Projects.projectTwitterMessage": "Neka se vaš glas čuje! Sudjelujte u {projectName} | {orgName}", "app.containers.Projects.projects": "Projekti", "app.containers.Projects.proposals": "Prijedlozi", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "Pročitajte manje", "app.containers.Projects.readMore": "Pročitajte više", "app.containers.Projects.removeItem": "Izbriši stavku", "app.containers.Projects.requiredSelection": "Traženi odabir", "app.containers.Projects.reviewDocument": "Pregledajte dokument", "app.containers.Projects.seeTheContributions": "Pogledajte doprinose", "app.containers.Projects.seeTheIdeas": "Pogledajte ideje", "app.containers.Projects.seeTheInitiatives": "Pogledajte inicijative", "app.containers.Projects.seeTheIssues": "Pogledajte komentare", "app.containers.Projects.seeTheOptions": "Pogledajte opcije", "app.containers.Projects.seeThePetitions": "<PERSON><PERSON><PERSON> petici<PERSON>", "app.containers.Projects.seeTheProjects": "Pogledajte projekte", "app.containers.Projects.seeTheProposals": "Pogledajte prijedloge", "app.containers.Projects.seeTheQuestions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeUpcomingEvents": "Pogledajte nadolazeće događaje", "app.containers.Projects.share": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.shareThisProject": "Podijelite ovaj projekt", "app.containers.Projects.submitMyBasket": "Predajte košaricu", "app.containers.Projects.survey": "Upitnik", "app.containers.Projects.takeThePoll": "Popunite anketu", "app.containers.Projects.takeTheSurvey": "Popunite upitnik", "app.containers.Projects.timeline": "Vremenska traka", "app.containers.Projects.upcomingAndOngoingEvents": "Predstojeći događaji i događaji u tijeku", "app.containers.Projects.upcomingEvents": "Nadolazeći događaji", "app.containers.Projects.whatsAppMessage": "{projectName} | s platforme za sudjelovanje organizacije {orgName}", "app.containers.Projects.yourBudget": "Ukupni proračun", "app.containers.ProjectsIndexPage.metaDescription": "Istražite sve trenutne projekte {orgName} i saznajte kako možete sudjelovati. Pridružite nam se u raspravi o lokalnim projektima do kojih vam je stalo.", "app.containers.ProjectsIndexPage.metaTitle1": "Projekti | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projekti", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "<PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Molimo vas da se prijavite {signInLink} ili registrirate {signUpLink} kako biste se dobrovoljno javili", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Sudjelovanje trenutno nije otvoreno za ovu aktivnost.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "pri<PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "registracija", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Povlačim svoju ponudu za volontiranje", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {nema volontera} one {# volonter} other {# volontera}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Upozorenje: ugrađena anketa može imati problema s pristupačnošću za korisnike čitača zaslona. Ako naiđete na bilo kakve izazove, obratite se administratoru platforme kako biste dobili poveznicu na anketu s izvorne platforme. Alternativno, možete zatražiti druge načine ispunjavanja ankete.", "app.containers.ProjectsShowPage.process.survey.survey": "Upitnik", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Kako biste znali možete li sudjelovati u ovom upitniku, molimo vas da se prvo prijavite {logInLink} na platformu.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Možete sudjelovati u ovom upitniku tek kada ova faza projekta na vremenskoj traci bude aktivna.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Molimo {completeRegistrationLink} da biste ispunili anketu.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Ovaj upitnik trenutno nije omogućen", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Popunjavanje ovog upitnika zahtijeva potvrdu vašeg identiteta. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "Anketa više nije dostupna jer ovaj projekt više nije aktivan.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "završi registraciju", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "prija<PERSON>ti se", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "prijavite se", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Sada potvrdite svoj račun.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Samo određeni korisnici mogu pregledati ovaj dokument. Prvo {signUpLink} ili {logInLink}.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "O<PERSON><PERSON> dokument se može pregledati samo kada je ova faza aktivna.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Molimo {completeRegistrationLink} da pregledate dokument.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "<PERSON><PERSON><PERSON><PERSON>, nemate prava na pregled ovog dokumenta.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Pregledavanje ovog dokumenta zahtijeva potvrdu vašeg računa. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Dokument više nije dostupan jer ovaj projekt više nije aktivan.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(uklj. 1 offline)} other {(uklj. # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 odabir} other {# odabira}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "Postotak sudionika koji su odabrali ovu opciju.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "Postotak ukupnih glasova koje je ova opcija dobila.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Cijena:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Prikaži više", "app.containers.ReactionControl.a11y_likesDislikes": "<PERSON><PERSON><PERSON><PERSON>: {likesCount}, <PERSON><PERSON><PERSON>: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Uspješno ste poništili nesviđanje za ovaj unos.", "app.containers.ReactionControl.cancelLikeSuccess": "Uspješno ste otkazali lajk za ovaj unos.", "app.containers.ReactionControl.dislikeSuccess": "Uspješno ste označili da vam se ovaj unos ne sviđa.", "app.containers.ReactionControl.likeSuccess": "Označili ste da vam se ovaj unos sviđa.", "app.containers.ReactionControl.reactionErrorSubTitle": "Zbog pogreške vaša reakcija nije mogla biti registrirana. <PERSON><PERSON> pokušajte ponovno za par minuta.", "app.containers.ReactionControl.reactionSuccessTitle": "<PERSON><PERSON><PERSON> reakcija je uspješno registrirana!", "app.containers.ReactionControl.vote": "Glasanje", "app.containers.ReactionControl.voted": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SearchInput.a11y_cancelledPostingComment": "Otkazano objavljivanje komentara.", "app.containers.SearchInput.a11y_commentsHaveChanged": "Učitano je {sortOder} komentara.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# događaji su učitani} one {# događaj je učitan} other {# događaji su učitani}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# rezultati su učitani} one {# rezultat je učitan} other {# rezultati su učitani}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# rezultati pretraživanja su učitani} one {# rezultati pretraživanja su učitani} other {# rezultati pretraživanja su učitani}}.", "app.containers.SearchInput.removeSearchTerm": "Ukloni pojam za pretraživanje", "app.containers.SearchInput.searchAriaLabel": "Pretraži", "app.containers.SearchInput.searchLabel": "Pretraži", "app.containers.SearchInput.searchPlaceholder": "Pretraži", "app.containers.SearchInput.searchTerm": "Pojam za pretraživanje: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect je rešenje koje je francuska država predložila za osiguranje i pojednostavljivanje registracije za više od 700 mrežnih usluga.", "app.containers.SignIn.or": "<PERSON><PERSON>", "app.containers.SignIn.signInError": "Unesene informacije nisu točne. Kliknite „Zaboravi lozinku“ kako biste je ponovno postavili.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Koristite FranceConnect kako biste se prija<PERSON>, prija<PERSON> ili potvrdili svoj račun.", "app.containers.SignIn.whatIsFranceConnect": "Što je FranceConnect?", "app.containers.SignUp.adminOptions2": "Za administratore i voditelje projekata", "app.containers.SignUp.backToSignUpOptions": "Vrati se na opcije registracije", "app.containers.SignUp.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.emailConsent": "Kad se registrirate, time dajete suglasnost za primanje obavijesti e-poštom s ove platforme. Na stranici „Moje postavke“ možete odabrati koje obavijesti želite primati.", "app.containers.SignUp.emptyFirstNameError": "Unesite ime", "app.containers.SignUp.emptyLastNameError": "Unesite prezime", "app.containers.SignUp.firstNamesLabel": "Ime", "app.containers.SignUp.goToLogIn": "Ve<PERSON> imate račun? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "Pročitao sam i slažem se s {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "Pročitao sam i slažem se s {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "<PERSON><PERSON><PERSON><PERSON> da se podaci koriste na web-mjestu mitgestalten.wien.gv.at. Više informacija možete pronaći na adresi {link}.", "app.containers.SignUp.invitationErrorText": "Vaša pozivnica je istekla ili je već iskorištena. Ako ste već upotrijebili vezu za pozivnicu za stvaranje računa, pokušajte se prijaviti. U suprotnom, prijavite se za stvaranje novog računa.", "app.containers.SignUp.lastNameLabel": "Prezime", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Pratite svoja područja fokusa kako biste bili obaviješteni o njima:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Pratite svoje omiljene teme kako biste bili obaviješteni o njima:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Spremi postavke", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Preskoči Za sada", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Za nastavak prihvatite naša pravila o privatnosti", "app.containers.SignUp.signUp2": "Registracija", "app.containers.SignUp.skip": "Preskoči ovaj korak", "app.containers.SignUp.tacError": "Prihvatite odredbe i uvjete kako biste nastavili", "app.containers.SignUp.thePrivacyPolicy": "pravila o privatnosti", "app.containers.SignUp.theTermsAndConditions": "odredbe i uvjeti", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {<PERSON><PERSON> se da ste se prije pokušali registrirati bez da ste dovr<PERSON><PERSON> postupak. Umjesto toga kliknite na Prijavu koristeći vjerodajnice koje ste odabrali tijekom prethodnog pokušaja.} other {<PERSON><PERSON><PERSON> je do pogreške. Molimo pokušajte kasnije.}}", "app.containers.SignUp.viennaConsentEmail": "Adresa e-pošte", "app.containers.SignUp.viennaConsentFirstName": "Ime", "app.containers.SignUp.viennaConsentFooter": "Možete izmijeniti podatke profila nakon prijave. <PERSON><PERSON> već imate račun s istom adresom e-pošte na web-mjestu mitgestalten.wien.gv.at, bit će povezan s vašim trenutnim računom.", "app.containers.SignUp.viennaConsentHeader": "Prenijet će se sljedeći podaci:", "app.containers.SignUp.viennaConsentLastName": "Prezime", "app.containers.SignUp.viennaConsentUserName": "Korisničko ime", "app.containers.SignUp.viennaDataProtection": "Pravila o privatnosti Beča", "app.containers.SiteMap.contributions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.cookiePolicyLinkTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.issues": "Komentari", "app.containers.SiteMap.options": "Opcije", "app.containers.SiteMap.projects": "Projekti", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "Izvješće", "app.containers.SpamReport.buttonSuccess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.inappropriate": "<PERSON><PERSON> p<PERSON> ili je uvredljivo", "app.containers.SpamReport.messageError": "<PERSON><PERSON><PERSON> je do pogreške prilikom slanja obrasca, pokušajte ponovo.", "app.containers.SpamReport.messageSuccess": "Vaše izvješ<PERSON>e je poslano", "app.containers.SpamReport.other": "<PERSON>i razlog", "app.containers.SpamReport.otherReasonPlaceholder": "Opis", "app.containers.SpamReport.wrong_content": "<PERSON><PERSON>", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Ukloni profilnu sliku", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Vaši glasovi za prijedloge koji su još otvoreni za glasovanje bit će izbrisani. Glasovi o prijedlozima za koje je zatvoreno razdoblje glasovanja neće biti izbrisani.", "app.containers.UsersEditPage.addPassword": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Za sudjelovanje u projektima koji zahtijevaju potvrdu.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Potvrdite vaš identitet", "app.containers.UsersEditPage.bio": "O vama", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Ne možete urediti ovo polje jer sadrži potvrđene informacije.", "app.containers.UsersEditPage.buttonSuccessLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.cancel": "Odustani", "app.containers.UsersEditPage.changeEmail": "Promijeni e-mail", "app.containers.UsersEditPage.changePassword2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Kliknite ovdje kako biste ažurirali svoju potvrdu.", "app.containers.UsersEditPage.conditionsLinkText": "naši uvjeti", "app.containers.UsersEditPage.contactUs": "Drugi razlog za napuštanje? {feedbackLink} i možda vam možemo pomoći.", "app.containers.UsersEditPage.deleteAccountSubtext": "<PERSON><PERSON> nam je š<PERSON> odl<PERSON>.", "app.containers.UsersEditPage.deleteMyAccount": "Izbriši moj račun", "app.containers.UsersEditPage.deleteYourAccount": "Izbrišite svoj račun", "app.containers.UsersEditPage.deletionSection": "Izbrišite svoj račun", "app.containers.UsersEditPage.deletionSubtitle": "Ova akcija se ne može poništiti. Sadržaj koji ste objavili na platformi će biti anoniman. Ako želite izbrisati sav svoj sadrž<PERSON>, možete nas <NAME_EMAIL>.", "app.containers.UsersEditPage.email": "E-pošta", "app.containers.UsersEditPage.emailEmptyError": "Unesite adresu e-pošte", "app.containers.UsersEditPage.emailInvalidError": "Unesite adresu e-pošte u ispravnom formatu, <NAME_EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Recite nam", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Ime", "app.containers.UsersEditPage.firstNamesEmptyError": "Unesite imena", "app.containers.UsersEditPage.h1": "Podaci vašeg računa", "app.containers.UsersEditPage.h1sub": "Uredite podatke svojeg računa", "app.containers.UsersEditPage.image": "Slika avatara", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Kliknite kako biste odabrali profilnu sliku (maks. 5 MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Sve postavke vašeg profila", "app.containers.UsersEditPage.language": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.lastName": "Prezime", "app.containers.UsersEditPage.lastNameEmptyError": "Unesite prezimena", "app.containers.UsersEditPage.loading": "Učitavanje...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Ovdje možete promijeniti svoju e-poštu ili lozinku.", "app.containers.UsersEditPage.loginCredentialsTitle": "vjerodajnice za prijavu", "app.containers.UsersEditPage.messageError": "Nismo uspeli spremiti vaš profil. Pokušajte kasnije <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "<PERSON><PERSON><PERSON> profil je spremljen.", "app.containers.UsersEditPage.metaDescription": "Ovo je stranica za postavke profila korisnika {firstName} {lastName} na mrežnoj platformi za sudjelovanje od {tenantName}. Ovdje možete potvrditi svoj identitet, obrisati podatke svojeg računa, izbrisati račun i uređivati postavke e-pošte.", "app.containers.UsersEditPage.metaTitle1": "Stranica postavki profila {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Nakon što kliknete na ovo gumb, neće biti moguće vratiti vaš račun.", "app.containers.UsersEditPage.noNameWarning2": "Vaše ime je trenutno prikazano na platformi kao: \"{displayName}\" jer niste unijeli svoje ime. Ovo je automatski generirano ime. <PERSON>ko ga želite promijeniti, unesite svoje ime ispod.", "app.containers.UsersEditPage.notificationsSubTitle": "Kakvu vrstu obavijesti e-poštom želite primati? ", "app.containers.UsersEditPage.notificationsTitle": "Obavijesti e-poštom", "app.containers.UsersEditPage.password": "Odaberite novu zaporku", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Unesite zaporku koja sadrži najmanje {minimumPasswordLength} znakova", "app.containers.UsersEditPage.passwordAddSection": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON>", "app.containers.UsersEditPage.passwordAddSubtitle2": "Postavite lozinku i jednostavno se prijavite na platformu, bez potrebe da svaki put potvrđujete svoju e-poštu.", "app.containers.UsersEditPage.passwordChangeSection": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.passwordChangeSubtitle": "Potvrdite trenutačnu lozinku i promijenite je u novu lozinku.", "app.containers.UsersEditPage.privacyReasons": "Ako brinete o privatnosti, možete pročitati {conditionsLink}.", "app.containers.UsersEditPage.processing": "Slanje...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Ime je potrebno prilikom unosa prezimena", "app.containers.UsersEditPage.reasonsToStayListTitle": "Prije nego što odete...", "app.containers.UsersEditPage.submit": "S<PERSON><PERSON>i promje<PERSON>", "app.containers.UsersEditPage.tooManyEmails": "Primate previše e-poruka? Možete upravljati postavkama e-pošte u postavkama svojeg profila.", "app.containers.UsersEditPage.updateverification": "Je li došlo do promjene vaših službenih podataka? {reverifyButton}", "app.containers.UsersEditPage.user": "Kada želite da vam pošaljemo podsjetnik e-poštom?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Možete sudjelovati u projektima koji zahtijevaju potvrdu.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Potvrđeni ste", "app.containers.UsersEditPage.verifyNow": "Potvrdite sada", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Preuzmite svoje odgovore (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {<PERSON><PERSON>} one {1 sviđa mi se} other {# sviđanja}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Unos u kojem je ovaj komentar objavljen kao odgovor na:", "app.containers.UsersShowPage.areas": "Podru<PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "Komentari ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Uredi svoj profil", "app.containers.UsersShowPage.emptyInfoText": "Ne pratite nijednu stavku gore navedenog filtera.", "app.containers.UsersShowPage.eventsWithCount": "<PERSON><PERSON><PERSON><PERSON><PERSON> ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "<PERSON><PERSON><PERSON><PERSON> ({followingCount})", "app.containers.UsersShowPage.inputs": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.invisibleTitlePostsList": "Svi unosi ovog sudionika", "app.containers.UsersShowPage.invisibleTitleUserComments": "Svi komentari koje je objavio ovaj sudionik", "app.containers.UsersShowPage.loadMore": "Učitaj više", "app.containers.UsersShowPage.loadMoreComments": "Učitaj više komentara", "app.containers.UsersShowPage.loadingComments": "Učitavanje komentara...", "app.containers.UsersShowPage.loadingEvents": "Učitavanje događaja...", "app.containers.UsersShowPage.memberSince": "<PERSON><PERSON> od {date}", "app.containers.UsersShowPage.metaTitle1": "Stranica profila {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "<PERSON><PERSON> osoba još nije obja<PERSON> nijedan komentar.", "app.containers.UsersShowPage.noCommentsForYou": "<PERSON><PERSON><PERSON><PERSON> još nema komentara.", "app.containers.UsersShowPage.noEventsForUser": "Još niste prisustvovali nijednom događaju.", "app.containers.UsersShowPage.postsWithCount": "Predaje ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Projektne mape", "app.containers.UsersShowPage.projects": "Projekti", "app.containers.UsersShowPage.proposals": "Prijedlozi", "app.containers.UsersShowPage.seePost": "Pogledajte predaju", "app.containers.UsersShowPage.surveyResponses": "Odgovori ({responses})", "app.containers.UsersShowPage.topics": "teme", "app.containers.UsersShowPage.tryAgain": "Došlo je do pogreške. Pokušajte ponovno kasnije.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Ovo je stranica profila {firstName} {lastName} na platformi za mrežno sudjelovanje organizacije {orgName}. U nastavku pregledajte sve njihove unose.", "app.containers.VoteControl.close": "Zatvori", "app.containers.VoteControl.voteErrorTitle": "Nešto je pošlo po krivu", "app.containers.admin.ContentBuilder.default": "zadana vrijednost", "app.containers.admin.ContentBuilder.imageTextCards": "Slike i tekstualne kartice", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info & harmonike", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 stupac", "app.containers.admin.ContentBuilder.projectDescription": "Opis projekta", "app.containers.app.navbar.admin": "Upravljanje platformom", "app.containers.app.navbar.allProjects": "Svi projekti", "app.containers.app.navbar.ariaLabel": "Primarno", "app.containers.app.navbar.closeMobileNavMenu": "Zatvorite izbornik za mobilnu navigaciju", "app.containers.app.navbar.editProfile": "<PERSON><PERSON>", "app.containers.app.navbar.fullMobileNavigation": "Puni mobilni", "app.containers.app.navbar.logIn": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.logoImgAltText": "{orgName} Početna", "app.containers.app.navbar.myProfile": "Moja aktivnost", "app.containers.app.navbar.search": "Pretraži", "app.containers.app.navbar.showFullMenu": "Prikaži cijeli izbornik", "app.containers.app.navbar.signOut": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.errorWhenFetchingEvents": "Došlo je do pogreške prilikom učitavanja događaja. Pokušajte ponovo učitati stranicu.", "app.containers.eventspage.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.eventsPageDescription": "Prikaži sve događaje objavljene na {orgName} platformi.", "app.containers.eventspage.eventsPageTitle1": "Doga<PERSON><PERSON><PERSON> | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projekti", "app.containers.eventspage.noPastEvents": "<PERSON><PERSON> pro<PERSON> događaja za prikaz", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Trenutno nema zakazanih predstojećih događaja ili događaja u tijeku.", "app.containers.eventspage.pastEvents": "Prethodni dog<PERSON>đaji", "app.containers.eventspage.upcomingAndOngoingEvents": "Predstojeći događaji i događaji u tijeku", "app.containers.footer.accessibility-statement": "Izjava o pristupačnosti", "app.containers.footer.ariaLabel": "Sekundarno", "app.containers.footer.cookie-policy": "Pravila o kolačićima", "app.containers.footer.cookieSettings": "<PERSON><PERSON><PERSON>", "app.containers.footer.feedbackEmptyError": "Polje za povratne informacije ne može biti prazno.", "app.containers.footer.poweredBy": "<PERSON><PERSON><PERSON><PERSON>ava", "app.containers.footer.privacy-policy": "Pravila o privatnosti", "app.containers.footer.siteMap": "Mapa web-mjesta", "app.containers.footer.terms-and-conditions": "Odredbe i uvjeti", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON>, <PERSON><PERSON><PERSON>", "app.containers.ideaHeading.editForm": "<PERSON><PERSON><PERSON>", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Jeste li sigurni da ž<PERSON>te otići?", "app.containers.ideaHeading.leaveIdeaForm": "Ostavite obrazac za ideju", "app.containers.ideaHeading.leaveIdeaText": "Vaši odgovori neće biti spremljeni.", "app.containers.landing.cityProjects": "Projekti", "app.containers.landing.completeProfile": "Popunite svoj profil", "app.containers.landing.completeYourProfile": "<PERSON><PERSON>šli, {firstName}. Vrijeme je da popunite svoj profil.", "app.containers.landing.createAccount": "Registracija", "app.containers.landing.defaultSignedInMessage": "{orgName} vas sluša. Na vama je da se čuje vaš glas!", "app.containers.landing.doItLater": "Uradit ću to kasnije", "app.containers.landing.new": "novo", "app.containers.landing.subtitleCity": "Dobrodošli na platformu za sudjelovanje {orgName}", "app.containers.landing.titleCity": "Oblik<PERSON>jmo budućnost {orgName} zajedno", "app.containers.landing.twitterMessage": "Glasujte za {ideaTitle} na", "app.containers.landing.upcomingEventsWidgetTitle": "Predstojeći događaji i događaji u tijeku", "app.containers.landing.userDeletedSubtitle": "U bilo kom trenutku možete otvoriti novi račun ili {contactLink} kako biste nas obavijestili što možemo poboljšati.", "app.containers.landing.userDeletedSubtitleLinkText": "pišite nam", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "<PERSON><PERSON>š <PERSON> račun je izbrisan.", "app.containers.landing.userDeletionFailed": "Došlo je do pogreške prilikom brisanja vašeg računa, obaviješteni smo o problemu i potrudit ćemo se da ga riješimo. Pokušajte ponovo kasnije.", "app.containers.landing.verifyNow": "Potvrdite sada", "app.containers.landing.verifyYourIdentity": "Potvrdite vaš identitet", "app.containers.landing.viewAllEventsText": "Prikaži sve događaje", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Natrag u mapu", "app.errors.after_end_at": "Datum početka postavljen je nakon datuma završetka", "app.errors.avatar_carrierwave_download_error": "Neuspješno preuzimanje datoteke avatara.", "app.errors.avatar_carrierwave_integrity_error": "Odabrani avatar ne spada u dozvoljeni tip datoteke.", "app.errors.avatar_carrierwave_processing_error": "<PERSON><PERSON> mogu<PERSON>e obraditi avatar.", "app.errors.avatar_extension_blacklist_error": "Datotečni nastavak slike avatara nije dozvoljen. Dozvoljene datotečni nastavci su: jpg, jpeg, gif i png.", "app.errors.avatar_extension_whitelist_error": "Datotečni nastavak slike avatara nije dozvoljen. Dozvoljene datotečni nastavci su: jpg, jpeg, gif i png.", "app.errors.banner_cta_button_multiloc_blank": "Unesite tekst gumba.", "app.errors.banner_cta_button_url_blank": "Unesite vezu.", "app.errors.banner_cta_button_url_url": "Unesite valjanu vezu. Pobrinite se da veza započinje s 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Unesite tekst gumba.", "app.errors.banner_cta_signed_in_url_blank": "Unesite vezu.", "app.errors.banner_cta_signed_in_url_url": "Unesite valjanu vezu. Pobrinite se da veza započinje s 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Unesite tekst gumba.", "app.errors.banner_cta_signed_out_url_blank": "Unesite vezu.", "app.errors.banner_cta_signed_out_url_url": "Unesite valjanu vezu. Pobrinite se da veza započinje s 'https://'.", "app.errors.base_includes_banned_words": "Možda ste upotrijebili jednu ili više riječi koje se smatraju psovkama. Molimo vas da izmijenite tekst kako biste uklonili sve psovke koje bi mogle biti prisutne.", "app.errors.body_multiloc_includes_banned_words": "Opis sadrži riječi koje se smatraju neprikladnim.", "app.errors.bulk_import_idea_not_valid": "Dobivena ideja nije važeća: {value}.", "app.errors.bulk_import_image_url_not_valid": "<PERSON>je bilo moguće preuzeti sliku s {value}. Provjerite je li URL valjan i završava li ekstenzijom datoteke kao što je .png ili .jpg. Ovaj se problem javlja u retku s ID-om {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Idejna lokacija s koordinatama koje nedos<PERSON>ju u {value}. Ovaj se problem javlja u retku s ID-om {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Idejna lokacija s nenumeričkom koordinatom u {value}. Ovaj se problem javlja u retku s ID-om {row}.", "app.errors.bulk_import_malformed_pdf": "Čini se da je učitana PDF datoteka neispravna. Pokušajte ponovno izvesti PDF iz svog izvora, a zatim ponovno prenesite.", "app.errors.bulk_import_maximum_ideas_exceeded": "Premašen je maksimum od {value} ideja.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Premašen je maksimum od {value} stranica u PDF-u.", "app.errors.bulk_import_not_enough_pdf_pages": "Učitani PDF nema dovoljno stranica - trebao bi imati barem isti broj stranica kao preuzeti predložak.", "app.errors.bulk_import_publication_date_invalid_format": "Ideja s nevažećim formatom datuma objave \"{value}\". Molimo koristite format \"DD-MM-GGGG\".", "app.errors.cannot_contain_ideas": "Odabrana participativna metoda ne podržava ovaj tip unosa. Molimo vas da promenite vaš izbor i pokušate ponovo.", "app.errors.cant_change_after_first_response": "Ovo ne možete izmijeniti jer su određeni korisnici već odgovorili", "app.errors.category_name_taken": "Kategorija s ovim imenom već postoji", "app.errors.confirmation_code_expired": "Kod je istekao. Zatražite novi kod.", "app.errors.confirmation_code_invalid": "Nevaljani potvrdni kod. Provjerite točan kod u vašoj e-pošti ili pokušajte sa „Pošalji novi kod“", "app.errors.confirmation_code_too_many_resets": "Previše ste puta zatražili slanje potvrdnog koda. Molimo vas da nas kontaktirate radi dobivanja koda.", "app.errors.confirmation_code_too_many_retries": "Pokušali ste previše puta. Zatražite novi kod ili pokušajte promijeniti adresu e-pošte.", "app.errors.email_already_active": "Adresa e-pošte {value} u redu {row} pripada već registriranom sudioniku", "app.errors.email_already_invited": "<PERSON><PERSON><PERSON> <PERSON><PERSON>p<PERSON><PERSON><PERSON> {value} u redu {row} je već pozvana", "app.errors.email_blank": "Ovo ne može biti prazno", "app.errors.email_domain_blacklisted": "Molimo vas da koristite drugu domenu e-pošte za registraciju.", "app.errors.email_invalid": "<PERSON><PERSON><PERSON> da unesete valjanu adresu e-poš<PERSON>.", "app.errors.email_taken": "Već postoji račun s ovom adresom e-pošte. Možete se pokušati prijaviti.", "app.errors.email_taken_by_invite": "{value} je već zauzet s pozivnicom koja je na čekanju. Provjerite mapu s neželjenim sadrž<PERSON>em ili kontaktirajte {supportEmail} ukoliko je ne možete pronaći.", "app.errors.emails_duplicate": "Jedna ili više dvostrukih adrese e-pošte {value} pronađena je u sljedećim redovima: {rows}", "app.errors.extension_whitelist_error": "Format datoteke koju ste pokušali prenijeti nije podržan.", "app.errors.file_extension_whitelist_error": "Format datoteke koji ste pokušali prenijeti nije podržan.", "app.errors.first_name_blank": "Ovo ne može biti prazno", "app.errors.generics.blank": "Ovo ne može biti prazno.", "app.errors.generics.invalid": "Ovo ne izgleda kao valjana vrijednost", "app.errors.generics.taken": "Ova e-adresa već postoji i za nju je vezan drugi račun.", "app.errors.generics.unsupported_locales": "Ovo polje ne podržava trenutno područje.", "app.errors.group_ids_unauthorized_choice_moderator": "Kao projekt<PERSON>, možete slati samo obavijesti o mogućnosti pristupa projektima", "app.errors.has_other_overlapping_phases": "Projekti ne mogu imati faze koje se preklapaju.", "app.errors.invalid_email": "E-pošta {value} pronađena u redu {row} nije valjana", "app.errors.invalid_row": "<PERSON><PERSON><PERSON> je do nepoznate pogreške prilikom obrade reda {row}", "app.errors.is_not_timeline_project": "Trenutni projekt ne podržava faze.", "app.errors.key_invalid": "Ključ može sadržati samo slova, brojeve i donje crte (_)", "app.errors.last_name_blank": "Ovo ne može biti prazno", "app.errors.locale_blank": "Odaberite jezik", "app.errors.locale_inclusion": "Odaberite podržani jezik", "app.errors.malformed_admin_value": "Administratorska vrijednost {value} pronađena u redu {row} nije valjana", "app.errors.malformed_groups_value": "Grupa {value} pronađena u redu {row} nije valjana", "app.errors.max_invites_limit_exceeded1": "Broj pozivnica premašuje ograničenje od 1000.", "app.errors.maximum_attendees_greater_than1": "Maksimalan broj registriranih mora biti veći od 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Maksimalni broj prijavljenih mora biti veći ili jednak trenutnom broju prijavljenih.", "app.errors.no_invites_specified": "Nismo uspjeli pronaći bilo koju adresu e-pošte.", "app.errors.no_recipients": "Kampanja se ne može poslati jer nema primatelja. Grupa kojoj šaljete je prazna ili nitko nije pristao na primanje e-pošte.", "app.errors.number_invalid": "Unesite važeći broj.", "app.errors.password_blank": "Ovo ne može biti prazno", "app.errors.password_invalid": "Ponovno provjerite svoju trenutačnu lozinku.", "app.errors.password_too_short": "Lozinka mora sadržati najmanje 8 znakova", "app.errors.resending_code_failed": "Nešto nije u redu prilikom slanja potvrdnog koda.", "app.errors.slug_taken": "Ovaj URL projekta već postoji. Molimo vas izmijenite „puž“ projekta.", "app.errors.tag_name_taken": "Oznaka s ovim nazivom već postoji", "app.errors.title_multiloc_blank": "Naziv ne može biti prazan.", "app.errors.title_multiloc_includes_banned_words": "Naslov sadrži riječi koje se smatraju neprikladnima.", "app.errors.token_invalid": "Veze za ponovno postavljanje lozinke mogu se koristiti samo jednom i vrijede jedan sat nakon što su poslane. {passwordResetLink}.", "app.errors.too_common": "Ovu lozinku moguće je lako pogoditi. Izaberite jaču lozinku.", "app.errors.too_long": "Odaberite kraću lozinku (najviše 72 znaka)", "app.errors.too_short": "Odaberite lozinku s najmanje 8 znakova", "app.errors.uncaught_error": "Dogodila se nepoznata pogreška.", "app.errors.unknown_group": "Grupa {value} pronađena u redu {row} nije poznata", "app.errors.unknown_locale": "<PERSON><PERSON><PERSON> {value} pronađen u redu {row} nije konfiguriran", "app.errors.unparseable_excel": "Odabrana Excel datoteka nije mogla biti obrađena.", "app.errors.url": "Unesite valjanu vezu. Provjerite počinje li veza s https://", "app.errors.verification_taken": "Potvrda se ne može dovršiti jer je drugi račun potvrđen pomoću istih podataka.", "app.errors.view_name_taken": "Prikaz s ovim nazivom već postoji", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Neprimjeren sadržaj automatski je otkriven u objavi ili komentaru", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "<PERSON><PERSON><PERSON><PERSON> putem usluge StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Registracije putem StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Izradite Wien račun i upotrijebite jednu prijavu za mnogo digitalnih usluga grada Beča.", "app.modules.id_cow.cancel": "Odustani", "app.modules.id_cow.emptyFieldError": "Ovo polje ne može biti prazno.", "app.modules.id_cow.helpAltText": "Pokazuje gdje se nalazi ID serijski broj na identifikacijskoj kartici", "app.modules.id_cow.invalidIdSerialError": "Nevaljan ID serijski broj", "app.modules.id_cow.invalidRunError": "Nevaljani RUN", "app.modules.id_cow.noMatchFormError": "Nisu pronađena podudaranja.", "app.modules.id_cow.notEntitledFormError": "Nemate pravo.", "app.modules.id_cow.showCOWHelp": "Gdje mogu pronaći svoj ID serijski broj?", "app.modules.id_cow.somethingWentWrongError": "Nismo mogli potvrditi jer je nešto po<PERSON>lo po krivu", "app.modules.id_cow.submit": "Pošalji", "app.modules.id_cow.takenFormError": "Već je zauzeto.", "app.modules.id_cow.verifyCow": "Potvrdite se putem COW-a", "app.modules.id_franceconnect.verificationButtonAltText": "Potvrdite sa FranceConnectom", "app.modules.id_gent_rrn.cancel": "Odustani", "app.modules.id_gent_rrn.emptyFieldError": "Ovo polje ne može biti prazno.", "app.modules.id_gent_rrn.gentRrnHelp": "<PERSON>aš broj socijalnog osiguranja prikazan je na poleđini vaše digitalne osobne karte", "app.modules.id_gent_rrn.invalidRrnError": "Nevaljani broj socijalnog osiguranja", "app.modules.id_gent_rrn.noMatchFormError": "Nismo mogli pronaći podatke o vašem broju socijalnog osiguranja", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Ne možemo vas potvrditi jer živite izvan <PERSON>", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Ne možemo vas potvrditi jer ste mlađi od 14 godina", "app.modules.id_gent_rrn.rrnLabel": "broj socijalnog osiguranja", "app.modules.id_gent_rrn.rrnTooltip": "Potreban nam je vaš broj socijalnog osiguranja kako bismo utvrdili jeste li ste stanovnik Ghenta stariji od 14 godina.", "app.modules.id_gent_rrn.showGentRrnHelp": "Gdje mogu pronaći svoj ID serijski broj?", "app.modules.id_gent_rrn.somethingWentWrongError": "Nismo mogli potvrditi jer je nešto po<PERSON>lo po krivu", "app.modules.id_gent_rrn.submit": "Pošalji", "app.modules.id_gent_rrn.takenFormError": "V<PERSON>š broj socijalnog osiguranja već je korišten za potvrđivanje drugog računa", "app.modules.id_gent_rrn.verifyGentRrn": "Potvrdite pomoću GentRrna", "app.modules.id_id_card_lookup.cancel": "Odustani", "app.modules.id_id_card_lookup.emptyFieldError": "Ovo polje ne može biti prazno.", "app.modules.id_id_card_lookup.helpAltText": "Objašnjenje ID kartice", "app.modules.id_id_card_lookup.invalidCardIdError": "Ovaj ID nije valjan.", "app.modules.id_id_card_lookup.noMatchFormError": "Nisu pronađena podudaranja.", "app.modules.id_id_card_lookup.showHelp": "Gdje mogu pronaći svoj ID serijski broj?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Nismo mogli potvrditi jer je nešto po<PERSON>lo po krivu", "app.modules.id_id_card_lookup.submit": "Pošalji", "app.modules.id_id_card_lookup.takenFormError": "Već je zauzeto.", "app.modules.id_oostende_rrn.cancel": "Odustani", "app.modules.id_oostende_rrn.emptyFieldError": "Ovo polje ne može biti prazno.", "app.modules.id_oostende_rrn.invalidRrnError": "Nevaljani broj socijalnog osiguranja", "app.modules.id_oostende_rrn.noMatchFormError": "Nismo mogli pronaći podatke o vašem broju socijalnog osiguranja", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Ne možemo vas potvrditi jer živite izvan O<PERSON>endea", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Ne možemo vas potvrditi jer ste mlađi od 14 godina", "app.modules.id_oostende_rrn.oostendeRrnHelp": "<PERSON>aš broj socijalnog osiguranja prikazan je na poleđini vaše digitalne osobne karte", "app.modules.id_oostende_rrn.rrnLabel": "broj socijalnog osiguranja", "app.modules.id_oostende_rrn.rrnTooltip": "Potreban nam je vaš broj socijalnog osiguranja kako bismo utvrdili jeste li ste stanovnik Oostendea stariji od 14 godina.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Gdje mogu pronaći svoj broj socijalnog osiguranja?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Nismo mogli potvrditi jer je nešto po<PERSON>lo po krivu", "app.modules.id_oostende_rrn.submit": "Pošalji", "app.modules.id_oostende_rrn.takenFormError": "V<PERSON>š broj socijalnog osiguranja već je korišten za potvrđivanje drugog računa", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Potvrdi putem broja socijalnog osiguranja", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Dodijeljena su vam administratorska prava nad mapom „{folderName}“.", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Prikažite projekte na {folderUrl} kako bi se čuo vaš glas!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | s platforme za sudjelovanje organizacije {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | s platforme za sudjelovanje organizacije {orgName}", "app.sessionRecording.accept": "Da, prihvaćam", "app.sessionRecording.modalDescription1": "<PERSON><PERSON> bismo bolje razumjeli naše koris<PERSON>e, nasu<PERSON>čno tražimo od malog postotka posjetitelja da detaljno prate svoju sesiju pregledavanja.", "app.sessionRecording.modalDescription2": "Jedina svrha snimljenih podataka je poboljšanje web stranice. Nijedan od vaših podataka neće se dijeliti s trećom stranom. Sve osjetljive informacije koje unesete bit će filtrirane.", "app.sessionRecording.modalDescription3": "Prihvaćate li?", "app.sessionRecording.modalDescriptionFaq": "FAQ ovdje.", "app.sessionRecording.modalTitle": "Pomozite nam poboljšati ovu web stranicu", "app.sessionRecording.reject": "Ne, odbijam", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Provedite vježbu raspodjele proračuna", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Prikupite povratne informacije o dokumentu", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Izradite upitnik unutar platforme", "app.utils.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Ugradite vanjski upitnik", "app.utils.AdminPage.ProjectEdit.findVolunteers": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Prikupljajte unose i povratne informacije", "app.utils.AdminPage.ProjectEdit.shareInformation": "Podijelite informacije", "app.utils.FormattedCurrency.credits": "krediti", "app.utils.FormattedCurrency.tokens": "<PERSON>i", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# kredit} one {# kredit} other {# kredit}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokeni} one {# token} other {# tokeni}}", "app.utils.IdeaCards.mostDiscussed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.mostReacted": "Većina reakci<PERSON>", "app.utils.IdeaCards.newest": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.oldest": "naj<PERSON><PERSON>", "app.utils.IdeaCards.random": "Slučajno", "app.utils.IdeaCards.trending": "<PERSON> <PERSON>u", "app.utils.IdeasNewPage.contributionFormTitle": "Do<PERSON>j novi doprinos", "app.utils.IdeasNewPage.ideaFormTitle": "Dodaj novu ideju", "app.utils.IdeasNewPage.initiativeFormTitle": "Dodajte novu inicijativu", "app.utils.IdeasNewPage.issueFormTitle1": "Dodajte novi komentar", "app.utils.IdeasNewPage.optionFormTitle": "Dodaj novu opciju", "app.utils.IdeasNewPage.petitionFormTitle": "Dodaj novu peticiju", "app.utils.IdeasNewPage.projectFormTitle": "Dodaj novi projekt", "app.utils.IdeasNewPage.proposalFormTitle": "Dodajte novi prijedlog", "app.utils.IdeasNewPage.questionFormTitle": "Dodaj novo pitanje", "app.utils.IdeasNewPage.surveyTitle": "Upitnik", "app.utils.IdeasNewPage.viewYourComment": "Pogledajte svoj komentar", "app.utils.IdeasNewPage.viewYourContribution": "Pogledajte svoj doprinos", "app.utils.IdeasNewPage.viewYourIdea": "Pogledajte svoju ideju", "app.utils.IdeasNewPage.viewYourInitiative": "Pogledajte svoju inicijativu", "app.utils.IdeasNewPage.viewYourInput": "Pogledajte svoj unos", "app.utils.IdeasNewPage.viewYourIssue": "Pogledajte svoj problem", "app.utils.IdeasNewPage.viewYourOption": "Pogledajte svoju opciju", "app.utils.IdeasNewPage.viewYourPetition": "Pogledajte svoju peticiju", "app.utils.IdeasNewPage.viewYourProject": "Pogledajte svoj projekt", "app.utils.IdeasNewPage.viewYourProposal": "Pogledajte svoj prijedlog", "app.utils.IdeasNewPage.viewYourQuestion": "Pogledajte svoje pitanje", "app.utils.Projects.sendSubmission": "Pošalji identifikator prijave na moju e-poštu", "app.utils.Projects.sendSurveySubmission": "Pošalji identifikator podnošenja ankete na moju e-poštu", "app.utils.Projects.surveySubmission": "Podnošenje an<PERSON>e", "app.utils.Projects.yourResponseHasTheFollowingId": "<PERSON><PERSON><PERSON> odgovor ima sljedeći identifikator: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "Ako kasnije odlučite da želite da se vaš odgovor ukloni, kontaktirajte nas sa sljedećim jedinstvenim identifikatorom:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Morate ispuniti svoj profil da biste prisustvovali ovom događaju.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Ne ispunjavate uvjete za prisustvovanje ovom događaju.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Nije vam dopušteno prisustvovati ovom događaju.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Morate se prijaviti ili registrirati da biste prisustvovali ovom događaju.", "app.utils.actionDescriptors.attendingEventNotVerified": "Morate potvrditi svoj račun prije nego što možete prisustvovati ovom događaju.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "Za volontiranje morate ispuniti svoj profil.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Ne ispunjavate uvjete za volontiranje.", "app.utils.actionDescriptors.volunteeringNotPermitted": "Nije vam dopušteno volontirati.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Za volontiranje se morate prijaviti ili registrirati.", "app.utils.actionDescriptors.volunteeringNotVerified": "Morate potvrditi svoj račun prije nego što možete volontirati.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Molimo {completeRegistrationLink} da volontirate.", "app.utils.errors.api_error_default.in": "<PERSON><PERSON>", "app.utils.errors.default.ajv_error_birthyear_required": "Unesite svoju godinu rođenja", "app.utils.errors.default.ajv_error_date_any": "Unesite valjani datum", "app.utils.errors.default.ajv_error_domicile_required": "Unesite svoju mjesto prebivališta", "app.utils.errors.default.ajv_error_gender_required": "Unesite svoj spol", "app.utils.errors.default.ajv_error_invalid": "<PERSON><PERSON> v<PERSON>", "app.utils.errors.default.ajv_error_maxItems": "Ne može sadrž<PERSON>ti više od {limit, plural, one {# stavke} other {# stavki}}", "app.utils.errors.default.ajv_error_minItems": "<PERSON><PERSON> naj<PERSON>je {limit, plural, one {# stavke} other {# stavki}}", "app.utils.errors.default.ajv_error_number_any": "Unesite valjani broj", "app.utils.errors.default.ajv_error_politician_required": "Unesite jeste li političar", "app.utils.errors.default.ajv_error_required3": "<PERSON><PERSON> je obavezno: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Ne može biti prazno", "app.utils.errors.default.api_error_accepted": "Mora se prihvatiti", "app.utils.errors.default.api_error_blank": "Ne može biti prazno", "app.utils.errors.default.api_error_confirmation": "<PERSON>e poklapa se", "app.utils.errors.default.api_error_empty": "Ne može ostati prazno", "app.utils.errors.default.api_error_equal_to": "<PERSON><PERSON>", "app.utils.errors.default.api_error_even": "<PERSON>ra biti parno", "app.utils.errors.default.api_error_exclusion": "<PERSON> rezervirano", "app.utils.errors.default.api_error_greater_than": "Je premalo", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Je premalo", "app.utils.errors.default.api_error_inclusion": "Nije uključeno na popis", "app.utils.errors.default.api_error_invalid": "<PERSON><PERSON> v<PERSON>", "app.utils.errors.default.api_error_less_than": "<PERSON> preveliko", "app.utils.errors.default.api_error_less_than_or_equal_to": "<PERSON> preveliko", "app.utils.errors.default.api_error_not_a_number": "<PERSON><PERSON> broj", "app.utils.errors.default.api_error_not_an_integer": "<PERSON><PERSON> biti cijeli broj", "app.utils.errors.default.api_error_other_than": "<PERSON><PERSON>", "app.utils.errors.default.api_error_present": "<PERSON>ra biti prazno", "app.utils.errors.default.api_error_too_long": "Je predugo", "app.utils.errors.default.api_error_too_short": "<PERSON> pre<PERSON>ko", "app.utils.errors.default.api_error_wrong_length": "<PERSON> pogrešne duljine", "app.utils.errors.defaultapi_error_.odd": "<PERSON>ra biti neparno", "app.utils.notInGroup": "Ne ispunjavate uvjete za sudjelovanje.", "app.utils.participationMethod.onSurveySubmission": "H<PERSON>a vam. <PERSON><PERSON><PERSON> smo vaš odgovor.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Glasovanje više nije dostupno jer ova faza više nije aktivna.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Ne ispunjavate uvjete za glasanje.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Nije vam dopušteno glasati.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Za glasanje se morate prijaviti ili registrirati.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Morate potvrditi svoj račun prije nego što možete glasati.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Slanje proračuna zatvoreno je {endDate}.</b> Sudionici su imali ukupno <b>{maxBudget} svaki za raspodjelu između {optionCount} opcija.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Predan proračun", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Predan proračun 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Ne ispunjavate uvjete za dodjelu proračuna.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Nije vam dopušteno dodjeljivati proračune.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Morate se prijaviti ili registrirati da biste dodijelili proračune.", "app.utils.votingMethodUtils.budgetingNotVerified": "Morate potvrditi svoj račun prije nego što možete dodijeliti proračune.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b><PERSON><PERSON><PERSON> se neće računati kao</b> dok ne kliknete \"Pošalji\"", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Minimalni potrebni proračun je {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "<PERSON><PERSON> ste gotovi, <PERSON><PERSON><PERSON><PERSON> \"Pošalji\" da pošaljete svoj proračun.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Odaberite željene opcije dodirom na \"Dodaj\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "I<PERSON> <b>{maxBudget} za raspod<PERSON><PERSON> između {optionCount} opcija</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON>, vaš <PERSON> je predan!</b> Možete provjeriti svoje opcije ispod u bilo kojem trenutku ili ih izmijeniti prije <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON>, vaš <PERSON> je predan!</b> Možete provjeriti svoje opcije u nastavku u bilo kojem trenutku.", "app.utils.votingMethodUtils.castYourVote": "<PERSON>jte svoj glas", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Možete dodati maksimalno {maxVotes, plural, one {# kredita} other {# kredita}} po opciji.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Možete dodati maksimalno {maxVotes, plural, one {# bodova} other {# bodova}} po opciji.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Možete dodati maksimalno {maxVotes, plural, one {# tokena} other {# tokena}} po opciji.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Možete dodati maksimalno {maxVotes, plural, one {# glasova} other {# glasova}} po opciji.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON><PERSON> ste gotovi, <PERSON><PERSON><PERSON><PERSON> \"Pošalji\" da biste dali svoj glas.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Odaberite željene opcije dodirom na \"Odaberi\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "<PERSON><PERSON><PERSON><PERSON> imate <b>{totalVotes, plural, one {# kredita} other {# kredita}} za raspod<PERSON>lu između {optionCount, plural, one {# opcije} other {# opcija}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "<PERSON><PERSON><PERSON><PERSON> imate <b>{totalVotes, plural, one {# bod} other {# bodova}} za raspodijeliti između {optionCount, plural, one {# opcija} other {# opcije}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "<PERSON><PERSON><PERSON><PERSON> imate <b>{totalVotes, plural, one {# žeton} other {# žetona}} za raspod<PERSON>lu između {optionCount, plural, one {# opcija} other {# opcija}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "Imate <PERSON><PERSON><PERSON> <b>{totalVotes, plural, one {# glasova} other {# glasova}} za raspod<PERSON><PERSON> između {optionCount, plural, one {# opcija} other {# opcije}}</b>.", "app.utils.votingMethodUtils.finalResults": "Konačni rezultati", "app.utils.votingMethodUtils.finalTally": "Konačni zbroj", "app.utils.votingMethodUtils.howToParticipate": "<PERSON><PERSON>", "app.utils.votingMethodUtils.howToVote": "<PERSON><PERSON>", "app.utils.votingMethodUtils.multipleVotingEnded1": "Glasan<PERSON> je zatvoreno <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 kredita} one {1 kredit} other {# kredita}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 bodova} one {1 bod} other {# bodova}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokena} one {1 token} other {# tokena}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 glasova} one {1 glas} other {# glasova}}", "app.utils.votingMethodUtils.results": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.singleVotingEnded": "Glasovanje je zatvoreno <b>{endDate}.</b> sudionika mogla su <b>glasati za {maxVotes} opcije.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Odaberite željene opcije dodirom na \"Glasaj\"", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Imate <b>{totalVotes} glasova</b> koje možete dodijeliti opcijama.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON><PERSON> ste gotovi, <PERSON><PERSON><PERSON><PERSON> \"Pošalji\" da biste dali svoj glas.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Glasovanje je zatvoreno <b>{endDate}.</b> sudionika mogla su <b>glasati za 1 opciju.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Odaberite željenu opciju dodirom na \"Glasaj\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Imate <b>1 glas</b> koji možete dodijeliti jednoj od opcija.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Glasovanje je zatvoreno <b>{endDate}.</b> Sudionika su mogla <b>glasati za onoliko opcija koliko žele.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Možete glasati za onoliko opcija koliko želite.", "app.utils.votingMethodUtils.submitYourBudget": "Pošaljite svoj proračun", "app.utils.votingMethodUtils.submittedBudgetCountText2": "osoba je predala svoj proračun online", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "ljudi predali su svoje proračune online", "app.utils.votingMethodUtils.submittedVoteCountText2": "osoba je poslala svoj glas online", "app.utils.votingMethodUtils.submittedVotesCountText2": "osoba je dalo svoje glasove online", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Glasovanje poslano 🎉", "app.utils.votingMethodUtils.votesCast": "<PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.votingClosed": "Glasanje je zatvoreno", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b><PERSON><PERSON><PERSON>las neće biti uračunat</b> dok ne kliknete \"Pošalji\"", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON>, vaš je glas poslan!</b> Svoju prijavu možete provjeriti ili izmijeniti prije <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON>, vaš je glas poslan!</b> Svoju prijavu možete provjeriti ili izmijeniti u bilo kojem trenutku.", "components.UI.IdeaSelect.noIdeaAvailable": "<PERSON><PERSON> id<PERSON>.", "components.UI.IdeaSelect.selectIdea": "Odaberite ideju", "containers.SiteMap.allProjects": "Svi projekti", "containers.SiteMap.customPageSection": "Prilagođene stranice", "containers.SiteMap.folderInfo": "Više informacija", "containers.SiteMap.headSiteMapTitle": "Karta stranice | {orgName}", "containers.SiteMap.homeSection": "Općenito", "containers.SiteMap.pageContents": "<PERSON><PERSON><PERSON><PERSON> stranic<PERSON>", "containers.SiteMap.profilePage": "<PERSON><PERSON>ša profilna stranica", "containers.SiteMap.profileSettings": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectIdeas": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectInfo": "Informacije", "containers.SiteMap.projectPoll": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectSurvey": "Upitnik", "containers.SiteMap.projectsArchived": "<PERSON><PERSON><PERSON><PERSON><PERSON> projekti", "containers.SiteMap.projectsCurrent": "Trenutačni projekti", "containers.SiteMap.projectsDraft": "Nacrti projekata", "containers.SiteMap.projectsSection": "Projekti {orgName}", "containers.SiteMap.signInPage": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.signUpPage": "Registracija", "containers.SiteMap.siteMapDescription": "S ove stranice možete pristupiti bilo kom sadržaju na platformi.", "containers.SiteMap.siteMapTitle": "Mapa web-mjesta platforme za sudjelovanje organizacije {orgName}", "containers.SiteMap.successStories": "Priče o uspjehu", "containers.SiteMap.timeline": "Faze projekta", "containers.SiteMap.userSpaceSection": "<PERSON><PERSON><PERSON>", "containers.SubscriptionEndedPage.accessDenied": "<PERSON><PERSON><PERSON><PERSON> nemate pristup", "containers.SubscriptionEndedPage.subscriptionEnded": "Ova stranica dostupna e samo za platforme s aktivnom pretplatom."}
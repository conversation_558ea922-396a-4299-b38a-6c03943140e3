{"EmailSettingsPage.emailSettings": "Configuración del correo electrónico", "EmailSettingsPage.initialUnsubscribeError": "Hubo un problema al cancelar su suscripción a esta campaña. Inténtelo más tarde.", "EmailSettingsPage.initialUnsubscribeLoading": "Su solicitud se está procesando, espere por favor...", "EmailSettingsPage.initialUnsubscribeSuccess": "Ha cancelado correctamente su suscripción a {campaignTitle}.", "UI.FormComponents.optional": "opcional", "app.closeIconButton.a11y_buttonActionMessage": "<PERSON><PERSON><PERSON>", "app.components.Areas.areaUpdateError": "Se ha producido un error al guardar tu área. Inténtalo de nuevo.", "app.components.Areas.followedArea": "<PERSON><PERSON> de seguimiento: {areaTitle}", "app.components.Areas.followedTopic": "<PERSON><PERSON>: {topicTitle}", "app.components.Areas.topicUpdateError": "Se ha producido un error al guardar tu tema. Por favor, inténtalo de nuevo.", "app.components.Areas.unfollowedArea": "Área no seguida: {areaTitle}", "app.components.Areas.unfollowedTopic": "<PERSON><PERSON> no seguido: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Precio:", "app.components.AssignBudgetControl.add": "Agregar", "app.components.AssignBudgetControl.added": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.addVote": "<PERSON><PERSON><PERSON> voto", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Has distribuido todos tus créditos.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Has distribuido el número máximo de créditos para esta opción.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Has distribuido todos tus puntos.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Has distribuido el número máximo de puntos para esta opción.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Has distribuido todas tus fichas.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Has distribuido el número máximo de fichas para esta opción.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Has distribuido todos tus votos.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Has distribuido el número máximo de votos para esta opción.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "No se puede votar, ya que esta fase no está activa.", "app.components.AssignMultipleVotesControl.removeVote": "Eliminar voto", "app.components.AssignMultipleVotesControl.select": "Seleccione", "app.components.AssignMultipleVotesControl.votesSubmitted1": "<PERSON> has enviado tu voto. Para modificarlo, haz clic en \"Modificar tu participación\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Ya has enviado tu voto. Para modificarlo, vuelve a la página del proyecto y haz clic en \"Modificar tu participación\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {crédito} other {créditos}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {punto} other {puntos}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {ficha} other {fichas}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {vota} other {vota}}", "app.components.AssignVoteControl.maxVotesReached1": "Has distribuido todos tus votos.", "app.components.AssignVoteControl.phaseNotActive": "No se puede votar, ya que esta fase no está activa.", "app.components.AssignVoteControl.select": "Seleccione", "app.components.AssignVoteControl.selected2": "Selección", "app.components.AssignVoteControl.voteForAtLeastOne": "Vota al menos 1 opción", "app.components.AssignVoteControl.votesSubmitted1": "<PERSON> has enviado tu voto. Para modificarlo, haz clic en \"Modificar tu participación\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Ya has enviado tu voto. Para modificarlo, vuelve a la página del proyecto y haz clic en \"Modificar tu participación\".", "app.components.AuthProviders.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.continueWithAzure": "Continúa con {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Continúa con Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Continuar con el SSO falso", "app.components.AuthProviders.continueWithGoogle": "Continúa con Google", "app.components.AuthProviders.continueWithHoplr": "<PERSON><PERSON><PERSON><PERSON> con <PERSON>", "app.components.AuthProviders.continueWithIdAustria": "Continuar con ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Continúa con {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Continuar con MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Ya existe una cuenta con esta dirección de correo electrónico.{br}{br}No puede acceder a la plataforma utilizando FranceConnect ya que los datos personales no coinciden. Para acceder a través de FranceConnect, tendrá que cambiar primero su nombre o apellido en esta plataforma para que coincida con sus datos oficiales.{br}{br}Puede acceder como lo hace normalmente a continuación.", "app.components.AuthProviders.goToLogIn": "¿Ya tienes una cuenta? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "¿No tienes una cuenta? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Inicia se<PERSON>", "app.components.AuthProviders.logInWithEmail": "Ingresa con el correo electrónico", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Debes tener la edad mínima especificada o superior para ser verificado.", "app.components.AuthProviders.signUp2": "Inscríbete", "app.components.AuthProviders.signUpButtonAltText": "Registrate con {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Ingresa con el correo electrónico", "app.components.AuthProviders.verificationRequired": "Verificación necesaria", "app.components.Author.a11yPostedBy": "Publicado por", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 participante} other {{numberOfParticipants} participantes}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} usuarios", "app.components.AvatarBubbles.participant": "participante", "app.components.AvatarBubbles.participants1": "participantes", "app.components.Comments.cancel": "<PERSON><PERSON><PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "No es posible comentar en la fase actual.", "app.components.Comments.commentingDisabledInactiveProject": "No es posible comentar porque este proyecto no está activo actualmente.", "app.components.Comments.commentingDisabledProject": "Los comentarios en este proyecto actualmente están deshabilitados.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} para comentar.", "app.components.Comments.commentingMaybeNotPermitted": "Por favor {signInLink} para ver las acciones que puede llevar a cabo.", "app.components.Comments.inputsAssociatedWithProfile": "Por defecto, tus aportes se asociarán a tu perfil, a menos que selecciones esta opción.", "app.components.Comments.invisibleTitleComments": "Comentarios", "app.components.Comments.leastRecent": "Menos reciente", "app.components.Comments.likeComment": "Me gusta este comentario", "app.components.Comments.mostLiked": "Con más reacciones", "app.components.Comments.mostRecent": "Más recientes", "app.components.Comments.official": "Oficial", "app.components.Comments.postAnonymously": "Publicar anónimamente", "app.components.Comments.replyToComment": "Responder al comentario", "app.components.Comments.reportAsSpam": "Reporte como spam", "app.components.Comments.seeOriginal": "Ver original", "app.components.Comments.seeTranslation": "<PERSON><PERSON>", "app.components.Comments.yourComment": "Tu comentario", "app.components.CommonGroundResults.divisiveDescription": "Afirmaciones en las que la gente está de acuerdo y en desacuerdo por igual:", "app.components.CommonGroundResults.divisiveTitle": "Divisivo", "app.components.CommonGroundResults.majorityDescription": "Más del 60% votó en un sentido u otro sobre lo siguiente:", "app.components.CommonGroundResults.majorityTitle": "<PERSON><PERSON>", "app.components.CommonGroundResults.participantLabel": "participante", "app.components.CommonGroundResults.participantsLabel1": "participantes", "app.components.CommonGroundResults.statementLabel": "opinión", "app.components.CommonGroundResults.statementsLabel1": "opiniones", "app.components.CommonGroundResults.votesLabe": "voto", "app.components.CommonGroundResults.votesLabel1": "votos", "app.components.CommonGroundStatements.agreeLabel": "De acuerdo", "app.components.CommonGroundStatements.disagreeLabel": "En desacuerdo", "app.components.CommonGroundStatements.noMoreStatements": "No hay opiniones por responder en este momento", "app.components.CommonGroundStatements.noResults": "Todavía no hay resultados que mostrar. Por favor, asegúrate de que has participado y vuelve a comprobarlo aquí después.", "app.components.CommonGroundStatements.unsureLabel": "Pasar", "app.components.CommonGroundTabs.resultsTabLabel": "Resul<PERSON><PERSON>", "app.components.CommonGroundTabs.statementsTabLabel": "Opiniones", "app.components.CommunityMonitorModal.formError": "Se ha producido un error.", "app.components.CommunityMonitorModal.surveyDescription2": "Esta encuesta continua analiza tu opinión sobre la gobernanza y los servicios públicos.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {<PERSON>lev<PERSON> <1 minuto} one {Lleva 1 minuto} other {Lleva # minutos}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Se ha enviado un código de confirmación a tu correo electrónico {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Cambia tu correo electrónico.", "app.components.ConfirmationModal.codeInput": "Código", "app.components.ConfirmationModal.confirmationCodeSent": "Nuevo código enviado", "app.components.ConfirmationModal.didntGetAnEmail": "¿No has recibido un correo electrónico?", "app.components.ConfirmationModal.foundYourCode": "¿Encontraste tu código?", "app.components.ConfirmationModal.goBack": "Vuelve atrás.", "app.components.ConfirmationModal.sendEmailWithCode": "Enviar correo electrónico con código", "app.components.ConfirmationModal.sendNewCode": "Enviar nuevo código.", "app.components.ConfirmationModal.verifyAndContinue": "Verificar y continuar", "app.components.ConfirmationModal.wrongEmail": "¿Correo electrónico incorrecto?", "app.components.ConsentManager.Banner.accept": "Aceptar", "app.components.ConsentManager.Banner.ariaButtonClose2": "<PERSON><PERSON><PERSON>ol<PERSON> y cerrar banner", "app.components.ConsentManager.Banner.close": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.mainText": "Esta plataforma utiliza cookies de acuerdo con nuestra {policyLink}.", "app.components.ConsentManager.Banner.manage": "Administrar", "app.components.ConsentManager.Banner.policyLink": "Política de cookies", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Publicidad", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Usamos esto para personalizar y medir la efectividad de campañas de nuestro sitio Web. No mostramos ningún tipo de publicidad en esta plataforma, pero los siguientes servicios pueden ofrecer un anuncio personalizado basado en las páginas que visitas en nuestro sitio.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analytics", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Utilizamos este seguimiento para comprender mejor cómo usar la plataforma para aprender y mejorar tu navegación. Esta información sólo se utiliza en análisis de masa y de ninguna manera personalmente.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Volver", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "No permitir", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funcional", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Esto es necesario para activar y controlar funciones básicas del sitio Web. Algunas herramientas mencionadas anteriormente podrían no aplicarse a usted. Por favor lea nuestra política de cookies para obtener más información.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Obligatorio", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Para tener una plataforma funcional, guardamos una cookie de autenticación si te registras, y el idioma en el que usas esta plataforma.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Guardar", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Sus preferencias de cookies", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Herramientas", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Cláusula de exención de responsabilidad sobre la carga de contenidos", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Al subir contenido, declaras que este contenido no viola ninguna normativa o derecho de terceros, como derechos de propiedad intelectual, derechos de privacidad, derechos sobre secretos comerciales, etc. En consecuencia, al subir este contenido, te comprometes a asumir la responsabilidad total y exclusiva de todos los daños directos e indirectos derivados del contenido subido. Además, te comprometes a indemnizar al propietario de la plataforma y a Go Vocal por cualquier reclamación o responsabilidad de terceros contra terceros, y por cualquier coste asociado, que surja o resulte del contenido que subiste.", "app.components.ContentUploadDisclaimer.onAccept": "Co<PERSON><PERSON><PERSON>", "app.components.ContentUploadDisclaimer.onCancel": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "<PERSON><PERSON> por qué", "app.components.CustomFieldsForm.addressInputAriaLabel": "Dirección", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Introduce una dirección...", "app.components.CustomFieldsForm.adminFieldTooltip": "Campo solo visible para los administradores", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Las respuestas a esta encuesta son anónimas.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Para un polígono se necesitan al menos tres puntos.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "Para una línea se necesitan al menos dos puntos.", "app.components.CustomFieldsForm.attachmentRequired": "Se requiere al menos un adjunto", "app.components.CustomFieldsForm.authorFieldLabel": "Autor", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Buscar por correo o nombre de usuario...", "app.components.CustomFieldsForm.back": "Volver", "app.components.CustomFieldsForm.budgetFieldLabel": "Presupuesto", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "<PERSON>z clic en el mapa para dibujar. Luego, arrastra sobre los puntos para moverlos.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Haz clic en el mapa o escribe una dirección abajo para añadir tu respuesta.", "app.components.CustomFieldsForm.confirm": "Confirmar", "app.components.CustomFieldsForm.descriptionMinLength": "La descripción debe tener al menos {min} caracteres", "app.components.CustomFieldsForm.descriptionRequired": "La descripción es obligatoria", "app.components.CustomFieldsForm.fieldMaximumItems": "Como máximo se puede seleccionar {maxSelections, plural, one {# opción} other {# opciones}} para el campo \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "Al menos {minSelections, plural, one {# opción} other {# opciones}} pueden ser seleccionadas para el campo \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "El campo \"{fieldName}\" es obligatorio", "app.components.CustomFieldsForm.fileSizeLimit": "El límite de tamaño del archivo es {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "La imagen es necesaria", "app.components.CustomFieldsForm.minimumCoordinates2": "Se requiere un mínimo de {numPoints} puntos de mapa.", "app.components.CustomFieldsForm.notPublic1": "*Esta respuesta sólo se compartirá con los gestores del proyecto, y no con el público.", "app.components.CustomFieldsForm.otherArea": "En algún otro lugar", "app.components.CustomFieldsForm.progressBarLabel": "Progreso", "app.components.CustomFieldsForm.removeAnswer": "Eliminar respuesta", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Selecciona tantos como quieras", "app.components.CustomFieldsForm.selectBetween": "*Selecciona entre {minItems} y {maxItems} opciones", "app.components.CustomFieldsForm.selectExactly2": "Selecciona exactamente {selectExactly, plural, one {# opción} other {# opciones}}", "app.components.CustomFieldsForm.selectMany": "*Elige tantas como quieras", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Toca en el mapa para dibujar. Luego, arrastra sobre los puntos para moverlos.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Toca en el mapa para dibujar.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Toca en el mapa para añadir tu respuesta.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Toca en el mapa o escribe una dirección abajo para añadir tu respuesta.", "app.components.CustomFieldsForm.tapToAddALine": "Toca para añadir una línea", "app.components.CustomFieldsForm.tapToAddAPoint": "Toca para añadir un punto", "app.components.CustomFieldsForm.tapToAddAnArea": "Toca para añadir un área", "app.components.CustomFieldsForm.titleMaxLength": "El título debe tener como máximo {max} caracteres", "app.components.CustomFieldsForm.titleMinLength": "El título debe tener al menos {min} caracteres", "app.components.CustomFieldsForm.titleRequired": "El título es obligatorio", "app.components.CustomFieldsForm.topicRequired": "Se requiere al menos una etiqueta", "app.components.CustomFieldsForm.typeYourAnswer": "Escribe tu respuesta", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Es necesario que escribas tu respuesta", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Sube un archivo zip que contenga uno o varios archivos shape.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "Si la ubicación no aparece entre las opciones mientras escribes, puedes añadir coordenadas válidas en el formato \"latitud, longitud\" para especificar una ubicación precisa (por ejemplo: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Algunos campos son inv<PERSON>lid<PERSON>. Por favor inténtalo nuevamente.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Error desconocido. Por favor inténtalo nuevamente.", "app.components.ErrorBoundary.errorFormLabelClose": "<PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelComments": "¿Qué ha pasado?", "app.components.ErrorBoundary.errorFormLabelEmail": "Correo electrónico", "app.components.ErrorBoundary.errorFormLabelName": "Nombre", "app.components.ErrorBoundary.errorFormLabelSubmit": "Enviar", "app.components.ErrorBoundary.errorFormSubtitle": "Nuestro equipo ha sido notificado.", "app.components.ErrorBoundary.errorFormSubtitle2": "Si quieres ayudarnos, cuén<PERSON>s que ocurrió.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Has enviado tus comentarios. ¡Gracias!", "app.components.ErrorBoundary.errorFormTitle": "Parece que hay un problema.", "app.components.ErrorBoundary.genericErrorWithForm": "Se ha producido un error y no podemos mostrar este contenido. ¡Por favor, inténtelo de nuevo {openForm}!", "app.components.ErrorBoundary.openFormText": "<PERSON><PERSON><PERSON><PERSON><PERSON> a entender", "app.components.ErrorToast.budgetExceededError": "No tienes suficiente presupuesto", "app.components.ErrorToast.votesExceededError": "No te quedan votos suficientes", "app.components.EventAttendanceButton.forwardToFriend": "Reenviar a un amigo", "app.components.EventAttendanceButton.maxRegistrationsReached": "Se ha alcanzado el número máximo de inscripciones al evento. No quedan plazas.", "app.components.EventAttendanceButton.register": "Regístrate", "app.components.EventAttendanceButton.registered": "Registrado", "app.components.EventAttendanceButton.seeYouThere": "¡Nos vemos allí!", "app.components.EventAttendanceButton.seeYouThereName": "¡Nos vemos allí, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Menos información de los eventos visible.", "app.components.EventCard.a11y_moreContentVisible": "Se ha hecho visible más información sobre el evento.", "app.components.EventCard.a11y_readMore": "Más información sobre el evento \"{eventTitle}\".", "app.components.EventCard.endsAt": "Termina en", "app.components.EventCard.readMore": "<PERSON><PERSON>", "app.components.EventCard.showLess": "<PERSON><PERSON> menos", "app.components.EventCard.showMore": "Mostrar más", "app.components.EventCard.startsAt": "Empieza en", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Eventos próximos y en curso en este proyecto", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Eventos próximos y en curso en esta fase", "app.components.FileUploader.a11y_file": "Archivo:", "app.components.FileUploader.a11y_filesToBeUploaded": "Archivos por subir: {fileNames}", "app.components.FileUploader.a11y_noFiles": "No se han añadido archivos.", "app.components.FileUploader.a11y_removeFile": "Eliminar este archivo", "app.components.FileUploader.fileInputDescription": "Haga clic para seleccionar un archivo", "app.components.FileUploader.fileUploadLabel": "Archivos adjuntos (máximo 50MB)", "app.components.FileUploader.file_too_large2": "No se permiten archivos de más de {maxSizeMb}MB.", "app.components.FileUploader.incorrect_extension": "{fileName} no es compatible con nuestro sistema, no será cargado.", "app.components.FilterBoxes.a11y_allFilterSelected": "Filtro de estado seleccionado: todos", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# aporte} other {# aportes}}", "app.components.FilterBoxes.a11y_removeFilter": "Eliminar filtro", "app.components.FilterBoxes.a11y_selectedFilter": "Filtro de estado seleccionado: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Seleccionado {numberOfSelectedTopics, plural, =0 {zero topic filters} one {one topic filter} otro {# topic filters}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Todos", "app.components.FilterBoxes.areas": "Filtrar por área", "app.components.FilterBoxes.inputs": "aportes", "app.components.FilterBoxes.noValuesFound": "No hay valores disponibles.", "app.components.FilterBoxes.showLess": "<PERSON><PERSON> menos", "app.components.FilterBoxes.showTagsWithNumber": "Mostrar todo ({numberTags})", "app.components.FilterBoxes.statusTitle": "Estado", "app.components.FilterBoxes.topicsTitle": "<PERSON><PERSON>", "app.components.FiltersModal.filters": "<PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.a11y_folderDescription": "Descripción de la carpeta:", "app.components.FolderFolderCard.a11y_folderTitle": "<PERSON><PERSON><PERSON><PERSON>:", "app.components.FolderFolderCard.archived": "Archivado", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# proyectos} one {# proyecto} other {# proyectos}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "El tipo de campo no puede cambiarse una vez que se han recibido respuestas.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Tipo", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Autoguardado", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "El guardado automático está activado por defecto cuando abres el editor de formularios. Cada vez que cierres el panel de configuración de campos con el botón \"X\", se activará automáticamente el guardado.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "Trimestre", "app.components.GanttChart.timeRange.timeRangeMultiyear": "Varios años", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON>", "app.components.GanttChart.today": "Hoy", "app.components.GoBackButton.group.edit.goBack": "Volver", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Volver a la página anterior", "app.components.HookForm.Feedback.errorTitle": "Hay un problema", "app.components.HookForm.Feedback.submissionError": "Inténtelo de nuevo. Si el problema persiste, póngase en contacto con nosotros", "app.components.HookForm.Feedback.submissionErrorTitle": "Hubo un problema de nuestro parte, lo sentimos", "app.components.HookForm.Feedback.successMessage": "Formulario enviado con éxito", "app.components.HookForm.PasswordInput.passwordLabel": "Contraseña", "app.components.HorizontalScroll.scrollLeftLabel": "Desplázate a la izquierda.", "app.components.HorizontalScroll.scrollRightLabel": "Desplázate a la derecha.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} las ideas se han cargado.", "app.components.IdeaCards.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.mostDiscussed": "Lo más discutido", "app.components.IdeaCards.filters.newest": "Nuevo", "app.components.IdeaCards.filters.oldest": "Antigu<PERSON>", "app.components.IdeaCards.filters.popular": "<PERSON> m<PERSON> likes", "app.components.IdeaCards.filters.random": "Aleat<PERSON>", "app.components.IdeaCards.filters.sortBy": "Ordenar por", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Clasificación cambiada a: {currentSortType}", "app.components.IdeaCards.filters.trending": "Trending", "app.components.IdeaCards.showMore": "Mostrar más", "app.components.IdeasMap.a11y_hideIdeaCard": "Ocultar tarjeta de idea.", "app.components.IdeasMap.a11y_mapTitle": "Descripción general del mapa", "app.components.IdeasMap.clickOnMapToAdd": "Haz clic en el mapa para añadir tu aporte", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Como administrador, puedes hacer clic en el mapa para añadir tu aportación, aunque esta fase no esté activa.", "app.components.IdeasMap.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.multipleInputsAtLocation": "<PERSON>úl<PERSON>les entradas en este lugar", "app.components.IdeasMap.noFilteredResults": "Los filtros que seleccionaste no arrojaron resultados", "app.components.IdeasMap.noResults": "No se han encontrado resultados\n", "app.components.IdeasMap.or": "o", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, sin aversiones.} one {1 aversión.} other {, # no me gusta.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, sin gustos.} one {, 1 like.} other {, # me gusta.}}", "app.components.IdeasMap.signInLinkText": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.signUpLinkText": "inscríbete", "app.components.IdeasMap.submitIdea2": "Enviar entrada", "app.components.IdeasMap.tapOnMapToAdd": "Pincha en el mapa para añadir tu aporte", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Como administrador, puedes tocar en el mapa para añadir tu aportación, aunque esta fase no esté activa.", "app.components.IdeasMap.userInputs2": "Aportaciones de los participantes", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, sin comentarios} one {, 1 comentario} other {, # comentarios}}", "app.components.IdeasShow.bodyTitle": "Descripción", "app.components.IdeasShow.deletePost": "Eliminar", "app.components.IdeasShow.editPost": "<PERSON><PERSON>", "app.components.IdeasShow.goBack": "Volver", "app.components.IdeasShow.moreOptions": "Más opciones", "app.components.IdeasShow.or": "o", "app.components.IdeasShow.proposedBudgetTitle": "Proyecto de presupuesto", "app.components.IdeasShow.reportAsSpam": "Reporte como spam", "app.components.IdeasShow.send": "Enviar", "app.components.IdeasShow.skipSharing": "Lo haré más adelante", "app.components.IdeasShowPage.signIn2": "Conectarse", "app.components.IdeasShowPage.sorryNoAccess": "Lo sentimos, no puede acceder a esta página. Es posible que tenga que iniciar sesión o registrarse antes.", "app.components.LocationInput.noOptions": "No hay opciones", "app.components.Modal.closeWindow": "<PERSON><PERSON><PERSON> ventana", "app.components.MultiSelect.clearButtonAction": "<PERSON><PERSON><PERSON>", "app.components.MultiSelect.clearSearchButtonAction": "Limpiar b<PERSON>", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Introduzca una nueva dirección de correo electrónico", "app.components.PageNotFound.goBackToHomePage": "Vuelta a la página principal", "app.components.PageNotFound.notFoundTitle": "Página no encontrada", "app.components.PageNotFound.pageNotFoundDescription": "Lo sentimos, no se pudo encontrar la página solicitada.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Proporcionar contenido para al menos un idioma", "app.components.PagesForm.editContent": "Contenido", "app.components.PagesForm.fileUploadLabel": "Archivos adjuntos (máximo 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Los archivos adjuntos aparecerán al fondo de esta página.", "app.components.PagesForm.navbarItemTitle": "Nombre en la barra de navegación", "app.components.PagesForm.pageTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PagesForm.savePage": "Guardar página", "app.components.PagesForm.saveSuccess": "Página guardada con éxito.", "app.components.PagesForm.titleMissingOneLanguageError": "Proporcionar título para al menos un idioma", "app.components.Pagination.back": "Página anterior", "app.components.Pagination.next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Has gastado {votesCast}, lo que supera el límite de {votesLimit}. Por favor, elimina algunos artículos de tu cesta e inténtalo de nuevo.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "Quedan {budgetLeft} / {totalBudget} ", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Necesitas gastar un mínimo de {votesMinimum}  antes de poder enviar tu presupuesto.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Tienes que seleccionar al menos una opción antes de poder enviar.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Tienes que añadir algo a tu cesta antes de poder enviarla.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {No quedan créditos} other {# de {totalNumberOfVotes, plural, one {1 crédito} other {# créditos}} quedan}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {No quedan puntos} other {# de {totalNumberOfVotes, plural, one {1 punto} other {# puntos}} quedan}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {No quedan fichas} other {# de {totalNumberOfVotes, plural, one {1 ficha} other {# fichas}} restantes}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {No quedan votos} other {# de {totalNumberOfVotes, plural, one {1 voto} other {# votos}} quedan}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# votos} one {# voto} other {# votos}} emitidos", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Has emitido {votesCast} votos, lo que excede el límite de {votesLimit}. Por favor, elimina algunos votos e inténtalo de nuevo.", "app.components.ParticipationCTABars.addInput": "<PERSON><PERSON><PERSON> entrada", "app.components.ParticipationCTABars.allocateBudget": "<PERSON><PERSON>a tu presupuesto", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Tu presupuesto se ha enviado correctamente.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Abierto a la participación", "app.components.ParticipationCTABars.poll": "Haz la encuesta", "app.components.ParticipationCTABars.reviewDocument": "Revisa el documento", "app.components.ParticipationCTABars.seeContributions": "Ver aportaciones", "app.components.ParticipationCTABars.seeEvents3": "Ver eventos", "app.components.ParticipationCTABars.seeIdeas": "Ver ideas", "app.components.ParticipationCTABars.seeInitiatives": "<PERSON>er iniciativas", "app.components.ParticipationCTABars.seeIssues": "Ver comentarios", "app.components.ParticipationCTABars.seeOptions": "Ver opciones", "app.components.ParticipationCTABars.seePetitions": "Ver peticiones", "app.components.ParticipationCTABars.seeProjects": "Ver proyectos", "app.components.ParticipationCTABars.seeProposals": "Ver propuestas", "app.components.ParticipationCTABars.seeQuestions": "Ver preguntas", "app.components.ParticipationCTABars.submit": "Enviar", "app.components.ParticipationCTABars.takeTheSurvey": "Responde la consulta", "app.components.ParticipationCTABars.userHasParticipated": "Ha participado en este proyecto.", "app.components.ParticipationCTABars.viewInputs": "Ver entradas", "app.components.ParticipationCTABars.volunteer": "Participar", "app.components.ParticipationCTABars.votesCounter.vote": "votar", "app.components.ParticipationCTABars.votesCounter.votes": "votos", "app.components.PasswordInput.a11y_passwordHidden": "Contraseña oculta", "app.components.PasswordInput.a11y_passwordVisible": "Contraseña visible", "app.components.PasswordInput.a11y_strength1Password": "Poca solidez de las contraseña", "app.components.PasswordInput.a11y_strength2Password": "Poca solidez de las contraseña", "app.components.PasswordInput.a11y_strength3Password": "Solidez de contraseña media", "app.components.PasswordInput.a11y_strength4Password": "Solidez de seguridad de la contraseña", "app.components.PasswordInput.a11y_strength5Password": "Solidez de contraseña muy fuerte", "app.components.PasswordInput.hidePassword": "Ocultar contraseña", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON><PERSON><PERSON> corta (min. {MinimumPasswordLength} caracteres)", "app.components.PasswordInput.minimumPasswordLengthError": "Proporcione una contraseña que tenga al menos {minimumPasswordLength} caracteres", "app.components.PasswordInput.passwordEmptyError": "Introduzca su contraseña", "app.components.PasswordInput.passwordStrengthTooltip1": "Para fortalecer su contraseña:", "app.components.PasswordInput.passwordStrengthTooltip2": "Utilice una combinación de letras minúsculas no consecutivas, letras mayúsculas, cifras, caracteres especiales y puntuación.", "app.components.PasswordInput.passwordStrengthTooltip3": "Evite las palabras comunes o fáciles de adivinar", "app.components.PasswordInput.passwordStrengthTooltip4": "Aumentar la longitud", "app.components.PasswordInput.showPassword": "Mostrar contraseña", "app.components.PasswordInput.strength1Password": "Pobre", "app.components.PasswordInput.strength2Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength3Password": "Media", "app.components.PasswordInput.strength4Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON>", "app.components.PostCardsComponents.list": "Lista", "app.components.PostCardsComponents.map": "Mapa", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Añade una actualización", "app.components.PostComponents.OfficialFeedback.cancel": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Eliminar", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "¿Está seguro que deseas eliminar esta actualización oficial?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "Ultima edición el {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Última actualización: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Oficial", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Elige tu nombre de usuario", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Nombre oficial de quien actualiza", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Texto oficial de la actualización", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Actualizaciones oficiales", "app.components.PostComponents.OfficialFeedback.postedOn": "Publicado el {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Publica", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Mostrar versiones anteriores", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Dar una actualización...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON> sentimo<PERSON>, hubo un error", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Mensaje de actualización", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Tu actualización ha sido publicada con éxito!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "¡Apoya mi aporte'{postTitle}' en {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Apoya mi aporte: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Apoya mi aporte: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "¿Qué piensas de esta idea? Voten y compartan la discusión en {postUrl} para hacer oír tu voz!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Vota por mi idea: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Vota por mi idea: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "¿Qué opinas de esta propuesta? Voten y compartan la discusión en {postUrl} para hacer oír tu voz!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Apoya mi propuesta: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Apoya mi propuesta: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "¡Publiqué un problema '{postTitle}' en {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Acabo de publicar un tema: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Acabo de publicar un tema: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "¡Apoya mi opción propuesta '{postTitle}' en {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Apoya mi opción propuesta: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Apoya mi opción: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "¡Apoya mi petición '{postTitle}' en {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Apoya mi petición: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Apoya mi petición: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "¡Apoya mi proyecto '{postTitle}' en {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Apoya mi proyecto: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Apoya mi proyecto: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "¡Apoya mi propuesta '{postTitle}' en {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Apoya mi propuesta: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Acabo de publicar una propuesta para {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "¡Únete a la discusión sobre esta pregunta '{postTitle}' en {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Únete al debate: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Únete al debate: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Vota por {postTitle} en", "app.components.PostComponents.linkToHomePage": "Enlace a la página de inicio", "app.components.PostComponents.readMore": "<PERSON>r más...", "app.components.PostComponents.topics": "<PERSON><PERSON>", "app.components.ProjectArchivedIndicator.archivedProject": "Desafortunadamente, ya no puedes participar en este proyecto porque ha sido archivado", "app.components.ProjectArchivedIndicator.previewProject": "Project en borrador:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Visible sólo para los moderadores y los que tengan un enlace de previsualización.", "app.components.ProjectCard.a11y_projectDescription": "Descripción del proyecto:", "app.components.ProjectCard.a11y_projectTitle": "Título del proyecto:", "app.components.ProjectCard.addYourOption": "Agrega tu opción", "app.components.ProjectCard.allocateYourBudget": "<PERSON><PERSON>a tu presupuesto", "app.components.ProjectCard.archived": "Archivado", "app.components.ProjectCard.comment": "Comentario", "app.components.ProjectCard.contributeYourInput": "Contribuye con tu entrada", "app.components.ProjectCard.finished": "Finalizado", "app.components.ProjectCard.joinDiscussion": "Únete al debate", "app.components.ProjectCard.learnMore": "Conoce más", "app.components.ProjectCard.reaction": "Reacción", "app.components.ProjectCard.readTheReport": "<PERSON><PERSON> el informe", "app.components.ProjectCard.reviewDocument": "Revisa el documento", "app.components.ProjectCard.submitAnIssue": "Envía tu respuesta", "app.components.ProjectCard.submitYourIdea": "Envía tu idea", "app.components.ProjectCard.submitYourInitiative": "Envía tu iniciativa", "app.components.ProjectCard.submitYourPetition": "Envía tu petición", "app.components.ProjectCard.submitYourProject": "Envia tu proyecto", "app.components.ProjectCard.submitYourProposal": "Envía tu propuesta", "app.components.ProjectCard.takeThePoll": "Haz la consulta ciudadana", "app.components.ProjectCard.takeTheSurvey": "Responde la consulta", "app.components.ProjectCard.viewTheContributions": "Ver las propuestas", "app.components.ProjectCard.viewTheIdeas": "Ver las ideas", "app.components.ProjectCard.viewTheInitiatives": "Ver las iniciativas", "app.components.ProjectCard.viewTheIssues": "Ver los temas", "app.components.ProjectCard.viewTheOptions": "Ver las opciones", "app.components.ProjectCard.viewThePetitions": "Ver las peticiones", "app.components.ProjectCard.viewTheProjects": "Ver los proyectos", "app.components.ProjectCard.viewTheProposals": "Ver las propuestas", "app.components.ProjectCard.viewTheQuestions": "Ver las preguntas", "app.components.ProjectCard.vote": "Voto", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# comentario} other {# comentarios}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, no {# contribucións} one {# contribución} other {# contribuciones}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {no hay ideas aún} one {idea #} other {ideas de #}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# initiatives} una {# initiative} otras {# initiatives}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, no {# temas} one {# tema} other {# temas}}\n", "app.components.ProjectCard.xOptions": "{ideasCount, plural, no {# opciones} one {# opción} other {# opciones}}\n", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# peticiones} one {# petición} other {# peticiones}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, no {# proyecto} one {# proyecto} other {# proyectos}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# propuestas} one {# propuesta} other {# propuestas}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, no {# preguntas} one {# pregunta} other {#  preguntas}}\n", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# comments} un {# comments} otro {# comments}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# inputs} uno {# input} otro {# inputs}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# proyectos} one {# proyecto} other {# proyectos}}", "app.components.ProjectFolderCards.components.Topbar.all": "Todos", "app.components.ProjectFolderCards.components.Topbar.archived": "Archivado", "app.components.ProjectFolderCards.components.Topbar.draft": "<PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filtrar por", "app.components.ProjectFolderCards.components.Topbar.published2": "Publicado", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Etiqueta", "app.components.ProjectFolderCards.noProjectYet": "Aún no hay proyecto", "app.components.ProjectFolderCards.noProjectsAvailable": "Ningún proyecto disponible", "app.components.ProjectFolderCards.showMore": "Mostrar más", "app.components.ProjectFolderCards.stayTuned": "<PERSON><PERSON><PERSON> atento, un proyecto aparecerá muy pronto.", "app.components.ProjectFolderCards.tryChangingFilters": "Intente cambiar los filtros seleccionados", "app.components.ProjectTemplatePreview.alsoUsedIn": "También se utiliza en estas ciudades", "app.components.ProjectTemplatePreview.copied": "Copiado", "app.components.ProjectTemplatePreview.copyLink": "<PERSON><PERSON><PERSON> el enlace", "app.components.QuillEditor.alignCenter": "Centrar texto", "app.components.QuillEditor.alignLeft": "Alinear izquierda", "app.components.QuillEditor.alignRight": "Alinear derecha", "app.components.QuillEditor.bold": "Negrilla", "app.components.QuillEditor.clean": "Eliminar el formato", "app.components.QuillEditor.customLink": "Añade un botón", "app.components.QuillEditor.customLinkPrompt": "Entrar al enlace:", "app.components.QuillEditor.edit": "<PERSON><PERSON>", "app.components.QuillEditor.image": "Subir imagen", "app.components.QuillEditor.imageAltPlaceholder": "Breve descripción de la imagen", "app.components.QuillEditor.italic": "Cursiva", "app.components.QuillEditor.link": "Agregar enlace", "app.components.QuillEditor.linkPrompt": "Entrar al enlace:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Lista ordenada", "app.components.QuillEditor.remove": "Eliminar", "app.components.QuillEditor.save": "Guardar", "app.components.QuillEditor.subtitle": "<PERSON>t<PERSON><PERSON><PERSON>", "app.components.QuillEditor.title": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.unorderedList": "Lista desordenada", "app.components.QuillEditor.video": "<PERSON><PERSON><PERSON> v<PERSON>", "app.components.QuillEditor.videoPrompt": "Entrar en video:", "app.components.QuillEditor.visitPrompt": "Visita el enlace:", "app.components.ReactionControl.completeProfileToReact": "Completa tu perfil para reaccionar", "app.components.ReactionControl.dislike": "No me gusta", "app.components.ReactionControl.dislikingDisabledMaxReached": "Has alcanzado el número máximo de \"no me gustas\" en {projectName}", "app.components.ReactionControl.like": "Me gusta", "app.components.ReactionControl.likingDisabledMaxReached": "Has alcanzado el número máximo de me gustas en {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "La capacidad de reaccionar se activará cuando comience esta fase", "app.components.ReactionControl.reactingDisabledPhaseOver": "Ya no es posible reaccionar en esta fase", "app.components.ReactionControl.reactingDisabledProjectInactive": "Ya no puedes reaccionar a las ideas en {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Las reacciones no están activadas actualmente para este proyecto", "app.components.ReactionControl.reactingNotPermitted": "Las reacciones sólo están activadas para determinados grupos", "app.components.ReactionControl.reactingNotSignedIn": "Inicia sesión para reaccionar.", "app.components.ReactionControl.reactingPossibleLater": "La posibilidad de reaccionar comenzará en {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Verifica tu identidad para poder reaccionar.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "<PERSON>cha del evento: {startDate} en {startTime} a {endDate} en {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Fecha del evento: {eventDate} de {startTime} a {endTime}.", "app.components.Sharing.linkCopied": "Link copied", "app.components.Sharing.or": "o", "app.components.Sharing.share": "<PERSON><PERSON><PERSON>", "app.components.Sharing.shareByEmail": "Compartir por correo", "app.components.Sharing.shareByLink": "<PERSON><PERSON><PERSON> el enlace", "app.components.Sharing.shareOnFacebook": "Compartir en Facebook", "app.components.Sharing.shareOnTwitter": "Compartir en Twitter", "app.components.Sharing.shareThisEvent": "Comparte este evento", "app.components.Sharing.shareThisFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Sharing.shareThisProject": "Compartir este proyecto", "app.components.Sharing.shareViaMessenger": "Compartir a través de Messenger", "app.components.Sharing.shareViaWhatsApp": "Compartir por WhatsApp", "app.components.SideModal.closeButtonAria": "<PERSON><PERSON><PERSON>", "app.components.StatusModule.futurePhase": "Estás viendo una fase que aún no ha comenzado. Podrás participar cuando comience la fase.", "app.components.StatusModule.modifyYourSubmission1": "Modifica tu respuesta", "app.components.StatusModule.submittedUntil3": "<PERSON><PERSON><PERSON> votar hasta", "app.components.TopicsPicker.numberOfSelectedTopics": "Seleccionado/s {numberOfSelectedTopics, plural, =0 {zero topics} one {one topic} other {# topics}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Ampliar imagen", "app.components.UI.MoreActionsMenu.moreOptions": "Más opciones", "app.components.UI.MoreActionsMenu.showMoreActions": "Mostrar más acciones", "app.components.UI.NewLabel.new": "NUEVO", "app.components.UI.PhaseFilter.noAppropriatePhases": "No se han encontrado fases adecuadas para este proyecto", "app.components.UI.RemoveImageButton.a11y_removeImage": "Eliminar", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "Traducir", "app.components.Unauthorized.additionalInformationRequired": "Se requiere información adicional para que puedas participar.", "app.components.Unauthorized.completeProfile": "<PERSON><PERSON>l completo", "app.components.Unauthorized.completeProfileTitle": "Completa tu perfil para participar", "app.components.Unauthorized.noPermission": "No tiene permiso para ver esta página", "app.components.Unauthorized.notAuthorized": "Lo sentimos. No está autorizado a acceder a esta página.", "app.components.Upload.errorImageMaxSizeExceeded": "La imagen que seleccionó es más grande que {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Una o más imágenes seleccionadas superen el tamaño máximo permitido {maxFileSize} Mb por imagen", "app.components.Upload.onlyOneImage": "<PERSON><PERSON>lo puede cargar 1 imagen", "app.components.Upload.onlyXImages": "Solo puedes subir {maxItemsCount} imágines", "app.components.Upload.remaining": "restante", "app.components.Upload.uploadImageLabel": "Selecciona una imagen (máx. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Sube una o más imágenes", "app.components.UpsellTooltip.tooltipContent": "Esta función no está incluida en tu plan actual. Habla con tu Gestor de Éxito Gubernamental o con el administrador para desbloquearla.", "app.components.UserName.anonymous": "<PERSON><PERSON><PERSON>", "app.components.UserName.anonymousTooltip2": "Este usuario ha decidido anonimizar su contribución", "app.components.UserName.authorWithNoNameTooltip": "Tu nombre se ha autogenerado porque no has introducido tu nombre. Actualiza tu perfil si deseas cambiarlo.", "app.components.UserName.deletedUser": "autor desconocido", "app.components.UserName.verified": "Verificado", "app.components.VerificationModal.verifyAuth0": "Verifica con NemID", "app.components.VerificationModal.verifyBOSA": "Verificar con su nombre o con su Ci", "app.components.VerificationModal.verifyBosaFas": "Verificar con 'itsme' o con su verificador de identidad electrónico", "app.components.VerificationModal.verifyClaveUnica": "Verificar con ClaveÚnica", "app.components.VerificationModal.verifyFakeSSO": "Verificar con SSO falso", "app.components.VerificationModal.verifyIdAustria": "Verifica con ID Austria", "app.components.VerificationModal.verifyKeycloak": "Verifica con ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Verificar con MitID", "app.components.VerificationModal.verifyTwoday2": "Verificar con BankID o Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Verifica tu identidad", "app.components.VoteControl.budgetingFutureEnabled": "<PERSON><PERSON><PERSON> asignar tu presupuesto a partir de {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "El presupuesto participativo solo está habilitado para ciertos grupos.", "app.components.VoteControl.budgetingNotPossible": "No es posible realizar cambios en tu presupuesto en este momento.", "app.components.VoteControl.budgetingNotVerified": "{VerifyAccountLink} para continuar.", "app.components.VoteInputs._shared.currencyLeft1": "Te queda {budgetLeft} / {totalBudget}", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Te quedan {votesLeft, plural, =0 {sin créditos} other {# sin {totalNumberOfVotes, plural, one {1 crédito} other {# créditos}} sin}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Te queda {votesLeft, plural, =0 {ningún punto} other {# de {totalNumberOfVotes, plural, one {1 punto} other {# puntos}} te queda}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Te quedan {votesLeft, plural, =0 {ninguna ficha} other {# de {totalNumberOfVotes, plural, one {1 ficha} other {# fichas}} restantes}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Te queda {votesLeft, plural, =0 {ningún voto} other {# de {totalNumberOfVotes, plural, one {1 voto} other {# votos}} te queda}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "<PERSON> has presentado tu presupuesto. Para modificarlo, haz clic en \"Modificar tu participación\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "<PERSON> has presentado tu presupuesto. Para modificarlo, vuelve a la página del proyecto y haz clic en \"Modificar tu participación\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "El ejercicio de asignación de presupuesto no está disponible, ya que esta fase no está activa.", "app.components.VoteInputs.single.youHaveVotedForX2": "Has votado por {votes, plural, =0 {# opciones} one {# opción} other {# opciones}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "Esto significa que perderás todos los datos asociados a esta entrada, como comentarios, reacciones y votos. Esta acción no se puede deshacer.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "¿Estás seguro de que deseas eliminar esta entrada?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Confirmar", "app.components.admin.SlugInput.resultingURL": "URL resultante", "app.components.admin.SlugInput.slugTooltip": "El slug es el conjunto único de palabras al final de la dirección web de la página, o URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "Si cambia la URL, los enlaces a la página con la antigua URL ya no funcionarán.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "Añadir una condición", "app.components.admin.UserFilterConditions.field_email": "Correo electrónico", "app.components.admin.UserFilterConditions.field_event_attendance": "Inscripciones a eventos", "app.components.admin.UserFilterConditions.field_follow": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_lives_in": "Vive en", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Encuesta del barómetro de satisfacción", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Interacción con una entrada con estado", "app.components.admin.UserFilterConditions.field_participated_in_project": "Participó en el proyecto", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Contribuyó al tema", "app.components.admin.UserFilterConditions.field_registration_completed_at": "<PERSON>cha de registro", "app.components.admin.UserFilterConditions.field_role": "Rol", "app.components.admin.UserFilterConditions.field_verified": "Verificación", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideación", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Propuestas", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "no está inscrito en ninguno de estos actos", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "no está inscrito en ningún acontecimiento", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "está inscrito en uno de estos eventos", "app.components.admin.UserFilterConditions.predicate_attends_something": "está inscrito en al menos un acontecimiento", "app.components.admin.UserFilterConditions.predicate_begins_with": "comienza con", "app.components.admin.UserFilterConditions.predicate_commented_in": "comentado", "app.components.admin.UserFilterConditions.predicate_contains": "contiene", "app.components.admin.UserFilterConditions.predicate_ends_on": "termina en", "app.components.admin.UserFilterConditions.predicate_has_value": "es igual a", "app.components.admin.UserFilterConditions.predicate_in": "realizado alguna acción", "app.components.admin.UserFilterConditions.predicate_is": "es", "app.components.admin.UserFilterConditions.predicate_is_admin": "es un administrador", "app.components.admin.UserFilterConditions.predicate_is_after": "es después de", "app.components.admin.UserFilterConditions.predicate_is_before": "es antes de", "app.components.admin.UserFilterConditions.predicate_is_checked": "es verificado", "app.components.admin.UserFilterConditions.predicate_is_empty": "está vacía", "app.components.admin.UserFilterConditions.predicate_is_equal": "es", "app.components.admin.UserFilterConditions.predicate_is_exactly": "es exactamente", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "es más grande que", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "es mayor o igual a", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "es un usuario normal", "app.components.admin.UserFilterConditions.predicate_is_not_area": "excluye la zona", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "excluye la carpeta", "app.components.admin.UserFilterConditions.predicate_is_not_input": "excluye aportes", "app.components.admin.UserFilterConditions.predicate_is_not_project": "excluye el proyecto", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "excluye el tema", "app.components.admin.UserFilterConditions.predicate_is_one_of": "es uno de los", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "una de las zonas", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "una de las carpetas", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "uno de los aportes", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "uno de los proyectos", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "uno de los temas", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "es un administrador de proyecto", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "es más pequeño que", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "es menor o igual a", "app.components.admin.UserFilterConditions.predicate_is_verified": "Verificado", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "no comienza con", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "no comentado", "app.components.admin.UserFilterConditions.predicate_not_contains": "no contiene", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "no termina en", "app.components.admin.UserFilterConditions.predicate_not_has_value": "no es igual a", "app.components.admin.UserFilterConditions.predicate_not_in": "no contribuyó", "app.components.admin.UserFilterConditions.predicate_not_is": "no es", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "no es un administrador", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "no se verifica", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "no está vacío", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "no es", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "no es un usuario normal", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "no es uno de los", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "no es un administrador de proyecto", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "no fue verificado", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "no publicó una entrada", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "no reaccionó al comentario", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "no reaccionó a los aportes", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "no te has inscrito en un evento", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "no ha realizado la encuesta", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "no se ofreció como voluntario", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "no participó en la votación", "app.components.admin.UserFilterConditions.predicate_nothing": "nada", "app.components.admin.UserFilterConditions.predicate_posted_input": "publicó una entrada", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reaccionó al comentario", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reaccionó al aporte", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "inscrito en un evento", "app.components.admin.UserFilterConditions.predicate_something": "algo", "app.components.admin.UserFilterConditions.predicate_taken_survey": "ha realizado la encuesta", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "se ofreció como voluntario", "app.components.admin.UserFilterConditions.predicate_voted_in3": "participaron en la votación", "app.components.admin.UserFilterConditions.rulesFormLabelField": "A", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "B", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "C", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "No recibirás notificaciones sobre tu contribución", "app.components.anonymousParticipationModal.cancel": "<PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.participateAnonymously": "Participa de manera anónima", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "Esto ocultará con seguridad <b>tu perfil</b> hacia administradores, moderadores y otros residentes. Nadie podrá vincular esta contribución o aporte específico a ti. Las contribuciones o aportes anónimos no pueden ser editados una vez enviados.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Hacer que nuestra plataforma sea segura para todos los usuarios es una prioridad para nosotros. Tus palabras importan, así que, por favor, sé amable con los demás.", "app.components.avatar.titleForAccessibility": "Perfil de {fullName}", "app.components.customFields.mapInput.removeAnswer": "Eliminar respuesta", "app.components.customFields.mapInput.undo": "<PERSON><PERSON><PERSON>", "app.components.customFields.mapInput.undoLastPoint": "<PERSON><PERSON><PERSON> el último punto", "app.components.followUnfollow.follow": "<PERSON><PERSON>", "app.components.followUnfollow.followADiscussion": "Sigue el debate", "app.components.followUnfollow.followTooltipInputPage2": "Seguir activa las actualizaciones por correo electrónico sobre cambios de estado, actualizaciones oficiales y comentarios. Puedes {unsubscribeLink} en cualquier momento.", "app.components.followUnfollow.followTooltipProjects2": "Seguir activa las actualizaciones por correo electrónico sobre los cambios del proyecto. Puedes {unsubscribeLink} en cualquier momento.", "app.components.followUnfollow.unFollow": "Deja de seguir a", "app.components.followUnfollow.unsubscribe": "cancelar la suscripción", "app.components.followUnfollow.unsubscribeUrl": "/perfil/editar", "app.components.form.ErrorDisplay.guidelinesLinkText": "nuestras directrices", "app.components.form.ErrorDisplay.next": "Siguient<PERSON>", "app.components.form.ErrorDisplay.previous": "Anterior", "app.components.form.ErrorDisplay.save": "Guardar", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Empieza a escribir para buscar por correo electrónico o nombre de usuario...", "app.components.form.anonymousSurveyMessage2": "Las respuestas a esta encuesta son anónimas.", "app.components.form.backToInputManager": "Volver al gestor de entradas", "app.components.form.backToProject": "Volver al proyecto", "app.components.form.components.controls.mapInput.removeAnswer": "Eliminar respuesta", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "<PERSON><PERSON><PERSON> el último punto", "app.components.form.controls.addressInputAriaLabel": "Introducción de la dirección", "app.components.form.controls.addressInputPlaceholder6": "Introduce una dirección...", "app.components.form.controls.adminFieldTooltip": "Campo solo visible para los administradores", "app.components.form.controls.allStatementsError": "Debes seleccionar una respuesta para todas las afirmaciones.", "app.components.form.controls.back": "Volver", "app.components.form.controls.clearAll": "<PERSON><PERSON><PERSON> todo", "app.components.form.controls.clearAllScreenreader": "Borra todas las respuestas de la pregunta matriz anterior", "app.components.form.controls.clickOnMapMultipleToAdd3": "<PERSON>z clic en el mapa para dibujarlo. Luego, arrastra sobre los puntos para moverlos.", "app.components.form.controls.clickOnMapToAddOrType": "Haz clic en el mapa o escribe una dirección abajo para añadir tu respuesta.", "app.components.form.controls.confirm": "Confirmar", "app.components.form.controls.cosponsorsPlaceholder": "Empieza a escribir un nombre a buscar", "app.components.form.controls.currentRank": "<PERSON><PERSON> actual:", "app.components.form.controls.minimumCoordinates2": "Se requiere un mínimo de {numPoints} puntos de mapa.", "app.components.form.controls.noRankSelected": "Sin rango seleccionado", "app.components.form.controls.notPublic1": "*Esta respuesta sólo se compartirá con los gestores del proyecto, y no con el público.", "app.components.form.controls.optionalParentheses": "(opcional)", "app.components.form.controls.rankingInstructions": "Arrastra y suelta para clasificar las opciones.", "app.components.form.controls.selectAsManyAsYouLike": "*Selecciona tantos como quieras", "app.components.form.controls.selectBetween": "*Selecciona entre las opciones {minItems} y {maxItems}", "app.components.form.controls.selectExactly2": "¡*¡Selecciona exactamente {selectExactly, plural, one {# opción} other {# opciones}}", "app.components.form.controls.selectMany": "*<PERSON>ja tantos como quiera", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Toca en el mapa para dibujar. Luego, arrastra sobre los puntos para moverlos.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Toca en el mapa para dibujar.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Toca en el mapa para añadir tu respuesta.", "app.components.form.controls.tapOnMapToAddOrType": "Toca en el mapa o escribe una dirección abajo para añadir tu respuesta.", "app.components.form.controls.tapToAddALine": "Toca para añadir una línea", "app.components.form.controls.tapToAddAPoint": "Toca para añadir un punto", "app.components.form.controls.tapToAddAnArea": "Toca para añadir un área", "app.components.form.controls.uploadShapefileInstructions": "* Sube un archivo zip que contenga uno o varios archivos shape.", "app.components.form.controls.validCordinatesTooltip2": "Si la ubicación no aparece entre las opciones mientras escribes, puedes añadir coordenadas válidas en el formato \"latitud, longitud\" para especificar una ubicación precisa (por ejemplo: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} de {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} fuera de {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} de {total}, donde {maxValue} es {maxLabel}", "app.components.form.error": "Error", "app.components.form.locationGoogleUnavailable": "No se ha podido cargar el campo de ubicación proporcionado por google maps.", "app.components.form.progressBarLabel": "Progreso de la encuesta", "app.components.form.submit": "Enviar", "app.components.form.submitApiError": "Ha habido un problema al enviar el formulario. Por favor, compruebe si hay algún error y vuelva a intentarlo.", "app.components.form.verifiedBlocked": "No puede editar este campo porque contiene información verificada", "app.components.formBuilder.Page": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.accessibilityStatement": "declaración de accesibilidad", "app.components.formBuilder.addAnswer": "<PERSON><PERSON><PERSON> respuest<PERSON>", "app.components.formBuilder.addStatement": "Añadir declarac<PERSON>", "app.components.formBuilder.agree": "De acuerdo", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "Si tienes acceso a nuestro paquete de IA, podrás resumir y categorizar respuestas de texto con IA", "app.components.formBuilder.askFollowUpToggleLabel": "Pregunta tras respuesta inicial", "app.components.formBuilder.bad": "Mal", "app.components.formBuilder.buttonLabel": "Etiqueta del botón", "app.components.formBuilder.buttonLink": "Enlace al botón", "app.components.formBuilder.cancelLeaveBuilderButtonText": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.category": "Categoría", "app.components.formBuilder.chooseMany": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.chooseOne": "<PERSON><PERSON><PERSON> una", "app.components.formBuilder.close": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.closed": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.configureMap": "Configurar mapa", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON><PERSON>, quiero irme", "app.components.formBuilder.content": "Contenido", "app.components.formBuilder.continuePageLabel": "Continúa a", "app.components.formBuilder.cosponsors": "Copatrocinadores", "app.components.formBuilder.default": "Por defecto", "app.components.formBuilder.defaultContent": "Contenido por defecto", "app.components.formBuilder.delete": "Eliminar", "app.components.formBuilder.deleteButtonLabel": "Eliminar", "app.components.formBuilder.description": "Descripción", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Esto ya se ha añadido en el formulario. El contenido por defecto solo puede utilizarse una vez.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Añadir contenido personalizado no forma parte de tu licencia actual. Ponte en contacto con tu gestor de GovSuccess o administrador para obtener más información al respecto.", "app.components.formBuilder.disagree": "No estoy de acuerdo", "app.components.formBuilder.displayAsDropdown": "Mostrar como desplegable", "app.components.formBuilder.displayAsDropdownTooltip": "Muestra las opciones en un desplegable. Si tienes muchas opciones, esto es lo recomendable.", "app.components.formBuilder.done": "<PERSON><PERSON>", "app.components.formBuilder.drawArea": "<PERSON>rea de dibujo", "app.components.formBuilder.drawRoute": "<PERSON><PERSON><PERSON> ruta", "app.components.formBuilder.dropPin": "Alfiler de caída", "app.components.formBuilder.editButtonLabel": "<PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "Proporciona al menos 1 respuesta. Ten en cuenta que cada respuesta debe tener un título.", "app.components.formBuilder.emptyOptionError": "Proporcionar al menos 1 respuesta", "app.components.formBuilder.emptyStatementError": "Proporciona al menos 1 declaración", "app.components.formBuilder.emptyTitleError": "Proporcionar un título a la pregunta", "app.components.formBuilder.emptyTitleMessage": "Pon un título a todas las respuestas", "app.components.formBuilder.emptyTitleStatementMessage": "Pon un título a todas las declaraciones", "app.components.formBuilder.enable": "Habilitar", "app.components.formBuilder.errorMessage": "Hay un problema, solucione el problema para poder guardar sus cambios", "app.components.formBuilder.fieldGroup.description": "Descripción (opcional)", "app.components.formBuilder.fieldGroup.title": "T<PERSON><PERSON><PERSON> (opcional)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Actualmente, las respuestas a estas preguntas solo están disponibles en el archivo excel exportado en Input Manager, y no están visibles para los usuarios.", "app.components.formBuilder.fieldLabel": "Opciones de respuesta", "app.components.formBuilder.fieldLabelStatement": "Declaraciones", "app.components.formBuilder.fileUpload": "Carga del archivo", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Página basada en mapas", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Incorpora el mapa como contexto o haz preguntas a los participantes basadas en la ubicación.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Para una experiencia de usuario óptima, no recomendamos añadir preguntas sobre puntos, rutas o áreas a las páginas basadas en mapas.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Página normal", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Las funciones de cartografía topográfica no están incluidas en tu licencia actual. Ponte en contacto con tu punto de contacto para obtener más información.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Tipo de página", "app.components.formBuilder.formEnd": "Fin del formulario", "app.components.formBuilder.formField.cancelDeleteButtonText": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "Sí, eliminar página", "app.components.formBuilder.formField.copyNoun": "Copiar", "app.components.formBuilder.formField.copyVerb": "Copiar", "app.components.formBuilder.formField.delete": "Bo<PERSON>r", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Al eliminar esta página también se eliminará la lógica asociada a ella. ¿Está seguro de que desea eliminarla?", "app.components.formBuilder.formField.deleteResultsInfo": "No se puede deshacer", "app.components.formBuilder.goToPageInputLabel": "La siguiente página es:", "app.components.formBuilder.good": "Bien", "app.components.formBuilder.helmetTitle": "C<PERSON>or de formularios", "app.components.formBuilder.imageFileUpload": "<PERSON><PERSON> imagen", "app.components.formBuilder.invalidLogicBadgeMessage": "Lógica inválida", "app.components.formBuilder.labels2": "Etiquetas (opcional)", "app.components.formBuilder.labelsTooltipContent2": "Elige etiquetas opcionales para cualquiera de los valores de la escala lineal.", "app.components.formBuilder.lastPage": "Final", "app.components.formBuilder.layout": "Diseño", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "¿Está seguro de que quiere salir?", "app.components.formBuilder.leaveBuilderText": "Tienes cambios sin guardar. Por favor, gu<PERSON><PERSON>los antes de irte. Si sales, perderás los cambios.", "app.components.formBuilder.limitAnswersTooltip": "Cuando está activado, los encuestados tienen que seleccionar el número especificado de respuestas para continuar.", "app.components.formBuilder.limitNumberAnswers": "Limitar el número de respuestas", "app.components.formBuilder.linePolygonMapWarning2": "El dibujo de líneas y polígonos puede no cumplir las normas de accesibilidad. Puedes encontrar más información en {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Escala lineal", "app.components.formBuilder.locationDescription": "Ubicación", "app.components.formBuilder.logic": "Lógica", "app.components.formBuilder.logicAnyOtherAnswer": "Cualquier otra respuesta", "app.components.formBuilder.logicConflicts.conflictingLogic": "Lógica contradictoria", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Esta página contiene preguntas que llevan a diferentes páginas. Si los participantes responden a varias preguntas, se mostrará la página más lejana. Asegúrate de que este comportamiento se ajusta a tu flujo previsto.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Esta página tiene aplicadas varias reglas lógicas: lógica de pregunta multiselección, lógica a nivel de página y lógica entre preguntas. Cuando estas condiciones se solapen, la lógica de pregunta tendrá prioridad sobre la lógica de página, y se mostrará la página más alejada. Revisa la lógica para asegurarte de que se alinea con tu flujo previsto.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Esta página contiene una pregunta de selección múltiple en la que las opciones llevan a diferentes páginas. Si los participantes seleccionan varias opciones, se mostrará la página más lejana. Asegúrate de que este comportamiento se ajusta a tu flujo previsto.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Esta página contiene una pregunta de selección múltiple en la que las opciones llevan a diferentes páginas y tiene preguntas que llevan a otras páginas. Se mostrará la página más lejana si estas condiciones se solapan. Asegúrate de que este comportamiento se ajusta a tu flujo previsto.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Esta página contiene una pregunta de selección múltiple en la que las opciones conducen a diferentes páginas y tiene una lógica establecida tanto a nivel de página como de pregunta. La lógica de la pregunta tendrá prioridad y se mostrará la página más lejana. Asegúrate de que este comportamiento se ajusta a tu flujo previsto.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Esta página tiene lógica tanto a nivel de página como a nivel de pregunta. La lógica de la pregunta tendrá prioridad sobre la lógica a nivel de página. Asegúrate de que este comportamiento se ajusta a tu flujo previsto.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Esta página tiene una lógica establecida tanto a nivel de página como de pregunta, y varias preguntas dirigen a páginas diferentes. La lógica de la pregunta tendrá prioridad y se mostrará la página más lejana. Asegúrate de que este comportamiento se ajusta a tu flujo previsto.", "app.components.formBuilder.logicNoAnswer2": "Sin respuesta", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Si hay otra respuesta", "app.components.formBuilder.logicPanelNoAnswer": "Si no contestas", "app.components.formBuilder.logicValidationError": "La lógica no puede enlazar con páginas anteriores", "app.components.formBuilder.longAnswer": "<PERSON><PERSON><PERSON>a larga", "app.components.formBuilder.mapConfiguration": "Configuración del mapa", "app.components.formBuilder.mapping": "Cartografía", "app.components.formBuilder.mappingNotInCurrentLicense": "Las funciones de cartografía topográfica no están incluidas en tu licencia actual. Ponte en contacto con tu punto de contacto para obtener más información.", "app.components.formBuilder.matrix": "<PERSON><PERSON>", "app.components.formBuilder.matrixSettings.columns": "Columnas", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "Opción múltiple", "app.components.formBuilder.multipleChoiceHelperText": "Si varias opciones llevan a páginas diferentes y los participantes seleccionan más de una, se mostrará la página más alejada. Asegúrate de que este comportamiento se ajusta a tu flujo previsto.", "app.components.formBuilder.multipleChoiceImage": "Elección de imagen", "app.components.formBuilder.multiselect.maximum": "Máximo", "app.components.formBuilder.multiselect.minimum": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.neutral": "Neutro", "app.components.formBuilder.newField": "Nuevo campo", "app.components.formBuilder.number": "Número", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Abrir", "app.components.formBuilder.optional": "Opcional", "app.components.formBuilder.other": "<PERSON><PERSON>", "app.components.formBuilder.otherOption": "\"Opción \"Otros", "app.components.formBuilder.otherOptionTooltip": "Permitir a los participantes introducir una respuesta personalizada si las respuestas proporcionadas no se ajustan a sus preferencias", "app.components.formBuilder.page": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.pageCannotBeDeleted": "Esta página no se puede borrar.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Esta página no se puede eliminar y no permite añadir ningún campo adicional.", "app.components.formBuilder.pageRuleLabel": "La página siguiente es:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Si no se añade lógica, el formulario seguirá su flujo normal. Si tanto la página como sus preguntas tienen lógica, la lógica de la pregunta tendrá prioridad. Para más información, visita {supportPageLink}", "app.components.formBuilder.preview": "Vista previa:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Los copatrocinadores no aparecen en el PDF descargado y no son compatibles con la importación a través de FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "Las preguntas de carga de archivos se muestran como no compatibles en el PDF descargado y no son compatibles con la importación a través de FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Las preguntas de mapeo se muestran en el PDF descargado, pero las capas no serán visibles. Las preguntas de mapeo no son compatibles con la importación a través de FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Las preguntas de matriz se muestran en el PDF descargado, pero actualmente no son compatibles con la importación a través de FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Los títulos y descripciones de las páginas se muestran como encabezado de sección en el PDF descargado.", "app.components.formBuilder.printSupportTooltip.ranking": "Las preguntas de clasificación aparecen en el PDF descargado, pero actualmente no son compatibles con la importación a través de FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Las etiquetas se muestran como no compatibles en el PDF descargado y no son compatibles con la importación a través de FormSync.", "app.components.formBuilder.proposedBudget": "Presupuesto propuesto", "app.components.formBuilder.question": "Pregunta", "app.components.formBuilder.questionCannotBeDeleted": "Esta pregunta no se puede eliminar.", "app.components.formBuilder.questionDescriptionOptional": "Descripción de la pregunta (opcional)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON><PERSON><PERSON> de <PERSON> pregunta", "app.components.formBuilder.randomize": "Aleatoriza", "app.components.formBuilder.randomizeToolTip": "El orden de las respuestas será aleatorio por usuario", "app.components.formBuilder.range": "Ra<PERSON>", "app.components.formBuilder.ranking": "Clasificación", "app.components.formBuilder.rating": "Valoración", "app.components.formBuilder.removeAnswer": "Eliminar respuesta", "app.components.formBuilder.required": "Requerido", "app.components.formBuilder.requiredToggleLabel": "Hacer obligatoria la respuesta a esta pregunta", "app.components.formBuilder.ruleForAnswerLabel": "Si la respuesta es:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "Si las respuestas incluyen:", "app.components.formBuilder.save": "Guardar", "app.components.formBuilder.selectRangeTooltip": "Elegir el valor máximo de su escala.", "app.components.formBuilder.sentiment": "Escala de sentimiento", "app.components.formBuilder.shapefileUpload": "Carga de archivos shapefile de Esri", "app.components.formBuilder.shortAnswer": "Respuesta corta", "app.components.formBuilder.showResponseToUsersToggleLabel": "Mostrar respuesta a los usuarios", "app.components.formBuilder.singleChoice": "Opción única", "app.components.formBuilder.staleDataErrorMessage2": "Se ha producido un problema. Este formulario se ha guardado recientemente en otro lugar. Esto puede deberse a que tú u otro usuario lo tenéis abierto para editarlo en otra ventana del navegador. Por favor, actualiza la página para obtener el formulario más reciente y vuelve a realizar los cambios.", "app.components.formBuilder.stronglyAgree": "Totalmente de acuerdo", "app.components.formBuilder.stronglyDisagree": "Totalmente en desacuerdo", "app.components.formBuilder.supportArticleLinkText": "esta página", "app.components.formBuilder.tags": "Etiquetas", "app.components.formBuilder.title": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.toLabel": "para", "app.components.formBuilder.unsavedChanges": "Tienes cambios sin guardar", "app.components.formBuilder.useCustomButton2": "Usar botón de página personalizado", "app.components.formBuilder.veryBad": "<PERSON><PERSON> mal", "app.components.formBuilder.veryGood": "Muy bien", "app.components.ideas.similarIdeas.engageHere": "Participa aquí", "app.components.ideas.similarIdeas.noSimilarSubmissions": "No se han encontrado aportes similares.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Encontramos aportes similares: ¡comprometerse con ellas puede ayudar a fortalecerlas!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Ya se han publicado aportes similares:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Buscando aportes similares ...", "app.components.phaseTimeLeft.xDayLeft": "Quedan {timeLeft, plural, =0 {menos de un día} one {# día} other {# días}}", "app.components.phaseTimeLeft.xWeeksLeft": "quedan {timeLeft} semanas", "app.components.screenReaderCurrency.AED": "Dirham de los Emiratos Árabes Unidos", "app.components.screenReaderCurrency.AFN": "Afgano Afgano", "app.components.screenReaderCurrency.ALL": "Lek albanés", "app.components.screenReaderCurrency.AMD": "<PERSON><PERSON> armenio", "app.components.screenReaderCurrency.ANG": "Florín <PERSON>o <PERSON>", "app.components.screenReaderCurrency.AOA": "Kwanza angol<PERSON>ño", "app.components.screenReaderCurrency.ARS": "Peso argentino", "app.components.screenReaderCurrency.AUD": "<PERSON><PERSON><PERSON> austra<PERSON>", "app.components.screenReaderCurrency.AWG": "Florín de Aruba", "app.components.screenReaderCurrency.AZN": "Manat azerbaiyano", "app.components.screenReaderCurrency.BAM": "Marca convertible Bosnia-Herzegovina", "app.components.screenReaderCurrency.BBD": "Dólar de Barbados", "app.components.screenReaderCurrency.BDT": "Taka bang<PERSON>í", "app.components.screenReaderCurrency.BGN": "<PERSON>", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON> bah<PERSON>", "app.components.screenReaderCurrency.BIF": "<PERSON>", "app.components.screenReaderCurrency.BMD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BND": "Dólar de Brunei", "app.components.screenReaderCurrency.BOB": "Boliviano", "app.components.screenReaderCurrency.BOV": "<PERSON>v<PERSON><PERSON> boli<PERSON>o", "app.components.screenReaderCurrency.BRL": "Real brasileño", "app.components.screenReaderCurrency.BSD": "<PERSON><PERSON><PERSON> b<PERSON>", "app.components.screenReaderCurrency.BTN": "Ngultrum <PERSON>", "app.components.screenReaderCurrency.BWP": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BZD": "Dólar de Belice", "app.components.screenReaderCurrency.CAD": "Dólar canadiense", "app.components.screenReaderCurrency.CDF": "Franco con<PERSON>ño", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "<PERSON>", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Unidad de Cuenta Chilena (UF)", "app.components.screenReaderCurrency.CLP": "Peso chileno", "app.components.screenReaderCurrency.CNY": "Yuan chino", "app.components.screenReaderCurrency.COP": "Peso colombiano", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Colón costarricense", "app.components.screenReaderCurrency.CRE": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CUC": "Peso cubano convertible", "app.components.screenReaderCurrency.CUP": "Peso cubano", "app.components.screenReaderCurrency.CVE": "Escudo caboverdiano", "app.components.screenReaderCurrency.CZK": "Corona checa", "app.components.screenReaderCurrency.DJF": "Franco <PERSON>", "app.components.screenReaderCurrency.DKK": "Corona danesa", "app.components.screenReaderCurrency.DOP": "Peso dominicano", "app.components.screenReaderCurrency.DZD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.EGP": "Libra egipcia", "app.components.screenReaderCurrency.ERN": "Nakfa eritreo", "app.components.screenReaderCurrency.ETB": "Birr etíope", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.FKP": "Libra de las Islas Malvinas", "app.components.screenReaderCurrency.GBP": "Libra esterlina", "app.components.screenReaderCurrency.GEL": "<PERSON><PERSON> geor<PERSON>", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Libra de Gibraltar", "app.components.screenReaderCurrency.GMD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GNF": "<PERSON> guineano", "app.components.screenReaderCurrency.GTQ": "Quetzal guatemalteco", "app.components.screenReaderCurrency.GYD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HKD": "Dólar de Hong Kong", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "<PERSON><PERSON> croata", "app.components.screenReaderCurrency.HTG": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HUF": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.IDR": "<PERSON><PERSON>ia indonesia", "app.components.screenReaderCurrency.ILS": "Nuevo shekel israelí", "app.components.screenReaderCurrency.INR": "Rupia india", "app.components.screenReaderCurrency.IQD": "<PERSON>ar iraqu<PERSON>", "app.components.screenReaderCurrency.IRR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.ISK": "Króna islandesa", "app.components.screenReaderCurrency.JMD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "<PERSON>n j<PERSON>", "app.components.screenReaderCurrency.KES": "<PERSON><PERSON><PERSON> k<PERSON>", "app.components.screenReaderCurrency.KGS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KHR": "<PERSON><PERSON> ca<PERSON>", "app.components.screenReaderCurrency.KMF": "<PERSON>", "app.components.screenReaderCurrency.KPW": "Won norcoreano", "app.components.screenReaderCurrency.KRW": "Won surcoreano", "app.components.screenReaderCurrency.KWD": "Dinar kuwai<PERSON>", "app.components.screenReaderCurrency.KYD": "Dólar de las Islas Caimán", "app.components.screenReaderCurrency.KZT": "<PERSON><PERSON> ka<PERSON>", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Libra libanesa", "app.components.screenReaderCurrency.LKR": "<PERSON><PERSON><PERSON> de Sri Lanka", "app.components.screenReaderCurrency.LRD": "Dólar liberiano", "app.components.screenReaderCurrency.LSL": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LTL": "Litas lituana", "app.components.screenReaderCurrency.LVL": "<PERSON>ts letón", "app.components.screenReaderCurrency.LYD": "<PERSON>ar libio", "app.components.screenReaderCurrency.MAD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MDL": "<PERSON><PERSON>o", "app.components.screenReaderCurrency.MGA": "<PERSON><PERSON> malgache", "app.components.screenReaderCurrency.MKD": "<PERSON><PERSON> maced<PERSON>o", "app.components.screenReaderCurrency.MMK": "Kyat de Myanmar", "app.components.screenReaderCurrency.MNT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mongol", "app.components.screenReaderCurrency.MOP": "Pataca Mac<PERSON>sa", "app.components.screenReaderCurrency.MRO": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MUR": "<PERSON><PERSON><PERSON> mauriciana", "app.components.screenReaderCurrency.MVR": "Rufiyaa maldivo", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MXN": "Peso mexicano", "app.components.screenReaderCurrency.MXV": "Unidad de Inversión Mexicana (UDI)", "app.components.screenReaderCurrency.MYR": "Ringgit malasio", "app.components.screenReaderCurrency.MZN": "Metical mozam<PERSON>", "app.components.screenReaderCurrency.NAD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NGN": "<PERSON><PERSON>", "app.components.screenReaderCurrency.NIO": "Córdoba nicaragüense", "app.components.screenReaderCurrency.NOK": "Corona <PERSON>", "app.components.screenReaderCurrency.NPR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NZD": "Dólar <PERSON>", "app.components.screenReaderCurrency.OMR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.PAB": "Balboa panameño", "app.components.screenReaderCurrency.PEN": "Sol peruano", "app.components.screenReaderCurrency.PGK": "<PERSON><PERSON>púa Nueva Guinea", "app.components.screenReaderCurrency.PHP": "Peso filipino", "app.components.screenReaderCurrency.PKR": "<PERSON><PERSON><PERSON> p<PERSON>", "app.components.screenReaderCurrency.PLN": "Złoty polaco", "app.components.screenReaderCurrency.PYG": "Guaraní para<PERSON>", "app.components.screenReaderCurrency.QAR": "Riyal qatarí", "app.components.screenReaderCurrency.RON": "<PERSON><PERSON>", "app.components.screenReaderCurrency.RSD": "<PERSON><PERSON> serbio", "app.components.screenReaderCurrency.RUB": "<PERSON><PERSON>lo ruso", "app.components.screenReaderCurrency.RWF": "<PERSON> ruand<PERSON>", "app.components.screenReaderCurrency.SAR": "Riyal saudí", "app.components.screenReaderCurrency.SBD": "Dólar de las Islas Salomón", "app.components.screenReaderCurrency.SCR": "<PERSON><PERSON><PERSON> se<PERSON>a", "app.components.screenReaderCurrency.SDG": "Libra sudanesa", "app.components.screenReaderCurrency.SEK": "Corona sueca", "app.components.screenReaderCurrency.SGD": "<PERSON><PERSON><PERSON> de Singapur", "app.components.screenReaderCurrency.SHP": "Libra Santa Elena", "app.components.screenReaderCurrency.SLL": "Sierra Leona", "app.components.screenReaderCurrency.SOS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SRD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SSP": "Libra sudanesa", "app.components.screenReaderCurrency.STD": "Santo <PERSON> y Príncipe <PERSON>", "app.components.screenReaderCurrency.SYP": "Libra siria", "app.components.screenReaderCurrency.SZL": "Suazi <PERSON>", "app.components.screenReaderCurrency.THB": "Baht tailandés", "app.components.screenReaderCurrency.TJS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TMT": "Manat turcomano", "app.components.screenReaderCurrency.TND": "<PERSON><PERSON>o", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Paʻanga tongano", "app.components.screenReaderCurrency.TRY": "Lira turca", "app.components.screenReaderCurrency.TTD": "Dólar de Trinidad y Tobago", "app.components.screenReaderCurrency.TWD": "Nuevo dólar de Taiwán", "app.components.screenReaderCurrency.TZS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.UAH": "Hryvnia ucraniana", "app.components.screenReaderCurrency.UGX": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.USD": "Dólar estadounidense", "app.components.screenReaderCurrency.USN": "Dólar estadounidense (día siguiente)", "app.components.screenReaderCurrency.USS": "<PERSON><PERSON><PERSON> estadounidense (mismo día)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Peso uruguayo", "app.components.screenReaderCurrency.UZS": "Uzbekist<PERSON>", "app.components.screenReaderCurrency.VEF": "Bolívar ve<PERSON>zo<PERSON>o", "app.components.screenReaderCurrency.VND": "<PERSON><PERSON>", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "<PERSON><PERSON> sa<PERSON>", "app.components.screenReaderCurrency.XAF": "Franco CFA centroafricano", "app.components.screenReaderCurrency.XAG": "Plata (una onza troy)", "app.components.screenReaderCurrency.XAU": "Oro (una onza troy)", "app.components.screenReaderCurrency.XBA": "Unidad compuesta europea (EURCO)", "app.components.screenReaderCurrency.XBB": "Unidad Monetaria Europea (U.M.E.-6)", "app.components.screenReaderCurrency.XBC": "Unidad de Cuenta Europea 9 (U.C.E.-9)", "app.components.screenReaderCurrency.XBD": "Unidad de Cuenta Europea 17 (U.C.E.-17)", "app.components.screenReaderCurrency.XCD": "Dólar del Caribe Oriental", "app.components.screenReaderCurrency.XDR": "Derechos Especiales de Giro", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "Franco CFA de África Occidental", "app.components.screenReaderCurrency.XPD": "Paladio (una onza troy)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON><PERSON> (una onza troy)", "app.components.screenReaderCurrency.XTS": "Códigos reservados específicamente para pruebas", "app.components.screenReaderCurrency.XXX": "<PERSON> moneda", "app.components.screenReaderCurrency.YER": "<PERSON><PERSON> ye<PERSON>", "app.components.screenReaderCurrency.ZAR": "Rand sudafricano", "app.components.screenReaderCurrency.ZMW": "<PERSON><PERSON>cha <PERSON>am<PERSON>", "app.components.screenReaderCurrency.amount": "Importe", "app.components.screenReaderCurrency.currency": "Moneda", "app.components.trendIndicator.lastQuarter2": "último trimestre", "app.containers.AccessibilityStatement.applicability": "Esta declaración de accesibilidad se aplica a un {demoPlatformLink} que es representativo de este sitio web: Utiliza el mismo código fuente y tiene la misma funcionalidad.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Método de evaluación", "app.containers.AccessibilityStatement.assesmentText2022": "La accesibilidad de este sitio fue evaluada por una entidad externa que no participó en el proceso de diseño y desarrollo. La conformidad del mencionado {demoPlatformLink} puede identificarse en este {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "puedes cambiar tus preferencias", "app.containers.AccessibilityStatement.changePreferencesText": "En cualquier momento, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Excepciones de conformidad", "app.containers.AccessibilityStatement.conformanceStatus": "Estado de conformidad", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Nos esforzamos por hacer que nuestro contenido sea inclusivo para todos. Sin embargo, en algunos casos puede haber contenido inaccesible en la plataforma como se indica a continuación:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "sitio web de demostración", "app.containers.AccessibilityStatement.email": "Correo electrónico:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Herramientas de encuesta integradas", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Las herramientas de encuesta que están disponibles para su uso en esta plataforma corresponden a software de terceros y pueden no estar accesibles.", "app.containers.AccessibilityStatement.exception_1": "Nuestras plataformas de compromiso digital facilitan el contenido generado por el usuario y publicado por particulares y organizaciones. Es posible que los usuarios de la plataforma suban a ella archivos PDF, imágenes u otros tipos de archivos, incluidos los multimedia, como archivos adjuntos o los añadan a los campos de texto. Es posible que no se pueda acceder plenamente a estos documentos.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Agradecemos sus comentarios sobre la accesibilidad de este sitio. Por favor, póngase en contacto con nosotros a través de uno de los siguientes canales:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Proceso de retroalimentación", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Bruselas, Bélgica", "app.containers.AccessibilityStatement.headTitle": "Declaración de Accesibilidad | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} se compromete a proporcionar una plataforma que sea accesible para todos los usuarios, independientemente de su tecnología o capacidad. En nuestros esfuerzos continuos por maximizar la accesibilidad y la facilidad de uso de nuestras plataformas para todos los usuarios, nos atenemos a las normas de accesibilidad vigentes.", "app.containers.AccessibilityStatement.mapping": "Mapas", "app.containers.AccessibilityStatement.mapping_1": "Los mapas de la plataforma cumplen parcialmente con las normas de accesibilidad. La extensión del mapa, el zoom y los widgets de la interfaz de usuario pueden controlarse mediante un teclado al visualizar los mapas. Los administradores también pueden configurar el estilo de las capas de los mapas en el back office, o mediante la integración con Esri, para crear paletas de colores y simbología más accesibles. Utilizar diferentes estilos de líneas o polígonos (por ejemplo, líneas discontinuas) también ayudará a diferenciar las capas de los mapas siempre que sea posible, y aunque ese estilo no puede configurarse dentro de nuestra plataforma en este momento, puede configurarse si se utilizan mapas con la integración Esri.", "app.containers.AccessibilityStatement.mapping_2": "Los mapas de la plataforma no son totalmente accesibles, ya que no presentan de forma audible los mapas base, las capas de los mapas ni las tendencias de los datos a los usuarios que utilizan lectores de pantalla. Los mapas totalmente accesibles tendrían que presentar de forma audible las capas del mapa y describir cualquier tendencia relevante en los datos. Además, el dibujo de mapas de líneas y polígonos en las encuestas no es accesible, ya que las formas no se pueden dibujar con el teclado. En este momento no se dispone de métodos de entrada alternativos debido a la complejidad técnica.", "app.containers.AccessibilityStatement.mapping_3": "Para que el dibujo de mapas lineales y poligonales sea más accesible, recomendamos incluir una introducción o explicación en la pregunta de la encuesta o en la descripción de la página sobre lo que muestra el mapa y cualquier tendencia relevante. Además, se podría incluir una pregunta de texto de respuesta corta o larga para que los encuestados puedan describir su respuesta en términos sencillos si es necesario (en lugar de hacer clic en el mapa). También recomendamos incluir información de contacto del director del proyecto para que los encuestados que no puedan rellenar una pregunta del mapa puedan solicitar un método alternativo para responder a la pregunta (por ejemplo, una reunión por vídeo).", "app.containers.AccessibilityStatement.mapping_4": "Para los proyectos de Ideación y propuestas, hay una opción para mostrar los aportes en una vista de mapa. No podemos garantizar la accessibilidad de esta vista. Sin embargo, para estos métodos existe una vista alternativa de lista de aportes, que sí es accesible.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Nuestros talleres en línea tienen un componente de transmisión de vídeo en directo, que actualmente no admite subtítulos.", "app.containers.AccessibilityStatement.pageDescription": "Declaración sobre la accesibilidad de este sitio web", "app.containers.AccessibilityStatement.postalAddress": "Dirección postal:", "app.containers.AccessibilityStatement.publicationDate": "Fecha de publicación", "app.containers.AccessibilityStatement.publicationDate2024": "Esta declaración de accesibilidad se publicó el 21 de agosto de 2024.", "app.containers.AccessibilityStatement.responsiveness": "Nuestro objetivo es responder a los comentarios dentro de 1 o 2 días hábiles.", "app.containers.AccessibilityStatement.statusPageText": "estado de la página", "app.containers.AccessibilityStatement.technologiesIntro": "La accesibilidad de este sitio depende de las siguientes tecnologías para funcionar:", "app.containers.AccessibilityStatement.technologiesTitle": "Tecnologías", "app.containers.AccessibilityStatement.title": "Declaración de accesibilidad", "app.containers.AccessibilityStatement.userGeneratedContent": "Contenido generado por el usuario", "app.containers.AccessibilityStatement.workshops": "Talleres", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Seleccionar proyecto", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Utilizar el Constructor de Contenidos te permitirá utilizar opciones de diseño más avanzadas. Para los idiomas en los que no haya contenido disponible en el generador de contenido, se mostrará en su lugar el contenido normal de la descripción del proyecto.", "app.containers.AdminPage.ProjectDescription.linkText": "Editar la descripción en el constructor de páginas", "app.containers.AdminPage.ProjectDescription.saveError": "Algo ha ido mal al guardar la descripción del proyecto.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Utilizar el constructor de contenido para la descripción", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "El uso del constructor de contenido le permitirá utilizar opciones de diseño más avanzadas.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "Vista del proyecto", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Fin de la encuesta", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Crear un grupo smart", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Los usuarios que cumplen todas las condiciones siguientes se añadirá automáticamente al Grupo:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Proporcionar al menos una regla", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Algunas condiciones están incompletas", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Guardar grupo", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Configurar grupos inteligentes no forma parte de tu licencia actual. Ponte en contacto con tu gestor de GovSuccess o administrador para obtener más información al respecto.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Proporcionar un nombre de grupo", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "La verificación está deshabilitada en su plataforma. Elimina la opción de verificación o póngase en contacto con el servicio de asistencia técnica.", "app.containers.App.appMetaDescription": "Bienvenido a la plataforma de participación en línea de {orgName}. \n¡Explora proyectos locales y participa en la discusión!", "app.containers.App.loading": "Cargando...", "app.containers.App.metaTitle1": "Plataforma de participación ciudadana | {orgName}", "app.containers.App.skipLinkText": "Saltar al contenido principal", "app.containers.AreaTerms.areaTerm": "Localidad/barrio/zona", "app.containers.AreaTerms.areasTerm": "Localidades/barrios/zonas", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Ya existe una cuenta con este correo electrónico. Puedes cerrar sesión, iniciar sesión con esta dirección de correo electrónico y verificar tu cuenta en la página de configuración.", "app.containers.Authentication.steps.AccessDenied.close": "<PERSON><PERSON><PERSON>", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "No cumples con los requisitos para participar en este proceso.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Volver a la verificación de inicio de sesión único", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Por favor, introduzca un token", "app.containers.Authentication.steps.Invitation.token": "Token", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "¿Ya tienes una cuenta? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Conectarse", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Emails en esta categoría", "app.containers.CampaignsConsentForm.messageError": "Hubo un error al guardar tus preferencias de correo electrónico.", "app.containers.CampaignsConsentForm.messageSuccess": "Tus preferencias de correo electrónico se han guardado.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "¿Qué tipo de notificaciones por correo electrónico quiere recibir?", "app.containers.CampaignsConsentForm.notificationsTitle": "Notificaciones", "app.containers.CampaignsConsentForm.submit": "Guardar", "app.containers.ChangeEmail.backToProfile": "Volver a la configuración del perfil", "app.containers.ChangeEmail.confirmationModalTitle": "Confirme su correo electrónico", "app.containers.ChangeEmail.emailEmptyError": "Indique una dirección de correo electrónico", "app.containers.ChangeEmail.emailInvalidError": "Proporcione una dirección de correo electrónico en el formato correcto, <NAME_EMAIL>", "app.containers.ChangeEmail.emailRequired": "Introduzca una dirección de correo electrónico.", "app.containers.ChangeEmail.emailTaken": "Este correo electrónico ya está en uso.", "app.containers.ChangeEmail.emailUpdateCancelled": "Se ha cancelado la actualización por correo electrónico.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Para actualizar su correo electrónico, reinicie el proceso.", "app.containers.ChangeEmail.helmetDescription": "Cambiar la página de correo electrónico", "app.containers.ChangeEmail.helmetTitle": "Cambia tu correo electrónico", "app.containers.ChangeEmail.newEmailLabel": "Nuevo correo electrónico", "app.containers.ChangeEmail.submitButton": "Enviar", "app.containers.ChangeEmail.titleAddEmail": "Añade tu correo electrónico", "app.containers.ChangeEmail.titleChangeEmail": "Cambia tu correo electrónico", "app.containers.ChangeEmail.updateSuccessful": "Su correo electrónico se ha actualizado correctamente.", "app.containers.ChangePassword.currentPasswordLabel": "Contraseña actual", "app.containers.ChangePassword.currentPasswordRequired": "Introduzca su contraseña actual", "app.containers.ChangePassword.goHome": "Ir a la página de inicio", "app.containers.ChangePassword.helmetDescription": "Cambiar la página de su contraseña", "app.containers.ChangePassword.helmetTitle": "Cambiar contraseña", "app.containers.ChangePassword.newPasswordLabel": "Nueva contraseña", "app.containers.ChangePassword.newPasswordRequired": "Introduzca su nueva contraseña", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Introduzca una contraseña de al menos {minimumPasswordLength} caracteres", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Su contraseña se ha actualizado correctamente", "app.containers.ChangePassword.passwordEmptyError": "Introduzca su contraseña", "app.containers.ChangePassword.passwordsDontMatch": "Confirmar la nueva contraseña", "app.containers.ChangePassword.titleAddPassword": "Añadir una contraseña", "app.containers.ChangePassword.titleChangePassword": "Cambiar la contraseña", "app.containers.Comments.a11y_commentDeleted": "Comentario eliminado", "app.containers.Comments.a11y_commentPosted": "Comentario publicado", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {no me gusta} one {1 me gusta} other {# me gustas}}", "app.containers.Comments.a11y_undoLike": "<PERSON><PERSON><PERSON> 'me gusta'", "app.containers.Comments.addCommentError": "Algo salió mal. Por favor Inténtalo más tarde.", "app.containers.Comments.adminCommentDeletionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.Comments.adminCommentDeletionConfirmButton": "Borrar este comentario", "app.containers.Comments.cancelCommentEdit": "<PERSON><PERSON><PERSON>", "app.containers.Comments.childCommentBodyPlaceholder": "Escribe una respuesta...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "Este comentario ha sido eliminado.", "app.containers.Comments.commentDeletionCancelButton": "Mantener el comentario", "app.containers.Comments.commentDeletionConfirmButton": "Borrar mi comentario", "app.containers.Comments.commentLike": "Me gusta", "app.containers.Comments.commentReplyButton": "Respuesta", "app.containers.Comments.commentsSortTitle": "Ordena los comentarios por", "app.containers.Comments.completeProfileLinkText": "completa tu perfil", "app.containers.Comments.completeProfileToComment": "Por favor, {completeRegistrationLink} para comentar.", "app.containers.Comments.confirmCommentDeletion": "¿Está seguro que desea eliminar este comentario? ¡No hay marcha atrás!", "app.containers.Comments.deleteComment": "Eliminar", "app.containers.Comments.deleteReasonDescriptionError": "Proporcione más información sobre su motivo", "app.containers.Comments.deleteReasonError": "Indique el motivo", "app.containers.Comments.deleteReason_inappropriate": "Es inapropiado o ofensivo", "app.containers.Comments.deleteReason_irrelevant": "Esto no corresponde aquí", "app.containers.Comments.deleteReason_other": "Otra razón", "app.containers.Comments.editComment": "<PERSON><PERSON>", "app.containers.Comments.guidelinesLinkText": "nuestras directrices", "app.containers.Comments.ideaCommentBodyPlaceholder": "Escribe tu comentario aquí", "app.containers.Comments.internalCommentingNudgeMessage": "Hacer comentarios internos no está incluido en tu licencia actual. Ponte en contacto con tu gestor de GovSuccess o administrador para obtener más información al respecto.", "app.containers.Comments.internalConversation": "Conversación interna", "app.containers.Comments.loadMoreComments": "Cargar más comentarios", "app.containers.Comments.loadingComments": "Cargar más comentarios...", "app.containers.Comments.loadingMoreComments": "Cargar más comentarios...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Este comentario no es visible para los usuarios regulares", "app.containers.Comments.postInternalComment": "Postear comentario de manera interna", "app.containers.Comments.postPublicComment": "Postear comentario de manera pública", "app.containers.Comments.profanityError": "Es posible que haya utilizado una o más palabras consideradas groserías por {guidelinesLink}. Por favor, modifique su texto para eliminar cualquier insulto que pueda estar presente.", "app.containers.Comments.publicDiscussion": "Debate público", "app.containers.Comments.publishComment": "Publica tu comentario", "app.containers.Comments.reportAsSpamModalTitle": "¿Por qué quieres reportar esto como spam?", "app.containers.Comments.saveComment": "Guardar", "app.containers.Comments.signInLinkText": "inicia sesión", "app.containers.Comments.signInToComment": "Por favor {signInLink} para comentar.", "app.containers.Comments.signUpLinkText": "inscríbete", "app.containers.Comments.verifyIdentityLinkText": "Verifica tu identidad", "app.containers.Comments.visibleToUsersPlaceholder": "Este comentario es visible para los usuarios regulares", "app.containers.Comments.visibleToUsersWarning": "Los comentarios publicados aquí serán visibles para los usuarios habituales.", "app.containers.ContentBuilder.PageTitle": "Descripción del proyecto", "app.containers.CookiePolicy.advertisingContent": "En {orgName}, no utilizamos herramientas de publicidad en las plataformas de participación.", "app.containers.CookiePolicy.advertisingTitle": "Publicidad", "app.containers.CookiePolicy.analyticsContents": "Las cookies analíticas rastrean el comportamiento de los visitantes, por ejemplo, qué páginas se visitan y durante cuánto tiempo. También pueden recoger algunos datos técnicos, como la información del navegador, la ubicación aproximada y las direcciones IP. Solo utilizamos estos datos de manera interna para seguir mejorando la experiencia general del usuario y el funcionamiento de la plataforma. Estos datos también pueden ser compartidos entre Go Vocal y {orgName} para evaluar y mejorar el compromiso con los proyectos en la plataforma. Tenga en cuenta que los datos son anónimos y se utilizan a nivel agregado, es decir no lo identifican personalmente. Sin embargo, es posible que si estos datos se combinan con otras fuentes de datos, dicha identificación tenga lugar.", "app.containers.CookiePolicy.analyticsTitle": "Analytics", "app.containers.CookiePolicy.cookiePolicyDescription": "Una explicación detallada de cómo usamos cookies en esta plataforma", "app.containers.CookiePolicy.cookiePolicyTitle": "Política de cookies", "app.containers.CookiePolicy.essentialContent": "Algunas cookies son esenciales para garantizar el buen funcionamiento de esta plataforma. Estas cookies esenciales se utilizan principalmente para autenticar su cuenta cuando visita la plataforma y para guardar su preferencia de idioma.", "app.containers.CookiePolicy.essentialTitle": "Cookies esenciales", "app.containers.CookiePolicy.externalContent": "Algunas de nuestras páginas pueden mostrar contenidos de proveedores externos, por ejemplo, YouTube o Typeform. No tenemos control sobre estas cookies de terceros y la visualización del contenido de estos proveedores externos también puede dar lugar a la instalación de cookies en su dispositivo.", "app.containers.CookiePolicy.externalTitle": "Cookies externos", "app.containers.CookiePolicy.functionalContents": "Pueden habilitarse cookies funcionales para que los visitantes reciban notificaciones sobre actualizaciones y accedan a los canales de asistencia directamente desde la plataforma.", "app.containers.CookiePolicy.functionalTitle": "Funcional", "app.containers.CookiePolicy.headCookiePolicyTitle": "Política de Cookies | {orgName}", "app.containers.CookiePolicy.intro": "Como la mayoría de sitios web, utilizamos cookies para optimizar tu experiencia y la de otros visitantes en esta plataforma. Queremos ser totalmente transparentes sobre el por qué y cómo de este uso de la cookie, por eso encontrarás todos los detalles de cómo las usamos, tan claro como por escrito puede quedar. Nunca se utilizan cookies para identificar y rastrear usuarios concretos en nuestra plataforma. Las cockies \"no saben quien eres\". Es importante destacar, que el seguimiento de datos técnicos, como información de navegador, ubicación aproximada y una dirección IP, aunque no se utilizan de tal manera, en combinación con otras fuentes de datos, podrían conducir a una identificación.", "app.containers.CookiePolicy.manageCookiesDescription": "Puede activar o desactivar las cookies analíticas, de marketing y funcionales en cualquier momento en sus preferencias de cookies. También puede eliminar manual o automáticamente las cookies existentes, utilizando su navegador de Internet. Sin embargo, las cookies pueden volver a colocarse tras su consentimiento en cualquier visita posterior a esta plataforma. Si no elimina las cookies, sus preferencias de cookies se almacenan durante 60 días, tras los cuales se le pedirá que renueve su consentimiento.", "app.containers.CookiePolicy.manageCookiesPreferences": "Vaya a su {manageCookiesPreferencesButtonText} para ver una lista completa de las integraciones de terceros utilizadas en esta plataforma y para gestionar sus preferencias.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "configuración de las cookies", "app.containers.CookiePolicy.manageCookiesTitle": "Gestión de sus cookes", "app.containers.CookiePolicy.viewPreferencesButtonText": "Configuración de las cookies", "app.containers.CookiePolicy.viewPreferencesText": "Las siguientes categorías de cookies pueden no aplicar a todos los visitantes o plataformas; consulte su {viewPreferencesButton} para ver una lista completa de las integraciones de terceros que aplican en su caso.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "¿Para qué utilizamos las cookies?", "app.containers.CustomPageShow.editPage": "<PERSON><PERSON>", "app.containers.CustomPageShow.goBack": "Volver", "app.containers.CustomPageShow.notFound": "Página no encontrada", "app.containers.DisabledAccount.bottomText": "<PERSON>uede iniciar sesi<PERSON> de nuevo desde {date}.", "app.containers.DisabledAccount.termsAndConditions": "términos y condiciones", "app.containers.DisabledAccount.text2": "Su cuenta en la plataforma de participación de {orgName} ha sido desactivada temporalmente por violación de las directrices comunitarias. Para más información al respecto, puede consultar la página {TermsAndConditions}.", "app.containers.DisabledAccount.title": "Su cuenta ha sido desactivada temporalmente", "app.containers.EventsShow.addToCalendar": "<PERSON>ñadir al calendario", "app.containers.EventsShow.editEvent": "Editar evento", "app.containers.EventsShow.emailSharingBody2": "Asiste a este acto: {eventTitle}. Más información en {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Fecha y hora del evento", "app.containers.EventsShow.eventFrom2": "<PERSON><PERSON> \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Volver", "app.containers.EventsShow.goToProject": "Ir al proyecto", "app.containers.EventsShow.haveRegistered": "se han inscrito", "app.containers.EventsShow.icsError": "Error al descargar el archivo ICS", "app.containers.EventsShow.linkToOnlineEvent": "Enlace al evento en línea", "app.containers.EventsShow.locationIconAltText": "Ubicación", "app.containers.EventsShow.metaTitle": "Evento: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Reunión en línea", "app.containers.EventsShow.onlineLinkIconAltText": "Enlace a la reunión en línea", "app.containers.EventsShow.registered": "registrado", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 inscritos} one {1 inscrito} other {# inscritos}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} inscritos", "app.containers.EventsShow.registrantsIconAltText": "Inscritos", "app.containers.EventsShow.socialMediaSharingMessage": "Asiste a este acto: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# Participante} other {# Participantes}}", "app.containers.EventsViewer.allTime": "Todas las fechas", "app.containers.EventsViewer.date": "<PERSON><PERSON>", "app.containers.EventsViewer.thisMonth2": "Próximo mes", "app.containers.EventsViewer.thisWeek2": "Próxima semana", "app.containers.EventsViewer.today": "Hoy", "app.containers.IdeaButton.addAContribution": "Enviar una propuesta", "app.containers.IdeaButton.addAPetition": "Añadir una petición", "app.containers.IdeaButton.addAProject": "Agregue un proyecto", "app.containers.IdeaButton.addAProposal": "<PERSON><PERSON>dir una propuesta", "app.containers.IdeaButton.addAQuestion": "<PERSON><PERSON><PERSON> una pregunta", "app.containers.IdeaButton.addAnInitiative": "Añade una iniciativa", "app.containers.IdeaButton.addAnOption": "Añadir una opción", "app.containers.IdeaButton.postingDisabled": "Por el momento no aceptar nuevas entradas", "app.containers.IdeaButton.postingInNonActivePhases": "Los nuevos entradas solo se pueden agregar en fases activas.", "app.containers.IdeaButton.postingInactive": "Por el momento no aceptar nuevas entradas", "app.containers.IdeaButton.postingLimitedMaxReached": "Ya ha completado esta encuesta. ¡Gracias por su respuesta!", "app.containers.IdeaButton.postingNoPermission": "Por el momento no aceptar nuevas entradas", "app.containers.IdeaButton.postingNotYetPossible": "Aún no se aceptan nuevas entradas aquí.", "app.containers.IdeaButton.signInLinkText": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.signUpLinkText": "inscríbete", "app.containers.IdeaButton.submitAnIssue": "Enviar una respuesta", "app.containers.IdeaButton.submitYourIdea": "Envía tu idea", "app.containers.IdeaButton.takeTheSurvey": "Responde la consulta", "app.containers.IdeaButton.verificationLinkText": "Verifica tu identidad.", "app.containers.IdeaCard.readMore": "<PERSON><PERSON>", "app.containers.IdeaCard.xComments": "{commentsCount, plural, one {ningún comentario} other {# comentarios}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {sin votos} one {1 voto} other {# votos}} fuera de {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Cerrar el panel de filtros", "app.containers.IdeaCards.a11y_totalItems": "Total de artículos: {ideasCount}", "app.containers.IdeaCards.all": "Todos", "app.containers.IdeaCards.allStatuses": "Todos los estados o roles", "app.containers.IdeaCards.contributions": "Propuestas", "app.containers.IdeaCards.ideaTerm": "Ideas", "app.containers.IdeaCards.initiatives": "Iniciativas", "app.containers.IdeaCards.issueTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.list": "Lista", "app.containers.IdeaCards.map": "Mapa", "app.containers.IdeaCards.mostDiscussed": "Lo más discutido", "app.containers.IdeaCards.newest": "Más reciente", "app.containers.IdeaCards.noFilteredResults": "No se han encontrado resultados. Prueba con un filtro o término de búsqueda diferente.", "app.containers.IdeaCards.numberResults": "Resultados ({postCount})", "app.containers.IdeaCards.oldest": "Más antigua", "app.containers.IdeaCards.optionTerm": "Opciones", "app.containers.IdeaCards.petitions": "Peticiones", "app.containers.IdeaCards.popular": "Más votados", "app.containers.IdeaCards.projectFilterTitle": "Proyectos", "app.containers.IdeaCards.projectTerm": "Proyectos", "app.containers.IdeaCards.proposals": "Propuestas", "app.containers.IdeaCards.questionTerm": "Preguntas", "app.containers.IdeaCards.random": "Aleat<PERSON>", "app.containers.IdeaCards.resetFilters": "Restablecer los filtros", "app.containers.IdeaCards.showXResults": "Mostrar {ideasCount, plural, no {# resultados} one {# resultado} other {# resultados}}\n", "app.containers.IdeaCards.sortTitle": "Ordenar", "app.containers.IdeaCards.statusTitle": "Estado", "app.containers.IdeaCards.statusesTitle": "Rol", "app.containers.IdeaCards.topics": "<PERSON><PERSON>", "app.containers.IdeaCards.topicsTitle": "<PERSON><PERSON>", "app.containers.IdeaCards.trending": "Tendencias", "app.containers.IdeaCards.tryDifferentFilters": "No se han encontrado resultados. Prueba con un filtro o término de búsqueda diferente.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} comentarios} one {{ideasCount} comentarios} other {{ideasCount} comentarios}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} contribuciones} one {{ideasCount} contribución} other {{ideasCount} contribuciones}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ideas} one {{ideasCount} idea} other {{ideasCount} ideas}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} iniciativas} one {{ideasCount} iniciativa} other {{ideasCount} iniciativas}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} opciones} one {{ideasCount} opción} other {{ideasCount} opciones}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} peticiones} one {{ideasCount} petición} other {{ideasCount} peticiones}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} proyectos} one {{ideasCount} proyecto} other {{ideasCount} proyectos}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} propuestas} one {{ideasCount} propuesta} other {{ideasCount} propuestas}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} preguntas} one {{ideasCount} pregunta} other {{ideasCount} preguntas}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, no {# resultados} one {# resultado} other {# resultados}}\n", "app.containers.IdeasEditPage.contributionFormTitle": "Editar propuesta", "app.containers.IdeasEditPage.editedPostSave": "Guardar", "app.containers.IdeasEditPage.fileUploadError": "No se pudieron cargar uno o más archivos. Comprueba el tamaño y el formato del archivo y vuelve a intentarlo.", "app.containers.IdeasEditPage.formTitle": "Editar una idea", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Edita tu publicación. Añade información nueva y cambia la anterior.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Editar {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Editar propuesta", "app.containers.IdeasEditPage.issueFormTitle": "<PERSON>ar problema", "app.containers.IdeasEditPage.optionFormTitle": "Editar opción", "app.containers.IdeasEditPage.petitionFormTitle": "<PERSON>ar <PERSON>", "app.containers.IdeasEditPage.projectFormTitle": "Editar este proyecto", "app.containers.IdeasEditPage.proposalFormTitle": "Editar propuesta", "app.containers.IdeasEditPage.questionFormTitle": "<PERSON><PERSON>", "app.containers.IdeasEditPage.save": "Guardar", "app.containers.IdeasEditPage.submitApiError": "Ha habido un problema al enviar el formulario. Por favor, compruebe si hay algún error y vuelva a intentarlo.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Todas las entradas publicadas", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Explora todas las contribuciones publicadas en la plataforma de participación de {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Men<PERSON>jes | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Aportes", "app.containers.IdeasIndexPage.loadMore": "Carga más...", "app.containers.IdeasIndexPage.loading": "Cargando...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Por defecto, tus aportes se asociarán a tu perfil, a menos que selecciones esta opción.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Publicar anónimamente", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Visibilidad del perfil", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Esta encuesta no está actualmente abierta a respuestas. Por favor, vuelve al proyecto para obtener más información.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Esta encuesta no está activa actualmente.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Volver al proyecto", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "<PERSON> has completado esta encuesta.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Encuesta enviada", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "<PERSON><PERSON><PERSON> por tu respuesta.", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "La descripción de tu aporte debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "El cuerpo de la idea debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "El título del aporte debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "El título de la contribución debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Selecciona al menos un copatrocinador", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "La descripción de la idea debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "La descripción de la idea debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Por favor proporcione una descripción", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "El título de la idea debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "El título de la idea debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "La descripción de la iniciativa debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "La descripción de la iniciativa debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "El título de la iniciativa debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "El título de la iniciativa debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "La descripción del asunto debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "La descripción del asunto debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "El título del asunto debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "El título de la respuesta debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_number_required": "Este campo es obligatorio, introduce un número válido", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "La descripción de la opción debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "La descripción de la opción debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "El título de la opción debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "El título de la opción debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Seleccione al menos una etiqueta", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "La descripción de la petición debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "La descripción de la petición debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "El título de la petición debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "El título de la petición debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "La descripción del proyecto debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "La descripción del proyecto debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "El título del proyecto debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "El título del proyecto debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "La descripción de la propuesta debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "La descripción de la propuesta debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "El título de la propuesta debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "El título de la propuesta debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Introduzca un número", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Introduzca un número", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "La descripción de la pregunta debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "La descripción de la pregunta debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "El título de la pregunta debe tener menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "El título de la pregunta debe tener más de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Proporciona un título", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "La descripción de tu aporte debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "La descripción del aporte debe tener al menos 30 caracteres", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "El título del aporte debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "El título del aporte debe tener al menos 10 caracteres", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "La descripción de la idea debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "La idea debe ser describida en al menos 30 caracteres de extensión", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Proporciona un título", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "El título de la idea debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "El título de la idea debe ser de al menos 10 caracteres de extensión", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Es posible que haya utilizado una o más palabras consideradas groserías por {guidelinesLink}. Modifique su texto para eliminar cualquier grosería que pudiera estar presente.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "La descripción de la iniciativa debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "La descripción debe tener al menos 30 caracteres", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "El título de la iniciativa debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "El título de la iniciativa debe tener al menos 10 caracteres", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "La descripción del asunto debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "La descripción del asunto debe tener al menos 30 caracteres", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "El título del asunto debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "El título del asunto debe tener al menos 10 caracteres", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "La descripción de la opción debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "La descripción de la opción debe tener al menos 30 caracteres", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "El título de la opción debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "El título de la opción debe tener al menos 10 caracteres", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "La descripción de la petición debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "La descripción de la petición debe tener al menos 30 caracteres", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "El título de la petición debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "El título de la petición debe tener al menos 10 caracteres", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "La descripción del proyecto debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "La descripción del proyecto debe tener al menos 30 caracteres", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "El título del proyecto debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "El título del proyecto debe tener al menos 10 caracteres", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "La descripción de la propuesta debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "La descripción de la propuesta debe tener al menos 30 caracteres", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "El título de la propuesta debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "El título de la propuesta debe tener al menos 10 caracteres", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Por favor proporcione una descripción", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "La descripción de la pregunta debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "La descripción de la pregunta debe tener al menos 30 caracteres", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "El título de la pregunta debe tener menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "El título de la pregunta debe tener al menos 10 caracteres", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON>, quiero irme", "app.containers.IdeasNewPage.contributionMetaTitle1": "Añadir nueva contribución al proyecto | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Editar encuesta", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Publica un envío y únete a la conversación en la plataforma de participación de {orgName}.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Añadir una nueva idea al proyecto | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Añade una nueva iniciativa al proyecto | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Añadir nueva incidencia al proyecto | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "¿Está seguro de que quiere salir?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "El borrador de tus respuestas se ha guardado en privado y puedes volver a completarlo más tarde.", "app.containers.IdeasNewPage.leaveSurvey": "Deja la encuesta", "app.containers.IdeasNewPage.leaveSurveyText": "Sus respuestas no se guardarán.", "app.containers.IdeasNewPage.optionMetaTitle1": "Añadir nueva opción al proyecto | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Añadir nueva petición al proyecto | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Añadir nuevo proyecto al proyecto | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Añadir nueva propuesta al proyecto | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Añadir una nueva pregunta al proyecto | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Acepta la invitación", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Invitación al copatrocinio", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Copatrocinadores", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Se te ha invitado a convertirte en copatrocinador.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Invitación aceptada", "app.containers.IdeasShow.Cosponsorship.pending": "pendiente", "app.containers.IdeasShow.MetaInformation.attachments": "Archivos adjuntos", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "Por {userName} en {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Estado actual", "app.containers.IdeasShow.MetaInformation.location": "Ubicación", "app.containers.IdeasShow.MetaInformation.postedBy": "Publicado por", "app.containers.IdeasShow.MetaInformation.similar": "Aportes similares", "app.containers.IdeasShow.MetaInformation.topics": "<PERSON><PERSON>", "app.containers.IdeasShow.commentCTA": "Añade un comentario", "app.containers.IdeasShow.contributionEmailSharingBody": "Apoya esta propuesta '{postTitle}' en {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Apoya este aporte: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "¡G<PERSON><PERSON> por enviar tu propuesta!", "app.containers.IdeasShow.contributionTwitterMessage": "Apoya esta propuesta: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Apoya esta propuesta: {postTitle}", "app.containers.IdeasShow.currentStatus": "Estado actual", "app.containers.IdeasShow.deletedUser": "autor desconocido", "app.containers.IdeasShow.ideaEmailSharingBody": "¡Apoya mi idea '{ideaTitle}' en {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Vota por mi idea: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Apoya esta idea: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Apoya esta idea: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Apoya este tema: {postTitle}", "app.containers.IdeasShow.imported": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.importedTooltip": "Este {inputTerm} se recogió fuera de línea y se subió automáticamente a la plataforma.", "app.containers.IdeasShow.initiativeEmailSharingBody": "¡Apoya esta iniciativa '{ideaTitle}' en {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Apoya esta iniciativa: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "¡<PERSON><PERSON><PERSON> por presentar tu iniciativa!", "app.containers.IdeasShow.initiativeTwitterMessage": "Apoya esta iniciativa: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Apoya esta iniciativa: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "¡Apoye este tema  '{postTitle}' en {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Apoya este tema: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "<PERSON><PERSON><PERSON> por enviar tu problema.", "app.containers.IdeasShow.issueTwitterMessage": "Apoya este problema: {postTitle}", "app.containers.IdeasShow.metaTitle": "Entrada: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "¡Apoye esta opción '{postTitle}' en {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Apoya esta opción: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "¡Tu opción se ha publicado correctamente!", "app.containers.IdeasShow.optionTwitterMessage": "Apoya esta opción: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Apoya esta opción: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "¡Apoya esta petición '{ideaTitle}' en {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Apoya esta petición: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "¡<PERSON><PERSON><PERSON> por presentar tu petición!", "app.containers.IdeasShow.petitionTwitterMessage": "Apoya esta petición: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Apoya esta petición: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "¡Apoya este proyecto '{postTitle}' en {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Apoya este proyecto: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "¡<PERSON><PERSON><PERSON> por enviar tu proyecto!", "app.containers.IdeasShow.projectTwitterMessage": "Apoya este proyecto: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Apoya este proyecto: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "¡Apoya esta propuesta '{ideaTitle}' en {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Apoya esta propuesta: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "¡G<PERSON><PERSON> por enviar tu propuesta!", "app.containers.IdeasShow.proposalTwitterMessage": "Apoya esta propuesta: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Apoya esta propuesta: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Queda tiempo para votar:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} de {votingThreshold} votos necesarios", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Cancelar el voto", "app.containers.IdeasShow.proposals.VoteControl.days": "días", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "nuestras directrices", "app.containers.IdeasShow.proposals.VoteControl.hours": "horas", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Estado y votos", "app.containers.IdeasShow.proposals.VoteControl.minutes": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Más información", "app.containers.IdeasShow.proposals.VoteControl.vote": "Voto", "app.containers.IdeasShow.proposals.VoteControl.voted": "Votado", "app.containers.IdeasShow.proposals.VoteControl.votedText": "Se le notificará cuando esta propuesta pase al siguiente paso. {x, plural, =0 {There's {xDays} left.} one {There's {xDays} left.} other {There are {xDays} left.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Tu voto ha sido enviado!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Lamentablemente, no puedes votar sobre esta propuesta. <PERSON> por qué en {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural,=0 {menos de un día} one {un día} other {# días}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural,=0 {ningún voto} one {un voto} other {# votos}}", "app.containers.IdeasShow.questionEmailSharingBody": "¡Únete al debate sobre esta pregunta '{postTitle}' en {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Únete a la discusión: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "¡Tú pregunta se publico de manera exitosa!", "app.containers.IdeasShow.questionTwitterMessage": "Únete a la discusión: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Únete a la discusión: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "¿Por qué quiere reportar esto como spam?", "app.containers.IdeasShow.share": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.sharingModalSubtitle": "Llegue a más personas y dese a conocer.", "app.containers.IdeasShow.sharingModalTitle": "¡<PERSON><PERSON><PERSON> por enviar tu idea!", "app.containers.Navbar.completeOnboarding": "Completa tu perfil", "app.containers.Navbar.completeProfile": "<PERSON><PERSON>l completo", "app.containers.Navbar.confirmEmail2": "Confirmar correo electr<PERSON><PERSON>", "app.containers.Navbar.unverified": "No verificado", "app.containers.Navbar.verified": "Verificado", "app.containers.NewAuthModal.beforeYouFollow": "<PERSON><PERSON> de se<PERSON>ir", "app.containers.NewAuthModal.beforeYouParticipate": "Antes de participar", "app.containers.NewAuthModal.completeYourProfile": "Completa tu perfil", "app.containers.NewAuthModal.confirmYourEmail": "Confirme su correo electrónico", "app.containers.NewAuthModal.logIn": "In<PERSON><PERSON>", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Revise las condiciones siguientes para continuar.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Por favor, complete su perfil.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Volver a las opciones de inicio de sesión", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "¿No tiene una cuenta? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Inscríbase", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "El código debe tener 4 dígitos.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Continuar con FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "No hay métodos de autenticación habilitados en esta plataforma.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "Al continuar, estará aceptando recibir correos electrónicos de esta plataforma. Puede seleccionar los correos electrónicos que desea recibir en la página \"Mi configuración\".", "app.containers.NewAuthModal.steps.EmailSignUp.email": "Correo electrónico", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Proporcione una dirección de correo electrónico en el formato correcto, <NAME_EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Proporcione una dirección de correo electrónico", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Introduzca su dirección de correo electrónico para continuar.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "¿Ha olvidado su contraseña?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Inicie sesión en su cuenta:  {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Introduzca su contraseña", "app.containers.NewAuthModal.steps.Password.password": "Contraseña", "app.containers.NewAuthModal.steps.Password.rememberMe": "Recordarme", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "No seleccionar si se utiliza un ordenador público", "app.containers.NewAuthModal.steps.Success.allDone": "Todo hecho", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Ahora continúe su participación.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Tu identidad ha sido verificada. Ahora eres miembro de pleno derecho de la comunidad de esta plataforma.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Ya está verificado.", "app.containers.NewAuthModal.steps.close": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.whatAreYouInterestedIn": "¿Qué te interesa?", "app.containers.NewAuthModal.youCantParticipate": "No puedes participar", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {ninguna notificación no vista} one {1 notificación no vista} other {# notificaciones no vistas}}", "app.containers.NotificationMenu.adminRightsReceived": "<PERSON><PERSON> eres un administrador de la plataforma", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Tu comentario en '{postTitle}' ha sido eliminado por un administrador porque\n      {reasonCode, select, irrelevant {es irrelevante} inappropriate {su contenido es inapropiado} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} aceptó tu invitación de copatrocinio", "app.containers.NotificationMenu.deletedUser": "Autor desconocido", "app.containers.NotificationMenu.error": "No se pueden cargar las notificaciones", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "Los comentarios publicados aquí serán visibles para los usuarios regulares", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} comentó internamente en una entrada que comentaste internamente", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} comentó internamente en una entrada en un proyecto que gestionas", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} comentó internamente en una entrada no asignada en un proyecto no gestionado", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} comentó sobre tu comentario interno", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} te invita a copatrocinar una contribución", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} te invitó a copatrocinar una idea", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} te invitó a co-patrocinar una propuesta", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} te invitó a copatrocinar un comentario", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} te invitó a copatrocinar una opción", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} te invitó a copatrocinar una petición", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} te invitó a copatrocinar un proyecto", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} te invitó a co-patrocinar una propuesta", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} te invita a copatrocinar una pregunta", "app.containers.NotificationMenu.loadMore": "Carga más...", "app.containers.NotificationMenu.loading": "Cargando las notificaciones...", "app.containers.NotificationMenu.mentionInComment": "{name} te mencionó en un comentario", "app.containers.NotificationMenu.mentionInInternalComment": "{name} te mencionó en un comentario interno", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} te menciona en una actualización oficial", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "No has enviado tu encuesta", "app.containers.NotificationMenu.noNotifications": "No tienes notificaciones por ahora", "app.containers.NotificationMenu.notificationsLabel": "Notificaciones", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} dio una actualización oficial sobre una contribución que sigues", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} dio una actualización oficial sobre una idea que sigues", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} dio una actualización oficial sobre una propuesta que sigues", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} dio una actualización oficial sobre un tema que sigues", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} dio una actualización oficial sobre una opción que sigues", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} dio una actualización oficial sobre una petición que sigues", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} dio una actualización oficial sobre un proyecto que sigues", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} dio una actualización oficial sobre una propuesta que sigues", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} dio una actualización oficial sobre una cuestión que sigues", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} te fue asignado", "app.containers.NotificationMenu.projectModerationRightsReceived": "<PERSON><PERSON> eres un moderador de {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} entró a una nueva fase", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} entrará a una nueva fase en {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "Se ha publicado un nuevo proyecto", "app.containers.NotificationMenu.projectReviewRequest": "{name} solicitó la aprobación para publicar el proyecto \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} ha aprobado \"{projectTitle}\" para su publicación", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "El estado de {ideaTitle} ha cambiado a {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} alcanzó el número requerido de votos", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} aceptó tu invitación", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} has comentado una aportación que sigues", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} comentas una idea que sigues", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} comentaste una iniciativa que sigues", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} comentaste un tema que sigues", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} comentado una opción que sigues", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} ha comentado en una petición que sigues", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} has comentado un proyecto que sigues", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} comentó en una propuesta que sigues", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} comentó una pregunta que sigues", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} denunciado \"{postTitle}\" como spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} reaccionó ante tu comentario", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} reportó un comentario en '{postTitle}' como spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "No has enviado tus votos", "app.containers.NotificationMenu.votingBasketSubmitted": "Has votado correctamente", "app.containers.NotificationMenu.votingLastChance": "Última oportunidad para votar a {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} resultados de la votación revelados", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} fue asignada/o {postTitle} a ti", "app.containers.PasswordRecovery.emailError": "Esto no parece un correo electrónico válido", "app.containers.PasswordRecovery.emailLabel": "Correo electrónico", "app.containers.PasswordRecovery.emailPlaceholder": "Mi dirección de correo", "app.containers.PasswordRecovery.helmetDescription": "Restablecer tu contraseña", "app.containers.PasswordRecovery.helmetTitle": "Restablece tu contraseña", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Si esta dirección de correo electrónico está registrada en la plataforma, se ha enviado un enlace para restablecer la contraseña.", "app.containers.PasswordRecovery.resetPassword": "Recibir un enlace", "app.containers.PasswordRecovery.submitError": "No pudimos encontrar una cuenta vinculada a este correo electrónico. Puede intentar registrarte.", "app.containers.PasswordRecovery.subtitle": "¿Dónde se puede enviar un enlace para elegir una nueva contraseña?", "app.containers.PasswordRecovery.title": "Restablecimiento de contraseña", "app.containers.PasswordReset.helmetDescription": "Restablece tu contraseña", "app.containers.PasswordReset.helmetTitle": "Restablece tu contraseña", "app.containers.PasswordReset.login": "In<PERSON><PERSON>", "app.containers.PasswordReset.passwordError": "Tu contraseña debe contener al menos 8 caracteres", "app.containers.PasswordReset.passwordLabel": "Contraseña", "app.containers.PasswordReset.passwordPlaceholder": "Nueva contraseña", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Su contraseña se ha actualizado correctamente.", "app.containers.PasswordReset.pleaseLogInMessage": "Inicie sesión con su nueva contraseña.", "app.containers.PasswordReset.requestNewPasswordReset": "Solicitar un nuevo restablecimiento de la contraseña", "app.containers.PasswordReset.submitError": "Algo salió mal. Por favor Inténtalo más tarde.", "app.containers.PasswordReset.title": "Restablece tu contraseña", "app.containers.PasswordReset.updatePassword": "Confirma tu nueva contraseña", "app.containers.ProjectFolderCards.allProjects": "Todos los proyectos", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} está trabajando actualmente en", "app.containers.ProjectFolderShowPage.editFolder": "<PERSON><PERSON> la <PERSON>a", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Información sobre este proyecto", "app.containers.ProjectFolderShowPage.metaTitle1": "Carpeta: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON>", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON>er menos", "app.containers.ProjectFolderShowPage.share": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.PollForm.document": "Documento", "app.containers.Projects.PollForm.formCompleted": "<PERSON><PERSON><PERSON>! Su respuesta ha sido recibida.", "app.containers.Projects.PollForm.maxOptions": "máx. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "<PERSON> has respondido a esta consulta ciudadana.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Esta consulta ciudadana solo puede realizarse cuando esta fase está activa.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Lamentable<PERSON>e, no tienes permisos para responder esta consulta ciudadana", "app.containers.Projects.PollForm.pollDisabledNotPossible": "No es posible responder ahora esta consulta ciudadana.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "La consulta ciudadana no está disponible, porque el proyecto ya no está activo.", "app.containers.Projects.PollForm.sendAnswer": "Enviar", "app.containers.Projects.a11y_phase": "Fase {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Resumen de las fases", "app.containers.Projects.a11y_titleInputs": "Todas las aportaciones enviadas a este proyecto", "app.containers.Projects.a11y_titleInputsPhase": "Todas las entradas enviadas a esta fase", "app.containers.Projects.accessRights": "Derechos de acceso", "app.containers.Projects.addedToBasket": "Añadido a la bolsa", "app.containers.Projects.allocateBudget": "<PERSON><PERSON>a tu presupuesto", "app.containers.Projects.archived": "Archivado", "app.containers.Projects.basketSubmitted": "Su selección se ha presentado", "app.containers.Projects.contributions": "Propuestas", "app.containers.Projects.createANewPhase": "<PERSON><PERSON><PERSON> una nueva fase", "app.containers.Projects.currentPhase": "Fase actual", "app.containers.Projects.document": "Documento", "app.containers.Projects.editProject": "Editar este proyecto", "app.containers.Projects.emailSharingBody": "¿Qué le parece esta iniciativa? ¡Vote sobre ella y comparta el debate en {initiativeUrl} para hacer oír su voz!", "app.containers.Projects.emailSharingSubject": "Apoye mi iniciativa: {initiativeTitle}.", "app.containers.Projects.endedOn": "<PERSON><PERSON><PERSON><PERSON> {date}", "app.containers.Projects.events": "Eventos", "app.containers.Projects.header": "Proyectos", "app.containers.Projects.ideas": "Ideas", "app.containers.Projects.information": "Información", "app.containers.Projects.initiatives": "Iniciativas", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Revisa el documento", "app.containers.Projects.invisibleTitlePhaseAbout": "Acerca de esta fase", "app.containers.Projects.invisibleTitlePoll": "Haz la consulta ciudadana", "app.containers.Projects.invisibleTitleSurvey": "Responde la consulta", "app.containers.Projects.issues": "<PERSON><PERSON><PERSON>", "app.containers.Projects.liveDataMessage": "Estás viendo datos en tiempo real. El recuento de participantes se actualiza continuamente para los administradores. Ten en cuenta que los usuarios normales ven los datos en caché, lo que puede dar lugar a ligeras diferencias en las cifras.", "app.containers.Projects.location": "Ubicación:", "app.containers.Projects.manageBasket": "Gestionar la bolsa", "app.containers.Projects.meetMinBudgetRequirement": "Cumpla con el presupuesto mínimo para presentar su bolsa.", "app.containers.Projects.meetMinSelectionRequirement": "Cumpla con la selección requerida para enviar su bolsa.", "app.containers.Projects.metaTitle1": "Proyecto: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Presupuesto mínimo requer<PERSON>", "app.containers.Projects.myBasket": "Cesta", "app.containers.Projects.navPoll": "Consulta ciudadana", "app.containers.Projects.navSurvey": "Consulta", "app.containers.Projects.newPhase": "Nueva fase", "app.containers.Projects.nextPhase": "Siguiente fase", "app.containers.Projects.noEndDate": "Sin fecha de finalización", "app.containers.Projects.noItems": "Todavía no has seleccionado ningún artículo", "app.containers.Projects.noPastEvents": "No eventos anteriores que mostrar", "app.containers.Projects.noPhaseSelected": "Ninguna fase seleccionada", "app.containers.Projects.noUpcomingOrOngoingEvents": "Actualmente no hay eventos próximos o en curso.", "app.containers.Projects.offlineVotersTooltip": "Esta cifra no refleja ningún recuento de votantes offline.", "app.containers.Projects.options": "Opciones", "app.containers.Projects.participants": "Participantes", "app.containers.Projects.participantsTooltip4": "Esta cifra también refleja los envíos anónimos de encuestas. Los envíos anónimos de encuestas son posibles si las encuestas están abiertas a todo el mundo (consulta la pestaña {accessRightsLink} de este proyecto).", "app.containers.Projects.pastEvents": "Eventos anteriores", "app.containers.Projects.petitions": "Peticiones", "app.containers.Projects.phases": "Fases", "app.containers.Projects.previousPhase": "Fase previa", "app.containers.Projects.project": "Proyecto", "app.containers.Projects.projectTwitterMessage": "¡Haz que se escuche tu voz! Participa en {projectName} | {orgName}", "app.containers.Projects.projects": "Proyectos", "app.containers.Projects.proposals": "Propuestas", "app.containers.Projects.questions": "Preguntas", "app.containers.Projects.readLess": "<PERSON><PERSON> menos", "app.containers.Projects.readMore": "<PERSON><PERSON>", "app.containers.Projects.removeItem": "Eliminar elemento", "app.containers.Projects.requiredSelection": "Selección requerida", "app.containers.Projects.reviewDocument": "Revisa el documento", "app.containers.Projects.seeTheContributions": "Ver las propuestas", "app.containers.Projects.seeTheIdeas": "Ve las ideas", "app.containers.Projects.seeTheInitiatives": "Ver las iniciativas", "app.containers.Projects.seeTheIssues": "Ver los temas", "app.containers.Projects.seeTheOptions": "Ver las opciones", "app.containers.Projects.seeThePetitions": "Ver las peticiones", "app.containers.Projects.seeTheProjects": "Ver los proyectos", "app.containers.Projects.seeTheProposals": "Ver las propuestas", "app.containers.Projects.seeTheQuestions": "Ver las preguntas", "app.containers.Projects.seeUpcomingEvents": "Ver próximos eventos", "app.containers.Projects.share": "Compartir", "app.containers.Projects.shareThisProject": "Compartir este proyecto", "app.containers.Projects.submitMyBasket": "<PERSON><PERSON><PERSON> bolsa", "app.containers.Projects.survey": "Encuesta", "app.containers.Projects.takeThePoll": "Haz la consulta ciudadana", "app.containers.Projects.takeTheSurvey": "Responde la consulta", "app.containers.Projects.timeline": "Línea de tiempo", "app.containers.Projects.upcomingAndOngoingEvents": "Eventos en curso y próximos eventos", "app.containers.Projects.upcomingEvents": "Próximos eventos", "app.containers.Projects.whatsAppMessage": "{projectName} | de la plataforma de participación de {orgName}", "app.containers.Projects.yourBudget": "Presupuesto total", "app.containers.ProjectsIndexPage.metaDescription": "Explora todos los proyectos en curso de {orgName} para entender cómo puedes participar.\nÚnete a la conversación sobre los proyectos que más te interesen.", "app.containers.ProjectsIndexPage.metaTitle1": "{orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Proyectos", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "<PERSON><PERSON><PERSON> partici<PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Por favor, {signInLink} o {signUpLink} primero para poder participar en esta actividad", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "La participación en esta actividad se encuentra cerrada actualmente.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "inicia sesión", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "inscríbete", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Dejar de participar", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {ningún participante} one {# participante} other {# participantes}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Advertencia: La encuesta incrustada puede tener problemas de accesibilidad para los usuarios de lectores de pantalla. Si tienes algún problema, ponte en contacto con el administrador de la plataforma para recibir un enlace a la encuesta desde la plataforma original. Alternativamente, puedes solicitar otras formas de rellenar la encuesta.", "app.containers.ProjectsShowPage.process.survey.survey": "Consulta", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Para saber si puedes participar en esta encuesta, primero debes {logInLink} a la plataforma.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "La consulta puede solamente responderse cuando esta fase en la línea de tiempo este activa.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Por favor, {completeRegistrationLink} para realizar la encuesta.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Lamentablemente, no tienes los permisos para participar en esta consulta", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Para realizar esta consulta se requiere la verificación de tu identidad. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "La consulta no está disponible, porque el proyecto ya no está activo.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "complete el registro", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "conectarse", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "inscríbete", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Verifica tu cuenta ahora.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Solo determinados usuarios pueden revisar este documento. Por favor, {signUpLink} o {logInLink} primero.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Este documento solo puede revisarse cuando esta fase está activa.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Por favor, {completeRegistrationLink} para revisar el documento.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Lamentablemente, no tienes derecho a revisar este documento.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "La revisión de este documento requiere la verificación de tu cuenta. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "El documento ya no está disponible, puesto que este proyecto ya no está activo.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 pick} other {# picks}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "El porcentaje de participantes que eligieron esta opción.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "El porcentaje de votos totales que recibió esta opción.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Coste:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Mostrar más", "app.containers.ReactionControl.a11y_likesDislikes": "Total de ''me gustas': {likesCount}Total de 'no me gustas': {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Has cancelado correctamente tu aversión a esta entrada.", "app.containers.ReactionControl.cancelLikeSuccess": "Has cancelado correctamente tu like para esta entrada.", "app.containers.ReactionControl.dislikeSuccess": "Esta entrada no te ha gustado.", "app.containers.ReactionControl.likeSuccess": "Esta entrada te ha gustado.", "app.containers.ReactionControl.reactionErrorSubTitle": "Debido a un error no se ha podido registrar tu reacción. Por favor, inténtalo de nuevo en unos minutos.", "app.containers.ReactionControl.reactionSuccessTitle": "¡Tu reacción se ha registrado correctamente!", "app.containers.ReactionControl.vote": "Voto", "app.containers.ReactionControl.voted": "Votado", "app.containers.SearchInput.a11y_cancelledPostingComment": "Comentario de publicación cancelado.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} Los comentarios se han cargado.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# eventos se han cargado} one {# evento se ha cargado} other {# eventos se han cargado}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# se han cargado los resultados} one {# se han cargado los resultados} other {# se han cargado los resultados}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# se han cargado los resultados de la búsqueda} one {# se ha cargado el resultado de la búsqueda} other {# se han cargado los resultados de la búsqueda}}.", "app.containers.SearchInput.removeSearchTerm": "Eliminar t<PERSON><PERSON><PERSON>ús<PERSON>da", "app.containers.SearchInput.searchAriaLabel": "Búsqueda", "app.containers.SearchInput.searchLabel": "Búsqueda", "app.containers.SearchInput.searchPlaceholder": "Búsqueda", "app.containers.SearchInput.searchTerm": "<PERSON><PERSON><PERSON><PERSON>: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect is the solution proposed by the French state to secure and simplify the sign up to more than 700 online services.", "app.containers.SignIn.or": "O", "app.containers.SignIn.signInError": "La información proporcionada no es correcta. Haga clic en \"Olvidaste tu contraseña\" para restablecer tu contraseña.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Use FranceConnect to log in, sign up or verify your account.", "app.containers.SignIn.whatIsFranceConnect": "¿Qué es France Connect?", "app.containers.SignUp.adminOptions2": "Para administradores y jefes de proyecto", "app.containers.SignUp.backToSignUpOptions": "Volver a las opciones de registro", "app.containers.SignUp.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.emailConsent": "Al registrarse, aceptas recibir correos electrónicos de esta plataforma. Puedes seleccionar los correos electrónicos que deseas recibir desde tu configuración de usuario.", "app.containers.SignUp.emptyFirstNameError": "Introduzca su nombre", "app.containers.SignUp.emptyLastNameError": "Introduzca su apellido", "app.containers.SignUp.firstNamesLabel": "Nombre", "app.containers.SignUp.goToLogIn": "¿Ya tienes una cuenta? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "He leído y estoy de acuerdo con {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "He leído y estoy de acuerdo con {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "Acepto que los datos sean utilizados en mitgestalten.wien.gv.at. Puede encontrar más información {link}.", "app.containers.SignUp.invitationErrorText": "Tu invitación ha caducado o ya ha sido utilizada. Si ya has utilizado el enlace de invitación para crear una cuenta, intenta iniciar sesión. De lo contrario, regístrate para crear una cuenta nueva.", "app.containers.SignUp.lastNameLabel": "Apellido", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Sigue tus áreas de interés para recibir notificaciones sobre ellas:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Sigue tus temas favoritos para recibir notificaciones sobre ellos:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Guardar preferencias", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Saltar por ahora", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Acepte nuestra política de privacidad para continuar", "app.containers.SignUp.signUp2": "Inscríbete", "app.containers.SignUp.skip": "O<PERSON><PERSON> este paso", "app.containers.SignUp.tacError": "Es necesario aceptar nuestros términos y condiciones para continuar.", "app.containers.SignUp.thePrivacyPolicy": "la Política de privacidad", "app.containers.SignUp.theTermsAndConditions": "los Términos y Condiciones", "app.containers.SignUp.unknownError": "Algo salió mal. Por favor Inténtalo más tarde.", "app.containers.SignUp.viennaConsentEmail": "Dirección de correo electrónico", "app.containers.SignUp.viennaConsentFirstName": "Nombre", "app.containers.SignUp.viennaConsentFooter": "<PERSON>uede cambiar los datos de su perfil después de iniciar la sesión. Si ya tiene una cuenta con la misma dirección de correo electrónico en mitgestalten.wien.gv.at, esta se vinculará con su cuenta actual.", "app.containers.SignUp.viennaConsentHeader": "Se transmitirán los siguientes datos:", "app.containers.SignUp.viennaConsentLastName": "Apellido", "app.containers.SignUp.viennaConsentUserName": "Nombre de usuario", "app.containers.SignUp.viennaDataProtection": "política de privacidad de vienNa", "app.containers.SiteMap.contributions": "Propuestas", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "<PERSON><PERSON><PERSON>", "app.containers.SiteMap.options": "Opciones", "app.containers.SiteMap.projects": "Proyectos", "app.containers.SiteMap.questions": "Preguntas", "app.containers.SpamReport.buttonSave": "Reporte", "app.containers.SpamReport.buttonSuccess": "¡Perfecto", "app.containers.SpamReport.inappropriate": "Es inapropiado u ofensivo", "app.containers.SpamReport.messageError": "Error al enviar el formulario, por favor, inténtelo de nuevo.", "app.containers.SpamReport.messageSuccess": "Su informe ha sido enviado", "app.containers.SpamReport.other": "Otra razón", "app.containers.SpamReport.otherReasonPlaceholder": "Descripción", "app.containers.SpamReport.wrong_content": "Esto no pertenece aquí", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Eliminar la imagen de perfil", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Se borrarán tus votos sobre propuestas cuyo plazo de votación aún esté abierto. Los votos sobre propuestas cuyo periodo de votación se haya cerrado no se eliminarán.", "app.containers.UsersEditPage.addPassword": "<PERSON>ñadir con<PERSON>", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Participar en proyectos para ciudadanos verificados.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Verifica tu identidad", "app.containers.UsersEditPage.bio": "Acerca de ti", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "No puede editar este campo porque contiene información verificada.", "app.containers.UsersEditPage.buttonSuccessLabel": "¡Perfecto!", "app.containers.UsersEditPage.cancel": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.changeEmail": "Cambiar correo electrónico", "app.containers.UsersEditPage.changePassword2": "Cambiar contraseña", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Por favor, haz click aquí para actualizar tu verificación.", "app.containers.UsersEditPage.conditionsLinkText": "nuestras condiciones", "app.containers.UsersEditPage.contactUs": "¿Otra razón para salir? {feedbackLink} y quizás podamos ayudarle.", "app.containers.UsersEditPage.deleteAccountSubtext": "Lamentamos que nos dejes.", "app.containers.UsersEditPage.deleteMyAccount": "Borrar mi cuenta", "app.containers.UsersEditPage.deleteYourAccount": "Borrar mi cuenta", "app.containers.UsersEditPage.deletionSection": "Bo<PERSON> tu cuenta", "app.containers.UsersEditPage.deletionSubtitle": "Esta acción no se puede deshacer. El contenido que publiques en la plataforma será anonimizado. Si desea eliminar todo su contenido, puedes ponerte en contacto con <NAME_EMAIL>.", "app.containers.UsersEditPage.email": "Correo electrónico", "app.containers.UsersEditPage.emailEmptyError": "Proporcionar una dirección de correo electrónico", "app.containers.UsersEditPage.emailInvalidError": "Proporcionar una dirección de correo electrónico en el formato correcto, <NAME_EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Haz clic aquí", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Nombre", "app.containers.UsersEditPage.firstNamesEmptyError": "Proporcionar nombres de pila", "app.containers.UsersEditPage.h1": "La información de su cuenta", "app.containers.UsersEditPage.h1sub": "Editar la información de su cuenta", "app.containers.UsersEditPage.image": "<PERSON>n de avatar", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Haga clic para seleccionar una imagen de perfil (máx. 5 MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Todos los ajustes de su cuenta", "app.containers.UsersEditPage.language": "Idioma", "app.containers.UsersEditPage.lastName": "Apellido", "app.containers.UsersEditPage.lastNameEmptyError": "Proporcionar apellidos", "app.containers.UsersEditPage.loading": "Cargando...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Puede cambiar su correo electrónico o contraseña aquí.", "app.containers.UsersEditPage.loginCredentialsTitle": "Credenciales de acceso", "app.containers.UsersEditPage.messageError": "No pudimos guardar su perfil. Inténtelo más tarde o comuní<NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Su perfil se ha guardado.", "app.containers.UsersEditPage.metaDescription": "Esta es la página de configuración del perfil de {Nombre} {Apellidos} en la plataforma de participación en línea de {Nombredelposeedor}. Aquí puede confirmar su identidad, editar la información de su cuenta, eliminar su cuenta y editar sus preferencias de correo electrónico.", "app.containers.UsersEditPage.metaTitle1": "Página de configuración del perfil de {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Una vez que presiones este botón, no vamos a tener forma de recuperar tu cuenta.", "app.containers.UsersEditPage.noNameWarning2": "Tu nombre aparece actualmente en la plataforma como: \"{displayName}\" porque no has introducido tu nombre. Se trata de un nombre autogenerado. Si quieres cambiarlo, introduce tu nombre a continuación.", "app.containers.UsersEditPage.notificationsSubTitle": "¿Qué tipo de notificaciones por correo electrónico quiere recibir?", "app.containers.UsersEditPage.notificationsTitle": "Email notificaciones", "app.containers.UsersEditPage.password": "Elige una contraseña nueva", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Proporcione una contraseña que tenga al menos {minimumPasswordLength} caracteres", "app.containers.UsersEditPage.passwordAddSection": "Añadir una contraseña", "app.containers.UsersEditPage.passwordAddSubtitle2": "Establezca una contraseña y acceda fácilmente a la plataforma, sin tener que confirmar su correo electrónico cada vez.", "app.containers.UsersEditPage.passwordChangeSection": "Cambiar contraseña", "app.containers.UsersEditPage.passwordChangeSubtitle": "Confirmar su contraseña actual y cambiar a la nueva contraseña.", "app.containers.UsersEditPage.privacyReasons": "Si le preocupa su privacidad, puede leer {conditionsLink}.", "app.containers.UsersEditPage.processing": "Enviando...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "El nombre es obligatorio cuando se proporciona el apellido", "app.containers.UsersEditPage.reasonsToStayListTitle": "Antes que te vayas...", "app.containers.UsersEditPage.submit": "Guardar cambios", "app.containers.UsersEditPage.tooManyEmails": "¿Recibes demasiados correos electrónicos? Puedes administrar sus preferencias de correo en la configuración de su perfil.", "app.containers.UsersEditPage.updateverification": "¿Cambió tu información oficial? {reverifyButton}", "app.containers.UsersEditPage.user": "¿Cuándo quieres que te enviemos un correo electrónico para notificarte?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Puedes participar en proyectos que sólo es posible para ciudadanos verificados.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Eres un ciudadano verificado", "app.containers.UsersEditPage.verifyNow": "Verifica ahora", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Descarga tus respuestas (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {no me gusta} one {1 me gusta} other {# me gusta}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Publicación en la que se publicaste este comentario:", "app.containers.UsersShowPage.areas": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "Comentarios({commentsCount})", "app.containers.UsersShowPage.editProfile": "Editar mi perfil", "app.containers.UsersShowPage.emptyInfoText": "No sigues ningún elemento del filtro especificado anteriormente.", "app.containers.UsersShowPage.eventsWithCount": "Eventos ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "<PERSON><PERSON><PERSON><PERSON> ({followingCount})", "app.containers.UsersShowPage.inputs": "Entradas", "app.containers.UsersShowPage.invisibleTitlePostsList": "Todas las entradas enviadas por este participante", "app.containers.UsersShowPage.invisibleTitleUserComments": "Todos los comentarios publicados por este usuario", "app.containers.UsersShowPage.loadMore": "Carga más", "app.containers.UsersShowPage.loadMoreComments": "Cargar más comentarios", "app.containers.UsersShowPage.loadingComments": "Cargando comentarios...", "app.containers.UsersShowPage.loadingEvents": "Cargando eventos...", "app.containers.UsersShowPage.memberSince": "Miembro desde {date}", "app.containers.UsersShowPage.metaTitle1": "P<PERSON>gina de perfil de {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Esta persona no ha publicado ningún comentario todavía.", "app.containers.UsersShowPage.noCommentsForYou": "No hay comentarios aquí todavía.", "app.containers.UsersShowPage.noEventsForUser": "Aún no has asistido a ningún evento.", "app.containers.UsersShowPage.postsWithCount": "Entradas ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Carpetas de proyectos", "app.containers.UsersShowPage.projects": "Proyectos", "app.containers.UsersShowPage.proposals": "Propuestas", "app.containers.UsersShowPage.seePost": "Ver publicación", "app.containers.UsersShowPage.surveyResponses": "({responses}) respuestas ", "app.containers.UsersShowPage.topics": "<PERSON><PERSON>", "app.containers.UsersShowPage.tryAgain": "Un error ha ocurrido, por favor inténtalo más tarde.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Esta es la página de perfil de {firstName} {lastName} en la plataforma de participación en línea de {orgName}. Esta es una descripción general de todas sus publicaciones.", "app.containers.VoteControl.close": "<PERSON><PERSON><PERSON>", "app.containers.VoteControl.voteErrorTitle": "<PERSON><PERSON>, algo salió mal", "app.containers.admin.ContentBuilder.default": "por defecto", "app.containers.admin.ContentBuilder.imageTextCards": "Imagen y tarjetas de texto", "app.containers.admin.ContentBuilder.infoWithAccordions": "Información y acordeones", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 columna", "app.containers.admin.ContentBuilder.projectDescription": "Descripción del proyecto", "app.containers.app.navbar.admin": "Admin", "app.containers.app.navbar.allProjects": "Todos los proyectos", "app.containers.app.navbar.ariaLabel": "Primario", "app.containers.app.navbar.closeMobileNavMenu": "Cerrar el menú de navegación móvil", "app.containers.app.navbar.editProfile": "Mi configuración", "app.containers.app.navbar.fullMobileNavigation": "Móvil completo", "app.containers.app.navbar.logIn": "Inicia se<PERSON>", "app.containers.app.navbar.logoImgAltText": "{orgName} Inicio", "app.containers.app.navbar.myProfile": "Mi perfil", "app.containers.app.navbar.search": "Búsqueda", "app.containers.app.navbar.showFullMenu": "Mostrar menú completo", "app.containers.app.navbar.signOut": "<PERSON><PERSON><PERSON>", "app.containers.eventspage.errorWhenFetchingEvents": "Se ha producido un error al cargar los eventos. Por favor, intente recargar la página.", "app.containers.eventspage.events": "Eventos", "app.containers.eventspage.eventsPageDescription": "Mostrar todos los eventos publicados en la plataforma de participación de {orgName}.", "app.containers.eventspage.eventsPageTitle1": "Eventos | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Proyectos", "app.containers.eventspage.noPastEvents": "No eventos anteriores que mostrar", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Actualmente no hay eventos próximos o en curso.", "app.containers.eventspage.pastEvents": "Eventos anteriores", "app.containers.eventspage.upcomingAndOngoingEvents": "Eventos en curso y próximos eventos", "app.containers.footer.accessibility-statement": "Declaración de accesibilidad", "app.containers.footer.ariaLabel": "Secundario", "app.containers.footer.cookie-policy": "Política de cookies", "app.containers.footer.cookieSettings": "Configuración de cookies", "app.containers.footer.feedbackEmptyError": "Esto no puede estar vacío.", "app.containers.footer.poweredBy": "Impulsado por", "app.containers.footer.privacy-policy": "Política de privacidad", "app.containers.footer.siteMap": "Mapa del sitio", "app.containers.footer.terms-and-conditions": "Términos y condiciones", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "<PERSON><PERSON><PERSON>", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON>, quiero irme", "app.containers.ideaHeading.editForm": "Editar formulario", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "¿Está seguro de que quiere salir?", "app.containers.ideaHeading.leaveIdeaForm": "Dejar formulario de ideas", "app.containers.ideaHeading.leaveIdeaText": "Tus respuestas no se guardarán.", "app.containers.landing.cityProjects": "Proyectos", "app.containers.landing.completeProfile": "Completa tu perfil", "app.containers.landing.completeYourProfile": "{tenantN<PERSON>, select, Unicef {Bienvenida, {firstName}. Completa tu perfil.} other {Bienvenido, {firstName}. Completa tu perfil.}}", "app.containers.landing.createAccount": "Inscríbete", "app.containers.landing.defaultSignedInMessage": "{firstName} ¡Impulsa un cambio hoy!", "app.containers.landing.doItLater": "Lo voy a hacer más tarde", "app.containers.landing.new": "nuevo", "app.containers.landing.subtitleCity": "Les damos la bienvenida a la plataforma de participación oficial de {orgName}", "app.containers.landing.titleCity": "Construyamos el futuro de {orgName} juntos", "app.containers.landing.twitterMessage": "Vota por {ideaTitle} en", "app.containers.landing.upcomingEventsWidgetTitle": "Eventos en curso y próximos eventos", "app.containers.landing.userDeletedSubtitle": "<PERSON><PERSON><PERSON> crear una nueva cuenta en cualquier momento o {contactLink} hacernos saber en qué podemos mejorar.", "app.containers.landing.userDeletedSubtitleLinkText": "escríbenos", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "Tu cuenta ha sido eliminado.", "app.containers.landing.userDeletionFailed": "Un error ha ocurrido borrando tu cuenta, hemos notificado este asunto y haremos lo posible por arreglarlo. Inténtalo más tarde.", "app.containers.landing.verifyNow": "Verificar ahora", "app.containers.landing.verifyYourIdentity": "Confirma que tu identidad ha sido verificada", "app.containers.landing.viewAllEventsText": "Ver todos los eventos", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Volver a la carpeta", "app.errors.after_end_at": "La fecha de inicio es anterior a la fecha de término", "app.errors.avatar_carrierwave_download_error": "No pude descargar el archivo del avatar.", "app.errors.avatar_carrierwave_integrity_error": "El archivo de Avatar no es del tipo permitido.", "app.errors.avatar_carrierwave_processing_error": "No pudo procesar el avatar.", "app.errors.avatar_extension_blacklist_error": "La extensión del archivo de la imagen del avatar no está permitida. Las extensiones permitidas son: jpg, jpeg, gif y png.", "app.errors.avatar_extension_whitelist_error": "La extensión del archivo de la imagen del avatar no está permitida. Las extensiones permitidas son: jpg, jpeg, gif y png.", "app.errors.banner_cta_button_multiloc_blank": "Introduzca el texto de un botón.", "app.errors.banner_cta_button_url_blank": "Introduzca un enlace.", "app.errors.banner_cta_button_url_url": "Introduzca un enlace válido. Asegúrese de que el enlace empiece por 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Introduzca el texto de un botón.", "app.errors.banner_cta_signed_in_url_blank": "Introduzca un enlace.", "app.errors.banner_cta_signed_in_url_url": "Introduzca un enlace válido. Asegúrese de que el enlace empiece por 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Introduzca el texto de un botón.", "app.errors.banner_cta_signed_out_url_blank": "Introduzca un enlace.", "app.errors.banner_cta_signed_out_url_url": "Introduzca un enlace válido. Asegúrese de que el enlace empiece por 'https://'.", "app.errors.base_includes_banned_words": "Es posible que hayas utilizado una o más palabras que se consideran blasfemias. Por favor, modifica tu texto para eliminar cualquier blasfemia que pueda contener.", "app.errors.body_multiloc_includes_banned_words": "La descripción contiene palabras que se consideran inapropiadas.", "app.errors.bulk_import_idea_not_valid": "La idea resultante no es válida: {value}.", "app.errors.bulk_import_image_url_not_valid": "No se ha podido descargar ninguna imagen de {value}. Asegúrese de que la URL sea válida y termine con una extensión de archivo como .png o .jpg. Este problema se produce en la fila con el ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Ubicación de la idea con una coordenada que falta en {value}. Este problema se produce en la fila con el ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Ubicación de la idea con una coordenada no numérica en {value}. Este problema se produce en la fila con el ID {row}.", "app.errors.bulk_import_malformed_pdf": "El archivo PDF cargado parece estar malformado. Intenta exportar de nuevo el PDF desde tu fuente y luego vuelve a subirlo.", "app.errors.bulk_import_maximum_ideas_exceeded": "Se ha superado el máximo de ideas de {value}.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Se ha superado el máximo de {value} páginas en un PDF.", "app.errors.bulk_import_not_enough_pdf_pages": "El PDF cargado no tiene suficientes páginas - debería tener al menos el mismo número de páginas que la plantilla descargada.", "app.errors.bulk_import_publication_date_invalid_format": "Idea con formato de fecha de publicación no válido \"{value}\". <PERSON><PERSON> favor, utiliza el formato \"DD-MM-AAAA\".", "app.errors.cannot_contain_ideas": "Esta fase contiene {ideasCount, plural, one {one idea} other {{ideasCount} ideas}} y el método de participación al que intenta cambiarla no es compatible con las ideas. Elimine {ideasCount, plural, one {the idea} other {the ideas}} de la fase e inténtelo de nuevo.", "app.errors.cant_change_after_first_response": "No es posible modificarlo, ya que algunos usuarios ya lo han respondido.", "app.errors.category_name_taken": "Ya existe una etiqueta con este nombre", "app.errors.confirmation_code_expired": "El código ha caducado. Por favor, vuelva a ingresar un nuevo código.", "app.errors.confirmation_code_invalid": "Código de confirmación inválido. Por favor, comprueba tu correo electrónico para obtener el código correcto o intenta 'Enviar nuevo código'", "app.errors.confirmation_code_too_many_resets": "Has reenviado el código de confirmación demasiadas veces. Ponte en contacto con nosotros para recibir un código de invitación en su lugar.", "app.errors.confirmation_code_too_many_retries": "<PERSON> has intentado demasiadas veces. Por favor, vuelve a enviar un código o intenta cambiar tu correo electrónico.", "app.errors.email_already_active": "La direccion de correo {value} encontrada en {row} ya pertenece a un usuario registrado", "app.errors.email_already_invited": "El correo electrónico {value} en fila {row} ya fue invitado", "app.errors.email_blank": "Esto no puede estar vacío", "app.errors.email_domain_blacklisted": "Por favor, utilice un dominio de correo electrónico diferente para registrarse.", "app.errors.email_invalid": "Por favor ingrese una dirección válida de correo.", "app.errors.email_taken": "Ya existe una cuenta con este correo electrónico. Puede iniciar sesión.", "app.errors.email_taken_by_invite": "{value} ya está tomado por una invitación pendiente.", "app.errors.emails_duplicate": "Uno o más valores duplicados para la dirección de correo electrónico {value} fueron encontrados en las siguientes filas: {rows}", "app.errors.extension_whitelist_error": "No se admite el formato del archivo que has intentado subir.", "app.errors.file_extension_whitelist_error": "No se admite el formato del archivo que has intentado subir.", "app.errors.first_name_blank": "Esto no puede estar vacío", "app.errors.generics.blank": "Esto no puede estar vacío", "app.errors.generics.invalid": "Esto no parece un valor válido", "app.errors.generics.taken": "Ya existe este correo electrónico. Otra cuenta está vinculada a ese correo.", "app.errors.generics.unsupported_locales": "Este campo no admite la configuración regional.", "app.errors.group_ids_unauthorized_choice_moderator": "Como administrador de proyecto. s<PERSON>lo puedes enviar correos a las personas que pueden acceder a tu(s) proyecto(s)", "app.errors.has_other_overlapping_phases": "Las fases de un proyecto no se pueden traslapar.", "app.errors.invalid_email": "El correo {value} encontrado en {row} no es un correo válido", "app.errors.invalid_row": "Un error desconocido ocurrio mientras tratabamos de procesar la fila {row}", "app.errors.is_not_timeline_project": "El presente proyecto no soporta fases.", "app.errors.key_invalid": "La clave solo puede contener letras, números y guiones bajos(_)", "app.errors.last_name_blank": "Esto no puede estar vacío", "app.errors.locale_blank": "Por favor escoge un lenguage", "app.errors.locale_inclusion": "Por favor escoge un lenguage que soporte la plataforma", "app.errors.malformed_admin_value": "El valor de admin {value} encontrado en {row} no es válido", "app.errors.malformed_groups_value": "El grupo {value} encontrado en {row} no es un grupo válido", "app.errors.max_invites_limit_exceeded1": "El número de invitaciones supera el límite de 1000.", "app.errors.maximum_attendees_greater_than1": "El número máximo de inscritos debe ser superior a 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "El número máximo de inscritos debe ser mayor o igual que el número actual de inscritos.", "app.errors.no_invites_specified": "No se ha podido encontrar ninguna dirección de correo.", "app.errors.no_recipients": "La campaña no puede enviarse porque no hay destinatarios. El grupo al que estás enviando está vacío o nadie ha dado su consentimiento para recibir correos electrónicos.", "app.errors.number_invalid": "Introduce un número válido.", "app.errors.password_blank": "Esto no puede estar vacío", "app.errors.password_invalid": "Compruebe de nuevo su contraseña actual.", "app.errors.password_too_short": "La contraseña debe tener al menos 8 caracteres", "app.errors.resending_code_failed": "Algo ha fallado al enviar el código de confirmación.", "app.errors.slug_taken": "La URL de este proyecto ya existe. Por favor, cambie el slug del proyecto por otro.", "app.errors.tag_name_taken": "Ya existe una etiqueta con este nombre", "app.errors.title_multiloc_blank": "El título no puede estar vacío.", "app.errors.title_multiloc_includes_banned_words": "El título contiene palabras que se consideran inapropiadas.", "app.errors.token_invalid": "Los enlaces para restablecer la contraseña sólo pueden usarse una vez y son válidos durante una hora después de ser enviados. {passwordResetLink}.", "app.errors.too_common": "Esta contraseña es fácil de adivinar. Por favor, elige una contraseña más fuerte.", "app.errors.too_long": "La contraseña debe tener al menos 72 caracteres", "app.errors.too_short": "La contraseña debe tener al menos 8 caracteres", "app.errors.uncaught_error": "Se ha producido un error desconocido.", "app.errors.unknown_group": "El grupo {value} en la fila {row} no es un grupo conocido", "app.errors.unknown_locale": "El idioma {value} en la fila {row} no es un idioma configurado", "app.errors.unparseable_excel": "El archivo de Excel seleccionado podría no ser procesado.", "app.errors.url": "Introduzca un enlace válido. Asegúrese de que el enlace empiece por 'https://", "app.errors.verification_taken": "No se puede completar la verificación porque se ha verificado otra cuenta utilizando los mismos datos.", "app.errors.view_name_taken": "Ya existe una vista con este nombre", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Se ha detectado contenido inapropiado en una publicación o comentario automáticamente", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Ingrese con StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Inscríbase con StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Cree ahora una cuenta de Stadt Wien y utilice un único acceso para muchos servicios digitales de Viena.", "app.modules.id_cow.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_cow.emptyFieldError": "Esto no puede estar vacío", "app.modules.id_cow.helpAltText": "Muestra dónde encontrar el número de serie de un documento de identidad", "app.modules.id_cow.invalidIdSerialError": "CI número de serie/ documento inválido", "app.modules.id_cow.invalidRunError": "Tú RUT no está registrado. Revisa otras formas de participar en la plataforma o con tu municipalidad.", "app.modules.id_cow.noMatchFormError": "No hubo coincidencia.", "app.modules.id_cow.notEntitledFormError": "Lamentamos no poder verificar tu identidad.", "app.modules.id_cow.showCOWHelp": "¿Dónde puedo encontrar mi número de serie/ documento en la cédula de identidad?", "app.modules.id_cow.somethingWentWrongError": "Algo salió mal, no podemos verificarlo", "app.modules.id_cow.submit": "Enviar", "app.modules.id_cow.takenFormError": "Ya está ocupado.", "app.modules.id_cow.verifyCow": "Verifícate usando COW", "app.modules.id_franceconnect.verificationButtonAltText": "Verificar con FranceConnect", "app.modules.id_gent_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "Este campo no puede estar vacío.", "app.modules.id_gent_rrn.gentRrnHelp": "Su número de seguridad social aparece en el reverso de su documento de identidad digital", "app.modules.id_gent_rrn.invalidRrnError": "Número de seguridad social inválido", "app.modules.id_gent_rrn.noMatchFormError": "No hemos podido encontrar información sobre su número de seguridad social", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "No podemos verificarle porque vive fuera de Ghent", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "No podemos verificarle porque es menor de 14 años", "app.modules.id_gent_rrn.rrnLabel": "Número de la seguridad social", "app.modules.id_gent_rrn.rrnTooltip": "Le pedimos su número de la seguridad social para verificar si usted es ciudadano de Ghent, mayor de 14 años.", "app.modules.id_gent_rrn.showGentRrnHelp": "¿Dónde puedo encontrar mi número de serie/ documento en la cédula de identidad?", "app.modules.id_gent_rrn.somethingWentWrongError": "Algo salió mal, no podemos verificarlo", "app.modules.id_gent_rrn.submit": "Enviar", "app.modules.id_gent_rrn.takenFormError": "Su número de seguridad social ya ha sido utilizado para verificar otra cuenta", "app.modules.id_gent_rrn.verifyGentRrn": "Verificar con GentRrn", "app.modules.id_id_card_lookup.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.emptyFieldError": "Esto no puede estar vacío", "app.modules.id_id_card_lookup.helpAltText": "Explicación de documento de identidad", "app.modules.id_id_card_lookup.invalidCardIdError": "Esta identificación no es válida.", "app.modules.id_id_card_lookup.noMatchFormError": "No hubo coincidencia.", "app.modules.id_id_card_lookup.showHelp": "¿Dónde puedo encontrar mi número de serie/ documento en la cédula de identidad?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Algo salió mal, no podemos verificarlo", "app.modules.id_id_card_lookup.submit": "Enviar", "app.modules.id_id_card_lookup.takenFormError": "Ya está ocupado.", "app.modules.id_oostende_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.emptyFieldError": "Esto no puede estar vacío", "app.modules.id_oostende_rrn.invalidRrnError": "Número de seguridad social inválido", "app.modules.id_oostende_rrn.noMatchFormError": "No hemos podido encontrar información sobre su número de seguridad social", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "No podemos verificarlo porque vive fuera de Oostende", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "No podemos verificarle porque es menor de 14 años", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Su número de seguridad social aparece en el reverso de su documento de identidad digital", "app.modules.id_oostende_rrn.rrnLabel": "Número de la seguridad social", "app.modules.id_oostende_rrn.rrnTooltip": "Le pedimos su número de la seguridad social para verificar si es ciudadano de Oostende y  mayor de 14 años.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "¿Dónde puedo encontrar mi número de la seguridad social?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Algo salió mal, no podemos verificarlo", "app.modules.id_oostende_rrn.submit": "Enviar", "app.modules.id_oostende_rrn.takenFormError": "Su número de seguridad social ya ha sido utilizado para verificar otra cuenta", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Verificar con el número de la seguridad social", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Has recibido derechos de administrador para la carpeta \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "¡Mire los proyectos en {folderUrl} para hacer oír su voz!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | de la plataforma de participación de {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | de la plataforma de participación de {orgName}", "app.sessionRecording.accept": "Sí, acepto", "app.sessionRecording.modalDescription1": "Para comprender mejor a nuestros usuarios, pedimos aleatoriamente a un pequeño porcentaje de visitantes que sigan su sesión de navegación en detalle.", "app.sessionRecording.modalDescription2": "El único propósito de los datos registrados es mejorar el sitio web. Ninguno de tus datos se compartirá con terceros. Cualquier información sensible que introduzcas será filtrada.", "app.sessionRecording.modalDescription3": "¿Ace<PERSON><PERSON>?", "app.sessionRecording.modalDescriptionFaq": "Preguntas más frecuentes, aquí.", "app.sessionRecording.modalTitle": "Ayúdanos a mejorar este sitio web", "app.sessionRecording.reject": "No, rechazo", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Realizar un ejercicio de asignación presupuestaria", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Recolectar opiniones y comentarios sobre un documento", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "<PERSON><PERSON>r una encuesta en la plataforma", "app.utils.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON><PERSON> una encuesta", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Crear una encuesta con un servicio externo", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Encuentra voluntarios", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Recopilar aportes y retroalimentaciones", "app.utils.AdminPage.ProjectEdit.shareInformation": "Comparte información", "app.utils.FormattedCurrency.credits": "c<PERSON><PERSON><PERSON>", "app.utils.FormattedCurrency.tokens": "fi<PERSON>s", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# créditos} one {# créditos} other {# créditos}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# fichas} one {# ficha} other {# fichas}}", "app.utils.IdeaCards.mostDiscussed": "Lo más discutido", "app.utils.IdeaCards.mostReacted": "Con más reacciones", "app.utils.IdeaCards.newest": "Más reciente", "app.utils.IdeaCards.oldest": "<PERSON>ás antiguo", "app.utils.IdeaCards.random": "Al azar", "app.utils.IdeaCards.trending": "Tendencias", "app.utils.IdeasNewPage.contributionFormTitle": "Agregar nueva propuesta", "app.utils.IdeasNewPage.ideaFormTitle": "Agregar nueva idea", "app.utils.IdeasNewPage.initiativeFormTitle": "Añadir nueva iniciativa", "app.utils.IdeasNewPage.issueFormTitle1": "Agregar nueva respuesta", "app.utils.IdeasNewPage.optionFormTitle": "Agregar nueva opción", "app.utils.IdeasNewPage.petitionFormTitle": "Añadir nueva petición", "app.utils.IdeasNewPage.projectFormTitle": "Agregar nuevo proyecto", "app.utils.IdeasNewPage.proposalFormTitle": "<PERSON><PERSON>dir nueva propuesta", "app.utils.IdeasNewPage.questionFormTitle": "Agregar nueva pregunta", "app.utils.IdeasNewPage.surveyTitle": "Consulta", "app.utils.IdeasNewPage.viewYourComment": "Ver tu comentario", "app.utils.IdeasNewPage.viewYourContribution": "Ver tu contribución", "app.utils.IdeasNewPage.viewYourIdea": "Ver tu idea", "app.utils.IdeasNewPage.viewYourInitiative": "Ver tu iniciativa", "app.utils.IdeasNewPage.viewYourInput": "Ver tu aportación", "app.utils.IdeasNewPage.viewYourIssue": "Ver tu asunto", "app.utils.IdeasNewPage.viewYourOption": "Ver tu opción", "app.utils.IdeasNewPage.viewYourPetition": "Ver tu petición", "app.utils.IdeasNewPage.viewYourProject": "Ver tu proyecto", "app.utils.IdeasNewPage.viewYourProposal": "Ver tu propuesta", "app.utils.IdeasNewPage.viewYourQuestion": "Ver tu pregunta", "app.utils.Projects.sendSubmission": "Enviar identificador de envío a mi correo electrónico", "app.utils.Projects.sendSurveySubmission": "Enviar el identificador de envío de la encuesta a mi correo electrónico", "app.utils.Projects.surveySubmission": "Envío de encuesta", "app.utils.Projects.yourResponseHasTheFollowingId": "Tu respuesta tiene el siguiente identificador: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "Si más adelante decides que quieres que se elimine tu respuesta, ponte en contacto con nosotros con el siguiente identificador único:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Debes completar tu perfil para asistir a este acto.", "app.utils.actionDescriptors.attendingEventNotInGroup": "No cumples los requisitos para asistir a este acto.", "app.utils.actionDescriptors.attendingEventNotPermitted": "No se te permite asistir a este acto.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Debes iniciar sesión o registrarte para asistir a este acto.", "app.utils.actionDescriptors.attendingEventNotVerified": "Debes verificar tu cuenta antes de poder asistir a este acto.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "Debes completar tu perfil para ser voluntario.", "app.utils.actionDescriptors.volunteeringNotInGroup": "No cumples los requisitos para ser voluntario.", "app.utils.actionDescriptors.volunteeringNotPermitted": "No se te permite ser voluntario.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Debes iniciar sesión o registrarte para ser voluntario.", "app.utils.actionDescriptors.volunteeringNotVerified": "Debes verificar tu cuenta antes de poder ser voluntario.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Por favor, {completeRegistrationLink} para ser voluntario.", "app.utils.errors.api_error_default.in": "No es correcto", "app.utils.errors.default.ajv_error_birthyear_required": "Introduzca su año de nacimiento", "app.utils.errors.default.ajv_error_date_any": "Introduzca una fecha válida", "app.utils.errors.default.ajv_error_domicile_required": "Indique su lugar de residencia", "app.utils.errors.default.ajv_error_gender_required": "Indique su género", "app.utils.errors.default.ajv_error_invalid": "No es válido", "app.utils.errors.default.ajv_error_maxItems": "No puede incluir más de {limit, plural, one {# item} other {# items}}", "app.utils.errors.default.ajv_error_minItems": "<PERSON><PERSON> incluir al menos {limit, plural, one {# item} other {# items}}", "app.utils.errors.default.ajv_error_number_any": "Introduzca un número válido", "app.utils.errors.default.ajv_error_politician_required": "Indique si usted es político", "app.utils.errors.default.ajv_error_required3": "El campo es obligatorio: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "No puede estar en blanco", "app.utils.errors.default.api_error_accepted": "Debe ser aceptado", "app.utils.errors.default.api_error_blank": "No puede estar en blanco", "app.utils.errors.default.api_error_confirmation": "No coincide", "app.utils.errors.default.api_error_empty": "No puede estar vacío", "app.utils.errors.default.api_error_equal_to": "No es correcto", "app.utils.errors.default.api_error_even": "Debe ser par", "app.utils.errors.default.api_error_exclusion": "Está reservado", "app.utils.errors.default.api_error_greater_than": "Es demasiado pequeño", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Es demasiado pequeño", "app.utils.errors.default.api_error_inclusion": "No está incluido en la lista", "app.utils.errors.default.api_error_invalid": "No es válido", "app.utils.errors.default.api_error_less_than": "Es demasiado grande", "app.utils.errors.default.api_error_less_than_or_equal_to": "Es demasiado grande", "app.utils.errors.default.api_error_not_a_number": "No es un número", "app.utils.errors.default.api_error_not_an_integer": "Debe ser un número entero", "app.utils.errors.default.api_error_other_than": "No es correcto", "app.utils.errors.default.api_error_present": "Debe estar en blanco", "app.utils.errors.default.api_error_too_long": "Es demasiado largo", "app.utils.errors.default.api_error_too_short": "Es demasiado corto", "app.utils.errors.default.api_error_wrong_length": "Tiene una longitud incorrecta", "app.utils.errors.defaultapi_error_.odd": "Debe ser impar", "app.utils.notInGroup": "No cumples los requisitos para participar.", "app.utils.participationMethod.onSurveySubmission": "Gracias. Su respuesta ha sido recibida.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "La votación ya no está disponible, puesto que esta fase ya no está activa.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "No cumples los requisitos para votar.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "No se te permite votar.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Debes iniciar sesión o registrarte para votar.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Debes verificar tu cuenta antes de poder votar.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>El envío de presupuestos cerró en {endDate}.</b> Los participantes disponían de un total de <b>{maxBudget} cada uno para distribuir entre {optionCount} opcion(es).</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Presupuesto enviado", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Presupuesto enviado 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "No cumples los requisitos para asignar presupuestos.", "app.utils.votingMethodUtils.budgetingNotPermitted": "No puedes asignar presupuestos.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Debes iniciar sesión o registrarte para asignar presupuestos.", "app.utils.votingMethodUtils.budgetingNotVerified": "Debes verificar tu cuenta antes de poder asignar presupuestos.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Tu presupuesto no se contabilizará</b> hasta que hagas clic en \"Enviar\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "El presupuesto mínimo requerido es {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "<PERSON><PERSON>do hayas terminado, haz clic en \"Enviar\" para enviar tu presupuesto.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Selecciona las opciones que prefieras pulsando \"Añadir\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Tienes un total de <b>{maxBudget} para distribuir entre {optionCount} opciones</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON>, ¡tu presupuesto ha sido enviado!</b> <PERSON><PERSON><PERSON> revisar tu selección a continuación en cualquier momento o modificarla antes de <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON>, ¡tu presupuesto ha sido enviado!</b> <PERSON><PERSON>es comprobar tus opciones a continuación en cualquier momento.", "app.utils.votingMethodUtils.castYourVote": "Vota", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "<PERSON><PERSON><PERSON> a<PERSON>dir un máximo de {maxVotes, plural, one {# créditos} other {# créditos}} por opción.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "<PERSON><PERSON><PERSON> a<PERSON>dir un máximo de {maxVotes, plural, one {# puntos} other {# puntos}} por opción.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "<PERSON><PERSON><PERSON> a<PERSON>dir un máximo de {maxVotes, plural, one {# fichas} other {# fichas}} por opción.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "<PERSON><PERSON><PERSON> a<PERSON>dir un máximo de {maxVotes, plural, one {# votos} other {# votos}} por opción.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON><PERSON>do hayas terminado, haz clic en \"Enviar\" para emitir tu voto.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Selecciona las opciones que prefieras pulsando \"Votar\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "Tienes un total de <b>{totalVotes, plural, one {# crédito} other {# créditos}} para distribuir entre {optionCount, plural, one {# opción} other {# opciones}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "Tienes un total de <b>{totalVotes, plural, one {# punto} other {# puntos}} para distribuir entre {optionCount, plural, one {# opción} other {# opciones}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "Tienes un total de <b>{totalVotes, plural, one {# ficha} other {# fichas}} para distribuir entre {optionCount, plural, one {# opción} other {# opciones}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "Tienes un total de <b>{totalVotes, plural, one {# voto} other {# votos}} para distribuir entre {optionCount, plural, one {# opción} other {# opciones}}</b>.", "app.utils.votingMethodUtils.finalResults": "Resultados de la votación", "app.utils.votingMethodUtils.finalTally": "Recuento final", "app.utils.votingMethodUtils.howToParticipate": "Cómo participar", "app.utils.votingMethodUtils.howToVote": "Cómo votar", "app.utils.votingMethodUtils.multipleVotingEnded1": "La votación se cerró en <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 créditos} one {1 crédito} other {# créditos}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 puntos} one {1 punto} other {# puntos}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 fichas} one {1 ficha} other {# fichas}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 votos} one {1 voto} other {# votos}}", "app.utils.votingMethodUtils.results": "Resul<PERSON><PERSON>", "app.utils.votingMethodUtils.singleVotingEnded": "La votación se cerró en <b>{endDate}.</b> Los participantes podían <b>votar las opciones de {maxVotes} .</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Selecciona las opciones que prefieras pulsando \"Seleccionar\".", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Tienes <b>{totalVotes} votos</b> que puedes asignar a las opciones.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON><PERSON>do hayas terminado, haz clic en \"Enviar\" para emitir tu voto.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "La votación se cerró en <b>{endDate}.</b> Los participantes podían <b>votar por 1 opción.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Selecciona la opción que prefieras pulsando \"Votar\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Tienes <b>1 voto</b> que puedes asignar a una de las opciones.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "La votación se cerró en <b>{endDate}.</b> Los participantes podían <b>votar tantas opciones como quisieran.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Puedes votar tantas opciones como quieras.", "app.utils.votingMethodUtils.submitYourBudget": "Envía tu presupuesto", "app.utils.votingMethodUtils.submittedBudgetCountText2": "persona presentó su presupuesto en línea", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "personas presentaron sus presupuestos en línea", "app.utils.votingMethodUtils.submittedVoteCountText2": "persona emitió su voto en línea", "app.utils.votingMethodUtils.submittedVotesCountText2": "personas enviaron sus votos en línea", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Voto emitido 🎉", "app.utils.votingMethodUtils.votesCast": "Votos emitidos", "app.utils.votingMethodUtils.votingClosed": "Votación cerrada", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Tu voto no se contabilizará</b> hasta que hagas clic en \"Enviar\".", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON>, ¡tu voto ha sido enviado!</b> <PERSON><PERSON><PERSON> comprobar o modificar tu voto antes de <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON>, ¡tu voto ha sido enviado!</b> <PERSON><PERSON><PERSON> comprobar o modificar tu voto a continuación en cualquier momento.", "components.UI.IdeaSelect.noIdeaAvailable": "No hay ideas disponibles.", "components.UI.IdeaSelect.selectIdea": "Selecciona una idea", "containers.SiteMap.allProjects": "Todos los proyectos", "containers.SiteMap.customPageSection": "Páginas personalizadas", "containers.SiteMap.folderInfo": "Más información", "containers.SiteMap.headSiteMapTitle": "Mapa del sitio | {orgName}", "containers.SiteMap.homeSection": "General", "containers.SiteMap.pageContents": "Contenido de la página", "containers.SiteMap.profilePage": "Tu página de perfil", "containers.SiteMap.profileSettings": "Tus opciones de configuración", "containers.SiteMap.projectEvents": "Eventos", "containers.SiteMap.projectIdeas": "Ideas", "containers.SiteMap.projectInfo": "Información", "containers.SiteMap.projectPoll": "Consulta Ciudadana", "containers.SiteMap.projectSurvey": "Consulta", "containers.SiteMap.projectsArchived": "Proyectos archivados", "containers.SiteMap.projectsCurrent": "Proyectos en curso", "containers.SiteMap.projectsDraft": "Anteproyectos", "containers.SiteMap.projectsSection": "Proyectos de {orgName}", "containers.SiteMap.signInPage": "Inicia se<PERSON>", "containers.SiteMap.signUpPage": "Inscríbete", "containers.SiteMap.siteMapDescription": "Desde esta página, puede navegar a cualquier contenido de la plataforma.", "containers.SiteMap.siteMapTitle": "Mapa del sitio de la plataforma de participación de {orgName}", "containers.SiteMap.successStories": "Historias de éxito", "containers.SiteMap.timeline": "Fases del proyecto", "containers.SiteMap.userSpaceSection": "Tu perfil", "containers.SubscriptionEndedPage.accessDenied": "Ya no tienes acceso", "containers.SubscriptionEndedPage.subscriptionEnded": "It appears that you no longer can access this page, since your Go Vocal subscription has ended."}
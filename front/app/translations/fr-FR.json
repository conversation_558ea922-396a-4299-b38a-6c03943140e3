{"EmailSettingsPage.emailSettings": "Paramètres des e-mails", "EmailSettingsPage.initialUnsubscribeError": "Il y a eu un problème lors de votre désabonnement à cette campagne, veuil<PERSON>z réessayer.", "EmailSettingsPage.initialUnsubscribeLoading": "Votre demande est en cours de traitement, veuillez patienter...", "EmailSettingsPage.initialUnsubscribeSuccess": "Vous avez réussi à vous désabonner de {campaignTitle}.", "UI.FormComponents.optional": "optionnel", "app.closeIconButton.a11y_buttonActionMessage": "<PERSON><PERSON><PERSON>", "app.components.Areas.areaUpdateError": "Une erreur s'est produite lors de l'enregistrement de votre zone. Veuillez réessayer.", "app.components.Areas.followedArea": "Zone suivie : {areaTitle}", "app.components.Areas.followedTopic": "Sujet suivi : {topicTitle}", "app.components.Areas.topicUpdateError": "Une erreur s'est produite lors de l'enregistrement de votre sujet. Veuillez réessayer.", "app.components.Areas.unfollowedArea": "Zone non suivie : {areaTitle}", "app.components.Areas.unfollowedTopic": "Thème non suivi : {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Prix :", "app.components.AssignBudgetControl.add": "Ajouter", "app.components.AssignBudgetControl.added": "<PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.addVote": "Ajouter un vote", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Vous avez utilisé tous vos crédits.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Vous avez attribué le nombre maximum de crédits pour cette option.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "<PERSON><PERSON> avez utilisé tous vos points.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Vous avez attribué le nombre maximum de points pour cette option.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Vous avez utilisé tous vos jetons.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Vous avez attribué le nombre maximum de jetons pour cette option.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "V<PERSON> avez utilisé tous vos votes.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Vous avez attribué le nombre maximum de votes pour cette option.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(dont 1 hors ligne)} other {(dont # hors ligne)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Vous ne pouvez pas voter, car cette phase n'est pas active.", "app.components.AssignMultipleVotesControl.removeVote": "Retirer un vote", "app.components.AssignMultipleVotesControl.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Vous avez déjà soumis votre vote. Pour le modifier, cliquez sur « Modifier votre soumission ».", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Vous avez déjà soumis votre vote. Pour le modifier, retournez à la page du projet et cliquez sur « Modifier votre soumission ».", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {crédit} other {crédits}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {point} other {points}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {jeton} other {jetons}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {vote} other {votes}}", "app.components.AssignVoteControl.maxVotesReached1": "V<PERSON> avez utilisé tous vos votes.", "app.components.AssignVoteControl.phaseNotActive": "Vous ne pouvez pas voter, car cette phase n'est pas active.", "app.components.AssignVoteControl.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.selected2": "Sélectionné", "app.components.AssignVoteControl.voteForAtLeastOne": "Votez pour au moins 1 option", "app.components.AssignVoteControl.votesSubmitted1": "Vous avez déjà soumis votre vote. Pour le modifier, cliquez sur « Modifier votre soumission ».", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Vous avez déjà soumis votre vote. Pour le modifier, retournez à la page du projet et cliquez sur « Modifier votre soumission ».", "app.components.AuthProviders.continue": "<PERSON><PERSON><PERSON>", "app.components.AuthProviders.continueWithAzure": "Continuer avec {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Continuer avec Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Continuer avec Fake SSO", "app.components.AuthProviders.continueWithGoogle": "Continuer avec Google", "app.components.AuthProviders.continueWithHoplr": "Continuer avec Hoplr", "app.components.AuthProviders.continueWithIdAustria": "Continuer avec ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Continuer avec {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Continuer avec MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Un compte d’utilisateur avec cette adresse mail existe déjà.{br}{br}Vous ne pouvez pas accéder à la plateforme en utilisant FranceConnect, puisque les informations personnelles ne correspondent pas au compte. Pour vous identifier en utilisant FranceConnect, il faudrait d’abord changer le nom et prénom sur la plateforme pour qu’ils correspondent aux informations officielles.{br}{br}Vous pouvez vous connectez en suivant le champ ci-dessous.", "app.components.AuthProviders.goToLogIn": "Vous avez déjà un compte ? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Vous n'avez pas de compte ? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Se connecter", "app.components.AuthProviders.logInWithEmail": "Connectez-vous avec votre e-mail", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Vous devez avoir l'âge minimum spécifié ou plus pour être vérifié.", "app.components.AuthProviders.signUp2": "S'inscrire", "app.components.AuthProviders.signUpButtonAltText": "Inscrivez-vous avec {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Inscrivez-vous avec votre e-mail", "app.components.AuthProviders.verificationRequired": "Vérification requise", "app.components.Author.a11yPostedBy": "Publié par", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 participant} other {{numberOfParticipants} participants}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} utilisateurs", "app.components.AvatarBubbles.participant": "participant", "app.components.AvatarBubbles.participants1": "participants", "app.components.Comments.cancel": "Annuler", "app.components.Comments.commentingDisabledInCurrentPhase": "Il n'est pas possible de faire des commentaires dans la phase actuelle.", "app.components.Comments.commentingDisabledInactiveProject": "Il n'est pas possible de commenter car ce projet n'est pas actif actuellement.", "app.components.Comments.commentingDisabledProject": "Les commentaires dans ce projet sont actuellement désactivés.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} pour commenter.", "app.components.Comments.commentingMaybeNotPermitted": "Veuillez vous {signInLink} sur la plateforme pour voir quelles actions vous pouvez entreprendre.", "app.components.Comments.inputsAssociatedWithProfile": "<PERSON><PERSON> <PERSON><PERSON>, tes contributions seront associées à ton profil, à moins que tu ne sélectionnes cette option.", "app.components.Comments.invisibleTitleComments": "Commentaires", "app.components.Comments.leastRecent": "<PERSON><PERSON> r<PERSON>", "app.components.Comments.likeComment": "Aimer ce commentaire", "app.components.Comments.mostLiked": "Plus de réactions", "app.components.Comments.mostRecent": "Plus récents", "app.components.Comments.official": "Officiel", "app.components.Comments.postAnonymously": "Publier anonymement", "app.components.Comments.replyToComment": "Répondre au commentaire", "app.components.Comments.reportAsSpam": "Signaler", "app.components.Comments.seeOriginal": "Afficher l'original", "app.components.Comments.seeTranslation": "Voir la traduction", "app.components.Comments.yourComment": "Votre commentaire", "app.components.CommonGroundResults.divisiveDescription": "Affirmations pour lesquelles les opinions divergent le plus, par ordre décroissant :", "app.components.CommonGroundResults.divisiveTitle": "Opinion partagée", "app.components.CommonGroundResults.majorityDescription": "Affirmations par degré de consensus décroissant :", "app.components.CommonGroundResults.majorityTitle": "Consensus", "app.components.CommonGroundResults.participantLabel": "participant", "app.components.CommonGroundResults.participantsLabel1": "participants", "app.components.CommonGroundResults.statementLabel": "affirmation", "app.components.CommonGroundResults.statementsLabel1": "affirmations", "app.components.CommonGroundResults.votesLabe": "vote", "app.components.CommonGroundResults.votesLabel1": "votes", "app.components.CommonGroundStatements.agreeLabel": "D'accord", "app.components.CommonGroundStatements.disagreeLabel": "Pas d'accord", "app.components.CommonGroundStatements.noMoreStatements": "Il n’y a plus d’affirmations auxquelles réagir pour le moment", "app.components.CommonGroundStatements.noResults": "Aucun résultat pour l'instant. Veuillez participer à la phase de Consensus, puis revenez consulter les résultats ici.", "app.components.CommonGroundStatements.unsureLabel": "Incertain(e)", "app.components.CommonGroundTabs.resultsTabLabel": "Résultats", "app.components.CommonGroundTabs.statementsTabLabel": "Affirmations", "app.components.CommunityMonitorModal.formError": "Une erreur s'est produite.", "app.components.CommunityMonitorModal.surveyDescription2": "Cette enquête continue a pour objectif de recueillir votre opinion sur la gouvernance et les services publics.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Prend <1 minute} one {Prend 1 minute} other {Prend # minutes}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Un e-mail avec un code de confirmation a été envoyé à {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Changez votre adresse e-mail.", "app.components.ConfirmationModal.codeInput": "Code", "app.components.ConfirmationModal.confirmationCodeSent": "Nouveau code envoyé", "app.components.ConfirmationModal.didntGetAnEmail": "Vous n'avez pas reçu d'e-mail ?", "app.components.ConfirmationModal.foundYourCode": "Vous avez trouvé votre code ?", "app.components.ConfirmationModal.goBack": "<PERSON><PERSON><PERSON>.", "app.components.ConfirmationModal.sendEmailWithCode": "Envoyer un e-mail avec le code", "app.components.ConfirmationModal.sendNewCode": "Envoyer un nouveau code.", "app.components.ConfirmationModal.verifyAndContinue": "Vérifier et continuer", "app.components.ConfirmationModal.wrongEmail": "Mauvaise adresse e-mail ?", "app.components.ConsentManager.Banner.accept": "Accepter", "app.components.ConsentManager.Banner.ariaButtonClose2": "Refuser la politique et fermer bannière", "app.components.ConsentManager.Banner.close": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.mainText": "Cette plateforme utilise des cookies conformément à notre {policyLink}.", "app.components.ConsentManager.Banner.manage": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.policyLink": "Politique de cookie", "app.components.ConsentManager.Banner.reject": "Refuser", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Publicité", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Nous utilisons ce suivi afin de personnaliser et mesurer l’efficacité de la publicité des campagnes de notre site Web. Nous ne montrerons aucune publicité sur cette plate-forme, mais les services suivants pourraient vous afficher des annonces personnalisées basées sur les pages que vous visitez sur notre site.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Permettre", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analytiques", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Nous utilisons ce suivi afin de mieux comprendre comment vous utilisez la plate-forme afin d’apprendre et d’améliorer votre navigation. Ces informations sont utilisées uniquement en masse analytique et en aucun cas individuellement.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Retour", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Annuler", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Ne pas autoriser", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Fonctionnel", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Nécessaire pour activer et contrôler les fonctionnalités de base du site Web. Certains outils répertoriés ici peuvent ne pas vous concerner. Veuillez consulter notre politique de cookie pour plus d’informations.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Requis", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Pour avoir une plateforme fonctionnelle, nous sauvegardons un cookie d'authentification si vous vous inscrivez, et la langue dans laquelle vous utilisez cette plateforme.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Enregistrer", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Vos préférences de cookie", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Outils ", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Clause de non-responsabilité concernant le contenu téléchargé", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "En téléchargeant du contenu, vous déclarez que ce contenu ne viole aucune réglementation ou droit de tiers, tels que les droits de propriété intellectuelle, les droits relatifs à la vie privée, les droits relatifs aux secrets commerciaux, etc. Par conséquent, en téléchargeant ce contenu, vous vous engagez à assumer l'entière et exclusive responsabilité de tous les dommages directs et indirects résultant du contenu téléchargé. De plus, vous vous engagez à indemniser le propriétaire de la plateforme et Go Vocal contre toute réclamation ou responsabilité à l'égard de tiers, ainsi que tous les coûts associés, qui pourraient découler ou résulter du contenu que vous avez téléchargé.", "app.components.ContentUploadDisclaimer.onAccept": "Je comprends", "app.components.ContentUploadDisclaimer.onCancel": "Annuler", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Dites-nous pourquoi", "app.components.CustomFieldsForm.addressInputAriaLabel": "<PERSON><PERSON> d'adresse", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Entrez une adresse...", "app.components.CustomFieldsForm.adminFieldTooltip": "Champ visible uniquement pour les administrateurs", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Les réponses à cette enquête sont anonymes.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Un polygone doit comporter au moins trois points.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "Au moins deux points sont requis pour une ligne.", "app.components.CustomFieldsForm.attachmentRequired": "Au moins un fichier joint est requis", "app.components.CustomFieldsForm.authorFieldLabel": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Commencez à taper pour rechercher par email ou nom d'utilisateur...", "app.components.CustomFieldsForm.back": "Retour", "app.components.CustomFieldsForm.budgetFieldLabel": "Budget", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Cliquez sur la carte pour commencer à dessiner, puis faites glisser les points pour les déplacer.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Cliquez sur la carte ou entrez une adresse ci-dessous pour ajouter votre réponse.", "app.components.CustomFieldsForm.confirm": "Confirmer", "app.components.CustomFieldsForm.descriptionMinLength": "La description doit comporter au minimum {min} caractères", "app.components.CustomFieldsForm.descriptionRequired": "La description est obligatoire", "app.components.CustomFieldsForm.fieldMaximumItems": "« {maxSelections, plural, one {Une seule option peut être sélectionnée} other {Un maximum de # options peuvent être sélectionnées}} pour le champ « {fieldName} »", "app.components.CustomFieldsForm.fieldMinimumItems": "Au moins {minSelections, plural, one {# option doit être sélectionnée} other {# options doivent être sélectionnées}} pour le champ « {fieldName} »", "app.components.CustomFieldsForm.fieldRequired": "Le champ « {fieldName} » est obligatoire", "app.components.CustomFieldsForm.fileSizeLimit": "La taille maximale des fichiers est de {maxFileSize} Mo.", "app.components.CustomFieldsForm.imageRequired": "L'image est obligatoire", "app.components.CustomFieldsForm.minimumCoordinates2": "Un minimum de {numPoints} points est requis.", "app.components.CustomFieldsForm.notPublic1": "*Cette réponse sera partagée uniquement avec les gestionnaires de projet, et non avec le public.", "app.components.CustomFieldsForm.otherArea": "Autre lieu", "app.components.CustomFieldsForm.progressBarLabel": "Progression", "app.components.CustomFieldsForm.removeAnswer": "Supprimer la réponse", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "* Sélectionnez autant d'options que vous le souhaitez", "app.components.CustomFieldsForm.selectBetween": "* Sélectionnez entre {minItems} et {maxItems} options", "app.components.CustomFieldsForm.selectExactly2": "* Sélectionnez exactement {selectExactly, plural, one {# option} other {# options}}", "app.components.CustomFieldsForm.selectMany": "*Choisissez-en autant que vous le souhaitez", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Appuyez sur la carte pour commencer à dessiner, puis faites glisser les points pour les déplacer.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Appuyez sur la carte pour commencer à dessiner.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Appuyez sur la carte pour ajouter votre réponse.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Appuyez sur la carte ou entrez une adresse ci-dessous pour ajouter votre réponse.", "app.components.CustomFieldsForm.tapToAddALine": "Appuyez pour ajouter une ligne", "app.components.CustomFieldsForm.tapToAddAPoint": "Cliquez pour ajouter un point", "app.components.CustomFieldsForm.tapToAddAnArea": "Appuyez pour délimiter une zone", "app.components.CustomFieldsForm.titleMaxLength": "Le titre doit comporter au maximum {max} caractères", "app.components.CustomFieldsForm.titleMinLength": "Le titre doit comporter au minimum {min} caractères", "app.components.CustomFieldsForm.titleRequired": "Le titre est obligatoire", "app.components.CustomFieldsForm.topicRequired": "Au moins une étiquette est nécessaire", "app.components.CustomFieldsForm.typeYourAnswer": "Saisissez votre réponse", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Il est nécessaire de taper votre réponse", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Téléchargez un fichier zip contenant un ou plusieurs fichiers de formes.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "Si la localisation n'apparaît pas parmi les options proposées lorsque vous tapez, vous pouvez ajouter des coordonnées valides au format « latitude, longitude » pour spécifier un emplacement précis (par exemple : -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Certains champs sont invalides. <PERSON><PERSON>i de corriger ces champs puis de réessayer.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Une erreur est survenue lors de l'enregistrement de vos données. Veuillez réessayer.", "app.components.ErrorBoundary.errorFormLabelClose": "<PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelComments": "Que s'est-il passé ?", "app.components.ErrorBoundary.errorFormLabelEmail": "Adresse e-mail", "app.components.ErrorBoundary.errorFormLabelName": "Nom", "app.components.ErrorBoundary.errorFormLabelSubmit": "Envoyer", "app.components.ErrorBoundary.errorFormSubtitle": "Le message a été transmis à notre équipe.", "app.components.ErrorBoundary.errorFormSubtitle2": "Si vous souhaitez aider, dites-nous ce qui s’est passé dessous.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Merci ! Votre commentaire a été envoyé.", "app.components.ErrorBoundary.errorFormTitle": "On dirait que quelque chose ne va pas.", "app.components.ErrorBoundary.genericErrorWithForm": "Une erreur s'est produite et le contenu n'a pas pu être affiché. Veuillez essayer à nouveau, ou {openForm} !", "app.components.ErrorBoundary.openFormText": "aidez-nous à comprendre ce qui s'est passé", "app.components.ErrorToast.budgetExceededError": "Vous n'avez plus assez de budget", "app.components.ErrorToast.votesExceededError": "Il ne vous reste plus suffisamment de votes", "app.components.EventAttendanceButton.forwardToFriend": "Partager avec un ami", "app.components.EventAttendanceButton.maxRegistrationsReached": "L'événement est complet. Il n'y a plus de places disponibles.", "app.components.EventAttendanceButton.register": "S'inscrire", "app.components.EventAttendanceButton.registered": "Inscrit(e)", "app.components.EventAttendanceButton.seeYouThere": "Au plaisir de vous y retrouver !", "app.components.EventAttendanceButton.seeYouThereName": "Au plaisir de vous y retrouver, {userFirstName} !", "app.components.EventCard.a11y_lessContentVisible": "Moins d'informations sur les événements sont devenues visibles.", "app.components.EventCard.a11y_moreContentVisible": "Moins d'informations sur les événements sont devenues visibles.", "app.components.EventCard.a11y_readMore": "En savoir plus sur l'événement « {eventTitle} ».", "app.components.EventCard.endsAt": "<PERSON><PERSON><PERSON> le", "app.components.EventCard.readMore": "Lire la suite", "app.components.EventCard.showLess": "<PERSON><PERSON> moins", "app.components.EventCard.showMore": "Voir plus", "app.components.EventCard.startsAt": "Commence le", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Prochains événements dans le cadre de ce projet", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Prochains événements durant cette phase", "app.components.FileUploader.a11y_file": "Fichier:", "app.components.FileUploader.a11y_filesToBeUploaded": "Fichiers à télécharger : {fileNames}", "app.components.FileUploader.a11y_noFiles": "<PERSON><PERSON><PERSON><PERSON>.", "app.components.FileUploader.a11y_removeFile": "Supprimer ce fichier", "app.components.FileUploader.fileInputDescription": "Cliquez pour sélectionner un fichier", "app.components.FileUploader.fileUploadLabel": "Pièces jointes (max. 50Mo)", "app.components.FileUploader.file_too_large2": "Les fichiers de plus de {maxSizeMb} Mo ne sont pas autorisés.", "app.components.FileUploader.incorrect_extension": "{fileName} n'est pas supporté par notre système, il ne sera pas chargé.", "app.components.FilterBoxes.a11y_allFilterSelected": "Filtre d'état sélectionné : tous", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, no {# contributions} one {# contribution} other {# contributions}}", "app.components.FilterBoxes.a11y_removeFilter": "En<PERSON>er le filtre", "app.components.FilterBoxes.a11y_selectedFilter": "Filtre d'état sélectionné : {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Sélectionné {numberOfSelectedTopics, plural, =0 {zéro filtre de sujet} one {un filtre de sujet} other {# Filtres de sujet}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Tous", "app.components.FilterBoxes.areas": "Trier par lieux", "app.components.FilterBoxes.inputs": "contributions", "app.components.FilterBoxes.noValuesFound": "Aucune valeur disponible.", "app.components.FilterBoxes.showLess": "<PERSON><PERSON> moins", "app.components.FilterBoxes.showTagsWithNumber": "<PERSON><PERSON> montrer ({numberTags})", "app.components.FilterBoxes.statusTitle": "Statut", "app.components.FilterBoxes.topicsTitle": "Étiquettes", "app.components.FiltersModal.filters": "Filtres", "app.components.FolderFolderCard.a11y_folderDescription": "Description du dossier :", "app.components.FolderFolderCard.a11y_folderTitle": "Titre du dossier :", "app.components.FolderFolderCard.archived": "Archivés", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projets} one {# projet} other {# projets}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "Le type de champ ne peut pas être modifié une fois que des réponses ont été soumises.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Type", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Sauvegarde automatique", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "La sauvegarde automatique est activée par défaut lorsque vous ouvrez l'éditeur de formulaire. Chaque fois que vous fermez le panneau de configuration d'un champ en utilisant le bouton « X », cela déclenchera automatiquement une sauvegarde.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "Trimestre", "app.components.GanttChart.timeRange.timeRangeMultiyear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON>", "app.components.GanttChart.today": "<PERSON><PERSON><PERSON>'hui", "app.components.GoBackButton.group.edit.goBack": "Retour", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Retour à la page précédente", "app.components.HookForm.Feedback.errorTitle": "Il y a un problème", "app.components.HookForm.Feedback.submissionError": "Essayez à nouveau. Si le problème persiste, contactez-nous", "app.components.HookForm.Feedback.submissionErrorTitle": "Il y a eu un problème de notre côté, désolé", "app.components.HookForm.Feedback.successMessage": "Modifications sauvegardées avec succès", "app.components.HookForm.PasswordInput.passwordLabel": "Mot de passe", "app.components.HorizontalScroll.scrollLeftLabel": "Faire défiler vers la gauche.", "app.components.HorizontalScroll.scrollRightLabel": "Faire défiler vers la droite.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "Les idées ont été chargées dans l'ordre suivant : {sortOder}.", "app.components.IdeaCards.filters": "Filtres", "app.components.IdeaCards.filters.mostDiscussed": "Les plus discutés", "app.components.IdeaCards.filters.newest": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.oldest": "Anciens", "app.components.IdeaCards.filters.popular": "Les plus likés", "app.components.IdeaCards.filters.random": "Aléatoire", "app.components.IdeaCards.filters.sortBy": "Trier par", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Tri modifié en : {currentSortType}", "app.components.IdeaCards.filters.trending": "Tendance", "app.components.IdeaCards.showMore": "Afficher plus d'idées", "app.components.IdeasMap.a11y_hideIdeaCard": "Cachez la carte des idées.", "app.components.IdeasMap.a11y_mapTitle": "Aperçu cartographique", "app.components.IdeasMap.clickOnMapToAdd": "Cliquez sur la carte pour ajouter votre contribution", "app.components.IdeasMap.clickOnMapToAddAdmin2": "En tant qu'administrateur, vous pouvez cliquer sur la carte pour ajouter une contribution, même si cette phase n'est pas active.", "app.components.IdeasMap.filters": "Filtres", "app.components.IdeasMap.multipleInputsAtLocation": "Plusieurs entrées sur ce site", "app.components.IdeasMap.noFilteredResults": "Les filtres que vous avez sélectionnés n'ont renvoyé aucun résultat", "app.components.IdeasMap.noResults": "Aucun résultat trouvé", "app.components.IdeasMap.or": "ou", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, aucune mention « Je n'aime pas ».} one {, 1 mention « Je n'aime pas ».} other {, # mentions « Je n'aime pas ».}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, aucune mention « J'aime ».} one {, 1 mention « J'aime ».} other {, # mentions « J'aime ».}}", "app.components.IdeasMap.signInLinkText": "vous connecter", "app.components.IdeasMap.signUpLinkText": "s'inscrire", "app.components.IdeasMap.submitIdea2": "Soumettre une contribution", "app.components.IdeasMap.tapOnMapToAdd": "Tapez sur la carte pour ajouter votre contribution", "app.components.IdeasMap.tapOnMapToAddAdmin2": "En tant qu'administrateur, vous pouvez cliquer sur la carte pour ajouter votre contribution, même si cette phase n'est pas active.", "app.components.IdeasMap.userInputs2": "Contributions des participants", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, aucun commentaire} one {, 1 commentaire} other {, # commentaires}}", "app.components.IdeasShow.bodyTitle": "Description", "app.components.IdeasShow.deletePost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.editPost": "Modifier", "app.components.IdeasShow.goBack": "Retour", "app.components.IdeasShow.moreOptions": "Plus d’options", "app.components.IdeasShow.or": "ou", "app.components.IdeasShow.proposedBudgetTitle": "Budget proposé", "app.components.IdeasShow.reportAsSpam": "Signaler", "app.components.IdeasShow.send": "Envoyer", "app.components.IdeasShow.skipSharing": "<PERSON><PERSON><PERSON>, je le ferai plus tard", "app.components.IdeasShowPage.signIn2": "Se connecter", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON><PERSON>, vous ne pouvez pas accéder à cette page. Vous devez vous connecter ou vous inscrire pour y accéder.", "app.components.LocationInput.noOptions": "Pas d'options", "app.components.Modal.closeWindow": "<PERSON><PERSON><PERSON> la fenêtre", "app.components.MultiSelect.clearButtonAction": "Effacer la sélection", "app.components.MultiSelect.clearSearchButtonAction": "Effacer la recherche", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Entrez une nouvelle adresse e-mail", "app.components.PageNotFound.goBackToHomePage": "Retour à la page d'accueil", "app.components.PageNotFound.notFoundTitle": "Page introuvable", "app.components.PageNotFound.pageNotFoundDescription": "La page demandée n'a pas été trouvée.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Fournir du contenu pour au moins une langue", "app.components.PagesForm.editContent": "Contenu", "app.components.PagesForm.fileUploadLabel": "Pièces jointes (max. 50Mo)", "app.components.PagesForm.fileUploadLabelTooltip": "Les pièces jointes seront visibles en bas de cette page.", "app.components.PagesForm.navbarItemTitle": "Nom dans la barre de navigation", "app.components.PagesForm.pageTitle": "Titre", "app.components.PagesForm.savePage": "Sauvegarder la page", "app.components.PagesForm.saveSuccess": "Page sauvegardée avec succès.", "app.components.PagesForm.titleMissingOneLanguageError": "Fournir un titre pour au moins une langue", "app.components.Pagination.back": "<PERSON> p<PERSON>", "app.components.Pagination.next": "<PERSON> suivante", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Vous avez dépen<PERSON>é {votesCast}, ce qui dépasse la limite de {votesLimit}. <PERSON><PERSON><PERSON><PERSON> retirer quelques options de votre panier et réessayer.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget}  restant", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Vous devez dépenser un minimum de {votesMinimum}  avant de pouvoir soumettre votre panier.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "V<PERSON> devez sélectionner au moins une option avant de pouvoir soumettre.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "V<PERSON> devez ajouter au moins une option à votre panier avant de pouvoir le soumettre.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {# crédit sur {totalNumberOfVotes} restant} one {# crédit sur {totalNumberOfVotes} restant} other {# crédits sur {totalNumberOfVotes} restants}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {# point sur {totalNumberOfVotes} restant} one {# point sur {totalNumberOfVotes} restant} other {# points sur {totalNumberOfVotes} restants}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {# jeton sur {totalNumberOfVotes} restant} one {# jeton sur {totalNumberOfVotes} restant} other {# jetons sur {totalNumberOfVotes} restants}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {# vote sur {totalNumberOfVotes} restant} one {# vote sur {totalNumberOfVotes} restant} other {# votes sur {totalNumberOfVotes} restants}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# votes} one {# vote} other {# votes}} cast", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Vous avez attribué {votesCast} votes, ce qui dépasse la limite de {votesLimit}. <PERSON><PERSON><PERSON><PERSON> retirer certains votes et réessayer.", "app.components.ParticipationCTABars.addInput": "Ajouter une contribution", "app.components.ParticipationCTABars.allocateBudget": "Allouez votre budget", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Votre budget a bien été soumis.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Ouvert à la participation", "app.components.ParticipationCTABars.poll": "Participez au sondage", "app.components.ParticipationCTABars.reviewDocument": "Révisez le document", "app.components.ParticipationCTABars.seeContributions": "Voir les contributions", "app.components.ParticipationCTABars.seeEvents3": "Voir les événements", "app.components.ParticipationCTABars.seeIdeas": "Voir les idées", "app.components.ParticipationCTABars.seeInitiatives": "Voir les initiatives", "app.components.ParticipationCTABars.seeIssues": "Voir les problèmes", "app.components.ParticipationCTABars.seeOptions": "Voir les options", "app.components.ParticipationCTABars.seePetitions": "Voir les pétitions", "app.components.ParticipationCTABars.seeProjects": "Voir les projets", "app.components.ParticipationCTABars.seeProposals": "Voir les propositions", "app.components.ParticipationCTABars.seeQuestions": "Voir les questions", "app.components.ParticipationCTABars.submit": "So<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.takeTheSurvey": "Répondre à l'enquête", "app.components.ParticipationCTABars.userHasParticipated": "Vous avez participé à ce projet.", "app.components.ParticipationCTABars.viewInputs": "Voir les contributions", "app.components.ParticipationCTABars.volunteer": "Participer", "app.components.ParticipationCTABars.votesCounter.vote": "vote", "app.components.ParticipationCTABars.votesCounter.votes": "votes", "app.components.PasswordInput.a11y_passwordHidden": "Mot de passe masqué", "app.components.PasswordInput.a11y_passwordVisible": "Mot de passe visible", "app.components.PasswordInput.a11y_strength1Password": "Niveau de sécurité du mot de passe insuffisant", "app.components.PasswordInput.a11y_strength2Password": "Niveau de sécurité du mot de passe bas", "app.components.PasswordInput.a11y_strength3Password": "Niveau de sécurité du mot de passe moyen", "app.components.PasswordInput.a11y_strength4Password": "Niveau de sécurité du mot de passe élevé", "app.components.PasswordInput.a11y_strength5Password": "Niveau de sécurité du mot de passe très élevé", "app.components.PasswordInput.hidePassword": "Masquer le mot de passe", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "Trop court (min. {minimumPasswordLength} caractères)", "app.components.PasswordInput.minimumPasswordLengthError": "Fournissez un mot de passe d'au moins {minimumPasswordLength} caractères", "app.components.PasswordInput.passwordEmptyError": "Entrez votre mot de passe", "app.components.PasswordInput.passwordStrengthTooltip1": "Pour renforcer la sécurité de votre mot de passe :", "app.components.PasswordInput.passwordStrengthTooltip2": "Utilisez une combinaison de lettres minuscules, de lettres majuscules, de chiffres, de caractères spéciaux et de ponctuations non consécutifs", "app.components.PasswordInput.passwordStrengthTooltip3": "<PERSON><PERSON><PERSON><PERSON> les mots courants ou faciles à deviner", "app.components.PasswordInput.passwordStrengthTooltip4": "Augmenter la longueur", "app.components.PasswordInput.showPassword": "Afficher le mot de passe", "app.components.PasswordInput.strength1Password": "Insuffisant", "app.components.PasswordInput.strength2Password": "Bas", "app.components.PasswordInput.strength3Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength4Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON><PERSON>", "app.components.PostCardsComponents.list": "Liste", "app.components.PostCardsComponents.map": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Ajouter une mise à jour officielle", "app.components.PostComponents.OfficialFeedback.cancel": "Annuler", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Êtes-vous sûr de vouloir supprimer ce statut ?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Modifier", "app.components.PostComponents.OfficialFeedback.lastEdition": "Dernière modification le {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Der<PERSON><PERSON> mise à jour : {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Officiel", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Choisissez comment les autres utilisateurs voient votre nom", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Mise à jour du nom de l'auteur", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Mise à jour du corps de texte", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Mises à jour officielles", "app.components.PostComponents.OfficialFeedback.postedOn": "<PERSON><PERSON> {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Publier", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Aff<PERSON><PERSON> les mises à jour précédentes", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Publier une mise à jour...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON><PERSON><PERSON>, une erreur est survenue", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Mettre à jour le message", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Votre mise à jour a été publiée avec succès !", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Soutenez ma suggestion « {postTitle} » à {postUrl} !", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Soutenez ma suggestion : {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Soutenez ma suggestion : {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Que pensez-vous de cette idée ? Votez et rejoignez la discussion sur {postUrl} pour que votre voix soit entendue !", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Soutenir mon idée : {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Soutenir mon idée : {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Que pensez-vous de cette proposition ? Votez et rejoignez la discussion sur {postUrl} pour que votre voix soit entendue !", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Soutenir ma proposition : {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Soutenez mon initiative : {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "J'ai publié un problème « {postTitle} » à {postUrl} !", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Je viens de publier un problème : {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Je viens de publier un problème : {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Soutenez mon option proposée « {postTitle} » à {postUrl} !", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Soutenez mon option proposée : {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Soutenez mon option : {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Soutenez ma pétition « {postTitle} » sur {postUrl} !", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Soutenez ma pétition : {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Soutenez ma pétition : {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Soutenez mon projet « {postTitle} » à {postUrl} !", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Soutenez mon projet : {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Soutenez mon projet : {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Soutenez ma proposition « {postTitle} » sur {postUrl} !", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Soutenir ma proposition : {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Je viens de poster une proposition pour {orgName} : {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Rejoignez la discussion sur cette question « {postTitle} » à {postUrl} !", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Rejo<PERSON><PERSON> la discussion : {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Rejo<PERSON><PERSON> la discussion : {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Votez pour {postTitle} sur", "app.components.PostComponents.linkToHomePage": "Liens vers la page d'accueil", "app.components.PostComponents.readMore": "Voir plus...", "app.components.PostComponents.topics": "Étiquettes", "app.components.ProjectArchivedIndicator.archivedProject": "Malheure<PERSON>ment, vous ne pouvez plus participer à ce projet, celui-ci a été archivé", "app.components.ProjectArchivedIndicator.previewProject": "Projet en brouillon :", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Visible uniquement par les modérateurs et les personnes disposant d'un lien d'aperçu.", "app.components.ProjectCard.a11y_projectDescription": "Description du projet :", "app.components.ProjectCard.a11y_projectTitle": "Titre du projet :", "app.components.ProjectCard.addYourOption": "Ajoutez votre option", "app.components.ProjectCard.allocateYourBudget": "Allouez votre budget", "app.components.ProjectCard.archived": "Archivé", "app.components.ProjectCard.comment": "Publier", "app.components.ProjectCard.contributeYourInput": "Apportez votre contribution", "app.components.ProjectCard.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.joinDiscussion": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.learnMore": "En savoir plus", "app.components.ProjectCard.reaction": "Réaction", "app.components.ProjectCard.readTheReport": "<PERSON>re le rapport", "app.components.ProjectCard.reviewDocument": "Réviser le document", "app.components.ProjectCard.submitAnIssue": "Soumettez un problème", "app.components.ProjectCard.submitYourIdea": "Déposez votre idée", "app.components.ProjectCard.submitYourInitiative": "Soumettez votre initiative", "app.components.ProjectCard.submitYourPetition": "Soumettez votre pétition", "app.components.ProjectCard.submitYourProject": "Soumettez votre projet", "app.components.ProjectCard.submitYourProposal": "Soumettez votre proposition", "app.components.ProjectCard.takeThePoll": "{tenant<PERSON><PERSON>, select, vitrysurseine {<PERSON><PERSON><PERSON><PERSON><PERSON> à l'enquête} other {Répondre au sondage}}", "app.components.ProjectCard.takeTheSurvey": "Répondre à l'enquête", "app.components.ProjectCard.viewTheContributions": "Voir les suggestions", "app.components.ProjectCard.viewTheIdeas": "Voir les idées", "app.components.ProjectCard.viewTheInitiatives": "Voir les initiatives", "app.components.ProjectCard.viewTheIssues": "Afficher les problèmes", "app.components.ProjectCard.viewTheOptions": "Voir les options", "app.components.ProjectCard.viewThePetitions": "Voir les pétitions", "app.components.ProjectCard.viewTheProjects": "Voir les projets", "app.components.ProjectCard.viewTheProposals": "Voir les propositions", "app.components.ProjectCard.viewTheQuestions": "Voir les questions", "app.components.ProjectCard.vote": "Votez", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# commentaires} other {# commentaires}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, no {# suggestions} one {# suggestion} other {# suggestions}}", "app.components.ProjectCard.xIdeas": "{IdeasCount, plural, =0 {ingen ideer} one {# idé} other {# ideer}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# proposition} one {# proposition} other {# propositions}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, no {# issues} one {# issue} other {# issues}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, no {# options} one {# option} other {# options}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# pétition} one {# pétition} other {# pétitions}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural,no {# projets} one {# projet} other {# projets}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# proposition} one {# proposition} other {# propositions}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, no {# questions} one {# question} other {# questions}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# commentaire} one {# commentaire} other {# commentaires}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# contribution} one {# contribution} other {# contributions}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projets} one {# projet} other {# projets}}", "app.components.ProjectFolderCards.components.Topbar.all": "Tous", "app.components.ProjectFolderCards.components.Topbar.archived": "Archivés", "app.components.ProjectFolderCards.components.Topbar.draft": "Brouillon", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filtrer par", "app.components.ProjectFolderCards.components.Topbar.published2": "<PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Étiquette", "app.components.ProjectFolderCards.noProjectYet": "Il n'y a pas encore de projet", "app.components.ProjectFolderCards.noProjectsAvailable": "Aucun projet disponible", "app.components.ProjectFolderCards.showMore": "Voir plus", "app.components.ProjectFolderCards.stayTuned": "Restez à l’affût, un projet va apparaître prochainement.", "app.components.ProjectFolderCards.tryChangingFilters": "Essayez de changer les filtres sélectionnés.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Également utilisé dans ces villes", "app.components.ProjectTemplatePreview.copied": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.copyLink": "Copier le lien", "app.components.QuillEditor.alignCenter": "<PERSON><PERSON> le texte", "app.components.QuillEditor.alignLeft": "<PERSON><PERSON><PERSON> à gauche", "app.components.QuillEditor.alignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "app.components.QuillEditor.bold": "Gras", "app.components.QuillEditor.clean": "Supprimer le formatage", "app.components.QuillEditor.customLink": "Ajouter un bouton", "app.components.QuillEditor.customLinkPrompt": "Entrer le lien :", "app.components.QuillEditor.edit": "Editer", "app.components.QuillEditor.image": "Charger une image", "app.components.QuillEditor.imageAltPlaceholder": "Brève description de l'image", "app.components.QuillEditor.italic": "Italique", "app.components.QuillEditor.link": "Ajouter un lien", "app.components.QuillEditor.linkPrompt": "Entrer le lien :", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Liste ordonnée", "app.components.QuillEditor.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.save": "Enregistrer", "app.components.QuillEditor.subtitle": "Sous-titre", "app.components.QuillEditor.title": "Titre", "app.components.QuillEditor.unorderedList": "Liste non ordonnée", "app.components.QuillEditor.video": "Ajouter une vidéo", "app.components.QuillEditor.videoPrompt": "Entrer la vidéo :", "app.components.QuillEditor.visitPrompt": "Visiter le lien :", "app.components.ReactionControl.completeProfileToReact": "Complétez votre profil pour réagir", "app.components.ReactionControl.dislike": "Je n'aime pas", "app.components.ReactionControl.dislikingDisabledMaxReached": "Vous avez atteint le nombre maximum de dislikes sur {projectName}", "app.components.ReactionControl.like": "<PERSON>'aime", "app.components.ReactionControl.likingDisabledMaxReached": "Vous avez atteint le nombre maximum de votes dans le projet '{projectName}'", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Les réactions seront activées dès le début de cette phase", "app.components.ReactionControl.reactingDisabledPhaseOver": "Il n'est plus possible de réagir dans cette phase", "app.components.ReactionControl.reactingDisabledProjectInactive": "Vous ne pouvez plus réagir aux idées du projet {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Les réactions ne sont actuellement pas activées pour ce projet", "app.components.ReactionControl.reactingNotPermitted": "Les réactions ne sont activées que pour certains groupes", "app.components.ReactionControl.reactingNotSignedIn": "Connectez-vous pour réagir.", "app.components.ReactionControl.reactingPossibleLater": "Les réactions commenceront le {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Vérifiez votre identité pour pouvoir réagir.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Date de l'événement : du {startDate} à {startTime} au {endDate} à {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Date de l'événement : {eventDate} de {startTime} à {endTime}.", "app.components.Sharing.linkCopied": "<PERSON>n copié", "app.components.Sharing.or": "ou", "app.components.Sharing.share": "Partager", "app.components.Sharing.shareByEmail": "Partager par e-mail ", "app.components.Sharing.shareByLink": "Copier le lien", "app.components.Sharing.shareOnFacebook": "Partager sur Facebook", "app.components.Sharing.shareOnTwitter": "Partager sur Twitter", "app.components.Sharing.shareThisEvent": "Partager cet événement", "app.components.Sharing.shareThisFolder": "Partager", "app.components.Sharing.shareThisProject": "Partager ce projet", "app.components.Sharing.shareViaMessenger": "Partagez via Messenger", "app.components.Sharing.shareViaWhatsApp": "Partagez via WhatsApp", "app.components.SideModal.closeButtonAria": "<PERSON><PERSON><PERSON>", "app.components.StatusModule.futurePhase": "Vous visualisez une phase qui n'a pas encore débuté. Vous pourrez participer lorsque celle-ci aura commencé.", "app.components.StatusModule.modifyYourSubmission1": "Modifier votre soumission", "app.components.StatusModule.submittedUntil3": "Vous pouvez voter jusqu'au", "app.components.TopicsPicker.numberOfSelectedTopics": "Sélectionné {numberOfSelectedTopics, plural, =0 {zéro sujet} one {un sujet} other {# topics}}. {selectedTopicNames}> <notr", "app.components.UI.FullscreenImage.expandImage": "Agrandir l'image", "app.components.UI.MoreActionsMenu.moreOptions": "Plus d’options", "app.components.UI.MoreActionsMenu.showMoreActions": "Afficher d'autres actions", "app.components.UI.NewLabel.new": "NOUVEAU", "app.components.UI.PhaseFilter.noAppropriatePhases": "Aucune phase compatible n'a été trouvée pour ce projet", "app.components.UI.RemoveImageButton.a11y_removeImage": "<PERSON><PERSON><PERSON><PERSON>", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Unauthorized.additionalInformationRequired": "Des informations supplémentaires sont nécessaires pour que vous puissiez participer.", "app.components.Unauthorized.completeProfile": "Complétez votre profil", "app.components.Unauthorized.completeProfileTitle": "Complétez votre profil pour participer", "app.components.Unauthorized.noPermission": "Vous n'avez pas le droit de consulter cette page", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON><PERSON>, vous n'êtes pas autorisé à accéder à cette page.", "app.components.Upload.errorImageMaxSizeExceeded": "L'image que vous avez sélectionnée est plus grande que {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "L'image sélectionnée dépasse la taille maximum autorisée de {maxFileSize} Mo par image", "app.components.Upload.onlyOneImage": "Vous ne pouvez télécharger qu'une image", "app.components.Upload.onlyXImages": "Vous ne pouvez charger que {maxItemsCount} images", "app.components.Upload.remaining": "restant(s)", "app.components.Upload.uploadImageLabel": "Sélectionner une image (max. {maxImageSizeInMb}Mo)", "app.components.Upload.uploadMultipleImagesLabel": "Sélectionnez une ou plusieurs images", "app.components.UpsellTooltip.tooltipContent": "Cette fonctionnalité n'est pas incluse dans votre plan actuel. Adressez-vous à votre Spécialiste en participation Go Vocal ou à votre administrateur pour la débloquer.", "app.components.UserName.anonymous": "Anonyme", "app.components.UserName.anonymousTooltip2": "Cet utilisateur a décidé d'anonymiser sa contribution", "app.components.UserName.authorWithNoNameTooltip": "Comme vous n'avez pas renseigné de nom, nous en avons choisi un automatiquement pour vous. Vous pouvez le modifier dans votre profil.", "app.components.UserName.deletedUser": "auteur inconnu", "app.components.UserName.verified": "Vérifié", "app.components.VerificationModal.verifyAuth0": "Vérifier avec NemID", "app.components.VerificationModal.verifyBOSA": "Vérifier avec itsme ou eID", "app.components.VerificationModal.verifyBosaFas": "Vérifier avec itsme ou eID", "app.components.VerificationModal.verifyClaveUnica": "Vérifier avec Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "Vérifier avec Fake SSO", "app.components.VerificationModal.verifyIdAustria": "Vérifier avec ID Austria", "app.components.VerificationModal.verifyKeycloak": "Vérifier avec ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Vérifier avec MitID", "app.components.VerificationModal.verifyTwoday2": "Vérifier avec BankID ou Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Vérifiez votre identité", "app.components.VoteControl.budgetingFutureEnabled": "Vous pouvez allouer votre budget à partir du {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "La budgétisation participative est actuellement désactivée.", "app.components.VoteControl.budgetingNotPossible": "Il n'est pas possible d'apporter des modifications à votre budget pour le moment.", "app.components.VoteControl.budgetingNotVerified": "Veuillez {verifyAccountLink} pour continuer.", "app.components.VoteInputs._shared.currencyLeft1": "Il vous reste {budgetLeft} / {totalBudget}", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Il {votesLeft, plural, =0 {vous reste 0 crédit} one {vous reste 1 crédit} other {vous reste # crédits}} sur {totalNumberOfVotes}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Il {votesLeft, plural, =0 {vous reste 0 point} one {vous reste 1 point} other {vous reste # points}} sur {totalNumberOfVotes}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Il {votesLeft, plural, =0 {vous reste 0 jeton} one {vous reste 1 jeton} other {vous reste # jetons}} sur {totalNumberOfVotes}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Il {votesLeft, plural, =0 {vous reste 0 vote} one {vous reste 1 votes} other {vous reste # votes}} sur {totalNumberOfVotes}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Vous avez déjà soumis votre budget. Pour le modifier, cliquez sur « Modifier votre soumission ».", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Vous avez déjà soumis votre budget. Pour le modifier, retournez sur la page du projet et cliquez sur « Modifier votre soumission ».", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Vous ne pouvez pas proposer de budget, car cette phase n'est pas active.", "app.components.VoteInputs.single.youHaveVotedForX2": "Vous avez voté pour {votes, plural, =0 {# options} one {# option} other {# options}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON><PERSON> signifie que vous perdrez toutes les données associées à cette contribution, comme les commentaires, les réactions et les votes. Cette action ne peut pas être annulée.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Êtes-vous sûr de vouloir supprimer cette contribution ?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "Annuler", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Confirmer", "app.components.admin.SlugInput.resultingURL": "URL résultant", "app.components.admin.SlugInput.slugTooltip": "Le slug est l'ensemble unique de mots qui se trouve à la fin de l'adresse web, ou de l'URL, d'une page.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "Si vous modifiez l'URL, les liens vers la page utilisant l'ancienne URL ne fonctionneront plus.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "Ajouter une condition", "app.components.admin.UserFilterConditions.field_email": "Adresse e-mail", "app.components.admin.UserFilterConditions.field_event_attendance": "Inscriptions aux événements", "app.components.admin.UserFilterConditions.field_follow": "Suivre", "app.components.admin.UserFilterConditions.field_lives_in": "Habite à", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Enquête de l'Observatoire de communauté", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Interaction avec une entrée avec statut", "app.components.admin.UserFilterConditions.field_participated_in_project": "A participé au projet", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Contribué à une étiquette", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Date d'inscription", "app.components.admin.UserFilterConditions.field_role": "Statut", "app.components.admin.UserFilterConditions.field_verified": "Vérification", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Idéation", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Propositions", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "n'est inscrit à aucun de ces événements", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "n'est inscrit à aucun événement", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "est inscrit à l'un de ces événements", "app.components.admin.UserFilterConditions.predicate_attends_something": "est inscrit à au moins un événement", "app.components.admin.UserFilterConditions.predicate_begins_with": "commence par", "app.components.admin.UserFilterConditions.predicate_commented_in": "commenté", "app.components.admin.UserFilterConditions.predicate_contains": "contient", "app.components.admin.UserFilterConditions.predicate_ends_on": "se termine le", "app.components.admin.UserFilterConditions.predicate_has_value": "est égal à", "app.components.admin.UserFilterConditions.predicate_in": "effectué tout type d'action", "app.components.admin.UserFilterConditions.predicate_is": "est", "app.components.admin.UserFilterConditions.predicate_is_admin": "est un administrateur", "app.components.admin.UserFilterConditions.predicate_is_after": "est postérieure à", "app.components.admin.UserFilterConditions.predicate_is_before": "est antérieure à", "app.components.admin.UserFilterConditions.predicate_is_checked": "est coché", "app.components.admin.UserFilterConditions.predicate_is_empty": "est vide", "app.components.admin.UserFilterConditions.predicate_is_equal": "est égal à", "app.components.admin.UserFilterConditions.predicate_is_exactly": "est exactement égal", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "est plus grand que", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "est plus grand ou égal à", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "est un utilisateur normal", "app.components.admin.UserFilterConditions.predicate_is_not_area": "ne suit pas la zone géographique", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "ne suit pas le dossier", "app.components.admin.UserFilterConditions.predicate_is_not_input": "exclut la contribution", "app.components.admin.UserFilterConditions.predicate_is_not_project": "ne suit pas le projet", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "ne suit pas le sujet", "app.components.admin.UserFilterConditions.predicate_is_one_of": "est l'une des", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "suit au moins une des zones géographiques", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "suit au moins un des dossiers", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "une des contributions", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "suit au moins un des projets", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "l'un des thèmes", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "est un administrateur projet", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "est plus petit que", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "est plus petit ou égal à", "app.components.admin.UserFilterConditions.predicate_is_verified": "est vérifié", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "ne commence pas par", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "n'a pas commenté", "app.components.admin.UserFilterConditions.predicate_not_contains": "ne contient pas", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "ne finit pas par", "app.components.admin.UserFilterConditions.predicate_not_has_value": "n'est pas égal à", "app.components.admin.UserFilterConditions.predicate_not_in": "n'a pas contribué", "app.components.admin.UserFilterConditions.predicate_not_is": "n'est pas", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "n'est pas administrateur", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "n'est pas coché", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "n'est pas vide", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "n'est pas", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "n’est pas un utilisateur normal", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "n'est pas une des", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "n’est pas un administrateur projet", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "n'est pas vérifié", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "n'a pas posté de contribution", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "n'a pas réagi au commentaire", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "n'a pas réagi à la contribution", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "n'est inscrit à aucun événement de ces projets", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "n'a pas participé à l'enquête", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "pas volontaire", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "n'ont pas voté", "app.components.admin.UserFilterConditions.predicate_nothing": "ne suit rien", "app.components.admin.UserFilterConditions.predicate_posted_input": "a publié une contribution", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "a réagi au commentaire", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "a réagi à la contribution", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "est inscrit à un événement du projet", "app.components.admin.UserFilterConditions.predicate_something": "suit quelque chose", "app.components.admin.UserFilterConditions.predicate_taken_survey": "a participé à l'enquête", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "volontaire", "app.components.admin.UserFilterConditions.predicate_voted_in3": "ont pris part au vote", "app.components.admin.UserFilterConditions.rulesFormLabelField": "A", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "B", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "C", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Vous ne recevrez pas de notifications concernant vos contributions anonymes", "app.components.anonymousParticipationModal.cancel": "Annuler", "app.components.anonymousParticipationModal.continue": "<PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.participateAnonymously": "Participer de façon anonyme", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "<PERSON><PERSON> <b>masquera votre profil</b> aux administrateurs, gestionnaires et autres utilisateurs de la plateforme pour cette contribution, de sorte que personne ne pourra faire le lien entre celle-ci et vous. Les contributions anonymes ne peuvent pas être modifiées ou supprimées et sont considérées comme définitives.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Rendre notre plateforme sûre pour chaque utilisateur est une priorité absolue pour nous. Les mots ont de l'importance, alors prêtez-y attention et veillez à être bienveillants les uns envers les autres.", "app.components.avatar.titleForAccessibility": "Profil de {fullName}", "app.components.customFields.mapInput.removeAnswer": "Supprimer la réponse", "app.components.customFields.mapInput.undo": "Annuler", "app.components.customFields.mapInput.undoLastPoint": "<PERSON><PERSON>r le dernier point", "app.components.followUnfollow.follow": "Suivre", "app.components.followUnfollow.followADiscussion": "<PERSON><PERSON><PERSON>", "app.components.followUnfollow.followTooltipInputPage2": "En suivant, vous recevrez des mises à jour par e-mail concernant les changements de statut, les mises à jour officielles et les commentaires. Vous pouvez {unsubscribeLink} à tout moment.", "app.components.followUnfollow.followTooltipProjects2": "En suivant, vous serez informé(e) par e-mail des évolutions du projet. Vous pouvez {unsubscribeLink} à tout moment.", "app.components.followUnfollow.unFollow": "Ne plus suivre", "app.components.followUnfollow.unsubscribe": "vous d<PERSON><PERSON><PERSON>ner", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "nos directives", "app.components.form.ErrorDisplay.next": "Suivant", "app.components.form.ErrorDisplay.previous": "Précédent", "app.components.form.ErrorDisplay.save": "Enregistrer", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Commencez à taper pour rechercher par email ou nom d'utilisateur...", "app.components.form.anonymousSurveyMessage2": "Les réponses à cette enquête sont anonymes.", "app.components.form.backToInputManager": "Retour au gestionnaire de contributions", "app.components.form.backToProject": "Retour au projet", "app.components.form.components.controls.mapInput.removeAnswer": "Supprimer la réponse", "app.components.form.components.controls.mapInput.undo": "Annuler", "app.components.form.components.controls.mapInput.undoLastPoint": "<PERSON><PERSON>r le dernier point", "app.components.form.controls.addressInputAriaLabel": "<PERSON><PERSON> d'adresse", "app.components.form.controls.addressInputPlaceholder6": "Entrez une adresse...", "app.components.form.controls.adminFieldTooltip": "Champ visible uniquement pour les administrateurs", "app.components.form.controls.allStatementsError": "Une réponse doit être sélectionnée pour chacune des affirmations.", "app.components.form.controls.back": "Retour", "app.components.form.controls.clearAll": "Tout effacer", "app.components.form.controls.clearAllScreenreader": "Effacer toutes les réponses de la question matricielle ci-dessus", "app.components.form.controls.clickOnMapMultipleToAdd3": "Cliquez sur la carte pour commencer à dessiner. Ensuite, faites glisser les points pour les déplacer.", "app.components.form.controls.clickOnMapToAddOrType": "Cliquez sur la carte ou entrez une adresse ci-dessous pour ajouter votre réponse.", "app.components.form.controls.confirm": "Confirmer", "app.components.form.controls.cosponsorsPlaceholder": "Commencez à taper un nom pour lancer une recherche", "app.components.form.controls.currentRank": "Classement actuel :", "app.components.form.controls.minimumCoordinates2": "Un minimum de {numPoints} points est requis.", "app.components.form.controls.noRankSelected": "Aucun classement attribué", "app.components.form.controls.notPublic1": "*Cette réponse sera partagée uniquement avec les gestionnaires de projet, et non avec le public.", "app.components.form.controls.optionalParentheses": "(facultatif)", "app.components.form.controls.rankingInstructions": "Glissez-<PERSON><PERSON><PERSON>z les options pour les classer.", "app.components.form.controls.selectAsManyAsYouLike": "* Sélectionnez autant d'options que vous le souhaitez", "app.components.form.controls.selectBetween": "* Sélectionnez entre {minItems} et {maxItems} options", "app.components.form.controls.selectExactly2": "* Sélectionnez exactement {selectExactly, plural, one {# option} other {# options}}", "app.components.form.controls.selectMany": "*Choisissez-en autant que vous le souhaitez", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Appuyez sur la carte pour commencer à dessiner. Ensuite, faites glisser les points pour les déplacer.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Appuyez sur la carte pour commencer à dessiner.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Appuyez sur la carte pour ajouter votre réponse.", "app.components.form.controls.tapOnMapToAddOrType": "Appuyez sur la carte ou entrez une adresse ci-dessous pour ajouter votre réponse.", "app.components.form.controls.tapToAddALine": "Appuyez pour ajouter une ligne", "app.components.form.controls.tapToAddAPoint": "Cliquez pour ajouter un point", "app.components.form.controls.tapToAddAnArea": "Appuyez pour délimiter une zone", "app.components.form.controls.uploadShapefileInstructions": "* Téléchargez un fichier zip contenant un ou plusieurs fichiers de formes.", "app.components.form.controls.validCordinatesTooltip2": "Si la localisation n'apparaît pas parmi les options proposées lorsque vous tapez, vous pouvez ajouter des coordonnées valides au format « latitude, longitude » pour spécifier un emplacement précis (par exemple : -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} sur {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} sur {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} sur {total}, où {maxValue} est {maxLabel}", "app.components.form.error": "<PERSON><PERSON><PERSON>", "app.components.form.locationGoogleUnavailable": "Impossible de charger le champ de localisation fourni par Google Maps.", "app.components.form.progressBarLabel": "Progression de l'enquête", "app.components.form.submit": "Envoyer", "app.components.form.submitApiError": "Un problème est survenu lors de la soumission du formulaire. Veuillez vérifier s'il y a des erreurs et réessayer.", "app.components.form.verifiedBlocked": "Vous ne pouvez pas modifier ce champ car il contient des informations vérifiées", "app.components.formBuilder.Page": "Page", "app.components.formBuilder.accessibilityStatement": "déclaration d'accessibilité", "app.components.formBuilder.addAnswer": "Ajouter une réponse", "app.components.formBuilder.addStatement": "Ajouter une affirmation", "app.components.formBuilder.agree": "D'accord", "app.components.formBuilder.ai1": "IA", "app.components.formBuilder.aiUpsellText1": "Si vous avez accès à notre package IA, vous pourrez résumer et catégoriser les réponses textuelles à l'aide de l'IA", "app.components.formBuilder.askFollowUpToggleLabel": "Demandez un suivi", "app.components.formBuilder.bad": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.buttonLabel": "Texte du bouton", "app.components.formBuilder.buttonLink": "<PERSON>n du bouton", "app.components.formBuilder.cancelLeaveBuilderButtonText": "Annuler", "app.components.formBuilder.category": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.chooseMany": "Choix multiple", "app.components.formBuilder.chooseOne": "Choisissez une option", "app.components.formBuilder.close": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.closed": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.configureMap": "Configurer la carte", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON><PERSON>, je veux quitter", "app.components.formBuilder.content": "Contenu", "app.components.formBuilder.continuePageLabel": "Continue vers", "app.components.formBuilder.cosponsors": "Coparrainants", "app.components.formBuilder.default": "<PERSON><PERSON> <PERSON><PERSON>", "app.components.formBuilder.defaultContent": "Contenu par défaut", "app.components.formBuilder.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.description": "Description", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Ceci a déjà été ajouté dans le formulaire. Le contenu par défaut ne peut être utilisé qu'une seule fois.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "L'ajout de contenu personnalisé n'est pas inclus dans votre licence actuelle. Contactez votre Spécialiste en participation pour en savoir plus.", "app.components.formBuilder.disagree": "Pas d'accord", "app.components.formBuilder.displayAsDropdown": "Afficher sous forme de menu déroulant", "app.components.formBuilder.displayAsDropdownTooltip": "Affiche les options dans un menu déroulant. Recommandé lorsqu'il y a beaucoup d'options.", "app.components.formBuilder.done": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.drawArea": "Zone de dessin", "app.components.formBuilder.drawRoute": "<PERSON><PERSON>er un tracé", "app.components.formBuilder.dropPin": "Placer un repère", "app.components.formBuilder.editButtonLabel": "Modifier", "app.components.formBuilder.emptyImageOptionError": "Fournissez au moins une réponse. Veuillez noter que chaque réponse doit avoir un titre.", "app.components.formBuilder.emptyOptionError": "Fournissez au moins 1 réponse", "app.components.formBuilder.emptyStatementError": "Fournissez au moins 1 affirmation", "app.components.formBuilder.emptyTitleError": "Donnez un titre à la question", "app.components.formBuilder.emptyTitleMessage": "Veuillez fournir un titre pour toutes les réponses", "app.components.formBuilder.emptyTitleStatementMessage": "Fournissez un titre pour toutes les affirmations", "app.components.formBuilder.enable": "Activer", "app.components.formBuilder.errorMessage": "Il y a un problème, veuil<PERSON>z le résoudre pour pouvoir enregistrer vos modifications", "app.components.formBuilder.fieldGroup.description": "Description (facultatif)", "app.components.formBuilder.fieldGroup.title": "Titre (facultatif)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Actuellement, les réponses à ces questions ne sont disponibles que dans le fichier Excel exporté sur Input Manager, et ne sont pas visibles pour les utilisateurs.", "app.components.formBuilder.fieldLabel": "Choix de réponse<PERSON>", "app.components.formBuilder.fieldLabelStatement": "Affirmations", "app.components.formBuilder.fileUpload": "Téléchargement de fichier", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Page avec cartographie", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Intégrez une carte pour donner du contexte ou posez des questions de localisation aux participants.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Pour une expérience optimale, nous vous déconseillons d'ajouter d'autres questions cartographiques (tracés linéaires, tracés de polygones, placer un repère,...) aux pages avec cartographie.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Page normale", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Les fonctionnalités cartographiques des enquêtes ne sont pas incluses dans votre licence actuelle. Contactez votre Spécialiste en participation pour en savoir plus.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Type de page", "app.components.formBuilder.formEnd": "Fin du formulaire", "app.components.formBuilder.formField.cancelDeleteButtonText": "Annuler", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "<PERSON><PERSON>, supprimer la page", "app.components.formBuilder.formField.copyNoun": "<PERSON><PERSON>", "app.components.formBuilder.formField.copyVerb": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "La suppression de cette page entraînera également la suppression de la logique qui lui est associée. Êtes-vous sûr de vouloir la supprimer ?", "app.components.formBuilder.formField.deleteResultsInfo": "Cette action est irréversible", "app.components.formBuilder.goToPageInputLabel": "P<PERSON>s la page suivante est :", "app.components.formBuilder.good": "<PERSON>", "app.components.formBuilder.helmetTitle": "Éditeur de formulaires", "app.components.formBuilder.imageFileUpload": "Téléchargement d’image", "app.components.formBuilder.invalidLogicBadgeMessage": "Logique invalide", "app.components.formBuilder.labels2": "Étiquettes (optionnel)", "app.components.formBuilder.labelsTooltipContent2": "Attribuez une étiquette descriptive à chaque valeur de l'échelle linéaire.", "app.components.formBuilder.lastPage": "Fin", "app.components.formBuilder.layout": "Mise en page", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Êtes-vous sûr de vouloir quitter ?", "app.components.formBuilder.leaveBuilderText": "Vos dernières modifications n'ont pas été sauvegardées. Si vous quittez la page, ces modifications seront perdues.", "app.components.formBuilder.limitAnswersTooltip": "Lorsque cette option est activée, les répondants doivent sélectionner le nombre de réponses spécifié pour continuer.", "app.components.formBuilder.limitNumberAnswers": "Limiter le nombre de réponses", "app.components.formBuilder.linePolygonMapWarning2": "Les tracés linéaires et de polygones peuvent ne pas répondre aux normes d'accessibilité. Pour plus d'informations, consultez le site {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Échelle linéaire", "app.components.formBuilder.locationDescription": "Emplacement", "app.components.formBuilder.logic": "Logique", "app.components.formBuilder.logicAnyOtherAnswer": "Toute autre réponse", "app.components.formBuilder.logicConflicts.conflictingLogic": "Règles logiques incompatibles", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Cette page contient des questions qui renvoient à des pages différentes. Si les participants répondent à plusieurs de ces questions, la page la plus avancée sera directement affichée. Assurez-vous que cela correspond au comportement souhaité.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Cette page comporte plusieurs types de règles logiques : pour les questions à choix multiples, au niveau de la page elle-même, et entre les questions. Si plusieurs règles sont activées par les réponses du participant, la règle associée à une question qui renvoie à la page la plus avancée sera appliquée. Assurez-vous que cela correspond au comportement souhaité.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Cette page contient une question à choix multiples dont les options renvoient à des pages différentes. Si les participants sélectionnent plusieurs options, la page la plus avancée sera directement affichée. Assurez-vous que cela correspond au comportement souhaité.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Cette page contient une question à choix multiples dont les options renvoient à des pages différentes, ainsi que des questions qui renvoient à d'autres pages. Si plusieurs règles sont activées par les réponses du participant, la page la plus avancée sera directement affichée. Assurez-vous que cela correspond au comportement souhaité.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Cette page contient une question à choix multiples dont les options renvoient à des pages différentes, et comporte des règles logiques à la fois au niveau de la page et des questions. Si plusieurs règles sont activées par les réponses du participant, la règle associée à une question qui renvoie à la page la plus avancée sera appliquée. Assurez-vous que cela correspond au comportement souhaité.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Cette page comporte des règles logiques à la fois au niveau de la page et des questions. Les règles logiques des questions prévaudront sur celle de la page. Assurez-vous que cela correspond au comportement souhaité.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Cette page comporte des règles logiques à la fois au niveau de la page et des questions, et plusieurs questions renvoient à des pages différentes. Si plusieurs règles sont activées par les réponses du participant, la règle associée à une question qui renvoie à la page la plus avancée sera appliquée. Assurez-vous que cela correspond au comportement souhaité.", "app.components.formBuilder.logicNoAnswer2": "Pas de réponse", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Si une autre réponse", "app.components.formBuilder.logicPanelNoAnswer": "Si non répondue", "app.components.formBuilder.logicValidationError": "Logic ne peut pas créer de liens vers des pages antérieures", "app.components.formBuilder.longAnswer": "Réponse longue", "app.components.formBuilder.mapConfiguration": "Configuration de la carte", "app.components.formBuilder.mapping": "Cartographie", "app.components.formBuilder.mappingNotInCurrentLicense": "Les fonctionnalités cartographiques des enquêtes ne sont pas incluses dans votre licence actuelle. Contactez votre Spécialiste en participation pour en savoir plus.", "app.components.formBuilder.matrix": "<PERSON><PERSON>", "app.components.formBuilder.matrixSettings.columns": "Colonnes", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "Choix multiples", "app.components.formBuilder.multipleChoiceHelperText": "Si les participants sélectionnent plusieurs options qui renvoient à des pages différentes, la page la plus avancée sera directement affichée. Assurez-vous que cela correspond au comportement souhaité.", "app.components.formBuilder.multipleChoiceImage": "Choix unique (Images)", "app.components.formBuilder.multiselect.maximum": "Maximum", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "Neutre", "app.components.formBuilder.newField": "Nouveau champ", "app.components.formBuilder.number": "Nombre", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.optional": "Facultatif", "app.components.formBuilder.other": "<PERSON><PERSON>", "app.components.formBuilder.otherOption": "\"Option \"Autre", "app.components.formBuilder.otherOptionTooltip": "Permettre aux participants de saisir une réponse personnalisée si les options proposées ne leur conviennent pas", "app.components.formBuilder.page": "Page", "app.components.formBuilder.pageCannotBeDeleted": "Cette page ne peut pas être supprimée.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Cette page ne peut être supprimée et ne permet pas l'ajout de champs supplémentaires.", "app.components.formBuilder.pageRuleLabel": "La page suivante est :", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Si aucune règle logique n'est ajoutée, le formulaire suivra son cours normal. Si une page et ses questions comportent des règles logiques, celles associées aux questions prévaudront. Assurez-vous que cela correspond au comportement souhaité pour votre formulaire. Pour plus d'informations, consultez {supportPageLink}", "app.components.formBuilder.preview": "Aperçu :", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Le champ « co-parrains » n'est pas affiché dans le PDF téléchargé et n'est pas pris en charge par l'importation via FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "Dans le PDF téléchargé, il est clairement indiqué que les questions liées au téléchargement de fichiers ne sont pas prises en charge par l'importation via FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Les questions cartographiques apparaissent dans le PDF téléchargé, mais les couches ne seront pas visibles. Ces questions ne sont pas prises en charge lors de l’importation via FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Les questions matricielles sont affichées sur le PDF téléchargé, mais leur importation via FormSync n'est actuellement pas prise en charge.", "app.components.formBuilder.printSupportTooltip.page": "Les titres et descriptions de page s'affichent comme des en-têtes de section dans le PDF téléchargé.", "app.components.formBuilder.printSupportTooltip.ranking": "Les questions de classement sont affichées dans le PDF téléchargé, mais leur importation via FormSync n'est pas encore prise en charge.", "app.components.formBuilder.printSupportTooltip.topics2": "Dans le PDF téléchargé, il est clairement indiqué que les balises ne sont pas prises en charge par l'importation via FormSync.", "app.components.formBuilder.proposedBudget": "Budget proposé", "app.components.formBuilder.question": "Question", "app.components.formBuilder.questionCannotBeDeleted": "Cette question ne peut pas être supprimée.", "app.components.formBuilder.questionDescriptionOptional": "Description de la question (facultatif)", "app.components.formBuilder.questionTitle": "Titre de la question", "app.components.formBuilder.randomize": "Randomiser", "app.components.formBuilder.randomizeToolTip": "L'ordre des réponses sera aléatoire et différent pour chaque utilisateur", "app.components.formBuilder.range": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.ranking": "Classement", "app.components.formBuilder.rating": "Évaluation", "app.components.formBuilder.removeAnswer": "Supprimer la réponse", "app.components.formBuilder.required": "Requis", "app.components.formBuilder.requiredToggleLabel": "Rendre obligatoire la réponse à cette question", "app.components.formBuilder.ruleForAnswerLabel": "Si la réponse est :", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "Si les réponses incluent :", "app.components.formBuilder.save": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.selectRangeTooltip": "Choisissez la valeur maximale de votre échelle.", "app.components.formBuilder.sentiment": "Échelle de sentiment", "app.components.formBuilder.shapefileUpload": "Téléchargement de fichier de formes Esri", "app.components.formBuilder.shortAnswer": "Réponse courte", "app.components.formBuilder.showResponseToUsersToggleLabel": "Afficher la réponse aux utilisateurs", "app.components.formBuilder.singleChoice": "Choix unique", "app.components.formBuilder.staleDataErrorMessage2": "Un problème est survenu. Ce formulaire a été enregistré plus récemment ailleurs. <PERSON><PERSON> peut être dû au fait que vous ou un autre utilisateur l'avez ouvert dans une autre fenêtre de navigateur pour le modifier. Veuillez actualiser la page pour accéder à la version la plus récente du formulaire, puis réappliquez vos modifications.", "app.components.formBuilder.stronglyAgree": "Tout à fait d'accord", "app.components.formBuilder.stronglyDisagree": "Pas du tout d'accord", "app.components.formBuilder.supportArticleLinkText": "cette page", "app.components.formBuilder.tags": "Étiquettes", "app.components.formBuilder.title": "Titre", "app.components.formBuilder.toLabel": "à", "app.components.formBuilder.unsavedChanges": "Vos dernières modifications ne sont pas enregistrées", "app.components.formBuilder.useCustomButton2": "Utiliser un bouton personnalisé", "app.components.formBuilder.veryBad": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.veryGood": "<PERSON><PERSON><PERSON> bon", "app.components.ideas.similarIdeas.engageHere": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Aucune contribution similaire trouvée.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Nous avons trouvé des contributions similaires à la vôtre. Nous vous encourageons à les consulter et, si elles sont pertinentes, à interagir avec elles plutôt que d'en créer une nouvelle.", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Contributions similaires déjà publiées :", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Recherche de contributions similaires en cours...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {Moins d'un jour restant} one {Un jour restant} other {# jours restants}}", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft} semaines restantes", "app.components.screenReaderCurrency.AED": "Dirham des Émirats arabes unis", "app.components.screenReaderCurrency.AFN": "Afghani", "app.components.screenReaderCurrency.ALL": "Lek", "app.components.screenReaderCurrency.AMD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.ANG": "Florin des Antilles néerlandaises", "app.components.screenReaderCurrency.AOA": "Kwan<PERSON>", "app.components.screenReaderCurrency.ARS": "Peso argentin", "app.components.screenReaderCurrency.AUD": "Dollar australien", "app.components.screenReaderCurrency.AWG": "Florin a<PERSON>is", "app.components.screenReaderCurrency.AZN": "Manat <PERSON>", "app.components.screenReaderCurrency.BAM": "Mark convertible de Bosnie-Herzégovine", "app.components.screenReaderCurrency.BBD": "Dollar barbadien", "app.components.screenReaderCurrency.BDT": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BGN": "<PERSON> bulgare", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BMD": "Dollar bermudien", "app.components.screenReaderCurrency.BND": "Dollar de Brunei", "app.components.screenReaderCurrency.BOB": "Boliviano", "app.components.screenReaderCurrency.BOV": "Mvdol", "app.components.screenReaderCurrency.BRL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BSD": "Dollar bahaméen", "app.components.screenReaderCurrency.BTN": "Ngultrum", "app.components.screenReaderCurrency.BWP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "Rouble bié<PERSON>usse", "app.components.screenReaderCurrency.BZD": "Dollar bélizien", "app.components.screenReaderCurrency.CAD": "Dollar canadien", "app.components.screenReaderCurrency.CDF": "Franc congolais", "app.components.screenReaderCurrency.CHE": "Euro WIR", "app.components.screenReaderCurrency.CHF": "Franc suisse", "app.components.screenReaderCurrency.CHW": "Franc WIR", "app.components.screenReaderCurrency.CLF": "Unidad de Fomento chilienne (UF)", "app.components.screenReaderCurrency.CLP": "Peso chilien", "app.components.screenReaderCurrency.CNY": "Yuan", "app.components.screenReaderCurrency.COP": "Peso colombien", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real colombienne (UVR)", "app.components.screenReaderCurrency.CRC": "Colón costaricien", "app.components.screenReaderCurrency.CRE": "Crédit", "app.components.screenReaderCurrency.CUC": "Peso cubain convertible", "app.components.screenReaderCurrency.CUP": "Peso cubain", "app.components.screenReaderCurrency.CVE": "Escudo cap-verdien", "app.components.screenReaderCurrency.CZK": "Couronne tchèque", "app.components.screenReaderCurrency.DJF": "Franc Djibouti", "app.components.screenReaderCurrency.DKK": "<PERSON><PERSON><PERSON> danoise", "app.components.screenReaderCurrency.DOP": "Peso dominicain", "app.components.screenReaderCurrency.DZD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.EGP": "Livre égyptienne", "app.components.screenReaderCurrency.ERN": "Nakfa", "app.components.screenReaderCurrency.ETB": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Dollar des Fidji", "app.components.screenReaderCurrency.FKP": "Livre des îles Malouines", "app.components.screenReaderCurrency.GBP": "<PERSON><PERSON> sterling", "app.components.screenReaderCurrency.GEL": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Livre de Gibraltar", "app.components.screenReaderCurrency.GMD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GNF": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GTQ": "Quetzal guatémaltèque", "app.components.screenReaderCurrency.GYD": "Dollar guyanien", "app.components.screenReaderCurrency.HKD": "Dollar de Hong Kong", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.HTG": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HUF": "Forint hongrois", "app.components.screenReaderCurrency.IDR": "R<PERSON>pie indonésienne", "app.components.screenReaderCurrency.ILS": "Nouveau shekel", "app.components.screenReaderCurrency.INR": "Roupie indienne", "app.components.screenReaderCurrency.IQD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.IRR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.ISK": "Couronne islandaise", "app.components.screenReaderCurrency.JMD": "Dollar jamaïcain", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON> j<PERSON>", "app.components.screenReaderCurrency.JPY": "Yen", "app.components.screenReaderCurrency.KES": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KGS": "Som", "app.components.screenReaderCurrency.KHR": "Riel", "app.components.screenReaderCurrency.KMF": "Franc comorien", "app.components.screenReaderCurrency.KPW": "Won nord-coréen", "app.components.screenReaderCurrency.KRW": "Won sud-coréen", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "Dollar des Îles Caïmans", "app.components.screenReaderCurrency.KZT": "Tenge kazakh", "app.components.screenReaderCurrency.LAK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LBP": "Livre libanaise", "app.components.screenReaderCurrency.LKR": "<PERSON><PERSON><PERSON> sri-lankaise", "app.components.screenReaderCurrency.LRD": "Dollar libérien", "app.components.screenReaderCurrency.LSL": "Loti", "app.components.screenReaderCurrency.LTL": "Litas", "app.components.screenReaderCurrency.LVL": "Lats", "app.components.screenReaderCurrency.LYD": "<PERSON>ar libyen", "app.components.screenReaderCurrency.MAD": "<PERSON><PERSON><PERSON> maro<PERSON>in", "app.components.screenReaderCurrency.MDL": "<PERSON><PERSON> mold<PERSON>", "app.components.screenReaderCurrency.MGA": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MKD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MMK": "Kyat", "app.components.screenReaderCurrency.MNT": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MOP": "Pataca", "app.components.screenReaderCurrency.MRO": "Ouguiya", "app.components.screenReaderCurrency.MUR": "<PERSON><PERSON><PERSON> ma<PERSON>", "app.components.screenReaderCurrency.MVR": "Roupie maldivienne", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON><PERSON> ma<PERSON>", "app.components.screenReaderCurrency.MXN": "Peso mexicain", "app.components.screenReaderCurrency.MXV": "Unidad de Inversión mexicaine (UDI)", "app.components.screenReaderCurrency.MYR": "<PERSON><PERSON> malaisien", "app.components.screenReaderCurrency.MZN": "Metical", "app.components.screenReaderCurrency.NAD": "Dollar namibien", "app.components.screenReaderCurrency.NGN": "Na<PERSON><PERSON>", "app.components.screenReaderCurrency.NIO": "Cordoba", "app.components.screenReaderCurrency.NOK": "Couronne norvégienne", "app.components.screenReaderCurrency.NPR": "Roupie népalaise", "app.components.screenReaderCurrency.NZD": "Dollar néo-zélandais", "app.components.screenReaderCurrency.OMR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.PAB": "Balboa", "app.components.screenReaderCurrency.PEN": "Sol", "app.components.screenReaderCurrency.PGK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.PHP": "Peso philippin", "app.components.screenReaderCurrency.PKR": "R<PERSON>pie paki<PERSON>e", "app.components.screenReaderCurrency.PLN": "Złoty", "app.components.screenReaderCurrency.PYG": "Guaraní", "app.components.screenReaderCurrency.QAR": "<PERSON><PERSON><PERSON> qatari", "app.components.screenReaderCurrency.RON": "<PERSON><PERSON>", "app.components.screenReaderCurrency.RSD": "<PERSON><PERSON> serbe", "app.components.screenReaderCurrency.RUB": "Rouble russe", "app.components.screenReaderCurrency.RWF": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SAR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SBD": "Dollar des Îles Salomon", "app.components.screenReaderCurrency.SCR": "<PERSON><PERSON><PERSON> se<PERSON>", "app.components.screenReaderCurrency.SDG": "<PERSON><PERSON> souda<PERSON>", "app.components.screenReaderCurrency.SEK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SGD": "Dollar de Singapour", "app.components.screenReaderCurrency.SHP": "Livre de Sainte-H<PERSON>ène", "app.components.screenReaderCurrency.SLL": "Leone", "app.components.screenReaderCurrency.SOS": "Shilling somalien", "app.components.screenReaderCurrency.SRD": "Dollar du Suriname", "app.components.screenReaderCurrency.SSP": "Livre sud-soudanaise", "app.components.screenReaderCurrency.STD": "Dobra", "app.components.screenReaderCurrency.SYP": "Livre syrienne", "app.components.screenReaderCurrency.SZL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.THB": "Baht", "app.components.screenReaderCurrency.TJS": "So<PERSON><PERSON>", "app.components.screenReaderCurrency.TMT": "Manat turkmène", "app.components.screenReaderCurrency.TND": "<PERSON><PERSON> tunisien", "app.components.screenReaderCurrency.TOK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.TOP": "Paʻanga", "app.components.screenReaderCurrency.TRY": "<PERSON><PERSON> turque", "app.components.screenReaderCurrency.TTD": "Dollar de Trinité-et-Tobago", "app.components.screenReaderCurrency.TWD": "Nouveau dollar de Taïwan", "app.components.screenReaderCurrency.TZS": "Shilling tanzanien", "app.components.screenReaderCurrency.UAH": "Hryvnia", "app.components.screenReaderCurrency.UGX": "Shilling ougandais", "app.components.screenReaderCurrency.USD": "Dollar américain", "app.components.screenReaderCurrency.USN": "Dollar américain (jour suivant)", "app.components.screenReaderCurrency.USS": "Dollar américain (même jour)", "app.components.screenReaderCurrency.UYI": "Peso uruguayen en unités indexées (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Peso u<PERSON>uay<PERSON>", "app.components.screenReaderCurrency.UZS": "Sum", "app.components.screenReaderCurrency.VEF": "Bolivar fort", "app.components.screenReaderCurrency.VND": "Đồng", "app.components.screenReaderCurrency.VUV": "Vatu", "app.components.screenReaderCurrency.WST": "Tālā", "app.components.screenReaderCurrency.XAF": "Franc CFA (Coopération financière en Afrique centrale)", "app.components.screenReaderCurrency.XAG": "Argent (une once troy)", "app.components.screenReaderCurrency.XAU": "Or (une once troy)", "app.components.screenReaderCurrency.XBA": "Unité composite européenne (EURCO)", "app.components.screenReaderCurrency.XBB": "Unité monétaire européenne (UME -6)", "app.components.screenReaderCurrency.XBC": "Unité de compte européenne 9 (U.C.E.-9)", "app.components.screenReaderCurrency.XBD": "Unité de compte européenne 17 (U.C.E.-17)", "app.components.screenReaderCurrency.XCD": "Dollar des Caraïbes orientales", "app.components.screenReaderCurrency.XDR": "Droits de tirage spéciaux", "app.components.screenReaderCurrency.XFU": "Franc UIC", "app.components.screenReaderCurrency.XOF": "Franc CFA (Communauté financière en Afrique)", "app.components.screenReaderCurrency.XPD": "Palladium (une once troy)", "app.components.screenReaderCurrency.XPF": "Franc CFP", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON> (une once troy)", "app.components.screenReaderCurrency.XTS": "Codes spécifiquement réservés à des fins de test", "app.components.screenReaderCurrency.XXX": "Pas de devise", "app.components.screenReaderCurrency.YER": "<PERSON>ial yé<PERSON>te", "app.components.screenReaderCurrency.ZAR": "Rand", "app.components.screenReaderCurrency.ZMW": "<PERSON><PERSON>cha <PERSON>", "app.components.screenReaderCurrency.amount": "<PERSON><PERSON>", "app.components.screenReaderCurrency.currency": "<PERSON><PERSON>", "app.components.trendIndicator.lastQuarter2": "dernier trimestre", "app.containers.AccessibilityStatement.applicability": "Cette déclaration d'accessibilité s'applique à un {demoPlatformLink} qui est représentatif de ce site Web ; il utilise le même code source et possède les mêmes fonctionnalités.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Méthode d'évaluation", "app.containers.AccessibilityStatement.assesmentText2022": "L'accessibilité de ce site a été évaluée par une entité externe non impliquée dans le processus de conception et de développement. La conformité des {demoPlatformLink} susmentionnés peut être identifiée sur ce {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "vous pouvez changer vos préférences", "app.containers.AccessibilityStatement.changePreferencesText": "A tout moment, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Exceptions de conformité", "app.containers.AccessibilityStatement.conformanceStatus": "Niveau de conformité", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Nous faisons le maximum pour fournir un contenu accessible à tous, quelles que soient sa situation ou ses capacités. Néanmoins, les situations suivantes peuvent ne pas nous le permettre :", "app.containers.AccessibilityStatement.demoPlatformLinkText": "site démo", "app.containers.AccessibilityStatement.email": "Adresse e-mail :", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Outils d'enquête tiers", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Il n'est pas garanti que les outils d'enquête proposés via des intégrations avec des services tiers respectent les normes d'accessibilité.", "app.containers.AccessibilityStatement.exception_1": "Nos plateformes d'engagement numérique facilitent le contenu généré par les utilisateurs publié par des individus et des organisations. Il est possible que des PDF, des images ou d'autres types de fichiers, y compris des fichiers multimédias, soient téléchargés sur la plate-forme en tant que pièces jointes ou ajoutés dans des champs de texte par les utilisateurs de la plate-forme. Ces documents peuvent ne pas être entièrement accessibles.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Vos commentaires nous aident à améliorer l'accessibilité de notre site. Merci de nous contacter par :", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Remar<PERSON> et suggestions", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Bruxelles, Belgique", "app.containers.AccessibilityStatement.headTitle": "Déclaration d'accessibilité | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} s'engage à fournir une plateforme accessible à tous les utilisateurs, quelle que soit leur technologie ou leurs capacités. Les normes d'accessibilité en vigueur sont respectées dans le cadre de nos efforts constants pour maximiser l'accessibilité et la convivialité de nos plateformes pour tous les utilisateurs.", "app.containers.AccessibilityStatement.mapping": "Cartographie", "app.containers.AccessibilityStatement.mapping_1": "Les cartes sur la plateforme répondent partiellement aux normes d'accessibilité. Le zoom et les widgets de l'interface peuvent être contrôlés par un utilisateur à l'aide d'un clavier lors de la visualisation des cartes. Les administrateurs peuvent également configurer le style des couches cartographiques dans le back-office, ou en utilisant l'intégration Esri, pour créer des palettes de couleurs et une symbologie plus accessibles. L'utilisation de différents styles de tracés linéaires ou polygones (par exemple des lignes en pointillés) permet également de différencier les couches de la carte là où c'est possible, et bien que ce style ne puisse pas être configuré dans notre plateforme pour le moment, il peut l'être si vous utilisez des cartes avec l'intégration Esri.", "app.containers.AccessibilityStatement.mapping_2": "Les cartes de la plateforme ne sont pas totalement accessibles car elles ne présentent pas de manière audible les cartes de base, les couches cartographiques ou les tendances des données pour les utilisateurs qui utilisent des lecteurs d'écran. Les cartes entièrement accessibles devraient présenter de manière audible les couches de la carte et décrire toutes les tendances pertinentes dans les données. En outre, les tracés linéaires et de polygones dans les enquêtes n'est pas accessible car les formes ne peuvent pas être dessinées à l'aide d'un clavier. D'autres méthodes de saisie ne sont pas disponibles à l'heure actuelle en raison de leur complexité technique.", "app.containers.AccessibilityStatement.mapping_3": "Pour rendre les tracés linéaires et de polygones plus accessible, nous recommandons d'inclure une introduction ou une explication dans la question de l'enquête ou dans la description de la page sur ce que la carte montre et sur les tendances pertinentes. En outre, une question textuelle à réponse courte ou longue pourrait être fournie afin que les répondants puissent décrire leur réponse en termes simples si nécessaire (plutôt que de cliquer sur la carte). Nous recommandons également d'inclure dans l'enquête un moyen de contacter le propriétaire du projet afin que les personnes qui ne peuvent pas répondre à une question sur la carte puissent demander une autre méthode pour répondre à la question (par exemple, une réunion vidéo).", "app.containers.AccessibilityStatement.mapping_4": "Pour les projets d’idéation il existe une option permettant d'afficher les contributions dans une vue cartographique, qui n'est pas accessible. Toutefois, pour ces méthodes, il existe une autre vue en liste des contributions, qui est accessible.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Nos ateliers en ligne comportent une composante de diffusion vidéo en direct, qui ne prend actuellement pas en charge les sous-titres.", "app.containers.AccessibilityStatement.pageDescription": "Standard d'accessibilité de ce site internet", "app.containers.AccessibilityStatement.postalAddress": "Courrier postal :", "app.containers.AccessibilityStatement.publicationDate": "Date de publication", "app.containers.AccessibilityStatement.publicationDate2024": "Cette déclaration sur l'accessibilité de la plateforme a été publiée le 21 août 2024.", "app.containers.AccessibilityStatement.responsiveness": "Nous vous répondons en 1 à 2 jours ouvrés.", "app.containers.AccessibilityStatement.statusPageText": "page de statut", "app.containers.AccessibilityStatement.technologiesIntro": "L'accessibilité de ce site nécessite les technologies suivantes :", "app.containers.AccessibilityStatement.technologiesTitle": "Technologies", "app.containers.AccessibilityStatement.title": "Standards d'accessibilité", "app.containers.AccessibilityStatement.userGeneratedContent": "Contenu généré par les utilisateurs", "app.containers.AccessibilityStatement.workshops": "Ateliers", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Sélectionnez le projet", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "L'utilisation de l'éditeur sur-mesure vous permettra d'utiliser des options de mise en page plus avancées. Si aucun contenu n'est enregistré dans l'éditeur pour certaines langues, la description standard du projet sera affichée à la place pour ces langues.", "app.containers.AdminPage.ProjectDescription.linkText": "Modifier la description dans l'éditeur sur-mesure", "app.containers.AdminPage.ProjectDescription.saveError": "Un problème s'est produit lors de l'enregistrement de la description du projet.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Utiliser l'éditeur sur-mesure pour la description", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "L'utilisation de l'éditeur sur-mesure vous permettra d'utiliser des options de mise en page plus avancées.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "Visualiser le projet", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Fin de l'enquête", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Créer un groupe automatique", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Les utilisateurs correspondant à toutes les conditions suivantes seront automatiquement ajoutés à ce groupe :", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Fournir au moins une règle", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Certaines conditions sont incomplètes", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Enregistrer le groupe", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Les groupes automatiques ne sont pas inclus dans votre licence actuelle. Contactez votre Spécialiste en participation pour en savoir plus.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Fournir un nom de groupe", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "La vérification est désactivée pour votre plate-forme, supprimez la règle de vérification ou contactez le support technique.", "app.containers.App.appMetaDescription": "Bienvenue sur la plateforme de participation en ligne de {orgName}. \nExplorez les projets locaux et participez à la discussion !", "app.containers.App.loading": "Chargement...", "app.containers.App.metaTitle1": "Plateforme de participation citoyenne | {orgName}", "app.containers.App.skipLinkText": "Aller directement au contenu principal", "app.containers.AreaTerms.areaTerm": "zone géographique", "app.containers.AreaTerms.areasTerm": "zones géographiques", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Un compte avec cette adresse e-mail existe déjà. Vous pouvez vous déconnecter, vous connecter avec cette adresse e-mail et vérifier votre compte sur la page \"Mes réglages\".", "app.containers.Authentication.steps.AccessDenied.close": "<PERSON><PERSON><PERSON>", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Vous ne remplissez pas les conditions requises pour participer à ce processus.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Retour à la vérification SSO (Single Sign-On)", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Veuillez saisir le code d'invitation", "app.containers.Authentication.steps.Invitation.token": "Code", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Vous avez déjà un compte ? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Se connecter", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Emails dans cette catégorie", "app.containers.CampaignsConsentForm.messageError": "Une erreur est survenue lors de l'enregistrement de vos préférences d'email.", "app.containers.CampaignsConsentForm.messageSuccess": "Vos préférences d'email ont été enregistrées.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Quels types de notifications par email souhaitez-vous recevoir ?", "app.containers.CampaignsConsentForm.notificationsTitle": "Notifications", "app.containers.CampaignsConsentForm.submit": "Enregistrer", "app.containers.ChangeEmail.backToProfile": "Retour aux réglages du profil", "app.containers.ChangeEmail.confirmationModalTitle": "Confirmez votre adresse e-mail", "app.containers.ChangeEmail.emailEmptyError": "Fournissez une adresse e-mail", "app.containers.ChangeEmail.emailInvalidError": "Fournissez une adresse e-mail au format correct, <NAME_EMAIL>", "app.containers.ChangeEmail.emailRequired": "<PERSON>eu<PERSON>z saisir une adresse e-mail.", "app.containers.ChangeEmail.emailTaken": "Cette adresse e-mail est déjà utilisée.", "app.containers.ChangeEmail.emailUpdateCancelled": "La modification de l'adresse e-mail a été annulée.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Pour modifier votre adresse e-mail, veuil<PERSON><PERSON> recommencer la procédure.", "app.containers.ChangeEmail.helmetDescription": "Page pour modifier votre adresse e-mail", "app.containers.ChangeEmail.helmetTitle": "Modifiez votre adresse e-mail", "app.containers.ChangeEmail.newEmailLabel": "Nouvelle adresse e-mail", "app.containers.ChangeEmail.submitButton": "So<PERSON><PERSON><PERSON>", "app.containers.ChangeEmail.titleAddEmail": "Ajoutez votre adresse e-mail", "app.containers.ChangeEmail.titleChangeEmail": "Modifiez votre adresse e-mail", "app.containers.ChangeEmail.updateSuccessful": "Votre adresse e-mail a été mise à jour avec succès.", "app.containers.ChangePassword.currentPasswordLabel": "Mot de passe actuel", "app.containers.ChangePassword.currentPasswordRequired": "Saisissez votre mot de passe actuel", "app.containers.ChangePassword.goHome": "Aller à la page d’accueil", "app.containers.ChangePassword.helmetDescription": "Page de modification du mot de passe", "app.containers.ChangePassword.helmetTitle": "Modifiez votre mot de passe", "app.containers.ChangePassword.newPasswordLabel": "Nouveau mot de passe", "app.containers.ChangePassword.newPasswordRequired": "Saisissez votre nouveau mot de passe", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Indiquez un mot de passe d'au moins {minimumPasswordLength} caractères.", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Votre mot de passe a été mis à jour avec succès", "app.containers.ChangePassword.passwordEmptyError": "Saisissez votre mot de passe", "app.containers.ChangePassword.passwordsDontMatch": "Confirmez le nouveau mot de passe", "app.containers.ChangePassword.titleAddPassword": "Ajouter un mot de passe", "app.containers.ChangePassword.titleChangePassword": "Modifier votre mot de passe", "app.containers.Comments.a11y_commentDeleted": "Commentaire supprimé", "app.containers.Comments.a11y_commentPosted": "Commentaire publié", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {pas de j'aime} one {1 j'aime} other {# j'aime}}", "app.containers.Comments.a11y_undoLike": "<PERSON><PERSON><PERSON> le j'aime", "app.containers.Comments.addCommentError": "Une erreur est survenue. Veuillez réessayer.", "app.containers.Comments.adminCommentDeletionCancelButton": "Annuler", "app.containers.Comments.adminCommentDeletionConfirmButton": "Supprimer ce commentaire", "app.containers.Comments.cancelCommentEdit": "Annuler", "app.containers.Comments.childCommentBodyPlaceholder": "Ecrire une réponse...", "app.containers.Comments.commentCancelUpvote": "Annuler", "app.containers.Comments.commentDeletedPlaceholder": "Ce commentaire a été enlevé.", "app.containers.Comments.commentDeletionCancelButton": "Garder ce commentaire", "app.containers.Comments.commentDeletionConfirmButton": "Supprimer ce commentaire", "app.containers.Comments.commentLike": "<PERSON>'aime", "app.containers.Comments.commentReplyButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.commentsSortTitle": "Trier les commentaires par", "app.containers.Comments.completeProfileLinkText": "compléter votre profil", "app.containers.Comments.completeProfileToComment": "Veuillez {completeRegistrationLink} pour pouvoir commenter.", "app.containers.Comments.confirmCommentDeletion": "Êtes-vous sûr de vouloir supprimer ce commentaire ? L'action est définitive !", "app.containers.Comments.deleteComment": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.deleteReasonDescriptionError": "Fournissez plus d'informations sur votre raison", "app.containers.Comments.deleteReasonError": "Donnez une raison", "app.containers.Comments.deleteReason_inappropriate": "C'est inapproprié ou offensant", "app.containers.Comments.deleteReason_irrelevant": "<PERSON>la n'a pas sa place ici.", "app.containers.Comments.deleteReason_other": "Autre raison", "app.containers.Comments.editComment": "Modifier", "app.containers.Comments.guidelinesLinkText": "nos directives", "app.containers.Comments.ideaCommentBodyPlaceholder": "Écrivez votre commentaire ici", "app.containers.Comments.internalCommentingNudgeMessage": "La rédaction de commentaires internes n'est pas incluse dans votre licence actuelle. Contactez votre GovSuccess Manager pour en savoir plus.", "app.containers.Comments.internalConversation": "Conversation interne", "app.containers.Comments.loadMoreComments": "Afficher plus de commentaires", "app.containers.Comments.loadingComments": "Chargement des commentaires...", "app.containers.Comments.loadingMoreComments": "Chargement des commentaires...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Ce commentaire n'est pas visible pour les simples utilisateurs", "app.containers.Comments.postInternalComment": "Poster un commentaire interne", "app.containers.Comments.postPublicComment": "Poster un commentaire public", "app.containers.Comments.profanityError": "Vous avez peut-être utilisé un ou plusieurs mots considérés comme étant inappropriés par {guidelinesLink}. Veuillez modifier votre texte afin de supprimer les propos injurieux qui pourraient y être présent.", "app.containers.Comments.publicDiscussion": "Discussion publique", "app.containers.Comments.publishComment": "<PERSON>ez votre commentaire", "app.containers.Comments.reportAsSpamModalTitle": "Pourquoi voulez-vous signaler cet élément ?", "app.containers.Comments.saveComment": "Enregistrer", "app.containers.Comments.signInLinkText": "connecter", "app.containers.Comments.signInToComment": "Vous devez vous {signInLink} pour commenter.", "app.containers.Comments.signUpLinkText": "s'inscrire", "app.containers.Comments.verifyIdentityLinkText": "Vérifiez votre identité", "app.containers.Comments.visibleToUsersPlaceholder": "Ce commentaire est visible par les simples utilisateurs", "app.containers.Comments.visibleToUsersWarning": "Les commentaires postés ici seront visibles par les simples utilisateurs.", "app.containers.ContentBuilder.PageTitle": "Description du projet", "app.containers.CookiePolicy.advertisingContent": "Les cookies publicitaires peuvent être utilisés pour personnaliser et mesurer l'efficacité que les campagnes de marketing externes ont sur la participation à cette plateforme. Nous ne montrerons aucune publicité sur cette plateforme, mais vous pouvez recevoir des annonces personnalisées en fonction des pages que vous visitez.", "app.containers.CookiePolicy.advertisingTitle": "Cookies publicitaires", "app.containers.CookiePolicy.analyticsContents": "Les cookies analytiques suivent le comportement des visiteurs, comme les pages visitées et leur durée. Ils peuvent également recueillir certaines données techniques, notamment des informations sur le navigateur, la localisation approximative et les adresses IP. Nous utilisons ces données uniquement en interne pour continuer à améliorer l'expérience utilisateur globale et le fonctionnement de la plateforme. Ces données peuvent également être partagées entre Go Vocal et {orgName} pour évaluer et améliorer la participation dans les projets sur la plateforme. Notez que ces données sont anonymes et utilisées à un niveau agrégé - elles ne vous identifient pas personnellement. Cependant, il est possible que si ces données étaient combinées avec d'autres sources de données, une telle identification pourrait être possible.", "app.containers.CookiePolicy.analyticsTitle": "Cookies analytiques", "app.containers.CookiePolicy.cookiePolicyDescription": "Une explication détaillée de la façon dont nous utilisons les cookies sur cette plateforme", "app.containers.CookiePolicy.cookiePolicyTitle": "Politique de cookies", "app.containers.CookiePolicy.essentialContent": "Certains cookies sont essentiels pour assurer le bon fonctionnement de cette plateforme. Ces cookies essentiels sont principalement utilisés pour authentifier votre compte lorsque vous visitez la plateforme et pour enregistrer votre langue préférée.", "app.containers.CookiePolicy.essentialTitle": "<PERSON>ies essentiels", "app.containers.CookiePolicy.externalContent": "Certaines de nos pages peuvent afficher du contenu provenant de fournisseurs externes, par exemple YouTube ou Typeform. Nous n'avons aucun contrôle sur ces cookies tiers et la visualisation du contenu de ces fournisseurs externes peut également entraîner l'installation de cookies sur votre appareil.", "app.containers.CookiePolicy.externalTitle": "Cookies externes", "app.containers.CookiePolicy.functionalContents": "Des cookies fonctionnels peuvent être activés pour que les visiteurs reçoivent des notifications sur les mises à jour et puissent accéder aux canaux de support directement depuis la plateforme.", "app.containers.CookiePolicy.functionalTitle": "Cookies fonctionnels", "app.containers.CookiePolicy.headCookiePolicyTitle": "Politique en matière de cookies | {orgName}", "app.containers.CookiePolicy.intro": "Les cookies sont des fichiers texte stockés sur le navigateur ou sur le disque dur de votre ordinateur ou appareil mobile lorsque vous visitez un site web et qui peuvent être référencés par le site web lors de visites ultérieures. Nous utilisons des cookies pour comprendre comment les visiteurs utilisent cette plateforme afin d'en améliorer la conception et l'expérience d’utilisation, pour mémoriser vos préférences (comme votre langue préférée) et pour prendre en charge des fonctions clés pour les utilisateurs enregistrés et les administrateurs de la plateforme.", "app.containers.CookiePolicy.manageCookiesDescription": "Vous pouvez activer ou désactiver les cookies analytiques, marketing et fonctionnels à tout moment dans vos préférences en matière de cookies. Vous pouvez également supprimer manuellement ou automatiquement tous les cookies existants via votre navigateur internet. Toutefois, les cookies peuvent être placés à nouveau après votre consentement lors de toute visite ultérieure sur cette plateforme. Si vous ne supprimez pas les cookies, vos préférences en matière de cookies sont stockées pendant 60 jours, après quoi votre consentement vous sera à nouveau demandé.", "app.containers.CookiePolicy.manageCookiesPreferences": "Allez dans vos {manageCookiesPreferencesButtonText} pour voir une liste complète des intégrations tierces utilisées sur cette plateforme et pour gérer vos préférences.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "paramètres de cookies", "app.containers.CookiePolicy.manageCookiesTitle": "Gestion de vos cookies", "app.containers.CookiePolicy.viewPreferencesButtonText": "Paramètres des cookies", "app.containers.CookiePolicy.viewPreferencesText": "Les catégories de cookies ci-dessous peuvent ne pas s'appliquer à tous les visiteurs ou à toutes les plateformes ; consultez vos {viewPreferencesButton} pour obtenir une liste complète des intégrations tierces qui vous sont applicables.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "À quoi servent les cookies ?", "app.containers.CustomPageShow.editPage": "Modifier la page", "app.containers.CustomPageShow.goBack": "Retour", "app.containers.CustomPageShow.notFound": "Page introuvable", "app.containers.DisabledAccount.bottomText": "Vous pourrez vous connecter à nouveau à partir du {date}.", "app.containers.DisabledAccount.termsAndConditions": "conditions générales", "app.containers.DisabledAccount.text2": "Votre compte sur la plateforme de participation {orgName} a été temporairement désactivé en raison d'une violation des règles de la communauté. Pour plus d'informations à ce sujet, veuillez vous référer aux {TermsAndConditions}.", "app.containers.DisabledAccount.title": "Votre compte a été temporairement désactivé", "app.containers.EventsShow.addToCalendar": "A<PERSON>ter au calendrier", "app.containers.EventsShow.editEvent": "Modifier l'événement", "app.containers.EventsShow.emailSharingBody2": "Participez à cet événement : {eventTitle}. Pour en savoir plus : {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Date et heure de l'événement", "app.containers.EventsShow.eventFrom2": "Dans le cadre de « {projectTitle} »", "app.containers.EventsShow.goBack": "Retour", "app.containers.EventsShow.goToProject": "Accéder au projet", "app.containers.EventsShow.haveRegistered": "se sont inscrits", "app.containers.EventsShow.icsError": "<PERSON><PERSON>ur lors du téléchargement du fichier calendrier (ICS)", "app.containers.EventsShow.linkToOnlineEvent": "Lien vers l'événement en ligne", "app.containers.EventsShow.locationIconAltText": "<PERSON><PERSON>", "app.containers.EventsShow.metaTitle": "Événement : {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Réunion en ligne", "app.containers.EventsShow.onlineLinkIconAltText": "Lien vers la réunion en ligne", "app.containers.EventsShow.registered": "inscrit", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 inscrit} one {1 inscrit} other {# inscrits}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} inscriptions", "app.containers.EventsShow.registrantsIconAltText": "Personne inscrites", "app.containers.EventsShow.socialMediaSharingMessage": "Participez à cet événement : {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, no {# participants} one {# participant} other {# participants}}", "app.containers.EventsViewer.allTime": "Tout le temps", "app.containers.EventsViewer.date": "Date", "app.containers.EventsViewer.thisMonth2": "<PERSON>is à venir", "app.containers.EventsViewer.thisWeek2": "<PERSON><PERSON><PERSON> à venir", "app.containers.EventsViewer.today": "<PERSON><PERSON><PERSON>'hui", "app.containers.IdeaButton.addAContribution": "A<PERSON><PERSON>z une suggestion", "app.containers.IdeaButton.addAPetition": "Ajouter une pétition", "app.containers.IdeaButton.addAProject": "Ajouter un nouveau projet", "app.containers.IdeaButton.addAProposal": "Ajouter une proposition", "app.containers.IdeaButton.addAQuestion": "Ajouter une question", "app.containers.IdeaButton.addAnInitiative": "Ajouter une initiative", "app.containers.IdeaButton.addAnOption": "Ajouter une option", "app.containers.IdeaButton.postingDisabled": "Les nouvelles soumissions ne sont pas acceptées actuellement", "app.containers.IdeaButton.postingInNonActivePhases": "Les nouvelles soumissions ne peuvent être ajoutées que dans les phases actives.", "app.containers.IdeaButton.postingInactive": "Les nouvelles soumissions ne sont actuellement pas acceptées.", "app.containers.IdeaButton.postingLimitedMaxReached": "Vous avez déjà répondu à cette enquête. Merci pour votre réponse !", "app.containers.IdeaButton.postingNoPermission": "Il n’est pas possible de commenter actuellement", "app.containers.IdeaButton.postingNotYetPossible": "Les nouvelles soumissions ne sont pas encore acceptées ici.", "app.containers.IdeaButton.signInLinkText": "vous connecter", "app.containers.IdeaButton.signUpLinkText": "s'inscrire", "app.containers.IdeaButton.submitAnIssue": "Soumettez un commentaire", "app.containers.IdeaButton.submitYourIdea": "Déposez votre idée", "app.containers.IdeaButton.takeTheSurvey": "Répondre à l'enquête", "app.containers.IdeaButton.verificationLinkText": "Vérifiez votre identité maintenant.", "app.containers.IdeaCard.readMore": "Lire la suite", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {aucun commentaire} one {1 commentaire} other {# commentaires}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {0 vote} one {1 vote} other {# votes}} sur {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "<PERSON><PERSON><PERSON> le panneau des filtres", "app.containers.IdeaCards.a11y_totalItems": "Total des éléments : {ideasCount}", "app.containers.IdeaCards.all": "Tous", "app.containers.IdeaCards.allStatuses": "Tous les statuts", "app.containers.IdeaCards.contributions": "Contributions", "app.containers.IdeaCards.ideaTerm": "Idées", "app.containers.IdeaCards.initiatives": "Initiatives", "app.containers.IdeaCards.issueTerm": "Problèmes", "app.containers.IdeaCards.list": "Liste", "app.containers.IdeaCards.map": "<PERSON><PERSON>", "app.containers.IdeaCards.mostDiscussed": "Les plus discutés", "app.containers.IdeaCards.newest": "Plus récentes", "app.containers.IdeaCards.noFilteredResults": "Aucun résultat trouvé. Veuillez essayer un autre filtre ou terme de recherche.", "app.containers.IdeaCards.numberResults": "Résultats ({postCount})", "app.containers.IdeaCards.oldest": "Plus anciennes", "app.containers.IdeaCards.optionTerm": "Options", "app.containers.IdeaCards.petitions": "Pétitions", "app.containers.IdeaCards.popular": "Plus votées", "app.containers.IdeaCards.projectFilterTitle": "{tenant<PERSON><PERSON>, select, frw {Communes} other {Projets}}", "app.containers.IdeaCards.projectTerm": "Projets", "app.containers.IdeaCards.proposals": "Propositions", "app.containers.IdeaCards.questionTerm": "Questions", "app.containers.IdeaCards.random": "Aléatoire", "app.containers.IdeaCards.resetFilters": "Réinitialiser", "app.containers.IdeaCards.showXResults": "Affichez {ideasCount, plural, no {# results} one {# result} other {# results}}", "app.containers.IdeaCards.sortTitle": "<PERSON><PERSON>", "app.containers.IdeaCards.statusTitle": "Statut", "app.containers.IdeaCards.statusesTitle": "Statuts", "app.containers.IdeaCards.topics": "Étiquettes", "app.containers.IdeaCards.topicsTitle": "Étiquettes", "app.containers.IdeaCards.trending": "Populaires", "app.containers.IdeaCards.tryDifferentFilters": "Aucun résultat trouvé. Veuillez essayer un autre filtre ou terme de recherche.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} commentaire} one {{ideasCount} commentaire} other {{ideasCount} commentaires}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} contribution} one {{ideasCount} contribution} other {{ideasCount} contributions}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} idée} one {{ideasCount} idée} other {{ideasCount} idées}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} initiative} one {{ideasCount} initiative} other {{ideasCount} initiatives}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} option} one {{ideasCount} option} other {{ideasCount} options}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} pétition} one {{ideasCount} pétition} other {{ideasCount} pétitions}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projet} one {{ideasCount} projet} other {{ideasCount} projets}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} proposition} one {{ideasCount} proposition} other {{ideasCount} propositions}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} question} one {{ideasCount} question} other {{ideasCount} questions}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, no {# results} one {# result} other {# results}}", "app.containers.IdeasEditPage.contributionFormTitle": "Modifiez la <PERSON>", "app.containers.IdeasEditPage.editedPostSave": "Enregistrer", "app.containers.IdeasEditPage.fileUploadError": "Un ou plusieurs fichiers n'ont pas pu être téléchargés. Veuillez vérifier la taille et le format du fichier et réessayer.", "app.containers.IdeasEditPage.formTitle": "Modifier l'idée", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Modifiez votre message. Ajoutez de nouvelles et modifiez les anciennes informations.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Modifiez {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Modifier l'initiative", "app.containers.IdeasEditPage.issueFormTitle": "Modifiez le problème", "app.containers.IdeasEditPage.optionFormTitle": "Modifiez l'option", "app.containers.IdeasEditPage.petitionFormTitle": "Modifier la pétition", "app.containers.IdeasEditPage.projectFormTitle": "Modifier le projet", "app.containers.IdeasEditPage.proposalFormTitle": "Modifier la proposition", "app.containers.IdeasEditPage.questionFormTitle": "Modifiez la question", "app.containers.IdeasEditPage.save": "Enregistrer", "app.containers.IdeasEditPage.submitApiError": "Un problème est survenu lors de la soumission du formulaire. Veuillez vérifier s'il y a des erreurs et réessayer.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Toutes les contributions postées", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Explorez toutes les contributions publiées sur la plate-forme de participation de {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Posts | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Contributions", "app.containers.IdeasIndexPage.loadMore": "Afficher la suite...", "app.containers.IdeasIndexPage.loading": "Chargement...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "<PERSON>r <PERSON><PERSON><PERSON>, vos soumissions seront associées à votre profil, sauf si vous sélectionnez cette option.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Publier anonymement", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Visibilité du profil", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Cette enquête n'est actuellement pas ouverte aux réponses. Veuillez vous référer au projet pour davantage d'informations.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Cette enquête n'est pas actuellement active.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Retour au projet", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Vous avez déjà répondu à cette enquête.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> soumise", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Merci de votre réponse !", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "La description de la contribution doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Le corps de l'idée doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Le titre de la contribution doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "Le titre de la contribution doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Veuillez sélectionner au moins un coparrainant", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "La description de l'idée doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "La description de l'idée doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "<PERSON><PERSON><PERSON>z fournir une description", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Le titre de l'idée doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Le titre de l'idée doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "La description de l'initiative doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "La description de l'initiative doit comporter plus de {limit} caractères.", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "Le titre de l'initiative doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "Le titre de l'initiative doit comporter plus de {limit} caractères.", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "La description du problème doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "La description du problème doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Le titre du numéro doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "La description du commentaire doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_number_required": "Ce champ est obligatoire. Veuillez saisir un nombre valide.", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "La description de l'option doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "La description de l'option doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Le titre de l'option doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Le titre de l'option doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Veuillez sélectionner au moins un tag", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "La description de la pétition doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "La description de la pétition doit comporter plus de {limit} caractères.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "Le titre de la pétition doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "Le titre de la pétition doit comporter plus de {limit} caractères.", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "La description du projet doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "La description du projet doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Le titre du projet doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Le titre du projet doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "La description de la proposition doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "La description de la proposition doit comporter plus de {limit} caractères.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "Le titre de la proposition doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "Le titre de la proposition doit comporter plus de {limit} caractères.", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Veuillez saisir un nombre", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Veuillez saisir un nombre", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "La description de la question doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "La description de la question doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "Le titre de la question doit comporter moins de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "Le titre de la question doit comporter plus de {limit} caractères", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Veuillez donner un titre à votre idée", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "La description de la contribution doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "La description de la contribution doit comporter au moins 30 caractères", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Le titre de la contribution doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Le titre de la contribution doit comporter au moins 10 caractères", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "La description de l'idée doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "La description de l’idée doit comporter au moins 30 caractères", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Veuillez donner un titre à votre idée", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Le titre de l'idée doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Le titre d’idée doit comporter au moins 10 caractères", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Vous avez peut-être utilisé un ou plusieurs mots qui sont considérés comme des blasphèmes par {guidelinesLink}. Veuillez modifier votre texte afin de supprimer tout blasphème qui pourrait être présent.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "La description de l'initiative doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "La description doit comporter au moins 30 caractères", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Le titre de l'initiative doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Le titre de l'initiative doit comporter au moins 10 caractères", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "La description du problème doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "La description du problème doit comporter au moins 30 caractères", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Le titre du numéro doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Le titre du numéro doit comporter au moins 10 caractères", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "La description de l'option doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "La description de l'option doit comporter au moins 30 caractères", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Le titre de l'option doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Le titre de l'option doit comporter au moins 10 caractères", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "La description de la pétition doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "La description de la pétition doit comporter au moins 30 caractères", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Le titre de la pétition doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Le titre de la pétition doit comporter au moins 10 caractères", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "La description du projet doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "La description du projet doit comporter au moins 30 caractères", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Le titre du projet doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Le titre du projet doit comporter au moins 10 caractères", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "La description de la proposition doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "La description de la proposition doit comporter au moins 30 caractères", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Le titre de la proposition doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Le titre de la proposition doit comporter au moins 10 caractères", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "<PERSON><PERSON><PERSON>z fournir une description", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "La description de la question doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "La description de la question doit comporter au moins 30 caractères", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Le titre de la question doit comporter moins de 80 caractères", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Le titre de la question doit comporter au moins 10 caractères", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "Annuler", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON>, je veux quitter", "app.containers.IdeasNewPage.contributionMetaTitle1": "Ajouter une nouvelle contribution au projet | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Modifier l'enquête", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Publiez une soumission et rejoignez la conversation sur la plateforme de participation de {orgName}.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Ajouter une nouvelle idée au projet | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Ajouter une nouvelle initiative au projet | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Ajouter un nouveau commentaire au projet | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Êtes-vous sûr de vouloir quitter ?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Vos réponses ont été enregistrées en tant que brouillon (privé). Vous pouvez revenir plus tard pour les terminer.", "app.containers.IdeasNewPage.leaveSurvey": "<PERSON><PERSON><PERSON> l'enquê<PERSON>", "app.containers.IdeasNewPage.leaveSurveyText": "Vos réponses ne seront pas enregistrées.", "app.containers.IdeasNewPage.optionMetaTitle1": "Ajouter une nouvelle option au projet | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Ajouter une nouvelle pétition au projet | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Ajouter un nouveau projet au projet {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Ajouter une nouvelle proposition au projet | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Ajouter une nouvelle question au projet {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Accepter l'invitation", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Invitation à soutenir", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Coparrainants", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Vous avez été invité à soutenir une proposition.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Invitation acceptée", "app.containers.IdeasShow.Cosponsorship.pending": "en attente", "app.containers.IdeasShow.MetaInformation.attachments": "Pièces jointes", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName}, le {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Statut actuel", "app.containers.IdeasShow.MetaInformation.location": "<PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.postedBy": "Publié par", "app.containers.IdeasShow.MetaInformation.similar": "Contributions similaires", "app.containers.IdeasShow.MetaInformation.topics": "Étiquettes", "app.containers.IdeasShow.commentCTA": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.contributionEmailSharingBody": "Soutenez cette suggestion « {postTitle} » à {postUrl} !", "app.containers.IdeasShow.contributionEmailSharingSubject": "Soutenez cette suggestion : {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Merci d'avoir soumis votre suggestion !", "app.containers.IdeasShow.contributionTwitterMessage": "Soutenez cette suggestion : {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Soutenez cette suggestion : {postTitle}", "app.containers.IdeasShow.currentStatus": "Statut actuel", "app.containers.IdeasShow.deletedUser": "auteur inconnu", "app.containers.IdeasShow.ideaEmailSharingBody": "Soutenez mon idée « {ideaTitle} » à {ideaUrl} !", "app.containers.IdeasShow.ideaEmailSharingSubject": "Soutenir mon idée : {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Soutenez cette idée : {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Soutenez cette idée : {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Soutenez ce problème : {postTitle}", "app.containers.IdeasShow.imported": "Importé", "app.containers.IdeasShow.importedTooltip": "Cette contribution a été collectée hors ligne et téléchargée automatiquement sur la plateforme.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Soutenez l'initiative « {ideaTitle} » sur {ideaUrl} !", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Soutenir cette initiative : {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Merci d'avoir soumis votre initiative !", "app.containers.IdeasShow.initiativeTwitterMessage": "Soutenir cette initiative : {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Soutenir cette initiative : {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Soutenez ce problème « {postTitle} » à {postUrl} !", "app.containers.IdeasShow.issueEmailSharingSubject": "Soutenez ce problème : {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Merci d'avoir soumis votre problème !", "app.containers.IdeasShow.issueTwitterMessage": "Soutenez ce problème : {postTitle}", "app.containers.IdeasShow.metaTitle": "Contribution : {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Soutenez cette option « {postTitle} » à {postUrl} !", "app.containers.IdeasShow.optionEmailSharingSubject": "Soutenez cette option : {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Votre option a été publiée avec succès !", "app.containers.IdeasShow.optionTwitterMessage": "Soutenez cette option : {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Soutenez cette option : {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Soutenez la pétition« {ideaTitle} » sur {ideaUrl} !", "app.containers.IdeasShow.petitionEmailSharingSubject": "Soutenez cette pétition : {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Merci d'avoir soumis votre pétition !", "app.containers.IdeasShow.petitionTwitterMessage": "Soutenez cette pétition : {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Soutenez cette pétition : {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Soutenez ce projet « {postTitle} » à {postUrl} !", "app.containers.IdeasShow.projectEmailSharingSubject": "Soutenez ce projet : {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Merci d'avoir soumis votre projet !", "app.containers.IdeasShow.projectTwitterMessage": "Soutenez ce projet : {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Soutenez ce projet : {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Soutenez la proposition « {ideaTitle} » sur {ideaUrl} !", "app.containers.IdeasShow.proposalEmailSharingSubject": "Soutenir cette proposition : {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Merci d'avoir soumis votre proposition !", "app.containers.IdeasShow.proposalTwitterMessage": "Soutenir cette proposition : {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Soutenir cette proposition : {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Temps restant pour voter :", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} sur {votingThreshold} votes requis", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Annuler le vote", "app.containers.IdeasShow.proposals.VoteControl.days": "jours", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "notre FAQ", "app.containers.IdeasShow.proposals.VoteControl.hours": "heures", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Statut et votes", "app.containers.IdeasShow.proposals.VoteControl.minutes": "min", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Plus d'informations", "app.containers.IdeasShow.proposals.VoteControl.vote": "Vote", "app.containers.IdeasShow.proposals.VoteControl.voted": "Voté", "app.containers.IdeasShow.proposals.VoteControl.votedText": "Vous serez informé lorsque cette proposition passera à l'étape suivante. Il reste {x, plural, =0 {moins d'un jour} one {{xDays} jour} other {{xDays} jours}}.", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Votre vote a été enregistré !", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Malheureusement, vous ne pouvez pas voter pour cette proposition. Consultez {link} pour en savoir plus à ce sujet.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {moins d'un jour} one {un jour} other {# jours}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {aucun vote} one {1 vote} other {# votes}}", "app.containers.IdeasShow.questionEmailSharingBody": "Rejoignez la discussion sur cette question « {postTitle} » à {postUrl} !", "app.containers.IdeasShow.questionEmailSharingSubject": "Rejo<PERSON>ez la discussion : {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Votre question a été publiée avec succès !", "app.containers.IdeasShow.questionTwitterMessage": "Rejo<PERSON>ez la discussion : {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Rejo<PERSON>ez la discussion : {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Pourquoi voulez vous signaler cet élément ?", "app.containers.IdeasShow.share": "Partager", "app.containers.IdeasShow.sharingModalSubtitle": "Donnez à cette idée l'écho qu'elle mérite en informant vos amis.", "app.containers.IdeasShow.sharingModalTitle": "Merci d'avoir soumis votre idée !", "app.containers.Navbar.completeOnboarding": "Terminer la configuration du compte", "app.containers.Navbar.completeProfile": "Profil complet", "app.containers.Navbar.confirmEmail2": "Confirmez l'e-mail", "app.containers.Navbar.unverified": "Non vérifié", "app.containers.Navbar.verified": "Vérifié", "app.containers.NewAuthModal.beforeYouFollow": "Avant de suivre", "app.containers.NewAuthModal.beforeYouParticipate": "Avant de participer", "app.containers.NewAuthModal.completeYourProfile": "Complétez votre profil", "app.containers.NewAuthModal.confirmYourEmail": "Confirmez votre adresse e-mail", "app.containers.NewAuthModal.logIn": "Se connecter", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Veuillez lire attentivement les termes ci-dessous pour continuer.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Veuillez compléter votre profil.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Retour aux options de connexion", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Vous n'avez pas de compte ? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "S'inscrire", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Le code doit comporter 4 chiffres.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Continuer avec FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Aucune méthode d'authentification n'est activée sur cette plateforme.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "En continuant, vous acceptez de recevoir des e-mails de cette plateforme. Vous pouvez sélectionner les e-mails que vous souhaitez recevoir sur la page \"Mes réglages\".", "app.containers.NewAuthModal.steps.EmailSignUp.email": "E-mail", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Fournissez une adresse e-mail au format correct, <NAME_EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Fournir une adresse e-mail", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Saisissez votre adresse e-mail pour continuer.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Mot de passe oublié ?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Connectez-vous à votre compte : {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Veuillez saisir votre mot de passe", "app.containers.NewAuthModal.steps.Password.password": "Mot de passe", "app.containers.NewAuthModal.steps.Password.rememberMe": "Se souvenir de moi", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Ne pas sélectionner si vous utilisez un ordinateur public", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "<PERSON>rci d’avoir confirmé votre adresse email.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Votre identité a été vérifiée. Vous êtes maintenant un membre à part entière de la communauté sur cette plateforme.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Votre compte a été vérifié avec succès !", "app.containers.NewAuthModal.steps.close": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.continue": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Quels sujets vous intéressent ?", "app.containers.NewAuthModal.youCantParticipate": "Vous ne pouvez pas participer", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {aucune notification non vue} one {1 notification non vue} other {# notifications non vues}}", "app.containers.NotificationMenu.adminRightsReceived": "Vous êtes à présent administrateur de la plateforme", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Votre commentaire sur '{postTitle}' a été supprimé par un administrateur car {reasonCode, select, irrelevant {il a été considéré hors sujet} inappropriate {son contenu a été considéré inapproprié} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} a accepté de soutenir votre proposition", "app.containers.NotificationMenu.deletedUser": "<PERSON><PERSON><PERSON> inconnu", "app.containers.NotificationMenu.error": "Les notifications n'ont pas pu être chargées", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} a commenté en interne une contribution qui vous a été attribuée", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} a commenté en interne une contribution que vous avez commentée en interne", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} a commenté en interne une contribution dans un projet que vous gérez", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} a commenté en interne une contribution non attribuée dans un projet non géré", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} a commenté votre commentaire interne", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} vous a invité à soutenir une contribution", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} vous a invité à soutenir une idée", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} vous a invité à soutenir une proposition", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} vous a invité à soutenir un problème", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} vous a invité à soutenir une option", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} vous a invité à soutenir une pétition", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} vous a invité à soutenir un projet", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} vous a invité à soutenir une proposition", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} vous a invité à soutenir une question", "app.containers.NotificationMenu.loadMore": "Afficher la suite...", "app.containers.NotificationMenu.loading": "Chargement des notifications...", "app.containers.NotificationMenu.mentionInComment": "{name} vous a mentionné dans un commentaire", "app.containers.NotificationMenu.mentionInInternalComment": "{name} vous a mentionné dans un commentaire interne", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{officialName} vous mentionné dans une mise à jour officielle", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Vous n'avez pas soumis votre réponse à l'enquête", "app.containers.NotificationMenu.noNotifications": "Vous n'avez pas de notifications", "app.containers.NotificationMenu.notificationsLabel": "Notifications", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} a publié une mise à jour officielle sur une contribution que vous suivez", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} a publié une mise à jour officielle sur une idée que vous suivez", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} a publié une mise à jour officielle sur une proposition que vous suivez", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} a publié une mise à jour officielle sur un problème que vous suivez", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} a publié une mise à jour officielle sur une option que vous suivez", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} a publié une mise à jour officielle sur une pétition que vous suivez", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} a publié une mise à jour officielle sur un projet que vous suivez", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} a publié une mise à jour officielle sur une proposition que vous suivez", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} a publié une mise à jour officielle sur une question que vous suivez", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} vous a été attribué", "app.containers.NotificationMenu.projectModerationRightsReceived": "Vous êtes maintenant un administrateur projet de {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} est entré dans une nouvelle phase", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} va franchir une nouvelle étape sur {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "Un nouveau projet a été publié", "app.containers.NotificationMenu.projectReviewRequest": "{name} a demandé l'approbation pour publier le projet « {projectTitle} »", "app.containers.NotificationMenu.projectReviewStateChange": "{name} a approuvé « {projectTitle} »", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "Le statut de « {ideaTitle} » est passé à « {status} »", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} a atteint le nombre de voix requis", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} a accepté votre invitation", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} a commenté une contribution que vous suivez", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} a commenté une idée que vous suivez", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} a commenté une proposition que vous suivez", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} a commenté un problème que vous suivez", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} commenté une option que vous suivez", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} a commenté une pétition que vous suivez", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} commenté un projet que vous suivez", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} a commenté une proposition que vous suivez", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} a commenté une question que vous suivez", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} a signalé \"{postTitle}\" comme spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} a répondu à votre commentaire", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} a signalé un commentaire sur « {postTitle} » comme spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Vous n'avez pas enregistré vos votes", "app.containers.NotificationMenu.votingBasketSubmitted": "Votre vote a été enregistré avec succès", "app.containers.NotificationMenu.votingLastChance": "Dernière chance de voter pour {phaseTitle}", "app.containers.NotificationMenu.votingResults": "Les résultats du vote pour {phaseTitle} sont disponibles", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} vous a attribué {postTitle}", "app.containers.PasswordRecovery.emailError": "Ceci ne ressemble pas à un courriel valide", "app.containers.PasswordRecovery.emailLabel": "Adresse e-mail", "app.containers.PasswordRecovery.emailPlaceholder": "Adresse e-mail", "app.containers.PasswordRecovery.helmetDescription": "Réinitialiser votre mot de passe", "app.containers.PasswordRecovery.helmetTitle": "Réinitialiser votre mot de passe", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Si cette adresse e-mail existe sur la plateforme, un lien de réinitialisation du mot de passe a été envoyé.", "app.containers.PasswordRecovery.resetPassword": "Recevoir un lien de réinitialisation", "app.containers.PasswordRecovery.submitError": "Nous n'avons pas trouvé de compte lié à cette adresse e-mail. Vous pouvez essayer de vous inscrire par contre.", "app.containers.PasswordRecovery.subtitle": "Où peut-on envoyer un lien pour changer votre mot de passe ?", "app.containers.PasswordRecovery.title": "Réinitialisation de mot de passe", "app.containers.PasswordReset.helmetDescription": "Page de réinitialisation de votre mot de passe", "app.containers.PasswordReset.helmetTitle": "Réinitialisez votre mot de passe", "app.containers.PasswordReset.login": "Se connecter", "app.containers.PasswordReset.passwordError": "Votre mot de passe doit contenir au moins 8 caractères", "app.containers.PasswordReset.passwordLabel": "Mot de passe", "app.containers.PasswordReset.passwordPlaceholder": "Nouveau mot de passe", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Votre mot de passe a été mis à jour avec succès.", "app.containers.PasswordReset.pleaseLogInMessage": "Connectez-vous avec votre nouveau mot de passe.", "app.containers.PasswordReset.requestNewPasswordReset": "Demander la réinitialisation du mot de passe", "app.containers.PasswordReset.submitError": "Une erreur est survenue. Veuillez réessayer.", "app.containers.PasswordReset.title": "Réinitialisez votre mot de passe", "app.containers.PasswordReset.updatePassword": "Confirmer le nouveau mot de passe", "app.containers.ProjectFolderCards.allProjects": "Tous les projets", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} travaille actuellement sur", "app.containers.ProjectFolderShowPage.editFolder": "Modifier le dossier", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informations sur ce projet", "app.containers.ProjectFolderShowPage.metaTitle1": "Dossier : {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Voir plus", "app.containers.ProjectFolderShowPage.seeLess": "Voir moins", "app.containers.ProjectFolderShowPage.share": "Partager", "app.containers.Projects.PollForm.document": "Document", "app.containers.Projects.PollForm.formCompleted": "Merci pour la soumission! ", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Vous avez déjà répondu à ce sondage.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Ce sondage ne peut être complété que lorsque cette phase est active.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "{tenant<PERSON><PERSON>, select, vitrysurseine {Cette enquête n'est pas activée actuellement.} other {Ce sondage n'est pas activé actuellement .}}", "app.containers.Projects.PollForm.pollDisabledNotPossible": "{tenant<PERSON><PERSON>, select, vitrysurseine {Il est actuellement impossible de participer à cette enquête} other {Il est actuellement impossible de participer à ce sondage.}}", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Le sondage n’est plus disponible, puisque ce projet n’est plus actif.", "app.containers.Projects.PollForm.sendAnswer": "Envoyer", "app.containers.Projects.a11y_phase": "Phase {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Vue d'ensemble des phases", "app.containers.Projects.a11y_titleInputs": "Toutes les contributions soumises à ce projet", "app.containers.Projects.a11y_titleInputsPhase": "Toutes les contributions soumises à cette phase", "app.containers.Projects.accessRights": "Droits d'accès", "app.containers.Projects.addedToBasket": "Ajouté à votre panier", "app.containers.Projects.allocateBudget": "Allouez votre budget", "app.containers.Projects.archived": "Archivé", "app.containers.Projects.basketSubmitted": "Votre panier a été soumis !", "app.containers.Projects.contributions": "Suggestions", "app.containers.Projects.createANewPhase": "Créer une nouvelle phase", "app.containers.Projects.currentPhase": "Phase en cours", "app.containers.Projects.document": "Document", "app.containers.Projects.editProject": "Modifier ce projet", "app.containers.Projects.emailSharingBody": "Que pensez-vous de cette initiative ? Votez-la et partagez la discussion à {initiativeUrl} pour faire entendre votre voix !", "app.containers.Projects.emailSharingSubject": "Soutenir mon initiative : {initiativeTitle}.", "app.containers.Projects.endedOn": "<PERSON><PERSON><PERSON><PERSON> {date}", "app.containers.Projects.events": "Evénements", "app.containers.Projects.header": "{tenant<PERSON><PERSON>, select, frw {Communes} other {Projets}}", "app.containers.Projects.ideas": "Idées", "app.containers.Projects.information": "Informations", "app.containers.Projects.initiatives": "Initiatives", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Réviser le document", "app.containers.Projects.invisibleTitlePhaseAbout": "A propos de cette phase", "app.containers.Projects.invisibleTitlePoll": "{tenant<PERSON><PERSON>, select, vitrysurseine {<PERSON><PERSON><PERSON><PERSON><PERSON> à l'enquête} other {Répondre au sondage}}", "app.containers.Projects.invisibleTitleSurvey": "Répondre à l'enquête", "app.containers.Projects.issues": "Problèmes", "app.containers.Projects.liveDataMessage": "En tant qu'administrateur, les chiffres de participation qui vous sont présentés sont mis à jour en temps réel, contrairement aux autres utilisateurs qui, pour des raisons de performance, voient des données mises en cache. Cela peut entraîner de légères différences dans les chiffres.", "app.containers.Projects.location": "Localisation :", "app.containers.Projects.manageBasket": "<PERSON><PERSON><PERSON> le panier", "app.containers.Projects.meetMinBudgetRequirement": "Respectez le budget minimum pour soumettre votre panier.", "app.containers.Projects.meetMinSelectionRequirement": "Respectez la sélection requise pour soumettre votre panier.", "app.containers.Projects.metaTitle1": "Projet : {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Un budget minimum est requis", "app.containers.Projects.myBasket": "<PERSON><PERSON>", "app.containers.Projects.navPoll": "{tenant<PERSON><PERSON>, select, vitrysurseine {Enquê<PERSON>} other {Sondage}}", "app.containers.Projects.navSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.newPhase": "Nouvelle phase", "app.containers.Projects.nextPhase": "Phase suivante", "app.containers.Projects.noEndDate": "Pas de date de fin", "app.containers.Projects.noItems": "Vous n'avez encore rien sélectionné", "app.containers.Projects.noPastEvents": "Aucun événement passé à afficher", "app.containers.Projects.noPhaseSelected": "Pas de phase sélectionnée", "app.containers.Projects.noUpcomingOrOngoingEvents": "Aucun événement à venir ou en cours n'est actuellement prévu.", "app.containers.Projects.offlineVotersTooltip": "Ce nombre ne prend pas en compte les votes hors ligne.", "app.containers.Projects.options": "Options", "app.containers.Projects.participants": "Participants", "app.containers.Projects.participantsTooltip4": "Ce chiffre inclut également les réponses anonymes à l'enquête. Les réponses anonymes sont possibles si l'enquête est ouverte à tous (voir l'onglet {accessRightsLink} de ce projet).", "app.containers.Projects.pastEvents": "Evénements passés", "app.containers.Projects.petitions": "Pétitions", "app.containers.Projects.phases": "Phases", "app.containers.Projects.previousPhase": "Phase précédente", "app.containers.Projects.project": "Projet", "app.containers.Projects.projectTwitterMessage": "Faites entendre votre voix ! Participez à {projectName} | {orgName}", "app.containers.Projects.projects": "Projets", "app.containers.Projects.proposals": "Propositions", "app.containers.Projects.questions": "Questions", "app.containers.Projects.readLess": "<PERSON><PERSON> moins", "app.containers.Projects.readMore": "Voir plus", "app.containers.Projects.removeItem": "Retirer l'élément", "app.containers.Projects.requiredSelection": "Sélection obligatoire", "app.containers.Projects.reviewDocument": "Réviser le document", "app.containers.Projects.seeTheContributions": "Voir les suggestions", "app.containers.Projects.seeTheIdeas": "Voir les idées", "app.containers.Projects.seeTheInitiatives": "Voir les initiatives", "app.containers.Projects.seeTheIssues": "Voir les problèmes", "app.containers.Projects.seeTheOptions": "Voir les options", "app.containers.Projects.seeThePetitions": "Voir les pétitions", "app.containers.Projects.seeTheProjects": "Voir les projets", "app.containers.Projects.seeTheProposals": "Voir les propositions", "app.containers.Projects.seeTheQuestions": "Voir les questions", "app.containers.Projects.seeUpcomingEvents": "Voir les prochains événements", "app.containers.Projects.share": "Partager", "app.containers.Projects.shareThisProject": "Partager ce projet", "app.containers.Projects.submitMyBasket": "So<PERSON><PERSON><PERSON>", "app.containers.Projects.survey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.takeThePoll": "{tenant<PERSON><PERSON>, select, vitrysurseine {<PERSON><PERSON><PERSON><PERSON><PERSON> à l'enquête} other {Répondre au sondage}}", "app.containers.Projects.takeTheSurvey": "Répondre à l'enquête", "app.containers.Projects.timeline": "Ligne du temps", "app.containers.Projects.upcomingAndOngoingEvents": "Événements à venir et en cours", "app.containers.Projects.upcomingEvents": "Événements à venir", "app.containers.Projects.whatsAppMessage": "{projectName} | de la plateforme de participation de {orgName}", "app.containers.Projects.yourBudget": "Budget total", "app.containers.ProjectsIndexPage.metaDescription": "Découvrez tous les projets en cours à {orgName} pour comprendre comment vous pouvez participer. \n Venez discutez des sujets qui comptent pour vous.", "app.containers.ProjectsIndexPage.metaTitle1": "Projets | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "{tenant<PERSON><PERSON>, select, frw {Communes} other {Projets}}", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Je souhaite participer", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Je {signInLink} ou {signUpLink} pour me porter volontaire\n", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "L'inscription à cette activité n'est pas ouverte pour le moment.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "me connecte", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "m'inscris", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Je ne souhaite plus participer", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {pas de volontaires} one {# volontaire} other {# volontaires}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Attention : Les enquêtes intégrées peuvent poser des problèmes d'accessibilité pour les utilisateurs de lecteurs d'écran. Si vous rencontrez des difficultés, veuillez contacter l'administrateur de la plateforme pour obtenir un lien vers l'enquête sur la plateforme d'origine. Vous pouvez également demander d'autres moyens pour répondre à l'enquête.", "app.containers.ProjectsShowPage.process.survey.survey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Pour savoir si vous pouvez participer à cette enquête, {logInLink}.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Vous pourrez participer à cette enquête lorsque cette phase du processus sera active.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Veuillez {completeRegistrationLink} pour répondre à l'enquête.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "<PERSON>tte enquête n'est pas activée actuellement", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "La participation à cette enquête nécessite une vérification de votre identité. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "L'enquête n'est plus disponible, car ce projet n'est plus actif.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "compléter l'inscription", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "connecter", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "inscrire", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Vérifiez votre compte maintenant.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Seuls certains utilisateurs peuvent réviser ce document. \nVeuillez vous {logInLink} ou vous {signUpLink} d'abord.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Ce document ne peut être révisé que lorsque cette phase est active.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Veuillez {completeRegistrationLink} pour réviser le document.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Malheureusement, vous n'avez pas les permissions nécessaires pour réviser ce document.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Pour pouvoir réviser ce document, il est nécessaire de vérifier votre compte. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Le document n'est plus disponible, car ce projet n'est plus actif.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(dont 1 hors ligne)} other {(dont # hors ligne)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 choix} other {# choix}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "Le pourcentage de participants qui ont choisi cette option.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "Le pourcentage des votes totaux recueilli par cette option.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Coût :", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Afficher plus", "app.containers.ReactionControl.a11y_likesDislikes": "Total des j'aime : {likesCount}, total des je n'aime pas : {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Vous avez supprimé votre mention « Je n'aime pas » pour cette contribution avec succès.", "app.containers.ReactionControl.cancelLikeSuccess": "Vous avez supprimé votre mention « J'aime » pour cette contribution avec succès.", "app.containers.ReactionControl.dislikeSuccess": "Vous avez ajouté une mention « Je n'aime pas » pour cette contribution avec succès.", "app.containers.ReactionControl.likeSuccess": "Vous avez ajouté une mention « J'aime » pour cette contribution avec succès.", "app.containers.ReactionControl.reactionErrorSubTitle": "En raison d'une erreur, votre réaction n'a pas pu être enregistrée. Veuillez réessayer dans quelques minutes.", "app.containers.ReactionControl.reactionSuccessTitle": "Votre réaction a été enregistrée avec succès !", "app.containers.ReactionControl.vote": "Vote", "app.containers.ReactionControl.voted": "Voté", "app.containers.SearchInput.a11y_cancelledPostingComment": "Annulation de la publication du commentaire.", "app.containers.SearchInput.a11y_commentsHaveChanged": "Des commentaires ont été chargés dans l'ordre suivant : {sortOder}.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# événements ont été chargés} one {# événement a été chargé} other {# événements ont été chargés}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# résultats ont été chargés} one {# résultat a été chargé} other {# résultats ont été chargés}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# résultats de recherche ont été chargés} one {# résultat de recherche a été chargé} other {# résultats de recherche ont été chargés}}.", "app.containers.SearchInput.removeSearchTerm": "Supprimer le terme de recherche", "app.containers.SearchInput.searchAriaLabel": "<PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchLabel": "<PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchTerm": "Terme de recherche : {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect est la solution proposée par l'État pour sécuriser et simplifier la connexion à vos services en ligne.", "app.containers.SignIn.or": "Ou", "app.containers.SignIn.signInError": "Les informations fournies ne sont pas correctes. Cliquez sur « Mot de passe oublié » ci-dessous pour réinitialiser votre mot de passe.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Utilisez FranceConnect pour vous connecter, créer ou vérifier votre compte.", "app.containers.SignIn.whatIsFranceConnect": "Qu'est-ce que FranceConnect ?", "app.containers.SignUp.adminOptions2": "Pour les administrateurs et les gestionnaires de projet", "app.containers.SignUp.backToSignUpOptions": "Retour aux options d'inscription", "app.containers.SignUp.continue": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.emailConsent": "En vous inscrivant, vous acceptez de recevoir des e-mails envoyés depuis cette plateforme. Vous pouvez paramétrer quels e-mails vous recevez dans vos préférences utilisateur.", "app.containers.SignUp.emptyFirstNameError": "Entrez votre prénom", "app.containers.SignUp.emptyLastNameError": "Entrez votre nom", "app.containers.SignUp.firstNamesLabel": "Prénom", "app.containers.SignUp.goToLogIn": "Vous avez déjà un compte ? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "J'ai lu et j'approuve {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "J'ai lu et j'approuve {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "J'accepte que les données soient utilisées sur mitgestalten.wien.gv.at. De plus amples informations peuvent être trouvées {link}.", "app.containers.SignUp.invitationErrorText": "Votre invitation a expiré ou a déjà été utilisée. Si vous avez déjà cliqué sur le lien d'invitation pour créer un compte, veuillez vous connecter. Sinon, inscrivez-vous pour créer un nouveau compte.", "app.containers.SignUp.lastNameLabel": "Nom", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Suivez vos zones préférées pour recevoir des notifications lorsque du nouveau contenu les concernant est publié:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Su<PERSON>z vos sujets préférés pour recevoir des notifications lorsque du nouveau contenu lié à ces sujets est publié:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Sauvegarder les préférences", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Ignorer pour le moment", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Acceptez notre politique de confidentialité pour continuer", "app.containers.SignUp.signUp2": "S'inscrire", "app.containers.SignUp.skip": "Sauter cette étape", "app.containers.SignUp.tacError": "Accepter nos Conditions générales d'utilisation est nécessaire pour continuer", "app.containers.SignUp.thePrivacyPolicy": "la politique de confidentialité", "app.containers.SignUp.theTermsAndConditions": "les Conditions générales d'utilisation", "app.containers.SignUp.unknownError": "Une erreur est survenue. Veuillez réessayer.", "app.containers.SignUp.viennaConsentEmail": "Adresse e-mail", "app.containers.SignUp.viennaConsentFirstName": "Prénom", "app.containers.SignUp.viennaConsentFooter": "Vous pouvez modifier les informations de votre profil après vous être connecté. Si vous avez déjà un compte avec la même adresse e-mail sur mitgestalten.wien.gv.at, il sera lié à votre compte actuel.", "app.containers.SignUp.viennaConsentHeader": "Les données suivantes seront transmises :", "app.containers.SignUp.viennaConsentLastName": "Nom", "app.containers.SignUp.viennaConsentUserName": "Nom de l'utilisateur", "app.containers.SignUp.viennaDataProtection": "la politique de confidentialité de vienne", "app.containers.SiteMap.contributions": "Suggestions", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "Problèmes", "app.containers.SiteMap.options": "Options", "app.containers.SiteMap.projects": "Projets", "app.containers.SiteMap.questions": "Questions", "app.containers.SpamReport.buttonSave": "Signaler", "app.containers.SpamReport.buttonSuccess": "Enregistré !", "app.containers.SpamReport.inappropriate": "C'est inapproprié ou offensant", "app.containers.SpamReport.messageError": "Une erreur est survenue lors de l'envoi, ve<PERSON><PERSON><PERSON> réessayer", "app.containers.SpamReport.messageSuccess": "Le signalement a été effectué", "app.containers.SpamReport.other": "Autre raison", "app.containers.SpamReport.otherReasonPlaceholder": "Description", "app.containers.SpamReport.wrong_content": "<PERSON>la n'appartient pas ici", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Supprimer l'image du profil", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Vos votes sur les propositions encore ouvertes au vote seront supprimés. En revanche, les votes sur les propositions dont la période de vote est terminée ne seront pas effacés.", "app.containers.UsersEditPage.addPassword": "Ajouter un mot de passe", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Pour participer à des projets pour les citoyens vérifiés.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Vérifiez votre identité", "app.containers.UsersEditPage.bio": "À propos de vous", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Vous ne pouvez pas modifier ce champ car il contient des informations vérifiées.", "app.containers.UsersEditPage.buttonSuccessLabel": "Enregistré !", "app.containers.UsersEditPage.cancel": "Annuler", "app.containers.UsersEditPage.changeEmail": "Modifier mon adresse e-mail", "app.containers.UsersEditPage.changePassword2": "Modifier mon mot de passe", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Veuillez cliquer ici pour mettre à jour votre vérification.", "app.containers.UsersEditPage.conditionsLinkText": "nos conditions", "app.containers.UsersEditPage.contactUs": "Une autre raison pour partir ? {feedbackLink} et peut-être pouvons-nous vous aider.", "app.containers.UsersEditPage.deleteAccountSubtext": "Nous sommes désolé de vous voir partir.", "app.containers.UsersEditPage.deleteMyAccount": "Supprimer mon compte", "app.containers.UsersEditPage.deleteYourAccount": "Supprimer mon compte", "app.containers.UsersEditPage.deletionSection": "Supprimer mon compte", "app.containers.UsersEditPage.deletionSubtitle": "Cette action est irréversible. Le contenu publié sur la plateforme sera anonymisé. Si vous souhaitez supprimer tout votre contenu, vous pouvez nous contacter à <EMAIL>.", "app.containers.UsersEditPage.email": "E-mail", "app.containers.UsersEditPage.emailEmptyError": "Fournir une adresse e-mail", "app.containers.UsersEditPage.emailInvalidError": "Fournissez une adresse e-mail au format correct, <NAME_EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Cliquez ici", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/JY5GYA?source={url}", "app.containers.UsersEditPage.firstNames": "Prénom", "app.containers.UsersEditPage.firstNamesEmptyError": "<PERSON><PERSON><PERSON> les prénoms", "app.containers.UsersEditPage.h1": "Les informations de votre compte", "app.containers.UsersEditPage.h1sub": "Modifiez les informations de votre compte", "app.containers.UsersEditPage.image": "Image d’avatar", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Cliquez pour sélectionner une photo de profil (maximum 5 Mo)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Tous les paramètres de votre profile", "app.containers.UsersEditPage.language": "<PERSON><PERSON>", "app.containers.UsersEditPage.lastName": "Nom", "app.containers.UsersEditPage.lastNameEmptyError": "Fournir un nom de famille", "app.containers.UsersEditPage.loading": "Chargement...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Vous pouvez modifier votre adresse e-mail ou votre mot de passe ici.", "app.containers.UsersEditPage.loginCredentialsTitle": "Identifiants de connexion", "app.containers.UsersEditPage.messageError": "Nous n'avons pas pu enregistrer votre profil. Réessayez plus tard <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Votre profil a été enregistré ", "app.containers.UsersEditPage.metaDescription": "Voici la page de paramétrage du profil de {firstName} {lastName} sur la plateforme de participation en ligne de {tenantName}. <PERSON><PERSON>, vous pouvez vérifier votre identité, modifier les informations de votre compte, supprimer votre compte et modifier vos préférences de courrier électronique.", "app.containers.UsersEditPage.metaTitle1": "Page des paramètres du profil de {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Une fois que vous aurez cliqué sur ce bouton, nous n'aurons aucuns moyens de restaurer votre compte.", "app.containers.UsersEditPage.noNameWarning2": "Vous apparaissez actuellement sur la plateforme sous le nom de « {displayName} » car vous n'avez pas renseigné de nom. Il s'agit d'un nom généré automatiquement. Si vous souhaitez le modifier, veuillez saisir votre nom ci-dessous.", "app.containers.UsersEditPage.notificationsSubTitle": "Quels types de notifications par email souhaitez-vous recevoir ?", "app.containers.UsersEditPage.notificationsTitle": "Notifications par email", "app.containers.UsersEditPage.password": "Choisissez un nouveau mot de passe", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Fournissez un mot de passe d'au moins {minimumPasswordLength} caractères", "app.containers.UsersEditPage.passwordAddSection": "Ajouter un mot de passe", "app.containers.UsersEditPage.passwordAddSubtitle2": "Définissez un mot de passe et connectez-vous facilement à la plateforme, sans avoir à confirmer votre adresse e-mail à chaque fois.", "app.containers.UsersEditPage.passwordChangeSection": "Modifiez votre mot de passe", "app.containers.UsersEditPage.passwordChangeSubtitle": "Confirmez votre mot de passe actuel et modifiez votre mot de passe.", "app.containers.UsersEditPage.privacyReasons": "Si vous vous inquiétez pour votre vie privée, vous pouvez lire {conditionsLink}.", "app.containers.UsersEditPage.processing": "Envoi...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Le prénom est obligatoire lorsque le nom de famille est fourni", "app.containers.UsersEditPage.reasonsToStayListTitle": "Avant de partir...", "app.containers.UsersEditPage.submit": "Enregistrer les modifications", "app.containers.UsersEditPage.tooManyEmails": "Vous recevez trop d'emails? Vous pouvez changer votre préférences dans les paramètres de votre profil.", "app.containers.UsersEditPage.updateverification": "Vos informations officielles ont-elles changé ? {reverifyButton}", "app.containers.UsersEditPage.user": "Quand voulez-vous être tenu notifié?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Vous pouvez participer à des projets qui ne sont accessibles qu'aux citoyens vérifiés.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Vous êtes un citoyen vérifié", "app.containers.UsersEditPage.verifyNow": "Vérifier maintenant", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Téléchargez vos réponses (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {pas de j'aime} one {1 j'aime} other {# j'aime}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Message dans lequel ce commentaire a été publié :", "app.containers.UsersShowPage.areas": "Zones géographiques", "app.containers.UsersShowPage.commentsWithCount": "Commentaires ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Modifier mon profil", "app.containers.UsersShowPage.emptyInfoText": "Vous ne suivez aucun élément correspondant au filtre spécifié ci-dessus.", "app.containers.UsersShowPage.eventsWithCount": "Événements ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "<PERSON><PERSON><PERSON> ({followingCount})", "app.containers.UsersShowPage.inputs": "Contributions", "app.containers.UsersShowPage.invisibleTitlePostsList": "Tous les contributions soumis par ce participant", "app.containers.UsersShowPage.invisibleTitleUserComments": "Tous les commentaires postés par cet utilisateur", "app.containers.UsersShowPage.loadMore": "Charger la suite", "app.containers.UsersShowPage.loadMoreComments": "Afficher plus de commentaires", "app.containers.UsersShowPage.loadingComments": "Chargement des commentaires...", "app.containers.UsersShowPage.loadingEvents": "Chargement des événements...", "app.containers.UsersShowPage.memberSince": "Membre depuis le {date}", "app.containers.UsersShowPage.metaTitle1": "Profil de {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "<PERSON>tte personne n'a pas encore posté de commentaire.", "app.containers.UsersShowPage.noCommentsForYou": "Il n'y a pas de commentaires pour le moment.", "app.containers.UsersShowPage.noEventsForUser": "Vous n'avez encore participé à aucun événement.", "app.containers.UsersShowPage.postsWithCount": "Contributions ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Dossiers de projets", "app.containers.UsersShowPage.projects": "Projets", "app.containers.UsersShowPage.proposals": "Propositions", "app.containers.UsersShowPage.seePost": "Voir l'article", "app.containers.UsersShowPage.surveyResponses": "Réponses ({responses})", "app.containers.UsersShowPage.topics": "Étiquettes", "app.containers.UsersShowPage.tryAgain": "Une erreur s’est produite, veuillez réessayer ", "app.containers.UsersShowPage.userShowPageMetaDescription": "Voici la page de profil de {firstName} {lastName} sur la plate-forme de participation en ligne de {orgName}. Voici un aperçu de tous leurs articles.", "app.containers.VoteControl.close": "<PERSON><PERSON><PERSON>", "app.containers.VoteControl.voteErrorTitle": "Oups... Une erreur est survenue", "app.containers.admin.ContentBuilder.default": "par défaut", "app.containers.admin.ContentBuilder.imageTextCards": "Cartes image et texte", "app.containers.admin.ContentBuilder.infoWithAccordions": "Infos et sections accordéons", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 colonne", "app.containers.admin.ContentBuilder.projectDescription": "Description du projet", "app.containers.app.navbar.admin": "Panneau admin", "app.containers.app.navbar.allProjects": "{tenant<PERSON><PERSON>, select, frw {Toutes les communes} other {Tous les projets}}", "app.containers.app.navbar.ariaLabel": "Primaire", "app.containers.app.navbar.closeMobileNavMenu": "Fermer le menu de navigation mobile", "app.containers.app.navbar.editProfile": "<PERSON><PERSON>", "app.containers.app.navbar.fullMobileNavigation": "Mobile complet", "app.containers.app.navbar.logIn": "Se connecter", "app.containers.app.navbar.logoImgAltText": "{orgName} Accueil", "app.containers.app.navbar.myProfile": "Mon activité", "app.containers.app.navbar.search": "<PERSON><PERSON><PERSON>", "app.containers.app.navbar.showFullMenu": "Affiche<PERSON> le menu complet", "app.containers.app.navbar.signOut": "Se déconnecter", "app.containers.eventspage.errorWhenFetchingEvents": "Une erreur s'est produite lors du chargement des événements. Veuillez essayer de recharger la page.", "app.containers.eventspage.events": "Événements", "app.containers.eventspage.eventsPageDescription": "Afficher tous les événements publiés sur la plateforme de participation de {orgName}.", "app.containers.eventspage.eventsPageTitle1": "Événements | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projets", "app.containers.eventspage.noPastEvents": "Aucun événement passé à afficher", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Aucun événement à venir ou en cours n'est actuellement prévu.", "app.containers.eventspage.pastEvents": "Evénements passés", "app.containers.eventspage.upcomingAndOngoingEvents": "Événements à venir et en cours", "app.containers.footer.accessibility-statement": "Accessibilité : partiellement conforme", "app.containers.footer.ariaLabel": "Secondaire", "app.containers.footer.cookie-policy": "Politique de cookies", "app.containers.footer.cookieSettings": "Paramètres des cookies", "app.containers.footer.feedbackEmptyError": "Le champ de feedbacks ne peut pas être vide.", "app.containers.footer.poweredBy": "Généré par", "app.containers.footer.privacy-policy": "Politique de confidentialité", "app.containers.footer.siteMap": "Plan du site", "app.containers.footer.terms-and-conditions": "Conditions générales", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "Annuler", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON>, je veux quitter", "app.containers.ideaHeading.editForm": "Modifier le formulaire", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Êtes-vous sûr de vouloir quitter ?", "app.containers.ideaHeading.leaveIdeaForm": "<PERSON><PERSON><PERSON> le formulaire", "app.containers.ideaHeading.leaveIdeaText": "Vos réponses ne seront pas enregistrées.", "app.containers.landing.cityProjects": "Les {tenant<PERSON><PERSON>, select, frw {communes} other {projets}}", "app.containers.landing.completeProfile": "Complétez votre profil", "app.containers.landing.completeYourProfile": "Bienvenue, {firstName}. Il est temps de compléter votre profil.", "app.containers.landing.createAccount": "S'inscrire", "app.containers.landing.defaultSignedInMessage": "{orgName} vous écoute. Faites entendre votre voix !", "app.containers.landing.doItLater": "Je le ferai plus tard", "app.containers.landing.new": "nouveau", "app.containers.landing.subtitleCity": "Bienvenue sur la plateforme de participation officielle de {orgName}", "app.containers.landing.titleCity": "Ensemble, définissons l'avenir de {orgName}", "app.containers.landing.twitterMessage": "Votez pour {ideaTitle} sur", "app.containers.landing.upcomingEventsWidgetTitle": "Événements à venir et en cours", "app.containers.landing.userDeletedSubtitle": "Vous pouvez créer un nouveau compte à tout moment ou {contactLink} pour nous indiquer ce que nous devrions améliorer.", "app.containers.landing.userDeletedSubtitleLinkText": "envoyer un message", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/JY5GYA?source={url}", "app.containers.landing.userDeletedTitle": "Votre compte a été supprimé.", "app.containers.landing.userDeletionFailed": "Une erreur est survenue lors de la suppression de votre compte, nous avons été notifié et faisons notre mieux pour résoudre ce problème. Réessayer plus tard.", "app.containers.landing.verifyNow": "Vérifier maintenant", "app.containers.landing.verifyYourIdentity": "Devenir un citoyen vérifié", "app.containers.landing.viewAllEventsText": "Tous les événements", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Retour au dossier", "app.errors.after_end_at": "La date de début a lieu après la date de fin", "app.errors.avatar_carrierwave_download_error": "Impossible de télécharger l'image de profil.", "app.errors.avatar_carrierwave_integrity_error": "Le type de fichier choisi pour l'image de profil n'est pas autorisé.", "app.errors.avatar_carrierwave_processing_error": "Impossible de traiter l'image de profil.", "app.errors.avatar_extension_blacklist_error": "Le type de fichier utilisé pour l'image de profil n'est pas autorisé. Les extensions autorisées sont : jpg, jpeg, gif et png.", "app.errors.avatar_extension_whitelist_error": "Le type de fichier utilisé pour l'image de profil n'est pas autorisé. Les extensions autorisées sont : jpg, jpeg, gif et png.", "app.errors.banner_cta_button_multiloc_blank": "Saisissez le texte du bouton.", "app.errors.banner_cta_button_url_blank": "Entrez un lien.", "app.errors.banner_cta_button_url_url": "Entrez un lien valide. Assurez-vous que le lien commence par 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Saisissez le texte du bouton.", "app.errors.banner_cta_signed_in_url_blank": "Entrez un lien.", "app.errors.banner_cta_signed_in_url_url": "Entrez un lien valide. Assurez-vous que le lien commence par 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Saisissez le texte du bouton.", "app.errors.banner_cta_signed_out_url_blank": "Entrez un lien.", "app.errors.banner_cta_signed_out_url_url": "Entrez un lien valide. Assurez-vous que le lien commence par 'https://'.", "app.errors.base_includes_banned_words": "Vous avez peut-être utilisé un ou plusieurs mots qui sont considérés comme des blasphèmes. Veuillez modifier votre texte afin de supprimer tout blasphème qui pourrait être présent.", "app.errors.body_multiloc_includes_banned_words": "La description contient des mots considérés comme inappropriés.", "app.errors.bulk_import_idea_not_valid": "L'idée importée n'est pas valide : {value}.", "app.errors.bulk_import_image_url_not_valid": "Aucune image n'a pu être téléchargée depuis {value}. Assurez-vous que l'URL est valide et se termine par une extension de fichier telle que .png ou .jpg. Le problème se produit pour l'idée à la ligne {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Une des coordonnées de la localisation est manquante : \"{value}\". Le problème se produit pour l'idée à la ligne {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Les coordonnées de la localisation ne sont pas valides: \"{value}\". Seules les valeurs numériques sont acceptées. Le problème se produit pour l'idée à la ligne {row}.", "app.errors.bulk_import_malformed_pdf": "Le fichier PDF téléchargé semble être corrompu. Essayez d'exporter à nouveau le PDF depuis votre source, puis téléchargez-le à nouveau.", "app.errors.bulk_import_maximum_ideas_exceeded": "Le nombre maximum de {value} idées a été dépassé.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Le fichier PDF ne peut pas contenir plus de {value} pages.", "app.errors.bulk_import_not_enough_pdf_pages": "Les nombre de pages du PDF téléchargé est insuffisant. Il doit contenir au moins autant de pages que le modèle.", "app.errors.bulk_import_publication_date_invalid_format": "Le format de la date de publication est invalide : \"{value}\". Veuillez utiliser le format \"JJ-MM-AAAA\".", "app.errors.cannot_contain_ideas": "Cette phase contient {ideasCount, plural, one {une idée} other {{ideasCount} idées}} et la méthode de participation que vous essayez de changer ne prend pas les idées en compte. Veuillez retirer {ideasCount, plural, one {l'idée} other {les idées}} de la phase et réessayer.", "app.errors.cant_change_after_first_response": "Vous ne pouvez plus modifier ceci, car certains utilisateurs ont déjà répondu", "app.errors.category_name_taken": "Une catégorie portant ce nom existe déjà", "app.errors.confirmation_code_expired": "Le code a expiré. Veuillez demander un nouveau code.", "app.errors.confirmation_code_invalid": "Code de confirmation invalide. Veuillez vérifier votre e-mail pour le code correct ou essayez « Envoyer un nouveau code »", "app.errors.confirmation_code_too_many_resets": "Vous avez renvoyé le code de confirmation trop de fois. Veuillez nous contacter pour recevoir un code d'invitation à la place.", "app.errors.confirmation_code_too_many_retries": "Vous avez essayé trop de fois. Veuillez renvoyer un code ou essayez de modifier votre adresse e-mail.", "app.errors.email_already_active": "L'adresse e-mail {value} trouvée ligne {row} du fichier appartient a un utilisateur déjà inscrit", "app.errors.email_already_invited": "L'adresse {value} trouvée à la ligne {row} du fichier a déjà reçu une invitation", "app.errors.email_blank": "<PERSON><PERSON> ne peut pas être vide", "app.errors.email_domain_blacklisted": "Veuillez utiliser un domaine de messagerie différent pour vous inscrire.", "app.errors.email_invalid": "<PERSON><PERSON><PERSON>z entrer une adresse e-mail valide.", "app.errors.email_taken": "Un compte avec cette adresse e-mail existe déjà. Vous pouvez vous connecter à la place.", "app.errors.email_taken_by_invite": "{value} est déjà pris par une invitation en attente.", "app.errors.emails_duplicate": "Un ou plusieurs doubles de l'adresse e-mail {value} ont été trouvés aux lignes suivantes : ligne(s) {rows}", "app.errors.extension_whitelist_error": "Le format du fichier que vous avez essayé de téléverser n’est pas pris en charge.", "app.errors.file_extension_whitelist_error": "Le format du fichier que vous essayé de télécharger n’est pas pris en charge.", "app.errors.first_name_blank": "<PERSON><PERSON> ne peut pas être vide", "app.errors.generics.blank": "<PERSON><PERSON> ne peut pas être vide.", "app.errors.generics.invalid": "<PERSON><PERSON> ne ressemble pas à une valeur valide", "app.errors.generics.taken": "Ce courriel existe déjà. Un autre compte est lié à celui-ci.", "app.errors.generics.unsupported_locales": "Ce champ ne supporte pas les paramètres régionaux actuels.", "app.errors.group_ids_unauthorized_choice_moderator": "En tant qu'administrateur projet, vous pouvez contacter uniquement les personnes qui peuvent accéder à votre projet (s)", "app.errors.has_other_overlapping_phases": "Les projets ne peuvent pas avoir des phases qui se superposent.", "app.errors.invalid_email": "L'adresse e-mail{value} qui se trouve ligne {row} n'est pas valide", "app.errors.invalid_row": "Une erreur inconnue est survenue lors du traitement de la ligne {row}", "app.errors.is_not_timeline_project": "Le projet actuel ne contient pas de phases.", "app.errors.key_invalid": "La clé ne peut contenir que des lettres, chiffres et underscores (_)", "app.errors.last_name_blank": "<PERSON><PERSON> ne peut pas être vide", "app.errors.locale_blank": "<PERSON>euillez choisir une langue", "app.errors.locale_inclusion": "Veuillez choisir une langue prise en charge", "app.errors.malformed_admin_value": "La valeur {value} dans la colonne 'administrateur' à la ligne {row} n'est pas valide", "app.errors.malformed_groups_value": "Le groupe {value} trouvé à la ligne {row} n'est pas valide", "app.errors.max_invites_limit_exceeded1": "Le nombre d'invitations dépasse la limite de 1000.", "app.errors.maximum_attendees_greater_than1": "Le nombre maximum d'inscrits doit être supérieur à 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "La valeur doit être supérieure ou égale au nombre de personnes déjà inscrites.\n", "app.errors.no_invites_specified": "Aucune adresse e-mail n'a été trouvée.", "app.errors.no_recipients": "La campagne ne peut pas être envoyée car il n'y a aucun destinataire. Le groupe auquel vous souhaitez l'envoyer est soit vide, soit aucun membre du groupe n'a donné son consentement pour recevoir des emails.", "app.errors.number_invalid": "Veuillez saisir un nombre valide.", "app.errors.password_blank": "<PERSON><PERSON> ne peut pas être vide", "app.errors.password_invalid": "Veuillez revérifier votre mot de passe actuel.", "app.errors.password_too_short": "Le mot de passe doit comporter au moins 8 caractères", "app.errors.resending_code_failed": "Un problème s'est produit lors de l'envoi du code de confirmation.", "app.errors.slug_taken": "Cette URL de projet existe déjà. Merci de modifier le slug du projet.", "app.errors.tag_name_taken": "Une étiquette portant ce nom existe déjà", "app.errors.title_multiloc_blank": "Le titre ne peut être vide.", "app.errors.title_multiloc_includes_banned_words": "Le titre contient des mots considérés comme inappropriés.", "app.errors.token_invalid": "Les liens de réinitialisation de mot de passe ne peuvent être utilisés qu'une seule fois et sont valables pendant une heure après avoir été envoyés. {passwordResetLink}.", "app.errors.too_common": "Ce mot de passe est trop simple. Veuillez choisir un mot de passe plus complexe.", "app.errors.too_long": "Votre mot de passe peut comporter au maximum 72 caractères.", "app.errors.too_short": "Votre mot de passe doit comporter au minimum 8 caractères.", "app.errors.uncaught_error": "Une erreur s'est produite.", "app.errors.unknown_group": "Le groupe {value} trouvé à la ligne {row} n'est pas un groupe connu", "app.errors.unknown_locale": "La langue {value} trouvée ligne {row} ne fait pas partie des langues paramétrées", "app.errors.unparseable_excel": "Le fichier Excel sélectionné n'a pas pu être traité.", "app.errors.url": "Veuillez entrer un lien valide commençant par « https:// »", "app.errors.verification_taken": "La vérification ne peut pas être effectuée car un autre compte a été vérifié avec les mêmes informations.", "app.errors.view_name_taken": "Une vue avec ce nom existe déjà", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Contenu signalé automatiquement (contribution ou commentaire)", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Se connecter avec StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "S'inscrire avec StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Créez un compte Stadt Wien maintenant et utilisez un seul identifiant pour de nombreux services numériques de Vienne.", "app.modules.id_cow.cancel": "Annuler", "app.modules.id_cow.emptyFieldError": "<PERSON><PERSON> ne peut pas être vide.", "app.modules.id_cow.helpAltText": "Indique où trouver le numéro de série de la carte d'identité", "app.modules.id_cow.invalidIdSerialError": "Série de l'ID invalide", "app.modules.id_cow.invalidRunError": "EXÉCUTATION non valide", "app.modules.id_cow.noMatchFormError": "Aucune correspondance n'a été trouvée.", "app.modules.id_cow.notEntitledFormError": "N'y a pas droit.", "app.modules.id_cow.showCOWHelp": "Où puis-je trouver mon numéro de série d'identité ?", "app.modules.id_cow.somethingWentWrongError": "Nous ne pouvons pas vous vérifier parce que quelque chose s'est mal passé.", "app.modules.id_cow.submit": "Envoyer", "app.modules.id_cow.takenFormError": "<PERSON><PERSON><PERSON><PERSON> pris.", "app.modules.id_cow.verifyCow": "Vérifier à l'aide de COW", "app.modules.id_franceconnect.verificationButtonAltText": "Vérifier avec FranceConnect", "app.modules.id_gent_rrn.cancel": "Annuler", "app.modules.id_gent_rrn.emptyFieldError": "<PERSON><PERSON> ne peut pas être vide.", "app.modules.id_gent_rrn.gentRrnHelp": "Votre numéro de sécurité sociale figure au dos de votre carte d'identité numérique", "app.modules.id_gent_rrn.invalidRrnError": "Numéro de sécurité sociale invalide", "app.modules.id_gent_rrn.noMatchFormError": "Nous n'avons pas trouvé d'informations sur votre numéro de sécurité sociale", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Nous ne pouvons pas vérifier votre identité car vous n'habitez pas à Gand", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Nous ne pouvons pas vous vérifier parce que vous avez moins de 14 ans", "app.modules.id_gent_rrn.rrnLabel": "Numéro de sécurité sociale", "app.modules.id_gent_rrn.rrnTooltip": "Nous vous demandons votre numéro de sécurité sociale pour vérifier si vous êtes un citoyen gantois, âgé de plus de 14 ans.", "app.modules.id_gent_rrn.showGentRrnHelp": "Où puis-je trouver mon numéro de série d'identité ?", "app.modules.id_gent_rrn.somethingWentWrongError": "Nous ne pouvons pas vous vérifier parce que quelque chose s'est mal passé.", "app.modules.id_gent_rrn.submit": "Envoyer", "app.modules.id_gent_rrn.takenFormError": "Votre numéro de sécurité sociale a déjà été utilisé pour vérifier un autre compte", "app.modules.id_gent_rrn.verifyGentRrn": "Vérifier en utilisant GentRrn", "app.modules.id_id_card_lookup.cancel": "Annuler", "app.modules.id_id_card_lookup.emptyFieldError": "<PERSON><PERSON> ne peut pas être vide.", "app.modules.id_id_card_lookup.helpAltText": "Explication de la carte d'identité", "app.modules.id_id_card_lookup.invalidCardIdError": "Cet identifiant n'est pas valide.", "app.modules.id_id_card_lookup.noMatchFormError": "Aucune correspondance n'a été trouvée.", "app.modules.id_id_card_lookup.showHelp": "Où puis-je trouver mon numéro de série d'identité?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Nous ne pouvons pas vous vérifier parce que quelque chose s'est mal passé.", "app.modules.id_id_card_lookup.submit": "Envoyer", "app.modules.id_id_card_lookup.takenFormError": "<PERSON><PERSON><PERSON><PERSON> pris.", "app.modules.id_oostende_rrn.cancel": "Annuler", "app.modules.id_oostende_rrn.emptyFieldError": "<PERSON><PERSON> ne peut pas être vide.", "app.modules.id_oostende_rrn.invalidRrnError": "Numéro de sécurité sociale invalide", "app.modules.id_oostende_rrn.noMatchFormError": "Nous n'avons pas trouvé d'informations sur votre numéro de sécurité sociale", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Nous ne pouvons pas vérifier votre identité car vous habitez en dehors d'Ostende", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Nous ne pouvons pas vous vérifier parce que vous avez moins de 14 ans", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Votre numéro de sécurité sociale figure au dos de votre carte d'identité numérique", "app.modules.id_oostende_rrn.rrnLabel": "Numéro de sécurité sociale", "app.modules.id_oostende_rrn.rrnTooltip": "Nous vous demandons votre numéro de sécurité sociale pour vérifier si vous êtes un citoyen d'Ostende, âgé de plus de 14 ans.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Où puis-je trouver mon numéro de sécurité sociale ?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Nous ne pouvons pas vous vérifier parce que quelque chose s'est mal passé.", "app.modules.id_oostende_rrn.submit": "Envoyer", "app.modules.id_oostende_rrn.takenFormError": "Votre numéro de sécurité sociale a déjà été utilisé pour vérifier un autre compte", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Vérifier à l'aide du numéro de sécurité sociale", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "V<PERSON> avez reçu des droits d'administration sur le dossier \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Partager", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Consultez les projets sur {folderUrl} pour faire entendre votre voix !", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | de la plateforme de participation de {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | de la plateforme de participation de {orgName}", "app.sessionRecording.accept": "<PERSON><PERSON>, j'accepte", "app.sessionRecording.modalDescription1": "Afin de mieux comprendre nos utilisateurs, nous demandons de manière aléatoire à un petit pourcentage de visiteurs de suivre en détail leur session de navigation.", "app.sessionRecording.modalDescription2": "Les données collectées ont pour seul but d'améliorer le site web. Aucune de vos données ne sera communiquée à un tiers. Toute information sensible que vous saisissez sera filtrée.", "app.sessionRecording.modalDescription3": "Acceptez-vous ?", "app.sessionRecording.modalDescriptionFaq": "FAQ ici.", "app.sessionRecording.modalTitle": "Aidez-nous à améliorer ce site", "app.sessionRecording.reject": "Non, je refuse", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Allouer un budget", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Recueillir des commentaires sur un document", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "<PERSON><PERSON>ez une enquête sur la plateforme", "app.utils.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON><PERSON> un sondage", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Intégrer une enquête externe", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Inviter à participer", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Recueillir des contributions et des commentaires", "app.utils.AdminPage.ProjectEdit.shareInformation": "Partager l'information", "app.utils.FormattedCurrency.credits": "crédits", "app.utils.FormattedCurrency.tokens": "jetons", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# crédit} one {# crédit} other {# crédits}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# jeton} one {# jeton} other {# jetons}}", "app.utils.IdeaCards.mostDiscussed": "Les plus discutés", "app.utils.IdeaCards.mostReacted": "Plus de réactions", "app.utils.IdeaCards.newest": "Le plus récent", "app.utils.IdeaCards.oldest": "Le plus ancien", "app.utils.IdeaCards.random": "<PERSON>", "app.utils.IdeaCards.trending": "Tendance", "app.utils.IdeasNewPage.contributionFormTitle": "Ajoutez une nouvelle suggestion", "app.utils.IdeasNewPage.ideaFormTitle": "A<PERSON>tez une nouvelle idée", "app.utils.IdeasNewPage.initiativeFormTitle": "Ajouter une nouvelle initiative", "app.utils.IdeasNewPage.issueFormTitle1": "Ajouter un nouveau problème", "app.utils.IdeasNewPage.optionFormTitle": "Ajoutez une nouvelle option", "app.utils.IdeasNewPage.petitionFormTitle": "Ajouter une nouvelle pétition", "app.utils.IdeasNewPage.projectFormTitle": "Ajoutez un nouveau projet", "app.utils.IdeasNewPage.proposalFormTitle": "Ajouter une nouvelle proposition", "app.utils.IdeasNewPage.questionFormTitle": "Ajoutez une nouvelle question", "app.utils.IdeasNewPage.surveyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourComment": "Voir votre commentaire", "app.utils.IdeasNewPage.viewYourContribution": "Voir votre contribution", "app.utils.IdeasNewPage.viewYourIdea": "Voir votre idée", "app.utils.IdeasNewPage.viewYourInitiative": "Voir votre initiative", "app.utils.IdeasNewPage.viewYourInput": "Voir votre contribution", "app.utils.IdeasNewPage.viewYourIssue": "Afficher votre problème", "app.utils.IdeasNewPage.viewYourOption": "Voir votre option", "app.utils.IdeasNewPage.viewYourPetition": "Voir votre pétition", "app.utils.IdeasNewPage.viewYourProject": "Voir votre projet", "app.utils.IdeasNewPage.viewYourProposal": "Voir votre proposition", "app.utils.IdeasNewPage.viewYourQuestion": "Voir votre question", "app.utils.Projects.sendSubmission": "Envoyez l'identifiant de soumission à mon adresse électronique", "app.utils.Projects.sendSurveySubmission": "Envoyer l'identifiant de la soumission à mon adresse e-mail", "app.utils.Projects.surveySubmission": "Soumission de l'enquête", "app.utils.Projects.yourResponseHasTheFollowingId": "Votre réponse a l'identifiant suivant : {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "Si vous souhaitez que votre réponse soit supprimée, veuillez nous contacter en utilisant l'identifiant suivant :", "app.utils.actionDescriptors.attendingEventMissingRequirements": "<PERSON><PERSON> devez compléter votre profil pour participer à cet événement.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Vous ne remplissez pas les conditions pour participer à cet événement.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Vous n'êtes pas autorisé à assister à cet événement.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Vous devez vous connecter pour vous inscrire à cet événement.", "app.utils.actionDescriptors.attendingEventNotVerified": "V<PERSON> devez vérifier votre compte avant de pouvoir participer à cet événement.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "<PERSON><PERSON> devez compléter votre profil pour vous porter volontaire.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Vous ne remplissez pas les conditions pour vous porter volontaire.", "app.utils.actionDescriptors.volunteeringNotPermitted": "Vous n'êtes pas autorisé à vous porter volontaire.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Vous devez vous connecter ou vous inscrire pour vous porter volontaire.", "app.utils.actionDescriptors.volunteeringNotVerified": "Vous devez vérifier votre compte avant de pouvoir vous porter volontaire.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Veuillez {completeRegistrationLink} pour pouvoir vous porter volontaire.", "app.utils.errors.api_error_default.in": "Ce n'est pas bien", "app.utils.errors.default.ajv_error_birthyear_required": "Veuillez indiquer votre année de naissance", "app.utils.errors.default.ajv_error_date_any": "Veuillez indiquer une date valide", "app.utils.errors.default.ajv_error_domicile_required": "Veuillez indiquer votre lieu de résidence", "app.utils.errors.default.ajv_error_gender_required": "Veuillez indiquer votre genre", "app.utils.errors.default.ajv_error_invalid": "N'est pas valide", "app.utils.errors.default.ajv_error_maxItems": "Impossible de sélectionner plus {limit, plural, one {d'une option} other {de # options}}", "app.utils.errors.default.ajv_error_minItems": "Doit inclure au moins {limit, plural, one {# article} other {# articles}}", "app.utils.errors.default.ajv_error_number_any": "Veuillez renseigner un chiffre valide", "app.utils.errors.default.ajv_error_politician_required": "Veuillez indiquer si vous êtes un politicien", "app.utils.errors.default.ajv_error_required3": "Le champ suivant est obligatoire : {fieldName}", "app.utils.errors.default.ajv_error_type": "Ne peut pas être vide", "app.utils.errors.default.api_error_accepted": "<PERSON><PERSON> être accepté", "app.utils.errors.default.api_error_blank": "Ne peut pas être vide", "app.utils.errors.default.api_error_confirmation": "Ne correspond pas", "app.utils.errors.default.api_error_empty": "Ne peut être vide", "app.utils.errors.default.api_error_equal_to": "Ce n'est pas bien", "app.utils.errors.default.api_error_even": "<PERSON>it ê<PERSON>", "app.utils.errors.default.api_error_exclusion": "Est réservé", "app.utils.errors.default.api_error_greater_than": "Est trop petit", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Est trop petit", "app.utils.errors.default.api_error_inclusion": "N'est pas inclus dans la liste", "app.utils.errors.default.api_error_invalid": "N'est pas valide", "app.utils.errors.default.api_error_less_than": "Est trop grand", "app.utils.errors.default.api_error_less_than_or_equal_to": "Est trop grand", "app.utils.errors.default.api_error_not_a_number": "N'est pas un nombre", "app.utils.errors.default.api_error_not_an_integer": "Doit être un nombre entier", "app.utils.errors.default.api_error_other_than": "Ce n'est pas bien", "app.utils.errors.default.api_error_present": "Doit être vide", "app.utils.errors.default.api_error_too_long": "Est trop long", "app.utils.errors.default.api_error_too_short": "Est trop court", "app.utils.errors.default.api_error_wrong_length": "Est de la mauvaise longueur", "app.utils.errors.defaultapi_error_.odd": "Ça doit être bizarre", "app.utils.notInGroup": "Vous ne répondez pas aux critères de participation.", "app.utils.participationMethod.onSurveySubmission": "<PERSON><PERSON><PERSON>, votre réponse a bien été reçue.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Il n'est plus possible de voter, car cette phase n'est plus active.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Vous ne remplissez pas les conditions pour voter.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Vous n'êtes pas autorisé à voter.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Vous devez vous connecter ou vous inscrire pour voter.", "app.utils.participationMethodConfig.voting.votingNotVerified": "V<PERSON> de<PERSON> vérifier votre compte avant de pouvoir voter.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>La soumission des budgets s'est clôturée le {endDate}.</b> Chaque participant disposait d'un budget total de <b>{maxBudget} à répartir entre {optionCount} options.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Votre budget a été soumis", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Votre budget a été soumis 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Vous ne remplissez pas les conditions pour soumettre un budget.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Vous n'êtes pas autorisé à soumettre un budget.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Vous devez vous connecter ou vous inscrire pour soumettre un budget.", "app.utils.votingMethodUtils.budgetingNotVerified": "V<PERSON> devez vérifier votre compte avant de pouvoir soumettre un budget.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Votre budget ne sera pas pris en compte</b> tant que vous n'aurez pas cliqué sur \"Soumettre\"", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Le budget minimum requis est de {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "Une fois terminé, cliquez sur « Soumettre » pour soumettre votre budget.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Sélectionnez vos options préférées en cliquant sur \"Ajouter\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Vous disposez d'un total de <b>{maxBudget} à répartir entre {optionCount} options</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b>Félicitations ! Votre budget a été soumis avec succès !</b> Vous pouvez vérifier vos choix ci-dessous. Vous avez la possibilité de les modifier jusqu'au <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b>Félicitations, votre budget a été soumis avec succès !</b> Vous pouvez vérifier vos choix ci-dessous.", "app.utils.votingMethodUtils.castYourVote": "Participez au vote", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Vous pouvez donner {maxVotes, plural, one {uniquement # crédit} other {jusqu'à # crédits}} par option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Vous pouvez donner {maxVotes, plural, one {uniquement # point} other {jusqu'à # points}} par option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Vous pouvez donner {maxVotes, plural, one {uniquement # jeton} other {jusqu'à # jetons}} par option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Vous pouvez donner {maxVotes, plural, one {uniquement # vote} other {jusqu'à # votes}} par option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "Une fois terminé, cliquez sur « Soumettre » pour voter.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Choisissez vos options préférées en cliquant sur « Sélectionner ».", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "Vous disposez d'un total de <b>{totalVotes, plural, one {# crédit} other {# crédits}} à répartir entre {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "Vous disposez d'un total de <b>{totalVotes, plural, one {# point} other {# points}} à répartir entre {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "Vous disposez d'un total de <b>{totalVotes, plural, one {# jeton} other {# jetons}} à répartir entre {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "Vous disposez d'un total de <b>{totalVotes, plural, one {# vote} other {# votes}} à répartir entre {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "Résultats finaux", "app.utils.votingMethodUtils.finalTally": "Résultat final", "app.utils.votingMethodUtils.howToParticipate": "Comment participer", "app.utils.votingMethodUtils.howToVote": "Comment voter ?", "app.utils.votingMethodUtils.multipleVotingEnded1": "Le vote a pris fin le <b>{endDate}</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {# crédit} one {# crédit} other {# crédits}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 point} one {1 point} other {# points}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {# jeton} one {# jeton} other {# jetons}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 vote} one {1 vote} other {# votes}}", "app.utils.votingMethodUtils.results": "Résultats", "app.utils.votingMethodUtils.singleVotingEnded": "Le vote a été clôturé le <b>{endDate}.</b> Les participants pouvaient <b>voter pour {maxVotes} options.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Choisissez vos options préférées en cliquant sur « Sélectionner ».", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Vous disposez de <b>{totalVotes} votes</b> que vous pouvez attribuer aux différentes options.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "Une fois terminé, cliquez sur « Soumettre » pour voter.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Le vote a été clôturé le <b>{endDate}.</b> Les participants pouvaient <b>voter pour une seule option</b>.", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Choisissez votre option préférée en cliquant sur « Sélectionner ».", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Vous disposez d'<b>un vote</b> que vous pouvez attribuer à l'une des options.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Le vote a été clôturé le <b>{endDate}.</b> Les participants pouvaient <b>voter pour autant d'options qu'ils le souhaitaient</b>.", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Vous pouvez voter pour autant d'options que vous le souhaitez.", "app.utils.votingMethodUtils.submitYourBudget": "Soumettre ton budget", "app.utils.votingMethodUtils.submittedBudgetCountText2": "personne a soumis un budget en ligne", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "personnes ont soumis un budget en ligne", "app.utils.votingMethodUtils.submittedVoteCountText2": "personne a voté en ligne", "app.utils.votingMethodUtils.submittedVotesCountText2": "personnes ont voté en ligne", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Votes soumis 🎉", "app.utils.votingMethodUtils.votesCast": "Votes enregistrés", "app.utils.votingMethodUtils.votingClosed": "Les votes sont clos", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Votre vote ne sera pas pris en compte</b> tant que vous n'aurez pas cliqué sur « Soumettre »", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b>Félicitations, votre vote a été soumis !</b> V<PERSON> pouvez vérifier ou modifier votre soumission avant le <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b>Félicitations, votre vote a été soumis !</b> V<PERSON> pouvez vérifier ou modifier votre soumission à tout moment ci-dessous.", "components.UI.IdeaSelect.noIdeaAvailable": "Il n'y a pas d'idées disponibles.", "components.UI.IdeaSelect.selectIdea": "Sélectionnez une idée", "containers.SiteMap.allProjects": "Tous les projets", "containers.SiteMap.customPageSection": "Pages personnal<PERSON>", "containers.SiteMap.folderInfo": "Plus d'informations", "containers.SiteMap.headSiteMapTitle": "Plan du site | {orgName}", "containers.SiteMap.homeSection": "Général", "containers.SiteMap.pageContents": "Contenu de la page", "containers.SiteMap.profilePage": "Votre profil", "containers.SiteMap.profileSettings": "<PERSON><PERSON> r<PERSON>", "containers.SiteMap.projectEvents": "Evénements", "containers.SiteMap.projectIdeas": "Idées", "containers.SiteMap.projectInfo": "Information", "containers.SiteMap.projectPoll": "{tenant<PERSON><PERSON>, select, vitrysurseine {Enquê<PERSON>} other {Sondage}}", "containers.SiteMap.projectSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectsArchived": "Projets archivés", "containers.SiteMap.projectsCurrent": "Projets en cours", "containers.SiteMap.projectsDraft": "Ébauches de projets", "containers.SiteMap.projectsSection": "Les projets pour {orgName}", "containers.SiteMap.signInPage": "Connecter", "containers.SiteMap.signUpPage": "S'inscrire", "containers.SiteMap.siteMapDescription": "A partir de cette page, vous pouvez naviguer vers n'importe quel contenu de la plateforme.", "containers.SiteMap.siteMapTitle": "Plan du site de la plate-forme de participation de {orgName}", "containers.SiteMap.successStories": "Histoires de réussite", "containers.SiteMap.timeline": "Phases du projet", "containers.SiteMap.userSpaceSection": "Votre compte", "containers.SubscriptionEndedPage.accessDenied": "Vous n'avez plus accès", "containers.SubscriptionEndedPage.subscriptionEnded": "Vous n'avez plus accès à cette page car votre licence Go Vocal a pris fin."}
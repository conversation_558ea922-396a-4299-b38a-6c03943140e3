{"EmailSettingsPage.emailSettings": "Email settings", "EmailSettingsPage.initialUnsubscribeError": "Do<PERSON><PERSON> je do greške prilikom odjave. Molimo pokušajte ponovo.", "EmailSettingsPage.initialUnsubscribeLoading": "<PERSON><PERSON><PERSON>te<PERSON> se obrađuje, molimo sa<PERSON>j<PERSON>...", "EmailSettingsPage.initialUnsubscribeSuccess": "<PERSON><PERSON><PERSON><PERSON> ste se od<PERSON> sa {campaignTitle}.", "UI.FormComponents.optional": "opciono", "app.closeIconButton.a11y_buttonActionMessage": "Zatvori", "app.components.Areas.areaUpdateError": "An error occurred while saving your area. Please try again.", "app.components.Areas.followedArea": "Followed area: {areaTitle}", "app.components.Areas.followedTopic": "Followed topic: {topicTitle}", "app.components.Areas.topicUpdateError": "An error occurred while saving your topic. Please try again.", "app.components.Areas.unfollowedArea": "Unfollowed area: {areaTitle}", "app.components.Areas.unfollowedTopic": "Unfollowed topic: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Cena:", "app.components.AssignBudgetControl.add": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignBudgetControl.added": "Додато", "app.components.AssignMultipleVotesControl.addVote": "Add vote", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "You have distributed all of your credits.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "You have distributed the maximum number of credits for this option.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "You have distributed all of your points.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "You have distributed the maximum number of points for this option.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "You have distributed all of your tokens.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "You have distributed the maximum number of tokens for this option.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "You have distributed all of your votes.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "You have distributed the maximum number of votes for this option.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Voting is not available, since this phase is not active.", "app.components.AssignMultipleVotesControl.removeVote": "Remove vote", "app.components.AssignMultipleVotesControl.select": "Select", "app.components.AssignMultipleVotesControl.votesSubmitted1": "You have already submitted your vote. To modify it, click \"Modify your submission\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "You have already submitted your vote. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {credit} other {credits}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {point} other {points}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {vote} other {votes}}", "app.components.AssignVoteControl.maxVotesReached1": "You have distributed all of your votes.", "app.components.AssignVoteControl.phaseNotActive": "Voting is not available, since this phase is not active.", "app.components.AssignVoteControl.select": "Select", "app.components.AssignVoteControl.selected2": "Selected", "app.components.AssignVoteControl.voteForAtLeastOne": "Vote for at least 1 option", "app.components.AssignVoteControl.votesSubmitted1": "You have already submitted your vote. To modify it, click \"Modify your submission\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "You have already submitted your vote. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.AuthProviders.continue": "Nastavite", "app.components.AuthProviders.continueWithAzure": "Nastavite putem {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Nastavite uz Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Continue with Fake SSO", "app.components.AuthProviders.continueWithGoogle": "Nastavite uz Google", "app.components.AuthProviders.continueWithHoplr": "Continue with Hoplr", "app.components.AuthProviders.continueWithIdAustria": "Continue with ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Наставите са {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Continue with MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Već postoji nalog sa ovom adresom e-pošte.{br}{br}Platformi ne možete pristupiti pomoću FranceConnect, zato što se lični podaci ne poklapaju. Za prijavljivanje pomoću FranceConnect potrebno je najpre da izmenite ime ili prezime na ovoj platformi kako bi se poklopili sa vašim zvaničnim podacima.{br}{br}Ispod se možete prijaviti na uobičajeni način.", "app.components.AuthProviders.goToLogIn": "Već imate nalog? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Nemate nalog? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "<PERSON><PERSON><PERSON><PERSON> se", "app.components.AuthProviders.logInWithEmail": "<PERSON><PERSON><PERSON><PERSON> se putem emaila", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "You must be the specified minimum age or above to be verified.", "app.components.AuthProviders.signUp2": "Regis<PERSON><PERSON><PERSON><PERSON> se", "app.components.AuthProviders.signUpButtonAltText": "Registr<PERSON>j<PERSON> se sa {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Regis<PERSON><PERSON><PERSON><PERSON> se putem <PERSON>", "app.components.AuthProviders.verificationRequired": "Verification required", "app.components.Author.a11yPostedBy": "Posted by", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 učesnik} other {{numberOfParticipants} učesnika}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} users", "app.components.AvatarBubbles.participant": "participant", "app.components.AvatarBubbles.participants1": "učesnika", "app.components.Comments.cancel": "Prekid", "app.components.Comments.commentingDisabledInCurrentPhase": "Komentarisanje nije moguće u ovoj fazi.", "app.components.Comments.commentingDisabledInactiveProject": "Komentarisanje nije moguće jer projekat nije trenutno aktivan.", "app.components.Comments.commentingDisabledProject": "<PERSON>va vrsta projekta ne predviđa komentarisanje.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} da biste komentarisali.", "app.components.Comments.commentingMaybeNotPermitted": "{signInLink} da biste mogli da učestvujete.", "app.components.Comments.inputsAssociatedWithProfile": "Подразумевано, ваши поднесци ће бити повезани са вашим профилом, осим ако не изаберете ову опцију.", "app.components.Comments.invisibleTitleComments": "Komentari", "app.components.Comments.leastRecent": "Најмање недавно", "app.components.Comments.likeComment": "Лајкујте овај коментар", "app.components.Comments.mostLiked": "Naj<PERSON>še reak<PERSON>", "app.components.Comments.mostRecent": "Најновији", "app.components.Comments.official": "Zvanično", "app.components.Comments.postAnonymously": "Објавите анонимно", "app.components.Comments.replyToComment": "Odgovorite na komentar", "app.components.Comments.reportAsSpam": "Prijavite spam", "app.components.Comments.seeOriginal": "Pogledajte izvornu verziju", "app.components.Comments.seeTranslation": "Pogledajte prevod", "app.components.Comments.yourComment": "<PERSON><PERSON><PERSON> k<PERSON>", "app.components.CommonGroundResults.divisiveDescription": "Statements where people agree and disagree equally:", "app.components.CommonGroundResults.divisiveTitle": "Divisive", "app.components.CommonGroundResults.majorityDescription": "More than 60% voted one way or the other on the following:", "app.components.CommonGroundResults.majorityTitle": "Majority", "app.components.CommonGroundResults.participantLabel": "participant", "app.components.CommonGroundResults.participantsLabel1": "participants", "app.components.CommonGroundResults.statementLabel": "statement", "app.components.CommonGroundResults.statementsLabel1": "statements", "app.components.CommonGroundResults.votesLabe": "vote", "app.components.CommonGroundResults.votesLabel1": "votes", "app.components.CommonGroundStatements.agreeLabel": "Agree", "app.components.CommonGroundStatements.disagreeLabel": "Disagree", "app.components.CommonGroundStatements.noMoreStatements": "There are no statements to respond to right now", "app.components.CommonGroundStatements.noResults": "There are no results to show yet. Please make sure you have participated in the Common Ground phase and check back here after.", "app.components.CommonGroundStatements.unsureLabel": "Unsure", "app.components.CommonGroundTabs.resultsTabLabel": "Results", "app.components.CommonGroundTabs.statementsTabLabel": "Statements", "app.components.CommunityMonitorModal.formError": "Encountered an error.", "app.components.CommunityMonitorModal.surveyDescription2": "This ongoing survey tracks how you feel about governance and public services.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Takes <1 minute} one {Takes 1 minute} other {Takes # minutes}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Email sa verifikacionim kodom je poslat na {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Promenite vaš email.", "app.components.ConfirmationModal.codeInput": "<PERSON><PERSON><PERSON>", "app.components.ConfirmationModal.confirmationCodeSent": "Novi kod je poslat", "app.components.ConfirmationModal.didntGetAnEmail": "Niste primili email?", "app.components.ConfirmationModal.foundYourCode": "<PERSON>našli ste svoj kod?", "app.components.ConfirmationModal.goBack": "<PERSON>vrata<PERSON> nazad.", "app.components.ConfirmationModal.sendEmailWithCode": "Pošalji email sa verifikacionim kodom", "app.components.ConfirmationModal.sendNewCode": "Pošalji novi kod.", "app.components.ConfirmationModal.verifyAndContinue": "Verifikujte i nastavite", "app.components.ConfirmationModal.wrongEmail": "Pogrešan email?", "app.components.ConsentManager.Banner.accept": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.ariaButtonClose2": "Odbij politiku i zatvori baner", "app.components.ConsentManager.Banner.close": "Zatvori", "app.components.ConsentManager.Banner.mainText": "Ova platforma koristi kolačiće u skladu sa našom {policyLink}.", "app.components.ConsentManager.Banner.manage": "Upravljajte", "app.components.ConsentManager.Banner.policyLink": "Deklaracija o kolačićima", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON><PERSON>j", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Oglaša<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "<PERSON>vo koristimo kako bismo personalizovali oglašavanje na našem sajtu i merili njegovu efikasnost. Mi ne prikazujemo oglase, ali to na osnovu stranica koje na našem sajtu posećujete mogu učiniti sledeći servisi. ", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Dozvoli", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Pratimo upotrebu platforme kako bismo bolje razumeli kako je koristite i unapredili vašu navigaciju. Dobijene informacije se koriste samo u generalnoj analitici, pri čemu nikada ne pratimo pojedinačne osobe. ", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Povratak nazad", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Prekid", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funkcionalni", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Ovo je neophodno dozvoliti kako bi se nadgledale osnovne funkcionalnosti web stranice. Pojedini alati koji su nabrojani ovde se ne moraju odnositi na vas. Molimo vas da pročitate našu deklaraciju o kolačićima za više informacija. ", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Obavezno", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Da bi platforma bila funkcionalna, ukoliko se registrujete čuvamo autentifikacioni k<PERSON>čić, kao i odabrani jezik na kome koristite platformu.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Sačuvajte", "app.components.ConsentManager.Modal.PreferencesDialog.title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "<PERSON><PERSON>", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Content upload disclaimer", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "By uploading content, you declare that this content does not violate any regulations or rights of third parties, such as intellectual property rights, privacy rights, rights to trade secrets, and so on. Consequently, by uploading this content, you undertake to bear full and exclusive liability for all direct and indirect damages resulting from the uploaded content. Furthermore, you undertake to indemnify the platform owner and Go Vocal against any third party claims or liabilities against third parties, and any associated costs, that would arise or result from the content you uploaded.", "app.components.ContentUploadDisclaimer.onAccept": "I understand", "app.components.ContentUploadDisclaimer.onCancel": "Cancel", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Tell us why", "app.components.CustomFieldsForm.addressInputAriaLabel": "Address input", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Enter an address...", "app.components.CustomFieldsForm.adminFieldTooltip": "Field only visible to admins", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "All responses to this survey are anonymized.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "At least three points are required for a polygon.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "At least two points are required for a line.", "app.components.CustomFieldsForm.attachmentRequired": "At least one attachment is required", "app.components.CustomFieldsForm.authorFieldLabel": "Author", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Start typing to search by user email or name...", "app.components.CustomFieldsForm.back": "Back", "app.components.CustomFieldsForm.budgetFieldLabel": "Budget", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Click on the map to draw. Then, drag on points to move them.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Click on the map or type an address below to add your answer.", "app.components.CustomFieldsForm.confirm": "Confirm", "app.components.CustomFieldsForm.descriptionMinLength": "The description must be at least {min} characters long", "app.components.CustomFieldsForm.descriptionRequired": "The description is required", "app.components.CustomFieldsForm.fieldMaximumItems": "At most {maxSelections, plural, one {# option} other {# options}} can be selected for the field \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "At least {minSelections, plural, one {# option} other {# options}} can be selected for the field \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "The field \"{fieldName}\" is required", "app.components.CustomFieldsForm.fileSizeLimit": "The file size limit is {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "The image is required", "app.components.CustomFieldsForm.minimumCoordinates2": "A minimum of {numPoints} map points is required.", "app.components.CustomFieldsForm.notPublic1": "*This answer will only be shared with project managers, and not to the public.", "app.components.CustomFieldsForm.otherArea": "Somewhere else", "app.components.CustomFieldsForm.progressBarLabel": "Progress", "app.components.CustomFieldsForm.removeAnswer": "Remove answer", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Select as many as you like", "app.components.CustomFieldsForm.selectBetween": "*Select between {minItems} and {maxItems} options", "app.components.CustomFieldsForm.selectExactly2": "*Select exactly {selectExactly, plural, one {# option} other {# options}}", "app.components.CustomFieldsForm.selectMany": "*Choose as many as you like", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Tap on the map to draw. Then, drag on points to move them.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Tap on the map to draw.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Tap on the map to add your answer.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Tap on the map or type an address below to add your answer.", "app.components.CustomFieldsForm.tapToAddALine": "Tap to add a line", "app.components.CustomFieldsForm.tapToAddAPoint": "Tap to add a point", "app.components.CustomFieldsForm.tapToAddAnArea": "Tap to add an area", "app.components.CustomFieldsForm.titleMaxLength": "The title must be at most {max} characters long", "app.components.CustomFieldsForm.titleMinLength": "The title must be at least {min} characters long", "app.components.CustomFieldsForm.titleRequired": "The title is required", "app.components.CustomFieldsForm.topicRequired": "At least one tag is required", "app.components.CustomFieldsForm.typeYourAnswer": "Type your answer", "app.components.CustomFieldsForm.typeYourAnswerRequired": "It is required to type your answer", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Upload a zip file containing one or more shapefiles.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "If the location is not displayed among the options as you type, you can add valid coordinates in the format 'latitude, longitude' to specify a precise location (eg: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Pojedina polja sadrže greške. Molimo vas da ih ispravite i pokušate ponovo.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Došlo je do nepoznate greške prilikom slanja izveštaja. Molimo vas pokušajte ponovo.", "app.components.ErrorBoundary.errorFormLabelClose": "Zatvori", "app.components.ErrorBoundary.errorFormLabelComments": "Šta se dogodilo?", "app.components.ErrorBoundary.errorFormLabelEmail": "Email", "app.components.ErrorBoundary.errorFormLabelName": "Ime", "app.components.ErrorBoundary.errorFormLabelSubmit": "Pošalji", "app.components.ErrorBoundary.errorFormSubtitle": "<PERSON><PERSON> tim je primio obaveštenje.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON><PERSON> da vam pomognemo, opišite nam šta se dogodilo u nastavku.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Uspešno ste nam poslali povratne informacije. Hvala vam!", "app.components.ErrorBoundary.errorFormTitle": "<PERSON>ini se da je došlo do greš<PERSON>.", "app.components.ErrorBoundary.genericErrorWithForm": "<PERSON><PERSON><PERSON> je do greške te ne možemo prikazati ovaj sadržaj. Molimo vas pokušajte ponovo ili {openForm}", "app.components.ErrorBoundary.openFormText": "pomozite nam da shvatimo", "app.components.ErrorToast.budgetExceededError": "You don't have enough budget", "app.components.ErrorToast.votesExceededError": "You don't have enough votes left", "app.components.EventAttendanceButton.forwardToFriend": "Forward to a friend", "app.components.EventAttendanceButton.maxRegistrationsReached": "The maximum number of event registrations has been reached. There are no spots left.", "app.components.EventAttendanceButton.register": "Register", "app.components.EventAttendanceButton.registered": "Registered", "app.components.EventAttendanceButton.seeYouThere": "See you there!", "app.components.EventAttendanceButton.seeYouThereName": "See you there, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Manje podataka o događaju će postati vidljivo.", "app.components.EventCard.a11y_moreContentVisible": "Više podataka o događaju će postati vidljivo.", "app.components.EventCard.a11y_readMore": "Read more about the \"{eventTitle}\" event.", "app.components.EventCard.endsAt": "Završava se u", "app.components.EventCard.readMore": "Read more", "app.components.EventCard.showLess": "Prika<PERSON><PERSON> manje", "app.components.EventCard.showMore": "Prikaži više", "app.components.EventCard.startsAt": "Počinje u", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Upcoming and ongoing events in this project", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Upcoming and ongoing events in this phase", "app.components.FileUploader.a11y_file": "Fajl:", "app.components.FileUploader.a11y_filesToBeUploaded": "Fajlovi koji će biti postavljeni: {fileNames}", "app.components.FileUploader.a11y_noFiles": "<PERSON><PERSON> do<PERSON> faj<PERSON>i", "app.components.FileUploader.a11y_removeFile": "Ukloni ovaj fajl", "app.components.FileUploader.fileInputDescription": "Kliknite da biste odabrali fajl", "app.components.FileUploader.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.components.FileUploader.file_too_large2": "Files larger than {maxSizeMb}MB are not permitted.", "app.components.FileUploader.incorrect_extension": "{fileName} nije pod<PERSON> od strane našeg sistema i neće biti podignut.", "app.components.FilterBoxes.a11y_allFilterSelected": "Odabrani status filter: svi", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# unos} other {# unosa}}", "app.components.FilterBoxes.a11y_removeFilter": "Uklonite filter", "app.components.FilterBoxes.a11y_selectedFilter": "Odabrani status filter: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Oda<PERSON>ni {numberOfSelectedTopics, plural, =0 {zero topic filters} one {one topic filter} other {# topic filters}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Sve", "app.components.FilterBoxes.areas": "Filtriraj po oblasti", "app.components.FilterBoxes.inputs": "inputs", "app.components.FilterBoxes.noValuesFound": "Trenutno nema filtriranja po temama.", "app.components.FilterBoxes.showLess": "Show less", "app.components.FilterBoxes.showTagsWithNumber": "Prikaži sve ({numberTags})", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "Teme", "app.components.FiltersModal.filters": "<PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.a11y_folderDescription": "Opis foldera:", "app.components.FolderFolderCard.a11y_folderTitle": "Naziv foldera:", "app.components.FolderFolderCard.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projekata} one {# projekat} other {# projekata}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "The field type cannot be changed once there are submissions.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Type", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Autosave", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Auto-saving is enabled by default when you open the form editor. Any time you close the field settings panel using the \"X\" button, it will automatically trigger a save.", "app.components.GanttChart.timeRange.month": "Month", "app.components.GanttChart.timeRange.quarter": "Quarter", "app.components.GanttChart.timeRange.timeRangeMultiyear": "Multi-year", "app.components.GanttChart.timeRange.year": "Year", "app.components.GanttChart.today": "Today", "app.components.GoBackButton.group.edit.goBack": "Povratak nazad", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Vratite se na prethodnu stranicu", "app.components.HookForm.Feedback.errorTitle": "<PERSON><PERSON><PERSON> je do problema", "app.components.HookForm.Feedback.submissionError": "Pokušajte ponovo. A<PERSON> <PERSON> potraje, kontaktirajte nas", "app.components.HookForm.Feedback.submissionErrorTitle": "<PERSON><PERSON><PERSON><PERSON>, postoji problem sa naše strane", "app.components.HookForm.Feedback.successMessage": "<PERSON><PERSON><PERSON> je uspeš<PERSON> prosleđen", "app.components.HookForm.PasswordInput.passwordLabel": "Password", "app.components.HorizontalScroll.scrollLeftLabel": "<PERSON><PERSON> left.", "app.components.HorizontalScroll.scrollRightLabel": "<PERSON>roll right.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} ideas have loaded.", "app.components.IdeaCards.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.mostDiscussed": "Naj<PERSON>še komentara", "app.components.IdeaCards.filters.newest": "Najnovije", "app.components.IdeaCards.filters.oldest": "Najstarije", "app.components.IdeaCards.filters.popular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.random": "<PERSON><PERSON><PERSON>č<PERSON>", "app.components.IdeaCards.filters.sortBy": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Trenutni prikaz: {currentSortType}", "app.components.IdeaCards.filters.trending": "Najpopularnije", "app.components.IdeaCards.showMore": "Prikaži više", "app.components.IdeasMap.a11y_hideIdeaCard": "<PERSON><PERSON><PERSON><PERSON> karticu sa idejom.", "app.components.IdeasMap.a11y_mapTitle": "Prikaz mape", "app.components.IdeasMap.clickOnMapToAdd": "Kliknite na mapu kako biste dodali svoj unos", "app.components.IdeasMap.clickOnMapToAddAdmin2": "As an admin, you can click on the map to add your input, even if this phase is not active.", "app.components.IdeasMap.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.multipleInputsAtLocation": "Multiple inputs at this location", "app.components.IdeasMap.noFilteredResults": "Filteri koje ste odabrali nisu dali rezultat", "app.components.IdeasMap.noResults": "<PERSON>su pronađeni re<PERSON>ltati", "app.components.IdeasMap.or": "ili", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, no dislikes.} one {1 dislike.} other {, # dislikes.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, no likes.} one {, 1 like.} other {, # likes.}}", "app.components.IdeasMap.signInLinkText": "<PERSON><PERSON><PERSON><PERSON> se", "app.components.IdeasMap.signUpLinkText": "registrujte se", "app.components.IdeasMap.submitIdea2": "Submit input", "app.components.IdeasMap.tapOnMapToAdd": "Dodirnite mapu kako biste dodali unos", "app.components.IdeasMap.tapOnMapToAddAdmin2": "As an admin, you can tap on the map to add your input, even if this phase is not active.", "app.components.IdeasMap.userInputs2": "Inputs from participants", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, no comments} one {, 1 comment} other {, # comments}}", "app.components.IdeasShow.bodyTitle": "Opis", "app.components.IdeasShow.deletePost": "Izbriši", "app.components.IdeasShow.editPost": "Izmenite", "app.components.IdeasShow.goBack": "Povratak nazad", "app.components.IdeasShow.moreOptions": "Više opcija", "app.components.IdeasShow.or": "ili", "app.components.IdeasShow.proposedBudgetTitle": "Predloženi budžet", "app.components.IdeasShow.reportAsSpam": "Prijavite spam", "app.components.IdeasShow.send": "Pošalji", "app.components.IdeasShow.skipSharing": "Preskačem korak, učiniću ovo kasnije", "app.components.IdeasShowPage.signIn2": "Пријавите се", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON><PERSON>, nemate pristup ovoj stranici. Možda će biti potrebno da se prijavite ili registrujete za pristup.", "app.components.LocationInput.noOptions": "No options", "app.components.Modal.closeWindow": "Close window", "app.components.MultiSelect.clearButtonAction": "Clear selection", "app.components.MultiSelect.clearSearchButtonAction": "Clear search", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Унесите нову адресу е-поште", "app.components.PageNotFound.goBackToHomePage": "Nazad na matičnu stranicu", "app.components.PageNotFound.notFoundTitle": "Stranica nije pronađena", "app.components.PageNotFound.pageNotFoundDescription": "Tražena stranica nije nađena.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Unesite sadržaj za najmanje jedan jezik", "app.components.PagesForm.editContent": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PagesForm.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Fajlovi ne bi trebalo da budu veći od 50Mb. Dodati fajlovi će se prikazivati na dnu ove stranice.", "app.components.PagesForm.navbarItemTitle": "Ime u traci za navigaciju", "app.components.PagesForm.pageTitle": "<PERSON><PERSON>", "app.components.PagesForm.savePage": "Sačuvaj stranicu", "app.components.PagesForm.saveSuccess": "Stranica je us<PERSON>š<PERSON> saču<PERSON>.", "app.components.PagesForm.titleMissingOneLanguageError": "Unesite naslov za najmanje jedan jezik", "app.components.Pagination.back": "Previous page", "app.components.Pagination.next": "Next page", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "You spent {votesCast}, which exceeds the limit of {votesLimit}. Please remove some items from your basket and try again.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} left", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "You need to spend a minimum of {votesMinimum} before you can submit your basket.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "You need to select at least one option before you can submit.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "You need to add something to your basket before you can submit it.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {No credits left} other {# out of {totalNumberOfVotes, plural, one {1 credit} other {# credits}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {No points left} other {# out of {totalNumberOfVotes, plural, one {1 point} other {# points}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {No tokens left} other {# out of {totalNumberOfVotes, plural, one {1 token} other {# tokens}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {No votes left} other {# out of {totalNumberOfVotes, plural, one {1 vote} other {# votes}} left}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# votes} one {# vote} other {# votes}} cast", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "You cast {votesCast} votes, which exceeds the limit of {votesLimit}. Please remove some votes and try again.", "app.components.ParticipationCTABars.addInput": "Add input", "app.components.ParticipationCTABars.allocateBudget": "Rasporedite svoj budžet", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Your budget has been submitted successfully.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "U toku", "app.components.ParticipationCTABars.poll": "Учествујте у анкети", "app.components.ParticipationCTABars.reviewDocument": "Прегледајте документ", "app.components.ParticipationCTABars.seeContributions": "See contributions", "app.components.ParticipationCTABars.seeEvents3": "See events", "app.components.ParticipationCTABars.seeIdeas": "Pogledajte ideje", "app.components.ParticipationCTABars.seeInitiatives": "See initiatives", "app.components.ParticipationCTABars.seeIssues": "See comments", "app.components.ParticipationCTABars.seeOptions": "See options", "app.components.ParticipationCTABars.seePetitions": "See petitions", "app.components.ParticipationCTABars.seeProjects": "Pogledajte projekte", "app.components.ParticipationCTABars.seeProposals": "See proposals", "app.components.ParticipationCTABars.seeQuestions": "See questions", "app.components.ParticipationCTABars.submit": "прихвати", "app.components.ParticipationCTABars.takeTheSurvey": "Popunite upitnik", "app.components.ParticipationCTABars.userHasParticipated": "Učestvovali ste u ovom projektu.", "app.components.ParticipationCTABars.viewInputs": "View inputs", "app.components.ParticipationCTABars.volunteer": "Volontiraj", "app.components.ParticipationCTABars.votesCounter.vote": "vote", "app.components.ParticipationCTABars.votesCounter.votes": "votes", "app.components.PasswordInput.a11y_passwordHidden": "Lozinka je skrivena", "app.components.PasswordInput.a11y_passwordVisible": "Lozinka je vidljiva", "app.components.PasswordInput.a11y_strength1Password": "I<PERSON><PERSON><PERSON><PERSON> slaba lozinka", "app.components.PasswordInput.a11y_strength2Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength3Password": "Srednje jaka lo<PERSON>", "app.components.PasswordInput.a11y_strength4Password": "<PERSON><PERSON>", "app.components.PasswordInput.a11y_strength5Password": "<PERSON><PERSON><PERSON> j<PERSON> lo<PERSON>", "app.components.PasswordInput.hidePassword": "Sakrijte lozinku", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON><PERSON><PERSON> (min. {minimumPasswordLength} karaktera)", "app.components.PasswordInput.minimumPasswordLengthError": "Unesite lozinku koja sadrži najmanje {minimumPasswordLength} znakova", "app.components.PasswordInput.passwordEmptyError": "Unesite lozinku", "app.components.PasswordInput.passwordStrengthTooltip1": "Da biste lozinku učinili jačom:", "app.components.PasswordInput.passwordStrengthTooltip2": "Koristite kombinaciju uzastopnih malih slova, velikih slova, cifara, posebnih znakova i interpunkcije", "app.components.PasswordInput.passwordStrengthTooltip3": "Izbegavajte česte reči koje se lako pogode", "app.components.PasswordInput.passwordStrengthTooltip4": "Povećaj<PERSON> dužinu", "app.components.PasswordInput.showPassword": "Prikaži lozinku", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "Slabo", "app.components.PasswordInput.strength3Password": "Srednje", "app.components.PasswordInput.strength4Password": "Jako", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON><PERSON> jako", "app.components.PostCardsComponents.list": "Lista", "app.components.PostCardsComponents.map": "Mapa", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Dodajte zvanično obaveštenje", "app.components.PostComponents.OfficialFeedback.cancel": "Prekid", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Izbriši", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Da li ste sigurni da želite da obrišete ovo zvanično obaveštenje?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Izmenite", "app.components.PostComponents.OfficialFeedback.lastEdition": "Poslednji put i<PERSON><PERSON><PERSON>no dana {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Posledn<PERSON> put a<PERSON><PERSON><PERSON> {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Zvanično", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Odaberite kako drugi vide vaše ime", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Ime autora zvaničnog saopštenja", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Tekst zvaničnog saopštenja", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Zvanična obaveštenja", "app.components.PostComponents.OfficialFeedback.postedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dana {date} ", "app.components.PostComponents.OfficialFeedback.publishButtonText": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Prikaži prethodna obaveštenja", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Objavite obaveštenje...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON> nam je, do<PERSON><PERSON> je do greške", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Vaše obaveštenje je uspešno objavljeno!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Podržite moj doprinos '{postTitle}' ovde {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Podržite moj doprinos: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Podržite moj doprinos: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Podržite moju ideju '{postTitle}' ovde {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Podržite moju ideju: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Podržite moju ideju: {postTitle}", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Šta mislite o ovom predlogu? Glasajte i proširite diskusiju na {postUrl} kako bi se vaš glas čuo!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Podržite moj predlog: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Support my initiative: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Postavio/la sam komentar '{postTitle}' ovde {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Upravo sam postavio/la problem: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Upravo sam postavio/la problem: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Podržite moj predlog '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Podržite moj predlog: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Podržite moj predlog: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Support my petition '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Support my petition: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Support my petition: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Podržite moj projekat '{postTitle}' ovde {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Podržite moj projekat: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Podržite moj projekat: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Support my proposal '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Support my proposal: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "I just posted a proposal for {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Pridružite se diskusiji '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Pridružite se diskusiji: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Pridružite se diskusiji: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Glasajte za {postTitle} na", "app.components.PostComponents.linkToHomePage": "Link do početne stranice", "app.components.PostComponents.readMore": "Pročitajte više...", "app.components.PostComponents.topics": "Topics", "app.components.ProjectArchivedIndicator.archivedProject": "<PERSON><PERSON><PERSON><PERSON>, više ne možete učestvovati u ovom projektu jer je arhiviran", "app.components.ProjectArchivedIndicator.previewProject": "Draft project:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Visible only to moderators and those with a preview link.", "app.components.ProjectCard.a11y_projectDescription": "Opis projekta", "app.components.ProjectCard.a11y_projectTitle": "Naziv projekta:", "app.components.ProjectCard.addYourOption": "Dodajte svoju opciju", "app.components.ProjectCard.allocateYourBudget": "Rasporedite svoj budžet", "app.components.ProjectCard.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.comment": "Komentariši", "app.components.ProjectCard.contributeYourInput": "Doprinesite svojim unosom", "app.components.ProjectCard.finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.joinDiscussion": "Uključite se u diskusiju", "app.components.ProjectCard.learnMore": "Saznajte više", "app.components.ProjectCard.reaction": "Glasajte", "app.components.ProjectCard.readTheReport": "Read the report", "app.components.ProjectCard.reviewDocument": "Прегледајте документ", "app.components.ProjectCard.submitAnIssue": "Prijavite problem", "app.components.ProjectCard.submitYourIdea": "Pošaljite svoju ideju", "app.components.ProjectCard.submitYourInitiative": "Submit your initiative", "app.components.ProjectCard.submitYourPetition": "Submit your petition", "app.components.ProjectCard.submitYourProject": "Postavite vaš projekat", "app.components.ProjectCard.submitYourProposal": "Submit your proposal", "app.components.ProjectCard.takeThePoll": "Popunite anketu", "app.components.ProjectCard.takeTheSurvey": "Popunite upitnik", "app.components.ProjectCard.viewTheContributions": "Pogledajte sve unose", "app.components.ProjectCard.viewTheIdeas": "Pogledajte ideje", "app.components.ProjectCard.viewTheInitiatives": "View the initiatives", "app.components.ProjectCard.viewTheIssues": "Pogledajte komentare", "app.components.ProjectCard.viewTheOptions": "Pogledajte opcije", "app.components.ProjectCard.viewThePetitions": "View the petitions", "app.components.ProjectCard.viewTheProjects": "Pogledajte projekte", "app.components.ProjectCard.viewTheProposals": "View the proposals", "app.components.ProjectCard.viewTheQuestions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.vote": "Vote", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# komentar} other {# komentara}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# doprinos} other {# doprinosa}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {nema ideja} one {# ideja} other {# ideja}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# initiatives} one {# initiative} other {# initiatives}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# komentar} other {# komentara}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# solucija} other {# solucije}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petitions} one {# petition} other {# petitions}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# projekat} other {# projekata}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# proposals} one {# proposal} other {# proposals}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# pitanje} other {# pitanja}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# цомментс} one {# цомментс} other {# цомментс}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# улаза} one {# улазни} other {# улаза}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projekata} one {# projekat} other {# projekata}}", "app.components.ProjectFolderCards.components.Topbar.all": "Sve", "app.components.ProjectFolderCards.components.Topbar.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.draft": "Nacrt", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filtriraj po", "app.components.ProjectFolderCards.components.Topbar.published2": "Objavljeno", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Kategorija", "app.components.ProjectFolderCards.noProjectYet": "Trenutno nema otvorenih projekata", "app.components.ProjectFolderCards.noProjectsAvailable": "<PERSON><PERSON> proje<PERSON>", "app.components.ProjectFolderCards.showMore": "Prikaži više", "app.components.ProjectFolderCards.stayTuned": "Proverite kasnije da li postoje nove prilike za učešće", "app.components.ProjectFolderCards.tryChangingFilters": "Pokušajte da izmenite izabrane filtere.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Takođe primenjeno i u ovim gradovima", "app.components.ProjectTemplatePreview.copied": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.copyLink": "Kopiraj link", "app.components.QuillEditor.alignCenter": "Centrirajte tekst", "app.components.QuillEditor.alignLeft": "Pomerite levo", "app.components.QuillEditor.alignRight": "Pomerite desno", "app.components.QuillEditor.bold": "Podebljajte", "app.components.QuillEditor.clean": "Uklonite formatiranje", "app.components.QuillEditor.customLink": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.customLinkPrompt": "Unesite link:", "app.components.QuillEditor.edit": "Izmenite", "app.components.QuillEditor.image": "<PERSON><PERSON><PERSON> s<PERSON>", "app.components.QuillEditor.imageAltPlaceholder": "Kratak opis slike", "app.components.QuillEditor.italic": "Italic", "app.components.QuillEditor.link": "Dodajte link", "app.components.QuillEditor.linkPrompt": "Unesite link:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Uređ<PERSON> lista", "app.components.QuillEditor.remove": "Ukloni", "app.components.QuillEditor.save": "Sačuvajte", "app.components.QuillEditor.subtitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.title": "<PERSON><PERSON>", "app.components.QuillEditor.unorderedList": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "app.components.QuillEditor.video": "<PERSON><PERSON><PERSON><PERSON> video", "app.components.QuillEditor.videoPrompt": "Unesite video:", "app.components.QuillEditor.visitPrompt": "Posetite link:", "app.components.ReactionControl.completeProfileToReact": "Попуните свој профил да бисте реаговали", "app.components.ReactionControl.dislike": "Не свиђа ми се", "app.components.ReactionControl.dislikingDisabledMaxReached": "Достигли сте максималан број несвиђања за {projectName}", "app.components.ReactionControl.like": "Као", "app.components.ReactionControl.likingDisabledMaxReached": "Достигли сте максималан број свиђања за {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Реаговање ће бити омогућено када ова фаза почне", "app.components.ReactionControl.reactingDisabledPhaseOver": "У овој фази више није могуће реаговати", "app.components.ReactionControl.reactingDisabledProjectInactive": "Више не можете реаговати на идеје у {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Реаговање тренутно није омогућено за овај пројекат", "app.components.ReactionControl.reactingNotPermitted": "Glasanje u ovom projektu je završeno", "app.components.ReactionControl.reactingNotSignedIn": "Пријавите се да бисте реаговали.", "app.components.ReactionControl.reactingPossibleLater": "Реаговање ће почети {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Потврдите свој идентитет да бисте реаговали.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Event date: {startDate} at {startTime} to {endDate} at {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Event date: {eventDate} from {startTime} to {endTime}.", "app.components.Sharing.linkCopied": "Veza je kopirana", "app.components.Sharing.or": "or", "app.components.Sharing.share": "<PERSON><PERSON><PERSON>", "app.components.Sharing.shareByEmail": "Delite putem email-a", "app.components.Sharing.shareByLink": "Kopiraj link", "app.components.Sharing.shareOnFacebook": "Delite kroz Facebook", "app.components.Sharing.shareOnTwitter": "Delite kroz Twitter", "app.components.Sharing.shareThisEvent": "Share this event", "app.components.Sharing.shareThisFolder": "<PERSON><PERSON><PERSON>", "app.components.Sharing.shareThisProject": "Podelite ovaj projekat", "app.components.Sharing.shareViaMessenger": "Delite kroz Messenger", "app.components.Sharing.shareViaWhatsApp": "Delite kroz WhatsApp", "app.components.SideModal.closeButtonAria": "Zatvori", "app.components.StatusModule.futurePhase": "You are viewing a phase that has not started yet. You will be able to participate when the phase starts.", "app.components.StatusModule.modifyYourSubmission1": "Modify your submission", "app.components.StatusModule.submittedUntil3": "Your vote may be submitted until", "app.components.TopicsPicker.numberOfSelectedTopics": "Selected {numberOfSelectedTopics, plural, =0 {zero topics} one {one topic} other {# topics}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Expand image", "app.components.UI.MoreActionsMenu.moreOptions": "More options", "app.components.UI.MoreActionsMenu.showMoreActions": "Prikaži jo<PERSON> r<PERSON>", "app.components.UI.NewLabel.new": "NEW", "app.components.UI.PhaseFilter.noAppropriatePhases": "No appropriate phases found for this project", "app.components.UI.RemoveImageButton.a11y_removeImage": "Ukloni", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "<PERSON><PERSON><PERSON>", "app.components.Unauthorized.additionalInformationRequired": "Additional information is required for you to participate.", "app.components.Unauthorized.completeProfile": "Complete profile", "app.components.Unauthorized.completeProfileTitle": "Complete your profile to participate", "app.components.Unauthorized.noPermission": "Nemate dozvolu da pregledate ovu stranicu", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON><PERSON>, nemate ovlašćenje za pristup ovoj stranici.", "app.components.Upload.errorImageMaxSizeExceeded": "Slika koju ste odabrali je veća od {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Jedna ili više odabranih slika su veće od {maxFileSize}MB", "app.components.Upload.onlyOneImage": "Možete postaviti samo 1 sliku", "app.components.Upload.onlyXImages": "Možete postaviti najviše {maxItemsCount} slika", "app.components.Upload.remaining": "do završetka", "app.components.Upload.uploadImageLabel": "Odaberite sliku (max. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Odaberite jednu ili više slika", "app.components.UpsellTooltip.tooltipContent": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.components.UserName.anonymous": "Анонимоус", "app.components.UserName.anonymousTooltip2": "Овај корисник је одлучио да анонимизира свој допринос", "app.components.UserName.authorWithNoNameTooltip": "Your name has been autogenerated because you have not entered your name. Please update your profile if you would like to change it.", "app.components.UserName.deletedUser": "nepoznat autor", "app.components.UserName.verified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.VerificationModal.verifyAuth0": "Verify with NemID", "app.components.VerificationModal.verifyBOSA": "Verifikujte se putem itsme ili eID", "app.components.VerificationModal.verifyBosaFas": "Verify with itsme or eID", "app.components.VerificationModal.verifyClaveUnica": "Verifikacija sa Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "Verify with Fake SSO", "app.components.VerificationModal.verifyIdAustria": "Verify with ID Austria", "app.components.VerificationModal.verifyKeycloak": "Verify with ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Verify with MitID", "app.components.VerificationModal.verifyTwoday2": "Verify with BankID or Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Verifikujte vaš identitet.", "app.components.VoteControl.budgetingFutureEnabled": "Bićete u mogućnosti da rasporedite budžetska sredstva od dana {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Participativno budžetiranje trenutno nije omogućeno.", "app.components.VoteControl.budgetingNotPossible": "U ovom trenutku, nije moguće vršenje izmena u vašem budžetu.", "app.components.VoteControl.budgetingNotVerified": "Mo<PERSON><PERSON> vas {verifyAccountLink} da biste nastavili.", "app.components.VoteInputs._shared.currencyLeft1": "You have {budgetLeft} / {totalBudget} left", "app.components.VoteInputs._shared.numberOfCreditsLeft": "You have {votesLeft, plural, =0 {no credits left} other {# out of {totalNumberOfVotes, plural, one {1 credit} other {# credits}} left}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "You have {votesLeft, plural, =0 {no points left} other {# out of {totalNumberOfVotes, plural, one {1 point} other {# points}} left}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "You have {votesLeft, plural, =0 {no tokens left} other {# out of {totalNumberOfVotes, plural, one {1 token} other {# tokens}} left}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "You have {votesLeft, plural, =0 {no votes left} other {# out of {totalNumberOfVotes, plural, one {1 vote} other {# votes}} left}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "You have already submitted your budget. To modify it, click \"Modify your submission\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "You have already submitted your budget. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budgeting is not available, since this phase is not active.", "app.components.VoteInputs.single.youHaveVotedForX2": "You have voted for {votes, plural, =0 {# options} one {# option} other {# options}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "This means you will lose all data associated with this input, like comments, reactions and votes. This action cannot be undone.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Are you sure you want to delete this input?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "Cancel", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Confirm", "app.components.admin.SlugInput.resultingURL": "Dobijeni URL", "app.components.admin.SlugInput.slugTooltip": "Opis je jedinstveni skup reči na kraju veb adrese stranice ili URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "Ako promenite URL, veze ka ovoj stranici pomoću starog URL-a neće više raditi.", "app.components.admin.SlugInput.urlSlugLabel": "Opis", "app.components.admin.UserFilterConditions.addCondition": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_email": "Email", "app.components.admin.UserFilterConditions.field_event_attendance": "Event registrations", "app.components.admin.UserFilterConditions.field_follow": "Pratite", "app.components.admin.UserFilterConditions.field_lives_in": "Živi u", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Community monitor survey", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Dodao je status unosu", "app.components.admin.UserFilterConditions.field_participated_in_project": "<PERSON><PERSON><PERSON><PERSON> je projektu", "app.components.admin.UserFilterConditions.field_participated_in_topic": "<PERSON><PERSON><PERSON><PERSON> je unos sa tagom", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Datum registracije", "app.components.admin.UserFilterConditions.field_role": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_verified": "Verifikacija", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideation", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Proposals", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "is not registered for any of these events", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "is not registered for any event", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "is registered for one of these events", "app.components.admin.UserFilterConditions.predicate_attends_something": "is registered for at least one event", "app.components.admin.UserFilterConditions.predicate_begins_with": "po<PERSON><PERSON><PERSON> sa", "app.components.admin.UserFilterConditions.predicate_commented_in": "komentarisao", "app.components.admin.UserFilterConditions.predicate_contains": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_ends_on": "završava se na", "app.components.admin.UserFilterConditions.predicate_has_value": "ima kao vrednost", "app.components.admin.UserFilterConditions.predicate_in": "preduzeo bilo koju aktivnost", "app.components.admin.UserFilterConditions.predicate_is": "je", "app.components.admin.UserFilterConditions.predicate_is_admin": "je administrator", "app.components.admin.UserFilterConditions.predicate_is_after": "je nakon", "app.components.admin.UserFilterConditions.predicate_is_before": "je pre", "app.components.admin.UserFilterConditions.predicate_is_checked": "je <PERSON><PERSON>n", "app.components.admin.UserFilterConditions.predicate_is_empty": "je prazno", "app.components.admin.UserFilterConditions.predicate_is_equal": "je", "app.components.admin.UserFilterConditions.predicate_is_exactly": "je ta<PERSON>no", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "je veće od", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "je veće ili jednako", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "je obi<PERSON><PERSON> koris<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_not_area": "excludes area", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "excludes folder", "app.components.admin.UserFilterConditions.predicate_is_not_input": "excludes input", "app.components.admin.UserFilterConditions.predicate_is_not_project": "excludes project", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "excludes topic", "app.components.admin.UserFilterConditions.predicate_is_one_of": "je jedan od", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "one of the areas", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "one of the folders", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "one of the inputs", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "one of the projects", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "one of the topics", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "je projektni men<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "je manje od", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "je manje ili jednako", "app.components.admin.UserFilterConditions.predicate_is_verified": "je veri<PERSON><PERSON>n", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "ne počinje sa", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "nije kome<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_contains": "ne <PERSON><PERSON><PERSON>i", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "ne zav<PERSON>va se na", "app.components.admin.UserFilterConditions.predicate_not_has_value": "ne sadrži kao vrednost", "app.components.admin.UserFilterConditions.predicate_not_in": "<PERSON>je <PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is": "nije", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "nije administrator", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "nije p<PERSON>no", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "nije", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "nije obi<PERSON> koris<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "nije jedan od", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "nije projektni menadžer", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "<PERSON>je veri<PERSON>n", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "nije obja<PERSON> unos", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "није реаговао на коментар", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "није реаговао на унос", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "didn't register to an event", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "has not taken survey", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "nije volontirao", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "didn't participate in voting", "app.components.admin.UserFilterConditions.predicate_nothing": "nothing", "app.components.admin.UserFilterConditions.predicate_posted_input": "obja<PERSON> je unos", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "реаговао на коментар", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "реаговао на унос", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "registered to an event", "app.components.admin.UserFilterConditions.predicate_something": "something", "app.components.admin.UserFilterConditions.predicate_taken_survey": "has taken survey", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "volontirao", "app.components.admin.UserFilterConditions.predicate_voted_in3": "participated in voting", "app.components.admin.UserFilterConditions.rulesFormLabelField": "A", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "B", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "C", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Нећете добијати обавештења о свом доприносу", "app.components.anonymousParticipationModal.cancel": "Поништити, отказати", "app.components.anonymousParticipationModal.continue": "Настави", "app.components.anonymousParticipationModal.participateAnonymously": "Учествујте анонимно", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "This will safely <b>hide your profile</b> from admins, project managers and other residents for this specific contribution so that nobody is able to link this contribution to you. Anonymous contributions cannot be edited, and are considered final.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Учинити нашу платформу безбедном за сваког корисника је за нас главни приоритет. Речи су важне, зато будите љубазни једни према другима.", "app.components.avatar.titleForAccessibility": "Profile of {fullName}", "app.components.customFields.mapInput.removeAnswer": "Remove answer", "app.components.customFields.mapInput.undo": "Undo", "app.components.customFields.mapInput.undoLastPoint": "Undo last point", "app.components.followUnfollow.follow": "Pratite", "app.components.followUnfollow.followADiscussion": "Pratite", "app.components.followUnfollow.followTooltipInputPage2": "Praćеnjе aktivira imеjl obavеštеnja o projеktu. {unsubscribeLink} u bilo kom trеnutku.", "app.components.followUnfollow.followTooltipProjects2": "Praćеnjе aktivira imеjl obavеštеnja o projеktu. {unsubscribeLink} u bilo kom trеnutku.", "app.components.followUnfollow.unFollow": "<PERSON><PERSON><PERSON> pratiti", "app.components.followUnfollow.unsubscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON> se", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "na<PERSON>e s<PERSON>e", "app.components.form.ErrorDisplay.next": "<PERSON><PERSON><PERSON>ć<PERSON>", "app.components.form.ErrorDisplay.previous": "Prethodno", "app.components.form.ErrorDisplay.save": "сачувати", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Počnite sa kucanjem za pretragu po e-pošti ili imenu korisnika...", "app.components.form.anonymousSurveyMessage2": "All responses to this survey are anonymized.", "app.components.form.backToInputManager": "Back to input manager", "app.components.form.backToProject": "Back to project", "app.components.form.components.controls.mapInput.removeAnswer": "Remove answer", "app.components.form.components.controls.mapInput.undo": "Undo", "app.components.form.components.controls.mapInput.undoLastPoint": "Undo last point", "app.components.form.controls.addressInputAriaLabel": "Address input", "app.components.form.controls.addressInputPlaceholder6": "Enter an address...", "app.components.form.controls.adminFieldTooltip": "Polje je vidljivo samo administratorima", "app.components.form.controls.allStatementsError": "An answer must be selected for all statements.", "app.components.form.controls.back": "Back", "app.components.form.controls.clearAll": "Clear all", "app.components.form.controls.clearAllScreenreader": "Clear all answers from above matrix question", "app.components.form.controls.clickOnMapMultipleToAdd3": "Click on the map to draw. Then, drag on points to move them.", "app.components.form.controls.clickOnMapToAddOrType": "Click on the map or type an address below to add your answer.", "app.components.form.controls.confirm": "Confirm", "app.components.form.controls.cosponsorsPlaceholder": "Start typing a name to search", "app.components.form.controls.currentRank": "Current rank:", "app.components.form.controls.minimumCoordinates2": "A minimum of {numPoints} map points is required.", "app.components.form.controls.noRankSelected": "No rank selected", "app.components.form.controls.notPublic1": "*This answer will only be shared with project managers, and not to the public.", "app.components.form.controls.optionalParentheses": "(optional)", "app.components.form.controls.rankingInstructions": "Drag and drop to rank options.", "app.components.form.controls.selectAsManyAsYouLike": "*Select as many as you like", "app.components.form.controls.selectBetween": "*Select between {minItems} and {maxItems} options", "app.components.form.controls.selectExactly2": "*Select exactly {selectExactly, plural, one {# option} other {# options}}", "app.components.form.controls.selectMany": "*Izaberi koliko god <PERSON>", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Tap on the map to draw. Then, drag on points to move them.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Tap on the map to draw.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Tap on the map to add your answer.", "app.components.form.controls.tapOnMapToAddOrType": "Tap on the map or type an address below to add your answer.", "app.components.form.controls.tapToAddALine": "Tap to add a line", "app.components.form.controls.tapToAddAPoint": "Tap to add a point", "app.components.form.controls.tapToAddAnArea": "Tap to add an area", "app.components.form.controls.uploadShapefileInstructions": "* Upload a zip file containing one or more shapefiles.", "app.components.form.controls.validCordinatesTooltip2": "If the location is not displayed among the options as you type, you can add valid coordinates in the format 'latitude, longitude' to specify a precise location (eg: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} out of {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} out of {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} out of {total}, where {maxValue} is {maxLabel}", "app.components.form.error": "Greška", "app.components.form.locationGoogleUnavailable": "Učitavanje polja sa lokacijom google mape nije uspelo.", "app.components.form.progressBarLabel": "Survey progress", "app.components.form.submit": "Pošalji", "app.components.form.submitApiError": "Do<PERSON><PERSON> je do problema prilikom slanja forme. Molimo vas da proverite greške i pokušate ponovo.", "app.components.form.verifiedBlocked": "You can't edit this field because it contains verified information", "app.components.formBuilder.Page": "Stranica", "app.components.formBuilder.accessibilityStatement": "accessibility statement", "app.components.formBuilder.addAnswer": "Dodaj odgovor", "app.components.formBuilder.addStatement": "Add statement", "app.components.formBuilder.agree": "Agree", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "If you have access to our AI package, you will be able to summarise and categorise text responses with AI", "app.components.formBuilder.askFollowUpToggleLabel": "Ask follow up", "app.components.formBuilder.bad": "Bad", "app.components.formBuilder.buttonLabel": "Button label", "app.components.formBuilder.buttonLink": "Button link", "app.components.formBuilder.cancelLeaveBuilderButtonText": "Cancel", "app.components.formBuilder.category": "Category", "app.components.formBuilder.chooseMany": "Izaberi više", "app.components.formBuilder.chooseOne": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>no", "app.components.formBuilder.close": "Zatvori", "app.components.formBuilder.closed": "Zatvoreno", "app.components.formBuilder.configureMap": "Configure map", "app.components.formBuilder.confirmLeaveBuilderButtonText": "Yes, I want to leave", "app.components.formBuilder.content": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.continuePageLabel": "Continues to", "app.components.formBuilder.cosponsors": "Co-sponsors", "app.components.formBuilder.default": "Podra<PERSON>mevan<PERSON>", "app.components.formBuilder.defaultContent": "Podrazume<PERSON>", "app.components.formBuilder.delete": "Izbriši", "app.components.formBuilder.deleteButtonLabel": "Izbriši", "app.components.formBuilder.description": "Opis", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Ovo je već dodato u obrazac. Podrazumevani sadržaj se može upotrebiti samo jednom.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Adding custom content is not part of your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.components.formBuilder.disagree": "Disagree", "app.components.formBuilder.displayAsDropdown": "Display as dropdown", "app.components.formBuilder.displayAsDropdownTooltip": "Display the options in a dropdown. If you have many options, this is recommended.", "app.components.formBuilder.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.drawArea": "Draw area", "app.components.formBuilder.drawRoute": "Draw route", "app.components.formBuilder.dropPin": "Drop pin", "app.components.formBuilder.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "Provide at least 1 answer. Please note that each answer has to have a title.", "app.components.formBuilder.emptyOptionError": "<PERSON><PERSON><PERSON> barem 1 odgovor", "app.components.formBuilder.emptyStatementError": "Provide at least 1 statement", "app.components.formBuilder.emptyTitleError": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.emptyTitleMessage": "Provide a title for all the answers", "app.components.formBuilder.emptyTitleStatementMessage": "Provide a title for all the statements", "app.components.formBuilder.enable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.errorMessage": "<PERSON><PERSON><PERSON> je do problema, rešite problem kako bi vaše promene bile sačuvane", "app.components.formBuilder.fieldGroup.description": "Opis (opcionalno)", "app.components.formBuilder.fieldGroup.title": "<PERSON><PERSON><PERSON> (opcionalno)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Odgovori na ova pitanja su trenutno dostupni samo u izveženoj excel datoteci u menadžeru unosa i nisu vidljivi korisnicima.", "app.components.formBuilder.fieldLabel": "Izbori odgovora", "app.components.formBuilder.fieldLabelStatement": "Statements", "app.components.formBuilder.fileUpload": "Otpremanje <PERSON>", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Map-based page", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Embed map as context or ask location based questions to participants.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "For optimal user experience, we do not recommend adding point, route, or area questions to map-based pages.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normal page", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Survey mapping features are not included in your current license. Reach out to your GovSuccess Manager to learn more.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Page type", "app.components.formBuilder.formEnd": "<PERSON><PERSON>", "app.components.formBuilder.formField.cancelDeleteButtonText": "Cancel", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "Yes, delete page", "app.components.formBuilder.formField.copyNoun": "Copy", "app.components.formBuilder.formField.copyVerb": "Copy", "app.components.formBuilder.formField.delete": "Delete", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Deleting this page will also delete the logic associated with it. Are you sure you want to delete it?", "app.components.formBuilder.formField.deleteResultsInfo": "This cannot be undone", "app.components.formBuilder.goToPageInputLabel": "Onda je sledeća stranica:", "app.components.formBuilder.good": "Good", "app.components.formBuilder.helmetTitle": "<PERSON><PERSON><PERSON> o<PERSON>", "app.components.formBuilder.imageFileUpload": "Otpremanje slike", "app.components.formBuilder.invalidLogicBadgeMessage": "Nevažeća logika", "app.components.formBuilder.labels2": "Labels (optional)", "app.components.formBuilder.labelsTooltipContent2": "Choose optional labels for any of the linear scale values.", "app.components.formBuilder.lastPage": "Ending", "app.components.formBuilder.layout": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Are you sure you want to leave?", "app.components.formBuilder.leaveBuilderText": "You have unsaved changes. Please save before leaving. If you leave, you'll lose your changes.", "app.components.formBuilder.limitAnswersTooltip": "When turned on, respondents need to select the specified number of answers to proceed.", "app.components.formBuilder.limitNumberAnswers": "Limit number of answers", "app.components.formBuilder.linePolygonMapWarning2": "Line and polygon drawing may not meet accessibility standards. More information can be found in the {accessibilityStatement}.", "app.components.formBuilder.linearScale": "<PERSON><PERSON><PERSON> skala", "app.components.formBuilder.locationDescription": "Lokacija", "app.components.formBuilder.logic": "Logika", "app.components.formBuilder.logicAnyOtherAnswer": "Any other answer", "app.components.formBuilder.logicConflicts.conflictingLogic": "Conflicting logic", "app.components.formBuilder.logicConflicts.interQuestionConflict": "This page contains questions that lead to different pages. If participants answer multiple questions, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "This page has multiple logic rules applied: multi-select question logic, page-level logic, and inter-question logic. When these conditions overlap, question logic will take precedence over page logic, and the furthest page will be shown. Review the logic to ensure it aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "This page contains a multi-select question where options lead to different pages. If participants select multiple options, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "This page contains a multi-select question where options lead to different pages and has questions that lead to other pages. The furthest page will be shown if these conditions overlap. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "This page contains a multi-select question where options lead to different pages and has logic set at both the page and question level. Question logic will take precedence, and the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "This page has logic set at both the page level and question level. Question logic will take precedence over page-level logic. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "This page has logic set at both the page and question levels, and multiple questions direct to different pages. Question logic will take precedence, and the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicNoAnswer2": "Not answered", "app.components.formBuilder.logicPanelAnyOtherAnswer": "If any other answer", "app.components.formBuilder.logicPanelNoAnswer": "If not answered", "app.components.formBuilder.logicValidationError": "Logika se možda neće povezati sa prethodnim stranicama", "app.components.formBuilder.longAnswer": "Dugačak odgovor", "app.components.formBuilder.mapConfiguration": "Map configuration", "app.components.formBuilder.mapping": "Mapping", "app.components.formBuilder.mappingNotInCurrentLicense": "Survey mapping features are not included in your current license. Reach out to your GovSuccess Manager to learn more.", "app.components.formBuilder.matrix": "Matrix", "app.components.formBuilder.matrixSettings.columns": "Columns", "app.components.formBuilder.matrixSettings.rows": "Rows", "app.components.formBuilder.multipleChoice": "Višestruki izbor", "app.components.formBuilder.multipleChoiceHelperText": "If multiple options lead to different pages and participants select more than one, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.multipleChoiceImage": "Image choice", "app.components.formBuilder.multiselect.maximum": "Maximum", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "Neutral", "app.components.formBuilder.newField": "Novo polje", "app.components.formBuilder.number": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Otvori", "app.components.formBuilder.optional": "Opcionalno", "app.components.formBuilder.other": "Other", "app.components.formBuilder.otherOption": "\"Other\" option", "app.components.formBuilder.otherOptionTooltip": "Allow participants to enter a custom response if the provided answers do not match their preference", "app.components.formBuilder.page": "Stranica", "app.components.formBuilder.pageCannotBeDeleted": "This page can't be deleted.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "This page cannot be deleted and does not allow any additional fields to be added.", "app.components.formBuilder.pageRuleLabel": "Sledeća stranica je:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "If no logic is added, the form will follow its normal flow. If both the page and its questions have logic, the question logic will take precedence. Ensure this aligns with your intended flow For more information, visit {supportPageLink}", "app.components.formBuilder.preview": "Preview:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Co-sponsors are not shown on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "File upload questions are shown as unsupported on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Mapping questions are shown on the downloaded PDF, but layers will not be visible. Mapping questions are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Matrix questions are shown on the downloaded PDF but are not currently supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Page titles and descriptions are shown as a section header in the downloaded PDF.", "app.components.formBuilder.printSupportTooltip.ranking": "Ranking questions are shown on the downloaded PDF but are not currently supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Tags are shown as unsupported on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.proposedBudget": "Predloženi budžet", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "<PERSON>vo pitanje ne može se izbrisati.", "app.components.formBuilder.questionDescriptionOptional": "<PERSON><PERSON> pitan<PERSON> (opcionalno)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.randomize": "Randomize", "app.components.formBuilder.randomizeToolTip": "The order of the answers will be randomized per user", "app.components.formBuilder.range": "Opseg", "app.components.formBuilder.ranking": "Ranking", "app.components.formBuilder.rating": "Rating", "app.components.formBuilder.removeAnswer": "Ukloni odgovor", "app.components.formBuilder.required": "Obavezno", "app.components.formBuilder.requiredToggleLabel": "Obeleži odgovor na ovo pitanje kao obavezan", "app.components.formBuilder.ruleForAnswerLabel": "Ako je odgovor:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "If answers include:", "app.components.formBuilder.save": "Sačuvaj", "app.components.formBuilder.selectRangeTooltip": "Izaberite maksimalnu vrednost vaše skale.", "app.components.formBuilder.sentiment": "Sentiment scale", "app.components.formBuilder.shapefileUpload": "Esri shapefile upload", "app.components.formBuilder.shortAnswer": "Kratak odgovor", "app.components.formBuilder.showResponseToUsersToggleLabel": "Prikaži odgovor k<PERSON>", "app.components.formBuilder.singleChoice": "<PERSON><PERSON>", "app.components.formBuilder.staleDataErrorMessage2": "There has been a problem. This input form has been saved more recently somewhere else. This may be because you or another user has it open for editing in another browser window. Please refresh the page to get the latest form and then make your changes again.", "app.components.formBuilder.stronglyAgree": "Strongly agree", "app.components.formBuilder.stronglyDisagree": "Strongly disagree", "app.components.formBuilder.supportArticleLinkText": "ova stranica", "app.components.formBuilder.tags": "Kategorije", "app.components.formBuilder.title": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.toLabel": "do", "app.components.formBuilder.unsavedChanges": "You have unsaved changes", "app.components.formBuilder.useCustomButton2": "Use custom page button", "app.components.formBuilder.veryBad": "Very bad", "app.components.formBuilder.veryGood": "Very good", "app.components.ideas.similarIdeas.engageHere": "Engage here", "app.components.ideas.similarIdeas.noSimilarSubmissions": "No similar submissions found.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "We found similar submisisons - engaging with them can help make them stronger!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Similar submissions already posted:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Looking for similar submissions ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {Less than a day} one {# day} other {# days}} left", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  weeks left", "app.components.screenReaderCurrency.AED": "United Arab Emirates Dirham", "app.components.screenReaderCurrency.AFN": "Afghan Afghani", "app.components.screenReaderCurrency.ALL": "Albanian Lek", "app.components.screenReaderCurrency.AMD": "Armenian Dram", "app.components.screenReaderCurrency.ANG": "Netherlands Antillean Guilder", "app.components.screenReaderCurrency.AOA": "Angolan <PERSON>", "app.components.screenReaderCurrency.ARS": "Argentine Peso", "app.components.screenReaderCurrency.AUD": "Australian Dollar", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "Azerbaijani Manat", "app.components.screenReaderCurrency.BAM": "Bosnia-Herzegovina Convertible Mark", "app.components.screenReaderCurrency.BBD": "Barbadian Dollar", "app.components.screenReaderCurrency.BDT": "Bangladeshi Taka", "app.components.screenReaderCurrency.BGN": "Bulgarian Lev", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "Burundian Franc", "app.components.screenReaderCurrency.BMD": "Bermudian Dollar", "app.components.screenReaderCurrency.BND": "Brunei Dollar", "app.components.screenReaderCurrency.BOB": "Bolivian Boliviano", "app.components.screenReaderCurrency.BOV": "Bolivian M<PERSON>", "app.components.screenReaderCurrency.BRL": "Brazilian Real", "app.components.screenReaderCurrency.BSD": "Bahamian Dollar", "app.components.screenReaderCurrency.BTN": "Bhutanese Ngultrum", "app.components.screenReaderCurrency.BWP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "Belarusian Ruble", "app.components.screenReaderCurrency.BZD": "Belize Dollar", "app.components.screenReaderCurrency.CAD": "Canadian Dollar", "app.components.screenReaderCurrency.CDF": "Congolese Franc", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Swiss Franc", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Chilean Unit of Account (UF)", "app.components.screenReaderCurrency.CLP": "Chilean Peso", "app.components.screenReaderCurrency.CNY": "Chinese Yuan", "app.components.screenReaderCurrency.COP": "Colombian Peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Rican Colón", "app.components.screenReaderCurrency.CRE": "Credit", "app.components.screenReaderCurrency.CUC": "Cuban Convertible Peso", "app.components.screenReaderCurrency.CUP": "Cuban Peso", "app.components.screenReaderCurrency.CVE": "Cape Verdean Escudo", "app.components.screenReaderCurrency.CZK": "Czech Koruna", "app.components.screenReaderCurrency.DJF": "Djiboutian Franc", "app.components.screenReaderCurrency.DKK": "Danish Krone", "app.components.screenReaderCurrency.DOP": "Dominican Peso", "app.components.screenReaderCurrency.DZD": "Algerian Dinar", "app.components.screenReaderCurrency.EGP": "Egyptian Pound", "app.components.screenReaderCurrency.ERN": "Eritrean Nakfa", "app.components.screenReaderCurrency.ETB": "Ethiopian Birr", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Fijian Dollar", "app.components.screenReaderCurrency.FKP": "Falkland Islands Pound", "app.components.screenReaderCurrency.GBP": "British Pound", "app.components.screenReaderCurrency.GEL": "Georgian Lari", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Gibraltar Pound", "app.components.screenReaderCurrency.GMD": "Gambian Dalasi", "app.components.screenReaderCurrency.GNF": "Guinean Franc", "app.components.screenReaderCurrency.GTQ": "Guatemalan <PERSON>", "app.components.screenReaderCurrency.GYD": "Guyanese Dollar", "app.components.screenReaderCurrency.HKD": "Hong Kong Dollar", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "Croatian Kuna", "app.components.screenReaderCurrency.HTG": "Haitian Gourde", "app.components.screenReaderCurrency.HUF": "Hungarian Forint", "app.components.screenReaderCurrency.IDR": "Indonesian Rupiah", "app.components.screenReaderCurrency.ILS": "Israeli New <PERSON>", "app.components.screenReaderCurrency.INR": "Indian Rupee", "app.components.screenReaderCurrency.IQD": "Iraqi <PERSON>", "app.components.screenReaderCurrency.IRR": "Iranian Rial", "app.components.screenReaderCurrency.ISK": "Icelandic Króna", "app.components.screenReaderCurrency.JMD": "Jamaican Dollar", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "Japanese Yen", "app.components.screenReaderCurrency.KES": "Kenyan Shilling", "app.components.screenReaderCurrency.KGS": "Kyrgyzstani Som", "app.components.screenReaderCurrency.KHR": "Cambodian Riel", "app.components.screenReaderCurrency.KMF": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KPW": "North Korean Won", "app.components.screenReaderCurrency.KRW": "South Korean Won", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "Cayman Islands Dollar", "app.components.screenReaderCurrency.KZT": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Lebanese Pound", "app.components.screenReaderCurrency.LKR": "Sri Lankan Rupee", "app.components.screenReaderCurrency.LRD": "Liberian Dollar", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Lithuanian Litas", "app.components.screenReaderCurrency.LVL": "Latvian Lats", "app.components.screenReaderCurrency.LYD": "Libyan Dinar", "app.components.screenReaderCurrency.MAD": "Moroccan <PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MDL": "Moldovan Leu", "app.components.screenReaderCurrency.MGA": "Malagasy Ariary", "app.components.screenReaderCurrency.MKD": "Macedonian Denar", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "Mongolian Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "Mauritanian Ouguiya", "app.components.screenReaderCurrency.MUR": "Mauritian Rupee", "app.components.screenReaderCurrency.MVR": "Maldivian Rufiyaa", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MXN": "Mexican Peso", "app.components.screenReaderCurrency.MXV": "Mexican Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malaysian Ringgit", "app.components.screenReaderCurrency.MZN": "Mozambican Metical", "app.components.screenReaderCurrency.NAD": "Namibian Dollar", "app.components.screenReaderCurrency.NGN": "Nigerian Naira", "app.components.screenReaderCurrency.NIO": "Nicaraguan Córdoba", "app.components.screenReaderCurrency.NOK": "Norwegian Krone", "app.components.screenReaderCurrency.NPR": "Nepalese Rupee", "app.components.screenReaderCurrency.NZD": "New Zealand Dollar", "app.components.screenReaderCurrency.OMR": "Omani R<PERSON>", "app.components.screenReaderCurrency.PAB": "Panamanian Balboa", "app.components.screenReaderCurrency.PEN": "Peruvian Sol", "app.components.screenReaderCurrency.PGK": "Papua New Guinean Kina", "app.components.screenReaderCurrency.PHP": "Philippine Peso", "app.components.screenReaderCurrency.PKR": "Pakistani Rupee", "app.components.screenReaderCurrency.PLN": "Polish Złoty", "app.components.screenReaderCurrency.PYG": "Paraguayan Guaraní", "app.components.screenReaderCurrency.QAR": "Qatari Riyal", "app.components.screenReaderCurrency.RON": "Romanian Leu", "app.components.screenReaderCurrency.RSD": "Serbian Dinar", "app.components.screenReaderCurrency.RUB": "Russian Ruble", "app.components.screenReaderCurrency.RWF": "Rwandan <PERSON>", "app.components.screenReaderCurrency.SAR": "Saudi Riyal", "app.components.screenReaderCurrency.SBD": "Solomon Islands Dollar", "app.components.screenReaderCurrency.SCR": "Seychellois Rupee", "app.components.screenReaderCurrency.SDG": "Sudanese Pound", "app.components.screenReaderCurrency.SEK": "Swedish Krona", "app.components.screenReaderCurrency.SGD": "Singapore Dollar", "app.components.screenReaderCurrency.SHP": "<PERSON>", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "Somali Shilling", "app.components.screenReaderCurrency.SRD": "Surinamese Dollar", "app.components.screenReaderCurrency.SSP": "South Sudanese Pound", "app.components.screenReaderCurrency.STD": "São Tomé and Pr<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SYP": "Syrian Pound", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "Thai Baht", "app.components.screenReaderCurrency.TJS": "<PERSON>i Somoni", "app.components.screenReaderCurrency.TMT": "Turkmenistani Manat", "app.components.screenReaderCurrency.TND": "Tunisian Dinar", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Tongan Paʻanga", "app.components.screenReaderCurrency.TRY": "Turkish Lira", "app.components.screenReaderCurrency.TTD": "Trinidad and Tobago Dollar", "app.components.screenReaderCurrency.TWD": "New Taiwan Dollar", "app.components.screenReaderCurrency.TZS": "Tanzanian <PERSON>", "app.components.screenReaderCurrency.UAH": "Ukrainian Hryvnia", "app.components.screenReaderCurrency.UGX": "Ugandan <PERSON>", "app.components.screenReaderCurrency.USD": "United States Dollar", "app.components.screenReaderCurrency.USN": "United States Dollar (Next day)", "app.components.screenReaderCurrency.USS": "United States Dollar (Same day)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguayan Peso", "app.components.screenReaderCurrency.UZS": "Uzbekistani Som", "app.components.screenReaderCurrency.VEF": "Venezuelan Bolívar", "app.components.screenReaderCurrency.VND": "Vietnamese Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoan <PERSON>", "app.components.screenReaderCurrency.XAF": "Central African CFA Franc", "app.components.screenReaderCurrency.XAG": "Silver (one troy ounce)", "app.components.screenReaderCurrency.XAU": "Gold (one troy ounce)", "app.components.screenReaderCurrency.XBA": "European Composite Unit (EURCO)", "app.components.screenReaderCurrency.XBB": "European Monetary Unit (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "European Unit of Account 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "European Unit of Account 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "East Caribbean Dollar", "app.components.screenReaderCurrency.XDR": "Special Drawing Rights", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "West African CFA Franc", "app.components.screenReaderCurrency.XPD": "Palladium (one troy ounce)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "Platinum (one troy ounce)", "app.components.screenReaderCurrency.XTS": "Codes specifically reserved for testing purposes", "app.components.screenReaderCurrency.XXX": "No currency", "app.components.screenReaderCurrency.YER": "Yemeni R<PERSON>", "app.components.screenReaderCurrency.ZAR": "South African Rand", "app.components.screenReaderCurrency.ZMW": "Zambian <PERSON>", "app.components.screenReaderCurrency.amount": "Amount", "app.components.screenReaderCurrency.currency": "<PERSON><PERSON><PERSON><PERSON>", "app.components.trendIndicator.lastQuarter2": "last quarter", "app.containers.AccessibilityStatement.applicability": "Ova izava o pristupačnosti odnosi se na {demoPlatformLink} koja predstavlja ovaj veb sajt. Ona koristi isti izvorni kôd i ima istu funkcionalnost.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Metoda procene", "app.containers.AccessibilityStatement.assesmentText2022": "Pristupačnost ovog sajta ocenio je spoljni entitet koji nije povezan sa procesom dizajniranja i razvoja. Saglasnost sa pomenutom vezom {demoPlatformLink} može se ustanoviti na ovoj vezi {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "možete izmeniti vaše preferencije", "app.containers.AccessibilityStatement.changePreferencesText": "U bilo kom trenutku, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Izuzeci kod usaglašenosti", "app.containers.AccessibilityStatement.conformanceStatus": "Status usaglašenosti", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Nastojimo da naš sadržaj bude prilagođen svima. Ipak, u pojedinim situacijama se može dogoditi da određeni sadržaj ne bude dostupan, što je naglašeno u nastavku:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demo verzija veb sajta", "app.containers.AccessibilityStatement.email": "Email:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Embedded survey tools", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "The embedded survey tools that are available for use on this platform are third-party software and may not be accessible.", "app.containers.AccessibilityStatement.exception_1": "Naše platforme za digitalno učešće upravljaju korisničkim sadržajima koje objavljuju pojedinci i organizacije. Može se dogoditi da su PDF dokumenti, slike, i druge vrste dokumenata (uključujući i multi-mediju) dodati na platformu kao prilozi ili uneti u polja za tekst od strane korisnika. Ovakvi dokumenti mogu biti nepristupačni. ", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Vaš utisak o pristupačnosti ove platforme je dobrodošao. Molimo vas kontaktirajte nas na jedan od sledećih načina:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Proces slanja utisaka", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Brisel, Belgija", "app.containers.AccessibilityStatement.headTitle": "Accessibility Statement | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} ima obavezu da obezbedi platformu koja je pristupačna svim korisnicima, nevezano za tehnologiju ili sposobnost. Poštuju se trenutni odgovarajući standardi za pristupačnost u našem neprekidnom angažovanju da pristupačnost i upotrebljivost naših platformi dovedemo do maksimuma za sve korisnike.", "app.containers.AccessibilityStatement.mapping": "Mapping", "app.containers.AccessibilityStatement.mapping_1": "Mape na platformi delimično ispunjavaju standarde pristupačnosti. Obim mape, zumiranje i UI vidžeti se mogu kontrolisati pomoću tastature prilikom pregleda mapa. Administratori takođe mogu da konfigurišu stil slojeva mape u komandnom panelu ili koristeći Esri integraciju, da kreiraju pristupačnije palete boja i simbologiju. Korišćenje različitih stilova linija ili poligona (npr. isprekidanih linija) će takođe pomoći da se razlikuju slojevi mape gde god je to moguće, i iako takav stil trenutno ne može da se konfiguriše u okviru naše platforme, može se konfigurisati ako koristite mape sa Esri integracijom.", "app.containers.AccessibilityStatement.mapping_2": "Mape na platformi nisu u potpunosti dostupne jer ne predstavljaju adekvatno osnovne mape, slojeve mape ili trendove u podacima korisnicima koji koriste čitače ekrana. Potpuno dostupne mape bi morale da zvučno predstave slojeve karte i opišu sve relevantne trendove u podacima. Štaviše, crtanje mapa linija i poligona u anketama nije dostupno jer se oblici ne mogu crtati pomoću tastature. Alternativne metode unosa trenutno nisu dostupne zbog tehničke složenosti.", "app.containers.AccessibilityStatement.mapping_3": "Da bi se crtanje mapa linija i poligona učinilo pristupačnijim, preporučujemo da u pitanje ankete ili opis stranice uključite uvod ili objašnjenje o tome šta mapa prikazuje i o svim relevantnim trendovima. <PERSON><PERSON><PERSON><PERSON><PERSON>, može se dati kratko ili dugačko tekstualno pitanje kako bi ispitanici mogli da opišu svoj odgovor jednostavnim rečima ako je potrebno (umesto da kliknu na mapu). Takođe preporučujemo da uključite kontakt informacije za menadžera projekta kako bi ispitanici koji ne mogu da popune pitanje na mapi mogli da zatraže alternativni metod za odgovor na pitanje.", "app.containers.AccessibilityStatement.mapping_4": "Za projekte i predloge postoji opcija za prikaz unosa u prikazu mape. Za ove metode postoji i alternativni prikaz liste.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Naše radionice na mreži poseduju komponentu strimovanja videa, koja trenutno ne podržava titlove.", "app.containers.AccessibilityStatement.pageDescription": "Izjava o pristupačnosti ove web stranice", "app.containers.AccessibilityStatement.postalAddress": "Poštanska adresa:", "app.containers.AccessibilityStatement.publicationDate": "<PERSON>tum o<PERSON>", "app.containers.AccessibilityStatement.publicationDate2024": "This accessibility statement was published on August 21, 2024.", "app.containers.AccessibilityStatement.responsiveness": "Nastojimo da odgovorimo u roku od 1-2 radna dana.", "app.containers.AccessibilityStatement.statusPageText": "statusna stranica", "app.containers.AccessibilityStatement.technologiesIntro": "Pristupačnost ove web stranice omogućuju sledeće tehnologije:", "app.containers.AccessibilityStatement.technologiesTitle": "Tehnologije", "app.containers.AccessibilityStatement.title": "Izjava o pristupačnosti", "app.containers.AccessibilityStatement.userGeneratedContent": "Sadržaj koji generiše korisnik", "app.containers.AccessibilityStatement.workshops": "Radionice", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Select project", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Using the Content Builder will let you use more advanced layout options. For languages where no content is available in the content builder, the regular project description content will be displayed instead.", "app.containers.AdminPage.ProjectDescription.linkText": "Edit description in Content Builder", "app.containers.AdminPage.ProjectDescription.saveError": "Something went wrong while saving the project description.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Use Content Builder for description", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Using the Content Builder will let you use more advanced layout options.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "View project", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "<PERSON><PERSON>", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Kreirajte smart grupu", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Korisnici koji zadovoljavaju navedene uslove će biti automatski dodati u grupu:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Navedite barem jedno pravilo", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Pojedini uslovi nisu kompleti<PERSON>", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Sačuvajte grupu", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Configuring smart groups is not part of your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "<PERSON>vedi naziv grupe", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Verifikacija je onemogućena na vašoj platformi, promenite način verifikacije ili kontaktirajte podršku.", "app.containers.App.appMetaDescription": "Dobrodošli na participativnu platformu {orgName}.\nOtkrijte lokalne projekte i priključite se diskusiji!", "app.containers.App.loading": "Učitavanje...", "app.containers.App.metaTitle1": "Citizen engagement platform | {orgName}", "app.containers.App.skipLinkText": "Skip to main content", "app.containers.AreaTerms.areaTerm": "area", "app.containers.AreaTerms.areasTerm": "areas", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "An account with this email already exists. You can sign out, log in with this email address and verify your account on the settings page.", "app.containers.Authentication.steps.AccessDenied.close": "Close", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "You do not meet the requirements to participate in this process.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Go back to single sign-on verification", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Унесите токен", "app.containers.Authentication.steps.Invitation.token": "Токен", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Already have an account? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Log in", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Email poruke u ovoj kategoriji", "app.containers.CampaignsConsentForm.messageError": "Došlo je do greške prilikom čuvanja vaših email preferencija.", "app.containers.CampaignsConsentForm.messageSuccess": "Vaše email preferencije su sačuvane. ", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Kakvu vrstu email obaveštenja želite da primate? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Notifikacije", "app.containers.CampaignsConsentForm.submit": "Sačuvajte", "app.containers.ChangeEmail.backToProfile": "Назад на подешавања профила", "app.containers.ChangeEmail.confirmationModalTitle": "Потврди свој емаил", "app.containers.ChangeEmail.emailEmptyError": "Наведите адресу е-поште", "app.containers.ChangeEmail.emailInvalidError": "Наведите адресу е-поште у исправном формату, на пример име@провидер.цом", "app.containers.ChangeEmail.emailRequired": "Молимо Вас да унесете емаил адресу.", "app.containers.ChangeEmail.emailTaken": "Овај емаил је већ у употреби.", "app.containers.ChangeEmail.emailUpdateCancelled": "Ажурирање имејлом је отказано.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Да бисте ажурирали своју е-пошту, поново покрените процес.", "app.containers.ChangeEmail.helmetDescription": "Промените своју страницу е-поште", "app.containers.ChangeEmail.helmetTitle": "Промените своју е-пошту", "app.containers.ChangeEmail.newEmailLabel": "Нова е-маил", "app.containers.ChangeEmail.submitButton": "прихвати", "app.containers.ChangeEmail.titleAddEmail": "Додајте своју е-пошту", "app.containers.ChangeEmail.titleChangeEmail": "Промените своју е-пошту", "app.containers.ChangeEmail.updateSuccessful": "Ваша е-пошта је успешно ажурирана.", "app.containers.ChangePassword.currentPasswordLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.currentPasswordRequired": "<PERSON>esi trenutnu lozinku", "app.containers.ChangePassword.goHome": "Idi na početnu", "app.containers.ChangePassword.helmetDescription": "Promeni stranicu sa lozinkom", "app.containers.ChangePassword.helmetTitle": "<PERSON><PERSON><PERSON> lo<PERSON>", "app.containers.ChangePassword.newPasswordLabel": "Nova lozinka", "app.containers.ChangePassword.newPasswordRequired": "Unesi novu lozinku", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Unesi lozinku koja sadrži naj<PERSON>je {minimumPasswordLength} znakova", "app.containers.ChangePassword.passwordChangeSuccessMessage": "<PERSON><PERSON><PERSON> lo<PERSON> je us<PERSON>š<PERSON> až<PERSON>", "app.containers.ChangePassword.passwordEmptyError": "<PERSON><PERSON> lozinku", "app.containers.ChangePassword.passwordsDontMatch": "Potvrdi novu lozinku", "app.containers.ChangePassword.titleAddPassword": "Додајте лозинку", "app.containers.ChangePassword.titleChangePassword": "Промените своју лозинку", "app.containers.Comments.a11y_commentDeleted": "<PERSON><PERSON><PERSON> je obrisan", "app.containers.Comments.a11y_commentPosted": "Komentar je objavljen", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {нема лајкова} one {1 лике} other {# свиђа}}", "app.containers.Comments.a11y_undoLike": "Поништи свиђање", "app.containers.Comments.addCommentError": "Došlo je do greške. Pokušajte ponovo kasnije.", "app.containers.Comments.adminCommentDeletionCancelButton": "Prekid", "app.containers.Comments.adminCommentDeletionConfirmButton": "Obriši ovaj dokument", "app.containers.Comments.cancelCommentEdit": "Prekid", "app.containers.Comments.childCommentBodyPlaceholder": "Napišite odgovor...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "<PERSON><PERSON><PERSON> komentar je obrisan", "app.containers.Comments.commentDeletionCancelButton": "Sačuvaj moj komentar", "app.containers.Comments.commentDeletionConfirmButton": "Obriši moj komentar", "app.containers.Comments.commentLike": "Као", "app.containers.Comments.commentReplyButton": "Odgovor", "app.containers.Comments.commentsSortTitle": "Sortirajte komentare prema", "app.containers.Comments.completeProfileLinkText": "попуните свој профил", "app.containers.Comments.completeProfileToComment": "Молимо {completeRegistrationLink} за коментар.", "app.containers.Comments.confirmCommentDeletion": "Da li ste sigurni da želite da obrišete ovaj komentar? Ovakva odluka je finalna!", "app.containers.Comments.deleteComment": "Izbriši", "app.containers.Comments.deleteReasonDescriptionError": "Navedi više informacija o svom razlogu", "app.containers.Comments.deleteReasonError": "<PERSON><PERSON><PERSON>", "app.containers.Comments.deleteReason_inappropriate": "nije p<PERSON>", "app.containers.Comments.deleteReason_irrelevant": "Ovo ne pripada ovde", "app.containers.Comments.deleteReason_other": "<PERSON>i razlog", "app.containers.Comments.editComment": "Izmenite", "app.containers.Comments.guidelinesLinkText": "naše smernice za zajednice", "app.containers.Comments.ideaCommentBodyPlaceholder": "Napišite vaš komentar ovde", "app.containers.Comments.internalCommentingNudgeMessage": "Making internal comments is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Comments.internalConversation": "Интерни разговор", "app.containers.Comments.loadMoreComments": "Učitajte više komentara", "app.containers.Comments.loadingComments": "Učitavanje komentara...", "app.containers.Comments.loadingMoreComments": "Učitava se više komentara...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Овај коментар није видљив редовним корисницима", "app.containers.Comments.postInternalComment": "Објавите интерни коментар", "app.containers.Comments.postPublicComment": "Објави јавни коментар", "app.containers.Comments.profanityError": "Ups! Izgleda da vaš unos sadrži reči koje nisu u skladu sa našim {guidelinesLink}. Trud<PERSON> se da ovo mesto bude bezbedno za sve. Uredite unos i pokušajte ponovo.", "app.containers.Comments.publicDiscussion": "Јавна расправа", "app.containers.Comments.publishComment": "Postavite svoj komentar", "app.containers.Comments.reportAsSpamModalTitle": "<PERSON><PERSON><PERSON><PERSON> da ovo prijavite kao spam?", "app.containers.Comments.saveComment": "Sačuvajte", "app.containers.Comments.signInLinkText": "<PERSON><PERSON><PERSON><PERSON> se", "app.containers.Comments.signInToComment": "Molimo vas {signInLink} da biste komentarisali.", "app.containers.Comments.signUpLinkText": "registrujte se", "app.containers.Comments.verifyIdentityLinkText": "Verifikujte vaš identitet.", "app.containers.Comments.visibleToUsersPlaceholder": "Овај коментар је видљив редовним корисницима", "app.containers.Comments.visibleToUsersWarning": "Коментари објављени овде биће видљиви редовним корисницима.", "app.containers.ContentBuilder.PageTitle": "Project description", "app.containers.CookiePolicy.advertisingContent": "Reklamni kolačići mogu da se koriste za personalizaciju i merenje efikasnosti koju spoljne marketinške kampanje imaju kroz angažovanje na ovoj platformi. Na ovoj platformi nećemo prikazivati bilo kakvo oglašavanje, ali može se dešavati da primate personalizovane oglase na osnovu stranica koje posećujete.", "app.containers.CookiePolicy.advertisingTitle": "Reklamni kolačići", "app.containers.CookiePolicy.analyticsContents": "Analitički kolačići prate ponašanje posetioca, kao što su stranice koje posećuje i koliko dugo se zadržava na njima. Oni takođe mogu da prikupljaju neke tehničke podatke, uključujući informacije o pregledaču, približnu lokaciju i IP adrese. Mi možemo samo interno da koristimo ove podatke kako bismo nastavili sa poboljšanjem celokupnog korisničkog iskustva i funkcionisanjem platforme. Ovakvi podaci takođe se mogu deliti između Go Vocal i {orgName} za procenu i poboljšanje angažovanja u projektima na platformi. Imajte u vidu da su podaci anonimni i da se koriste na agregiranom nivou - lično vas ne identifikuju. Međutim, moglo bi da dođe do ovakve identifikacije ako ovi podaci dođu u kombinaciju sa ostalim izvorima podataka.", "app.containers.CookiePolicy.analyticsTitle": "Analitički kolačići", "app.containers.CookiePolicy.cookiePolicyDescription": "Detaljno objašnjenje o načinu upotrebe kolačića na ovoj platformi", "app.containers.CookiePolicy.cookiePolicyTitle": "Deklaracija o kolačićima", "app.containers.CookiePolicy.essentialContent": "Neki kolačići esencijalni su za ispravno funkcionisanje ove platforme. Ovi esencijalni kolačići prvenstveno se koriste za potvrdu identiteta vašeg naloga kada posetite platformu i za čuvanje vašeg željenog jezika.", "app.containers.CookiePolicy.essentialTitle": "Esencijalni kolačići", "app.containers.CookiePolicy.externalContent": "Na nekim od naših stranica može se prikazati sadržaj spoljnih dobavljača, kao što su YouTube ili Typeform. Nemamo kontrolu nad ovim kolačićima treće strane i pregledavanje sadržaja ovih spoljnih dobavljača takođe može dovesti do instaliranja kolačića na vaš uređaj.", "app.containers.CookiePolicy.externalTitle": "Eks<PERSON><PERSON>čići", "app.containers.CookiePolicy.functionalContents": "Funkcionalni kolačići mogu se omogućiti posetiocima kako bi primali obaveštenja o izmenama i pristupali kanalima podrške direktno sa platforme.", "app.containers.CookiePolicy.functionalTitle": "Funkcionalni kolačići", "app.containers.CookiePolicy.headCookiePolicyTitle": "Cookie Policy | {orgName}", "app.containers.CookiePolicy.intro": "Kolačići su tekstualne datoteke koje se skladište u pregledaču ili na čvrstom disku vašeg računara ili mobilnog telefona kada posetite veb sajt, a na koje veb sajt može upućivati u toku narednih poseta. Mi koristimo kolačiće da bismo razumeli kako posetioci koriste ovu platformu kako bismo poboljšali njen izgled i iskustvo na njoj, zapamtili vaše željene opcije (kao što je željeni jezik) i podržali ključne funkcije za registrovane korisnike i administratore platforme.", "app.containers.CookiePolicy.manageCookiesDescription": "Možete da omogućite ili onemogućite analitiku, marketing i funkcionalne kolačiće u bilo kom trenutku u željenim opcijama za kolačiće. Takođe možete da izbrišete bilo koje postojeće kolačiće ručno ili automatski putem svog internet pregledača. Međutim, kolačići se ponovo mogu vratiti nakon saglasnosti koju dajete prilikom bilo koje naredne posete ovoj platformi. Ako ne izbrišete kolačiće, vaše željene opcije za kolačiće čuvaju se 60 dana, nakon čega će se od vas ponovo tražiti saglasnost.", "app.containers.CookiePolicy.manageCookiesPreferences": "Idite u svoje {manageCookiesPreferencesButtonText} za pregled kompletne liste integracija sa trećom stranom koje se koriste na ovoj platformi i za upravljanje svojim željenim opcijama.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "postav<PERSON> k<PERSON>", "app.containers.CookiePolicy.manageCookiesTitle": "Upravljanje va<PERSON><PERSON>", "app.containers.CookiePolicy.viewPreferencesButtonText": "<PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.viewPreferencesText": "Niženavedene kategorije kolačića možda se ne odnose na sve posetioce ili platforme. Pogledajte svoje {viewPreferencesButton} za kompletnu listu integracija sa trećom stranom koje se odnose na vas.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Za <PERSON>ta koristimo kola<PERSON>?", "app.containers.CustomPageShow.editPage": "<PERSON><PERSON><PERSON> stra<PERSON>u", "app.containers.CustomPageShow.goBack": "Povratak nazad", "app.containers.CustomPageShow.notFound": "Stranica nije pronađena", "app.containers.DisabledAccount.bottomText": "Можете се поново пријавити од {date}.", "app.containers.DisabledAccount.termsAndConditions": "<PERSON><PERSON>i korišćenja", "app.containers.DisabledAccount.text2": "Ваш налог на платформи за учешће {orgName} је привремено онемогућен због кршења смерница заједнице. За више информација о овоме, можете консултовати {TermsAndConditions}.", "app.containers.DisabledAccount.title": "Ваш налог је привремено онемогућен", "app.containers.EventsShow.addToCalendar": "Add to calendar", "app.containers.EventsShow.editEvent": "Edit event", "app.containers.EventsShow.emailSharingBody2": "Attend this event: {eventTitle}. Read more at {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Event date and time", "app.containers.EventsShow.eventFrom2": "Objavljeno u \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Go back", "app.containers.EventsShow.goToProject": "Pogledajte sve", "app.containers.EventsShow.haveRegistered": "have registered", "app.containers.EventsShow.icsError": "Error downloading the ICS file", "app.containers.EventsShow.linkToOnlineEvent": "Link to online event", "app.containers.EventsShow.locationIconAltText": "Location", "app.containers.EventsShow.metaTitle": "Event: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Online meeting", "app.containers.EventsShow.onlineLinkIconAltText": "Online meeting link", "app.containers.EventsShow.registered": "registered", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 registrants} one {1 registrant} other {# registrants}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} registrants", "app.containers.EventsShow.registrantsIconAltText": "Registrants", "app.containers.EventsShow.socialMediaSharingMessage": "Attend this event: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# participant} other {# participants}}", "app.containers.EventsViewer.allTime": "All time", "app.containers.EventsViewer.date": "Date", "app.containers.EventsViewer.thisMonth2": "Upcoming month", "app.containers.EventsViewer.thisWeek2": "Upcoming week", "app.containers.EventsViewer.today": "Today", "app.containers.IdeaButton.addAContribution": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAPetition": "Add a petition", "app.containers.IdeaButton.addAProject": "<PERSON><PERSON><PERSON><PERSON> proje<PERSON>", "app.containers.IdeaButton.addAProposal": "Add a proposal", "app.containers.IdeaButton.addAQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAnInitiative": "Add an initiative", "app.containers.IdeaButton.addAnOption": "Dodajte soluciju", "app.containers.IdeaButton.postingDisabled": "Novi unosi se trenutno ne prihvataju", "app.containers.IdeaButton.postingInNonActivePhases": "Novi unosi mogu biti dodati samo u sklopu aktivnih faza.", "app.containers.IdeaButton.postingInactive": "Novi unosi se trenutno ne prihvataju.", "app.containers.IdeaButton.postingLimitedMaxReached": "Već ste popunili ovu anketu. Hvala vam na odgovoru!", "app.containers.IdeaButton.postingNoPermission": "Novi unosi se trenutno ne prihvataju", "app.containers.IdeaButton.postingNotYetPossible": "Novi unosi se još uvek ne prihvataju", "app.containers.IdeaButton.signInLinkText": "<PERSON><PERSON><PERSON><PERSON> se", "app.containers.IdeaButton.signUpLinkText": "registrujte se", "app.containers.IdeaButton.submitAnIssue": "Postavite komentar", "app.containers.IdeaButton.submitYourIdea": "Pošaljite svoju ideju", "app.containers.IdeaButton.takeTheSurvey": "Popunite upitnik", "app.containers.IdeaButton.verificationLinkText": "Potvrdite svoj identitet.", "app.containers.IdeaCard.readMore": "Опширније", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {nema komentara} one {1 komentar} other {# komentara}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {no votes} one {1 vote} other {# votes}} out of {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Close filters panel", "app.containers.IdeaCards.a11y_totalItems": "<PERSON><PERSON><PERSON><PERSON> unosa: {ideasCount}", "app.containers.IdeaCards.all": "Sve", "app.containers.IdeaCards.allStatuses": "Svi statusi", "app.containers.IdeaCards.contributions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.ideaTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.initiatives": "Инитиативес", "app.containers.IdeaCards.issueTerm": "Issues", "app.containers.IdeaCards.list": "Lista", "app.containers.IdeaCards.map": "Mapa", "app.containers.IdeaCards.mostDiscussed": "Most discussed", "app.containers.IdeaCards.newest": "Najskorije", "app.containers.IdeaCards.noFilteredResults": "Nisu pronađeni rezultati. Molimo vas pokušajte sa drugim filterom ili terminom pretrage.", "app.containers.IdeaCards.numberResults": "Results ({postCount})", "app.containers.IdeaCards.oldest": "Najstarije", "app.containers.IdeaCards.optionTerm": "Solucije", "app.containers.IdeaCards.petitions": "Petitions", "app.containers.IdeaCards.popular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.projectFilterTitle": "Projekti", "app.containers.IdeaCards.projectTerm": "Projekti", "app.containers.IdeaCards.proposals": "Proposals", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "<PERSON><PERSON><PERSON>č<PERSON>", "app.containers.IdeaCards.resetFilters": "Poništi filtere", "app.containers.IdeaCards.showXResults": "Prika<PERSON>i {ideasCount, plural, one {# rezultat} other {# rezultata}}", "app.containers.IdeaCards.sortTitle": "Sortiranje", "app.containers.IdeaCards.statusTitle": "Status", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "Teme", "app.containers.IdeaCards.topicsTitle": "Teme", "app.containers.IdeaCards.trending": "<PERSON> <PERSON>u", "app.containers.IdeaCards.tryDifferentFilters": "Nisu pronađeni rezultati. Molimo vas pokušajte sa drugim filterom ili terminom pretrage.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} comments} one {{ideasCount} comment} other {{ideasCount} comments}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} contributions} one {{ideasCount} contribution} other {{ideasCount} contributions}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ideja} one {{ideasCount} ideja} other {{ideasCount} ideja}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} initiatives} one {{ideasCount} initiative} other {{ideasCount} initiatives}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} options} one {{ideasCount} option} other {{ideasCount} options}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} petitions} one {{ideasCount} petition} other {{ideasCount} petitions}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projekata} one {{ideasCount} projekat} other {{ideasCount} projekata}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} proposals} one {{ideasCount} proposal} other {{ideasCount} proposals}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} questions} one {{ideasCount} question} other {{ideasCount} questions}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# rezultat} other {# rezultata}}", "app.containers.IdeasEditPage.contributionFormTitle": "Izmenite doprinos", "app.containers.IdeasEditPage.editedPostSave": "Sačuvajte", "app.containers.IdeasEditPage.fileUploadError": "Jedan ili više dokumenata nisu postavljeni. Molimo vas proverite njihovu veličinu i format i pokušajte ponovo. ", "app.containers.IdeasEditPage.formTitle": "Izmenite ideju", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Izmenite vašu objavu. Dodajte nove ili promenite postojeće informacije.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "<PERSON>z<PERSON>i {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Edit initiative", "app.containers.IdeasEditPage.issueFormTitle": "Izmenite problem", "app.containers.IdeasEditPage.optionFormTitle": "Izmenite soluciju", "app.containers.IdeasEditPage.petitionFormTitle": "Edit petition", "app.containers.IdeasEditPage.projectFormTitle": "Uredite projekat", "app.containers.IdeasEditPage.proposalFormTitle": "Edit proposal", "app.containers.IdeasEditPage.questionFormTitle": "Izmenite pitanje", "app.containers.IdeasEditPage.save": "Sačuvajte", "app.containers.IdeasEditPage.submitApiError": "Do<PERSON><PERSON> je do problema prilikom slanja forme. Molimo vas da proverite greške i pokušate ponovo.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "All inputs posted", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Istražite sve unose koji su postavljeni na participativnoj platformi {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Posts | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Objave", "app.containers.IdeasIndexPage.loadMore": "Učitaj više...", "app.containers.IdeasIndexPage.loading": "Učitavanje...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Подразумевано, ваши поднесци ће бити повезани са вашим профилом, осим ако не изаберете ову опцију.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Објавите анонимно", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Видљивост профила", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "This survey is not currently open for responses. Please return to the project for more information.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "This survey is not currently active.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Return to project", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "You have already completed this survey.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Survey submitted", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Thanks for your response!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Opis priloga mora da sadrži manje od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Telo ideje mora da sadrži više od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Naslov priloga mora da sadrži manje od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "Наслов прилога мора да има више од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Please select at least one cosponsor", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Opis ideje mora da sadrži manje od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Opis ideje mora da sadrži više od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Molimo vas da unesete opis", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Наслов идеје мора бити краћи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Na<PERSON>lov ideje mora da sadrži više od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "The initiative description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "The initiative description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "The initiative title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "The initiative title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Opis teme mora da sadr<PERSON>i manje od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Opis teme mora da sadrži više od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "<PERSON><PERSON><PERSON> teme mora da sadr<PERSON>i manje od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "The issue title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_number_required": "This field is required, please enter a valid number", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Opis opcije mora da sadrži manje od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "The option description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "<PERSON><PERSON><PERSON> opcije mora da sadrži manje od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Наслов опције мора бити дужи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Dodajte najmanje jednu kategori<PERSON>", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "The petition description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "The petition description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "The petition title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "The petition title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Opis projekta mora da sadrži manje od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Opis projekta mora da sadrži više od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "<PERSON><PERSON>lov projekta mora da sadrži manje od {limit} z<PERSON><PERSON>", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Наслов пројекта мора бити дужи од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "The proposal description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "The proposal description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "The proposal title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "The proposal title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Unesite broj", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Unesite broj", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "Opis pitanja mora da sadrži manje od {limit} z<PERSON>kova", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "Opis pitanja mora da sadrži više od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "<PERSON><PERSON><PERSON> pitanja mora da sadrži manje od {limit} znakova", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "Наслов питања мора да има више од {limit} знакова", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Molimo vas unesite naziv", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Opis priloga mora da sadrži manje od 80 znakova", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Opis priloga mora da sadrži najmanje 30 znakova", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Naslov priloga mora da sadrži manje od 80 znakova", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Naslov priloga mora da sadrži najmanje od 10 znakova", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Opis ideje mora da sadrži manje od 80 znakova", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Opis ideje mora sadržati najmanje 30 karaktera", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Molimo vas unesite naziv", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Naslov ideje mora da sadrži manje od 80 znakova", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Naziv ideje mora sadržati najmanje 10 karaktera", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Možda ste upotrebili jednu ili više reči koje {guidelinesLink} smatra vulgarnim. Izmenite tekst tako što ćete izbaciti sve što se potencijalno smatra vulgarnim.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "The initiative description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "The initiative description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "The initiative title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "The initiative title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Opis teme mora da sadrži manje od 80 znakova", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Opis teme mora da sadrži najmanje 30 znakova", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "<PERSON><PERSON><PERSON> teme mora da sad<PERSON><PERSON>i manje od 80 znakova", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Naslov teme mora da sadrži najmanje 10 znakova", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Opis opcije mora da sadrži manje od 80 znakova", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Opis opcije mora da sadrži najmanje 30 znakova", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Naslov opcije mora da sadrži manje od 80 znakova", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Naslov opcije mora da sadrži najmanje 10 znakova", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "The petition description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "The petition description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "The petition title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "The petition title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Opis projekta mora da sadrži manje od 80 znakova", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Opis projekta mora da sadrži najmanje 30 znakova", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Naslov projekta mora da sadrži manje od 80 znakova", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Naslov projekta mora da sadrži najmanje 10 znakova", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "The proposal description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "The proposal description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "The proposal title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "The proposal title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Molimo vas da unesete opis", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Opis pitanja mora da sadrži manje od 80 znakova", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Opis pitanja mora da sadrži najmanje 30 znakova", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "<PERSON><PERSON><PERSON> pitanja mora da sadrži manje od 80 znakova", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Naslov pitanja mora da sadrži najmanje 10 znakova", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "Prekid", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "Yes, I want to leave", "app.containers.IdeasNewPage.contributionMetaTitle1": "Add new contribution to project | {orgName}", "app.containers.IdeasNewPage.editSurvey": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Postavite objavu i pridružite se konverzaciji na {orgName} participativnoj platformi.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Add new idea to project | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Add new initiative to project | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Add new issue to project | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Are you sure you want to leave?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Your draft answers have been saved privately and you can return to complete this later.", "app.containers.IdeasNewPage.leaveSurvey": "Leave survey", "app.containers.IdeasNewPage.leaveSurveyText": "Vaši odgovori neće biti sačuvani.", "app.containers.IdeasNewPage.optionMetaTitle1": "Add new option to project | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Add new petition to project | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Add new project to project | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Add new proposal to project | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Add new question to project | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Accept invitation", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Co-sponsorship invitation", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Co-sponsors", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "You have been invited to become a co-sponsor.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Invitation accepted", "app.containers.IdeasShow.Cosponsorship.pending": "pending", "app.containers.IdeasShow.MetaInformation.attachments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} na dan {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Trenutni status", "app.containers.IdeasShow.MetaInformation.location": "Lokacija", "app.containers.IdeasShow.MetaInformation.postedBy": "Objava od", "app.containers.IdeasShow.MetaInformation.similar": "Similar inputs", "app.containers.IdeasShow.MetaInformation.topics": "Teme", "app.containers.IdeasShow.commentCTA": "Dodajte komentar", "app.containers.IdeasShow.contributionEmailSharingBody": "Podržite '{postTitle}' na {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Podržite: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Hvala vam na vašem doprinosu!", "app.containers.IdeasShow.contributionTwitterMessage": "Podržite: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Podržite: {postTitle}", "app.containers.IdeasShow.currentStatus": "Trenutni status", "app.containers.IdeasShow.deletedUser": "nepoznat autor", "app.containers.IdeasShow.ideaEmailSharingBody": "Podržite moju ideju '{ideaTitle}' na {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Podržite moju ideju: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Podržite ovu ideju: {postTitle} ", "app.containers.IdeasShow.ideaWhatsAppMessage": "Podržite ovu ideju: {postTitle} ", "app.containers.IdeasShow.ideasWhatsAppMessage": "Podržite ovaj problem: {postTitle} ", "app.containers.IdeasShow.imported": "Imported", "app.containers.IdeasShow.importedTooltip": "This {inputTerm} was collected offline and automatically uploaded to the platform.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Support this initiative '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Support this initiative: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Thanks for submitting your initiative!", "app.containers.IdeasShow.initiativeTwitterMessage": "Support this initiative: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Support this initiative: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Podržite problem '{postTitle}' na {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Podržite ovaj problem: {postTitle} ", "app.containers.IdeasShow.issueSharingModalTitle": "<PERSON><PERSON>a vam na objavi ovog komentara!", "app.containers.IdeasShow.issueTwitterMessage": "Podržite ovaj problem: {postTitle} ", "app.containers.IdeasShow.metaTitle": "Input: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Podržite ovu soluciju '{postTitle}' na {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Podržite ovu soluciju: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "<PERSON><PERSON>ša solucija je uspešno objavljena!", "app.containers.IdeasShow.optionTwitterMessage": "Podržite ovu soluciju: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Podržite ovu soluciju: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Support this petition '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Support this petition: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Thanks for submitting your petition!", "app.containers.IdeasShow.petitionTwitterMessage": "Support this petition: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Support this petition: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Podržite ovaj projekat '{postTitle}' na {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Podržite ovaj projekat: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "<PERSON><PERSON>a vam na objavi ovog projekta!", "app.containers.IdeasShow.projectTwitterMessage": "Podržite ovaj projekat: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Podržite ovaj projekat: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Support this proposal '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Support this proposal: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Thanks for submitting your proposal!", "app.containers.IdeasShow.proposalTwitterMessage": "Support this proposal: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Support this proposal: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Time left to vote:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} out of {votingThreshold} required votes", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Cancel vote", "app.containers.IdeasShow.proposals.VoteControl.days": "days", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "our guidelines", "app.containers.IdeasShow.proposals.VoteControl.hours": "hours", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status and votes", "app.containers.IdeasShow.proposals.VoteControl.minutes": "mins", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "More info", "app.containers.IdeasShow.proposals.VoteControl.vote": "Vote", "app.containers.IdeasShow.proposals.VoteControl.voted": "Voted", "app.containers.IdeasShow.proposals.VoteControl.votedText": "You'll get notified when this initiative goes to the next step. {x, plural, =0 {There's {xDays} left.} one {There's {xDays} left.} other {There are {xDays} left.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Your vote has been submitted!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Unfortunately, you cannot vote on this proposal. Read why in {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {less than a day} one {one day} other {# days}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {no votes} one {1 vote} other {# votes}}", "app.containers.IdeasShow.questionEmailSharingBody": "Pridružite se diskusiji '{postTitle}' na {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Pridružite se diskusiji: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Vaše pitanje je uspešno objavljeno!", "app.containers.IdeasShow.questionTwitterMessage": "Pridružite se diskusiji: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Pridružite se diskusiji: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "<PERSON><PERSON><PERSON><PERSON> da ovo prijavite kao spam?", "app.containers.IdeasShow.share": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.sharingModalSubtitle": "Neka se vaš glas čuje.", "app.containers.IdeasShow.sharingModalTitle": "<PERSON><PERSON>a vam na objavi vaše ideje!", "app.containers.Navbar.completeOnboarding": "Popunite svoj profil", "app.containers.Navbar.completeProfile": "Комплетан профил", "app.containers.Navbar.confirmEmail2": "Confirm email", "app.containers.Navbar.unverified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Navbar.verified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.beforeYouFollow": "Pre nego što počnete praćenje", "app.containers.NewAuthModal.beforeYouParticipate": "Pre vašeg učešća", "app.containers.NewAuthModal.completeYourProfile": "Попуните свој профил", "app.containers.NewAuthModal.confirmYourEmail": "Potvrdi svoju adresu e-pošte", "app.containers.NewAuthModal.logIn": "<PERSON><PERSON><PERSON><PERSON> se", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Прегледајте услове у наставку да бисте наставили.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Попуните свој профил.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Вратите се на опције за пријаву", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Немате налог? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Пријави се", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Šifra mora da sadrži 4 cifre.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Наставите са ФранцеЦоннецт-ом", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "На овој платформи нису омогућене методе аутентификације.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "Ако наставите, прихватате да примате е-поруке са ове платформе. Можете да изаберете које имејлове желите да примате на страници „Моја подешавања“.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "Е-p<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Navedite adresu e-pošte u ispravnom formatu, na primer, <EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Navedite adresu e-p<PERSON>šte", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Za nastavak unesite svoju adresu e-pošte.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Zaboravljena lozinka?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "P<PERSON>javi se na svoj nalog: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Unesite svoju lozinku", "app.containers.NewAuthModal.steps.Password.password": "Lozinka", "app.containers.NewAuthModal.steps.Password.rememberMe": "<PERSON><PERSON><PERSON><PERSON> me", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Ne označavaj ako koristiš javni računar", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON> je zav<PERSON>š<PERSON>", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Sada nastavite sa svojim učešćem.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Ваш идентитет је верификован. Сада сте пуноправни члан заједнице на овој платформи.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Сада сте верификовани!", "app.containers.NewAuthModal.steps.close": "Близу", "app.containers.NewAuthModal.steps.continue": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.whatAreYouInterestedIn": "What are you interested in?", "app.containers.NewAuthModal.youCantParticipate": "You can't participate", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {nema nepregledanih obaveštenja} one {1 nepregledano obaveštenje} other {# nepregledanih obaveštenja}}", "app.containers.NotificationMenu.adminRightsReceived": "Sada ste administrator platforme", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Your comment on \"{postTitle}\" has been deleted by an admin because\n      {reasonCode, select, irrelevant {it is irrelevant} inappropriate {its content is inappropriate} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} accepted your co-sponsorship invitation", "app.containers.NotificationMenu.deletedUser": "Nepoznat autor", "app.containers.NotificationMenu.error": "Neuspešno učitavanje notifikacija", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} је интерно коментариса<PERSON> унос који вам је додељен", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} је интерно коментарисало унос који сте ви интерно коментарисали", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} је интерно коментари<PERSON><PERSON><PERSON> унос у пројекат којим управљате", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} је интерно коментарисао недодељени унос у пројекту којим се не управља", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} је коментарисало ваш интерни коментар", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} invited you to co-sponsor a contribution", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} invited you to co-sponsor an idea", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} invited you to co-sponsor an initiative", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} invited you to co-sponsor an issue", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} invited you to co-sponsor an option", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} invited you to co-sponsor a petition", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} invited you to co-sponsor a project", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} invited you to co-sponsor a proposal", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} invited you to co-sponsor a question", "app.containers.NotificationMenu.loadMore": "Učitaj više...", "app.containers.NotificationMenu.loading": "Učitavanje notifikacija...", "app.containers.NotificationMenu.mentionInComment": "{name} vas je pomenuo u komentaru", "app.containers.NotificationMenu.mentionInInternalComment": "{name} вас је поменуо у интерном коментару", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} vas je pomenuo u zvaničnom obaveštenju", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "You didn't submit your survey", "app.containers.NotificationMenu.noNotifications": "Još uvek nema notifikacija za vas", "app.containers.NotificationMenu.notificationsLabel": "Notifikacije", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} gave an official update on a contribution you follow", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} gave an official update on an idea you follow", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} gave an official update on an initiative you follow", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} gave an official update on an issue you follow", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} gave an official update on an option you follow", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} gave an official update on a petition you follow", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} gave an official update on a project you follow", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} gave an official update on a proposal you follow", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} gave an official update on a question you follow", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} vam je do<PERSON>n", "app.containers.NotificationMenu.projectModerationRightsReceived": "<PERSON>a ste menadžer projekta {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} je u<PERSON>o u novu fazu", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} će ući u novu fazu {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "A new project was published", "app.containers.NotificationMenu.projectReviewRequest": "{name} requested approval to publish the project \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} approved \"{projectTitle}\" for publication", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} status has changed to {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} je dostigao neophodan broj glasova", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} je prihvatio va<PERSON> poziv", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} commented on a contribution that you follow", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} commented on an idea that you follow", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} commented on an initiative that you follow", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} commented on a issue that you follow", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} commented on an option that you follow", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} commented on a petition that you follow", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} commented on a project that you follow", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} commented on a proposal that you follow", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} commented on a question that you follow", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} reported \"{postTitle}\" as spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} je reagovao/la na vaš komentar", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} reported a comment on \"{postTitle}\" as spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "You didn't submit your votes", "app.containers.NotificationMenu.votingBasketSubmitted": "You voted successfully", "app.containers.NotificationMenu.votingLastChance": "Last chance to vote for {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} vote results revealed", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} vam je dodelio/la {postTitle}", "app.containers.PasswordRecovery.emailError": "Ovo ne deluje kao ispravna email adresa", "app.containers.PasswordRecovery.emailLabel": "Email", "app.containers.PasswordRecovery.emailPlaceholder": "Moja email adresa ", "app.containers.PasswordRecovery.helmetDescription": "Stranica za resetovanje lozinke", "app.containers.PasswordRecovery.helmetTitle": "Resetujte vašu lozinku", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Ako je ova adresa e-pošte registrovana na platformi, poslata je veza za poništavanje lozinke.", "app.containers.PasswordRecovery.resetPassword": "Pošaljite link za ponovno postavljanje lozinke", "app.containers.PasswordRecovery.submitError": "Nismo mogli da pronađemo nalog povezan sa ovom email adresom. Umesto toga, možete pokušati da se registrujete.", "app.containers.PasswordRecovery.subtitle": "Gde možemo poslati link za izbor nove lozinke?", "app.containers.PasswordRecovery.title": "Reset<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.helmetDescription": "Stranica za resetovanje lozinke", "app.containers.PasswordReset.helmetTitle": "Resetujte vašu lozinku", "app.containers.PasswordReset.login": "<PERSON><PERSON><PERSON><PERSON> se", "app.containers.PasswordReset.passwordError": "Dužina lozinke mora biti najmanje 8 karaktera", "app.containers.PasswordReset.passwordLabel": "Lozinka", "app.containers.PasswordReset.passwordPlaceholder": "Nova lozinka", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "<PERSON><PERSON>ša lozinka uspešno je ažurirana.", "app.containers.PasswordReset.pleaseLogInMessage": "Prijavite se sa novom lozinkom", "app.containers.PasswordReset.requestNewPasswordReset": "Zatražite novo resetovanje lozinke", "app.containers.PasswordReset.submitError": "Došlo je do greške. Pokušajte ponovo kasnije.", "app.containers.PasswordReset.title": "Resetujte vašu lozinku", "app.containers.PasswordReset.updatePassword": "Potvrdite novu lozinku", "app.containers.ProjectFolderCards.allProjects": "Svi projekti", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} trenutno radi na", "app.containers.ProjectFolderShowPage.editFolder": "Izmeni folder", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informacije o ovom projektu", "app.containers.ProjectFolderShowPage.metaTitle1": "Folder: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Pročitajte više", "app.containers.ProjectFolderShowPage.seeLess": "Pogledajte manje", "app.containers.ProjectFolderShowPage.share": "<PERSON><PERSON><PERSON>", "app.containers.Projects.PollForm.document": "Документ", "app.containers.Projects.PollForm.formCompleted": "<PERSON><PERSON>a vam što ste odgovorili na ovu anketu!", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Већ сте узели ову анкету.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Ова анкета се може узети само када је ова фаза активна.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "<PERSON><PERSON> anketa trenutno nije omoguc<PERSON>ena", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Trenutno je nemoguće učestvovati u ovoj anketi.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Anketa više nije dostupna jer ovaj projekat više nije aktivan.", "app.containers.Projects.PollForm.sendAnswer": "Pošalji", "app.containers.Projects.a11y_phase": "Faza {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Pre<PERSON> faza", "app.containers.Projects.a11y_titleInputs": "Svi unosi u ovom projektu", "app.containers.Projects.a11y_titleInputsPhase": "Svi unosi u ovoj fazi", "app.containers.Projects.accessRights": "Access rights", "app.containers.Projects.addedToBasket": "Dodato u vašu korpu", "app.containers.Projects.allocateBudget": "Rasporedite svoj budžet", "app.containers.Projects.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.basketSubmitted": "Vaši predlozi su poslati!", "app.containers.Projects.contributions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.createANewPhase": "Create a new phase", "app.containers.Projects.currentPhase": "<PERSON><PERSON><PERSON><PERSON> faza", "app.containers.Projects.document": "Документ", "app.containers.Projects.editProject": "Uredite projekat", "app.containers.Projects.emailSharingBody": "Šta mislite o ovoj inicijativi? Glasajte za nju i podelite diskusiju na {initiativeUrl} kako bi se čuo i vaš <PERSON>las!", "app.containers.Projects.emailSharingSubject": "Podržite moju inicijativu: {initiativeTitle}.", "app.containers.Projects.endedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "app.containers.Projects.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.header": "Projekti", "app.containers.Projects.ideas": "<PERSON><PERSON><PERSON>", "app.containers.Projects.information": "Informacije", "app.containers.Projects.initiatives": "Initiatives", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Прегледајте документ", "app.containers.Projects.invisibleTitlePhaseAbout": "O ovoj fazi", "app.containers.Projects.invisibleTitlePoll": "Popunite anketu", "app.containers.Projects.invisibleTitleSurvey": "Popunite upitnik", "app.containers.Projects.issues": "<PERSON>i", "app.containers.Projects.liveDataMessage": "You're viewing real-time data. Participant counts are continuously updated for administrators. Please note that regular users see cached data, which may result in slight differences in the numbers.", "app.containers.Projects.location": "Lokacija:", "app.containers.Projects.manageBasket": "Upravljajte korpom", "app.containers.Projects.meetMinBudgetRequirement": "Ispunite minimalni budžet kako biste poslali predloge.", "app.containers.Projects.meetMinSelectionRequirement": "Ispunite traženu selekciju da biste poslali odabrane predloge.", "app.containers.Projects.metaTitle1": "Projekat: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Minimalni budžet je obavezan", "app.containers.Projects.myBasket": "Basket", "app.containers.Projects.navPoll": "<PERSON><PERSON><PERSON>", "app.containers.Projects.navSurvey": "Upitnik", "app.containers.Projects.newPhase": "New phase", "app.containers.Projects.nextPhase": "Sledeća faza", "app.containers.Projects.noEndDate": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.noItems": "Još uvek niste odabrali nijednu stavku", "app.containers.Projects.noPastEvents": "<PERSON><PERSON> pro<PERSON> događaja za prikaz", "app.containers.Projects.noPhaseSelected": "<PERSON><PERSON> izabrana nijedna faza", "app.containers.Projects.noUpcomingOrOngoingEvents": "Trenutno nema zakazanih predstojećih i tekućih događaja.", "app.containers.Projects.offlineVotersTooltip": "This number does not reflect any offline voter counts.", "app.containers.Projects.options": "Solucije", "app.containers.Projects.participants": "Učesnici", "app.containers.Projects.participantsTooltip4": "This number also reflects anonymous survey submissions. Anonymous survey submissions are possible if surveys are open to everyone (see the {accessRightsLink} tab for this project).", "app.containers.Projects.pastEvents": "Prethodni dog<PERSON>đaji", "app.containers.Projects.petitions": "Petitions", "app.containers.Projects.phases": "Faze", "app.containers.Projects.previousPhase": "<PERSON><PERSON><PERSON><PERSON> faza", "app.containers.Projects.project": "Projekti", "app.containers.Projects.projectTwitterMessage": "Neka se vaš glas čuje! Učestvujte u {projectName} | {orgName}", "app.containers.Projects.projects": "Projekti", "app.containers.Projects.proposals": "Proposals", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "Pročitajte manje", "app.containers.Projects.readMore": "Pročitajte više", "app.containers.Projects.removeItem": "Obriši stavku", "app.containers.Projects.requiredSelection": "Tražena selekcija", "app.containers.Projects.reviewDocument": "Прегледајте документ", "app.containers.Projects.seeTheContributions": "Pogledajte priloge", "app.containers.Projects.seeTheIdeas": "Pogledajte ideje", "app.containers.Projects.seeTheInitiatives": "See the initiatives", "app.containers.Projects.seeTheIssues": "Pogledajte probleme", "app.containers.Projects.seeTheOptions": "Pogledajte solucije", "app.containers.Projects.seeThePetitions": "See the petitions", "app.containers.Projects.seeTheProjects": "Pogledajte projekte", "app.containers.Projects.seeTheProposals": "See the proposals", "app.containers.Projects.seeTheQuestions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeUpcomingEvents": "See upcoming events", "app.containers.Projects.share": "<PERSON><PERSON><PERSON>", "app.containers.Projects.shareThisProject": "Podelite ovaj projekat", "app.containers.Projects.submitMyBasket": "Submit basket", "app.containers.Projects.survey": "Upitnik", "app.containers.Projects.takeThePoll": "Popunite anketu", "app.containers.Projects.takeTheSurvey": "Popunite upitnik", "app.containers.Projects.timeline": "Vremenski okviri", "app.containers.Projects.upcomingAndOngoingEvents": "Predstojeći i tekući događaji", "app.containers.Projects.upcomingEvents": "Predstojeći događaji", "app.containers.Projects.whatsAppMessage": "{projectName} | sa participativne platforme od {orgName}", "app.containers.Projects.yourBudget": "Total budget", "app.containers.ProjectsIndexPage.metaDescription": "Istražite sve trenutne projekte od {orgName} i saznajte kako možete učestvovati. Pridružite nam se u diskusiji o lokalnim projektima do kojih vam je stalo.", "app.containers.ProjectsIndexPage.metaTitle1": "Projekti • {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projekti", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Ž<PERSON><PERSON> da volontiram", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Molimo vas {signInLink} ili {signUpLink} kako biste se prijavili za volontiranje", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Participation is not currently open for this activity.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "<PERSON><PERSON><PERSON><PERSON> se", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "registrujte se", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Povlačim svoju ponudu za volontiranje", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {nema volontera} one {# volonter} other {#volontera}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Warning: The embedded survey may have accessibility issues for screenreader users. If you experience any challenges, please reach out to the platform admin to receive a link to the survey from the original platform. Alternatively, you can request other ways to fill out the survey.", "app.containers.ProjectsShowPage.process.survey.survey": "Upitnik", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Da biste znali da li možete da učestvujete u ovom upitniku, molimo vas {logInLink} na platformu.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Ovaj upitnik se može popuniti tek nakon što ova faza bude aktivna.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Молимо {completeRegistrationLink} да попуните анкету.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "<PERSON><PERSON> anketa trenutno nije omoguc<PERSON>ena", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Popunjavanje ovog upitnika zahteva verifikaciju vašeg identiteta. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "The survey is no longer available, since this project is no longer active.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "комплетне регистрације", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "Пријавите се", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "пријави се", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Верификујте свој налог сада.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Само одређени корисници могу прегледати овај документ. Молимо прво {signUpLink} или {logInLink}.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Овај документ се може прегледати само када је ова фаза активна.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Молимо {completeRegistrationLink} да прегледате документ.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Нажалост, немате права да прегледате овај документ.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Прегледавање овог документа заһтева верификацију вашег налога. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Документ више није доступан, пошто овај пројекат више није активан.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 pick} other {# picks}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "The percentage of participants who picked this option.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "The percentage of total votes this option received.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Cost:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Show more", "app.containers.ReactionControl.a11y_likesDislikes": "Укупно свиђања: {likesCount}, укупно несвиђања: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "You cancelled your dislike for this input successfully.", "app.containers.ReactionControl.cancelLikeSuccess": "You cancelled your like for this input successfully.", "app.containers.ReactionControl.dislikeSuccess": "You disliked this input successfully.", "app.containers.ReactionControl.likeSuccess": "You liked this input successfully.", "app.containers.ReactionControl.reactionErrorSubTitle": "Због грешке није било могуће регистровати вашу реакцију. Молим покушајте поново за неколико минута.", "app.containers.ReactionControl.reactionSuccessTitle": "Ваша реакција је успешно регистрована!", "app.containers.ReactionControl.vote": "Vote", "app.containers.ReactionControl.voted": "Voted", "app.containers.SearchInput.a11y_cancelledPostingComment": "Cancelled posting comment.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} comments have loaded.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# events have loaded} one {# event has loaded} other {# events have loaded}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# results have loaded} one {# result has loaded} other {# results have loaded}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# search results have loaded} one {# search result has loaded} other {# search results have loaded}}.", "app.containers.SearchInput.removeSearchTerm": "Uklonite termin pretrage", "app.containers.SearchInput.searchAriaLabel": "Pretraga", "app.containers.SearchInput.searchLabel": "Pretraga", "app.containers.SearchInput.searchPlaceholder": "Pretraga", "app.containers.SearchInput.searchTerm": "Termin pretrage: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect je rešenje koje je francuska država predložila da obezbedi i pojednostavi registraciju za više od 700 mrežnih usluga.", "app.containers.SignIn.or": "<PERSON><PERSON>", "app.containers.SignIn.signInError": "Unesene informacije nisu tačne. Kliknite na link za zaboravljenu lozinku kako biste je resetovali.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Koristite FranceConnect da biste se prijavili, pri<PERSON><PERSON> ili verifikovali svoj nalog.", "app.containers.SignIn.whatIsFranceConnect": "Šta je FranceConnect?", "app.containers.SignUp.adminOptions2": "For admins and project managers", "app.containers.SignUp.backToSignUpOptions": "Vratite se na opcije registracije", "app.containers.SignUp.continue": "Continue", "app.containers.SignUp.emailConsent": "Registrujući se dajete saglasnost da primate email obaveštenja sa ove platforme. Na stranici „Moja podešavanja“ možete da odaberete koju obaveštenja želite da primate.", "app.containers.SignUp.emptyFirstNameError": "Unesite ime", "app.containers.SignUp.emptyLastNameError": "Unesite prezime", "app.containers.SignUp.firstNamesLabel": "Ime", "app.containers.SignUp.goToLogIn": "Već imate nalog? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "Pročitao/la sam i slažem se sa {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "Pročitao/la sam i slažem se sa {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "Prihvatam da se podaci koriste na mitgestalten.wien.gv.at. Dalje informacije mogu se pronaći na {link}.", "app.containers.SignUp.invitationErrorText": "Your invitation has expired or has already been used. If you have already used the invitation link to create an account, try signing in. Otherwise, sign up to create a new account.", "app.containers.SignUp.lastNameLabel": "Prezime", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Follow your areas of focus to be notified about them:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Follow your favorite topics to be notified about them:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Save preferences", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Skip for now", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Za nastavak prihvatite našu politiku privatnosti", "app.containers.SignUp.signUp2": "Regis<PERSON><PERSON><PERSON><PERSON> se", "app.containers.SignUp.skip": "Preskočite ovaj korak", "app.containers.SignUp.tacError": "Potrebno je da prihvatite uslove kako bi nastavili dalje", "app.containers.SignUp.thePrivacyPolicy": "politika privatnosti", "app.containers.SignUp.theTermsAndConditions": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.unknownError": "Do<PERSON><PERSON> je do greške. Molimo pokušajte kasnije.", "app.containers.SignUp.viennaConsentEmail": "<PERSON><PERSON> ad<PERSON>", "app.containers.SignUp.viennaConsentFirstName": "Ime", "app.containers.SignUp.viennaConsentFooter": "Informacije na profilu možete promeniti nakon što se prijavite. <PERSON><PERSON> već imate nalog sa istom adresom e-pošte na mitgestalten.wien.gv.at, on će se povezati sa vašim trenutnim nalogom.", "app.containers.SignUp.viennaConsentHeader": "Doći će do prenosa sledećih podataka:", "app.containers.SignUp.viennaConsentLastName": "Prezime", "app.containers.SignUp.viennaConsentUserName": "Korisničko ime", "app.containers.SignUp.viennaDataProtection": "bečka politika privatnosti", "app.containers.SiteMap.contributions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "<PERSON>i", "app.containers.SiteMap.options": "Solucije", "app.containers.SiteMap.projects": "Projekti", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "Izveštaji", "app.containers.SpamReport.buttonSuccess": "Uspeh", "app.containers.SpamReport.inappropriate": "nije p<PERSON>", "app.containers.SpamReport.messageError": "<PERSON><PERSON><PERSON> je do greške prilikom slanja forme, molimo vas pokušajte ponovo.", "app.containers.SpamReport.messageSuccess": "<PERSON><PERSON>š <PERSON>z<PERSON> je poslat", "app.containers.SpamReport.other": "<PERSON>i razlog", "app.containers.SpamReport.otherReasonPlaceholder": "Opis", "app.containers.SpamReport.wrong_content": "Ovo ne pripada ovde", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Uklonite profilnu sliku", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Your votes on proposals that are still open for voting will be deleted. Votes on proposals where the voting period has closed will not be deleted.", "app.containers.UsersEditPage.addPassword": "Додајте лозинку", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Za učešće u projektima koji zahtevaju verifikaciju.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Verifikujte vaš identitet.", "app.containers.UsersEditPage.bio": "O vama", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Ne možete menjati polje koje sadrži verifikovane informacije.", "app.containers.UsersEditPage.buttonSuccessLabel": "Uspeh", "app.containers.UsersEditPage.cancel": "Prekid", "app.containers.UsersEditPage.changeEmail": "Промена Е-маил", "app.containers.UsersEditPage.changePassword2": "Промени лозинку", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Kliknite ovde kako biste ažurirali vašu verifikaciju.", "app.containers.UsersEditPage.conditionsLinkText": "na<PERSON><PERSON>i", "app.containers.UsersEditPage.contactUs": "Drugi razlog za napuštanje? {feedbackLink} i možda možemo da vam pomognemo.", "app.containers.UsersEditPage.deleteAccountSubtext": "<PERSON><PERSON> nam je š<PERSON> odl<PERSON>.", "app.containers.UsersEditPage.deleteMyAccount": "Obriši moj nalog", "app.containers.UsersEditPage.deleteYourAccount": "Obrišite vaš nalog", "app.containers.UsersEditPage.deletionSection": "Obrišite vaš nalog", "app.containers.UsersEditPage.deletionSubtitle": "Ova akcija se ne može poništiti. Sadržaj koji ste objavili na platformi će biti anoniman. Ako želite izbrisati sav svoj sadrž<PERSON>, možete nas <NAME_EMAIL>.", "app.containers.UsersEditPage.email": "Email", "app.containers.UsersEditPage.emailEmptyError": "<PERSON><PERSON><PERSON> ad<PERSON> e-p<PERSON>", "app.containers.UsersEditPage.emailInvalidError": "Navedite adresu e-pošte u ispravnom formatu, na primer, <EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Kažite nam", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Ime", "app.containers.UsersEditPage.firstNamesEmptyError": "<PERSON><PERSON><PERSON> imena", "app.containers.UsersEditPage.h1": "Informacije o vašem nalogu", "app.containers.UsersEditPage.h1sub": "Izmenite informacije o vašem nalogu", "app.containers.UsersEditPage.image": "Slika avatara", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Kliknite kako biste odabrali profilnu sliku (max. 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Sva podešavanja vašeg profila", "app.containers.UsersEditPage.language": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.lastName": "Prezime", "app.containers.UsersEditPage.lastNameEmptyError": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.loading": "Učitavanje...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Овде можете променити своју адресу е-поште или лозинку.", "app.containers.UsersEditPage.loginCredentialsTitle": "Акредитиве за пријављивање", "app.containers.UsersEditPage.messageError": "Nismo uspeli da sačuvamo vaš profil. Pokušajte kasnije <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "<PERSON><PERSON><PERSON> profil je sa<PERSON>", "app.containers.UsersEditPage.metaDescription": "Ovo je stranica za podešavanje profila od {firstName} {lastName} na online participativnoj platformi od {tenantName}. Ovde možete verifikovati svoj identitet, obrisati informacije o vašem nalogu, izbrisati nalog i menjati vaše email preferencije.", "app.containers.UsersEditPage.metaTitle1": "Profile settings page of {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Nakon što kliknete na ovo dugme, neće biti moguće vratiti vaš nalog.", "app.containers.UsersEditPage.noNameWarning2": "Your name is currently displayed on the platform as: \"{displayName}\" because you have not entered your name. This is an autogenerated name. If you would like to change it, please enter your name below.", "app.containers.UsersEditPage.notificationsSubTitle": "Kakvu vrstu email obaveštenja želite da primate? ", "app.containers.UsersEditPage.notificationsTitle": "<PERSON><PERSON>", "app.containers.UsersEditPage.password": "Izaberite novu lozinku", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Unesite lozinku koja sadrži najmanje {minimumPasswordLength} znakova", "app.containers.UsersEditPage.passwordAddSection": "Додајте лозинку", "app.containers.UsersEditPage.passwordAddSubtitle2": "Поставите лозинку и лако се пријавите на платформу, без потребе да сваки пут потврђујете своју е-пошту.", "app.containers.UsersEditPage.passwordChangeSection": "<PERSON><PERSON><PERSON> lo<PERSON>", "app.containers.UsersEditPage.passwordChangeSubtitle": "Potvrdite trenutnu lozinku i zamenite je novom lozinkom.", "app.containers.UsersEditPage.privacyReasons": "Ukoliko brinete o privatnosti, možete pročitati {conditionsLink}.", "app.containers.UsersEditPage.processing": "Šalje se...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Име је обавезно када наведете презиме", "app.containers.UsersEditPage.reasonsToStayListTitle": "Pre nego što odete...", "app.containers.UsersEditPage.submit": "Sačuvajte izmene", "app.containers.UsersEditPage.tooManyEmails": "Primate previše email obaveštenja? Možete upravljati email preferencijama u podešavanju vašeg profila.", "app.containers.UsersEditPage.updateverification": "Da li su se vaši zvanični podaci izmenili?  {reverifyButton}", "app.containers.UsersEditPage.user": "Kada želite da vam po<PERSON><PERSON><PERSON>mo email podsetnik?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Možete učestvovati u projektima koji zahtevaju verifikaciju.", "app.containers.UsersEditPage.verifiedIdentityTitle": "V<PERSON>fikovani ste", "app.containers.UsersEditPage.verifyNow": "Potvrdite sada", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Download your responses (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {нема лајкова} one {1 лике} other {# свиђа}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Objava u kojoj je ovaj komentar objavljen:", "app.containers.UsersShowPage.areas": "Areas", "app.containers.UsersShowPage.commentsWithCount": "Komentari ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Izmeni moj profil", "app.containers.UsersShowPage.emptyInfoText": "You are not following any items of the specified filter above.", "app.containers.UsersShowPage.eventsWithCount": "Events ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Following ({followingCount})", "app.containers.UsersShowPage.inputs": "Inputs", "app.containers.UsersShowPage.invisibleTitlePostsList": "Svi postovi ovog učesnika", "app.containers.UsersShowPage.invisibleTitleUserComments": "Svi komentari ovog učesnika", "app.containers.UsersShowPage.loadMore": "Load more", "app.containers.UsersShowPage.loadMoreComments": "Učitajte više komentara", "app.containers.UsersShowPage.loadingComments": "Učitavanje komentara...", "app.containers.UsersShowPage.loadingEvents": "Loading events...", "app.containers.UsersShowPage.memberSince": "<PERSON><PERSON> od {date}", "app.containers.UsersShowPage.metaTitle1": "Profile page of {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "<PERSON><PERSON> osoba još nije obja<PERSON> nijedan komentar.", "app.containers.UsersShowPage.noCommentsForYou": "<PERSON><PERSON><PERSON> još nema komentara.", "app.containers.UsersShowPage.noEventsForUser": "You have not attended any events yet.", "app.containers.UsersShowPage.postsWithCount": "Objave ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Project folders", "app.containers.UsersShowPage.projects": "Projekti", "app.containers.UsersShowPage.proposals": "Proposals", "app.containers.UsersShowPage.seePost": "Pogledajte post", "app.containers.UsersShowPage.surveyResponses": "Responses ({responses})", "app.containers.UsersShowPage.topics": "Topics", "app.containers.UsersShowPage.tryAgain": "Do<PERSON><PERSON> je do greške. Molimo vas pokušajte kasnije.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Ovo je stranica profila {firstName} {lastName} na platformi za online učešće organizacije {orgName}. U nastavku pregledajte sve objavljene postove.", "app.containers.VoteControl.close": "Zatvori", "app.containers.VoteControl.voteErrorTitle": "<PERSON><PERSON><PERSON> je do greške", "app.containers.admin.ContentBuilder.default": "default", "app.containers.admin.ContentBuilder.imageTextCards": "Image & text cards", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info & accordions", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 column", "app.containers.admin.ContentBuilder.projectDescription": "Project description", "app.containers.app.navbar.admin": "Upravljanje platformom", "app.containers.app.navbar.allProjects": "Svi projekti", "app.containers.app.navbar.ariaLabel": "Primarno", "app.containers.app.navbar.closeMobileNavMenu": "Zatvorite mobilni navigacioni meni", "app.containers.app.navbar.editProfile": "<PERSON><PERSON>", "app.containers.app.navbar.fullMobileNavigation": "Puni mobilni", "app.containers.app.navbar.logIn": "<PERSON><PERSON><PERSON><PERSON> se", "app.containers.app.navbar.logoImgAltText": "{orgName} Početna", "app.containers.app.navbar.myProfile": "Moja aktivnost", "app.containers.app.navbar.search": "Pretraga", "app.containers.app.navbar.showFullMenu": "Show full menu", "app.containers.app.navbar.signOut": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.errorWhenFetchingEvents": "Došlo je do greške pri učitavanju događaja. Pokušajte ponovo učitati stranicu.", "app.containers.eventspage.events": "Events", "app.containers.eventspage.eventsPageDescription": "Prikaži sve događaje objavljene na {orgName} platformi.", "app.containers.eventspage.eventsPageTitle1": "Events | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projekti", "app.containers.eventspage.noPastEvents": "<PERSON><PERSON> pro<PERSON> događaja za prikaz", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Trenutno nema zakazanih predstojećih i tekućih događaja.", "app.containers.eventspage.pastEvents": "Prethodni dog<PERSON>đaji", "app.containers.eventspage.upcomingAndOngoingEvents": "Predstojeći i tekući događaji", "app.containers.footer.accessibility-statement": "Izjava o pristupačnosti", "app.containers.footer.ariaLabel": "Sekundarno", "app.containers.footer.cookie-policy": "Deklaracija o kolačićima", "app.containers.footer.cookieSettings": "Podešavanja <PERSON>", "app.containers.footer.feedbackEmptyError": "Polje za povratne informacije ne može biti prazno.", "app.containers.footer.poweredBy": "Platformu pokreće", "app.containers.footer.privacy-policy": "Politika privatnosti", "app.containers.footer.siteMap": "Mapa sajta", "app.containers.footer.terms-and-conditions": "<PERSON><PERSON><PERSON>", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "Cancel", "app.containers.ideaHeading.confirmLeaveFormButtonText": "Yes, I want to leave", "app.containers.ideaHeading.editForm": "Edit form", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Are you sure you want to leave?", "app.containers.ideaHeading.leaveIdeaForm": "Leave idea form", "app.containers.ideaHeading.leaveIdeaText": "Your responses won't be saved.", "app.containers.landing.cityProjects": "Projekti", "app.containers.landing.completeProfile": "Popunite svoj profil", "app.containers.landing.completeYourProfile": "<PERSON><PERSON>š<PERSON>, {firstName}. Vreme je da popunite svoj profil.", "app.containers.landing.createAccount": "Regis<PERSON><PERSON><PERSON><PERSON> se", "app.containers.landing.defaultSignedInMessage": "{orgName} vas sluša. Na vama je da se vaš glas čuje!", "app.containers.landing.doItLater": "Uradiću to kasnije", "app.containers.landing.new": "novo", "app.containers.landing.subtitleCity": "Dobrodošli na participativnu platformu {orgName}", "app.containers.landing.titleCity": "Oblik<PERSON>jmo budućnost {orgName} zajedno", "app.containers.landing.twitterMessage": "Glasajte za {ideaTitle} na", "app.containers.landing.upcomingEventsWidgetTitle": "Predstojeći i tekući događaji", "app.containers.landing.userDeletedSubtitle": "U bilo kom trenutku možete da otvorite novi nalog ili {contactLink} da biste nas obavestili šta možemo poboljšati.", "app.containers.landing.userDeletedSubtitleLinkText": "Pišite nam", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "<PERSON><PERSON>š na<PERSON> je izbrisan.", "app.containers.landing.userDeletionFailed": "Došlo je do greške prilikom brisanja vašeg naloga, obavešteni smo o problemu i potrudićemo se da ga rešimo. Pokušajte ponovo kasnije.", "app.containers.landing.verifyNow": "Potvrdite sada", "app.containers.landing.verifyYourIdentity": "Verifikujte vaš identitet.", "app.containers.landing.viewAllEventsText": "Pogledaj sve događaje", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Povratak", "app.errors.after_end_at": "Datum početka je postavljen nakon datuma završetka", "app.errors.avatar_carrierwave_download_error": "Neuspešno preuzimanje avatar fajla.", "app.errors.avatar_carrierwave_integrity_error": "Odabrani avatar ne spada u dozvoljeni tip fajlova.", "app.errors.avatar_carrierwave_processing_error": "<PERSON><PERSON> mogu<PERSON>e obraditi avatar.", "app.errors.avatar_extension_blacklist_error": "Ekstenzija slike avatara nije dozvoljena. Dozvoljene ekstenzije su: jpg, jpeg, gif i png.", "app.errors.avatar_extension_whitelist_error": "Ekstenzija slike avatara nije dozvoljena. Dozvoljene ekstenzije su: jpg, jpeg, gif i png.", "app.errors.banner_cta_button_multiloc_blank": "Unesite tekst za dugme.", "app.errors.banner_cta_button_url_blank": "Unesite vezu.", "app.errors.banner_cta_button_url_url": "Unesite važeću vezu. Uverite se da veza počinje sa 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Unesite tekst za dugme.", "app.errors.banner_cta_signed_in_url_blank": "Unesite vezu.", "app.errors.banner_cta_signed_in_url_url": "Unesite važeću vezu. Uverite se da veza počinje sa 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Unesite tekst za dugme.", "app.errors.banner_cta_signed_out_url_blank": "Unesite vezu.", "app.errors.banner_cta_signed_out_url_url": "Unesite važeću vezu. Uverite se da veza počinje sa 'https://'.", "app.errors.base_includes_banned_words": "You may have used one or more words that are considered profanity. Please alter your text to remove any profanities that might be present.", "app.errors.body_multiloc_includes_banned_words": "The description contains words that are considered inappropriate.", "app.errors.bulk_import_idea_not_valid": "The resulting idea is not valid: {value}.", "app.errors.bulk_import_image_url_not_valid": "No image could be downloaded from {value}. Make sure the URL is valid and ends with a file extension such as .png or .jpg. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Idea location with a missing coordinate in {value}. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Idea location with a non-numeric coordinate in {value}. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_malformed_pdf": "The uploaded PDF file appears to be malformed. Try exporting the PDF again from your source and then upload again.", "app.errors.bulk_import_maximum_ideas_exceeded": "The maximum of {value} ideas has been exceeded.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "The maximum of {value} pages in a PDF has been exceeded.", "app.errors.bulk_import_not_enough_pdf_pages": "The uploaded PDF does not have enough pages - it should have at least the same number of pages as the downloaded template.", "app.errors.bulk_import_publication_date_invalid_format": "Idea with invalid publication date format \"{value}\". Please use the format \"DD-MM-YYYY\".", "app.errors.cannot_contain_ideas": "Odabrana participativna metoda ne podržava ovaj tip unosa. Molimo vas da promenite vaš izbor i pokušate ponovo.", "app.errors.cant_change_after_first_response": "Ovo ne možete izmeniti jer su određeni korisnici već odgovorili", "app.errors.category_name_taken": "Kategorija sa ovim imenom već postoji", "app.errors.confirmation_code_expired": "Kod je istekao. Zatražite novi kod.", "app.errors.confirmation_code_invalid": "Nevažeći kod za potvrdu. Proverite tačan kod u vašoj e -pošti ili pokušajte sa „Pošalji novi kod“", "app.errors.confirmation_code_too_many_resets": "Previše ste puta zatražili slanje koda za potvrdu. Molimo vas da nas kontaktirate radi dobijanja koda.", "app.errors.confirmation_code_too_many_retries": "Pokušali ste previše puta. Zatražite novi kod ili pokušajte da promenite adresu e -pošte.", "app.errors.email_already_active": "Email adresa {value} u redu {row} pripada već registrovanom korisniku", "app.errors.email_already_invited": "Email adresa {value} u redu {row} je ve<PERSON> pozvana", "app.errors.email_blank": "Ovo ne može biti prazno", "app.errors.email_domain_blacklisted": "Molimo vas da koristite drugi email domen za registraciju.", "app.errors.email_invalid": "Molimo vas da koristite ispravnu email adresu.", "app.errors.email_taken": "Već postoji nalog sa ovom email adresom. Možete pokušati da pristupite.", "app.errors.email_taken_by_invite": "{value} je već zauzet sa pozivnicom koja je na čekanju. Proverite vaš spam folder ili kontaktirajte {supportEmail} ukoliko ne možete da je pronađete.", "app.errors.emails_duplicate": "<PERSON><PERSON> ili više duplikata email adrese {value} je pronađen u sledećim redovima: {rows}", "app.errors.extension_whitelist_error": "The format of the file you tried to upload is not supported.", "app.errors.file_extension_whitelist_error": "Format fajla koji ste pokušali da postavite nije podržan. ", "app.errors.first_name_blank": "Ovo ne može biti prazno", "app.errors.generics.blank": "Ovo ne može biti prazno.", "app.errors.generics.invalid": "Ovo ne deluje kao ispravna vrednost", "app.errors.generics.taken": "Ovaj email već postoji i za njega je vezan drugi nalog.", "app.errors.generics.unsupported_locales": "Ovo polje ne podržava trenutni prostor.", "app.errors.group_ids_unauthorized_choice_moderator": "Kao projekt<PERSON>, možete slati samo obaveštenja o mogućnosti pristupa projektima", "app.errors.has_other_overlapping_phases": "Projekti ne mogu imati faze koje se preklapaju.", "app.errors.invalid_email": "Email {value} pronađen u redu {row} nije is<PERSON>van.", "app.errors.invalid_row": "<PERSON><PERSON><PERSON> je do nepoznate greške prilikom obrade reda {row} ", "app.errors.is_not_timeline_project": "Trenutni projekat ne podržava faze.", "app.errors.key_invalid": "Ključ može sadržati samo slova, brojeve i donje crte(_)", "app.errors.last_name_blank": "Ovo ne može biti prazno", "app.errors.locale_blank": "Molimo vas odaberite jezik", "app.errors.locale_inclusion": "Molimo vas odaberite podržani jezik", "app.errors.malformed_admin_value": "Administratorska vrednost {value} pronađena u redu {row} nije ispravna", "app.errors.malformed_groups_value": "Grupa {value} pronađena u redu {row} nije ispravna grupa.", "app.errors.max_invites_limit_exceeded1": "Број позивница прелази границу од 1000.", "app.errors.maximum_attendees_greater_than1": "The maximum number of registrants must be greater than 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "The maximum number of registrants must be greater than or equal to the current number of registrants.", "app.errors.no_invites_specified": "Nismo uspeli da pronađemo nijednu email adresu", "app.errors.no_recipients": "The campaign can't be sent out because there are no recipients. The group you're sending to is either empty, or nobody has consented to receiving emails.", "app.errors.number_invalid": "Please enter a valid number.", "app.errors.password_blank": "Ovo ne može biti prazno", "app.errors.password_invalid": "Proverite ponovo svoju trenutnu lozinku.", "app.errors.password_too_short": "Dužina lozinke mora biti najmanje 8 karaktera", "app.errors.resending_code_failed": "Something went wrong while sending out the confirmation code.", "app.errors.slug_taken": "Ovaj URL projekta već postoji. Molimo vas izmenite slug projekta.", "app.errors.tag_name_taken": "A tag with this name already exists", "app.errors.title_multiloc_blank": "Naziv ne može biti prazan.", "app.errors.title_multiloc_includes_banned_words": "The title contains words that are considered inappropriate.", "app.errors.token_invalid": "Linkovi za resetovanje šifre mogu se koristiti samo jednom i važe jedan sat nakon što su poslati. {passwordResetLink}.", "app.errors.too_common": "Ovu lozinku je moguće lako pogoditi. Molimo vas izaberite snažniju lozinku.", "app.errors.too_long": "Molimo odaberite kraću lozinku (najviše 72 znaka)", "app.errors.too_short": "Izaberite lozinku sa najmanje 8 znakova", "app.errors.uncaught_error": "An unknown error occurred.", "app.errors.unknown_group": "Grupa {value} pronađena u redu {row} nije poznata grupa", "app.errors.unknown_locale": "<PERSON><PERSON><PERSON> {value} pronađen u redu {row} nije konfigurisani jezik", "app.errors.unparseable_excel": "Odabrana Excel datoteka nije mogla da se obradi.", "app.errors.url": "Enter a valid link. Make sure the link starts with https://", "app.errors.verification_taken": "Verification cannot be completed as another account has been verified using the same details.", "app.errors.view_name_taken": "Pregled sa ovim imenom već postoji", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Neprikladan sadržaj je automatski otkriven u postu ili komentaru", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Prijavi se na StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Registruj se na StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Kreirajte nalog Stadt Wien sada i upotrebite jedno prijavljivanje za više digitalnih usluga u Beču.", "app.modules.id_cow.cancel": "Prekid", "app.modules.id_cow.emptyFieldError": "Ovo polje ne može biti prazno.", "app.modules.id_cow.helpAltText": "Pokazuje gde se nalazi serijski broj ID na identifikacionoj kartici", "app.modules.id_cow.invalidIdSerialError": "Neispravan ID broj", "app.modules.id_cow.invalidRunError": "Neispravan RUN", "app.modules.id_cow.noMatchFormError": "<PERSON><PERSON> pronađen nijedan pogodak.", "app.modules.id_cow.notEntitledFormError": "<PERSON>ema pravo.", "app.modules.id_cow.showCOWHelp": "Gde mogu da pronađem svoj ID serijski broj ?", "app.modules.id_cow.somethingWentWrongError": "Nismo mogli da vas verifikujemo jer je došlo do greške", "app.modules.id_cow.submit": "Pošalji", "app.modules.id_cow.takenFormError": "Već je urađeno.", "app.modules.id_cow.verifyCow": "Verifikujte se putem COW", "app.modules.id_franceconnect.verificationButtonAltText": "Verifikujte sa FranceConnect", "app.modules.id_gent_rrn.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "Ovo polje ne može biti prazno.", "app.modules.id_gent_rrn.gentRrnHelp": "<PERSON>aš broj socijalnog osiguranja prikazan je na poleđini vaše digitalne lične karte", "app.modules.id_gent_rrn.invalidRrnError": "Nevažeći broj socijalnog osiguranja", "app.modules.id_gent_rrn.noMatchFormError": "Nismo mogli da pronađemo podatke o vašem broju socijalnog osiguranja", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Ne možemo vas verifikovati jer živite izvan Genta", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Ne možemo vas verifikovati jer ste mlađi od 14 godina", "app.modules.id_gent_rrn.rrnLabel": "Broj socijalnog osiguranja", "app.modules.id_gent_rrn.rrnTooltip": "Potreban nam je vaš broj socijalnog osiguranja kako bismo utvrdili da li ste stanovnik Genta, stariji od 14 godina.", "app.modules.id_gent_rrn.showGentRrnHelp": "Gde mogu da pronađem svoj ID serijski broj ?", "app.modules.id_gent_rrn.somethingWentWrongError": "Nismo mogli da vas verifikujemo jer je došlo do greške", "app.modules.id_gent_rrn.submit": "Pošalji", "app.modules.id_gent_rrn.takenFormError": "<PERSON><PERSON>š broj socijalnog osiguranja je već korišćen za verifikaciju drugog naloga", "app.modules.id_gent_rrn.verifyGentRrn": "Verifikujte pomoću GentRrn", "app.modules.id_id_card_lookup.cancel": "Prekid", "app.modules.id_id_card_lookup.emptyFieldError": "Ovo polje ne može biti prazno.", "app.modules.id_id_card_lookup.helpAltText": "Objašnjenje ID kartice", "app.modules.id_id_card_lookup.invalidCardIdError": "Ovaj ID nije validan.", "app.modules.id_id_card_lookup.noMatchFormError": "<PERSON><PERSON> pronađen nijedan pogodak.", "app.modules.id_id_card_lookup.showHelp": "Gde mogu da pronađem svoj ID serijski broj?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Nismo mogli da vas verifikujemo jer je došlo do greške", "app.modules.id_id_card_lookup.submit": "Pošalji", "app.modules.id_id_card_lookup.takenFormError": "Već je urađeno.", "app.modules.id_oostende_rrn.cancel": "Prekid", "app.modules.id_oostende_rrn.emptyFieldError": "Ovo polje ne može biti prazno.", "app.modules.id_oostende_rrn.invalidRrnError": "Nevažeći broj socijalnog osiguranja", "app.modules.id_oostende_rrn.noMatchFormError": "Nismo mogli da pronađemo podatke o vašem broju socijalnog osiguranja", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "<PERSON>e možemo da vas verifiku<PERSON>, jer <PERSON><PERSON><PERSON> O<PERSON>", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Ne možemo vas verifikovati jer ste mlađi od 14 godina", "app.modules.id_oostende_rrn.oostendeRrnHelp": "<PERSON>aš broj socijalnog osiguranja prikazan je na poleđini vaše digitalne lične karte", "app.modules.id_oostende_rrn.rrnLabel": "Broj socijalnog osiguranja", "app.modules.id_oostende_rrn.rrnTooltip": "<PERSON><PERSON>š broj socijalnog osiguranja tražimo kako bismo potvrdili da ste stanovnik <PERSON>, star<PERSON> od 14 godina.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Gde mogu da pronađem svoj broj socijalnog osiguranja?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Nismo mogli da vas verifikujemo jer je došlo do greške", "app.modules.id_oostende_rrn.submit": "Pošalji", "app.modules.id_oostende_rrn.takenFormError": "<PERSON><PERSON>š broj socijalnog osiguranja je već korišćen za verifikaciju drugog naloga", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Verifikuj pomoću broja socijalnog osiguranja", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Dodeljena su vam administratorska prava nad \"{folderName}\" folderom.", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "<PERSON><PERSON><PERSON>", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Pogledajte projekte na {folderUrl} kako bi se čuo i vaš glas!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | sa platforme za učešće {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | sa platforme za učešće {orgName}", "app.sessionRecording.accept": "Yes, I accept", "app.sessionRecording.modalDescription1": "In order to better understand our users, we randomly ask a small percentage of visitors to track their browsing session in detail.", "app.sessionRecording.modalDescription2": "The sole purpose of the recorded data is to improve the website. None of your data will be shared with a 3rd party. Any sensitive information you enter will be filtered.", "app.sessionRecording.modalDescription3": "Do you accept?", "app.sessionRecording.modalDescriptionFaq": "FAQ here.", "app.sessionRecording.modalTitle": "Help us improve this website", "app.sessionRecording.reject": "No, I reject", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Sprovedite vežbu raspodele budžeta", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Прикупите повратне информације о документу", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Kreiraj anketu u okviru plafome", "app.utils.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON><PERSON><PERSON><PERSON> an<PERSON>", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Ugradi spoljnu anketu", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Pronađite volontere", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "prikupljajte unose i povratne informacije", "app.utils.AdminPage.ProjectEdit.shareInformation": "Delite informacije", "app.utils.FormattedCurrency.credits": "krediti", "app.utils.FormattedCurrency.tokens": "<PERSON>i", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# credits} one {# credit} other {# credits}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokens} one {# token} other {# tokens}}", "app.utils.IdeaCards.mostDiscussed": "Most discussed", "app.utils.IdeaCards.mostReacted": "Naj<PERSON>še reak<PERSON>", "app.utils.IdeaCards.newest": "Najnovije", "app.utils.IdeaCards.oldest": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.random": "<PERSON><PERSON><PERSON>č<PERSON>", "app.utils.IdeaCards.trending": "<PERSON> <PERSON>u", "app.utils.IdeasNewPage.contributionFormTitle": "Dodajte novi doprinos", "app.utils.IdeasNewPage.ideaFormTitle": "Dodajte novu ideju", "app.utils.IdeasNewPage.initiativeFormTitle": "Add new initiative", "app.utils.IdeasNewPage.issueFormTitle1": "Add new comment", "app.utils.IdeasNewPage.optionFormTitle": "Dodajte novu soluciju", "app.utils.IdeasNewPage.petitionFormTitle": "Add new petition", "app.utils.IdeasNewPage.projectFormTitle": "Dodajte novi projekat", "app.utils.IdeasNewPage.proposalFormTitle": "Add new proposal", "app.utils.IdeasNewPage.questionFormTitle": "Dodajte novo pitanje", "app.utils.IdeasNewPage.surveyTitle": "Upitnik", "app.utils.IdeasNewPage.viewYourComment": "View your comment", "app.utils.IdeasNewPage.viewYourContribution": "View your contribution", "app.utils.IdeasNewPage.viewYourIdea": "View your idea", "app.utils.IdeasNewPage.viewYourInitiative": "View your initiative", "app.utils.IdeasNewPage.viewYourInput": "View your input", "app.utils.IdeasNewPage.viewYourIssue": "View your issue", "app.utils.IdeasNewPage.viewYourOption": "View your option", "app.utils.IdeasNewPage.viewYourPetition": "View your petition", "app.utils.IdeasNewPage.viewYourProject": "View your project", "app.utils.IdeasNewPage.viewYourProposal": "View your proposal", "app.utils.IdeasNewPage.viewYourQuestion": "View your question", "app.utils.Projects.sendSubmission": "Send submission identifier to my email", "app.utils.Projects.sendSurveySubmission": "Send survey submission identifier to my email", "app.utils.Projects.surveySubmission": "Survey submission", "app.utils.Projects.yourResponseHasTheFollowingId": "Your response has the following identifier: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "If you later decide that you want your response to be removed, please contact us with the following unique identifier:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "You must complete your profile to attend this event.", "app.utils.actionDescriptors.attendingEventNotInGroup": "You do not meet the requirements to attend this event.", "app.utils.actionDescriptors.attendingEventNotPermitted": "You are not permitted to attend this event.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "You must log in or register to attend this event.", "app.utils.actionDescriptors.attendingEventNotVerified": "You must verify your account before you can attend this event.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "You must complete your profile to volunteer.", "app.utils.actionDescriptors.volunteeringNotInGroup": "You do not meet the requirements to volunteer.", "app.utils.actionDescriptors.volunteeringNotPermitted": "You are not permitted to volunteer.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "You must log in or register to volunteer.", "app.utils.actionDescriptors.volunteeringNotVerified": "You must verify your account before you can volunteer.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Please {completeRegistrationLink} to volunteer.", "app.utils.errors.api_error_default.in": "<PERSON><PERSON>", "app.utils.errors.default.ajv_error_birthyear_required": "Unesite svoju godinu rođenja", "app.utils.errors.default.ajv_error_date_any": "Unesite važeći datum", "app.utils.errors.default.ajv_error_domicile_required": "Unesite svoje mesto stanovanja", "app.utils.errors.default.ajv_error_gender_required": "Unesite kog ste roda", "app.utils.errors.default.ajv_error_invalid": "Nije v<PERSON>ž<PERSON>će", "app.utils.errors.default.ajv_error_maxItems": "Ne može da sadrži više od {limit, plural, one {# artikal} other {# artikli}}", "app.utils.errors.default.ajv_error_minItems": "<PERSON><PERSON> da <PERSON><PERSON>i naj<PERSON>je {limit, plural, one {# artikal} other {# artikli}}", "app.utils.errors.default.ajv_error_number_any": "Unesite važeći datum", "app.utils.errors.default.ajv_error_politician_required": "Unesite da li ste političar", "app.utils.errors.default.ajv_error_required3": "Field is required: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Ne može ostati prazno", "app.utils.errors.default.api_error_accepted": "Mora se prihvatiti", "app.utils.errors.default.api_error_blank": "Ne može ostati prazno", "app.utils.errors.default.api_error_confirmation": "<PERSON>e poklapa se", "app.utils.errors.default.api_error_empty": "Ne može ostati prazno", "app.utils.errors.default.api_error_equal_to": "<PERSON><PERSON>", "app.utils.errors.default.api_error_even": "<PERSON><PERSON> da bude jednako", "app.utils.errors.default.api_error_exclusion": "Je rezervisano", "app.utils.errors.default.api_error_greater_than": "Je premalo", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Je premalo", "app.utils.errors.default.api_error_inclusion": "Nije uključeno u listu", "app.utils.errors.default.api_error_invalid": "Nije v<PERSON>ž<PERSON>će", "app.utils.errors.default.api_error_less_than": "<PERSON> preveliko", "app.utils.errors.default.api_error_less_than_or_equal_to": "<PERSON> preveliko", "app.utils.errors.default.api_error_not_a_number": "<PERSON><PERSON> broj", "app.utils.errors.default.api_error_not_an_integer": "<PERSON><PERSON> da bude ceo broj", "app.utils.errors.default.api_error_other_than": "<PERSON><PERSON>", "app.utils.errors.default.api_error_present": "<PERSON><PERSON> da bude prazno", "app.utils.errors.default.api_error_too_long": "<PERSON> predugačko", "app.utils.errors.default.api_error_too_short": "<PERSON> pre<PERSON>ko", "app.utils.errors.default.api_error_wrong_length": "<PERSON> pogreš<PERSON> du<PERSON>", "app.utils.errors.defaultapi_error_.odd": "<PERSON><PERSON> da bude neparno", "app.utils.notInGroup": "Не испуњавате услове за учешће.", "app.utils.participationMethod.onSurveySubmission": "<PERSON><PERSON>a V<PERSON>. <PERSON><PERSON><PERSON> smo <PERSON> odgovor.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Voting is no longer available, since this phase is no longer active.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "You do not meet the requirements to vote.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "You are not permitted to vote.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "You must log in or register to vote.", "app.utils.participationMethodConfig.voting.votingNotVerified": "You must verify your account before you can vote.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Submitting budgets closed on {endDate}.</b> Participants had a total of <b>{maxBudget} each to distribute between {optionCount} options.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Буџет је поднет", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Буџет је поднет 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "You do not meet the requirements to assign budgets.", "app.utils.votingMethodUtils.budgetingNotPermitted": "You are not permitted to assign budgets.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "You must log in or register to assign budgets.", "app.utils.votingMethodUtils.budgetingNotVerified": "You must verify your account before you can assign budgets.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Your budget will not be counted</b> until you click \"Submit\"", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "The minimum required budget is {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "Once you are done, click \"Submit\" to submit your budget.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Select your preferred options by tapping on \"Add\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "You have a total of <b>{maxBudget} to distribute between {optionCount} options</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b>Честитамо, ваш буџет је достављен!</b> Можете да проверите своје опције испод у било ком тренутку или да их измените пре <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b>Congratulations, your budget has been submitted!</b> You can check your options below at any point.", "app.utils.votingMethodUtils.castYourVote": "Cast your vote", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "You can add a maximum of {maxVotes, plural, one {# credit} other {# credits}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "You can add a maximum of {maxVotes, plural, one {# point} other {# points}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "You can add a maximum of {maxVotes, plural, one {# token} other {# tokens}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "You can add a maximum of {maxVotes, plural, one {# vote} other {# votes}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "Once you are done, click “Submit” to cast your vote.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Select your preferred options by tapping on \"Select\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "You have a total of <b>{totalVotes, plural, one {# credit} other {# credits}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "You have a total of <b>{totalVotes, plural, one {# token} other {# tokens}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "You have a total of <b>{totalVotes, plural, one {# vote} other {# votes}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "Коначни резултати", "app.utils.votingMethodUtils.finalTally": "Final tally", "app.utils.votingMethodUtils.howToParticipate": "Како учествовати", "app.utils.votingMethodUtils.howToVote": "How to vote", "app.utils.votingMethodUtils.multipleVotingEnded1": "Voting closed on <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 credits} one {1 credit} other {# credits}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 points} one {1 point} other {# points}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokens} one {1 token} other {# tokens}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 votes} one {1 vote} other {# votes}}", "app.utils.votingMethodUtils.results": "Резултати", "app.utils.votingMethodUtils.singleVotingEnded": "Voting closed on <b>{endDate}.</b> Participants could <b>vote for {maxVotes} options.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Select your preferred options by tapping on “Vote”", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "You have <b>{totalVotes} votes</b> that you can assign to the options.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "Once you are done, click “Submit” to cast your vote.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Voting closed on <b>{endDate}.</b> Participants could <b>vote for 1 option.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Select your preferred option by tapping on “Vote”.", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "You have <b>1 vote</b> that you can assign to one of the options.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Voting closed on <b>{endDate}.</b> Participants could <b>vote for as many options as they wished.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "You can vote for as many options as you would like.", "app.utils.votingMethodUtils.submitYourBudget": "Пошаљите свој буџет", "app.utils.votingMethodUtils.submittedBudgetCountText2": "person submitted their budget online", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "people submitted their budgets online", "app.utils.votingMethodUtils.submittedVoteCountText2": "person submitted their vote online", "app.utils.votingMethodUtils.submittedVotesCountText2": "people submitted their votes online", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Vote submitted 🎉", "app.utils.votingMethodUtils.votesCast": "Votes cast", "app.utils.votingMethodUtils.votingClosed": "Voting closed", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Your vote will not be counted</b> until you click \"Submit\"", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b>Congratulations, your vote has been submitted!</b> You can check or modify your submission before <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b>Congratulations, your vote has been submitted!</b> You can check or modify your submission below at any point.", "components.UI.IdeaSelect.noIdeaAvailable": "There are no ideas available.", "components.UI.IdeaSelect.selectIdea": "Select idea", "containers.SiteMap.allProjects": "Svi projekti", "containers.SiteMap.customPageSection": "Prilagođene stranice", "containers.SiteMap.folderInfo": "Više informacija", "containers.SiteMap.headSiteMapTitle": "Site map | {orgName}", "containers.SiteMap.homeSection": "General<PERSON>", "containers.SiteMap.pageContents": "<PERSON><PERSON><PERSON><PERSON> stranic<PERSON>", "containers.SiteMap.profilePage": "<PERSON><PERSON>ša profilna stranica", "containers.SiteMap.profileSettings": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectIdeas": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectInfo": "Informacije", "containers.SiteMap.projectPoll": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectSurvey": "Upitnik", "containers.SiteMap.projectsArchived": "<PERSON><PERSON><PERSON><PERSON><PERSON> projekti", "containers.SiteMap.projectsCurrent": "Trenutni projekti", "containers.SiteMap.projectsDraft": "Nacrti projekata", "containers.SiteMap.projectsSection": "Projekti od {orgName}", "containers.SiteMap.signInPage": "Prijavite se", "containers.SiteMap.signUpPage": "Regis<PERSON><PERSON><PERSON><PERSON> se", "containers.SiteMap.siteMapDescription": "Sa ove stranice, možete pristupiti bilo kom sadržaju na platformi.", "containers.SiteMap.siteMapTitle": "Mapa sajta {orgName} participativne platforme", "containers.SiteMap.successStories": "Uspešne priče", "containers.SiteMap.timeline": "Faze projekta", "containers.SiteMap.userSpaceSection": "<PERSON><PERSON><PERSON>", "containers.SubscriptionEndedPage.accessDenied": "<PERSON><PERSON><PERSON><PERSON> nemate pristup", "containers.SubscriptionEndedPage.subscriptionEnded": "Ova stranica je dostupna samo za platforme sa aktivnom pretplatom."}
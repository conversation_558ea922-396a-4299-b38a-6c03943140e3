import { defineMessages } from 'react-intl';

export default defineMessages({
  close: {
    id: 'app.components.ConsentManager.Banner.close',
    defaultMessage: 'Close',
  },
  mainText: {
    id: 'app.components.ConsentManager.Banner.mainText',
    defaultMessage: 'By navigating the platform, you agree to our {policyLink}',
  },
  policyLink: {
    id: 'app.components.ConsentManager.Banner.policyLink',
    defaultMessage: 'Cookie Policy',
  },
  manage: {
    id: 'app.components.ConsentManager.Banner.manage',
    defaultMessage: 'Manage',
  },
  reject: {
    id: 'app.components.ConsentManager.Banner.reject',
    defaultMessage: 'Reject',
  },
  accept: {
    id: 'app.components.ConsentManager.Banner.accept',
    defaultMessage: 'Accept',
  },
  ariaButtonClose: {
    id: 'app.components.ConsentManager.Banner.ariaButtonClose2',
    defaultMessage: 'Reject policy and close banner',
  },
  title: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.title',
    defaultMessage: 'Your cookie preferences',
  },
  back: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.back',
    defaultMessage: 'Go back',
  },
  functional: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.functional',
    defaultMessage: 'Functional',
  },
  functionalPurpose: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose',
    defaultMessage:
      'Required to enable and monitor basic functionalities of the website. Some tools listed here might not apply to you. Please refer to our Cookie Policy for more information.',
  },
  analytics: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.analytics',
    defaultMessage: 'Marketing and analytics',
  },
  analyticsPurpose: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose',
    defaultMessage:
      'We use this tracking to understand better how you use the platform in order to learn and improve your navigation. This information is only used in mass analytics, and in no way to track individuals.',
  },
  advertising: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.advertising',
    defaultMessage: 'Advertising',
  },
  advertisingPurpose: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose',
    defaultMessage:
      'To personalize and measure the effectiveness of advertising campains of our website. We will not show any advertising on this platform, but the following services might serve you a personalized ad based on the pages you visit on our site.',
  },
  required: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.required',
    defaultMessage: 'Required',
  },
  requiredPurpose: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose',
    defaultMessage:
      'To have a fonctional platorm, we save the cookie preferences, an authenticating cookie if you sign up, and the language in which you use this platform',
  },
  tools: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.tools',
    defaultMessage: 'Tools',
  },
  allow: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.allow',
    defaultMessage: 'Allow',
  },
  disallow: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.disallow',
    defaultMessage: 'Disallow',
  },
  save: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.save',
    defaultMessage: 'Save',
  },
  cancel: {
    id: 'app.components.ConsentManager.Modal.PreferencesDialog.cancel',
    defaultMessage: 'Cancel',
  },
});

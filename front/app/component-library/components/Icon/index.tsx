import React from 'react';

import styled from 'styled-components';

import { isRtl } from '../../utils/styleUtils';

import Svg, { SvgProps } from './Svg';

export type IconNames = keyof typeof icons;

type ariaHiddenProps =
  | {
      ariaHidden?: true;
    }
  | {
      ariaHidden: false;
      title: string | JSX.Element;
    };

type IconPropsWithoutName = ariaHiddenProps &
  SvgProps & {
    className?: string;
    'data-testid'?: string;
  };

export type IconProps = IconPropsWithoutName & { name: IconNames };

const MirrorOnRtlSvg = styled(Svg)`
  ${isRtl`
    transform: rotate(180deg);
  `}
`;

export const icons = {
  close: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
    </Svg>
  ),
  copy: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" />
    </Svg>
  ),
  'upload-image': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0.621 79 53.713"
      {...props}
    >
      <path d="M72.227 39.8C71.234 34.785 66.81 31 61.5 31c-4.215 0-7.867 2.392-9.697 5.885-4.383.473-7.803 4.185-7.803 8.7 0 4.833 3.916 8.75 8.75 8.75h18.96c4.023 0 7.29-3.27 7.29-7.294 0-3.85-2.997-6.97-6.773-7.24zm-7.81 4.325v5.834h-5.833v-5.835H54.21l7.29-7.29 7.29 7.29h-4.374zM27.378 11.803c0 2.73 2.243 4.97 4.978 4.97s4.978-2.24 4.978-4.97-2.244-4.97-4.978-4.97-4.978 2.24-4.978 4.97zm7.466 0c0 1.387-1.1 2.485-2.49 2.485s-2.488-1.098-2.488-2.485c0-1.387 1.1-2.485 2.49-2.485s2.488 1.098 2.488 2.485z" />
      <path d="M42.597 37.895h-2.833l-6.222-6.95 8.128-8.62 8.77 9.575c.562-.622 1.187-1.17 1.836-1.685l-9.673-10.57c-.255-.27-.62-.413-.99-.387-.32.018-.62.156-.837.388L31.87 29.08 18.98 14.697c-.28-.32-.707-.474-1.127-.408-.276.044-.53.182-.72.388L2.49 30.38V4.35c0-.71.533-1.244 1.244-1.244h48.533c.71 0 1.245.533 1.245 1.243V29.33c.79-.5 1.618-.932 2.488-1.276V4.35C56 2.305 54.313.62 52.267.62H3.733C1.687.62 0 2.306 0 4.35V36.65c0 2.044 1.687 3.728 3.733 3.728h37.392c.398-.884.89-1.718 1.472-2.485zm-38.864 0c-.71 0-1.245-.533-1.245-1.243v-2.62l15.536-16.657 18.395 20.52H3.732z" />
    </Svg>
  ),
  'upload-file': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M14,13V17H10V13H7L12,8L17,13M19.35,10.03C18.67,6.59 15.64,4 12,4C9.11,4 6.6,5.64 5.35,8.03C2.34,8.36 0,10.9 0,14A6,6 0 0,0 6,20H19A5,5 0 0,0 24,15C24,12.36 21.95,10.22 19.35,10.03Z" />
    </Svg>
  ),
  'alert-circle': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M11,15H13V17H11V15M11,7H13V13H11V7M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20Z" />
    </Svg>
  ),
  check: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
    </Svg>
  ),
  halt: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12 2C17.5 2 22 6.5 22 12S17.5 22 12 22 2 17.5 2 12 6.5 2 12 2M12 4C10.1 4 8.4 4.6 7.1 5.7L18.3 16.9C19.3 15.5 20 13.8 20 12C20 7.6 16.4 4 12 4M16.9 18.3L5.7 7.1C4.6 8.4 4 10.1 4 12C4 16.4 7.6 20 12 20C13.9 20 15.6 19.4 16.9 18.3Z" />
    </Svg>
  ),
  'arrow-right': (props: IconPropsWithoutName) => (
    <MirrorOnRtlSvg viewBox="0 0 24 24" {...props}>
      <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z" />
    </MirrorOnRtlSvg>
  ),
  'arrow-down': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="m12 20-8-8 1.4-1.425 5.6 5.6V4h2v12.175l5.6-5.6L20 12Z" />
    </Svg>
  ),
  'arrow-up': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M11 20V7.825l-5.6 5.6L4 12l8-8 8 8-1.4 1.425-5.6-5.6V20Z" />
    </Svg>
  ),
  audio: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 18 14"
      {...props}
    >
      <path d="M2.33341 0.333374L3.68758 3.04171C3.7848 3.23615 3.92369 3.38893 4.10425 3.50004C4.2848 3.61115 4.47925 3.66671 4.68758 3.66671C5.10425 3.66671 5.42369 3.48962 5.64591 3.13546C5.86814 2.78129 5.88203 2.41671 5.68758 2.04171L4.83341 0.333374H6.50008L7.85425 3.04171C7.95147 3.23615 8.09036 3.38893 8.27091 3.50004C8.45147 3.61115 8.64591 3.66671 8.85425 3.66671C9.27091 3.66671 9.59036 3.48962 9.81258 3.13546C10.0348 2.78129 10.0487 2.41671 9.85425 2.04171L9.00008 0.333374H10.6667L12.0209 3.04171C12.1181 3.23615 12.257 3.38893 12.4376 3.50004C12.6181 3.61115 12.8126 3.66671 13.0209 3.66671C13.4376 3.66671 13.757 3.48962 13.9792 3.13546C14.2015 2.78129 14.2154 2.41671 14.0209 2.04171L13.1667 0.333374H15.6667C16.1251 0.333374 16.5174 0.496568 16.8438 0.822957C17.1702 1.14935 17.3334 1.54171 17.3334 2.00004V12C17.3334 12.4584 17.1702 12.8507 16.8438 13.1771C16.5174 13.5035 16.1251 13.6667 15.6667 13.6667H2.33341C1.87508 13.6667 1.48272 13.5035 1.15633 13.1771C0.829942 12.8507 0.666748 12.4584 0.666748 12V2.00004C0.666748 1.54171 0.829942 1.14935 1.15633 0.822957C1.48272 0.496568 1.87508 0.333374 2.33341 0.333374Z" />
    </Svg>
  ),
  plus: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
    </Svg>
  ),
  'plus-circle': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M17,13H13V17H11V13H7V11H11V7H13V11H17M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
    </Svg>
  ),
  delete: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M7 21q-.825 0-1.412-.587Q5 19.825 5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413Q17.825 21 17 21Zm2-4h2V8H9Zm4 0h2V8h-2Z" />
    </Svg>
  ),
  edit: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z" />
    </Svg>
  ),
  'vote-up': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M23,10C23,8.89 22.1,8 21,8H14.68L15.64,3.43C15.66,3.33 15.67,3.22 15.67,3.11C15.67,2.7 15.5,2.32 15.23,2.05L14.17,1L7.59,7.58C7.22,7.95 7,8.45 7,9V19A2,2 0 0,0 9,21H18C18.83,21 19.54,20.5 19.84,19.78L22.86,12.73C22.95,12.5 23,12.26 23,12V10M1,21H5V9H1V21Z" />
    </Svg>
  ),
  'vote-down': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M19,15H23V3H19M15,3H6C5.17,3 4.46,3.5 4.16,4.22L1.14,11.27C1.05,11.5 1,11.74 1,12V14A2,2 0 0,0 3,16H9.31L8.36,20.57C8.34,20.67 8.33,20.77 8.33,20.88C8.33,21.3 8.5,21.67 8.77,21.94L9.83,23L16.41,16.41C16.78,16.05 17,15.55 17,15V5C17,3.89 16.1,3 15,3Z" />
    </Svg>
  ),
  'chevron-right': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
    </Svg>
  ),
  'chevron-left': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z" />
    </Svg>
  ),
  'chevron-up': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M7.41,15.41L12,10.83L16.59,15.41L18,14L12,8L6,14L7.41,15.41Z" />
    </Svg>
  ),
  'chevron-down': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z" />
    </Svg>
  ),
  idea: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12,2A7,7 0 0,1 19,9C19,11.38 17.81,13.47 16,14.74V17A1,1 0 0,1 15,18H9A1,1 0 0,1 8,17V14.74C6.19,13.47 5,11.38 5,9A7,7 0 0,1 12,2M9,21V20H15V21A1,1 0 0,1 14,22H10A1,1 0 0,1 9,21M12,4A5,5 0 0,0 7,9C7,11.05 8.23,12.81 10,13.58V16H14V13.58C15.77,12.81 17,11.05 17,9A5,5 0 0,0 12,4Z" />
    </Svg>
  ),
  'sidebar-input-manager': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 18 25"
      width="18px"
      height="25px"
      {...props}
    >
      <g className="Ideas-icon">
        <path
          className="cl-icon-primary LightBulb-icon"
          d="M8.69628 0L8.55598 0.00113636C6.27294 0.0363636 4.03628 1.01705 2.41879 2.68864C0.798979 4.36364 -0.0810757 6.60909 0.00588619 8.84886C0.0963265 11.2034 1.20132 13.4398 3.0368 14.983C3.14463 15.0739 3.20956 15.1784 3.23275 15.2966C3.52378 16.8409 4.20788 20.4545 6.37729 20.4545H11.0153C13.1858 20.4545 13.8931 16.7057 14.1598 15.2943C14.1818 15.1784 14.2468 15.0739 14.3534 14.9841C16.284 13.3602 17.3913 11.0057 17.3913 8.52273C17.3925 3.82386 13.4919 0 8.69628 0Z"
        />
        <path
          className="cl-icon-accent Ring-1-icon"
          d="M7.3048 0H0.811644C0.363617 0 0 0.339394 0 0.757576C0 1.17576 0.361993 1.51515 0.811644 1.51515H7.3048C7.75283 1.51515 8.11644 1.17576 8.11644 0.757576C8.11644 0.339394 7.75283 0 7.3048 0Z"
          transform="translate(4.63794 20.4545)"
        />
        <path
          className="cl-icon-secondary Ring-2-icon"
          d="M5.21771 0H0.579746C0.259726 0 0 0.339394 0 0.757576C0 1.17576 0.258567 1.51515 0.579746 1.51515H5.21771C5.53773 1.51515 5.79746 1.17576 5.79746 0.757576C5.79746 0.339394 5.53773 0 5.21771 0Z"
          transform="translate(5.79761 21.9697)"
        />
        <path
          className="cl-icon-accent Ring-3-icon"
          d="M2.89873 0H0.579746C0.259726 0 0 0.339394 0 0.757576C0 1.17576 0.258567 1.51515 0.579746 1.51515H2.89873C3.21875 1.51515 3.47848 1.17576 3.47848 0.757576C3.47848 0.339394 3.21875 0 2.89873 0Z"
          transform="translate(6.95703 23.4848)"
        />
      </g>
    </Svg>
  ),
  'sidebar-proposals': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      width="26px"
      height="19px"
      viewBox="0 0 26 19"
      {...props}
    >
      <path
        className="cl-icon-primary"
        d="M1.18286 4.07342C1.37569 5.24106 1.55655 6.34353 2.20154 6.98852C2.86116 7.64814 3.72825 7.97795 4.59533 7.97795C5.23101 7.97795 5.86403 7.79709 6.41726 7.44334C7.96258 9.60705 9.97336 15.7684 10.6662 18.1409C10.75 18.4255 11.0093 18.6183 11.3046 18.6183C11.3099 18.6183 11.3152 18.6183 11.3205 18.6183C11.6211 18.6117 11.8804 18.4016 11.9509 18.109C11.9575 18.0837 12.2847 16.7764 13.2728 14.8242C14.3261 15.5636 15.5416 15.9532 16.7651 15.9519C18.3237 15.9519 19.877 15.3561 21.0619 14.1712C22.2003 13.0328 22.546 10.9303 22.9118 8.70539C23.3067 6.30629 23.715 3.82473 25.0728 2.4656C25.263 2.27542 25.3202 1.9895 25.2164 1.74081C25.114 1.49213 24.8707 1.32988 24.602 1.32988C19.6123 1.32988 15.0602 2.90313 12.427 5.53629C10.9069 7.05634 10.3072 9.3065 10.8644 11.4117C10.9322 11.6697 11.149 11.8625 11.4136 11.8998C11.6796 11.937 11.9403 11.8147 12.0772 11.5846C13.2954 9.56051 14.7982 7.64947 16.543 5.90334C16.8023 5.64401 17.2239 5.64401 17.4832 5.90334C17.7425 6.16266 17.7425 6.58424 17.4832 6.84356C16.9725 7.35424 16.5084 7.86092 16.0682 8.36362C16.0256 8.41282 15.9858 8.46203 15.9432 8.51123C13.451 11.3944 12.0573 14.0781 11.3405 15.8123C10.4761 13.11 8.85626 8.47001 7.37744 6.50976C8.28574 5.19185 8.15941 3.37125 6.98778 2.20095C5.59008 0.801918 3.2854 0 0.665532 0C0.396896 0 0.153528 0.162245 0.0511275 0.410933C-0.0526031 0.65962 0.00458148 0.945545 0.194754 1.13572C0.79985 1.73948 0.994012 2.92707 1.18286 4.07342Z"
      />
    </Svg>
  ),
  'user-circle': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12,19.2C9.5,19.2 7.29,17.92 6,16C6.03,14 10,12.9 12,12.9C14,12.9 17.97,14 18,16C16.71,17.92 14.5,19.2 12,19.2M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12C22,6.47 17.5,2 12,2Z" />
    </Svg>
  ),
  notification: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M21,19V20H3V19L5,17V11C5,7.9 7.03,5.17 10,4.29C10,4.19 10,4.1 10,4A2,2 0 0,1 12,2A2,2 0 0,1 14,4C14,4.1 14,4.19 14,4.29C16.97,5.17 19,7.9 19,11V17L21,19M14,21A2,2 0 0,1 12,23A2,2 0 0,1 10,21" />
    </Svg>
  ),
  search: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
    </Svg>
  ),
  video: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 18 14"
      {...props}
    >
      <path d="M2.38804 0.333313L3.74221 3.04165C3.83943 3.23609 3.97832 3.38887 4.15887 3.49998C4.33943 3.61109 4.53387 3.66665 4.74221 3.66665C5.15887 3.66665 5.47832 3.48956 5.70054 3.1354C5.92276 2.78123 5.93665 2.41665 5.74221 2.04165L4.88804 0.333313H6.55471L7.90887 3.04165C8.0061 3.23609 8.14498 3.38887 8.32554 3.49998C8.5061 3.61109 8.70054 3.66665 8.90887 3.66665C9.32554 3.66665 9.64498 3.48956 9.86721 3.1354C10.0894 2.78123 10.1033 2.41665 9.90887 2.04165L9.05471 0.333313H10.7214L12.0755 3.04165C12.1728 3.23609 12.3117 3.38887 12.4922 3.49998C12.6728 3.61109 12.8672 3.66665 13.0755 3.66665C13.4922 3.66665 13.8117 3.48956 14.0339 3.1354C14.2561 2.78123 14.27 2.41665 14.0755 2.04165L13.2214 0.333313H15.7214C16.1797 0.333313 16.5721 0.496507 16.8985 0.822896C17.2248 1.14929 17.388 1.54165 17.388 1.99998V12C17.388 12.4583 17.2248 12.8507 16.8985 13.1771C16.5721 13.5035 16.1797 13.6666 15.7214 13.6666H2.38804C1.92971 13.6666 1.53735 13.5035 1.21096 13.1771C0.884569 12.8507 0.721375 12.4583 0.721375 12V1.99998C0.721375 1.54165 0.884569 1.14929 1.21096 0.822896C1.53735 0.496507 1.92971 0.333313 2.38804 0.333313Z" />
    </Svg>
  ),
  lock: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
    </Svg>
  ),
  facebook: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="4.588 4.588 257.717 257.719"
      {...props}
    >
      <path d="M248.082 4.588H18.812c-7.856 0-14.224 6.367-14.224 14.224v229.27c0 7.855 6.366 14.226 14.224 14.226h123.432v-99.803H108.66V123.61h33.584V94.924c0-33.287 20.33-51.413 50.024-51.413 14.225 0 26.45 1.06 30.012 1.533v34.788l-20.596.01c-16.147 0-19.274 7.673-19.274 18.934v24.832h38.515l-5.016 38.895h-33.5v99.803h65.673c7.854 0 14.223-6.37 14.223-14.225V18.813c0-7.858-6.37-14.225-14.224-14.225z" />
    </Svg>
  ),
  'facebook-messenger': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="96.216 93 2484.567 2500"
      {...props}
    >
      <path d="M1338.5 93C652.405 93 96.216 611.187 96.216 1250.407c0 364.236 180.648 689.128 462.963 901.289V2593l422.986-233.588c112.886 31.436 232.477 48.402 356.335 48.402 686.096 0 1242.283-518.187 1242.283-1157.407S2024.596 93 1338.5 93zm123.456 1558.643l-316.357-339.507-617.284 339.507 679.012-725.31 324.074 339.507 609.568-339.507-679.013 725.31z" />
    </Svg>
  ),
  twitter: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="1.001 0.991 298.004 242.19"
      {...props}
    >
      <path d="M94.72 243.182c112.46 0 173.96-93.168 173.96-173.96 0-2.646-.054-5.28-.173-7.903 11.938-8.63 22.314-19.4 30.498-31.66-10.955 4.868-22.744 8.146-35.11 9.625 12.623-7.57 22.313-19.543 26.886-33.817-11.812 7.003-24.895 12.093-38.823 14.84C240.8 8.426 224.917.994 207.327.994c-33.763 0-61.143 27.38-61.143 61.132 0 4.798.536 9.465 1.585 13.94-50.816-2.557-95.875-26.886-126.03-63.88-5.252 9.035-8.28 19.53-8.28 30.73 0 21.212 10.795 39.938 27.21 50.893-10.032-.31-19.455-3.063-27.69-7.646-.01.257-.01.507-.01.78 0 29.61 21.075 54.332 49.052 59.935-5.138 1.4-10.543 2.15-16.122 2.15-3.934 0-7.767-.386-11.49-1.102 7.782 24.293 30.353 41.97 57.114 42.465-20.927 16.402-47.287 26.17-75.938 26.17-4.93 0-9.798-.28-14.584-.845 27.06 17.344 59.19 27.464 93.722 27.464" />
    </Svg>
  ),
  'microsoft-windows': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M3,12V6.75L9,5.43V11.91L3,12M20,3V11.75L10,11.9V5.21L20,3M3,13L9,13.09V19.9L3,18.75V13M20,13.25V22L10,20.09V13.1L20,13.25Z" />
    </Svg>
  ),
  google: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 533.5 544.3"
      {...props}
    >
      <path
        fill="#4285f4"
        d="M533.5 278.4c0-18.5-1.5-37.1-4.7-55.3H272.1v104.8h147c-6.1 33.8-25.7 63.7-54.4 82.7v68h87.7c51.5-47.4 81.1-117.4 81.1-200.2z"
      />
      <path
        fill="#34a853"
        d="M272.1 544.3c73.4 0 135.3-24.1 180.4-65.7l-87.7-68c-24.4 16.6-55.9 26-92.6 26-71 0-131.2-47.9-152.8-112.3H28.9v70.1c46.2 91.9 140.3 149.9 243.2 149.9z"
      />
      <path
        fill="#fbbc04"
        d="M119.3 324.3c-11.4-33.8-11.4-70.4 0-104.2V150H28.9c-38.6 76.9-38.6 167.5 0 244.4l90.4-70.1z"
      />
      <path
        fill="#ea4335"
        d="M272.1 107.7c38.8-.6 76.3 14 104.4 40.8l77.7-77.7C405 24.6 339.7-.8 272.1 0 169.2 0 75.1 58 28.9 150l90.4 70.1c21.5-64.5 81.8-112.4 152.8-112.4z"
      />
    </Svg>
  ),
  hoplr: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 141 164"
      fill-rule="evenodd"
      clip-rule="evenodd"
      stroke-linejoin="round"
      stroke-miterlimit="1.414"
      {...props}
    >
      <path
        fill="#ff314b"
        d="M69.399.001c5.638.095 11.219 2.232 15.516 5.94 11.947 10.562 23.82 21.229 35.689 31.914 10.936 9.98 17.471 24.629 17.62 39.576.051 15.57.05 31.141 0 46.712-.188 20.202-17.081 38.926-38.598 39.133-20.33.065-40.66.065-60.99 0C18.748 163.084.242 145.959.038 124.143-.011 108.572-.014 93 .038 77.43c.148-14.906 6.654-29.57 17.62-39.576C29.46 27.231 41.327 16.69 53.158 6.11 57.626 2.16 63.458-.032 69.399 0zm-.294 22.172c-.52.01-1.032.196-1.437.53C55.56 32.913 44.01 43.784 32.234 54.386c-6.406 5.846-10.24 14.431-10.326 23.193-.052 15.512-.145 31.025 0 46.537.14 8.903 7.78 16.9 16.866 16.987 20.275.065 40.55.194 60.824 0 8.764-.142 16.669-7.75 16.757-17.1.048-15.474.05-30.95 0-46.424-.087-8.762-3.898-17.327-10.327-23.193-11.754-10.58-23.57-21.077-35.343-31.605a2.348 2.348 0 0 0-1.58-.608z"
        fillRule="nonzero"
      />
      <path fill="none" d="M0 0h140.87v163.324H0z" />
    </Svg>
  ),
  // Shortened version (only "ID") of https://seeklogo.com/vector-logo/461433/mitid
  mitid: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="640 0 640 333.63934"
      fill-rule="evenodd"
      clip-rule="evenodd"
      stroke-linejoin="round"
      stroke-miterlimit="1.414"
      aria-label="MitID logo"
      {...props}
    >
      <defs id="defs8" />
      <path
        fill="#0060e6"
        fillOpacity="1"
        stroke="none"
        strokeWidth="20.9836"
        d="m 1089.0492,4.1967213 c 134.2951,0 190.9508,73.4426227 190.9508,163.6721287 0,90.22951 -56.6557,163.67213 -190.9508,163.67213 h -54.5574 V 4.1967213 Z"
        id="path352"
        clipPath="none"
      />
      <path
        id="path350"
        clipPath="none"
        fill="#0060e6"
        fillOpacity="1"
        stroke="none"
        strokeWidth="20.9836"
        d="M 847.73828 0 C 803.67276 0 768 35.672751 768 79.738281 C 768 123.80381 803.67276 159.47461 847.73828 159.47461 C 891.80382 159.47461 927.47461 123.80381 927.47461 79.738281 C 927.47461 35.672751 891.80382 0 847.73828 0 z M 847.73828 188.85156 C 774.29573 188.85156 713.44226 230.81981 705.04883 331.54102 L 992.52539 331.54102 C 984.13196 230.81981 923.2792 188.85156 847.73828 188.85156 z "
      />
    </Svg>
  ),
  idporten: (props: IconPropsWithoutName) => (
    <Svg viewBox="0 0 25 25" fill="none" aria-label="ID-Porten logo" {...props}>
      <g clipPath="url(#clip0_44:1969)">
        <path
          d="M12.67 0H0.5C0.367392 0 0.240215 0.0526785 0.146447 0.146447C0.0526785 0.240215 0 0.367392 0 0.5V24.24C0 24.3726 0.0526785 24.4998 0.146447 24.5936C0.240215 24.6873 0.367392 24.74 0.5 24.74H12.67C15.8885 24.6485 18.9445 23.3056 21.1887 20.9967C23.4329 18.6878 24.6883 15.5948 24.6883 12.375C24.6883 9.15517 23.4329 6.06222 21.1887 3.75332C18.9445 1.44442 15.8885 0.101539 12.67 0.0100002V0ZM13.86 21.01C12.0187 21.3269 10.1242 21.0495 8.45107 20.2179C6.77789 19.3864 5.4128 18.0439 4.55347 16.3848C3.69414 14.7258 3.38516 12.8363 3.67129 10.9899C3.95741 9.1435 4.82378 7.43612 6.14495 6.11495C7.46612 4.79378 9.1735 3.92741 11.0199 3.64129C12.8663 3.35516 14.7558 3.66414 16.4148 4.52347C18.0739 5.3828 19.4164 6.74789 20.2479 8.42106C21.0795 10.0942 21.3569 11.9887 21.04 13.83C20.7316 15.6219 19.8757 17.2743 18.59 18.56C17.3043 19.8457 15.6519 20.7016 13.86 21.01Z"
          fill="#0062B8"
        />
      </g>
      <defs>
        <clipPath id="clip0_44:1969">
          <rect width="24.69" height="24.74" fill="white" />
        </clipPath>
      </defs>
    </Svg>
  ),
  idaustria: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      width="24px"
      height="24px"
      viewBox="7 7 24 24"
      aria-label="ID Austria logo"
      {...props}
    >
      <title>Group</title>
      <defs>
        <polygon
          id="path-1"
          points="113.037546 16.6442442 126.167633 16.6442442 126.167633 31.5545706 113.037546 31.5545706"
        />
        <polygon
          id="path-3"
          points="0 0 38.6685573 0 38.6685573 38.6685302 0 38.6685302"
        />
        <path
          d="M21.7962304,13.5174575 C21.6108262,13.5174575 21.4321885,13.5640116 21.2584228,13.6563077 L21.2584228,13.6563077 C21.0849277,13.7491452 20.9514908,13.8763568 20.8594653,14.0379427 L20.8594653,14.0379427 L19.7838501,15.9117435 L16.3491351,15.9117435 L16.3491351,17.4729284 L20.0612798,17.4729284 C20.2464134,17.4729284 20.4261337,17.4269156 20.5996287,17.3343489 L20.5996287,17.3343489 C20.7725825,17.2420527 20.9054781,17.1145704 20.9983155,16.9524432 L20.9983155,16.9524432 L22.0736601,15.0789131 L28.4239582,15.0789131 C28.7934134,15.0789131 29.1054879,15.2061247 29.3601819,15.4605481 L29.3601819,15.4605481 C29.6146053,15.7152421 29.7420876,16.0275873 29.7420876,16.3973131 L29.7420876,16.3973131 L29.7420876,21.9145102 C29.7420876,22.2845067 29.6146053,22.5971226 29.3601819,22.8510046 L29.3601819,22.8510046 C29.1054879,23.1056986 28.7934134,23.2326396 28.4239582,23.2326396 L28.4239582,23.2326396 L22.1085756,23.2326396 L20.2004006,19.9365041 C20.1075632,19.7749182 19.9749383,19.6471652 19.8011725,19.5548691 L19.8011725,19.5548691 C19.6279481,19.462573 19.4482278,19.4157483 19.2636356,19.4157483 L19.2636356,19.4157483 L16.3491351,19.4157483 L16.3491351,20.9772038 L18.9856646,20.9772038 L20.8594653,24.238965 C20.9514908,24.4010923 21.0849277,24.5337172 21.2584228,24.6379225 L21.2584228,24.6379225 C21.4321885,24.7421278 21.6108262,24.7940951 21.7962304,24.7940951 L21.7962304,24.7940951 L28.7011172,24.7940951 C29.4411102,24.7940951 30.0595754,24.545897 30.5578662,24.0481475 L30.5578662,24.0481475 C31.0548037,23.5512101 31.3032725,22.9324742 31.3032725,22.1919399 L31.3032725,22.1919399 L31.3032725,16.1196128 C31.3032725,15.3796198 31.0548037,14.7611546 30.5578662,14.2634051 L30.5578662,14.2634051 C30.0595754,13.7661969 29.4411102,13.5174575 28.7011172,13.5174575 L28.7011172,13.5174575 L21.7962304,13.5174575 Z M25.6826821,17.2301436 C25.4279881,17.4848376 25.3010471,17.7852736 25.3010471,18.1322637 L25.3010471,18.1322637 C25.3010471,18.4792539 25.4336721,18.7861858 25.6997339,19.0517064 L25.6997339,19.0517064 C25.9657958,19.3177682 26.2721864,19.4506638 26.6191765,19.4506638 L26.6191765,19.4506638 C26.9664373,19.4506638 27.2666027,19.3239935 27.5212967,19.0692995 L27.5212967,19.0692995 C27.77572,18.8148761 27.9029317,18.5022602 27.9029317,18.1322637 L27.9029317,18.1322637 C27.9029317,17.7625379 27.7816746,17.4556059 27.5386191,17.2128211 L27.5386191,17.2128211 C27.296105,16.9700363 26.989173,16.8482379 26.6191765,16.8482379 L26.6191765,16.8482379 C26.2489094,16.8482379 25.9368348,16.9759909 25.6826821,17.2301436 L25.6826821,17.2301436 Z"
          id="path-5"
        />
        <linearGradient
          x1="49.9996813%"
          y1="-2.68823656e-05%"
          x2="49.9996813%"
          y2="99.9999597%"
          id="linearGradient-7"
        >
          <stop stopColor="#466272" offset="0%" />
          <stop stopColor="#8AA3BD" offset="100%" />
        </linearGradient>
      </defs>
      <g
        id="V3_24_2-—-tom"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <g
          id="220228_IDAustria_Landing_Desktop_V4_Hover_Link_1"
          transform="translate(-100.000000, -284.000000)"
        >
          <g id="Header-/-desktop" transform="translate(0.000000, 140.000000)">
            <g id="Group" transform="translate(100.000000, 144.000027)">
              <polygon
                id="Fill-1"
                fill="#FEFEFE"
                points="51.7104 31.1859299 55.3920444 31.1859299 55.3920444 9.71046468 51.7104 9.71046468"
              />
              <path
                d="M70.793606,27.1060201 C71.6309063,26.7183282 72.3472314,26.19061 72.9406763,25.5257231 C73.5341211,24.8617888 73.9884923,24.094978 74.3066473,23.2252907 C74.6228972,22.356556 74.7810222,21.4306677 74.7810222,20.4485783 C74.7810222,19.4664889 74.6228972,18.5358378 74.3066473,17.6566249 C73.9884923,16.7774121 73.5341211,16.0106013 72.9406763,15.3561925 C72.3472314,14.7017838 71.6309063,14.1797809 70.793606,13.7911365 C69.9544007,13.402492 69.0132714,13.2081697 67.9702182,13.2081697 L64.6572145,13.2081697 L64.6572145,27.6889869 L67.9702182,27.6889869 C69.0132714,27.6889869 69.9544007,27.4946646 70.793606,27.1060201 M72.2205409,10.5238557 C73.5188802,11.0658623 74.6333754,11.8126693 75.5640265,12.7633242 C76.4937251,13.7139791 77.2195758,14.8446678 77.7425312,16.1534853 C78.2635815,17.4623027 78.5236304,18.8940004 78.5236304,20.4485783 C78.5236304,22.0031562 78.2635815,23.4348539 77.7425312,24.7436713 C77.2195758,26.0534413 76.4937251,27.1822249 75.5640265,28.1338324 C74.6333754,29.0844873 73.5188802,29.8312943 72.2205409,30.3733009 C70.9202965,30.9153076 69.483836,31.1858346 67.9092543,31.1858346 L60.9755701,31.1858346 L60.9755701,9.71036942 L67.9092543,9.71036942 C69.483836,9.71036942 70.9202965,9.98184902 72.2205409,10.5238557"
                id="Fill-2"
                fill="#FEFEFE"
              />
              <path
                d="M103.434599,22.7191003 L100.213041,14.3432402 L96.9914827,22.7191003 L103.434599,22.7191003 Z M93.7089609,31.1864062 L89.9977872,31.1864062 L98.3412602,9.71094096 L102.084821,9.71094096 L110.429247,31.1864062 L106.71712,31.1864062 L104.753894,26.0940204 L95.6731396,26.0940204 L93.7089609,31.1864062 Z"
                id="Fill-4"
                fill="#FEFEFE"
              />
              <mask id="mask-2" fill="white">
                <use xlinkHref="#path-1" />
              </mask>
              <g id="Clip-7" />
              <path
                d="M116.365791,31.1860251 C115.72186,30.9402647 115.154135,30.5830547 114.663566,30.11249 C114.172045,29.6419254 113.777686,29.0694368 113.482392,28.3940718 C113.185193,27.7196593 113.037546,26.9423703 113.037546,26.0631575 L113.037546,16.6442442 L116.718238,16.6442442 L116.718238,25.0505862 C116.718238,26.1345995 116.963999,26.9176038 117.45552,27.3967415 C117.946088,27.8777843 118.661461,28.1178294 119.601637,28.1178294 C120.073155,28.1178294 120.492281,28.0263836 120.860922,27.841587 C121.227657,27.6586955 121.524856,27.4538951 121.74966,27.2290909 C122.036381,26.9623741 122.282141,26.6661279 122.486942,26.3384473 L122.486942,16.6442442 L126.167633,16.6442442 L126.167633,31.1860251 L122.73175,31.1860251 L122.73175,29.0694368 C122.445029,29.5400015 122.087819,29.9486497 121.658214,30.2963341 C121.309577,30.6240147 120.865684,30.9154981 120.322725,31.1707842 C119.781671,31.4260703 119.131073,31.5546659 118.37474,31.5546659 C117.680324,31.5546659 117.010674,31.4317856 116.365791,31.1860251"
                id="Fill-6"
                fill="#FEFEFE"
                mask="url(#mask-2)"
              />
              <path
                d="M131.597416,26.2474778 C131.985108,26.6351697 132.414712,26.9828541 132.88623,27.290531 C133.274874,27.5562953 133.734008,27.7963404 134.266489,28.0116189 C134.798018,28.2259449 135.371459,28.3326317 135.983955,28.3326317 C136.740287,28.3326317 137.390886,28.2259449 137.93194,28.0116189 C138.474899,27.7963404 138.745426,27.4638969 138.745426,27.0142886 C138.745426,26.543724 138.540626,26.2169959 138.131978,26.0321993 C137.723329,25.8483552 136.966044,25.6435548 135.862027,25.4187506 C135.145702,25.2549103 134.460811,25.0558253 133.806403,24.8205429 C133.151994,24.5852606 132.578553,24.2832991 132.088937,23.9156109 C131.597416,23.5469702 131.203056,23.1126029 130.90681,22.6115563 C130.610564,22.1105097 130.462917,21.5123021 130.462917,20.8169333 C130.462917,20.2444447 130.600086,19.6871971 130.876328,19.1442379 C131.15257,18.6031838 131.556456,18.1164257 132.088937,17.6877737 C132.619513,17.2572165 133.274874,16.9162001 134.05121,16.6599614 C134.829452,16.4046753 135.71819,16.2760797 136.721236,16.2760797 C137.559489,16.2760797 138.311059,16.3684781 138.975946,16.5523221 C139.63988,16.7361662 140.207606,16.9409666 140.67817,17.1657708 C141.250659,17.431535 141.751706,17.7287337 142.18131,18.0554618 L140.340964,20.7550169 C140.013284,20.5302127 139.655121,20.3158867 139.267429,20.1110863 C138.939748,19.947246 138.556819,19.804362 138.116737,19.6814817 C137.676654,19.5586015 137.211805,19.4976377 136.721236,19.4976377 C136.024915,19.4976377 135.411466,19.6157551 134.879938,19.8500849 C134.348409,20.0853672 134.081692,20.4282888 134.081692,20.8778971 C134.081692,21.328458 134.348409,21.6456605 134.879938,21.828552 C135.411466,22.0133486 136.291632,22.2381528 137.518529,22.503917 C138.233901,22.6477536 138.894026,22.8258823 139.496996,23.0411609 C140.100919,23.2554869 140.621969,23.5364921 141.062052,23.885129 C141.501182,24.2328134 141.844104,24.662418 142.089864,25.1729901 C142.334672,25.6845149 142.457552,26.3189198 142.457552,27.0752525 C142.457552,27.6896537 142.298475,28.2716678 141.982225,28.8241526 C141.665023,29.3756849 141.22494,29.8519649 140.663882,30.2501349 C140.100919,30.6492576 139.431269,30.9664601 138.653028,31.2017424 C137.876691,31.4360721 137.027008,31.5541896 136.106835,31.5541896 C135.043778,31.5541896 134.102649,31.4170209 133.2844,31.1407785 C132.466151,30.8645361 131.78126,30.5625746 131.228775,30.2358465 C130.574366,29.8462495 130.012356,29.4061668 129.541791,28.9165509 L131.597416,26.2474778 Z"
                id="Fill-9"
                fill="#FEFEFE"
              />
              <path
                d="M144.727693,16.6441489 L147.580611,16.6441489 L147.580611,12.318574 L151.261303,12.318574 L151.261303,16.6441489 L155.648794,16.6441489 L155.648794,19.9581052 L151.261303,19.9581052 L151.261303,25.8173017 C151.261303,26.6965146 151.455625,27.3004376 151.845222,27.6281183 C152.232914,27.9557989 152.672996,28.1177341 153.163565,28.1177341 C153.389322,28.1177341 153.588407,28.0929676 153.761773,28.0415293 C153.936091,27.9910436 154.094216,27.9243644 154.2371,27.8414917 C154.40094,27.7805279 154.554302,27.6995603 154.698139,27.5976364 L156.047917,30.6039157 C155.761196,30.7877598 155.443993,30.9401694 155.097262,31.0630496 C154.789585,31.1859299 154.421897,31.298332 153.992292,31.4012084 C153.562687,31.5031324 153.082597,31.5545706 152.550116,31.5545706 C151.854747,31.5545706 151.200339,31.4316904 150.58689,31.1859299 C149.973441,30.9401694 149.446676,30.5934376 149.006593,30.1428767 C148.56651,29.6942209 148.219778,29.1512617 147.96354,28.5168568 C147.708254,27.8834044 147.580611,27.1775574 147.580611,26.4002684 L147.580611,19.9581052 L144.727693,19.9581052 L144.727693,16.6441489 Z"
                id="Fill-11"
                fill="#FEFEFE"
              />
              <path
                d="M167.690581,16.3530466 C167.986827,16.4044848 168.247829,16.4502077 168.472633,16.4911678 C168.738397,16.5730879 168.993683,16.6550081 169.239444,16.7359757 L167.95063,20.0499319 C167.807746,19.9880155 167.654384,19.9270517 167.490544,19.8660879 C167.326703,19.8251278 167.142859,19.7889305 166.939011,19.7584486 C166.734211,19.7270141 166.48845,19.7117731 166.202682,19.7117731 C165.650198,19.7117731 165.170107,19.8146496 164.760507,20.01945 C164.350906,20.2232979 164.0137,20.448102 163.747935,20.6938625 C163.421207,20.9805831 163.15449,21.3082637 162.94969,21.6759519 L162.94969,31.1863109 L159.268998,31.1863109 L159.268998,16.6445299 L162.765846,16.6445299 L162.765846,18.7611183 C163.03161,18.2905536 163.359291,17.8714272 163.747935,17.5037391 C164.074663,17.1960622 164.484264,16.9160095 164.974833,16.6597709 C165.466354,16.4044848 166.048368,16.2758892 166.723733,16.2758892 C167.071417,16.2758892 167.393382,16.3016083 167.690581,16.3530466"
                id="Fill-13"
                fill="#FEFEFE"
              />
              <path
                d="M172.337836,31.1859299 L176.01948,31.1859299 L176.01948,16.6441489 L172.337836,16.6441489 L172.337836,31.1859299 Z M172.460716,10.2324676 C172.93128,9.76190292 173.504722,9.5266206 174.179134,9.5266206 C174.854499,9.5266206 175.426035,9.76190292 175.897552,10.2324676 C176.368117,10.7030322 176.603399,11.2755208 176.603399,11.9499332 C176.603399,12.6252983 176.368117,13.1977868 175.897552,13.6683515 C175.426035,14.1389161 174.854499,14.3741984 174.179134,14.3741984 C173.504722,14.3741984 172.93128,14.1389161 172.460716,13.6683515 C171.990151,13.1977868 171.755821,12.6252983 171.755821,11.9499332 C171.755821,11.2755208 171.990151,10.7030322 172.460716,10.2324676 L172.460716,10.2324676 Z"
                id="Fill-15"
                fill="#FEFEFE"
              />
              <path
                d="M188.061744,28.2260402 C188.479917,28.0517217 188.833317,27.8526367 189.119085,27.6278325 C189.446766,27.382072 189.733486,27.1058296 189.979247,26.7991053 L189.979247,25.2959656 C189.71253,25.1530816 189.405806,25.031154 189.058121,24.9273249 C188.771401,24.8263536 188.429432,24.738718 188.031262,24.667276 C187.632139,24.5948815 187.175863,24.5596368 186.665291,24.5596368 C186.37857,24.5596368 186.077561,24.5948815 185.760359,24.667276 C185.443156,24.738718 185.151673,24.8511201 184.885908,25.0044823 C184.620144,25.1578444 184.400103,25.3674076 184.226737,25.6331719 C184.052418,25.8998887 183.965736,26.2266168 183.965736,26.6152612 C183.965736,27.0039057 184.046703,27.3163454 184.211496,27.5506752 C184.374384,27.7859575 184.58871,27.9755169 184.855427,28.1184009 C185.121191,28.2612849 185.407911,28.3593986 185.714636,28.4098843 C186.020407,28.4613225 186.317606,28.4860891 186.604327,28.4860891 C187.156812,28.4860891 187.642617,28.4003587 188.061744,28.2260402 M191.927232,17.7488328 C193.082687,18.7299696 193.659939,20.0502177 193.659939,21.7067196 L193.659939,31.1856441 L190.255489,31.1856441 L190.255489,29.3452982 C189.947812,29.754899 189.559168,30.1225872 189.088603,30.4502678 C188.699959,30.7369884 188.20939,30.9922744 187.615945,31.2170786 C187.022501,31.4418828 186.307128,31.5542848 185.468875,31.5542848 C184.79351,31.5542848 184.139101,31.4475981 183.505649,31.2323196 C182.871244,31.0179936 182.309234,30.7007911 181.817713,30.2807121 C181.328097,29.8625383 180.9385,29.3510136 180.652732,28.7470905 C180.366011,28.14412 180.223127,27.453514 180.223127,26.6762251 C180.223127,25.9818088 180.370774,25.3369257 180.66702,24.7434808 C180.964219,24.1509885 181.37382,23.6394638 181.89487,23.2098592 C182.41592,22.7802547 183.019843,22.4487638 183.704734,22.2134815 C184.390577,21.9772466 185.131669,21.8591292 185.928962,21.8591292 C186.604327,21.8591292 187.192056,21.91152 187.693103,22.0134439 C188.194149,22.1163204 188.618039,22.238248 188.965723,22.381132 C189.354367,22.5249686 189.692526,22.6983345 189.979247,22.9031349 L189.979247,22.0744077 C189.979247,21.6047956 189.87637,21.205673 189.67157,20.8779924 C189.467722,20.5512643 189.211483,20.2902628 188.905712,20.0959406 C188.598035,19.9016184 188.260829,19.7634972 187.892188,19.681577 C187.5245,19.5996568 187.175863,19.5586968 186.850087,19.5586968 C186.317606,19.5586968 185.79084,19.6148978 185.268838,19.7272999 C184.747787,19.839702 184.281985,19.9578194 183.87429,20.0806996 C183.382769,20.24454 182.92173,20.428384 182.493078,20.6322319 L181.296662,17.5954706 C181.828191,17.3497101 182.401632,17.124906 183.014128,16.9201056 C183.546609,16.7562652 184.159105,16.6086184 184.855427,16.47526 C185.549843,16.3428542 186.276646,16.276175 187.033931,16.276175 C189.140041,16.276175 190.771777,16.7667434 191.927232,17.7488328"
                id="Fill-17"
                fill="#FEFEFE"
              />
              <mask id="mask-4" fill="white">
                <use xlinkHref="#path-3" />
              </mask>
              <g id="Clip-2" />
              <path
                d="M8.37810622,-2.7066312e-05 L30.2901804,-2.7066312e-05 C34.9174371,-2.7066312e-05 38.6685573,3.75109311 38.6685573,8.37807915 L38.6685573,30.290424 C38.6685573,34.9174101 34.9174371,38.6685302 30.2901804,38.6685302 L8.37810622,38.6685302 C3.75084952,38.6685302 0,34.9174101 0,30.290424 L0,8.37807915 C0,3.75109311 3.75084952,-2.7066312e-05 8.37810622,-2.7066312e-05"
                id="Fill-1"
                fill="#FFFFFF"
                mask="url(#mask-4)"
              />
              <mask id="mask-6" fill="white">
                <use xlinkHref="#path-5" />
              </mask>
              <g id="Clip-5" />
              <path
                d="M21.7962304,13.5174575 C21.6108262,13.5174575 21.4321885,13.5640116 21.2584228,13.6563077 L21.2584228,13.6563077 C21.0849277,13.7491452 20.9514908,13.8763568 20.8594653,14.0379427 L20.8594653,14.0379427 L19.7838501,15.9117435 L16.3491351,15.9117435 L16.3491351,17.4729284 L20.0612798,17.4729284 C20.2464134,17.4729284 20.4261337,17.4269156 20.5996287,17.3343489 L20.5996287,17.3343489 C20.7725825,17.2420527 20.9054781,17.1145704 20.9983155,16.9524432 L20.9983155,16.9524432 L22.0736601,15.0789131 L28.4239582,15.0789131 C28.7934134,15.0789131 29.1054879,15.2061247 29.3601819,15.4605481 L29.3601819,15.4605481 C29.6146053,15.7152421 29.7420876,16.0275873 29.7420876,16.3973131 L29.7420876,16.3973131 L29.7420876,21.9145102 C29.7420876,22.2845067 29.6146053,22.5971226 29.3601819,22.8510046 L29.3601819,22.8510046 C29.1054879,23.1056986 28.7934134,23.2326396 28.4239582,23.2326396 L28.4239582,23.2326396 L22.1085756,23.2326396 L20.2004006,19.9365041 C20.1075632,19.7749182 19.9749383,19.6471652 19.8011725,19.5548691 L19.8011725,19.5548691 C19.6279481,19.462573 19.4482278,19.4157483 19.2636356,19.4157483 L19.2636356,19.4157483 L16.3491351,19.4157483 L16.3491351,20.9772038 L18.9856646,20.9772038 L20.8594653,24.238965 C20.9514908,24.4010923 21.0849277,24.5337172 21.2584228,24.6379225 L21.2584228,24.6379225 C21.4321885,24.7421278 21.6108262,24.7940951 21.7962304,24.7940951 L21.7962304,24.7940951 L28.7011172,24.7940951 C29.4411102,24.7940951 30.0595754,24.545897 30.5578662,24.0481475 L30.5578662,24.0481475 C31.0548037,23.5512101 31.3032725,22.9324742 31.3032725,22.1919399 L31.3032725,22.1919399 L31.3032725,16.1196128 C31.3032725,15.3796198 31.0548037,14.7611546 30.5578662,14.2634051 L30.5578662,14.2634051 C30.0595754,13.7661969 29.4411102,13.5174575 28.7011172,13.5174575 L28.7011172,13.5174575 L21.7962304,13.5174575 Z M25.6826821,17.2301436 C25.4279881,17.4848376 25.3010471,17.7852736 25.3010471,18.1322637 L25.3010471,18.1322637 C25.3010471,18.4792539 25.4336721,18.7861858 25.6997339,19.0517064 L25.6997339,19.0517064 C25.9657958,19.3177682 26.2721864,19.4506638 26.6191765,19.4506638 L26.6191765,19.4506638 C26.9664373,19.4506638 27.2666027,19.3239935 27.5212967,19.0692995 L27.5212967,19.0692995 C27.77572,18.8148761 27.9029317,18.5022602 27.9029317,18.1322637 L27.9029317,18.1322637 C27.9029317,17.7625379 27.7816746,17.4556059 27.5386191,17.2128211 L27.5386191,17.2128211 C27.296105,16.9700363 26.989173,16.8482379 26.6191765,16.8482379 L26.6191765,16.8482379 C26.2489094,16.8482379 25.9368348,16.9759909 25.6826821,17.2301436 L25.6826821,17.2301436 Z"
                id="Fill-4"
                fill="url(#linearGradient-7)"
                mask="url(#mask-6)"
              />
              <polygon
                id="Fill-7"
                fill="#E6320F"
                points="14.6139409 17.5770795 14.6139409 15.8421289 6.84157875 15.8421289 7.67440917 17.5770795"
              />
              <polygon
                id="Fill-9"
                fill="#E6320F"
                points="14.6139409 21.0467913 14.6139409 19.3118407 8.50723959 19.3118407 9.33979935 21.0467913"
              />
            </g>
          </g>
        </g>
      </g>
    </Svg>
  ),
  comment: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M21.99 4c0-1.1-.89-2-1.99-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4z" />
    </Svg>
  ),
  comments: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M17,12V3A1,1 0 0,0 16,2H3A1,1 0 0,0 2,3V17L6,13H16A1,1 0 0,0 17,12M21,6H19V15H6V17A1,1 0 0,0 7,18H18L22,22V7A1,1 0 0,0 21,6Z" />
    </Svg>
  ),
  'info-outline': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z" />
    </Svg>
  ),
  'calendar-range': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M9,10H7V12H9V10M13,10H11V12H13V10M17,10H15V12H17V10M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z" />
    </Svg>
  ),
  calendar: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z" />
    </Svg>
  ),
  community_monitor: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 14 12"
      {...props}
    >
      <path
        className="cl-icon-primary"
        d="M0.333252 2.00008C0.333252 1.63341 0.463808 1.31953 0.724919 1.05841C0.98603 0.797304 1.29992 0.666748 1.66659 0.666748H12.3333C12.6999 0.666748 13.0138 0.797304 13.2749 1.05841C13.536 1.31953 13.6666 1.63341 13.6666 2.00008V3.33341C13.6666 3.5223 13.6027 3.68064 13.4749 3.80841C13.3471 3.93619 13.1888 4.00008 12.9999 4.00008C12.811 4.00008 12.6527 3.93619 12.5249 3.80841C12.3971 3.68064 12.3333 3.5223 12.3333 3.33341V2.00008H1.66659V3.33341C1.66659 3.5223 1.6027 3.68064 1.47492 3.80841C1.34714 3.93619 1.18881 4.00008 0.999919 4.00008C0.81103 4.00008 0.652696 3.93619 0.524919 3.80841C0.397141 3.68064 0.333252 3.5223 0.333252 3.33341V2.00008ZM1.66659 11.3334C1.29992 11.3334 0.98603 11.2029 0.724919 10.9417C0.463808 10.6806 0.333252 10.3667 0.333252 10.0001V8.66675C0.333252 8.47786 0.397141 8.31953 0.524919 8.19175C0.652696 8.06397 0.81103 8.00008 0.999919 8.00008C1.18881 8.00008 1.34714 8.06397 1.47492 8.19175C1.6027 8.31953 1.66659 8.47786 1.66659 8.66675V10.0001H12.3333V8.66675C12.3333 8.47786 12.3971 8.31953 12.5249 8.19175C12.6527 8.06397 12.811 8.00008 12.9999 8.00008C13.1888 8.00008 13.3471 8.06397 13.4749 8.19175C13.6027 8.31953 13.6666 8.47786 13.6666 8.66675V10.0001C13.6666 10.3667 13.536 10.6806 13.2749 10.9417C13.0138 11.2029 12.6999 11.3334 12.3333 11.3334H1.66659ZM5.66659 9.33342C5.78881 9.33342 5.90547 9.30286 6.01659 9.24175C6.1277 9.18064 6.21103 9.08897 6.26659 8.96675L8.33325 4.83342L9.06659 6.30008C9.12214 6.4223 9.20547 6.51397 9.31659 6.57508C9.4277 6.63619 9.54436 6.66675 9.66659 6.66675H12.9999C13.1888 6.66675 13.3471 6.60286 13.4749 6.47508C13.6027 6.3473 13.6666 6.18897 13.6666 6.00008C13.6666 5.81119 13.6027 5.65286 13.4749 5.52508C13.3471 5.3973 13.1888 5.33342 12.9999 5.33342H10.0833L8.93325 3.03341C8.8777 2.91119 8.79436 2.82508 8.68325 2.77508C8.57214 2.72508 8.45547 2.70008 8.33325 2.70008C8.21103 2.70008 8.09436 2.72508 7.98325 2.77508C7.87214 2.82508 7.78881 2.91119 7.73325 3.03341L5.66659 7.16675L4.93325 5.70008C4.8777 5.57786 4.79436 5.48619 4.68325 5.42508C4.57214 5.36397 4.45547 5.33342 4.33325 5.33342H0.999919C0.81103 5.33342 0.652696 5.3973 0.524919 5.52508C0.397141 5.65286 0.333252 5.81119 0.333252 6.00008C0.333252 6.18897 0.397141 6.3473 0.524919 6.47508C0.652696 6.60286 0.81103 6.66675 0.999919 6.66675H3.91659L5.06659 8.96675C5.12214 9.08897 5.20547 9.18064 5.31659 9.24175C5.4277 9.30286 5.54436 9.33342 5.66659 9.33342Z"
      />
    </Svg>
  ),
  power: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M16.56,5.44L15.11,6.89C16.84,7.94 18,9.83 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12C6,9.83 7.16,7.94 8.88,6.88L7.44,5.44C5.36,6.88 4,9.28 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12C20,9.28 18.64,6.88 16.56,5.44M13,3H11V13H13" />
    </Svg>
  ),
  'trend-up': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 14 8"
      {...props}
    >
      <path d="M0.79991 7.75013C0.666577 7.6168 0.602688 7.45846 0.608243 7.27513C0.613799 7.0918 0.677688 6.93902 0.79991 6.8168L4.31658 3.25013C4.57213 2.99457 4.8888 2.8668 5.26658 2.8668C5.64435 2.8668 5.96102 2.99457 6.21658 3.25013L7.93324 4.98346L11.3999 1.55013H10.3332C10.1444 1.55013 9.98602 1.48624 9.85824 1.35846C9.73047 1.23069 9.66658 1.07235 9.66658 0.883464C9.66658 0.694575 9.73047 0.536241 9.85824 0.408464C9.98602 0.280686 10.1444 0.216797 10.3332 0.216797H12.9999C13.1888 0.216797 13.3471 0.280686 13.4749 0.408464C13.6027 0.536241 13.6666 0.694575 13.6666 0.883464V3.55013C13.6666 3.73902 13.6027 3.89735 13.4749 4.02513C13.3471 4.15291 13.1888 4.2168 12.9999 4.2168C12.811 4.2168 12.6527 4.15291 12.5249 4.02513C12.3971 3.89735 12.3332 3.73902 12.3332 3.55013V2.48346L8.88324 5.93346C8.62769 6.18902 8.31102 6.3168 7.93324 6.3168C7.55547 6.3168 7.2388 6.18902 6.98324 5.93346L5.26658 4.2168L1.73324 7.75013C1.61102 7.87235 1.45547 7.93346 1.26658 7.93346C1.07769 7.93346 0.922132 7.87235 0.79991 7.75013Z" />
    </Svg>
  ),
  'trend-down': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 14 8"
      {...props}
    >
      <path d="M11.3999 6.66654L7.93324 3.2332L6.21658 4.94987C5.96102 5.20543 5.64435 5.3332 5.26658 5.3332C4.8888 5.3332 4.57213 5.20543 4.31658 4.94987L0.79991 1.39987C0.677688 1.27765 0.613799 1.12487 0.608243 0.941536C0.602688 0.758203 0.666577 0.59987 0.79991 0.466536C0.922132 0.344314 1.07769 0.283203 1.26658 0.283203C1.45547 0.283203 1.61102 0.344314 1.73324 0.466536L5.26658 3.99987L6.98324 2.2832C7.2388 2.02765 7.55547 1.89987 7.93324 1.89987C8.31102 1.89987 8.62769 2.02765 8.88324 2.2832L12.3332 5.7332V4.66654C12.3332 4.47765 12.3971 4.31931 12.5249 4.19154C12.6527 4.06376 12.811 3.99987 12.9999 3.99987C13.1888 3.99987 13.3471 4.06376 13.4749 4.19154C13.6027 4.31931 13.6666 4.47765 13.6666 4.66654V7.3332C13.6666 7.52209 13.6027 7.68043 13.4749 7.8082C13.3471 7.93598 13.1888 7.99987 12.9999 7.99987H10.3332C10.1444 7.99987 9.98602 7.93598 9.85824 7.8082C9.73047 7.68043 9.66658 7.52209 9.66658 7.3332C9.66658 7.14431 9.73047 6.98598 9.85824 6.8582C9.98602 6.73043 10.1444 6.66654 10.3332 6.66654H11.3999Z" />
    </Svg>
  ),
  'shield-checkered': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12 22q-3.475-.875-5.737-3.988Q4 14.9 4 11.1V5l8-3 8 3v6.1q0 3.8-2.262 6.912Q15.475 21.125 12 22Zm0-2.1q2.425-.75 4.05-2.962 1.625-2.213 1.9-4.938H12V4.125l-6 2.25v5.175q0 .175.05.45H12Z" />
    </Svg>
  ),
  'sidebar-settings': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      width="25px"
      height="26px"
      viewBox="0 0 25 26"
      {...props}
    >
      <g className="Settings-icon">
        <path
          className="cl-icon-primary Gear-Big-icon"
          d="M16.9145 10.4054C16.9362 10.1898 16.9481 9.96992 16.9481 9.75C16.9481 9.52683 16.9351 9.30475 16.9134 9.08158L18.637 7.72958C19.0183 7.40783 19.1104 6.86725 18.8613 6.45233L17.0153 3.26192C16.7672 2.85025 16.2797 2.65525 15.7824 2.8405L13.775 3.64758C13.4143 3.39083 13.0383 3.17092 12.6375 2.98242L12.3331 0.821167C12.2464 0.35425 11.8196 0 11.3418 0H7.65851C7.17751 0 6.75067 0.361833 6.66184 0.860167L6.36176 2.99975C5.95659 3.19042 5.57634 3.4125 5.22426 3.66167L3.20492 2.84917C2.75534 2.68233 2.23426 2.86 1.98076 3.2825L0.143424 6.45883C-0.110076 6.88133 -0.0169095 7.42083 0.378507 7.75558L2.08584 9.0935C2.06526 9.31017 2.05226 9.53008 2.05226 9.75C2.05226 9.97317 2.06526 10.1952 2.08692 10.4184L0.36334 11.7704C-0.0179929 12.0922 -0.110076 12.6328 0.139091 13.0477L1.98509 16.2381C2.23317 16.6508 2.72176 16.848 3.21792 16.6595L5.22534 15.8524C5.58609 16.1092 5.96201 16.3291 6.36284 16.5176L6.66726 18.6788C6.75501 19.1458 7.18076 19.5 7.65851 19.5H11.3418C11.8228 19.5 12.2497 19.1382 12.3385 18.6398L12.6386 16.5013C13.0438 16.3096 13.424 16.0875 13.775 15.8383L15.7943 16.6508C15.9081 16.6942 16.0283 16.7158 16.1508 16.7158C16.5072 16.7158 16.83 16.5328 17.0196 16.2186L18.8569 13.0422C19.1104 12.6197 19.0173 12.0802 18.6218 11.7455L16.9145 10.4054ZM9.50017 11.9167C8.30526 11.9167 7.33351 10.9449 7.33351 9.75C7.33351 8.55508 8.30526 7.58333 9.50017 7.58333C10.6951 7.58333 11.6668 8.55508 11.6668 9.75C11.6668 10.9449 10.6951 11.9167 9.50017 11.9167Z"
          transform="translate(0 6.5)"
        />
        <path
          className="cl-icon-accent Gear-Small-icon"
          d="M10.2882 6.27575L9.48332 5.65175C9.48981 5.57483 9.49307 5.49683 9.49307 5.41667C9.49307 5.33542 9.4909 5.25633 9.48657 5.17725L10.3066 4.53375C10.5861 4.28458 10.6576 3.88267 10.4735 3.54683L9.50498 1.872C9.36957 1.62283 9.10523 1.46792 8.81707 1.46792C8.74773 1.46792 8.58956 1.49608 8.52456 1.52208L7.59073 1.898C7.45531 1.80808 7.3199 1.729 7.18231 1.66075L7.04365 0.703083C7.01223 0.30875 6.67206 0 6.27015 0H4.32015C3.93881 0 3.60515 0.28275 3.54015 0.6695L3.39715 1.66725C3.25415 1.73983 3.11981 1.81675 2.99198 1.89908L2.03865 1.51558C1.97906 1.49392 1.83173 1.46792 1.76781 1.46792C1.49481 1.46792 1.2424 1.60658 1.08098 1.86008L0.100564 3.55767C-0.0868525 3.9 -0.00776914 4.30842 0.299898 4.55542L1.10265 5.18375C1.0994 5.26067 1.09723 5.33758 1.09723 5.41667C1.09723 5.49575 1.0994 5.57267 1.10265 5.6485L0.274981 6.29742C-0.00560221 6.54658 -0.0781853 6.9485 0.107065 7.2865L1.07556 8.96133C1.21098 9.2105 1.47531 9.36542 1.76348 9.36542C1.83281 9.36542 1.99098 9.33725 2.05598 9.31125L2.98982 8.93533C3.12523 9.02525 3.26065 9.10433 3.39823 9.17258L3.54232 10.1757C3.60623 10.5571 3.9334 10.8333 4.32015 10.8333H6.27015C6.65148 10.8333 6.98515 10.5506 7.05015 10.1638L7.19315 9.165C7.33506 9.0935 7.46831 9.0155 7.5929 8.93533L8.55056 9.31883C8.61015 9.3405 8.75748 9.3665 8.8214 9.3665C9.0944 9.3665 9.34682 9.22783 9.50823 8.97433L10.4897 7.27458C10.6674 6.94742 10.5948 6.54658 10.2882 6.27575ZM5.29515 6.5C4.69823 6.5 4.21182 6.01358 4.21182 5.41667C4.21182 4.81975 4.69823 4.33333 5.29515 4.33333C5.89207 4.33333 6.37848 4.81975 6.37848 5.41667C6.37848 6.01358 5.89315 6.5 5.29515 6.5Z"
          transform="translate(13.9551)"
        />
      </g>
    </Svg>
  ),
  send: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z" />
    </Svg>
  ),
  group: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M16 17V19H2V17S2 13 9 13 16 17 16 17M12.5 7.5A3.5 3.5 0 1 0 9 11A3.5 3.5 0 0 0 12.5 7.5M15.94 13A5.32 5.32 0 0 1 18 17V19H22V17S22 13.37 15.94 13M15 4A3.39 3.39 0 0 0 13.07 4.59A5 5 0 0 1 13.07 10.41A3.39 3.39 0 0 0 15 11A3.5 3.5 0 0 0 15 4Z" />
    </Svg>
  ),
  building: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M11.5,1L2,6V8H21V6M16,10V17H19V10M2,22H21V19H2M10,10V17H13V10M4,10V17H7V10H4Z" />
    </Svg>
  ),
  position: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5M12,2A7,7 0 0,0 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9A7,7 0 0,0 12,2Z" />
    </Svg>
  ),
  mention: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12,15C12.81,15 13.5,14.7 14.11,14.11C14.7,13.5 15,12.81 15,12C15,11.19 14.7,10.5 14.11,9.89C13.5,9.3 12.81,9 12,9C11.19,9 10.5,9.3 9.89,9.89C9.3,10.5 9,11.19 9,12C9,12.81 9.3,13.5 9.89,14.11C10.5,14.7 11.19,15 12,15M12,2C14.75,2 17.1,3 19.05,4.95C21,6.9 22,9.25 22,12V13.45C22,14.45 21.65,15.3 21,16C20.3,16.67 19.5,17 18.5,17C17.3,17 16.31,16.5 15.56,15.5C14.56,16.5 13.38,17 12,17C10.63,17 9.45,16.5 8.46,15.54C7.5,14.55 7,13.38 7,12C7,10.63 7.5,9.45 8.46,8.46C9.45,7.5 10.63,7 12,7C13.38,7 14.55,7.5 15.54,8.46C16.5,9.45 17,10.63 17,12V13.45C17,13.86 17.16,14.22 17.46,14.53C17.76,14.84 18.11,15 18.5,15C18.92,15 19.27,14.84 19.57,14.53C19.87,14.22 20,13.86 20,13.45V12C20,9.81 19.23,7.93 17.65,6.35C16.07,4.77 14.19,4 12,4C9.81,4 7.93,4.77 6.35,6.35C4.77,7.93 4,9.81 4,12C4,14.19 4.77,16.07 6.35,17.65C7.93,19.23 9.81,20 12,20H17V22H12C9.25,22 6.9,21 4.95,19.05C3,17.1 2,14.75 2,12C2,9.25 3,6.9 4.95,4.95C6.9,3 9.25,2 12,2Z" />
    </Svg>
  ),
  'dots-horizontal': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M16,12A2,2 0 0,1 18,10A2,2 0 0,1 20,12A2,2 0 0,1 18,14A2,2 0 0,1 16,12M10,12A2,2 0 0,1 12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12M4,12A2,2 0 0,1 6,10A2,2 0 0,1 8,12A2,2 0 0,1 6,14A2,2 0 0,1 4,12Z" />
    </Svg>
  ),
  map: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M15,19L9,16.89V5L15,7.11M20.5,3C20.44,3 20.39,3 20.34,3L15,5.1L9,3L3.36,4.9C3.15,4.97 3,5.15 3,5.38V20.5A0.5,0.5 0 0,0 3.5,21C3.55,21 3.61,21 3.66,20.97L9,18.9L15,21L20.64,19.1C20.85,19 21,18.85 21,18.62V3.5A0.5,0.5 0 0,0 20.5,3Z" />
    </Svg>
  ),
  gps: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8M3.05,13H1V11H3.05C3.5,6.83 6.83,3.5 11,3.05V1H13V3.05C17.17,3.5 20.5,6.83 20.95,11H23V13H20.95C20.5,17.17 17.17,20.5 13,20.95V23H11V20.95C6.83,20.5 3.5,17.17 3.05,13M12,5A7,7 0 0,0 5,12A7,7 0 0,0 12,19A7,7 0 0,0 19,12A7,7 0 0,0 12,5Z" />
    </Svg>
  ),
  'location-simple': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 36 48"
      {...props}
    >
      <path d="M16.2 47C2.5 27.3 0 25.3 0 18a18 18 0 1136 0c0 7.3-2.5 9.3-16.2 29a2.3 2.3 0 01-3.6 0z" />
    </Svg>
  ),
  timeline: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M3,14L3.5,14.07L8.07,9.5C7.89,8.85 8.06,8.11 8.59,7.59C9.37,6.8 10.63,6.8 11.41,7.59C11.94,8.11 12.11,8.85 11.93,9.5L14.5,12.07L15,12C15.18,12 15.35,12 15.5,12.07L19.07,8.5C19,8.35 19,8.18 19,8A2,2 0 0,1 21,6A2,2 0 0,1 23,8A2,2 0 0,1 21,10C20.82,10 20.65,10 20.5,9.93L16.93,13.5C17,13.65 17,13.82 17,14A2,2 0 0,1 15,16A2,2 0 0,1 13,14L13.07,13.5L10.5,10.93C10.18,11 9.82,11 9.5,10.93L4.93,15.5L5,16A2,2 0 0,1 3,18A2,2 0 0,1 1,16A2,2 0 0,1 3,14Z" />
    </Svg>
  ),
  survey: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 30 30"
      {...props}
    >
      <path
        d="M15 1C14.3416 1.00402 13.6958 1.17679 13.128 1.50089C12.5629 1.82261 12.0963 2.28491 11.776 2.84022H3V29H27V2.82622H18.224C17.904 2.274 17.4352 1.81511 16.8688 1.49311C16.3011 1.17226 15.6566 1.0022 15 1ZM13.68 5.29489L13.8976 4.32578C13.954 4.08387 14.0941 3.86809 14.2944 3.71444C14.4445 3.59981 14.6218 3.52368 14.8101 3.49304C14.9983 3.46241 15.1915 3.47824 15.3718 3.5391C15.5521 3.59996 15.7138 3.70389 15.842 3.84135C15.9702 3.97881 16.0609 4.14538 16.1056 4.32578L16.3232 5.29489H18.1184V6.38378H11.8848V5.27933L13.68 5.29489ZM24.4448 26.5453H5.5552V5.28089H9.3296V8.82444H20.6704V5.27778H24.4448V26.5453Z"
        fill={props.fill}
      />
      <path
        d="M7.1712 17.0953H13.4528V11.0644H7.1712V17.0953ZM9.728 13.5176H10.896V14.6422H9.728V13.5176Z"
        fill={props.fill}
      />
      <path
        d="M22.8272 12.8533H14.7536V15.3064H22.8272V12.8533Z"
        fill={props.fill}
      />
      <path
        d="M7.1712 24.4578H13.4528V18.4269H7.1712V24.4593V24.4578ZM9.728 20.8816H10.896V22.0047H9.728V20.8816Z"
        fill={props.fill}
      />
      <path
        d="M22.8272 20.2173H14.7536V22.6704H22.8272V20.2173Z"
        fill={props.fill}
      />
    </Svg>
  ),
  download: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" />
    </Svg>
  ),
  'user-check': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M21.1,12.5L22.5,13.91L15.97,20.5L12.5,17L13.9,15.59L15.97,17.67L21.1,12.5M10,17L13,20H3V18C3,15.79 6.58,14 11,14L12.89,14.11L10,17M11,4A4,4 0 0,1 15,8A4,4 0 0,1 11,12A4,4 0 0,1 7,8A4,4 0 0,1 11,4Z" />
    </Svg>
  ),
  label: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M17.63,5.84C17.27,5.33 16.67,5 16,5H5A2,2 0 0,0 3,7V17A2,2 0 0,0 5,19H16C16.67,19 17.27,18.66 17.63,18.15L22,12L17.63,5.84Z" />
    </Svg>
  ),
  'arrow-left': (props: IconPropsWithoutName) => (
    <MirrorOnRtlSvg viewBox="0 0 24 24" {...props}>
      <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" />
    </MirrorOnRtlSvg>
  ),

  'shield-check': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1Z" />
    </Svg>
  ),
  'sidebar-pages-menu': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 53 70"
      {...props}
    >
      <g className="Blank-Page-icon">
        <path
          className="Fillin-Paper-icon"
          d="M8.5 7.75L17 0H51V66H0V17.5L8.5 7.75Z"
          transform="translate(2 1)"
          fill="rgba(1, 161, 177, 0.2)"
        />
        <g className="Stroke-Paper-icon">
          <path
            d="M51.5278 70H1.47222C0.659556 70 0 69.3467 0 68.5417V18.9583C0 18.5704 0.156055 18.2 0.429889 17.9258L18.0966 0.425833C18.3733 0.154583 18.7473 0 19.1389 0H51.5278C52.3404 0 53 0.653333 53 1.45833V68.5417C53 69.3467 52.3404 70 51.5278 70ZM2.94444 67.0833H50.0556V2.91667H19.7484L2.94444 19.5621V67.0833Z"
            fill="#01A1B1"
          />
          <path
            d="M19.1389 20.4167H1.47222C0.659556 20.4167 0 19.7633 0 18.9583C0 18.1533 0.659556 17.5 1.47222 17.5H17.6667V1.45833C17.6667 0.653333 18.3262 0 19.1389 0C19.9516 0 20.6111 0.653333 20.6111 1.45833V18.9583C20.6111 19.7633 19.9516 20.4167 19.1389 20.4167Z"
            fill="#01A1B1"
          />
        </g>
      </g>
    </Svg>
  ),
  'email-2': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 60 40"
      {...props}
    >
      <path
        d="M53.75 0H6.25A6.259 6.259 0 0 0 0 6.25v27.5A6.259 6.259 0 0 0 6.25 40h47.5A6.259 6.259 0 0 0 60 33.75V6.25A6.259 6.259 0 0 0 53.75 0zm-31.7 22.21l-15 12.5c-.232.195-.518.29-.8.29a1.252 1.252 0 0 1-.803-2.212l15-12.5c.53-.44 1.32-.368 1.763.16.442.532.37 1.322-.16 1.762zm7.95.332a6.98 6.98 0 0 1-3.953-1.232L5.546 7.283a1.252 1.252 0 0 1-.327-1.738 1.246 1.246 0 0 1 1.737-.325l20.502 14.027a4.476 4.476 0 0 0 5.083 0L53.042 5.22a1.25 1.25 0 0 1 1.413 2.063L33.953 21.31A6.98 6.98 0 0 1 30 22.543zM54.71 34.55a1.244 1.244 0 0 1-1.76.16l-15-12.5a1.252 1.252 0 0 1-.16-1.762c.443-.528 1.23-.6 1.763-.16l15 12.5c.527.442.6 1.232.157 1.762z"
        fill="#01A1B1"
        fillOpacity=".25"
      />
    </Svg>
  ),
  'minus-circle': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M17,13H7V11H17M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
    </Svg>
  ),
  'sidebar-guide': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      width="23px"
      height="23px"
      viewBox="0 0 23 23"
      {...props}
    >
      <path
        d="M11.5 0C5.159 0 0 5.159 0 11.5C0 17.841 5.159 23 11.5 23C17.841 23 23 17.841 23 11.5C23 5.159 17.841 0 11.5 0ZM11.5 19C10.949 19 10.5 18.552 10.5 18C10.5 17.448 10.949 17 11.5 17C12.051 17 12.5 17.448 12.5 18C12.5 18.552 12.051 19 11.5 19ZM15 9.258C15 10.18 14.626 11.081 13.975 11.733L12.733 12.975C12.267 13.439 12 14.084 12 14.742V15.5C12 15.776 11.776 16 11.5 16C11.224 16 11 15.776 11 15.5V14.742C11 13.82 11.374 12.919 12.025 12.267L13.267 11.025C13.733 10.561 14 9.916 14 9.258V8.5C14 7.121 12.878 6 11.5 6C10.122 6 9 7.121 9 8.5V9.5C9 9.776 8.776 10 8.5 10C8.224 10 8 9.776 8 9.5V8.5C8 6.57 9.57 5 11.5 5C13.43 5 15 6.57 15 8.5V9.258Z"
        fill="#32B67A"
      />
    </Svg>
  ),
  paperclip: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M16.5,6V17.5A4,4 0 0,1 12.5,21.5A4,4 0 0,1 8.5,17.5V5A2.5,2.5 0 0,1 11,2.5A2.5,2.5 0 0,1 13.5,5V15.5A1,1 0 0,1 12.5,16.5A1,1 0 0,1 11.5,15.5V6H10V15.5A2.5,2.5 0 0,0 12.5,18A2.5,2.5 0 0,0 15,15.5V5A4,4 0 0,0 11,1A4,4 0 0,0 7,5V17.5A5.5,5.5 0 0,0 12.5,23A5.5,5.5 0 0,0 18,17.5V6H16.5Z" />
    </Svg>
  ),
  code: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M14.6,16.6L19.2,12L14.6,7.4L16,6L22,12L16,18L14.6,16.6M9.4,16.6L4.8,12L9.4,7.4L8,6L2,12L8,18L9.4,16.6Z" />
    </Svg>
  ),
  'question-bubble': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 17.391 20"
      {...props}
    >
      <path d="M15.459 0H1.932C.865 0 0 .853 0 1.905v13.333c0 1.053.865 1.904 1.932 1.904h3.865L8.696 20l2.898-2.857h3.865c1.067 0 1.932-.852 1.932-1.904V1.905C17.391.853 16.526 0 15.459 0zM9.662 15.238H7.729v-1.904h1.933v1.904zm1.995-7.376l-.864.876c-.696.686-1.131 1.262-1.131 2.691H7.729v-.477c0-1.052.435-2.004 1.13-2.695l1.203-1.2c.348-.343.565-.819.565-1.343 0-1.052-.864-1.905-1.932-1.905s-1.932.853-1.932 1.905H4.831c0-2.105 1.729-3.81 3.865-3.81s3.865 1.705 3.865 3.81c0 .838-.344 1.596-.904 2.148z" />
    </Svg>
  ),
  'question-circle': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z" />
    </Svg>
  ),
  refresh: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
    </Svg>
  ),
  translate: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12.87,15.07L10.33,12.56L10.36,12.53C12.1,10.59 13.34,8.36 14.07,6H17V4H10V2H8V4H1V6H12.17C11.5,7.92 10.44,9.75 9,11.35C8.07,10.32 7.3,9.19 6.69,8H4.69C5.42,9.63 6.42,11.17 7.67,12.56L2.58,17.58L4,19L9,14L12.11,17.11L12.87,15.07M18.5,10H16.5L12,22H14L15.12,19H19.87L21,22H23L18.5,10M15.88,17L17.5,12.67L19.12,17H15.88Z" />
    </Svg>
  ),
  share: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M21,12L14,5V9C7,10 4,15 3,20C5.5,16.5 9,14.9 14,14.9V19L21,12Z" />
    </Svg>
  ),
  flash: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M7,2V13H10V22L17,10H13L17,2H7Z" />
    </Svg>
  ),
  stars: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M19 9l1.25-2.75L23 5l-2.75-1.25L19 1l-1.25 2.75L15 5l2.75 1.25L19 9zm-7.5.5L9 4 6.5 9.5 1 12l5.5 2.5L9 20l2.5-5.5L17 12l-5.5-2.5zM19 15l-1.25 2.75L15 19l2.75 1.25L19 23l1.25-2.75L23 19l-2.75-1.25L19 15z" />
    </Svg>
  ),
  database: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12 11q-3.75 0-6.375-1.175T3 7q0-1.65 2.625-2.825Q8.25 3 12 3t6.375 1.175Q21 5.35 21 7q0 1.65-2.625 2.825Q15.75 11 12 11Zm0 5q-3.75 0-6.375-1.175T3 12V9.5q0 1.1 1.025 1.863 1.025.762 2.45 1.237 1.425.475 2.963.687 1.537.213 2.562.213t2.562-.213q1.538-.212 2.963-.687 1.425-.475 2.45-1.237Q21 10.6 21 9.5V12q0 1.65-2.625 2.825Q15.75 16 12 16Zm0 5q-3.75 0-6.375-1.175T3 17v-2.5q0 1.1 1.025 1.863 1.025.762 2.45 1.237 1.425.475 2.963.688 1.537.212 2.562.212t2.562-.212q1.538-.213 2.963-.688t2.45-1.237Q21 15.6 21 14.5V17q0 1.65-2.625 2.825Q15.75 21 12 21Z" />
    </Svg>
  ),
  'folder-move': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M14,18V15H10V11H14V8L19,13M20,6H12L10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6Z" />
    </Svg>
  ),
  'user-data': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M11 9C11 10.66 9.66 12 8 12C6.34 12 5 10.66 5 9C5 7.34 6.34 6 8 6C9.66 6 11 7.34 11 9M14 20H2V18C2 15.79 4.69 14 8 14C11.31 14 14 15.79 14 18M22 12V14H13V12M22 8V10H13V8M22 4V6H13V4Z" />
    </Svg>
  ),
  settings: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" />
    </Svg>
  ),
  initiatives: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 26 19"
      {...props}
    >
      <path d="M1.183 4.073c.193 1.168.374 2.27 1.019 2.916a3.382 3.382 0 0 0 4.216.455c1.545 2.163 3.555 8.324 4.248 10.697a.664.664 0 0 0 .639.477h.015c.301-.006.56-.216.63-.509.008-.025.335-1.333 1.323-3.285 1.053.74 2.269 1.13 3.492 1.128a6.057 6.057 0 0 0 4.297-1.78c1.138-1.14 1.484-3.242 1.85-5.467.395-2.399.803-4.88 2.16-6.24a.665.665 0 1 0-.47-1.136c-4.99 0-9.542 1.574-12.175 4.207-1.52 1.52-2.12 3.77-1.563 5.876a.664.664 0 0 0 1.213.173 29.822 29.822 0 0 1 4.466-5.682.665.665 0 0 1 .94.94c-.51.511-.975 1.018-1.415 1.52-.042.05-.082.099-.125.148-2.492 2.883-3.886 5.567-4.602 7.301-.865-2.702-2.485-7.342-3.964-9.302a3.384 3.384 0 0 0-.39-4.309C5.59.801 3.286 0 .667 0a.665.665 0 0 0-.471 1.136c.605.603.799 1.791.988 2.937z" />
    </Svg>
  ),
  'sidebar-folder': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 26 20"
      width="26px"
      height="20px"
      {...props}
    >
      <g className="Folder-icon">
        <path
          className="cl-icon-primary Cover-icon"
          d="M23.2917 3.15789H10.7694C10.0696 3.15789 9.44992 2.72421 9.22783 2.07895L9.1325 1.79895C8.76308 0.723158 7.73067 0 6.56392 0H2.70833C1.2155 0 0 1.18105 0 2.63158V15.2632C0 16.7137 1.2155 17.8947 2.70833 17.8947H23.2917C24.7845 17.8947 26 16.7137 26 15.2632V5.78947C26 4.33895 24.7845 3.15789 23.2917 3.15789Z"
          transform="translate(0 2.10526)"
        />
        <path
          className="cl-icon-accent Back-Cover-icon"
          d="M16.1861 0H0.541667C0.242667 0 1.65304e-08 0.235789 1.65304e-08 0.526316C1.65304e-08 0.816842 0.242667 1.05263 0.541667 1.05263C2.17533 1.05263 3.62158 2.06526 4.13833 3.57053L4.23367 3.85158C4.30733 4.06737 4.51425 4.21263 4.74825 4.21263L17.2694 4.21053C17.5576 4.21053 17.8577 4.25263 18.2163 4.34316C18.2618 4.35474 18.3073 4.36 18.3528 4.36C18.4708 4.36 18.5878 4.32211 18.6842 4.25053C18.8164 4.15053 18.8944 3.99684 18.8944 3.83368V2.63158C18.8944 1.18105 17.6789 0 16.1861 0Z"
          transform="translate(6.02246)"
        />
      </g>
    </Svg>
  ),
  'folder-add': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M14 16h2v-2h2v-2h-2v-2h-2v2h-2v2h2ZM4 20q-.825 0-1.412-.587Q2 18.825 2 18V6q0-.825.588-1.412Q3.175 4 4 4h6l2 2h8q.825 0 1.413.588Q22 7.175 22 8v10q0 .825-.587 1.413Q20.825 20 20 20Z" />
    </Svg>
  ),
  'sidebar-activity': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 45 45"
      width="45px"
      height="45px"
      {...props}
    >
      <defs>
        <clipPath id="a" clipPathUnits="userSpaceOnUse">
          <path d="M198.958 198.958h1375v1375h-1375z" clipRule="evenodd" />
        </clipPath>
      </defs>
      <g>
        <path
          className="cl-icon-primary"
          d="M14.025 9.317a.471.471 0 00-.443.31l-5.65 15.537-.028.161v9.417c0 .52.421.941.942.941h27.308c.52 0 .942-.421.942-.941v-9.418l-.028-.16-5.65-15.538a.471.471 0 00-.443-.31zm.33.941h16.29l5.308 14.596h-8.745c-.26 0-.47.211-.47.471v2.825h-8.476v-2.825c0-.26-.21-.47-.47-.47H9.047z"
        />
        <path
          className="cl-icon-accent"
          d="M27.208 14.025a.47.47 0 00-.333.138l-6.258 6.259-2.492-2.492a.47.47 0 10-.666.665l2.825 2.825a.47.47 0 00.666 0l6.591-6.591a.47.47 0 00-.333-.804z"
        />
      </g>
    </Svg>
  ),
  'sidebar-workshops': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 43 43"
      width="43px"
      height="43px"
      {...props}
    >
      <path
        className="cl-icon-accent"
        d="M28.6 13.3l-4.5 3.1v-3.1h-9.6V22.9h9.6v-2.8l4.5 2.8z"
      />
      <path
        className="cl-icon-primary"
        d="M35.7 7.5V5.6H7.5v26.2H28l2.3 1.7 5.3 4.1v-9l.1-21.1zm-25 21.1V8.8h21.8v19.8H10.7z"
      />
    </Svg>
  ),
  'sidebar-users': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      width="28px"
      height="28px"
      viewBox="0 0 28 28"
      {...props}
    >
      <g className="Users-icon">
        <path
          className="cl-icon-primary Body-icon"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M22.9087 1.0619L19.9068 0.0315792C19.6875 -0.0433321 19.4437 0.0177481 19.2862 0.18601C17.815 1.77183 15.9378 2.64541 14 2.64541C12.0633 2.64541 10.1862 1.77183 8.715 0.18601C8.5575 0.0177481 8.31367 -0.0433321 8.09317 0.0315792L5.0925 1.0619C2.04633 2.10835 0 4.95152 0 8.13813V8.98406C0 9.30214 0.261333 9.5603 0.583333 9.5603H27.4167C27.7398 9.5603 28 9.30214 28 8.98406V8.13813C28 4.95152 25.9548 2.10835 22.9087 1.0619Z"
          transform="translate(0 18.4397)"
        />
        <path
          className="cl-icon-accent Face-Top-icon"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.16667 0C3.6645 0 0 3.61879 0 8.06737C0 8.60212 0.0676669 9.1507 0.211167 9.79494C0.2695 10.0589 0.506334 10.2467 0.7805 10.2467C1.44083 10.2467 2.23883 10.3643 2.247 10.3654C2.27967 10.3712 2.31 10.3735 2.345 10.3723C2.66467 10.3769 2.94 10.1188 2.94 9.79609C2.94 9.72579 2.92833 9.6601 2.905 9.59787C2.83383 8.44769 2.90733 7.28484 3.0695 6.91489H3.15817C5.1765 6.91489 6.181 6.28333 6.64533 5.48466C7.903 6.7109 10.4323 7.89335 12.8777 8.05008C13.0165 9.01817 13.4458 9.98164 13.4668 10.03C13.5613 10.2398 13.7713 10.3723 14 10.3723C14.0163 10.3723 14.0315 10.3723 14.0478 10.3712L15.6018 10.2456C15.8573 10.2248 16.0685 10.0416 16.1233 9.79494C16.2668 9.1507 16.3333 8.60212 16.3333 8.06737C16.3333 3.61879 12.67 0 8.16667 0Z"
          transform="translate(5.83325)"
        />
        <path
          className="cl-icon-secondary Face-Bottom-icon"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.16667 10.498C5.29433 10.498 2.74167 8.20461 1.68233 4.73449C0.718667 4.67917 0 3.45984 0 1.85444C0 1.33697 0.0793335 0.832184 0.2275 0.394241C0.329 0.0922908 0.659167 -0.0713615 0.964834 0.0300569C1.2705 0.130323 1.435 0.457629 1.3335 0.758426C1.225 1.08112 1.16667 1.46029 1.16667 1.85444C1.16667 2.97004 1.60533 3.58316 1.75 3.58316C1.90867 3.48635 2.12333 3.45523 2.29717 3.52553C2.46983 3.59468 2.61683 3.72722 2.66233 3.90701C3.47783 7.16046 5.691 9.34557 8.16667 9.34557C10.6435 9.34557 12.8567 7.16046 13.6722 3.90701C13.7177 3.72722 13.846 3.58085 14.0198 3.5117C14.1925 3.44256 14.3885 3.45869 14.5472 3.5555C14.7478 3.58201 15.1667 2.96888 15.1667 1.85444C15.1667 1.46029 15.1095 1.08112 15.001 0.758426C14.8983 0.457629 15.064 0.130323 15.3697 0.0300569C15.6753 -0.0713615 16.0055 0.0922908 16.107 0.394241C16.2552 0.832184 16.3333 1.33697 16.3333 1.85444C16.3333 3.45984 15.6158 4.67917 14.6522 4.73449C13.5928 8.20461 11.0402 10.498 8.16667 10.498Z"
          transform="translate(5.83325 9.21985)"
        />
      </g>
    </Svg>
  ),
  'sidebar-dashboards': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      width="27px"
      height="25px"
      viewBox="0 0 27 25"
      {...props}
    >
      <g className="Stats-icon">
        <path
          className="cl-icon-secondary Segment-2-icon"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.452157 0C0.202566 0 0 0.197891 0 0.449752V10.3443C0 10.5962 0.202566 10.7941 0.452157 10.7941H10.3996C10.6492 10.7941 10.8518 10.5962 10.8518 10.3443C10.8518 4.64144 6.18641 0 0.452157 0Z"
          transform="matrix(0.998753 -0.0499261 0.0513545 0.99868 13.2483 0.541794)"
        />
        <path
          className="cl-icon-accent Segment-3-icon"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.9495 0H0.568901C0.350394 0 0.150095 0.115402 0.0556365 0.29375C-0.0388223 0.482589 -0.0115086 0.692411 0.126197 0.849777L7.52358 9.29509C7.62259 9.41049 7.76826 9.48393 7.92645 9.49442H7.96628C8.10968 9.49442 8.24852 9.44197 8.35436 9.34755C11.0003 7.07098 12.5185 3.86071 12.5185 0.524554C12.5185 0.230804 12.2636 0 11.9495 0Z"
          transform="translate(14.4814 12.6067)"
        />
        <g
          className="cl-icon-primary Segment-1-icon"
          transform="translate(1 2.01292)"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M17.0821 19.9883C15.1739 21.5493 13.7169 21.9871 11.0531 21.9871C4.94878 21.9871 0 17.065 0 10.9935C0 4.92211 4.55288 0 10.0483 0V10.9935L17.0821 19.9883Z"
          />
          <path
            d="M17.0821 19.9883C15.1739 21.5493 13.7169 21.9871 11.0531 21.9871C4.94878 21.9871 0 17.065 0 10.9935C0 4.92211 4.55288 0 10.0483 0V10.9935L17.0821 19.9883Z"
            strokeWidth="1.1"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
      </g>
    </Svg>
  ),
  'chart-bar': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M4 20V9h4v11Zm6 0V4h4v16Zm6 0v-7h4v7Z" />
    </Svg>
  ),
  'sidebar-invitations': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      width="25px"
      height="25px"
      viewBox="0 0 25 25"
      {...props}
    >
      <path
        className="cl-icon-accent Invitations-plus-icon"
        d="M11.1897 15.8621L19.8276 9.69224V6.22155V5.60345V0.431034C19.8276 0.316379 19.7819 0.206896 19.7017 0.125862C19.6216 0.0448275 19.5112 0 19.3966 0L0.431035 0.0137929C0.193103 0.0137929 0 0.206896 0 0.444827V5.60345V6.22241V9.69138L8.63793 15.8431C9.40172 15.425 10.4172 15.431 11.1897 15.8621ZM6.03448 7.32759C6.03448 7.08965 6.22759 6.89655 6.46552 6.89655H9.48276V3.87931C9.48276 3.64138 9.67586 3.44828 9.91379 3.44828C10.1517 3.44828 10.3448 3.64138 10.3448 3.87931V6.89655H13.3621C13.6 6.89655 13.7931 7.08965 13.7931 7.32759C13.7931 7.56552 13.6 7.75862 13.3621 7.75862H10.3448V10.7759C10.3448 11.0138 10.1517 11.2069 9.91379 11.2069C9.67586 11.2069 9.48276 11.0138 9.48276 10.7759V7.75862H6.46552C6.22759 7.75862 6.03448 7.56552 6.03448 7.32759Z"
        transform="translate(2.58618)"
      />
      <g className="cl-icon-primary Invitations-envelope-icon">
        <path
          d="M1.90517 3.52328C1.79138 3.44224 1.72414 3.31207 1.72414 3.17241V-2.63083e-07L0.586207 0.686207C0.225 0.987069 0 1.44397 0 1.87931V16.75C0 17.0112 0.0732759 17.2543 0.190517 17.469L10.4802 9.63103L1.90517 3.52328Z"
          transform="translate(0 6.74138)"
        />
        <path
          d="M10.6569 0.315486L-1.31541e-08 8.43273C0.209483 8.54393 0.444828 8.61204 0.697414 8.61204H22.6802C22.9328 8.61204 23.1681 8.54393 23.3776 8.43359L12.7216 0.334453C12.1457 -0.10434 11.2198 -0.1121 10.6569 0.315486Z"
          transform="translate(0.811035 16.388)"
        />
        <path
          d="M9.83965 0.65L8.75603 3.3543e-07V3.175C8.75603 3.31466 8.68879 3.44483 8.57586 3.52586L3.55161e-07 9.65172L10.2897 17.4716C10.4069 17.2569 10.4802 17.0138 10.4802 16.7526V1.8819C10.4802 1.44655 10.2552 0.989655 9.83965 0.65Z"
          transform="translate(14.5198 6.73879)"
        />
      </g>
    </Svg>
  ),
  'sidebar-messaging': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      width="32px"
      height="17px"
      viewBox="0 0 32 17"
      {...props}
    >
      <path
        className="cl-icon-primary emails-icon"
        d="M26.3743 1.04615C26.0518 0.392308 25.4067 0 24.8691 0H7.02205C5.7319 0 4.54926 0.915384 4.01169 2.48461L0.14124 13.8615C-0.0737851 14.5154 -0.0737848 15.4308 0.356266 15.9538C0.786316 16.4769 1.32388 17 1.86144 17H19.8161C21.1062 17 22.2888 16.0846 22.8264 14.5154L26.3743 3.13846C26.8044 2.48461 26.6969 1.7 26.3743 1.04615ZM10.4625 10.4615L3.47413 14.3846C3.36662 14.3846 3.36662 14.3846 3.25911 14.3846C3.04408 14.3846 2.82906 14.2538 2.72154 13.9923C2.61403 13.6 2.72154 13.3385 2.93657 13.0769L9.92489 9.15385C10.2474 9.02308 10.4625 9.15385 10.6775 9.41538C10.8925 9.80769 10.785 10.2 10.4625 10.4615ZM19.8161 14.1231C19.7085 14.2538 19.601 14.3846 19.386 14.3846C19.2785 14.3846 19.171 14.3846 19.0635 14.2538L15.3005 10.3308C15.0855 10.0692 15.0855 9.67692 15.193 9.41538C15.408 9.15385 15.7306 9.15385 15.9456 9.28462L19.7085 13.2077C20.0311 13.4692 20.0311 13.8615 19.8161 14.1231ZM22.3964 3.79231L15.408 8.63077C14.763 9.02308 14.1179 9.15385 13.4728 9.15385C12.5052 9.15385 11.5376 8.76154 10.6775 7.97692L6.80702 3.79231C6.592 3.53077 6.592 3.13846 6.80702 2.87692C7.02205 2.61538 7.34459 2.61538 7.55961 2.87692L11.4301 7.06154C12.3977 7.97692 13.7953 8.23846 14.978 7.58462L21.9663 2.87692C22.1813 2.74615 22.5039 2.87692 22.7189 3.13846C22.7189 3.26923 22.6114 3.66154 22.3964 3.79231Z"
        transform="translate(5.34204)"
      />
      <path
        className="cl-icon-accent emails-icon"
        d="M5.91319 1.30769H0.537563C0.215025 1.30769 0 1.04615 0 0.653846C0 0.261538 0.215025 0 0.537563 0H5.91319C6.23573 0 6.45076 0.261538 6.45076 0.653846C6.45076 1.04615 6.23573 1.30769 5.91319 1.30769Z"
        transform="translate(0 1.30774)"
      />
      <path
        className="cl-icon-accent emails-icon"
        d="M1.93523 1.30769H0.537563C0.215025 1.30769 0 1.04615 0 0.653846C0 0.261538 0.215025 0 0.537563 0H1.93523C2.25777 0 2.47279 0.261538 2.47279 0.653846C2.47279 1.04615 2.25777 1.30769 1.93523 1.30769Z"
        transform="translate(0 14.3846)"
      />
      <path
        className="cl-icon-accent emails-icon"
        d="M3.22538 1.30769H0.537563C0.215025 1.30769 0 1.04615 0 0.653846C0 0.261538 0.215025 0 0.537563 0H3.22538C3.54792 0 3.76294 0.261538 3.76294 0.653846C3.76294 1.04615 3.54792 1.30769 3.22538 1.30769Z"
        transform="translate(0 7.84619)"
      />
    </Svg>
  ),
  'sidebar-academy': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      width="25px"
      height="23px"
      viewBox="0 0 25 23"
      fill="none"
      {...props}
    >
      <path
        d="M12.5357 22.2223C12.5214 22.2223 12.5071 22.2223 12.4928 22.2223C6.82895 22.2011 4.76591 18.2966 4.67995 18.1325C4.59877 17.9739 4.55579 17.7886 4.55579 17.6088L4.57967 11.4768C4.58444 10.8949 5.01424 10.4187 5.53955 10.424C6.06486 10.4293 6.49466 10.9002 6.48988 11.4875L6.46601 17.2914C6.65226 17.5664 7.05338 18.085 7.7029 18.5928C8.98269 19.5928 10.5969 20.1007 12.4928 20.1113C14.3552 20.1166 15.936 19.6139 17.1871 18.614C17.8318 18.0955 18.2234 17.5718 18.4049 17.2966L18.4287 11.4768C18.4335 10.8949 18.8633 10.4187 19.3887 10.424C19.9139 10.4293 20.3437 10.9002 20.339 11.4875L20.3151 17.6193C20.3151 17.7939 20.2721 17.9686 20.1957 18.122C20.1193 18.286 18.1088 22.2223 12.5357 22.2223Z"
        fill="#01A1B1"
      />
      <path
        d="M12.469 14.6724C12.3591 14.6724 12.254 14.6512 12.149 14.6089L0.635147 10.0747C0.253103 9.92659 0 9.52451 0 9.07478L0.00955097 5.90038C0.00955097 5.31841 0.439351 4.84753 0.96466 4.84753C0.96466 4.84753 0.96466 4.84753 0.969437 4.84753C1.49474 4.84753 1.92455 5.32369 1.91977 5.91096L1.91022 8.33409L12.469 12.4926L23.0898 8.31826V5.91625C23.0898 5.33428 23.5196 4.85812 24.0449 4.85812C24.5702 4.85812 25 5.33428 25 5.91625V9.06423C25 9.51395 24.7469 9.91069 24.3649 10.0589L12.7889 14.6089C12.6839 14.6512 12.5788 14.6724 12.469 14.6724Z"
        fill="#01A1B1"
      />
      <path
        d="M12.469 11.1806C12.3591 11.1806 12.254 11.1594 12.149 11.1171L0.635147 6.58294C0.253103 6.4348 0 6.0327 0 5.58828C0 5.14387 0.253103 4.74178 0.635147 4.59364L12.149 0.0595202C12.3544 -0.0198401 12.5788 -0.0198401 12.7889 0.0595202L24.3649 4.59364C24.7469 4.74178 25 5.14387 25 5.59358C25 6.04328 24.7469 6.44009 24.3649 6.59352L12.7889 11.1224C12.6839 11.1594 12.5788 11.1806 12.469 11.1806ZM3.80611 5.58828L12.469 9.00076L21.1748 5.59358L12.469 2.18108L3.80611 5.58828Z"
        fill="#01A1B1"
      />
      <path
        d="M16.041 16.778C15.6446 16.778 15.3246 16.4236 15.3246 15.9844V8.16483L12.1824 6.18609C11.8385 5.96918 11.7192 5.48243 11.9149 5.1015C12.1107 4.72057 12.5501 4.58831 12.8939 4.80523L16.3992 7.01671C16.6236 7.15955 16.7621 7.41886 16.7621 7.70455V15.9898C16.7574 16.4236 16.4326 16.778 16.041 16.778Z"
        fill="#80CFD8"
      />
      <path
        d="M16.041 18.9578C15.3008 18.9578 14.7039 18.2964 14.7039 17.4764V15.5928C14.7039 14.7728 15.3008 14.1115 16.041 14.1115C16.7812 14.1115 17.3782 14.7728 17.3782 15.5928V17.4764C17.3782 18.2964 16.7764 18.9578 16.041 18.9578Z"
        fill="#80CFD8"
      />
    </Svg>
  ),
  email: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M22 6C22 4.9 21.1 4 20 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6M20 6L12 11L4 6H20M20 18H4V8L12 13L20 8V18Z" />
    </Svg>
  ),
  'money-bag': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 19 24"
      {...props}
    >
      <path d="M10 17.133v1.803c.575-.141 1-.511 1-.936 0-.397-.333-.676-1-.867zM8 15c0 .397.334.674 1 .867v-1.803c-.575.141-1 .512-1 .936z" />
      <g>
        <path d="M6.14 6h6.72c1.265-1.128 3.196-3.049 3.312-4.365.032-.375-.07-.709-.297-.966-.142-.16-.406-.351-.867-.351-1.006 0-2.604.972-3.375 1.484C11.062.992 10.217 0 9.5 0c-.719 0-1.563.992-2.134 1.803C6.594 1.29 4.996.319 3.991.319c-.46 0-.726.191-.867.351-.227.256-.329.59-.296.965C2.942 2.95 4.875 4.872 6.14 6zM12.658 7H6.343C4.434 9.065 0 14.114 0 16.5 0 20.636 4.262 24 9.5 24s9.5-3.364 9.5-7.5c0-2.387-4.434-7.436-6.342-9.5zM10 19.959v.541a.5.5 0 0 1-1 0v-.539c-1.094-.171-1.921-.898-1.998-1.822a.5.5 0 1 1 .996-.084c.034.405.451.75 1.002.883v-2.039c-1.345-.331-2-.963-2-1.899 0-.966.86-1.773 2-1.959V12.5a.5.5 0 0 1 1 0v.539c1.094.171 1.92.896 1.998 1.822a.501.501 0 0 1-.456.541c-.267.035-.517-.182-.54-.457-.034-.405-.451-.748-1.002-.883v2.039c1.346.33 2 .961 2 1.899 0 .966-.859 1.773-2 1.959z" />
      </g>
    </Svg>
  ),
  home: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z" />
    </Svg>
  ),
  'info-solid': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
    </Svg>
  ),
  dot: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12 19q-2.925 0-4.962-2.038Q5 14.925 5 12t2.038-4.963Q9.075 5 12 5t4.962 2.037Q19 9.075 19 12q0 2.925-2.038 4.962Q14.925 19 12 19Z" />
    </Svg>
  ),
  pen: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      width="16px"
      height="17px"
      viewBox="0 0 16 17"
      {...props}
    >
      <path d="M13.44 6.0667L10.9334 3.56004L10.3467 2.97337C10.24 2.8667 10.08 2.8667 9.97337 2.97337L5.44003 7.5067C5.33337 7.61337 5.33337 7.77337 5.44003 7.88004C5.49337 7.93337 5.5467 7.93337 5.65337 7.93337C5.76003 7.93337 5.81337 7.93337 5.8667 7.88004L10.1334 3.5067L10.56 3.93337L1.8667 12.6267L4.37337 15.1334C5.12003 14.3334 8.64003 10.8667 11.52 7.98671C12.0534 7.45337 12.5867 6.92004 13.0667 6.44004L13.44 6.81337C13.5467 6.92004 13.7067 6.92004 13.8134 6.81337C13.92 6.7067 13.92 6.54671 13.8134 6.44004L13.44 6.0667Z" />
      <path d="M15.36 1.48003C14.72 0.786698 13.6534 0.840031 12.8534 1.5867L11.3067 3.13336L13.8134 5.64003C14.4 5.05336 14.9334 4.52003 15.36 4.09336C15.7867 3.72003 16 3.1867 16 2.7067C15.9467 2.2267 15.7334 1.80003 15.36 1.48003Z" />
      <path d="M0.0533414 16.6267C8.0578e-06 16.7333 0.0533414 16.84 0.106675 16.9467C0.160008 16.9467 0.213341 17 0.266675 17C0.320008 17 0.320008 17 0.373341 17L3.94667 15.4533L1.54667 13.0533L0.0533414 16.6267Z" />
    </Svg>
  ),
  'cl-favicon': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 20.034"
      {...props}
    >
      <path fill="none" d="M-2-1.966h24v24H-2z" />
      <path d="M12.953 2.973H3.588v4.923c0 2.167.267 4.109.848 5.69.49 1.354 1.16 2.529 2.007 3.432.045.046.09.09.09.135.356.362.713.679 1.115.947.044.046.09.046.09.091 0 0 .044 0 .044.046 0 0 .045 0 .045.045.89.586 1.65.723 1.739.768l.267.044.267-.044c.09 0 .491-.091 1.117-.407.757-.361 1.471-.949 2.094-1.58.848-.905 1.517-2.08 2.008-3.433.579-1.626.846-3.522.846-5.69V5.636c.044-.27.044-.497.044-.677V2.973zM5.06 6.676V4.463h4.103V6.09L7.648 7.625H5.06zm4.102 10.567c-.133-.09-.312-.18-.446-.271h-.044s-.045 0-.045-.046c-.045 0-.045-.045-.09-.045a7.219 7.219 0 01-.89-.768l-.135-.134c-.713-.768-1.25-1.716-1.65-2.892-.4-1.127-.668-2.481-.758-3.972h2.542l1.516 1.536zm.715-7.947l-.893-.905.893-.902.89.902zm4.057 3.791c-.4 1.176-.98 2.124-1.693 2.892a6.249 6.249 0 01-1.65 1.264V10.65l1.514-1.536h2.587c-.09 1.49-.312 2.8-.758 3.972zm.758-8.533V7.58h-2.587L10.59 6.044v-1.58h4.102z" />
    </Svg>
  ),
  filter: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M11 21v-6h2v2h8v2h-8v2Zm-8-2v-2h6v2Zm4-4v-2H3v-2h4V9h2v6Zm4-2v-2h10v2Zm4-4V3h2v2h4v2h-4v2ZM3 7V5h10v2Z" />
    </Svg>
  ),
  clock: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
    </Svg>
  ),
  'clock-circle': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 14 14"
      {...props}
    >
      <path d="M7.665 6.733v-2.4a.645.645 0 0 0-.191-.475.645.645 0 0 0-.475-.191.645.645 0 0 0-.475.191.645.645 0 0 0-.192.475v2.65a.69.69 0 0 0 .2.484l2.2 2.2a.632.632 0 0 0 .467.183.632.632 0 0 0 .466-.183.632.632 0 0 0 .184-.467.632.632 0 0 0-.184-.467l-2-2ZM7 13.667a6.492 6.492 0 0 1-2.6-.525 6.732 6.732 0 0 1-2.117-1.425A6.732 6.732 0 0 1 .857 9.6 6.492 6.492 0 0 1 .332 7c0-.922.175-1.789.525-2.6a6.732 6.732 0 0 1 1.425-2.117c.6-.6 1.306-1.075 2.117-1.425a6.492 6.492 0 0 1 2.6-.525c.922 0 1.789.175 2.6.525.81.35 1.516.825 2.116 1.425.6.6 1.075 1.306 1.425 2.117.35.811.525 1.678.525 2.6 0 .922-.175 1.789-.525 2.6a6.732 6.732 0 0 1-1.425 2.117c-.6.6-1.305 1.075-2.116 1.425a6.492 6.492 0 0 1-2.6.525Z" />
    </Svg>
  ),
  bullseye: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12C22,10.84 21.79,9.69 21.39,8.61L19.79,10.21C19.93,10.8 20,11.4 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.6,4 13.2,4.07 13.79,4.21L15.4,2.6C14.31,2.21 13.16,2 12,2M19,2L15,6V7.5L12.45,10.05C12.3,10 12.15,10 12,10A2,2 0 0,0 10,12A2,2 0 0,0 12,14A2,2 0 0,0 14,12C14,11.85 14,11.7 13.95,11.55L16.5,9H18L22,5H19V2M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12H16A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8V6Z" />
    </Svg>
  ),
  'email-check': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 52 52"
      {...props}
    >
      <path
        d="M45.5 2.16663H6.50004C3.51221 2.16663 1.08337 4.59763 1.08337 7.58329V31.4166C1.08337 34.4023 3.51221 36.8333 6.50004 36.8333H11.0067C11.5462 36.8333 12.0034 36.4368 12.0792 35.9038C13.0694 29.0225 19.0537 23.8333 26 23.8333C32.9464 23.8333 38.9307 29.0225 39.9209 35.9038C39.9967 36.4368 40.6272 36.8333 41.1667 36.8333H45.5C48.4879 36.8333 50.9167 34.4023 50.9167 31.4166V7.58329C50.9167 4.59763 48.4879 2.16663 45.5 2.16663ZM18.1004 21.3503L7.26704 32.1836C7.05471 32.3938 6.77737 32.5 6.50004 32.5C6.22271 32.5 5.94537 32.3938 5.73304 32.1836C5.31054 31.7611 5.31054 31.0743 5.73304 30.6518L16.5664 19.8185C16.9889 19.396 17.6757 19.396 18.0982 19.8185C18.5207 20.241 18.5229 20.9256 18.1004 21.3503ZM26 21.6883C24.843 21.6883 23.6882 21.333 22.7002 20.6201L5.86521 8.46296C5.37987 8.11196 5.27154 7.43379 5.62254 6.94846C5.97354 6.46096 6.64954 6.35696 7.13487 6.70579L23.9699 18.863C25.2005 19.7535 26.8017 19.7535 28.0324 18.863L44.8674 6.70579C45.3527 6.35479 46.0287 6.46313 46.3797 6.94846C46.7307 7.43379 46.6202 8.11196 46.137 8.46079L29.302 20.618C28.3119 21.3308 27.157 21.6883 26 21.6883ZM46.267 32.1836C46.0547 32.3938 45.7774 32.5 45.5 32.5C45.2227 32.5 44.9454 32.3938 44.733 32.1836L33.8997 21.3503C33.4772 20.9278 33.4772 20.241 33.8997 19.8185C34.3222 19.396 35.009 19.396 35.4315 19.8185L46.2649 30.6518C46.6895 31.0743 46.6895 31.759 46.267 32.1836Z"
        fillOpacity="0.6"
      />
      <path d="M26 26C19.4307 26 14.0834 31.3452 14.0834 37.9167C14.0834 44.4882 19.4307 49.8333 26 49.8333C32.5694 49.8333 37.9167 44.4882 37.9167 37.9167C37.9167 31.3452 32.5694 26 26 26ZM32.1837 35.4337L24.6004 43.017C24.388 43.2272 24.1107 43.3333 23.8334 43.3333C23.556 43.3333 23.2787 43.2272 23.0664 43.017L18.733 38.6837C18.3105 38.2612 18.3105 37.5743 18.733 37.1518C19.1555 36.7293 19.8424 36.7293 20.2649 37.1518L23.8334 40.7182L30.6497 33.9018C31.0722 33.4793 31.759 33.4793 32.1815 33.9018C32.604 34.3243 32.6062 35.009 32.1837 35.4337Z" />
    </Svg>
  ),
  'check-circle': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M10,17L5,12L6.41,10.58L10,14.17L17.59,6.58L19,8M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
    </Svg>
  ),
  template: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 23 23"
      {...props}
    >
      <path d="M12.65 3.833h-2.3c-.843 0-1.533-.69-1.533-1.533S9.507.767 10.35.767h2.3c.843 0 1.533.69 1.533 1.533s-.69 1.533-1.533 1.533zm-2.3-2.3c-.46 0-.767.307-.767.767s.307.767.767.767h2.3c.46 0 .767-.307.767-.767s-.307-.767-.767-.767h-2.3zM21.237 13.033H1.763c-.766 0-1.38.537-1.38 1.15 0 .614.614 1.15 1.38 1.15h19.55c.767 0 1.38-.536 1.38-1.15 0-.613-.69-1.15-1.456-1.15z" />
      <path d="M1.533 12.267H21.39c.077 0 .153 0 .23-.077.077-.077.153-.153.153-.307v-9.2c.077-.23-.076-.383-.306-.383h-6.21c-.154 0-.384.153-.384.307C14.72 3.757 13.8 4.6 12.65 4.6h-2.3c-1.15 0-2.07-.843-2.3-1.993 0-.154-.153-.307-.383-.307H1.533c-.23 0-.383.153-.383.383v9.2c0 .077.077.23.153.307.077.077.154.077.23.077zM11.5 1.533c-.23 0-.383-.153-.383-.383V.383c0-.23.153-.383.383-.383s.383.153.383.383v.767c0 .23-.153.383-.383.383zM8.357 16.483L5.75 21.697c-.077.153 0 .383.153.536h.23c.154 0 .307-.076.307-.23l2.76-5.52h-.843zM11.117 16.483v6.134c0 .23.153.383.383.383s.383-.153.383-.383v-6.134h-.766zM14.797 16.483h-.844l2.607 5.52c.077.154.23.23.383.23h.154c.23-.076.306-.306.153-.536l-2.453-5.214z" />
    </Svg>
  ),
  'blank-paper': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 17 21"
      {...props}
    >
      <path d="M15.938 0H6.312c-.02 0-.036.009-.056.011a.385.385 0 0 0-.055.012.433.433 0 0 0-.198.106L.753 5.378a.43.43 0 0 0-.105.197.424.424 0 0 0-.012.056c-.002.02-.011.036-.011.056v14.875c0 .242.196.438.438.438h14.874a.438.438 0 0 0 .438-.438V.438A.438.438 0 0 0 15.937 0zM5.874 1.494v3.319a.438.438 0 0 1-.438.437H2.12l3.756-3.756z" />
    </Svg>
  ),
  list: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M9,5V9H21V5M9,19H21V15H9M9,14H21V10H9M4,9H8V5H4M4,19H8V15H4M4,14H8V10H4V14Z" />
    </Svg>
  ),
  menu: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z" />
    </Svg>
  ),
  link: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M3.9,12C3.9,10.29 5.29,8.9 7,8.9H11V7H7A5,5 0 0,0 2,12A5,5 0 0,0 7,17H11V15.1H7C5.29,15.1 3.9,13.71 3.9,12M8,13H16V11H8V13M17,7H13V8.9H17C18.71,8.9 20.1,10.29 20.1,12C20.1,13.71 18.71,15.1 17,15.1H13V17H17A5,5 0 0,0 22,12A5,5 0 0,0 17,7Z" />
    </Svg>
  ),
  'participation-level': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M23.5 10.125H22V1.94a.724.724 0 0 1 1 .685.5.5 0 0 0 1 0c0-.618-.315-1.177-.845-1.497a1.74 1.74 0 0 0-1.719-.05L2.312 11.163c-.008.003-.014.008-.022.01l-.423.224A3.494 3.494 0 0 0 0 14.493v.13a.5.5 0 0 0 1 0v-.13c0-.79.384-1.505 1-1.972v6.604H.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h23a.5.5 0 0 0 .5-.5v-12a.5.5 0 0 0-.5-.5zM3 11.93L21 2.44v7.685h-2.5a.5.5 0 0 0-.5.5v2.5h-5.5a.5.5 0 0 0-.5.5v2.5H6.5a.5.5 0 0 0-.5.5v2.5H3V11.93z" />
    </Svg>
  ),
  key: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M7 14C5.9 14 5 13.1 5 12S5.9 10 7 10 9 10.9 9 12 8.1 14 7 14M12.6 10C11.8 7.7 9.6 6 7 6C3.7 6 1 8.7 1 12S3.7 18 7 18C9.6 18 11.8 16.3 12.6 14H16V18H20V14H23V10H12.6Z" />
    </Svg>
  ),
  minus: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M19,13H5V11H19V13Z" />
    </Svg>
  ),
  inbox: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M19,15H15A3,3 0 0,1 12,18A3,3 0 0,1 9,15H5V5H19M19,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3Z" />
    </Svg>
  ),
  bookmark: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z" />
    </Svg>
  ),
  'bookmark-outline': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2zm0 15-5-2.18L7 18V5h10v13z" />
    </Svg>
  ),
  eye: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z" />
    </Svg>
  ),
  'eye-off': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.08L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.74,7.13 11.35,7 12,7Z" />
    </Svg>
  ),
  'open-in-new': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z" />
    </Svg>
  ),
  file: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M13,9V3.5L18.5,9M6,2C4.89,2 4,2.89 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2H6Z" />
    </Svg>
  ),
  'file-add': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M11 18h2v-3h3v-2h-3v-3h-2v3H8v2h3Zm-5 4q-.825 0-1.412-.587Q4 20.825 4 20V4q0-.825.588-1.413Q5.175 2 6 2h8l6 6v12q0 .825-.587 1.413Q18.825 22 18 22Zm7-13h5l-5-5Z" />
    </Svg>
  ),
  'folder-solid': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z" />
    </Svg>
  ),
  'folder-outline': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M20,18H4V8H20M20,6H12L10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6Z" />
    </Svg>
  ),
  flag: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M14.4,6L14,4H5V21H7V14H12.6L13,16H20V6H14.4Z" />
    </Svg>
  ),
  user: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" />
    </Svg>
  ),
  basket: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M5.5,21C4.72,21 4.04,20.55 3.71,19.9V19.9L1.1,10.44L1,10A1,1 0 0,1 2,9H6.58L11.18,2.43C11.36,2.17 11.66,2 12,2C12.34,2 12.65,2.17 12.83,2.44L17.42,9H22A1,1 0 0,1 23,10L22.96,10.29L20.29,19.9C19.96,20.55 19.28,21 18.5,21H5.5M12,4.74L9,9H15L12,4.74M12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17A2,2 0 0,0 14,15A2,2 0 0,0 12,13Z" />
    </Svg>
  ),
  'basket-plus': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M20 15V18H23V20H20V23H18V20H15V18H18V15H20M23 10L22.96 10.29L22 13.8C21.11 13.29 20.09 13 19 13C15.69 13 13 15.69 13 19C13 19.7 13.13 20.37 13.35 21H5.5C4.72 21 4.04 20.55 3.71 19.9L1.1 10.44L1 10C1 9.45 1.45 9 2 9H6.58L11.18 2.43C11.36 2.17 11.66 2 12 2S12.65 2.17 12.83 2.44L17.42 9H22C22.55 9 23 9.45 23 10M14 15C14 13.9 13.11 13 12 13S10 13.9 10 15 10.9 17 12 17 14 16.11 14 15M15 9L12 4.74L9 9H15Z" />
    </Svg>
  ),
  'basket-minus': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M23 18V20H15V18H23M23 10L22.96 10.29L22 13.8C21.11 13.29 20.09 13 19 13C15.69 13 13 15.69 13 19C13 19.7 13.13 20.37 13.35 21H5.5C4.72 21 4.04 20.55 3.71 19.9L1.1 10.44L1 10C1 9.45 1.45 9 2 9H6.58L11.18 2.43C11.36 2.17 11.66 2 12 2S12.65 2.17 12.83 2.44L17.42 9H22C22.55 9 23 9.45 23 10M14 15C14 13.9 13.11 13 12 13S10 13.9 10 15 10.9 17 12 17 14 16.11 14 15M15 9L12 4.74L9 9H15Z" />
    </Svg>
  ),
  'basket-checkmark': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M21.63 16.27L17.76 20.17L16.41 18.8L15 20.22L17.75 23L23.03 17.68L21.63 16.27M13 20C13 16.69 15.69 14 19 14C20 14 20.92 14.24 21.74 14.67L22.96 10.29L23 10C23 9.45 22.55 9 22 9H17.42L12.83 2.44C12.65 2.17 12.34 2 12 2S11.36 2.17 11.18 2.43L6.58 9H2C1.45 9 1 9.45 1 10L1.1 10.44L3.71 19.9C4.04 20.55 4.72 21 5.5 21H13.09C13.04 20.67 13 20.34 13 20M12 4.74L15 9H9L12 4.74M10 15C10 13.9 10.9 13 12 13S14 13.9 14 15 13.11 17 12 17 10 16.11 10 15Z" />
    </Svg>
  ),
  volunteer: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 28"
      {...props}
    >
      <path d="M21 6a2.74 2.74 0 00-1 .19V5a3 3 0 00-3-3 2.87 2.87 0 00-1.13.25 3 3 0 00-5.75 0A2.82 2.82 0 009 2a3 3 0 00-3 3v10.75l-.88-.87a3 3 0 00-4.25 0 3 3 0 000 4.25l6.78 6.78A6.74 6.74 0 0012.37 28H17a7 7 0 007-7V9a3 3 0 00-3-3zm1 15a5 5 0 01-5 5h-4.63a4.65 4.65 0 01-3.28-1.53l-6.81-6.75a1 1 0 010-1.44h.06a1 1 0 011.38 0l2.56 2.6L8 20.59V5a1 1 0 012 0v8h2V3a1 1 0 012 0v10h2V5a1 1 0 012 0v8h2V9a1 1 0 012 0z" />
      <path d="M17 16a2 2 0 00-2 2 2 2 0 00-4 0c0 1.95 4 5 4 5s4-2.83 4-5a2 2 0 00-2-2z" />
    </Svg>
  ),
  'volunteer-off': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 25.34 28"
      {...props}
    >
      <path d="M.001 3.804L1.465 2.34l23.872 23.872-1.464 1.464zM16.6 16.2l2.67 2.68a2.41 2.41 0 00.19-.88 2 2 0 00-2-2 2.05 2.05 0 00-.86.2zM15.46 23s.54-.38 1.21-1l-5-5a2 2 0 00-.24.93C11.46 20 15.46 23 15.46 23z" />
      <path d="M8.46 5a1 1 0 012 0v5.07l2 2V3a1 1 0 012 0v10h2V5a1 1 0 012 0v8h2V9a1 1 0 012 0v12a4.43 4.43 0 01-.1 1l1.6 1.6a6.88 6.88 0 00.5-2.57V9a3 3 0 00-3-3 2.82 2.82 0 00-1 .19V5a3 3 0 00-3-3 2.82 2.82 0 00-1.12.25 3 3 0 00-5.75 0A2.87 2.87 0 009.46 2a3 3 0 00-3 3v1.07l2 2zM17.46 26h-4.62a4.63 4.63 0 01-3.28-1.53l-6.82-6.75a1 1 0 010-1.44h.07a1 1 0 011.37 0l2.56 2.6 1.72 1.71v-6.76l-2-2v3.92l-.87-.87a3 3 0 00-4.25 4.25l6.78 6.78A6.72 6.72 0 0012.84 28h4.62a6.93 6.93 0 003.95-1.23L20 25.32a5 5 0 01-2.54.68z" />
    </Svg>
  ),
  'gv-logo-de': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 719.42 240.99"
      width="719.42px"
      height="240.99px"
      {...props}
    >
      <g>
        <g>
          <path
            className="cls-1"
            d="M0,64.5h13c.6,6.9,2.7,11.1,16.9,11.1s16.5-4.2,16.5-7.3c0-5.2-4.7-6.3-15.6-7.7C9.3,57.9.9,56.5.9,47s3.5-8,10.1-9.5c-6.8-3.5-9-9.1-9-15.8C2.1.8,23.5.8,31.3.8h26.2v5.9l-8.4,2.4c3.6,2,6.6,5.7,6.6,14.2s-4.9,19.1-26.1,19.7c-12.3.3-16.2.5-16.2,3.3s5.6,3.4,17.9,5c12.8,1.7,27.9,3,27.9,16.3s-5.9,17.8-30.5,17.8S0,77.4,0,64.5ZM28.9,34.2c11.3,0,14-4.9,14-12.1s-2.3-11.1-14-11.1-14.1,4.5-14.1,11.4,2.4,11.8,14.1,11.8Z"
          />
          <path
            className="cls-1"
            d="M64.7,30.6c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.4-14.8-29.4-30.6ZM94.2,51.1c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5Z"
          />
          <path
            className="cls-1"
            d="M62.5,85.5h13.6l17.7,49.1,18.2-49.1h14.1l-25.3,59.6h-14.2l-24.1-59.6Z"
          />
          <path
            className="cls-1"
            d="M128,115.3c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.4-14.8-29.4-30.6ZM157.5,135.8c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5Z"
          />
          <path
            className="cls-1"
            d="M225.1,135.2c-13.2,0-16.6-8.7-16.6-20s3.6-20.4,16.4-20.4,12.8,4.2,14.1,11.7h13c-1-13.4-9.6-21.9-26.7-21.9s-29.5,14.8-29.5,30.6,6.2,30.6,29.7,30.6,25.3-8.2,26.4-21.1h-13c-1.4,6.6-5.1,10.3-13.7,10.3Z"
          />
          <path
            className="cls-1"
            d="M260.5,127.6c0-10.9,7.8-15.8,24.8-17.6l18.7-1.9c-.3-9.8-3.5-13.6-15.5-13.6s-14.1,4.4-14.1,11.9h-12.1c0-12.2,5.1-21.8,26.1-21.8s27.8,10.5,27.8,26.5v33.9h-6.8l-2.6-9c-2.8,4.7-11.1,9.8-23.4,9.8s-23-5.7-23-18.3ZM286.7,136.1c12,0,17.3-6.3,17.3-16.6v-1.9l-16.9,1.9c-10.4,1.2-13.9,3.4-13.9,8s4.7,8.6,13.4,8.6Z"
          />
          <path className="cls-1" d="M328,62.8h12.5v82.3h-12.5V62.8Z" />
        </g>
        <polygon
          className="cls-1"
          points="352.3 156.5 349.4 166.5 234.7 166.5 234.7 166.5 218 166.5 188.4 200.4 188.4 166.5 183.7 166.5 183.7 166.5 128 166.5 128 178.9 175.9 178.9 175.9 219.6 188.1 219.6 223.7 178.9 359.6 178.9 359.6 168.7 359.6 166.5 359.6 156.5 352.3 156.5"
        />
      </g>
      <g>
        <path
          className="cls-1"
          d="M410.7,110.8c-3.2,0-6-.7-8.5-2-2.4-1.4-4.3-3.3-5.7-6s-2-5.8-2-9.5v-1.6c0-3.7.7-6.9,2.2-9.5s3.4-4.6,5.9-6c2.5-1.4,5.4-2,8.6-2h.9c3,0,5.6.6,7.9,1.8,2.3,1.2,4,3,5.2,5.2,1.2,2.3,1.9,4.9,1.9,7.9v4.3h-24.2c0,2.4.3,4.5,1,6.3.6,1.8,1.6,3.2,2.9,4.1s2.8,1.4,4.7,1.4,2.9-.3,4.2-.9c1.2-.6,2.2-1.5,2.9-2.8.7-1.2,1.1-2.8,1.1-4.6h7.5c0,2.8-.7,5.2-2.1,7.2-1.4,2.1-3.2,3.7-5.6,4.8-2.3,1.1-5,1.7-8,1.7h-.7ZM403,89.1h16.3c0-3.2-.7-5.6-2.1-7.1-1.4-1.5-3.3-2.3-5.8-2.3s-4.2.8-5.8,2.4-2.4,3.9-2.6,7.1Z"
        />
        <path
          className="cls-1"
          d="M435.8,110.1v-49.6h8v19c1.3-1.7,2.9-3.1,4.7-4,1.9-.9,3.8-1.3,6-1.3s4.4.5,6.2,1.4c1.8.9,3.3,2.3,4.3,4s1.6,3.9,1.6,6.5v24h-8v-22.5c0-2.6-.6-4.5-1.9-5.6s-2.9-1.7-5.1-1.7-2.7.4-3.9,1.1-2.1,1.7-2.9,2.9-1.1,2.5-1.1,4.1v21.8h-8Z"
        />
        <path
          className="cls-1"
          d="M491.5,110.8c-3.2,0-6-.7-8.5-2-2.4-1.4-4.3-3.3-5.7-6s-2-5.8-2-9.5v-1.6c0-3.7.7-6.9,2.2-9.5s3.4-4.6,5.9-6c2.5-1.4,5.4-2,8.6-2h.9c3,0,5.6.6,7.9,1.8,2.3,1.2,4,3,5.2,5.2,1.2,2.3,1.9,4.9,1.9,7.9v4.3h-24.2c0,2.4.3,4.5,1,6.3.6,1.8,1.6,3.2,2.9,4.1s2.8,1.4,4.7,1.4,2.9-.3,4.2-.9c1.2-.6,2.2-1.5,2.9-2.8.7-1.2,1.1-2.8,1.1-4.6h7.5c0,2.8-.7,5.2-2.1,7.2-1.4,2.1-3.2,3.7-5.6,4.8-2.3,1.1-5,1.7-8,1.7h-.7ZM483.7,89.1h16.3c0-3.2-.7-5.6-2.1-7.1-1.4-1.5-3.3-2.3-5.8-2.3s-4.2.8-5.8,2.4-2.4,3.9-2.6,7.1Z"
        />
        <path
          className="cls-1"
          d="M516.5,110.1v-35.2h6l1.1,5.2c1.2-1.8,2.6-3.3,4.3-4.3,1.7-1,3.6-1.5,5.9-1.5s4.3.4,6.1,1.3c1.8.9,3.2,2.4,4.2,4.4,1.1-1.7,2.5-3.1,4.2-4.2s3.9-1.5,6.3-1.5,4.2.5,6,1.4c1.7.9,3.1,2.3,4.1,4,1,1.8,1.5,3.9,1.5,6.5v24h-8v-22.2c0-2.7-.5-4.6-1.6-5.8s-2.6-1.8-4.5-1.8-2.4.3-3.4,1c-1,.7-1.8,1.6-2.4,2.8s-.9,2.5-.9,4.1v21.9h-8v-22.2c0-2.7-.5-4.6-1.6-5.8-1.1-1.2-2.6-1.8-4.6-1.8s-2.4.3-3.4,1c-1,.7-1.8,1.6-2.4,2.8s-.9,2.5-.9,4.1v21.9h-8Z"
        />
        <path className="cls-1" d="M576.3,110.1v-8.3h8.3v8.3h-8.3Z" />
        <path
          className="cls-1"
          d="M414.5,179.1c-2.7,0-5.2-.4-7.6-1.2-2.4-.8-4.4-2-6.2-3.8s-3.2-4-4.2-6.9c-1-2.8-1.5-6.3-1.5-10.4v-4.3c0-4,.5-7.4,1.5-10.2,1-2.8,2.4-5.1,4.2-6.9,1.8-1.8,3.8-3.1,6.1-3.9,2.3-.8,4.8-1.2,7.5-1.2h1c2.5,0,4.8.3,7.1,1,2.2.7,4.2,1.8,5.9,3.3,1.7,1.5,3,3.4,4,5.9,1,2.4,1.5,5.3,1.5,8.8h-8.1c0-3.3-.5-5.8-1.4-7.5-.9-1.8-2.2-3-3.9-3.7-1.7-.7-3.5-1.1-5.7-1.1s-3,.2-4.3.7c-1.3.5-2.5,1.2-3.4,2.3s-1.7,2.5-2.2,4.2c-.5,1.8-.8,4-.8,6.6v7.9c0,3.5.5,6.2,1.4,8.2.9,2,2.2,3.4,3.8,4.3,1.6.9,3.5,1.3,5.6,1.3s4.2-.4,5.9-1.1c1.7-.7,3-2,3.9-3.7.9-1.8,1.4-4.3,1.4-7.5h7.8c0,3.3-.5,6.2-1.4,8.5-1,2.4-2.3,4.3-4,5.9s-3.6,2.6-5.8,3.4c-2.2.7-4.5,1.1-7,1.1h-1Z"
        />
        <path
          className="cls-1"
          d="M442.7,135.3v-6.5h8.4v6.5h-8.4ZM442.9,178.4v-35.2h8v35.2h-8Z"
        />
        <path
          className="cls-1"
          d="M472.6,179.1c-1.8,0-3.5-.3-4.9-.8-1.4-.6-2.6-1.5-3.4-2.7-.8-1.3-1.2-2.9-1.2-5v-21.2h-3.9v-6.1h4.1l2.8-11.2h5v11.2h8.2v6.1h-8.2v19.6c0,2,.4,3.3,1.2,3.9.8.6,1.7.8,2.7.8s1.2-.1,2.1-.3c.9-.2,1.6-.5,2.2-.7v5.2c-.6.3-1.2.5-2,.7-.8.2-1.6.4-2.4.4-.8,0-1.6.1-2.3.1Z"
        />
        <path
          className="cls-1"
          d="M487.9,135.3v-6.5h8.4v6.5h-8.4ZM488.1,178.4v-35.2h8v35.2h-8Z"
        />
        <path
          className="cls-1"
          d="M504.3,178.4v-2.8l16.4-26.8h-15.4v-5.6h26.1v2.8l-16.4,26.9h16.9v5.6h-27.6Z"
        />
        <path
          className="cls-1"
          d="M554.2,179.1c-3.2,0-6-.7-8.5-2-2.4-1.4-4.3-3.3-5.7-6s-2-5.8-2-9.5v-1.6c0-3.7.7-6.9,2.2-9.5s3.4-4.6,5.9-6c2.5-1.4,5.4-2,8.6-2h.9c3,0,5.6.6,7.9,1.8,2.3,1.2,4,3,5.2,5.2,1.2,2.3,1.9,4.9,1.9,7.9v4.3h-24.2c0,2.4.3,4.5,1,6.3.6,1.8,1.6,3.2,2.9,4.1s2.8,1.4,4.7,1.4,2.9-.3,4.2-.9c1.2-.6,2.2-1.5,2.9-2.8.7-1.2,1.1-2.8,1.1-4.6h7.5c0,2.8-.7,5.2-2.1,7.2-1.4,2.1-3.2,3.7-5.6,4.8-2.3,1.1-5,1.7-8,1.7h-.7ZM546.4,157.4h16.3c0-3.2-.7-5.6-2.1-7.1-1.4-1.5-3.3-2.3-5.8-2.3s-4.2.8-5.8,2.4-2.4,3.9-2.6,7.1Z"
        />
        <path
          className="cls-1"
          d="M579.2,178.4v-35.2h5.9l1,5.2c1.3-1.9,3-3.4,5-4.4,2-1,4.2-1.5,6.6-1.5s4.6.5,6.4,1.4c1.8.9,3.3,2.3,4.3,4s1.5,3.9,1.5,6.5v24h-8v-22.5c0-2.6-.6-4.5-1.8-5.6s-2.9-1.7-5.1-1.7-2.7.4-3.9,1.1-2.1,1.7-2.9,2.9-1.1,2.5-1.1,4.1v21.8h-8Z"
        />
        <path
          className="cls-1"
          d="M620.8,178.4v-47.3h8.5v40.6h20.7l1,6.6h-30.2Z"
        />
        <path
          className="cls-1"
          d="M666.4,179.1c-1.9,0-3.7-.3-5.4-.9-1.7-.6-3.1-1.6-4.2-3.1-1.1-1.4-1.7-3.4-1.7-5.9s.6-4.6,1.7-6.1c1.1-1.6,2.7-2.8,4.8-3.6,2.1-.8,4.5-1.4,7.4-1.7,2.9-.3,6.1-.4,9.6-.4v-3.3c0-2.3-.6-3.9-1.8-4.9-1.2-.9-3-1.4-5.3-1.4s-3.7.4-5.2,1.3c-1.5.9-2.3,2.3-2.3,4.1v.7h-7.7c0-.2,0-.4,0-.7v-.7c0-2,.6-3.8,1.9-5.3s3-2.7,5.3-3.5c2.2-.8,4.8-1.2,7.7-1.2h1.2c4.8,0,8.3.9,10.7,2.8,2.3,1.9,3.5,4.5,3.5,7.9v18c0,.9.2,1.5.6,1.9.4.4.9.6,1.4.6s1,0,1.5-.3c.5-.2,1-.4,1.5-.6v4.9c-.6.5-1.4.8-2.3,1.1-.9.3-2,.4-3.3.4s-2.8-.3-3.8-.8c-1-.6-1.7-1.3-2.2-2.2-.5-.9-.8-2-.9-3.2-1.3,2-3.1,3.5-5.2,4.6-2.1,1.1-4.6,1.7-7.4,1.7ZM669.8,173.7c1.5,0,2.9-.3,4.2-1,1.3-.7,2.4-1.7,3.3-3,.9-1.3,1.3-3,1.3-5v-2.7c-3.2,0-6,.2-8.3.5-2.3.3-4,1-5.2,1.9-1.2.9-1.8,2.3-1.8,4.1s.6,3.2,1.7,4c1.1.8,2.7,1.2,4.8,1.2Z"
        />
        <path
          className="cls-1"
          d="M716.2,179.1c-5.2,0-9.1-2.1-11.8-6.3l-2.8,5.6h-3v-49.6h8v18.7c.9-1.3,2.3-2.5,4-3.5,1.8-1,3.8-1.5,6.2-1.5s4.8.6,6.9,1.8c2.1,1.2,3.8,3,5,5.4,1.2,2.5,1.9,5.6,1.9,9.5v1.6c0,4.2-.7,7.7-2,10.4-1.3,2.7-3.1,4.7-5.2,6-2.2,1.3-4.6,1.9-7.3,1.9ZM714.7,173.7c1.5,0,2.8-.4,3.9-1.1,1.1-.7,2.1-1.9,2.7-3.4.7-1.6,1-3.6,1-6.1v-4.9c0-2.2-.3-4.1-1-5.6-.6-1.5-1.5-2.6-2.6-3.3s-2.4-1.1-3.9-1.1-2.8.4-4.1,1.1-2.2,1.8-3,3.3c-.8,1.5-1.1,3.4-1.1,5.6v4.9c0,2.5.4,4.6,1.1,6.1s1.7,2.7,2.9,3.4,2.6,1.1,4,1.1Z"
        />
      </g>
    </Svg>
  ),
  'gv-logo-dk': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 719.42 240.99"
      width="719.42px"
      height="240.99px"
      {...props}
    >
      <g>
        <g>
          <path
            className="cls-1"
            d="M0,64.5h13c.6,6.9,2.7,11.1,16.9,11.1s16.5-4.2,16.5-7.3c0-5.2-4.7-6.3-15.6-7.7C9.3,57.9.9,56.5.9,47s3.5-8,10.1-9.5c-6.8-3.5-9-9.1-9-15.8C2.1.8,23.5.8,31.3.8h26.2v5.9l-8.4,2.4c3.6,2,6.6,5.7,6.6,14.2s-4.9,19.1-26.1,19.7c-12.3.3-16.2.5-16.2,3.3s5.6,3.4,17.9,5c12.8,1.7,27.9,3,27.9,16.3s-5.9,17.8-30.5,17.8S0,77.4,0,64.5ZM28.9,34.2c11.3,0,14-4.9,14-12.1s-2.3-11.1-14-11.1-14.1,4.5-14.1,11.4,2.4,11.8,14.1,11.8Z"
          />
          <path
            className="cls-1"
            d="M64.7,30.6c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.4-14.8-29.4-30.6ZM94.2,51.1c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5Z"
          />
          <path
            className="cls-1"
            d="M62.5,85.5h13.6l17.7,49.1,18.2-49.1h14.1l-25.3,59.6h-14.2l-24.1-59.6Z"
          />
          <path
            className="cls-1"
            d="M128,115.3c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.4-14.8-29.4-30.6ZM157.5,135.8c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5Z"
          />
          <path
            className="cls-1"
            d="M225.1,135.2c-13.2,0-16.6-8.7-16.6-20s3.6-20.4,16.4-20.4,12.8,4.2,14.1,11.7h13c-1-13.4-9.6-21.9-26.7-21.9s-29.5,14.8-29.5,30.6,6.2,30.6,29.7,30.6,25.3-8.2,26.4-21.1h-13c-1.4,6.6-5.1,10.3-13.7,10.3Z"
          />
          <path
            className="cls-1"
            d="M260.5,127.6c0-10.9,7.8-15.8,24.8-17.6l18.7-1.9c-.3-9.8-3.5-13.6-15.5-13.6s-14.1,4.4-14.1,11.9h-12.1c0-12.2,5.1-21.8,26.1-21.8s27.8,10.5,27.8,26.5v33.9h-6.8l-2.6-9c-2.8,4.7-11.1,9.8-23.4,9.8s-23-5.7-23-18.3ZM286.7,136.1c12,0,17.3-6.3,17.3-16.6v-1.9l-16.9,1.9c-10.4,1.2-13.9,3.4-13.9,8s4.7,8.6,13.4,8.6Z"
          />
          <path className="cls-1" d="M328,62.8h12.5v82.3h-12.5V62.8Z" />
        </g>
        <polygon
          className="cls-1"
          points="352.3 156.5 349.4 166.5 234.7 166.5 234.7 166.5 218 166.5 188.4 200.4 188.4 166.5 183.7 166.5 183.7 166.5 128 166.5 128 178.9 175.9 178.9 175.9 219.6 188.1 219.6 223.7 178.9 359.6 178.9 359.6 168.7 359.6 166.5 359.6 156.5 352.3 156.5"
        />
      </g>
      <g>
        <path
          className="cls-1"
          d="M405.9,109.4v-41.5h-14.4v-6.7h37.5v6.7h-14.4v41.5h-8.7Z"
        />
        <path
          className="cls-1"
          d="M435,65.4v-6.6h8.6v6.6h-8.6ZM435.2,109.4v-35.9h8.2v35.9h-8.2Z"
        />
        <path
          className="cls-1"
          d="M467.7,110.1c-2.7,0-5.2-.7-7.5-2s-4.1-3.4-5.4-6.1-2-6.3-2-10.6v-.8c0-3.9.6-7.2,1.9-9.9,1.3-2.6,3-4.6,5.1-6,2.1-1.3,4.5-2,7.1-2s4.7.5,6.5,1.4c1.8,1,3.1,2.2,4.1,3.7v-19.1h8.2v50.6h-6.3l-1-4.9c-1.2,1.7-2.6,3-4.4,4-1.7,1-3.8,1.5-6.3,1.5ZM469.3,104.6c1.5,0,2.9-.4,4.1-1.1s2.2-1.9,3-3.5c.7-1.6,1.1-3.7,1.1-6.3v-5c0-2.3-.4-4.2-1.1-5.7-.7-1.5-1.7-2.7-3-3.4s-2.6-1.1-4.1-1.1c-2.3,0-4.1.9-5.7,2.6-1.5,1.7-2.3,4.3-2.3,7.7v4.9c0,2.6.4,4.7,1.1,6.3s1.7,2.8,2.8,3.5c1.2.8,2.5,1.1,4,1.1Z"
        />
        <path className="cls-1" d="M496,109.4v-50.6h8.2v50.6h-8.2Z" />
        <path
          className="cls-1"
          d="M515.5,65.4v-6.6h8.6v6.6h-8.6ZM515.8,109.4v-35.9h8.2v35.9h-8.2Z"
        />
        <path
          className="cls-1"
          d="M540.8,122.6c-1.6,0-3.1-.4-4.5-1.2-1.3-.8-2.4-1.8-3.2-3.1s-1.2-2.8-1.2-4.4.5-3.3,1.4-4.8c.9-1.5,2.2-2.6,3.9-3.4-1-.7-1.8-1.5-2.4-2.5-.6-1-.9-2.1-.9-3.3,0-1.7.5-3.2,1.6-4.4,1.1-1.2,2.5-2.1,4.2-2.7-1.5-.9-2.6-2.1-3.5-3.4-.9-1.3-1.3-2.9-1.3-4.8s.5-3.9,1.6-5.8c1.1-1.8,2.7-3.3,4.9-4.5s4.9-1.7,8.3-1.7,2.8,0,4,.2c1.2.2,2.3.4,3.3.8,1.8-.8,3.2-1.9,4.1-3.1.9-1.2,1.5-2.4,1.5-3.8h6.5c0,1.5-.3,3-.9,4.2-.6,1.3-1.4,2.3-2.5,3.2-1.1.8-2.4,1.4-3.9,1.8v.2c1,.9,1.9,2,2.5,3.3.7,1.3,1,2.7,1,4.2s-.6,4-1.7,5.7c-1.1,1.7-2.7,3.1-4.7,4.2-2.1,1.1-4.5,1.6-7.5,1.6h-5.4c-1.5,0-2.6.3-3.4,1-.8.7-1.3,1.5-1.3,2.6s.4,1.9,1.2,2.5c.8.7,2,1,3.5,1h13.1c3.1,0,5.5.9,7.3,2.8,1.8,1.9,2.7,4.2,2.7,6.9s-.5,3.7-1.4,5.3c-.9,1.6-2.2,2.9-3.9,3.8-1.7.9-3.6,1.4-5.8,1.4h-17.4ZM543.7,117.3h12.9c1.4,0,2.5-.5,3.5-1.4,1-.9,1.5-2,1.5-3.3s-.4-2.1-1.2-2.9c-.8-.8-1.8-1.2-3.1-1.2h-13.6c-1.3,0-2.4.4-3.3,1.3-.9.9-1.3,1.9-1.3,3.1s.4,2.2,1.3,3.1,2,1.3,3.3,1.3ZM550.1,90.6c2.3,0,4.2-.6,5.5-1.9s2-2.8,2-4.6-.7-3.3-2-4.6c-1.3-1.3-3.2-1.9-5.5-1.9s-4.2.6-5.5,1.9c-1.3,1.3-2,2.8-2,4.6s.7,3.3,2,4.6c1.3,1.3,3.2,1.9,5.5,1.9Z"
        />
        <path
          className="cls-1"
          d="M590.4,110.1c-3.3,0-6.2-.7-8.6-2.1-2.5-1.4-4.4-3.4-5.8-6.1s-2.1-5.9-2.1-9.7v-1.6c0-3.8.7-7,2.2-9.7s3.5-4.7,6-6.1c2.6-1.4,5.5-2.1,8.8-2.1h.9c3,0,5.7.6,8,1.9,2.3,1.2,4.1,3,5.3,5.3,1.3,2.3,1.9,5,1.9,8.1v4.4h-24.7c0,2.5.3,4.6,1,6.5.7,1.8,1.6,3.2,2.9,4.2s2.9,1.4,4.8,1.4,3-.3,4.3-.9c1.2-.6,2.2-1.6,3-2.8.7-1.3,1.1-2.8,1.1-4.7h7.7c0,2.8-.7,5.3-2.1,7.4-1.4,2.1-3.3,3.8-5.7,4.9-2.4,1.2-5.1,1.8-8.2,1.8h-.7ZM582.4,88h16.6c0-3.3-.7-5.7-2.1-7.3-1.4-1.6-3.4-2.4-5.9-2.4s-4.3.8-5.9,2.4-2.5,4-2.6,7.2Z"
        />
        <path
          className="cls-1"
          d="M615.9,109.4v-35.9h6.1l1.1,5.3c0-.1.3-.5.7-1.1s1-1.3,1.8-2,1.8-1.4,3-2,2.7-.8,4.4-.8c2.5,0,4.5.5,6.1,1.5,1.6,1,2.7,2.3,3.5,4s1.2,3.7,1.2,5.9,0,1,0,1.6c0,.5,0,1-.1,1.4h-6.9v-1.4c0-1-.2-2-.6-3-.4-1-1-1.9-1.9-2.6s-2.1-1.1-3.7-1.1-2.2.2-3,.7-1.5,1.1-2,1.9c-.5.8-.9,1.6-1.1,2.4-.2.9-.3,1.7-.3,2.4v22.9h-8.2Z"
        />
        <path
          className="cls-1"
          d="M665.6,110.1c-3.3,0-6.2-.7-8.6-2.1-2.5-1.4-4.4-3.4-5.8-6.1s-2.1-5.9-2.1-9.7v-1.6c0-3.8.7-7,2.2-9.7s3.5-4.7,6-6.1c2.6-1.4,5.5-2.1,8.8-2.1h.9c3,0,5.7.6,8,1.9,2.3,1.2,4.1,3,5.3,5.3,1.3,2.3,1.9,5,1.9,8.1v4.4h-24.7c0,2.5.3,4.6,1,6.5.7,1.8,1.6,3.2,2.9,4.2s2.9,1.4,4.8,1.4,3-.3,4.3-.9c1.2-.6,2.2-1.6,3-2.8.7-1.3,1.1-2.8,1.1-4.7h7.7c0,2.8-.7,5.3-2.1,7.4-1.4,2.1-3.3,3.8-5.7,4.9-2.4,1.2-5.1,1.8-8.2,1.8h-.7ZM657.6,88h16.6c0-3.3-.7-5.7-2.1-7.3-1.4-1.6-3.4-2.4-5.9-2.4s-4.3.8-5.9,2.4-2.5,4-2.6,7.2Z"
        />
        <path
          className="cls-1"
          d="M413.7,179.9c-2.8,0-5.4-.4-7.8-1.2-2.4-.8-4.5-2.1-6.3-3.9s-3.2-4.1-4.3-7c-1-2.9-1.5-6.4-1.5-10.6v-4.4c0-4.1.5-7.6,1.5-10.4,1-2.9,2.4-5.2,4.3-7,1.8-1.8,3.9-3.1,6.3-3.9,2.4-.8,4.9-1.2,7.6-1.2h1.1c2.5,0,4.9.4,7.2,1.1,2.3.7,4.3,1.8,6,3.3,1.7,1.5,3.1,3.5,4.1,6,1,2.5,1.5,5.4,1.5,9h-8.2c0-3.3-.5-5.9-1.4-7.7-1-1.8-2.3-3.1-4-3.8-1.7-.7-3.6-1.1-5.8-1.1s-3.1.2-4.4.7c-1.4.5-2.5,1.2-3.5,2.3s-1.7,2.5-2.3,4.3c-.5,1.8-.8,4-.8,6.7v8c0,3.6.5,6.4,1.4,8.4.9,2,2.2,3.5,3.9,4.4,1.7.9,3.6,1.3,5.7,1.3s4.3-.4,6-1.1c1.7-.7,3-2,4-3.8,1-1.8,1.4-4.3,1.4-7.6h7.9c0,3.4-.5,6.3-1.5,8.7-1,2.4-2.3,4.4-4,6s-3.7,2.7-5.9,3.4c-2.2.7-4.6,1.1-7.2,1.1h-1.1Z"
        />
        <path
          className="cls-1"
          d="M442.5,135.1v-6.6h8.6v6.6h-8.6ZM442.7,179.2v-35.9h8.2v35.9h-8.2Z"
        />
        <path
          className="cls-1"
          d="M473,179.9c-1.9,0-3.6-.3-5-.8-1.5-.6-2.6-1.5-3.4-2.8-.8-1.3-1.2-3-1.2-5.1v-21.7h-3.9v-6.3h4.1l2.9-11.5h5.1v11.5h8.4v6.3h-8.4v20c0,2.1.4,3.4,1.3,3.9.8.6,1.8.8,2.7.8s1.2-.1,2.1-.4c.9-.2,1.7-.5,2.2-.7v5.3c-.6.3-1.2.5-2,.7-.8.2-1.6.4-2.5.5-.8,0-1.6.1-2.3.1Z"
        />
        <path
          className="cls-1"
          d="M488.6,135.1v-6.6h8.6v6.6h-8.6ZM488.8,179.2v-35.9h8.2v35.9h-8.2Z"
        />
        <path
          className="cls-1"
          d="M505.3,179.2v-2.9l16.7-27.4h-15.7v-5.7h26.6v2.8l-16.7,27.4h17.3v5.7h-28.2Z"
        />
        <path
          className="cls-1"
          d="M556.3,179.9c-3.3,0-6.2-.7-8.6-2.1-2.5-1.4-4.4-3.4-5.8-6.1-1.4-2.7-2.1-5.9-2.1-9.7v-1.6c0-3.8.7-7,2.2-9.7s3.5-4.7,6-6.1c2.6-1.4,5.5-2.1,8.8-2.1h.9c3,0,5.7.6,8,1.9,2.3,1.2,4.1,3,5.3,5.3,1.3,2.3,1.9,5,1.9,8.1v4.4h-24.7c0,2.5.3,4.6,1,6.5.7,1.8,1.6,3.2,2.9,4.2s2.9,1.4,4.8,1.4,3-.3,4.3-.9c1.2-.6,2.2-1.6,3-2.8.7-1.3,1.1-2.8,1.1-4.7h7.7c0,2.8-.7,5.3-2.1,7.4-1.4,2.1-3.3,3.8-5.7,4.9-2.4,1.2-5.1,1.8-8.2,1.8h-.7ZM548.3,157.7h16.6c0-3.3-.7-5.7-2.1-7.3-1.4-1.6-3.4-2.4-5.9-2.4s-4.3.8-5.9,2.4-2.5,4-2.6,7.2Z"
        />
        <path
          className="cls-1"
          d="M581.8,179.2v-35.9h6l1.1,5.3c1.4-2,3-3.5,5.1-4.5,2-1,4.3-1.5,6.8-1.5s4.7.5,6.5,1.4c1.9.9,3.3,2.3,4.4,4.1s1.6,4,1.6,6.6v24.5h-8.2v-23c0-2.7-.6-4.6-1.9-5.7s-3-1.7-5.2-1.7-2.8.4-4,1.1-2.2,1.7-2.9,2.9-1.1,2.6-1.1,4.1v22.3h-8.2Z"
        />
        <path
          className="cls-1"
          d="M624.3,179.2v-48.2h8.7v41.5h21.1l1.1,6.7h-30.9Z"
        />
        <path
          className="cls-1"
          d="M670.8,179.9c-1.9,0-3.8-.3-5.5-.9-1.8-.6-3.2-1.7-4.3-3.1-1.1-1.5-1.7-3.5-1.7-6s.6-4.7,1.7-6.3c1.1-1.6,2.8-2.8,4.9-3.7,2.1-.8,4.6-1.4,7.6-1.7,3-.3,6.2-.5,9.8-.5v-3.4c0-2.3-.6-4-1.8-5-1.2-1-3-1.4-5.4-1.4s-3.8.4-5.3,1.3c-1.5.9-2.3,2.3-2.3,4.2v.7h-7.9c0-.2,0-.4,0-.7v-.7c0-2.1.6-3.9,1.9-5.4s3.1-2.7,5.4-3.6c2.3-.8,4.9-1.3,7.9-1.3h1.2c4.9,0,8.5,1,10.9,2.9,2.4,1.9,3.6,4.6,3.6,8.1v18.4c0,.9.2,1.5.6,1.9.4.4.9.6,1.4.6s1,0,1.5-.3c.5-.2,1-.4,1.5-.6v5c-.6.5-1.4.8-2.3,1.1-.9.3-2.1.4-3.4.4s-2.8-.3-3.8-.8c-1-.6-1.8-1.3-2.3-2.3-.5-1-.8-2.1-.9-3.3-1.4,2-3.1,3.6-5.3,4.7-2.2,1.1-4.7,1.7-7.6,1.7ZM674.3,174.3c1.5,0,2.9-.3,4.3-1,1.4-.7,2.5-1.7,3.4-3.1.9-1.4,1.3-3.1,1.3-5.1v-2.7c-3.3,0-6.1.2-8.4.5-2.3.4-4.1,1-5.3,1.9-1.2.9-1.8,2.3-1.8,4.2s.6,3.2,1.7,4c1.1.8,2.8,1.2,4.9,1.2Z"
        />
        <path
          className="cls-1"
          d="M721.7,179.9c-5.3,0-9.3-2.1-12.1-6.4l-2.8,5.7h-3v-50.6h8.2v19.1c.9-1.4,2.3-2.6,4.1-3.6,1.8-1,3.9-1.5,6.4-1.5s4.9.6,7.1,1.8c2.1,1.2,3.8,3,5.1,5.6,1.3,2.5,1.9,5.7,1.9,9.7v1.6c0,4.3-.7,7.9-2,10.6-1.3,2.8-3.1,4.8-5.3,6.1-2.2,1.3-4.7,2-7.4,2ZM720.1,174.3c1.5,0,2.8-.4,4-1.1,1.2-.7,2.1-1.9,2.8-3.5.7-1.6,1-3.7,1-6.3v-5c0-2.3-.3-4.2-1-5.7-.7-1.5-1.5-2.6-2.7-3.4s-2.4-1.1-3.9-1.1-2.9.4-4.1,1.1-2.3,1.9-3.1,3.4c-.8,1.5-1.2,3.4-1.2,5.7v5c0,2.6.4,4.7,1.1,6.3s1.7,2.8,3,3.5,2.6,1.1,4.1,1.1Z"
        />
      </g>
    </Svg>
  ),
  'gv-logo-es': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 719.42 240.99"
      width="719.42px"
      height="240.99px"
      {...props}
    >
      <g>
        <g>
          <path
            className="cls-1"
            d="M0,64.5h13c.6,6.9,2.7,11.1,16.9,11.1s16.5-4.2,16.5-7.3c0-5.2-4.7-6.3-15.6-7.7C9.3,57.9.9,56.5.9,47s3.5-8,10.1-9.5c-6.8-3.5-9-9.1-9-15.8C2.1.8,23.5.8,31.3.8h26.2v5.9l-8.4,2.4c3.6,2,6.6,5.7,6.6,14.2s-4.9,19.1-26.1,19.7c-12.3.3-16.2.5-16.2,3.3s5.6,3.4,17.9,5c12.8,1.7,27.9,3,27.9,16.3s-5.9,17.8-30.5,17.8S0,77.4,0,64.5ZM28.9,34.2c11.3,0,14-4.9,14-12.1s-2.3-11.1-14-11.1-14.1,4.5-14.1,11.4,2.4,11.8,14.1,11.8Z"
          />
          <path
            className="cls-1"
            d="M64.7,30.6c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.4-14.8-29.4-30.6ZM94.2,51.1c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5Z"
          />
          <path
            className="cls-1"
            d="M62.5,85.5h13.6l17.7,49.1,18.2-49.1h14.1l-25.3,59.6h-14.2l-24.1-59.6Z"
          />
          <path
            className="cls-1"
            d="M128,115.3c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.4-14.8-29.4-30.6ZM157.5,135.8c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5Z"
          />
          <path
            className="cls-1"
            d="M225.1,135.2c-13.2,0-16.6-8.7-16.6-20s3.6-20.4,16.4-20.4,12.8,4.2,14.1,11.7h13c-1-13.4-9.6-21.9-26.7-21.9s-29.5,14.8-29.5,30.6,6.2,30.6,29.7,30.6,25.3-8.2,26.4-21.1h-13c-1.4,6.6-5.1,10.3-13.7,10.3Z"
          />
          <path
            className="cls-1"
            d="M260.5,127.6c0-10.9,7.8-15.8,24.8-17.6l18.7-1.9c-.3-9.8-3.5-13.6-15.5-13.6s-14.1,4.4-14.1,11.9h-12.1c0-12.2,5.1-21.8,26.1-21.8s27.8,10.5,27.8,26.5v33.9h-6.8l-2.6-9c-2.8,4.7-11.1,9.8-23.4,9.8s-23-5.7-23-18.3ZM286.7,136.1c12,0,17.3-6.3,17.3-16.6v-1.9l-16.9,1.9c-10.4,1.2-13.9,3.4-13.9,8s4.7,8.6,13.4,8.6Z"
          />
          <path className="cls-1" d="M328,62.8h12.5v82.3h-12.5V62.8Z" />
        </g>
        <polygon
          className="cls-1"
          points="352.3 156.5 349.4 166.5 234.7 166.5 234.7 166.5 218 166.5 188.4 200.4 188.4 166.5 183.7 166.5 183.7 166.5 128 166.5 128 178.9 175.9 178.9 175.9 219.6 188.1 219.6 223.7 178.9 359.6 178.9 359.6 168.7 359.6 166.5 359.6 156.5 352.3 156.5"
        />
      </g>
      <g>
        <path
          className="cls-1"
          d="M390.6,109.8l18.1-47.8h10l18,47.8h-9l-4-10.4h-20.7l-4,10.4h-8.4ZM405.3,93.1h16.2l-7.9-21.7h-.3l-8,21.7Z"
        />
        <path
          className="cls-1"
          d="M442.4,109.8v-35.6h5.9l1,5.2c1.3-2,3-3.4,5-4.4,2-1,4.2-1.5,6.7-1.5s4.6.5,6.5,1.4c1.9.9,3.3,2.3,4.4,4.1s1.6,4,1.6,6.6v24.2h-8.1v-22.8c0-2.6-.6-4.5-1.8-5.7s-2.9-1.7-5.1-1.7-2.8.4-4,1.1-2.2,1.7-2.9,2.9-1.1,2.6-1.1,4.1v22.1h-8.1Z"
        />
        <path
          className="cls-1"
          d="M494.9,110.5c-1.9,0-3.5-.3-5-.8-1.5-.6-2.6-1.5-3.4-2.8-.8-1.3-1.2-3-1.2-5v-21.4h-3.9v-6.2h4.1l2.9-11.4h5v11.4h8.3v6.2h-8.3v19.8c0,2,.4,3.3,1.3,3.9.8.6,1.7.8,2.7.8s1.2-.1,2.1-.3c.9-.2,1.6-.5,2.2-.7v5.2c-.6.3-1.2.5-2,.7-.8.2-1.6.4-2.4.5-.8,0-1.6.1-2.3.1Z"
        />
        <path
          className="cls-1"
          d="M524.8,110.5c-3.2,0-6.1-.7-8.6-2.1-2.5-1.4-4.4-3.4-5.7-6s-2.1-5.8-2.1-9.6v-1.6c0-3.8.7-7,2.2-9.6s3.5-4.7,6-6c2.5-1.4,5.4-2.1,8.7-2.1h.9c3,0,5.7.6,7.9,1.8,2.3,1.2,4,3,5.3,5.3,1.3,2.3,1.9,4.9,1.9,8v4.4h-24.4c0,2.5.3,4.6,1,6.4.6,1.8,1.6,3.2,2.9,4.1s2.9,1.4,4.8,1.4,3-.3,4.2-.9c1.2-.6,2.2-1.6,2.9-2.8.7-1.3,1.1-2.8,1.1-4.7h7.6c0,2.8-.7,5.2-2.1,7.3-1.4,2.1-3.3,3.7-5.6,4.9-2.4,1.2-5.1,1.7-8.1,1.7h-.7ZM516.9,88.5h16.4c0-3.2-.7-5.6-2.1-7.2-1.4-1.6-3.4-2.3-5.8-2.3s-4.3.8-5.9,2.4-2.5,4-2.6,7.1Z"
        />
        <path
          className="cls-1"
          d="M550.2,109.8v-35.6h6.1l1,5.2c0-.1.3-.5.7-1.1s1-1.3,1.7-2,1.8-1.4,3-2,2.7-.8,4.4-.8c2.5,0,4.5.5,6,1.5,1.6,1,2.7,2.3,3.5,4s1.1,3.6,1.1,5.8,0,1,0,1.6c0,.5,0,1-.1,1.4h-6.8v-1.4c0-1-.2-2-.6-3-.4-1-1-1.9-1.9-2.6s-2.1-1-3.7-1-2.1.2-3,.7-1.5,1.1-2,1.8c-.5.7-.9,1.5-1.1,2.4-.2.9-.3,1.7-.3,2.4v22.7h-8.1Z"
        />
        <path
          className="cls-1"
          d="M584.8,66.2v-6.5h8.5v6.5h-8.5ZM585,109.8v-35.6h8.1v35.6h-8.1Z"
        />
        <path
          className="cls-1"
          d="M618.9,110.5c-3.2,0-6-.7-8.5-2.2s-4.5-3.5-5.9-6.2c-1.4-2.7-2.1-5.9-2.1-9.5v-1.2c0-3.7.7-6.9,2.1-9.6,1.4-2.7,3.4-4.7,5.8-6.2s5.3-2.2,8.5-2.2h1.1c3.2,0,6,.7,8.5,2.2,2.5,1.4,4.4,3.5,5.8,6.2,1.4,2.7,2.1,5.9,2.1,9.6v1.2c0,3.7-.7,6.8-2.1,9.5-1.4,2.7-3.4,4.8-5.8,6.2-2.5,1.4-5.3,2.2-8.5,2.2h-1.1ZM619.4,105c1.6,0,3.1-.4,4.4-1.1,1.3-.8,2.3-1.9,3.1-3.4.8-1.5,1.1-3.4,1.1-5.6v-5.6c0-2.2-.4-4.1-1.1-5.6-.8-1.5-1.8-2.6-3.1-3.4-1.3-.8-2.7-1.1-4.4-1.1s-3,.4-4.3,1.1c-1.3.8-2.3,1.9-3.1,3.4-.8,1.5-1.1,3.4-1.1,5.6v5.6c0,2.2.4,4,1.1,5.6.8,1.5,1.8,2.7,3.1,3.4,1.3.8,2.7,1.1,4.3,1.1Z"
        />
        <path
          className="cls-1"
          d="M645.2,109.8v-35.6h6.1l1,5.2c0-.1.3-.5.7-1.1s1-1.3,1.7-2,1.8-1.4,3-2,2.7-.8,4.4-.8c2.5,0,4.5.5,6,1.5,1.6,1,2.7,2.3,3.5,4s1.1,3.6,1.1,5.8,0,1,0,1.6c0,.5,0,1-.1,1.4h-6.8v-1.4c0-1-.2-2-.6-3-.4-1-1-1.9-1.9-2.6s-2.1-1-3.7-1-2.1.2-3,.7-1.5,1.1-2,1.8c-.5.7-.9,1.5-1.1,2.4-.2.9-.3,1.7-.3,2.4v22.7h-8.1Z"
        />
        <path
          className="cls-1"
          d="M679.6,109.8v-35.6h6.1l1.1,5.2c1.2-1.9,2.6-3.3,4.3-4.4,1.7-1,3.6-1.6,5.9-1.6s4.4.5,6.2,1.4c1.8.9,3.2,2.4,4.2,4.4,1.1-1.8,2.5-3.2,4.3-4.2s3.9-1.6,6.4-1.6,4.3.5,6.1,1.4c1.8.9,3.1,2.3,4.1,4.1,1,1.8,1.5,4,1.5,6.6v24.2h-8.1v-22.5c0-2.7-.5-4.6-1.6-5.8s-2.6-1.8-4.6-1.8-2.5.3-3.5,1c-1,.7-1.8,1.6-2.4,2.8s-.9,2.6-.9,4.1v22.1h-8.1v-22.5c0-2.7-.5-4.6-1.6-5.8-1.1-1.2-2.6-1.8-4.6-1.8s-2.5.3-3.5,1c-1,.7-1.8,1.6-2.4,2.8s-.9,2.6-.9,4.1v22.1h-8.1Z"
        />
        <path
          className="cls-1"
          d="M755,110.5c-3.2,0-6.1-.7-8.6-2.1-2.5-1.4-4.4-3.4-5.7-6s-2.1-5.8-2.1-9.6v-1.6c0-3.8.7-7,2.2-9.6s3.5-4.7,6-6c2.5-1.4,5.4-2.1,8.7-2.1h.9c3,0,5.7.6,7.9,1.8,2.3,1.2,4,3,5.3,5.3,1.3,2.3,1.9,4.9,1.9,8v4.4h-24.4c0,2.5.3,4.6,1,6.4.6,1.8,1.6,3.2,2.9,4.1s2.9,1.4,4.8,1.4,3-.3,4.2-.9c1.2-.6,2.2-1.6,2.9-2.8.7-1.3,1.1-2.8,1.1-4.7h7.6c0,2.8-.7,5.2-2.1,7.3-1.4,2.1-3.3,3.7-5.6,4.9-2.4,1.2-5.1,1.7-8.1,1.7h-.7ZM747.1,88.5h16.4c0-3.2-.7-5.6-2.1-7.2-1.4-1.6-3.4-2.3-5.8-2.3s-4.3.8-5.9,2.4-2.5,4-2.6,7.1Z"
        />
        <path
          className="cls-1"
          d="M780.3,109.8v-35.6h5.9l1,5.2c1.3-2,3-3.4,5-4.4,2-1,4.2-1.5,6.7-1.5s4.6.5,6.5,1.4c1.9.9,3.3,2.3,4.4,4.1s1.6,4,1.6,6.6v24.2h-8.1v-22.8c0-2.6-.6-4.5-1.8-5.7s-2.9-1.7-5.1-1.7-2.8.4-4,1.1-2.2,1.7-2.9,2.9-1.1,2.6-1.1,4.1v22.1h-8.1Z"
        />
        <path
          className="cls-1"
          d="M832.8,110.5c-1.9,0-3.5-.3-5-.8-1.5-.6-2.6-1.5-3.4-2.8-.8-1.3-1.2-3-1.2-5v-21.4h-3.9v-6.2h4.1l2.9-11.4h5v11.4h8.3v6.2h-8.3v19.8c0,2,.4,3.3,1.3,3.9.8.6,1.7.8,2.7.8s1.2-.1,2.1-.3c.9-.2,1.6-.5,2.2-.7v5.2c-.6.3-1.2.5-2,.7-.8.2-1.6.4-2.4.5-.8,0-1.6.1-2.3.1Z"
        />
        <path
          className="cls-1"
          d="M862.7,110.5c-3.2,0-6.1-.7-8.6-2.1-2.5-1.4-4.4-3.4-5.7-6s-2.1-5.8-2.1-9.6v-1.6c0-3.8.7-7,2.2-9.6s3.5-4.7,6-6c2.5-1.4,5.4-2.1,8.7-2.1h.9c3,0,5.7.6,7.9,1.8,2.3,1.2,4,3,5.3,5.3,1.3,2.3,1.9,4.9,1.9,8v4.4h-24.4c0,2.5.3,4.6,1,6.4.6,1.8,1.6,3.2,2.9,4.1s2.9,1.4,4.8,1.4,3-.3,4.2-.9c1.2-.6,2.2-1.6,2.9-2.8.7-1.3,1.1-2.8,1.1-4.7h7.6c0,2.8-.7,5.2-2.1,7.3-1.4,2.1-3.3,3.7-5.6,4.9-2.4,1.2-5.1,1.7-8.1,1.7h-.7ZM854.8,88.5h16.4c0-3.2-.7-5.6-2.1-7.2-1.4-1.6-3.4-2.3-5.8-2.3s-4.3.8-5.9,2.4-2.5,4-2.6,7.1Z"
        />
        <path
          className="cls-1"
          d="M413.9,179.6c-2.7,0-5.3-.4-7.7-1.2-2.4-.8-4.5-2.1-6.3-3.8s-3.2-4.1-4.2-6.9c-1-2.9-1.5-6.3-1.5-10.5v-4.3c0-4,.5-7.5,1.5-10.3,1-2.9,2.4-5.2,4.2-7,1.8-1.8,3.9-3.1,6.2-3.9,2.3-.8,4.9-1.2,7.6-1.2h1c2.5,0,4.9.3,7.1,1,2.3.7,4.2,1.8,6,3.3,1.7,1.5,3.1,3.5,4.1,5.9,1,2.4,1.5,5.4,1.5,8.9h-8.1c0-3.3-.5-5.8-1.4-7.6-1-1.8-2.3-3-3.9-3.8-1.7-.7-3.6-1.1-5.7-1.1s-3,.2-4.4.7c-1.3.5-2.5,1.2-3.5,2.3s-1.7,2.5-2.3,4.3c-.5,1.8-.8,4-.8,6.6v7.9c0,3.5.5,6.3,1.4,8.3.9,2,2.2,3.5,3.9,4.3,1.6.9,3.5,1.3,5.7,1.3s4.3-.4,6-1.1c1.7-.7,3-2,4-3.8.9-1.8,1.4-4.3,1.4-7.6h7.9c0,3.3-.5,6.2-1.5,8.6-1,2.4-2.3,4.4-4,5.9s-3.7,2.7-5.9,3.4c-2.2.7-4.6,1.1-7.1,1.1h-1Z"
        />
        <path
          className="cls-1"
          d="M442.4,135.3v-6.5h8.5v6.5h-8.5ZM442.6,178.9v-35.6h8.1v35.6h-8.1Z"
        />
        <path
          className="cls-1"
          d="M472.6,179.6c-1.9,0-3.5-.3-5-.8-1.5-.6-2.6-1.5-3.4-2.8-.8-1.3-1.2-3-1.2-5v-21.4h-3.9v-6.2h4.1l2.9-11.4h5v11.4h8.3v6.2h-8.3v19.8c0,2,.4,3.3,1.3,3.9.8.6,1.7.8,2.7.8s1.2-.1,2.1-.3c.9-.2,1.6-.5,2.2-.7v5.2c-.6.3-1.2.5-2,.7-.8.2-1.6.4-2.4.5-.8,0-1.6.1-2.3.1Z"
        />
        <path
          className="cls-1"
          d="M488.1,135.3v-6.5h8.5v6.5h-8.5ZM488.3,178.9v-35.6h8.1v35.6h-8.1Z"
        />
        <path
          className="cls-1"
          d="M504.6,178.9v-2.9l16.6-27.1h-15.6v-5.6h26.4v2.8l-16.6,27.2h17.1v5.6h-27.9Z"
        />
        <path
          className="cls-1"
          d="M555,179.6c-3.2,0-6.1-.7-8.6-2.1-2.5-1.4-4.4-3.4-5.7-6s-2.1-5.8-2.1-9.6v-1.6c0-3.8.7-7,2.2-9.6s3.5-4.7,6-6c2.5-1.4,5.4-2.1,8.7-2.1h.9c3,0,5.7.6,7.9,1.8,2.3,1.2,4,3,5.3,5.3,1.3,2.3,1.9,4.9,1.9,8v4.4h-24.4c0,2.5.3,4.6,1,6.4.6,1.8,1.6,3.2,2.9,4.1s2.9,1.4,4.8,1.4,3-.3,4.2-.9c1.2-.6,2.2-1.6,2.9-2.8.7-1.3,1.1-2.8,1.1-4.7h7.6c0,2.8-.7,5.2-2.1,7.3-1.4,2.1-3.3,3.7-5.6,4.9-2.4,1.2-5.1,1.7-8.1,1.7h-.7ZM547.2,157.6h16.4c0-3.2-.7-5.6-2.1-7.2-1.4-1.6-3.4-2.3-5.8-2.3s-4.3.8-5.9,2.4-2.5,4-2.6,7.1Z"
        />
        <path
          className="cls-1"
          d="M580.4,178.9v-35.6h5.9l1,5.2c1.3-2,3-3.4,5-4.4,2-1,4.2-1.5,6.7-1.5s4.6.5,6.5,1.4c1.9.9,3.3,2.3,4.4,4.1s1.6,4,1.6,6.6v24.2h-8.1v-22.8c0-2.6-.6-4.5-1.8-5.7s-2.9-1.7-5.1-1.7-2.8.4-4,1.1-2.2,1.7-2.9,2.9-1.1,2.6-1.1,4.1v22.1h-8.1Z"
        />
        <path
          className="cls-1"
          d="M622.4,178.9v-47.8h8.6v41.1h20.9l1,6.7h-30.6Z"
        />
        <path
          className="cls-1"
          d="M668.5,179.6c-1.9,0-3.7-.3-5.5-.9-1.7-.6-3.2-1.6-4.3-3.1-1.1-1.5-1.7-3.4-1.7-6s.6-4.6,1.7-6.2c1.1-1.6,2.8-2.8,4.8-3.6,2.1-.8,4.6-1.4,7.5-1.7,2.9-.3,6.2-.5,9.7-.5v-3.3c0-2.3-.6-4-1.8-4.9-1.2-1-3-1.4-5.4-1.4s-3.8.4-5.3,1.3c-1.5.9-2.3,2.3-2.3,4.2v.7h-7.8c0-.2,0-.4,0-.7v-.7c0-2,.6-3.8,1.9-5.4s3.1-2.7,5.3-3.6c2.3-.8,4.9-1.3,7.8-1.3h1.2c4.8,0,8.4,1,10.8,2.9,2.4,1.9,3.6,4.6,3.6,8v18.2c0,.9.2,1.5.6,1.9.4.4.9.6,1.4.6s1,0,1.5-.3c.5-.2,1-.4,1.5-.6v4.9c-.6.5-1.4.8-2.3,1.1-.9.3-2,.4-3.3.4s-2.8-.3-3.8-.8c-1-.6-1.8-1.3-2.3-2.3-.5-1-.8-2-.9-3.2-1.3,2-3.1,3.6-5.2,4.7-2.1,1.1-4.6,1.7-7.5,1.7ZM671.9,174.1c1.5,0,2.9-.3,4.2-1,1.3-.7,2.5-1.7,3.3-3,.9-1.3,1.3-3,1.3-5.1v-2.7c-3.3,0-6,.2-8.4.5-2.3.3-4.1,1-5.3,1.9-1.2.9-1.8,2.3-1.8,4.2s.6,3.2,1.7,4c1.1.8,2.7,1.2,4.9,1.2Z"
        />
        <path
          className="cls-1"
          d="M718.9,179.6c-5.2,0-9.2-2.1-12-6.3l-2.8,5.6h-3v-50.1h8.1v18.9c.9-1.3,2.3-2.5,4.1-3.6,1.8-1,3.9-1.5,6.3-1.5s4.9.6,7,1.8c2.1,1.2,3.8,3,5,5.5,1.3,2.5,1.9,5.7,1.9,9.6v1.6c0,4.3-.7,7.8-2,10.5-1.3,2.7-3.1,4.8-5.3,6.1-2.2,1.3-4.7,2-7.3,2ZM717.3,174.1c1.5,0,2.8-.4,4-1.1,1.2-.7,2.1-1.9,2.8-3.5.7-1.6,1-3.6,1-6.2v-4.9c0-2.3-.3-4.2-1-5.6-.7-1.5-1.5-2.6-2.6-3.3s-2.4-1.1-3.9-1.1-2.9.4-4.1,1.1-2.3,1.9-3,3.4c-.8,1.5-1.1,3.4-1.1,5.7v4.9c0,2.6.4,4.6,1.1,6.2s1.7,2.7,2.9,3.4,2.6,1.1,4.1,1.1Z"
        />
      </g>
    </Svg>
  ),
  'gv-logo-fr': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 719.42 240.99"
      width="719.42px"
      height="240.99px"
      {...props}
    >
      <g>
        <g>
          <path
            className="cls-1"
            d="M0,64.5h13c.6,6.9,2.7,11.1,16.9,11.1s16.5-4.2,16.5-7.3c0-5.2-4.7-6.3-15.6-7.7C9.3,57.9.9,56.5.9,47s3.5-8,10.1-9.5c-6.8-3.5-9-9.1-9-15.8C2.1.8,23.5.8,31.3.8h26.2v5.9l-8.4,2.4c3.6,2,6.6,5.7,6.6,14.2s-4.9,19.1-26.1,19.7c-12.3.3-16.2.5-16.2,3.3s5.6,3.4,17.9,5c12.8,1.7,27.9,3,27.9,16.3s-5.9,17.8-30.5,17.8S0,77.4,0,64.5ZM28.9,34.2c11.3,0,14-4.9,14-12.1s-2.3-11.1-14-11.1-14.1,4.5-14.1,11.4,2.4,11.8,14.1,11.8Z"
          />
          <path
            className="cls-1"
            d="M64.7,30.6c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.4-14.8-29.4-30.6ZM94.2,51.1c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5Z"
          />
          <path
            className="cls-1"
            d="M62.5,85.5h13.6l17.7,49.1,18.2-49.1h14.1l-25.3,59.6h-14.2l-24.1-59.6Z"
          />
          <path
            className="cls-1"
            d="M128,115.3c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.4-14.8-29.4-30.6ZM157.5,135.8c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5Z"
          />
          <path
            className="cls-1"
            d="M225.1,135.2c-13.2,0-16.6-8.7-16.6-20s3.6-20.4,16.4-20.4,12.8,4.2,14.1,11.7h13c-1-13.4-9.6-21.9-26.7-21.9s-29.5,14.8-29.5,30.6,6.2,30.6,29.7,30.6,25.3-8.2,26.4-21.1h-13c-1.4,6.6-5.1,10.3-13.7,10.3Z"
          />
          <path
            className="cls-1"
            d="M260.5,127.6c0-10.9,7.8-15.8,24.8-17.6l18.7-1.9c-.3-9.8-3.5-13.6-15.5-13.6s-14.1,4.4-14.1,11.9h-12.1c0-12.2,5.1-21.8,26.1-21.8s27.8,10.5,27.8,26.5v33.9h-6.8l-2.6-9c-2.8,4.7-11.1,9.8-23.4,9.8s-23-5.7-23-18.3ZM286.7,136.1c12,0,17.3-6.3,17.3-16.6v-1.9l-16.9,1.9c-10.4,1.2-13.9,3.4-13.9,8s4.7,8.6,13.4,8.6Z"
          />
          <path className="cls-1" d="M328,62.8h12.5v82.3h-12.5V62.8Z" />
        </g>
        <polygon
          className="cls-1"
          points="352.3 156.5 349.4 166.5 234.7 166.5 234.7 166.5 218 166.5 188.4 200.4 188.4 166.5 183.7 166.5 183.7 166.5 128 166.5 128 178.9 175.9 178.9 175.9 219.6 188.1 219.6 223.7 178.9 359.6 178.9 359.6 168.7 359.6 166.5 359.6 156.5 352.3 156.5"
        />
      </g>
      <g>
        <path
          className="cls-1"
          d="M400,109.7l18.2-48h10.1l18.1,48h-9.1l-4-10.4h-20.9l-4,10.4h-8.5ZM414.6,93h16.3l-8-21.8h-.3l-8,21.8Z"
        />
        <path
          className="cls-1"
          d="M452,109.7v-35.8h5.9l1,5.2c1.4-2,3-3.4,5-4.4,2-1,4.2-1.5,6.7-1.5s4.6.5,6.5,1.4c1.9.9,3.3,2.3,4.4,4.1,1,1.8,1.6,4,1.6,6.6v24.4h-8.1v-22.9c0-2.7-.6-4.6-1.9-5.7-1.2-1.1-3-1.7-5.1-1.7s-2.8.4-4,1.1-2.2,1.7-2.9,2.9c-.7,1.2-1.1,2.6-1.1,4.1v22.2h-8.1Z"
        />
        <path
          className="cls-1"
          d="M507.9,110.4c-3,0-5.7-.7-8-2.2-2.4-1.5-4.2-3.5-5.6-6.2-1.4-2.7-2-5.7-2-9.2v-1.9c0-2.7.4-5.1,1.3-7.2.8-2.1,2-4,3.5-5.6s3.2-2.8,5.1-3.6c2-.8,4.1-1.3,6.3-1.3h1c3,0,5.7.6,7.9,1.8,2.2,1.2,4,2.9,5.2,5.1,1.2,2.2,1.9,4.8,1.9,7.8h-7.2c0-2.1-.3-3.8-1-5.1-.7-1.4-1.6-2.4-2.8-3.1-1.2-.7-2.6-1-4.2-1-2.4,0-4.4.8-6.1,2.5s-2.4,4.1-2.4,7.4v6.2c0,3.3.8,5.8,2.3,7.5,1.6,1.7,3.6,2.6,6.2,2.6s3.1-.3,4.4-.9c1.2-.6,2.2-1.6,2.9-3,.7-1.4,1-3.1,1-5.3h6.9c0,3.1-.7,5.8-2,8s-3.2,3.9-5.5,5c-2.3,1.1-5,1.7-7.9,1.7h-1.1Z"
        />
        <path
          className="cls-1"
          d="M533,65.9v-6.6h8.5v6.6h-8.5ZM533.2,109.7v-35.8h8.1v35.8h-8.1Z"
        />
        <path
          className="cls-1"
          d="M567,110.4c-3.3,0-6.1-.7-8.6-2.1-2.5-1.4-4.4-3.4-5.8-6.1-1.4-2.7-2.1-5.9-2.1-9.7v-1.6c0-3.8.7-7,2.2-9.7,1.5-2.7,3.5-4.7,6-6.1,2.5-1.4,5.4-2.1,8.7-2.1h.9c3,0,5.7.6,8,1.9,2.3,1.2,4.1,3,5.3,5.3,1.3,2.3,1.9,5,1.9,8v4.4h-24.6c0,2.5.3,4.6,1,6.4s1.6,3.2,2.9,4.2c1.3,1,2.9,1.4,4.8,1.4s3-.3,4.2-.9c1.2-.6,2.2-1.6,2.9-2.8.7-1.3,1.1-2.8,1.1-4.7h7.6c0,2.8-.7,5.2-2.1,7.3-1.4,2.1-3.3,3.7-5.7,4.9-2.4,1.2-5.1,1.8-8.1,1.8h-.7ZM559.1,88.4h16.5c0-3.3-.7-5.7-2.1-7.2-1.4-1.6-3.4-2.3-5.8-2.3s-4.3.8-5.9,2.4c-1.6,1.6-2.5,4-2.6,7.2Z"
        />
        <path
          className="cls-1"
          d="M592.5,109.7v-35.8h5.9l1,5.2c1.4-2,3-3.4,5-4.4,2-1,4.2-1.5,6.7-1.5s4.6.5,6.5,1.4c1.9.9,3.3,2.3,4.4,4.1,1,1.8,1.6,4,1.6,6.6v24.4h-8.1v-22.9c0-2.7-.6-4.6-1.9-5.7-1.2-1.1-3-1.7-5.1-1.7s-2.8.4-4,1.1-2.2,1.7-2.9,2.9c-.7,1.2-1.1,2.6-1.1,4.1v22.2h-8.1Z"
        />
        <path
          className="cls-1"
          d="M634.2,109.7v-35.8h5.9l1,5.2c1.4-2,3-3.4,5-4.4,2-1,4.2-1.5,6.7-1.5s4.6.5,6.5,1.4c1.9.9,3.3,2.3,4.4,4.1,1,1.8,1.6,4,1.6,6.6v24.4h-8.1v-22.9c0-2.7-.6-4.6-1.9-5.7-1.2-1.1-3-1.7-5.1-1.7s-2.8.4-4,1.1-2.2,1.7-2.9,2.9c-.7,1.2-1.1,2.6-1.1,4.1v22.2h-8.1Z"
        />
        <path
          className="cls-1"
          d="M690.7,110.4c-3.3,0-6.1-.7-8.6-2.1-2.5-1.4-4.4-3.4-5.8-6.1-1.4-2.7-2.1-5.9-2.1-9.7v-1.6c0-3.8.7-7,2.2-9.7,1.5-2.7,3.5-4.7,6-6.1,2.5-1.4,5.4-2.1,8.7-2.1h.9c3,0,5.7.6,8,1.9,2.3,1.2,4.1,3,5.3,5.3,1.3,2.3,1.9,5,1.9,8v4.4h-24.6c0,2.5.3,4.6,1,6.4s1.6,3.2,2.9,4.2c1.3,1,2.9,1.4,4.8,1.4s3-.3,4.2-.9c1.2-.6,2.2-1.6,2.9-2.8.7-1.3,1.1-2.8,1.1-4.7h7.6c0,2.8-.7,5.2-2.1,7.3-1.4,2.1-3.3,3.7-5.7,4.9-2.4,1.2-5.1,1.8-8.1,1.8h-.7ZM682.8,88.4h16.5c0-3.3-.7-5.7-2.1-7.2-1.4-1.6-3.4-2.3-5.8-2.3s-4.3.8-5.9,2.4c-1.6,1.6-2.5,4-2.6,7.2Z"
        />
        <path
          className="cls-1"
          d="M716.2,109.7v-35.8h6.1l1.1,5.2c1.2-1.9,2.7-3.3,4.3-4.4,1.7-1,3.7-1.6,5.9-1.6s4.4.5,6.2,1.4c1.8.9,3.2,2.4,4.3,4.4,1.1-1.8,2.5-3.2,4.3-4.2s3.9-1.6,6.4-1.6,4.3.5,6.1,1.4c1.8.9,3.2,2.3,4.2,4.1,1,1.8,1.5,4,1.5,6.6v24.4h-8.1v-22.6c0-2.7-.5-4.7-1.6-5.8s-2.6-1.8-4.6-1.8-2.5.3-3.5,1c-1,.7-1.8,1.6-2.4,2.8-.6,1.2-.9,2.6-.9,4.1v22.3h-8.1v-22.6c0-2.7-.5-4.7-1.6-5.8-1.1-1.2-2.6-1.8-4.7-1.8s-2.5.3-3.5,1c-1,.7-1.8,1.6-2.4,2.8-.6,1.2-.9,2.6-.9,4.1v22.3h-8.1Z"
        />
        <path
          className="cls-1"
          d="M791.9,110.4c-3.3,0-6.1-.7-8.6-2.1-2.5-1.4-4.4-3.4-5.8-6.1-1.4-2.7-2.1-5.9-2.1-9.7v-1.6c0-3.8.7-7,2.2-9.7,1.5-2.7,3.5-4.7,6-6.1,2.5-1.4,5.4-2.1,8.7-2.1h.9c3,0,5.7.6,8,1.9,2.3,1.2,4.1,3,5.3,5.3,1.3,2.3,1.9,5,1.9,8v4.4h-24.6c0,2.5.3,4.6,1,6.4s1.6,3.2,2.9,4.2c1.3,1,2.9,1.4,4.8,1.4s3-.3,4.2-.9c1.2-.6,2.2-1.6,2.9-2.8.7-1.3,1.1-2.8,1.1-4.7h7.6c0,2.8-.7,5.2-2.1,7.3-1.4,2.1-3.3,3.7-5.7,4.9-2.4,1.2-5.1,1.8-8.1,1.8h-.7ZM784,88.4h16.5c0-3.3-.7-5.7-2.1-7.2-1.4-1.6-3.4-2.3-5.8-2.3s-4.3.8-5.9,2.4c-1.6,1.6-2.5,4-2.6,7.2Z"
        />
        <path
          className="cls-1"
          d="M817.4,109.7v-35.8h5.9l1,5.2c1.4-2,3-3.4,5-4.4,2-1,4.2-1.5,6.7-1.5s4.6.5,6.5,1.4c1.9.9,3.3,2.3,4.4,4.1,1,1.8,1.6,4,1.6,6.6v24.4h-8.1v-22.9c0-2.7-.6-4.6-1.9-5.7-1.2-1.1-3-1.7-5.1-1.7s-2.8.4-4,1.1-2.2,1.7-2.9,2.9c-.7,1.2-1.1,2.6-1.1,4.1v22.2h-8.1Z"
        />
        <path
          className="cls-1"
          d="M870.1,110.4c-1.9,0-3.5-.3-5-.8-1.5-.6-2.6-1.5-3.4-2.8-.8-1.3-1.2-3-1.2-5.1v-21.6h-3.9v-6.2h4.1l2.9-11.4h5v11.4h8.3v6.2h-8.3v19.9c0,2.1.4,3.4,1.3,3.9.8.6,1.7.8,2.7.8s1.2-.1,2.1-.4c.9-.2,1.6-.5,2.2-.7v5.2c-.6.3-1.2.5-2,.7s-1.6.4-2.4.5c-.8,0-1.6.1-2.3.1Z"
        />
        <path
          className="cls-1"
          d="M423.3,179.9c-2.8,0-5.3-.4-7.7-1.2-2.4-.8-4.5-2.1-6.3-3.9s-3.2-4.1-4.2-7c-1-2.9-1.5-6.4-1.5-10.5v-4.3c0-4.1.5-7.5,1.5-10.4,1-2.9,2.4-5.2,4.2-7,1.8-1.8,3.9-3.1,6.2-3.9,2.4-.8,4.9-1.2,7.6-1.2h1c2.5,0,4.9.4,7.2,1,2.3.7,4.3,1.8,6,3.3,1.7,1.5,3.1,3.5,4.1,6,1,2.4,1.5,5.4,1.5,8.9h-8.2c0-3.3-.5-5.9-1.4-7.7-1-1.8-2.3-3.1-4-3.8-1.7-.7-3.6-1.1-5.7-1.1s-3.1.2-4.4.7c-1.4.5-2.5,1.2-3.5,2.3-1,1.1-1.7,2.5-2.3,4.3-.5,1.8-.8,4-.8,6.7v8c0,3.5.5,6.3,1.4,8.4.9,2,2.2,3.5,3.9,4.3,1.7.9,3.6,1.3,5.7,1.3s4.3-.4,6-1.1,3-2,4-3.8c1-1.8,1.4-4.3,1.4-7.6h7.9c0,3.4-.5,6.3-1.5,8.7-1,2.4-2.3,4.4-4,5.9-1.7,1.5-3.7,2.7-5.9,3.4-2.2.7-4.6,1.1-7.1,1.1h-1Z"
        />
        <path
          className="cls-1"
          d="M451.9,135.4v-6.6h8.5v6.6h-8.5ZM452.2,179.2v-35.8h8.1v35.8h-8.1Z"
        />
        <path
          className="cls-1"
          d="M482.3,179.9c-1.9,0-3.5-.3-5-.8-1.5-.6-2.6-1.5-3.4-2.8-.8-1.3-1.2-3-1.2-5.1v-21.6h-3.9v-6.2h4.1l2.9-11.4h5v11.4h8.3v6.2h-8.3v19.9c0,2.1.4,3.4,1.3,3.9.8.6,1.7.8,2.7.8s1.2-.1,2.1-.4c.9-.2,1.6-.5,2.2-.7v5.2c-.6.3-1.2.5-2,.7s-1.6.4-2.4.5c-.8,0-1.6.1-2.3.1Z"
        />
        <path
          className="cls-1"
          d="M497.8,135.4v-6.6h8.5v6.6h-8.5ZM498.1,179.2v-35.8h8.1v35.8h-8.1Z"
        />
        <path
          className="cls-1"
          d="M514.5,179.2v-2.9l16.7-27.2h-15.7v-5.7h26.5v2.8l-16.7,27.3h17.2v5.7h-28.1Z"
        />
        <path
          className="cls-1"
          d="M565.2,179.9c-3.3,0-6.1-.7-8.6-2.1-2.5-1.4-4.4-3.4-5.8-6.1-1.4-2.7-2.1-5.9-2.1-9.7v-1.6c0-3.8.7-7,2.2-9.7,1.5-2.7,3.5-4.7,6-6.1,2.5-1.4,5.4-2.1,8.7-2.1h.9c3,0,5.7.6,8,1.9,2.3,1.2,4.1,3,5.3,5.3,1.3,2.3,1.9,5,1.9,8v4.4h-24.6c0,2.5.3,4.6,1,6.4s1.6,3.2,2.9,4.2c1.3,1,2.9,1.4,4.8,1.4s3-.3,4.2-.9c1.2-.6,2.2-1.6,2.9-2.8.7-1.3,1.1-2.8,1.1-4.7h7.6c0,2.8-.7,5.2-2.1,7.3-1.4,2.1-3.3,3.7-5.7,4.9-2.4,1.2-5.1,1.8-8.1,1.8h-.7ZM557.3,157.8h16.5c0-3.3-.7-5.7-2.1-7.2-1.4-1.6-3.4-2.3-5.8-2.3s-4.3.8-5.9,2.4c-1.6,1.6-2.5,4-2.6,7.2Z"
        />
        <path
          className="cls-1"
          d="M590.6,179.2v-35.8h5.9l1,5.2c1.4-2,3-3.4,5-4.4,2-1,4.2-1.5,6.7-1.5s4.6.5,6.5,1.4c1.9.9,3.3,2.3,4.4,4.1,1,1.8,1.6,4,1.6,6.6v24.4h-8.1v-22.9c0-2.7-.6-4.6-1.9-5.7-1.2-1.1-3-1.7-5.1-1.7s-2.8.4-4,1.1-2.2,1.7-2.9,2.9c-.7,1.2-1.1,2.6-1.1,4.1v22.2h-8.1Z"
        />
        <path className="cls-1" d="M632.9,179.2v-48h8.7v41.3h21l1,6.7h-30.7Z" />
        <path
          className="cls-1"
          d="M679.2,179.9c-1.9,0-3.7-.3-5.5-.9-1.8-.6-3.2-1.6-4.3-3.1-1.1-1.5-1.7-3.5-1.7-6s.6-4.6,1.7-6.2,2.8-2.8,4.9-3.6c2.1-.8,4.6-1.4,7.6-1.7,2.9-.3,6.2-.5,9.8-.5v-3.4c0-2.3-.6-4-1.8-4.9-1.2-1-3-1.4-5.4-1.4s-3.8.4-5.3,1.3c-1.5.9-2.3,2.3-2.3,4.2v.7h-7.8c0-.2,0-.4,0-.7v-.7c0-2.1.6-3.8,1.9-5.4,1.3-1.5,3.1-2.7,5.4-3.6,2.3-.8,4.9-1.3,7.8-1.3h1.2c4.9,0,8.5,1,10.8,2.9,2.4,1.9,3.6,4.6,3.6,8v18.3c0,.9.2,1.5.6,1.9.4.4.9.6,1.4.6s1,0,1.5-.3c.5-.2,1-.4,1.5-.6v5c-.6.5-1.4.8-2.3,1.1-.9.3-2.1.4-3.4.4s-2.8-.3-3.8-.8c-1-.6-1.8-1.3-2.3-2.3-.5-1-.8-2-.9-3.3-1.4,2-3.1,3.6-5.2,4.7-2.1,1.1-4.7,1.7-7.6,1.7ZM682.7,174.3c1.5,0,2.9-.3,4.3-1s2.5-1.7,3.4-3c.9-1.4,1.3-3.1,1.3-5.1v-2.7c-3.3,0-6.1.2-8.4.5-2.3.3-4.1,1-5.3,1.9s-1.8,2.3-1.8,4.2.6,3.2,1.7,4c1.1.8,2.8,1.2,4.9,1.2Z"
        />
        <path
          className="cls-1"
          d="M729.8,179.9c-5.3,0-9.3-2.1-12-6.4l-2.8,5.7h-3v-50.4h8.1v19c.9-1.4,2.3-2.5,4.1-3.6,1.8-1,3.9-1.5,6.3-1.5s4.9.6,7,1.8c2.1,1.2,3.8,3,5.1,5.5,1.3,2.5,1.9,5.7,1.9,9.6v1.6c0,4.3-.7,7.8-2,10.6-1.3,2.8-3.1,4.8-5.3,6.1-2.2,1.3-4.7,2-7.4,2ZM728.2,174.3c1.5,0,2.8-.4,4-1.1,1.2-.7,2.1-1.9,2.8-3.5.7-1.6,1-3.7,1-6.2v-5c0-2.3-.3-4.2-1-5.7-.7-1.5-1.5-2.6-2.7-3.4-1.1-.7-2.4-1.1-3.9-1.1s-2.9.4-4.1,1.1c-1.3.7-2.3,1.9-3,3.4-.8,1.5-1.2,3.4-1.2,5.7v5c0,2.6.4,4.6,1.1,6.2s1.7,2.7,2.9,3.5,2.6,1.1,4.1,1.1Z"
        />
      </g>
    </Svg>
  ),
  'gv-logo-nl': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 719.42 240.99"
      width="719.42px"
      height="240.99px"
      {...props}
    >
      <g>
        <g>
          <path
            className="cls-1"
            d="M0,64.5h13c.6,6.9,2.7,11.1,16.9,11.1s16.5-4.2,16.5-7.3c0-5.2-4.7-6.3-15.6-7.7C9.3,57.9.9,56.5.9,47s3.5-8,10.1-9.5c-6.8-3.5-9-9.1-9-15.8C2.1.8,23.5.8,31.3.8h26.2v5.9l-8.4,2.4c3.6,2,6.6,5.7,6.6,14.2s-4.9,19.1-26.1,19.7c-12.3.3-16.2.5-16.2,3.3s5.6,3.4,17.9,5c12.8,1.7,27.9,3,27.9,16.3s-5.9,17.8-30.5,17.8S0,77.4,0,64.5ZM28.9,34.2c11.3,0,14-4.9,14-12.1s-2.3-11.1-14-11.1-14.1,4.5-14.1,11.4,2.4,11.8,14.1,11.8Z"
          />
          <path
            className="cls-1"
            d="M64.7,30.6c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.4-14.8-29.4-30.6ZM94.2,51.1c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5Z"
          />
          <path
            className="cls-1"
            d="M62.5,85.5h13.6l17.7,49.1,18.2-49.1h14.1l-25.3,59.6h-14.2l-24.1-59.6Z"
          />
          <path
            className="cls-1"
            d="M128,115.3c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.4-14.8-29.4-30.6ZM157.5,135.8c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5Z"
          />
          <path
            className="cls-1"
            d="M225.1,135.2c-13.2,0-16.6-8.7-16.6-20s3.6-20.4,16.4-20.4,12.8,4.2,14.1,11.7h13c-1-13.4-9.6-21.9-26.7-21.9s-29.5,14.8-29.5,30.6,6.2,30.6,29.7,30.6,25.3-8.2,26.4-21.1h-13c-1.4,6.6-5.1,10.3-13.7,10.3Z"
          />
          <path
            className="cls-1"
            d="M260.5,127.6c0-10.9,7.8-15.8,24.8-17.6l18.7-1.9c-.3-9.8-3.5-13.6-15.5-13.6s-14.1,4.4-14.1,11.9h-12.1c0-12.2,5.1-21.8,26.1-21.8s27.8,10.5,27.8,26.5v33.9h-6.8l-2.6-9c-2.8,4.7-11.1,9.8-23.4,9.8s-23-5.7-23-18.3ZM286.7,136.1c12,0,17.3-6.3,17.3-16.6v-1.9l-16.9,1.9c-10.4,1.2-13.9,3.4-13.9,8s4.7,8.6,13.4,8.6Z"
          />
          <path className="cls-1" d="M328,62.8h12.5v82.3h-12.5V62.8Z" />
        </g>
        <polygon
          className="cls-1"
          points="352.3 156.5 349.4 166.5 234.7 166.5 234.7 166.5 218 166.5 188.4 200.4 188.4 166.5 183.7 166.5 183.7 166.5 128 166.5 128 178.9 175.9 178.9 175.9 219.6 188.1 219.6 223.7 178.9 359.6 178.9 359.6 168.7 359.6 166.5 359.6 156.5 352.3 156.5"
        />
      </g>
      <g>
        <path
          className="cls-1"
          d="M406.9,111.4l-15.9-46.5h8.8l12.1,35.5h.3l12.1-35.5h8.1l-16,46.5h-9.4Z"
        />
        <path
          className="cls-1"
          d="M451.2,112.1c-3.1,0-5.8-.7-8.3-2.1s-4.4-3.4-5.7-6c-1.4-2.6-2.1-5.7-2.1-9.3v-1.2c0-3.6.7-6.7,2.1-9.3,1.4-2.6,3.3-4.6,5.7-6s5.2-2.1,8.2-2.1h1.1c3.1,0,5.9.7,8.3,2.1,2.4,1.4,4.3,3.4,5.7,6,1.4,2.6,2.1,5.7,2.1,9.3v1.2c0,3.6-.7,6.7-2.1,9.3-1.4,2.6-3.3,4.6-5.7,6-2.4,1.4-5.2,2.1-8.2,2.1h-1.1ZM451.7,106.7c1.6,0,3-.4,4.2-1.1,1.2-.7,2.2-1.9,3-3.4.7-1.5,1.1-3.3,1.1-5.4v-5.5c0-2.2-.4-4-1.1-5.5-.7-1.5-1.7-2.6-3-3.3-1.2-.7-2.7-1.1-4.2-1.1s-2.9.4-4.2,1.1c-1.2.7-2.2,1.9-3,3.3-.7,1.5-1.1,3.3-1.1,5.5v5.5c0,2.1.4,3.9,1.1,5.4.7,1.5,1.7,2.6,3,3.4,1.2.7,2.6,1.1,4.2,1.1Z"
        />
        <path
          className="cls-1"
          d="M491.5,112.1c-3.1,0-5.8-.7-8.3-2.1s-4.4-3.4-5.7-6c-1.4-2.6-2.1-5.7-2.1-9.3v-1.2c0-3.6.7-6.7,2.1-9.3,1.4-2.6,3.3-4.6,5.7-6s5.2-2.1,8.2-2.1h1.1c3.1,0,5.9.7,8.3,2.1,2.4,1.4,4.3,3.4,5.7,6,1.4,2.6,2.1,5.7,2.1,9.3v1.2c0,3.6-.7,6.7-2.1,9.3-1.4,2.6-3.3,4.6-5.7,6-2.4,1.4-5.2,2.1-8.2,2.1h-1.1ZM491.9,106.7c1.6,0,3-.4,4.2-1.1,1.2-.7,2.2-1.9,3-3.4.7-1.5,1.1-3.3,1.1-5.4v-5.5c0-2.2-.4-4-1.1-5.5-.7-1.5-1.7-2.6-3-3.3-1.2-.7-2.7-1.1-4.2-1.1s-2.9.4-4.2,1.1c-1.2.7-2.2,1.9-3,3.3-.7,1.5-1.1,3.3-1.1,5.5v5.5c0,2.1.4,3.9,1.1,5.4.7,1.5,1.7,2.6,3,3.4,1.2.7,2.6,1.1,4.2,1.1Z"
        />
        <path
          className="cls-1"
          d="M517.1,111.4v-34.7h5.9l1,5.1c0-.1.3-.5.7-1.1s.9-1.2,1.7-2,1.7-1.4,2.9-1.9,2.6-.8,4.3-.8c2.4,0,4.4.5,5.9,1.4,1.5.9,2.6,2.3,3.4,3.9s1.1,3.5,1.1,5.7,0,1,0,1.5c0,.5,0,1-.1,1.3h-6.6v-1.4c0-1-.2-1.9-.5-2.9-.4-1-1-1.8-1.8-2.5s-2.1-1-3.6-1-2.1.2-2.9.7c-.8.5-1.4,1.1-1.9,1.8-.5.7-.8,1.5-1.1,2.3-.2.8-.3,1.6-.3,2.3v22.1h-7.9Z"
        />
        <path
          className="cls-1"
          d="M550.6,111.4v-48.8h7.9v18.7c1.3-1.7,2.8-3,4.6-3.9,1.8-.9,3.8-1.3,5.9-1.3s4.4.5,6.1,1.4c1.8.9,3.2,2.2,4.2,4s1.6,3.9,1.6,6.4v23.6h-7.9v-22.2c0-2.6-.6-4.4-1.8-5.5s-2.9-1.7-5-1.7-2.7.4-3.9,1.1-2.1,1.6-2.8,2.8-1.1,2.5-1.1,4v21.5h-7.9Z"
        />
        <path
          className="cls-1"
          d="M605.4,112.1c-3.2,0-5.9-.7-8.3-2-2.4-1.3-4.3-3.3-5.6-5.9s-2-5.7-2-9.4v-1.6c0-3.7.7-6.8,2.1-9.4s3.4-4.5,5.8-5.9c2.5-1.3,5.3-2,8.4-2h.9c2.9,0,5.5.6,7.7,1.8,2.2,1.2,3.9,2.9,5.2,5.1,1.2,2.2,1.8,4.8,1.8,7.8v4.3h-23.8c0,2.4.3,4.5.9,6.2.6,1.8,1.6,3.1,2.8,4s2.8,1.4,4.6,1.4,2.9-.3,4.1-.9c1.2-.6,2.1-1.5,2.8-2.7.7-1.2,1.1-2.7,1.1-4.5h7.4c0,2.7-.7,5.1-2,7.1-1.4,2-3.2,3.6-5.5,4.7-2.3,1.1-4.9,1.7-7.9,1.7h-.7ZM597.8,90.7h16c0-3.2-.7-5.5-2.1-7-1.4-1.5-3.3-2.3-5.7-2.3s-4.2.8-5.7,2.3-2.4,3.9-2.5,7Z"
        />
        <path
          className="cls-1"
          d="M644.5,112.1c-3.2,0-5.9-.7-8.3-2-2.4-1.3-4.3-3.3-5.6-5.9s-2-5.7-2-9.4v-1.6c0-3.7.7-6.8,2.1-9.4s3.4-4.5,5.8-5.9c2.5-1.3,5.3-2,8.4-2h.9c2.9,0,5.5.6,7.7,1.8,2.2,1.2,3.9,2.9,5.2,5.1,1.2,2.2,1.8,4.8,1.8,7.8v4.3h-23.8c0,2.4.3,4.5.9,6.2.6,1.8,1.6,3.1,2.8,4s2.8,1.4,4.6,1.4,2.9-.3,4.1-.9c1.2-.6,2.1-1.5,2.8-2.7.7-1.2,1.1-2.7,1.1-4.5h7.4c0,2.7-.7,5.1-2,7.1-1.4,2-3.2,3.6-5.5,4.7-2.3,1.1-4.9,1.7-7.9,1.7h-.7ZM636.8,90.7h16c0-3.2-.7-5.5-2.1-7-1.4-1.5-3.3-2.3-5.7-2.3s-4.2.8-5.7,2.3-2.4,3.9-2.5,7Z"
        />
        <path
          className="cls-1"
          d="M669.2,111.4v-34.7h5.8l1,5.1c1.3-1.9,2.9-3.3,4.9-4.3,1.9-1,4.1-1.5,6.5-1.5s4.5.5,6.3,1.4c1.8.9,3.2,2.2,4.2,4s1.5,3.9,1.5,6.4v23.6h-7.9v-22.2c0-2.6-.6-4.4-1.8-5.5s-2.9-1.7-5-1.7-2.7.4-3.9,1.1-2.1,1.6-2.8,2.8-1.1,2.5-1.1,4v21.5h-7.9Z"
        />
        <path
          className="cls-1"
          d="M413.4,179.4c-2.7,0-5.2-.4-7.5-1.2-2.3-.8-4.4-2-6.1-3.7s-3.1-4-4.1-6.8c-1-2.8-1.5-6.2-1.5-10.2v-4.2c0-3.9.5-7.3,1.5-10.1,1-2.8,2.4-5,4.1-6.8,1.7-1.7,3.8-3,6-3.8,2.3-.8,4.7-1.2,7.4-1.2h1c2.4,0,4.8.3,7,1,2.2.7,4.1,1.8,5.8,3.2,1.7,1.5,3,3.4,4,5.8,1,2.4,1.5,5.3,1.5,8.6h-7.9c0-3.2-.5-5.7-1.4-7.4-.9-1.7-2.2-3-3.8-3.7-1.6-.7-3.5-1.1-5.6-1.1s-3,.2-4.3.7c-1.3.5-2.4,1.2-3.4,2.2s-1.7,2.4-2.2,4.2c-.5,1.7-.8,3.9-.8,6.5v7.7c0,3.4.5,6.1,1.4,8.1.9,2,2.2,3.4,3.8,4.2,1.6.8,3.4,1.3,5.5,1.3s4.2-.3,5.8-1.1c1.7-.7,2.9-1.9,3.9-3.7.9-1.7,1.4-4.2,1.4-7.4h7.7c0,3.3-.5,6.1-1.4,8.4-1,2.4-2.3,4.3-3.9,5.8s-3.6,2.6-5.7,3.3c-2.2.7-4.5,1.1-6.9,1.1h-1Z"
        />
        <path
          className="cls-1"
          d="M441.2,136.2v-6.4h8.3v6.4h-8.3ZM441.4,178.7v-34.7h7.9v34.7h-7.9Z"
        />
        <path
          className="cls-1"
          d="M470.6,179.4c-1.8,0-3.4-.3-4.8-.8-1.4-.5-2.5-1.4-3.3-2.7-.8-1.2-1.2-2.9-1.2-4.9v-20.9h-3.8v-6h4l2.8-11.1h4.9v11.1h8.1v6h-8.1v19.3c0,2,.4,3.3,1.2,3.8.8.5,1.7.8,2.6.8s1.2-.1,2.1-.3c.9-.2,1.6-.5,2.1-.7v5.1c-.5.3-1.2.5-2,.7-.8.2-1.6.3-2.4.4-.8,0-1.6.1-2.2.1Z"
        />
        <path
          className="cls-1"
          d="M485.7,136.2v-6.4h8.3v6.4h-8.3ZM485.9,178.7v-34.7h7.9v34.7h-7.9Z"
        />
        <path
          className="cls-1"
          d="M501.8,178.7v-2.8l16.1-26.4h-15.2v-5.5h25.7v2.7l-16.1,26.5h16.7v5.5h-27.2Z"
        />
        <path
          className="cls-1"
          d="M551,179.4c-3.2,0-5.9-.7-8.3-2-2.4-1.3-4.3-3.3-5.6-5.9s-2-5.7-2-9.4v-1.6c0-3.7.7-6.8,2.1-9.4s3.4-4.5,5.8-5.9c2.5-1.3,5.3-2,8.4-2h.9c2.9,0,5.5.6,7.7,1.8,2.2,1.2,3.9,2.9,5.2,5.1,1.2,2.2,1.8,4.8,1.8,7.8v4.3h-23.8c0,2.4.3,4.5.9,6.2.6,1.8,1.6,3.1,2.8,4s2.8,1.4,4.6,1.4,2.9-.3,4.1-.9c1.2-.6,2.1-1.5,2.8-2.7.7-1.2,1.1-2.7,1.1-4.5h7.4c0,2.7-.7,5.1-2,7.1-1.4,2-3.2,3.6-5.5,4.7-2.3,1.1-4.9,1.7-7.9,1.7h-.7ZM543.3,158h16c0-3.2-.7-5.5-2.1-7-1.4-1.5-3.3-2.3-5.7-2.3s-4.2.8-5.7,2.3-2.4,3.9-2.5,7Z"
        />
        <path
          className="cls-1"
          d="M575.7,178.7v-34.7h5.8l1,5.1c1.3-1.9,2.9-3.3,4.9-4.3,1.9-1,4.1-1.5,6.5-1.5s4.5.5,6.3,1.4c1.8.9,3.2,2.2,4.2,4s1.5,3.9,1.5,6.4v23.6h-7.9v-22.2c0-2.6-.6-4.4-1.8-5.5s-2.9-1.7-5-1.7-2.7.4-3.9,1.1-2.1,1.6-2.8,2.8-1.1,2.5-1.1,4v21.5h-7.9Z"
        />
        <path
          className="cls-1"
          d="M616.6,178.7v-46.5h8.4v40h20.4l1,6.5h-29.8Z"
        />
        <path
          className="cls-1"
          d="M661.5,179.4c-1.9,0-3.6-.3-5.3-.9-1.7-.6-3.1-1.6-4.2-3-1.1-1.4-1.6-3.4-1.6-5.8s.6-4.5,1.7-6c1.1-1.5,2.7-2.7,4.7-3.5,2-.8,4.5-1.4,7.3-1.7,2.8-.3,6-.4,9.5-.4v-3.3c0-2.3-.6-3.9-1.7-4.8-1.2-.9-2.9-1.4-5.3-1.4s-3.7.4-5.2,1.3c-1.5.9-2.2,2.2-2.2,4.1v.7h-7.6c0-.2,0-.4,0-.6v-.7c0-2,.6-3.7,1.9-5.2s3-2.6,5.2-3.5c2.2-.8,4.7-1.2,7.6-1.2h1.2c4.7,0,8.2.9,10.5,2.8,2.3,1.9,3.5,4.5,3.5,7.8v17.7c0,.9.2,1.5.6,1.8.4.4.9.5,1.4.5s.9,0,1.5-.3c.5-.2,1-.4,1.5-.6v4.8c-.6.5-1.3.8-2.2,1.1-.9.2-2,.4-3.3.4s-2.7-.3-3.7-.8c-1-.5-1.7-1.3-2.2-2.2-.5-.9-.8-2-.9-3.2-1.3,1.9-3,3.5-5.1,4.5-2.1,1.1-4.5,1.6-7.3,1.6ZM664.9,174c1.4,0,2.8-.3,4.1-1,1.3-.7,2.4-1.6,3.3-3,.9-1.3,1.3-3,1.3-5v-2.6c-3.2,0-5.9.2-8.1.5-2.3.3-4,1-5.2,1.9-1.2.9-1.8,2.3-1.8,4.1s.5,3.1,1.6,3.9c1.1.8,2.7,1.2,4.7,1.2Z"
        />
        <path
          className="cls-1"
          d="M710.6,179.4c-5.1,0-9-2.1-11.7-6.2l-2.7,5.5h-2.9v-48.8h7.9v18.5c.9-1.3,2.2-2.5,4-3.5,1.7-1,3.8-1.5,6.1-1.5s4.8.6,6.8,1.7c2.1,1.2,3.7,2.9,4.9,5.4,1.2,2.4,1.8,5.5,1.8,9.3v1.6c0,4.2-.6,7.6-1.9,10.2-1.3,2.7-3,4.6-5.2,5.9-2.1,1.3-4.5,1.9-7.2,1.9ZM709,174c1.4,0,2.7-.4,3.9-1.1,1.1-.7,2-1.9,2.7-3.4.7-1.5,1-3.5,1-6v-4.8c0-2.2-.3-4-.9-5.5-.6-1.4-1.5-2.5-2.6-3.3s-2.4-1.1-3.8-1.1-2.8.4-4,1.1-2.2,1.8-3,3.3c-.7,1.5-1.1,3.3-1.1,5.5v4.8c0,2.5.4,4.5,1.1,6s1.7,2.7,2.8,3.4,2.5,1.1,4,1.1Z"
        />
      </g>
    </Svg>
  ),
  'gv-logo-en': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 719.42 240.99"
      width="719.42px"
      height="240.99px"
      {...props}
    >
      <path
        className="cls-1"
        d="M0,64.5h13c.6,6.9,2.7,11.1,16.9,11.1s16.5-4.2,16.5-7.3c0-5.2-4.7-6.3-15.6-7.7C9.3,57.8.9,56.5.9,47s3.5-8,10.1-9.5c-6.8-3.5-9-9.1-9-15.8C2.1.8,23.5.8,31.3.8h26.2v5.9l-8.4,2.4c3.6,2,6.6,5.7,6.6,14.2s-4.9,19.1-26.1,19.7c-12.3.3-16.2.5-16.2,3.3s5.6,3.4,17.9,5c12.8,1.8,27.9,3,27.9,16.3s-5.9,17.8-30.5,17.8S0,77.4,0,64.5h0ZM28.9,34.2c11.3,0,14-4.9,14-12.1s-2.3-11.1-14-11.1-14.1,4.5-14.1,11.4,2.4,11.8,14.1,11.8Z"
      />
      <path
        className="cls-1"
        d="M64.7,30.6c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.5-14.8-29.5-30.6h0ZM94.2,51.1c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5h0Z"
      />
      <path
        className="cls-1"
        d="M62.5,85.5h13.6l17.7,49.1,18.2-49.1h14.1l-25.3,59.6h-14.2l-24.1-59.6h0Z"
      />
      <path
        className="cls-1"
        d="M128,115.3c0-15.7,6.2-30.6,29.6-30.6s29.6,14.9,29.6,30.6-6.4,30.6-29.7,30.6-29.4-14.8-29.4-30.6h0ZM157.5,135.8c14,0,17-9,17-20.5s-2.8-20.5-16.9-20.5-16.9,9.3-16.9,20.5,3,20.5,16.8,20.5h0Z"
      />
      <path
        className="cls-1"
        d="M225.1,135.2c-13.2,0-16.6-8.7-16.6-20s3.6-20.4,16.4-20.4,12.8,4.2,14.1,11.8h12.9c-1-13.4-9.6-21.9-26.7-21.9s-29.5,14.8-29.5,30.6,6.2,30.6,29.7,30.6,25.2-8.2,26.4-21.1h-13c-1.4,6.6-5.1,10.3-13.7,10.3h0Z"
      />
      <path
        className="cls-1"
        d="M260.5,127.6c0-10.9,7.8-15.8,24.8-17.6l18.7-1.9c-.4-9.8-3.5-13.6-15.5-13.6s-14.1,4.4-14.1,11.9h-12.1c0-12.2,5.1-21.8,26.1-21.8s27.8,10.5,27.8,26.5v33.9h-6.8l-2.6-9c-2.8,4.7-11.1,9.8-23.4,9.8s-23-5.7-23-18.3h0ZM286.7,136.1c12,0,17.3-6.3,17.3-16.6v-1.9l-16.9,1.9c-10.4,1.2-13.9,3.4-13.9,8s4.7,8.6,13.4,8.6Z"
      />
      <path className="cls-1" d="M328,62.8h12.5v82.3h-12.5V62.8Z" />
      <polygon
        className="cls-1"
        points="352.3 156.5 349.4 166.5 234.7 166.5 234.7 166.5 218 166.5 188.4 200.4 188.4 166.5 183.7 166.5 183.7 166.5 128 166.5 128 178.9 175.9 178.9 175.9 219.6 188.1 219.6 223.7 178.9 359.6 178.9 359.6 168.7 359.6 166.5 359.6 156.5 352.3 156.5"
      />
      <path
        className="cls-1"
        d="M404.7,111.5v-47.4h30.8l-1,6.6h-21.3v13.8h18.8v6.6h-18.8v20.3h-8.6Z"
      />
      <path
        className="cls-1"
        d="M456.6,112.2c-3.1,0-5.9-.7-8.4-2.1s-4.4-3.5-5.8-6.2c-1.4-2.7-2.1-5.8-2.1-9.5v-1.2c0-3.7.7-6.9,2.1-9.5s3.3-4.7,5.8-6.1c2.5-1.4,5.3-2.1,8.4-2.1h1.1c3.2,0,6,.7,8.5,2.1,2.5,1.4,4.4,3.5,5.8,6.1,1.4,2.6,2.1,5.8,2.1,9.5v1.2c0,3.6-.7,6.8-2.1,9.5s-3.3,4.7-5.8,6.2c-2.5,1.4-5.3,2.1-8.4,2.1h-1.1ZM457.1,106.8c1.6,0,3.1-.4,4.3-1.1s2.3-1.9,3-3.4c.8-1.5,1.1-3.4,1.1-5.5v-5.6c0-2.2-.4-4.1-1.1-5.6-.8-1.5-1.8-2.6-3-3.4s-2.7-1.1-4.3-1.1-3,.4-4.3,1.1c-1.3.8-2.3,1.9-3,3.4-.8,1.5-1.1,3.4-1.1,5.6v5.6c0,2.2.4,4,1.1,5.5.8,1.5,1.8,2.7,3,3.4s2.7,1.1,4.3,1.1Z"
      />
      <path
        className="cls-1"
        d="M482.7,111.5v-35.3h6l1,5.2c0-.1.3-.5.7-1.1s.9-1.3,1.7-2,1.8-1.4,3-1.9c1.2-.6,2.6-.8,4.4-.8,2.4,0,4.4.5,6,1.4s2.7,2.3,3.5,4,1.1,3.6,1.1,5.8,0,1,0,1.6,0,1-.1,1.3h-6.8v-1.4c0-1-.2-2-.6-3s-1-1.9-1.9-2.6c-.9-.7-2.1-1-3.7-1s-2.1.2-2.9.7-1.5,1.1-2,1.8-.9,1.5-1.1,2.4c-.2.9-.3,1.7-.3,2.4v22.5h-8,0Z"
      />
      <path
        className="cls-1"
        d="M516.9,111.5v-35.3h6l1.1,5.2c1.2-1.8,2.6-3.3,4.3-4.3,1.7-1,3.6-1.6,5.9-1.6s4.4.5,6.2,1.3,3.2,2.4,4.2,4.4c1.1-1.7,2.5-3.1,4.3-4.2,1.8-1,3.9-1.6,6.3-1.6s4.3.5,6,1.4c1.7.9,3.1,2.3,4.1,4s1.5,4,1.5,6.5v24.1h-8v-22.3c0-2.7-.5-4.6-1.6-5.8s-2.6-1.8-4.6-1.8-2.4.3-3.5,1-1.8,1.6-2.4,2.8-.9,2.6-.9,4.1v22h-8v-22.3c0-2.7-.5-4.6-1.6-5.8s-2.6-1.8-4.6-1.8-2.4.3-3.5,1-1.8,1.6-2.4,2.8c-.6,1.2-.9,2.6-.9,4.1v22h-8,0Z"
      />
      <path
        className="cls-1"
        d="M591.7,112.2c-3.2,0-6.1-.7-8.5-2-2.4-1.4-4.3-3.4-5.7-6s-2-5.8-2-9.5v-1.6c0-3.7.7-6.9,2.2-9.5s3.4-4.6,5.9-6,5.4-2,8.6-2h.9c3,0,5.6.6,7.9,1.8s4,3,5.3,5.2,1.9,4.9,1.9,7.9v4.4h-24.3c0,2.4.3,4.6,1,6.4s1.6,3.2,2.9,4.1c1.3,1,2.8,1.4,4.7,1.4s3-.3,4.2-.9c1.2-.6,2.2-1.6,2.9-2.8s1.1-2.8,1.1-4.6h7.5c0,2.8-.7,5.2-2.1,7.3s-3.2,3.7-5.6,4.8c-2.4,1.2-5,1.7-8,1.7h-.7,0ZM583.9,90.5h16.3c0-3.2-.7-5.6-2.1-7.2-1.4-1.5-3.3-2.3-5.8-2.3s-4.3.8-5.8,2.4c-1.6,1.6-2.5,4-2.6,7.1Z"
      />
      <path
        className="cls-1"
        d="M616.9,111.5v-35.3h6l1,5.2c0-.1.3-.5.7-1.1s.9-1.3,1.7-2c.8-.7,1.8-1.4,3-1.9,1.2-.6,2.6-.8,4.4-.8,2.4,0,4.4.5,6,1.4,1.5,1,2.7,2.3,3.5,4,.8,1.7,1.1,3.6,1.1,5.8s0,1,0,1.6,0,1-.1,1.3h-6.8v-1.4c0-1-.2-2-.6-3s-1-1.9-1.9-2.6-2.1-1-3.7-1-2.1.2-2.9.7-1.5,1.1-2,1.8-.9,1.5-1.1,2.4c-.2.9-.3,1.7-.3,2.4v22.5h-8,0Z"
      />
      <path className="cls-1" d="M651,111.5v-49.8h8v49.8h-8Z" />
      <path
        className="cls-1"
        d="M673.2,124.4c-1.1,0-2,0-2.9-.2s-1.7-.4-2.5-.7v-6.2c.5.2,1.2.4,2,.6.8.2,1.6.2,2.4.2,1.2,0,2.2-.2,3-.7.8-.5,1.5-1.2,2.1-2.3.6-1.1,1.2-2.4,1.8-4.1l-13.8-34.8h8.6l9.1,24.8h.4l8.8-24.8h7.9l-13.1,33.7c-1,2.7-2.1,5.1-3.4,7.3-1.2,2.2-2.7,3.9-4.4,5.2s-3.7,1.9-6.2,1.9h0Z"
      />
      <path
        className="cls-1"
        d="M422.4,180.8c-2.7,0-5.3-.4-7.6-1.2s-4.4-2.1-6.2-3.8c-1.8-1.7-3.2-4-4.2-6.9-1-2.8-1.5-6.3-1.5-10.4v-4.3c0-4,.5-7.4,1.5-10.3s2.4-5.1,4.2-6.9c1.8-1.8,3.8-3.1,6.2-3.9s4.8-1.2,7.5-1.2h1c2.5,0,4.8.3,7.1,1s4.2,1.8,5.9,3.3,3.1,3.5,4,5.9,1.5,5.4,1.5,8.8h-8.1c0-3.3-.5-5.8-1.4-7.6-.9-1.8-2.2-3-3.9-3.7s-3.6-1.1-5.7-1.1-3,.2-4.4.7-2.5,1.2-3.5,2.3c-1,1.1-1.7,2.5-2.2,4.3-.5,1.8-.8,4-.8,6.6v7.9c0,3.5.5,6.2,1.4,8.3.9,2,2.2,3.4,3.8,4.3s3.5,1.3,5.6,1.3,4.2-.4,5.9-1.1c1.7-.7,3-2,3.9-3.7.9-1.8,1.4-4.3,1.4-7.5h7.8c0,3.3-.5,6.2-1.4,8.6s-2.3,4.4-4,5.9c-1.7,1.5-3.6,2.6-5.8,3.4-2.2.7-4.6,1.1-7,1.1h-1,0Z"
      />
      <path
        className="cls-1"
        d="M450.7,136.9v-6.5h8.4v6.5h-8.4ZM450.9,180.1v-35.3h8v35.3h-8Z"
      />
      <path
        className="cls-1"
        d="M480.7,180.8c-1.8,0-3.5-.3-4.9-.8s-2.6-1.5-3.4-2.7-1.2-2.9-1.2-5v-21.3h-3.9v-6.2h4.1l2.8-11.3h5v11.3h8.2v6.2h-8.2v19.7c0,2,.4,3.3,1.2,3.9.8.5,1.7.8,2.7.8s1.2-.1,2.1-.3c.9-.2,1.6-.5,2.2-.7v5.2c-.6.3-1.2.5-2,.7s-1.6.4-2.4.5-1.6.1-2.3.1h0Z"
      />
      <path
        className="cls-1"
        d="M496,136.9v-6.5h8.4v6.5h-8.4ZM496.2,180.1v-35.3h8v35.3h-8Z"
      />
      <path
        className="cls-1"
        d="M512.5,180.1v-2.8l16.5-26.9h-15.5v-5.6h26.2v2.8l-16.5,27h17v5.6h-27.7Z"
      />
      <path
        className="cls-1"
        d="M562.5,180.8c-3.2,0-6.1-.7-8.5-2-2.4-1.4-4.3-3.4-5.7-6-1.4-2.6-2-5.8-2-9.5v-1.6c0-3.7.7-6.9,2.2-9.5,1.4-2.6,3.4-4.6,5.9-6s5.4-2,8.6-2h.9c3,0,5.6.6,7.9,1.8s4,3,5.3,5.2,1.9,4.9,1.9,7.9v4.4h-24.3c0,2.4.3,4.6,1,6.4.6,1.8,1.6,3.2,2.9,4.1,1.3,1,2.8,1.4,4.7,1.4s3-.3,4.2-.9c1.2-.6,2.2-1.6,2.9-2.8s1.1-2.8,1.1-4.6h7.5c0,2.8-.7,5.2-2.1,7.3s-3.2,3.7-5.6,4.8c-2.4,1.2-5,1.7-8,1.7h-.7,0ZM554.7,159.1h16.3c0-3.2-.7-5.6-2.1-7.2s-3.3-2.3-5.8-2.3-4.3.8-5.8,2.4-2.5,4-2.6,7.1Z"
      />
      <path
        className="cls-1"
        d="M587.7,180.1v-35.3h5.9l1,5.2c1.3-1.9,3-3.4,5-4.4s4.2-1.5,6.6-1.5,4.6.5,6.4,1.4c1.8.9,3.3,2.3,4.3,4s1.6,4,1.6,6.5v24.1h-8v-22.6c0-2.6-.6-4.5-1.8-5.6-1.2-1.1-2.9-1.7-5.1-1.7s-2.7.4-3.9,1.1c-1.2.7-2.2,1.7-2.9,2.9-.7,1.2-1.1,2.6-1.1,4.1v21.9h-8,0Z"
      />
      <path
        className="cls-1"
        d="M629.4,180.1v-47.4h8.6v40.8h20.7l1,6.6h-30.3,0Z"
      />
      <path
        className="cls-1"
        d="M675.1,180.8c-1.9,0-3.7-.3-5.4-.9-1.7-.6-3.1-1.6-4.3-3.1s-1.7-3.4-1.7-5.9.6-4.6,1.7-6.2,2.7-2.8,4.8-3.6c2.1-.8,4.6-1.4,7.5-1.7,2.9-.3,6.1-.5,9.7-.5v-3.3c0-2.3-.6-3.9-1.8-4.9-1.2-.9-3-1.4-5.4-1.4s-3.7.4-5.3,1.3c-1.5.9-2.3,2.3-2.3,4.1v.7h-7.7c0-.2,0-.4,0-.7v-.7c0-2,.6-3.8,1.9-5.3,1.3-1.5,3-2.7,5.3-3.5s4.8-1.2,7.7-1.2h1.2c4.8,0,8.4.9,10.7,2.8,2.4,1.9,3.5,4.5,3.5,7.9v18c0,.9.2,1.5.6,1.9.4.4.9.5,1.4.5s1,0,1.5-.3,1-.4,1.5-.6v4.9c-.6.5-1.4.8-2.3,1.1-.9.2-2,.4-3.3.4s-2.8-.3-3.8-.8c-1-.6-1.7-1.3-2.2-2.2s-.8-2-.9-3.2c-1.3,2-3.1,3.5-5.2,4.6-2.1,1.1-4.6,1.7-7.5,1.7h0ZM678.6,175.4c1.5,0,2.9-.3,4.2-1,1.3-.7,2.4-1.7,3.3-3s1.3-3,1.3-5v-2.7c-3.2,0-6,.2-8.3.5-2.3.3-4.1,1-5.3,1.9s-1.8,2.3-1.8,4.1.5,3.2,1.7,4c1.1.8,2.7,1.2,4.8,1.2h0Z"
      />
      <path
        className="cls-1"
        d="M725.2,180.8c-5.2,0-9.2-2.1-11.9-6.3l-2.8,5.6h-3v-49.8h8v18.8c.9-1.3,2.3-2.5,4-3.5s3.9-1.5,6.3-1.5,4.8.6,7,1.8c2.1,1.2,3.8,3,5,5.5s1.9,5.6,1.9,9.5v1.6c0,4.2-.7,7.7-2,10.4s-3.1,4.7-5.3,6c-2.2,1.3-4.6,1.9-7.3,1.9ZM723.6,175.4c1.5,0,2.8-.4,3.9-1.1,1.2-.7,2.1-1.9,2.7-3.5s1-3.6,1-6.2v-4.9c0-2.3-.3-4.1-1-5.6s-1.5-2.6-2.6-3.3-2.4-1.1-3.9-1.1-2.8.4-4.1,1.1-2.2,1.9-3,3.4c-.8,1.5-1.1,3.4-1.1,5.6v4.9c0,2.5.4,4.6,1.1,6.2.7,1.6,1.7,2.7,2.9,3.4,1.2.7,2.6,1.1,4,1.1h0Z"
      />
    </Svg>
  ),
  'arrow-left-circle': (props: IconPropsWithoutName) => (
    <MirrorOnRtlSvg viewBox="0 0 24 24" {...props}>
      <path d="m12 16 1.4-1.4-1.6-1.6H16v-2h-4.2l1.6-1.6L12 8l-4 4Zm0 6q-2.075 0-3.9-.788-1.825-.787-3.175-2.137-1.35-1.35-2.137-3.175Q2 14.075 2 12t.788-3.9q.787-1.825 2.137-3.175 1.35-1.35 3.175-2.138Q9.925 2 12 2t3.9.787q1.825.788 3.175 2.138 1.35 1.35 2.137 3.175Q22 9.925 22 12t-.788 3.9q-.787 1.825-2.137 3.175-1.35 1.35-3.175 2.137Q14.075 22 12 22Z" />
    </MirrorOnRtlSvg>
  ),
  whatsapp: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M.057 24l1.687-6.163a11.867 11.867 0 01-1.587-5.946C.16 5.335 5.495 0 12.05 0a11.817 11.817 0 018.413 3.488 11.824 11.824 0 013.48 8.414c-.003 6.557-5.338 11.892-11.893 11.892a11.9 11.9 0 01-5.688-1.448L.057 24zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z" />
    </Svg>
  ),
  'sidebar-reporting': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 27 31"
      {...props}
    >
      <g className="Processing-icon">
        <path
          className="cl-icon-primary Head"
          d="M22.0671 21.8819C23.0755 17.9491 28.3528 16.0332 26.6721 8.0333C24.9579 0.0333888 15.5462 -0.773325 10.6723 0.470358C5.63035 1.78127 1.59679 7.15936 2.94131 11.6299C3.04215 11.966 2.97492 12.3358 2.77324 12.6383C2.16821 13.5795 0.487556 15.6635 0.0505859 16.8735C-0.117479 17.3105 0.151425 17.7811 0.588395 17.9155C0.722847 17.9492 1.59679 18.2853 1.73124 18.3189C2.9077 18.6886 2.6724 21.2768 2.6724 23.6298C2.6724 26.0835 5.29422 25.7474 6.13455 25.5793C7.00849 25.3776 8.92444 25.2096 8.92444 27.5625C8.92444 27.865 8.92444 28.1339 8.95805 28.3356C8.99166 28.8062 9.29418 29.2095 9.73115 29.3776C11.4454 30.0499 16.4538 31.0919 22.5041 28.907C22.8739 28.7726 23.0083 28.3692 22.8402 28.0331C22.3024 26.9575 21.294 24.8062 22.0671 21.8819ZM19.5126 5.41148C20.7226 5.57954 21.5629 6.68878 21.3613 7.89885C21.1932 9.10892 20.084 9.94924 18.8739 9.74756C17.6638 9.5795 16.8235 8.47027 17.0252 7.2602C17.1932 6.08374 18.3025 5.24341 19.5126 5.41148ZM15.0084 19.4954C10.3362 19.4954 6.53791 15.6971 6.53791 11.0249C6.53791 6.35264 10.3362 2.55437 15.0084 2.55437C15.7143 2.55437 16.4201 2.65521 17.0924 2.82327C17.0252 3.26024 16.7899 3.6636 16.4201 3.9325C15.8487 4.36947 15.1429 4.40309 14.5378 4.10057C14.2689 4.4367 14.0336 4.84006 13.8656 5.24341C14.4034 5.61316 14.7059 6.28542 14.605 6.99129C14.5042 7.69717 14.0336 8.23498 13.395 8.43665C13.4622 8.87362 13.563 9.31059 13.7311 9.71395C14.3698 9.61311 15.0756 9.8484 15.479 10.4198C15.916 10.9912 15.9496 11.6971 15.647 12.3022C15.9832 12.5711 16.3865 12.8064 16.7899 12.9744C17.1596 12.4366 17.8319 12.1341 18.5378 12.2349C19.2436 12.3358 19.7815 12.8064 19.9831 13.445C20.4201 13.3778 20.8571 13.2769 21.2604 13.1089C21.1596 12.4702 21.3949 11.7643 21.9663 11.361C22.4369 11.0249 22.9747 10.924 23.4789 11.0585C23.4789 15.6971 19.6806 19.4954 15.0084 19.4954Z"
        />
        <path
          className="cl-icon-accent Gear"
          d="M14.5042 12.5375C14.3698 12.2013 14.437 11.798 14.6387 11.5291C14.5042 11.361 14.3025 11.1929 14.1345 11.0249C13.8656 11.2602 13.4958 11.3274 13.1261 11.1929C12.7563 11.0585 12.5547 10.7224 12.521 10.3862C12.2858 10.3526 12.0505 10.3526 11.8152 10.3862C11.7816 10.756 11.5799 11.0585 11.2101 11.2266C10.874 11.361 10.4707 11.2938 10.2017 11.0921C10.0337 11.2266 9.86562 11.4282 9.69755 11.5963C9.93284 11.8652 10.0001 12.235 9.86562 12.6047C9.73116 12.9744 9.39503 13.1761 9.0589 13.2097C9.02529 13.445 9.02529 13.6803 9.0589 13.9156C9.42864 13.9492 9.73116 14.1509 9.89923 14.5206C10.0337 14.8568 9.96645 15.2601 9.76478 15.529C9.89923 15.6971 10.1009 15.8652 10.269 16.0332C10.5379 15.7979 10.9076 15.7307 11.2774 15.8652C11.6471 15.9996 11.8488 16.3357 11.8824 16.6719C12.1177 16.7055 12.353 16.7055 12.5883 16.6719C12.6219 16.3021 12.8236 15.9996 13.1933 15.8315C13.5294 15.6971 13.9328 15.7643 14.2017 15.966C14.3698 15.8315 14.5378 15.6299 14.7059 15.4618C14.4706 15.1929 14.4034 14.8232 14.5378 14.4534C14.6723 14.0837 15.0084 13.882 15.3445 13.8484C15.3782 13.6131 15.3782 13.3778 15.3445 13.1425C14.9748 13.1089 14.6387 12.9072 14.5042 12.5375ZM13.2941 13.9828C13.0589 14.5879 12.353 14.8904 11.7479 14.6551C11.1429 14.4198 10.8404 13.7139 11.0757 13.1089C11.311 12.5039 12.0169 12.2013 12.6219 12.4366C13.2269 12.6719 13.5294 13.3778 13.2941 13.9828Z"
        />
      </g>
    </Svg>
  ),
  'alert-octagon': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M12 17q.425 0 .713-.288Q13 16.425 13 16t-.287-.713Q12.425 15 12 15t-.712.287Q11 15.575 11 16t.288.712Q11.575 17 12 17Zm-1-4h2V7h-2Zm-2.75 8L3 15.75v-7.5L8.25 3h7.5L21 8.25v7.5L15.75 21Zm.85-2h5.8l4.1-4.1V9.1L14.9 5H9.1L5 9.1v5.8Zm2.9-7Z" />
    </Svg>
  ),
  'alert-octagon-off': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="m19.8 16.95-1.45-1.4.65-.65V9.1L14.9 5H9.1l-.65.65-1.4-1.45L8.25 3h7.5L21 8.25v7.45ZM13 10.2V7h-2v1.2Zm7.5 13.1-3.55-3.55-1.2 1.25h-7.5L3 15.7V8.25l1.2-1.2L.7 3.5l1.4-1.4 19.8 19.8Zm-7.1-12.7ZM9.1 19h5.8l.65-.65-9.9-9.9L5 9.1v5.8Zm2.9-2q-.425 0-.712-.288Q11 16.425 11 16t.288-.713Q11.575 15 12 15t.713.287Q13 15.575 13 16t-.287.712Q12.425 17 12 17Zm-1.4-3.6Z" />
    </Svg>
  ),
  'filter-2': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M11 20q-.425 0-.712-.288Q10 19.425 10 19v-6L4.2 5.6q-.375-.5-.112-1.05Q4.35 4 5 4h14q.65 0 .913.55.262.55-.113 1.05L14 13v6q0 .425-.287.712Q13.425 20 13 20Z" />
    </Svg>
  ),
  categories: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M15 21v-3h-4V8H9v3H2V3h7v3h6V3h7v8h-7V8h-2v8h2v-3h7v8Z" />
    </Svg>
  ),
  token: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        d="M12 5C7.03158 5 2 6.49067 2 9.33983C2 12.189 7.03158 13.6797 12 13.6797C16.9705 13.6797 22 12.189 22 9.33983C22 6.49067 16.9705 5 12 5ZM7 14.5853V17.4778C8.23684 17.7671 9.60526 17.9437 11 18V15.1075C9.65417 15.057 8.31547 14.8822 7 14.5853V14.5853ZM13 15.1053V18C14.3459 17.9505 15.6847 17.7757 17 17.4778V14.5853C15.6874 14.8822 14.3474 15.0566 13 15.1064V15.1053ZM19 13.9668V16.8592C20.8011 16.1334 22 15.0696 22 13.6807V10.7883C22 12.1771 20.8011 13.2398 19 13.9678V13.9668ZM5 16.8592V13.9668C3.2 13.2388 2 12.1749 2 10.7872V13.6797C2 15.0685 3.2 16.1313 5 16.8603V16.8592Z"
        fill="black"
      />
    </Svg>
  ),
  'coin-stack': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 20"
      {...props}
    >
      <g clipPath="url(#clip0)" fill={props.fill}>
        <path d="M5.81 11.42c.563 0 1.106-.036 1.62-.103V5.682a12.465 12.465 0 00-1.62-.104C2.603 5.578 0 6.758 0 8.214v.571c0 1.456 2.602 2.636 5.81 2.636zM5.81 14.363c.563 0 1.106-.036 1.62-.104v-1.652a12.45 12.45 0 01-1.62.105c-2.8 0-5.138-.9-5.688-2.096-.08.175-.122.355-.122.54v.571c0 1.456 2.602 2.636 5.81 2.636zM7.43 15.678v-.128c-.514.067-1.057.104-1.62.104-2.8 0-5.139-.899-5.688-2.095-.08.174-.122.354-.122.54v.57c0 1.456 2.602 2.636 5.81 2.636.643 0 1.26-.047 1.837-.134a2.079 2.079 0 01-.217-.921v-.572zM14.19 1.114c-3.21 0-5.812 1.18-5.812 2.636v.572c0 1.455 2.602 2.635 5.811 2.635C17.4 6.957 20 5.777 20 4.322V3.75c0-1.456-2.602-2.636-5.81-2.636z" />
        <path d="M14.19 8.248c-2.802 0-5.14-.899-5.69-2.095-.08.174-.122.354-.122.54v.57C8.378 8.72 10.98 9.9 14.19 9.9 17.4 9.9 20 8.72 20 7.264v-.572c0-.185-.042-.365-.122-.54-.55 1.197-2.887 2.096-5.689 2.096z" />
        <path d="M14.19 11.19c-2.802 0-5.14-.898-5.69-2.095-.08.174-.122.355-.122.54v.571c0 1.456 2.602 2.636 5.811 2.636 3.21 0 5.811-1.18 5.811-2.636v-.571c0-.185-.042-.366-.122-.54-.55 1.197-2.887 2.096-5.689 2.096z" />
        <path d="M14.19 14.292c-2.802 0-5.14-.9-5.69-2.096-.08.175-.122.355-.122.54v.572c0 1.455 2.602 2.635 5.811 2.635 3.21 0 5.811-1.18 5.811-2.635v-.572c0-.185-.042-.366-.122-.54-.55 1.197-2.887 2.096-5.689 2.096z" />
        <path d="M14.19 17.234c-2.802 0-5.14-.899-5.69-2.095-.08.174-.122.354-.122.54v.571c0 1.456 2.602 2.636 5.811 2.636 3.21 0 5.811-1.18 5.811-2.636v-.572c0-.185-.042-.365-.122-.54-.55 1.197-2.887 2.096-5.689 2.096z" />
      </g>
      <defs>
        <clipPath id="clip0">
          <path fill="#fff" d="M0 0h20v20H0z" />
        </clipPath>
      </defs>
    </Svg>
  ),
  image: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 127 103"
      {...props}
    >
      <path d="M127 91.5556V11.4444C127 5.15 120.65 0 112.889 0H14.1111C6.35 0 0 5.15 0 11.4444V91.5556C0 97.85 6.35 103 14.1111 103H112.889C120.65 103 127 97.85 127 91.5556ZM38.8056 60.0833L56.4444 77.3072L81.1389 51.5L112.889 85.8333H14.1111L38.8056 60.0833Z" />
    </Svg>
  ),
  accordion: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 20"
      {...props}
    >
      <path d="M1 13.0015C0.447715 13.0015 -1.95703e-08 12.5537 -4.37114e-08 12.0015L-2.13006e-07 8.12845C-2.37147e-07 7.57617 0.447715 7.12845 1 7.12845L19 7.12845C19.5523 7.12845 20 7.57617 20 8.12845L20 12.0015C20 12.5537 19.5523 13.0015 19 13.0015L1 13.0015Z" />
      <rect
        y="6.01709"
        width="5.87302"
        height="20"
        rx="1"
        transform="rotate(-90 0 6.01709)"
      />
      <rect
        y="19.9854"
        width="5.87302"
        height="20"
        rx="1"
        transform="rotate(-90 0 19.9854)"
      />
    </Svg>
  ),
  'layout-1column': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 77 126"
      {...props}
    >
      <rect width="77" height="126" rx="10" />
    </Svg>
  ),
  'layout-2column-1': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 126 126"
      {...props}
    >
      <rect x="69" width="57" height="126" rx="2" />
      <rect width="57" height="126" rx="2" />
    </Svg>
  ),
  'layout-3column': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 126 126"
      {...props}
    >
      <path d="M44 2C44 0.89543 44.8954 0 46 0H79C80.1046 0 81 0.895431 81 2V124C81 125.105 80.1046 126 79 126H46C44.8954 126 44 125.105 44 124V2Z" />
      <rect x="88" width="37" height="126" rx="2" />
      <rect width="37" height="126" rx="2" />
    </Svg>
  ),
  'layout-2column-3': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 126 126"
      {...props}
    >
      <rect width="75" height="126" rx="2" />
      <rect x="88" width="38" height="126" rx="2" />
    </Svg>
  ),
  'layout-2column-2': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 126 126"
      {...props}
    >
      <rect x="51" width="75" height="126" rx="2" />
      <rect width="38" height="126" rx="2" />
    </Svg>
  ),
  text: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 129 126"
      {...props}
    >
      <path d="M119.232 0L129 36.54L120.916 38.724C117.127 31.416 113.254 24.108 108.791 20.412C104.328 16.8 99.1077 16.8 93.9713 16.8H72.9204V105C72.9204 109.2 72.9204 113.4 75.6991 115.5C78.562 117.6 84.1194 117.6 89.7611 117.6V126H39.2389V117.6C44.8805 117.6 50.438 117.6 53.3009 115.5C56.0796 113.4 56.0796 109.2 56.0796 105V16.8H35.0287C29.8923 16.8 24.6717 16.8 20.2089 20.412C15.7461 24.108 11.8727 31.416 8.08355 38.724L0 36.54L9.76762 0H119.232Z" />
    </Svg>
  ),
  message: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 40 40"
      {...props}
    >
      <path
        d="M36 0H4a4 4 0 0 0-4 4v36l8-8h28a4 4 0 0 0 4-4V4a4 4 0 0 0-4-4ZM8 14h24v4H8v-4Zm16 10H8v-4h16v4Zm8-12H8V8h24"
        fill="#01A1B1"
        fillOpacity=".25"
      />
    </Svg>
  ),
  'layout-white-space': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 14"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 6C1.10457 6 2 5.10457 2 4V2H5.5C6.60457 2 7.5 1.10457 7.5 0H1C0.95575 0 0.912171 0.00287412 0.86944 0.00844586C0.781234 0.0199471 0.696641 0.0429428 0.617212 0.0758806C0.372772 0.177246 0.177246 0.372772 0.0758805 0.617213C0.0429427 0.696641 0.0199471 0.781235 0.00844584 0.86944C0.00287411 0.912171 0 0.95575 0 1V6ZM12.5 0C12.5 1.10457 13.3954 2 14.5 2H18V4C18 5.10457 18.8954 6 20 6V1C20 0.955748 19.9971 0.912176 19.9916 0.869444C19.98 0.78124 19.9571 0.69664 19.9241 0.617212C19.8228 0.372772 19.6272 0.177246 19.3828 0.0758809C19.3034 0.0429431 19.2188 0.019947 19.1306 0.0084458C19.0878 0.0028741 19.0443 0 19 0H12.5ZM20 8C18.8954 8 18 8.89543 18 10V12H14.5C13.3954 12 12.5 12.8954 12.5 14H19C19.0443 14 19.0878 13.9971 19.1306 13.9915C19.2188 13.98 19.3034 13.9571 19.3828 13.9241C19.6272 13.8228 19.8228 13.6272 19.9241 13.3828C19.9571 13.3034 19.98 13.2188 19.9916 13.1306C19.9971 13.0878 20 13.0443 20 13V8ZM7.5 14C7.5 12.8954 6.60457 12 5.5 12H2V10C2 8.89543 1.10457 8 0 8V13C0 13.0443 0.00287411 13.0878 0.00844585 13.1306C0.0199471 13.2188 0.0429427 13.3034 0.0758806 13.3828C0.177246 13.6272 0.372772 13.8228 0.617212 13.9241C0.696641 13.9571 0.781235 13.9801 0.86944 13.9916C0.912171 13.9971 0.95575 14 1 14H7.5Z"
        fill={props.fill}
      />
    </Svg>
  ),
  'section-info-accordion': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 20"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.617212 19.9241L1 19.001V19H1.9V20H1C0.864409 20 0.73512 19.973 0.617212 19.9241ZM18.1 20V19H19L19.3828 19.9241C19.2649 19.973 19.1356 20 19 20H18.1ZM20 1.9H19V1L19.9241 0.617212C19.973 0.73512 20 0.864409 20 1V1.9ZM1.9 0H1C0.864408 0 0.73512 0.0269862 0.617212 0.0758805L1 0.998965V1H1.9V0ZM0.0758806 19.3828L0.998963 19H1L1 18.1H0V19C0 19.1356 0.0269862 19.2649 0.0758806 19.3828ZM0 16.3H1V14.5H0V16.3ZM0 12.7H1V10.9H0V12.7ZM0 9.1H1V7.3H0V9.1ZM0 5.5H1V3.7H0V5.5ZM0 1.9H1V1L0.0758806 0.617212C0.0269862 0.73512 0 0.864409 0 1V1.9ZM3.7 0V1H5.5V0H3.7ZM7.3 0V1H9.1V0H7.3ZM10.9 0V1H12.7V0H10.9ZM14.5 0V1H16.3V0H14.5ZM18.1 0V1H19L19.3828 0.0758806C19.2649 0.0269862 19.1356 0 19 0H18.1ZM20 3.7H19V5.5H20V3.7ZM20 7.3H19V9.1H20V7.3ZM20 10.9H19V12.7H20V10.9ZM20 14.5H19V16.3H20V14.5ZM20 18.1H19V19L19.9241 19.3828C19.973 19.2649 20 19.1356 20 19V18.1ZM16.3 20V19H14.5V20H16.3ZM12.7 20V19H10.9V20H12.7ZM9.1 20V19H7.3V20H9.1ZM5.5 20V19H3.7V20H5.5Z"
      />
      <path d="M3 12C2.44772 12 2 11.5523 2 11C2 10.4477 2.44772 10 3 10H9C9.55228 10 10 10.4477 10 11C10 11.5523 9.55228 12 9 12H3Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3 3L3 8H9V3L3 3ZM2 8C2 8.55228 2.44772 9 3 9H9C9.55228 9 10 8.55228 10 8V3C10 2.44772 9.55228 2 9 2L3 2C2.44772 2 2 2.44772 2 3L2 8Z"
      />
      <path d="M12 9C11.4477 9 11 8.55228 11 8V3C11 2.44772 11.4477 2 12 2L17 2C17.5523 2 18 2.44772 18 3V8C18 8.55228 17.5523 9 17 9H12Z" />
      <path d="M3 15C2.44772 15 2 14.5523 2 14C2 13.4477 2.44772 13 3 13H9C9.55228 13 10 13.4477 10 14C10 14.5523 9.55228 15 9 15H3Z" />
      <path d="M3 18C2.44772 18 2 17.5523 2 17C2 16.4477 2.44772 16 3 16H9C9.55228 16 10 16.4477 10 17C10 17.5523 9.55228 18 9 18H3Z" />
    </Svg>
  ),
  'section-image-text': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 20"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.617212 19.9241L1 19.001V19H1.9V20H1C0.864409 20 0.73512 19.973 0.617212 19.9241ZM18.1 20V19H19L19.3828 19.9241C19.2649 19.973 19.1356 20 19 20H18.1ZM20 1.9H19V1L19.9241 0.617212C19.973 0.73512 20 0.864409 20 1V1.9ZM1.9 0H1C0.864408 0 0.73512 0.0269862 0.617212 0.0758805L1 0.998965V1H1.9V0ZM0.0758806 19.3828L0.998963 19H1L1 18.1H0V19C0 19.1356 0.0269862 19.2649 0.0758806 19.3828ZM0 16.3H1V14.5H0V16.3ZM0 12.7H1V10.9H0V12.7ZM0 9.1H1V7.3H0V9.1ZM0 5.5H1V3.7H0V5.5ZM0 1.9H1V1L0.0758806 0.617212C0.0269862 0.73512 0 0.864409 0 1V1.9ZM3.7 0V1H5.5V0H3.7ZM7.3 0V1H9.1V0H7.3ZM10.9 0V1H12.7V0H10.9ZM14.5 0V1H16.3V0H14.5ZM18.1 0V1H19L19.3828 0.0758806C19.2649 0.0269862 19.1356 0 19 0H18.1ZM20 3.7H19V5.5H20V3.7ZM20 7.3H19V9.1H20V7.3ZM20 10.9H19V12.7H20V10.9ZM20 14.5H19V16.3H20V14.5ZM20 18.1H19V19L19.9241 19.3828C19.973 19.2649 20 19.1356 20 19V18.1ZM16.3 20V19H14.5V20H16.3ZM12.7 20V19H10.9V20H12.7ZM9.1 20V19H7.3V20H9.1ZM5.5 20V19H3.7V20H5.5Z"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.617212 19.9241L1 19.001V19H1.9V20H1C0.864409 20 0.73512 19.973 0.617212 19.9241ZM18.1 20V19H19L19.3828 19.9241C19.2649 19.973 19.1356 20 19 20H18.1ZM20 1.9H19V1L19.9241 0.617212C19.973 0.73512 20 0.864409 20 1V1.9ZM1.9 0H1C0.864408 0 0.73512 0.0269862 0.617212 0.0758805L1 0.998965V1H1.9V0ZM0.0758806 19.3828L0.998963 19H1L1 18.1H0V19C0 19.1356 0.0269862 19.2649 0.0758806 19.3828ZM0 16.3H1V14.5H0V16.3ZM0 12.7H1V10.9H0V12.7ZM0 9.1H1V7.3H0V9.1ZM0 5.5H1V3.7H0V5.5ZM0 1.9H1V1L0.0758806 0.617212C0.0269862 0.73512 0 0.864409 0 1V1.9ZM3.7 0V1H5.5V0H3.7ZM7.3 0V1H9.1V0H7.3ZM10.9 0V1H12.7V0H10.9ZM14.5 0V1H16.3V0H14.5ZM18.1 0V1H19L19.3828 0.0758806C19.2649 0.0269862 19.1356 0 19 0H18.1ZM20 3.7H19V5.5H20V3.7ZM20 7.3H19V9.1H20V7.3ZM20 10.9H19V12.7H20V10.9ZM20 14.5H19V16.3H20V14.5ZM20 18.1H19V19L19.9241 19.3828C19.973 19.2649 20 19.1356 20 19V18.1ZM16.3 20V19H14.5V20H16.3ZM12.7 20V19H10.9V20H12.7ZM9.1 20V19H7.3V20H9.1ZM5.5 20V19H3.7V20H5.5Z"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3 3V5H5V3L3 3ZM2 5C2 5.55228 2.44772 6 3 6H5C5.55228 6 6 5.55228 6 5V3C6 2.44772 5.55228 2 5 2L3 2C2.44772 2 2 2.44772 2 3L2 5Z"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3 15V17H5V15H3ZM2 17C2 17.5523 2.44772 18 3 18H5C5.55228 18 6 17.5523 6 17V15C6 14.4477 5.55228 14 5 14H3C2.44772 14 2 14.4477 2 15L2 17Z"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3 9V11H5V9H3ZM2 11C2 11.5523 2.44772 12 3 12H5C5.55228 12 6 11.5523 6 11V9C6 8.44772 5.55228 8 5 8H3C2.44772 8 2 8.44772 2 9L2 11Z"
      />
      <path d="M8 18C7.44771 18 7 17.5523 7 17V15C7 14.4477 7.44772 14 8 14H17C17.5523 14 18 14.4477 18 15V17C18 17.5523 17.5523 18 17 18H8Z" />
      <path d="M8 12C7.44771 12 7 11.5523 7 11V9C7 8.44772 7.44772 8 8 8H17C17.5523 8 18 8.44772 18 9V11C18 11.5523 17.5523 12 17 12L8 12Z" />
      <path d="M8 6C7.44771 6 7 5.55228 7 5V3C7 2.44772 7.44772 2 8 2L17 2C17.5523 2 18 2.44772 18 3V5C18 5.55228 17.5523 6 17 6L8 6Z" />
    </Svg>
  ),
  button: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M20 20.5C20 21.3 19.3 22 18.5 22H13C12.6 22 12.3 21.9 12 21.6L8 17.4L8.7 16.6C8.9 16.4 9.2 16.3 9.5 16.3H9.7L12 18V9C12 8.4 12.4 8 13 8S14 8.4 14 9V13.5L15.2 13.6L19.1 15.8C19.6 16 20 16.6 20 17.1V20.5M20 2H4C2.9 2 2 2.9 2 4V12C2 13.1 2.9 14 4 14H8V12H4V4H20V12H18V14H20C21.1 14 22 13.1 22 12V4C22 2.9 21.1 2 20 2Z" />
    </Svg>
  ),
  tablet: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 30 30"
      {...props}
    >
      <path
        d="M24.2639 23.1667H5.7361V4.5H24.2639V23.1667ZM17.5556 26.6666H12.4444V25.4999H17.5556V26.6666ZM22.6666 1H7.33333C6.31667 1 5.34165 1.36875 4.62277 2.02513C3.90386 2.68151 3.5 3.57175 3.5 4.5V25.4999C3.5 26.4282 3.90386 27.3184 4.62277 27.9748C5.34165 28.6311 6.31667 29 7.33333 29H22.6666C23.6834 29 24.6583 28.6311 25.3773 27.9748C26.0961 27.3184 26.5 26.4282 26.5 25.4999V4.5C26.5 3.57175 26.0961 2.68151 25.3773 2.02513C24.6583 1.36875 23.6834 1 22.6666 1Z"
        fill={props.fill}
      />
    </Svg>
  ),
  desktop: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M21,16H3V4H21M21,2H3C1.89,2 1,2.89 1,4V16A2,2 0 0,0 3,18H10V20H8V22H16V20H14V18H21A2,2 0 0,0 23,16V4C23,2.89 22.1,2 21,2Z" />
    </Svg>
  ),
  'survey-number-field': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M9.58637 16.5L10.0788 14.0677H9.30005V13.3048H10.2277L10.5656 11.6297H9.30005V10.884H10.7145L11.2127 8.40002H11.9685L11.4646 10.884H13.1539L13.6464 8.40002H14.4023L13.8984 10.884H14.7V11.6297H13.7552L13.4116 13.3048H14.7V14.0677H13.2684L12.776 16.5H12.0144L12.5068 14.0677H10.8405L10.3251 16.5H9.58637ZM10.9893 13.3048H12.6729L13.005 11.6297H11.3215L10.9893 13.3048Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19 5H5V19H19V5ZM5 3C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H5Z"
      />
    </Svg>
  ),
  sort: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M5.90916 10.1817H18.6362C18.8824 10.1817 19.0956 10.0918 19.2754 9.91188C19.4552 9.73198 19.5454 9.51894 19.5454 9.27272C19.5454 9.0265 19.4552 8.81357 19.2754 8.63342L12.9119 2.26985C12.7321 2.09015 12.5191 2 12.2727 2C12.0263 2 11.8134 2.09015 11.6334 2.26985L5.26985 8.63342C5.0899 8.81337 5 9.0265 5 9.27272C5 9.51889 5.0899 9.73198 5.26985 9.91188C5.45 10.0918 5.66299 10.1817 5.90916 10.1817Z" />
      <path d="M18.6362 13.8184H5.90916C5.66279 13.8184 5.4498 13.9084 5.26985 14.0881C5.0899 14.2681 5 14.4811 5 14.7273C5 14.9735 5.0899 15.1866 5.26985 15.3665L11.6334 21.7299C11.8136 21.9099 12.0265 22 12.2727 22C12.5189 22 12.7321 21.9099 12.9119 21.7299L19.2754 15.3664C19.4552 15.1866 19.5454 14.9735 19.5454 14.7272C19.5454 14.4811 19.4552 14.2681 19.2754 14.0881C19.0956 13.9082 18.8824 13.8184 18.6362 13.8184Z" />
    </Svg>
  ),
  page: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M6 2C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2H6ZM6 4H13V9H18V20H6V4ZM8 12V14H16V12H8ZM8 16V18H13V16H8Z" />
    </Svg>
  ),
  'survey-short-answer': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M20 20H4C3.46957 20 2.96086 19.7893 2.58579 19.4142C2.21071 19.0391 2 18.5304 2 18V6C2 5.46957 2.21071 4.96086 2.58579 4.58579C2.96086 4.21071 3.46957 4 4 4H20C20.5304 4 21.0391 4.21071 21.4142 4.58579C21.7893 4.96086 22 5.46957 22 6V18C22 18.5304 21.7893 19.0391 21.4142 19.4142C21.0391 19.7893 20.5304 20 20 20ZM4 6V18H20V6H4ZM6 9H18V11H6V9ZM6 13H16V15H6V13Z" />
    </Svg>
  ),
  'survey-long-answer': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M5 3C3.89 3 3 3.89 3 5V19C3 20.11 3.89 21 5 21H19C20.11 21 21 20.11 21 19V5C21 3.89 20.11 3 19 3H5ZM5 5H19V19H5V5ZM7 7V9H17V7H7ZM7 11V13H17V11H7ZM7 15V17H14V15H7Z" />
    </Svg>
  ),
  'survey-linear-scale': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 5"
      {...props}
    >
      <path d="M17.5 0C16.47 0 15.6 0.62 15.21 1.5H12.29C11.9 0.62 11.03 0 10 0C8.97 0 8.1 0.62 7.71 1.5H4.79C4.4 0.62 3.53 0 2.5 0C1.12 0 0 1.12 0 2.5C0 3.88 1.12 5 2.5 5C3.53 5 4.4 4.38 4.79 3.5H7.71C8.1 4.38 8.97 5 10 5C11.03 5 11.9 4.38 12.29 3.5H15.21C15.6 4.38 16.47 5 17.5 5C18.88 5 20 3.88 20 2.5C20 1.12 18.88 0 17.5 0Z" />
    </Svg>
  ),
  'survey-ranking': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 14 16"
      {...props}
    >
      <path d="M0.333313 15.5V14.375H2.18516V13.8125H1.07405V12.6875H2.18516V12.125H0.333313V11H2.55554C2.76541 11 2.94134 11.0719 3.08331 11.2156C3.22529 11.3594 3.29628 11.5375 3.29628 11.75V12.5C3.29628 12.7125 3.22529 12.8906 3.08331 13.0344C2.94134 13.1781 2.76541 13.25 2.55554 13.25C2.76541 13.25 2.94134 13.3219 3.08331 13.4656C3.22529 13.6094 3.29628 13.7875 3.29628 14V14.75C3.29628 14.9625 3.22529 15.1406 3.08331 15.2844C2.94134 15.4281 2.76541 15.5 2.55554 15.5H0.333313ZM0.333313 10.25V8.1875C0.333313 7.975 0.404301 7.79688 0.546276 7.65313C0.688251 7.50938 0.864177 7.4375 1.07405 7.4375H2.18516V6.875H0.333313V5.75H2.55554C2.76541 5.75 2.94134 5.82188 3.08331 5.96563C3.22529 6.10938 3.29628 6.2875 3.29628 6.5V7.8125C3.29628 8.025 3.22529 8.20313 3.08331 8.34688C2.94134 8.49063 2.76541 8.5625 2.55554 8.5625H1.44442V9.125H3.29628V10.25H0.333313ZM1.44442 5V1.625H0.333313V0.5H2.55554V5H1.44442ZM4.77776 13.25V11.75H13.6666V13.25H4.77776ZM4.77776 8.75V7.25H13.6666V8.75H4.77776ZM4.77776 4.25V2.75H13.6666V4.25H4.77776Z" />
    </Svg>
  ),
  'survey-matrix': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 18 14"
      {...props}
    >
      <path d="M12.4027 6.04766H15.4814V2.23814H12.4027V6.04766ZM7.44903 6.04766H10.5277V2.23814H7.44903V6.04766ZM2.51848 6.04766H5.59718V2.23814H2.51848V6.04766ZM2.51848 11.7619H5.59718V7.95242H2.51848V11.7619ZM7.44903 11.7619H10.5277V7.95242H7.44903V11.7619ZM12.4027 11.7619H15.4814V7.95242H12.4027V11.7619ZM0.666626 11.7619V2.23814C0.666626 1.71433 0.847953 1.26591 1.21061 0.892898C1.57326 0.519882 2.00922 0.333374 2.51848 0.333374H15.4814C15.9907 0.333374 16.4267 0.519882 16.7893 0.892898C17.152 1.26591 17.3333 1.71433 17.3333 2.23814V11.7619C17.3333 12.2858 17.152 12.7342 16.7893 13.1072C16.4267 13.4802 15.9907 13.6667 15.4814 13.6667H2.51848C2.00922 13.6667 1.57326 13.4802 1.21061 13.1072C0.847953 12.7342 0.666626 12.2858 0.666626 11.7619Z" />
    </Svg>
  ),
  'survey-sentiment': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 18 18"
      {...props}
    >
      <path d="M8.99984 13.5834C9.80539 13.5834 10.5484 13.389 11.229 13.0001C11.9096 12.6112 12.4582 12.0834 12.8748 11.4167C12.9582 11.2501 12.9512 11.0834 12.854 10.9167C12.7568 10.7501 12.6109 10.6667 12.4165 10.6667H5.58317C5.38873 10.6667 5.24289 10.7501 5.14567 10.9167C5.04845 11.0834 5.0415 11.2501 5.12484 11.4167C5.5415 12.0834 6.09359 12.6112 6.78109 13.0001C7.46859 13.389 8.20817 13.5834 8.99984 13.5834ZM6.4165 7.29175L6.854 7.72925C6.979 7.85425 7.12484 7.91675 7.2915 7.91675C7.45817 7.91675 7.604 7.85425 7.729 7.72925C7.854 7.60425 7.91303 7.45841 7.90609 7.29175C7.89914 7.12508 7.84011 6.97925 7.729 6.85425L6.99984 6.10425C6.83317 5.93758 6.63525 5.85425 6.40609 5.85425C6.17692 5.85425 5.979 5.93758 5.81234 6.10425L5.06234 6.85425C4.93734 6.97925 4.87484 7.12508 4.87484 7.29175C4.87484 7.45841 4.93734 7.60425 5.06234 7.72925C5.17345 7.84036 5.31581 7.89939 5.48942 7.90633C5.66303 7.91328 5.81234 7.86119 5.93734 7.75008L6.4165 7.29175ZM11.5832 7.29175L12.0623 7.75008C12.1873 7.86119 12.3332 7.91675 12.4998 7.91675C12.6665 7.91675 12.8123 7.85425 12.9373 7.72925C13.0623 7.60425 13.1248 7.45841 13.1248 7.29175C13.1248 7.12508 13.0623 6.97925 12.9373 6.85425L12.1873 6.10425C12.0207 5.93758 11.8228 5.85425 11.5936 5.85425C11.3644 5.85425 11.1665 5.93758 10.9998 6.10425L10.2498 6.85425C10.1387 6.97925 10.0832 7.12508 10.0832 7.29175C10.0832 7.45841 10.1457 7.60425 10.2707 7.72925C10.3957 7.85425 10.5415 7.91675 10.7082 7.91675C10.8748 7.91675 11.0207 7.85425 11.1457 7.72925L11.5832 7.29175ZM8.99984 17.3334C7.84706 17.3334 6.76373 17.1147 5.74984 16.6772C4.73595 16.2397 3.854 15.6459 3.104 14.8959C2.354 14.1459 1.76025 13.264 1.32275 12.2501C0.885254 11.2362 0.666504 10.1529 0.666504 9.00008C0.666504 7.8473 0.885254 6.76397 1.32275 5.75008C1.76025 4.73619 2.354 3.85425 3.104 3.10425C3.854 2.35425 4.73595 1.7605 5.74984 1.323C6.76373 0.885498 7.84706 0.666748 8.99984 0.666748C10.1526 0.666748 11.2359 0.885498 12.2498 1.323C13.2637 1.7605 14.1457 2.35425 14.8957 3.10425C15.6457 3.85425 16.2394 4.73619 16.6769 5.75008C17.1144 6.76397 17.3332 7.8473 17.3332 9.00008C17.3332 10.1529 17.1144 11.2362 16.6769 12.2501C16.2394 13.264 15.6457 14.1459 14.8957 14.8959C14.1457 15.6459 13.2637 16.2397 12.2498 16.6772C11.2359 17.1147 10.1526 17.3334 8.99984 17.3334ZM8.99984 15.6667C10.8609 15.6667 12.4373 15.0209 13.729 13.7292C15.0207 12.4376 15.6665 10.8612 15.6665 9.00008C15.6665 7.13897 15.0207 5.56258 13.729 4.27091C12.4373 2.97925 10.8609 2.33341 8.99984 2.33341C7.13873 2.33341 5.56234 2.97925 4.27067 4.27091C2.979 5.56258 2.33317 7.13897 2.33317 9.00008C2.33317 10.8612 2.979 12.4376 4.27067 13.7292C5.56234 15.0209 7.13873 15.6667 8.99984 15.6667Z" />
    </Svg>
  ),
  'survey-multiple-choice': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M11 15H17V17H11V15M9 7H7V9H9V7M11 13H17V11H11V13M11 9H17V7H11V9M9 11H7V13H9V11M21 5V19C21 20.1 20.1 21 19 21H5C3.9 21 3 20.1 3 19V5C3 3.9 3.9 3 5 3H19C20.1 3 21 3.9 21 5M19 5H5V19H19V5M9 15H7V17H9V15Z" />
    </Svg>
  ),
  'survey-long-answer-2': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M13.8 22.7502V20.3752L19.325 14.8502L21.7 17.2252L16.175 22.7502H13.8ZM4.8 17.7752V15.4002H12V17.7752H4.8ZM22.775 16.1502L20.4 13.7752L21.125 13.0502C21.3417 12.8335 21.6167 12.7252 21.95 12.7252C22.2833 12.7252 22.5583 12.8335 22.775 13.0502L23.5 13.7752C23.7167 13.9919 23.825 14.2669 23.825 14.6002C23.825 14.9335 23.7167 15.2085 23.5 15.4252L22.775 16.1502ZM4.8 13.0502V10.7002H15.6V13.0502H4.8ZM4.8 8.3502V6.0002H17.4V8.3502H4.8Z" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 3.6002C0 2.27471 1.07452 1.2002 2.4 1.2002H19.2C20.5255 1.2002 21.6 2.27471 21.6 3.6002V9.6002H19.2V3.6002H2.4V20.4002H9.6V22.8002H2.4C1.07452 22.8002 0 21.7257 0 20.4002V3.6002Z"
      />
    </Svg>
  ),
  'survey-short-answer-2': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M21.6 21.5999H2.4C1.76348 21.5999 1.15303 21.347 0.702944 20.897C0.252856 20.4469 0 19.8364 0 19.1999V4.7999C0 4.16338 0.252856 3.55293 0.702944 3.10285C1.15303 2.65276 1.76348 2.3999 2.4 2.3999H21.6C22.2365 2.3999 22.847 2.65276 23.2971 3.10285C23.7471 3.55293 24 4.16338 24 4.7999V19.1999C24 19.8364 23.7471 20.4469 23.2971 20.897C22.847 21.347 22.2365 21.5999 21.6 21.5999ZM2.4 4.7999V19.1999H21.6V4.7999H2.4ZM4.8 8.3999H19.2V10.7999H4.8V8.3999ZM4.8 13.1999H13.2V15.5999H4.8V13.1999Z" />
    </Svg>
  ),
  'survey-multiple-choice-2': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <g clipPath="url(#clip0_1805_18537)">
        <path d="M12.825 13.7748L18.725 7.8498L17.475 6.6498L12.85 11.2748L10.425 8.8248L9.17499 10.0998L12.825 13.7748ZM7.14999 19.1998C6.49999 19.1998 5.94582 18.9706 5.48749 18.5123C5.02915 18.054 4.79999 17.4998 4.79999 16.8498V3.2998C4.79999 2.6498 5.02915 2.09147 5.48749 1.6248C5.94582 1.15814 6.49999 0.924805 7.14999 0.924805H20.7C21.35 0.924805 21.9083 1.15814 22.375 1.6248C22.8417 2.09147 23.075 2.6498 23.075 3.2998V16.8498C23.075 17.4998 22.8417 18.054 22.375 18.5123C21.9083 18.9706 21.35 19.1998 20.7 19.1998H7.14999ZM7.14999 16.8498H20.7V3.2998H7.14999V16.8498V16.8498ZM3.29999 23.0748C2.64999 23.0748 2.09165 22.8415 1.62499 22.3748C1.15832 21.9081 0.924988 21.3498 0.924988 20.6998V4.7998H3.29999V20.6998H19.2V23.0748H3.29999ZM7.14999 3.2998V16.8498V3.2998Z" />
      </g>
      <defs>
        <clipPath id="clip0_1805_18537">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </Svg>
  ),
  'survey-single-choice': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M10.475 16.9251L17.825 9.5751L16.175 7.9251L10.475 13.6251L7.7 10.8501L6.075 12.5001L10.475 16.9251ZM12 22.6251C10.5333 22.6251 9.15417 22.3501 7.8625 21.8001C6.57083 21.2501 5.44167 20.4918 4.475 19.5251C3.50833 18.5584 2.75 17.4293 2.2 16.1376C1.65 14.8459 1.375 13.4668 1.375 12.0001C1.375 10.5168 1.65 9.12926 2.2 7.8376C2.75 6.54593 3.50833 5.42093 4.475 4.4626C5.44167 3.50426 6.57083 2.74593 7.8625 2.1876C9.15417 1.62926 10.5333 1.3501 12 1.3501C13.4833 1.3501 14.8708 1.62926 16.1625 2.1876C17.4542 2.74593 18.5792 3.50426 19.5375 4.4626C20.4958 5.42093 21.2542 6.54593 21.8125 7.8376C22.3708 9.12926 22.65 10.5168 22.65 12.0001C22.65 13.4668 22.3708 14.8459 21.8125 16.1376C21.2542 17.4293 20.4958 18.5584 19.5375 19.5251C18.5792 20.4918 17.4542 21.2501 16.1625 21.8001C14.8708 22.3501 13.4833 22.6251 12 22.6251ZM12 20.2751C14.3 20.2751 16.2542 19.4709 17.8625 17.8626C19.4708 16.2543 20.275 14.3001 20.275 12.0001C20.275 9.7001 19.4708 7.74593 17.8625 6.1376C16.2542 4.52926 14.3 3.7251 12 3.7251C9.7 3.7251 7.74583 4.52926 6.1375 6.1376C4.52917 7.74593 3.725 9.7001 3.725 12.0001C3.725 14.3001 4.52917 16.2543 6.1375 17.8626C7.74583 19.4709 9.7 20.2751 12 20.2751Z" />
    </Svg>
  ),
  'drag-handle': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 10 15"
      {...props}
    >
      <path d="M2.49992 14.1667C2.04159 14.1667 1.64922 14.0035 1.32284 13.6771C0.996446 13.3507 0.833252 12.9584 0.833252 12.5C0.833252 12.0417 0.996446 11.6493 1.32284 11.323C1.64922 10.9966 2.04159 10.8334 2.49992 10.8334C2.95825 10.8334 3.35061 10.9966 3.677 11.323C4.00339 11.6493 4.16659 12.0417 4.16659 12.5C4.16659 12.9584 4.00339 13.3507 3.677 13.6771C3.35061 14.0035 2.95825 14.1667 2.49992 14.1667ZM7.49992 14.1667C7.04159 14.1667 6.64922 14.0035 6.32284 13.6771C5.99645 13.3507 5.83325 12.9584 5.83325 12.5C5.83325 12.0417 5.99645 11.6493 6.32284 11.323C6.64922 10.9966 7.04159 10.8334 7.49992 10.8334C7.95825 10.8334 8.35061 10.9966 8.677 11.323C9.00339 11.6493 9.16659 12.0417 9.16659 12.5C9.16659 12.9584 9.00339 13.3507 8.677 13.6771C8.35061 14.0035 7.95825 14.1667 7.49992 14.1667ZM2.49992 9.16671C2.04159 9.16671 1.64922 9.00351 1.32284 8.67712C0.996446 8.35073 0.833252 7.95837 0.833252 7.50004C0.833252 7.04171 0.996446 6.64935 1.32284 6.32296C1.64922 5.99657 2.04159 5.83337 2.49992 5.83337C2.95825 5.83337 3.35061 5.99657 3.677 6.32296C4.00339 6.64935 4.16659 7.04171 4.16659 7.50004C4.16659 7.95837 4.00339 8.35073 3.677 8.67712C3.35061 9.00351 2.95825 9.16671 2.49992 9.16671ZM7.49992 9.16671C7.04159 9.16671 6.64922 9.00351 6.32284 8.67712C5.99645 8.35073 5.83325 7.95837 5.83325 7.50004C5.83325 7.04171 5.99645 6.64935 6.32284 6.32296C6.64922 5.99657 7.04159 5.83337 7.49992 5.83337C7.95825 5.83337 8.35061 5.99657 8.677 6.32296C9.00339 6.64935 9.16659 7.04171 9.16659 7.50004C9.16659 7.95837 9.00339 8.35073 8.677 8.67712C8.35061 9.00351 7.95825 9.16671 7.49992 9.16671ZM2.49992 4.16671C2.04159 4.16671 1.64922 4.00351 1.32284 3.67712C0.996446 3.35073 0.833252 2.95837 0.833252 2.50004C0.833252 2.04171 0.996446 1.64935 1.32284 1.32296C1.64922 0.996568 2.04159 0.833374 2.49992 0.833374C2.95825 0.833374 3.35061 0.996568 3.677 1.32296C4.00339 1.64935 4.16659 2.04171 4.16659 2.50004C4.16659 2.95837 4.00339 3.35073 3.677 3.67712C3.35061 4.00351 2.95825 4.16671 2.49992 4.16671ZM7.49992 4.16671C7.04159 4.16671 6.64922 4.00351 6.32284 3.67712C5.99645 3.35073 5.83325 2.95837 5.83325 2.50004C5.83325 2.04171 5.99645 1.64935 6.32284 1.32296C6.64922 0.996568 7.04159 0.833374 7.49992 0.833374C7.95825 0.833374 8.35061 0.996568 8.677 1.32296C9.00339 1.64935 9.16659 2.04171 9.16659 2.50004C9.16659 2.95837 9.00339 3.35073 8.677 3.67712C8.35061 4.00351 7.95825 4.16671 7.49992 4.16671Z" />
    </Svg>
  ),
  section: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 14 14"
      {...props}
    >
      <path d="M2.03372 6.51634C1.73372 6.51634 1.46984 6.40523 1.24206 6.18301C1.01428 5.96078 0.900391 5.69412 0.900391 5.38301V1.98301C0.900391 1.6719 1.01428 1.40245 1.24206 1.17467C1.46984 0.946897 1.73372 0.833008 2.03372 0.833008H11.9671C12.2782 0.833008 12.5448 0.946897 12.7671 1.17467C12.9893 1.40245 13.1004 1.6719 13.1004 1.98301V5.38301C13.1004 5.69412 12.9893 5.96078 12.7671 6.18301C12.5448 6.40523 12.2782 6.51634 11.9671 6.51634H2.03372ZM2.03372 13.1663C1.73372 13.1663 1.46984 13.0525 1.24206 12.8247C1.01428 12.5969 0.900391 12.3275 0.900391 12.0163V8.61634C0.900391 8.30523 1.01428 8.03856 1.24206 7.81634C1.46984 7.59412 1.73372 7.48301 2.03372 7.48301H11.9671C12.2782 7.48301 12.5448 7.59412 12.7671 7.81634C12.9893 8.03856 13.1004 8.30523 13.1004 8.61634V12.0163C13.1004 12.3275 12.9893 12.5969 12.7671 12.8247C12.5448 13.0525 12.2782 13.1663 11.9671 13.1663H2.03372Z" />
    </Svg>
  ),
  rectangle: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 19 19"
      {...props}
    >
      <rect width="19" height="19" rx="3" />
    </Svg>
  ),
  line: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 19.7"
      {...props}
    >
      <path
        d="M20 2.8a2.8 2.8 0 01-3.8 2.5L5.3 15.9a2.8 2.8 0 01-2.5 3.8A2.8 2.8 0 114 14.4L14.7 4A2.7 2.7 0 0117.2 0C18.8 0 20 1.2 20 2.8z"
        clipRule="evenodd"
        fillRule="evenodd"
      />
    </Svg>
  ),
  logic: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 18 14"
      {...props}
    >
      <path d="M7.33301 7.83398C7.33301 5.03398 6.20801 4.11732 5.46634 3.81732C5.08301 4.70898 4.19968 5.33398 3.16634 5.33398C2.5033 5.33398 1.86742 5.07059 1.39858 4.60175C0.929735 4.13291 0.666344 3.49702 0.666344 2.83398C0.666344 2.17094 0.929735 1.53506 1.39858 1.06622C1.86742 0.597376 2.5033 0.333984 3.16634 0.333984C4.25801 0.333984 5.18301 1.02565 5.52468 2.00065L12.4747 2.00065C12.8163 1.02565 13.7413 0.333984 14.833 0.333984C15.496 0.333984 16.1319 0.597376 16.6008 1.06622C17.0696 1.53506 17.333 2.17094 17.333 2.83398C17.333 3.49703 17.0696 4.13291 16.6008 4.60175C16.1319 5.07059 15.496 5.33398 14.833 5.33398C13.7413 5.33398 12.8163 4.64232 12.4747 3.66732L8.06634 3.66732C8.60801 4.40065 8.99968 5.46732 8.99968 7.00065C8.99968 9.22565 10.1163 9.96732 10.858 10.209C11.233 9.30898 12.1247 8.66732 13.1663 8.66732C13.8294 8.66732 14.4653 8.93071 14.9341 9.39955C15.4029 9.86839 15.6663 10.5043 15.6663 11.1673C15.6663 11.8304 15.4029 12.4662 14.9341 12.9351C14.4653 13.4039 13.8294 13.6673 13.1663 13.6673C12.0497 13.6673 11.083 12.934 10.783 11.9256C9.59134 11.709 7.33301 10.9006 7.33301 7.83398ZM3.99968 2.83398C3.99968 2.61297 3.91188 2.40101 3.7556 2.24473C3.59932 2.08845 3.38736 2.00065 3.16634 2.00065C2.94533 2.00065 2.73337 2.08845 2.57709 2.24473C2.42081 2.40101 2.33301 2.61297 2.33301 2.83398C2.33301 3.055 2.42081 3.26696 2.57709 3.42324C2.73337 3.57952 2.94533 3.66732 3.16634 3.66732C3.38736 3.66732 3.59932 3.57952 3.7556 3.42324C3.91188 3.26696 3.99968 3.055 3.99968 2.83398ZM15.6663 2.83398C15.6663 2.61297 15.5785 2.40101 15.4223 2.24473C15.266 2.08845 15.054 2.00065 14.833 2.00065C14.612 2.00065 14.4 2.08845 14.2438 2.24473C14.0875 2.40101 13.9997 2.61297 13.9997 2.83398C13.9997 3.055 14.0875 3.26696 14.2438 3.42324C14.4 3.57952 14.612 3.66732 14.833 3.66732C15.054 3.66732 15.266 3.57952 15.4223 3.42324C15.5785 3.26696 15.6663 3.055 15.6663 2.83398ZM13.9997 11.1673C13.9997 10.9463 13.9119 10.7343 13.7556 10.5781C13.5993 10.4218 13.3874 10.334 13.1663 10.334C12.9453 10.334 12.7334 10.4218 12.5771 10.5781C12.4208 10.7343 12.333 10.9463 12.333 11.1673C12.333 11.3883 12.4208 11.6003 12.5771 11.7566C12.7334 11.9129 12.9453 12.0007 13.1663 12.0007C13.3874 12.0007 13.5993 11.9129 13.7556 11.7566C13.9119 11.6003 13.9997 11.3883 13.9997 11.1673Z" />
    </Svg>
  ),
  save: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 18 18"
      {...props}
    >
      <path d="M-3-3h24v24H-3z" fill="none" />
      <path d="M14 0H2a2 2 0 00-2 2v14c0 1.1.9 2 2 2h14a2 2 0 002-2V4zM9 16a3 3 0 110-6 3 3 0 010 6zm3-10H2V2h10z" />
    </Svg>
  ),
  grid: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9C9.53043 3 10.0391 3.21071 10.4142 3.58579C10.7893 3.96086 11 4.46957 11 5V9C11 9.53043 10.7893 10.0391 10.4142 10.4142C10.0391 10.7893 9.53043 11 9 11H5C4.46957 11 3.96086 10.7893 3.58579 10.4142C3.21071 10.0391 3 9.53043 3 9V5C3 4.46957 3.21071 3.96086 3.58579 3.58579ZM9 5H5V9H9V5ZM13.5858 3.58579C13.9609 3.21071 14.4696 3 15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V9C21 9.53043 20.7893 10.0391 20.4142 10.4142C20.0391 10.7893 19.5304 11 19 11H15C14.4696 11 13.9609 10.7893 13.5858 10.4142C13.2107 10.0391 13 9.53043 13 9V5C13 4.46957 13.2107 3.96086 13.5858 3.58579ZM19 5H15V9H19L19 5ZM3.58579 13.5858C3.96086 13.2107 4.46957 13 5 13H9C9.53043 13 10.0391 13.2107 10.4142 13.5858C10.7893 13.9609 11 14.4696 11 15V19C11 19.5304 10.7893 20.0391 10.4142 20.4142C10.0391 20.7893 9.53043 21 9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15C3 14.4696 3.21071 13.9609 3.58579 13.5858ZM9 15H5V19H9V15ZM13.5858 13.5858C13.9609 13.2107 14.4696 13 15 13H19C19.5304 13 20.0391 13.2107 20.4142 13.5858C20.7893 13.9609 21 14.4696 21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15C14.4696 21 13.9609 20.7893 13.5858 20.4142C13.2107 20.0391 13 19.5304 13 19V15C13 14.4696 13.2107 13.9609 13.5858 13.5858ZM19 15H15V19H19L19 15Z"
      />
    </Svg>
  ),
  projects: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.17157 4.17157C4.92172 3.42143 5.93913 3 7 3H11C12.0609 3 13.0783 3.42143 13.8284 4.17157C14.5786 4.92172 15 5.93913 15 7V20H18V17.8284C17.5821 17.6807 17.1981 17.4407 16.8787 17.1213C16.3161 16.5587 16 15.7957 16 15V13C16 12.2044 16.3161 11.4413 16.8787 10.8787C17.4413 10.3161 18.2043 10 19 10C19.7956 10 20.5587 10.3161 21.1213 10.8787C21.6839 11.4413 22 12.2044 22 13V15C22 15.7957 21.6839 16.5587 21.1213 17.1213C20.8019 17.4407 20.4179 17.6807 20 17.8284V20H21C21.5523 20 22 20.4477 22 21C22 21.5523 21.5523 22 21 22H3C2.44772 22 2 21.5523 2 21C2 20.4477 2.44772 20 3 20V7C3 5.93913 3.42143 4.92172 4.17157 4.17157ZM5 20H8V17C8 16.4477 8.44772 16 9 16C9.55229 16 10 16.4477 10 17V20H13V7C13 6.46957 12.7893 5.96086 12.4142 5.58579C12.0391 5.21071 11.5304 5 11 5H7C6.46957 5 5.96086 5.21071 5.58579 5.58579C5.21071 5.96086 5 6.46957 5 7V20ZM7 9C7 8.44772 7.44772 8 8 8H10C10.5523 8 11 8.44772 11 9C11 9.55228 10.5523 10 10 10H8C7.44772 10 7 9.55228 7 9ZM19 12C18.7348 12 18.4804 12.1054 18.2929 12.2929C18.1054 12.4804 18 12.7348 18 13V15C18 15.2652 18.1054 15.5196 18.2929 15.7071C18.4804 15.8946 18.7348 16 19 16C19.2652 16 19.5196 15.8946 19.7071 15.7071C19.8946 15.5196 20 15.2652 20 15V13C20 12.7348 19.8946 12.4804 19.7071 12.2929C19.5196 12.1054 19.2652 12 19 12ZM7 13C7 12.4477 7.44772 12 8 12H10C10.5523 12 11 12.4477 11 13C11 13.5523 10.5523 14 10 14H8C7.44772 14 7 13.5523 7 13Z"
      />
    </Svg>
  ),
  messages: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5 6C4.73478 6 4.48043 6.10536 4.29289 6.29289C4.21971 6.36608 4.15904 6.44944 4.11226 6.53965L12 11.7981L19.8877 6.53965C19.841 6.44944 19.7803 6.36608 19.7071 6.29289C19.5196 6.10536 19.2652 6 19 6H5ZM20 8.86852L12.5547 13.832C12.2188 14.056 11.7812 14.056 11.4453 13.832L4 8.86852V17C4 17.2652 4.10536 17.5196 4.29289 17.7071C4.48043 17.8946 4.73478 18 5 18H19C19.2652 18 19.5196 17.8946 19.7071 17.7071C19.8946 17.5196 20 17.2652 20 17V8.86852ZM2.87868 4.87868C3.44129 4.31607 4.20435 4 5 4H19C19.7956 4 20.5587 4.31607 21.1213 4.87868C21.6839 5.44129 22 6.20435 22 7V17C22 17.7957 21.6839 18.5587 21.1213 19.1213C20.5587 19.6839 19.7956 20 19 20H5C4.20435 20 3.44129 19.6839 2.87868 19.1213C2.31607 18.5587 2 17.7956 2 17V7C2 6.20435 2.31607 5.44129 2.87868 4.87868Z"
      />
    </Svg>
  ),
  users: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.46447 3.46447C6.40215 2.52678 7.67392 2 9 2C10.3261 2 11.5979 2.52678 12.5355 3.46447C13.4732 4.40215 14 5.67392 14 7C14 8.32608 13.4732 9.59785 12.5355 10.5355C11.5979 11.4732 10.3261 12 9 12C7.67392 12 6.40215 11.4732 5.46447 10.5355C4.52678 9.59785 4 8.32608 4 7C4 5.67392 4.52678 4.40215 5.46447 3.46447ZM9 4C8.20435 4 7.44129 4.31607 6.87868 4.87868C6.31607 5.44129 6 6.20435 6 7C6 7.79565 6.31607 8.55871 6.87868 9.12132C7.44129 9.68393 8.20435 10 9 10C9.79565 10 10.5587 9.68393 11.1213 9.12132C11.6839 8.55871 12 7.79565 12 7C12 6.20435 11.6839 5.44129 11.1213 4.87868C10.5587 4.31607 9.79565 4 9 4ZM15.0313 2.88196C15.1682 2.34694 15.713 2.02426 16.248 2.16125C17.3236 2.43663 18.2768 3.06213 18.9576 3.93914C19.6383 4.81615 20.0078 5.89479 20.0078 7.005C20.0078 8.11521 19.6383 9.19385 18.9576 10.0709C18.2768 10.9479 17.3236 11.5734 16.248 11.8488C15.713 11.9857 15.1682 11.6631 15.0313 11.128C14.8943 10.593 15.2169 10.0482 15.752 9.91125C16.3973 9.74602 16.9692 9.37072 17.3777 8.84452C17.7861 8.31831 18.0078 7.67113 18.0078 7.005C18.0078 6.33887 17.7861 5.69169 17.3777 5.16548C16.9692 4.63928 16.3973 4.26398 15.752 4.09875C15.2169 3.96176 14.8943 3.41699 15.0313 2.88196ZM7 16C6.20435 16 5.44129 16.3161 4.87868 16.8787C4.31607 17.4413 4 18.2044 4 19V21C4 21.5523 3.55228 22 3 22C2.44772 22 2 21.5523 2 21V19C2 17.6739 2.52678 16.4021 3.46447 15.4645C4.40215 14.5268 5.67392 14 7 14H11C12.3261 14 13.5979 14.5268 14.5355 15.4645C15.4732 16.4021 16 17.6739 16 19V21C16 21.5523 15.5523 22 15 22C14.4477 22 14 21.5523 14 21V19C14 18.2044 13.6839 17.4413 13.1213 16.8787C12.5587 16.3161 11.7956 16 11 16H7ZM17.0317 14.9C17.1698 14.3653 17.7152 14.0437 18.25 14.1817C19.3185 14.4576 20.2658 15.0793 20.9441 15.9498C21.6224 16.8202 21.9936 17.8907 22 18.9942L22 19L22 21C22 21.5523 21.5523 22 21 22C20.4477 22 20 21.5523 20 21V19.003C19.9956 18.3418 19.7729 17.7006 19.3665 17.179C18.9595 16.6568 18.3911 16.2838 17.75 16.1182C17.2153 15.9802 16.8937 15.4348 17.0317 14.9Z"
      />
    </Svg>
  ),
  proposals: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.96096 5.86961C8.08606 4.02614 11.3577 3 15.986 3H20C20.5523 3 21 3.44772 21 4V6C21 6.01911 20.9994 6.03822 20.9984 6.0573C20.7402 10.5562 19.4959 13.8287 17.3515 15.974C15.1973 18.129 12.2822 19 8.99999 19H6.36933C6.20008 19.6673 6.07604 20.371 5.99388 21.1104C5.93289 21.6593 5.43847 22.0549 4.88956 21.9939C4.34065 21.9329 3.94512 21.4385 4.00611 20.8896C4.12017 19.8631 4.30975 18.8746 4.58876 17.933C2.98991 15.9072 2.98599 13.9562 2.98599 13L2.986 12.9972C2.99356 10.2954 3.81138 7.73432 5.96096 5.86961ZM7.06464 17C7.28661 16.5136 7.54179 16.0526 7.83204 15.6172C8.84199 14.1023 10.3121 12.8445 12.4061 11.9138C12.9108 11.6895 13.1381 11.0985 12.9138 10.5939C12.6895 10.0892 12.0985 9.86189 11.5939 10.0862C9.18791 11.1555 7.40799 12.6477 6.16794 14.5078C5.92318 14.8749 5.70085 15.2543 5.49963 15.645C4.98597 14.574 4.98598 13.6271 4.98599 13.008L4.98599 13.0014C4.99269 10.7039 5.67492 8.7654 7.27152 7.38039C8.89293 5.97386 11.6143 5 15.986 5H19V5.97107C18.7547 10.1681 17.6093 12.8871 15.937 14.56C14.2706 16.227 11.9357 17 8.99999 17H7.06464Z"
      />
    </Svg>
  ),
  'messages-inbox': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6 5H18C18.5523 5 19 5.44772 19 6V12H17.9142C17.3838 12 16.8751 12.2107 16.5 12.5858L14.5858 14.5H9.48063L8.08111 12.7506C7.70157 12.2762 7.12694 12 6.51938 12H5V6C5 5.44772 5.44772 5 6 5ZM5 14V18C5 18.5523 5.44772 19 6 19H18C18.5523 19 19 18.5523 19 18V14H17.9142L16 15.9142C15.6249 16.2893 15.1162 16.5 14.5858 16.5H9.48063C8.87306 16.5 8.29843 16.2238 7.91889 15.7494L6.51937 14H5ZM3 6C3 4.34315 4.34315 3 6 3H18C19.6569 3 21 4.34315 21 6V18C21 19.6569 19.6569 21 18 21H6C4.34315 21 3 19.6569 3 18V6Z"
      />
    </Svg>
  ),
  dashboard: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.9431 3.00163C14.3802 2.97674 14.7826 3.239 14.9363 3.64889L17.693 11H21C21.5523 11 22 11.4477 22 12C22 12.5523 21.5523 13 21 13H17C16.5832 13 16.21 12.7414 16.0637 12.3511L14.1913 7.35805L10.9701 20.2425C10.864 20.6672 10.4939 20.9735 10.0569 20.9984C9.61982 21.0233 9.21738 20.761 9.06367 20.3511L6.307 13H3C2.44772 13 2 12.5523 2 12C2 11.4477 2.44772 11 3 11H7C7.41684 11 7.78997 11.2586 7.93633 11.6489L9.80873 16.642L13.0299 3.75748C13.136 3.33279 13.5061 3.02652 13.9431 3.00163Z"
      />
    </Svg>
  ),
  help: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.55585 3.68508C9.64778 3.23279 10.8181 3 12 3C13.1819 3 14.3522 3.23279 15.4441 3.68508C16.5361 4.13738 17.5282 4.80031 18.364 5.63604C19.1997 6.47177 19.8626 7.46392 20.3149 8.55585C20.7672 9.64778 21 10.8181 21 12C21 13.1819 20.7672 14.3522 20.3149 15.4441C19.8626 16.5361 19.1997 17.5282 18.364 18.364C17.5282 19.1997 16.5361 19.8626 15.4441 20.3149C14.3522 20.7672 13.1819 21 12 21C10.8181 21 9.64778 20.7672 8.55585 20.3149C7.46392 19.8626 6.47177 19.1997 5.63604 18.364C4.80031 17.5282 4.13738 16.5361 3.68508 15.4441C3.23279 14.3522 3 13.1819 3 12C3 10.8181 3.23279 9.64778 3.68508 8.55585C4.13738 7.46392 4.80031 6.47177 5.63604 5.63604C6.47177 4.80031 7.46392 4.13738 8.55585 3.68508ZM12 4.8C11.0545 4.8 10.1182 4.98623 9.24468 5.34807C8.37114 5.7099 7.57741 6.24025 6.90883 6.90883C6.24025 7.57741 5.7099 8.37114 5.34807 9.24468C4.98623 10.1182 4.8 11.0545 4.8 12C4.8 12.9455 4.98623 13.8818 5.34807 14.7553C5.7099 15.6289 6.24025 16.4226 6.90883 17.0912C7.57741 17.7597 8.37114 18.2901 9.24468 18.6519C10.1182 19.0138 11.0545 19.2 12 19.2C12.9455 19.2 13.8818 19.0138 14.7553 18.6519C15.6289 18.2901 16.4226 17.7597 17.0912 17.0912C17.7597 16.4226 18.2901 15.6289 18.6519 14.7553C19.0138 13.8818 19.2 12.9455 19.2 12C19.2 11.0545 19.0138 10.1182 18.6519 9.24468C18.2901 8.37114 17.7597 7.57741 17.0912 6.90883C16.4226 6.24025 15.6289 5.7099 14.7553 5.34807C13.8818 4.98623 12.9455 4.8 12 4.8ZM10.6273 6.91042C11.0767 6.68802 11.5717 6.57298 12.0732 6.57438C12.5746 6.57577 13.0689 6.69354 13.5172 6.91843C13.9654 7.14332 14.3553 7.46918 14.6562 7.87036C14.9571 8.27154 15.1607 8.73709 15.2511 9.23035C15.3415 9.72362 15.3162 10.2311 15.1771 10.7129C15.038 11.1947 14.789 11.6377 14.4496 12.0069C14.1103 12.3761 13.6899 12.6615 13.2215 12.8406C13.2139 12.8435 13.2062 12.8463 13.1986 12.849C13.1065 12.8814 13.0275 12.9428 12.9734 13.0239C12.9193 13.1051 12.893 13.2016 12.8986 13.299C12.9267 13.7953 12.5472 14.2204 12.051 14.2486C11.5547 14.2767 11.1296 13.8972 11.1014 13.401C11.0738 12.914 11.2052 12.4313 11.4757 12.0255C11.7434 11.6239 12.1331 11.3191 12.5871 11.1561C12.7918 11.0763 12.9756 10.9507 13.1244 10.7888C13.2752 10.6247 13.3859 10.4279 13.4477 10.2137C13.5095 9.99959 13.5208 9.77403 13.4806 9.5548C13.4404 9.33558 13.3499 9.12867 13.2162 8.95036C13.0825 8.77206 12.9092 8.62723 12.71 8.52728C12.5107 8.42733 12.2911 8.37499 12.0682 8.37437C11.8453 8.37375 11.6253 8.42488 11.4256 8.52372C11.2258 8.62256 11.0517 8.76643 10.917 8.94399C10.6166 9.33997 10.052 9.41743 9.65601 9.11699C9.26003 8.81656 9.18257 8.252 9.48301 7.85601C9.78611 7.45651 10.1778 7.13281 10.6273 6.91042ZM12 15.6C12.4971 15.6 12.9 16.0029 12.9 16.5V16.5103C12.9 17.0073 12.4971 17.4103 12 17.4103C11.5029 17.4103 11.1 17.0073 11.1 16.5103V16.5C11.1 16.0029 11.5029 15.6 12 15.6Z"
      />
    </Svg>
  ),
  cog: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.86692 4.22393C10.2542 3.56068 10.8982 3.133 11.6588 3.02404C12.5741 2.89293 13.5551 3.30499 14.0546 4.08955C14.1539 4.24555 14.2339 4.40757 14.2945 4.5823C14.3083 4.62175 14.3212 4.66138 14.334 4.70075C14.4199 4.96444 14.5021 5.21687 14.7951 5.33309C15.0384 5.42961 15.2437 5.32316 15.4516 5.21536C15.4791 5.20108 15.5067 5.18678 15.5344 5.17293C15.6972 5.09153 15.8738 5.02924 16.0496 4.98286C16.784 4.78916 17.5943 4.98669 18.1778 5.4662C18.9135 6.07085 19.251 7.03911 19.0207 7.96436C18.9717 8.1613 18.8922 8.34438 18.7993 8.5243C18.7918 8.53875 18.7843 8.55309 18.7769 8.56736C18.6747 8.76331 18.5798 8.94513 18.6605 9.17384C18.7782 9.50744 19.093 9.61487 19.4067 9.72189C19.5572 9.77327 19.7075 9.82455 19.8356 9.90071C20.4405 10.2605 20.8683 10.8952 20.9714 11.5928C21.1123 12.5469 20.7295 13.5368 19.9028 14.0634C19.755 14.1575 19.5978 14.2372 19.4323 14.2952C19.3881 14.3107 19.3437 14.325 19.2998 14.3391C19.0551 14.4179 18.8235 14.4925 18.693 14.7509C18.5501 15.034 18.6941 15.3152 18.8345 15.5891C18.8988 15.7145 18.9623 15.8385 18.9971 15.9604C19.1695 16.5644 19.1089 17.1919 18.8217 17.7522C18.3409 18.6897 17.2297 19.2744 16.184 19.0587C15.97 19.0146 15.7531 18.9395 15.5568 18.8439C15.5225 18.8272 15.4888 18.8099 15.4554 18.7928C15.2266 18.6758 15.0138 18.567 14.7503 18.6991C14.496 18.8266 14.4267 19.0354 14.348 19.2727C14.3379 19.303 14.3277 19.3337 14.317 19.3648C14.2608 19.5271 14.1888 19.686 14.1035 19.8352C13.7577 20.4399 13.1098 20.869 12.4205 20.9708C11.4819 21.1094 10.5026 20.753 9.96782 19.9472C9.87163 19.8022 9.78509 19.6404 9.72541 19.4768C9.70606 19.4237 9.68856 19.3704 9.67124 19.3176C9.58228 19.0464 9.49833 18.7904 9.19459 18.6689C8.95639 18.5737 8.76565 18.6728 8.55918 18.7801C8.54702 18.7864 8.53481 18.7928 8.52253 18.7991C8.35705 18.8845 8.18784 18.9614 8.00728 19.0091C7.23757 19.2121 6.42664 19.0415 5.81188 18.5297C5.09963 17.9367 4.74579 16.9793 4.97125 16.0724C5.01498 15.8964 5.07681 15.7195 5.15795 15.5572C5.17354 15.526 5.1895 15.4953 5.20527 15.465C5.3172 15.2499 5.42029 15.0517 5.32551 14.7957C5.21157 14.4878 4.90173 14.3855 4.59786 14.2852C4.44787 14.2357 4.29934 14.1866 4.17654 14.1136C3.51001 13.7172 3.11168 13.0494 3.01804 12.2871C2.9039 11.3579 3.3381 10.4165 4.13173 9.92248C4.293 9.82212 4.46604 9.74191 4.64698 9.68328C4.6754 9.67407 4.70372 9.66527 4.7318 9.65654C4.95855 9.58605 5.16966 9.52042 5.29249 9.28583C5.45141 8.98231 5.30296 8.68506 5.15747 8.39375C5.09286 8.26439 5.02884 8.1362 4.99258 8.00916C4.8221 7.41181 4.90682 6.78901 5.19041 6.23973C5.69725 5.25806 6.82334 4.73989 7.89527 4.96779C8.07274 5.0055 8.24734 5.06905 8.41052 5.14802C8.44617 5.16528 8.48129 5.18306 8.51607 5.20068C8.76577 5.32713 8.99794 5.44471 9.2842 5.29488C9.53534 5.16344 9.62148 4.89371 9.70674 4.62672C9.75212 4.48459 9.79726 4.34323 9.86692 4.22393ZM11.3327 5.43237C11.4272 5.15599 11.5177 4.89099 11.8604 4.8178C11.9324 4.80241 12.0114 4.80214 12.0844 4.81009C12.4804 4.8531 12.5889 5.17285 12.6972 5.49229C12.7407 5.62051 12.7842 5.74869 12.8462 5.85889C13.1648 6.42481 13.6565 6.86092 14.2798 7.06066C14.7642 7.21584 15.2983 7.22101 15.7808 7.0528C15.8811 7.01783 15.9818 6.96942 16.083 6.92083C16.4048 6.76623 16.7305 6.60979 17.0568 6.87841C17.1138 6.92531 17.1582 6.98214 17.1948 7.04597C17.3688 7.34961 17.2363 7.6165 17.1016 7.8877C17.0525 7.98664 17.0031 8.08617 16.9681 8.18826C16.7877 8.71597 16.7797 9.29751 16.9776 9.82329C17.1956 10.4025 17.613 10.8671 18.1554 11.1602C18.2544 11.2138 18.3745 11.2534 18.4971 11.2939C18.8087 11.3968 19.1372 11.5052 19.1817 11.8615C19.1924 11.9471 19.1922 12.0447 19.1818 12.1303C19.1366 12.5011 18.7986 12.6136 18.4754 12.7212C18.3446 12.7648 18.2162 12.8075 18.1106 12.8663C17.6697 13.1117 17.3051 13.4931 17.0794 13.9445C16.8037 14.4957 16.765 15.126 16.9328 15.7139C16.9673 15.8345 17.0277 15.9556 17.0887 16.0777C17.2296 16.36 17.3731 16.6474 17.2026 16.9458C17.1648 17.0118 17.1138 17.0702 17.0573 17.1208C16.8838 17.2766 16.6283 17.3254 16.408 17.2471C16.3343 17.2208 16.2656 17.1818 16.1969 17.1428C16.1483 17.1152 16.0998 17.0877 16.0496 17.0647C15.8862 16.9898 15.7098 16.9274 15.5343 16.8881C14.7726 16.7177 13.9276 16.9431 13.3614 17.4842C13.1318 17.7036 12.9285 17.953 12.7982 18.2449C12.7586 18.3336 12.7269 18.4258 12.6958 18.5164C12.5904 18.8236 12.4911 19.1132 12.1068 19.1899C12.0305 19.2051 11.9594 19.2092 11.8828 19.193C11.5033 19.1126 11.3972 18.8023 11.289 18.486C11.2448 18.3567 11.2003 18.2265 11.1366 18.1105C10.8399 17.5703 10.3381 17.1535 9.75462 16.9573C9.25291 16.7887 8.7093 16.7925 8.20888 16.9607C8.10367 16.9961 7.99978 17.046 7.89641 17.0956C7.58533 17.2449 7.27888 17.3919 6.95471 17.1355C6.89583 17.0889 6.84706 17.0323 6.80829 16.9682C6.62533 16.6654 6.75783 16.401 6.89319 16.131C6.9434 16.0308 6.99399 15.9299 7.02904 15.8259C7.21161 15.2841 7.22823 14.7065 7.01701 14.1685C6.80345 13.6246 6.41871 13.1567 5.90147 12.8763C5.78938 12.8156 5.66161 12.7704 5.53382 12.7253C5.1918 12.6044 4.84965 12.4836 4.80761 12.0631C4.80015 11.9887 4.80212 11.9117 4.82082 11.8391C4.9079 11.5009 5.18965 11.408 5.47555 11.3138C5.59118 11.2756 5.70748 11.2373 5.81187 11.1824C6.27306 10.9399 6.66899 10.5589 6.90401 10.0921C7.182 9.53983 7.24714 8.91777 7.07919 8.32264C7.04424 8.19877 6.98202 8.0737 6.91923 7.94746C6.77643 7.6604 6.63062 7.36727 6.79543 7.06837C6.83158 7.00279 6.87854 6.94303 6.93286 6.89154C6.97928 6.84753 7.03103 6.812 7.08878 6.78454C7.36754 6.65196 7.58458 6.76127 7.8184 6.87902C7.87973 6.90991 7.94221 6.94138 8.00726 6.96921C8.18709 7.04615 8.37531 7.10291 8.56732 7.13993C9.33963 7.2889 10.1568 7.00263 10.7159 6.45982C10.9582 6.22462 11.1439 5.92498 11.2679 5.61251C11.2914 5.55317 11.3122 5.4925 11.3327 5.43237Z"
      />
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.97867 9.01783C10.4596 8.68936 11.0187 8.50404 11.5916 8.42202C12.0125 8.36177 12.4814 8.41416 12.8909 8.51607C13.3152 8.62161 13.7202 8.80402 14.0782 9.05572C14.3981 9.28062 14.6741 9.55217 14.9037 9.86829C15.7711 11.0624 15.8361 12.7233 15.026 13.9671C14.7612 14.3735 14.4111 14.7235 14.011 14.9967C13.5581 15.306 13.0281 15.4925 12.4877 15.5701C12.0405 15.6343 11.5816 15.6075 11.1436 15.4985C10.6466 15.3748 10.1829 15.1567 9.77705 14.8431C9.48916 14.6206 9.24484 14.3558 9.03834 14.0567C7.9162 12.431 8.34695 10.1322 9.97867 9.01783ZM11.0092 10.4962C11.2428 10.3399 11.517 10.2533 11.7932 10.2138C11.9997 10.1842 12.2186 10.2033 12.4205 10.2519C13.7023 10.5607 14.2183 12.1508 13.3819 13.1607C13.2453 13.3256 13.0807 13.468 12.8909 13.5687C12.7026 13.6688 12.4991 13.7533 12.2861 13.7813C11.8226 13.8424 11.3518 13.7506 10.9643 13.4794C10.1915 12.9385 9.95668 11.7988 10.4946 11.0105C10.6311 10.8104 10.8076 10.6311 11.0092 10.4962Z"
      />
    </Svg>
  ),
  organigram: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11 4C10.7348 4 10.4804 4.10536 10.2929 4.29289C10.1054 4.48043 10 4.73478 10 5V7C10 7.26522 10.1054 7.51957 10.2929 7.70711C10.4804 7.89464 10.7348 8 11 8H13C13.2652 8 13.5196 7.89464 13.7071 7.70711C13.8946 7.51957 14 7.26522 14 7V5C14 4.73478 13.8946 4.48043 13.7071 4.29289C13.5196 4.10536 13.2652 4 13 4H11ZM8.87868 2.87868C9.44129 2.31607 10.2044 2 11 2H13C13.7956 2 14.5587 2.31607 15.1213 2.87868C15.6839 3.44129 16 4.20435 16 5V7C16 7.79565 15.6839 8.55871 15.1213 9.12132C14.5587 9.68393 13.7957 10 13 10V11H16C16.7957 11 17.5587 11.3161 18.1213 11.8787C18.6839 12.4413 19 13.2044 19 14C19.7957 14 20.5587 14.3161 21.1213 14.8787C21.6839 15.4413 22 16.2044 22 17V19C22 19.7957 21.6839 20.5587 21.1213 21.1213C20.5587 21.6839 19.7957 22 19 22H17C16.2044 22 15.4413 21.6839 14.8787 21.1213C14.3161 20.5587 14 19.7957 14 19V17C14 16.2044 14.3161 15.4413 14.8787 14.8787C15.4413 14.3161 16.2044 14 17 14C17 13.7348 16.8946 13.4804 16.7071 13.2929C16.5196 13.1054 16.2652 13 16 13H8C7.73478 13 7.48043 13.1054 7.29289 13.2929C7.10536 13.4804 7 13.7348 7 14C7.79565 14 8.55871 14.3161 9.12132 14.8787C9.68393 15.4413 10 16.2044 10 17V19C10 19.7957 9.68393 20.5587 9.12132 21.1213C8.55871 21.6839 7.79565 22 7 22H5C4.20435 22 3.44129 21.6839 2.87868 21.1213C2.31607 20.5587 2 19.7957 2 19V17C2 16.2044 2.31607 15.4413 2.87868 14.8787C3.44129 14.3161 4.20435 14 5 14C5 13.2044 5.31607 12.4413 5.87868 11.8787C6.44129 11.3161 7.20435 11 8 11H11L11 10C10.2043 10 9.44129 9.68393 8.87868 9.12132C8.31607 8.55871 8 7.79565 8 7V5C8 4.20435 8.31607 3.44129 8.87868 2.87868ZM5 16C4.73478 16 4.48043 16.1054 4.29289 16.2929C4.10536 16.4804 4 16.7348 4 17V19C4 19.2652 4.10536 19.5196 4.29289 19.7071C4.48043 19.8946 4.73478 20 5 20H7C7.26522 20 7.51957 19.8946 7.70711 19.7071C7.89464 19.5196 8 19.2652 8 19V17C8 16.7348 7.89464 16.4804 7.70711 16.2929C7.51957 16.1054 7.26522 16 7 16H5ZM17 16C16.7348 16 16.4804 16.1054 16.2929 16.2929C16.1054 16.4804 16 16.7348 16 17V19C16 19.2652 16.1054 19.5196 16.2929 19.7071C16.4804 19.8946 16.7348 20 17 20H19C19.2652 20 19.5196 19.8946 19.7071 19.7071C19.8946 19.5196 20 19.2652 20 19V17C20 16.7348 19.8946 16.4804 19.7071 16.2929C19.5196 16.1054 19.2652 16 19 16H17Z"
      />
    </Svg>
  ),
  community: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.8321 2.4453C12.6466 2.1671 12.3344 2 12 2C11.6656 2 11.3534 2.1671 11.168 2.4453L9.16795 5.4453C9.08337 5.57217 9.02892 5.7167 9.00877 5.86784L8.72448 8H8C7.44772 8 7 8.44772 7 9C7 9.55228 7.44772 10 8 10H8.45782L7.00877 20.8678C6.97067 21.1536 7.05773 21.4418 7.24762 21.6587C7.4375 21.8756 7.71174 22 8 22H16C16.2883 22 16.5625 21.8756 16.7524 21.6587C16.9423 21.4418 17.0293 21.1536 16.9912 20.8678L15.5422 10H16C16.5523 10 17 9.55228 17 9C17 8.44772 16.5523 8 16 8H15.2755L14.9912 5.86784C14.9711 5.7167 14.9166 5.57217 14.8321 5.4453L12.8321 2.4453ZM13.2578 8L13.0394 6.36188L12 4.80278L10.9606 6.36188L10.7422 8H13.2578ZM10.4755 10L9.14218 20H14.8578L13.5245 10H10.4755ZM2.29289 6.29289C2.68342 5.90237 3.31658 5.90237 3.70711 6.29289L5.70711 8.29289C6.09763 8.68342 6.09763 9.31658 5.70711 9.70711L3.70711 11.7071C3.31658 12.0976 2.68342 12.0976 2.29289 11.7071C1.90237 11.3166 1.90237 10.6834 2.29289 10.2929L3.58579 9L2.29289 7.70711C1.90237 7.31658 1.90237 6.68342 2.29289 6.29289ZM21.7071 7.70711C22.0976 7.31658 22.0976 6.68342 21.7071 6.29289C21.3166 5.90237 20.6834 5.90237 20.2929 6.29289L18.2929 8.29289C17.9024 8.68342 17.9024 9.31658 18.2929 9.70711L20.2929 11.7071C20.6834 12.0976 21.3166 12.0976 21.7071 11.7071C22.0976 11.3166 22.0976 10.6834 21.7071 10.2929L20.4142 9L21.7071 7.70711Z"
      />
    </Svg>
  ),
  academy: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.3714 4.07152C12.133 3.97616 11.867 3.97616 11.6286 4.07152L1.62861 8.07152C1.24895 8.22339 1 8.59109 1 9C1 9.4089 1.24895 9.77661 1.62861 9.92848L5 11.277V16C5 17.3662 6.06101 18.3912 7.31015 19.0157C8.60606 19.6637 10.2935 20 12 20C13.7065 20 15.3939 19.6637 16.6899 19.0157C17.939 18.3912 19 17.3662 19 16V11.277L21 10.477V15C21 15.5523 21.4477 16 22 16C22.5523 16 23 15.5523 23 15V9C23 8.59109 22.751 8.22339 22.3714 8.07152L12.3714 4.07152ZM17.6502 9.66289C17.6358 9.66825 17.6216 9.67393 17.6076 9.67992L12 11.923L6.39247 9.67996C6.37839 9.67394 6.36414 9.66824 6.34974 9.66286L4.69258 9L12 6.07703L19.3074 9L17.6502 9.66289ZM7 12.077L11.6286 13.9285C11.867 14.0238 12.133 14.0238 12.3714 13.9285L17 12.077V16C17 16.2251 16.7967 16.7262 15.7954 17.2269C14.8409 17.7041 13.4761 18 12 18C10.5239 18 9.15909 17.7041 8.20457 17.2269C7.20327 16.7262 7 16.2251 7 16V12.077Z"
      />
    </Svg>
  ),
  book: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.00491 5.90004C2.00092 5.93951 1.99925 5.97929 1.99998 6.01916V18.9826C1.9983 19.0747 2.00934 19.1677 2.03382 19.2588C2.06553 19.3775 2.11851 19.4874 2.18842 19.5844C2.27298 19.7021 2.37957 19.7967 2.49917 19.8657C2.61982 19.9357 2.75645 19.9811 2.90223 19.9952C3.02031 20.0069 3.14112 19.9978 3.25887 19.9661C3.34974 19.9418 3.43551 19.9051 3.51423 19.8578C4.72691 19.161 6.10117 18.7942 7.49998 18.7942C8.89879 18.7942 10.2731 19.161 11.4857 19.8578C11.6254 19.9417 11.7873 19.9925 11.9605 19.9992C11.9737 19.9997 11.9868 20 12 20C12.0108 20 12.0216 19.9998 12.0324 19.9994C12.2082 19.9938 12.3726 19.9429 12.5142 19.8578C13.7269 19.161 15.1012 18.7942 16.5 18.7942C17.8988 18.7942 19.2731 19.161 20.4857 19.8578C20.5645 19.9051 20.6502 19.9418 20.7411 19.9661C20.8588 19.9978 20.9797 20.0069 21.0977 19.9952C21.2434 19.9811 21.38 19.9357 21.5006 19.8658C21.6204 19.7967 21.7271 19.702 21.8117 19.5841C21.8816 19.4872 21.9345 19.3774 21.9662 19.2588C21.9906 19.1677 22.0017 19.0747 22 18.9826V6.01917C22.0007 5.97926 21.9991 5.93945 21.995 5.89995C21.9807 5.75543 21.9356 5.61997 21.8663 5.5002C21.7968 5.37946 21.7013 5.27194 21.5823 5.1869C21.5504 5.16399 21.517 5.14292 21.4825 5.12384C19.9665 4.25271 18.2486 3.79419 16.5 3.79419C14.9344 3.79419 13.3934 4.16174 12 4.8639C10.6066 4.16174 9.06557 3.79419 7.49998 3.79419C5.75136 3.79419 4.03343 4.25271 2.51752 5.12384C2.48254 5.14314 2.44884 5.16448 2.41658 5.18769C2.29807 5.2726 2.20294 5.3798 2.13367 5.50014C2.06439 5.61995 2.01926 5.75547 2.00491 5.90004ZM20 6.60044C18.9117 6.07096 17.7151 5.79419 16.5 5.79419C15.2848 5.79419 14.0882 6.07096 13 6.60044V17.4267C14.1153 17.01 15.3004 16.7942 16.5 16.7942C17.6995 16.7942 18.8846 17.01 20 17.4267V6.60044ZM11 6.60044V17.4267C9.88463 17.01 8.69953 16.7942 7.49998 16.7942C6.30044 16.7942 5.11534 17.01 3.99998 17.4267V6.60044C5.08824 6.07096 6.28485 5.79419 7.49998 5.79419C8.71512 5.79419 9.91173 6.07096 11 6.60044Z"
      />
    </Svg>
  ),
  reports: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7 4C6.73478 4 6.48043 4.10536 6.29289 4.29289C6.10536 4.48043 6 4.73478 6 5V19C6 19.2652 6.10536 19.5196 6.29289 19.7071C6.48043 19.8946 6.73478 20 7 20H17C17.2652 20 17.5196 19.8946 17.7071 19.7071C17.8946 19.5196 18 19.2652 18 19L18 9H15C14.4696 9 13.9609 8.78929 13.5858 8.41421C13.2107 8.03914 13 7.53043 13 7V4H7ZM15 5.41421L16.5858 7H15V5.41421ZM4.87868 2.87868C5.44129 2.31607 6.20435 2 7 2H14C14.2652 2 14.5196 2.10536 14.7071 2.29289L19.7071 7.29289C19.8946 7.48043 20 7.73478 20 8L20 19C20 19.7956 19.6839 20.5587 19.1213 21.1213C18.5587 21.6839 17.7957 22 17 22H7C6.20435 22 5.44129 21.6839 4.87868 21.1213C4.31607 20.5587 4 19.7956 4 19V5C4 4.20435 4.31607 3.44129 4.87868 2.87868ZM9 11C9.55229 11 10 11.4477 10 12V17C10 17.5523 9.55229 18 9 18C8.44772 18 8 17.5523 8 17V12C8 11.4477 8.44772 11 9 11ZM15 13C15.5523 13 16 13.4477 16 14V17C16 17.5523 15.5523 18 15 18C14.4477 18 14 17.5523 14 17V14C14 13.4477 14.4477 13 15 13ZM12 15C12.5523 15 13 15.4477 13 16V17C13 17.5523 12.5523 18 12 18C11.4477 18 11 17.5523 11 17V16C11 15.4477 11.4477 15 12 15Z"
      />
    </Svg>
  ),
  unlock: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 16 17"
      {...props}
    >
      <path d="M8.00002 11.8332C8.35364 11.8332 8.69278 11.6927 8.94283 11.4426C9.19288 11.1926 9.33335 10.8535 9.33335 10.4998C9.33335 9.75984 8.73335 9.1665 8.00002 9.1665C7.6464 9.1665 7.30726 9.30698 7.05721 9.55703C6.80716 9.80708 6.66669 10.1462 6.66669 10.4998C6.66669 10.8535 6.80716 11.1926 7.05721 11.4426C7.30726 11.6927 7.6464 11.8332 8.00002 11.8332ZM12 5.83317C12.3536 5.83317 12.6928 5.97365 12.9428 6.2237C13.1929 6.47374 13.3334 6.81288 13.3334 7.1665V13.8332C13.3334 14.1868 13.1929 14.5259 12.9428 14.776C12.6928 15.026 12.3536 15.1665 12 15.1665H4.00002C3.6464 15.1665 3.30726 15.026 3.05721 14.776C2.80716 14.5259 2.66669 14.1868 2.66669 13.8332V7.1665C2.66669 6.4265 3.26669 5.83317 4.00002 5.83317H4.66669V4.49984C4.66669 3.61578 5.01788 2.76794 5.643 2.14281C6.26812 1.51769 7.11596 1.1665 8.00002 1.1665C8.43776 1.1665 8.87121 1.25272 9.27563 1.42024C9.68005 1.58775 10.0475 1.83329 10.357 2.14281C10.6666 2.45234 10.9121 2.81981 11.0796 3.22423C11.2471 3.62864 11.3334 4.0621 11.3334 4.49984V5.1665V5.83317H12ZM8.00002 2.49984C7.46959 2.49984 6.96088 2.71055 6.58581 3.08562C6.21073 3.4607 6.00002 3.9694 6.00002 4.49984V5.83317H10H11.3334V4.49984H10C10 3.9694 9.78931 3.4607 9.41423 3.08562C9.03916 2.71055 8.53045 2.49984 8.00002 2.49984Z" />
    </Svg>
  ),
  'notification-outline': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 2C10.3151 2 8.69922 2.66601 7.50781 3.85152C6.3164 5.03703 5.64708 6.64492 5.64708 8.32148C5.64708 11.2392 5.02018 13.0376 4.45284 14.0726C4.1677 14.5928 3.89179 14.9309 3.70379 15.1283C3.60952 15.2274 3.53661 15.2918 3.49474 15.3265C3.47429 15.3435 3.4612 15.3534 3.45662 15.3568C3.07888 15.6166 2.91199 16.09 3.04545 16.5286C3.18066 16.973 3.59226 17.2769 4.05884 17.2769H19.9412C20.4078 17.2769 20.8194 16.973 20.9546 16.5286C21.088 16.09 20.9212 15.6166 20.5434 15.3568C20.5388 15.3534 20.5258 15.3435 20.5053 15.3265C20.4634 15.2918 20.3905 15.2274 20.2962 15.1283C20.1082 14.9309 19.8323 14.5928 19.5472 14.0726C18.9798 13.0376 18.353 11.2392 18.353 8.32148C18.353 6.64492 17.6836 5.03703 16.4922 3.85152C15.3008 2.66601 13.6849 2 12 2ZM3.45662 15.3568L3.46228 15.3529L3.46643 15.3501L3.45662 15.3568ZM6.26293 15.1697H17.7371C17.7208 15.1407 17.7044 15.1114 17.6881 15.0816C16.9319 13.7022 16.2353 11.5497 16.2353 8.32148C16.2353 7.20377 15.7891 6.13184 14.9948 5.34151C14.2006 4.55117 13.1233 4.10716 12 4.10716C10.8767 4.10716 9.79948 4.55117 9.00521 5.34151C8.21094 6.13184 7.76472 7.20377 7.76472 8.32148C7.76472 11.5497 7.06809 13.7022 6.31191 15.0816C6.2956 15.1114 6.27927 15.1407 6.26293 15.1697ZM20.5285 15.3467L20.5299 15.3476C20.5299 15.3476 20.5285 15.3467 19.9412 16.2233L20.5285 15.3467ZM11.3895 19.2067C11.0961 18.7034 10.4481 18.5321 9.9423 18.824C9.43648 19.116 9.26429 19.7607 9.55771 20.2641C9.80591 20.6898 10.1622 21.0432 10.5908 21.2889C11.0194 21.5345 11.5054 21.6638 12.0001 21.6638C12.4947 21.6638 12.9807 21.5345 13.4093 21.2889C13.838 21.0432 14.1942 20.6898 14.4424 20.2641C14.7358 19.7607 14.5636 19.116 14.0578 18.824C13.552 18.5321 12.9041 18.7034 12.6106 19.2067C12.5486 19.3132 12.4595 19.4015 12.3524 19.4629C12.2452 19.5244 12.1237 19.5567 12.0001 19.5567C11.8764 19.5567 11.7549 19.5244 11.6477 19.4629C11.5406 19.4015 11.4515 19.3132 11.3895 19.2067Z"
      />
    </Svg>
  ),
  'vote-ballot': (props: IconPropsWithoutName) => (
    <Svg width="15" height="17" viewBox="0 0 15 17" {...props}>
      <path d="M1.38587 16.8334C1.00543 16.8334 0.679348 16.6904 0.407609 16.4045C0.13587 16.1185 0 15.7802 0 15.3896V11.3093L2.85326 8.08694L3.8519 9.11225L1.44701 11.7906H13.553L11.2908 9.1541L12.2894 8.12879L15 11.3093V15.3896C15 15.7802 14.8641 16.1185 14.5924 16.4045C14.3207 16.6904 13.9946 16.8334 13.6141 16.8334H1.38587ZM6.60326 10.2003L3.44429 6.95702C3.14538 6.65012 3.00951 6.31184 3.03668 5.94217C3.06386 5.57251 3.22011 5.2412 3.50543 4.94826L7.74456 0.595945C8.0163 0.316951 8.34239 0.173966 8.72283 0.166991C9.10326 0.160016 9.43614 0.303001 9.72147 0.595945L12.8804 3.86018C13.1522 4.12522 13.288 4.44607 13.288 4.82271C13.288 5.19935 13.1386 5.54112 12.8397 5.84801L8.60054 10.2003C8.3288 10.4793 8.00272 10.6258 7.62228 10.6397C7.24185 10.6537 6.90217 10.5072 6.60326 10.2003ZM11.7187 4.78086L8.74321 1.72587L4.5856 5.99449L7.56114 9.0704L11.7187 4.78086Z" />
    </Svg>
  ),
  print: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 14 12"
      {...props}
    >
      <path d="M11.666 3.333H2.333c-1.107 0-2 .894-2 2v4H3V12h8V9.333h2.666v-4c0-1.106-.893-2-2-2Zm-2 7.334H4.333V7.333h5.333v3.334Zm2-4.667A.669.669 0 0 1 11 5.333c0-.366.3-.666.666-.666.367 0 .667.3.667.666 0 .367-.3.667-.667.667ZM11 0H3v2.667h8V0Z" />
    </Svg>
  ),
  dropPin: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 20"
      {...props}
    >
      <path d="M9.50944 8.96119L8.85382 8.30556C8.66649 8.11824 8.44795 8.02458 8.19819 8.02458C7.94843 8.02458 7.72988 8.11824 7.54256 8.30556C7.35524 8.49289 7.26158 8.71533 7.26158 8.9729C7.26158 9.23047 7.35524 9.45291 7.54256 9.64023L8.85382 10.9749C9.04114 11.1622 9.25968 11.2559 9.50944 11.2559C9.75921 11.2559 9.97775 11.1622 10.1651 10.9749L13.49 7.64994C13.6774 7.46261 13.771 7.24017 13.771 6.9826C13.771 6.72503 13.6774 6.50259 13.49 6.31527C13.3027 6.12794 13.0803 6.03428 12.8227 6.03428C12.5651 6.03428 12.3427 6.12794 12.1554 6.31527L9.50944 8.96119ZM10.4929 17.2502C12.3973 15.5019 13.81 13.9135 14.731 12.4852C15.652 11.0569 16.1125 9.78853 16.1125 8.68021C16.1125 6.9787 15.5701 5.58549 14.4852 4.50058C13.4003 3.41567 12.0695 2.87322 10.4929 2.87322C8.91626 2.87322 7.58549 3.41567 6.50058 4.50058C5.41567 5.58549 4.87322 6.9787 4.87322 8.68021C4.87322 9.78853 5.33372 11.0569 6.25472 12.4852C7.17572 13.9135 8.58844 15.5019 10.4929 17.2502ZM10.4929 19.1C10.2743 19.1 10.0558 19.061 9.83726 18.9829C9.61871 18.9049 9.42359 18.7878 9.25187 18.6317C8.23721 17.6951 7.33963 16.7819 6.55912 15.8921C5.77861 15.0023 5.12689 14.1399 4.60395 13.3047C4.081 12.4696 3.68295 11.6657 3.40977 10.893C3.13659 10.1202 3 9.38266 3 8.68021C3 6.33868 3.75319 4.47326 5.25957 3.08396C6.76595 1.69465 8.51039 1 10.4929 1C12.4754 1 14.2198 1.69465 15.7262 3.08396C17.2326 4.47326 17.9858 6.33868 17.9858 8.68021C17.9858 9.38266 17.8492 10.1202 17.576 10.893C17.3028 11.6657 16.9048 12.4696 16.3818 13.3047C15.8589 14.1399 15.2072 15.0023 14.4266 15.8921C13.6461 16.7819 12.7486 17.6951 11.7339 18.6317C11.5622 18.7878 11.3671 18.9049 11.1485 18.9829C10.93 19.061 10.7114 19.1 10.4929 19.1Z" />
    </Svg>
  ),
  drawRoute: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 20"
      {...props}
    >
      <path d="M7.66667 19C6.62778 19 5.73843 18.6301 4.99861 17.8903C4.2588 17.1505 3.88889 16.2611 3.88889 15.2222V7.50139C3.33796 7.29676 2.88542 6.9544 2.53125 6.47431C2.17708 5.99421 2 5.44722 2 4.83333C2 4.0463 2.27546 3.37731 2.82639 2.82639C3.37731 2.27546 4.0463 2 4.83333 2C5.62037 2 6.28935 2.27546 6.84028 2.82639C7.3912 3.37731 7.66667 4.0463 7.66667 4.83333C7.66667 5.44722 7.48958 5.99421 7.13542 6.47431C6.78125 6.9544 6.3287 7.29676 5.77778 7.50139V15.2222C5.77778 15.7417 5.96273 16.1863 6.33264 16.5563C6.70255 16.9262 7.14722 17.1111 7.66667 17.1111C8.18611 17.1111 8.63079 16.9262 9.00069 16.5563C9.3706 16.1863 9.55556 15.7417 9.55556 15.2222V5.77778C9.55556 4.73889 9.92546 3.84954 10.6653 3.10972C11.4051 2.36991 12.2944 2 13.3333 2C14.3722 2 15.2616 2.36991 16.0014 3.10972C16.7412 3.84954 17.1111 4.73889 17.1111 5.77778V13.4986C17.662 13.7032 18.1146 14.0456 18.4688 14.5257C18.8229 15.0058 19 15.5528 19 16.1667C19 16.9537 18.7245 17.6227 18.1736 18.1736C17.6227 18.7245 16.9537 19 16.1667 19C15.3796 19 14.7106 18.7245 14.1597 18.1736C13.6088 17.6227 13.3333 16.9537 13.3333 16.1667C13.3333 15.5528 13.5104 15.0019 13.8646 14.5139C14.2188 14.0259 14.6713 13.6875 15.2222 13.4986V5.77778C15.2222 5.25833 15.0373 4.81366 14.6674 4.44375C14.2975 4.07384 13.8528 3.88889 13.3333 3.88889C12.8139 3.88889 12.3692 4.07384 11.9993 4.44375C11.6294 4.81366 11.4444 5.25833 11.4444 5.77778V15.2222C11.4444 16.2611 11.0745 17.1505 10.3347 17.8903C9.59491 18.6301 8.70556 19 7.66667 19ZM4.83333 5.77778C5.10093 5.77778 5.32523 5.68727 5.50625 5.50625C5.68727 5.32523 5.77778 5.10093 5.77778 4.83333C5.77778 4.56574 5.68727 4.34144 5.50625 4.16042C5.32523 3.9794 5.10093 3.88889 4.83333 3.88889C4.56574 3.88889 4.34144 3.9794 4.16042 4.16042C3.9794 4.34144 3.88889 4.56574 3.88889 4.83333C3.88889 5.10093 3.9794 5.32523 4.16042 5.50625C4.34144 5.68727 4.56574 5.77778 4.83333 5.77778ZM16.1667 17.1111C16.4343 17.1111 16.6586 17.0206 16.8396 16.8396C17.0206 16.6586 17.1111 16.4343 17.1111 16.1667C17.1111 15.8991 17.0206 15.6748 16.8396 15.4938C16.6586 15.3127 16.4343 15.2222 16.1667 15.2222C15.8991 15.2222 15.6748 15.3127 15.4938 15.4938C15.3127 15.6748 15.2222 15.8991 15.2222 16.1667C15.2222 16.4343 15.3127 16.6586 15.4938 16.8396C15.6748 17.0206 15.8991 17.1111 16.1667 17.1111Z" />
    </Svg>
  ),
  drawPolygon: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 20"
      {...props}
    >
      <path d="M10 4.99984H11.6667V3.33317H10V4.99984ZM4.16667 11.6665H5.83333V9.99984H4.16667V11.6665ZM14.1667 16.6665H15.8333V14.9998H14.1667V16.6665ZM12.5 16.6665V16.2498L6.66667 13.3332H4.16667C3.70833 13.3332 3.31597 13.17 2.98958 12.8436C2.66319 12.5172 2.5 12.1248 2.5 11.6665V9.99984C2.5 9.5415 2.66319 9.14914 2.98958 8.82275C3.31597 8.49636 3.70833 8.33317 4.16667 8.33317H6.08333L8.33333 5.74984V3.33317C8.33333 2.87484 8.49653 2.48248 8.82292 2.15609C9.14931 1.8297 9.54167 1.6665 10 1.6665H11.6667C12.125 1.6665 12.5174 1.8297 12.8438 2.15609C13.1701 2.48248 13.3333 2.87484 13.3333 3.33317V4.99984C13.3333 5.45817 13.1701 5.85053 12.8438 6.17692C12.5174 6.50331 12.125 6.6665 11.6667 6.6665H9.75L7.5 9.24984V11.8748L12.6042 14.4165C12.7153 14.0971 12.9132 13.8366 13.1979 13.6353C13.4826 13.4339 13.8056 13.3332 14.1667 13.3332H15.8333C16.2917 13.3332 16.684 13.4964 17.0104 13.8228C17.3368 14.1491 17.5 14.5415 17.5 14.9998V16.6665C17.5 17.1248 17.3368 17.5172 17.0104 17.8436C16.684 18.17 16.2917 18.3332 15.8333 18.3332H14.1667C13.7083 18.3332 13.316 18.17 12.9896 17.8436C12.6632 17.5172 12.5 17.1248 12.5 16.6665Z" />
      <path d="M14.0001 13.3412L11.8589 6.58824L13.0118 5.62354L15.6236 13.3412H14.0001Z" />
    </Svg>
  ),
  undo: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 20 20"
      {...props}
    >
      <path d="M17.5412 12.4783C17.5412 15.898 14.7692 18.67 11.3495 18.67H4.20519V16.7649H11.3495C13.7309 16.7649 15.636 14.8597 15.636 12.4783C15.636 10.0969 13.7309 8.19171 11.3495 8.19171H5.9484L8.88232 11.1352L7.53919 12.4783L2.30005 7.23914L7.53919 2L8.89184 3.34313L5.9484 6.28657H11.3495C14.7692 6.28657 17.5412 9.05855 17.5412 12.4783Z" />
    </Svg>
  ),
  twilight: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 -960 960 960"
      {...props}
    >
      <path d="m734-556-56-58 86-84 56 56-86 86ZM80-160v-80h800v80H80Zm360-520v-120h80v120h-80ZM226-558l-84-86 56-56 86 86-58 56Zm-26 238q0-117 81.5-198.5T480-600q117 0 198.5 81.5T760-320H200Z" />
    </Svg>
  ),
  rocket: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 12 13"
      {...props}
    >
      <path d="M2.57297 6.58747C2.82574 5.9458 3.12956 5.32844 3.48442 4.73539C3.83928 4.14233 4.24033 3.5833 4.68755 3.0583L3.92922 2.89789C3.73477 2.859 3.54519 2.86872 3.36047 2.92705C3.17574 2.98539 3.01533 3.08261 2.87922 3.21872L1.04172 5.05622C0.895882 5.20205 0.839979 5.37705 0.874007 5.58122C0.908035 5.78539 1.02227 5.92636 1.21672 6.00414L2.57297 6.58747ZM11.148 0.899971C10.1174 0.85136 9.1379 1.05067 8.20942 1.49789C7.28095 1.94511 6.45213 2.5333 5.72297 3.26247C5.2563 3.72914 4.84553 4.23469 4.49067 4.77914C4.13581 5.32358 3.82713 5.89719 3.56463 6.49997C3.51602 6.62636 3.49172 6.75518 3.49172 6.88643C3.49172 7.01768 3.54033 7.13192 3.63755 7.22914L5.46047 9.05205C5.55769 9.14928 5.67192 9.19789 5.80317 9.19789C5.93442 9.19789 6.06324 9.17358 6.18963 9.12497C6.79241 8.86247 7.36602 8.55379 7.91047 8.19893C8.45491 7.84407 8.96047 7.4333 9.42713 6.96664C10.1563 6.23747 10.7445 5.40865 11.1917 4.48018C11.6389 3.55171 11.8382 2.57219 11.7896 1.54164C11.7896 1.46386 11.7702 1.38608 11.7313 1.3083C11.6924 1.23053 11.6438 1.16247 11.5855 1.10414C11.5271 1.0458 11.4591 0.997193 11.3813 0.958304C11.3035 0.919415 11.2257 0.899971 11.148 0.899971ZM7.4438 5.2458C7.22019 5.02219 7.10838 4.74754 7.10838 4.42185C7.10838 4.09615 7.22019 3.8215 7.4438 3.59789C7.66741 3.37428 7.94206 3.26247 8.26776 3.26247C8.59345 3.26247 8.8681 3.37428 9.09172 3.59789C9.31533 3.8215 9.42713 4.09615 9.42713 4.42185C9.42713 4.74754 9.31533 5.02219 9.09172 5.2458C8.8681 5.46942 8.59345 5.58122 8.26776 5.58122C7.94206 5.58122 7.66741 5.46942 7.4438 5.2458ZM6.10213 10.1166L6.68547 11.4729C6.76324 11.6673 6.90422 11.784 7.10838 11.8229C7.31255 11.8618 7.48755 11.8083 7.63338 11.6625L9.47088 9.82497C9.60699 9.68886 9.70422 9.52601 9.76255 9.33643C9.82088 9.14685 9.8306 8.95483 9.79172 8.76039L9.64588 8.00205C9.11116 8.44928 8.5497 8.85032 7.96151 9.20518C7.37331 9.56004 6.75352 9.86386 6.10213 10.1166ZM1.36255 8.86247C1.70283 8.52219 2.11602 8.34962 2.60213 8.34476C3.08824 8.3399 3.50144 8.50761 3.84172 8.84789C4.18199 9.18817 4.35213 9.60136 4.35213 10.0875C4.35213 10.5736 4.18199 10.9868 3.84172 11.3271C3.37505 11.7937 2.82331 12.0708 2.18651 12.1583C1.5497 12.2458 0.910465 12.3333 0.268799 12.4208C0.356299 11.7791 0.446229 11.1399 0.53859 10.5031C0.630952 9.86629 0.905604 9.31942 1.36255 8.86247Z" />
    </Svg>
  ),
  trendingUp: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 12 11"
      {...props}
    >
      <path d="M5.09583 6.06875L5.85417 6.82708C5.96111 6.93403 6.09722 6.9875 6.2625 6.9875C6.42778 6.9875 6.56389 6.93403 6.67083 6.82708L8.33333 5.16458V5.51458C8.33333 5.67986 8.38924 5.81597 8.50104 5.92292C8.61285 6.02986 8.75139 6.08333 8.91667 6.08333C9.08194 6.08333 9.22049 6.02743 9.33229 5.91563C9.4441 5.80382 9.5 5.66528 9.5 5.5V3.75C9.5 3.58472 9.4441 3.44618 9.33229 3.33438C9.22049 3.22257 9.08194 3.16667 8.91667 3.16667H7.15208C6.98681 3.16667 6.85069 3.22257 6.74375 3.33438C6.63681 3.44618 6.58333 3.58472 6.58333 3.75C6.58333 3.91528 6.63924 4.05382 6.75104 4.16563C6.86285 4.27743 7.00139 4.33333 7.16667 4.33333H7.50208L6.2625 5.5875L5.50417 4.82917C5.39722 4.7125 5.26111 4.65417 5.09583 4.65417C4.93056 4.65417 4.79444 4.7125 4.6875 4.82917L2.90833 6.60833C2.79167 6.71528 2.73333 6.85139 2.73333 7.01667C2.73333 7.18194 2.79167 7.31806 2.90833 7.425C3.01528 7.54167 3.15139 7.6 3.31667 7.6C3.48194 7.6 3.61806 7.54167 3.725 7.425L5.09583 6.06875ZM1.91667 10.75C1.59583 10.75 1.32118 10.6358 1.09271 10.4073C0.864236 10.1788 0.75 9.90417 0.75 9.58333V1.41667C0.75 1.09583 0.864236 0.821181 1.09271 0.592708C1.32118 0.364236 1.59583 0.25 1.91667 0.25H10.0833C10.4042 0.25 10.6788 0.364236 10.9073 0.592708C11.1358 0.821181 11.25 1.09583 11.25 1.41667V9.58333C11.25 9.90417 11.1358 10.1788 10.9073 10.4073C10.6788 10.6358 10.4042 10.75 10.0833 10.75H1.91667Z" />
    </Svg>
  ),
  random: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 12 11"
      {...props}
    >
      <path d="M3.375 9C3.61806 9 3.82465 8.91493 3.99479 8.74479C4.16493 8.57465 4.25 8.36806 4.25 8.125C4.25 7.88194 4.16493 7.67535 3.99479 7.50521C3.82465 7.33507 3.61806 7.25 3.375 7.25C3.13194 7.25 2.92535 7.33507 2.75521 7.50521C2.58507 7.67535 2.5 7.88194 2.5 8.125C2.5 8.36806 2.58507 8.57465 2.75521 8.74479C2.92535 8.91493 3.13194 9 3.375 9ZM3.375 3.75C3.61806 3.75 3.82465 3.66493 3.99479 3.49479C4.16493 3.32465 4.25 3.11806 4.25 2.875C4.25 2.63194 4.16493 2.42535 3.99479 2.25521C3.82465 2.08507 3.61806 2 3.375 2C3.13194 2 2.92535 2.08507 2.75521 2.25521C2.58507 2.42535 2.5 2.63194 2.5 2.875C2.5 3.11806 2.58507 3.32465 2.75521 3.49479C2.92535 3.66493 3.13194 3.75 3.375 3.75ZM6 6.375C6.24306 6.375 6.44965 6.28993 6.61979 6.11979C6.78993 5.94965 6.875 5.74306 6.875 5.5C6.875 5.25694 6.78993 5.05035 6.61979 4.88021C6.44965 4.71007 6.24306 4.625 6 4.625C5.75694 4.625 5.55035 4.71007 5.38021 4.88021C5.21007 5.05035 5.125 5.25694 5.125 5.5C5.125 5.74306 5.21007 5.94965 5.38021 6.11979C5.55035 6.28993 5.75694 6.375 6 6.375ZM8.625 9C8.86806 9 9.07465 8.91493 9.24479 8.74479C9.41493 8.57465 9.5 8.36806 9.5 8.125C9.5 7.88194 9.41493 7.67535 9.24479 7.50521C9.07465 7.33507 8.86806 7.25 8.625 7.25C8.38194 7.25 8.17535 7.33507 8.00521 7.50521C7.83507 7.67535 7.75 7.88194 7.75 8.125C7.75 8.36806 7.83507 8.57465 8.00521 8.74479C8.17535 8.91493 8.38194 9 8.625 9ZM8.625 3.75C8.86806 3.75 9.07465 3.66493 9.24479 3.49479C9.41493 3.32465 9.5 3.11806 9.5 2.875C9.5 2.63194 9.41493 2.42535 9.24479 2.25521C9.07465 2.08507 8.86806 2 8.625 2C8.38194 2 8.17535 2.08507 8.00521 2.25521C7.83507 2.42535 7.75 2.63194 7.75 2.875C7.75 3.11806 7.83507 3.32465 8.00521 3.49479C8.17535 3.66493 8.38194 3.75 8.625 3.75ZM1.91667 10.75C1.59583 10.75 1.32118 10.6358 1.09271 10.4073C0.864236 10.1788 0.75 9.90417 0.75 9.58333V1.41667C0.75 1.09583 0.864236 0.821181 1.09271 0.592708C1.32118 0.364236 1.59583 0.25 1.91667 0.25H10.0833C10.4042 0.25 10.6788 0.364236 10.9073 0.592708C11.1358 0.821181 11.25 1.09583 11.25 1.41667V9.58333C11.25 9.90417 11.1358 10.1788 10.9073 10.4073C10.6788 10.6358 10.4042 10.75 10.0833 10.75H1.91667Z" />
    </Svg>
  ),
  archive: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 12 13"
      {...props}
    >
      <path d="M1.91663 12.3334C1.59579 12.3334 1.32114 12.2192 1.09267 11.9907C0.864195 11.7622 0.749959 11.4876 0.749959 11.1667V4.58966C0.574959 4.48272 0.433987 4.34418 0.327043 4.17404C0.220098 4.0039 0.166626 3.80703 0.166626 3.58341V1.83341C0.166626 1.51258 0.280862 1.23793 0.509334 1.00946C0.737807 0.780984 1.01246 0.666748 1.33329 0.666748H10.6666C10.9875 0.666748 11.2621 0.780984 11.4906 1.00946C11.7191 1.23793 11.8333 1.51258 11.8333 1.83341V3.58341C11.8333 3.80703 11.7798 4.0039 11.6729 4.17404C11.5659 4.34418 11.425 4.48272 11.25 4.58966V11.1667C11.25 11.4876 11.1357 11.7622 10.9073 11.9907C10.6788 12.2192 10.4041 12.3334 10.0833 12.3334H1.91663ZM1.33329 3.58341H10.6666V1.83341H1.33329V3.58341ZM4.83329 7.66675H7.16663C7.3319 7.66675 7.47044 7.61084 7.58225 7.49904C7.69406 7.38723 7.74996 7.24869 7.74996 7.08341C7.74996 6.91814 7.69406 6.77959 7.58225 6.66779C7.47044 6.55598 7.3319 6.50008 7.16663 6.50008H4.83329C4.66801 6.50008 4.52947 6.55598 4.41767 6.66779C4.30586 6.77959 4.24996 6.91814 4.24996 7.08341C4.24996 7.24869 4.30586 7.38723 4.41767 7.49904C4.52947 7.61084 4.66801 7.66675 4.83329 7.66675Z" />
    </Svg>
  ),
  new: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 14 13"
      {...props}
    >
      <path d="M6.38753 6.90822L5.5417 6.07697C5.43475 5.97002 5.30107 5.91655 5.14066 5.91655C4.98024 5.91655 4.8417 5.97489 4.72503 6.09155C4.61809 6.1985 4.56462 6.33461 4.56462 6.49989C4.56462 6.66516 4.61809 6.80127 4.72503 6.90822L5.9792 8.16239C6.09587 8.27905 6.23198 8.33739 6.38753 8.33739C6.54309 8.33739 6.6792 8.27905 6.79587 8.16239L9.27503 5.68322C9.3917 5.56655 9.4476 5.43044 9.44274 5.27489C9.43788 5.11933 9.38198 4.98322 9.27503 4.86655C9.15837 4.74989 9.01982 4.68912 8.85941 4.68426C8.69899 4.6794 8.56045 4.7353 8.44378 4.85197L6.38753 6.90822ZM4.7542 12.1874L3.90837 10.7582L2.3042 10.4082C2.15837 10.3791 2.0417 10.3037 1.9542 10.1822C1.8667 10.0607 1.83267 9.92697 1.85212 9.78114L2.01253 8.13322L0.918783 6.87905C0.82156 6.77211 0.772949 6.64572 0.772949 6.49989C0.772949 6.35405 0.82156 6.22766 0.918783 6.12072L2.01253 4.86655L1.85212 3.21864C1.83267 3.0728 1.8667 2.93912 1.9542 2.81759C2.0417 2.69607 2.15837 2.62072 2.3042 2.59155L3.90837 2.24155L4.7542 0.812386C4.83198 0.685997 4.93892 0.600928 5.07503 0.557178C5.21114 0.513428 5.34725 0.520719 5.48337 0.579053L7.00003 1.22072L8.5167 0.579053C8.65281 0.520719 8.78892 0.513428 8.92503 0.557178C9.06114 0.600928 9.16809 0.685997 9.24587 0.812386L10.0917 2.24155L11.6959 2.59155C11.8417 2.62072 11.9584 2.69607 12.0459 2.81759C12.1334 2.93912 12.1674 3.0728 12.1479 3.21864L11.9875 4.86655L13.0813 6.12072C13.1785 6.22766 13.2271 6.35405 13.2271 6.49989C13.2271 6.64572 13.1785 6.77211 13.0813 6.87905L11.9875 8.13322L12.1479 9.78114C12.1674 9.92697 12.1334 10.0607 12.0459 10.1822C11.9584 10.3037 11.8417 10.3791 11.6959 10.4082L10.0917 10.7582L9.24587 12.1874C9.16809 12.3138 9.06114 12.3988 8.92503 12.4426C8.78892 12.4863 8.65281 12.4791 8.5167 12.4207L7.00003 11.7791L5.48337 12.4207C5.34725 12.4791 5.21114 12.4863 5.07503 12.4426C4.93892 12.3988 4.83198 12.3138 4.7542 12.1874Z" />
    </Svg>
  ),
  sportsScore: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 -960 960 960"
      {...props}
    >
      <path d="M360-720h80v-80h-80v80Zm160 0v-80h80v80h-80ZM360-400v-80h80v80h-80Zm320-160v-80h80v80h-80Zm0 160v-80h80v80h-80Zm-160 0v-80h80v80h-80Zm160-320v-80h80v80h-80Zm-240 80v-80h80v80h-80ZM200-160v-640h80v80h80v80h-80v80h80v80h-80v320h-80Zm400-320v-80h80v80h-80Zm-160 0v-80h80v80h-80Zm-80-80v-80h80v80h-80Zm160 0v-80h80v80h-80Zm80-80v-80h80v80h-80Z" />
    </Svg>
  ),
  personRaisedHand: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 14 16"
      {...props}
    >
      <path
        d="M.999 15.333a.645.645 0 0 1-.475-.191.645.645 0 0 1-.192-.475c0-.19.064-.348.192-.475A.645.645 0 0 1 .999 14h12c.189 0 .347.064.475.192a.645.645 0 0 1 .191.475.645.645 0 0 1-.191.475.645.645 0 0 1-.475.191h-12Zm1.333-2a.645.645 0 0 1-.475-.191.645.645 0 0 1-.192-.475V9.333a8.043 8.043 0 0 1-.85-1.908 7.18 7.18 0 0 1-.3-2.058c0-.678.086-1.345.259-2 .172-.656.375-1.3.608-1.934a1.17 1.17 0 0 1 1.1-.766c.344 0 .639.116.883.35a.923.923 0 0 1 .3.833l-.183 1.517a3.377 3.377 0 0 0 .142 1.516c.16.478.402.898.725 1.259.322.36.71.65 1.166.866.456.217.95.325 1.484.325.666 0 1.336.07 2.008.209a6.946 6.946 0 0 1 1.758.591c.5.256.886.581 1.159.975.272.395.408.881.408 1.459v2.1a.646.646 0 0 1-.192.475.645.645 0 0 1-.475.191h-6v-.616c0-.378.128-.703.384-.975a1.25 1.25 0 0 1 .95-.409h2a.645.645 0 0 0 .475-.191.645.645 0 0 0 .191-.475.645.645 0 0 0-.191-.475.645.645 0 0 0-.475-.192h-2c-.745 0-1.375.267-1.892.8a2.657 2.657 0 0 0-.775 1.917v.616h-2Zm4.667-6.666a2.568 2.568 0 0 1-1.884-.784A2.568 2.568 0 0 1 4.332 4c0-.733.261-1.361.783-1.883A2.568 2.568 0 0 1 7 1.333c.733 0 1.36.262 1.883.784.522.522.783 1.15.783 1.883 0 .733-.26 1.361-.783 1.883a2.568 2.568 0 0 1-1.883.784Z"
        fill="#044D6C"
      />
    </Svg>
  ),
  rating: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 248.294 248.294"
      {...props}
    >
      <path
        d="M55.688,242.322c2.882,0,6.069-0.719,9.439-2.24l59.032-32.156l59.032,32.156c3.369,1.521,6.557,2.24,9.437,2.24
        c8.933,0,14.963-6.917,14.543-18.36l-7.71-65.312l44.062-45.268c9.166-12.062,4.732-25.004-9.908-28.908l-65.53-10.529
        l-28.932-58.22c-4.242-6.49-9.959-9.754-15.732-9.754c-5.512,0-11.063,2.973-15.422,8.952L74.461,73.941l-59.893,10.06
        c-14.566,4.163-18.943,17.314-9.777,29.377l44.06,45.264l-7.71,65.311C40.721,235.405,46.753,242.322,55.688,242.322z
        M20.734,102.347l56.896-9.558l8.961-1.505l4.492-7.906l32.191-56.649l27.689,55.713l4.378,8.809l9.712,1.557l62.101,9.98
        l-41.388,42.515l-6.353,6.534l1.064,9.045l7.057,59.795l-54.231-29.548l-9.145-4.979l-9.147,4.979l-54.227,29.548
        l7.052-59.795l1.066-9.045l-6.352-6.534L20.734,102.347z"
      />
    </Svg>
  ),
  ratingFilled: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 32 32"
      {...props}
    >
      <path d="M30.383 12.699c-0.1-0.303-0.381-0.519-0.713-0.519-0 0-0 0-0 0h-9.898l-3.059-9.412c-0.124-0.276-0.396-0.464-0.713-0.464s-0.589 0.189-0.711 0.459l-0.002 0.005-3.059 9.412h-9.897c-0.414 0-0.749 0.336-0.749 0.75 0 0.248 0.121 0.469 0.307 0.605l0.002 0.001 8.007 5.818-3.059 9.412c-0.023 0.070-0.037 0.15-0.037 0.233 0 0.414 0.336 0.75 0.75 0.75 0.165 0 0.318-0.054 0.442-0.144l-0.002 0.001 8.008-5.818 8.006 5.818c0.122 0.090 0.275 0.144 0.441 0.144 0.414 0 0.75-0.336 0.75-0.75 0-0.083-0.014-0.164-0.039-0.239l0.002 0.005-3.059-9.412 8.010-5.818c0.188-0.138 0.308-0.357 0.308-0.605 0-0.083-0.014-0.163-0.038-0.238l0.002 0.005z" />
    </Svg>
  ),
  globe: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        className="cl-icon-primary"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8 15.5a7.304 7.304 0 0 1-2.925-.59 7.575 7.575 0 0 1-2.381-1.604 7.574 7.574 0 0 1-1.603-2.381A7.303 7.303 0 0 1 .5 8c0-1.037.197-2.013.59-2.925a7.574 7.574 0 0 1 1.604-2.381A7.574 7.574 0 0 1 5.075 1.09 7.303 7.303 0 0 1 8 .5c1.037 0 2.012.197 2.925.59a7.574 7.574 0 0 1 2.381 1.604 7.575 7.575 0 0 1 1.603 2.381c.394.912.591 1.888.591 2.925a7.304 7.304 0 0 1-.59 2.925 7.575 7.575 0 0 1-1.604 2.381 7.575 7.575 0 0 1-2.381 1.603A7.304 7.304 0 0 1 8 15.5Zm-.75-1.537V12.5c-.412 0-.766-.147-1.06-.44A1.445 1.445 0 0 1 5.75 11v-.75l-3.6-3.6c-.038.225-.072.45-.103.675C2.016 7.55 2 7.775 2 8c0 1.512.497 2.838 1.49 3.975.994 1.138 2.247 1.8 3.76 1.988Zm5.175-1.913a5.993 5.993 0 0 0 1.172-1.884A5.945 5.945 0 0 0 14 8c0-1.225-.34-2.344-1.022-3.356A5.776 5.776 0 0 0 10.25 2.45v.3c0 .413-.147.766-.44 1.06-.294.293-.648.44-1.06.44h-1.5v1.5c0 .213-.072.39-.216.534A.726.726 0 0 1 6.5 6.5H5V8h4.5c.213 0 .39.072.534.216a.726.726 0 0 1 .216.534V11H11c.325 0 .619.097.881.29.263.194.444.448.544.76Z"
      />
    </Svg>
  ),
  checkmarkStamp: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 30 28"
      {...props}
    >
      <path d="m13.6 14.934-1.933-1.9a1.244 1.244 0 0 0-.917-.367c-.367 0-.683.133-.95.4-.244.244-.367.555-.367.933s.123.689.367.934l2.867 2.866c.266.267.577.4.933.4.356 0 .667-.133.933-.4l5.667-5.667c.267-.266.394-.577.383-.933a1.407 1.407 0 0 0-.383-.933 1.36 1.36 0 0 0-.95-.417 1.241 1.241 0 0 0-.95.383l-4.7 4.7ZM9.867 27l-1.934-3.267-3.666-.8a1.258 1.258 0 0 1-.8-.516 1.237 1.237 0 0 1-.234-.917l.367-3.767-2.5-2.866A1.246 1.246 0 0 1 .767 14c0-.333.11-.622.333-.867l2.5-2.866L3.233 6.5a1.237 1.237 0 0 1 .234-.917c.2-.277.466-.45.8-.516l3.666-.8L9.867 1c.177-.289.422-.483.733-.583.311-.1.622-.084.933.05L15 1.933 18.467.467c.31-.134.622-.15.933-.05.311.1.556.294.733.583l1.934 3.267 3.666.8c.334.066.6.239.8.516.2.278.278.584.234.917l-.367 3.767 2.5 2.867c.222.244.333.533.333.866 0 .334-.11.622-.333.867l-2.5 2.866.367 3.767c.044.334-.034.639-.234.917s-.466.45-.8.516l-3.666.8L20.133 27a1.311 1.311 0 0 1-.733.584c-.311.1-.622.083-.933-.05L15 26.067l-3.467 1.466c-.31.134-.622.15-.933.05A1.311 1.311 0 0 1 9.867 27Z" />
    </Svg>
  ),
  quote: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 18 13"
      {...props}
    >
      <path d="m3.217 11.266 1.45-2.5c-1.1 0-2.042-.392-2.825-1.175C1.059 6.808.667 5.866.667 4.766s.392-2.042 1.175-2.825C2.625 1.158 3.567.766 4.667.766s2.042.392 2.825 1.175c.783.783 1.175 1.725 1.175 2.825 0 .383-.046.738-.138 1.063-.091.325-.229.637-.412.937l-3.175 5.5a.973.973 0 0 1-.85.5.934.934 0 0 1-.863-.5.96.96 0 0 1-.012-1Zm9 0 1.45-2.5c-1.1 0-2.042-.392-2.825-1.175-.783-.783-1.175-1.725-1.175-2.825s.392-2.042 1.175-2.825c.783-.783 1.725-1.175 2.825-1.175s2.042.392 2.825 1.175c.783.783 1.175 1.725 1.175 2.825 0 .383-.046.738-.138 1.063a3.74 3.74 0 0 1-.412.937l-3.175 5.5a.973.973 0 0 1-.85.5.934.934 0 0 1-.863-.5.96.96 0 0 1-.012-1Z" />
    </Svg>
  ),
  'chat-bubble': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 15 14"
      {...props}
    >
      <path
        id="Vector"
        d="M4.00033 10.4998L2.65866 11.8415C2.47394 12.0262 2.26248 12.0675 2.02428 11.9655C1.78609 11.8634 1.66699 11.6811 1.66699 11.4186V2.33317C1.66699 2.01234 1.78123 1.73768 2.0097 1.50921C2.23817 1.28074 2.51283 1.1665 2.83366 1.1665H12.167C12.4878 1.1665 12.7625 1.28074 12.991 1.50921C13.2194 1.73768 13.3337 2.01234 13.3337 2.33317V9.33317C13.3337 9.654 13.2194 9.92866 12.991 10.1571C12.7625 10.3856 12.4878 10.4998 12.167 10.4998H4.00033ZM3.50449 9.33317H12.167V2.33317H2.83366V9.98942L3.50449 9.33317Z"
      />
    </Svg>
  ),
  'thumb-down': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 15 14"
      {...props}
    >
      <path
        id="Vector"
        d="M2.24967 9.33333C1.93856 9.33333 1.66634 9.21667 1.43301 8.98333C1.19967 8.75 1.08301 8.47778 1.08301 8.16667V7C1.08301 6.93194 1.09273 6.85903 1.11217 6.78125C1.13162 6.70347 1.15106 6.63056 1.17051 6.5625L2.92051 2.45C3.00801 2.25556 3.15384 2.09028 3.35801 1.95417C3.56217 1.81806 3.77606 1.75 3.99967 1.75H10.4163V9.33333L6.91634 12.8042C6.77051 12.95 6.59794 13.0351 6.39863 13.0594C6.19933 13.0837 6.00731 13.0472 5.82259 12.95C5.63787 12.8528 5.50176 12.7167 5.41426 12.5417C5.32676 12.3667 5.30731 12.1868 5.35592 12.0021L6.01217 9.33333H2.24967ZM9.24967 8.8375V2.91667H3.99967L2.24967 7V8.16667H7.49967L6.71217 11.375L9.24967 8.8375ZM12.1663 1.75C12.4872 1.75 12.7618 1.86424 12.9903 2.09271C13.2188 2.32118 13.333 2.59583 13.333 2.91667V8.16667C13.333 8.4875 13.2188 8.76215 12.9903 8.99062C12.7618 9.2191 12.4872 9.33333 12.1663 9.33333H10.4163V8.16667H12.1663V2.91667H10.4163V1.75H12.1663Z"
      />
    </Svg>
  ),
  'thumb-up': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 15 14"
      {...props}
    >
      <path
        id="Vector"
        d="M12.7503 4.66644C13.0614 4.66644 13.3337 4.78311 13.567 5.01644C13.8003 5.24977 13.917 5.52199 13.917 5.83311V6.99977C13.917 7.06783 13.9073 7.14074 13.8878 7.21852C13.8684 7.2963 13.8489 7.36922 13.8295 7.43727L12.0795 11.5498C11.992 11.7442 11.8462 11.9095 11.642 12.0456C11.4378 12.1817 11.2239 12.2498 11.0003 12.2498H4.58366V4.66644L8.08366 1.19561C8.22949 1.04977 8.40206 0.964703 8.60137 0.940397C8.80067 0.916092 8.99269 0.95255 9.17741 1.04977C9.36213 1.14699 9.49824 1.28311 9.58574 1.45811C9.67324 1.63311 9.69269 1.81297 9.64408 1.99769L8.98783 4.66644H12.7503ZM5.75033 5.16227V11.0831H11.0003L12.7503 6.99977V5.83311H7.50033L8.28783 2.62477L5.75033 5.16227ZM2.83366 12.2498C2.51283 12.2498 2.23817 12.1355 2.0097 11.9071C1.78123 11.6786 1.66699 11.4039 1.66699 11.0831V5.83311C1.66699 5.51227 1.78123 5.23762 2.0097 5.00915C2.23817 4.78067 2.51283 4.66644 2.83366 4.66644H4.58366V5.83311H2.83366V11.0831H4.58366V12.2498H2.83366Z"
      />
    </Svg>
  ),
  bankId: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 316.01 298.8"
      {...props}
    >
      <path d="m99.7,155.9l13.2-83.2h-14.1c-6.6,0-15.1-3.7-17.6-10.5-.8-2.3-2.7-10.2,8.2-17.9,3.9-2.7,6.4-5.7,6.9-8,.5-2.4-.1-4.5-1.8-6.1-2.4-2.3-7.1-3.6-13.1-3.6-10.1,0-17.2,5.8-17.9,10-.5,3.1,1.9,5.6,4,7.2,6.3,4.7,7.8,11.5,3.9,17.9-4,6.6-12.7,10.9-22,11h-14.4c-1.2,8.1-20.8,132.3-22.3,142.1h77.8c.7-4.3,4.3-27.8,9.2-58.9h0Z" />
      <path d="m8.5,243.6h31.9c13.6,0,16.9,6.9,15.9,13.2-.8,5.1-4.3,8.9-10.3,11.4,7.6,2.9,10.6,7.4,9.5,14.5-1.4,8.9-9.1,15.5-19.2,15.5H0l8.5-54.6Zm21.1,22.6c6.2,0,9.1-3.3,9.7-7.2.6-4.2-1.3-7.1-7.5-7.1h-5.5l-2.2,14.3h5.5Zm-3.4,23.5c6.4,0,10.1-2.6,11-7.9.7-4.6-1.9-7.3-8.1-7.3h-6.2l-2.4,15.3h5.7v-.1Z" />
      <path d="m100.2,298.6c-8.3.6-12.3-.3-14.3-3.9-4.4,2.7-9.3,4.1-14.5,4.1-9.4,0-12.7-4.9-11.8-10.3.4-2.6,1.9-5.1,4.3-7.2,5.2-4.5,18-5.1,23-8.5.4-3.8-1.1-5.2-5.8-5.2-5.5,0-10.1,1.8-18,7.2l1.9-12.4c6.8-4.9,13.4-7.2,21-7.2,9.7,0,18.3,4,16.7,14.6l-1.9,12c-.7,4.2-.5,5.5,4.2,5.6l-4.8,11.2Zm-14.4-18.9c-4.4,2.8-12.6,2.3-13.5,8.1-.4,2.7,1.3,4.7,4,4.7s5.8-1.1,8.4-2.9c-.2-1-.1-2,.2-3.9l.9-6Z" />
      <path d="m115.7,255.9h16.6l-.9,5.5c5.3-4.5,9.3-6.2,14.5-6.2,9.3,0,13.6,5.7,12.1,15l-4.3,27.9h-16.6l3.6-23.1c.7-4.2-.6-6.2-3.8-6.2-2.6,0-5,1.4-7.3,4.5l-3.8,24.7h-16.6l6.5-42.1Z" />
      <path d="m171,243.6h16.6l-4.2,26.8,15.9-14.5h20.5l-20.4,18,16.4,24.2h-20.9l-12.6-19.6h-.2l-3,19.5h-16.6l8.5-54.4Z" />
      <path d="m229.7,243.6h19.1l-8.4,54.5h-19.1l8.4-54.5Z" />
      <path d="m258.1,243.6h27.3c21.1,0,27.2,15.3,25.2,28-1.9,12.4-11.7,26.5-30.2,26.5h-30.8l8.5-54.5Zm17.7,41.5c9.3,0,14.4-4.6,15.9-14.3,1.1-7.2-1.1-14.3-11.4-14.3h-5.1l-4.4,28.6h5Z" />
      <path d="m204.3,0h-79.5l-10.6,67.3h13.5c7.4,0,14.4-3.4,17.4-8.3,1-1.6,1.4-3,1.4-4.3,0-2.8-1.9-4.9-3.8-6.3-5.2-3.9-6.3-8-6.3-10.9,0-.6,0-1.1.1-1.6,1.1-7.1,10.7-14.8,23.4-14.8,7.6,0,13.4,1.8,16.9,5.1,3.1,2.9,4.3,7,3.4,11.3-1.1,5.1-6.2,9.3-9.1,11.4-7.7,5.4-6.7,10.1-6.2,11.5,1.6,4.2,7.7,6.9,12.4,6.9h20.6v.1c28,.2,43,13.1,38.3,43.1-4.4,27.9-25.8,39.9-51.3,40.1l-10.1,64.4h14.9c62.9,0,114.3-40.4,124.4-104.2C326.6,31.6,276.3,0,204.3,0Z" />
    </Svg>
  ),
  'sentiment-neutral': (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 19 19"
      {...props}
    >
      <g>
        <path d="M11.9321 8.51538C12.2446 8.51538 12.5103 8.406 12.729 8.18725C12.9478 7.9685 13.0571 7.70288 13.0571 7.39038C13.0571 7.07788 12.9478 6.81225 12.729 6.5935C12.5103 6.37475 12.2446 6.26538 11.9321 6.26538C11.6196 6.26538 11.354 6.37475 11.1353 6.5935C10.9165 6.81225 10.8071 7.07788 10.8071 7.39038C10.8071 7.70288 10.9165 7.9685 11.1353 8.18725C11.354 8.406 11.6196 8.51538 11.9321 8.51538ZM6.68213 8.51538C6.99463 8.51538 7.26025 8.406 7.479 8.18725C7.69775 7.9685 7.80713 7.70288 7.80713 7.39038C7.80713 7.07788 7.69775 6.81225 7.479 6.5935C7.26025 6.37475 6.99463 6.26538 6.68213 6.26538C6.36963 6.26538 6.104 6.37475 5.88525 6.5935C5.6665 6.81225 5.55713 7.07788 5.55713 7.39038C5.55713 7.70288 5.6665 7.9685 5.88525 8.18725C6.104 8.406 6.36963 8.51538 6.68213 8.51538ZM7.61963 11.8904H10.9946C11.1571 11.8904 11.2915 11.8373 11.3978 11.731C11.504 11.6248 11.5571 11.4904 11.5571 11.3279C11.5571 11.1654 11.504 11.031 11.3978 10.9248C11.2915 10.8185 11.1571 10.7654 10.9946 10.7654H7.61963C7.45713 10.7654 7.32275 10.8185 7.2165 10.9248C7.11025 11.031 7.05713 11.1654 7.05713 11.3279C7.05713 11.4904 7.11025 11.6248 7.2165 11.731C7.32275 11.8373 7.45713 11.8904 7.61963 11.8904ZM9.30713 16.7654C8.26963 16.7654 7.29463 16.5685 6.38213 16.1748C5.46963 15.781 4.67588 15.2466 4.00088 14.5716C3.32588 13.8966 2.7915 13.1029 2.39775 12.1904C2.004 11.2779 1.80713 10.3029 1.80713 9.26538C1.80713 8.22788 2.004 7.25288 2.39775 6.34038C2.7915 5.42788 3.32588 4.63413 4.00088 3.95913C4.67588 3.28413 5.46963 2.74976 6.38213 2.35601C7.29463 1.96226 8.26963 1.76538 9.30713 1.76538C10.3446 1.76538 11.3196 1.96226 12.2321 2.35601C13.1446 2.74976 13.9384 3.28413 14.6134 3.95913C15.2884 4.63413 15.8228 5.42788 16.2165 6.34038C16.6103 7.25288 16.8071 8.22788 16.8071 9.26538C16.8071 10.3029 16.6103 11.2779 16.2165 12.1904C15.8228 13.1029 15.2884 13.8966 14.6134 14.5716C13.9384 15.2466 13.1446 15.781 12.2321 16.1748C11.3196 16.5685 10.3446 16.7654 9.30713 16.7654Z" />
      </g>
    </Svg>
  ),
  cancel: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 19 19"
      {...props}
    >
      <g>
        <path d="M9.14038 10.3154L11.3154 12.4904C11.4529 12.6279 11.6279 12.6966 11.8404 12.6966C12.0529 12.6966 12.2279 12.6279 12.3654 12.4904C12.5029 12.3529 12.5716 12.1779 12.5716 11.9654C12.5716 11.7529 12.5029 11.5779 12.3654 11.4404L10.1904 9.26538L12.3654 7.09038C12.5029 6.95288 12.5716 6.77788 12.5716 6.56538C12.5716 6.35288 12.5029 6.17788 12.3654 6.04038C12.2279 5.90288 12.0529 5.83413 11.8404 5.83413C11.6279 5.83413 11.4529 5.90288 11.3154 6.04038L9.14038 8.21538L6.96538 6.04038C6.82788 5.90288 6.65288 5.83413 6.44038 5.83413C6.22788 5.83413 6.05288 5.90288 5.91538 6.04038C5.77788 6.17788 5.70913 6.35288 5.70913 6.56538C5.70913 6.77788 5.77788 6.95288 5.91538 7.09038L8.09038 9.26538L5.91538 11.4404C5.77788 11.5779 5.70913 11.7529 5.70913 11.9654C5.70913 12.1779 5.77788 12.3529 5.91538 12.4904C6.05288 12.6279 6.22788 12.6966 6.44038 12.6966C6.65288 12.6966 6.82788 12.6279 6.96538 12.4904L9.14038 10.3154ZM9.14038 16.7654C8.10288 16.7654 7.12788 16.5685 6.21538 16.1748C5.30288 15.781 4.50913 15.2466 3.83413 14.5716C3.15913 13.8966 2.62476 13.1029 2.23101 12.1904C1.83726 11.2779 1.64038 10.3029 1.64038 9.26538C1.64038 8.22788 1.83726 7.25288 2.23101 6.34038C2.62476 5.42788 3.15913 4.63413 3.83413 3.95913C4.50913 3.28413 5.30288 2.74976 6.21538 2.35601C7.12788 1.96226 8.10288 1.76538 9.14038 1.76538C10.1779 1.76538 11.1529 1.96226 12.0654 2.35601C12.9779 2.74976 13.7716 3.28413 14.4466 3.95913C15.1216 4.63413 15.656 5.42788 16.0498 6.34038C16.4435 7.25288 16.6404 8.22788 16.6404 9.26538C16.6404 10.3029 16.4435 11.2779 16.0498 12.1904C15.656 13.1029 15.1216 13.8966 14.4466 14.5716C13.7716 15.2466 12.9779 15.781 12.0654 16.1748C11.1529 16.5685 10.1779 16.7654 9.14038 16.7654Z" />
      </g>
    </Svg>
  ),
  claveUnica: (props: IconPropsWithoutName) => (
    <Svg
      className={`cl-icon ${props.className ? props.className : ''}`}
      viewBox="0 0 24 24"
      {...props}
    >
      <g>
        <path d="M11.47,14a.65.65,0,0,1-.21-.54.64.64,0,0,1,.29-.5.68.68,0,0,1,.53-.19.73.73,0,0,1,.49.27.75.75,0,0,1-.08,1,.7.7,0,0,1-.53.19.71.71,0,0,1-.49-.27Zm3.8-8.66A9,9,0,0,1,21,13.57a9,9,0,0,1-18,0A8.78,8.78,0,0,1,8.45,5.32c.37-.09.64-.09.82.36a.76.76,0,0,1-.36,1,7.52,7.52,0,1,0,5.82-.09.66.66,0,0,1-.4-.37.64.64,0,0,1,0-.54.62.62,0,0,1,.37-.39A.64.64,0,0,1,15.27,5.32Zm-7.77,8a4.64,4.64,0,0,1,3.75-4.59V2.33A.91.91,0,0,1,12,1.5h3.31a.75.75,0,0,1,.7.75.78.78,0,0,1-.7.75H12.75V8.76a4.5,4.5,0,0,1,3.75,4.58A4.61,4.61,0,0,1,12,18,4.61,4.61,0,0,1,7.5,13.33Zm7.5.09a3.1,3.1,0,0,0-3-3.12,3.12,3.12,0,0,0,0,6.2A3.09,3.09,0,0,0,15,13.42Z" />
      </g>
    </Svg>
  ),
} as const;

const Icon = (props: IconProps) => {
  return icons[props.name](props);
};

export default Icon;
